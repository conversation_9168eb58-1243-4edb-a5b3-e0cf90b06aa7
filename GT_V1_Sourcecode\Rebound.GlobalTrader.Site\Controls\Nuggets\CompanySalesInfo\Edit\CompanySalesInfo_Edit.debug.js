///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 17.05.2010:
// - [168]: Add new default setting for rating
//
// RP 06.01.2010:
// - fully dispose everything
//Marker     Changed by      Date         Remarks
//[001]      Vinay           09/08/2012   Customer No required if approved checked
//[002]      Suhail          02/05/2018   Added Credit Limit2
//[003]      Aashu Singh     14-Sep-2018  [REB-12820]:Provision to add Global Security on Contact Section
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.CompanySalesInfo_Edit = function(element) {
    Rebound.GlobalTrader.Site.Controls.Forms.CompanySalesInfo_Edit.initializeBase(this, [element]);
    this._intCompanyID = -1;
    this._blnFirstTimeApprovedClicked = true;
    this._blnApproved = false;
};

Rebound.GlobalTrader.Site.Controls.Forms.CompanySalesInfo_Edit.prototype = {

    get_intCompanyID: function() { return this._intCompanyID; }, set_intCompanyID: function(value) { if (this._intCompanyID !== value) this._intCompanyID = value; },
    get_lblCurrency_CreditLimit: function() { return this._lblCurrency_CreditLimit; }, set_lblCurrency_CreditLimit: function(value) { if (this._lblCurrency_CreditLimit !== value) this._lblCurrency_CreditLimit = value; },
    get_intDefaultRating: function() { return this._intDefaultRating; }, set_intDefaultRating: function(value) { if (this._intDefaultRating !== value) this._intDefaultRating = value; },
    get_intMailGroupNo: function () { return this._intMailGroupNo; }, set_intMailGroupNo: function (value) { if (this._intMailGroupNo !== value) this._intMailGroupNo = value; },
    //[002] code start
    get_lblCurrency_ActualCreditLimit: function () { return this._lblCurrency_ActualCreditLimit; }, set_lblCurrency_ActualCreditLimit: function (value) { if (this._lblCurrency_ActualCreditLimit !== value) this._lblCurrency_ActualCreditLimit = value; },
    //[002] code end
    initialize: function() {
        Rebound.GlobalTrader.Site.Controls.Forms.CompanySalesInfo_Edit.callBaseMethod(this, "initialize");
        this.addShown(Function.createDelegate(this, this.formShown));
    },

    dispose: function() {
        if (this.isDisposed) return;
        if (this._chkApproved) this._chkApproved.dispose();
        this._intCompanyID = null;
        this._lblCurrency_CreditLimit = null;
        this._chkApproved = null;
        this._intMailGroupNo = null;
        this._strMessage = null;
        this._blnApproved = null;
        //[002] code start
        this._lblCurrency_ActualCreditLimit = null;
        //[002] code end
        Rebound.GlobalTrader.Site.Controls.Forms.CompanySalesInfo_Edit.callBaseMethod(this, "dispose");
    },

    formShown: function () {
        this.showField("ctlShipVia", false);
        this.showField("ctlShippingAccountNo", false);
        if (this._blnFirstTimeShown) {
            this.addSave(Function.createDelegate(this, this.saveClicked));
            this._chkApproved = $find(this.getField("ctlIsApproved").ControlID);
            this._chkApproved.addClick(Function.createDelegate(this, this.approveClicked));
        }
        //[003] start
        this.getFieldControl("ctlSalesperson")._intGlobalLoginClientNo = this._globalLoginClientNo;
        this.getFieldControl("ctlCurrency")._intGlobalLoginClientNo = this._globalLoginClientNo;
        this.getFieldControl("ctlTerms")._intGlobalLoginClientNo = this._globalLoginClientNo;
        this.getFieldControl("ctlShipVia")._intGlobalLoginClientNo = this._globalLoginClientNo;
        //this.getFieldControl("ctlContact")._intGlobalLoginClientNo = this._globalLoginClientNo;
        //[003] end
        this.getFieldControl("ctlInsuredAmountCurrency")._intGlobalLoginClientNo = this._globalLoginClientNo;
        this.getFieldDropDownData("ctlCurrency");
        this.getFieldDropDownData("ctlSalesperson");
        this.getFieldDropDownData("ctlTerms");
        //ESMS #14
        // this.getFieldDropDownData("ctlTax");
        this.getFieldDropDownData("ctlShipVia");
        this.getFieldDropDownData("ctlContact");
        this.getFieldDropDownData("ctlPreferredWarehouse");
        this._blnApproved = this._chkApproved._blnChecked;
        this.getFieldDropDownData("ctlInsuredAmountCurrency");
        
    },

    saveClicked: function() {
        if (!this.validateForm()) return;
        //[001] code start
        if (!this.validateCustomerNo()) return;
        //[001] code end
        if (!this.InsuredCurrencyAmount()) return;
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/CompanySalesInfo");
        obj.set_DataObject("CompanySalesInfo");
        obj.set_DataAction("SaveEdit");
        obj.addParameter("id", this._intCompanyID);
        obj.addParameter("Salesman", this.getFieldValue("ctlSalesperson"));
        obj.addParameter("IsApproved", this.getFieldValue("ctlIsApproved"));
        obj.addParameter("CurrencyNo", this.getFieldValue("ctlCurrency"));
        obj.addParameter("CustomerNo", this.getFieldValue("ctlCustomerNo"));
        obj.addParameter("Rating", this.getFieldValue("ctlRating"));
        obj.addParameter("TermsNo", this.getFieldValue("ctlTerms"));
        //ESMS #14
        //obj.addParameter("TaxNo", this.getFieldValue("ctlTax"));
        obj.addParameter("OnStop", this.getFieldValue("ctlIsOnStop"));
        obj.addParameter("IsShippingWaived", this.getFieldValue("ctlIsShippingWaived"));
        obj.addParameter("ShipViaNo", null);
        obj.addParameter("ShippingAccountNo", null);
        //obj.addParameter("ShipViaNo", this.getFieldValue("ctlShipVia"));
        //obj.addParameter("ShippingAccountNo", this.getFieldValue("ctlShippingAccountNo"));
        obj.addParameter("ContactNo", this.getFieldValue("ctlContact"));
        obj.addParameter("CreditLimit", this.getFieldValue("ctlCreditLimit"));
         obj.addParameter("InsuranceFileNo", this.getFieldValue("ctlInsuranceFileNo"));
        obj.addParameter("InsuredAmount", this.getFieldValue("ctlInsuredAmount"));
        obj.addParameter("StopStatus", this.getFieldValue("ctlStopStatus"));
        obj.addParameter("NotesToInvoice", this.getFieldValue("ctlNotesToInvoice"));
        //[002] code start
        obj.addParameter("ActualCreditLimit", this.getFieldValue("ctlActualCreditLimit"));
        //[002] code end
        obj.addParameter("WarehouseNo", this.getFieldValue("ctlPreferredWarehouse"));
        obj.addParameter("InsuredAmountCurrencyNo", this.getFieldValue("ctlInsuredAmountCurrency"));
        obj.addDataOK(Function.createDelegate(this, this.saveEditComplete));
        obj.addError(Function.createDelegate(this, this.saveEditError));
        obj.addTimeout(Function.createDelegate(this, this.saveEditError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
        //this.onSave();        
    },

    saveEditError: function(args) {
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    saveEditComplete: function(args) {
        if (args._result.Result == true) {
            if (!this._blnApproved && this._chkApproved._blnChecked)
                this.getMessageText();
            this.showSavedOK(true);
            this.onSaveComplete();
        } else {
            this._strErrorMessage = args._errorMessage;
            this.onSaveError();
        }
    },

    validateForm: function() {
        this.onValidate();
        var blnOK = this.autoValidateFields();
        return blnOK;
    },

    cancelClicked: function() {
        this.onCancel();
    },

    setCurrencyLabel: function(strCurrency) {
        $R_FN.setInnerHTML(this._lblCurrency_CreditLimit, strCurrency);
        //[002] code start
        $R_FN.setInnerHTML(this._lblCurrency_ActualCreditLimit, strCurrency);
        //[002] code end
    },

    approveClicked: function() {
        //only set the default rating the first time
        if (this._blnFirstTimeApprovedClicked) {
            if (this._chkApproved._blnChecked) this.setFieldValue("ctlRating", this._intDefaultRating);
        }
        this._blnFirstTimeApprovedClicked = false;
    }
    ,
    //[001] code start
    validateCustomerNo: function() {
        if (this.getFieldValue("ctlIsApproved") == true && this.checkFieldEntered("ctlCustomerNo") == false) {
            this.showError(true, $R_RES.CustomerNoMessage);
            return false;
        }
        else
            return true;
    },
    InsuredCurrencyAmount: function () {
        if (this.getFieldValue("ctlInsuredAmount") > 0 && this.checkFieldEntered("ctlInsuredAmountCurrency") == false) {
            this.showError(true, $R_RES.InsuredCurrencyAmount);
            return false;
        }
        else
            //$("#ctl00_cphMain_ctlSalesInfo_ctlDB_ctl14_ctlEdit_ctlDB_ctlInsuredAmountCurrency_ctl03_ddlInsuredAmountCurrency_ddl").val(0);
            return true;
    },
    
    getMessageText: function() {
        Rebound.GlobalTrader.Site.WebServices.GetMailMessage_CompanySO(this._intCompanyID, Function.createDelegate(this, this.getMessageTextComplete));
    },
    getMessageTextComplete: function(strMsg) {
        this.sendMail(strMsg);
    },
    sendMail: function(strMsg) {
        Rebound.GlobalTrader.Site.WebServices.NotifyMessage("", this._intMailGroupNo, $R_RES.CompanyApproveSubject, strMsg, this._intCompanyID, Function.createDelegate(this, this.sendMailComplete));
    },
    sendMailComplete: function() {
        // this.finishedForm();
    }
    //[001] code end

};

Rebound.GlobalTrader.Site.Controls.Forms.CompanySalesInfo_Edit.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CompanySalesInfo_Edit", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
