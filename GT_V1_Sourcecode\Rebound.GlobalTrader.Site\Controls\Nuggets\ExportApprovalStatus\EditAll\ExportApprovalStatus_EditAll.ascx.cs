//[001]  A<PERSON><PERSON><PERSON>                     Date: 19-04-2022                        Add new edit all control to edit all requests..
using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.Forms
{
    public partial class ExportApprovalStatus_EditAll : Base
    {

        #region Properties
        private int _intPurchaseOrderID = -1;
        public int PurchaseOrderID
        {
            get { return _intPurchaseOrderID; }
            set { _intPurchaseOrderID = value; }
        }
        private string _strTitleMessage;
        public string TitleMessage
        {
            get { return _strTitleMessage; }
            set { _strTitleMessage = value; }
        }

        #endregion

        #region Overrides

        /// <summary>
        /// OnInit
        /// </summary>
        /// <param name="e"></param>
        protected override void OnInit(EventArgs e)
        {
            base.OnInit(e);
            TitleText = Functions.GetGlobalResource("FormTitles", "ExportApproval_EditAll");
            AddScriptReference("Controls.Nuggets.ExportApprovalStatus.EditAll.ExportApprovalStatus_EditAll.js");
            if (_objQSManager.PurchaseOrderID > 0) _intPurchaseOrderID = _objQSManager.PurchaseOrderID;
            _strTitleMessage = Functions.GetGlobalResource("FormTitles", "ExportApproval_EditAll");
        }

        /// <summary>
        /// OnPreRender
        /// </summary>
        /// <param name="e"></param>
        protected override void OnPreRender(EventArgs e)
        {
            WireUpControls();
            SetupScriptDescriptors();
            base.OnPreRender(e);
        }

        #endregion

        /// <summary>
        /// Setup Script Descriptors
        /// </summary>
        private void SetupScriptDescriptors()
        {
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Forms.ExportApprovalStatus_EditAll", ctlDesignBase.ClientID);
            _scScriptControlDescriptor.AddProperty("strTitleMessage", _strTitleMessage);
            _scScriptControlDescriptor.AddProperty("intPurchaseOrderID", _intPurchaseOrderID);
        }
        private void WireUpControls()
        {

        }
    }
}
