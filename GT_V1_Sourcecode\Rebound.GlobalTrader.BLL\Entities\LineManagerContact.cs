﻿//Marker     Changed by         Date          Remarks
//[001]      <PERSON><PERSON><PERSON><PERSON>     28/01/2022    Added class for LineManagerContact.
using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using Rebound.GlobalTrader.DAL;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.BLL
{
    public partial class LineManagerContact : BizObject
    {
        #region Properties
        /// <summary>
        /// LineManagerContactId
        /// </summary>
        public System.Int32 LineManagerContactId { get; set; }
        /// <summary>
        /// LineManagerContactName
        /// </summary>
        public System.String LineManagerContactName { get; set; }

        #endregion

        #region Methods
        /// <summary>
        /// DropDown
        /// Calls [usp_dropdown_LineManagerContacts]
        /// </summary>
        public static List<LineManagerContact> DropDown(System.Int32? BuyerId, System.Int32? ClientNo, System.Boolean? UpdateManager)
        {
            List<LineManagerContactDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.LineManagerContact.DropDown(BuyerId, ClientNo, UpdateManager);
            if (lstDetails == null)
            {
                return new List<LineManagerContact>();
            }
            else
            {
                List<LineManagerContact> lst = new List<LineManagerContact>();
                foreach (LineManagerContactDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.LineManagerContact obj = new Rebound.GlobalTrader.BLL.LineManagerContact();
                    obj.LineManagerContactId = objDetails.LineManagerContactId;
                    obj.LineManagerContactName = objDetails.LineManagerContactName;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }





        #endregion
    }
}
