using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Collections;

namespace Rebound.GlobalTrader.Site.Controls.AutoSearch {
	[DefaultProperty("")]
	[ToolboxData("<{0}:Manufacturers runat=server></{0}:Manufacturers>")]
	public class ManufacturersGroup : Base {


		#region Properties

		/// <summary>
		/// Manufacturer Dropdown list show active and inactive data
		/// </summary>
		//private bool _showInActive = true;
		//public bool ShowInactive
		//{
		//    get { return _showInActive; }
		//    set { _showInActive = value; }
		//}

		#endregion

		#region Overrides
		/// <summary>
		/// OnInit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
			AddScriptReference("Controls.AutoSearch.ManufacturersGroup.ManufacturersGroup.js");
			SetAutoSearchType("ManufacturersGroup");
		}

		protected override void OnLoad(EventArgs e) {
			CharactersToEnterBeforeSearch = 2;
			//ID = "txtManufacturersGroup";
			//RelatedTextBoxID = "txtManufacturersGroup";
			//IncludeInactive = false;
			base.OnLoad(e);
		}

		/// <summary>
		/// OnPreRender
		/// </summary>
		/// <param name="e"></param>
		protected override void OnPreRender(EventArgs e) {
			SetupScriptDescriptors();
			base.OnPreRender(e);
		}
		#endregion

		/// <summary>
		/// SetupScriptDescriptors
		/// </summary>
		private void SetupScriptDescriptors() {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.AutoSearch.ManufacturersGroup", ClientID);
			//_scScriptControlDescriptor.AddProperty("ShowInactive", IncludeInactive);
			_scScriptControlDescriptor.AddProperty("blnIsSelectionAllowed", IsSelectionAllowed);
		}
	}
}