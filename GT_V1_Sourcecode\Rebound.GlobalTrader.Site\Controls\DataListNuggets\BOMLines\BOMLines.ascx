<%--
 Marker     changed by      date           Remarks
[001]       <PERSON><PERSON>   12/09/2018    Add Header, Detail radio export Csv button
--%>

<%@ Control Language="C#" CodeBehind="BOMLines.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.DataListNuggets.BOMLines" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" BoxType="List">
    <%--[001] code start--%>
    <Links>
        <ReboundUI:IconButton ID="ibtnExportCSV" runat="server" Style="margin-left:8px;" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="ExportToExcel" />
    </Links>
    <%--[001] code end--%>

    <Filters>
        <ReboundUI_DataListNugget:Filter ID="ctlFilter" runat="server">

            <FieldsLeft>
                <%--[001] code start--%>
                <ReboundUI_Form:FormField runat="server">
                    <Field>
                        <asp:RadioButtonList ID="radHeaderDetail" name="radioList" runat="server" RepeatDirection="Horizontal">
                            <asp:ListItem Text="Header" Value="Header" />
                            <asp:ListItem Text="Detail" Value="Detail" />
                        </asp:RadioButtonList>
                    </Field>
                </ReboundUI_Form:FormField>
                <%--[001] code end--%>

                <ReboundUI_FilterDataItemRow:TextBox ID="ctlCode" runat="server" ResourceTitle="Code"
                    FilterField="Code" />
                <ReboundUI_FilterDataItemRow:DropDown ID="ctlStatus" runat="server" DropDownType="BomStatus"
                    ResourceTitle="Status" FilterField="BomStatus" DropDownAssembly="Rebound.GlobalTrader.Site" />

                <ReboundUI_FilterDataItemRow:DropDown ID="ctlSalesperson" runat="server" DropDownAssembly="Rebound.GlobalTrader.Site" DropDownType="PoHubBuyer" ResourceTitle="AssignedUser" FilterField="PoHubBuyer" />
           
                <%--<ReboundUI_FilterDataItemRow:DropDown ID="ddlSalesperson" runat="server" FieldID="ddlSalesperson" ResourceTitle="Buyer"/>	--%>
                <ReboundUI_FilterDataItemRow:DropDown ID="ctlClient" runat="server" DropDownType="ClientHubRFQ"
                    ResourceTitle="Client" FilterField="Client" DropDownAssembly="Rebound.GlobalTrader.Site" />
                <ReboundUI_FilterDataItemRow:DropDown ID="ctlClientSalesperson" runat="server" DropDownAssembly="Rebound.GlobalTrader.Site"
                    DropDownType="Employee" ResourceTitle="SalesPerson" FilterField="SalesPerson" />
                 <ReboundUI_FilterDataItemRow:DropDown ID="ctlcomType" runat="server" DropDownAssembly="Rebound.GlobalTrader.Site"
                    DropDownType="CompanyType" ResourceTitle="CompanyType" FilterField="CompanyType" />
              
            </FieldsLeft>
            
            <FieldsRight>
                <ReboundUI_FilterDataItemRow:TextBox ID="ctlName" runat="server" ResourceTitle="Name"
                    FilterField="Name" />


                <ReboundUI_FilterDataItemRow:TextBox ID="ctlManufacturer" runat="server" ResourceTitle="Manufacturer"
                    FilterField="Manufacturer" />
                <ReboundUI_FilterDataItemRow:TextBox ID="ctlPartNumber" runat="server" ResourceTitle="PartNo"
                    FilterField="Part" />
                BOMLines

                <ReboundUI_FilterDataItemRow:DropDown ID="ctlDivision" runat="server" DropDownType="Division" ResourceTitle="Division" FilterField="Division" DropDownAssembly="Rebound.GlobalTrader.Site" />
                    
                <%--[001] code start--%>
                <ReboundUI_FilterDataItemRow:DateSelect ID="ctlStartDate" runat="server" ResourceTitle="ReceivedStartDate" FilterField="StartDate" />
                <ReboundUI_FilterDataItemRow:DateSelect ID="ctlEndDate" runat="server" ResourceTitle="ReceivedEndDate" FilterField="EndDate" />

                   <ReboundUI_FilterDataItemRow:DateSelect ID="ctlRequiredStartDate" runat="server" ResourceTitle="RequiredStartDate" FilterField="RequiredStartDate" />
                <ReboundUI_FilterDataItemRow:DateSelect ID="ctlRequiredEndDate" runat="server" ResourceTitle="RequiredEndDate" FilterField="RequiredEndDate" />
                <%--[001] code end--%>
                <ReboundUI_FilterDataItemRow:DropDown ID="ctlAS6081Required" DropDownAssembly="Rebound.GlobalTrader.Site" DropDownType="CounterfeitElectronicParts" runat="server" ResourceTitle="AS6081Filter" FilterField="AS6081Required" />
                <<%--/FieldsLeft>--%><%--[001]--%>
            </FieldsRight>
        </ReboundUI_DataListNugget:Filter>

    </Filters>
    <%--<Content>
          <asp:Panel ID="pnlCustReq" runat="server">
               <ReboundUI:FlexiDataTable ID="Table" runat="server" PanelHeight="250" />data:image/svg+xml;base64,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
                    <ReboundUI:FlexiDataTable ID="tblCustReq" runat="server" PanelHeight="250" />
          </asp:Panel>
         </Content>--%>
</ReboundUI_Nugget:DesignBase>
