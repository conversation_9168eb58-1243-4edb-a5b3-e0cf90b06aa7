﻿  /*  
===========================================================================================  
TASK         UPDATED BY       DATE          ACTION    DESCRIPTION  
[US-217008]  Cuong DoX	      15-Nov-2024   Create    SupportTeamMemberNo  
===========================================================================================  
*/ 
CREATE OR ALTER VIEW [dbo].[vwInternalPurchaseOrder]                                    
--******************************************************************************************                                    
--* SK 10.04.2010:                                    
--* - Add ApprovedBy info                                    
--*                                    
--* SK 27.01.2010:                                    
--* - Add column CompanyPORating                                    
--*                                    
--* SK 05.11.2009:                                    
--* - Include new column Incoterms and description                                    
--*                                     
--* SK 30.06.2009:                                    
--* - adjusted to use DateReceived (ex GI) or current date for getting tax rates                                    
--*   as this is a PO it's possible that there could be more than one GI - so use the latest                                    
--*                                    
--* RP 01.06.2009                                    
--* - Remove default for ExpediteDate                                    
--******************************************************************************************                                    
AS                                      
SELECT  ipo.InternalPurchaseOrderId                                    
      , ipo.InternalPurchaseOrderNumber                             
      ,ipo.PurchaseOrderNo                               
      , ipo.ClientNo                                    
      , ipo.CompanyNo                                    
      , co.CompanyName                                    
      , ipo.ContactNo                                    
      , cn.ContactName                                    
      , ipo.DateOrdered                                    
      , ipo.WarehouseNo                                    
      , wh.WarehouseName                                    
      , ipo.CurrencyNo                                    
      , cu.CurrencyCode                                    
      , cu.CurrencyDescription                                    
      , ipo.Buyer                                    
      , lg.EmployeeName AS BuyerName                                    
      , po.ShipViaNo                                    
      , sh.ShipViaName                                    
      , po.Account                                    
      , ipo.TermsNo                                    
      , tm.TermsName                                    
      , po.ExpediteNotes                                    
      , po.ExpediteDate                                    
      , ipo.TotalShipInCost                                    
      , ipo.DivisionNo                                    
      , dv.DivisionName                                    
      , ipo.TaxNo                                    
      , tx.TaxName                                    
      , ipo.Notes                                    
      , ipo.Instructions                                    
      , ipo.Paid                                    
      , ipo.Confirmed                                    
      , ipo.ImportCountryNo                                    
      , cy.CountryName AS ImportCountryName                                    
      , cy.[ShippingCost] AS ImportCountryShippingCost                                    
      , ipo.FreeOnBoard                                    
      , (SELECT sum(isnull((z.Price * y.Quantity), 0))                                    
         FROM   dbo.tbInternalPurchaseOrderLine z                            
         JOIN dbo.tbPurchaseOrderLine  y  on z.PurchaseOrderLineNo=y.PurchaseOrderLineId                              
         WHERE  z.InternalPurchaseOrderNo = ipo.InternalPurchaseOrderId                                    
        ) AS PurchaseOrderValue                                    
   , ipo.Closed           
      , ipo.UpdatedBy                                    
      , ipo.DLUP              
      , dbo.ufn_get_taxrate(ipo.TaxNo, ipo.ClientNo, isnull( gi.DateReceived, getdate())) AS TaxRate              
      , dbo.ufn_get_purchaseOrder_statusNo(ipo.PurchaseOrderNo) AS StatusNo                                    
   , po.IncotermNo                                    
   , inc.Name AS IncotermName                                       
   , co.PORating AS CompanyPORating                    
      , ipo.ApprovedBy                                    
      , ipo.DateApproved                                    
      , lga.EmployeeName AS Approver                                    
      , po.AirWayBillPO                               
      ,co.ERAIMember                              
      ,co.ERAIReported                              
      ,co.SupplierCode                              
      ,ipo.RegionNo                
      ,po.Buyer as poBuyer                 
      ,po.PurchaseOrderNumber                    
      ,tms.TermsName as PoTermsName          
  ,    tms.IsApplyPOBankFee               
   , cu.GlobalCurrencyNo            
   ,ipo.DivisionHeaderNo             
  ,dh.DivisionName as DivisionHeaderName    
  , CASE WHEN ISNULL(ipo.AS6081,0)=1 THEN 'Yes' ELSE 'No' END AS AS6081
  ,ipo.SupportTeamMemberNo
 , stm.EmployeeName AS SupportTeamMemberName 
FROM    dbo.tbInternalPurchaseOrder ipo                           
JOIN    dbo.tbPurchaseOrder po  ON po.PurchaseOrderId = ipo.PurchaseOrderNo                                   
JOIN    dbo.tbCompany co ON ipo.CompanyNo = co.CompanyId                                    
LEFT JOIN dbo.tbContact cn ON ipo.ContactNo = cn.ContactId                                    
LEFT JOIN    dbo.tbCurrency cu ON ipo.CurrencyNo = cu.CurrencyId                                  
LEFT JOIN dbo.tbDivision dv ON ipo.DivisionNo = dv.DivisionId                                    
LEFT JOIN dbo.tbLogin lg ON ipo.Buyer = lg.LoginId                                    
LEFT JOIN    dbo.tbTerms tm ON ipo.TermsNo = tm.TermsId                                    
LEFT JOIN    dbo.tbTax tx ON ipo.TaxNo = tx.TaxId                                    
LEFT JOIN dbo.tbWarehouse wh ON ipo.WarehouseNo = wh.WarehouseId                                    
LEFT JOIN dbo.tbCountry cy ON ipo.ImportCountryNo = cy.CountryId                                    
LEFT JOIN dbo.tbShipVia sh ON po.ShipViaNo = sh.ShipViaId                                    
LEFT JOIN dbo.tbGoodsIn gi ON ipo.InternalPurchaseOrderId = gi.PurchaseOrderNo                                    
         AND gi.GoodsInId = (SELECT TOP 1 gi_ss.GoodsInId                                    
             FROM   dbo.tbGoodsIn gi_ss                                    
             WHERE  ipo.InternalPurchaseOrderId = gi_ss.PurchaseOrderNo                                    
             ORDER BY gi_ss.DateReceived desc, gi_ss.GoodsInId desc)                                    
LEFT JOIN dbo.tbIncoterm inc ON po.IncotermNo = inc.IncotermId                                    
LEFT JOIN dbo.tbLogin lga ON ipo.ApprovedBy = lga.LoginId              
left Join tbCompany c on po.CompanyNo=  c.CompanyId            
left join tbTerms tms on c.POTermsNo=tms.TermsId          
LEFT JOIN dbo.tbDivision dh ON ipo.DivisionHeaderNo = dh.DivisionId 
LEFT JOIN dbo.tbLogin stm ON ipo.SupportTeamMemberNo = stm.LoginId                     
