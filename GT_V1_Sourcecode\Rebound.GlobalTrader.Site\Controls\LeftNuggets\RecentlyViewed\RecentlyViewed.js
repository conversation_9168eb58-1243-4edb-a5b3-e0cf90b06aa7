Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.LeftNuggets");Rebound.GlobalTrader.Site.Controls.LeftNuggets.RecentlyViewed=function(n){Rebound.GlobalTrader.Site.Controls.LeftNuggets.RecentlyViewed.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.LeftNuggets.RecentlyViewed.prototype={get_pnlItems:function(){return this._pnlItems},set_pnlItems:function(n){this._pnlItems!==n&&(this._pnlItems=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.LeftNuggets.RecentlyViewed.callBaseMethod(this,"initialize")},dispose:function(){this.isDisposed||(this._pnlItems=null,Rebound.GlobalTrader.Site.Controls.LeftNuggets.RecentlyViewed.callBaseMethod(this,"dispose"))},addItem:function(n,t){n||(n=document.URL);Rebound.GlobalTrader.Site.WebServices.AddToRecentlyViewedList(this._element.id,n,t,Function.createDelegate(this,this.updateItems))},updateItems:function(n){n&&$R_FN.setInnerHTML(this._pnlItems,n)},refresh:function(){Rebound.GlobalTrader.Site.WebServices.RefreshRecentlyViewedList(this._element.id,Function.createDelegate(this,this.updateItems))},toggleLock:function(n,t){var i=$get(String.format("{0}_item{1}",this._element.id,n));i&&(Rebound.GlobalTrader.Site.WebServices.LockRecentlyViewedItem(n,t,i.getAttribute("bgt_locked")=="false",Function.createDelegate(this,this.toggleLockComplete)),i=null)},toggleLockComplete:function(n){var t=$get(String.format("{0}_item{1}",this._element.id,n));t&&(t.getAttribute("bgt_locked")=="true"?(t.setAttribute("bgt_locked","false"),t.className="recentlyViewedLockOff"):(t.setAttribute("bgt_locked","true"),t.className="recentlyViewedLockOn"),t=null)}};Rebound.GlobalTrader.Site.Controls.LeftNuggets.RecentlyViewed.registerClass("Rebound.GlobalTrader.Site.Controls.LeftNuggets.RecentlyViewed",Rebound.GlobalTrader.Site.Controls.LeftNuggets.Base);