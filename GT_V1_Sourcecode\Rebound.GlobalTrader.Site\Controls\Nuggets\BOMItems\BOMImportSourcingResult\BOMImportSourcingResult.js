Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.BOMImportSourcingResult=function(n){Rebound.GlobalTrader.Site.Controls.Forms.BOMImportSourcingResult.initializeBase(this,[n]);this._intBOMID=-1};Rebound.GlobalTrader.Site.Controls.Forms.BOMImportSourcingResult.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.BOMImportSourcingResult.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown));this.addCancel(Function.createDelegate(this,this.cancelClicked))},formShown:function(){var n,t,i,r;this._blnFirstTimeShown?($("#lblTotalRecordCount").text(""),$("#lblTotalRecordMatchCount").text(""),$("#lblTotalRecordNotMatchCount").text(""),$("#lblTotalRecordCount2").text(""),$("#lblTotalRecordMatchCount2").text(""),$("#lblTotalRecordNotMatchCount2").text(""),GetDataGridDataLeft(),GetDataGridDataRight(),n=$("#tableBomNotMacthDataLeft").dataTable(),t=n.fnGetData(),t.length==0&&GetDistoryLeftTable(),i=$("#tableBomNotMacthDataRight").dataTable(),r=i.fnGetData(),r.length==0&&GetDistoryRightTable(),this.addCancel(Function.createDelegate(this,this.cancelClicked))):($("#lblTotalRecordCount").text(""),$("#lblTotalRecordMatchCount").text(""),$("#lblTotalRecordNotMatchCount").text(""),$("#lblTotalRecordCount2").text(""),$("#lblTotalRecordMatchCount2").text(""),$("#lblTotalRecordNotMatchCount2").text(""),GetDataGridDataLeft(),GetDataGridDataRight(),n=$("#tableBomNotMacthDataLeft").dataTable(),t=n.fnGetData(),t.length==0&&GetDistoryLeftTable(),i=$("#tableBomNotMacthDataRight").dataTable(),r=i.fnGetData(),r.length==0&&GetDistoryRightTable())},dispose:function(){this.isDisposed||Rebound.GlobalTrader.Site.Controls.Forms.BOMImportSourcingResult.callBaseMethod(this,"dispose")},OfferimpotExcelData:function(n,t,i,r){$("#divLoader").show();var u=new Rebound.GlobalTrader.Site.Data;u._intTimeoutMilliseconds=2e5;u.set_PathToData("controls/Nuggets/BOMItems/BOMImportSourcingResult");u.set_DataObject("BOMImportSourcingResult");u.set_DataAction("ImportExcelData");u.addParameter("originalFilename",n);u.addParameter("generatedFilename",t);u.addParameter("BOMId",this._intBOMID);u.addParameter("SelectedClientType",r);u.addParameter("ColumnHeader",i);u.addDataOK(Function.createDelegate(this,this.importExcelDataOK));u.addError(Function.createDelegate(this,this.importExcelDataError));u.addTimeout(Function.createDelegate(this,this.importExcelDataError));$R_DQ.addToQueue(u);$R_DQ.processQueue();u=null},importExcelDataOK:function(n){flogId=n._result.Result;BomGridData.call(this);$("#divLoader").hide()},importExcelDataError:function(n){alert(n._errorMessage.split("<br/>")[0]);$("#divLoader").hide()},cancelClicked:function(){$("#divepo1").show();$("#divepo3").show();$("#hypShowHideepo1").val("-");$("#hypShowHideepo3").val("-");RowCount1=$("#tableBomNotMacthDataLeft tr").length;RowCount1!=0&&GetDistoryLeftTable();RowCount2=$("#tableBomNotMacthDataRight tr").length;RowCount2!=0&&GetDistoryRightTable()}};Rebound.GlobalTrader.Site.Controls.Forms.BOMImportSourcingResult.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.BOMImportSourcingResult",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);