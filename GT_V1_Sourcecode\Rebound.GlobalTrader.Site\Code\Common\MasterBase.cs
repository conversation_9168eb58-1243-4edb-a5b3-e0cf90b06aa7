using System;
using System.Collections.Generic;
using System.Text;
using System.Web.UI.HtmlControls;
using System.Web.UI;
using Rebound.GlobalTrader.Site.Controls;
using System.Web.UI.WebControls;
using System.Web;

namespace Rebound.GlobalTrader.Site.Masters {
	public class Base : System.Web.UI.MasterPage {

		#region Controls

		protected Rebound.GlobalTrader.Site.Site _objSite = Rebound.GlobalTrader.Site.Site.GetInstance();
		protected HtmlHead hdHead;
		protected HtmlGenericControl bdyBody;
		protected HtmlForm frmForm;
		protected ScriptManager ajaxScriptManager;
		protected LanguageSwitch ctlLanguageSwitch;
		protected Button FakeTarget;
		protected AjaxControlToolkit.AnimationExtender aex;
		protected Controls.TitleBar ctlTitleBar;
		protected Controls.TopMenu ctlTopMenu;
		protected Controls.Breadcrumb ctlBreadcrumb;
		protected ContentPlaceHolder cphSubTitleBarButtons;
		protected Panel pnlMainArea;
		protected Panel pnlShadow;
		protected Image imgShadow;
		protected Panel pnlLeft;
		protected Panel pnlLeftContent;
		protected ContentPlaceHolder cphLeft;
		protected Panel pnlLeftButton;
		protected HtmlAnchor ancCloseLeft;
		protected Image imgCloseLeft;
		protected Panel pnlRight;
		protected Panel pnlNewMessages;
		protected Label lblNewMessagesTitle;
		protected Controls.FlexiDataTable tblNewMessages;
		protected HyperLink hypCloseNewMessages;
		protected ContentPlaceHolder cphMain;
		protected ContentPlaceHolder cphFinalScript;
		protected Controls.FlexiDataTable tblAlerts;
		protected Image imgReboundLogo;
		protected string _strBackgroundImages;
		protected IconButton ibtnOpenItem;
		protected IconButton ibtnSnooze;
		protected IconButton ibtnDismiss;
		protected Pages.ContentControl ctlContentControl;
		protected Panel pnlToDoAlert;
		protected PlaceHolder plhMasterPageScripts;
		protected Controls.DropDowns.ToDoSnoozeTime ddlSnoozeTime;
		protected Controls.ToolTip ctlToolTip;
		protected Literal litPreloadImages;
		protected StringBuilder sbPreloadImages = new StringBuilder("");
        //Espire:14 Nov 2019: Implement SSO
        protected Controls.DropDowns.ClientByMaster ddlClientByMaster;

        #endregion

        #region Properties

        /// <summary>
        /// Which item on the top menu is selected
        /// </summary>
        private int _intSelectedSiteSectionID;
		public int SelectedSiteSectionID {
			get { return _intSelectedSiteSectionID; }
			set { _intSelectedSiteSectionID = value; }
		}

		/// <summary>
		/// What's the page ID?
		/// </summary>
		private SitePage _objCallingSitePage;
		public SitePage CallingSitePage {
			get { return _objCallingSitePage; }
			set { _objCallingSitePage = value; }
		}

		/// <summary>
		/// Should the page check for messages?
		/// </summary>
		private bool _blnShouldCheckForMessages = true;
		public bool ShouldCheckForMailMessages {
			get { return _blnShouldCheckForMessages; }
			set { _blnShouldCheckForMessages = value; }
		}

		/// <summary>
		/// The client id of the Html Form
		/// </summary>
		public string FormClientID {
			get { return frmForm.ClientID; }
		}

		protected bool _blnConfigurationIsDebug;
		public bool ConfigurationIsDebug {
			get { return _blnConfigurationIsDebug; }
			set { _blnConfigurationIsDebug = value; }
		}

		public string PageControlClientID {
			get { return ctlContentControl.ClientID; }
		}

		#endregion

		#region Overrides

		protected override void OnLoad(EventArgs e) {

			//pass page type to TitleBar (for context-sensitive help)
			ctlTitleBar.CallingPageType = _objCallingSitePage;

			//setup messages table
			lblNewMessagesTitle.Text = Functions.GetGlobalResource("Messages", "NewMailMessages");
			tblNewMessages.Columns.Add(new FlexiDataColumn("From", WidthManager.GetWidth(WidthManager.ColumnWidth.ContactName)));
			tblNewMessages.Columns.Add(new FlexiDataColumn("Subject"));
			tblNewMessages.Columns.Add(new FlexiDataColumn("Date", WidthManager.GetWidth(WidthManager.ColumnWidth.DateAndTime)));

			//setup alerts table
			tblAlerts.Columns.Add(new FlexiDataColumn("Subject"));
			tblAlerts.Columns.Add(new FlexiDataColumn("DueDate", WidthManager.GetWidth(WidthManager.ColumnWidth.DateAndTime)));

			ctlTopMenu.SelectedSiteSectionID = this._intSelectedSiteSectionID;
			SetupTopMenu();
			if (!IsPostBack) {
				if (SessionManager.LeftPanelVisible == null) SessionManager.LeftPanelVisible = true;
				SetInitialLeftPanelState();
			}

			//set configuration of scripts
			if (_blnConfigurationIsDebug) {
				ajaxScriptManager.ScriptMode = ScriptMode.Debug;
			} else {
				ajaxScriptManager.ScriptMode = ScriptMode.Release;
			}

			//setup content page control with common Client IDs
			ctlContentControl.bdyBody_ClientID = bdyBody.ClientID;
			ctlContentControl.ConfigurationIsDebug = _blnConfigurationIsDebug;
			ctlContentControl.CloseLeft_ClientID = ibtnDismiss.ClientID;
			ctlContentControl.CheckMessages = _blnShouldCheckForMessages;
			ctlContentControl.tblAlerts_ClientID = tblAlerts.ClientID;
			ctlContentControl.ancCloseLeft_ClientID = ancCloseLeft.ClientID;
			ctlContentControl.ibtnOpenItem_ClientID = ibtnOpenItem.ClientID;
			ctlContentControl.ibtnDismiss_ClientID = ibtnDismiss.ClientID;
			ctlContentControl.ibtnSnooze_ClientID = ibtnSnooze.ClientID;
			ctlContentControl.hypCloseNewMessages_ClientID = hypCloseNewMessages.ClientID;
			ctlContentControl.pnlShadow_ClientID = pnlShadow.ClientID;
			ctlContentControl.pnlLeftContent_ClientID = pnlLeftContent.ClientID;
			ctlContentControl.pnlLeftButton_ClientID = pnlLeftButton.ClientID;
			ctlContentControl.pnlNewMessages_ClientID = pnlNewMessages.ClientID;
			ctlContentControl.lblNewMessagesTitle_ClientID = lblNewMessagesTitle.ClientID;
			ctlContentControl.tblNewMessages_ClientID = tblNewMessages.ClientID;
			ctlContentControl.pnlToDoAlert_ClientID = pnlToDoAlert.ClientID;
			ctlContentControl.pnlMainArea_ClientID = pnlMainArea.ClientID;
			ctlContentControl.pnlTitleBar_ClientID = ctlTitleBar.ClientID;
			ctlContentControl.ddlSnoozeTime_ClientID = ddlSnoozeTime.ClientID;
			ctlContentControl.ctlToolTip_ClientID = ctlToolTip.ClientID;
            ctlContentControl.ddlClientByMaster_ClientID = ddlClientByMaster.ClientID;

            //add master page javascript
            StringBuilder sbScript = new StringBuilder("");
			sbScript.AppendLine("<script language=\"javascript\" type=\"text/javascript\">");
			sbScript.AppendLine("Sys.Application.add_load(function() {");
			sbScript.AppendLine(String.Format(@"$R_PAGE = $find(""{0}"");", PageControlClientID));
			sbScript.AppendLine("$R_PAGE.goInit();");
			sbScript.AppendLine("});");
			sbScript.AppendLine("</script>");
			ControlBuilders.CreateLiteralInsideParent(plhMasterPageScripts, sbScript.ToString());
			sbScript = null;
			base.OnLoad(e);
		}

		protected override void Render(HtmlTextWriter writer) {
			//litLogoutHyperLink.Text = ctlTitleBar.LogoutHyperLink.ClientID;
			imgReboundLogo.ImageUrl = Functions.GetLocalThemeImage("structure/rebound.gif", Page.Theme);
			Functions.AddBackgroundImageToHead(hdHead, Page.Theme);
			litPreloadImages.Text = sbPreloadImages.ToString();
			base.Render(writer);
		}

		#endregion

		#region Methods

		/// <summary>
		/// Set the initial state of the left panel to on or off
		/// </summary>
		private void SetInitialLeftPanelState() {
			pnlMainArea.CssClass = (bool)SessionManager.LeftPanelVisible ? "mainArea_On" : "mainArea_Off";
			pnlLeftContent.CssClass = (bool)SessionManager.LeftPanelVisible ? "leftbarContent" : "invisible";
			pnlLeftButton.CssClass = (bool)SessionManager.LeftPanelVisible ? "leftbarButton_On" : "leftbarButton_Off";
			pnlShadow.CssClass = (bool)SessionManager.LeftPanelVisible ? "invisible" : "mainAreaShadow";
		}

		/// <summary>
		/// Setup selection on top menu
		/// </summary>
		protected virtual void SetupTopMenu() {
			//do nothing - allow subclasses to add items
		}

		/// <summary>
		/// Finds a control in the Main ContentPlaceholder
		/// </summary>
		/// <param name="strControlID">ID of control to find</param>
		public Control FindControlInContentPlaceholderMain(string strControlID) {
			return FindControlInContentPlaceholder(cphMain, strControlID);
		}

		/// <summary>
		/// Finds a control in the Left ContentPlaceholder
		/// </summary>
		/// <param name="strControlID">ID of control to find</param>
		public Control FindControlInContentPlaceholderLeft(string strControlID) {
			return FindControlInContentPlaceholder(cphLeft, strControlID);
		}

		/// <summary>
		/// Finds a control in a ContentPlaceholder
		/// </summary>
		/// <param name="cph">ContentPlaceHolder to search in</param>
		/// <param name="strControlID">ID of control to find</param>
		private Control FindControlInContentPlaceholder(ContentPlaceHolder cph, string strControlID) {
			return Functions.FindControlRecursive(cph, strControlID);
		}

		public void SetupPageScript(string strScriptClass) {
			ctlContentControl.SetupScriptDescriptor(strScriptClass);
		}

		public void AddPageScript(string strAssembly, string strRef) {
			ctlContentControl.AddScriptReference(strAssembly, strRef);
		}

		public void AddPageScriptProperty(string strElement, object objValue) {
			ctlContentControl.AddScriptProperty(strElement, objValue);
		}

		public void AddPageScriptElementProperty(string strElement, string strID) {
			ctlContentControl.AddScriptElementProperty(strElement, strID);
		}

		public void AddPageScriptComponentProperty(string strElement, string strID) {
			ctlContentControl.AddScriptComponentProperty(strElement, strID);
		}

		public void AddImageForPreload(string strImagePath) {
			sbPreloadImages.AppendFormat(@"x += 1; _imgArray[x] = new Image(); _imgArray[x].src = ""{0}"";", strImagePath);
		}

		#endregion

	}
}
