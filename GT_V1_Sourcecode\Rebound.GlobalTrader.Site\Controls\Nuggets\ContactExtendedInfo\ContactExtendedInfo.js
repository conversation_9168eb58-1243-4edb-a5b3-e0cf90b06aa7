Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");Rebound.GlobalTrader.Site.Controls.Nuggets.ContactExtendedInfo=function(n){Rebound.GlobalTrader.Site.Controls.Nuggets.ContactExtendedInfo.initializeBase(this,[n]);this._intCompanyID=-1;this._intContactID=-1};Rebound.GlobalTrader.Site.Controls.Nuggets.ContactExtendedInfo.prototype={get_intCompanyID:function(){return this._intCompanyID},set_intCompanyID:function(n){this._intCompanyID!==n&&(this._intCompanyID=n)},get_intContactID:function(){return this._intContactID},set_intContactID:function(n){this._intContactID!==n&&(this._intContactID=n)},get_ibtnEdit:function(){return this._ibtnEdit},set_ibtnEdit:function(n){this._ibtnEdit!==n&&(this._ibtnEdit=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Nuggets.ContactExtendedInfo.callBaseMethod(this,"initialize");this.addRefreshEvent(Function.createDelegate(this,this.getData));this._ibtnEdit&&($R_IBTN.addClick(this._ibtnEdit,Function.createDelegate(this,this.showEditForm)),this._frmEdit=$find(this._aryFormIDs[0]),this._frmEdit.addCancel(Function.createDelegate(this,this.cancelEdit)),this._frmEdit.addSaveComplete(Function.createDelegate(this,this.saveEditComplete)));this._blnIsNoDataFound||this._blnHasInitialData||this.getData()},dispose:function(){this.isDisposed||(this._ibtnEdit&&$R_IBTN.clearHandlers(this._ibtnEdit),this._frmEdit&&this._frmEdit.dispose(),this._ibtnEdit=null,this._frmEdit=null,this._intCompanyID=null,this._intContactID=null,Rebound.GlobalTrader.Site.Controls.Nuggets.ContactExtendedInfo.callBaseMethod(this,"dispose"))},getData:function(){this.getData_Start();var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/ContactExtendedInfo");n.set_DataObject("ContactExtendedInfo");n.set_DataAction("GetData");n.addParameter("id",this._intContactID);n.addDataOK(Function.createDelegate(this,this.getDataOK));n.addError(Function.createDelegate(this,this.getDataError));n.addTimeout(Function.createDelegate(this,this.getDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getDataOK:function(n){var t=n._result,i;for(this.setFieldValue("ctlNotes",t.Notes),this.setFieldValue("ctlGender",t.Gender),this.setFieldValue("hidGenderID",t.GenderID),this.setFieldValue("ctlBirthday",t.Birthday),this.setFieldValue("ctlMaritalStatus",t.MaritalStatus),this.setFieldValue("hidMaritalStatusID",t.MaritalStatusID),this.setFieldValue("ctlPartner",t.PartnerName),this.setFieldValue("ctlPartnerBirthday",t.PartnerBirthday),this.setFieldValue("ctlAnniversary",t.Anniversary),this.setFieldValue("ctlNumberChildren",t.NumberChildren),i=1;i<=3;i++)this.setFieldValue("ctlChild"+i,t["Child"+i]),this.setFieldValue("hidChild"+i+"Name",t["Child"+i+"Name"]),this.setFieldValue("hidChild"+i+"Sex",t["Child"+i+"SexID"]),this.setFieldValue("hidChild"+i+"Birthday",t["Child"+i+"Birthday"]);this.setFieldValue("ctlMobileTel",t.MobileTel);this.setFieldValue("ctlFavouriteSport",t.FavouriteSport);this.setFieldValue("ctlFavouriteTeam",t.FavouriteTeam);this.setFieldValue("ctlHobbies",t.Hobbies);this.setDLUP(t.DLUP);this.getDataOK_End()},getDataError:function(n){this.showError(!0,n.get_ErrorMessage())},showEditForm:function(){this._frmEdit.setFormFieldsToDefaults();this._frmEdit.setFieldValue("ctlGender",this.getFieldValue("hidGenderID"));this._frmEdit.setFieldValue("ctlBirthday",this.getFieldValue("ctlBirthday"));this._frmEdit.setFieldValue("ctlMaritalStatus",this.getFieldValue("hidMaritalStatusID"));this._frmEdit.setFieldValue("ctlPartner",this.getFieldValue("ctlPartner"));this._frmEdit.setFieldValue("ctlPartnerBirthday",this.getFieldValue("ctlPartnerBirthday"));this._frmEdit.setFieldValue("ctlAnniversary",this.getFieldValue("ctlAnniversary"));this._frmEdit.setFieldValue("ctlNumberChildren",this.getFieldValue("ctlNumberChildren"));for(var n=1;n<=3;n++)this._frmEdit.setFieldValue("ctlChild"+n+"Name",this.getFieldValue("hidChild"+n+"Name")),this._frmEdit.setFieldValue("ctlChild"+n+"Sex",this.getFieldValue("hidChild"+n+"Sex")),this._frmEdit.setFieldValue("ctlChild"+n+"Birthday",this.getFieldValue("hidChild"+n+"Birthday"));this._frmEdit.setFieldValue("ctlMobileTel",this.getFieldValue("ctlMobileTel"));this._frmEdit.setFieldValue("ctlFavouriteSport",this.getFieldValue("ctlFavouriteSport"));this._frmEdit.setFieldValue("ctlFavouriteTeam",this.getFieldValue("ctlFavouriteTeam"));this._frmEdit.setFieldValue("ctlHobbies",this.getFieldValue("ctlHobbies"));this._frmEdit.setFieldValue("ctlNotes",this.getFieldValue("ctlNotes"));this.showForm(this._frmEdit,!0)},hideEditForm:function(){this.showForm(this._frmEdit,!1)},cancelEdit:function(){this.hideEditForm()},saveEditComplete:function(){this.getData();this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully);this.hideEditForm()}};Rebound.GlobalTrader.Site.Controls.Nuggets.ContactExtendedInfo.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.ContactExtendedInfo",Rebound.GlobalTrader.Site.Controls.Nuggets.Base);