using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.Forms {
    public partial class BOMItems_NoBidConfirm : Base
    {
        private string _strTitle_NoBid;
        private string _strTitle_RecallNoBid;
        private Label _lblExplainNoBid;
        private Label _lblExplainRecallNoBid;

	    #region Overrides

		/// <summary>
		/// OnInit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
            TitleText = Functions.GetGlobalResource("FormTitles", "BOMItems_Save");
            _strTitle_NoBid = Functions.GetGlobalResource("FormTitles", "HUBRFQNoBid");
            _strTitle_RecallNoBid = Functions.GetGlobalResource("FormTitles", "HUBRFQRecallNoBid");
            AddScriptReference("Controls.Nuggets.BOMItems.NoBidConfirm.BOMItems_NoBidConfirm");
            WireUpControls();
		}

		/// <summary>
		/// OnPreRender
		/// </summary>
		/// <param name="e"></param>
		protected override void OnPreRender(EventArgs e) {
			SetupScriptDescriptors();
			base.OnPreRender(e);
		}

		#endregion

		/// <summary>
		/// Setup Script Descriptors
		/// </summary>
		private void SetupScriptDescriptors() {
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_NoBidConfirm", ctlDesignBase.ClientID);
            _scScriptControlDescriptor.AddProperty("strTitle_NoBid", _strTitle_NoBid);
            _scScriptControlDescriptor.AddProperty("strTitle_RecallNoBid", _strTitle_RecallNoBid);
            _scScriptControlDescriptor.AddElementProperty("lblExplainNoBid", _lblExplainNoBid.ClientID);
            _scScriptControlDescriptor.AddElementProperty("lblExplainRecallNoBid", _lblExplainRecallNoBid.ClientID);
		}
        /// <summary>
        /// Wire up controls to the ascx
        /// </summary>
        private void WireUpControls()
        {
            _lblExplainNoBid = (Label)ctlDesignBase.FindExplanationControl("lblExplainNoBid");
            _lblExplainRecallNoBid = (Label)ctlDesignBase.FindExplanationControl("lblExplainRecallNoBid");
        }


	}
}