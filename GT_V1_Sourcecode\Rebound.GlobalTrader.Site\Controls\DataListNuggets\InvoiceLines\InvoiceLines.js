Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DataListNuggets");Rebound.GlobalTrader.Site.Controls.DataListNuggets.InvoiceLines=function(n){Rebound.GlobalTrader.Site.Controls.DataListNuggets.InvoiceLines.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DataListNuggets.InvoiceLines.prototype={get_IsGlobalLogin:function(){return this._IsGlobalLogin},set_IsGlobalLogin:function(n){this._IsGlobalLogin!==n&&(this._IsGlobalLogin=n)},get_IsGSA:function(){return this._IsGSA},set_IsGSA:function(n){this._IsGSA!==n&&(this._IsGSA=n)},initialize:function(){this.addSetupDataCallEvent(Function.createDelegate(this,this.setupDataCall));this.addGetDataOKEvent(Function.createDelegate(this,this.getDataOK));this.addInitCompleteEvent(Function.createDelegate(this,this.initAfterBaseIsReady));this._strPathToData="controls/DataListNuggets/InvoiceLines";this._strDataObject="InvoiceLines";Rebound.GlobalTrader.Site.Controls.DataListNuggets.InvoiceLines.callBaseMethod(this,"initialize")},initAfterBaseIsReady:function(){this.addPageTabChangedEvent(Function.createDelegate(this,this.pageTabChanged));this.updateFilterVisibility();this.getData()},dispose:function(){this.isDisposed||(this._IsGlobalLogin=null,this._IsGSA=null,Rebound.GlobalTrader.Site.Controls.DataListNuggets.InvoiceLines.callBaseMethod(this,"dispose"))},pageTabChanged:function(){this._table._intCurrentPage=1;this._enmViewLevel=this._intCurrentTab;this.updateFilterVisibility();this.getData()},setupDataCall:function(){this._objData.addParameter("ViewLevel",this._enmViewLevel);this._objData.addParameter("IsGlobalLogin",this._IsGlobalLogin)},getDataOK:function(){for(var n,i,t=0,r=this._objResult.Results.length;t<r;t++)n=this._objResult.Results[t],i=[$RGT_nubButton_Invoice(n.ID,n.No),$R_FN.writePartNo(n.Part,$R_FN.setCleanTextValue(n.ROHS)),n.Price,n.Quantity,$R_FN.writeDoubleCellValue(n.blnMakeYellow==!0?'<span style="background-color:yellow;">'+$RGT_nubButton_Company(n.CMNo,n.CM)+"<\/span>":$RGT_nubButton_Company(n.CMNo,n.CM),$R_FN.setCleanTextValue(n.CustPONO)),$R_FN.setCleanTextValue(n.Date),$R_FN.setCleanTextValue(n.ClientName)],this._table.addRow(i,n.ID,!1),i=null,n=null},updateFilterVisibility:function(){this.getFilterField("ctlSalesmanName").show(this._enmViewLevel!=0);this.getFilterField("ctlClientName").show(this._IsGlobalLogin);this.getFilterField("ctlClientName").show(this._IsGlobalLogin||this._IsGSA)}};Rebound.GlobalTrader.Site.Controls.DataListNuggets.InvoiceLines.registerClass("Rebound.GlobalTrader.Site.Controls.DataListNuggets.InvoiceLines",Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base,Sys.IDisposable);