Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.HomeNuggets");Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyOpenSalesOrders=function(n){Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyOpenSalesOrders.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyOpenSalesOrders.prototype={get_pnlOpen:function(){return this._pnlOpen},set_pnlOpen:function(n){this._pnlOpen!==n&&(this._pnlOpen=n)},get_pnlOverdue:function(){return this._pnlOverdue},set_pnlOverdue:function(n){this._pnlOverdue!==n&&(this._pnlOverdue=n)},get_tblOpen:function(){return this._tblOpen},set_tblOpen:function(n){this._tblOpen!==n&&(this._tblOpen=n)},get_tblOverdue:function(){return this._tblOverdue},set_tblOverdue:function(n){this._tblOverdue!==n&&(this._tblOverdue=n)},get_pnlMore:function(){return this._pnlMore},set_pnlMore:function(n){this._pnlMore!==n&&(this._pnlMore=n)},get_lnkMore:function(){return this._lnkMore},set_lnkMore:function(n){this._lnkMore!==n&&(this._lnkMore=n)},get_myLoginID:function(){return this.myLoginID},set_myLoginID:function(n){this.myLoginID!==n&&(this.myLoginID=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyOpenSalesOrders.callBaseMethod(this,"initialize");this.addRefreshEvent(Function.createDelegate(this,this.getData))},dispose:function(){this.isDisposed||(this._tblOpen&&this._tblOpen.dispose(),this._tblOverdue&&this._tblOverdue.dispose(),this._pnlOpen=null,this._pnlOverdue=null,this._tblOpen=null,this._tblOverdue=null,this._pnlMore=null,Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyOpenSalesOrders.callBaseMethod(this,"dispose"))},setupLoadingState:function(){$R_FN.showElement(this._pnlMore,!1);$R_FN.showElement(this._pnlOpen,!1);$R_FN.showElement(this._pnlOverdue,!1);Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyOpenSalesOrders.callBaseMethod(this,"setupLoadingState")},showNoData:function(n){this.showContent(!0);$R_FN.showElement(this._pnlNoData,n)},getData:function(){this.setupLoadingState();this._lnkMore.href=this._intLoginID_Other>0?$RGT_gotoURL_SalesOrderBrowse(this._intLoginID_Other):$RGT_gotoURL_SalesOrderBrowse(this.myLoginID);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/HomeNuggets/MyOpenSalesOrders");n.set_DataObject("MyOpenSalesOrders");n.set_DataAction("GetData");n.addParameter("rowcount",this._intRowCount);n.addParameter("OtherLoginID",this._intLoginID_Other);n.addDataOK(Function.createDelegate(this,this.getDataComplete));n.addError(Function.createDelegate(this,this.homeNuggetDataError));n.addTimeout(Function.createDelegate(this,this.homeNuggetDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getDataComplete:function(n){var r,u,t,i;for(this.showNoData(n._result.Count==0),$R_FN.showElement(this._pnlMore,!0),r=n._result,this._tblOpen.clearTable(),i=0;i<r.OpenSO.length;i++)t=r.OpenSO[i],u=[$RGT_nubButton_SalesOrder(t.ID,t.No),$RGT_nubButton_Company(t.CMNo,t.CM),t.Due],this._tblOpen.addRow(u,null),t=null;for($R_FN.showElement(this._pnlOpen,r.OpenSO.length>0),this._tblOverdue.clearTable(),i=0;i<r.OverdueSO.length;i++)t=r.OverdueSO[i],u=["<div style='float:left'>"+$RGT_nubButton_SalesOrder(t.ID,t.No)+"<\/div>"+this.generateShipStatusContent(t.ShipStatus),$RGT_nubButton_Company(t.CMNo,t.CM),t.Due],this._tblOverdue.addRow(u,null),t=null;$R_FN.showElement(this._pnlOverdue,r.OverdueSO.length>0);this.hideLoading()},generateShipStatusContent:function(n){var t="",i;return n!="Ready to ship"&&(i="<a class='tooltip' href='#'><img src='App_Themes/Original/images/smallicons/ShipStatus.png' /><div class='popupShipStatus'>"+n+"<\/div> <\/a>",t="<div style='float:left'>"+i+"<\/div>"),t}};Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyOpenSalesOrders.registerClass("Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyOpenSalesOrders",Rebound.GlobalTrader.Site.Controls.HomeNuggets.Base,Sys.IDisposable);