
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/* 
=======================================================================================================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-202689]		Trung Pham			11-SEP-2024		CREATE			Inactivate EpoImportactivity and tbEpo record
[US-216592]		Phuc Hoang			03-Nov-2024		UPDATE			Strategic/ RL Import Utility - Allow deactivating all Stock(s) except the ones attached in Sourcing
=======================================================================================================================================================================
*/
CREATE OR ALTER PROCEDURE [dbo].[usp_Inactivate_EpoImportActivity]
	@ClientId INT =0,
	@SelectedImportId INT=0,
	@InactiveBy INT,
	@IsSuccess BIT OUTPUT,
	@OuputMessage NVARCHAR(500) OUTPUT
AS
BEGIN
	IF EXISTS (SELECT 1 FROM dbo.tbSourcingResult sr INNER JOIN BorisGlobalTraderImports.dbo.tbEpo epo ON sr.SourcingTableItemNo = epo.EpoId
		WHERE sr.SourcingTable = 'EPPH' AND epo.ImportId = @SelectedImportId)
	BEGIN
		UPDATE epo 
		SET epo.Inactive=1, epo.UpdatedBy = @InactiveBy, epo.InactiveDate=GETDATE() 
		FROM BorisGlobalTraderimports.dbo.tbEpo epo
		LEFT JOIN dbo.tbSourcingResult sr ON sr.SourcingTableItemNo = epo.EpoId AND sr.SourcingTable = 'EPPH'
		WHERE epo.ImportId = @SelectedImportId AND ISNULL(sr.SourcingResultId, 0) = 0;

		SET @OuputMessage = 'Please note some lines on the sheet you have imported have been attached to sourcing results and therefore will not be removed from the sourcing results.'
	END

	ELSE
	BEGIN
		
		UPDATE BorisGlobalTraderimports.dbo.tbEpo SET Inactive = 1, InactiveBy = @InactiveBy, InactiveDate = GETDATE() WHERE ImportId = @SelectedImportId;
		UPDATE BorisGlobalTraderimports.dbo.tbImportActivity_Epo SET Inactive = 1, InactiveBy = @InactiveBy, InactiveDate = GETDATE() WHERE ImportId = @SelectedImportId;

		SET @OuputMessage = 'File items deactivated.'
	END

	SET @IsSuccess = @@ROWCOUNT;

	SELECT @IsSuccess, @OuputMessage
END
GO


