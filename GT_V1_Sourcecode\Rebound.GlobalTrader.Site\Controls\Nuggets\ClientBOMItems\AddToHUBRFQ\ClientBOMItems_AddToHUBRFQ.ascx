<%@ Control Language="C#" CodeBehind="ClientBOMItems_AddToHUBRFQ.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.ClientBOMItems_AddToHUBRFQ" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="true" ShowQuickHelp="false" NumberOfSteps="3" MultiStepControlID="ctlMultiStep">
	<Links>
		<ReboundUI:IconButton ID="ibtnSave" runat="server" IconGroup="Nugget" IconTitleResource="Save" IconCSSType="Save" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
		<ReboundUI:IconButton ID="ibtnCancel" runat="server" IconGroup="Nugget" IconTitleResource="Cancel" IconCSSType="Cancel" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
	</Links>
	
	<Content>
	
		
		<ReboundUI_Table:Form id="frmStep2" runat="server">
                <ReboundUI_Form:FormField ID="ctlBOMHeader" runat="server" FieldID="ddlBOM" ResourceTitle="IPOBOM">
                <Field>
                    <ReboundDropDown:BOM ID="ddlBOM" runat="server" />
                </Field>
            </ReboundUI_Form:FormField>
		</ReboundUI_Table:Form>
		
		
	</Content>
</ReboundUI_Form:DesignBase>
