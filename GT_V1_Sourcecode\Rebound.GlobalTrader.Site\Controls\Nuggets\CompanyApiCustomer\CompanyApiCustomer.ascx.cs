using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site.Controls;
using Rebound.GlobalTrader.Site;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets {
    public partial class CompanyApiCustomer : Base
    {

		#region Locals

		protected IconButton _ibtnAdd;
        protected IconButton _ibtnBomMapping;
		protected IconButton _ibtnSupplierImport;
		protected IconButton _ibtnEdit;
		protected FlexiDataTable _tbl;
		
		#endregion

		#region Properties

		private int _intCompanyID = -1;
		public int CompanyID {
			get { return _intCompanyID; }
			set { _intCompanyID = value; }
		}

		private bool _blnCanAdd = true;
		public bool CanAdd {
			get { return _blnCanAdd; }
			set { _blnCanAdd = value; }
		}

		private bool _blnCanEdit = true;
		public bool CanEdit {
			get { return _blnCanEdit; }
			set { _blnCanEdit = value; }
		}
        private bool _blnCanBomMapping = true;
        public bool CanBomMapping
        {
            get { return _blnCanBomMapping; }
            set { _blnCanBomMapping = value; }
        }
		private bool _blnibtnSupplierImport = true;
		public bool CanibtnSupplierImport
		{
			get { return _blnibtnSupplierImport; }
			set { _blnibtnSupplierImport = value; }
		}


		#endregion

		#region Overrides

		/// <summary>
		/// Oninit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
			WireUpControls();
            AddScriptReference("Controls.Nuggets.CompanyApiCustomer.CompanyApiCustomer.js");
            TitleText = Functions.GetGlobalResource("Nuggets", "CompanyApiCustomer");
			if (_objQSManager.CompanyID > 0) _intCompanyID = _objQSManager.CompanyID;
			SetupTables();
		}

		protected override void OnLoad(EventArgs e) {
			base.OnLoad(e);
			
		}

		/// <summary>
		/// OnPreRender
		/// </summary>
		/// <param name="e"></param>
		protected override void OnPreRender(EventArgs e) {
			_ibtnAdd.Visible = _blnCanAdd;
			_ibtnEdit.Visible = _blnCanEdit;
			_ibtnBomMapping.Visible = _blnCanBomMapping;
			_ibtnSupplierImport.Visible = CanibtnSupplierImport;
			SetupScriptDescriptors();
			base.OnPreRender(e);
		}

		#endregion

		/// <summary>
		/// Sets up AJAX script descriptors
		/// </summary>
		private void SetupScriptDescriptors() {
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyApiCustomer", ctlDesignBase.ClientID);
			if (_blnCanAdd) _scScriptControlDescriptor.AddElementProperty("ibtnAdd", _ibtnAdd.ClientID);
			if (_blnCanEdit) _scScriptControlDescriptor.AddElementProperty("ibtnEdit", _ibtnEdit.ClientID);
            if (_blnCanBomMapping) _scScriptControlDescriptor.AddElementProperty("ibtnBomMapping", _ibtnBomMapping.ClientID);
			if (_blnCanBomMapping) _scScriptControlDescriptor.AddElementProperty("ibtnSupplierImport", _ibtnSupplierImport.ClientID);

			_scScriptControlDescriptor.AddProperty("intCompanyID", _intCompanyID);
			_scScriptControlDescriptor.AddComponentProperty("tbl", _tbl.ClientID);
			
		}

		private void SetupTables() {
            //_tbl.Columns.Add(new FlexiDataColumn("CustomerAPINo"));
			//_tbl.Columns.Add(new FlexiDataColumn("CompanyNo", Unit.Empty));
			_tbl.Columns.Add(new FlexiDataColumn("Email", Unit.Empty));
            _tbl.Columns.Add(new FlexiDataColumn("Mobile", Unit.Empty));
			_tbl.Columns.Add(new FlexiDataColumn("Contact", Unit.Empty));
			_tbl.Columns.Add(new FlexiDataColumn("InActive", Unit.Empty));

		}

		/// <summary>
		/// Wire up controls from the ascx
		/// </summary>
		private void WireUpControls() {
			_ibtnAdd = FindIconButton("ibtnAdd");
			_ibtnEdit = FindIconButton("ibtnEdit");
			_ibtnBomMapping = FindIconButton("ibtnBomMapping");
			_ibtnSupplierImport = FindIconButton("ibtnSupplierImport");
			_tbl = (FlexiDataTable)ctlDesignBase.FindContentControl("tbl");
		}
	}
}
