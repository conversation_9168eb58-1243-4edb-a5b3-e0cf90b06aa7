///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//
// RP 23.10.2009:
// - new control
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists");

Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Contacts = function(element) { 
	Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Contacts.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Contacts.prototype = {

	get_enmCompanyListType: function() { return this._enmCompanyListType; }, 	set_enmCompanyListType: function(value) { if (this._enmCompanyListType !== value)  this._enmCompanyListType = value; }, 

	initialize: function() {
		this.addGetDataOKEvent(Function.createDelegate(this, this.getDataOK));
		this._strPathToData = "controls/DataListNuggets/Contacts";
		this._strDataObject = "Contacts";
		Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Contacts.callBaseMethod(this, "initialize");
	},
	
	dispose: function() { 
		if (this.isDisposed) return;
		this._enmCompanyListType = null;
		Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Contacts.callBaseMethod(this, "dispose");
	},

	getDataOK: function() {
		for (var i = 0, l = this._objResult.Results.length; i < l; i++) {
			var row = this._objResult.Results[i];				
			this._tbl.addRow([ String.format("<a href=\"{0}\">{1}</a>", $RGT_gotoURL_Contact(row.ID, this._enmCompanyListType), $R_FN.setCleanTextValue((row.Name) ? row.Name : "???")) ]);
			strData = null; row = null;
		}
	}

};
Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Contacts.registerClass("Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Contacts", Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Base, Sys.IDisposable);
