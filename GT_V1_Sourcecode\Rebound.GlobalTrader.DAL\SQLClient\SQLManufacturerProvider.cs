﻿using System;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using System.Web.Caching;
using System.Data.Common;

namespace Rebound.GlobalTrader.DAL.SqlClient {
	public class SqlManufacturerProvider : ManufacturerProvider {
        /// <summary>
        /// AutoSearch 
		/// Calls [usp_autosearch_Manufacturer]
        /// </summary>
        public override List<ManufacturerDetails> AutoSearch(System.String nameSearch, Boolean? showInactive)
        {
			SqlConnection cn = null;
			SqlCommand cmd = null;
			try {
				cn = new SqlConnection(this.ConnectionString);
				cmd = new SqlCommand("usp_autosearch_Manufacturer", cn);
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.CommandTimeout = 60;
				cmd.Parameters.Add("@NameSearch", SqlDbType.NVarChar).Value = nameSearch;
                cmd.Parameters.Add("@ShowInactive", SqlDbType.Bit).Value = showInactive;
				cn.Open();
				DbDataReader reader = ExecuteReader(cmd);
				List<ManufacturerDetails> lst = new List<ManufacturerDetails>();
				while (reader.Read()) {
					ManufacturerDetails obj = new ManufacturerDetails();
					obj.ManufacturerId = GetReaderValue_Int32(reader, "ManufacturerId", 0);
					obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
					obj.FullName = GetReaderValue_String(reader, "FullName", "");
					lst.Add(obj);
					obj = null;
				}
				return lst;
			} catch (SqlException sqlex) {
				//LogException(sqlex);
				throw new Exception("Failed to get Manufacturers", sqlex);
			} finally {
				cmd.Dispose();
				cn.Close();
				cn.Dispose();
			}
		}

        public override List<DAL.HUBOfferImportLargeFileTempDetails> GetIncorrectMfrToOfferImport(System.Int32? clientNo, System.Int32 importFileId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_Get_IncorrectMfrToBulkOfferImport", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = clientNo;
                cmd.Parameters.Add("@ImportFileId", SqlDbType.Int).Value = importFileId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<DAL.HUBOfferImportLargeFileTempDetails> lst = new List<DAL.HUBOfferImportLargeFileTempDetails>();
                while (reader.Read())
                {
                    DAL.HUBOfferImportLargeFileTempDetails obj = new DAL.HUBOfferImportLargeFileTempDetails();
                    obj.OfferTempId = GetReaderValue_Int32(reader, "OfferTempId", 0);
                    obj.MFR = GetReaderValue_String(reader, "MFR", "");
                    obj.MFRCount = GetReaderValue_Int32(reader, "MFRCount", 0);
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Manufacturers", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// AutoSearch 
        /// Calls [usp_autosearch_Manufacturer]
        /// </summary>
        public override List<ManufacturerDetails> LyticaAutoSearch(System.String partNo, System.String nameSearch, Boolean? showInactive)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_autosearch_LyticaManufacturer", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@NameSearch", SqlDbType.NVarChar).Value = nameSearch;
                cmd.Parameters.Add("@PartNo", SqlDbType.NVarChar).Value = partNo;
                cmd.Parameters.Add("@ShowInactive", SqlDbType.Bit).Value = showInactive;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<ManufacturerDetails> lst = new List<ManufacturerDetails>();
                while (reader.Read())
                {
                    ManufacturerDetails obj = new ManufacturerDetails();
                    obj.ManufacturerId = GetReaderValue_Int32(reader, "ManufacturerId", 0);
                    obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Manufacturers", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Count Manufacturer
        /// Calls [usp_count_Manufacturer]
        /// </summary>
        public override Int32 Count() {
			SqlConnection cn = null;
			SqlCommand cmd = null;
			try {
				cn = new SqlConnection(this.ConnectionString);
				cmd = new SqlCommand("usp_count_Manufacturer", cn);
				cmd.CommandType = CommandType.StoredProcedure;
				cn.Open();
				return (Int32)ExecuteScalar(cmd);
			} catch (SqlException sqlex) {
				//LogException(sqlex);
				throw new Exception("Failed to count Manufacturer", sqlex);
			} finally {
				cmd.Dispose();
				cn.Close();
				cn.Dispose();
			}
		}
		
		
        /// <summary>
        /// DataListNugget 
		/// Calls [usp_datalistnugget_Manufacturer]
        /// </summary>
		public override List<ManufacturerDetails> DataListNugget(System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, 
                    System.String nameSearch, System.String codeSearch, System.String manufGroupCode,System.String groupName, int clientId) {
			SqlConnection cn = null;
			SqlCommand cmd = null;
			try {
				cn = new SqlConnection(this.ConnectionString);
				cmd = new SqlCommand("usp_datalistnugget_Manufacturer", cn);
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.CommandTimeout = 60;
				cmd.Parameters.Add("@OrderBy", SqlDbType.Int).Value = orderBy;
				cmd.Parameters.Add("@SortDir", SqlDbType.Int).Value = sortDir;
				cmd.Parameters.Add("@PageIndex", SqlDbType.Int).Value = pageIndex;
				cmd.Parameters.Add("@PageSize", SqlDbType.Int).Value = pageSize;
				cmd.Parameters.Add("@NameSearch", SqlDbType.NVarChar).Value = nameSearch;
				cmd.Parameters.Add("@CodeSearch", SqlDbType.NVarChar).Value = codeSearch;
                cmd.Parameters.Add("@GroupCode", SqlDbType.NVarChar).Value = manufGroupCode;
                cmd.Parameters.Add("@GroupName", SqlDbType.NVarChar).Value = groupName;
                cmd.Parameters.Add("@ClientID", SqlDbType.Int).Value = clientId;
                cn.Open();
				DbDataReader reader = ExecuteReader(cmd);
				List<ManufacturerDetails> lst = new List<ManufacturerDetails>();
				while (reader.Read()) {
					ManufacturerDetails obj = new ManufacturerDetails();
					obj.ManufacturerId = GetReaderValue_Int32(reader, "ManufacturerId", 0);
					obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
					obj.ManufacturerCode = GetReaderValue_String(reader, "ManufacturerCode", "");
					obj.URL = GetReaderValue_String(reader, "URL", "");
                    obj.Inactive = GetReaderValue_Boolean(reader, "Inactive",false);
					obj.RowCnt = GetReaderValue_NullableInt32(reader, "RowCnt", null);
                    obj.ConflictResource = GetReaderValue_String(reader, "ConflictResource", "");
                    obj.GroupName = GetReaderValue_String(reader, "GroupName", "");
                    obj.GroupCode = GetReaderValue_String(reader, "GroupCode", "");
                    obj.SystemManufacturer = GetReaderValue_Int32(reader, "SystemManufacturer", 0);
                    obj.AdvisoryNotes = GetReaderValue_String(reader, "Notes", "");
                    lst.Add(obj);
					obj = null;
				}
				return lst;
			} catch (SqlException sqlex) {
				//LogException(sqlex);
				throw new Exception("Failed to get Manufacturers", sqlex);
			} finally {
				cmd.Dispose();
				cn.Close();
				cn.Dispose();
			}
		}
		
		
		/// <summary>
		/// Delete Manufacturer
		/// Calls [usp_delete_Manufacturer]
		/// </summary>
		public override bool Delete(System.Int32? manufacturerId) {
			SqlConnection cn = null;
			SqlCommand cmd = null;
			try {
				cn = new SqlConnection(this.ConnectionString);
				cmd = new SqlCommand("usp_delete_Manufacturer", cn);
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.Parameters.Add("@ManufacturerId", SqlDbType.Int).Value = manufacturerId;
				cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
				cn.Open();
				int ret = ExecuteNonQuery(cmd);
				return (ret > 0);
			} catch (SqlException sqlex) {
				//LogException(sqlex);
				throw new Exception("Failed to delete Manufacturer", sqlex);
			} finally {
				cmd.Dispose();
				cn.Close();
				cn.Dispose();
			}
		}
		
		
        /// <summary>
        /// DropDown 
		/// Calls [usp_dropdown_Manufacturer]
        /// </summary>
		public override List<ManufacturerDetails> DropDown() {
			SqlConnection cn = null;
			SqlCommand cmd = null;
			try {
				cn = new SqlConnection(this.ConnectionString);
				cmd = new SqlCommand("usp_dropdown_Manufacturer", cn);
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.CommandTimeout = 30;
				cn.Open();
				DbDataReader reader = ExecuteReader(cmd);
				List<ManufacturerDetails> lst = new List<ManufacturerDetails>();
				while (reader.Read()) {
					ManufacturerDetails obj = new ManufacturerDetails();
					obj.ManufacturerId = GetReaderValue_Int32(reader, "ManufacturerId", 0);
					obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
					lst.Add(obj);
					obj = null;
				}
				return lst;
			} catch (SqlException sqlex) {
				//LogException(sqlex);
				throw new Exception("Failed to get Manufacturers", sqlex);
			} finally {
				cmd.Dispose();
				cn.Close();
				cn.Dispose();
			}
		}
		
		
		/// <summary>
		/// Create a new row
		/// Calls [usp_insert_Manufacturer]
		/// </summary>
		public override Int32 Insert(System.String manufacturerName, System.String notes, System.String manufacturerCode, System.Int32? updatedBy, System.String url) {
			SqlConnection cn = null;
			SqlCommand cmd = null;
			try {
				cn = new SqlConnection(this.ConnectionString);
				cmd = new SqlCommand("usp_insert_Manufacturer", cn);
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.Parameters.Add("@ManufacturerName", SqlDbType.NVarChar).Value = manufacturerName;
				cmd.Parameters.Add("@Notes", SqlDbType.NVarChar).Value = notes;
				cmd.Parameters.Add("@ManufacturerCode", SqlDbType.NVarChar).Value = manufacturerCode;
				cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
				cmd.Parameters.Add("@URL", SqlDbType.NVarChar).Value = url;
				cmd.Parameters.Add("@ManufacturerId", SqlDbType.Int).Direction = ParameterDirection.Output;
				cn.Open();
				int ret = ExecuteNonQuery(cmd);
				return (Int32)cmd.Parameters["@ManufacturerId"].Value;
			} catch (SqlException sqlex) {
				//LogException(sqlex);
				throw new Exception("Failed to insert Manufacturer", sqlex);
			} finally {
				cmd.Dispose();
				cn.Close();
				cn.Dispose();
			}
		}
		
		
        /// <summary>
        /// Get 
		/// Calls [usp_select_Manufacturer]
        /// </summary>
		public override ManufacturerDetails Get(System.Int32? manufacturerId) {
			SqlConnection cn = null;
			SqlCommand cmd = null;
			try {
				cn = new SqlConnection(this.ConnectionString);
				cmd = new SqlCommand("usp_select_Manufacturer", cn);
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.CommandTimeout = 30;
				cmd.Parameters.Add("@ManufacturerId", SqlDbType.Int).Value = manufacturerId;
				cn.Open();
				DbDataReader reader = ExecuteReader(cmd, CommandBehavior.SingleRow);
				if (reader.Read()) {
					//return GetManufacturerFromReader(reader);
					ManufacturerDetails obj = new ManufacturerDetails();
					obj.ManufacturerId = GetReaderValue_Int32(reader, "ManufacturerId", 0);
					obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
					obj.Notes = GetReaderValue_String(reader, "Notes", "");
					obj.ManufacturerCode = GetReaderValue_String(reader, "ManufacturerCode", "");
					obj.Inactive = GetReaderValue_Boolean(reader, "Inactive", false);
					obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
					obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
					obj.URL = GetReaderValue_String(reader, "URL", "");
                    obj.IsPDFAvailable = GetReaderValue_Boolean(reader, "IsPDFAvailable", false);
                    obj.ConflictResource = GetReaderValue_String(reader, "ConflictResource", "");
                    obj.SystemManufacturer = GetReaderValue_Int32(reader, "SystemManufacturer", 0);
                    obj.AdvisoryNotes = GetReaderValue_String(reader, "AdvisoryNotes", "");
                    obj.IsDisplayAdvisory = GetReaderValue_Boolean(reader, "IsDisplayAdvisory", false);
                    return obj;
				} else {
					return null;
				}
			} catch (SqlException sqlex) {
				//LogException(sqlex);
				throw new Exception("Failed to get Manufacturer", sqlex);
			} finally {
				cmd.Dispose();
				cn.Close();
				cn.Dispose();
			}
		}
		
		
        /// <summary>
        /// Update Manufacturer
		/// Calls [usp_update_Manufacturer]
        /// </summary>
        public override bool Update(System.Int32? manufacturerId,
                                    System.String manufacturerName,
                                    System.String notes,
                                    System.String manufacturerCode,
                                    System.Boolean? inactive,
                                    System.Int32? updatedBy,
                                    System.String url,
                                    System.String conflictResource,
                                    string advisoryNotes,
                                    bool? isDisplayAdvisory)
        {
			SqlConnection cn = null;
			SqlCommand cmd = null;
			try {
				cn = new SqlConnection(this.ConnectionString);
				cmd = new SqlCommand("usp_update_Manufacturer", cn);
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.Parameters.Add("@ManufacturerId", SqlDbType.Int).Value = manufacturerId;
				cmd.Parameters.Add("@ManufacturerName", SqlDbType.NVarChar).Value = manufacturerName;
				cmd.Parameters.Add("@Notes", SqlDbType.NVarChar).Value = notes;
				cmd.Parameters.Add("@ManufacturerCode", SqlDbType.NVarChar).Value = manufacturerCode;
				cmd.Parameters.Add("@Inactive", SqlDbType.Bit).Value = inactive;
				cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
				cmd.Parameters.Add("@URL", SqlDbType.NVarChar).Value = url;
                cmd.Parameters.Add("@ConflictResource", SqlDbType.NVarChar).Value = conflictResource;
                cmd.Parameters.Add("@AdvisoryNotes", SqlDbType.NVarChar).Value = advisoryNotes;
                cmd.Parameters.Add("@IsDisplayAdvisory", SqlDbType.Bit).Value = isDisplayAdvisory;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
				cn.Open();
				int ret = ExecuteNonQuery(cmd);
				return (ret > 0);
            } catch (SqlException sqlex) {
				//LogException(sqlex);
				throw new Exception("Failed to update Manufacturer", sqlex);
			} finally {
				cmd.Dispose();
				cn.Close();
				cn.Dispose();
			}
		}        

        /// <summary>
        /// Calls [usp_insert_ManufacturerPDF]
        /// </summary>
        /// <param name="ManufacturerNo"></param>
        /// <param name="Caption"></param>
        /// <param name="FileName"></param>
        /// <param name="UpdatedBy"></param>
        /// <returns></returns>
        public override Int32 Insert(System.Int32? ManufacturerNo, System.String Caption, System.String FileName, System.Int32? UpdatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_insert_ManufacturerPDF", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@ManufacturerNo", SqlDbType.Int).Value = ManufacturerNo;
                cmd.Parameters.Add("@Caption", SqlDbType.NVarChar).Value = Caption;
                cmd.Parameters.Add("@FileName", SqlDbType.NVarChar).Value = FileName;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = UpdatedBy;
                cmd.Parameters.Add("@NewId", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (Int32)cmd.Parameters["@NewId"].Value;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert manufaturer pdf", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }




        //add ihs pdf document details to db
        /// <summary>
        /// Calls [usp_insert_IHSPDFDocument]
        /// </summary>
        /// <param name="IHSPartNo"></param>
        /// <param name="Caption"></param>
        /// <param name="FileName"></param>
        /// <param name="UpdatedBy"></param>
        /// <returns></returns>
        public override Int32 InsertIHS(System.Int32? IHSPartNo, System.String Caption, System.String FileName, System.Int32? UpdatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_insert_IHSPDFDocument", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@IHSPartNo", SqlDbType.Int).Value = IHSPartNo;
                cmd.Parameters.Add("@Caption", SqlDbType.NVarChar).Value = Caption;
                cmd.Parameters.Add("@FileName", SqlDbType.NVarChar).Value = FileName;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = UpdatedBy;
                cmd.Parameters.Add("@NewId", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (Int32)cmd.Parameters["@NewId"].Value;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert ihs pdf", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }




        /// <summary>
        /// Calls [usp_insert_ManufacturerExcel]
        /// </summary>
        /// <param name="ManufacturerNo"></param>
        /// <param name="Caption"></param>
        /// <param name="FileName"></param>
        /// <param name="UpdatedBy"></param>
        /// <returns></returns>
        public override Int32 InsertExcel(System.Int32? ManufacturerNo, System.String Caption, System.String FileName, System.Int32? UpdatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_insert_ManufacturerExcel", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@ManufacturerNo", SqlDbType.Int).Value = ManufacturerNo;
                cmd.Parameters.Add("@Caption", SqlDbType.NVarChar).Value = Caption;
                cmd.Parameters.Add("@FileName", SqlDbType.NVarChar).Value = FileName;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = UpdatedBy;
                cmd.Parameters.Add("@NewId", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (Int32)cmd.Parameters["@NewId"].Value;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert manufaturer excel", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        // [006] code start
        /// <summary>
        /// GetPDFListForInvoice 
        /// Calls [usp_selectAll_PDF_for_Manufacturer]
        /// </summary>
        public override List<PDFDocumentDetails> GetPDFListForManufacturer(System.Int32? manufacturerNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_selectAll_PDF_for_Manufacturer", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@ManufacturerNo", SqlDbType.Int).Value = manufacturerNo;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<PDFDocumentDetails> lstPDF = new List<PDFDocumentDetails>();
                while (reader.Read())
                {
                    PDFDocumentDetails obj = new PDFDocumentDetails();
                    obj.PDFDocumentId = GetReaderValue_Int32(reader, "ManufacturerPDFId", 0);
                    obj.PDFDocumentRefNo = GetReaderValue_Int32(reader, "ManufactureNo", 0);
                    obj.Caption = GetReaderValue_String(reader, "Caption", "");
                    obj.FileName = GetReaderValue_String(reader, "FileName", "");
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.DLUP = GetReaderValue_NullableDateTime(reader, "DLUP", null);
                    lstPDF.Add(obj);
                    obj = null;
                }
                return lstPDF;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get PDF list for invoice", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        //code added for ihs pdf list details
        // [004] code start
        /// <summary>
        /// GetPDFListForIHS
        /// Calls [usp_selectAll_PDF_for_IHSPDFDocument]
        /// </summary>
        /// <param name="IHSPartNo"></param>
        /// <returns></returns>
        public override List<PDFDocumentDetails> GetPDFListForIHS(System.Int32? IHSPartNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_selectAll_PDF_for_IHSPDFDocument", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@IHSPartNo", SqlDbType.Int).Value = IHSPartNo;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<PDFDocumentDetails> lstPDF = new List<PDFDocumentDetails>();
                while (reader.Read())
                {
                    PDFDocumentDetails obj = new PDFDocumentDetails();
                    obj.PDFDocumentId = GetReaderValue_Int32(reader, "IHSPartPDFId", 0);
                    obj.PDFDocumentRefNo = GetReaderValue_Int32(reader, "IHSPartNo", 0);
                    obj.Caption = GetReaderValue_String(reader, "Caption", "");
                    obj.FileName = GetReaderValue_String(reader, "FileName", "");
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.DLUP = GetReaderValue_NullableDateTime(reader, "DLUP", null);
                    lstPDF.Add(obj);
                    obj = null;
                }
                return lstPDF;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get PDF list for IHS", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        //code end




        /// Calls [usp_selectAll_Excel_for_Manufacturer]
        /// </summary>
        public override List<PDFDocumentDetails> GetExcelListForManufacturer(System.Int32? manufacturerNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_selectAll_Excel_for_Manufacturer", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@ManufacturerNo", SqlDbType.Int).Value = manufacturerNo;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<PDFDocumentDetails> lstPDF = new List<PDFDocumentDetails>();
                while (reader.Read())
                {
                    PDFDocumentDetails obj = new PDFDocumentDetails();
                    obj.PDFDocumentId = GetReaderValue_Int32(reader, "ManufacturerExcelId", 0);
                    obj.PDFDocumentRefNo = GetReaderValue_Int32(reader, "ManufactureNo", 0);
                    obj.Caption = GetReaderValue_String(reader, "Caption", "");
                    obj.FileName = GetReaderValue_String(reader, "FileName", "");
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.DLUP = GetReaderValue_NullableDateTime(reader, "DLUP", null);
                    lstPDF.Add(obj);
                    obj = null;
                }
                return lstPDF;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Excel list for invoice", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }




        /// <summary>
        /// DeleteManufacturerPDF
        /// Calls [usp_delete_ManufacturerPDF]
        /// </summary>
        /// <param name="ManufacturerPdfId"></param>
        /// <returns></returns>
        public override bool DeleteManufacturerPDF(System.Int32? ManufacturerPdfId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_delete_ManufacturerPDF", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@ManufacturerPdfId", SqlDbType.Int).Value = ManufacturerPdfId;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to delete manufacturer pdf", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }



        /// <summary>
        /// Calls [usp_delete_IHSDocumentPDF]
        /// </summary>
        /// <param name="IHSPDFID"></param>
        /// <returns></returns>
        public override bool DeleteIHSPDF(System.Int32? IHSPDFID)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_delete_IHSDocumentPDF", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@IHSPDFID", SqlDbType.Int).Value = IHSPDFID;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to delete ihs pdf", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// DeleteManufacturerExcel
        /// Calls [usp_delete_ManufacturerExcel]
        /// </summary>
        /// <param name="ManufacturerExcelId"></param>
        /// <returns></returns>
        public override bool DeleteManufacturerExcel(System.Int32? ManufacturerExcelId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_delete_ManufacturerExcel", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@ManufacturerExcelId", SqlDbType.Int).Value = ManufacturerExcelId;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to delete manufacturer excel", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override Int32 CheckIsExistsManufacturer(System.String manufacturerName, System.Int32? updatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_check_Manufacturer", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@ManufacturerName", SqlDbType.NVarChar).Value = manufacturerName;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@ManufacturerId", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (Int32)cmd.Parameters["@ManufacturerId"].Value;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to Check Existence of Manufacturer", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override List<ContactGroup> GetDataByNameorCode(System.String nameSearch, System.String codeSearch, System.String codeType)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_nugget_GetDetailsByNameOrCode", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@NameSearch", SqlDbType.NVarChar).Value = nameSearch;
                cmd.Parameters.Add("@CodeSearch", SqlDbType.NVarChar).Value = codeSearch;
                cmd.Parameters.Add("@CodeType", SqlDbType.NVarChar).Value = codeType;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<ContactGroup> lst = new List<ContactGroup>();
                while (reader.Read())
                {
                    ContactGroup obj = new ContactGroup();
                    obj.Id = GetReaderValue_Int32(reader, "Id", 0);
                    obj.ContactName = GetReaderValue_String(reader, "Name", "");
                    obj.Code = GetReaderValue_String(reader, "Code", "");
                    //obj.URL = GetReaderValue_String(reader, "URL", "");
                    //obj.Inactive = GetReaderValue_Boolean(reader, "Inactive", false);
                    //obj.RowCnt = GetReaderValue_NullableInt32(reader, "RowCnt", null);
                    //obj.ConflictResource = GetReaderValue_String(reader, "ConflictResource", "");
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Manufacturers", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override Int32 SaveGroupData(System.String groupName, System.String groupCode, System.String grpType, System.String commaSeperatedIDs)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            int output = 0;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_ContactGroup_Insert", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@ContactName", SqlDbType.NVarChar).Value = groupName;
                cmd.Parameters.Add("@Code", SqlDbType.NVarChar).Value = groupCode;
                cmd.Parameters.Add("@ContactGroupType", SqlDbType.NVarChar).Value = grpType;
                cmd.Parameters.Add("@CommaSeperatedValue", SqlDbType.NVarChar).Value = commaSeperatedIDs;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                output = (Int32)cmd.Parameters["@RowsAffected"].Value;
                return output;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert group data", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override DataTable AutoGroupSearch(System.String nameSearch,System.String groupType)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_autosearch_ManufacturerGroup_Search", cn);
                cmd.Parameters.Add("@GroupName", SqlDbType.NVarChar).Value = nameSearch;
                cmd.Parameters.Add("@GroupType", SqlDbType.NVarChar).Value = groupType;
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Group search", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override List<ContactGroup> GetDataByLot(System.Int32 LotNo, System.Int32 ClientNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_nugget_GetDetailsByLot", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@LotNo", SqlDbType.NVarChar).Value = LotNo;
                cmd.Parameters.Add("@ClientNo", SqlDbType.NVarChar).Value = ClientNo;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<ContactGroup> lst = new List<ContactGroup>();
                while (reader.Read())
                {
                    ContactGroup obj = new ContactGroup();
                    obj.StockId = GetReaderValue_Int32(reader, "StockId", 0);
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.ClientNo = GetReaderValue_Int32(reader, "ClientNo", 0);
                    obj.QuantityInStock = GetReaderValue_Int32(reader, "QuantityInStock", 0);
                    obj.QuantityOnOrder = GetReaderValue_Int32(reader, "QuantityOnOrder", 0);
                    obj.Location = GetReaderValue_String(reader, "Location", "");
                    obj.Unavailable = GetReaderValue_Boolean(reader, "Unavailable", false);
                    obj.LandedCost = GetReaderValue_NullableDouble(reader, "LandedCost", null);
                    obj.SupplierPart = GetReaderValue_String(reader, "SupplierPart", "");
                    obj.QuantityAllocated = GetReaderValue_Int32(reader, "QuantityAllocated", 0);
                    obj.WarehouseName = GetReaderValue_String(reader, "WarehouseName", "");
                    obj.SupplierName = GetReaderValue_String(reader, "SupplierName", "");
                    obj.QuantityAvailable = GetReaderValue_NullableInt32(reader, "QuantityAvailable", null);
                    obj.PODeliveryDate = GetReaderValue_NullableDateTime(reader, "PODeliveryDate", null);
                    obj.PurchaseOrderNumber = GetReaderValue_NullableInt32(reader, "PurchaseOrderNumber", null);
                    obj.CustomerRMANumber = GetReaderValue_NullableInt32(reader, "CustomerRMANumber", null);
                    obj.CustomerRMADate = GetReaderValue_NullableDateTime(reader, "CustomerRMADate", null);
                    obj.POSerialNo = GetReaderValue_Int16(reader, "POSerialNo", 0);
                    obj.LotCode = GetReaderValue_String(reader, "LotCode", "");
                    obj.LotName = GetReaderValue_String(reader, "LotName", "");
                    obj.LotName = GetReaderValue_String(reader, "LotName", "");
                    obj.ProductDescription = GetReaderValue_String(reader, "ProductDescription", "");
                    obj.ManufacturerCode = GetReaderValue_String(reader, "ManufacturerCode", "");
                    obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
                    //
                    obj.IsBookedLotQuote = GetReaderValue_Boolean(reader, "IsBookedLotQuote", false);
                    obj.BookedLotQuoteNo = GetReaderValue_Int32(reader, "BookedLotQuoteNo", 0);
                    obj.QuoteNumber = GetReaderValue_Int32(reader, "QuoteNumber", 0);

                    //lot by So line

                    obj.IsBookedLotSO = GetReaderValue_Boolean(reader, "IsBookedLotSO", false);
                    obj.BookedLotSONo = GetReaderValue_Int32(reader, "BookedLotSONo", 0);
                    obj.SONumber = GetReaderValue_Int32(reader, "SalesOrderNumber", 0);
       

        lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Manufacturers", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override string GetAdvisoryNotes(int manufacturerId, int clientId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            string notes = "";
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_select_ManufacturerAdvisoryNotes", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 90;
                cmd.Parameters.Add("@ManufacturerId", SqlDbType.Int).Value = manufacturerId;
                cmd.Parameters.Add("@ClientID", SqlDbType.Int).Value = clientId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                while (reader.Read())
                {
                    notes = GetReaderValue_String(reader, "AdvisoryNotes", "");
                }
                return notes;
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to get AdvisoryNotes of Manufacturer", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override List<ManufacturerDetails> GetAdvisoryNotes(string IDs, int clientId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_get_multiple_AdvisoryNotes", cn)
                {
                    CommandType = CommandType.StoredProcedure,
                    CommandTimeout = 30
                };
                cmd.Parameters.Add("@IDs", SqlDbType.NVarChar).Value = IDs;
                cmd.Parameters.Add("@ClientID", SqlDbType.Int).Value = clientId;

                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<ManufacturerDetails> lst = new List<ManufacturerDetails>();
                while (reader.Read())
                {
                    ManufacturerDetails obj = new ManufacturerDetails
                    {
                        ManufacturerId = GetReaderValue_Int32(reader, "ManufacturerId", 0),
                        AdvisoryNotes = GetReaderValue_String(reader, "AdvisoryNotes", "")
                    };
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to get usp_get_multiple_AdvisoryNotes", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
    }
}