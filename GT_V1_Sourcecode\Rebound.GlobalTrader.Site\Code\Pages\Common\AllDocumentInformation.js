Type.registerNamespace("Rebound.GlobalTrader.Site.Pages");Rebound.GlobalTrader.Site.Pages.AllDocumentInformation=function(n){Rebound.GlobalTrader.Site.Pages.AllDocumentInformation.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Pages.AllDocumentInformation.prototype={get_ctlMainInfo:function(){return this._ctlMainInfo},set_ctlMainInfo:function(n){this._ctlMainInfo!==n&&(this._ctlMainInfo=n)},initialize:function(){Rebound.GlobalTrader.Site.Pages.AllDocumentInformation.callBaseMethod(this,"initialize")},goInit:function(){Rebound.GlobalTrader.Site.Pages.AllDocumentInformation.callBaseMethod(this,"goInit")},selectPart:function(){},dispose:function(){this.isDisposed||(this._ctlMainInfo&&this._ctlMainInfo.dispose(),Rebound.GlobalTrader.Site.Pages.AllDocumentInformation.callBaseMethod(this,"dispose"))}};Rebound.GlobalTrader.Site.Pages.AllDocumentInformation.registerClass("Rebound.GlobalTrader.Site.Pages.AllDocumentInformation",Rebound.GlobalTrader.Site.Pages.Content,Sys.IDisposable);