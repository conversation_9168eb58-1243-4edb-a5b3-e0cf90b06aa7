<%--
 Marker     changed by      date           Remarks
[001]       <PERSON><PERSON>   12/09/2018    Add Header, Detail radio export Csv button
--%>

<%@ Control Language="C#" CodeBehind="BOMManagerLines.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.DataListNuggets.BOMManagerLines" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" BoxType="List">
    <%--[001] code start--%>
    <Links>
        <ReboundUI:IconButton  ID="ibtnExportCSV" runat="server" Style="margin-left:8px; display:none;" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="ExportToExcel" />
    </Links>
    <%--[001] code end--%>

    <Filters>
        <ReboundUI_DataListNugget:Filter ID="ctlFilter" runat="server">

            <FieldsLeft>
                <%--[001] code start--%>
                <ReboundUI_Form:FormField runat="server">
                    <Field>
                        <asp:RadioButtonList ID="radHeaderDetail" name="radioList" runat="server" RepeatDirection="Horizontal">
                            <asp:ListItem Text="Header" Value="Header" />
                            <asp:ListItem Text="Detail" Value="Detail" />
                        </asp:RadioButtonList>
                    </Field>
                </ReboundUI_Form:FormField>
                <%--[001] code end--%>

                <ReboundUI_FilterDataItemRow:TextBox ID="ctlCode" runat="server" ResourceTitle="Code"
                    FilterField="Code" />
                <ReboundUI_FilterDataItemRow:DropDown ID="ctlStatus" runat="server" DropDownType="BomManagerStatus"
                    ResourceTitle="Status" FilterField="BomManagerStatus" DropDownAssembly="Rebound.GlobalTrader.Site" />
                 
                <ReboundUI_FilterDataItemRow:DropDown ID="ctlSalesperson" runat="server" DropDownAssembly="Rebound.GlobalTrader.Site" DropDownType="PoHubBuyer" ResourceTitle="AssignedUserLead" FilterField="PoHubBuyer" />
                <%--<ReboundUI_FilterDataItemRow:DropDown ID="ddlSalesperson" runat="server" FieldID="ddlSalesperson" ResourceTitle="Buyer"/>	--%>
                <ReboundUI_FilterDataItemRow:DropDown ID="ctlClient" runat="server" DropDownType="Client"
                    ResourceTitle="Client" FilterField="Client" DropDownAssembly="Rebound.GlobalTrader.Site" />
                <ReboundUI_FilterDataItemRow:DropDown ID="ctlClientSalesperson" runat="server" DropDownAssembly="Rebound.GlobalTrader.Site"
                    DropDownType="Employee" ResourceTitle="SalesPerson" FilterField="SalesPerson" />
                 
                  <ReboundUI_FilterDataItemRow:DropDown ID="ctlBomMailGroups" runat="server" DropDownAssembly="Rebound.GlobalTrader.Site"
                    DropDownType="MailGroups" ResourceTitle="BomMailGroups" FilterField="BomMailGroups" />
            </FieldsLeft>
            <FieldsRight>
          
                <ReboundUI_FilterDataItemRow:TextBox ID="ctlName" runat="server" ResourceTitle="Name"
                    FilterField="Name" />
                <%--<ReboundUI_FilterDataItemRow:TextBox id="ctlCompanyName" runat="server" ResourceTitle="CompanyName" FilterField="CMName"  />--%>
                <ReboundUI_FilterDataItemRow:TextBox ID="ctlManufacturer" runat="server" ResourceTitle="Manufacturer"
                    FilterField="Manufacturer" />
                <ReboundUI_FilterDataItemRow:TextBox ID="ctlPartNumber" runat="server" ResourceTitle="PartNo"
                    FilterField="Part" />

                <%--[001] code start--%>
                    <ReboundUI_FilterDataItemRow:DropDown ID="ctlDivision" runat="server" DropDownType="Division"
                    ResourceTitle="Division" FilterField="Division" DropDownAssembly="Rebound.GlobalTrader.Site" />
                
               <ReboundUI_FilterDataItemRow:DateSelect ID="ctlStartDate" runat="server" ResourceTitle="ReceivedStartDate" FilterField="StartDate" />
                <ReboundUI_FilterDataItemRow:DateSelect ID="ctlEndDate" runat="server" ResourceTitle="ReceivedEndDate" FilterField="EndDate" />
                <%--[001] code end--%>
                
            </FieldsRight>
        </ReboundUI_DataListNugget:Filter>
          <style type="text/css">

        #ctl00_cphMain_ctlBOMManagerResults_ctlDB_pnlBoxInner .boxHeader{ height:52px !important;}
       #ctl00_cphMain_ctlBOMManagerResults_ctlDB_pnlBoxInner .boxHeaderInner{ height:52px !important;}

         
    </style>

    </Filters>
    <%--<Content>
          <asp:Panel ID="pnlCustReq" runat="server">
               <ReboundUI:FlexiDataTable ID="Table" runat="server" PanelHeight="250" />
                    <ReboundUI:FlexiDataTable ID="tblCustReq" runat="server" PanelHeight="250" />
          </asp:Panel>
         </Content>--%>
</ReboundUI_Nugget:DesignBase>
