﻿/*
============================================================================================================================ 
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-232043]     CuongDox		 12-Mar-2025		CREATE		Bug 232043: [PROD Bug] Stock is Quarantined until release
============================================================================================================================  
*/
CREATE OR ALTER VIEW vwStockLog  
--*********************************************************************************************  
--* RP 16.10.2009:  
--* - add GoodsInCurrencyCode (for PurchasePrice)  
--*  
--* RP 12.10.2009:  
--* - add DebitNo and DebitNumber  
--*  
--* RP 09.06.2009:  
--* - Use new GoodsInNo on tbStockLog rather than getting it from tbGoodsIn  
--*********************************************************************************************  
AS  SELECT  sl.StockLogId  
          , sl.StockLogTypeNo  
          , sl.StockNo  
          , sl.QuantityInStock  
          , sl.QuantityOnOrder  
          , sl.InvoiceNo  
          , sl.PurchaseOrderNo  
          , sl.RelatedStockNo  
          , sl.ActionQuantity  
          , sl.GoodsInNo  
          , sl.GoodsInLineNo  
          , sl.CRMALineNo  
          , sl.SalesOrderNo  
          , sl.SalesOrderLineNo  
          , sl.SRMALineNo  
          , sl.UpdatedBy  
          , sl.DLUP  
          , sl.Detail  
          , sl.ChangeNotes  
          , sl.StockLogReasonNo  
          , sl.DebitNo  
          , slr.[Name] AS StockLogReasonName  
          , iv.InvoiceNumber  
          , po.PurchaseOrderNumber  
          , st.Part AS RelatedStockPart  
          , lg.EmployeeName AS UpdatedByName  
          , gi.GoodsInNumber  
          , so.SalesOrderNumber  
          , crma.CustomerRMAId AS CustomerRMANo  
          , crma.CustomerRMANumber  
          , srma.SupplierRMAId AS SupplierRMANo  
          , srma.SupplierRMANumber  
          , db.DebitNumber  
          , cu.CurrencyCode AS GoodsInCurrencyCode
		  , stre.StockId AS StockReferenceNo
		  , stre.Part AS StockReferencePart
    FROM    tbStockLog sl  
    LEFT JOIN tbStockLogReason slr ON slr.StockLogReasonId = sl.StockLogReasonNo  
    LEFT JOIN tbInvoice iv ON sl.InvoiceNo = iv.InvoiceId  
    LEFT JOIN tbPurchaseOrder po ON sl.PurchaseOrderNo = po.PurchaseOrderId  
    LEFT JOIN tbStock st ON sl.RelatedStockNo = st.StockId  
    LEFT JOIN tbGoodsIn gi ON sl.GoodsInNo = gi.GoodsInId  
    LEFT JOIN tbCurrency cu ON gi.CurrencyNo = cu.CurrencyId  
    LEFT JOIN dbo.tbLogin lg ON sl.UpdatedBy = lg.LoginId  
    LEFT JOIN dbo.tbSalesOrder so ON sl.SalesOrderNo = so.SalesOrderId  
    LEFT JOIN dbo.tbCustomerRMALine crmal ON crmal.CustomerRMALineId = sl.CRMALineNo  
    LEFT JOIN dbo.tbCustomerRMA crma ON crmal.CustomerRMANo = crma.CustomerRMAId  
    LEFT JOIN dbo.tbSupplierRMALine srmal ON srmal.SupplierRMALineId = sl.srmaLineNo  
    LEFT JOIN dbo.tbSupplierRMA srma ON srmal.SupplierRMANo = srma.SupplierRMAId  
    LEFT JOIN dbo.tbDebit db ON sl.DebitNo = db.DebitId  
	LEFT JOIN tbStock stre ON stre.StockId = sl.StockReferenceNo
  
  
  
  
  
  
  
  
  
  
  