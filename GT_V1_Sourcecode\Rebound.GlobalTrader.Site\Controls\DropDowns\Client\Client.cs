﻿using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;

namespace Rebound.GlobalTrader.Site.Controls.DropDowns {
	public partial class Client : Base {

        private bool _IsGSA = false;
        public bool IsGSA
        {
            get { return _IsGSA; }
            set { _IsGSA = value; }
        }
        private bool _IsGlobalUser = false;
        public bool IsGlobalUser
        {
            get { return _IsGlobalUser; }
            set { _IsGlobalUser = value; }
        }

        protected override void OnLoad(EventArgs e) {
            SetDropDownType("Client");
            AddScriptReference("Controls.DropDowns.Client.Client");
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.DropDowns.Client", ClientID);
            _scScriptControlDescriptor.AddProperty("IsGSA", SessionManager.IsGSA);
            _scScriptControlDescriptor.AddProperty("IsGlobalUser", SessionManager.IsGlobalUser);
            base.OnLoad(e);
		}

	}
}