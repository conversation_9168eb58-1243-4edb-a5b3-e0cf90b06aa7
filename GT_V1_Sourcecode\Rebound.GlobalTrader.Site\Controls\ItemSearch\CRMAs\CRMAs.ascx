<%@ Control Language="C#" CodeBehind="CRMAs.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.ItemSearch.CRMAs" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_ItemSearch:DesignBase id="ctlDB" runat="server" InitialSortDirection="DESC">
	<FieldsLeft>
		<ReboundUI_FilterDataItemRow:Numerical id="ctlCRMANo" runat="server" ResourceTitle="CRMANo"/>
		<ReboundUI_FilterDataItemRow:TextBox id="ctlCompany" runat="server" ResourceTitle="Company" />
		<ReboundUI_FilterDataItemRow:TextBox id="ctlContact" runat="server" ResourceTitle="Contact" />
		<ReboundUI_FilterDataItemRow:DropDown id="ctlSalesman" runat="server" ResourceTitle="Salesman" DropDownType="Employee" DropDownAssembly="Rebound.GlobalTrader.Site" />
	</FieldsLeft>
	<FieldsRight>
		<ReboundUI_FilterDataItemRow:Numerical id="ctlInvoiceNo" runat="server" ResourceTitle="InvoiceNo"/>
		<ReboundUI_FilterDataItemRow:TextBox id="ctlCRMANotes" runat="server" ResourceTitle="Notes" />
		<ReboundUI_FilterDataItemRow:DateSelect id="ctlCRMADateFrom" runat="server" ResourceTitle="CustomerRMADateFrom" />
		<ReboundUI_FilterDataItemRow:DateSelect id="ctlCRMADateTo" runat="server" ResourceTitle="CustomerRMADateTo" />
	</FieldsRight>
</ReboundUI_ItemSearch:DesignBase>
