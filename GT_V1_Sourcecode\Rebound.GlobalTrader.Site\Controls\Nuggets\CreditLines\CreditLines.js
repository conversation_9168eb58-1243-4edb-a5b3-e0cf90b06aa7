Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");Rebound.GlobalTrader.Site.Controls.Nuggets.CreditLines=function(n){Rebound.GlobalTrader.Site.Controls.Nuggets.CreditLines.initializeBase(this,[n]);this._intCreditID=-1;this._intLineID=-1;this._intLineCount=0;this._intLineDataCalls=0;this._blnLineIsService=!1;this._blnLineLoaded=!1;this._CreditLineIds=[];this._IsPoHub=!1;this._isEditEnable=!1;this._hidInvoiceNumber=!1;this._blnHubLogin=!1;this._blnFronClientInvoice=!1;this._blnExported=!1};Array.prototype.remove=function(n){return this.indexOf(n)!==-1?(this.splice(this.indexOf(n),1),!0):!1};Rebound.GlobalTrader.Site.Controls.Nuggets.CreditLines.prototype={get_intCreditID:function(){return this._intCreditID},set_intCreditID:function(n){this._intCreditID!==n&&(this._intCreditID=n)},get_intLineID:function(){return this._intLineID},set_intLineID:function(n){this._intLineID!==n&&(this._intLineID=n)},get_ibtnAdd:function(){return this._ibtnAdd},set_ibtnAdd:function(n){this._ibtnAdd!==n&&(this._ibtnAdd=n)},get_ibtnEdit:function(){return this._ibtnEdit},set_ibtnEdit:function(n){this._ibtnEdit!==n&&(this._ibtnEdit=n)},get_ibtnDelete:function(){return this._ibtnDelete},set_ibtnDelete:function(n){this._ibtnDelete!==n&&(this._ibtnDelete=n)},get_tbl:function(){return this._tbl},set_tbl:function(n){this._tbl!==n&&(this._tbl=n)},get_hypPrev:function(){return this._hypPrev},set_hypPrev:function(n){this._hypPrev!==n&&(this._hypPrev=n)},get_hypNext:function(){return this._hypNext},set_hypNext:function(n){this._hypNext!==n&&(this._hypNext=n)},get_lblLineNumber:function(){return this._lblLineNumber},set_lblLineNumber:function(n){this._lblLineNumber!==n&&(this._lblLineNumber=n)},get_pnlLineDetail:function(){return this._pnlLineDetail},set_pnlLineDetail:function(n){this._pnlLineDetail!==n&&(this._pnlLineDetail=n)},get_pnlLoadingLineDetail:function(){return this._pnlLoadingLineDetail},set_pnlLoadingLineDetail:function(n){this._pnlLoadingLineDetail!==n&&(this._pnlLoadingLineDetail=n)},get_pnlLineDetailError:function(){return this._pnlLineDetailError},set_pnlLineDetailError:function(n){this._pnlLineDetailError!==n&&(this._pnlLineDetailError=n)},get_lblSubTotal:function(){return this._lblSubTotal},set_lblSubTotal:function(n){this._lblSubTotal!==n&&(this._lblSubTotal=n)},get_lblTax:function(){return this._lblTax},set_lblTax:function(n){this._lblTax!==n&&(this._lblTax=n)},get_lblFreight:function(){return this._lblFreight},set_lblFreight:function(n){this._lblFreight!==n&&(this._lblFreight=n)},get_lblTotal:function(){return this._lblTotal},set_lblTotal:function(n){this._lblTotal!==n&&(this._lblTotal=n)},get_ibtnConfirm:function(){return this._ibtnConfirm},set_ibtnConfirm:function(n){this._ibtnConfirm!==n&&(this._ibtnConfirm=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Nuggets.CreditLines.callBaseMethod(this,"initialize");this._strDataPath="controls/Nuggets/CreditLines";this._strDataObject="CreditLines";this.addRefreshEvent(Function.createDelegate(this,this.getData));this._tbl.addSelectedIndexChanged(Function.createDelegate(this,this.tbl_SelectedIndexChanged));$addHandler(this._hypPrev,"click",Function.createDelegate(this,this.prevLine));$addHandler(this._hypNext,"click",Function.createDelegate(this,this.nextLine));this._frmEdit=$find(this._aryFormIDs[1]);this._ibtnAdd&&($R_IBTN.addClick(this._ibtnAdd,Function.createDelegate(this,this.showAddForm)),this._frmAdd=$find(this._aryFormIDs[0]),this._frmAdd.addCancel(Function.createDelegate(this,this.hideAddForm)),this._frmAdd.addSaveComplete(Function.createDelegate(this,this.saveAddComplete)));this._ibtnEdit&&($R_IBTN.addClick(this._ibtnEdit,Function.createDelegate(this,this.showEditForm)),this._frmEdit=$find(this._aryFormIDs[1]),this._frmEdit.addCancel(Function.createDelegate(this,this.hideEditForm)),this._frmEdit.addSaveComplete(Function.createDelegate(this,this.saveEditComplete)));this._ibtnDelete&&($R_IBTN.addClick(this._ibtnDelete,Function.createDelegate(this,this.showDeleteForm)),this._frmDelete=$find(this._aryFormIDs[2]),this._frmDelete.addCancel(Function.createDelegate(this,this.hideDeleteForm)),this._frmDelete.addSaveComplete(Function.createDelegate(this,this.deleteComplete)),this._frmDelete.addNotConfirmed(Function.createDelegate(this,this.hideDeleteForm)));this._ibtnConfirm&&($R_IBTN.addClick(this._ibtnConfirm,Function.createDelegate(this,this.showConfirmForm)),this._frmConfirm=$find(this._aryFormIDs[3]),this._frmConfirm.addCancel(Function.createDelegate(this,this.hideConfirmForm)),this._frmConfirm.addSaveComplete(Function.createDelegate(this,this.ConfirmComplete)),this._frmConfirm.addNotConfirmed(Function.createDelegate(this,this.hideConfirmForm)));this.getData()},dispose:function(){this.isDisposed||(this._hypPrev&&$clearHandlers(this._hypPrev),this._hypNext&&$clearHandlers(this._hypNext),this._ibtnAdd&&$R_IBTN.clearHandlers(this._ibtnAdd),this._ibtnEdit&&$R_IBTN.clearHandlers(this._ibtnEdit),this._ibtnDelete&&$R_IBTN.clearHandlers(this._ibtnDelete),this.tbl&&this.tbl.dispose(),this._frmAdd&&this._frmAdd.dispose(),this._frmEdit&&this._frmEdit.dispose(),this._frmDelete&&this._frmDelete.dispose(),this.intCreditID=null,this.intLineID=null,this.ibtnAdd=null,this.ibtnEdit=null,this.ibtnDelete=null,this.tbl=null,this.hypPrev=null,this.hypNext=null,this.lblLineNumber=null,this.pnlLineDetail=null,this.pnlLoadingLineDetail=null,this.pnlLineDetailError=null,this.lblSubTotal=null,this.lblTax=null,this.lblFreight=null,this.lblTotal=null,this._blnHubLogin=null,this._blnFronClientInvoice=null,Rebound.GlobalTrader.Site.Controls.Nuggets.CreditLines.callBaseMethod(this,"dispose"))},getData:function(){this.enableEditButtons(!1);$R_FN.showElement(this._pnlLineDetail,!1);this.showLoading(!0);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strDataPath);n.set_DataObject(this._strDataObject);n.set_DataAction("GetLines");n.addParameter("id",this._intCreditID);n.addDataOK(Function.createDelegate(this,this.getDataOK));n.addError(Function.createDelegate(this,this.getDataError));n.addTimeout(Function.createDelegate(this,this.getDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},enableEditButtons:function(n){n?!Boolean.parse(this._blnHubLogin)&&Boolean.parse(this._blnFronClientInvoice)?(this._ibtnAdd&&$R_IBTN.enableButton(this._ibtnAdd,!1),this._ibtnEdit&&$R_IBTN.enableButton(this._ibtnEdit,!1),this._ibtnDelete&&$R_IBTN.enableButton(this._ibtnDelete,!1),this._ibtnConfirm&&$R_IBTN.enableButton(this._ibtnConfirm,!1)):(this._ibtnAdd&&$R_IBTN.enableButton(this._ibtnAdd,!this._blnExported),this._ibtnEdit&&$R_IBTN.enableButton(this._ibtnEdit,this._blnLineLoaded&&!this._blnExported),this._ibtnDelete&&$R_IBTN.enableButton(this._ibtnDelete,this._blnLineLoaded&&!this._blnExported),this._ibtnConfirm&&$R_IBTN.enableButton(this._ibtnConfirm,this._CreditLineIds.length>0&&!this._blnExported)):(this._ibtnEdit&&$R_IBTN.enableButton(this._ibtnEdit,!1),this._ibtnDelete&&$R_IBTN.enableButton(this._ibtnDelete,!1),this._ibtnConfirm&&$R_IBTN.enableButton(this._ibtnConfirm,!1))},tbl_SelectedIndexChanged:function(){this._isEditEnable=this._tbl.getSelectedExtraData().ParentCreditLineId;this.enableEditButtons(!0);this._intLineID=this._tbl._varSelectedValue;this.getLineData()},getDataOK:function(n){var i,r,u;if(this.showLoading(!1),i=n._result,this._tbl.clearTable(),i.Lines)for(r=0;r<i.Lines.length;r++){var t=i.Lines[r],f=t.IsPoHub==!0?!1:t.IsIpo==!1?!1:t.IsClientInvoiceLineId==!1?!1:t.ParentCreditLineNo==!1?!1:t.ParentCreditLineId==!0?!1:!0,e=[this.writeCheckbox(t.ID,r,this._tbl),$R_FN.writeDoubleCellValue($R_FN.writePartNo(t.Part,t.ROHS),$R_FN.setCleanTextValue(t.CustomerPart)),$R_FN.writeDoubleCellValue($RGT_nubButton_Manufacturer(t.ManufacturerNo,t.Manufacturer,t.MfrAdvisoryNotes),$R_FN.setCleanTextValue(t.DC)),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(t.Product),$R_FN.setCleanTextValue(t.Package)),t.Quantity,$R_FN.writeDoubleCellValue(t.Price,t.LC),$R_FN.writeDoubleCellValue(t.Total,t.Tax)],o={ParentCreditLineId:t.ParentCreditLineId};this._tbl.addRow(e,t.ID,t.ID==this._intLineID,o);this.registerCheckBox(t.ID,r,!1,f,this._tbl);u=this.getCheckBox(r,this._tbl);u._element.setAttribute("onClick",String.format('$find("{0}").getCheckedCellValue({1},{2});',this._element.id,r,t.ID));u=null;t=null}this._intLineCount=this._tbl.countRows();$R_FN.setInnerHTML(this._lblSubTotal,i.SubTotal);$R_FN.setInnerHTML(this._lblTax,i.Tax);$R_FN.setInnerHTML(this._lblFreight,i.Freight);$R_FN.setInnerHTML(this._lblTotal,i.Total);this.showContent(!0);this.showContentLoading(!1);this._tbl.resizeColumns();this.enableEditButtons(!0)},getDataError:function(n){this.showError(!0,n.get_ErrorMessage())},writeCheckbox:function(n,t,i){var r=this.getControlID("chk",t,i),u=this.getControlID("chkImg",t,i);return String.format('<div class="imageCheckBoxDisabled" id="{0}" ><img id="{1}" class="{2}" src="images/x.gif" style="border-width: 0px;" /> <\/div>',r,u,"off")},getControlID:function(n,t,i){return String.format("{0}_{1}{2}",i._element.id,n,t)},getCheckBox:function(n,t){return $find(this.getControlID("chk",n,t))},registerCheckBox:function(n,t,i,r,u){var e=this.getControlID("chk",t,u),o=this.getControlID("chkImg",t,u),f=this.getCheckBox(t,u);f&&(f.dispose(),f=null);eval($R_FN.getCreateStatement("Rebound.GlobalTrader.Site.Controls.ImageCheckBox",[["blnChecked",i],["blnEnabled",r],["img",String.format('$get("{0}")',o)]],e))},getCheckedCellValue:function(n,t){var i=this._tbl,r=this.getCheckBox(n,i),u=r._blnChecked,f=i._tbl.rows[n];f&&(u?this._CreditLineIds.push(t):this._CreditLineIds.remove(t),this._CreditLineIds.length>0?$R_IBTN.enableButton(this._ibtnConfirm,!0):$R_IBTN.enableButton(this._ibtnConfirm,!1))},prevLine:function(){var n=this._tbl._intSelectedIndex-1;n<0||this._tbl.selectRow(n,!0)},nextLine:function(){var n=this._tbl._intSelectedIndex+1;n>=this._intLineCount||this._tbl.selectRow(n,!0)},getLineData:function(){this._intLineDataCalls+=1;this._blnLineLoaded=!1;this.enableEditButtons(!1);this.showLoading(!0);$R_FN.showElement(this._pnlLoadingLineDetail,!0);$R_FN.showElement(this._pnlLineDetailError,!1);$R_FN.showElement(this._pnlLineDetail,!1);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strDataPath);n.set_DataObject(this._strDataObject);n.set_DataAction("GetData");n.addParameter("id",this._intLineID);n.addDataOK(Function.createDelegate(this,this.getLineDataOK));n.addError(Function.createDelegate(this,this.getLineDataError));n.addTimeout(Function.createDelegate(this,this.getLineDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getLineDataOK:function(n){this._intLineDataCalls-=1;this._intLineDataCalls<1&&this.showLoading(!1);$R_FN.showElement(this._pnlLineDetailError,!1);var t=n._result;this.setFieldValue("ctlQuantity",t.Quantity==null?0:t.Quantity);this.setFieldValue("ctlPrice",$R_FN.setCleanTextValue(t.Price));this.setFieldValue("hidPriceRaw",t.PriceRaw);this.setFieldValue("ctlPartNo",$RGT_nubButton_Stock(t.StockNo,t.Part,t.ROHS));this.setFieldValue("hidPartNo",$R_FN.setCleanTextValue(t.Part));this.setFieldValue("ctlManufacturer",$RGT_nubButton_Manufacturer(t.ManufacturerNo,t.Manufacturer,t.MfrAdvisoryNotes));this.setFieldValue("hidManufacturer",$R_FN.setCleanTextValue(t.Manufacturer));this.setFieldValue("ctlCustomerPart",$R_FN.setCleanTextValue(t.CustomerPart));this.setFieldValue("ctlProduct",$R_FN.setCleanTextValue(t.Product));this.setFieldValue("ctlPackage",$R_FN.setCleanTextValue(t.Package));this.setFieldValue("ctlROHS",$R_FN.writeROHS(t.ROHS));this.setFieldValue("ctlDateCode",$R_FN.setCleanTextValue(t.DC));this.setFieldValue("ctlTaxable",t.Taxable);this.setFieldValue("ctlLandedCost",t.LandedCost);this.setFieldValue("hidLandedCostRaw",t.LandedCostRaw);this.setFieldValue("ctlLineNotes",$R_FN.setCleanTextValue(t.LineNotes));this._blnLineIsService=t.IsService;this._frmEdit._dblCurrencyRateToBase=t.CurrencyRate;this._frmEdit._strCurrencyCode=t.CurrencyCode;this.setFieldValue("ctlService",$RGT_nubButton_Service(t.ServiceNo,t.Part));this.setFieldValue("hidService",$R_FN.setCleanTextValue(t.Part));this.setFieldValue("ctlServiceDescription",$R_FN.setCleanTextValue(t.CustomerPart));$R_FN.showElement(this._pnlLineDetail,!0);$R_FN.showElement(this._pnlLoadingLineDetail,!1);$R_FN.setInnerHTML(this._lblLineNumber,String.format($R_RES.LineXOfY,this._tbl._intSelectedIndex+1,this._intLineCount));this.showField("ctlPartNo",!this._blnLineIsService);this.showField("ctlManufacturer",!this._blnLineIsService);this.showField("ctlCustomerPart",!this._blnLineIsService);this.showField("ctlProduct",!this._blnLineIsService);this.showField("ctlPackage",!this._blnLineIsService);this.showField("ctlROHS",!this._blnLineIsService);this.showField("ctlDateCode",!this._blnLineIsService);this.showField("ctlService",this._blnLineIsService);this.showField("ctlServiceDescription",this._blnLineIsService);this._blnLineLoaded=!0;this.enableEditButtons(!0)},getLineDataError:function(n){this._intLineDataCalls-=1;this._intLineDataCalls<1&&this.showLoading(!1);$R_FN.showElement(this._pnlLoadingLineDetail,!1);$R_FN.showElement(this._pnlLineDetail,!1);$R_FN.showElement(this._pnlLineDetailError,!0);$R_FN.setInnerHTML(this._pnlLineDetailError,n.get_ErrorMessage())},showAddForm:function(){this._frmAdd._intCreditID=this._intCreditID;this.showForm(this._frmAdd,!0)},hideAddForm:function(){this.showForm(this._frmAdd,!1);this._tbl.resizeColumns()},saveAddComplete:function(){this.hideAddForm();this._intLineID=this._frmAdd._intLineID;this.getData();this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully)},showEditForm:function(){this._frmEdit._intLineID=this._intLineID;this._frmEdit._blnLineIsService=this._blnLineIsService;this._frmEdit.setFieldValue("ctlPartNo",this.getFieldValue("ctlPartNo"));this._frmEdit.setFieldValue("ctlManufacturer",this.getFieldValue("hidManufacturer"));this._frmEdit.setFieldValue("ctlDateCode",this.getFieldValue("ctlDateCode"));this._frmEdit.setFieldValue("ctlPackage",this.getFieldValue("ctlPackage"));this._frmEdit.setFieldValue("ctlProduct",this.getFieldValue("ctlProduct"));this._frmEdit.setFieldValue("ctlQuantity",this.getFieldValue("ctlQuantity"));this._frmEdit.setFieldValue("ctlPrice",this.getFieldValue("hidPriceRaw"));this._frmEdit.setFieldValue("ctlService",this.getFieldValue("hidService"));this._frmEdit.setFieldValue("ctlServiceDescription",this.getFieldValue("ctlServiceDescription"));this._frmEdit.setFieldValue("ctlLandedCost",this.getFieldValue("hidLandedCostRaw"));this._frmEdit.setFieldValue("ctlLineNotes",this.getFieldValue("ctlLineNotes"));this.showForm(this._frmEdit,!0)},hideEditForm:function(){this.showForm(this._frmEdit,!1);this._tbl.resizeColumns()},saveEditComplete:function(){this.hideEditForm();this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully);this.getData()},showConfirmForm:function(){this._frmConfirm._intLineID=this._intLineID;this._frmConfirm._CreditLineIds=this._CreditLineIds.toString();this.showForm(this._frmConfirm,!0)},hideConfirmForm:function(){this.showForm(this._frmConfirm,!1);this._tbl.resizeColumns()},ConfirmComplete:function(){this.hideConfirmForm();this.getData()},showDeleteForm:function(){this._frmDelete._intLineID=this._intLineID;this._frmDelete._blnLineIsService=this._blnLineIsService;this._frmDelete.setFieldValue("ctlPartNo",this.getFieldValue("ctlPartNo"));this._frmDelete.setFieldValue("ctlManufacturer",this.getFieldValue("hidManufacturer"));this._frmDelete.setFieldValue("ctlDateCode",this.getFieldValue("ctlDateCode"));this._frmDelete.setFieldValue("ctlPackage",this.getFieldValue("ctlPackage"));this._frmDelete.setFieldValue("ctlProduct",this.getFieldValue("ctlProduct"));this._frmDelete.setFieldValue("ctlService",this.getFieldValue("hidService"));this._frmDelete.setFieldValue("ctlServiceDescription",this.getFieldValue("ctlServiceDescription"));this.showForm(this._frmDelete,!0)},hideDeleteForm:function(){this.showForm(this._frmDelete,!1);this._tbl.resizeColumns()},deleteComplete:function(){this.hideDeleteForm();this.getData()}};Rebound.GlobalTrader.Site.Controls.Nuggets.CreditLines.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.CreditLines",Rebound.GlobalTrader.Site.Controls.Nuggets.Base);