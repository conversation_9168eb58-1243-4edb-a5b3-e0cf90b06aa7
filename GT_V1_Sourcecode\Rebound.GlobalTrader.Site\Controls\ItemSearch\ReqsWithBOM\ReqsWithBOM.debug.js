///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//----------------------------------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//----------------------------------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.ItemSearch");

Rebound.GlobalTrader.Site.Controls.ItemSearch.ReqsWithBOM = function(element) { 
	Rebound.GlobalTrader.Site.Controls.ItemSearch.ReqsWithBOM.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.ItemSearch.ReqsWithBOM.prototype = {

    initialize: function() {
        Rebound.GlobalTrader.Site.Controls.ItemSearch.ReqsWithBOM.callBaseMethod(this, "initialize");
        this.addSetupData(Function.createDelegate(this, this.doSetupData));
        this.addGetDataComplete(Function.createDelegate(this, this.doGetDataComplete));
        
    },

    dispose: function() {
        if (this.isDisposed) return;
        Rebound.GlobalTrader.Site.Controls.ItemSearch.ReqsWithBOM.callBaseMethod(this, "dispose");
    },

    doSetupData: function() {
        this._objData.set_PathToData("controls/ItemSearch/ReqsWithBOM");
        this._objData.set_DataObject("ReqsWithBOM");
        this._objData.set_DataAction("GetData");
        this._objData.addParameter("Part", this.getFieldValue("ctlPartNo"));
        //this._objData.addParameter("CM", this.getFieldValue("ctlCompany"));
       // this._objData.addParameter("IncludeClosed", this.getFieldValue("ctlIncludeClosed"));
        this._objData.addParameter("ReqNoLo", this.getFieldValue_Min("ctlReqNo"));
        this._objData.addParameter("ReqNoHi", this.getFieldValue_Max("ctlReqNo"));
        this._objData.addParameter("DateReceivedFrom", this.getFieldValue("ctlDateReceivedFrom"));
        this._objData.addParameter("DateReceivedTo", this.getFieldValue("ctlDateReceivedTo"));
        this._objData.addParameter("Client", this.getFieldValue("ctlClient"));
        this._objData.addParameter("BOM", this.getFieldValue("ctlBOM"));
        this._objData.addParameter("ViewLevel", this.getFieldValue("ctlViewLevel"));
    },

    doGetDataComplete: function() {
        for (var i = 0, l = this._objResult.Results.length; i < l; i++) {
            var row = this._objResult.Results[i];
            var aryData = [
				row.No
				, $R_FN.setCleanTextValue(row.CMName)
				, $R_FN.setCleanTextValue(row.BMName)
			    , $R_FN.writePartNo(row.Part, row.ROHS)
				, $R_FN.setCleanTextValue(row.Date)
				, row.Quantity
				, row.Price
				, (row.AS9120) ? $R_FN.showRedBoldText($R_RES.Yes) : "-"
			];
            this._tblResults.addRow(aryData, row.ID, (row.ID == this._intInitialID));
            aryData = null; row = null;
        }
    }

};

Rebound.GlobalTrader.Site.Controls.ItemSearch.ReqsWithBOM.registerClass("Rebound.GlobalTrader.Site.Controls.ItemSearch.ReqsWithBOM", Rebound.GlobalTrader.Site.Controls.ItemSearch.Base, Sys.IDisposable);
