//----------------------------------------------------------------------------------------------------
//Marker     Changed by      Date               Remarks
//[001]      Vinay           11/06/2013         CR:- Supplier Invoice
//----------------------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.DataListNuggets {
    public partial class SupplierInvoice : Base
    {

        #region Properties

        private bool _blnShowCanNotBeExported = false;
        public bool ShowCanNotBeExported {
            get { return _blnShowCanNotBeExported; }
            set { _blnShowCanNotBeExported = value; }
        }


        #endregion

        #region Overrides

        /// <summary>
        /// OnInit
        /// </summary>
        /// <param name="e"></param>
        protected override void OnInit(EventArgs e) {
            SetDataListNuggetType("SupplierInvoice");
            base.OnInit(e);
            TitleText = Functions.GetGlobalResource("Nuggets", "SupplierInvoice");
            AddScriptReference("Controls.DataListNuggets.SupplierInvoice.SupplierInvoice");
			SetupTable();
        }

		protected override void OnLoad(EventArgs e) {
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.DataListNuggets.SupplierInvoice", ctlDesignBase.ClientID);
			base.OnLoad(e);
		}
        protected override void RenderAdditionalState()
        {
            int intTab = 0;
            var strCallType = this.GetSavedStateValue("CallType").ToUpper();
            if (string.IsNullOrEmpty(strCallType)) strCallType = "ALL";
            if (strCallType == "ALL") intTab = 0;
            if (strCallType == "CANNOTBEEXPORTED") intTab = 1;
            this._blnShowCanNotBeExported = (intTab == 1);
            ((Pages.Content)Page).CurrentTab = intTab;
            this.OnAskPageToChangeTab();
            base.RenderAdditionalState();
        }

		protected override void OnPreRender(EventArgs e) {
			base.OnPreRender(e);
            _scScriptControlDescriptor.AddProperty("blnShowCanNotBeExported", _blnShowCanNotBeExported);
		}

        #endregion

        private void SetupTable() {
            _tbl.AllowSelection = false;
            _tbl.SortColumnDirection = Rebound.GlobalTrader.Site.Enumerations.SortColumnDirection.DESC;
            _tbl.Columns.Add(new FlexiDataColumn("SupplierInvoiceSelectAll", Unit.Pixel(60), false));
            _tbl.Columns.Add(new FlexiDataColumn("SupplierInvoice", WidthManager.GetWidth(WidthManager.ColumnWidth.ListName), true));

            _tbl.Columns.Add(new FlexiDataColumn("Supplier", Unit.Pixel(120), true));
            _tbl.Columns.Add(new FlexiDataColumn("GoodsIn", "PoOrIpo", Unit.Pixel(100), true));
           // _tbl.Columns.Add(new FlexiDataColumn("PurchaseOrder", WidthManager.GetWidth(WidthManager.ColumnWidth.SystemDocumentNumber), true));
            _tbl.Columns.Add(new FlexiDataColumn("URNNumber", Unit.Pixel(100), true));
            _tbl.Columns.Add(new FlexiDataColumn("InvoiceDate", Unit.Pixel(100), true));
          
            _tbl.Columns.Add(new FlexiDataColumn("PartNo", Unit.Pixel(100), true));
            _tbl.Columns.Add(new FlexiDataColumn("Value", Unit.Pixel(100), true));

            _tbl.Columns.Add(new FlexiDataColumn("SupplierInvoiceItemCount", "SupplierInvoiceDecision", Unit.Pixel(100), true));
            _tbl.Columns.Add(new FlexiDataColumn("SupplierInvoiceAction", Unit.Pixel(100), true));
        }
    }
}//_tbl.Columns.Add(new FlexiDataColumn("SupplierInvoiceDecision", 10, true));
 //_tbl.Columns.Add(new FlexiDataColumn("SupplierInvoiceSummary", 20, true));