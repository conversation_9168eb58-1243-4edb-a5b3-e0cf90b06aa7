//Marker     Changed by      Date         Remarks
//[001]      Soorya          03/03/2023   RP-1048 Remove AI code
using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using System.Threading.Tasks;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Text.RegularExpressions;
//using Microsoft.ApplicationInsights;  //[001]

namespace Rebound.GlobalTrader.Site.Controls.HomeNuggets.Data {
	/// <summary>
	/// Summary description for $codebehindclassname$
	/// </summary>
	[WebService(Namespace = "http://tempuri.org/")]
	[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
	public class MyRecentActivity : Rebound.GlobalTrader.Site.Data.Base {

		public override void ProcessRequest(HttpContext context) {
			if (base.init(context)) {
				switch (Action) {
					case "GetData": GetData(); break;
					default: WriteErrorActionNotFound(); break;
				}
			}
		}
        /// <summary>
        /// Gets the main data
        /// </summary>
        private void GetData()
        {
            try
            {

                int intLoginID = LoginID;
                int count = 0;
                if (GetFormValue_Int("OtherLoginID") > 0) intLoginID = GetFormValue_Int("OtherLoginID");
                DataTable dtResult = Activity.GetMyRecentActivity(intLoginID);
                JsonObject jsn = new JsonObject();
                JsonObject jsnItems = new JsonObject(true);

                if (dtResult == null)
                {
                    WriteErrorDataNotFound();
                }
                else if (dtResult.Rows.Count == 0)
                {
                    jsn.AddVariable("CRActivity", jsnItems);
                    jsn.AddVariable("GIActivity", jsnItems);
                    jsn.AddVariable("QUActivity", jsnItems);
                    jsn.AddVariable("POActivity", jsnItems);
                    jsn.AddVariable("SOActivity", jsnItems);
                    jsn.AddVariable("CRMAActivity", jsnItems);
                    jsn.AddVariable("SRMAActivity", jsnItems);
                    jsn.AddVariable("CreditActivity", jsnItems);
                    jsn.AddVariable("DebitActivity", jsnItems);
                    jsn.AddVariable("InvoiceActivity", jsnItems);
                    jsn.AddVariable("POSupplierApprovalActivity", jsnItems);
                    jsn.AddVariable("Count", count);
                    jsnItems.Dispose();
                    jsnItems = null;

                    OutputResult(jsn);
                    jsn.Dispose();
                    jsn = null;

                }
                else
                {
                    count = dtResult.Rows.Count;
                    DataTable dtEmpty = new DataTable();
                    DataTable dtCustomerReq = dtResult.Select("TableName = 'CustomerRequirement'").Length > 0 ? dtResult.Select("TableName = 'CustomerRequirement'").CopyToDataTable()
                                              : dtEmpty;
                    DataTable dtGood = dtResult.Select("TableName = 'GoodsIn'").Length > 0 ? dtResult.Select("TableName = 'GoodsIn'").CopyToDataTable()
                                              : dtEmpty;
                    DataTable dtQuot = dtResult.Select("TableName = 'Quote'").Length > 0 ? dtResult.Select("TableName = 'Quote'").CopyToDataTable()
                                             : dtEmpty;
                    DataTable dtPurc = dtResult.Select("TableName = 'PurchaseOrder'").Length > 0 ? dtResult.Select("TableName = 'PurchaseOrder'").CopyToDataTable()
                                            : dtEmpty;
                    DataTable dtSale = dtResult.Select("TableName = 'SalesOrder'").Length > 0 ? dtResult.Select("TableName = 'SalesOrder'").CopyToDataTable()
                                            : dtEmpty;
                    DataTable dtCustomerRMA = dtResult.Select("TableName = 'CustomerRMA'").Length > 0 ? dtResult.Select("TableName = 'CustomerRMA'").CopyToDataTable()
                                            : dtEmpty;
                    DataTable dtSupp = dtResult.Select("TableName = 'SupplierRMA'").Length > 0 ? dtResult.Select("TableName = 'SupplierRMA'").CopyToDataTable()
                                            : dtEmpty;
                    DataTable dtCred = dtResult.Select("TableName = 'Credit'").Length > 0 ? dtResult.Select("TableName = 'Credit'").CopyToDataTable()
                                            : dtEmpty;
                    DataTable dtDeb = dtResult.Select("TableName = 'Debit'").Length > 0 ? dtResult.Select("TableName = 'Debit'").CopyToDataTable()
                                            : dtEmpty;
                    DataTable dtInvoice = dtResult.Select("TableName = 'Invoice'").Length > 0 ? dtResult.Select("TableName = 'Invoice'").CopyToDataTable()
                                            : dtEmpty;
                    DataTable dtPurchaseOrderSupplierApproval = dtResult.Select("TableName = 'PurchaseOrderSupplierApproval'").Length > 0 ? dtResult.Select("TableName = 'PurchaseOrderSupplierApproval'").CopyToDataTable()
                                            : dtEmpty;

                    //customer requirements
                    foreach (DataRow dr in dtCustomerReq.Rows)
                    {
                        JsonObject jsnItem = new JsonObject();
                        jsnItem.AddVariable("ID", dr["RowId"]);
                        jsnItem.AddVariable("No", dr["RowNumber"]);
                        jsnItem.AddVariable("Date", Functions.FormatDate(Convert.ToDateTime(dr["RowDate"].ToString())));
                        jsnItem.AddVariable("CM", dr["CompanyName"]);
                        jsnItem.AddVariable("CMNo", dr["CompanyNo"]);
                        jsnItems.AddVariable(jsnItem);
                        jsnItem.Dispose();
                        jsnItem = null;
                    }
                    jsn.AddVariable("CRActivity", jsnItems);
                    jsnItems.Dispose();
                    jsnItems = null;

                    //goods in
                    jsnItems = new JsonObject(true);
                    foreach (DataRow dr in dtGood.Rows)
                    {
                        JsonObject jsnItem = new JsonObject();
                        jsnItem.AddVariable("ID", dr["RowId"]);
                        jsnItem.AddVariable("No", dr["RowNumber"]);
                        jsnItem.AddVariable("Date", Functions.FormatDate(Convert.ToDateTime(dr["RowDate"])));
                        jsnItem.AddVariable("CM", dr["CompanyName"]);
                        jsnItem.AddVariable("CMNo", dr["CompanyNo"]);
                        jsnItems.AddVariable(jsnItem);
                        jsnItem.Dispose();
                        jsnItem = null;
                    }
                    jsn.AddVariable("GIActivity", jsnItems);
                    jsnItems.Dispose();
                    jsnItems = null;

                    //quotes
                    jsnItems = new JsonObject(true);
                    foreach (DataRow dr in dtQuot.Rows)
                    {
                        JsonObject jsnItem = new JsonObject();
                        jsnItem.AddVariable("ID", dr["RowId"]);
                        jsnItem.AddVariable("No", dr["RowNumber"]);
                        jsnItem.AddVariable("Date", Functions.FormatDate(Convert.ToDateTime(dr["RowDate"])));
                        jsnItem.AddVariable("CM", dr["CompanyName"]);
                        jsnItem.AddVariable("CMNo", dr["CompanyNo"]);
                        jsnItems.AddVariable(jsnItem);
                        jsnItem.Dispose();
                        jsnItem = null;
                    }
                    jsn.AddVariable("QUActivity", jsnItems);
                    jsnItems.Dispose();
                    jsnItems = null;

                    //purchase orders
                    jsnItems = new JsonObject(true);
                    foreach (DataRow dr in dtPurc.Rows)
                    {
                        JsonObject jsnItem = new JsonObject();
                        jsnItem.AddVariable("ID", dr["RowId"]);
                        jsnItem.AddVariable("No", dr["RowNumber"]);
                        jsnItem.AddVariable("Date", Functions.FormatDate(Convert.ToDateTime(dr["RowDate"])));
                        jsnItem.AddVariable("CM", dr["CompanyName"]);
                        jsnItem.AddVariable("CMNo", dr["CompanyNo"]);
                        jsnItems.AddVariable(jsnItem);
                        jsnItem.Dispose();
                        jsnItem = null;
                    }
                    jsn.AddVariable("POActivity", jsnItems);
                    jsnItems.Dispose();
                    jsnItems = null;

                    //sales orders
                    jsnItems = new JsonObject(true);
                    foreach (DataRow dr in dtSale.Rows)
                    {
                        JsonObject jsnItem = new JsonObject();
                        jsnItem.AddVariable("ID", dr["RowId"]);
                        jsnItem.AddVariable("No", dr["RowNumber"]);
                        jsnItem.AddVariable("Date", Functions.FormatDate(Convert.ToDateTime(dr["RowDate"])));
                        jsnItem.AddVariable("CM", dr["CompanyName"]);
                        jsnItem.AddVariable("CMNo", dr["CompanyNo"]);
                        jsnItems.AddVariable(jsnItem);
                        jsnItem.Dispose();
                        jsnItem = null;
                    }
                    jsn.AddVariable("SOActivity", jsnItems);
                    jsnItems.Dispose();
                    jsnItems = null;

                    //customer rmas
                    jsnItems = new JsonObject(true);
                    foreach (DataRow dr in dtCustomerRMA.Rows)
                    {
                        JsonObject jsnItem = new JsonObject();
                        jsnItem.AddVariable("ID", dr["RowId"]);
                        jsnItem.AddVariable("No", dr["RowNumber"]);
                        jsnItem.AddVariable("Date", Functions.FormatDate(Convert.ToDateTime(dr["RowDate"])));
                        jsnItem.AddVariable("CM", dr["CompanyName"]);
                        jsnItem.AddVariable("CMNo", dr["CompanyNo"]);
                        jsnItems.AddVariable(jsnItem);
                        jsnItem.Dispose();
                        jsnItem = null;
                    }
                    jsn.AddVariable("CRMAActivity", jsnItems);
                    jsnItems.Dispose();
                    jsnItems = null;

                    //supplier rmas
                    jsnItems = new JsonObject(true);
                    foreach (DataRow dr in dtSupp.Rows)
                    {
                        JsonObject jsnItem = new JsonObject();
                        jsnItem.AddVariable("ID", dr["RowId"]);
                        jsnItem.AddVariable("No", dr["RowNumber"]);
                        jsnItem.AddVariable("Date", Functions.FormatDate(Convert.ToDateTime(dr["RowDate"])));
                        jsnItem.AddVariable("CM", dr["CompanyName"]);
                        jsnItem.AddVariable("CMNo", dr["CompanyNo"]);
                        jsnItems.AddVariable(jsnItem);
                        jsnItem.Dispose();
                        jsnItem = null;
                    }
                    jsn.AddVariable("SRMAActivity", jsnItems);
                    jsnItems.Dispose();
                    jsnItems = null;


                    //credits
                    jsnItems = new JsonObject(true);
                    foreach (DataRow dr in dtCred.Rows)
                    {
                        JsonObject jsnItem = new JsonObject();
                        jsnItem.AddVariable("ID", dr["RowId"]);
                        jsnItem.AddVariable("No", dr["RowNumber"]);
                        jsnItem.AddVariable("Date", Functions.FormatDate(Convert.ToDateTime(dr["RowDate"])));
                        jsnItem.AddVariable("CM", dr["CompanyName"]);
                        jsnItem.AddVariable("CMNo", dr["CompanyNo"]);
                        jsnItems.AddVariable(jsnItem);
                        jsnItem.Dispose();
                        jsnItem = null;
                    }
                    jsn.AddVariable("CreditActivity", jsnItems);
                    jsnItems.Dispose();
                    jsnItems = null;


                    //debits
                    jsnItems = new JsonObject(true);
                    foreach (DataRow dr in dtDeb.Rows)
                    {
                        JsonObject jsnItem = new JsonObject();
                        jsnItem.AddVariable("ID", dr["RowId"]);
                        jsnItem.AddVariable("No", dr["RowNumber"]);
                        jsnItem.AddVariable("Date", Functions.FormatDate(Convert.ToDateTime(dr["RowDate"])));
                        jsnItem.AddVariable("CM", dr["CompanyName"]);
                        jsnItem.AddVariable("CMNo", dr["CompanyNo"]);
                        jsnItems.AddVariable(jsnItem);
                        jsnItem.Dispose();
                        jsnItem = null;
                    }
                    jsn.AddVariable("DebitActivity", jsnItems);
                    jsnItems.Dispose();
                    jsnItems = null;

                    //invoices
                    jsnItems = new JsonObject(true);
                    foreach (DataRow dr in dtInvoice.Rows)
                    {
                        JsonObject jsnItem = new JsonObject();
                        jsnItem.AddVariable("ID", dr["RowId"]);
                        jsnItem.AddVariable("No", dr["RowNumber"]);
                        jsnItem.AddVariable("Date", Functions.FormatDate(Convert.ToDateTime(dr["RowDate"])));
                        jsnItem.AddVariable("CM", dr["CompanyName"]);
                        jsnItem.AddVariable("CMNo", dr["CompanyNo"]);
                        jsnItems.AddVariable(jsnItem);
                        jsnItem.Dispose();
                        jsnItem = null;
                    }
                    jsn.AddVariable("InvoiceActivity", jsnItems);
                    jsn.AddVariable("Count", count);
                    jsnItems.Dispose();
                    jsnItems = null;


                    //Open Supplier / PO Approvals

                    jsnItems = new JsonObject(true);
                    foreach (DataRow dr in dtPurchaseOrderSupplierApproval.Rows)
                    {
                        JsonObject jsnItem = new JsonObject();
                        jsnItem.AddVariable("ID", dr["RowId"]);
                        jsnItem.AddVariable("No", dr["RowNumber"]);
                        jsnItem.AddVariable("Date", Functions.FormatDate(Convert.ToDateTime(dr["RowDate"])));
                        jsnItem.AddVariable("CM", dr["CompanyName"]);
                        jsnItem.AddVariable("CMNo", dr["CompanyNo"]);
                        jsnItems.AddVariable(jsnItem);                        
                    }
                    jsn.AddVariable("POSupplierApprovalActivity", jsnItems);
                    jsnItems.Dispose();
                    jsnItems = null;

                    OutputResult(jsn);
                    jsn.Dispose();
                    jsn = null;
                }


            }
            catch (Exception ex)
            {
                Errorlog obj = new Errorlog();
                obj.LogMessage(Convert.ToString("Exception- " + ex.Message + " " + ex.StackTrace));
            }
            GetPowerAppNotification();
        }
		public void GetPowerAppNotification()
        {
            try
            {
                List<SalesOrder> RedSOs = SalesOrder.GetRedFlaggedSO(SessionManager.ClientID,SessionManager.LoginID);

                foreach(SalesOrder redso in RedSOs)
                {
                    HttpClient client = new HttpClient();
                    client.BaseAddress = new Uri(redso.PowerSorUrl);
                    client.DefaultRequestHeaders.Accept.Clear();
                    client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

                    HttpRequestMessage request = new HttpRequestMessage(HttpMethod.Post, client.BaseAddress);
                    var body = $"{{\"SalesPersonName\": \"{redso.SalesmanName}\",\"SalesPersonEmail\":\"{redso.ContactEmail}\",\"SODetails\":\"{redso.SalesOrderNumberDetail}\"}}";

                    var content = new StringContent(body, Encoding.UTF8, "application/json");
                    request.Content = content;
                    //var response = await MakeRequestAsync(request, client);
                    client.SendAsync(request).ConfigureAwait(false);
                    
                }
                
            }
            catch (Exception ex)
            {

            }
        }

	}
}
