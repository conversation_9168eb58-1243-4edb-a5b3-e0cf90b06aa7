﻿<?xml version="1.0"?>

<configuration>
  <configSections>
    <sectionGroup name="system.web.webPages.razor" type="System.Web.WebPages.Razor.Configuration.RazorWebSectionGroup, System.Web.WebPages.Razor, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35">
      <section name="host" type="System.Web.WebPages.Razor.Configuration.HostSection, System.Web.WebPages.Razor, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" requirePermission="false" />
      <section name="pages" type="System.Web.WebPages.Razor.Configuration.RazorPagesSection, System.Web.WebPages.Razor, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" requirePermission="false" />
    </sectionGroup>
  </configSections>
	<system.web>
		<!--Added by Rahil-->
		<httpHandlers>
			<add path="*" verb="*" type="System.Web.HttpNotFoundHandler"/>
		</httpHandlers>

		<!--
        Enabling request validation in view pages would cause validation to occur
        after the input has already been processed by the controller. By default
        MVC performs request validation before a controller processes the input.
        To change this behavior apply the ValidateInputAttribute to a
        controller or action.
    -->
		<pages
			validateRequest="false"
			pageParserFilterType="System.Web.Mvc.ViewTypeParserFilter, System.Web.Mvc, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35"
			pageBaseType="System.Web.Mvc.ViewPage, System.Web.Mvc, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35"
			userControlBaseType="System.Web.Mvc.ViewUserControl, System.Web.Mvc, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35">
			<controls>
				<add assembly="System.Web.Mvc, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" namespace="System.Web.Mvc" tagPrefix="mvc" />
			</controls>
		</pages>
		<!--Added by Rahil--> 
		<compilation>
			<assemblies>
				<add assembly="System.Runtime, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
				<!--Added by Rahil-->
				<add assembly="System.Web.Abstractions, Version=4.0.0.0, Culture=neutral, PublicKeyToken=31BF3856AD364E35" />
				<add assembly="System.Web.Helpers, Version=1.0.0.0, Culture=neutral, PublicKeyToken=31BF3856AD364E35" />
				<add assembly="System.Web.Routing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=31BF3856AD364E35" />
				<add assembly="System.Web.Mvc, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" />
				<add assembly="System.Web.WebPages, Version=1.0.0.0, Culture=neutral, PublicKeyToken=31BF3856AD364E35" />
				<!--Added by Rahil-->
			</assemblies>
		</compilation>
	</system.web>

	<!--Added by Rahil-->
	<runtime>
		<assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
			<dependentAssembly>
				<assemblyIdentity name="Antlr3.Runtime" publicKeyToken="eb42632606e9261f" />
				<bindingRedirect oldVersion="0.0.0.0-3.5.0.2" newVersion="3.5.0.2" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" />
				<bindingRedirect oldVersion="0.0.0.0-12.0.0.0" newVersion="12.0.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="WebGrease" publicKeyToken="31bf3856ad364e35" />
				<bindingRedirect oldVersion="0.0.0.0-1.6.5135.21930" newVersion="1.6.5135.21930" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Web.Helpers" publicKeyToken="31bf3856ad364e35" />
				<bindingRedirect oldVersion="1.0.0.0-*******" newVersion="*******" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Web.WebPages" publicKeyToken="31bf3856ad364e35" />
				<bindingRedirect oldVersion="1.0.0.0-*******" newVersion="*******" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Web.Mvc" publicKeyToken="31bf3856ad364e35" />
				<bindingRedirect oldVersion="1.0.0.0-*******" newVersion="*******" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="System.Web.Razor" culture="neutral" publicKeyToken="31bf3856ad364e35" />
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
			</dependentAssembly>
		</assemblyBinding>
		<assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
			<dependentAssembly>
				<assemblyIdentity name="System.Web.WebPages.Razor" culture="neutral" publicKeyToken="31bf3856ad364e35" />
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
			</dependentAssembly>
		</assemblyBinding>
	</runtime>
	<!--Added by Rahil-->
	<system.web.webPages.razor>
    <!--<host factoryType="System.Web.Mvc.MvcWebRazorHostFactory, System.Web.Mvc, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" />-->
		<!--Added by Rahil-->
		<host factoryType="System.Web.Mvc.MvcWebRazorHostFactory, System.Web.Mvc, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" />
		<!--Added by Rahil-->
    <pages pageBaseType="System.Web.Mvc.WebViewPage">
      <namespaces>
        <add namespace="System.Web.Mvc" />
        <add namespace="System.Web.Mvc.Ajax" />
        <add namespace="System.Web.Mvc.Html" />
        <add namespace="System.Web.Routing" />
		  <!--<add namespace="System.Web.Optimization" />-->
        <add namespace="Rebound.GlobalTrader.Site" />
        
      </namespaces>
    </pages>
  </system.web.webPages.razor>

  <appSettings>
    <add key="webpages:Enabled" value="false" />
  </appSettings>

  <system.webServer>
    <handlers>
      <remove name="BlockViewHandler"/>
      <add name="BlockViewHandler" path="*" verb="*" preCondition="integratedMode" type="System.Web.HttpNotFoundHandler" />
    </handlers>
  </system.webServer>
</configuration>