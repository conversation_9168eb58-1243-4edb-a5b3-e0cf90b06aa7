///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 09.09.2010:
// - hide Salesperson filter on "My" tab
//
// RP 06.01.2010:
// - fully dispose everything
//
// RP 23.12.2009:
// - render state on server
//
// RP 16.10.2009:
// - Changes due to changes in base class
/*
 Marker     ChangedBy       Date            Remarks
 [001]      A<PERSON><PERSON>     17-Aug-2018     Provision to add Global Security in Sales Order
 */
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DataListNuggets");

Rebound.GlobalTrader.Site.Controls.DataListNuggets.OGELLinesExport = function(element) {
    Rebound.GlobalTrader.Site.Controls.DataListNuggets.OGELLinesExport.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.DataListNuggets.OGELLinesExport.prototype = {
    get_intSalesPersonID: function () { return this._intSalesPersonID; }, set_intSalesPersonID: function (value) { if (this._intSalesPersonID !== value) this._intSalesPersonID = value; },
    //[001] start
    get_IsGlobalLogin: function () { return this._IsGlobalLogin; }, set_IsGlobalLogin: function (v) { if (this._IsGlobalLogin !== v) this._IsGlobalLogin = v; },
    //[001] end
    get_ibtnExportCSV: function () { return this._ibtnExportCSV; }, set_ibtnExportCSV: function (v) { if (this._ibtnExportCSV !== v) this._ibtnExportCSV = v; },//[001] 
    get_sortIndex: function () { return this._sortIndex; }, set_sortIndex: function (v) { if (this._sortIndex !== v) this._sortIndex = v; },
    get_sortDir: function () { return this._sortDir; }, set_sortDir: function (v) { if (this._sortDir !== v) this._sortDir = v; },
    get_pageIndex: function () { return this._pageIndex; }, set_pageIndex: function (v) { if (this._pageIndex !== v) this._pageIndex = v; },
    get_pageSize: function () { return this._pageSize; }, set_pageSize: function (v) { if (this._pageSize !== v) this._pageSize = v; },
    get_code: function () { return this._code; }, set_code: function (v) { if (this._code !== v) this._code = v; },
    get_name: function () { return this._name; }, set_name: function (v) { if (this._name !== v) this._name = v; },
    initialize: function() {
        this.addSetupDataCallEvent(Function.createDelegate(this, this.setupDataCall));
        this.addGetDataOKEvent(Function.createDelegate(this, this.getDataOK));
        this.addInitCompleteEvent(Function.createDelegate(this, this.initAfterBaseIsReady));
        this._strPathToData = "controls/DataListNuggets/OGELLinesExport";
        this._strDataObject = "OGELLinesExport";
        Rebound.GlobalTrader.Site.Controls.DataListNuggets.OGELLinesExport.callBaseMethod(this, "initialize");
        if (this._ibtnExportCSV) $R_IBTN.addClick(this._ibtnExportCSV, Function.createDelegate(this, this.exportCSV));//[001]
    },

    initAfterBaseIsReady: function() {
        //this.addPageTabChangedEvent(Function.createDelegate(this, this.pageTabChanged));
        //this.applySalesPersonFilter();
        //this.updateFilterVisibility();
        this.getData();
    },

    dispose: function() {
        if (this.isDisposed) return;
        this._intSalesPersonID = null;
        //[001] start
        this._IsGlobalLogin = null;
        //[001] end
        if (this._ibtnExportCSV) $R_IBTN.clearHandlers(this._ibtnExportCSV);
        this._ibtnExportCSV = null;
        Rebound.GlobalTrader.Site.Controls.DataListNuggets.OGELLinesExport.callBaseMethod(this, "dispose");
    },

    pageTabChanged: function() {
        this._table._intCurrentPage = 1;
        this._enmViewLevel = this._intCurrentTab;
        this.updateFilterVisibility();
        this.getData();
    },

    setupDataCall: function() {
        this._objData.addParameter("ViewLevel", this._enmViewLevel);
        //[001] start
        this._objData.addParameter("IsGlobalLogin", this._IsGlobalLogin);
        //[001] end
    },

    getDataOK: function () {
        var chkdatestatus = '';
        //this._sortIndex = this._objResult.SortIndex;
        //this._sortDir = this._objResult.SortDir;
        //this._pageIndex = this._objResult.PageIndex;
        //this._pageSize = this._objResult.PageSize;
        //this._code = this._objResult.Code;
        //this._name = this._objResult.Name;
        //
        for (var i = 0, l = this._objResult.Results.length; i < l; i++) {
            chkdatestatus = '';
            var row = this._objResult.Results[i];
            if (row.DatePromisedStatus == 'Green')
                chkdatestatus = 'green';
            else if (row.DatePromisedStatus == 'Amber')
                chkdatestatus = '#FFBF00';
            else if (row.DatePromisedStatus == 'Red')
                chkdatestatus = 'Red';
            else if (row.DatePromisedStatus == 'White') 
                chkdatestatus = 'White';
            else
                chkdatestatus = 'White';
            var aryData = [

                $RGT_nubButton_SalesOrder(row.SoID, row.ID),
                $R_FN.writeDoubleCellValue($RGT_nubButton_Company(row.CMNo, row.CM), $R_FN.setCleanTextValue(row.CustPONO)),
                $R_FN.writeDoubleCellValue(row.Status, $R_FN.setCleanTextValue(row.DatePromised)),
                $R_FN.setCleanTextValue(row.No),
                $R_FN.setCleanTextValue(row.AirWayBill),
                $R_FN.setCleanTextValue(row.CommodityCode),
                //$R_FN.setCleanTextValue(row.ECCNCode),
                $R_FN.setCleanTextValue(row.PartNumber),
                $R_FN.setCleanTextValue(row.OGELNumber),
                $R_FN.setCleanTextValue(row.OGEL_MilitaryUse),
                $R_FN.setCleanTextValue(row.CountryName)


				//$RGT_nubButton_SalesOrder(row.ID, row.No)
				//, $R_FN.writeDoubleCellValue($R_FN.writePartNo(row.Part, row.ROHS), $R_FN.setCleanTextValue(row.CustPONO))
				//, $R_FN.writeTriCellValue(row.Quantity, row.QuantityShipped, row.QuantityInStock)
				//, $R_FN.writeDoubleCellValue($RGT_nubButton_Company(row.CMNo, row.CM), $RGT_nubButton_Contact(row.ContactNo, row.Contact))
				//, $R_FN.setCleanTextValue(row.DateOrdered)
				////, $R_FN.setCleanTextValue(row.DatePromised)
    //            , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.DatePromised),  "<span style='background-color:" + chkdatestatus +"!important;float: right;margin-top: -17px;height: 20px;width: 20px;'></span>")
				//, $R_FN.setCleanTextValue(row.Status)
    //            , $R_FN.setCleanTextValue(row.ContractNo)
		    ];
            this._table.addRow(aryData, row.ID, false);
            aryData = null; row = null;
        }
    },

    //updateFilterVisibility: function() {
    //    this.getFilterField("ctlSalesman").show(this._enmViewLevel != 0);
    //    //[001] start
    //    this.getFilterField("ctlClientName").show(this._IsGlobalLogin);
    //    //[001] end
    //},
    //applySalesPersonFilter: function() {
    //    if ((this._intSalesPersonID) && this._intSalesPersonID > 0)
    //        this.getFilterField("ctlSalesman").setValue(this._intSalesPersonID);
    //}

    exportCSV: function () {

        this.getData_Start();
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/DataListNuggets/OGELLinesExport");
        obj.set_DataObject("OGELLinesExport");
        obj.addParameter("ViewLevel", this._enmViewLevel);
        obj.set_DataAction("ExportToCSV");
        obj._intTimeoutMilliseconds = 500 * 1000;
        obj.addParameter("SortIndex", this._sortIndex);
        obj.addParameter("SortDir", this._sortDir);
        obj.addParameter("PageIndex", this._pageIndex);
        obj.addParameter("PageSize", this._pageSize);
        obj.addParameter("Code", this._code);
        obj.addParameter("Name", this._name);
        obj.addParameter("SalesPerson", this._intSalesPersonId);
        //obj.addParameter("SelectedRadio", this.getRadioButtonValue());
        
        obj.addParameter("SONoLo", this.getFilterFieldValue_Min("ctlSoNo"));
        obj.addParameter("SONoHi", this.getFilterFieldValue_Max("ctlSoNo"));
        obj.addParameter("RecentOnly", this.getFilterFieldValue("ctlRecentOnly"));
        
        obj.addParameter("InvoiceNoLo", this.getFilterFieldValue_Min("ctlInvoiceNo"));
        obj.addParameter("InvoiceNoHi", this.getFilterFieldValue_Max("ctlInvoiceNo"));
        obj.addParameter("Country", this.getFilterFieldValue("ctlCountry"));
        obj.addParameter("dateOrderedFrom", this.getFilterFieldValue("ctlDateOrderedFrom"));
        obj.addParameter("dateOrderedTo", this.getFilterFieldValue("ctlDateOrderedTo"));
        //obj.addParameter("StartDate", this.getFilterFieldValue("ctlStartDate"));
        //obj.addParameter("EndDate", this.getFilterFieldValue("ctlEndDate"));


        obj.addDataOK(Function.createDelegate(this, this.exportCSV_OK));
        obj.addError(Function.createDelegate(this, this.exportCSV_Error));
        obj.addTimeout(Function.createDelegate(this, this.exportCSV_Error));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;

    },

    exportCSV_OK: function (args) {
        var res = args._result;
        if (res.Filename) {
            //add the date to the file to force the latest version to be returned (not from cache)
            var dt = new Date();
            location.href = String.format("{0}?t={1}", res.Filename, dt.getTime());
            dt = null;
        }
        this.getDataOK_End();
    },

    exportCSV_Error: function (args) {
        this.showError(true, args.get_ErrorMessage());
    },
};
Rebound.GlobalTrader.Site.Controls.DataListNuggets.OGELLinesExport.registerClass("Rebound.GlobalTrader.Site.Controls.DataListNuggets.OGELLinesExport", Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base, Sys.IDisposable);
