﻿
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/* 
========================================================================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-224603]		Phuc Hoang			05-Feb-2025		CREATE			IPO- Sourcing - Check Supplier/ MFR Data- Addition of MFR Group Name
========================================================================================================================================
*/

CREATE OR ALTER PROCEDURE [dbo].[usp_GetSupplierType_By_MfrGroup](
	@ContactGroupId  INT = NULL,
	@ClientNo INT = NULL
)
AS

BEGIN
IF ISNULL(@ContactGroupId, 0) = 0  
BEGIN     
	SELECT DISTINCT CompanyId 'CompanyTypeId', Replace(CompanyName,'"','') as [Name] 
	FROM dbo.tbCompany comp 
	LEFT JOIN dbo.tbContact cont ON comp.defaultPOContactNo=cont.ContactId  
	WHERE comp.ClientNo = @ClientNo  
	AND ISNULL(comp.Inactive, 0) = 0 
	AND comp.IsSupplier = 1 
	ORDER BY Replace(CompanyName,'"','') 
END     
ELSE
BEGIN
	SELECT DISTINCT c.CompanyId 'CompanyTypeId', Replace(c.CompanyName,'"','') 'Name', t.Name as CompanyType, b.ContactGroupID
	FROM dbo.tbManufacturerLink a 
	JOIN dbo.tbManufacturer b ON a.ManufacturerNo = b.ManufacturerId 
	JOIN dbo.tbContactGroup cgr ON cgr.ItemId = b.ContactGroupID 
	JOIN dbo.tbCompany c ON a.SupplierCompanyNo = c.CompanyId AND c.ClientNo = @ClientNo 
	LEFT JOIN tbCompanyType t on c.TypeNo=t.CompanyTypeId 
	WHERE b.ContactGroupID = @ContactGroupId AND ISNULL(b.Inactive,0) = 0 AND ISNULL(c.Inactive, 0) = 0
	ORDER BY Replace(c.CompanyName,'"','')
END
END
GO


