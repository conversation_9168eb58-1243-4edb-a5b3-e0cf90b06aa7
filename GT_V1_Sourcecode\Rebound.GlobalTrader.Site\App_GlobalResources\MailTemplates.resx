﻿<?xml version="1.0" encoding="utf-8"?>
<root>
	<!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader><resheader name="version">2.0</resheader><resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader><resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader><data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data><data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data><data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64"><value>[base64 mime encoded serialized .NET Framework object]</value></data><data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64"><value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value><comment>This is a comment</comment></data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
	<xsd:schema id="root"
		xmlns=""
		xmlns:xsd="http://www.w3.org/2001/XMLSchema"
		xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
		<xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
		<xsd:element name="root" msdata:IsDataSet="true">
			<xsd:complexType>
				<xsd:choice maxOccurs="unbounded">
					<xsd:element name="metadata">
						<xsd:complexType>
							<xsd:sequence>
								<xsd:element name="value" type="xsd:string" minOccurs="0" />
							</xsd:sequence>
							<xsd:attribute name="name" use="required" type="xsd:string" />
							<xsd:attribute name="type" type="xsd:string" />
							<xsd:attribute name="mimetype" type="xsd:string" />
							<xsd:attribute ref="xml:space" />
						</xsd:complexType>
					</xsd:element>
					<xsd:element name="assembly">
						<xsd:complexType>
							<xsd:attribute name="alias" type="xsd:string" />
							<xsd:attribute name="name" type="xsd:string" />
						</xsd:complexType>
					</xsd:element>
					<xsd:element name="data">
						<xsd:complexType>
							<xsd:sequence>
								<xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
								<xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
							</xsd:sequence>
							<xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
							<xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
							<xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
							<xsd:attribute ref="xml:space" />
						</xsd:complexType>
					</xsd:element>
					<xsd:element name="resheader">
						<xsd:complexType>
							<xsd:sequence>
								<xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
							</xsd:sequence>
							<xsd:attribute name="name" type="xsd:string" use="required" />
						</xsd:complexType>
					</xsd:element>
				</xsd:choice>
			</xsd:complexType>
		</xsd:element>
	</xsd:schema>
	<resheader name="resmimetype">
		<value>text/microsoft-resx</value>
	</resheader>
	<resheader name="version">
		<value>2.0</value>
	</resheader>
	<resheader name="reader">
		<value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
	</resheader>
	<resheader name="writer">
		<value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
	</resheader>
	<data name="EmailCertificateOfConformance" xml:space="preserve">
		<value>Please find attached Certificate Of Conformance {0}

Regards
{1}</value>
	</data>
	<data name="EmailCreditNote" xml:space="preserve">
		<value>Please find attached details of Credit Note {0}

Regards
{1}</value>
	</data>
	<data name="EmailCustomerRMA" xml:space="preserve">
		<value>Please find attached details of Customer RMA {0}

Regards
{1}</value>
	</data>
	<data name="EmailDebitNote" xml:space="preserve">
		<value>Please find attached details of Debit Note {0}

Regards
{1}</value>
	</data>
	<data name="EmailInvoice" xml:space="preserve">
		<value>Please find attached Invoice {0}

Regards
{1}</value>
	</data>
	<data name="EmailPackingSlip" xml:space="preserve">
		<value>Please find attached Packing Slip {0}

Regards
{1}</value>
	</data>
	<data name="EmailProFormaInvoice" xml:space="preserve">
		<value>Please find attached Pro Forma Invoice {0}

Regards
{1}</value>
	</data>
	<data name="EmailPurchaseOrder" xml:space="preserve">
		<value>Please find attached details of Purchase Order {0}

Regards
{1}</value>
	</data>
	<data name="EmailQuote" xml:space="preserve">
		<value>Quote Number {0}

Regards
{1}</value>
	</data>
	<data name="EmailSalesOrder" xml:space="preserve">
		<value>Please find attached details of Sales Order {0}

Regards
{1}</value>
	</data>
	<data name="EmailSupplierRMA" xml:space="preserve">
		<value>Please find attached details of Supplier RMA {0}

Regards
{1}</value>
	</data>
	<data name="NewCredit" xml:space="preserve">
		<value>I have added a &lt;a href="[#HYPERLINK#]"&gt;new Credit Note&lt;/a&gt;:

Number: [#CREDIT_NUMBER#]
Customer: [#CUSTOMER#]
Contact: [#CONTACT#]
Invoice Number: [#INV_NUMBER#]
Customer PO: [#CUSTOMER_PO#]
Salesperson: [#SALESMAN#]</value>
	</data>
	<data name="NewCustomerRequirement" xml:space="preserve">
		<value>I have added a &lt;a href="[#HYPERLINK#]"&gt;new Customer Requirement&lt;/a&gt;:

Number: [#CUSREQ_NUMBER#]
Customer: [#CUSTOMER#]
Quantity: [#QUANTITY#]
Part No: [#PART#]
Cust Part No: [#CUSTOMERPART#]
Manufacturer:	[#MANUFACTURER#]
DateCode: [#DATECODE#]
Product: [#PRODUCT#]
Package: [#PACKAGE#]
Target Price: [#PRICE#]
Date Required: [#DATEREQUIRED#]</value>
	</data>
	<data name="NewCustomerRMA" xml:space="preserve">
		<value>I have added a &lt;a href="[#HYPERLINK#]"&gt;new Customer RMA&lt;/a&gt;:

Number: [#CRMA_NUMBER#]
Customer: [#CUSTOMER#]
Contact: [#CONTACT#]
Invoice Number: [#INVOICE_NUMBER#]
Authorised By: [#AUTHORISED_BY#]</value>
	</data>
	<data name="NewDebit" xml:space="preserve">
		<value>I have added a &lt;a href="[#HYPERLINK#]"&gt;new Debit Note&lt;/a&gt;:

Number: [#DEBIT_NUMBER#]
Supplier: [#SUPPLIER#]
Contact: [#CONTACT#]
Purchase Order: [#PO_NUMBER#]
Supplier RMA: [#SRMA_NUMBER#]
Supplier Invoice: [#SUPPLIER_INV#]
Buyer: [#BUYER#]</value>
	</data>
	<data name="NewGoodsIn" xml:space="preserve">
		<value>I have added a &lt;a href="[#HYPERLINK#]"&gt;new Goods In Note&lt;/a&gt;:

Number: [#GI_NUMBER#]
Supplier: [#SUPPLIER#]
PO Number: [#PO_NUMBER#]</value>
	</data>
	<data name="NewInvoice" xml:space="preserve">
		<value>I have added a &lt;a href="[#HYPERLINK#]"&gt;new Invoice&lt;/a&gt;:

Number: [#INVOICE_NUMBER#]
Customer: [#CUSTOMER#]
Contact: [#CONTACT#]
Sales Order: [#SO_NUMBER#]
Customer PO: [#CUSTOMER_PO#]
Salesperson: [#SALESMAN#]</value>
	</data>
	<data name="NewPurchaseOrder" xml:space="preserve">
		<value>I have added a &lt;a href="[#HYPERLINK#]"&gt;new Purchase Order&lt;/a&gt;:

Number: [#PO_NUMBER#]
Supplier: [#SUPPLIER#]
Contact: [#CONTACT#]
Buyer: [#BUYER#]</value>
	</data>
	<data name="NewQuote" xml:space="preserve">
		<value>I have added a &lt;a href="[#HYPERLINK#]"&gt;new Quote&lt;/a&gt;:

Number: [#QUOTE_NUMBER#]
Customer: [#CUSTOMER#]
Contact: [#CONTACT#]
Currency: [#CURRENCY#]
Date Quoted: [#DATE#]</value>
	</data>
	<data name="NewSalesOrder" xml:space="preserve">
		<value>I have added a &lt;a href="[#HYPERLINK#]"&gt;new Sales Order&lt;/a&gt;:

Number: [#SO_NUMBER#]
Customer: [#CUSTOMER#]
Contact: [#CONTACT#]
Salesperson: [#SALESMAN#]</value>
	</data>
	<data name="NewSupplierRMA" xml:space="preserve">
		<value>I have added a &lt;a href="[#HYPERLINK#]"&gt;new Supplier RMA&lt;/a&gt;:

Number: [#SRMA_NUMBER#]
Supplier: [#SUPPLIER#]
Contact: [#CONTACT#]
PO Number: [#PO_NUMBER#]
Authorised By: [#AUTHORISED_BY#]</value>
	</data>
	<data name="NotifyGoodsIn" xml:space="preserve">
		<value>Please review this &lt;a href="[#HYPERLINK#]"&gt;Goods In Note&lt;/a&gt;:

Number: [#GI_NUMBER#]
Supplier: [#SUPPLIER#]
Received By: [#RECEIVED_BY#]
PO Number: [#PO_NUMBER#]</value>
	</data>
	<data name="NotifySalesOrder" xml:space="preserve">
		<value>Please review this &lt;a href="[#HYPERLINK#]"&gt;Sales Order&lt;/a&gt;:

Number: [#SO_NUMBER#]
Customer: [#CUSTOMER#]
Contact: [#CONTACT#]
Salesperson: [#SALESMAN#]</value>
	</data>
	<data name="ReceivedPurchaseOrder" xml:space="preserve">
		<value>I have received Purchase Order [#PO_NUMBER#] on &lt;a href="[#HYPERLINK#]"&gt;Goods In Note [#GI_NUMBER#]&lt;/a&gt;:

Number: [#GI_NUMBER#]
Supplier: [#SUPPLIER#]
PO Number: [#PO_NUMBER#]</value>
	</data>
	<data name="RFQ" xml:space="preserve">
		<value>Please confirm price and availability for the following, thank you</value>
	</data>
	<data name="NotifyPurchaseOrder" xml:space="preserve">
		<value>Please review this &lt;a href="[#HYPERLINK#]"&gt;Purchase Order&lt;/a&gt;:

Number: [#PO_NUMBER#]
Supplier: [#SUPPLIER#]
Contact: [#CONTACT#]
Buyer: [#BUYER#]</value>
	</data>
	<data name="NewSupplierInvoice" xml:space="preserve">
		<value>I have added a &lt;a href="[#HYPERLINK#]"&gt;new Supplier Invoice&lt;/a&gt;:

Number: [#SI_NUMBER#]
Supplier: [#SUPPLIER#]
</value>
	</data>
	<data name="NotifySupplierInvoice" xml:space="preserve">
		<value>Please review this &lt;a href="[#HYPERLINK#]"&gt;Supplier Invoice&lt;/a&gt;:

Number: [#SI_NUMBER#]
Supplier: [#SUPPLIER#]</value>
	</data>
	<data name="NPREmail" xml:space="preserve">
		<value>&lt;a href="[#HYPERLINK#]"&gt;NPR&lt;/a&gt;</value>
	</data>
	<data name="CompanyApproved" xml:space="preserve">
		<value>Company has been approved:

Customer Name:  [#CUSTOMERNAME#]
Salesperson Name:  [#SALESPERSONNAME#]
Currency :  [#CURRENCY#]
Customer No :  [#CUSTOMERNO#]
Terms :  [#TERM#]
Approved By :  [#APPROVEDBY#]
Client :  [#CLIENTNAME#]
Supplier No: [#SUPPLIERNO#]</value>
	</data>
	<data name="NotifyNPR" xml:space="preserve">
		<value>PO Number: [#PO_NO#]
NPR Number: [#NPR_NO#]
Supplier: [#SUPPLIER#]
Client: [#CLIENT#]
Non-conformance detail (Reason for rejection): [#NC_DETAIL#]</value>
	</data>
	<data name="COCBodyMessage" xml:space="preserve">
		<value>Please find attached {0}

Regards
{1}</value>
	</data>
	<data name="InvoiceWithCofc" xml:space="preserve">
		<value>Please find invoice {0} attached with CofC

Regards
{1}</value>
	</data>
	<data name="NotifyEPR" xml:space="preserve">
		<value>Number: [#EPR_NO#]
Supplier: [#SUPPLIER#]
Client: [#CLIENT#]</value>
	</data>
	<data name="InvoicePDF" xml:space="preserve">
		<value>Please find attached invoice {0}

Regards
{1}</value>
	</data>
	<data name="PackingSlip" xml:space="preserve">
		<value>Please find attached Packing Slip {0}

Regards
{1}</value>
	</data>
	<data name="CommercialInvoice" xml:space="preserve">
		<value>Please find attached Commercial Invoice {0}

Regards
{1}</value>
	</data>
	<data name="PurchaseOrder" xml:space="preserve">
		<value>Please find attached purchase order {0}

Regards
{1}</value>
	</data>
	<data name="SalesOrder" xml:space="preserve">
		<value>Please find attached sales order {0}

Regards
{1}</value>
	</data>
	<data name="PackingSlipWithCofc" xml:space="preserve">
		<value>Please find packing slip {0} attached with invoice and CofC

Regards
{1}</value>
	</data>
	<data name="SOProForma" xml:space="preserve">
		<value>Please find attached pro forma invoice {0}

Regards
{1}</value>
	</data>
	<data name="NotifyCreateIPO" xml:space="preserve">
		<value>Please review this &lt;a href="[#HYPERLINK#]"&gt;Internal Purchase Order&lt;/a&gt;:

IPO Number:[#IPO_NUMBER#]
PO Number: [#PO_NUMBER#]
Buyer: [#BUYER#]</value>
	</data>
	<data name="NotifyCreateIPOSubject" xml:space="preserve">
		<value>Purchase order created &amp; pending for verification </value>
	</data>
	<data name="NotifyPurchaseRequestBom" xml:space="preserve">
		<value>Please review this &lt;a href="[#HYPERLINK#]"&gt;HUBRFQ&lt;/a&gt;:

HUBRFQ: [#IPO_BOM_NUMBER#]</value>
	</data>
	<data name="NotifyReleaseBom" xml:space="preserve">
		<value>Please review this &lt;a href="[#HYPERLINK#]"&gt;HUBRFQ&lt;/a&gt;:

HUBRFQ: [#IPO_BOM_NUMBER#]
</value>
	</data>
	<data name="NotifyPOApproved" xml:space="preserve">
		<value>Please review this &lt;a href="[#HYPERLINK#]"&gt;Purchase Order&lt;/a&gt;:

PO Number: [#PO_NUMBER#]</value>
	</data>
	<data name="NotifyPOApprovedSubject" xml:space="preserve">
		<value>Purchase order Approved</value>
	</data>
	<data name="NotifyPOApprovedNew" xml:space="preserve">
		<value>Please review this &lt;a href="[#HYPERLINK#]"&gt;Internal Purchase Order&lt;/a&gt;:

IPO Number:[#IPO_NUMBER#]
PO Number: [#PO_NUMBER#]
Buyer: [#BUYER#]</value>
	</data>
	<data name="NotifyPOUnApprovedSubject" xml:space="preserve">
		<value>Purchase order Un-Approved</value>
	</data>
	<data name="CreditNotesToPoHub" xml:space="preserve">
		<value>New credit notes created.</value>
	</data>
	<data name="CustomerRMAToPoHub" xml:space="preserve">
		<value>New CRMA created.</value>
	</data>
	<data name="DebitNotesToPoHub" xml:space="preserve">
		<value>New debit notes created.</value>
	</data>
	<data name="SupplierRMAToPoHub" xml:space="preserve">
		<value>New SRMA created.</value>
	</data>
	<data name="ClientInvoicePDF" xml:space="preserve">
		<value>Please find attached client invoice {0}

Regards
{1}</value>
	</data>
	<data name="NotifyCreatePOMail" xml:space="preserve">
		<value>Please review this &lt;a href="[#HYPERLINK#]"&gt;Purchase Order&lt;/a&gt;:

IPO Number:[#IPO_NUMBER#]
PO Number: [#PO_NUMBER#]
Buyer: [#BUYER#]</value>
	</data>
	<data name="DeallocationNotification" xml:space="preserve">
		<value>IPO No :  [#InternalPurchaseOrderNumber#]   Line :   [#PoLine#]   Part :   [#part#]    Qty : [#Quantity#]                                                                                             
Deallocated from the following Sales order(s) By  :  [#LoginFullName#]  
</value>
	</data>
	<data name="DeallocationNotificationLoopBody" xml:space="preserve">
		<value>SO : {0}  Line :  {1}  Qty : {2}</value>
	</data>
	<data name="DeallocationNotificationSubject" xml:space="preserve">
		<value>IPO No : [#InternalPurchaseOrderNumber#]  Line :  [#PoLine#]  Deallocation Notification from HUB</value>
	</data>
	<data name="CreditNote" xml:space="preserve">
		<value>Please find attached credit note {0}

Regards
{1}</value>
	</data>
	<data name="DebitNote" xml:space="preserve">
		<value>Please find attached debit note {0}

Regards
{1}</value>
	</data>
	<data name="HubDebit" xml:space="preserve">
		<value>Please find attached debit note {0}

Regards
{1}</value>
	</data>
	<data name="SOAuthorized" xml:space="preserve">
		<value>Sales order has been checked: {0}
Customer : {2}
Comments :{3}
Regards
{1}</value>
	</data>
	<data name="PromiseReasonEmail" xml:space="preserve">
		<value>Hi ,&lt;br/&gt;
The promise date has been changed for the sales order number {0}&lt;br/&gt;
Promise date : {1} &lt;br/&gt;
Reason: {2}	&lt;/br&gt;
Sales Order : {0} &lt;/br&gt;
Thanks &amp; Regards,&lt;/br&gt;
{3} &lt;/br&gt;</value>
	</data>
	<data name="GIQuerySales" xml:space="preserve">
		<value>Hello,

You have a GI query ({0}) to review - {3} click here for more information.

{1}

Kind Regards
{2}</value>
	</data>
	<data name="GIQueryScreenReply" xml:space="preserve">
		<value>Hello,

Below are the status of the Goods In Line ({0}) request:

Status: {1}
Action By: {2}
Additional Comments: {3}

Kind Regards
{4}</value>
	</data>
	<data name="SalesOrderRequestApproval" xml:space="preserve">
		<value>Please review this &lt;a href="[#HYPERLINK#]"&gt;Sales Order&lt;/a&gt;:

Number: [#SO_NUMBER#]
Customer: [#CUSTOMER#]
Contact: [#CONTACT#]
Salesperson: [#SALESMAN#]</value>
	</data>
	<data name="NewCustomerRequirementwithECCNNotify" xml:space="preserve">
		<value>&lt;b&gt;REQ Number :&lt;/b&gt; &lt;a href="[#HYPERLINK#]"&gt; [#CUSREQ_NUMBER#] &lt;/a&gt;&lt;/br&gt;
&lt;b&gt;Part No :&lt;/b&gt; [#PART#]&lt;/br&gt;
&lt;b&gt;ECCN Code :&lt;/b&gt; [#ECCNCODE#]&lt;/br&gt;
&lt;b&gt;Warning Message :&lt;/b&gt; [#WARNINGMESSAGE#]&lt;/br&gt;
&lt;b&gt;ECCN Message :&lt;/b&gt; [#ECCNMESSAGE#]&lt;/br&gt;</value>
	</data>
	<data name="NotifyECCNSalesOrder" xml:space="preserve">
		<value>&lt;b&gt;SO Number :&lt;/b&gt; &lt;a href="[#HYPERLINK#]"&gt; [#SO_NUMBER#] &lt;/a&gt;&lt;/br&gt;
&lt;b&gt;Part No :&lt;/b&gt; [#PART#]&lt;/br&gt;
&lt;b&gt;ECCN Code :&lt;/b&gt; [#ECCNCODE#]&lt;/br&gt;
&lt;b&gt;Warning Message :&lt;/b&gt; [#WARNINGMESSAGE#]&lt;/br&gt;
&lt;b&gt;ECCN Message :&lt;/b&gt; [#ECCNMESSAGE#]&lt;/br&gt;</value>
	</data>
	<data name="NotifyECCNPO" xml:space="preserve">
		<value>&lt;b&gt;PO Number :&lt;/b&gt; &lt;a href="[#HYPERLINK#]"&gt; [#PO_NUMBER#] &lt;/a&gt;&lt;/br&gt;
&lt;b&gt;Part No :&lt;/b&gt; [#PART#]&lt;/br&gt;
&lt;b&gt;ECCN Code :&lt;/b&gt; [#ECCNCODE#]&lt;/br&gt;
&lt;b&gt;Warning Message :&lt;/b&gt; [#WARNINGMESSAGE#]&lt;/br&gt;
&lt;b&gt;ECCN Message :&lt;/b&gt; [#ECCNMESSAGE#]&lt;/br&gt;</value>
	</data>
	<data name="NotifyReverseLogistics" xml:space="preserve">
		<value>Dear Reverse Logistics Team Member,&lt;/br&gt;A HUBRFQ has been raised for a part that we have recently made an offer for.&lt;/br&gt;Details of the HUBRFQ are here&lt;/br&gt;Please review this &lt;a href="[#HYPERLINK#]"&gt;[#IPO_BOM_NUMBER#]&lt;/a&gt;&lt;/br&gt;Part No : [#PART#]&lt;/br&gt;Company Name : [#COMPANYNAME#]&lt;/br&gt;Please visit and action&lt;/br&gt;Kind Regards&lt;/br&gt;Global Trader&lt;/br&gt;[#PARTLINESRL#]</value>
	</data>
	<data name="NotifyCloseBomManager" xml:space="preserve">
		<value>Please review this &lt;a href="[#HYPERLINK#]"&gt;BOM Manager&lt;/a&gt;:

BOM Manager: [#IPO_BOM_NUMBER#]</value>
	</data>
	<data name="NotifyReleaseBomManager" xml:space="preserve">
		<value>Please review this BOMManager:

BOMManager: &lt;a href="[#HYPERLINK#]"&gt; [#IPO_BOM_NUMBER#] &lt;/a&gt;</value>
	</data>
	<data name="NotifyPurchaseRequestBomManager" xml:space="preserve">
		<value>Please review this BOM Manager:

BOM Manager: &lt;a href="[#HYPERLINK#]"&gt; [#IPO_BOM_NUMBER#]&lt;/a&gt;</value>
	</data>
	<data name="CreditNoteIssueNotification" xml:space="preserve">
		<value>Hello [#SALESMAN#],

Kindly note the below mentioned Credit Note has been issued:

Customer: [#CUSTOMER#]
Contact: [#CONTACT#]
Invoice Number: [#INV_NUMBER#]
Customer PO: [#CUSTOMER_PO#]

Please review this &lt;a href="[#HYPERLINK#]"&gt;Credit Note [#CREDIT_NUMBER#]&lt;/a&gt;

Kind Regards
Global Trader</value>
	</data>
	<data name="CreditLimitApplicationNotification" xml:space="preserve">
		<value>Hello,

Your Credit Limit Application [#CAF#] has been [#APPLICATIONSTATUS#] against  &lt;a href="[#HYPERLINK#]"&gt; [#COMPANYNAME#]&lt;/a&gt;

Kind Regards
Global Trader</value>
	</data>
	<data name="GIQuarantine" xml:space="preserve">
		<value>Hello,&lt;/br&gt;
&lt;b&gt;SO-Line Number:&lt;/b&gt; {0}&lt;/br&gt;
&lt;b&gt;Part No:&lt;/b&gt; {1}&lt;/br&gt;
&lt;b&gt;Message:&lt;/b&gt; The above sales order has been deallocated and deauthorised due to containing a line of quarantined stock, please review and reallocate/ reauthorise the SO.</value>
	</data>
	<data name="ProspectiveOfferEmail" xml:space="preserve">
		<value>&lt;table width="100%" border="1" cellspacing="1" cellpadding="1" bordercolor="#000000"&gt;&lt;tr style="background-color:sandybrown"&gt;&lt;th style="text-align: center; vertical-align: top" class="Textclass"&gt;HUBRFQ No &lt;br /&gt;&lt;br/&gt;Date&lt;/th&gt;        &lt;th style="text-align: center; vertical-align: top" class="Textclass"&gt;Customer&lt;/th&gt;        &lt;th style="text-align: center; vertical-align: top " class="Textclass"&gt;Part No &lt;br /&gt;&lt;br/&gt;CustomerPart No&lt;/th&gt;        &lt;th style="text-align: center; vertical-align: top" class="Textclass"&gt; MFR &lt;br /&gt;&lt;br/&gt;DC&lt;/th&gt;        &lt;th style="text-align: center; vertical-align: top" class="Textclass"&gt;IHS Avg Price &lt;br /&gt;&lt;br/&gt;Lytica Avg Price&lt;/th&gt;        &lt;th style="text-align: center; vertical-align: top" class="Textclass"&gt;Product&lt;br /&gt;&lt;br/&gt;Package&lt;/th&gt;        &lt;th style="text-align: center; vertical-align: top" class="Textclass"&gt; Unit Price &lt;br /&gt; &lt;br/&gt;Qty &lt;/th&gt;  &lt;th style="text-align: center; vertical-align: top; width:150px" class="Textclass"&gt;&lt;/th&gt;&lt;/tr&gt;    #trProspectiveOfferLine#&lt;/table&gt;</value>
	</data>
	<data name="ProspectiveOfferHeaderMail" xml:space="preserve">
		<value>Hello [#SALEMAN#],</value>
	</data>
	<data name="ReverseLogisticStockBody1" xml:space="preserve">
		<value>&lt;table width="100%" border="1" cellspacing="1" cellpadding="1" bordercolor="#000000"&gt;&lt;tr style="background-color:sandybrown"&gt;&lt;th style="text-align: center; vertical-align: top" class="Textclass"&gt;Company Name&lt;/th&gt;&lt;th style="text-align: center; vertical-align: top" class="Textclass"&gt;Part No&lt;/th&gt;&lt;th style="text-align: center; vertical-align: top" class="Textclass"&gt;Mrf&lt;/th&gt;&lt;th style="text-align: center; vertical-align: top" class="Textclass"&gt;Product&lt;/th&gt;&lt;th style="text-align: center; vertical-align: top" class="Textclass"&gt;Package&lt;/th&gt;&lt;th style="text-align: center; vertical-align: top" class="Textclass"&gt;DC&lt;/th&gt;&lt;th style="text-align: center; vertical-align: top" class="Textclass"&gt;Qty&lt;/th&gt;&lt;th style="text-align: center; vertical-align: top" class="Textclass"&gt;Target Price&lt;/th&gt;&lt;th style="text-align: center; vertical-align: top" class="Textclass"&gt;Stock Location&lt;/th&gt;&lt;/tr&gt;#ResverseLogisticStocks#&lt;/table&gt;</value>
	</data>
	<data name="ReverseLogisticStockBody2" xml:space="preserve">
		<value>&lt;table width="100%" border="1" cellspacing="1" cellpadding="1" bordercolor="#000000"&gt;&lt;tr style="background-color:sandybrown"&gt;&lt;th style="text-align: center; vertical-align: top" class="Textclass"&gt;HUBRFQ No&lt;/th&gt;&lt;th style="text-align: center; vertical-align: top" class="Textclass"&gt;Company Name&lt;/th&gt;&lt;th style="text-align: center; vertical-align: top" class="Textclass"&gt;Mrf&lt;/th&gt;&lt;th style="text-align: center; vertical-align: top" class="Textclass"&gt;Product&lt;/th&gt;&lt;th style="text-align: center; vertical-align: top" class="Textclass"&gt;Package&lt;/th&gt;&lt;th style="text-align: center; vertical-align: top" class="Textclass"&gt;DC&lt;/th&gt;&lt;th style="text-align: center; vertical-align: top" class="Textclass"&gt;Qty&lt;/th&gt;&lt;th style="text-align: center; vertical-align: top" class="Textclass"&gt;Target Price&lt;/th&gt;&lt;th style="text-align: center; vertical-align: top" class="Textclass"&gt;Stock Location&lt;/th&gt;&lt;/tr&gt;#ResverseLogisticStocks#&lt;/table&gt;</value>
	</data>
</root>