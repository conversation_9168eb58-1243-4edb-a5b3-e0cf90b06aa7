﻿//Marker     Changed by      Date         Remarks
//[001]      Vinay           04/09/2012   Print Label
//[002]      <PERSON><PERSON><PERSON>     26-Oct-2018  Issue - with GI line edit at a time in two tabs.
//[003]      A<PERSON><PERSON><PERSON>  10/11/2021   add function for the new GI lines query messages.
//[004]      Ab<PERSON>av <PERSON>  19/01/2022   Add ability to CC any GT Users.
//[005]      <PERSON>    21-03-2023   [RP-968] Barcode dropdown box
//[006]      <PERSON>    08-06-2023    [RP-1758]
//[RP-2546]  <PERSON>    01-11-2023    GI Phase 2 Re-Open/ Released Issue
//Code merge for GI Line
using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using System.Data.Common;

namespace Rebound.GlobalTrader.DAL
{

    public abstract class GoodsInLineProvider : DataAccess
    {
        static private GoodsInLineProvider _instance = null;
        /// <summary>
        /// Returns an instance of the provider type specified in the config file
        /// </summary>       
        static public GoodsInLineProvider Instance
        {
            get
            {
                if (_instance == null) _instance = (GoodsInLineProvider)Activator.CreateInstance(Type.GetType(Globals.Settings.GoodsInLines.ProviderType));
                return _instance;
            }
        }
        public GoodsInLineProvider()
        {
            this.ConnectionString = Globals.Settings.GoodsInLines.ConnectionString;
        }

        #region Method Registrations

        /// <summary>
        /// CountForPurchaseOrderLine
        /// Calls [usp_count_GoodsInLine_for_PurchaseOrderLine]
        /// </summary>
        public abstract Int32 CountForPurchaseOrderLine(System.Int32? purchaseOrderLineId);
        /// <summary>
        /// DataListNugget
        /// Calls [usp_datalistnugget_GoodsInLine]
        /// </summary>
        public abstract List<GoodsInLineDetails> DataListNugget(System.Int32? clientId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.String partSearch, System.String cmSearch, System.Int32? receivedBy, System.String airWayBill, System.Boolean? includeInvoiced, System.Int32? purchaseOrderNoLo, System.Int32? purchaseOrderNoHi, System.Int32? goodsInNoLo, System.Int32? goodsInNoHi, System.DateTime? dateReceivedFrom, System.DateTime? dateReceivedTo, System.String supplierInvoice, System.String reference, System.Boolean? recentOnly, System.Boolean? uninspectedOnly, System.Int32? clientSearch, int? IsPoHub, Boolean IsGlobalLogin, int? warehouseno, System.Boolean? IsQueriedTab, System.Int32? QueryProgressStatus, System.Boolean? AS6081);

        /// <summary>
        /// DataListNugget IHS
        /// Calls [usp_datalistnugget_IHSCatalogue]
        /// </summary>
        public abstract List<GoodsInLineDetails> DataListNuggetIHS(System.Int32? clientId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.String partSearch, System.String MfrSearch, System.Int32? countryOforigin, System.String MSL, System.String HtcCode, System.String Description, System.Boolean? recentOnly, int? IsPoHub);
        /// <summary>
        /// DataListNuggetAsReceivedPO
        /// Calls [usp_datalistnugget_GoodsInLine_AsReceivedPO]
        /// </summary>
        public abstract List<GoodsInLineDetails> DataListNuggetAsReceivedPO(System.Int32? clientId, System.Int32? teamId, System.Int32? divisionId, System.Int32? loginId, System.Int32? pageIndex, System.Int32? pageSize, System.Int32? orderBy, System.Int32? sortDir, System.Int32? purchaseOrderNoLo, System.Int32? purchaseOrderNoHi, System.String partSearch, System.Boolean? recentOnly, System.String cmSearch, System.String contactSearch, System.Int32? buyerSearch, System.DateTime? receivedDateFrom, System.DateTime? receivedDateTo, System.String airWayBill, System.String supplierPartSearch, System.String reference, bool? isPoHub);
        /// <summary>
        /// Delete
        /// Calls [usp_delete_GoodsInLine]
        /// </summary>
        public abstract bool Delete(System.Int32? goodsInLineId, System.Int32? updatedBy);
        /// <summary>
        /// Insert
        /// Calls [usp_insert_GoodsInLine]
        /// </summary>
        public abstract Int32 Insert(System.Int32? goodsInNo, System.Int32? purchaseOrderLineNo, System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? packageNo, System.Int32? quantity, System.Double? price, System.Double? shipInCost, System.String qualityControlNotes, System.String location, System.Int32? lotNo, System.Int32? productNo, System.Int32? currencyNo, System.Int32? customerRmaLineNo, System.String supplierPart, System.Byte? rohs, System.Int32? countryOfManufacture, System.Boolean? unavailable, System.String notes, System.String changedFields, System.Int32? countingMethodNo, System.Boolean? serialNosRecorded, System.String partMarkings, System.Int32? updatedBy, System.Double? clientPrice, System.Boolean? reqSerialNo, out System.String strMessage);
        public abstract Int32 InsertIHS(System.String part,System.String partStatus,int? manufacturerNo,DateTime? OriginalEntryDate,int? PackageNo,string Descriptions,string HTSCode,int? CountryOfManufacture,string PackageName,int? UpdatedBy,string MSLLevel,string ECCNCode);

        /// <summary>
        /// Get
        /// Calls [usp_select_GoodsInLine]
        /// </summary>
        public abstract GoodsInLineDetails Get(System.Int32? goodsInLineId, System.Int32? LoginId, System.Int32? ClientId);

        /// <summary>
        /// Get
        /// Calls [usp_select_GoodsInLine]
        /// </summary>
        public abstract GoodsInLineDetails GetTemp(System.Int32? goodsInLineId, System.Int32? LoginId, System.Int32? ClientId);
        /// <summary>
        /// GetListForCustomerRMA
        /// Calls [usp_selectAll_GoodsInLine_for_CustomerRMA]
        /// </summary>
        public abstract List<GoodsInLineDetails> GetListForCustomerRMA(System.Int32? customerRmaId);
        /// <summary>
        /// GetListForCustomerRMALine
        /// Calls [usp_selectAll_GoodsInLine_for_CustomerRMALine]
        /// </summary>
        public abstract List<GoodsInLineDetails> GetListForCustomerRMALine(System.Int32? customerRmaLineId);
        /// <summary>
        /// GetListForGoodsIn
        /// Calls [usp_selectAll_GoodsInLine_for_GoodsIn]
        /// </summary>
        public abstract List<GoodsInLineDetails> GetListForGoodsIn(System.Int32? goodsInId, System.Int32? pageIndex, System.Int32? pageSize);
        /// <summary>
        /// GetListForPurchaseOrder
        /// Calls [usp_selectAll_GoodsInLine_for_PurchaseOrder]
        /// </summary>
        public abstract List<GoodsInLineDetails> GetListForPurchaseOrder(System.Int32? purchaseOrderId);
        /// <summary>
        /// GetListForPurchaseOrderLine
        /// Calls [usp_selectAll_GoodsInLine_for_PurchaseOrderLine]
        /// </summary>
        public abstract List<GoodsInLineDetails> GetListForPurchaseOrderLine(System.Int32? purchaseOrderLineId);
        /// <summary>
        /// Update
        /// Calls [usp_update_GoodsInLine]
        /// </summary>
        //[002] start
        public abstract bool Update(System.Int32? goodsInLineId, System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? packageNo, System.Int32? quantity, System.Double? price, System.Double? shipInCost, System.String qualityControlNotes, System.String location, System.Int32? lotNo, System.Int32? productNo, System.String supplierPart, System.Byte? rohs, System.Int32? countryOfManufacture, System.Int32? currencyNo, System.Boolean? unavailable, System.String changedFields, System.String notes, System.Int32? countingMethodNo, System.Boolean? serialNosRecorded, System.String partMarkings, System.Boolean? updateStock, System.Boolean? updateShipments, System.Int32? updatedBy, System.Double? clientPrice, System.Boolean? reqSerialNo, System.String mslLevel, System.Boolean? printHazardous, System.String previousDLUP, ref string message);
        /// <summary>
        /// UpdateInspect
        /// Calls [usp_update_GoodsInLine_Inspect]
        /// </summary>
        public abstract bool UpdateInspect(System.Int32? goodsInLineId, System.Int32? inspectedBy);
        //[001] code start

        /// <summary>
        /// UpdatePhysicalInspect
        /// Calls [usp_update_GoodsInLine_PhysicalInspect]
        /// </summary>
        /// <param name="goodsInLineId"></param>
        /// <param name="inspectedBy"></param>
        /// <returns></returns>
        public abstract bool UpdatePhysicalInspect(System.Int32? goodsInLineId, System.Int32? inspectedBy);
        /// <summary>
        /// GetDetailsPrintNiceLabelGoodsInLine
        /// Calls [usp_GetDetails_to_PrintNiceLabel_for_GoodsInLine]
        /// </summary>
        public abstract GoodsInLineDetails GetDetailsPrintNiceLabelGoodsInLine(System.Int32? goodsInLineId);
        //[001] code end
        /// <summary>
        /// usp_update_GoodsInLine_NPRStatus
        /// </summary>
        /// <param name="goodsInLineId"></param>
        /// <param name="nprPrintStatus"></param>
        /// <returns></returns>
        public abstract bool UpdateNPRStatus(System.Int32? goodsInLineId, System.Boolean? nprPrintStatus);
        #endregion

        /// <summary>
        /// Returns a new GoodsInLineDetails instance filled with the DataReader's current record data
        /// </summary>        
        protected virtual GoodsInLineDetails GetGoodsInLineFromReader(DbDataReader reader)
        {
            GoodsInLineDetails goodsInLine = new GoodsInLineDetails();
            if (reader.HasRows)
            {
                goodsInLine.GoodsInLineId = GetReaderValue_Int32(reader, "GoodsInLineId", 0); //From: [Table]
                goodsInLine.GoodsInNo = GetReaderValue_Int32(reader, "GoodsInNo", 0); //From: [Table]
                goodsInLine.PurchaseOrderLineNo = GetReaderValue_NullableInt32(reader, "PurchaseOrderLineNo", null); //From: [Table]
                goodsInLine.FullPart = GetReaderValue_String(reader, "FullPart", ""); //From: [Table]
                goodsInLine.Part = GetReaderValue_String(reader, "Part", ""); //From: [Table]
                goodsInLine.ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null); //From: [Table]
                goodsInLine.DateCode = GetReaderValue_String(reader, "DateCode", ""); //From: [Table]
                goodsInLine.PackageNo = GetReaderValue_NullableInt32(reader, "PackageNo", null); //From: [Table]
                goodsInLine.Quantity = GetReaderValue_Int32(reader, "Quantity", 0); //From: [Table]
                goodsInLine.Price = GetReaderValue_Double(reader, "Price", 0); //From: [Table]
                goodsInLine.ShipInCost = GetReaderValue_NullableDouble(reader, "ShipInCost", null); //From: [Table]
                goodsInLine.QualityControlNotes = GetReaderValue_String(reader, "QualityControlNotes", ""); //From: [Table]
                goodsInLine.Location = GetReaderValue_String(reader, "Location", ""); //From: [Table]
                goodsInLine.ProductNo = GetReaderValue_NullableInt32(reader, "ProductNo", null); //From: [Table]
                goodsInLine.LandedCost = GetReaderValue_Double(reader, "LandedCost", 0); //From: [Table]
                goodsInLine.CustomerRMALineNo = GetReaderValue_NullableInt32(reader, "CustomerRMALineNo", null); //From: [Table]
                goodsInLine.SupplierPart = GetReaderValue_String(reader, "SupplierPart", ""); //From: [Table]
                goodsInLine.ROHS = GetReaderValue_Byte(reader, "ROHS", (byte)0); //From: [Table]
                goodsInLine.CountryOfManufacture = GetReaderValue_NullableInt32(reader, "CountryOfManufacture", null); //From: [Table]
                goodsInLine.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null); //From: [Table]
                goodsInLine.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue); //From: [Table]
                goodsInLine.InspectedBy = GetReaderValue_NullableInt32(reader, "InspectedBy", null); //From: [Table]
                goodsInLine.DateInspected = GetReaderValue_NullableDateTime(reader, "DateInspected", null); //From: [Table]
                goodsInLine.CountingMethodNo = GetReaderValue_NullableInt32(reader, "CountingMethodNo", null); //From: [Table]
                goodsInLine.SerialNosRecorded = GetReaderValue_NullableBoolean(reader, "SerialNosRecorded", null); //From: [Table]
                goodsInLine.Unavailable = GetReaderValue_NullableBoolean(reader, "Unavailable", null); //From: [Table]
                goodsInLine.LotNo = GetReaderValue_NullableInt32(reader, "LotNo", null); //From: [Table]
                goodsInLine.Notes = GetReaderValue_String(reader, "Notes", ""); //From: [Table]
                goodsInLine.PartMarkings = GetReaderValue_String(reader, "PartMarkings", ""); //From: [Table]
                goodsInLine.FullSupplierPart = GetReaderValue_String(reader, "FullSupplierPart", ""); //From: [Table]
                goodsInLine.GoodsInId = GetReaderValue_Int32(reader, "GoodsInId", 0); //From: [usp_datalistnugget_GoodsInLine]
                goodsInLine.GoodsInNumber = GetReaderValue_Int32(reader, "GoodsInNumber", 0); //From: [usp_datalistnugget_GoodsInLine]
                goodsInLine.ManufacturerCode = GetReaderValue_String(reader, "ManufacturerCode", ""); //From: [usp_datalistnugget_GoodsInLine]
                goodsInLine.DateReceived = GetReaderValue_DateTime(reader, "DateReceived", DateTime.MinValue); //From: [usp_datalistnugget_GoodsInLine]
                goodsInLine.ReceiverName = GetReaderValue_String(reader, "ReceiverName", ""); //From: [usp_datalistnugget_GoodsInLine]
                goodsInLine.PurchaseOrderNo = GetReaderValue_NullableInt32(reader, "PurchaseOrderNo", null); //From: [usp_datalistnugget_GoodsInLine]
                goodsInLine.DeliveryDate = GetReaderValue_NullableDateTime(reader, "DeliveryDate", null); //From: [usp_datalistnugget_GoodsInLine]
                goodsInLine.PurchaseOrderNumber = GetReaderValue_NullableInt32(reader, "PurchaseOrderNumber", null); //From: [usp_datalistnugget_GoodsInLine]
                goodsInLine.CompanyNo = GetReaderValue_Int32(reader, "CompanyNo", 0); //From: [usp_datalistnugget_GoodsInLine]
                goodsInLine.CompanyName = GetReaderValue_String(reader, "CompanyName", ""); //From: [usp_datalistnugget_GoodsInLine]
                goodsInLine.AirWayBill = GetReaderValue_String(reader, "AirWayBill", ""); //From: [usp_datalistnugget_GoodsInLine]
                goodsInLine.RowCnt = GetReaderValue_NullableInt32(reader, "RowCnt", null); //From: [usp_datalistnugget_GoodsInLine]
                goodsInLine.QuantityOrdered = GetReaderValue_Int32(reader, "QuantityOrdered", 0); //From: [usp_datalistnugget_GoodsInLine_AsReceivedPO]
                goodsInLine.ContactNo = GetReaderValue_Int32(reader, "ContactNo", 0); //From: [usp_datalistnugget_GoodsInLine_AsReceivedPO]
                goodsInLine.ContactName = GetReaderValue_String(reader, "ContactName", ""); //From: [usp_datalistnugget_GoodsInLine_AsReceivedPO]
                goodsInLine.SupplierInvoice = GetReaderValue_String(reader, "SupplierInvoice", ""); //From: [usp_datalistnugget_GoodsInLine_AsReceivedPO]
                goodsInLine.InvoiceAmount = GetReaderValue_NullableDouble(reader, "InvoiceAmount", null); //From: [usp_datalistnugget_GoodsInLine_AsReceivedPO]
                goodsInLine.CurrencyNo = GetReaderValue_NullableInt32(reader, "CurrencyNo", null); //From: [usp_datalistnugget_GoodsInLine_AsReceivedPO]
                goodsInLine.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", ""); //From: [usp_datalistnugget_GoodsInLine_AsReceivedPO]
                goodsInLine.CountingMethodDescription = GetReaderValue_String(reader, "CountingMethodDescription", ""); //From: [usp_select_GoodsInLine]
                goodsInLine.LineNotes = GetReaderValue_String(reader, "LineNotes", ""); //From: [usp_select_GoodsInLine]
                goodsInLine.ClientNo = GetReaderValue_Int32(reader, "ClientNo", 0); //From: [usp_select_GoodsInLine]
                goodsInLine.PackageName = GetReaderValue_String(reader, "PackageName", ""); //From: [usp_select_GoodsInLine]
                goodsInLine.PackageDescription = GetReaderValue_String(reader, "PackageDescription", ""); //From: [usp_select_GoodsInLine]
                goodsInLine.ProductName = GetReaderValue_String(reader, "ProductName", ""); //From: [usp_select_GoodsInLine]
                goodsInLine.ProductDescription = GetReaderValue_String(reader, "ProductDescription", ""); //From: [usp_select_GoodsInLine]
                goodsInLine.ProductDutyCode = GetReaderValue_String(reader, "ProductDutyCode", ""); //From: [usp_select_GoodsInLine]
                goodsInLine.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", ""); //From: [usp_select_GoodsInLine]
                goodsInLine.CountryOfManufactureName = GetReaderValue_String(reader, "CountryOfManufactureName", ""); //From: [usp_select_GoodsInLine]
                goodsInLine.InspectorName = GetReaderValue_String(reader, "InspectorName", ""); //From: [usp_select_GoodsInLine]
                goodsInLine.CustomerRMANo = GetReaderValue_NullableInt32(reader, "CustomerRMANo", null); //From: [usp_select_GoodsInLine]
                goodsInLine.CustomerRMANumber = GetReaderValue_NullableInt32(reader, "CustomerRMANumber", null); //From: [usp_select_GoodsInLine]
                goodsInLine.CurrencyDescription = GetReaderValue_String(reader, "CurrencyDescription", ""); //From: [usp_select_GoodsInLine]
                goodsInLine.ReceivedBy = GetReaderValue_Int32(reader, "ReceivedBy", 0); //From: [usp_select_GoodsInLine]
                goodsInLine.DivisionNo = GetReaderValue_NullableInt32(reader, "DivisionNo", null); //From: [usp_select_GoodsInLine]
                goodsInLine.TeamNo = GetReaderValue_NullableInt32(reader, "TeamNo", null); //From: [usp_select_GoodsInLine]
                goodsInLine.StockNo = GetReaderValue_NullableInt32(reader, "StockNo", null); //From: [usp_select_GoodsInLine]
                goodsInLine.Reference = GetReaderValue_String(reader, "Reference", ""); //From: [usp_select_GoodsInLine]
                goodsInLine.LotName = GetReaderValue_String(reader, "LotName", ""); //From: [usp_select_GoodsInLine]
                goodsInLine.PurchaseOrderLineShipInCost = GetReaderValue_Double(reader, "PurchaseOrderLineShipInCost", 0); //From: [usp_select_GoodsInLine]
                goodsInLine.ChangedFields = GetReaderValue_String(reader, "ChangedFields", ""); //From: [usp_select_GoodsInLine]
                goodsInLine.UpdateStock = GetReaderValue_NullableBoolean(reader, "UpdateStock", null); //From: [usp_select_GoodsInLine]
                goodsInLine.UpdateShipments = GetReaderValue_NullableBoolean(reader, "UpdateShipments", null); //From: [usp_select_GoodsInLine]
            }
            return goodsInLine;
        }

        /// <summary>
        /// Returns a collection of GoodsInLineDetails objects with the data read from the input DataReader
        /// </summary>                
        protected virtual List<GoodsInLineDetails> GetGoodsInLineCollectionFromReader(DbDataReader reader)
        {
            List<GoodsInLineDetails> goodsInLines = new List<GoodsInLineDetails>();
            while (reader.Read()) goodsInLines.Add(GetGoodsInLineFromReader(reader));
            return goodsInLines;
        }


        public abstract Int32 InsertSerialNo(System.String subGroup, System.String serialNo, System.Int32? goodsInId, System.Int32? goodsInLineId, System.Int32? updatedBy, out System.String validateMessage, out System.Int32 totalSerial);

        public abstract Int32 UpdateSerialNo(System.Int32? serialId, System.String subGroup, System.String serialNo, System.Int32? goodsInId, System.String status, System.Int32? goodsInLineId, System.Int32? updatedBy, out System.String validateMessage);


        public abstract List<GoodsInLineDetails> GetDataGrid(System.Int32? goodsInId, System.Int32? goodsInLineId, System.Int32? updatedBy);

        public abstract Int32 InsertAllSerialNo(System.Int32? goodsInId, System.Int32? goodsInLineId, System.Int32? updatedBy, out System.String validateMessage);

        public abstract List<GoodsInLineDetails> GetSerial(System.Int32? goodsInLineId);

        public abstract List<GoodsInLineDetails> AutoSearch(System.String nameSearch, System.String groupName);

        public abstract List<GoodsInLineDetails> DropDown(System.Int32? goodsInLineNo, System.Int32? invoiceLineNo);

        public abstract List<GoodsInLineDetails> AutoSearchGroup(System.String nameSearch);

        public abstract Int32 DeleteSerialNo(System.Int32? serialNoId, System.String status, System.Int32? goodsInId, System.Int32? goodsInLineId, System.Int32? updatedBy);
        /// <summary>
        /// usp_itemsearch_GoodsInSerialNo
        /// </summary>
        public abstract List<GoodsInLineDetails> GISerialSearch(System.Int32? clientId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.String giSerialGroup, System.String serialNoLo, System.Int32? serialNoHi, System.DateTime? dateReceivedFrom, System.DateTime? dateReceivedTo, System.Int32? goodsInLineNo, System.Int32? invoiceLineNo);
        /// <summary>
        /// usp_get_AttachedSerialNo
        /// </summary>
        public abstract List<GoodsInLineDetails> GetAttachedSerial(System.Int32? clientId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.Int32? goodsInLineNO, System.Int32? salesOrderLineNo, System.DateTime? dateReceivedFrom, System.DateTime? dateReceivedTo, System.Int32? allocationNo);
        public abstract List<GoodsInLineDetails> GetAttachedSerial(System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.Int32? customerRMANo, System.Int32? customerRMALineNo);

        /// <summary>
        /// usp_delete_AttachedSerial
        /// </summary>
        public abstract Int32 DeleteAttachedSerial(System.Int32? serialId, System.Int32? goodsInLineId, System.Int32? soLineId, System.Int32? updatedBy, System.Int32? allocationNo);

        /// <summary>
        /// usp_update_SerialBySO
        /// </summary>
        public abstract Int32 UpdateSerialBySO(System.String subGroup, System.Int32? goodsInLineId, System.Int32? soLineId, System.Int32? qtyToShpped, System.Int32? allocatedId, out System.String validateMessage);


        /// <summary>
        /// usp_delete_SerialBySO
        /// </summary>
        public abstract Int32 DeleteSerialBySO(System.Int32? goodsInLineId, System.Int32? soLineId, System.Int32? AllocatedId);
        public abstract Int32 DeattachCRMASerial(System.Int32? invoiceLineNo);


        public abstract List<GoodsInLineDetails> GetReasonDetailByPart(System.String part);
        /// <summary>
        /// 
        /// </summary>
        /// <param name="goodsInLineNo"></param>
        /// <returns></returns>
        public abstract List<GoodsInLineDetails> GetShipCostHistory(System.Int32? goodsInLineNo);
        /// <summary>
        /// usp_itemsearch_GoodsInTempSerialNo
        /// </summary>
        public abstract List<GoodsInLineDetails> GITempSerialSearch(System.Int32? clientId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.Int32? goodsInNo, System.Int32? goodsInLineNo, System.Int32? loginNo);
        public abstract Int32 DeleteAttachedSerial(System.Int32? serialId, System.Int32? customerRMANo, System.Int32? customerRMALineNo, System.Int32? updatedBy);
        public abstract Int32 AttachSerialByCRMA(System.String subGroup, System.Int32? invoiceLineNo, System.Int32? customerRMANo, System.Int32? customerRMALineNo, out System.String validateMessage);

        /// <summary>
		/// Update
		/// Calls [usp_split_GoodsInLine]
		/// </summary>
        //[002] start
        public abstract bool SplitGI(System.Int32? goodsInLineId, System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? packageNo, System.Int32? quantity, System.Double? price, System.Double? shipInCost, System.String qualityControlNotes, System.String location, System.Int32? lotNo, System.Int32? productNo, System.String supplierPart, System.Byte? rohs, System.Int32? countryOfManufacture, System.Int32? currencyNo, System.Boolean? unavailable, System.String changedFields, System.String notes, System.Int32? countingMethodNo, System.Boolean? serialNosRecorded, System.String partMarkings, System.Boolean? updateStock, System.Boolean? updateShipments, System.Int32? updatedBy, System.Double? clientPrice, System.Boolean? reqSerialNo, System.String mslLevel, System.Boolean? printHazardous, System.String previousDLUP, System.String SerialNo, ref string message);
        /// <summary>
        /// SaveShortShipment
        /// Calls [usp_insert_ShortShipment]
        /// </summary>
        /// <param name="ID"></param>
        /// <param name="Supplier"></param>
        /// <param name="SalesContact"></param>
        /// <param name="AirWayBill"></param>
        /// <param name="GoodsIn"></param>
        /// <param name="ReceivedDate"></param>
        /// <param name="RaisedBy"></param>
        /// <param name="Buyer"></param>
        /// <param name="PartNo"></param>
        /// <param name="Manufacturer"></param>
        /// <param name="QuantityOrder"></param>
        /// <param name="QuantityAdvised"></param>
        /// <param name="QuantityReceived"></param>
        /// <param name="ShortageQuantity"></param>
        /// <param name="ShortageValue"></param>
        /// <param name="loginID"></param>
        /// <param name="clientID"></param>
        /// <param name="message"></param>
        /// <param name="ShortShipmentId"></param>
        /// <returns></returns>
        public abstract bool SaveShortShipment(System.Int32 ID, System.Int32? Supplier, System.Int32? PurchaseOrderNo, System.Int32? SalesContact, System.String Reference, System.Int32 GoodsIn, System.DateTime? ReceivedDate, System.Int32? RaisedBy, System.Int32? Buyer, System.String PartNo, System.Int32? Manufacturer, System.Int32? QuantityOrder, System.String QuantityAdvised, System.Int32 QuantityReceived, System.Int32 ShortageQuantity, System.Double? ShortageValue, System.Int32 loginID, System.Int32? clientID, ref System.String message, ref System.Int32 ShortShipmentId, System.Boolean? IsPOHub, System.Int32? SupportTeamMemberNo, ref System.String NoReplyEmail, ref System.Int32 NoReplyId);

        public abstract List<GoodsInLineDetails> GetHICStatus();
        public abstract List<GoodsInLineDetails> GetQueryHICStatus();
        /// <summary>
        /// calls[usp_update_GoodsInLineEdit]
        /// </summary>
        /// <param name="ID"></param>
        /// <param name="actShipInCost"></param>
        /// <param name="qCNotes"></param>
        /// <param name="location"></param>
        /// <param name="lotNo"></param>
        /// <param name="blnUpdateStock"></param>
        /// <param name="blnUpdateShipments"></param>
        /// <param name="loginID"></param>
        /// <param name="isFullQuantityReceived"></param>
        /// <param name="quantityReceived"></param>
        /// <param name="isPartNumberCorrect"></param>
        /// <param name="correctPartNo"></param>
        /// <param name="isManufacturerCorrect"></param>
        /// <param name="correctManufacturerNo"></param>
        /// <param name="isDateCodeCorrect"></param>
        /// <param name="correctDateCode"></param>
        /// <param name="isPackageCorrect"></param>
        /// <param name="correctPackageNo"></param>
        /// <param name="isMSLCorrect"></param>
        /// <param name="correctMslNo"></param>
        /// <param name="isHICCorrect"></param>
        /// <param name="correctHIC"></param>
        /// <param name="isRohsStatusCorrect"></param>
        /// <param name="correctStatusNo"></param>
        /// <param name="countryOfManufacture"></param>
        /// <param name="countingMethodNo"></param>
        /// <param name="isSerialNosRecorded"></param>
        /// <param name="isLotCodeReq"></param>
        /// <param name="isEnhancedInpection"></param>
        /// <param name="generalInspectionNotes"></param>
        /// <param name="isBakingYes"></param>
        /// <param name="isBakingNo"></param>
        /// <param name="isBakingNA"></param>
        /// <param name="isInspectionConducted"></param>
        /// <param name="supplierPart"></param>
        /// <param name="productNo"></param>
        /// <param name="price"></param>
        /// <param name="clientPrice"></param>
        /// <param name="unavailable"></param>
        /// <param name="changedFields"></param>
        /// <param name="currencyNo"></param>
        /// <param name="reqSerailNo"></param>
        /// <param name="partMarkings"></param>
        /// <param name="lineNotes"></param>
        /// <param name="PrintHazWar"></param>
        /// <param name="previousDLUP"></param>
        /// <param name="message"></param>
        /// <returns></returns>
        //[005] added two parameters [System.Int16? HasBarCode, System.String BarcodeRemarks] in UpdateGILineEdit method
        public abstract bool UpdateGILineEdit(System.Int32? ID, System.Double? actShipInCost, System.String QCNotes, System.String Location, System.Int32? LotNo, System.Boolean? blnUpdateStock, System.Boolean? blnUpdateShipments, System.Int32? loginID, System.Boolean? IsFullQuantityReceived, System.Int32? QuantityReceived, System.Boolean? IsPartNumberCorrect, System.String CorrectPartNo, System.Boolean? IsManufacturerCorrect, System.Int32? CorrectManufacturerNo, System.Boolean? IsDateCodeCorrect, System.String CorrectDateCode, System.Boolean? IsPackageCorrect, System.Int32? CorrectPackageNo, System.Boolean? IsMSLCorrect, System.String CorrectMslNo, System.Boolean? IsHICCorrect, System.String CorrectHIC, System.Boolean? IsRohsStatusCorrect, System.Int32? CorrectStatusNo, System.Int32? CountryOfManufacture, System.Int32? CountingMethodNo, System.Boolean? IsSerialNosRecorded, System.Boolean? IsLotCodeReq, System.Boolean? IsEnhancedInpection, System.String GeneralInspectionNotes, System.Boolean? IsBakingYes, System.Boolean? IsBakingNo, System.Boolean? IsBakingNA, System.Boolean? IsInspectionConducted, System.String SupplierPart, System.Int32? ProductNo, System.Double Price, System.Double? ClientPrice, System.Boolean? Unavailable, System.String ChangedFields, System.Int32? CurrencyNo, System.Boolean? ReqSerailNo, System.String PartMarkings, System.String LineNotes, System.Boolean? PrintHazWar, System.String PreviousDLUP, System.Double shipInCost, System.Int32? quantity, ref System.String message, System.Boolean? IsDateCodeRequired, System.String PackageBreakdownInfo, System.Int32? HICStatus, System.String PackBreakDownJSON, System.Boolean? IsBySendQueryBtn, System.Int32? ActeoneTestStatus, System.Int32? IsopropryleStatus, System.String ActeoneTest, System.String Isopropryle, System.String QueryBakingLevel, System.Int32? EnhInpectionReqId, System.String PrintDateCode, System.Boolean? IsPackageBreakdownChnaged,
            System.Int32? HasBarCode, System.String BarcodeRemarks, System.String PartNoQuery, System.String ManufacturerQuery, System.String PackagingTypeQuery, System.String MslQuery, System.String RohsQuery, System.Boolean? ReaiseGeneralQuery);
        /// <summary>
        /// calls [usp_select_PackagingBreakdown]
        /// </summary>
        /// <param name="goodsInLineId"></param>
        /// <returns></returns>
        public abstract List<PackagingBreakdown> GetPackagingBreakdownList(System.Int32 goodsInLineId);
        public abstract List<DateCode> GetDateCodeList(System.Int32 goodsInLineId);
        /// <summary>
        /// Calls[usp_select_GoodsInVariance_Query]
        /// </summary>
        /// <param name="goodsInLineId"></param>
        /// <returns></returns>
        public abstract GoodsInLineDetails GetGIQueryData(System.Int32? goodsInLineId);
        /// <summary>
        /// Calls [usp_select_GIUpdatedLine]
        /// </summary>
        /// <param name="goodsInLineId"></param>
        /// <returns></returns>
        public abstract GoodsInLineDetails GetGILineData(System.Int32? goodsInLineId);
        /// <summary>
        /// Calls [usp_update_GIQuery]
        /// </summary>
        /// <param name="IsSales"></param>
        /// <param name="IsPurchasing"></param>
        /// <param name="IsQualityApproval"></param>
        /// <param name="LoginID"></param>
        /// <param name="GoodsInLineId"></param>
        /// <returns></returns>
        public abstract bool UpdateGiQuery(System.Boolean IsSalesNotify, System.Boolean IsPurchaseNotify, System.Boolean IsQualityNotify, System.Int32? LoginID, System.Int32 GoodsInLineId, System.Int32 GoodsInId, System.String Query);
        /// <summary>
        /// call [usp_get_GILine_Quarantine_Status]
        /// </summary>
        /// <param name="GILineId"></param>
        /// <returns></returns>
        public abstract bool GetQuarantineStatus(System.Int32 GILineId);
        /// <summary>
        /// Calls [usp_update_NotifyQuery]
        /// </summary>
        /// <param name="ID"></param>
        /// <param name="GILineQueryStatus"></param>
        /// <param name="IsPDFReportRequired"></param>
        /// <param name="IsQuarantineProduct"></param>
        /// <param name="QueryReply"></param>
        /// <param name="LoginId"></param>
        /// <returns></returns>
        public abstract bool NotifyQuery(System.Int32 ID, System.Int32? QueryApprovedStatusSales, System.Int32? QueryApprovedStatusPurchase, System.Int32? QueryApprovedStatusQuality, System.Boolean IsPDFReportRequired, System.Boolean IsQuarantineProduct, System.String QueryReply, System.Int32? LoginId, System.Int32 LoginType);
        public abstract List<GoodsInLineDetails> GIQueryStatusDropDown(System.Int32? IsPartialGIQueryStatus);
        /// <summary>
        /// calls [usp_update_GILine_Quarantined]
        /// </summary>
        /// <param name="iD"></param>
        /// <param name="loginID"></param>
        /// <param name="Quarantine"></param>
        /// <param name="message"></param>
        /// <returns></returns>
        public abstract bool UpdateGILineQuarantine(int iD, int loginID, bool Quarantine,int? clientId, ref string message);
        /// <summary>
        /// Calls [usp_get_GILine_SaleEmail]
        /// </summary>
        /// <param name="GILineID"></param>
        /// <returns></returns>
        public abstract int GetSaleEmail(int GILineID, out int StockId, out string partNo, out string strSOLineNumber);
        /// <summary>
        /// Get
        /// Calls [usp_SelectAll_GILines_QueryMessages]
        /// </summary>
        public abstract List<GoodsInLineDetails> GetGILineQueryMessage(System.Int32? goodsInId, System.Int32? GiLineNo, System.Int32? LoginId, System.Int32? ClientId);

        /// Get
        /// Calls [usp_Previous_GILines_QueryMessages]
        /// </summary>
        public abstract List<GoodsInLineDetails> GetPreviousChatOnGILine(System.Int32? GI_QueryId);
        public abstract List<GoodsInLineDetails> GetRelatedReceiptLines(System.Int32? goodsInId, System.Int32? GiLineNo, System.Int32? LoginId);

        /// <summary>
        /// Get
        /// Calls [usp_Select_GILines_Approvals]
        /// </summary>
        public abstract List<GoodsInLineDetails> GetGILineApprovals(System.Int32? goodsInId, System.Int32? GiLineNo);
        /// <summary>
        /// Get
        /// Calls [usp_selectAll_PDF_for_GoodsInLine]
        /// </summary>
        public abstract List<PDFDocumentDetails> GetPDFListForGoodsInLine(System.Int32? GoodsInLineId);
        public abstract List<SupplierPoApprovalDetails> GetImageData(System.Int32? GoodsInLineId);
        /// <summary>
        /// Get
        /// Calls [usp_Insert_GILineQueryMessage]
        /// </summary>
        public abstract List<GoodsInLineDetails> AddGILineQueryMessage(System.Int32? GI_QueryId, System.Int32? goodsInId, System.Int32? GiLineNo, System.Int32? LoginId, System.String QueryMessage, System.Boolean? IsSalesNotify, System.Boolean? IsQualityNotify, System.Boolean? IsPurchasingNotify, System.Int32? ClientNo, System.String CCUserId, System.String CCGroupIDs);

        /// <summary>
        /// AddGILineQueryApprovalResponce
        /// Calls [usp_Insert_GILineQueryApprovalResponce]
        /// </summary>

        public abstract List<GoodsInLineDetails> AddGILineQueryApprovalResponce(System.Int32? GI_QueryId, System.Int32? goodsInId, System.Int32? GiLineNo, System.Int32? LoginId, System.String QueryMessage, System.Int32? SalesApprovalStatus, System.Int32? QualityApprovalStatus, System.Int32? PurchasingApprovalStatus, System.String CCUserId, System.Int32? ClientNo, System.String CCGroupIDs, System.String ApproverHtml, System.Int32? TotalCheckBoxcount, System.Int32? CheckedTotalCheckBoxcount, System.String GetEnableCheckBoxIds);

        /// <summary>
        /// AddGILineQueryApprovalResponce
        /// Calls [usp_GetQueryMessagesCCUsers]
        /// </summary>
        public abstract List<GoodsInLineDetails> GetCCUserFoEmail(System.Int32? GI_QueryId);
        /// <summary>
        /// Get
        /// Calls [usp_Insert_ChangeApprover]
        /// </summary>
        public abstract List<GoodsInLineDetails> ChangeApprover(System.Int32? GI_QueryId, System.Int32? NewSalesApprover, System.Int32? NewPurchasingApprover, System.Int32? LoginId, System.Int32? ClientNo);
        //[001] code start
        /// <summary>
        /// Insert
        /// Calls [usp_insert_StockImageByGILineForSAN]
        /// </summary>
        public abstract Int32 InsertImage(System.Int32? GILineID, System.String caption, System.String GILineImageName, System.Int32? updatedBy);
        //[001] code start
        /// <summary>
        /// Insert
        /// Calls [[usp_insert_GoodsInLinePDF]]
        /// </summary>
        public abstract Int32 InsertPDF(System.Int32? GILineID, System.String caption, System.String GILinePdfName, System.Int32? updatedBy);

        public abstract List<StockImageDetails> GetGILineImageList(System.Int32? GILineId, System.String fileType, System.Int32? GIId);

        public abstract Int32 InsertGILineImage(System.Int32? resultNo, System.String caption, System.String ImageName, System.Int32? updatedBy, System.Int32? GIId);
        public abstract bool DeleteGILineImage(System.Int32? ImageNo);

        #region Add/Edit/Get Lot Code for GI Edit 
        /// <summary>
        /// Calls [usp_itemsearch_GoodsInTempLotNo]
        /// </summary>
        /// <param name="clientId"></param>
        /// <param name="orderBy"></param>
        /// <param name="sortDir"></param>
        /// <param name="pageIndex"></param>
        /// <param name="pageSize"></param>
        /// <param name="goodsInNo"></param>
        /// <param name="goodsInLineNo"></param>
        /// <param name="loginNo"></param>
        /// <returns></returns>
        public abstract List<GoodsInLineDetails> GITempLotSearch(System.Int32? clientId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.Int32? goodsInNo, System.Int32? goodsInLineNo, System.Int32? loginNo);
        /// <summary>
        /// Calls [usp_insert_LotNo]
        /// </summary>
        /// <param name="subGroup"></param>
        /// <param name="serialNo"></param>
        /// <param name="goodsInId"></param>
        /// <param name="goodsInLineId"></param>
        /// <param name="updatedBy"></param>
        /// <param name="validateMessage"></param>
        /// <param name="totalSerial"></param>
        /// <returns></returns>
        public abstract Int32 InsertLotNo(System.String subGroup, System.String serialNo, System.Int32? goodsInId, System.Int32? goodsInLineId, System.Int32? updatedBy, out System.String validateMessage, out System.Int32 totalSerial);
        /// <summary>
        /// Calls [usp_update_LotNo]
        /// </summary>
        /// <param name="lotId"></param>
        /// <param name="subGroup"></param>
        /// <param name="lotNo"></param>
        /// <param name="goodsInId"></param>
        /// <param name="status"></param>
        /// <param name="goodsInLineId"></param>
        /// <param name="updatedBy"></param>
        /// <param name="validateMessage"></param>
        /// <returns></returns>
        public abstract Int32 UpdateLotNo(System.Int32? lotId, System.String subGroup, System.String lotNo, System.Int32? goodsInId, System.String status, System.Int32? goodsInLineId, System.Int32? updatedBy, out System.String validateMessage);
        /// <summary>
        /// Calls [usp_insert_AllLotNo]
        /// </summary>
        /// <param name="goodsInId"></param>
        /// <param name="goodsInLineId"></param>
        /// <param name="updatedBy"></param>
        /// <param name="validateMessage"></param>
        /// <returns></returns>
        public abstract Int32 InsertAllLotNo(System.Int32? goodsInId, System.Int32? goodsInLineId, System.Int32? updatedBy, out System.String validateMessage);
        /// <summary>
        /// Calls [usp_select_AllLotNo]
        /// </summary>
        /// <param name="goodsInLineId"></param>
        /// <returns></returns>
        public abstract List<GoodsInLineDetails> GetLot(System.Int32? goodsInLineId);
        /// <summary>
        /// Calls [usp_delete_LotNo]
        /// </summary>
        /// <param name="lotNoId"></param>
        /// <param name="status"></param>
        /// <param name="goodsInId"></param>
        /// <param name="goodsInLineId"></param>
        /// <param name="updatedBy"></param>
        /// <returns></returns>
        public abstract Int32 DeleteLotNo(System.Int32? lotNoId, System.String status, System.Int32? goodsInId, System.Int32? goodsInLineId, System.Int32? updatedBy);
        #endregion
        /// <summary>
        /// calls [usp_select_MyGIQueries]
        /// </summary>
        /// <param name="loginId"></param>
        /// <param name="clientId"></param>
        /// <returns></returns>
        public abstract List<GoodsInLineDetails> GetMyGIQueries(System.Int32? loginId, System.Int32? clientId);
        /// <summary>
        /// calls [GetEnhancedInspection]
        /// </summary>
        /// <returns></returns>
        public abstract List<GoodsInLineDetails> GetEnhancedInspection();
        public abstract List<GoodsInLineDetails> GetMFRLabelListJSON();
        public abstract Int32 DraftGILineQueryMessage(System.Int32? GI_QueryId, System.Int32 GoodsInId, System.Int32? GoodsInLineId, System.Int32? LoginID, System.String QueryMessage);
        public abstract Int32 RenameCaption(System.Int32? AttachmentId, System.String Caption, System.String Type, System.Int32? LoginId);
        public abstract bool CheckDeleteAttcmntPermission(int? ClientNo, int? LoginUser);
        public abstract List<GoodsInLineDetails> BulkAttachmentDelete(System.String ImageAttachments, System.String PdfAttachments);
        
        public abstract Int32 StockAttachmentFileCountByFileName(System.String FileName, System.String FileType); //[006]
        public abstract bool DeleteGILine_PDF(int PDFDocId);
        public abstract bool DeleteGILine_Image(int ImageId);
        public abstract string GetPackagingBreakdownHtml(System.Int32 goodsInLineId, System.Boolean? IsPDF);
        /// <summary>
        /// calls [usp_select_MyQualityGIQueries]
        /// </summary>
        /// <param name="loginId"></param>
        /// <param name="clientId"></param>
        /// <returns></returns>
        public abstract List<GoodsInLineDetails> MyQualityGIQueries(System.Int32? loginId, System.Int32? clientId);

        //[005] start
        public abstract List<GoodsInLineDetails> GetBarcodeStatusList(System.Int32? loginId, System.Int32? clientId);

        //[005] end
        /// <summary>
        /// calls [usp_GILine_StartOrCloseInspection]
        /// </summary>
        /// <param name="iD"></param>
        /// <param name="Comment"></param>
        /// <param name="LoginId"></param>
        /// <param name="InspectionOption"></param>
        /// <returns></returns>
        public abstract bool StartOrCloseInspection(int iD, string Comment, System.Int32? LoginId, System.Int32? InspectionOption);

        /// <summary>
        /// usp_GI_InspectionsLog
        /// </summary>
        public abstract List<GoodsInLineDetails> GetInspectionHistory(System.Int32? goodsInLineNo);

        /// <summary>
        /// GetCloseInspectionData
        /// Calls [usp_select_CloseInspectionData]
        /// </summary>
        public abstract GoodsInLineDetails GetCloseInspectionData(System.Int32? goodsInLineId);

        public abstract GoodsInLineDetails GetGiLineReleaseStatus(System.Int32? goodsInLineId); //[RP-2546]

        public abstract GoodsInLineDetails GetGiLineInspectionStatus(System.Int32? goodsInLineId); //[RP-2546]

        /// <summary>
        /// Get
        /// Calls [usp_select_GoodsInLineSerialNo]
        /// </summary>
        public abstract List<GoodsInLineDetails> GetSerialNoForSplit(System.Int32? goodsInLineId);

    }
}