//---------------------------------------------------------------------------------------------------------
// RP 04.12.2009:
// - allow sources for new lines to be set on permissions
//---------------------------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using Rebound.GlobalTrader.Site;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.Forms
{
    public partial class CreditLines_Add : Base
    {

        #region Locals
        protected FlexiDataTable _tblLines;
        protected RadioButtonList _radSelectSource;
        protected List<string> _lstSources = new List<string>();

        #endregion

        #region Properties

        private bool _blnCanAddFromCRMALine = true;
        public bool CanAddFromCRMALine
        {
            get { return _blnCanAddFromCRMALine; }
            set { _blnCanAddFromCRMALine = value; }
        }

        private bool _blnCanAddFromInvoiceLine = true;
        public bool CanAddFromInvoiceLine
        {
            get { return _blnCanAddFromInvoiceLine; }
            set { _blnCanAddFromInvoiceLine = value; }
        }

        private bool _blnCanAddFromService = true;
        public bool CanAddFromService
        {
            get { return _blnCanAddFromService; }
            set { _blnCanAddFromService = value; }
        }

        private bool _blnCanAddFromClientInvoice = true;
        public bool CanAddFromClientInvoice
        {
            get { return _blnCanAddFromClientInvoice; }
            set { _blnCanAddFromClientInvoice = value; }
        }
        #endregion

        #region Overrides

        /// <summary>
        /// OnInit
        /// </summary>
        /// <param name="e"></param>
        protected override void OnInit(EventArgs e)
        {
            base.OnInit(e);
            WireUpControls();
            TitleText = Functions.GetGlobalResource("FormTitles", "CreditLines_Add");
            AddScriptReference("Controls.Nuggets.CreditLines.Add.CreditLines_Add.js");
            SetupTables();
        }

        /// <summary>
        /// OnPreRender
        /// </summary>
        /// <param name="e"></param>
        protected override void OnPreRender(EventArgs e)
        {
            SetupSelectSourceScreen();
            SetupScriptDescriptors();
            base.OnPreRender(e);
        }

        #endregion

        /// <summary>
        /// Setup controls for Select Source screen
        /// </summary>
        private void SetupSelectSourceScreen()
        {
            _radSelectSource = (RadioButtonList)FindFieldControl("ctlSelectSource", "radSelectSource");
            if (_blnCanAddFromCRMALine && !SessionManager.IsPOHub.Value) AddRadioButton("FromCustomerRMALine", "CRMA");
            if (_blnCanAddFromInvoiceLine) AddRadioButton("FromInvoiceLine",  "INVOICE" );
            if (_blnCanAddFromService) AddRadioButton("FromService", "SERVICE");
          //  if (_blnCanAddFromClientInvoice) AddRadioButton("FromClientInvoice", "ClientInvoice");
            _radSelectSource.SelectedIndex = 0;
        }

        private void AddRadioButton(string strResourceTitle, string strJavascriptType)
        {
            _radSelectSource.Items.Add(new ListItem(Functions.GetGlobalResource("Misc", strResourceTitle)));
            _lstSources.Add(strJavascriptType);
        }

        private void SetupTables()
        {
            _tblLines.Columns.Add(new FlexiDataColumn("ClientInvoice", WidthManager.GetWidth(WidthManager.ColumnWidth.SystemDocumentNumber), true));
            // _tblLines.Columns.Add(new FlexiDataColumn("Company", Unit.Empty, true));
            _tblLines.Columns.Add(new FlexiDataColumn("PartNo", Unit.Empty, true));
            _tblLines.Columns.Add(new FlexiDataColumn("DateInvoiced", WidthManager.GetWidth(WidthManager.ColumnWidth.Date), true));
            _tblLines.Columns.Add(new FlexiDataColumn("Price", WidthManager.GetWidth(WidthManager.ColumnWidth.CurrencyValue), true));
            _tblLines.Columns.Add(new FlexiDataColumn("Quantity", WidthManager.GetWidth(WidthManager.ColumnWidth.Quantity), true));
            _tblLines.Columns.Add(new FlexiDataColumn("GoodsIn", WidthManager.GetWidth(WidthManager.ColumnWidth.ExternalCompanyDocument), true));
            _tblLines.Columns.Add(new FlexiDataColumn("IPO", WidthManager.GetWidth(WidthManager.ColumnWidth.SystemDocumentNumber), true));
            _tblLines.Columns.Add(new FlexiDataColumn("DebitNumber", WidthManager.GetWidth(WidthManager.ColumnWidth.SystemDocumentNumber), true));

        }

        private void WireUpControls()
        {
            TableRow tr = ((TableRow)FindContentControl("trSourceFromCustomerRMA"));
            _tblLines = (FlexiDataTable)tr.FindControl("tblLines");

        }
        /// <summary>
        /// Setup Script Descriptors
        /// </summary>
        private void SetupScriptDescriptors()
        {
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Forms.CreditLines_Add", ctlDesignBase.ClientID);
            _scScriptControlDescriptor.AddProperty("intCreditID", _objQSManager.CreditID);
            _scScriptControlDescriptor.AddElementProperty("ibtnContinue", ((IconButton)FindIconButton("ibtnContinue")).ClientID);
            if (FooterLinksHolder != null) _scScriptControlDescriptor.AddElementProperty("ibtnContinue_Footer", ((IconButton)FindFooterIconButton("ibtnContinue")).ClientID);
            _scScriptControlDescriptor.AddElementProperty("radSelectSource", _radSelectSource.ClientID);
            _scScriptControlDescriptor.AddElementProperty("trSourceFromCustomerRMA", ((TableRow)FindContentControl("trSourceFromCustomerRMA")).ClientID);
            _scScriptControlDescriptor.AddComponentProperty("ctlSourceFromCustomerRMA", ((ItemSearch.CRMALines)FindContentControl("ctlSourceFromCustomerRMA")).ctlDesignBase.ClientID);
            _scScriptControlDescriptor.AddElementProperty("trSourceFromInvoice", ((TableRow)FindContentControl("trSourceFromInvoice")).ClientID);
            _scScriptControlDescriptor.AddComponentProperty("ctlSourceFromInvoice", ((ItemSearch.InvoiceLines)FindContentControl("ctlSourceFromInvoice")).ctlDesignBase.ClientID);

            _scScriptControlDescriptor.AddElementProperty("trSourceFromClientInvoice", ((TableRow)FindContentControl("trSourceFromClientInvoice")).ClientID);
            // _scScriptControlDescriptor.AddComponentProperty("ctlSourceFromClientInvoice", ((ItemSearch.ClientInvoiceLines)FindContentControl("ctlSourceFromClientInvoice")).ctlDesignBase.ClientID);

            _scScriptControlDescriptor.AddComponentProperty("tblLines", _tblLines.ClientID);
            _scScriptControlDescriptor.AddElementProperty("trSourceFromService", ((TableRow)FindContentControl("trSourceFromService")).ClientID);
            _scScriptControlDescriptor.AddComponentProperty("ctlSourceFromService", ((ItemSearch.Service)FindContentControl("ctlSourceFromService")).ctlDesignBase.ClientID);
            _scScriptControlDescriptor.AddElementProperty("lblCurrency_Price", FindFieldControl("ctlPrice", "lblCurrency_Price").ClientID);
            _scScriptControlDescriptor.AddElementProperty("lblLandedCost", FindFieldControl("ctlLandedCost", "lblLandedCost").ClientID);
            _scScriptControlDescriptor.AddProperty("arySources", _lstSources);

        }

    }
}