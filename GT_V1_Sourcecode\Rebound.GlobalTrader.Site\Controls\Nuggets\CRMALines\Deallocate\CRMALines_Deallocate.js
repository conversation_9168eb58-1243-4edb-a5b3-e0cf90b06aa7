Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.CRMALines_Deallocate=function(n){Rebound.GlobalTrader.Site.Controls.Forms.CRMALines_Deallocate.initializeBase(this,[n]);this._aryLineIDs=[];this._intCRMAID=0};Rebound.GlobalTrader.Site.Controls.Forms.CRMALines_Deallocate.prototype={get_intCRMAID:function(){return this._intCRMAID},set_intCRMAID:function(n){this._intCRMAID!==n&&(this._intCRMAID=n)},get_aryLineIDs:function(){return this._aryLineIDs},set_aryLineIDs:function(n){this._aryLineIDs!==n&&(this._aryLineIDs=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.CRMALines_Deallocate.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown))},dispose:function(){this.isDisposed||(this._aryLineIDs=null,this._intCRMAID=null,Rebound.GlobalTrader.Site.Controls.Forms.CRMALines_Deallocate.callBaseMethod(this,"dispose"))},formShown:function(){this._blnFirstTimeShown&&(this._ctlConfirm=this.getFieldComponent("ctlConfirm"),this._ctlConfirm.addClickYesEvent(Function.createDelegate(this,this.yesClicked)),this._ctlConfirm.addClickNoEvent(Function.createDelegate(this,this.noClicked)))},setFieldsFromHeader:function(n,t){this.setFieldValue("ctlCustomerRMA",n);this.setFieldValue("ctlCustomer",t)},yesClicked:function(){this.showSaving(!0);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("Controls/Nuggets/CRMALines");n.set_DataObject("CRMALines");n.set_DataAction("DeleteAllocation");n.addParameter("LineIDs",$R_FN.arrayToSingleString(this._aryLineIDs));n.addDataOK(Function.createDelegate(this,this.saveComplete));n.addError(Function.createDelegate(this,this.saveError));n.addTimeout(Function.createDelegate(this,this.saveError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null;this.onSave()},noClicked:function(){this.showSaving(!1);this.onNotConfirmed()},saveError:function(n){this.showSaving(!1);this._strErrorMessage=n._errorMessage;this.onSaveError()},saveComplete:function(n){this.showSaving(!1);n._result.Result==!0?this.onSaveComplete():(this._strErrorMessage=n._errorMessage,this.onSaveError())}};Rebound.GlobalTrader.Site.Controls.Forms.CRMALines_Deallocate.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CRMALines_Deallocate",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);