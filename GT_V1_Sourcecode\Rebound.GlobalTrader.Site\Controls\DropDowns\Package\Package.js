Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.Package=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.Package.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DropDowns.Package.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.DropDowns.Package.callBaseMethod(this,"initialize");this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK))},dispose:function(){this.isDisposed||Rebound.GlobalTrader.Site.Controls.DropDowns.Package.callBaseMethod(this,"dispose")},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/Package");this._objData.set_DataObject("Package");this._objData.set_DataAction("GetData")},dataCallOK:function(){var t=this._objData._result,n;if(t.Packages)for(n=0;n<t.Packages.length;n++)this.addOption(t.Packages[n].Name,t.Packages[n].ID)}};Rebound.GlobalTrader.Site.Controls.DropDowns.Package.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.Package",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);