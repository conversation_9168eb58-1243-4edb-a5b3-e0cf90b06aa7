Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.OGELLicenses_Add=function(n){Rebound.GlobalTrader.Site.Controls.Forms.OGELLicenses_Add.initializeBase(this,[n]);this._intItemID=-1};Rebound.GlobalTrader.Site.Controls.Forms.OGELLicenses_Add.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.OGELLicenses_Add.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown))},dispose:function(){this.isDisposed||(this._intItemID=null,Rebound.GlobalTrader.Site.Controls.Forms.OGELLicenses_Add.callBaseMethod(this,"dispose"))},formShown:function(){this._blnFirstTimeShown&&(this.addSave(Function.createDelegate(this,this.saveClicked)),this._strPathToData="controls/SetupNuggets/OGELLicenses",this._strDataObject="OGELLicenses");this.setFormFieldsToDefaults()},saveClicked:function(){this.resetFormFields();this.validateForm()&&this.saveEdit()},validateForm:function(){return this.autoValidateFields()},saveEdit:function(){if(this.validateForm()){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("AddNew");n.addParameter("OgelNumber",this.getFieldValue("ctlOgelNumber"));n.addParameter("Description",this.getFieldValue("ctlDescription"));n.addParameter("InActive",this.getFieldValue("ctlInActive"));n.addDataOK(Function.createDelegate(this,this.saveEditOK));n.addError(Function.createDelegate(this,this.saveEditError));n.addTimeout(Function.createDelegate(this,this.saveEditError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null}},saveEditError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},saveEditOK:function(n){if(n._result.Result==-1){this.showError(!0,"'OGEL Number' is already exists.");return}this.onSaveComplete()}};Rebound.GlobalTrader.Site.Controls.Forms.OGELLicenses_Add.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.OGELLicenses_Add",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);