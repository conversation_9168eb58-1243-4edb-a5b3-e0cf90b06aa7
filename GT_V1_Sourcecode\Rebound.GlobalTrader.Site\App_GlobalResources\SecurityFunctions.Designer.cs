//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option or rebuild the Visual Studio project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.VisualStudio.Web.Application.StronglyTypedResourceProxyBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class SecurityFunctions {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal SecurityFunctions() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Resources.SecurityFunctions", global::System.Reflection.Assembly.Load("App_GlobalResources"));
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow Emailing.
        /// </summary>
        internal static string AllowEmailing {
            get {
                return ResourceManager.GetString("AllowEmailing", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow Printing.
        /// </summary>
        internal static string AllowPrinting {
            get {
                return ResourceManager.GetString("AllowPrinting", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Communications section.
        /// </summary>
        internal static string CommunicationsSection_View {
            get {
                return ResourceManager.GetString("CommunicationsSection_View", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new Message.
        /// </summary>
        internal static string Communications_NewMessage_Add {
            get {
                return ResourceManager.GetString("Communications_NewMessage_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new ToDo item.
        /// </summary>
        internal static string Communications_NewToDo_Add {
            get {
                return ResourceManager.GetString("Communications_NewToDo_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Contact section.
        /// </summary>
        internal static string ContactSection_View {
            get {
                return ResourceManager.GetString("ContactSection_View", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to All companies.
        /// </summary>
        internal static string Contact_AllCompanies_View {
            get {
                return ResourceManager.GetString("Contact_AllCompanies_View", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new Address.
        /// </summary>
        internal static string Contact_CompanyDetail_Addresses_Add {
            get {
                return ResourceManager.GetString("Contact_CompanyDetail_Addresses_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow ceasing an Address.
        /// </summary>
        internal static string Contact_CompanyDetail_Addresses_Cease {
            get {
                return ResourceManager.GetString("Contact_CompanyDetail_Addresses_Cease", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing an Address.
        /// </summary>
        internal static string Contact_CompanyDetail_Addresses_Edit {
            get {
                return ResourceManager.GetString("Contact_CompanyDetail_Addresses_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow making an Address default billing or shipping for a company.
        /// </summary>
        internal static string Contact_CompanyDetail_Addresses_MakeDefault {
            get {
                return ResourceManager.GetString("Contact_CompanyDetail_Addresses_MakeDefault", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow Add/Edit Tax information for a company.
        /// </summary>
        internal static string Contact_CompanyDetail_Addresses_Tax_AddEdit {
            get {
                return ResourceManager.GetString("Contact_CompanyDetail_Addresses_Tax_AddEdit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Company Detail.
        /// </summary>
        internal static string Contact_CompanyDetail_AllCompanies_View {
            get {
                return ResourceManager.GetString("Contact_CompanyDetail_AllCompanies_View", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding to Contact Log.
        /// </summary>
        internal static string Contact_CompanyDetail_ContactLog_Add {
            get {
                return ResourceManager.GetString("Contact_CompanyDetail_ContactLog_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing Contact Log items.
        /// </summary>
        internal static string Contact_CompanyDetail_ContactLog_Edit {
            get {
                return ResourceManager.GetString("Contact_CompanyDetail_ContactLog_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Customer Detail.
        /// </summary>
        internal static string Contact_CompanyDetail_Customer_View {
            get {
                return ResourceManager.GetString("Contact_CompanyDetail_Customer_View", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow managing global sales user.
        /// </summary>
        internal static string Contact_CompanyDetail_GSA_Edit {
            get {
                return ResourceManager.GetString("Contact_CompanyDetail_GSA_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow edit Account Notes.
        /// </summary>
        internal static string Contact_CompanyDetail_MainInfo_AccountNotes_Edit {
            get {
                return ResourceManager.GetString("Contact_CompanyDetail_MainInfo_AccountNotes_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow Add/Edit certificates.
        /// </summary>
        internal static string Contact_CompanyDetail_MainInfo_Certificates_AddEdit {
            get {
                return ResourceManager.GetString("Contact_CompanyDetail_MainInfo_Certificates_AddEdit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing Company Type/Quality Notes.
        /// </summary>
        internal static string Contact_CompanyDetail_MainInfo_CompanyType_Edit {
            get {
                return ResourceManager.GetString("Contact_CompanyDetail_MainInfo_CompanyType_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow Permission to Add Customer API.
        /// </summary>
        internal static string Contact_CompanyDetail_MainInfo_CustomerAPI_Add {
            get {
                return ResourceManager.GetString("Contact_CompanyDetail_MainInfo_CustomerAPI_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow Permission to Add Customer API Bom Mapping.
        /// </summary>
        internal static string Contact_CompanyDetail_MainInfo_CustomerAPI_BomMapping {
            get {
                return ResourceManager.GetString("Contact_CompanyDetail_MainInfo_CustomerAPI_BomMapping", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow Permission to Edit Customer API.
        /// </summary>
        internal static string Contact_CompanyDetail_MainInfo_CustomerAPI_Edit {
            get {
                return ResourceManager.GetString("Contact_CompanyDetail_MainInfo_CustomerAPI_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow Permission to Add Customer API Supplier Import.
        /// </summary>
        internal static string Contact_CompanyDetail_MainInfo_CustomerAPI_SupplierImport {
            get {
                return ResourceManager.GetString("Contact_CompanyDetail_MainInfo_CustomerAPI_SupplierImport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing Main Information.
        /// </summary>
        internal static string Contact_CompanyDetail_MainInfo_Edit {
            get {
                return ResourceManager.GetString("Contact_CompanyDetail_MainInfo_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing HUB supplier.
        /// </summary>
        internal static string Contact_CompanyDetail_MainInfo_Edit_SupplierHUB {
            get {
                return ResourceManager.GetString("Contact_CompanyDetail_MainInfo_Edit_SupplierHUB", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow Add/Edit Insurance certificates.
        /// </summary>
        internal static string Contact_CompanyDetail_MainInfo_InsuranceCertificates_AddEdit {
            get {
                return ResourceManager.GetString("Contact_CompanyDetail_MainInfo_InsuranceCertificates_AddEdit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow inactive/ active premier Customer.
        /// </summary>
        internal static string Contact_CompanyDetail_MainInfo_PremierCustomer_Activate {
            get {
                return ResourceManager.GetString("Contact_CompanyDetail_MainInfo_PremierCustomer_Activate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow Add/Edit premier Customer.
        /// </summary>
        internal static string Contact_CompanyDetail_MainInfo_PremierCustomer_Edit {
            get {
                return ResourceManager.GetString("Contact_CompanyDetail_MainInfo_PremierCustomer_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow edit Purchasing Notes.
        /// </summary>
        internal static string Contact_CompanyDetail_MainInfo_PurchasingNotes_Edit {
            get {
                return ResourceManager.GetString("Contact_CompanyDetail_MainInfo_PurchasingNotes_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a Manufacturer Supplied.
        /// </summary>
        internal static string Contact_CompanyDetail_ManufacturersSupplied_Add {
            get {
                return ResourceManager.GetString("Contact_CompanyDetail_ManufacturersSupplied_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow deleting a Manufacturer Supplied.
        /// </summary>
        internal static string Contact_CompanyDetail_ManufacturersSupplied_Delete {
            get {
                return ResourceManager.GetString("Contact_CompanyDetail_ManufacturersSupplied_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing a Manufacturer Supplied.
        /// </summary>
        internal static string Contact_CompanyDetail_ManufacturersSupplied_Edit {
            get {
                return ResourceManager.GetString("Contact_CompanyDetail_ManufacturersSupplied_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a PDF document.
        /// </summary>
        internal static string Contact_CompanyDetail_PDFDocument_Add {
            get {
                return ResourceManager.GetString("Contact_CompanyDetail_PDFDocument_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow deleting a PDF document.
        /// </summary>
        internal static string Contact_CompanyDetail_PDFDocument_Delete {
            get {
                return ResourceManager.GetString("Contact_CompanyDetail_PDFDocument_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Prospects Detail.
        /// </summary>
        internal static string Contact_CompanyDetail_Prospects_View {
            get {
                return ResourceManager.GetString("Contact_CompanyDetail_Prospects_View", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing Purchasing Information.
        /// </summary>
        internal static string Contact_CompanyDetail_PurchasingInfo_Edit {
            get {
                return ResourceManager.GetString("Contact_CompanyDetail_PurchasingInfo_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing Supplier On Stop.
        /// </summary>
        internal static string Contact_CompanyDetail_PurchasingInfo_SupplierOnStop {
            get {
                return ResourceManager.GetString("Contact_CompanyDetail_PurchasingInfo_SupplierOnStop", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing sales information credit status.
        /// </summary>
        internal static string Contact_CompanyDetail_SalesInfo_CreditStatus {
            get {
                return ResourceManager.GetString("Contact_CompanyDetail_SalesInfo_CreditStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing Sales Information.
        /// </summary>
        internal static string Contact_CompanyDetail_SalesInfo_Edit {
            get {
                return ResourceManager.GetString("Contact_CompanyDetail_SalesInfo_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Supplier Detail.
        /// </summary>
        internal static string Contact_CompanyDetail_Supplier_View {
            get {
                return ResourceManager.GetString("Contact_CompanyDetail_Supplier_View", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new Company.
        /// </summary>
        internal static string Contact_Company_Add {
            get {
                return ResourceManager.GetString("Contact_Company_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow Active/Inactive Company.
        /// </summary>
        internal static string Contact_Company_Allow_Active_Inactive {
            get {
                return ResourceManager.GetString("Contact_Company_Allow_Active_Inactive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new Contact.
        /// </summary>
        internal static string Contact_ContactDetail_ContactList_Add {
            get {
                return ResourceManager.GetString("Contact_ContactDetail_ContactList_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow deleting a Contact.
        /// </summary>
        internal static string Contact_ContactDetail_ContactList_Delete {
            get {
                return ResourceManager.GetString("Contact_ContactDetail_ContactList_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow making a Company&apos;s default Contact for Purchase Orders.
        /// </summary>
        internal static string Contact_ContactDetail_ContactList_MakePODefault {
            get {
                return ResourceManager.GetString("Contact_ContactDetail_ContactList_MakePODefault", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow making a Company&apos;s default Contact for Sales Orders.
        /// </summary>
        internal static string Contact_ContactDetail_ContactList_MakeSODefault {
            get {
                return ResourceManager.GetString("Contact_ContactDetail_ContactList_MakeSODefault", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding to Contact Log.
        /// </summary>
        internal static string Contact_ContactDetail_ContactLog_Add {
            get {
                return ResourceManager.GetString("Contact_ContactDetail_ContactLog_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing Contact Log items.
        /// </summary>
        internal static string Contact_ContactDetail_ContactLog_Edit {
            get {
                return ResourceManager.GetString("Contact_ContactDetail_ContactLog_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing Extended Information.
        /// </summary>
        internal static string Contact_ContactDetail_ExtendedInfo_Edit {
            get {
                return ResourceManager.GetString("Contact_ContactDetail_ExtendedInfo_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow Permission to Link Accounts.
        /// </summary>
        internal static string Contact_ContactDetail_Finance_Link_Accounts {
            get {
                return ResourceManager.GetString("Contact_ContactDetail_Finance_Link_Accounts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing Main Information.
        /// </summary>
        internal static string Contact_ContactDetail_MainInfo_Edit {
            get {
                return ResourceManager.GetString("Contact_ContactDetail_MainInfo_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Contact Detail.
        /// </summary>
        internal static string Contact_ContactDetail_View {
            get {
                return ResourceManager.GetString("Contact_ContactDetail_View", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contacts.
        /// </summary>
        internal static string Contact_Contacts_View {
            get {
                return ResourceManager.GetString("Contact_Contacts_View", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customers.
        /// </summary>
        internal static string Contact_Customers_View {
            get {
                return ResourceManager.GetString("Contact_Customers_View", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow Approve and Reject Credit Application Form.
        /// </summary>
        internal static string Contact_MainInfo_CreditApplicationForm_ApproveReject_Accounts {
            get {
                return ResourceManager.GetString("Contact_MainInfo_CreditApplicationForm_ApproveReject_Accounts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow Create and Send Credit Application Form.
        /// </summary>
        internal static string Contact_MainInfo_CreditApplicationForm_CreateSend_SalesPerson {
            get {
                return ResourceManager.GetString("Contact_MainInfo_CreditApplicationForm_CreateSend_SalesPerson", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing Main Information.
        /// </summary>
        internal static string Contact_ManufacturerDetail_MainInfo_Edit {
            get {
                return ResourceManager.GetString("Contact_ManufacturerDetail_MainInfo_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new Related Company.
        /// </summary>
        internal static string Contact_ManufacturerDetail_RelatedCompanies_Add {
            get {
                return ResourceManager.GetString("Contact_ManufacturerDetail_RelatedCompanies_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow deleting a Related Company.
        /// </summary>
        internal static string Contact_ManufacturerDetail_RelatedCompanies_Delete {
            get {
                return ResourceManager.GetString("Contact_ManufacturerDetail_RelatedCompanies_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new Supplier.
        /// </summary>
        internal static string Contact_ManufacturerDetail_Suppliers_Add {
            get {
                return ResourceManager.GetString("Contact_ManufacturerDetail_Suppliers_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow deleting a Supplier.
        /// </summary>
        internal static string Contact_ManufacturerDetail_Suppliers_Delete {
            get {
                return ResourceManager.GetString("Contact_ManufacturerDetail_Suppliers_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing a Supplier.
        /// </summary>
        internal static string Contact_ManufacturerDetail_Suppliers_Edit {
            get {
                return ResourceManager.GetString("Contact_ManufacturerDetail_Suppliers_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Manufacturer Detail.
        /// </summary>
        internal static string Contact_ManufacturerDetail_View {
            get {
                return ResourceManager.GetString("Contact_ManufacturerDetail_View", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new Manufacturer.
        /// </summary>
        internal static string Contact_Manufacturer_Add {
            get {
                return ResourceManager.GetString("Contact_Manufacturer_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow Add Manufacturer Group Code.
        /// </summary>
        internal static string Contact_Manufacturer_Group_Add {
            get {
                return ResourceManager.GetString("Contact_Manufacturer_Group_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow active/inactive Manufacturer.
        /// </summary>
        internal static string Contact_Manufacturer_MainInfo_AllowInactive {
            get {
                return ResourceManager.GetString("Contact_Manufacturer_MainInfo_AllowInactive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Prospects.
        /// </summary>
        internal static string Contact_Prospects_View {
            get {
                return ResourceManager.GetString("Contact_Prospects_View", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Suppilers.
        /// </summary>
        internal static string Contact_Suppliers_View {
            get {
                return ResourceManager.GetString("Contact_Suppliers_View", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Power BI Sales Report.
        /// </summary>
        internal static string DashboardPowerBISales_ReportAllow {
            get {
                return ResourceManager.GetString("DashboardPowerBISales_ReportAllow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Power BI Sales Performance Report.
        /// </summary>
        internal static string DashboardPowerBIStock_ReportAllow {
            get {
                return ResourceManager.GetString("DashboardPowerBIStock_ReportAllow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing BI Report.
        /// </summary>
        internal static string DashboardPowerBI_ReportAllow {
            get {
                return ResourceManager.GetString("DashboardPowerBI_ReportAllow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Dashboards.
        /// </summary>
        internal static string DashboardSection_View {
            get {
                return ResourceManager.GetString("DashboardSection_View", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to General Permissions.
        /// </summary>
        internal static string GeneralPermissions {
            get {
                return ResourceManager.GetString("GeneralPermissions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow EntertainmentType.
        /// </summary>
        internal static string hypGlobalSettings_EntertainmentType {
            get {
                return ResourceManager.GetString("hypGlobalSettings_EntertainmentType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Orders section.
        /// </summary>
        internal static string OrdersSection_View {
            get {
                return ResourceManager.GetString("OrdersSection_View", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow exporting HUBRFQ and importing Sourcing.
        /// </summary>
        internal static string Orders_BOMDetail_Import_Export_SourcingResult {
            get {
                return ResourceManager.GetString("Orders_BOMDetail_Import_Export_SourcingResult", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing HUBRFQ Detail.
        /// </summary>
        internal static string Orders_BOM_View {
            get {
                return ResourceManager.GetString("Orders_BOM_View", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new Client Invoice.
        /// </summary>
        internal static string Orders_ClientInvoice_Add {
            get {
                return ResourceManager.GetString("Orders_ClientInvoice_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing Client Invoice .
        /// </summary>
        internal static string Orders_ClientInvoice_MainInfo_Edit {
            get {
                return ResourceManager.GetString("Orders_ClientInvoice_MainInfo_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Credit Notes.
        /// </summary>
        internal static string Orders_CreditNotes_View {
            get {
                return ResourceManager.GetString("Orders_CreditNotes_View", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new Credit Note.
        /// </summary>
        internal static string Orders_CreditNote_Add {
            get {
                return ResourceManager.GetString("Orders_CreditNote_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing credit note bulk print/bulk email.
        /// </summary>
        internal static string Orders_CreditNote_BulkPrintEmail {
            get {
                return ResourceManager.GetString("Orders_CreditNote_BulkPrintEmail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow emailing.
        /// </summary>
        internal static string Orders_CreditNote_Email {
            get {
                return ResourceManager.GetString("Orders_CreditNote_Email", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow generating Credit Note in XML format.
        /// </summary>
        internal static string Orders_CreditNote_Generate_XML {
            get {
                return ResourceManager.GetString("Orders_CreditNote_Generate_XML", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new Line.
        /// </summary>
        internal static string Orders_CreditNote_Lines_Add {
            get {
                return ResourceManager.GetString("Orders_CreditNote_Lines_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new Line based on a CRMA line.
        /// </summary>
        internal static string Orders_CreditNote_Lines_CanAdd_CRMA {
            get {
                return ResourceManager.GetString("Orders_CreditNote_Lines_CanAdd_CRMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new Line based on an Invoice line.
        /// </summary>
        internal static string Orders_CreditNote_Lines_CanAdd_Invoice {
            get {
                return ResourceManager.GetString("Orders_CreditNote_Lines_CanAdd_Invoice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new Line based on a Service.
        /// </summary>
        internal static string Orders_CreditNote_Lines_CanAdd_Service {
            get {
                return ResourceManager.GetString("Orders_CreditNote_Lines_CanAdd_Service", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow deleting a Line.
        /// </summary>
        internal static string Orders_CreditNote_Lines_Delete {
            get {
                return ResourceManager.GetString("Orders_CreditNote_Lines_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing a Line.
        /// </summary>
        internal static string Orders_CreditNote_Lines_Edit {
            get {
                return ResourceManager.GetString("Orders_CreditNote_Lines_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing Main Information.
        /// </summary>
        internal static string Orders_CreditNote_MainInfo_Edit {
            get {
                return ResourceManager.GetString("Orders_CreditNote_MainInfo_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow making for export /releasing for edit.
        /// </summary>
        internal static string Orders_CreditNote_MainInfo_Export_Release {
            get {
                return ResourceManager.GetString("Orders_CreditNote_MainInfo_Export_Release", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow printing.
        /// </summary>
        internal static string Orders_CreditNote_Print {
            get {
                return ResourceManager.GetString("Orders_CreditNote_Print", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Credit Note Detail.
        /// </summary>
        internal static string Orders_CreditNote_View {
            get {
                return ResourceManager.GetString("Orders_CreditNote_View", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new Credit based on a CRMA.
        /// </summary>
        internal static string Orders_Credit_Add_FromCRMA {
            get {
                return ResourceManager.GetString("Orders_Credit_Add_FromCRMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new Credit based on an Invoice.
        /// </summary>
        internal static string Orders_Credit_Add_FromInvoice {
            get {
                return ResourceManager.GetString("Orders_Credit_Add_FromInvoice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new Customer RMA.
        /// </summary>
        internal static string Orders_CRMA_Add {
            get {
                return ResourceManager.GetString("Orders_CRMA_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow emailing.
        /// </summary>
        internal static string Orders_CRMA_Email {
            get {
                return ResourceManager.GetString("Orders_CRMA_Email", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new Line.
        /// </summary>
        internal static string Orders_CRMA_Lines_Add {
            get {
                return ResourceManager.GetString("Orders_CRMA_Lines_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow deallocating a Customer RMA Line.
        /// </summary>
        internal static string Orders_CRMA_Lines_Deallocate {
            get {
                return ResourceManager.GetString("Orders_CRMA_Lines_Deallocate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow deleting a Line.
        /// </summary>
        internal static string Orders_CRMA_Lines_Delete {
            get {
                return ResourceManager.GetString("Orders_CRMA_Lines_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing a Line.
        /// </summary>
        internal static string Orders_CRMA_Lines_Edit {
            get {
                return ResourceManager.GetString("Orders_CRMA_Lines_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing Main Information.
        /// </summary>
        internal static string Orders_CRMA_MainInfo_Edit {
            get {
                return ResourceManager.GetString("Orders_CRMA_MainInfo_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a PDF document.
        /// </summary>
        internal static string Orders_CRMA_PDFDocument_Add {
            get {
                return ResourceManager.GetString("Orders_CRMA_PDFDocument_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow deleting a PDF document.
        /// </summary>
        internal static string Orders_CRMA_PDFDocument_Delete {
            get {
                return ResourceManager.GetString("Orders_CRMA_PDFDocument_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow printing.
        /// </summary>
        internal static string Orders_CRMA_Print {
            get {
                return ResourceManager.GetString("Orders_CRMA_Print", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Customer RMA Detail.
        /// </summary>
        internal static string Orders_CRMA_View {
            get {
                return ResourceManager.GetString("Orders_CRMA_View", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new Customer Requirement.
        /// </summary>
        internal static string Orders_CustomerRequirement_Add {
            get {
                return ResourceManager.GetString("Orders_CustomerRequirement_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing Main Information.
        /// </summary>
        internal static string Orders_CustomerRequirement_MainInformation_Edit {
            get {
                return ResourceManager.GetString("Orders_CustomerRequirement_MainInformation_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding an Alternate Part.
        /// </summary>
        internal static string Orders_CustomerRequirement_PartsRequired_AddAlternate {
            get {
                return ResourceManager.GetString("Orders_CustomerRequirement_PartsRequired_AddAlternate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow closing a Customer Requirement.
        /// </summary>
        internal static string Orders_CustomerRequirement_PartsRequired_Close {
            get {
                return ResourceManager.GetString("Orders_CustomerRequirement_PartsRequired_Close", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new Sourcing Result.
        /// </summary>
        internal static string Orders_CustomerRequirement_SourcingResults_Add {
            get {
                return ResourceManager.GetString("Orders_CustomerRequirement_SourcingResults_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing a Sourcing Result to a Customer Requirement.
        /// </summary>
        internal static string Orders_CustomerRequirement_SourcingResults_Edit {
            get {
                return ResourceManager.GetString("Orders_CustomerRequirement_SourcingResults_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding an Offer.
        /// </summary>
        internal static string Orders_CustomerRequirement_Sourcing_AddOffer {
            get {
                return ResourceManager.GetString("Orders_CustomerRequirement_Sourcing_AddOffer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a Stock Info.
        /// </summary>
        internal static string Orders_CustomerRequirement_Sourcing_AddStockInfo {
            get {
                return ResourceManager.GetString("Orders_CustomerRequirement_Sourcing_AddStockInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a Sourcing Result to a Customer Requirement.
        /// </summary>
        internal static string Orders_CustomerRequirement_Sourcing_AddToRequirement {
            get {
                return ResourceManager.GetString("Orders_CustomerRequirement_Sourcing_AddToRequirement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a Trusted.
        /// </summary>
        internal static string Orders_CustomerRequirement_Sourcing_AddTrusted {
            get {
                return ResourceManager.GetString("Orders_CustomerRequirement_Sourcing_AddTrusted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing a Sourcing Result.
        /// </summary>
        internal static string Orders_CustomerRequirement_Sourcing_EditOffer {
            get {
                return ResourceManager.GetString("Orders_CustomerRequirement_Sourcing_EditOffer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing a Stock Info.
        /// </summary>
        internal static string Orders_CustomerRequirement_Sourcing_EditStockInfo {
            get {
                return ResourceManager.GetString("Orders_CustomerRequirement_Sourcing_EditStockInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow sending a Request for Quote.
        /// </summary>
        internal static string Orders_CustomerRequirement_Sourcing_RFQ {
            get {
                return ResourceManager.GetString("Orders_CustomerRequirement_Sourcing_RFQ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing sourcing.
        /// </summary>
        internal static string Orders_CustomerRequirement_Sourcing_View {
            get {
                return ResourceManager.GetString("Orders_CustomerRequirement_Sourcing_View", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Customer Requirement Detail.
        /// </summary>
        internal static string Orders_CustomerRequirement_View {
            get {
                return ResourceManager.GetString("Orders_CustomerRequirement_View", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer RMAs.
        /// </summary>
        internal static string Orders_CustomerRMA_View {
            get {
                return ResourceManager.GetString("Orders_CustomerRMA_View", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Debit Notes.
        /// </summary>
        internal static string Orders_DebitNotes_View {
            get {
                return ResourceManager.GetString("Orders_DebitNotes_View", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new Debit Note.
        /// </summary>
        internal static string Orders_DebitNote_Add {
            get {
                return ResourceManager.GetString("Orders_DebitNote_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing debit note bulk print/bulk email.
        /// </summary>
        internal static string Orders_DebitNote_BulkPrintEmail {
            get {
                return ResourceManager.GetString("Orders_DebitNote_BulkPrintEmail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow emailing.
        /// </summary>
        internal static string Orders_DebitNote_Email {
            get {
                return ResourceManager.GetString("Orders_DebitNote_Email", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new Line.
        /// </summary>
        internal static string Orders_DebitNote_Lines_Add {
            get {
                return ResourceManager.GetString("Orders_DebitNote_Lines_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new Line based on a PO line.
        /// </summary>
        internal static string Orders_DebitNote_Lines_CanAdd_PO {
            get {
                return ResourceManager.GetString("Orders_DebitNote_Lines_CanAdd_PO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new Line based on a Service.
        /// </summary>
        internal static string Orders_DebitNote_Lines_CanAdd_Service {
            get {
                return ResourceManager.GetString("Orders_DebitNote_Lines_CanAdd_Service", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow deleting a Line.
        /// </summary>
        internal static string Orders_DebitNote_Lines_Delete {
            get {
                return ResourceManager.GetString("Orders_DebitNote_Lines_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing a Line.
        /// </summary>
        internal static string Orders_DebitNote_Lines_Edit {
            get {
                return ResourceManager.GetString("Orders_DebitNote_Lines_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing Main Information.
        /// </summary>
        internal static string Orders_DebitNote_MainInfo_Edit {
            get {
                return ResourceManager.GetString("Orders_DebitNote_MainInfo_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow printing.
        /// </summary>
        internal static string Orders_DebitNote_Print {
            get {
                return ResourceManager.GetString("Orders_DebitNote_Print", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Debit Note Detail.
        /// </summary>
        internal static string Orders_DebitNote_View {
            get {
                return ResourceManager.GetString("Orders_DebitNote_View", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Print/Email HUB Debit Note.
        /// </summary>
        internal static string Orders_DebitNote_View_PrintAndEmail {
            get {
                return ResourceManager.GetString("Orders_DebitNote_View_PrintAndEmail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing Internal Purchase Order Line Price.
        /// </summary>
        internal static string Orders_InternalPurchaseOrder_Line_EditPrice {
            get {
                return ResourceManager.GetString("Orders_InternalPurchaseOrder_Line_EditPrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invoices.
        /// </summary>
        internal static string Orders_Invoices_View {
            get {
                return ResourceManager.GetString("Orders_Invoices_View", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new Invoice.
        /// </summary>
        internal static string Orders_Invoice_Add {
            get {
                return ResourceManager.GetString("Orders_Invoice_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow invoice bulk email.
        /// </summary>
        internal static string Orders_Invoice_BulkEmail_Email {
            get {
                return ResourceManager.GetString("Orders_Invoice_BulkEmail_Email", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow bulk printing Invoices.
        /// </summary>
        internal static string Orders_Invoice_BulkPrint {
            get {
                return ResourceManager.GetString("Orders_Invoice_BulkPrint", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing deleted Invoice Lines.
        /// </summary>
        internal static string Orders_Invoice_DeletedLines {
            get {
                return ResourceManager.GetString("Orders_Invoice_DeletedLines", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow emailing.
        /// </summary>
        internal static string Orders_Invoice_Email {
            get {
                return ResourceManager.GetString("Orders_Invoice_Email", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow emailing of Certificate Of Conformance.
        /// </summary>
        internal static string Orders_Invoice_EmailCertificateOfConformance {
            get {
                return ResourceManager.GetString("Orders_Invoice_EmailCertificateOfConformance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Access to email Inv/Commercial/Inv inc CofC.
        /// </summary>
        internal static string Orders_Invoice_EmailInvoice {
            get {
                return ResourceManager.GetString("Orders_Invoice_EmailInvoice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow emailing of Packing Slip.
        /// </summary>
        internal static string Orders_Invoice_EmailPackingSlip {
            get {
                return ResourceManager.GetString("Orders_Invoice_EmailPackingSlip", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow generating Invoice in XML format.
        /// </summary>
        internal static string Orders_Invoice_Generate_XML {
            get {
                return ResourceManager.GetString("Orders_Invoice_Generate_XML", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new Line.
        /// </summary>
        internal static string Orders_Invoice_Lines_Add {
            get {
                return ResourceManager.GetString("Orders_Invoice_Lines_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new manual Line.
        /// </summary>
        internal static string Orders_Invoice_Lines_CanAdd_NewLine {
            get {
                return ResourceManager.GetString("Orders_Invoice_Lines_CanAdd_NewLine", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new Line based on a Service.
        /// </summary>
        internal static string Orders_Invoice_Lines_CanAdd_Service {
            get {
                return ResourceManager.GetString("Orders_Invoice_Lines_CanAdd_Service", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow deleting an Invoice Line.
        /// </summary>
        internal static string Orders_Invoice_Lines_Delete {
            get {
                return ResourceManager.GetString("Orders_Invoice_Lines_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing an Invoice Line.
        /// </summary>
        internal static string Orders_Invoice_Lines_Edit {
            get {
                return ResourceManager.GetString("Orders_Invoice_Lines_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing an Invoice Line Allocation.
        /// </summary>
        internal static string Orders_Invoice_Lines_EditAllocation {
            get {
                return ResourceManager.GetString("Orders_Invoice_Lines_EditAllocation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing Main Information.
        /// </summary>
        internal static string Orders_Invoice_MainInfo_Edit {
            get {
                return ResourceManager.GetString("Orders_Invoice_MainInfo_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing accounts related fields.
        /// </summary>
        internal static string Orders_Invoice_MainInfo_EditDivision {
            get {
                return ResourceManager.GetString("Orders_Invoice_MainInfo_EditDivision", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing Invoice Shipped Date.
        /// </summary>
        internal static string Orders_Invoice_MainInfo_EditShippedDate {
            get {
                return ResourceManager.GetString("Orders_Invoice_MainInfo_EditShippedDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing Invoice Term.
        /// </summary>
        internal static string Orders_Invoice_MainInfo_Edit_Term {
            get {
                return ResourceManager.GetString("Orders_Invoice_MainInfo_Edit_Term", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow marking for export.
        /// </summary>
        internal static string Orders_Invoice_MainInfo_Export {
            get {
                return ResourceManager.GetString("Orders_Invoice_MainInfo_Export", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow releasing for edit.
        /// </summary>
        internal static string Orders_Invoice_MainInfo_Release {
            get {
                return ResourceManager.GetString("Orders_Invoice_MainInfo_Release", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a PDF document.
        /// </summary>
        internal static string Orders_Invoice_PDFDocument_Add {
            get {
                return ResourceManager.GetString("Orders_Invoice_PDFDocument_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow deleting a PDF document.
        /// </summary>
        internal static string Orders_Invoice_PDFDocument_Delete {
            get {
                return ResourceManager.GetString("Orders_Invoice_PDFDocument_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow printing.
        /// </summary>
        internal static string Orders_Invoice_Print {
            get {
                return ResourceManager.GetString("Orders_Invoice_Print", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow printing of Certificate Of Conformance.
        /// </summary>
        internal static string Orders_Invoice_PrintCertificateOfConformance {
            get {
                return ResourceManager.GetString("Orders_Invoice_PrintCertificateOfConformance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow Print Invoice After Export.
        /// </summary>
        internal static string Orders_Invoice_PrintEmailAfterExport {
            get {
                return ResourceManager.GetString("Orders_Invoice_PrintEmailAfterExport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Access to print Inv/Commercial/Inv inc CofC.
        /// </summary>
        internal static string Orders_Invoice_PrintInvoice {
            get {
                return ResourceManager.GetString("Orders_Invoice_PrintInvoice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow printing of Packing Slip.
        /// </summary>
        internal static string Orders_Invoice_PrintlPackingSlip {
            get {
                return ResourceManager.GetString("Orders_Invoice_PrintlPackingSlip", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow printing Packing Slip.
        /// </summary>
        internal static string Orders_Invoice_PrintPackingSlip {
            get {
                return ResourceManager.GetString("Orders_Invoice_PrintPackingSlip", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Invoice Detail.
        /// </summary>
        internal static string Orders_Invoice_View {
            get {
                return ResourceManager.GetString("Orders_Invoice_View", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Prospective Cross Selling Details.
        /// </summary>
        internal static string Orders_ProsCrossSellingDetail {
            get {
                return ResourceManager.GetString("Orders_ProsCrossSellingDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Prospective Cross Selling Import and List section.
        /// </summary>
        internal static string Orders_ProspectiveCrossSelling {
            get {
                return ResourceManager.GetString("Orders_ProspectiveCrossSelling", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchase Orders.
        /// </summary>
        internal static string Orders_PurchaseOrders_View {
            get {
                return ResourceManager.GetString("Orders_PurchaseOrders_View", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new Purchase Order.
        /// </summary>
        internal static string Orders_PurchaseOrder_Add {
            get {
                return ResourceManager.GetString("Orders_PurchaseOrder_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow modification of default currency and terms for unapproved supplier.
        /// </summary>
        internal static string Orders_PurchaseOrder_AddCurrencyAndTermsForUnapproved {
            get {
                return ResourceManager.GetString("Orders_PurchaseOrder_AddCurrencyAndTermsForUnapproved", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow modification of default currency and terms.
        /// </summary>
        internal static string Orders_PurchaseOrder_EditCurrencyAndTerms {
            get {
                return ResourceManager.GetString("Orders_PurchaseOrder_EditCurrencyAndTerms", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow modification of default currency and terms for unapproved supplier.
        /// </summary>
        internal static string Orders_PurchaseOrder_EditCurrencyAndTermsForUnapproved {
            get {
                return ResourceManager.GetString("Orders_PurchaseOrder_EditCurrencyAndTermsForUnapproved", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow modification of default tax.
        /// </summary>
        internal static string Orders_PurchaseOrder_EditTax {
            get {
                return ResourceManager.GetString("Orders_PurchaseOrder_EditTax", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow emailing.
        /// </summary>
        internal static string Orders_PurchaseOrder_Email {
            get {
                return ResourceManager.GetString("Orders_PurchaseOrder_Email", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow completing EPR.
        /// </summary>
        internal static string Orders_PurchaseOrder_EPR_Complete {
            get {
                return ResourceManager.GetString("Orders_PurchaseOrder_EPR_Complete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow deleting EPR.
        /// </summary>
        internal static string Orders_PurchaseOrder_EPR_Delete {
            get {
                return ResourceManager.GetString("Orders_PurchaseOrder_EPR_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow edit EPR after Authorise.
        /// </summary>
        internal static string Orders_PurchaseOrder_EPR_EditAfterAuthorise {
            get {
                return ResourceManager.GetString("Orders_PurchaseOrder_EPR_EditAfterAuthorise", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow authorise EPR.
        /// </summary>
        internal static string Orders_PurchaseOrder_EPR_RefAuthorise {
            get {
                return ResourceManager.GetString("Orders_PurchaseOrder_EPR_RefAuthorise", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow Export To Excel.
        /// </summary>
        internal static string Orders_PurchaseOrder_ExportToExcel {
            get {
                return ResourceManager.GetString("Orders_PurchaseOrder_ExportToExcel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new Line.
        /// </summary>
        internal static string Orders_PurchaseOrder_Lines_Add {
            get {
                return ResourceManager.GetString("Orders_PurchaseOrder_Lines_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new manual Line.
        /// </summary>
        internal static string Orders_PurchaseOrder_Lines_CanAdd_NewLine {
            get {
                return ResourceManager.GetString("Orders_PurchaseOrder_Lines_CanAdd_NewLine", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new Line based on another Purchase Order.
        /// </summary>
        internal static string Orders_PurchaseOrder_Lines_CanAdd_PO {
            get {
                return ResourceManager.GetString("Orders_PurchaseOrder_Lines_CanAdd_PO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new Line based on a Purchase Requisition.
        /// </summary>
        internal static string Orders_PurchaseOrder_Lines_CanAdd_POReq {
            get {
                return ResourceManager.GetString("Orders_PurchaseOrder_Lines_CanAdd_POReq", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new Line based on a Requirement.
        /// </summary>
        internal static string Orders_PurchaseOrder_Lines_CanAdd_Req {
            get {
                return ResourceManager.GetString("Orders_PurchaseOrder_Lines_CanAdd_Req", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new Line for Stock.
        /// </summary>
        internal static string Orders_PurchaseOrder_Lines_CanAdd_Stock {
            get {
                return ResourceManager.GetString("Orders_PurchaseOrder_Lines_CanAdd_Stock", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow release Purchase Order Line locked by EPR.
        /// </summary>
        internal static string Orders_PurchaseOrder_Lines_Can_Release {
            get {
                return ResourceManager.GetString("Orders_PurchaseOrder_Lines_Can_Release", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow closing of Purchase Order Lines.
        /// </summary>
        internal static string Orders_PurchaseOrder_Lines_Close {
            get {
                return ResourceManager.GetString("Orders_PurchaseOrder_Lines_Close", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow deallocating a Line.
        /// </summary>
        internal static string Orders_PurchaseOrder_Lines_Deallocate {
            get {
                return ResourceManager.GetString("Orders_PurchaseOrder_Lines_Deallocate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow deleting a Line.
        /// </summary>
        internal static string Orders_PurchaseOrder_Lines_Delete {
            get {
                return ResourceManager.GetString("Orders_PurchaseOrder_Lines_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing a Line.
        /// </summary>
        internal static string Orders_PurchaseOrder_Lines_Edit {
            get {
                return ResourceManager.GetString("Orders_PurchaseOrder_Lines_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing Purchase Order line after post.
        /// </summary>
        internal static string Orders_PurchaseOrder_Lines_EditPriceWithoutUnpost {
            get {
                return ResourceManager.GetString("Orders_PurchaseOrder_Lines_EditPriceWithoutUnpost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow posting a Line.
        /// </summary>
        internal static string Orders_PurchaseOrder_Lines_Post {
            get {
                return ResourceManager.GetString("Orders_PurchaseOrder_Lines_Post", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow unposting a Line.
        /// </summary>
        internal static string Orders_PurchaseOrder_Lines_Unpost {
            get {
                return ResourceManager.GetString("Orders_PurchaseOrder_Lines_Unpost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow approving.
        /// </summary>
        internal static string Orders_PurchaseOrder_MainInfo_Approve {
            get {
                return ResourceManager.GetString("Orders_PurchaseOrder_MainInfo_Approve", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow approve Purchase Order without allocating to Sales Order.
        /// </summary>
        internal static string Orders_PurchaseOrder_MainInfo_ApprovePOAfterChecked {
            get {
                return ResourceManager.GetString("Orders_PurchaseOrder_MainInfo_ApprovePOAfterChecked", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow closing a Purchase Order.
        /// </summary>
        internal static string Orders_PurchaseOrder_MainInfo_Close {
            get {
                return ResourceManager.GetString("Orders_PurchaseOrder_MainInfo_Close", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow disapproving.
        /// </summary>
        internal static string Orders_PurchaseOrder_MainInfo_Disapprove {
            get {
                return ResourceManager.GetString("Orders_PurchaseOrder_MainInfo_Disapprove", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing Main Information.
        /// </summary>
        internal static string Orders_PurchaseOrder_MainInfo_Edit {
            get {
                return ResourceManager.GetString("Orders_PurchaseOrder_MainInfo_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow modification of default currency and terms.
        /// </summary>
        internal static string Orders_PurchaseOrder_MainInfo_EditCurrencyAndTerms {
            get {
                return ResourceManager.GetString("Orders_PurchaseOrder_MainInfo_EditCurrencyAndTerms", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow edit purchase order supplier .
        /// </summary>
        internal static string Orders_PurchaseOrder_MainInfo_EditSupplier {
            get {
                return ResourceManager.GetString("Orders_PurchaseOrder_MainInfo_EditSupplier", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow modification of default tax.
        /// </summary>
        internal static string Orders_PurchaseOrder_MainInfo_EditTax {
            get {
                return ResourceManager.GetString("Orders_PurchaseOrder_MainInfo_EditTax", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow creating a PDF for Purchase Order.
        /// </summary>
        internal static string Orders_PurchaseOrder_PDF {
            get {
                return ResourceManager.GetString("Orders_PurchaseOrder_PDF", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a PDF document.
        /// </summary>
        internal static string Orders_PurchaseOrder_PDFDocument_Add {
            get {
                return ResourceManager.GetString("Orders_PurchaseOrder_PDFDocument_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow deleting a PDF document.
        /// </summary>
        internal static string Orders_PurchaseOrder_PDFDocument_Delete {
            get {
                return ResourceManager.GetString("Orders_PurchaseOrder_PDFDocument_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow printing.
        /// </summary>
        internal static string Orders_PurchaseOrder_Print {
            get {
                return ResourceManager.GetString("Orders_PurchaseOrder_Print", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Purchase Order Detail.
        /// </summary>
        internal static string Orders_PurchaseOrder_View {
            get {
                return ResourceManager.GetString("Orders_PurchaseOrder_View", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchase Requisitions.
        /// </summary>
        internal static string Orders_PurchaseRequisitions_View {
            get {
                return ResourceManager.GetString("Orders_PurchaseRequisitions_View", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing AS9120 from Quote and SO page.
        /// </summary>
        internal static string Orders_QuoteAndSO_Edit_AS9120 {
            get {
                return ResourceManager.GetString("Orders_QuoteAndSO_Edit_AS9120", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new Line.
        /// </summary>
        internal static string Orders_QuoteLines_Add {
            get {
                return ResourceManager.GetString("Orders_QuoteLines_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new Line from Lot.
        /// </summary>
        internal static string Orders_QuoteLines_CanAdd_FromLot {
            get {
                return ResourceManager.GetString("Orders_QuoteLines_CanAdd_FromLot", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new manual Line.
        /// </summary>
        internal static string Orders_QuoteLines_CanAdd_NewLine {
            get {
                return ResourceManager.GetString("Orders_QuoteLines_CanAdd_NewLine", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new Line based on a Requirement.
        /// </summary>
        internal static string Orders_QuoteLines_CanAdd_Req {
            get {
                return ResourceManager.GetString("Orders_QuoteLines_CanAdd_Req", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new Line based on a Service.
        /// </summary>
        internal static string Orders_QuoteLines_CanAdd_Service {
            get {
                return ResourceManager.GetString("Orders_QuoteLines_CanAdd_Service", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new Line based on a Requirement Sourcing Result.
        /// </summary>
        internal static string Orders_QuoteLines_CanAdd_SourcingResult {
            get {
                return ResourceManager.GetString("Orders_QuoteLines_CanAdd_SourcingResult", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new Line from Stock.
        /// </summary>
        internal static string Orders_QuoteLines_CanAdd_Stock {
            get {
                return ResourceManager.GetString("Orders_QuoteLines_CanAdd_Stock", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow deleting a Quote Line.
        /// </summary>
        internal static string Orders_QuoteLines_CanDelete_FromLot {
            get {
                return ResourceManager.GetString("Orders_QuoteLines_CanDelete_FromLot", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow closing a Line.
        /// </summary>
        internal static string Orders_QuoteLines_Close {
            get {
                return ResourceManager.GetString("Orders_QuoteLines_Close", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing a Line.
        /// </summary>
        internal static string Orders_QuoteLines_Edit {
            get {
                return ResourceManager.GetString("Orders_QuoteLines_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quotes.
        /// </summary>
        internal static string Orders_Quotes_View {
            get {
                return ResourceManager.GetString("Orders_Quotes_View", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new Quote.
        /// </summary>
        internal static string Orders_Quote_Add {
            get {
                return ResourceManager.GetString("Orders_Quote_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new blank Quote.
        /// </summary>
        internal static string Orders_Quote_Add_FromNew {
            get {
                return ResourceManager.GetString("Orders_Quote_Add_FromNew", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new Quote based on a Requirement.
        /// </summary>
        internal static string Orders_Quote_Add_FromReq {
            get {
                return ResourceManager.GetString("Orders_Quote_Add_FromReq", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow emailing.
        /// </summary>
        internal static string Orders_Quote_Email {
            get {
                return ResourceManager.GetString("Orders_Quote_Email", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing Main Information.
        /// </summary>
        internal static string Orders_Quote_MainInfo_Edit {
            get {
                return ResourceManager.GetString("Orders_Quote_MainInfo_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow creating a PDF for Quote.
        /// </summary>
        internal static string Orders_Quote_PDF {
            get {
                return ResourceManager.GetString("Orders_Quote_PDF", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow printing.
        /// </summary>
        internal static string Orders_Quote_Print {
            get {
                return ResourceManager.GetString("Orders_Quote_Print", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Quote Detail.
        /// </summary>
        internal static string Orders_Quote_View {
            get {
                return ResourceManager.GetString("Orders_Quote_View", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Requirements.
        /// </summary>
        internal static string Orders_Requirements_View {
            get {
                return ResourceManager.GetString("Orders_Requirements_View", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SalesOrders.
        /// </summary>
        internal static string Orders_SalesOrders_View {
            get {
                return ResourceManager.GetString("Orders_SalesOrders_View", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new Sales Order.
        /// </summary>
        internal static string Orders_SalesOrder_Add {
            get {
                return ResourceManager.GetString("Orders_SalesOrder_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow SO authorization, if Customer On Stop.
        /// </summary>
        internal static string Orders_SalesOrder_AllowCheckedCompanyOnStop {
            get {
                return ResourceManager.GetString("Orders_SalesOrder_AllowCheckedCompanyOnStop", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding/removing Notify Sales Person on Request Approval.
        /// </summary>
        internal static string Orders_SalesOrder_Authorise_EnableSendSONotify {
            get {
                return ResourceManager.GetString("Orders_SalesOrder_Authorise_EnableSendSONotify", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow Send Request Approval.
        /// </summary>
        internal static string Orders_SalesOrder_Authorise_RequestApproval {
            get {
                return ResourceManager.GetString("Orders_SalesOrder_Authorise_RequestApproval", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow modification of default currency and terms.
        /// </summary>
        internal static string Orders_SalesOrder_EditCurrencyAndTerms {
            get {
                return ResourceManager.GetString("Orders_SalesOrder_EditCurrencyAndTerms", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow modification of default tax.
        /// </summary>
        internal static string Orders_SalesOrder_EditTax {
            get {
                return ResourceManager.GetString("Orders_SalesOrder_EditTax", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow emailing.
        /// </summary>
        internal static string Orders_SalesOrder_Email {
            get {
                return ResourceManager.GetString("Orders_SalesOrder_Email", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow Edit/Edit All Export Approval Details.
        /// </summary>
        internal static string Orders_SalesOrder_ExportApproval_EditApproval {
            get {
                return ResourceManager.GetString("Orders_SalesOrder_ExportApproval_EditApproval", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow Export Approval.
        /// </summary>
        internal static string Orders_SalesOrder_ExportApproval_RequestApproval {
            get {
                return ResourceManager.GetString("Orders_SalesOrder_ExportApproval_RequestApproval", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow Send Export Approval.
        /// </summary>
        internal static string Orders_SalesOrder_ExportApproval_SendApproval {
            get {
                return ResourceManager.GetString("Orders_SalesOrder_ExportApproval_SendApproval", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow Export To Excel.
        /// </summary>
        internal static string Orders_SalesOrder_ExportToExcle {
            get {
                return ResourceManager.GetString("Orders_SalesOrder_ExportToExcle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new Line.
        /// </summary>
        internal static string Orders_SalesOrder_Lines_Add {
            get {
                return ResourceManager.GetString("Orders_SalesOrder_Lines_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow allocating a Line.
        /// </summary>
        internal static string Orders_SalesOrder_Lines_Allocate {
            get {
                return ResourceManager.GetString("Orders_SalesOrder_Lines_Allocate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new manual Line.
        /// </summary>
        internal static string Orders_SalesOrder_Lines_CanAdd_NewLine {
            get {
                return ResourceManager.GetString("Orders_SalesOrder_Lines_CanAdd_NewLine", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new Line based on a Quote.
        /// </summary>
        internal static string Orders_SalesOrder_Lines_CanAdd_Quote {
            get {
                return ResourceManager.GetString("Orders_SalesOrder_Lines_CanAdd_Quote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new Line based on a Requirement.
        /// </summary>
        internal static string Orders_SalesOrder_Lines_CanAdd_Req {
            get {
                return ResourceManager.GetString("Orders_SalesOrder_Lines_CanAdd_Req", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new Line for Service.
        /// </summary>
        internal static string Orders_SalesOrder_Lines_CanAdd_Service {
            get {
                return ResourceManager.GetString("Orders_SalesOrder_Lines_CanAdd_Service", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new Line based on another Sales Order.
        /// </summary>
        internal static string Orders_SalesOrder_Lines_CanAdd_SO {
            get {
                return ResourceManager.GetString("Orders_SalesOrder_Lines_CanAdd_SO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new Line for Stock.
        /// </summary>
        internal static string Orders_SalesOrder_Lines_CanAdd_Stock {
            get {
                return ResourceManager.GetString("Orders_SalesOrder_Lines_CanAdd_Stock", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow closing of Sales Order Lines.
        /// </summary>
        internal static string Orders_SalesOrder_Lines_Close {
            get {
                return ResourceManager.GetString("Orders_SalesOrder_Lines_Close", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow deallocating a Line.
        /// </summary>
        internal static string Orders_SalesOrder_Lines_Deallocate {
            get {
                return ResourceManager.GetString("Orders_SalesOrder_Lines_Deallocate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow deleting a Line.
        /// </summary>
        internal static string Orders_SalesOrder_Lines_Delete {
            get {
                return ResourceManager.GetString("Orders_SalesOrder_Lines_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing ECCN Log.
        /// </summary>
        internal static string Orders_SalesOrder_Lines_ECCNLog {
            get {
                return ResourceManager.GetString("Orders_SalesOrder_Lines_ECCNLog", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing a Line.
        /// </summary>
        internal static string Orders_SalesOrder_Lines_Edit {
            get {
                return ResourceManager.GetString("Orders_SalesOrder_Lines_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing all sales order.
        /// </summary>
        internal static string Orders_SalesOrder_Lines_EditAll {
            get {
                return ResourceManager.GetString("Orders_SalesOrder_Lines_EditAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow edit date promised between current month and end of date promised.
        /// </summary>
        internal static string Orders_SalesOrder_Lines_EditDatePromisedAfterChecked {
            get {
                return ResourceManager.GetString("Orders_SalesOrder_Lines_EditDatePromisedAfterChecked", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow edit Date Required after post.
        /// </summary>
        internal static string Orders_SalesOrder_Lines_Edit_DateRequired {
            get {
                return ResourceManager.GetString("Orders_SalesOrder_Lines_Edit_DateRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow edit Date Promised after checked.
        /// </summary>
        internal static string Orders_SalesOrder_Lines_Edit_PromiseDateAfterAuthorisation {
            get {
                return ResourceManager.GetString("Orders_SalesOrder_Lines_Edit_PromiseDateAfterAuthorisation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow posting a Line.
        /// </summary>
        internal static string Orders_SalesOrder_Lines_Post {
            get {
                return ResourceManager.GetString("Orders_SalesOrder_Lines_Post", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow unposting a Line.
        /// </summary>
        internal static string Orders_SalesOrder_Lines_Unpost {
            get {
                return ResourceManager.GetString("Orders_SalesOrder_Lines_Unpost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow authorising.
        /// </summary>
        internal static string Orders_SalesOrder_MainInfo_Authorise {
            get {
                return ResourceManager.GetString("Orders_SalesOrder_MainInfo_Authorise", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding Credit Card payment .
        /// </summary>
        internal static string Orders_SalesOrder_MainInfo_Can_Pay_By_CreditCard {
            get {
                return ResourceManager.GetString("Orders_SalesOrder_MainInfo_Can_Pay_By_CreditCard", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow closing a Sales Order.
        /// </summary>
        internal static string Orders_SalesOrder_MainInfo_Close {
            get {
                return ResourceManager.GetString("Orders_SalesOrder_MainInfo_Close", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow deauthorising.
        /// </summary>
        internal static string Orders_SalesOrder_MainInfo_Deauthorise {
            get {
                return ResourceManager.GetString("Orders_SalesOrder_MainInfo_Deauthorise", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing Main Information.
        /// </summary>
        internal static string Orders_SalesOrder_MainInfo_Edit {
            get {
                return ResourceManager.GetString("Orders_SalesOrder_MainInfo_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow modification of default currency and terms.
        /// </summary>
        internal static string Orders_SalesOrder_MainInfo_EditCurrencyAndTerms {
            get {
                return ResourceManager.GetString("Orders_SalesOrder_MainInfo_EditCurrencyAndTerms", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing fields after authorisation.
        /// </summary>
        internal static string Orders_SalesOrder_MainInfo_EditFieldsAfterAuthorisation {
            get {
                return ResourceManager.GetString("Orders_SalesOrder_MainInfo_EditFieldsAfterAuthorisation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing shipping costs and freight.
        /// </summary>
        internal static string Orders_SalesOrder_MainInfo_EditShippingCosts {
            get {
                return ResourceManager.GetString("Orders_SalesOrder_MainInfo_EditShippingCosts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow modification of default tax.
        /// </summary>
        internal static string Orders_SalesOrder_MainInfo_EditTax {
            get {
                return ResourceManager.GetString("Orders_SalesOrder_MainInfo_EditTax", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow notification.
        /// </summary>
        internal static string Orders_SalesOrder_MainInfo_Notify {
            get {
                return ResourceManager.GetString("Orders_SalesOrder_MainInfo_Notify", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding Paid Box.
        /// </summary>
        internal static string Orders_SalesOrder_MainInfo_Paid_Add {
            get {
                return ResourceManager.GetString("Orders_SalesOrder_MainInfo_Paid_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing Paid Box.
        /// </summary>
        internal static string Orders_SalesOrder_MainInfo_Paid_Edit {
            get {
                return ResourceManager.GetString("Orders_SalesOrder_MainInfo_Paid_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a PDF Document.
        /// </summary>
        internal static string Orders_SalesOrder_PDFDocument_Add {
            get {
                return ResourceManager.GetString("Orders_SalesOrder_PDFDocument_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow deleting a PDF Document.
        /// </summary>
        internal static string Orders_SalesOrder_PDFDocument_Delete {
            get {
                return ResourceManager.GetString("Orders_SalesOrder_PDFDocument_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow delete Customer PO pdf after authorised.
        /// </summary>
        internal static string Orders_SalesOrder_PDFDocument_DeleteAfterAuthorise {
            get {
                return ResourceManager.GetString("Orders_SalesOrder_PDFDocument_DeleteAfterAuthorise", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow printing.
        /// </summary>
        internal static string Orders_SalesOrder_Print {
            get {
                return ResourceManager.GetString("Orders_SalesOrder_Print", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow emailing Pro Forma Invoice.
        /// </summary>
        internal static string Orders_SalesOrder_ProForma_Email {
            get {
                return ResourceManager.GetString("Orders_SalesOrder_ProForma_Email", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow printing Pro Forma Invoice.
        /// </summary>
        internal static string Orders_SalesOrder_ProForma_Print {
            get {
                return ResourceManager.GetString("Orders_SalesOrder_ProForma_Print", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow production of Sales Order Report.
        /// </summary>
        internal static string Orders_SalesOrder_Report {
            get {
                return ResourceManager.GetString("Orders_SalesOrder_Report", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Allow Ready To Ship? tickbox.
        /// </summary>
        internal static string Orders_SalesOrder_SOAuth_AllowReadyToShip {
            get {
                return ResourceManager.GetString("Orders_SalesOrder_SOAuth_AllowReadyToShip", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow printing Sales Order Report.
        /// </summary>
        internal static string Orders_SalesOrder_SOReport_Print {
            get {
                return ResourceManager.GetString("Orders_SalesOrder_SOReport_Print", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow deleting SOR PDF.
        /// </summary>
        internal static string Orders_SalesOrder_SORPDFDocument_Delete {
            get {
                return ResourceManager.GetString("Orders_SalesOrder_SORPDFDocument_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Sales Order Detail.
        /// </summary>
        internal static string Orders_SalesOrder_View {
            get {
                return ResourceManager.GetString("Orders_SalesOrder_View", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow to Create SO Line From Lot.
        /// </summary>
        internal static string Orders_SoLines_CanAdd_FromLot {
            get {
                return ResourceManager.GetString("Orders_SoLines_CanAdd_FromLot", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding an Offer.
        /// </summary>
        internal static string Orders_Sourcing_AddOffer {
            get {
                return ResourceManager.GetString("Orders_Sourcing_AddOffer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding Stock Import Tool.
        /// </summary>
        internal static string Orders_Sourcing_AddStockImportTool {
            get {
                return ResourceManager.GetString("Orders_Sourcing_AddStockImportTool", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding Sourcing Info.
        /// </summary>
        internal static string Orders_Sourcing_AddStockInfo {
            get {
                return ResourceManager.GetString("Orders_Sourcing_AddStockInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow add to requirement button to be visible.
        /// </summary>
        internal static string Orders_Sourcing_AddToRequirement {
            get {
                return ResourceManager.GetString("Orders_Sourcing_AddToRequirement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a Trusted.
        /// </summary>
        internal static string Orders_Sourcing_AddTrusted {
            get {
                return ResourceManager.GetString("Orders_Sourcing_AddTrusted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow this security group to be assigned HUB RFQS.
        /// </summary>
        internal static string Orders_Sourcing_AssignHUBRFQ {
            get {
                return ResourceManager.GetString("Orders_Sourcing_AssignHUBRFQ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow Permission to View Bulk Edit Strategic Logistic.
        /// </summary>
        internal static string Orders_Sourcing_BulkEdit_StrategicStock {
            get {
                return ResourceManager.GetString("Orders_Sourcing_BulkEdit_StrategicStock", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing a Sourcing Result.
        /// </summary>
        internal static string Orders_Sourcing_EditOffer {
            get {
                return ResourceManager.GetString("Orders_Sourcing_EditOffer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing Sourcing Info.
        /// </summary>
        internal static string Orders_Sourcing_EditStockInfo {
            get {
                return ResourceManager.GetString("Orders_Sourcing_EditStockInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow Permission to Edit Bulk Button on Reverse Logistic Nugget.
        /// </summary>
        internal static string Orders_Sourcing_Edit_ReverseLogistic_Bulk {
            get {
                return ResourceManager.GetString("Orders_Sourcing_Edit_ReverseLogistic_Bulk", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow Permission to Edit Strategic Offers Virtual Cost Price.
        /// </summary>
        internal static string Orders_Sourcing_Edit_StrategicOffers_VirtualCostPrice {
            get {
                return ResourceManager.GetString("Orders_Sourcing_Edit_StrategicOffers_VirtualCostPrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow Permission to View Bulk Edit Reverse Logistic.
        /// </summary>
        internal static string Orders_Sourcing_HideEdit_ReverseLogistic_Bulk {
            get {
                return ResourceManager.GetString("Orders_Sourcing_HideEdit_ReverseLogistic_Bulk", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow sending a Request for Quote.
        /// </summary>
        internal static string Orders_Sourcing_RFQ {
            get {
                return ResourceManager.GetString("Orders_Sourcing_RFQ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing sourcing section.
        /// </summary>
        internal static string Orders_Sourcing_View {
            get {
                return ResourceManager.GetString("Orders_Sourcing_View", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new Supplier RMA.
        /// </summary>
        internal static string Orders_SRMA_Add {
            get {
                return ResourceManager.GetString("Orders_SRMA_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow emailing.
        /// </summary>
        internal static string Orders_SRMA_Email {
            get {
                return ResourceManager.GetString("Orders_SRMA_Email", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new Line.
        /// </summary>
        internal static string Orders_SRMA_Lines_Add {
            get {
                return ResourceManager.GetString("Orders_SRMA_Lines_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow deleting a Line.
        /// </summary>
        internal static string Orders_SRMA_Lines_Delete {
            get {
                return ResourceManager.GetString("Orders_SRMA_Lines_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing a Line.
        /// </summary>
        internal static string Orders_SRMA_Lines_Edit {
            get {
                return ResourceManager.GetString("Orders_SRMA_Lines_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing Main Information.
        /// </summary>
        internal static string Orders_SRMA_MainInfo_Edit {
            get {
                return ResourceManager.GetString("Orders_SRMA_MainInfo_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a PDF document.
        /// </summary>
        internal static string Orders_SRMA_PDFDocument_Add {
            get {
                return ResourceManager.GetString("Orders_SRMA_PDFDocument_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow deleting a PDF document.
        /// </summary>
        internal static string Orders_SRMA_PDFDocument_Delete {
            get {
                return ResourceManager.GetString("Orders_SRMA_PDFDocument_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow printing.
        /// </summary>
        internal static string Orders_SRMA_Print {
            get {
                return ResourceManager.GetString("Orders_SRMA_Print", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Supplier RMA Detail.
        /// </summary>
        internal static string Orders_SRMA_View {
            get {
                return ResourceManager.GetString("Orders_SRMA_View", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Print/Email HUB Supplier RMA.
        /// </summary>
        internal static string Orders_SRMA_View_HUBPrintAndEmail {
            get {
                return ResourceManager.GetString("Orders_SRMA_View_HUBPrintAndEmail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing IPO Supplier RMA PDF Nugget.
        /// </summary>
        internal static string Orders_SRMA_View_PDFDocument {
            get {
                return ResourceManager.GetString("Orders_SRMA_View_PDFDocument", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier RMAs.
        /// </summary>
        internal static string Orders_SupplierRMA_View {
            get {
                return ResourceManager.GetString("Orders_SupplierRMA_View", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow entrance to Setup Screens.
        /// </summary>
        internal static string OverallSetup {
            get {
                return ResourceManager.GetString("OverallSetup", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Permissions for {0} Section.
        /// </summary>
        internal static string PermissionsForSection {
            get {
                return ResourceManager.GetString("PermissionsForSection", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Reports section.
        /// </summary>
        internal static string ReportsSection_View {
            get {
                return ResourceManager.GetString("ReportsSection_View", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Sales Bom Manager Sheet.
        /// </summary>
        internal static string SalesBomManagerSheet {
            get {
                return ResourceManager.GetString("SalesBomManagerSheet", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Setup section.
        /// </summary>
        internal static string SetupSection_View {
            get {
                return ResourceManager.GetString("SetupSection_View", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Company Settings.
        /// </summary>
        internal static string Setup_CompanySettings {
            get {
                return ResourceManager.GetString("Setup_CompanySettings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing company Application Settings setup.
        /// </summary>
        internal static string Setup_CompanySettings_AppSettings {
            get {
                return ResourceManager.GetString("Setup_CompanySettings_AppSettings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing company Application Settings.
        /// </summary>
        internal static string Setup_CompanySettings_AppSettings_Edit {
            get {
                return ResourceManager.GetString("Setup_CompanySettings_AppSettings_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Countries setup.
        /// </summary>
        internal static string Setup_CompanySettings_Countries {
            get {
                return ResourceManager.GetString("Setup_CompanySettings_Countries", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new Country.
        /// </summary>
        internal static string Setup_CompanySettings_Countries_Add {
            get {
                return ResourceManager.GetString("Setup_CompanySettings_Countries_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing a Country.
        /// </summary>
        internal static string Setup_CompanySettings_Countries_Edit {
            get {
                return ResourceManager.GetString("Setup_CompanySettings_Countries_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Currencies setup.
        /// </summary>
        internal static string Setup_CompanySettings_Currencies {
            get {
                return ResourceManager.GetString("Setup_CompanySettings_Currencies", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new Currency.
        /// </summary>
        internal static string Setup_CompanySettings_Currencies_Add {
            get {
                return ResourceManager.GetString("Setup_CompanySettings_Currencies_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing a Currency.
        /// </summary>
        internal static string Setup_CompanySettings_Currencies_Edit {
            get {
                return ResourceManager.GetString("Setup_CompanySettings_Currencies_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing current Currency rates.
        /// </summary>
        internal static string Setup_CompanySettings_Currencies_EditRates {
            get {
                return ResourceManager.GetString("Setup_CompanySettings_Currencies_EditRates", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow deleting a Currency Rate.
        /// </summary>
        internal static string Setup_CompanySettings_CurrencyRateHistory_Delete {
            get {
                return ResourceManager.GetString("Setup_CompanySettings_CurrencyRateHistory_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing a Currency Rate.
        /// </summary>
        internal static string Setup_CompanySettings_CurrencyRateHistory_Edit {
            get {
                return ResourceManager.GetString("Setup_CompanySettings_CurrencyRateHistory_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Divisions setup.
        /// </summary>
        internal static string Setup_CompanySettings_Divisions {
            get {
                return ResourceManager.GetString("Setup_CompanySettings_Divisions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new Division.
        /// </summary>
        internal static string Setup_CompanySettings_Divisions_Add {
            get {
                return ResourceManager.GetString("Setup_CompanySettings_Divisions_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding and deleting a Division&apos;s Document Header Image.
        /// </summary>
        internal static string Setup_CompanySettings_Divisions_DocumentHeader_AddDelete {
            get {
                return ResourceManager.GetString("Setup_CompanySettings_Divisions_DocumentHeader_AddDelete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing a Division.
        /// </summary>
        internal static string Setup_CompanySettings_Divisions_Edit {
            get {
                return ResourceManager.GetString("Setup_CompanySettings_Divisions_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Email Composer.
        /// </summary>
        internal static string Setup_CompanySettings_EmailComposer {
            get {
                return ResourceManager.GetString("Setup_CompanySettings_EmailComposer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow add/edit Email Composer.
        /// </summary>
        internal static string Setup_CompanySettings_EmailComposer_Add_Edit {
            get {
                return ResourceManager.GetString("Setup_CompanySettings_EmailComposer_Add_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing nice label path.
        /// </summary>
        internal static string Setup_CompanySettings_LabelPath {
            get {
                return ResourceManager.GetString("Setup_CompanySettings_LabelPath", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a nice label path.
        /// </summary>
        internal static string Setup_CompanySettings_LabelPath_Add {
            get {
                return ResourceManager.GetString("Setup_CompanySettings_LabelPath_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing nice label path.
        /// </summary>
        internal static string Setup_CompanySettings_LabelPath_Edit {
            get {
                return ResourceManager.GetString("Setup_CompanySettings_LabelPath_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing local Currencies setup.
        /// </summary>
        internal static string Setup_CompanySettings_LocalCurrencies {
            get {
                return ResourceManager.GetString("Setup_CompanySettings_LocalCurrencies", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new local Currencies.
        /// </summary>
        internal static string Setup_CompanySettings_LocalCurrencies_Add {
            get {
                return ResourceManager.GetString("Setup_CompanySettings_LocalCurrencies_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing a local Currencies.
        /// </summary>
        internal static string Setup_CompanySettings_LocalCurrencies_Edit {
            get {
                return ResourceManager.GetString("Setup_CompanySettings_LocalCurrencies_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow edting Mail Groups.
        /// </summary>
        internal static string Setup_CompanySettings_MailGroups {
            get {
                return ResourceManager.GetString("Setup_CompanySettings_MailGroups", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new Mail Group.
        /// </summary>
        internal static string Setup_CompanySettings_MailGroups_Add {
            get {
                return ResourceManager.GetString("Setup_CompanySettings_MailGroups_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow deleting a Mail Group.
        /// </summary>
        internal static string Setup_CompanySettings_MailGroups_Delete {
            get {
                return ResourceManager.GetString("Setup_CompanySettings_MailGroups_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing a Mail Group.
        /// </summary>
        internal static string Setup_CompanySettings_MailGroups_Edit {
            get {
                return ResourceManager.GetString("Setup_CompanySettings_MailGroups_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing OGEL Licenses setup.
        /// </summary>
        internal static string Setup_CompanySettings_OGELLicenses {
            get {
                return ResourceManager.GetString("Setup_CompanySettings_OGELLicenses", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new OGEL License.
        /// </summary>
        internal static string Setup_CompanySettings_OGELLicenses_Add {
            get {
                return ResourceManager.GetString("Setup_CompanySettings_OGELLicenses_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing a OGEL License.
        /// </summary>
        internal static string Setup_CompanySettings_OGELLicenses_Edit {
            get {
                return ResourceManager.GetString("Setup_CompanySettings_OGELLicenses_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Printed Documents setup.
        /// </summary>
        internal static string Setup_CompanySettings_PrintedDocuments {
            get {
                return ResourceManager.GetString("Setup_CompanySettings_PrintedDocuments", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing Document Footer text.
        /// </summary>
        internal static string Setup_CompanySettings_PrintedDocuments_DocumentFooters_Edit {
            get {
                return ResourceManager.GetString("Setup_CompanySettings_PrintedDocuments_DocumentFooters_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding and deleting the Document Header Image.
        /// </summary>
        internal static string Setup_CompanySettings_PrintedDocuments_HeaderImage_AddDelete {
            get {
                return ResourceManager.GetString("Setup_CompanySettings_PrintedDocuments_HeaderImage_AddDelete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Printer setup.
        /// </summary>
        internal static string Setup_CompanySettings_Printer {
            get {
                return ResourceManager.GetString("Setup_CompanySettings_Printer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new Printer.
        /// </summary>
        internal static string Setup_CompanySettings_Printer_Add {
            get {
                return ResourceManager.GetString("Setup_CompanySettings_Printer_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing a Printer.
        /// </summary>
        internal static string Setup_CompanySettings_Printer_Edit {
            get {
                return ResourceManager.GetString("Setup_CompanySettings_Printer_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Products setup.
        /// </summary>
        internal static string Setup_CompanySettings_Products {
            get {
                return ResourceManager.GetString("Setup_CompanySettings_Products", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new Product.
        /// </summary>
        internal static string Setup_CompanySettings_Products_Add {
            get {
                return ResourceManager.GetString("Setup_CompanySettings_Products_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing a Product.
        /// </summary>
        internal static string Setup_CompanySettings_Products_Edit {
            get {
                return ResourceManager.GetString("Setup_CompanySettings_Products_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Sequence Numbers setup.
        /// </summary>
        internal static string Setup_CompanySettings_SequenceNumbers {
            get {
                return ResourceManager.GetString("Setup_CompanySettings_SequenceNumbers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Shipping Methods setup.
        /// </summary>
        internal static string Setup_CompanySettings_ShippingMethods {
            get {
                return ResourceManager.GetString("Setup_CompanySettings_ShippingMethods", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new Shipping Method.
        /// </summary>
        internal static string Setup_CompanySettings_ShippingMethods_Add {
            get {
                return ResourceManager.GetString("Setup_CompanySettings_ShippingMethods_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing a Shipping Method.
        /// </summary>
        internal static string Setup_CompanySettings_ShippingMethods_Edit {
            get {
                return ResourceManager.GetString("Setup_CompanySettings_ShippingMethods_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Sourcing Links setup.
        /// </summary>
        internal static string Setup_CompanySettings_SourcingLinks {
            get {
                return ResourceManager.GetString("Setup_CompanySettings_SourcingLinks", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new Sourcing Link.
        /// </summary>
        internal static string Setup_CompanySettings_SourcingLinks_Add {
            get {
                return ResourceManager.GetString("Setup_CompanySettings_SourcingLinks_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow deleting a Sourcing Link.
        /// </summary>
        internal static string Setup_CompanySettings_SourcingLinks_Delete {
            get {
                return ResourceManager.GetString("Setup_CompanySettings_SourcingLinks_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing a Sourcing Link.
        /// </summary>
        internal static string Setup_CompanySettings_SourcingLinks_Edit {
            get {
                return ResourceManager.GetString("Setup_CompanySettings_SourcingLinks_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Stock Log Reasons setup.
        /// </summary>
        internal static string Setup_CompanySettings_StockLogReasons {
            get {
                return ResourceManager.GetString("Setup_CompanySettings_StockLogReasons", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new Stock Log Reason.
        /// </summary>
        internal static string Setup_CompanySettings_StockLogReasons_Add {
            get {
                return ResourceManager.GetString("Setup_CompanySettings_StockLogReasons_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing a Stock Log Reason.
        /// </summary>
        internal static string Setup_CompanySettings_StockLogReasons_Edit {
            get {
                return ResourceManager.GetString("Setup_CompanySettings_StockLogReasons_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Taxes setup.
        /// </summary>
        internal static string Setup_CompanySettings_Taxes {
            get {
                return ResourceManager.GetString("Setup_CompanySettings_Taxes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new Tax.
        /// </summary>
        internal static string Setup_CompanySettings_Taxes_Add {
            get {
                return ResourceManager.GetString("Setup_CompanySettings_Taxes_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow deleting a future Tax Rate.
        /// </summary>
        internal static string Setup_CompanySettings_Taxes_DeleteFutureRate {
            get {
                return ResourceManager.GetString("Setup_CompanySettings_Taxes_DeleteFutureRate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing a Tax.
        /// </summary>
        internal static string Setup_CompanySettings_Taxes_Edit {
            get {
                return ResourceManager.GetString("Setup_CompanySettings_Taxes_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing Tax Rates.
        /// </summary>
        internal static string Setup_CompanySettings_Taxes_EditRates {
            get {
                return ResourceManager.GetString("Setup_CompanySettings_Taxes_EditRates", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Teams setup.
        /// </summary>
        internal static string Setup_CompanySettings_Teams {
            get {
                return ResourceManager.GetString("Setup_CompanySettings_Teams", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new Team.
        /// </summary>
        internal static string Setup_CompanySettings_Teams_Add {
            get {
                return ResourceManager.GetString("Setup_CompanySettings_Teams_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing a Team.
        /// </summary>
        internal static string Setup_CompanySettings_Teams_Edit {
            get {
                return ResourceManager.GetString("Setup_CompanySettings_Teams_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Terms setup.
        /// </summary>
        internal static string Setup_CompanySettings_Terms {
            get {
                return ResourceManager.GetString("Setup_CompanySettings_Terms", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new set of Terms.
        /// </summary>
        internal static string Setup_CompanySettings_Terms_Add {
            get {
                return ResourceManager.GetString("Setup_CompanySettings_Terms_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing a set of Terms.
        /// </summary>
        internal static string Setup_CompanySettings_Terms_Edit {
            get {
                return ResourceManager.GetString("Setup_CompanySettings_Terms_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Warehouses setup.
        /// </summary>
        internal static string Setup_CompanySettings_Warehouses {
            get {
                return ResourceManager.GetString("Setup_CompanySettings_Warehouses", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new Warehouse.
        /// </summary>
        internal static string Setup_CompanySettings_Warehouses_Add {
            get {
                return ResourceManager.GetString("Setup_CompanySettings_Warehouses_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing a Warehouse.
        /// </summary>
        internal static string Setup_CompanySettings_Warehouses_Edit {
            get {
                return ResourceManager.GetString("Setup_CompanySettings_Warehouses_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow setting / clearing a default Warehouse for POs and CRMAs.
        /// </summary>
        internal static string Setup_CompanySettings_Warehouses_SetDefault {
            get {
                return ResourceManager.GetString("Setup_CompanySettings_Warehouses_SetDefault", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Global Settings.
        /// </summary>
        internal static string Setup_GlobalSettings {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing global Application Settings setup.
        /// </summary>
        internal static string Setup_GlobalSettings_AppSettings {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_AppSettings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing global Application Settings.
        /// </summary>
        internal static string Setup_GlobalSettings_AppSettings_Edit {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_AppSettings_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing AS6081 setup.
        /// </summary>
        internal static string Setup_GlobalSettings_AS6081 {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_AS6081", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding AS6081 setup data.
        /// </summary>
        internal static string Setup_GlobalSettings_AS6081_Add {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_AS6081_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow deleting AS6081 setup data.
        /// </summary>
        internal static string Setup_GlobalSettings_AS6081_Delete {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_AS6081_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing AS6081 setup data.
        /// </summary>
        internal static string Setup_GlobalSettings_AS6081_Edit {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_AS6081_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Certificate &amp; Certificate Category.
        /// </summary>
        internal static string Setup_GlobalSettings_Certificate {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_Certificate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a Certificate &amp; Certificate Category.
        /// </summary>
        internal static string Setup_GlobalSettings_Certificate_Add {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_Certificate_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing a Certificate &amp; Certificate Category.
        /// </summary>
        internal static string Setup_GlobalSettings_Certificate_Edit {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_Certificate_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Communication Log Types setup.
        /// </summary>
        internal static string Setup_GlobalSettings_CommunicationLogTypes {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_CommunicationLogTypes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new Communication Log Type.
        /// </summary>
        internal static string Setup_GlobalSettings_CommunicationLogTypes_Add {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_CommunicationLogTypes_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing a Communication Log Type.
        /// </summary>
        internal static string Setup_GlobalSettings_CommunicationLogTypes_Edit {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_CommunicationLogTypes_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Company Types setup.
        /// </summary>
        internal static string Setup_GlobalSettings_CompanyTypes {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_CompanyTypes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new Company Type.
        /// </summary>
        internal static string Setup_GlobalSettings_CompanyType_Add {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_CompanyType_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing a Company Type.
        /// </summary>
        internal static string Setup_GlobalSettings_CompanyType_Edit {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_CompanyType_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Counting Methods.
        /// </summary>
        internal static string Setup_GlobalSettings_CountingMethods {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_CountingMethods", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new Counting Method.
        /// </summary>
        internal static string Setup_GlobalSettings_CountingMethod_Add {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_CountingMethod_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing a Counting Method.
        /// </summary>
        internal static string Setup_GlobalSettings_CountingMethod_Edit {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_CountingMethod_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow add/edit Root Cause Code.
        /// </summary>
        internal static string Setup_GlobalSettings_EightDCode {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_EightDCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Entertainment Type.
        /// </summary>
        internal static string Setup_GlobalSettings_EntertainmentType {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_EntertainmentType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new Entertainment Type.
        /// </summary>
        internal static string Setup_GlobalSettings_EntertainmentType_Add {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_EntertainmentType_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing a Entertainment Type.
        /// </summary>
        internal static string Setup_GlobalSettings_EntertainmentType_Edit {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_EntertainmentType_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Incoterms setup.
        /// </summary>
        internal static string Setup_GlobalSettings_Incoterms {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_Incoterms", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new Incoterm.
        /// </summary>
        internal static string Setup_GlobalSettings_Incoterm_Add {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_Incoterm_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing an Incoterm.
        /// </summary>
        internal static string Setup_GlobalSettings_Incoterm_Edit {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_Incoterm_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Industry Types setup.
        /// </summary>
        internal static string Setup_GlobalSettings_IndustryTypes {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_IndustryTypes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new Industry Type.
        /// </summary>
        internal static string Setup_GlobalSettings_IndustryType_Add {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_IndustryType_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing a Industry Type.
        /// </summary>
        internal static string Setup_GlobalSettings_IndustryType_Edit {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_IndustryType_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Master Country List setup.
        /// </summary>
        internal static string Setup_GlobalSettings_MasterCountryList {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_MasterCountryList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new Master Country.
        /// </summary>
        internal static string Setup_GlobalSettings_MasterCountryList_Add {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_MasterCountryList_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing a Master Country.
        /// </summary>
        internal static string Setup_GlobalSettings_MasterCountryList_Edit {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_MasterCountryList_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Master Currency List setup.
        /// </summary>
        internal static string Setup_GlobalSettings_MasterCurrencyList {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_MasterCurrencyList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new Master Currency.
        /// </summary>
        internal static string Setup_GlobalSettings_MasterCurrencyList_Add {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_MasterCurrencyList_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing a Master Currency.
        /// </summary>
        internal static string Setup_GlobalSettings_MasterCurrencyList_Edit {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_MasterCurrencyList_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Packages setup.
        /// </summary>
        internal static string Setup_GlobalSettings_Packages {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_Packages", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new Package.
        /// </summary>
        internal static string Setup_GlobalSettings_Package_Add {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_Package_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing a Package.
        /// </summary>
        internal static string Setup_GlobalSettings_Package_Edit {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_Package_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Document File Size setup.
        /// </summary>
        internal static string Setup_GlobalSettings_PDFDocumentFileSize {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_PDFDocumentFileSize", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new Document File Size.
        /// </summary>
        internal static string Setup_GlobalSettings_PDFDocumentFileSize_Add {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_PDFDocumentFileSize_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing a Document File Size.
        /// </summary>
        internal static string Setup_GlobalSettings_PDFDocumentFileSize_Edit {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_PDFDocumentFileSize_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing PPV/ BOM Qualification.
        /// </summary>
        internal static string Setup_GlobalSettings_PPVBOMQualification {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_PPVBOMQualification", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding PPV/ BOM Qualification.
        /// </summary>
        internal static string Setup_GlobalSettings_PPVBOMQualification_Add {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_PPVBOMQualification_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing PPV/ BOM Qualification.
        /// </summary>
        internal static string Setup_GlobalSettings_PPVBOMQualification_Edit {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_PPVBOMQualification_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Product Types setup.
        /// </summary>
        internal static string Setup_GlobalSettings_ProductTypes {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_ProductTypes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new Product Type.
        /// </summary>
        internal static string Setup_GlobalSettings_ProductType_Add {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_ProductType_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing a Product Type.
        /// </summary>
        internal static string Setup_GlobalSettings_ProductType_Edit {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_ProductType_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Reasons setup.
        /// </summary>
        internal static string Setup_GlobalSettings_Reasons {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_Reasons", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new Reason.
        /// </summary>
        internal static string Setup_GlobalSettings_Reason_Add {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_Reason_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing a Reason.
        /// </summary>
        internal static string Setup_GlobalSettings_Reason_Edit {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_Reason_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Security Settings.
        /// </summary>
        internal static string Setup_SecuritySettings {
            get {
                return ResourceManager.GetString("Setup_SecuritySettings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing General Permissions.
        /// </summary>
        internal static string Setup_SecuritySettings_GeneralPermissions_Edit {
            get {
                return ResourceManager.GetString("Setup_SecuritySettings_GeneralPermissions_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing Security Group Members.
        /// </summary>
        internal static string Setup_SecuritySettings_GroupMembers_Edit {
            get {
                return ResourceManager.GetString("Setup_SecuritySettings_GroupMembers_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Security Groups setup.
        /// </summary>
        internal static string Setup_SecuritySettings_Groups {
            get {
                return ResourceManager.GetString("Setup_SecuritySettings_Groups", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new Security Group.
        /// </summary>
        internal static string Setup_SecuritySettings_Groups_Add {
            get {
                return ResourceManager.GetString("Setup_SecuritySettings_Groups_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow cloning a Security Group.
        /// </summary>
        internal static string Setup_SecuritySettings_Groups_Clone {
            get {
                return ResourceManager.GetString("Setup_SecuritySettings_Groups_Clone", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow deleting a Security Group.
        /// </summary>
        internal static string Setup_SecuritySettings_Groups_Delete {
            get {
                return ResourceManager.GetString("Setup_SecuritySettings_Groups_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing a Security Group.
        /// </summary>
        internal static string Setup_SecuritySettings_Groups_Edit {
            get {
                return ResourceManager.GetString("Setup_SecuritySettings_Groups_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing Report Permissions.
        /// </summary>
        internal static string Setup_SecuritySettings_ReportPermissions_Edit {
            get {
                return ResourceManager.GetString("Setup_SecuritySettings_ReportPermissions_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Security Users setup.
        /// </summary>
        internal static string Setup_SecuritySettings_Users {
            get {
                return ResourceManager.GetString("Setup_SecuritySettings_Users", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new User.
        /// </summary>
        internal static string Setup_SecuritySettings_Users_Add {
            get {
                return ResourceManager.GetString("Setup_SecuritySettings_Users_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow disabling a User.
        /// </summary>
        internal static string Setup_SecuritySettings_Users_Disable {
            get {
                return ResourceManager.GetString("Setup_SecuritySettings_Users_Disable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing a User.
        /// </summary>
        internal static string Setup_SecuritySettings_Users_Edit {
            get {
                return ResourceManager.GetString("Setup_SecuritySettings_Users_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow enabling a User.
        /// </summary>
        internal static string Setup_SecuritySettings_Users_Enable {
            get {
                return ResourceManager.GetString("Setup_SecuritySettings_Users_Enable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing a user&apos;s Group Membership.
        /// </summary>
        internal static string Setup_SecuritySettings_Users_Groups_Edit {
            get {
                return ResourceManager.GetString("Setup_SecuritySettings_Users_Groups_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow changing a User&apos;s Password.
        /// </summary>
        internal static string Setup_SecuritySettings_Users_Profile_ChangePassword {
            get {
                return ResourceManager.GetString("Setup_SecuritySettings_Users_Profile_ChangePassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing a User Profile.
        /// </summary>
        internal static string Setup_SecuritySettings_Users_Profile_Edit {
            get {
                return ResourceManager.GetString("Setup_SecuritySettings_Users_Profile_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow resetting a User&apos;s Password.
        /// </summary>
        internal static string Setup_SecuritySettings_Users_Profile_ResetPassword {
            get {
                return ResourceManager.GetString("Setup_SecuritySettings_Users_Profile_ResetPassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow transferring a user&apos;s accounts to another user.
        /// </summary>
        internal static string Setup_SecuritySettings_Users_TransferAccounts {
            get {
                return ResourceManager.GetString("Setup_SecuritySettings_Users_TransferAccounts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Permissions for Tab Security.
        /// </summary>
        internal static string TabSecurity {
            get {
                return ResourceManager.GetString("TabSecurity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Utility BOM Import section.
        /// </summary>
        internal static string Utility_BOM_Import {
            get {
                return ResourceManager.GetString("Utility_BOM_Import", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Utility Bulk Offer Scheduled Import Tool.
        /// </summary>
        internal static string Utility_HUBOfferImportLarge {
            get {
                return ResourceManager.GetString("Utility_HUBOfferImportLarge", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Utility Offer Import section.
        /// </summary>
        internal static string Utility_Offer_Import {
            get {
                return ResourceManager.GetString("Utility_Offer_Import", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Utility Price Quote section.
        /// </summary>
        internal static string Utility_PriceQuote_Import {
            get {
                return ResourceManager.GetString("Utility_PriceQuote_Import", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Utility Prospective Offer Import and List section.
        /// </summary>
        internal static string Utility_ProspectiveOffer {
            get {
                return ResourceManager.GetString("Utility_ProspectiveOffer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Utility Prospective Offer Details.
        /// </summary>
        internal static string Utility_ProspectiveOfferDetail {
            get {
                return ResourceManager.GetString("Utility_ProspectiveOfferDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Reverse Logistics Import Tool.
        /// </summary>
        internal static string Utility_Reverse_Logistics_Import_Tool {
            get {
                return ResourceManager.GetString("Utility_Reverse_Logistics_Import_Tool", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Strategic Offers Import Tool.
        /// </summary>
        internal static string Utility_Strategic_Offers_Import_Tool {
            get {
                return ResourceManager.GetString("Utility_Strategic_Offers_Import_Tool", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing XMatch Tool.
        /// </summary>
        internal static string Utility_XMatch_Tool {
            get {
                return ResourceManager.GetString("Utility_XMatch_Tool", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Company Statistics.
        /// </summary>
        internal static string ViewAllStatistics {
            get {
                return ResourceManager.GetString("ViewAllStatistics", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing a different User&apos;s homepage.
        /// </summary>
        internal static string ViewDifferentUsersHomepage {
            get {
                return ResourceManager.GetString("ViewDifferentUsersHomepage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Team, Division or Company reports.
        /// </summary>
        internal static string ViewOnlyMyReports {
            get {
                return ResourceManager.GetString("ViewOnlyMyReports", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing &quot;{0}&quot;.
        /// </summary>
        internal static string ViewReport {
            get {
                return ResourceManager.GetString("ViewReport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing top Salesperson figures.
        /// </summary>
        internal static string ViewTopSalesman {
            get {
                return ResourceManager.GetString("ViewTopSalesman", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow edit button to be visible at all times regardless of inspection state.
        /// </summary>
        internal static string WarehouseSection_EditGILines_AfterStatInspection {
            get {
                return ResourceManager.GetString("WarehouseSection_EditGILines_AfterStatInspection", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow to Reopen an Inspection once completed.
        /// </summary>
        internal static string WarehouseSection_GILines_EditInspection {
            get {
                return ResourceManager.GetString("WarehouseSection_GILines_EditInspection", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow Quality Approval for GI Lines.
        /// </summary>
        internal static string WarehouseSection_GILines_QulityApprovalPermission {
            get {
                return ResourceManager.GetString("WarehouseSection_GILines_QulityApprovalPermission", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Warehouse section.
        /// </summary>
        internal static string WarehouseSection_View {
            get {
                return ResourceManager.GetString("WarehouseSection_View", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Stock/GoodsIn Document.
        /// </summary>
        internal static string Warehouse_GIStockDocument {
            get {
                return ResourceManager.GetString("Warehouse_GIStockDocument", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new Goods In Note.
        /// </summary>
        internal static string Warehouse_GoodsIn_Add {
            get {
                return ResourceManager.GetString("Warehouse_GoodsIn_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing IPO GoodsIn PDF Nugget.
        /// </summary>
        internal static string Warehouse_GoodsIn_Documents_View {
            get {
                return ResourceManager.GetString("Warehouse_GoodsIn_Documents_View", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow Finance to Edit Data within GI Screen after Release.
        /// </summary>
        internal static string Warehouse_GoodsIn_Lines_AccountPermission {
            get {
                return ResourceManager.GetString("Warehouse_GoodsIn_Lines_AccountPermission", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow deleting a Goods In line.
        /// </summary>
        internal static string Warehouse_GoodsIn_Lines_Delete {
            get {
                return ResourceManager.GetString("Warehouse_GoodsIn_Lines_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow delete attachments in GI Lines on attachments tab .
        /// </summary>
        internal static string Warehouse_GoodsIn_Lines_DeleteAttachmentPermission {
            get {
                return ResourceManager.GetString("Warehouse_GoodsIn_Lines_DeleteAttachmentPermission", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing a Goods In line.
        /// </summary>
        internal static string Warehouse_GoodsIn_Lines_Edit {
            get {
                return ResourceManager.GetString("Warehouse_GoodsIn_Lines_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow Warehouse to Edit data within GI Screen after Release.
        /// </summary>
        internal static string Warehouse_GoodsIn_Lines_EditAfterInspection {
            get {
                return ResourceManager.GetString("Warehouse_GoodsIn_Lines_EditAfterInspection", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Approval In Edit.
        /// </summary>
        internal static string Warehouse_GoodsIn_Lines_Edit_Approval {
            get {
                return ResourceManager.GetString("Warehouse_GoodsIn_Lines_Edit_Approval", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow download PDF in Download tab of Goods In line.
        /// </summary>
        internal static string Warehouse_GoodsIn_Lines_Edit_DownloadPDF {
            get {
                return ResourceManager.GetString("Warehouse_GoodsIn_Lines_Edit_DownloadPDF", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow download Word in Download tab of Goods In line.
        /// </summary>
        internal static string Warehouse_GoodsIn_Lines_Edit_DownloadWord {
            get {
                return ResourceManager.GetString("Warehouse_GoodsIn_Lines_Edit_DownloadWord", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing Purchase Request in Goods In Line.
        /// </summary>
        internal static string Warehouse_GoodsIn_Lines_Edit_PurchasePrice {
            get {
                return ResourceManager.GetString("Warehouse_GoodsIn_Lines_Edit_PurchasePrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing Ship In Cost in Goods In Line.
        /// </summary>
        internal static string Warehouse_GoodsIn_Lines_Edit_ShipInCost {
            get {
                return ResourceManager.GetString("Warehouse_GoodsIn_Lines_Edit_ShipInCost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow release a Goods In line.
        /// </summary>
        internal static string Warehouse_GoodsIn_Lines_Inspect {
            get {
                return ResourceManager.GetString("Warehouse_GoodsIn_Lines_Inspect", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow a Goods In line inspection.
        /// </summary>
        internal static string Warehouse_GoodsIn_Lines_Inspection {
            get {
                return ResourceManager.GetString("Warehouse_GoodsIn_Lines_Inspection", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow Manage Approver for GI Line Query.
        /// </summary>
        internal static string Warehouse_GoodsIn_Lines_MANAGEAPPROVER {
            get {
                return ResourceManager.GetString("Warehouse_GoodsIn_Lines_MANAGEAPPROVER", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow edit NPR after Authorise.
        /// </summary>
        internal static string Warehouse_GoodsIn_Lines_NPR_AfterAuthorise {
            get {
                return ResourceManager.GetString("Warehouse_GoodsIn_Lines_NPR_AfterAuthorise", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow authorise NPR.
        /// </summary>
        internal static string Warehouse_GoodsIn_Lines_NPR_Authorise {
            get {
                return ResourceManager.GetString("Warehouse_GoodsIn_Lines_NPR_Authorise", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow completing NPR.
        /// </summary>
        internal static string Warehouse_GoodsIn_Lines_NPR_CompletedBy {
            get {
                return ResourceManager.GetString("Warehouse_GoodsIn_Lines_NPR_CompletedBy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow deleting NPR Report.
        /// </summary>
        internal static string Warehouse_GoodsIn_Lines_NPR_Delete {
            get {
                return ResourceManager.GetString("Warehouse_GoodsIn_Lines_NPR_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new NPR.
        /// </summary>
        internal static string Warehouse_GoodsIn_Lines_NPR_NewNPR {
            get {
                return ResourceManager.GetString("Warehouse_GoodsIn_Lines_NPR_NewNPR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow printing a Goods In line.
        /// </summary>
        internal static string Warehouse_GoodsIn_Lines_Print {
            get {
                return ResourceManager.GetString("Warehouse_GoodsIn_Lines_Print", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow Spliting Goods In line.
        /// </summary>
        internal static string Warehouse_GoodsIn_Lines_SplitGI {
            get {
                return ResourceManager.GetString("Warehouse_GoodsIn_Lines_SplitGI", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing Main Information.
        /// </summary>
        internal static string Warehouse_GoodsIn_MainInfo_Edit {
            get {
                return ResourceManager.GetString("Warehouse_GoodsIn_MainInfo_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing Goods In Accounting Info.
        /// </summary>
        internal static string Warehouse_GoodsIn_MainInfo_EditAccountsInfo {
            get {
                return ResourceManager.GetString("Warehouse_GoodsIn_MainInfo_EditAccountsInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing Goods In Exported flag.
        /// </summary>
        internal static string Warehouse_GoodsIn_MainInfo_EditExportedFlag {
            get {
                return ResourceManager.GetString("Warehouse_GoodsIn_MainInfo_EditExportedFlag", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a PDF document.
        /// </summary>
        internal static string Warehouse_GoodsIn_PDFDocument_Add {
            get {
                return ResourceManager.GetString("Warehouse_GoodsIn_PDFDocument_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow deleting a PDF document.
        /// </summary>
        internal static string Warehouse_GoodsIn_PDFDocument_Delete {
            get {
                return ResourceManager.GetString("Warehouse_GoodsIn_PDFDocument_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow Permission to Add short Shipment.
        /// </summary>
        internal static string Warehouse_GoodsIn_ShortShipmentDetails_Add {
            get {
                return ResourceManager.GetString("Warehouse_GoodsIn_ShortShipmentDetails_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow user to change the status of a Short Shipment to cancel / close.
        /// </summary>
        internal static string Warehouse_GoodsIn_ShortShipmentDetails_ChangeStatusForCancelClose {
            get {
                return ResourceManager.GetString("Warehouse_GoodsIn_ShortShipmentDetails_ChangeStatusForCancelClose", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow user to change the status of a Short Shipment to Ship Partial / Hold for Remaining.
        /// </summary>
        internal static string Warehouse_GoodsIn_ShortShipmentDetails_ChangeStatusForShipAndHold {
            get {
                return ResourceManager.GetString("Warehouse_GoodsIn_ShortShipmentDetails_ChangeStatusForShipAndHold", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow permission to view client in short Shipment.
        /// </summary>
        internal static string Warehouse_GoodsIn_ShortShipmentDetails_ClientView {
            get {
                return ResourceManager.GetString("Warehouse_GoodsIn_ShortShipmentDetails_ClientView", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow downlaod excel report in short Shipment.
        /// </summary>
        internal static string Warehouse_GoodsIn_ShortShipmentDetails_DownloadXLS {
            get {
                return ResourceManager.GetString("Warehouse_GoodsIn_ShortShipmentDetails_DownloadXLS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow Purchasing to Edit Change Status in short Shipment.
        /// </summary>
        internal static string Warehouse_GoodsIn_ShortShipmentDetails_Edit {
            get {
                return ResourceManager.GetString("Warehouse_GoodsIn_ShortShipmentDetails_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing GoodsIn Detail.
        /// </summary>
        internal static string Warehouse_GoodsIn_View {
            get {
                return ResourceManager.GetString("Warehouse_GoodsIn_View", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow export IHS Catalogue.
        /// </summary>
        internal static string Warehouse_IHS_Allow_Export_CSV {
            get {
                return ResourceManager.GetString("Warehouse_IHS_Allow_Export_CSV", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow view IHS Catalogue Average Price.
        /// </summary>
        internal static string Warehouse_IHS_Allow_View_AvgPrice {
            get {
                return ResourceManager.GetString("Warehouse_IHS_Allow_View_AvgPrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new Lot.
        /// </summary>
        internal static string Warehouse_Lot_Add {
            get {
                return ResourceManager.GetString("Warehouse_Lot_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow Show Stock Is Available.
        /// </summary>
        internal static string Warehouse_Lot_Available {
            get {
                return ResourceManager.GetString("Warehouse_Lot_Available", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow deleting unallocated Services.
        /// </summary>
        internal static string Warehouse_Lot_Items_Services_Delete {
            get {
                return ResourceManager.GetString("Warehouse_Lot_Items_Services_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow transferring Services.
        /// </summary>
        internal static string Warehouse_Lot_Items_Services_Transfer {
            get {
                return ResourceManager.GetString("Warehouse_Lot_Items_Services_Transfer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow deleting unallocated Stock.
        /// </summary>
        internal static string Warehouse_Lot_Items_Stock_Delete {
            get {
                return ResourceManager.GetString("Warehouse_Lot_Items_Stock_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow transferring Stock.
        /// </summary>
        internal static string Warehouse_Lot_Items_Stock_Transfer {
            get {
                return ResourceManager.GetString("Warehouse_Lot_Items_Stock_Transfer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow deleting a Lot.
        /// </summary>
        internal static string Warehouse_Lot_MainInfo_Delete {
            get {
                return ResourceManager.GetString("Warehouse_Lot_MainInfo_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing Main Information.
        /// </summary>
        internal static string Warehouse_Lot_MainInfo_Edit {
            get {
                return ResourceManager.GetString("Warehouse_Lot_MainInfo_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow lot stock provision.
        /// </summary>
        internal static string Warehouse_Lot_StockProvision {
            get {
                return ResourceManager.GetString("Warehouse_Lot_StockProvision", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Lot Detail.
        /// </summary>
        internal static string Warehouse_Lot_View {
            get {
                return ResourceManager.GetString("Warehouse_Lot_View", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow receiving a Customer RMA.
        /// </summary>
        internal static string Warehouse_ReceiveCRMA {
            get {
                return ResourceManager.GetString("Warehouse_ReceiveCRMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow receiving a Customer RMA Line via Goods In process.
        /// </summary>
        internal static string Warehouse_ReceiveCRMA_Receive {
            get {
                return ResourceManager.GetString("Warehouse_ReceiveCRMA_Receive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow receiving a Purchase Order.
        /// </summary>
        internal static string Warehouse_ReceivePO {
            get {
                return ResourceManager.GetString("Warehouse_ReceivePO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow receiving a Purchase Order Line via Goods In process.
        /// </summary>
        internal static string Warehouse_ReceivePO_Receive {
            get {
                return ResourceManager.GetString("Warehouse_ReceivePO_Receive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new Service.
        /// </summary>
        internal static string Warehouse_Service_Add {
            get {
                return ResourceManager.GetString("Warehouse_Service_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow deallocating a Service.
        /// </summary>
        internal static string Warehouse_Service_Allocations_Deallocate {
            get {
                return ResourceManager.GetString("Warehouse_Service_Allocations_Deallocate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow deleting a Service.
        /// </summary>
        internal static string Warehouse_Service_MainInfo_Delete {
            get {
                return ResourceManager.GetString("Warehouse_Service_MainInfo_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing Main Information.
        /// </summary>
        internal static string Warehouse_Service_MainInfo_Edit {
            get {
                return ResourceManager.GetString("Warehouse_Service_MainInfo_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Service Detail.
        /// </summary>
        internal static string Warehouse_Service_View {
            get {
                return ResourceManager.GetString("Warehouse_Service_View", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow shipping a Sales Order.
        /// </summary>
        internal static string Warehouse_ShipSO {
            get {
                return ResourceManager.GetString("Warehouse_ShipSO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow shipping a SalesOrder Line.
        /// </summary>
        internal static string Warehouse_ShipSO_Ship {
            get {
                return ResourceManager.GetString("Warehouse_ShipSO_Ship", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow shipping surcharge waived off in shipping and invoice header.
        /// </summary>
        internal static string Warehouse_ShipSO_SurchargeWavedOff {
            get {
                return ResourceManager.GetString("Warehouse_ShipSO_SurchargeWavedOff", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow shipping a Supplier RMA.
        /// </summary>
        internal static string Warehouse_ShipSRMA {
            get {
                return ResourceManager.GetString("Warehouse_ShipSRMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow shipping a Supplier RMA Line.
        /// </summary>
        internal static string Warehouse_ShipSRMA_Ship {
            get {
                return ResourceManager.GetString("Warehouse_ShipSRMA_Ship", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a new Stock Item.
        /// </summary>
        internal static string Warehouse_Stock_Add {
            get {
                return ResourceManager.GetString("Warehouse_Stock_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow deallocating a Stock Item.
        /// </summary>
        internal static string Warehouse_Stock_Allocations_Deallocate {
            get {
                return ResourceManager.GetString("Warehouse_Stock_Allocations_Deallocate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding or deleting a Stock Image.
        /// </summary>
        internal static string Warehouse_Stock_Images_AddDelete {
            get {
                return ResourceManager.GetString("Warehouse_Stock_Images_AddDelete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing Main Information.
        /// </summary>
        internal static string Warehouse_Stock_MainInfo_Edit {
            get {
                return ResourceManager.GetString("Warehouse_Stock_MainInfo_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing Main Information On Stock Hold.
        /// </summary>
        internal static string Warehouse_Stock_MainInfo_EditLot_On_Hold {
            get {
                return ResourceManager.GetString("Warehouse_Stock_MainInfo_EditLot_On_Hold", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow quarantining Stock and GI line.
        /// </summary>
        internal static string Warehouse_Stock_MainInfo_Quarantine {
            get {
                return ResourceManager.GetString("Warehouse_Stock_MainInfo_Quarantine", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow releasing a Stock Item from quarantine.
        /// </summary>
        internal static string Warehouse_Stock_MainInfo_Release {
            get {
                return ResourceManager.GetString("Warehouse_Stock_MainInfo_Release", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow splitting a Stock Item.
        /// </summary>
        internal static string Warehouse_Stock_MainInfo_Split {
            get {
                return ResourceManager.GetString("Warehouse_Stock_MainInfo_Split", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding a PDF document.
        /// </summary>
        internal static string Warehouse_Stock_PDFDocument_Add {
            get {
                return ResourceManager.GetString("Warehouse_Stock_PDFDocument_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow deleting a PDF document.
        /// </summary>
        internal static string Warehouse_Stock_PDFDocument_Delete {
            get {
                return ResourceManager.GetString("Warehouse_Stock_PDFDocument_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow stock provision.
        /// </summary>
        internal static string Warehouse_Stock_Provision {
            get {
                return ResourceManager.GetString("Warehouse_Stock_Provision", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Purchase Price After Resale Price.
        /// </summary>
        internal static string Warehouse_Stock_PurchasePrice_View {
            get {
                return ResourceManager.GetString("Warehouse_Stock_PurchasePrice_View", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Stock Detail.
        /// </summary>
        internal static string Warehouse_Stock_View {
            get {
                return ResourceManager.GetString("Warehouse_Stock_View", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding Supplier Invoice.
        /// </summary>
        internal static string Warehouse_SupplierInvoice_Add {
            get {
                return ResourceManager.GetString("Warehouse_SupplierInvoice_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding unreleased GI lines on Supplier Invoice.
        /// </summary>
        internal static string Warehouse_SupplierInvoice_AddUnReleasedGI {
            get {
                return ResourceManager.GetString("Warehouse_SupplierInvoice_AddUnReleasedGI", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow adding Supplier Invoice Line.
        /// </summary>
        internal static string Warehouse_SupplierInvoice_Lines_Add {
            get {
                return ResourceManager.GetString("Warehouse_SupplierInvoice_Lines_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow deleting Supplier Invoice Line.
        /// </summary>
        internal static string Warehouse_SupplierInvoice_Lines_Delete {
            get {
                return ResourceManager.GetString("Warehouse_SupplierInvoice_Lines_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow Export .
        /// </summary>
        internal static string Warehouse_SupplierInvoice_MainInfo_CanExport {
            get {
                return ResourceManager.GetString("Warehouse_SupplierInvoice_MainInfo_CanExport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing Main Information.
        /// </summary>
        internal static string Warehouse_SupplierInvoice_MainInfo_Edit {
            get {
                return ResourceManager.GetString("Warehouse_SupplierInvoice_MainInfo_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow notify Supplier Invoice.
        /// </summary>
        internal static string Warehouse_SupplierInvoice_MainInfo_Notify {
            get {
                return ResourceManager.GetString("Warehouse_SupplierInvoice_MainInfo_Notify", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow editing URN Number.
        /// </summary>
        internal static string Warehouse_SupplierInvoice_URNNumber_Edit {
            get {
                return ResourceManager.GetString("Warehouse_SupplierInvoice_URNNumber_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Supplier Invoice.
        /// </summary>
        internal static string Warehouse_SupplierInvoice_View {
            get {
                return ResourceManager.GetString("Warehouse_SupplierInvoice_View", resourceCulture);
            }
        }
    }
}
