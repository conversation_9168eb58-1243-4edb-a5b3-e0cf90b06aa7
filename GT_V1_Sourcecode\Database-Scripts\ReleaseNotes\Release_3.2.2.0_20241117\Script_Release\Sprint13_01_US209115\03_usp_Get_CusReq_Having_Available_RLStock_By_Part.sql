﻿SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
/*
===========================================================================================
TASK      			UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-209115]			Trung Pham			22-Oct-2024		CREATE          Get Customer Requirement using RL stock by part
===========================================================================================
*/
CREATE OR ALTER PROCEDURE usp_Get_CusReq_Having_Available_RLStock_By_Part
	@Part NVARCHAR(MAX)
AS
BEGIN
	SELECT DISTINCT cr.BOMNo, 
		c.<PERSON>,
		cr.Part, 
		mfr.Man<PERSON>urer<PERSON>, 
		p.ProductName,
		pack.PackageName, 
		cr.DateCode,
		cr.Quantity,
		cr.Price,
		w.ClientNo,
		bom.BOMCode, 
		cr.DLUP,
		cr.CurrencyNo, 
		curs.CurrencyCode,
		cur.GlobalCurrencyNo AS ClientGlobalCurrencyNo,
		cur.CurrencyCode AS ClientCurrencyCode, 
		curs.GlobalCurrencyNo AS ReqGlobalCurrencyNo, 
		bom.ClientCurrencyNo
	FROM vwStock s
	JOIN tbLot l ON l.LotId = s.LotNo
	JOIN tbCustomerRequirement cr ON cr.Part = s.Part
	JOIN tbBOM bom ON bom.BOMId = cr.BOMNo
	JOIN tbCompany c ON c.CompanyId = bom.CompanyNo
	JOIN tbManufacturer mfr ON mfr.ManufacturerId = cr.ManufacturerNo
	JOIN tbCurrency cur ON cur.CurrencyId = bom.ClientCurrencyNo
	JOIN tbCurrency curs ON curs.CurrencyId = cr.CurrencyNo
	LEFT JOIN tbwarehouse w WITH (NOLOCK) ON w.WarehouseId = s.WarehouseNo
	LEFT JOIN tbProduct p ON p.ProductId = cr.ProductNo
	LEFT JOIN tbPackage pack ON pack.PackageId = cr.PackageNo
	WHERE s.Part = @Part AND SUBSTRING(l.LotName, 1, 2) = 'ex' AND s.QuantityAvailable > 0
	AND (bom.Status = 3)--New RFQ from client Status
	AND DATEDIFF(MONTH, cr.ReceivedDate, GETDATE()) <= 3
	ORDER BY cr.DLUP
END
GO
