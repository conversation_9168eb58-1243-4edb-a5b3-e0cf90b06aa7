<%--
Marker     Changed by      Date         Remarks
[001]      Vinay           26/07/2012   Add compulsory incoterms field when create Credit and debit note. :ESMS No:- 105
--%>
<%@ Control Language="C#" CodeBehind="CreditAdd_Add.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.CreditAdd_Add" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>

<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="true" ShowQuickHelp="false">

	<Links>
		<ReboundUI:IconButton ID="ibtnSave" runat="server" IconGroup="Nugget" IconTitleResource="Save" IconCSSType="Save" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
		<ReboundUI:IconButton ID="ibtnCancel" runat="server" IconGroup="Nugget" IconTitleResource="Cancel" IconCSSType="Cancel" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
		<ReboundUI:IconButton ID="ibtnSend" runat="server" IconGroup="Nugget" IconTitleResource="Send" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
		<ReboundUI:IconButton ID="ibtnContinue" runat="server" IconGroup="Nugget" IconTitleResource="Continue" IconCSSType="Continue" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
	</Links>

	<Explanation>
	
		<ReboundUI:MultiStep ID="ctlMultiStep" runat="server">
			<Items>
				<ReboundUI:MultiStepItem ID="ctlItem1" runat="server" ResourceTitle="CreditAdd_SelectSource" />
				<ReboundUI:MultiStepItem ID="ctlItem2" runat="server" ResourceTitle="CreditAdd_SelectItem" />
				<ReboundUI:MultiStepItem ID="ctlItem3" runat="server" ResourceTitle="CreditAdd_EnterDetail" />
				<ReboundUI:MultiStepItem ID="ctlItem4" runat="server" ResourceTitle="CreditAdd_Notify" />
			</Items>
		</ReboundUI:MultiStep>
		<asp:Label id="lblSelectInvoice" runat="server" CssClass="invisible"><%=Functions.GetGlobalResource("FormExplanations", "CreditAdd_SelectInvoice")%></asp:Label>
		<asp:Label id="lblSelectCRMA" runat="server" CssClass="invisible"><%=Functions.GetGlobalResource("FormExplanations", "CreditAdd_SelectCRMA")%></asp:Label>
		
	</Explanation>

	<Content>
		<!-- Step 1 ------------------------------------------------------------------->
		<ReboundUI_Table:Form id="frmStep1" runat="server">
		
			<ReboundUI_Form:FormField id="ctlSelectSource" runat="server" FieldID="radSelectSource" ResourceTitle="SelectSource">
				<Field><asp:RadioButtonList ID="radSelectSource" runat="server" RepeatDirection="Vertical" RepeatLayout="Flow" /></Field>
			</ReboundUI_Form:FormField>
			
		</ReboundUI_Table:Form>

		<!-- Step 2 ------------------------------------------------------------------->
		
		<ReboundUI_Table:Form id="frmStep2" runat="server">
		
			<asp:TableRow id="trSelectFromInvoice" runat="server">
				<asp:TableCell id="tdSelectFromInvoice" runat="server"><ReboundItemSearch:Invoices id="ctlSelectFromInvoice" runat="server" /></asp:TableCell>
			</asp:TableRow>
			
			<asp:TableRow id="trSelectFromCRMA" runat="server">
				<asp:TableCell id="tdSelectFromCRMA" runat="server"><ReboundItemSearch:CRMAs id="ctlSelectFromCRMA" runat="server" /></asp:TableCell>
			</asp:TableRow>
			
			<asp:TableRow id="trSelectFromClientInvoice" runat="server" >
				<asp:TableCell id="tdSelectFromClientInvoice" runat="server"><ReboundItemSearch:ClientInvoices id="ctlSelectFromClientInvoice" runat="server" /></asp:TableCell>
			</asp:TableRow>
			
		</ReboundUI_Table:Form>

		<!-- Step 3 ------------------------------------------------------------------->
		<ReboundUI_Table:Form id="frmStep3" runat="server">
		
			<ReboundUI_Form:FormField id="ctlCompany" runat="server" FieldID="lblCompany" ResourceTitle="Customer">
				<Field><asp:Label ID="lblCompany" runat="server" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlContact" runat="server" FieldID="lblContact" ResourceTitle="Contact">
				<Field><asp:Label ID="lblContact" runat="server" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlSalesman" runat="server" FieldID="lblSalesman" ResourceTitle="Salesman">
				<Field><asp:Label ID="lblSalesman" runat="server" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlSalesman2" runat="server" FieldID="ddlSalesman2" ResourceTitle="Salesperson2">
				<Field><ReboundDropDown:Employee ID="ddlSalesman2" runat="server" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlSalesman2Percent" runat="server" FieldID="txtSalesman2Percent" ResourceTitle="Salesman2Percent">
				<Field><ReboundUI:ReboundTextBox ID="txtSalesman2Percent" runat="server" Width="50" TextBoxMode="Numeric" /> %</Field>
			</ReboundUI_Form:FormField>
            
			<ReboundUI_Form:FormField id="ctlDivision" runat="server" FieldID="lblDivision" ResourceTitle="Division">
				<Field><asp:Label ID="lblDivision" runat="server" /></Field>
			</ReboundUI_Form:FormField>

			 <ReboundUI_Form:FormField id="ctlDivisionHeader" runat="server" FieldID="lblDivisionHeader" ResourceTitle="DivisionHeader" >
				 <Field><asp:Label ID="lblDivisionHeader" runat="server" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlInvoiceNumber" runat="server" FieldID="lblInvoiceNumber" ResourceTitle="InvoiceNo">
				<Field><asp:Label ID="lblInvoiceNumber" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			
			<ReboundUI_Form:FormField id="ctlClientInvoiceNumber" runat="server" FieldID="lblClientInvoiceNumber" ResourceTitle="ClientInvoiceNo">
				<Field><asp:Label ID="lblClientInvoiceNumber" runat="server" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlAuthorisedBy" runat="server" FieldID="lblAuthorisedBy" ResourceTitle="AuthorisedBy">
				<Field><asp:Label ID="lblAuthorisedBy" runat="server" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlCRMANumber" runat="server" FieldID="lblCRMANumber" ResourceTitle="CustomerRMANo">
				<Field><asp:Label ID="lblCRMANumber" runat="server" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlCreditDate" runat="server" FieldID="txtCreditDate" ResourceTitle="CreditDate" IsRequiredField="true">
				<Field>
					<ReboundUI:ReboundTextBox ID="txtCreditDate" runat="server" Width="150" />
					<ReboundUI:Calendar ID="calCreditDate" runat="server" RelatedTextBoxID="txtCreditDate" />
				</Field>
			</ReboundUI_Form:FormField>
			
			<%--<ReboundUI_Form:FormField id="ctlReferenceDate" runat="server" FieldID="txtReferenceDate" ResourceTitle="ReferenceDate" IsRequiredField="true">
				<Field>
					<ReboundUI:ReboundTextBox ID="txtReferenceDate" runat="server" Width="150" />
					<ReboundUI:Calendar ID="calReferenceDate" runat="server" RelatedTextBoxID="txtReferenceDate" />
				</Field>
			</ReboundUI_Form:FormField>--%>

			<ReboundUI_Form:FormField id="ctlReferenceDate" runat="server" FieldID="lblReferenceDate" ResourceTitle="ReferenceDate">
				<Field><asp:Label ID="lblReferenceDate" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			
			<ReboundUI_Form:FormField id="ctlCurrency" runat="server" FieldID="lblCurrency" ResourceTitle="Currency">
				<Field><asp:Label ID="lblCurrency" runat="server" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlTax" runat="server" FieldID="lblTax" ResourceTitle="Tax">
				<Field><asp:Label ID="lblTax" runat="server" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlShipVia" runat="server" FieldID="ddlShipVia" ResourceTitle="ShipVia">
				<Field><ReboundDropDown:ShipMethod ID="ddlShipVia" runat="server" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlAccount" runat="server" FieldID="txtAccount" ResourceTitle="ShippingAccountNo">
				<Field><ReboundUI:ReboundTextBox ID="txtAccount" runat="server" Width="300" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlShippingCost" runat="server" FieldID="txtShippingCost" ResourceTitle="ShippingCost">
				<Field><ReboundUI:ReboundTextBox ID="txtShippingCost" runat="server" TextBoxMode="currency" FormatDecimalPlaces="true" Width="100" /> <%=Rebound.GlobalTrader.Site.SessionManager.ClientCurrencyCode%></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlFreight" runat="server" FieldID="txtFreight" ResourceTitle="Freight">
				<Field><ReboundUI:ReboundTextBox ID="txtFreight" runat="server" TextBoxMode="currency" FormatDecimalPlaces="true" Width="100" /> <asp:Label id="lblCurrency_Freight" runat="server" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlRaisedBy" runat="server" FieldID="ddlRaisedBy" ResourceTitle="RaisedBy" IsRequiredField="true">
				<Field><ReboundDropDown:Employee ID="ddlRaisedBy" runat="server" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlCustomerPO" runat="server" FieldID="txtCustomerPO" ResourceTitle="CustomerPO">
				<Field><ReboundUI:ReboundTextBox ID="txtCustomerPO" runat="server" Width="200" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlCustomerReturn" runat="server" FieldID="txtCustomerReturn" ResourceTitle="CustomerReturn">
				<Field><ReboundUI:ReboundTextBox ID="txtCustomerReturn" runat="server" Width="200" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlCustomerDebit" runat="server" FieldID="txtCustomerDebit" ResourceTitle="CustomerDebit">
				<Field><ReboundUI:ReboundTextBox ID="txtCustomerDebit" runat="server" Width="200" /></Field>
			</ReboundUI_Form:FormField>
			
			<%--[001] code start--%>
			<ReboundUI_Form:FormField id="ctlIncoterm" runat="server" FieldID="ddlIncoterm" ResourceTitle="Incoterm" IsRequiredField="true">
				<Field><ReboundDropDown:Incoterm ID="ddlIncoterm" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			<%--[001] code end--%>
			<%--[002] code start--%>
			<ReboundUI_Form:FormField id="ctlCreditNoteBankFee" runat="server" FieldID="txtCreditNoteBankFee" ResourceTitle="BankFee">
				<Field><ReboundUI:ReboundTextBox ID="txtCreditNoteBankFee" runat="server" TextBoxMode="currency" FormatDecimalPlaces="true" Width="100" /></Field>
			</ReboundUI_Form:FormField>
	        <%--[002] code end--%>
			<ReboundUI_Form:FormField id="ctlNotes" runat="server" FieldID="txtNotes" ResourceTitle="CustomerNotes">
				<Field><ReboundUI:ReboundTextBox ID="txtNotes" runat="server" Width="450" Rows="2" TextMode="MultiLine" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlInstructions" runat="server" FieldID="txtInstructions" ResourceTitle="Instructions">
				<Field><ReboundUI:ReboundTextBox ID="txtInstructions" runat="server" Width="450" Rows="2" TextMode="MultiLine" /></Field>
			</ReboundUI_Form:FormField>
			
		</ReboundUI_Table:Form>
		
		
		<!-- Step 4 ------------------------------------------------------------------->
		<ReboundUI_Table:Form id="frmStep4" runat="server">
		
			<ReboundUI_Form:FormField id="ctlSendMail" runat="server" FieldID="chkSendMail" ResourceTitle="ShouldMailBeSent">
				<Field><ReboundUI:ImageCheckBox ID="chkSendMail" runat="server" Enabled="true" Checked="true" /></Field>
			</ReboundUI_Form:FormField>
			
			<ReboundFormFieldCollection:SendMailMessage id="ctlSendMailMessage" runat="server" />
			
		</ReboundUI_Table:Form>

	</Content>
	
</ReboundUI_Form:DesignBase>
