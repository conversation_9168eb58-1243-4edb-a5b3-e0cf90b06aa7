Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.FilterDataItemRows");Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.StarRating=function(n){Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.StarRating.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.StarRating.prototype={get_ctlRating:function(){return this._ctlRating},set_ctlRating:function(n){this._ctlRating!==n&&(this._ctlRating=n)},get_ddl:function(){return this._ddl},set_ddl:function(n){this._ddl!==n&&(this._ddl=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.StarRating.callBaseMethod(this,"initialize");this._ctlRating.addChanged(Function.createDelegate(this,this.ctlRating_StarsChanged))},dispose:function(){this.isDisposed||(this.get_element()&&$clearHandlers(this.get_element()),this._ctlRating&&this._ctlRating.dispose(),this._ctlRating=null,this._ddl=null,Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.StarRating.callBaseMethod(this,"dispose"))},ctlRating_StarsChanged:function(){this.enableField(this._ctlRating._intCurrentRating>0)},getMinValue:function(){this._ctlRating._intCurrentRating==null&&(this._ctlRating._intCurrentRating=0);var n=$R_FN.parseComparisonToMinMax(this._ddl.value,this._ctlRating._intCurrentRating);return n.Min},getMaxValue:function(){this._ctlRating._intCurrentRating==null&&(this._ctlRating._intCurrentRating=0);var n=$R_FN.parseComparisonToMinMax(this._ddl.value,this._ctlRating._intCurrentRating);return n.Max},setValue:function(n){(typeof n=="undefined"||n==null)&&(n=0);n=Number.parseInvariant(n.toString());this._ctlRating.setRating(n);this.enableField(!1);n>0&&this.enableField(!0)},getValue:function(){return this._ctlRating._intCurrentRating==null&&(this._ctlRating._intCurrentRating=0),this._ctlRating._intCurrentRating},deriveFilterExpression:function(){return this._strFilterExpression=this._blnOn?String.format("[{0}] {1} {2}",this._strFilterField,$R_FN.getNumericalComparator(this._ddl.value),this._ctlRating._intCurrentRating):"",this._strFilterExpression},reset:function(){this._ctlRating.resetRating();this.enableField(!1)},setComparisonType:function(n){this._ddl.value=n}};Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.StarRating.registerClass("Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.StarRating",Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.Base,Sys.IDisposable);