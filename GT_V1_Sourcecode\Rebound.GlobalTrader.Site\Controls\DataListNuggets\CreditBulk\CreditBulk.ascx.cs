using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.DataListNuggets {
	public partial class CreditBulk : Base {

		#region Properties
		private bool _IsGSA = false;
		public bool IsGSA
		{
			get { return _IsGSA; }
			set { _IsGSA = value; }
		}
		private bool _IsAllowGridSelection = false;
		public bool IsAllowGridSelection
        {
			get { return _IsAllowGridSelection; }
			set { _IsAllowGridSelection = value; }
        }
		public bool AllowGenerateXml { get; set; } = false;
		#endregion

		#region Overrides

		/// <summary>
		/// OnInit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			IsGSA = SessionManager.IsGSA.Value;
			SetDataListNuggetType("Credits");
			base.OnInit(e);
			TitleText = Functions.GetGlobalResource("Nuggets", "Credits");
			AddScriptReference("Controls.DataListNuggets.CreditBulk.CreditBulk.js");
			SetupTable();
		}

		protected override void OnLoad(EventArgs e) {
            bool hubCredit = false;
            if (Request.QueryString["hubcredit"] != null)
                hubCredit = Boolean.TryParse(Request.QueryString["hubcredit"], out hubCredit);

			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.DataListNuggets.CreditBulk", ctlDesignBase.ClientID);
            _scScriptControlDescriptor.AddProperty("blnPOHub", Convert.ToBoolean(SessionManager.IsPOHub));
            _scScriptControlDescriptor.AddProperty("blnHubCredit", Convert.ToBoolean(hubCredit));
			_scScriptControlDescriptor.AddProperty("IsGSA", _IsGSA);
			_scScriptControlDescriptor.AddProperty("ClientNo", Convert.ToInt32(SessionManager.ClientID));
			_scScriptControlDescriptor.AddProperty("AllowGenerateXml", AllowGenerateXml);
			base.OnLoad(e);
		}

		protected override void RenderAdditionalState() {
			string strViewLevel = this.GetSavedStateValue("ViewLevel");
			if (!string.IsNullOrEmpty(strViewLevel)) {
				((Pages.Content)Page).CurrentTab = Convert.ToInt32(strViewLevel);
				_enmViewLevel = (ViewLevelList)Convert.ToInt32(strViewLevel);
				this.OnAskPageToChangeTab();
			}
			base.RenderAdditionalState();
		}

		#endregion


		private void SetupTable() {
			_tbl.AllowSelection = AllowSelection;
			_tbl.AllowMultipleSelection = true;
			_tbl.SortColumnDirection = Rebound.GlobalTrader.Site.Enumerations.SortColumnDirection.DESC;
			_tbl.Columns.Add(new FlexiDataColumn("Number", WidthManager.GetWidth(WidthManager.ColumnWidth.SystemDocumentNumber), true));
			_tbl.Columns.Add(new FlexiDataColumn("PartNo", "Manufacturer", WidthManager.GetWidth(WidthManager.ColumnWidth.PartNo), true));
			_tbl.Columns.Add(new FlexiDataColumn("Company", "Contact", Unit.Empty, true));
			_tbl.Columns.Add(new FlexiDataColumn("CreditDate", WidthManager.GetWidth(WidthManager.ColumnWidth.Date), true));
           // if (SessionManager.IsPOHub == true)
            {
                _tbl.Columns.Add(new FlexiDataColumn("InvoiceNo", "ClientInvoiceNo", WidthManager.GetWidth(WidthManager.ColumnWidth.SystemDocumentNumber), true));
            }
          //  else
           // {
              //  _tbl.Columns.Add(new FlexiDataColumn("InvoiceNo",  WidthManager.GetWidth(WidthManager.ColumnWidth.SystemDocumentNumber), true));
          //  }
			_tbl.Columns.Add(new FlexiDataColumn("CustomerPurchaseOrderNo", WidthManager.GetWidth(WidthManager.ColumnWidth.ExternalCompanyDocument), true));
			_tbl.Columns.Add(new FlexiDataColumn("TotalValue", WidthManager.GetWidth(WidthManager.ColumnWidth.ExternalCompanyDocument), true));
		}
	}
}