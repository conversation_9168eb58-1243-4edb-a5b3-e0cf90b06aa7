﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].[usp_GenrateDynamicEpoData]
 /*        
   Updated By:  <PERSON><PERSON>        
   Updated On: 15/09/2023        
   Purpose   :*[001] RP-1950 (To handle decimal values in Quantity field)     
     
   Updated By:  <PERSON><PERSON>          
   Updated On: 19/09/2023          
   Purpose   :*[002] RP-2378 (CAST values  as float in Price field)     
   EXEC usp_GenrateDynamicEpoData 20,0,0,'','',114,114,4687 ,0,3      
   */  
 @DisplayLength int=0 ,                                                                               
 @DisplayStart int=0 ,                                                                               
 @SortCol int =0,                                                                               
 @SortDir nvarchar(10),                                                                      
 @Search nvarchar(255) = NULL,                                                  
 @SelectedClientId int,                                                                      
 @ClientId INT ,                                                                           
 @UserId INT,                                                    
 @UpliftPercentage  float=null ,        
 @NoChangesToSourcingSheet int=0        
AS                                                                            
BEGIN                                                           
                                              
Declare @FirstRec int, @LastRec int                                                          
Set @FirstRec = @DisplayStart;                                                          
Set @LastRec = @DisplayStart + @DisplayLength;                                       
                                      
                                     
                                     
  ;WITH TempResult as                                                    
   ( select top 20 ROW_NUMBER() over (order by  TempId asc ) as  RowNum ,COUNT(*) over() as TotalCount                            
   ,(case   when len(Part)>30 then dbo.stripAlphahnumeric(SUBSTRING(Part,0, 30)) else dbo.stripAlphahnumeric(Part) end )as Part      
    ,(case   when len( ManufacturerName)>50 then dbo.stripAlphahtestingMfr(SUBSTRING(ManufacturerName,0, 50))  
 else dbo.stripAlphahtestingMfr(ManufacturerName) end )as Manufacturer_Name       
   /*[001]  CODE START*/      
   --,dbo.stripNumeric(Quantity) as Quantity        
    ,FLOOR(dbo.stripNumeric(Quantity)) as Quantity       
    /*[001]  CODE END*/                     
   --,dbo.stripNumeric(@UpliftPercentage) as UpliftPercentage                            
   --,dbo.stripNumeric(Price+Price*(@UpliftPercentage )/100) as UpliftPrice      
   /*[002]  CODE START*/   
   -- ,cast(dbo.stripNumeric(Price) as FLOAT ) as Price  
   ,IIF(ISNUMERIC(Price)=1,CAST(Price as FLOAT), cast(dbo.stripNumeric(Price) as FLOAT))  AS Price    
   /*[002]  CODE END*/   
  -- ,cast(dbo.stripNumeric(@UpliftPercentage) as FLOAT ) as UpliftPercentage                   
   --,cast(dbo.stripNumeric(cast(dbo.stripNumeric(Price) as float)+cast(dbo.stripNumeric(Price) as float)*(cast(dbo.stripNumeric(@UpliftPercentage) as float) )/100) as FLOAT ) as Uplift_Price        
--  ,(case when (@NoChangesToSourcingSheet=0) then cast(dbo.stripNumeric(cast(dbo.stripNumeric(Price) as float)+cast(dbo.stripNumeric(Price) as float)*(cast(dbo.stripNumeric(@UpliftPercentage) as float) )/100) as FLOAT )      
 --else cast(dbo.stripNumeric(VirtualCostPrice) as float) end) as Uplift_Price      
    ,(case when (@NoChangesToSourcingSheet=3) then cast(dbo.stripNumeric(VirtualCostPrice) as float)      
 else cast(dbo.stripNumeric(cast(dbo.stripNumeric(Price) as float)+cast(dbo.stripNumeric(Price) as float)*(cast(dbo.stripNumeric(@UpliftPercentage) as float) )/100) as FLOAT ) end) as Uplift_Price      
 , (case   when len(LeadTime)>30 then dbo.stripAlphahnumeric2(SUBSTRING(LeadTime,0, 30))else dbo.stripAlphahnumeric2(LeadTime) end )as Lead_Time        
 , (case   when len(DateCode)>30 then dbo.stripAlphahnumeric2(SUBSTRING(DateCode,0, 30))else dbo.stripAlphahnumeric2(DateCode) end )as Date_Code       
 , (case   when len(SPQ)>30 then dbo.stripAlphahnumeric2(SUBSTRING(SPQ,0, 30))else dbo.stripAlphahnumeric2(SPQ) end )as SPQ                                       
 , (case   when len(SupplierMOQ)>30 then dbo.stripAlphahnumeric2(SUBSTRING(SupplierMOQ,0, 30))else dbo.stripAlphahnumeric2(SupplierMOQ) end )as MOQ                        
 --,(select top 1 CompanyId from tbcompany  c where lower(c.CompanyName)=lower(t.SupplierName) COLLATE SQL_Latin1_General_CP1_CI_AI and c.ClientNo=@SelectedClientId)as SupplierNo                      
 ,(case   when len(SupplierName)>50 then dbo.stripAlphahnumeric(SUBSTRING(SupplierName,0, 50))else dbo.stripAlphahnumeric(SupplierName) end )as Vender         
 ,(case   when len(SupplierType)>50 then dbo.stripAlphahnumeric(SUBSTRING(SupplierType,0, 50))else dbo.stripAlphahnumeric(SupplierType) end )as Vender_Type        
 ,(case   when len(FactorySealed)>50 then dbo.stripAlphahnumeric(SUBSTRING(FactorySealed,0, 50))else dbo.stripAlphahnumeric(FactorySealed) end )as FactorySealed        
 ,(case   when len(SupplierLTB)>50 then dbo.stripAlphahnumeric(SUBSTRING(SupplierLTB,0, 50))else dbo.stripAlphahnumeric(SupplierLTB) end )as SupplierLTB        
 --, (case   when len(OriginalEntryDate)>50 then dbo.stripAlphahnumeric2(OriginalEntryDate)else dbo.stripAlphahnumeric2(OriginalEntryDate) end )as OfferDate                         
 , (case   when len(OriginalEntryDate)>50 then OriginalEntryDate else OriginalEntryDate end )as Date              
 ,dbo.stripAlphahnumeric(Description) as Description                
  from BorisGlobalTraderimports.dbo.tbEpoToBeImported_Temp t WHERE  ClientId=@ClientId                                      
  and SelectedClientId=@SelectedClientId and CreatedBy=@UserId )                                                  
   SELECT *  from TempResult where RowNum >  cast(@FirstRec as varchar(20)) and  RowNum <=  cast(@LastRec as varchar(20))                        
 END       
GO


