﻿
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/* 
================================================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-210540]		Phuc Hoang			16-Aug-2024		UPDATE			[PROD Bug] DHL Interface - Label Print Issue
[US-232192]		Phuc Hoang		    20-Feb-2025		UPDATE		    [PROD Bug] DHL BBX Shipments
================================================================================================================
*/

CREATE OR ALTER  PROCEDURE [dbo].[ups_GetBBXInvoiceDataList]                                             
(                 
  @ClientId int,                
  @LoginId int,                
  @ParentId varchar(500)              
)                
AS 
 BEGIN                
   SELECT ts.*,(
   SELECT upsCt.CountryCode 
   FROM dbo.UPSCountryList upsCt
   JOIN tbGlobalCountryList glb ON upsCt.GTCountryID = glb.GlobalCountryId
   WHERE upsCt.GTCountryID = ts.CountryOfOrigin --AND glb.GlobalCountryName NOT LIKE '%EMBARGOED DESTINATION%'

   ) as CountryCode, 
   caw.Weight,
   caw.NumberOfBoxes,
   caw.DimensionalWeight,
   caw.InitialNote

   FROM ChildInvoiceAWBDHL caw            
   Left join tbSaveBBXInvoiceLineItems ts ON caw.InvoiceNo=ts.InvoiceNo  
   --caw.AWBNo=ts.AWBNo and           
   WHERE caw.SendStatus=1 and caw.ClientId=@ClientId and caw.ParentID=@ParentId and ts.isactive=1              
   --and LoginId=@LoginId                
 END 
 
GO


