///<reference name="MicrosoftAjax.js" />
///<reference path="~/Common/Data/Data.js" />
///<reference path="~/Common/Functions/Functions.js" />
///<reference path="~/Controls/IconButton/IconButton.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - dispose everything fully
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls");

Rebound.GlobalTrader.Site.Controls.ImageCheckBox = function(element) { 
	Rebound.GlobalTrader.Site.Controls.ImageCheckBox.initializeBase(this, [element]);
	this._blnChecked = false;
};

Rebound.GlobalTrader.Site.Controls.ImageCheckBox.prototype = {

	get_blnChecked: function() { return this._blnChecked; }, 	set_blnChecked: function(value) { if (this._blnChecked !== value)  this._blnChecked = value; }, 
	get_blnEnabled: function() { return this._blnEnabled; }, 	set_blnEnabled: function(value) { if (this._blnEnabled !== value)  this._blnEnabled = value; }, 
	get_img: function() { return this._img; }, 	set_img: function(value) { if (this._img !== value)  this._img = value; }, 

	addClick: function(handler) { this.get_events().addHandler("Click", handler); },
	removeClick: function(handler) { this.get_events().removeHandler("Click", handler); },
	onClick: function() { 
		var handler = this.get_events().getHandler("Click");
		if (handler) handler(this, Sys.EventArgs.Empty); 
	},

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.ImageCheckBox.callBaseMethod(this, "initialize");
		this.enableButton(this._blnEnabled);
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this.get_element()) $clearHandlers(this.get_element());
		this._blnChecked = null;
		this._blnEnabled = null;
		this._img = null;
		Rebound.GlobalTrader.Site.Controls.ImageCheckBox.callBaseMethod(this, "dispose");
		this.isDisposed = true;
	},
	
	enableButton: function(blnEnable) {
		this._blnEnabled = blnEnable;
		this.get_element().className = (this._blnEnabled) ? "imageCheckBox" : "imageCheckBoxDisabled";
		if (this._blnEnabled) {
			$clearHandlers(this.get_element());
			$addHandler(this.get_element(), "click", Function.createDelegate(this, this.toggleChecked));
			//$addHandler(this.get_element(), "mouseover", Function.createDelegate(this, this.onMouseOver));
			//$addHandler(this.get_element(), "mouseout", Function.createDelegate(this, this.onMouseOut));
		} else {
			$clearHandlers(this.get_element());
		}
	},
	
	setChecked: function(blnChecked) {
		this._blnChecked = blnChecked;
		this.updateDisplay();
	},

	toggleChecked: function() {
		this._blnChecked = !this._blnChecked;
		this.updateDisplay();
		this.onClick();
	},
	
	updateDisplay: function() {
		this._img.className = (this._blnChecked) ? "on" : "off";
		if (this._beingHovered) this._img.className += "Over";
	},
	
	onMouseOver: function() {
		this._beingHovered = true;
		this.updateDisplay();
	},
	
	onMouseOut: function() {
		this._beingHovered = false;
		this._img.className = (this._blnChecked) ? "on" : "off";
	}
	
};

Rebound.GlobalTrader.Site.Controls.ImageCheckBox.registerClass("Rebound.GlobalTrader.Site.Controls.ImageCheckBox", Sys.UI.Control, Sys.IDisposable);