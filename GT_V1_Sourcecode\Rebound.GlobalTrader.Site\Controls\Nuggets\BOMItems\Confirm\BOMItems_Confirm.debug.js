///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_Confirm = function(element) {
    Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_Confirm.initializeBase(this, [element]);
    this._intRequirementLineID = -1;
    this._intBOMID = -1;
    this._CustReqNo = -1;
    this._ReqSalesman = -1;
    this._SupportTeamMemberNo = null;
    this._UnitBuyPrice = null;
    this._UnitSellPrice = null;
};

Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_Confirm.prototype = {

    get_BomCode: function() { return this._BomCode; }, set_BomCode: function(value) { if (this._BomCode !== value) this._BomCode = value; },
    get_BomName: function() { return this._BomName; }, set_BomName: function(value) { if (this._BomName !== value) this._BomName = value; },
    get_BomCompanyName: function() { return this._BomCompanyName; }, set_BomCompanyName: function(value) { if (this._BomCompanyName !== value) this._BomCompanyName = value; },
    get_BomCompanyNo: function() { return this._BomCompanyNo; }, set_BomCompanyNo: function(value) { if (this._BomCompanyNo !== value) this._BomCompanyNo = value; },
    get_SalesManNo: function() { return this._SalesManNo; }, set_SalesManNo: function(value) { if (this._SalesManNo !== value) this._SalesManNo = value; },
    get_SalesManName: function() { return this._SalesManName; }, set_SalesManName: function(value) { if (this._SalesManName !== value) this._SalesManName = value; },
    get_tblAllReleasedetails: function () { return this._tblAllReleasedetails; }, set_tblAllReleasedetails: function (v) { if (this._tblAllReleasedetails !== v) this._tblAllReleasedetails = v; },
    initialize: function() {
        Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_Confirm.callBaseMethod(this, "initialize");
        this.addShown(Function.createDelegate(this, this.formShown));
    },

    dispose: function() {
        if (this.isDisposed) return;
        if (this._ctlConfirm) this._ctlConfirm.dispose();
        this._ctlConfirm = null;
        this._intRequirementLineID = null;
        this._ReqSalesman = null;
        this._SupportTeamMemberNo = null;
        this._tblAllReleasedetails = null;
        this._UnitBuyPrice = null;
        this._UnitSellPrice = null;
        Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_Confirm.callBaseMethod(this, "dispose");
    },

    formShown: function () {
        if (this._blnFirstTimeShown) {
            this._tblAllReleasedetails.clearTable();
            this.getSerialDetail(this._intRequirementLineID)
            this._tblAllReleasedetails.resizeColumns();

            this._ctlConfirm = this.getFieldComponent("ctlConfirm");
            this._ctlConfirm.addClickYesEvent(Function.createDelegate(this, this.yesClicked));
            this._ctlConfirm.addClickNoEvent(Function.createDelegate(this, this.noClicked));
        }
        else {
            this._tblAllReleasedetails.clearTable();
            this.getSerialDetail(this._intRequirementLineID)
            this._tblAllReleasedetails.resizeColumns();
        }
    },
    //code added for show error message when buy price grater then sell price
    getSerialDetail: function (intRequirementLineID) {
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/BOMCusReqSourcingResults");
        obj.set_DataObject("BOMCusReqSourcingResults");
        obj.set_DataAction("GetData");
        obj.addParameter("id", intRequirementLineID);
        obj.addDataOK(Function.createDelegate(this, this.getAllReleaseOK));
        obj.addError(Function.createDelegate(this, this.getAllReleaseError));
        obj.addTimeout(Function.createDelegate(this, this.getAllReleaseError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getAllReleaseOK: function (args) {
        res = args._result;
        var intCount = 0;
        var strwarningMsg = null;
        if (res.Results.length > 0) {
            var aryData = null;
            for (var i = 0; i < res.Results.length; i++) {
                var row = res.Results[i];
                strwarningMsg = null;
                if (row.UnitBuyPrice != null && row.UnitSellPrice != null) {
                    if (row.UnitBuyPrice >= row.UnitSellPrice) {
                        if (row.UnitBuyPrice > row.UnitSellPrice) { strwarningMsg = "The buy price (" + row.BuyPrice +") of selected part is greater then the sell price ("+row.Price +") "; }
                        if (row.UnitBuyPrice == row.UnitSellPrice) { strwarningMsg = "The buy price ("+row.BuyPrice+") of selected part is equal to the sell price ("+row.Price +")"; }
                        aryData = [
                             $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Supplier), $R_FN.writePartNo(row.Part))
                            , $R_FN.setCleanTextValue(row.BuyPrice)
                            , $R_FN.setCleanTextValue(row.Price)
                            , $R_FN.setCleanTextValue($R_FN.setCleanTextValue(strwarningMsg))
                        ];
                        intCount++

                    }
                    else {
                        $("#ctl00_cphMain_ctlBOMItems_ctlDB_ctl14_ctlConfirm_ctlDB_ctlSerialNoDetail").hide();

                    }

                }
                if (intCount > 0) {
                    $("#ctl00_cphMain_ctlBOMItems_ctlDB_ctl14_ctlConfirm_ctlDB_ctlSerialNoDetail").show();
                    this._tblAllReleasedetails.addRow(aryData, row.REQID, false);
                }
                else { $("#ctl00_cphMain_ctlBOMItems_ctlDB_ctl14_ctlConfirm_ctlDB_ctlSerialNoDetail").hide(); }
                row = null; aryData = null;
            }
           
        }
        else {
            $("#ctl00_cphMain_ctlBOMItems_ctlDB_ctl14_ctlConfirm_ctlDB_ctlSerialNoDetail").hide();
        }
    },

    getAllReleaseError: function (args) {
       
    },

    showLoadingAllRelease: function (blnShow) {
   
    },

    showAllReleaseError: function (blnShow, strMessage) {
    },


    //code end
    yesClicked: function () {
        //alert(this._SupportTeamMemberNo);
        this.showSaving(true);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("Controls/Nuggets/BOMItems");
        obj.set_DataObject("BOMItems");
        obj.set_DataAction("ReleaseRequirement");
        obj.addParameter("id", this._intRequirementLineID);
        obj.addParameter("BomId", this._intBOMID);
        obj.addParameter("BomCode", this._BomCode);
        obj.addParameter("BomName", this._BomName);
        obj.addParameter("BomCompanyName", this._BomCompanyName);
        obj.addParameter("BomCompanyNo", this._BomCompanyNo);
        obj.addParameter("SalesManName", this._SalesManName);
        obj.addParameter("SalesManNo", this._SalesManNo);
        obj.addParameter("CustReqNo", this._CustReqNo);
        obj.addParameter("Reqsalesman", this._ReqSalesman);
        obj.addParameter("SupportTeamMemberNo", this._SupportTeamMemberNo);
        obj.addDataOK(Function.createDelegate(this, this.saveComplete));
        obj.addError(Function.createDelegate(this, this.saveError));
        obj.addTimeout(Function.createDelegate(this, this.saveError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },


    noClicked: function() {
        this.showSaving(false);
        this.onNotConfirmed();
    },

    saveError: function(args) {
        this.showSaving(false);
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    saveComplete: function (args) {
        this.showSaving(false);
        if (args._result.Result) {
            this.onSaveComplete();
        } else {
            this._strErrorMessage = args._errorMessage;
            this.onSaveError();
        }
    }




};

Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_Confirm.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_Confirm", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
