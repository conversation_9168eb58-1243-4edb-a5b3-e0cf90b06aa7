﻿SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].[usp_ipobom_source_Excess]               
--********************************************************************************************                    
--* RP 09.03.2011:                    
--* - add recompile option                    
--*                    
--* RP 25.05.2010:                    
--* - remove UNIONS, process Clients in code                    
--*                    
--* SK 17.02.2010:                    
--* - adjust display of external client data                     
--*                    
--* SK 20.01.2010:                    
--* - add ClientId to parameters and predicate: if equal display data as now, if not show                      
--*   Client<PERSON>ame as customer  - with no hyperlink - and do not show any price                      
--*                    
--* SK 13.11.2009:                    
--* - coalesce Manufacturer, Product and Package with GO values derived from import                    
--*                    
--* RP 05.06.2009:                    
--* - search with <PERSON><PERSON>KE                    
--*                    
--* SK 01.06.2009:                    
--* - add order by clause                    
--*Marker     Changed by      Date         Remarks                    
--*[001]      Vinay           15/10/2012   Display supplier type in stock grid                  
--********************************************************************************************                        
     @ClientId INT                    
   , @PartSearch NVARCHAR(50) = '%%'                
   , @Index int =1                
   , @StartDate datetime = NULL                  
   , @FinishDate datetime = NULL 
   , @IsPoHUB bit =NULL               
    WITH RECOMPILE AS   
BEGIN             
   --DECLARE VARIABLE            
     DECLARE @Month int              
     DECLARE @FROMDATE DATETIME              
     DECLARE @ENDDATE DATETIME              
     DECLARE @OutPutDate DATETIME              
                 
   --CALCULATE NO OF MONTH TO DISPLAY RECORDS              
     SET @Month=6             
     /*            
        When we get index 1 then we find the maximum date from matching record            
        and decsrease no of month for the start date.            
        Next time when we get the index 2 then we decrease the no of month from previous max date for start date            
        In the last time we hardcoded minimum date value for whole remaining records            
     */
	  declare @HUBName nvarchar(300)     
	  select top 1 @HUBName = CompanyName from tbCompany where ClientNo = @ClientId and IsPOHub=1  
	              
     IF @Index=1              
     BEGIN              
      SELECT @FinishDate=MAX(ISNULL(OfferStatusChangeDate, OriginalEntryDate)) FROM    [BorisGlobalTraderImports].dbo.tbExcess e    
      JOIN    dbo.tbClient cl ON e.ClientNo = cl.ClientId                  
      WHERE    ((e.ClientNo = @ClientId)                    
                    OR (e.ClientNo <> @ClientId                    
                  --  AND cl.OwnDataVisibleToOthers = 1))     
				  	AND (case when e.ClientNo=114 then cast(1 as bit) else  cl.OwnDataVisibleToOthers end) = 1 )) 
             AND FullPart LIKE @PartSearch                
      SET @FROMDATE=dbo.ufn_get_date_from_datetime(DATEADD(month,-@Month,@FinishDate))              
      SET @ENDDATE =dbo.ufn_get_date_from_datetime(@FinishDate)              
     END              
     ELSE               
     BEGIN                
       SET @FROMDATE=dbo.ufn_get_date_from_datetime(@StartDate)                
       SET @ENDDATE =dbo.ufn_get_date_from_datetime(@FinishDate)                
     END        
      --SET THE OUTPUT DATE            
      SET @OutPutDate=DATEADD(month,-@Month,@FinishDate)               
 	 -- If Index value equal to 3 then more than one year data will be pick from archive database.	  
   IF @Index = 3
 BEGIN                   
    SELECT  e.ExcessId                    
          , e.ExcessName                    
          , e.FullPart                   
          , e.Part                    
          , e.ManufacturerNo 
          , e.DateCode                    
          , e.ProductNo                    
          , e.PackageNo                    
          , e.Quantity        
         -- , e.Price                    
		 ,case when e.ClientNo=114 then 0 else e.Price end AS Price
          , e.CurrencyNo                    
          , e.OriginalEntryDate                    
          , e.Salesman                    
          , e.CompanyNo                    
          , e.ROHS                    
          , e.UpdatedBy                    
          , e.DLUP                    
          , e.OfferStatusNo                    
          , ISNULL(e.OfferStatusChangeDate, e.OriginalEntryDate) AS OfferStatusChangeDate                    
          , e.OfferStatusChangeLoginNo                    
          , e.CompanyName                    
         -- , s.CompanyName AS SupplierName                    
		 , case when e.ClientNo=114 then @HUBName else  s.CompanyName end AS SupplierName  
          , s.EMail AS SupplierEmail                    
          , ISNULL(m.ManufacturerName, e.ManufacturerName) AS ManufacturerName                    
          , m.ManufacturerCode                    
          , ISNULL(p.ProductName, e.ProductName) AS ProductName                 
          , c.CurrencyCode                    
          , l.EmployeeName AS SalesmanName                    
          , l2.EmployeeName AS OfferStatusChangeEmployeeName                    
          , ISNULL(g.PackageName, e.PackageName) AS PackageName                    
          , e.Notes             
          , e.ClientNo                    
          , cl.ClientName                    
          , cl.OwnDataVisibleToOthers AS ClientDataVisibleToOthers                    
          --[001] code start                  
          , ISNULL(cotype.Name,'') as SupplierType                
          --[001] code end               
          , cl.ClientCode   
    , e.SPQ  
          , e.LeadTime  
          , e.ROHSStatus  
          , e.FactorySealed  
        --  , e.MSL     
        , ml.MSLLevel as MSL
    ,e.SupplierTotalQSA  
    ,e.SupplierLTB   
    ,e.SupplierMOQ    
	, dbo.ufn_GetSupplierMessage(e.CompanyNo) as  SupplierMessage          
    FROM    [BorisGlobalTraderArchive].dbo.tbExcess_Arc e               
    JOIN    dbo.tbClient cl ON e.ClientNo = cl.ClientId                    
    LEFT JOIN dbo.tbCompany s ON e.CompanyNo = s.CompanyId                    
    LEFT JOIN dbo.tbManufacturer m ON e.ManufacturerNo = m.ManufacturerId                    
    LEFT JOIN dbo.tbProduct p ON e.ProductNo = p.ProductId                    
    LEFT JOIN dbo.tbCurrency c ON e.CurrencyNo = c.CurrencyId                    
    LEFT JOIN dbo.tbLogin l ON e.Salesman = l.LoginId                    
    LEFT JOIN dbo.tbLogin l2 ON e.OfferStatusChangeLoginNo = l2.LoginId                    
    LEFT JOIN dbo.tbPackage g ON e.PackageNo = g.PackageId                    
    --[001] code start                  
    LEFT JOIN dbo.tbCompanyType cotype ON s.TypeNo=cotype.CompanyTypeId                  
    --[001] code end   
    left join tbMSLLevel ml on e.MSLLevelNo = ml.MSLLevelId                                                                      
    WHERE     ((e.ClientNo = @ClientId)                    
             OR (e.ClientNo <> @ClientId                    
                 --AND cl.OwnDataVisibleToOthers = 1))  
				 	AND (case when e.ClientNo=114 then cast(1 as bit) else  cl.OwnDataVisibleToOthers end) = 1 )) 
                  AND ((@IsPoHUB is NULL) 
                OR (not @IsPoHUB is NULL AND isnull(e.IsPoHub,0)= @IsPoHUB ))     
    AND    
    e.FullPart LIKE @PartSearch    
    AND (dbo.ufn_get_date_from_datetime(ISNULL(e.OfferStatusChangeDate, e.OriginalEntryDate)) between @FROMDATE   AND  @ENDDATE)                      
  ORDER BY ISNULL(e.OfferStatusChangeDate, e.OriginalEntryDate) DESC    
END
ELSE
BEGIN
     SELECT  e.ExcessId                    
          , e.ExcessName                    
          , e.FullPart                   
          , e.Part                    
          , e.ManufacturerNo                    
          , e.DateCode                    
          , e.ProductNo         
          , e.PackageNo                    
          , e.Quantity        
          --, e.Price                    
		    ,case when e.ClientNo=114 then 0 else e.Price end AS Price
          , e.CurrencyNo                    
          , e.OriginalEntryDate                    
          , e.Salesman                    
          , e.CompanyNo                    
          , e.ROHS                    
          , e.UpdatedBy                    
          , e.DLUP                    
          , e.OfferStatusNo                    
          , ISNULL(e.OfferStatusChangeDate, e.OriginalEntryDate) AS OfferStatusChangeDate                    
          , e.OfferStatusChangeLoginNo                    
          , e.CompanyName                    
          --, s.CompanyName AS SupplierName                    
		   , case when e.ClientNo=114 then @HUBName else  s.CompanyName end AS SupplierName  
          , s.EMail AS SupplierEmail                    
          , ISNULL(m.ManufacturerName, e.ManufacturerName) AS ManufacturerName                    
          , m.ManufacturerCode                    
          , ISNULL(p.ProductName, e.ProductName) AS ProductName                 
          , c.CurrencyCode                    
          , l.EmployeeName AS SalesmanName                    
          , l2.EmployeeName AS OfferStatusChangeEmployeeName                    
          , ISNULL(g.PackageName, e.PackageName) AS PackageName                    
          , e.Notes             
          , e.ClientNo                    
          , cl.ClientName                    
          , cl.OwnDataVisibleToOthers AS ClientDataVisibleToOthers                    
          --[001] code start                  
          , ISNULL(cotype.Name,'') as SupplierType                
          --[001] code end               
          , cl.ClientCode   
    , e.SPQ  
          , e.LeadTime  
          , e.ROHSStatus  
          , e.FactorySealed  
        --  , e.MSL     
        , ml.MSLLevel as MSL
    ,e.SupplierTotalQSA  
    ,e.SupplierLTB   
    ,e.SupplierMOQ   
	, dbo.ufn_GetSupplierMessage(e.CompanyNo) as  SupplierMessage           
    FROM    [BorisGlobalTraderImports].dbo.tbExcess e                  
    JOIN    dbo.tbClient cl ON e.ClientNo = cl.ClientId                    
    LEFT JOIN dbo.tbCompany s ON e.CompanyNo = s.CompanyId                    
    LEFT JOIN dbo.tbManufacturer m ON e.ManufacturerNo = m.ManufacturerId                    
    LEFT JOIN dbo.tbProduct p ON e.ProductNo = p.ProductId                    
    LEFT JOIN dbo.tbCurrency c ON e.CurrencyNo = c.CurrencyId                    
    LEFT JOIN dbo.tbLogin l ON e.Salesman = l.LoginId                    
    LEFT JOIN dbo.tbLogin l2 ON e.OfferStatusChangeLoginNo = l2.LoginId                    
    LEFT JOIN dbo.tbPackage g ON e.PackageNo = g.PackageId                    
    --[001] code start                  
    LEFT JOIN dbo.tbCompanyType cotype ON s.TypeNo=cotype.CompanyTypeId                  
    --[001] code end   
    left join tbMSLLevel ml on e.MSLLevelNo = ml.MSLLevelId                                                                      
    WHERE     ((e.ClientNo = @ClientId)                    
             OR (e.ClientNo <> @ClientId                    
                 --AND cl.OwnDataVisibleToOthers = 1))  
				 	AND (case when e.ClientNo=114 then cast(1 as bit) else  cl.OwnDataVisibleToOthers end) = 1 )) 
                  AND ((@IsPoHUB is NULL) 
                OR (not @IsPoHUB is NULL AND isnull(e.IsPoHub,0)= @IsPoHUB ))     
    AND    
    e.FullPart LIKE @PartSearch                 
    AND (dbo.ufn_get_date_from_datetime(ISNULL(e.OfferStatusChangeDate, e.OriginalEntryDate)) between @FROMDATE   AND  @ENDDATE)                      
    ORDER BY ISNULL(e.OfferStatusChangeDate, e.OriginalEntryDate) DESC    
END             
    --SELECT THE OUT DATE             
    SELECT @OutPutDate AS OutPutDate 
END








