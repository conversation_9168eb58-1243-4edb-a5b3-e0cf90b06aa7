﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="ActiveVendors" xml:space="preserve">
    <value>Aktive Verkäufer von [#STARTDATE#] zu [#ENDDATE#]</value>
  </data>
  <data name="AllBuyers" xml:space="preserve">
    <value>Alle Kaufer</value>
  </data>
  <data name="AllSalespeople" xml:space="preserve">
    <value>Alle Verkäufer</value>
  </data>
  <data name="AllWarehouses" xml:space="preserve">
    <value>Alle Lager</value>
  </data>
  <data name="ApprovedCustomersOnStop" xml:space="preserve">
    <value>Anerkannte Kunden auf Anschlag</value>
  </data>
  <data name="AutoEnteredSuppliers_Unedited" xml:space="preserve">
    <value>Eingeführte Selbstlieferanten ([#STARTDATE#] - [#ENDDATE#])</value>
  </data>
  <data name="ClosedRequirementsReasons" xml:space="preserve">
    <value>Geschlossene Anforderungs-Gründe</value>
  </data>
  <data name="CommunicationLogActivityforaUser" xml:space="preserve">
    <value>Kommunikations-Maschinenbordbuch-Tätigkeit für [#EMPLOYEE#] von [#STARTDATE#] zu [#ENDDATE#]</value>
  </data>
  <data name="CompaniesApprovedToPurchaseFrom" xml:space="preserve">
    <value>Firmen Genehmigten, um von zu Kkaufen</value>
  </data>
  <data name="CompaniesNotContacted" xml:space="preserve">
    <value>Nicht seit dem in Verbindung getreten [#CUTOFFDATE#]</value>
  </data>
  <data name="ContactEmailList" xml:space="preserve">
    <value>Kontakt-eMail-Liste innen [#COUNTRY#]</value>
  </data>
  <data name="ContactsNotContacted" xml:space="preserve">
    <value>Kontakte nicht seit dem in Verbindung getreten [#CUTOFFDATE#]</value>
  </data>
  <data name="CreditNotes" xml:space="preserve">
    <value>Kreditnoten von [#STARTDATE#] to [#ENDDATE#]</value>
  </data>
  <data name="CreditNotesforaCustomer" xml:space="preserve">
    <value>Kreditnoten für [#COMPANY_NAME#] from [#STARTDATE#] to [#ENDDATE#]</value>
  </data>
  <data name="CreditNotesforaSalesperson" xml:space="preserve">
    <value>Kreditnoten für [#SALESPERSON#] from [#STARTDATE#] to [#ENDDATE#]</value>
  </data>
  <data name="CustomerListforSalesperson" xml:space="preserve">
    <value>Kunden-Liste für [#SALESPERSON#]</value>
  </data>
  <data name="CustomerOnTimeDeliveryReport" xml:space="preserve">
    <value>Kunde auf Zeit-Anlieferungs-Report von [#STARTDATE#] to [#ENDDATE#]</value>
  </data>
  <data name="CustomerStatement" xml:space="preserve">
    <value>Kunden-Aussage für [#COMPANY_NAME#]  [#LATE_ONLY#]</value>
  </data>
  <data name="DailyCustomerRequirementsbySalesperson_Detailed" xml:space="preserve">
    <value>Tägliche Kunden-Anforderungs-Details für [#SALESPERSON#] von [#STARTDATE#] zu [#ENDDATE#]</value>
  </data>
  <data name="DailyCustomerRequirementsbySalesperson_Summary" xml:space="preserve">
    <value>Tägliche Kunden-Anforderungen zusammenfassend für [#SALESPERSON#] von [#STARTDATE#] zu [#ENDDATE#]</value>
  </data>
  <data name="DailyCustomerRequirementsbySalesperson_Totals" xml:space="preserve">
    <value>Tägliche Kunden-Anforderungs Gesamtmengen für [#SALESPERSON#] von [#STARTDATE#] zu [#ENDDATE#]</value>
  </data>
  <data name="DailyImports" xml:space="preserve">
    <value>Tägliche Importe ([#STARTDATE#] - [#ENDDATE#])</value>
  </data>
  <data name="DailyImportsBySource" xml:space="preserve">
    <value>Tägliche Importe durch Source ([#STARTDATE#] - [#ENDDATE#])</value>
  </data>
  <data name="DaysSinceLastInvoicebyContact" xml:space="preserve">
    <value>Tage seit letzte Rechnung durch Kontakt</value>
  </data>
  <data name="DaysSinceLastInvoicebyCustomer" xml:space="preserve">
    <value>Tage seit letzter Rechnung durch Kunde</value>
  </data>
  <data name="GoodsReceived" xml:space="preserve">
    <value>Waren Empfangen von [#STARTDATE#] zu [#ENDDATE#]</value>
  </data>
  <data name="GoodsReceivedNotInvoiced" xml:space="preserve">
    <value>Waren Empfingen nicht Fakturiert</value>
  </data>
  <data name="GoodsReceivedShipmentDetails" xml:space="preserve">
    <value>Waren Empfingen Versanddetails von [#STARTDATE#] zu [#ENDDATE#]</value>
  </data>
  <data name="GrossProfitBreakdown" xml:space="preserve">
    <value>Bruttogewinn-Zusammenbruch von [#STARTDATE#] zu [#ENDDATE#]</value>
  </data>
  <data name="IncludesCredits" xml:space="preserve">
    <value>Schließt Gutschriften ein</value>
  </data>
  <data name="IncludesShipping" xml:space="preserve">
    <value>Schließt Verschiffen ein</value>
  </data>
  <data name="IntrastatReportforEECArrivals_CustomerRMAs" xml:space="preserve">
    <value>Intrastat berichten für EWG-Ankünfte (Kunde RMAs) für [#WAREHOUSE#] von [#STARTDATE#] zu [#ENDDATE#]</value>
  </data>
  <data name="IntrastatReportforEECArrivals_Purchases" xml:space="preserve">
    <value>Intrastat berichten für EWG-Ankünfte (Käufe) für [#WAREHOUSE#] von [#STARTDATE#] zu [#ENDDATE#]</value>
  </data>
  <data name="IntrastatReportforEECDispatches_Sales" xml:space="preserve">
    <value>Intrastat berichten für EWG-Abfertigungen (Verkäufe) für [#WAREHOUSE#] von [#STARTDATE#] zu [#ENDDATE#]</value>
  </data>
  <data name="IntrastatReportforEECDispatches_SupplierRMAs" xml:space="preserve">
    <value>Intrastat berichten für EWG-Abfertigungen (Lieferant RMAs) für [#WAREHOUSE#] von [#STARTDATE#] zu [#ENDDATE#]</value>
  </data>
  <data name="InventoryLocationReport" xml:space="preserve">
    <value>Warenbestand-Positions Report für [#WAREHOUSE#]</value>
  </data>
  <data name="InventoryLocationReportforLot" xml:space="preserve">
    <value>Warenbestand-Positions Report für Los [#LOT#] at [#WAREHOUSE#] </value>
  </data>
  <data name="InvoicesSortedbyInvoiceNumber" xml:space="preserve">
    <value>Rechnungen sortiert von Invoice Nr von [#STARTDATE#] zu [#ENDDATE#] [#INCLUDING_SHIPPING#]</value>
  </data>
  <data name="InvoicesSortedbyInvoiceNumberforaCustomer" xml:space="preserve">
    <value>Rechnungen sortiert von Invoice Nr für [#COMPANY_NAME#] fvon rom [#STARTDATE#] zu [#ENDDATE#]</value>
  </data>
  <data name="InvoicesSortedbyInvoiceNumberforaSalesperson" xml:space="preserve">
    <value>Rechnungen sortiert von Invoice Nr für [#SALESPERSON#] von [#STARTDATE#] zu [#ENDDATE#]</value>
  </data>
  <data name="LoginStatistics" xml:space="preserve">
    <value>Login Statistiken  ([#STARTDATE#] - [#ENDDATE#])</value>
  </data>
  <data name="LoginStatisticsbyName" xml:space="preserve">
    <value>Login Statistiken für [#USERNAME#] ([#STARTDATE#] - [#ENDDATE#])</value>
  </data>
  <data name="NumberofAccountsbySalesperson" xml:space="preserve">
    <value>Zahl Rechnungsprüfungen durch Salesperson</value>
  </data>
  <data name="NumberofOffersbyVendor" xml:space="preserve">
    <value>Angebote durch Lieferant</value>
    <comment>was "Supplier Offers"</comment>
  </data>
  <data name="NumberofOffersHistorybyVendor" xml:space="preserve">
    <value>Angebote durch Lieferant - Geschichte</value>
    <comment>was "Supplier Offers in History"</comment>
  </data>
  <data name="NumberofRequirementsbyVendor" xml:space="preserve">
    <value>Anforderungen durch Customer</value>
    <comment>was "Supplier Offers in History"</comment>
  </data>
  <data name="OpenCustomerRMAs" xml:space="preserve">
    <value>Öffnen Kunden RMAs von [#STARTDATE#] zu [#ENDDATE#]</value>
  </data>
  <data name="OpenCustomerRMAsforaCustomer" xml:space="preserve">
    <value>Öffnen Kunden RMAs für [#COMPANY_NAME#] von [#STARTDATE#] zu [#ENDDATE#]</value>
  </data>
  <data name="OpenCustomerRMAsforaCustomerwithReasons" xml:space="preserve">
    <value>Öffnen Kunden RMAs für[#COMPANY_NAME#] von [#STARTDATE#] zu [#ENDDATE#]</value>
  </data>
  <data name="OpenCustomerRMAsforaSaleperson" xml:space="preserve">
    <value>Öffnen Kunden RMAs für [#SALESPERSON#] von [#STARTDATE#] zu [#ENDDATE#]</value>
  </data>
  <data name="OpenCustomerRMAsforaSalepersonwithReasons" xml:space="preserve">
    <value>Öffnen Kunden RMAs für [#SALESPERSON#] von [#STARTDATE#] zu [#ENDDATE#]</value>
  </data>
  <data name="OpenCustomerRMAswithReasons" xml:space="preserve">
    <value>Öffnen Kunden RMAs von [#STARTDATE#] zu [#ENDDATE#]</value>
  </data>
  <data name="OpenRequirementsbyCustomer" xml:space="preserve">
    <value>Öffnen Anforderungen durch Customer</value>
  </data>
  <data name="OpenRequirementsReportBySalesperson" xml:space="preserve">
    <value>Öffnen Anforderungen für [#SALESPERSON#]</value>
  </data>
  <data name="OpenSalesOrders" xml:space="preserve">
    <value>Öffnen Verkaufs-Aufträge [#POSTED_STATUS#]</value>
  </data>
  <data name="OpenSalesOrdersforSalesperson" xml:space="preserve">
    <value>Öffnen Verkaufs-Aufträge für [#SALESPERSON#][#POSTED_STATUS#]</value>
  </data>
  <data name="OpenSupplierRMAs" xml:space="preserve">
    <value>Öffnen Lieferanten RMAs von [#STARTDATE#] zu [#ENDDATE#]</value>
  </data>
  <data name="OpenSupplierRMAsforaBuyer" xml:space="preserve">
    <value>Öffnen Lieferanten RMAs für [#BUYER#] von [#STARTDATE#] zu [#ENDDATE#]</value>
  </data>
  <data name="OpenSupplierRMAsforaBuyerwithReasons" xml:space="preserve">
    <value>Öffnen Lieferanten RMAs für [#BUYER#] von [#STARTDATE#] zu [#ENDDATE#]</value>
  </data>
  <data name="OpenSupplierRMAsforaSupplier" xml:space="preserve">
    <value>Öffnen Lieferanten RMAs für [#COMPANY_NAME#] von [#STARTDATE#] zu [#ENDDATE#]</value>
  </data>
  <data name="OpenSupplierRMAsforaSupplierwithReasons" xml:space="preserve">
    <value>Öffnen Lieferanten RMAs für [#COMPANY_NAME#] von [#STARTDATE#] zu [#ENDDATE#]</value>
  </data>
  <data name="OpenSupplierRMAswithReasons" xml:space="preserve">
    <value>Öffnen Lieferanten RMAs von [#STARTDATE#] zu [#ENDDATE#]</value>
  </data>
  <data name="OrdersToBeShipped" xml:space="preserve">
    <value>Hervorragende zu Versendende Aufträge [#DUEDATE#]</value>
  </data>
  <data name="OrdersToBeShippedBySalesperson" xml:space="preserve">
    <value>Hervorragende zu Versendende Aufträge [#DUEDATE#] for [#SALESPERSON#] </value>
  </data>
  <data name="OustandingCustomerInvoices" xml:space="preserve">
    <value>Ausstehende Kunden-Rechnungen [#LATE_ONLY#]</value>
  </data>
  <data name="PickSheetSalesOrdersBasic" xml:space="preserve">
    <value>Auswahl-Blatt für [#DUEDATE#]</value>
  </data>
  <data name="PickSheetSalesOrdersDetailed" xml:space="preserve">
    <value>Auswahl-Blatt für [#DUEDATE#]</value>
  </data>
  <data name="PickSheetSupplierRMAs" xml:space="preserve">
    <value>Auswahl-Blatt des Lieferanten-RMA für [#DUEDATE#]</value>
  </data>
  <data name="PostedOnly" xml:space="preserve">
    <value>Nur informierte Linien</value>
  </data>
  <data name="PurchaseOrdersDueIn" xml:space="preserve">
    <value>Kaufaufträge Passend Innen von [#STARTDATE#] to [#ENDDATE#]</value>
  </data>
  <data name="PurchaseOrdersDueInforBuyer" xml:space="preserve">
    <value>Kaufaufträge Passend Innen für [#BUYER#] von [#STARTDATE#] zu [#ENDDATE#]</value>
  </data>
  <data name="PurchaseOrdersDueInforSalesperson" xml:space="preserve">
    <value>Kaufaufträge Passend Innen für [#SALESPERSON#] von [#STARTDATE#] zu [#ENDDATE#]</value>
  </data>
  <data name="PurchaseRequisitions" xml:space="preserve">
    <value>Kauf-Forderungen</value>
  </data>
  <data name="PurchaseRequisitionsforaCustomer" xml:space="preserve">
    <value>Kauf-Forderungen für [#COMPANY_NAME#]</value>
  </data>
  <data name="PurchaseRequisitionsforaSalesPerson" xml:space="preserve">
    <value>Kauf-Forderungen für [#SALESPERSON#]</value>
  </data>
  <data name="ReceivedCustomerRMAs" xml:space="preserve">
    <value>Empfangener Kunde RMAs von [#STARTDATE#] zu [#ENDDATE#]</value>
  </data>
  <data name="ReceivedCustomerRMAsforaCustomer" xml:space="preserve">
    <value>Empfangener Kunde RMAs für [#COMPANY_NAME#] von [#STARTDATE#] zu [#ENDDATE#]</value>
  </data>
  <data name="ReceivedCustomerRMAsforaCustomerwithReasons" xml:space="preserve">
    <value>Empfangener Kunde RMAs mit Gründen für  [#COMPANY_NAME#] von [#STARTDATE#] zu [#ENDDATE#]</value>
  </data>
  <data name="ReceivedCustomerRMAsforaSaleperson" xml:space="preserve">
    <value>Empfangener Kunde RMAs für [#SALESPERSON#] von [#STARTDATE#] zu [#ENDDATE#]</value>
  </data>
  <data name="ReceivedCustomerRMAsforaSalepersonwithReasons" xml:space="preserve">
    <value>Empfangener Kunde RMAs mit Gründen für [#SALESPERSON#] von [#STARTDATE#] zu [#ENDDATE#]</value>
  </data>
  <data name="ReceivedCustomerRMAswithReasons" xml:space="preserve">
    <value>Empfangener Kunde RMAs mit Gründen von [#STARTDATE#] zu [#ENDDATE#]</value>
  </data>
  <data name="ReceivedGoodsValuationbyCountry" xml:space="preserve">
    <value>Empfangene Waren-Schätzung durch Land für [#COUNTRY#] von [#STARTDATE#] zu [#ENDDATE#]</value>
  </data>
  <data name="ShippedGoodsValuationbyCountry" xml:space="preserve">
    <value>Versendete Waren-Schätzung durch Land für [#COUNTRY#] von [#STARTDATE#] zu [#ENDDATE#]</value>
  </data>
  <data name="ShippedOrdersSortedbyInvoiceNumber" xml:space="preserve">
    <value>Versendete Aufträge von [#STARTDATE#] to [#ENDDATE#] Bestellt durch Invoice Nr</value>
  </data>
  <data name="ShippedOrdersSortedbyInvoiceNumberforaCustomer" xml:space="preserve">
    <value>Versendete Aufträge für [#COMPANY_NAME#] von [#STARTDATE#] zu [#ENDDATE#] Bestellt durch Invoice Nr</value>
  </data>
  <data name="ShippedOrdersSortedbyInvoiceNumberforaSalesperson" xml:space="preserve">
    <value>Versendete Aufträge für [#SALESPERSON#] von [#STARTDATE#] zu [#ENDDATE#] Bestellt durch Invoice Nr</value>
  </data>
  <data name="ShippedSalesforLot" xml:space="preserve">
    <value>Versendete Verkäufe für Los [#LOT#] von [#STARTDATE#] zu [#ENDDATE#]</value>
  </data>
  <data name="ShippedSupplierRMAs" xml:space="preserve">
    <value>Versendeter Lieferant RMAs von [#STARTDATE#] zu [#ENDDATE#]</value>
  </data>
  <data name="ShippedSupplierRMAsforaBuyer" xml:space="preserve">
    <value>Versendeter Lieferant RMAs für [#BUYER#] von [#STARTDATE#] zu [#ENDDATE#]</value>
  </data>
  <data name="ShippedSupplierRMAsforaBuyerwithReasons" xml:space="preserve">
    <value>Versendeter Lieferant RMAs für [#BUYER#] von [#STARTDATE#] zu [#ENDDATE#]</value>
  </data>
  <data name="ShippedSupplierRMAsforaSupplier" xml:space="preserve">
    <value>Versendeter Lieferant RMAs für [#COMPANY_NAME#] von [#STARTDATE#] zu [#ENDDATE#]</value>
  </data>
  <data name="ShippedSupplierRMAsforaSupplierwithReasons" xml:space="preserve">
    <value>Versendeter Lieferant RMAs für [#COMPANY_NAME#] von [#STARTDATE#] zu [#ENDDATE#]</value>
  </data>
  <data name="ShippedSupplierRMAswithReasons" xml:space="preserve">
    <value>Versendeter Lieferant RMAs von [#STARTDATE#] zu [#ENDDATE#]</value>
  </data>
  <data name="StockCount" xml:space="preserve">
    <value>Auf Lagerzählimpuls für [#WAREHOUSE#]</value>
  </data>
  <data name="StockList" xml:space="preserve">
    <value>Bestandsliste</value>
  </data>
  <data name="StockValuation" xml:space="preserve">
    <value>Bewertung von Aktien für [#WAREHOUSE#]</value>
  </data>
  <data name="SummaryBookedOrdersbyCustomer" xml:space="preserve">
    <value>Zusammenfassung Angemeldete Aufträge durch Kunde von [#STARTDATE#] zu [#ENDDATE#] - Schließt Verschiffen ein</value>
  </data>
  <data name="SummaryBookedOrdersbyDivision" xml:space="preserve">
    <value>Zusammenfassung Angemeldete Aufträge durch Abteilung von [#STARTDATE#] zu [#ENDDATE#] - Schließt Verschiffen ein</value>
  </data>
  <data name="SummaryBookedOrdersbySalesperson" xml:space="preserve">
    <value>Zusammenfassung Angemeldete Aufträge durch Verkäufer von [#STARTDATE#] zu [#ENDDATE#] - Schließt Verschiffen ein</value>
  </data>
  <data name="SummaryOpenOrdersbyCustomer" xml:space="preserve">
    <value>Zusammenfassungs-geöffnete Aufträge durch Kunde von [#STARTDATE#] zu [#ENDDATE#] - Schließt Verschiffen ein</value>
  </data>
  <data name="SummaryOpenOrdersbyDivision" xml:space="preserve">
    <value>Zusammenfassungs-geöffnete Aufträge durch Abteilung von [#STARTDATE#] zu [#ENDDATE#] - Schließt Verschiffen ein</value>
  </data>
  <data name="SummaryOpenOrdersbySalesperson" xml:space="preserve">
    <value>Zusammenfassungs-geöffnete Aufträge durch Verkäufer von [#STARTDATE#] zu [#ENDDATE#] - Schließt Verschiffen ein</value>
  </data>
  <data name="SummaryShippedSalesbyCustomer" xml:space="preserve">
    <value>Zusammenfassung Versendete Verkäufe durch Kunde von [#STARTDATE#] zu [#ENDDATE#] - Schließt Verschiffen ein [#INCLUDING_CREDITS#]</value>
  </data>
  <data name="SummaryShippedSalesbyDivision" xml:space="preserve">
    <value>Zusammenfassung Versendete Verkäufe durch Abteilung von [#STARTDATE#] zu [#ENDDATE#] - Schließt Verschiffen ein [#INCLUDING_CREDITS#]</value>
  </data>
  <data name="SummaryShippedSalesbySalesperson" xml:space="preserve">
    <value>Zusammenfassung Versendete Verkäufe durch Verkäufer von [#STARTDATE#] zu [#ENDDATE#] - Schließt Verschiffen ein [#INCLUDING_CREDITS#]</value>
  </data>
  <data name="SupplierOnTimeDeliveryReport" xml:space="preserve">
    <value>Lieferant auf Zeit-Anlieferungs-Report von [#STARTDATE#] zu [#ENDDATE#]</value>
  </data>
  <data name="UserList" xml:space="preserve">
    <value>Benutzer-Liste</value>
  </data>
</root>