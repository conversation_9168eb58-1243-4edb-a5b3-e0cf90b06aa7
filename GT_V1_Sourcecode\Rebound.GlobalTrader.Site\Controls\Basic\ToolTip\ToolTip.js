Type.registerNamespace("Rebound.GlobalTrader.Site.Controls");Rebound.GlobalTrader.Site.Controls.ToolTip=function(n){Rebound.GlobalTrader.Site.Controls.ToolTip.initializeBase(this,[n]);this._aryElements=[];this._intElementCount=0;this._blnShown=!1;this._intCurrentItem=null;this._intHideTimeoutID=-1;this._intShowTimeoutID=-1;this._intMillisecondsWaitBeforeShow=1e3;this._intLoadingWidth=16;this._intOffsetRightX=12;this._blnMoveToRight=!1};Rebound.GlobalTrader.Site.Controls.ToolTip.prototype={get_pnlContent:function(){return this._pnlContent},set_pnlContent:function(n){this._pnlContent!==n&&(this._pnlContent=n)},get_pnlShadow:function(){return this._pnlShadow},set_pnlShadow:function(n){this._pnlShadow!==n&&(this._pnlShadow=n)},get_strLoading:function(){return this._strLoading},set_strLoading:function(n){this._strLoading!==n&&(this._strLoading=n)},get_intMillisecondsWaitBeforeShow:function(){return this._intMillisecondsWaitBeforeShow},set_intMillisecondsWaitBeforeShow:function(n){this._intMillisecondsWaitBeforeShow!==n&&(this._intMillisecondsWaitBeforeShow=n)},initialize:function(){$addHandler(this._element,"mouseover",Function.createDelegate(this,this.popupMouseOver));$addHandler(this._element,"mouseout",Function.createDelegate(this,this.mouseOut));Rebound.GlobalTrader.Site.Controls.ToolTip.callBaseMethod(this,"initialize")},dispose:function(){this.isDisposed||(this.get_element()&&$clearHandlers(this._element),this._aryElements&&this.clearAllElements(),this._aryElements=null,this._pnlContent=null,this._pnlShadow=null,this._intElementCount=null,this._blnShown=null,this._intCurrentItem=null,this._intHideTimeoutID=null,this._intShowTimeoutID=null,this._intScreenWidth=null,this._intLoadingWidth=null,this._blnMoveToRight=null,Rebound.GlobalTrader.Site.Controls.ToolTip.callBaseMethod(this,"dispose"),this.isDisposed=!0)},registerElement:function(n,t,i,r,u){if(n)return this.checkElementOKToBeAdded(n)?(u||(u=300),Array.add(this._aryElements,{Element:n,HasBeenShown:!1,Event:i,EventParameters:r,Content:t?t:this._strLoading,Width:u}),n.setAttribute("bui_mouseOverInfoID",this._intElementCount),$addHandler(n,"mouseout",Function.createDelegate(this,this.mouseOut)),$addHandler(n,"mouseover",Function.createDelegate(this,this.mouseOver)),this._intElementCount+=1,this._intElementCount-1):void 0},registerDynamicElement:function(n,t,i,r,u){this.registerElement(n,t,i,r,u);this.mouseOver({target:n})},checkElementOKToBeAdded:function(n){for(var i=!0,t=0,r=this._aryElements.length;t<r;t++)if(this._aryElements[t].Element==n){i=!1;break}return i},show:function(){(this.clearShowTimeout(),this._blnShown)||(this._intShowTimeoutID=setTimeout(Function.createDelegate(this,this.finishShow),this._intMillisecondsWaitBeforeShow))},finishShow:function(){if(this.clearShowTimeout(),!this._blnShown){this._blnShown=!0;this.populate(this._intCurrentItem);$R_FN.showElement(this._element,!0);var n=Function.createDelegate(this,this.getDataComplete);this._aryElements[this._intCurrentItem].HasBeenShown||this._aryElements[this._intCurrentItem].Event&&this._aryElements[this._intCurrentItem].Event(this._intCurrentItem,this._aryElements[this._intCurrentItem].EventParameters,n,n);this._aryElements[this._intCurrentItem].HasBeenShown=!0}},hide:function(){this.clearShowTimeout();$R_FN.showElement(this._element,!1);this._blnShown=!1},getDataComplete:function(n){n&&(n=Sys.Serialization.JavaScriptSerializer.deserialize(n),this.setContent(n.TooltipID,$R_FN.setCleanTextValue(n.Content)))},clearAllElements:function(){if(this._aryElements){for(var n=0,t=this._aryElements.length;n<t;n++)this._aryElements[n]&&$clearHandlers(this._aryElements[n].Element);Array.clear(this._aryElements);this._intElementCount=0}},mouseOver:function(n){(this.clearShowTimeout(),this.clearHideTimeout(),this._blnShown)||(this._pnlShadow.style.height="1px",this._pnlShadow.style.width="1px",this._intScreenWidth=Sys.UI.DomElement.getBounds(document.body).width,this._intCurrentItem=Number.parseInvariant(n.target.getAttribute("bui_mouseOverInfoID").toString()),this.checkMoveToRight(),this.show())},checkMoveToRight:function(){this._blnMoveToRight=!1;var t=this._aryElements[this._intCurrentItem],n=Sys.UI.DomElement.getBounds(t.Element);t.Width+n.x>this._intScreenWidth&&(n.x>this._intScreenWidth/2?(this._blnMoveToRight=!0,t.Width=this._intScreenWidth-(this._intScreenWidth-(n.x+n.width-this._intOffsetRightX))-this._intOffsetRightX):t.Width=this._intScreenWidth-n.x-2*this._intOffsetRightX);this._blnMoveToRight?(Sys.UI.DomElement.removeCssClass(this._element,"ToolTip_Left"),Sys.UI.DomElement.addCssClass(this._element,"ToolTip_Right")):(Sys.UI.DomElement.addCssClass(this._element,"ToolTip_Left"),Sys.UI.DomElement.removeCssClass(this._element,"ToolTip_Right"),this.setPosition(n.x,n.y+n.height));t=null;n=null},mouseOut:function(){this.clearShowTimeout();this.clearHideTimeout();this._intHideTimeoutID=setTimeout(Function.createDelegate(this,this.hide),100)},popupMouseOver:function(){this.clearHideTimeout()},setContent:function(n,t){this._aryElements[n].Content&&this._aryElements[n].Content!=this._strLoading||(this._aryElements[n].Content=t);this.populate(n)},populate:function(n){var i=this._aryElements[n],t=Sys.UI.DomElement.getBounds(i.Element);$R_FN.setInnerHTML(this._pnlContent,i.Content);i.Content&&i.Content!=this._strLoading?(this._pnlContent.style.width=i.Width+"px",this._blnMoveToRight&&this.setPosition(t.x+t.width-i.Width-this._intOffsetRightX,t.y+t.height)):(this._pnlContent.style.width=this._intLoadingWidth+"px",this._blnMoveToRight&&this.setPosition(t.x+t.width-this._intLoadingWidth-this._intOffsetRightX,t.y+t.height));setTimeout(Function.createDelegate(this,this.setShadowDimensions),0);i=null;t=null},setPosition:function(n,t){Sys.UI.DomElement.setLocation(this._element,n,t+6)},setShadowDimensions:function(){var n=Sys.UI.DomElement.getBounds(this._pnlContent);this._pnlShadow.style.height=n.height+"px";this._pnlShadow.style.width=n.width+"px";n=null},clearShowTimeout:function(){this._intShowTimeoutID!=-1&&clearTimeout(this._intShowTimeoutID)},clearHideTimeout:function(){this._intHideTimeoutID!=-1&&clearTimeout(this._intHideTimeoutID)}};Rebound.GlobalTrader.Site.Controls.ToolTip.registerClass("Rebound.GlobalTrader.Site.Controls.ToolTip",Sys.UI.Control,Sys.IDisposable);