/*
Marker     changed by      date         Remarks
[001]      Abhinav       17/11/20011   ESMS Ref:25 & 34  - Virtual Stock Update & Closeing of line CRMA
*/
///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 25.05.2011:
// - allow editing quantity before full receipt
//
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
//  [003]      Suhail          15/05/2018   Added Avoidable on CRMA Line
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.CRMALines_Edit = function(element) {
    Rebound.GlobalTrader.Site.Controls.Forms.CRMALines_Edit.initializeBase(this, [element]);
    this._intCRMAID = 0;
    this._intLineID = -1;
    this._intQuantityReceived = 0;
    this._intQuantityAvailable = 0;
    this._intLineQuantityExists = 0;
    this._intInvoiceLineNo = 0;
    this._intQtyShipped = 0;
    this._intQtyCRMA = 0;
    this._intQtyReceived = 0;
    this._isClosed = false;
    this._blnProductHaza = false;

};

Rebound.GlobalTrader.Site.Controls.Forms.CRMALines_Edit.prototype = {

    get_intCRMAID: function() { return this._intCRMAID; }, set_intCRMAID: function(v) { if (this._intCRMAID !== v) this._intCRMAID = v; },

    initialize: function() {

        Rebound.GlobalTrader.Site.Controls.Forms.CRMALines_Edit.callBaseMethod(this, "initialize");
        this.addShown(Function.createDelegate(this, this.formShown));
    },

    formShown: function() {
        if (this._blnFirstTimeShown) {
            this.addSave(Function.createDelegate(this, this.saveClicked));
        }
        this.getInvoiceLines();

        this.showField("ctlQuantity", !this._isClosed);

        this.showField("ctlQuantity_Lable", this._isClosed);

        this._ctlItemsReason1 = $find(this.getField("ctlItemsReason1").ID);
        this._ctlItemsReason1.addItem();

        this._ctlItemsReason2 = $find(this.getField("ctlItemsReason2").ID);
        this._ctlItemsReason2.addItem();

        this._ctlItemsReason1.hidMenuPanel();
        this._ctlItemsReason2.hidMenuPanel();
        this.enableFieldCheckBox("ctlPrintHazWar", this._blnProductHaza);

    },

    dispose: function() {
        if (this.isDisposed) return;
        this._intCRMAID = null;
        this._intLineID = null;
        this._intQuantityReceived = null;
        this._intQuantityAvailable = null;
        this._intLineQuantityExists = null;
        this._intInvoiceLineNo = null;
        this._intQtyShipped = null;
        this._intQtyCRMA = null;
        this._intQtyReceived = null;
        this._isClosed = null;
        this._blnProductHaza = null;
        Rebound.GlobalTrader.Site.Controls.Forms.CRMALines_Edit.callBaseMethod(this, "dispose");
    },

    setFieldsFromHeader: function(strCRMANumber, strCustomer) {
        this.setFieldValue("ctlCustomerRMA", strCRMANumber);
        this.setFieldValue("ctlCustomer", strCustomer);
    },

    getInvoiceLines: function() {
        // $R_FN.showElement(this._pnlLines, false);
        //   $R_FN.showElement(this._pnlLinesNotAvailable, false);        
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/CRMALines");
        obj.set_DataObject("CRMALines");
        obj.set_DataAction("GetQtyLine");
        obj.addParameter("ID", this._intLineID);
        obj.addParameter("invoiceLineID", this._intInvoiceLineNo);
        obj.addDataOK(Function.createDelegate(this, this.getInvoiceLinesOK));
        obj.addError(Function.createDelegate(this, this.getInvoiceLinesError));
        obj.addTimeout(Function.createDelegate(this, this.getInvoiceLinesError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getInvoiceLinesOK: function(args) {

        var result = args._result;
        var blnFoundLines = false;
        if (result.Lines) {
            $R_FN.showElement(this._pnlLines, true);
            $R_FN.showElement(this._pnlLinesNotAvailable, false);
            var row = result.Lines;
            this._intQuantityAvailable = row.QuantityAvailable;
            this._intQtyShipped = row.QuantityShipped;
            this._intQtyCRMA = row.QuantityCRMA;
            this._intQtyReceived = row.QuantityReceived;

            aryData = null; row = null;
            blnFoundLines = true;

        }
    },

    getInvoiceLinesError: function(args) {
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    saveClicked: function() {
        if (this._isClosed == false)
            if (!this.validateForm()) return;
        if (!this.validateReason()) return;
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/CRMALines");
        obj.set_DataObject("CRMALines");
        obj.set_DataAction("SaveEdit");
        obj.addParameter("id", this._intLineID);
        //obj.addParameter("Reason", "Reason");
        obj.addParameter("LineNotes", this.getFieldValue("ctlLineNotes"));
        obj.addParameter("Quantity", this.getFieldValue("ctlQuantity"));
        obj.addParameter("Reason1", this._ctlItemsReason1.getSubCategory());
        obj.addParameter("Reason2", this._ctlItemsReason2.getSubCategory());
        obj.addParameter("RootCause", this.getFieldValue("ctlRootCause"));
        // [003] code start
        obj.addParameter("Avoidable", this.getFieldValue("ctlIsAvoidable"));
        // [003] code end
        obj.addParameter("PrintHazWar", this.getFieldValue("ctlPrintHazWar"));
        obj.addDataOK(Function.createDelegate(this, this.saveEditComplete));
        obj.addError(Function.createDelegate(this, this.saveEditError));
        obj.addTimeout(Function.createDelegate(this, this.saveEditError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    saveEditError: function(args) {
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    saveEditComplete: function(args) {
        if (args._result.Result == true) {
            this.onSaveComplete();
        } else {
            this._strErrorMessage = args._errorMessage;
            this.onSaveError();
        }
    },

    validateForm: function() {
        this.onValidate();
        var blnOK = this.autoValidateFields();
        if (!this.checkNumericFieldLessThanOrEqualTo("ctlQuantity", this._intQuantityAvailable)) blnOK = false;
        if (!this.checkNumericFieldGreaterThanOrEqualTo("ctlQuantity", Math.max(1, this._intQtyReceived))) blnOK = false;
        if (!blnOK) this.showError(true);
        return blnOK;
    },
    validateReason: function() {
        var blnOK = true;
        if (this._ctlItemsReason1._txtReason.value == "") {
            blnOK = false;
            this.showError(true, $R_RES.ResReason1Value);
        }
        return blnOK;
    }

};

Rebound.GlobalTrader.Site.Controls.Forms.CRMALines_Edit.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CRMALines_Edit", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
