IF OBJECT_ID('usp_count_BOMManager_for_Company', 'P') IS NOT NULL
DROP PROC usp_count_BOMManager_for_Company
GO
/*
-- ==========================================================================================
-- TASK      		UPDATED BY     		DATE         ACTION 			DESCRIPTION                                    
-- US-200952 		Phuc.HoangDinh     	24-04-2024   Create				New SP for [RP-2999] BOM Manager Customer Contact Screen
-- ==========================================================================================
*/
CREATE PROCEDURE [dbo].[usp_count_BOMManager_for_Company]  
    @CompanyId int  
  , @IncludeClosed bit = 0  
AS   
    SELECT  count(*)  
    FROM    dbo.vwBOMManager  
    WHERE   CompanyNo = @CompanyId  
            AND StatusValue <> (CASE WHEN @IncludeClosed = 1 THEN 0 ELSE 7 END)
			AND (select top 1 (ReceivedDate) from tbCustomerRequirement where BOMManagerNo = BOMManagerId) >= DATEADD(MONTH,-24,GETDATE()) 
GO