//----------------------------------------------------------------------------------------
//Marker  Date          Changed By     Remarks
//[001]   26-Sep-2018   <PERSON><PERSON><PERSON>    REB-13083: Change request PO - delivery status
//----------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.HomeNuggets {
    public partial class PODeliveryStatus : Base
    {

        protected SimpleDataTable _tblPODeliveryStatus;

		/// <summary>
		/// On init
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
            this.HomePageNuggetType = "PODeliveryStatus";
			base.OnInit(e);
			WireUpControls();
			SetupTables();
            AddScriptReference("Controls.HomeNuggets.PODeliveryStatus.PODeliveryStatus.js");
		}

		protected override void OnLoad(EventArgs e) {
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.HomeNuggets.PODeliveryStatus", this.ctlDesignBase.ClientID);
            _scScriptControlDescriptor.AddComponentProperty("tblPODeliveryStatus", _tblPODeliveryStatus.ClientID);
			base.OnLoad(e);
		}

		private void SetupTables() {
            _tblPODeliveryStatus.Columns.Add(new SimpleDataColumn("Supplier"));
            _tblPODeliveryStatus.Columns.Add(new SimpleDataColumn("POIPONo",Unit.Pixel(65)));
            _tblPODeliveryStatus.Columns.Add(new SimpleDataColumn("Part", Unit.Pixel(75)));
            _tblPODeliveryStatus.Columns.Add(new SimpleDataColumn("PromiseDate", Unit.Pixel(75)));
		}

		private void WireUpControls() {
            _tblPODeliveryStatus = (SimpleDataTable)FindContentControl("tblPODeliveryStatus");
		}
	}
}