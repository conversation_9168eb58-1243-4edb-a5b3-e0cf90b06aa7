﻿/*   
===========================================================================================  
TASK			UPDATED BY      DATE			ACTION		DESCRIPTION  
[US-204867]		An.TranTan		26-Sep-2024		Backup		Drop table
===========================================================================================  
*/ 
IF TYPE_ID(N'dbo.UploadedProspectiveOffer') IS NOT NULL
BEGIN
	--drop depend SP
	IF OBJECT_ID('dbo.usp_saveProsOffer_tempData', 'P') IS NOT NULL
		DROP PROCEDURE dbo.usp_saveProsOffer_tempData
	DROP TYPE dbo.UploadedProspectiveOffer;
END
GO


