﻿//Marker     Changed by      Date         Remarks
//[0001]     <PERSON>     21/06/2022   /// code for converting dictionary to concurrent dictionary
using System;
using System.Configuration;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml.Linq;
using System.Xml;
using System.Collections.Concurrent;


namespace Rebound.GlobalTrader.Site
{
    public class APIHandler
    {
        //[0001] code start
        public string DataToPost { get; set; }
        public string APIUrl { get; private set; }
        //public Dictionary<string, string> RequestCollection { get; private set; }
        public ConcurrentDictionary<string, string> RequestCollection { get; private set; }
        public IAPIHandler Caller { get; private set; }

        public APIHandler(string parameter)
        {
            this.APIUrl = Convert.ToString(ConfigurationSettings.AppSettings["QlikTicketAPI"]);
            this.DataToPost = parameter;
            this.Caller = new BusinessHandler(this.DataToPost, this.APIUrl);
            this.RequestCollection = this.Caller.GetRequestCollection();
        }
    }
        
    public interface IAPIHandler
    {

        //QlikTicket GetTicket(Dictionary<string, string> req);
        //Dictionary<string, string> GetRequestCollection();
        QlikTicket GetTicket(ConcurrentDictionary<string, string> req);
        ConcurrentDictionary<string, string> GetRequestCollection();
    }
    //[0001] code end


}
