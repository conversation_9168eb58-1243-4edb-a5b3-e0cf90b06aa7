﻿///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />

Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.ItemSearch");
Rebound.GlobalTrader.Site.Controls.ItemSearch.SIDebitNotes = function (element) {
    Rebound.GlobalTrader.Site.Controls.ItemSearch.SIDebitNotes.initializeBase(this, [element]);
    this._CompanyNo = 0;
    this._intCount = 0;
    this._arrDebitNumber = [];
    this._selectedDebits = "";
};

Rebound.GlobalTrader.Site.Controls.ItemSearch.SIDebitNotes.prototype = {
    addPotentialStatusChange: function (handler) { this.get_events().addHandler("PotentialStatusChange", handler); },
    removePotentialStatusChange: function (handler) { this.get_events().removeHandler("PotentialStatusChange", handler); },
    onPotentialStatusChange: function () {
        var handler = this.get_events().getHandler("PotentialStatusChange");
        if (handler) handler(this, Sys.EventArgs.Empty);
    },
    initialize: function () {
        Rebound.GlobalTrader.Site.Controls.ItemSearch.SIDebitNotes.callBaseMethod(this, "initialize");
        this.addSetupData(Function.createDelegate(this, this.doSetupData));
        this.addGetDataComplete(Function.createDelegate(this, this.doGetDataComplete));
    },
    dispose: function () {
        if (this.isDisposed) return;
        this._CompanyNo = null;
        this._arrDebitNumber = null;
        this._selectedDebits = null;
        Rebound.GlobalTrader.Site.Controls.ItemSearch.SIDebitNotes.callBaseMethod(this, "dispose");
    },

    doSetupData: function () {
        this._objData.set_PathToData("controls/ItemSearch/SIDebitNotes");
        this._objData.set_DataObject("SIDebitNotes");
        this._objData.set_DataAction("GetData");
        this._objData.addParameter("CompanyNo",  this._CompanyNo);
        this._objData.addParameter("AddedDebit", this._selectedDebits);
        this._objData.addParameter("PurchaseOrderNumber",  this.getFieldValue("ctlPurchaseOrderNo"));
        this._objData.addParameter("DebitDateFrom",  this.getFieldValue("ctlDebitNoteDateFrom"));
        this._objData.addParameter("DebitDateTo",  this.getFieldValue("ctlDebitNoteDateTo"));
        this._intCount += 1;
    },

    doGetDataComplete: function () {
        Array.clear(this._arrDebitNumber);
        this._tblResults.clearTable();
        for (var i = 0, l = this._objResult.Results.length; i < l; i++) {
            var row = this._objResult.Results[i];
            var aryData = [
                this.writeCheckbox(row.ID, i, row.IsDebitNoteSelected)
                , row.DebitNoteNumber
                , $R_FN.setCleanTextValue(row.DebitAmount)
                , $R_FN.setCleanTextValue(row.Date)
                , row.PurchaseOrderNumber
                , row.IPONumber
                , row.SupplierRMANo
                , $R_FN.setCleanTextValue(row.SupplierNotes)
            ];
            this._tblResults.addRow(aryData, row.ID, false);
            this.registerCheckBox(row.ID, i, row.IsDebitNoteSelected, true);

            var chk = this.getCheckBox(i);
            chk._element.setAttribute("onClick", String.format("$find(\"{0}\").getCheckedCellValue({1});", this._element.id, i));
            if (row.IsDebitNoteSelected) {
                this.getCheckedCellValue(i);
            }
            chk = null; aryData = null; row = null;
        }
        this.onPotentialStatusChange();
        this._tblResults.resizeColumns();
    },

    writeCheckbox: function (varID, intIndex, blnChecked) {
        var strChkID = this.getControlID("chk", intIndex);
        var strChkImageID = this.getControlID("chkImg", intIndex);
        var str = String.format("<div class=\"imageCheckBoxDisabled\" id=\"{0}\" ><img id=\"{1}\" class=\"{2}\" src=\"images/x.gif\" style=\"border-width: 0px;\" /> </div>", strChkID, strChkImageID, (blnChecked) ? "on" : "off");
        return str;
    },

    getControlID: function (str, i) {
        return String.format("{0}_{1}{2}", this._tblResults._element.id, str, i);
    },

    registerCheckBox: function (varID, intIndex, blnChecked, blnEnabled) {
        var strChkID = this.getControlID("chk", intIndex);
        var strChkImageID = this.getControlID("chkImg", intIndex);
        var chk = this.getCheckBox(intIndex);
        if (chk) {
            chk.dispose();
            chk = null;
        }
        eval($R_FN.getCreateStatement("Rebound.GlobalTrader.Site.Controls.ImageCheckBox", [["blnChecked", blnChecked], ["blnEnabled", blnEnabled], ["img", String.format("$get(\"{0}\")", strChkImageID)]], strChkID));
    },

    getCheckBox: function (intCheckBox) {
        return $find(this.getControlID("chk", intCheckBox));
    },

    getCheckedCellValue: function (intIndex) {
        if (!(this._tblResults) || (this._tblResults == "undefined")) return;
        if (!(this._tblResults._tbl) || (this._tblResults._tbl == "undefined")) return;

        var chk = this.getCheckBox(intIndex);
        var tr = this._tblResults._tbl.rows[intIndex];
        if (!tr) return;

        var debitNumber = tr.cells[1].innerHTML;

        if (chk._blnChecked) {
            this.addDebitLine(debitNumber);
        }
        else {
            this.removeDebitLine(debitNumber);
        }
        this._selectedDebits = $R_FN.arrayToSingleString(this._arrDebitNumber, "/");
        this.onPotentialStatusChange();
    },

    addDebitLine: function (debitNumber) {
        Array.add(this._arrDebitNumber, debitNumber);
    },

    removeDebitLine: function (debitNumber) {
        Array.remove(this._arrDebitNumber, debitNumber);
    }
};
Rebound.GlobalTrader.Site.Controls.ItemSearch.SIDebitNotes.registerClass("Rebound.GlobalTrader.Site.Controls.ItemSearch.SIDebitNotes", Rebound.GlobalTrader.Site.Controls.ItemSearch.Base, Sys.IDisposable);
