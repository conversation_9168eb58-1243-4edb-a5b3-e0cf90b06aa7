/* Marker     changed by      date         Remarks
   [001]      V<PERSON><PERSON> kumar     17/11/2011  ESMS Ref:23 - PO No. and Crma No. should also be displayed */
/* [002]      <PERSON><PERSON><PERSON><PERSON>   14/07/2014  ESMS Ref:172 - Issue in Part Searching in whole GT 
   [003]      Ravi            19-09-2023      RP-2338  AS6081 Search/Filter functionality on different pages 
 */


using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using System.Text;
using System.Web.UI;
using System.Collections.Generic;
using Rebound.GlobalTrader.Site.Controls.DropDowns.Data;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site.Enumerations;

namespace Rebound.GlobalTrader.Site.Controls.DataListNuggets.Data {

	public class AllStock : Base {

		private string _strCallType;

		public override void ProcessRequest(HttpContext context) {
			base.ProcessRequest(context);
			switch (Action) {
				case "GetData_All": GetData_All(); break;
				case "GetData_Available": GetData_Available(); break;
				case "GetData_Quarantined": GetData_Quarantined(); break;
			}
		}

		private void GetData_All() {
			_strCallType = "ALL";
            string AS6081 = GetFormValue_String("AS6081"); //[003]
            List<BLL.Stock> lst = BLL.Stock.DataListNugget(
				SessionManager.ClientID
				, GetFormValue_NullableInt("SortIndex", 1)
				, GetFormValue_NullableInt("SortDir", SortColumnDirection.ASC)
				, GetFormValue_NullableInt("PageIndex", 0)
				, GetFormValue_NullableInt("PageSize", 10)
				, null
				//[0002] start code
                //, GetFormValue_StringForPartSearch("Part")
                  , GetFormValue_PartForLikeSearch("Part")
                //[0002] end code
				, GetFormValue_NullableInt("Lot")
				, GetFormValue_NullableInt("PONoLo")
				, GetFormValue_NullableInt("PONoHi")
                , GetFormValue_StringForPartSearch("SupplierPart")
                //, GetFormValue_StringForNameSearch("SupplierName")
                 , GetFormValue_StringForNameSearchDecode("SupplierName")
				, GetFormValue_StringForSearch("Location")
				, GetFormValue_NullableInt("Warehouse")
				, GetFormValue_Boolean("RecentOnly")
				, GetFormValue_NullableInt("CRMANoLo")
				, GetFormValue_NullableInt("CRMANoHi")
				, GetFormValue_Boolean("IncludeZeroStock")
                , GetFormValue_NullableInt("Client")
                , SessionManager.IsPOHub == true ? 1 : 0
                , GetFormValue_Boolean("IsGlobalLogin")
                , GetFormValue_NullableInt("StockNoLo")
                , GetFormValue_NullableInt("StockNoHi")
				, (AS6081 == "1" ? true : (AS6081 == "2" ? false : (bool?)null)) //[003]
                );
			ProcessData(lst);
			lst = null;
			SaveState();
		}

		private void GetData_Available() {
			_strCallType = "AVAILABLE";
            string AS6081 = GetFormValue_String("AS6081"); //[003]
            List<BLL.Stock> lst = BLL.Stock.DataListNugget(
				SessionManager.ClientID
				, GetFormValue_NullableInt("SortIndex", 1)
				, GetFormValue_NullableInt("SortDir", SortColumnDirection.ASC)
				, GetFormValue_NullableInt("PageIndex", 0)
				, GetFormValue_NullableInt("PageSize", 10)
				, false
                //[0002] start code
                //, GetFormValue_StringForPartSearch("Part")
                  , GetFormValue_PartForLikeSearch("Part")
                //[0002] end code
				, GetFormValue_NullableInt("Lot")
				, GetFormValue_NullableInt("PONoLo")
				, GetFormValue_NullableInt("PONoHi")
				, GetFormValue_StringForSearch("SupplierPart")
				, GetFormValue_StringForSearch("SupplierName")
				, GetFormValue_StringForSearch("Location")
				, GetFormValue_NullableInt("Warehouse")
				, GetFormValue_Boolean("RecentOnly")
				, GetFormValue_NullableInt("CRMANoLo")
				, GetFormValue_NullableInt("CRMANoHi")
				, GetFormValue_Boolean("IncludeZeroStock")
                , GetFormValue_NullableInt("Client")
                , SessionManager.IsPOHub == true ? 1 : 0
                , GetFormValue_Boolean("IsGlobalLogin")
                , GetFormValue_NullableInt("StockNoLo")
                , GetFormValue_NullableInt("StockNoHi")
                , (AS6081 == "1" ? true : (AS6081 == "2" ? false : (bool?)null)) //[003]
                );
			ProcessData(lst);
			lst = null;
			SaveState();
		}

		private void GetData_Quarantined() {
			_strCallType = "QUARANTINED";
            string AS6081 = GetFormValue_String("AS6081"); //[003]
            List<BLL.Stock> lst = BLL.Stock.DataListNugget(
				SessionManager.ClientID
				, GetFormValue_NullableInt("SortIndex", 1)
				, GetFormValue_NullableInt("SortDir", SortColumnDirection.ASC)
				, GetFormValue_NullableInt("PageIndex", 0)
				, GetFormValue_NullableInt("PageSize", 10)
				, true
                //[0002] start code
                //, GetFormValue_StringForPartSearch("Part")
                  , GetFormValue_PartForLikeSearch("Part")
                //[0002] end code
				, GetFormValue_NullableInt("Lot")
				, GetFormValue_NullableInt("PONoLo")
				, GetFormValue_NullableInt("PONoHi")
				, GetFormValue_StringForSearch("SupplierPart")
				, GetFormValue_StringForSearch("SupplierName")
				, GetFormValue_StringForSearch("Location")
				, GetFormValue_NullableInt("Warehouse")
				, GetFormValue_Boolean("RecentOnly")
				, GetFormValue_NullableInt("CRMANoLo")
				, GetFormValue_NullableInt("CRMANoHi")
				, GetFormValue_Boolean("IncludeZeroStock")
                , GetFormValue_NullableInt("Client")
                , SessionManager.IsPOHub == true ? 1 : 0
                , GetFormValue_Boolean("IsGlobalLogin")
                , GetFormValue_NullableInt("StockNoLo")
                , GetFormValue_NullableInt("StockNoHi")
                , (AS6081 == "1" ? true : (AS6081 == "2" ? false : (bool?)null)) //[003]
                );
			ProcessData(lst);
			lst = null;
			SaveState();
		}

		/// <summary>
		/// Gets the main data
		/// </summary>
		private void ProcessData(List<BLL.Stock> lst) {
			JsonObject jsn = new JsonObject();
			jsn.AddVariable("TotalRecords", (lst.Count > 0) ? lst[0].RowCnt : 0);
			JsonObject jsnRowsArray = new JsonObject(true);
			foreach (BLL.Stock st in lst) {
				JsonObject jsnRow = new JsonObject();
				jsnRow.AddVariable("ID", st.StockId);
				jsnRow.AddVariable("Part", st.Part);
				jsnRow.AddVariable("ROHS", st.ROHS);
				jsnRow.AddVariable("Available", Functions.FormatNumeric(st.QuantityAvailable));
				jsnRow.AddVariable("Allocated", Functions.FormatNumeric(st.QuantityAllocated));
				jsnRow.AddVariable("OnOrder", Functions.FormatNumeric(st.QuantityOnOrder));
				jsnRow.AddVariable("InStock", Functions.FormatNumeric(st.QuantityInStock));
				jsnRow.AddVariable("Supplier", st.SupplierName);
				jsnRow.AddVariable("SupplierNo", st.SupplierNo);
				jsnRow.AddVariable("Status", Functions.GetGlobalResource("Status", (BLL.StockStatus.List)st.StatusNo));
				jsnRow.AddVariable("Location", st.Location);
				jsnRow.AddVariable("Lot", st.LotName);
				jsnRow.AddVariable("LotNo", st.LotNo);
				jsnRow.AddVariable("Mfr", st.ManufacturerCode);
				jsnRow.AddVariable("MfrNo", st.ManufacturerNo);
				jsnRow.AddVariable("SupplierPart", st.SupplierPart);
				jsnRow.AddVariable("Warehouse", st.WarehouseName);
				jsnRow.AddVariable("WarehouseNo", st.WarehouseNo);
                //[001]Code Start
                jsnRow.AddVariable("PurchaseOrder", st.PurchaseOrderNumber);
                jsnRow.AddVariable("PurchaseOrderNo", st.PurchaseOrderNo);
                jsnRow.AddVariable("CRMA", st.CustomerRMANumber);
                jsnRow.AddVariable("CRMANo", st.CustomerRMANo);
                jsnRow.AddVariable("IsPoHub", SessionManager.IsPOHub);
                jsnRow.AddVariable("ClientName", st.ClientName);
                jsnRow.AddVariable("ClientNo", st.ClientNo);
                //jsnRow.AddVariable("SuppMessage", Functions.ReplaceLineBreaks(st.SupplierMessage));
                //[001]Code End
                jsnRow.AddVariable("AS6081", st.AS6081); //[003]
                jsnRowsArray.AddVariable(jsnRow);
				jsnRow.Dispose(); jsnRow = null;
			}
			jsn.AddVariable("Results", jsnRowsArray);
			OutputResult(jsn);
			jsnRowsArray.Dispose(); jsnRowsArray = null;
			jsn.Dispose(); jsn = null;
		}

		protected override void AddFilterStates() {
			AddExplicitFilterState("CallType", _strCallType);
			AddFilterState("Part");
			AddFilterState("Lot");
			AddFilterState("Warehouse");
			AddFilterState("RecentOnly");
			AddFilterState("IncludeZeroStock");
			AddFilterState("Location");
			AddFilterState("PONo");
			AddFilterState("CRMANo");
			AddFilterState("SupplierName");
			AddFilterState("SupplierPart");
            AddFilterState("StockNo");
			AddFilterState("AS6081"); //[003]
            base.AddFilterStates();
		}

	}
}
