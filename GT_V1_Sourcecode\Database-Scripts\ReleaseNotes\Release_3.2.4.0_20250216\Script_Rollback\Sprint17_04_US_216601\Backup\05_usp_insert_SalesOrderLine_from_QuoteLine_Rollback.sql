﻿
GO

/****** Object:  StoredProcedure [dbo].[usp_insert_SalesOrderLine_from_QuoteLine]    Script Date: 12/11/2024 1:39:51 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].[usp_insert_SalesOrderLine_from_QuoteLine]                                                              
--******************************************************************************************                                                              
--* SK 29.10.2009:                                                              
--* - allow for new column - FullCustomerPart - used for searching                                                            
/*                                                              
Marker     changed by      date         Remarks                                                              
[001]      Vinay           07/02/2014   CR:- Add AS9120 Requirement in GT application                                         
[002]      Suhail           18/04/2018   CR:- Added MSL into SO Line from QuoteLine                                
[003]      Anand           25/08/2020   CR:- Added IHS Column for passing data                                                              
*/                                                             
--******************************************************************************************                                                              
    @QuoteLineId int                                                              
  , @SalesOrderNo int                                                              
  , @DateOrdered datetime                                                              
  , @UpdatedBy int                                                              
  , @SalesOrderLineId int OUTPUT                                                              
                                                           
AS                               
                                    
     declare @ManuFacNo int                          
  declare @ClientNo int                          
  declare @ECCNCode nvarchar(50)= null                        
  select @ManuFacNo=ManufacturerNo,@ClientNo= b.ClientNo from tbQuoteLine a join tbQuote b on a.QuoteNo=b.QuoteId where QuoteLineId=@QuoteLineId                          
  select top 1 @ECCNCode= qline.ECCNCode from  tbQuoteLine qline where QuoteLineId=@QuoteLineId                        
  if exists(select * from tbRestrictedManufacturer where ManufacturerNo=@ManuFacNo and ClientNo=@ClientNo)                          
  begin                          
     set @SalesOrderLineId =0                          
  return                          
  end                                                
                                                              
    BEGIN                                                  
    Declare  @IsIPO BIT                                                          
    DECLARE  @PurchaseQuoteLinNo int                                                   
    Declare  @SourcingResultNo int                                                        
    SELECT @SourcingResultNo = SourcingResultNo from tbQuoteLine where QuoteLineId=@QuoteLineId                                                              
    SELECT @PurchaseQuoteLinNo=PurchaseQuoteLineNo from tbQuoteLine where QuoteLineId=@QuoteLineId                                            
 DECLARE @PODeliveryDate DATETIME                                             
                                                          
    INSERT  INTO tbSalesOrderLine (                                                              
                  SalesOrderNo                                                              
                , FullPart                                                              
                , Part                                                              
                , ManufacturerNo                                                              
                , DateCode                                                              
                , PackageNo                       
                , Quantity                 
                , Price        
                , DatePromised                                                              
           , RequiredDate                                                              
                , Instructions                                                              
                , ProductNo                                          
                , Taxable                                
                , CustomerPart                                                              
         , Posted                                                              
                , ShipASAP                                              
                , Inactive                                                              
     , Closed                                                              
                , ROHS                            
                , ServiceNo                                                              
                , StockNo                                              
                , UpdatedBy                                                              
                , DLUP                                                              
                , FullCustomerPart                                                            
                , QuoteLineNo                                                      
                --[001] code start                                                          
                , ProductSource                                                             
                --[001] code end                                                        
                , Notes                                                        
                ,SourcingResultNo                           
  --[002] code start                                                    
    , MSLLevel                                           
    --[002] code end                                 
 --[003] code start                                      
 --IHS code start from Columne to be insert to sales order line                                      
  --,CountryOfOrigin                                      
 ,CountryOfOriginNo                                      
 ,LifeCycleStage                                      
 ,HTSCode                                      
 ,AveragePrice                                      
 ,Packing                                      
 ,PackagingSize                                  
 ,Descriptions                            
 ,IHSProduct                            
 ,ECCNCode             
 ,AS6081                                
  --[003] code end                                                                         
      )                                                              
                SELECT  @SalesOrderNo                                                              
                      , FullPart                                                              
       , Part                                                              
                      , ManufacturerNo                                                              
                      , DateCode                                      
                      , PackageNo                                                              
                      , Quantity                                                              
                      , Price                                                          
                      , @DateOrdered                                                              
                      , @DateOrdered                                                              
                      , Instructions                                                              
                      , ProductNo                                                              
    , 'Y'           
                      , CustomerPart                            
       , 0                                               
        , 0                                                              
                      , 0                                                              
                      , 0                                                              
                      , ROHS                             
                      , ServiceNo                                                              
                      , StockNo                                                   
                      , @UpdatedBy                                                              
                      , CURRENT_TIMESTAMP                                                              
                      , dbo.ufn_get_fullpart(CustomerPart)                                                          
                      , @QuoteLineId                                                            
                      --[001] code start                                             
                      , ProductSource                                                          
                      --[001] code end                                                        
                    ,[dbo].[ufn_get_spqnew](@SourcingResultNo)                                                     
                    ,@SourcingResultNo                                        
     --[002] code start                                             
     ,MSLLevel                                     
     --[002] code end                                      
  --[003] code start                                
  --IHS code start from Columne to be select to tbQuoteLine                                       
 -- ,CountryOfOrigin                                      
 ,CountryOfOriginNo                                      
 ,LifeCycleStage                                      
 ,HTSCode                                      
 ,AveragePrice                                      
 ,Packing                                      
 ,PackagingSize                                  
 ,Descriptions                            
 ,IHSProduct                               
 --,ECCNCode                                 
 ,isnull(ECCNCode,(select top 1 ihs.ECCNCode from tbIHSparts ihs where  ihs.part=Part))              
  --[003] code end             
 , AS6081                                               
                FROM    tbQuoteLine                                                              
                WHERE   QuoteLineId = @QuoteLineId                                                              
                                                          
  --Close the quote line with 'Sold' status                                                              
        UPDATE  tbQuoteLine                                                              
        SET     Closed = 1, ReasonNo = 1 --sold                                                              
        WHERE   QuoteLineId = @QuoteLineId                                                              
                                                              
    END                                                         
                                                            
    --Reset the tbSalesOrderLine serial no                                                        
    EXEC usp_Update_SalesOrderLine_SerialNo @SalesOrderNo                                                        
                                                              
    SET @SalesOrderLineId = scope_identity()                                             
                                                  
    ----------------------------------------------------UPDATE SALES ORDER LINE START HERE                                                   
IF(@SourcingResultNo IS NOT NULL AND @SourcingResultNo>0)                         
BEGIN                                                       
DECLARE @IsChecked BIT=0                                              
DECLARE @IPOLineOrderNo INT=0                                               
DECLARE @SourcingTable NVARCHAR(20)      
DECLARE @IsBomManager BIT=0      
                                                        
SELECT @SourcingTable = SourcingTable ,@IsBomManager=IsBOMManager, @PODeliveryDate = DeliveryDate FROM tbSourcingResult WHERE SourcingResultId=@SourcingResultNo                                      
Update tbsalesOrderLine Set PODeliveryDate = @PODeliveryDate Where SalesOrderLineId=@SalesOrderLineId                                                            
IF(@SourcingTable='PQ' OR @SourcingTable='OFPH' OR @SourcingTable='EXPH' OR @SourcingTable='HUBSTK'OR @SourcingTable='EPPH'OR @SourcingTable='RLPH' OR @SourcingTable='ALTPART'      
OR @SourcingTable='API-Manual' OR (@SourcingTable='Offers' and @IsBomManager=1) OR @SourcingTable='API-FE' OR @SourcingTable='XMatch / Sourcing' OR @SourcingTable='API Results' -- changes for bommanager      
)            
BEGIN                                              
 update tbSourcingResult set IsSoCreated=1,Closed=1,SourceRef='S' WHERE SourcingResultId=@SourcingResultNo                                            
 IF EXISTS(SELECT * FROM tbSalesOrderLine WHERE SalesOrderNo=@SalesOrderNo AND SourcingResultNo=@SourcingResultNo  AND IsChecked=1)                                              
 BEGIN                                              
  SET @IsChecked=0                                              
  END                                              
  ELSE                   
  BEGIN                            
  SET @IsChecked=1                                              
  END                                              
 Update tbsalesOrderLine Set ISIPO=1,IsChecked=@IsChecked Where SalesOrderLineId=@SalesOrderLineId                                                    
 END                  
END                      
----------------------------------------------------UPADTE  SALESORDERLINE END HERE                           
-------------------------------------Log entery for ECCN Log----------------------------16-09-2022-----RP-30------------------------------                        
if(@ECCNCode is not null)                        
begin                        
  declare @SectionName varchar(50) = 'SOLineECCN'                                  
    declare @SubSectionName varchar(50) = 'ECCN'                                  
    declare @ActionName   varchar(10) = 'Print'                                  
    declare @DocumentNo     int     = @SalesOrderLineId                                  
    declare @Detail     nvarchar(max) = 'Action¦¦' +' SO LINE ADDED WITH THIS ECCN CODE  ( ' + @ECCNCode + ' )'                              
    declare @PrintDocumentLogId  int =NULL                                 
   -----------------------------------------------------------------------                                  
   EXEC [dbo].[usp_insert_PrintEmailLog]                                     
   @SectionName                                   
       , @SubSectionName                                  
       , @ActionName                                     
       , @DocumentNo                                     
       , @Detail                               
       , @UpdatedBy                                    
       , @PrintDocumentLogId                                    
                         
end                    
DECLARE @OGELRequired BIT=0;                        
SELECT @OGELRequired=ISNULL(OGEL_Required,0) FROM tbsalesOrder WHERE SalesOrderId=@SalesOrderNo                    
                    
IF(ISNULL(@OGELRequired,0)=1)                    
BEGIN                    
INSERT INTO tbSO_ExportApprovalStatusOGEL(                    
SalesOrderNo,SalesOrderLineNo,ApprovalStatusId,UpdatedBy,DLUP                    
)VALUES(@SalesOrderNo,@SalesOrderLineId,3,@UpdatedBy,GETDATE())                    
END            
IF((SELECT COUNT(1) FROM tbSalesOrderLine WHERE SalesOrderNo=@SalesOrderNo AND ISNULL(AS6081,0)=1)>0)             
BEGIN            
UPDATE tbSalesOrder SET AS6081=1 WHERE SalesOrderId=@SalesOrderNo            
END   

GO


