///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//Marker     Changed by      Date         Remarks
//[001]      Vinay           04/07/2012   This need to notify the user by email.
//[002]      Shashi Keshar   06/01/2015   This need to notify the user by email.
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.ExportApprovalStatus_RequestApproval = function(element) { 
    Rebound.GlobalTrader.Site.Controls.Forms.ExportApprovalStatus_RequestApproval.initializeBase(this, [element]);
    this._strSONumber = "";
    this._strRequestSubject = "";
    this._strRequestBody = '';
};

Rebound.GlobalTrader.Site.Controls.Forms.ExportApprovalStatus_RequestApproval.prototype = {

	get_strSONumber: function() { return this._strSONumber; }, set_strSONumber: function(v) { if (this._strSONumber !== v)  this._strSONumber = v; }, 
	get_intCompanyID: function() { return this._intCompanyID; }, set_intCompanyID: function(v) { if (this._intCompanyID !== v)  this._intCompanyID = v; }, 
    get_intSalesOrderLineID: function () { return this._intSalesOrderLineID; }, set_intSalesOrderLineID: function (v) { if (this._intSalesOrderLineID !== v) this._intSalesOrderLineID = v; }, 
	get_ibtnSend: function() { return this._ibtnSend; }, set_ibtnSend: function(value) { if (this._ibtnSend !== value) this._ibtnSend = value; }, 
	get_ibtnSend_Footer: function() { return this._ibtnSend_Footer; }, set_ibtnSend_Footer: function(v) { if (this._ibtnSend_Footer !== v) this._ibtnSend_Footer = v; }, 

	initialize: function() {
        Rebound.GlobalTrader.Site.Controls.Forms.ExportApprovalStatus_RequestApproval.callBaseMethod(this, "initialize");
        this.addShown(Function.createDelegate(this, this.formShown));
        $R_IBTN.addClick(this._ibtnSend, Function.createDelegate(this, this.sendApprovalRequest));
        $R_IBTN.addClick(this._ibtnSend_Footer, Function.createDelegate(this, this.sendApprovalRequest));
	},

    formShown: function () {
        this.getExportApprovalData();
		if (this._blnFirstTimeShown) {
            this._ctlRequestApproval = $find(this.getField("ctlRequestApproval").ID);
            this._ctlRequestApproval._ctlRelatedForm = this;
            this._ctlRequestApproval.SetApproverType('Export Approval');
            this._ctlRequestApproval.SetDocID(this._intSalesOrderLineID);
            this._ctlRequestApproval.addNewLoginRecipient(1, 'Abhinav Saxena')
            $("#ctl00_cphMain_ctlExportApprovalStatus_ctlDB_ctl14_frmSendApproval_ctlDB_ctlRequestApproval_ctlTo").hide();
            this.ShowSONotifyer();
            this.addFieldCheckBoxClickEvent("ctlSendMail", Function.createDelegate(this, this.ShowSONotifyer));
        }
        
        
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this._ibtnSend) $R_IBTN.clearHandlers(this._ibtnSend);
		if (this._ibtnSend_Footer) $R_IBTN.clearHandlers(this._ibtnSend_Footer);
        if (this._ctlRequestApproval) this._ctlRequestApproval.dispose();
        this._ctlRequestApproval = null;
		this._strSONumber = null;
		this._intCompanyID = null;
        this._intSalesOrderLineID = null;
		this._ibtnSend = null;
		this._ibtnSend_Footer = null;
        Rebound.GlobalTrader.Site.Controls.Forms.ExportApprovalStatus_RequestApproval.callBaseMethod(this, "dispose");
	},

	getMessageText: function() {
        this.getMessageTextComplete();
	},
	
	getMessageTextComplete: function(strMsg) {
        this._ctlRequestApproval.setValue_Body(this._strRequestBody);
        this._ctlRequestApproval.setValue_Subject(String.format(this._strRequestSubject));
    },
    //-------------------------------------------------------------------//
    getExportApprovalData: function () {

        this.showLoading(true);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/ExportApprovalStatus");
        obj.set_DataObject("ExportApprovalStatus");
        obj.set_DataAction("GetExportApprovalData");
        obj.addParameter("id", this._intSalesOrderLineID);
        obj.addDataOK(Function.createDelegate(this, this.getExportApprovalDataOK));
        obj.addError(Function.createDelegate(this, this.getExportApprovalDataError));
        obj.addTimeout(Function.createDelegate(this, this.getExportApprovalDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getExportApprovalDataError: function (args) {
        this.showInnerContent(true);
        this.showLoading(false);
    },

    getExportApprovalDataOK: function (args) {
        var result = args._result;
        this._strRequestSubject = result.SubJect;

        this._strRequestBody = 'Hi\nBelow are the information for your review';
        this._strRequestBody += '\n\nSales Person: '+ result.SalesmanName;
        this._strRequestBody += '\nSales Order: ' + result.SalesOrderNo;
        this._strRequestBody += '\nSO Line Number: ' + result.SOSerialNo;
        this._strRequestBody += '\nCustomer: ' + result.CustomerName;
        this._strRequestBody += '\nPart Number: ' + result.Part;
        this._strRequestBody += '\nShip From Warehouse (Country): ' + result.ShipFromWarehouse;
        this._strRequestBody += '\nShip From Country (Supplier): ' + result.ShipFromCountry;
        this._strRequestBody += '\nCountry of Origin: ' + result.CountryOfOrigin;
        this._strRequestBody += '\nShip to Customer Name: ' + result.ShipToCustomerName;
        this._strRequestBody += '\nShip to Customer Country: ' + result.ShipToCustomerCountry;
        this._strRequestBody += '\nCommodity Code: ' + result.CommodityCode;
        this._strRequestBody += '\nECCN: ' + (result.ECCN === '' ? 'Blank' : result.ECCN);
        this._strRequestBody += '\nDestination Country: ' + result.DestinationCountry;
        this._strRequestBody += '\nMilitary Use?: ' + result.MilitaryUseName;
        this._strRequestBody += '\nEnd User: ' + result.EndUser;
        this._strRequestBody += '\nPart Application: ' + result.PartApplication;
        this._strRequestBody += '\nIf Export Control is required, has the supplier verified they have the appropriate license: ' + (result.ExportControl === 0 ? 'No' : 'Yes');
        this._strRequestBody += '\nAerospace Use: ' + (result.AerospaceUse === 0 ? 'No' : 'Yes');
        this._strRequestBody += '\nWill the parts be tested and where (Please also advise Company Test House name): ' + result.PartTested;

        this.getMessageText();
        this.showInnerContent(true);
        this.showLoading(false);
    },
    //-------------------------------------------//
    //[001] code start 
    sendApprovalRequest: function () {
		if (!this.validateForm()) return;

        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/ExportApprovalStatus");
        obj.set_DataObject("ExportApprovalStatus");
        obj.set_DataAction("SendExportApprovalRequest");
        obj.addParameter("id", this._intSalesOrderLineID);
        obj.addParameter("Subject", this._ctlRequestApproval.getValue_Subject());
        obj.addParameter("Message", this._ctlRequestApproval.getValue_Body());
        obj.addParameter("ApproverIds", $R_FN.arrayToSingleString(this._ctlRequestApproval._aryRecipientLoginIDs));
        obj.addParameter("IsNotifySales", this.getFieldValue("ctlSendMail"));
        obj.addDataOK(Function.createDelegate(this, this.sendApprovalRequestComplete));
        obj.addError(Function.createDelegate(this, this.sendApprovalRequesError));
        obj.addTimeout(Function.createDelegate(this, this.sendApprovalRequesError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
        this.onSave();
        
    },
	//[001] code end
	validateForm: function() {
        var blnOK = this._ctlRequestApproval.validateFields();
		if (!blnOK) this.showError(true);
		return blnOK;
	},
    sendApprovalRequesError: function (args) {
        this.showSaving(false);
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },
	sendApprovalRequestComplete: function() {
		this.showSavedOK(true);
        this.onSaveComplete();
        setTimeout(function () {
            $("#ctl00_cphMain_ctlDragDropForSOR_ctlDB_imgRefresh").trigger('click');
        }, 1000);

    },
    ShowSONotifyer: function () {
        var blnNotifySo = this.getFieldValue("ctlSendMail");
        if (blnNotifySo) {
            var obj = new Rebound.GlobalTrader.Site.Data();
            obj.set_PathToData("controls/Nuggets/ExportApprovalStatus");
            obj.set_DataObject("ExportApprovalStatus");
            obj.set_DataAction("ShowSONotifyerForExportApproval");
            obj.addParameter("id", this._intSalesOrderLineID);

            obj.addDataOK(Function.createDelegate(this, this.ShowSONotifyerOk));
            obj.addError(Function.createDelegate(this, this.sendApprovalRequesError));
            obj.addTimeout(Function.createDelegate(this, this.sendApprovalRequesError));
            $R_DQ.addToQueue(obj);
            $R_DQ.processQueue();
            obj = null;
        }
        else {
            $("#lblsoNotifyer").html('');
        }
    },
    ShowSONotifyerOk: function (args) {
        var res = args._result;
        $("#lblsoNotifyer").html(res.SoNotifyersName);
        this.enableFieldCheckBox("ctlSendMail", res.IsAllowCheckSoNotify);
    },
    ShowSONotifyerError: function () {

    }
	
};

Rebound.GlobalTrader.Site.Controls.Forms.ExportApprovalStatus_RequestApproval.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.ExportApprovalStatus_RequestApproval", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
