using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using System.Text;
using Rebound.GlobalTrader.Site.Controls;
using Rebound.GlobalTrader.Site;
using Rebound.GlobalTrader.Site.Enumerations;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets {
	public partial class CompanyManufacturers : Base {

		#region Locals

		private FlexiDataTable _tbl;
		private IconButton _ibtnAdd;
		private IconButton _ibtnDelete;
		private IconButton _ibtnView;

		#endregion

		#region Properties

		private int? _intSupplierID = -1;
		public int? SupplierID {
			get { return _intSupplierID; }
			set { _intSupplierID = value; }
		}

		private bool _blnCanAdd = true;
		public bool CanAdd {
			get { return _blnCanAdd; }
			set { _blnCanAdd = value; }
		}

		private bool _blnCanDelete = true;
		public bool CanDelete {
			get { return _blnCanDelete; }
			set { _blnCanDelete = value; }
		}

		private bool _blnCanView = true;
		public bool CanView
		{
			get { return _blnCanView; }
			set { _blnCanView = value; }
		}
		#endregion

		#region Overrides

		/// <summary>
		/// OnInit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
			WireUpControls();
			AddScriptReference("Controls.Nuggets.CompanyManufacturers.CompanyManufacturers.js");
			TitleText = Functions.GetGlobalResource("Nuggets", "CompanyManufacturers");
			_intSupplierID = _objQSManager.CompanyID;
			SetupTable();
		}

		/// <summary>
		/// OnLoad
		/// </summary>
		/// <param name="e"></param>
		protected override void OnLoad(EventArgs e) {
			SetupScriptDescriptors();
			base.OnLoad(e);
		}

		protected override void OnPreRender(EventArgs e) {
			_ibtnAdd.Visible = _blnCanAdd;
			_ibtnDelete.Visible = _blnCanDelete;
            _ibtnView.Visible = _blnCanView;
			base.OnPreRender(e);
		}

		#endregion

		/// <summary>
		/// sets up the list of contacts table
		/// </summary>
		private void SetupTable() {
			_tbl.AllowSelection = true;
			_tbl.AllowMultipleSelection = false;
			_tbl.SortColumnDirection = SortColumnDirection.ASC;
			_tbl.Columns.Add(new FlexiDataColumn("CompanyName", Unit.Empty, true));
			_tbl.Columns.Add(new FlexiDataColumn("Franchise", WidthManager.GetWidth(WidthManager.ColumnWidth.POLineCount), false));
			_tbl.Columns.Add(new FlexiDataColumn("POLineCount", WidthManager.GetWidth(WidthManager.ColumnWidth.POLineCount), false));
            _tbl.Columns.Add(new FlexiDataColumn("SupplierRating", WidthManager.GetWidth(WidthManager.ColumnWidth.StarRating), false));
		}

		/// <summary>
		/// Sets up AJAX script descriptors
		/// </summary>
		private void SetupScriptDescriptors() {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyManufacturers", ctlDesignBase.ClientID);
			_scScriptControlDescriptor.AddProperty("intSupplierID", _intSupplierID);
			_scScriptControlDescriptor.AddComponentProperty("tbl", _tbl.ClientID);
			if (_blnCanAdd) _scScriptControlDescriptor.AddElementProperty("ibtnAdd", _ibtnAdd.ClientID);
			if (_blnCanDelete) _scScriptControlDescriptor.AddElementProperty("ibtnDelete", _ibtnDelete.ClientID);
            if (_blnCanView) _scScriptControlDescriptor.AddElementProperty("ibtnView", _ibtnView.ClientID);
        }

		/// <summary>
		/// Wire up controls from the ascx
		/// </summary>
		private void WireUpControls() {
			_tbl = (FlexiDataTable)ctlDesignBase.FindContentControl("tbl");
			_ibtnAdd = (IconButton)FindIconButton("ibtnAdd");
			_ibtnDelete = (IconButton)FindIconButton("ibtnDelete");
            _ibtnView = (IconButton)FindIconButton("ibtnView");
        }


	}
}
