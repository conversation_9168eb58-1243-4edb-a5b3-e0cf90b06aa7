Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.TeamKpi=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.TeamKpi.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DropDowns.TeamKpi.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.DropDowns.TeamKpi.callBaseMethod(this,"initialize");this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK))},dispose:function(){this.isDisposed||Rebound.GlobalTrader.Site.Controls.DropDowns.TeamKpi.callBaseMethod(this,"dispose")},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/TeamKpi");this._objData.set_DataObject("TeamKpi");this._objData.set_DataAction("GetData")},dataCallOK:function(){var t=this._objData._result,n;if(t.TeamKpis)for(n=0;n<t.TeamKpis.length;n++)this.addOption(t.TeamKpis[n].Name,t.TeamKpis[n].ID)}};Rebound.GlobalTrader.Site.Controls.DropDowns.TeamKpi.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.TeamKpi",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);