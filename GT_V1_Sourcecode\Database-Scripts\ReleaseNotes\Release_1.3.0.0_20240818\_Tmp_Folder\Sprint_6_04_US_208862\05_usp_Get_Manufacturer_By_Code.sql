﻿-- Drop the procedure if it already exists
IF OBJECT_ID('dbo.usp_Get_Manufacturer_By_Code', 'P') IS NOT NULL
BEGIN
    DROP PROCEDURE dbo.usp_Get_Manufacturer_By_Code;
END

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-208862]		Trung Pham			95-Jul-2024		CREATE			Get ManufacturerName By Code
===========================================================================================
*/

CREATE PROCEDURE usp_Get_Manufacturer_By_Code
	@manufacturerCode NVARCHAR(256),
	@mfrNo NVARCHAR(256)
AS
BEGIN
	SELECT DISTINCT ManufacturerName FROM tbManufacturer WHERE ManufacturerCode = @manufacturerCode AND ManufacturerId = @mfrNo
END
GO
