﻿
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


/*  
======================================================================================================================================================
TASK            UPDATED BY       DATE            ACTION      DESCRIPTION    
[US-223401]     Phuc Hoang       20-Feb-2025     CREATE      IPO - Bulk Offer Import tool - Apply fuzzy matching and rationalize data before importing  
======================================================================================================================================================
*/  
CREATE OR ALTER PROC [dbo].[usp_Get_IncorrectVendorToBulkOfferImport] (      
 @ClientNo INT = 0,    
 @ImportFileId INT = 0
)    
AS    
    
BEGIN    
	SELECT offerImport.OfferTempId,
	CASE 
		WHEN ISNULL(offerImport.Vendor, '') = '' THEN '_Blank_'  
		ELSE offerImport.Vendor  
	END AS [Vendor],	 
	offerImport.VendorCount
	FROM (
		SELECT Vendor, MAX(OfferTempId) AS OfferTempId, COUNT(Vendor) AS VendorCount
		FROM [BorisGlobalTraderImports].[dbo].[tbOfferImportByExcelTemp] WITH (NOLOCK)
		WHERE ClientNo = @ClientNo AND HUBOfferImportLargeFileID = @ImportFileId
		GROUP BY Vendor
	) offerImport
	OUTER APPLY (
			SELECT TOP 1 * FROM dbo.tbCompany cmp 
			WHERE cmp.CompanyName = offerImport.Vendor AND cmp.ClientNo = @ClientNo AND cmp.IsSupplier = 1
		) cmp
	WHERE ISNULL(cmp.CompanyName, '') = ''
	ORDER BY OfferTempId 
END 
GO


