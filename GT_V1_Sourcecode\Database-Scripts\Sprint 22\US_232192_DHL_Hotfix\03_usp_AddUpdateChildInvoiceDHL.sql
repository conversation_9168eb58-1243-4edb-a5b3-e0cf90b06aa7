﻿
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/* 
================================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-232192]		Phuc Hoang			20-Feb-2025		UPDATE			[PROD Bug] DHL BBX Shipments
================================================================================================
*/
CREATE OR ALTER PROCEDURE [dbo].[usp_AddUpdateChildInvoiceDHL] (
  @StatementId Int = 0,
  @AWBNo bigint,
  @ParentID varchar(150),
  @ChildID bigint = 0,
  @ChildLabel varchar(500),
  @InvoiceNo bigint = 0,
  @ParentInvoiceAWBDHLId int = 0,
  @PInvoiceNo varchar(500) = null,
  @InvoiceFile varchar(500) = null,
  @InvoiceLabel varchar(500) = null,
  @BBXJson nvarchar(max) = null,
  @CurrencyCode varchar(10),
  @ClientId int = 0,
  @LoginId int = 0,
  @ShipperNo varchar(50) = null,
  @Flag bit = 0,
  @Message varchar(500) OUTPUT,
  @IsValid bit OUTPUT,
  @ReturnId int OUTPUT,
  @Weight Decimal = 0,
  @NumberOfBoxes INT = 0,
  @DimensionalWeight FLOAT = 0,
  @InitialNote NVARCHAR(500) = NULL
)
AS 
--Created By: Manoj Kumar                                           
--Created On: 07 Oct 2022                         
--Purpose: To add/update child invoice on DHL                                            
BEGIN 
  IF @StatementId > 0 
  BEGIN 
    IF EXISTS(SELECT 'X' FROM ChildInvoiceAWBDHL WHERE ParentID = @ParentID) 
    BEGIN
      UPDATE ChildInvoiceAWBDHL
      SET PAWBNo = @AWBNo,
		  ParentInvoiceAWBDHLId = @ParentInvoiceAWBDHLId,
		  InvoiceFile = @InvoiceFile,
		  InvoiceLabel = @InvoiceLabel,
		  WPXJson = @BBXJson,
		  IsActive = 1,
		  Updated = CURRENT_TIMESTAMP
      WHERE ParentID = @ParentID;

      SET @Message = 'Declare statement has been added successfully.'
      SET @IsValid = 1
      SET @ReturnId = @ParentInvoiceAWBDHLId
    END
  END

  ELSE 
  BEGIN 
    IF NOT EXISTS(SELECT 'X' FROM ChildInvoiceAWBDHL WHERE AWBNo = @AWBNo AND IsActive = 1) 
    BEGIN 
      IF EXISTS(SELECT 'X' FROM ChildInvoiceAWBDHL WHERE InvoiceNo = @InvoiceNo AND ClientId = @ClientId AND IsActive = 0 AND ISNULL(SendStatus, 0) = 1) 
      BEGIN
        UPDATE ChildInvoiceAWBDHL
        SET SendStatus = 0
        WHERE InvoiceNo = @InvoiceNo AND SendStatus = 1;

        UPDATE t
        SET t.AirWayBill = NULL,
          t.IsUpsInvoiceExported = 0
        FROM tbInvoice t
          JOIN ChildInvoiceAWBDHL ci ON ci.InvoiceNo = t.InvoiceNumber
          AND t.ClientNo = ci.ClientId
          AND ci.LoginId = @LoginId
        WHERE ci.SendStatus = 1
      END 

      IF ((SELECT TOP 1 ISNULL(IsUpsInvoiceExported, 0) FROM tbInvoice WHERE InvoiceNumber = @InvoiceNo AND ClientNo = @ClientId) = 0) 
      BEGIN 
        IF EXISTS( SELECT 'X' FROM ChildInvoiceAWBDHL WHERE InvoiceNo = @InvoiceNo AND ClientId = @ClientId AND ISNULL(SendStatus, 0) = 1) 
        BEGIN
          UPDATE ChildInvoiceAWBDHL
          SET SendStatus = 0,
            IsActive = 0
          WHERE InvoiceNo = @InvoiceNo AND ClientId = @ClientId AND SendStatus = 1;  
        END

        INSERT INTO ChildInvoiceAWBDHL(
          AWBNo,
          ParentID,
          ChildID,
          ChildLabel,
          InvoiceNo,
          BBXJson,
          ClientId,
          CurrencyCode,
          LoginId,
          shipperno,
          [Weight],
		  NumberOfBoxes,
		  DimensionalWeight,
		  InitialNote
        )
        VALUES(
          @AWBNo,
          @ParentID,
          @ChildID,
          @ChildLabel,
          @InvoiceNo,
          @BBXJson,
          @ClientId,
          @CurrencyCode,
          @LoginId,
          @ShipperNo,
          @Weight,
		  @NumberOfBoxes,
		  @DimensionalWeight,
		  @InitialNote
        );

        UPDATE t
        SET t.AirWayBill = ci.AWBNo, t.IsUpsInvoiceExported = 1
        FROM tbInvoice t
          JOIN ChildInvoiceAWBDHL ci ON ci.InvoiceNo = t.InvoiceNumber
            AND ci.ParentID = @ParentID
            AND t.ClientNo = ci.ClientId
        WHERE t.ClientNo = @ClientId AND ci.SendStatus = 1;

        SET @Message = 'Declare statement has been added successfully.'
        SET @IsValid = 1
        SET @ReturnId = SCOPE_IDENTITY()
      END
    END
    ELSE 
    BEGIN
      SET @Message = 'Already processed.'
      SET @IsValid = 0
      SET @ReturnId = 0
    END
  END --end of ELSE condition
END
GO


