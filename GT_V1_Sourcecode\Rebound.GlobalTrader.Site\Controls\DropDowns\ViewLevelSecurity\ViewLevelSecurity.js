Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.ViewLevelSecurity=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.ViewLevelSecurity.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DropDowns.ViewLevelSecurity.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.DropDowns.ViewLevelSecurity.callBaseMethod(this,"initialize");this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK))},dispose:function(){this.isDisposed||(this._intPOHubClientNo=null,Rebound.GlobalTrader.Site.Controls.DropDowns.ViewLevelSecurity.callBaseMethod(this,"dispose"))},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/ViewLevelSecurity");this._objData.set_DataObject("ViewLevelSecurity");this._objData.set_DataAction("GetData")},dataCallOK:function(){var t=this._objData._result,n;if(t.ViewLevelSecuritys)for(n=0;n<t.ViewLevelSecuritys.length;n++)this.addOption(t.ViewLevelSecuritys[n].Name,t.ViewLevelSecuritys[n].ID)}};Rebound.GlobalTrader.Site.Controls.DropDowns.ViewLevelSecurity.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.ViewLevelSecurity",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);