<%--
 Marker     changed by      date         Remarks
[001]       <PERSON><PERSON>    30/08/2018    Add  <PERSON>erson field
--%>
<%@ Control Language="C#" CodeBehind="BOM.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.DataListNuggets.BOM" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<script src="js/AS6081.js"></script>
<style>
    .boxList .boxHeader {
    height: 60px;
    padding: 0px;
    background-color: #a3e998;
}
.boxList .dlnSelectModeLinks {
    position: relative;
    top: 7px;
    padding-left: 5px;
    padding-top: 7px;
}
.boxList .boxHeaderInner {
    height: 60px;
    background-image: url(../../../App_Themes/Original/images/Nuggets/standard/bg_top.gif);
    background-repeat: repeat-x;
    border-bottom: solid 1px #AAE2A5;
}
</style>

<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" BoxType="List">
    <Links>
    <span style="margin-left:15%;">
    <label style="font-weight: bold;font-size: 11px;color: #009900;">Assignment Type</label>
    <input type="radio" id="checkType1" name="AssignmentType" onchange="showHideAssignPanel()" title="User" value="User" />
    <label for="User" style="color:#009900;">User</label>
    <input type="radio"  id="checkType2" name="AssignmentType" onchange="showHideAssignPanel()" title="Group" value="Group" />
    <label for="Group" style="color:#009900;">Group</label>
    </span>
    </Links>
    <Filters>  
        <ReboundUI_DataListNugget:Filter ID="ctlFilter" runat="server">
          
            <FieldsLeft>
                 <ReboundUI_Form:FormField runat="server">
                    <Field>
                        <asp:RadioButtonList ID="radHeaderDetail" name="radioList" runat="server" RepeatDirection="Horizontal">
                            <asp:ListItem Text="Header" Value="Header" Selected="True" />
                            <asp:ListItem Text="Detail" Value="Detail" />
                        </asp:RadioButtonList>
                    </Field>
                </ReboundUI_Form:FormField>
                <ReboundUI_FilterDataItemRow:TextBox ID="ctlCode" runat="server" ResourceTitle="Code"
                    FilterField="Code" />
                                <ReboundUI_FilterDataItemRow:DropDown ID="ctlStatus" runat="server" DropDownType="BomStatus"
                    ResourceTitle="Status" FilterField="BomStatus" DropDownAssembly="Rebound.GlobalTrader.Site" />
                    <ReboundUI_FilterDataItemRow:DropDown id="ctlSalesperson" runat="server" DropDownAssembly="Rebound.GlobalTrader.Site" DropDownType="PoHubBuyer" ResourceTitle="AssignedUser" FilterField="PoHubBuyer" />
            </FieldsLeft>
            <FieldsRight>
                <ReboundUI_FilterDataItemRow:TextBox ID="ctlName" runat="server" ResourceTitle="Name"
                    FilterField="Name" />
                <ReboundUI_FilterDataItemRow:DropDown ID="ctlClient" runat="server"    DropDownType="Client"
                    ResourceTitle="Client" FilterField="Client" DropDownAssembly="Rebound.GlobalTrader.Site" />
                <%--start [001] --%>
                <ReboundUI_FilterDataItemRow:DropDown id="ctlClientSalesperson" runat="server"  DropDownAssembly="Rebound.GlobalTrader.Site" 
                    DropDownType="Employee" ResourceTitle="SalesPerson" FilterField="SalesPerson"   />
                      <%--end [001] --%>
            </FieldsRight>
        </ReboundUI_DataListNugget:Filter>
       
    </Filters>
     <Forms>
		<ReboundForm:BOM_Confirm ID="ctlConfirm" runat="server" />
	</Forms>
</ReboundUI_Nugget:DesignBase>
