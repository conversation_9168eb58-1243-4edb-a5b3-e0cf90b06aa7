///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//
// RP 06.10.2009:
// - Changes due to changes in base class
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");

Rebound.GlobalTrader.Site.Controls.DropDowns.LinkMultiCurrency = function(element) { 
	Rebound.GlobalTrader.Site.Controls.DropDowns.LinkMultiCurrency.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.DropDowns.LinkMultiCurrency.prototype = {
    get_intCustomerClientNo: function () { return this._intCustomerClientNo; }, set_intCustomerClientNo: function (v) { if (this._intCustomerClientNo !== v) this._intCustomerClientNo = v; },
    get_intBuyCurrencyNo: function () { return this._intBuyCurrencyNo; }, set_intBuyCurrencyNo: function (v) { if (this._intBuyCurrencyNo !== v) this._intBuyCurrencyNo = v; },

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.DropDowns.LinkMultiCurrency.callBaseMethod(this, "initialize");
		this.addSetupDataCall(Function.createDelegate(this, this.setupDataCall));
		this.addGetDataOK(Function.createDelegate(this, this.dataCallOK));
	},
	
	dispose: function() { 
	    if (this.isDisposed) return;
	    this._intGlobalCurrencyNo = null;
	    this._intBuyCurrencyNo = null;
		Rebound.GlobalTrader.Site.Controls.DropDowns.LinkMultiCurrency.callBaseMethod(this, "dispose");
	},
	
	setupDataCall: function() { 
		this._objData.set_PathToData("controls/DropDowns/LinkMultiCurrency");
		this._objData.set_DataObject("LinkMultiCurrency");
		this._objData.set_DataAction("GetData");
		this._objData.addParameter("CustClientNo", this._intCustomerClientNo);
		this._objData.addParameter("BuyCurrencyNo", this._intBuyCurrencyNo);
	},
	
	dataCallOK: function() { 
		var result = this._objData._result;
        if (result != null) {
            if (result.Currencies) {
                for (var i = 0; i < result.Currencies.length; i++) {
                    this.addOption(result.Currencies[i].Name, result.Currencies[i].ID, result.Currencies[i].LinkID);
                }
            }

        }
	}
	
};

Rebound.GlobalTrader.Site.Controls.DropDowns.LinkMultiCurrency.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.LinkMultiCurrency", Rebound.GlobalTrader.Site.Controls.DropDowns.Base);
