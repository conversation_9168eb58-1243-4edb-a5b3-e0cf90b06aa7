<%@ Control Language="C#" CodeBehind="PowerBiSalesDashboard.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.HomeNuggets.PowerBiSalesDashboard" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" BoxType="AllWhite">
    <Content>
        <div class="homepageNugget">
            <asp:Panel ID="pnlSalesDashboard" runat="server">
                <%--<ReboundUI:SimpleDataTable ID="tblSalesDashboard" runat="server"  />--%>
                <a id="showSalesDashboard" href="javascript:void(0);" style="color: blue">Click here for Sales Dashboard</a>
                <%--<a id="showSalesDashboard1" href="https://app.powerbi.com/reportEmbed?reportId=61f839d2-636f-44a5-b90d-75e658193b70&autoAuth=true&ctid=3757b56d-5c28-440a-a26d-d6ba9c12a657" target="_blank" style="color: blue">Click here for Sales Dashboard</a>--%>


            </asp:Panel>
            <%--  <asp:Panel ID="pnlMore" runat="server" CssClass="homeNuggetMoreLink">
				<ReboundUI:PageHyperLink id="lnkMore" runat="server" OverrideTextResource="OpenPowerBISales" CssClass="nubButton nubButtonAlignLeft" />
			</asp:Panel>--%>
        </div>
    </Content>

</ReboundUI_Nugget:DesignBase>
