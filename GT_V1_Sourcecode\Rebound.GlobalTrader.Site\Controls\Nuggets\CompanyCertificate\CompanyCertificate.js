Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyCertificate=function(n){Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyCertificate.initializeBase(this,[n]);this._intCompanyID=-1;this._blnLineLoaded=!1;this._inactive=!1};Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyCertificate.prototype={get_intCompanyID:function(){return this._intCompanyID},set_intCompanyID:function(n){this._intCompanyID!==n&&(this._intCompanyID=n)},get_ibtnAdd:function(){return this._ibtnAdd},set_ibtnAdd:function(n){this._ibtnAdd!==n&&(this._ibtnAdd=n)},get_ibtnEdit:function(){return this._ibtnEdit},set_ibtnEdit:function(n){this._ibtnEdit!==n&&(this._ibtnEdit=n)},get_tbl:function(){return this._tbl},set_tbl:function(n){this._tbl!==n&&(this._tbl=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyCertificate.callBaseMethod(this,"initialize");this._strDataPath="controls/Nuggets/CompanyCertificate";this._strDataObject="CompanyCertificate";this.addRefreshEvent(Function.createDelegate(this,this.getData));this._tbl.addSelectedIndexChanged(Function.createDelegate(this,this.tbl_SelectedIndexChanged));this._ibtnAdd&&($R_IBTN.addClick(this._ibtnAdd,Function.createDelegate(this,this.showAddForm)),this._frmAdd=$find(this._aryFormIDs[0]),this._frmAdd.addCancel(Function.createDelegate(this,this.hideAddForm)),this._frmAdd.addSaveComplete(Function.createDelegate(this,this.saveAddComplete)));this._ibtnEdit&&($R_IBTN.addClick(this._ibtnEdit,Function.createDelegate(this,this.showEditForm)),this._frmEdit=$find(this._aryFormIDs[1]),this._frmEdit.addCancel(Function.createDelegate(this,this.hideEditForm)),this._frmEdit.addSaveComplete(Function.createDelegate(this,this.saveEditComplete)));this.getCompanyInactive();this.getData()},dispose:function(){this.isDisposed||(this._ibtnAdd&&$R_IBTN.clearHandlers(this._ibtnAdd),this._ibtnEdit&&$R_IBTN.clearHandlers(this._ibtnEdit),this._ibtnAdd&&$R_IBTN.clearHandlers(this._ibtnAdd),this._frmAdd&&this._frmAdd.dispose(),this._frmEdit&&this._frmEdit.dispose(),this._tbl&&this._tbl.dispose(),this._frmAdd=null,this._frmEdit=null,this._intCompanyID=null,this._blnLineLoaded=null,this._inactive=null,Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyCertificate.callBaseMethod(this,"dispose"))},enableEditButtons:function(n){n?(this._ibtnAdd&&$R_IBTN.enableButton(this._ibtnAdd,!this._inactive),this._ibtnEdit&&$R_IBTN.enableButton(this._ibtnEdit,!this._inactive&&this._blnLineLoaded)):this._ibtnEdit&&$R_IBTN.enableButton(this._ibtnEdit,!1)},tbl_SelectedIndexChanged:function(){this._intLineID=this._tbl._varSelectedValue;this._intLineID&&(this._blnLineLoaded=!0);this.enableEditButtons(!0)},getData:function(){this._blnLineLoaded=!1;$R_FN.showElement(this._pnlLineDetail,!1);this.enableEditButtons(!1);this.showLoading(!0);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strDataPath);n.set_DataObject(this._strDataObject);n.set_DataAction("GetCertificates");n.addParameter("CompanyID",this._intCompanyID);n.addDataOK(Function.createDelegate(this,this.getDataOK));n.addError(Function.createDelegate(this,this.getDataError));n.addTimeout(Function.createDelegate(this,this.getDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getDataOK:function(n){var f=!1,i,t;imagepath="app_themes/original/images/IconButton/pdficon.jpg";var r=this._inactive?'style="pointer-events: none; opacity: 0.5;"':"",o=this._inactive?'style="text-decoration:none; pointer-events: none; opacity: 0.5;"':'style="text-decoration:none"',u=n._result;if(this.showLoading(!1),this._tbl.clearTable(),u.Lines)for(i=0;i<u.Lines.length;i++){t=u.Lines[i];f=t.isCIPPDFAvailable;var s=[$R_FN.setCleanTextValue(t.CeriticateName),$R_FN.setCleanTextValue(t.CategoryName),$R_FN.setCleanTextValue(t.CertificateNum),$R_FN.setCleanTextValue(t.StartDate),$R_FN.setCleanTextValue(t.ExpiryDate),$R_FN.setCleanTextValue(f==!0?String.format('&nbsp;&nbsp;<center><a href="javascript:void(0);" onclick="$RGT_openCIPPDFDoc({0},{1})"'+r+"title=\"Click to View and add docs\"><img border='0'"+r+" src="+imagepath+" width='30' height='26'><\/center><\/a>",t.ID,this._intCompanyID):String.format('&nbsp;&nbsp;<a href="javascript:void(0);" onclick="$RGT_openCIPPDFDoc({0},{1})"'+o+" title=\"Click to add docs\"><center><b><img border='0'"+r+"src='app_themes/Original/images/buttons/sourcing/history_add.gif'><\/b><\/center><\/a>",t.ID,this._intCompanyID))],h={Inactive:t.Inactive,CategoryID:t.CategoryNo,Desc:$R_FN.setCleanTextValue(t.Desc),CertificateID:t.CertificateID},e=t.Inactive?"ceased":"";this._tbl.addRow(s,t.ID,t.ID==this._intLineID,h,e);t=null;e=null}this._intLineCount=this._tbl.countRows();this.showContent(!0);this.showContentLoading(!1);this._tbl.resizeColumns();this._intLineID>0&&this.tbl_SelectedIndexChanged();this.enableEditButtons(!0)},getDataError:function(n){this.showError(!0,n.get_ErrorMessage())},getCompanyInactive:function(){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strDataPath);n.set_DataObject(this._strDataObject);n.set_DataAction("GetCompanyDetailInactive");n.addParameter("id",this._intCompanyID);n.addDataOK(Function.createDelegate(this,this.getCompanyInactiveOK));n.addError(Function.createDelegate(this,this.getCompanyInactiveError));n.addTimeout(Function.createDelegate(this,this.getCompanyInactiveError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getCompanyInactiveOK:function(n){var t=n._result;this._inactive=t.Inactive;this._ibtnEdit&&$R_IBTN.enableButton(this._ibtnAdd,!this._inactive);this._ibtnEdit&&$R_IBTN.enableButton(this._ibtnEdit,!this._inactive)},getCompanyInactiveError:function(n){this.showError(!0,n.get_ErrorMessage())},showEditForm:function(){this._frmEdit.getFieldDropDownData("ctlCategory");this._frmEdit.getFieldComponent("ctlCertificate")._intCategoryID=this._tbl.getSelectedExtraData().CategoryID;this._frmEdit.getFieldDropDownData("ctlCertificate");this._frmEdit._intCompanyID=this._intCompanyID;this._frmEdit._intLineID=this._intLineID;this._frmEdit.setFieldValue("ctlCategory",this._tbl.getSelectedExtraData().CategoryID);this._frmEdit.setFieldValue("ctlCertificate",this._tbl.getSelectedExtraData().CertificateID);this._frmEdit.setFieldValue("ctlCertificateNumbre",this._tbl.getSelectedCellValue(2));this._frmEdit.setFieldValue("ctlStartDate",this._tbl.getSelectedCellValue(3));this._frmEdit.setFieldValue("ctlExpiryDate",this._tbl.getSelectedCellValue(4));this._frmEdit.setFieldValue("ctlInActive",this._tbl.getSelectedExtraData().Inactive);this._frmEdit.setFieldValue("ctlDescription",this._tbl.getSelectedExtraData().Desc);this.showForm(this._frmEdit,!0)},hideEditForm:function(){this.showForm(this._frmEdit,!1)},saveEditComplete:function(){this.hideEditForm();this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully);this.getData()},showAddForm:function(){this._frmAdd._intCompanyID=this._intCompanyID;this.showForm(this._frmAdd,!0)},hideAddForm:function(){this.showForm(this._frmAdd,!1)},saveAddComplete:function(){this.hideAddForm();this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully);this._intLineID=this._frmAdd._intNewID;this.getData()}};Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyCertificate.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyCertificate",Rebound.GlobalTrader.Site.Controls.Nuggets.Base);