<%@ Control Language="C#" CodeBehind="SupplierImport_Import.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.SupplierImport_Import" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<%@ Register TagPrefix="cc1" Namespace="Rebound.GlobalTrader.Site" Assembly="Rebound.GlobalTrader.Site" %>
<link href="css/uploadfile.css" rel="stylesheet">
<script src="js/jquery.uploadfile.js" ></script>
<script src="js/jquery.dataTables.min.js" ></script>
<script src="js/SupplierImport.js"></script>
<link href="css/jquery-ui.css.css" rel="stylesheet" />
<script src="js/jquery-ui.js"></script>
<%--<script src="css/jquery-ui.js" integrity="sha256-xI/qyl9vpwWFOXz7+x/9WkG5j/SVnSw21viy8fWwbeE=" crossorigin="anonymous"></script>--%>

<style>
/*table.dataTable tr th {
    background-color: #424241 !important;
    color: #fff;
    border-bottom: solid 1px #515050 !important;
}*/
.boxForms table.dataTable td {
    color: #000;
    font-weight: normal;
    white-space: nowrap;
}
table.formRows td.title{color:#fff!important;}
div.topMenu{z-index:10000;}
/*.mainArea_Off .mainRightInner{width: 1045px;
margin: 0 auto;}*/
/*.mainArea_On .mainRightInner{width:1045px;}*/
.spanBorder{margin: 0px 0 0 3px;color: #c1cbce;}

      fieldset {
        font-size: 12px;
        padding: 5px;
       /* width: 100%;*/
        line-height: 1.8;
    }

    .dataTables_wrapper .dataTables_paginate .paginate_button {
        width: 88%;
        text-align: right;
        height: 60px;
        border-radius: 10%;
        background-color: #C0C0C0;
        vertical-align: central;
        color: black;
        margin: 0 5px;
        border: 0 !important;
        line-height: 17px;
        box-shadow: none !important;padding: 2px 5px;
    }

    /** {
        box-sizing: border-box;
    }*/
        .ajax-file-upload {
        padding: 7px 25px !important;
        width: auto !important;
        background-color: #158684 !important;
        color: #fff !important;
        margin: 0 !important;
    }

    .ajax-file-upload {
        height: 24px!important;
    }

   

    #tableNew_length, #tableNew_info {
        padding: 10px;
        background: #3a6c34;
        color: #fff;
    }

    #tableNew_paginate {
        margin-top: 5px;
    }

        #tableNew_paginate a {
            padding: 5px;
            margin: 5px;
            background: #3a6c34;
            color: #fff;
        }

    

   

    #divhistory {
        border: 1px solid #3a6c34;
        overflow: scroll;
        width: 96%;
        height: 300px;
        display: block;
        margin-top: 36px;
        padding: 2px;
    }

    #tbshowhistory_wrapper .view-filter {
        background: #3a6c34;
        padding: 5px;
        width: 103%;
    }
		.container-greenbase {
			background-color: #56954e;
			color: #fff;
			font-size: 11px;
			font-family: tahoma;
			padding: 10px;
		}

		fieldset {
			border: 1px #6cab63 solid;
			margin-bottom: 10px;
		}

		legend {
			display: block;
			padding-left: 1px;
			padding-right: 5px;
			color: #fff;
			font-family: Tahoma;
		}
		table td{vertical-align: top;}

		/*table td.firstcol {
			width: 85%;
		}*/

		.radio-option {
			margin-bottom: 10px;
		}

		select {
			min-width: 133px;
			margin-right: 20px;
			border: 0px #fff solid;
			font-size: 11px;
			padding: 2px;
		}

	

		.space {
			padding: 10px;
		}

		.btn {
			border: none;
			padding: 5px 8px;
			border-radius: 5px;
			font-size: 11px;
			min-width: 87px;
		}

		.show {
			background-color: #009491;
			color: #fff;
			margin-right: 10px;
			border: 1px #009491 solid;padding: 6px!important;
		}

		.reset {
			background-color: #85d279;
			color: #2c6e23;
			border: 1px #2c6e23 solid;
		}

		.exlsfile {
			width: 100%;
			background: #d5d5d5;
			/*height: 110px;*/
			margin-top: 5px;
		}

		.exlsfile .header {
			background: #434343;
			padding: 5px 10px;
		}

		.clientblock .row {
			float: left;
			width: 100%;
			padding: 5px 0;
		}

		.clientblock .row .label {
			display: block;
			float: left;
			width: 13%;
		}

		.row .label {
			display: block;
			float: left;
			width: 4%;
		}

		.clientblock .row .radio-option {
			float: left;
		}

		.radio-option select {
			min-width: 240px;
			margin-right: 20px;
		}

		input[type='radio'] {
			vertical-align: bottom;
			margin: 0 5px 0 5px;
		}

		.gfg {
			font-size: 40px;
			color: green;
			font-weight: bold;
		}

		/*.col2 {
			width: 48%;
			margin: 0% 1%;
			float: left;
		}*/

		.file {
			position: relative;
			display: inline-block;
			cursor: pointer;
			margin-right: 105px;
		}

		.file input {
			min-width: 14rem;
			margin: 0;
			filter: alpha(opacity=0);
			opacity: 0;
		}

		.file-custom {
			position: absolute;
			top: 0;
			right: 0;
			left: 0;
			z-index: 5;
			line-height: 1.5;
			color: #555;
			background-color: #fff;
			border: .075rem solid #ddd;
		}

		.file-custom::before {
			position: absolute;
			top: -.075rem;
			right: -2.075rem;
			bottom: -.075rem;
			z-index: 6;
			display: block;
			content: "";
			padding: .1rem 0.9rem;
			line-height: 1.5;
			background-color: #85ce7a;
			border: 1px #93c58b solid;
		}

		.file-custom::after {
			content: "Choose file...";
		}

		.colright {
			float: right;
		}
    
		.btn.right {
			background: #cacaca;
color: #000;
padding: 6px 20px;
border: 1px #427f3a solid;
width: 100%;
white-space: normal;
float: right;
font-weight: bold !important;
		}
		#btnGetMappingSupp{margin-top: 9px;margin-bottom: 6px;}
		.btnfull{width: 100%!important;}
		.three-col td{width: 31%;text-align: right;}
table.formRows input {
    
    vertical-align: middle;
}

.lableNotes{
    color: white;
    font-style:normal;
    font-weight:bold;
}

.row *{box-sizing:border-box !important;}

	</style>
<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="true" ShowQuickHelp="false">

	<Links>
		<%--<ReboundUI:IconButton ID="ibtnSend" runat="server"  style="display:none" IconGroup="Nugget" IconTitleResource="Send"  IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />--%>
		<ReboundUI:IconButton ID="ibtnCancel" runat="server" IconGroup="Nugget" IconTitleResource="Cancel" IconCSSType="Cancel" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
	</Links>
     <%--<Explanation><%=Functions.GetGlobalResource("FormExplanations", "UtilityOffer_Import")%></Explanation>--%>
	<Content>
		
 <ReboundUI_Table:Form ID="frm" runat="server">
           <asp:TableRow>
                <asp:TableCell class="title" RowSpan="2" Style="width: 100%">
              <div class="container-greenbase">
		<!--Main client details-->
		<div class="clientblock">
			<div class="row">
				<%--<div class="label">Client Type</div>
				<div class="radio-option"><label>  <input type="radio" name="clientType" value="1" checked="checked">HUB</label><label>
                    <input type="radio" name="clientType" value="2">Clients</label></div>--%>
			</div>
			<div class="row">
				<%--<div class="label">Client</div>--%>
				<div class="radio-option">
                   <%-- <select id="ddlClient">
						
					</select><input class="btn show" type="button" style="display:none;"  id="btnHistory" value="Show History">
                    <input class="btn show" type="button"  id="btnHistory2" value="Import History">--%>
                    <input class="btn reset"type="button"  id="btnResetSupp" value="Reset"></div>
            
			</div>

		</div>
        <div class="LoaderPopup" id="divLoader">
        <div>
            <div class="cssload-loader">Uploading..</div>

        </div>
    </div>
		<!--client details end-->

		<!--csv data-->
		<fieldset class="space">
			<legend>CSV Data</legend>
			<div class="row"><label for="file contains"><input type="checkbox"  name="FileCCHSupp" value="FileCCHSupp" checked="checked" id="chkFileCCHSupp">File contains column header</label></div><br>
			<div class="row" style="margin-bottom:10px;">
				<div class="label">Files</div>
				<label class="file">
					
          <div class="lableopt" style="width: 250px;">

            <div id="ImagesingleuploadNew">Upload</div>

        </div>
					
				</label>

				<label style="display:none;">Format : </label>&nbsp;<select  id="ddlFormatSupp" style="width:200px; display:none;">
                <option value="CSVDelimited">CSV delimited</option>
               <%-- <option value="TabDelimited">Tab delimited</option>
                <option value="TabDelimited">Custom delimited</option>
                <option value="dBase">dBase</option>
                <option value="Text">Text</option>--%>
                <option value="Excel">Excel</option>
				</select>
				<div class="colright">
					<input type="button" class="btn right btnfull"  id="btnDisplayCsvDataSupp" value="Display Raw CSV Data">
                     
				</div>
                 <div class="LoaderPopup" id="divLoadershowdata">
                                <div>
                                    <div class="cssload-loader">Loading..</div>
                                </div>
                            </div>
                 </div>
				 
             <div class="row" style="">
			 <label class="file" style="">
			 <div class="lableopt" style="width: 283px;"></div>
			 </label>
			 <label style="display:none;">Delimiter : </label>&nbsp;<input type="text" style="width:25px; display:none" name="txtdelimiterSupp" id="txtdelimiterSupp" />
			 </div>
			
             <div class="lableNotes" style="width: 315px;">
               Note: Maximum limit should not exceed <%= ConfigurationManager.AppSettings["MaxUploadRowCount"]%> rows. 
             </div>     
             <asp:TableRow ID="tbhistory">
            <asp:TableCell ColumnSpan="4">
              <div class="exlsfile">
				<div class="header">History</div>
          <div id="divhistory" class="display dataTables_wrapper dataTables_paginate paginate_button" style"width:860px;overflow: scroll; border: solid 1px; overflow-x: scroll; overflow-y: scroll;  height: 120px; border-style: ridge;" class="display .dataTables_wrapper .dataTables_paginate .paginate_button overflow: auto">
                     <div id="myModal" class="modal" style="height: 700px;">
                        <!-- Modal content -->
                        <div class="modal-content" style="height: 430px; 0px 0px 15px 0px #000; width: 1075px;">
                            <div class="LoaderPopup" id="divLoaderhistory">
                                <div>
                                    <div class="cssload-loader">Loading..</div>
                                </div>
                            </div>
                            <div style="float: right;margin-bottom: 0px;margin-top: -20px; margin-right: -11px;">
							<input style="background: transparent;font-size: 22px;color: #3a6c34;" class="PartDetailsGridGoBtn2 close"type="button" id="btnClose" value="X">
							</div>
                             <div  class="display dataTables_wrapper dataTables_paginate paginate_button" style="width:100%;overflow: scroll; border: solid 1px; overflow-x: scroll; overflow-y: scroll;  height: 350px; border-style: ridge;" class="display dataTables_wrapper dataTables_paginate paginate_button">
                    <table id="tbshowhistory" class="table table-striped table-bordered nowrap " cellspacing="0" cellpadding="0" border="0" style="width: 100%; table-layout: auto; border-style: None; border-collapse: collapse;"></table>
                </div>
                  </div>
                    </div>
                   </div>
                    </div>
                </asp:TableCell>
        </asp:TableRow>
        <script type="text/javascript">
            $(document).ready(function () {
                $(function () {

                    var chkhead = "YES"
                    var Client_type = "";
                    formControlId = "<%=this.ClientID%>";
                    loginId = "<%=SessionManager.LoginID%>";
                    var dragdropObj = $("#ImagesingleuploadNew").uploadFile({
                        url: "DocImage.ashx?mxs=1&type=EXCELUPLOAD&IsDragDrop=true",
                        allowedTypes: "csv,xlsx",
                        fileName: "myfile",
                        section: "StockImport_StockImport_STKI",
                        autoSubmit: true,
                        multiple: false,
                        //maxFileSize: 79000000,
                        maxFileSize: 5253366,
                        showStatusAfterSuccess: false,
                        showCancel: true,
                        showDone: true,
                        async: false,
                        uploadDiv: "exceliploadNew",
                        timeout: 6000000,
                        dynamicFormData: function () {
                            var data = { section: "StockImport_StockImport_STKI" }
                            return data;
                        },
                        onSuccess: function (files, data, xhr) {
                            debugger;
                            var originalFilename = '';
                            var generatedFilename = '';
                            originalFilename = files[0];
                            var json = Sys.Serialization.JavaScriptSerializer.deserialize(data);
                            generatedFilename = json.FileName;
                            clientid = $('#ctl00_ddlClientByMaster_ddl').val();
                            chkhead = ($("#chkFileCCHSupp").is(':checked')) ? "YES" : "NO";
                            Client_type = $("input[name='clientType']:checked").val();
                            debugger;
                            $find("<%=this.ClientID%>").impotExcelData(originalFilename, generatedFilename, clientid, chkhead, Client_type);

                        },
                        onSelect: function (fup) {
                            var result = true;
                            $find("<%=this.ClientID%>")._dragobj = dragdropObj;

                            return result;
                        }
                    });
                });
            });
        </script>
                    
        <div  id="hidetableonewhenhistoryopen">
			
			<div class="exlsfile">
				<div class="header">Raw CSV data</div>
                           <div style="width:957px;overflow: scroll; border: solid 1px; overflow-x: scroll; overflow-y: scroll;  height: 300px;  border-style: ridge;" class="display dataTables_wrapper dataTables_paginate paginate_button">
        <table id="tableNew" class="table table-striped table-bordered nowrap " cellspacing="0" cellpadding="0" border="0" style="width: 100%; table-layout: auto; border-style: None; border-collapse: collapse;">
            <thead>
            </thead>
        </table>
    </div>
			</div>
                    </div>
        </div>   
		</fieldset>
		<!--csv data end-->
		<!--import setting-->
                    <br />
		<fieldset class="space" >
			<legend>Import Setting:</legend>
			
				 <table style="display:none">
						<ReboundUI_Form:FormField ID="ctlSupplier"   runat="server" FieldID="cmbSupplier" ResourceTitle="Supplier" IsRequiredField="true">
                        <Field>
                            <ReboundUI:Combo ID="cmbSupplier" runat="server"     AutoSearchControlAssembly="Rebound.GlobalTrader.Site" AutoSearchControlType="AllSuppliersImport" />
                        </Field>
                    </ReboundUI_Form:FormField>
					</table>
										
							
<table>

                <tr>
					<td class="firstcol">
						<div class="row">
								
                          <%--  <fieldset style="display:none">
								<legend>File Type</legend>
								<div class="radio-option">
									<label><input type="radio" name="fileType" value="2">Trusted Stock</label>
									<label><input type="radio"  name="fileType" value="1">Offers</label>
									<label><input type="radio" name="fileType" value="3">Sourcing Info</label>
								</div>
							</fieldset>--%>
						</div>
						<div class="row">
							

						</div>
					</td>
					
				</tr>
				<tr>
					<td>
						<div class="row">

							<fieldset>
								<legend>File column mapping</legend>
								<table class="three-col">
									<tr>
										<td>
											<div >
												<label>Manufactuer</label><input type="checkbox"  name="manufacturer" value="manufacturer" id="chkmanufacturerSupp">
                                                 &nbsp<select id="ddlManufacturerSupp" name="ddManufacturer" ></select>
											</div>
											<div >
												
                                                <label>Part</label>
                                                <input type="checkbox" name="Part" value="Part" id="chkPartSupp">
                                                &nbsp<select id="ddlPartSupp" ></select>
											</div>
											<div >
												<label>Quantity</label>
                                                  <input type="checkbox" name="Quantity" value="Quantity" id="chkQuantitySupp">
                                                   &nbsp<select id="ddlQuantitySupp" ></select>
											</div>

											<div >
												
                                                <label>Price</label>
                                                <input type="checkbox" name="Price" value="Price" id="chkPriceSupp">
                                                &nbsp<select id="ddlPriceSupp" ></select>
											</div>

										</td>
										<td>
											<div >
												<label>Description</label>
                                                  <input type="checkbox" name="Description" value="Description" id="chkDescription">
                                                &nbsp<select id="ddlDescription" ></select>
											</div>
											<div >
										        <label>Alt. Part No.</label>
                                                 <input type="checkbox" name="AlternatePartNo." value="AlternatePartNo." id="chkAlternatePartNo">
                                                &nbsp<select id="ddlAlternatePartNo" ></select>
											</div>
											<div >
												
                                                <label> Date Code</label><input type="checkbox" name="DateCode" value="DateCode" id="chkDateCodeSupp">
                                                 &nbsp<select id="ddlDateCodeSupp"></select>
											</div>

											<div >
												
                                                 <label>Product</label>
                                                 <input type="checkbox" name="Product" value="Product" id="chkProductSupp">
                                                    &nbsp<select id="ddlProductSupp" ></select>
											</div>
										</td>
										<td>
											
											<div >
										        <label>Package</label>
                                                 <input type="checkbox" name="Package" value="Package" id="chkPackageSupp">
                                                    &nbsp<select id="ddlPackageSupp" ></select>
                                        	</div>
                                        	<div>
												<label>ROHS</label>
                                                 <input type="checkbox" name="ROHS" value="ROHS" id="chkROHSSupp">
                                                &nbsp<select id="ddlROHSSupp" ></select>
											</div>
                                             <div>
                                                 <label>Fixed Currency</label>
											<input type="checkbox" name="FixedCurrency" value="FixedCurrency" id="chkFixedCurrencySupp">
                                                &nbsp<select id="ddlCurrencySupp" ></select>
											</div>

											<div >
												<label>Supplier Part</label>
                                                 <input type="checkbox" name="SupplierPart" value="SupplierPart" id="chkSupplierPart">
                                            &nbsp<select id="ddlSupplierPart" ></select>
											</div>
										</td>
									</tr>
								</table>

							</fieldset>
						</div>
					</td>
					<td>
						<input type="button" class="btn right" id="btnGetMappingSupp" value="Retrieve Supplier Mappings">
						<input type="button" class="btn right" id="btnSaveMappingSupp" value="Save Supplier Mappings">
                        <input type="button" class="btn right" id="btnColumnResetSupp" style="display:none;" value="Reset Mapping">
					</td>
				</tr>
			</table>
		</fieldset>
		<!--import setting end-->
		<!--Date to import-->
		<%--<fieldset style="display:none">
			<legend>Data to import</legend>
             <p><b>Note: Only showing top 20 records </b></p>
			<table>
				<tr>
					<td class="firstcol">
						<div class="exlsfile">
							<div class="header">Data to be Imported</div>
                            <div style="width:873px;overflow: scroll; border: solid 1px; overflow-x: scroll; overflow-y: scroll;  height: 300px; border-style: ridge;" class="display dataTables_wrapper dataTables_paginate paginate_button">
        <table id="Table2" class="table table-striped table-bordered nowrap " cellspacing="0" cellpadding="0" border="0" style="width: 100%; table-layout: auto; border-style: None; border-collapse: collapse;">
            <thead>
            </thead>
        </table>
    </div>
						</div>
					</td>
					<td>
						<input type="button" class="btn right" id="btnGenerateData" value="Generate Import Data">
						<input type="button" class="btn right" id="btnImport" value="Import">
					</td>
				</tr>
			</table>
		</fieldset>--%>
		<!--Date to import end-->
	</div>
          
            </asp:TableCell>
            </asp:TableRow>
          

            <asp:TableRow>
                <asp:TableCell>
                    <style>
                        .close {
                            color: #aaaaaa;
                            float: right;
                            font-size: 28px;
                            font-weight: bold;
                        }

                            .close:hover,
                            .close:focus {
                                color: #000;
                                text-decoration: none;
                                cursor: pointer;
                            }
                         span.PartDetailsGridGoBtn2 {
                            padding: 2px 0px;
                            margin-left: 0px;
                        }
                         .modal {
                            height: auto !important;
                        }

                        .modal-content {
                            0px 0px 15px 7px #000;
                        }

                            .modal-content input {
                                padding: 12px !important;
                            }

                        .headertitlet {
                            font-size: 15px;
                        }

                        .headertitle {
                            background: #5f9553;
                            padding: 12px;
                        }

                         #myModal {
                            height: 100% !important;z-index:11000;
                        }

                        .modal {
                            top: 25px;
                        }

                        .modal-content {
                            box-shadow: 2px 2px 20px 1px #000;
                            border: 3px #385e26 solid;
                        }
                         /* The Modal (background) */
                        .modal {
                            display: none;
                            /* Hidden by default */
                            position: fixed;
                            /* Stay in place */
                            z-index: 1;
                            /* Sit on top */
                            padding-top: 100px;
                            /* Location of the box */
                            left: 0;
                            top: 0;
                            width: 100%;
                            /* Full width */
                            height: 100%;
                            /* Full height */
                            overflow: auto;
                            /* Enable scroll if needed */
                            background-color: rgb(0, 0, 0);
                            /* Fallback color */
                            background-color: rgba(0, 0, 0, 0.4);
                            /* Black w/ opacity */
                        }

                        /* Modal Content */
                        .modal-content {
                            background-color: #66a75e;
                            margin: auto;
                            padding: 20px;
                            border: 1px solid #66a75e;
                            width: 50%;
                            position: relative;
                            overflow: hidden;
                        }

                            .modal-content table tr td,
                            .modal-content table tr th {
                                text-align: left;
                            }

                        /* The Close Button */
                        .close {
                            color: #aaaaaa;
                            float: right;
                            font-size: 28px;
                            font-weight: bold;
                        }

                            .close:hover,
                            .close:focus {
                                color: #000;
                                text-decoration: none;
                                cursor: pointer;
                            }
                        .bgbase2 {
                            float: left;
                            bottom: 26px;
                            right: 18px;
                            background-color: #5f9553;
                           
                        }

                        .LoaderPopup {
                            background: rgba(0,0,0,.4);
                            cursor: pointer;
                            display: none;
                            /* height: auto; */
                            height: 100%;
                            position: absolute;
                            text-align: center;
                            top: -49px;
                            width: 100%;
                            z-index: 10000;
                        }

                        .cssload-loader {
                            width: 244px;
                            height: 49px;
                            line-height: 49px;
                            text-align: center;
                            position: absolute;
                            left: 50%;
                            top: 20%;
                            transform: translate(-50%, -50%);
                            -o-transform: translate(-50%, -50%);
                            -ms-transform: translate(-50%, -50%);
                            -webkit-transform: translate(-50%, -50%);
                            -moz-transform: translate(-50%, -50%);
                            font-family: helvetica, arial, sans-serif;
                            text-transform: uppercase;
                            font-weight: 900;
                            font-size: 18px;
                            color: rgb(206,66,51);
                            letter-spacing: 0.2em;
                            background-color: aliceblue;
                        }
						.three-col select { width: 111px!important;}
						input:disabled {background: #4a7943;}
                    </style>
 
  
                     
                </asp:TableCell>
            </asp:TableRow>
     </ReboundUI_Table:Form>
	</Content>
	
</ReboundUI_Form:DesignBase>



