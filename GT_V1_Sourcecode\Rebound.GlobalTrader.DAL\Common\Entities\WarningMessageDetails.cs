﻿using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

namespace Rebound.GlobalTrader.DAL
{
    public class WarningMessageDetails
    {
        #region Constructors

        public WarningMessageDetails() { }

        #endregion

        #region Properties

        /// <summary>
        /// SystemWarningMessageId (from Table)
        /// </summary>
        public System.Int32 SystemWarningMessageId { get; set; }
        /// <summary>
        /// ClientNo (from Table)
        /// </summary>
        public System.Int32 ClientNo { get; set; }

        /// <summary>
        /// WarningText (from Table)
        /// </summary>
        public System.String WarningText { get; set; }
        /// <summary>
        /// UpdatedBy (from Table)
        /// </summary>
        public System.Int32? UpdatedBy { get; set; }
        /// <summary>
        /// DLUP (from Table)
        /// </summary>
        public System.DateTime? DLUP { get; set; }
        /// <summary>
        /// WarningKey (from usp_selectAll_SystemWarningMessage_for_Client)
        /// </summary>
        public System.String WarningKey { get; set; }

        /// <summary>
        /// ApplyToCatagory (from Table)
        /// </summary>
        public System.String ApplyToCatagory { get; set; }

        /// <summary>
        /// ApplyTo (from Table)
        /// </summary>
        public System.String ApplyTo { get; set; }
        public System.Boolean? InActive { get; set; }
        /// <summary>
        /// ProductName (from Table)
        /// </summary>
        public System.String ProductName { get; set; }

        /// <summary>
        /// ProductDescription (from Table)
        /// </summary>
        public System.String ProductDescription { get; set; }
        /// <summary>
        /// GlobalProductNameId (from Table)
        /// </summary>
        public System.Int32 GlobalProductNameId { get; set; }
        /// <summary>
        /// ProductId (from Table)
        /// </summary>
        public System.Int32 ProductId { get; set; }

        /// <summary>
        /// CategoryId (from Table)
        /// </summary>
        public System.Int32 GlobalProductCategoryId { get; set; }

        #endregion
    }
}
