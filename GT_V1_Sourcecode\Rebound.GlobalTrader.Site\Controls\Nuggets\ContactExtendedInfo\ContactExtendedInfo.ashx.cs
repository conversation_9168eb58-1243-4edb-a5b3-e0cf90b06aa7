using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets.Data {
	[WebService(Namespace = "http://tempuri.org/")]
	[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
	public class ContactExtendedInfo : Rebound.GlobalTrader.Site.Data.Base {

		public override void ProcessRequest(HttpContext context) {
			if (base.init(context)) {
				switch (Action) {
					case "GetData": GetData(); break;
					case "SaveEdit": SaveEdit(); break;
					default: WriteErrorActionNotFound(); break;
				}
			}
		}

		internal JsonObject GetDataJson(BLL.Contact con) {
			JsonObject jsn = new JsonObject();

			//supplement
			BLL.ContactSupplement conSup = BLL.ContactSupplement.Get(con.ContactId);
			if (conSup != null) {
				jsn.AddVariable("GenderID", conSup.GenderNo);
				jsn.AddVariable("Gender", Functions.FormatGender(conSup.GenderNo));
				jsn.AddVariable("Birthday", Functions.FormatBirthday(conSup.Birthday));
				jsn.AddVariable("MaritalStatus", conSup.MaritalStatusName);
				jsn.AddVariable("MaritalStatusID", conSup.MaritalStatusNo);
				jsn.AddVariable("PartnerName", conSup.PartnerName);
				jsn.AddVariable("PartnerBirthday", Functions.FormatBirthday(conSup.PartnerBirthday));
				jsn.AddVariable("Anniversary", Functions.FormatBirthday(conSup.Anniversary));
				jsn.AddVariable("NumberChildren", conSup.NumberOfChildren);
				jsn.AddVariable("Child1", FormatChildData(conSup.ChildName1, conSup.ChildGenderNo1, conSup.ChildBirthday1));
				jsn.AddVariable("Child1Name", conSup.ChildName1);
				jsn.AddVariable("Child1SexID", conSup.ChildGenderNo1);
				jsn.AddVariable("Child1Birthday", Functions.FormatBirthday(conSup.ChildBirthday1));
				jsn.AddVariable("Child2", FormatChildData(conSup.ChildName2, conSup.ChildGenderNo2, conSup.ChildBirthday2));
				jsn.AddVariable("Child2Name", conSup.ChildName2);
				jsn.AddVariable("Child2SexID", conSup.ChildGenderNo2);
				jsn.AddVariable("Child2Birthday", Functions.FormatBirthday(conSup.ChildBirthday2));
				jsn.AddVariable("Child3", FormatChildData(conSup.ChildName3, conSup.ChildGenderNo3, conSup.ChildBirthday3));
				jsn.AddVariable("Child3Name", conSup.ChildName3);
				jsn.AddVariable("Child3SexID", conSup.ChildGenderNo3);
				jsn.AddVariable("Child3Birthday", Functions.FormatBirthday(conSup.ChildBirthday3));
				jsn.AddVariable("MobileTel", conSup.PersonalCellphone);
				jsn.AddVariable("FavouriteSport", conSup.FavouriteSport);
				jsn.AddVariable("FavouriteTeam", conSup.FavouriteTeam);
				jsn.AddVariable("Hobbies", Functions.ReplaceLineBreaks(conSup.Hobbies));
				jsn.AddVariable("DLUP", Functions.FormatDLUP(conSup.DLUP, conSup.UpdatedBy));
			}
			jsn.AddVariable("HasSupplementalData", (conSup != null));
			conSup = null;

			//notes
			if (con != null) {
				jsn.AddVariable("Notes", Functions.ReplaceLineBreaks(con.Notes));
			}

			return jsn;
		}
		private void GetData() {
			BLL.Contact con = BLL.Contact.Get(ID);
			OutputResult(GetDataJson(con));
		}

		internal static string FormatChildData(String strName, int? intGender, DateTime? dtmBirthday) {
			string strOut = "";
			if (!String.IsNullOrEmpty(strName)) strOut += strName;
			if (dtmBirthday != null) {
				if (strOut.Length > 0) strOut += "<br />";
				strOut += String.Format("{0} {1}", Functions.GetGlobalResource("FormFields", "Birthday"), Functions.FormatBirthday(dtmBirthday));
			}
			string strGender = Functions.FormatGender(intGender);
			if (!String.IsNullOrEmpty(strGender)) {
				if (strOut.Length > 0) strOut += "<br />";
				strOut += strGender;
			}
			return strOut;
		}

		private void SaveEdit() {
			bool blnOK = false;
			JsonObject jsn = new JsonObject();

			//update notes
			BLL.Contact con = BLL.Contact.Get(ID);
			if (con != null) {
				con.Notes = GetFormValue_String("Notes");
				con.Update();
			}

			//either insert or update the contact supplement
			BLL.ContactSupplement sup = BLL.ContactSupplement.Get(ID);
			if (sup != null) {
				sup.GenderNo = GetFormValue_NullableInt("Gender");
				sup.Birthday = GetFormValue_NullableDateTime("Birthday");
				sup.MaritalStatusNo = GetFormValue_NullableInt("MaritalStatus");
				sup.PartnerName = GetFormValue_String("Partner");
				sup.PartnerBirthday = GetFormValue_NullableDateTime("PartnerBirthday");
				sup.Anniversary = GetFormValue_NullableDateTime("Anniversary");
				sup.NumberOfChildren = GetFormValue_NullableInt("NumberOfChildren");
				sup.ChildName1 = GetFormValue_String("ChildName1");
				sup.ChildGenderNo1 = GetFormValue_NullableInt("ChildGender1");
				sup.ChildBirthday1 = GetFormValue_NullableDateTime("ChildBirthday1");
				sup.ChildName2 = GetFormValue_String("ChildName2");
				sup.ChildGenderNo2 = GetFormValue_NullableInt("ChildGender2");
				sup.ChildBirthday2 = GetFormValue_NullableDateTime("ChildBirthday2");
				sup.ChildName3 = GetFormValue_String("ChildName3");
				sup.ChildGenderNo3 = GetFormValue_NullableInt("ChildGender3");
				sup.ChildBirthday3 = GetFormValue_NullableDateTime("ChildBirthday3");
				sup.PersonalCellphone = GetFormValue_String("PersonalCellphone");
				sup.FavouriteSport = GetFormValue_String("FavouriteSport");
				sup.FavouriteTeam = GetFormValue_String("FavouriteTeam");
				sup.Hobbies = GetFormValue_String("Hobbies");
				sup.UpdatedBy = LoginID;
				blnOK = sup.Update();
			} else {
				BLL.ContactSupplement.Insert(
					ID
				, GetFormValue_NullableInt("Gender")
				, GetFormValue_NullableDateTime("Birthday")
				, GetFormValue_NullableInt("MaritalStatus")
				, GetFormValue_String("Partner")
				, GetFormValue_NullableDateTime("PartnerBirthday")
				, GetFormValue_NullableDateTime("Anniversary")
				, GetFormValue_NullableInt("NumberOfChildren")
				, GetFormValue_String("ChildName1")
				, GetFormValue_NullableInt("ChildGender1")
				, GetFormValue_NullableDateTime("ChildBirthday1")
				, GetFormValue_String("ChildName2")
				, GetFormValue_NullableInt("ChildGender2")
				, GetFormValue_NullableDateTime("ChildBirthday2")
				, GetFormValue_String("ChildName3")
				, GetFormValue_NullableInt("ChildGender3")
				, GetFormValue_NullableDateTime("ChildBirthday3")
				, GetFormValue_String("PersonalCellphone")
				, GetFormValue_String("FavouriteSport")
				, GetFormValue_String("FavouriteTeam")
				, GetFormValue_String("Hobbies")
				, LoginID
				);
				blnOK = true;
			}
			jsn.AddVariable("Result", blnOK);
			OutputResult(jsn);
		}

	}
}
