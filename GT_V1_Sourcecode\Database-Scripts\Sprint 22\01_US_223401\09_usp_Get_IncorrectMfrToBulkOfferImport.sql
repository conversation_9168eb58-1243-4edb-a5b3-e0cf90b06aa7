﻿
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/*  
======================================================================================================================================================
TASK            UPDATED BY       DATE            ACTION      DESCRIPTION    
[US-223401]     Phuc Hoang       20-Feb-2025     CREATE      IPO - Bulk Offer Import tool - Apply fuzzy matching and rationalize data before importing  
======================================================================================================================================================
*/  
CREATE OR ALTER PROC [dbo].[usp_Get_IncorrectMfrToBulkOfferImport] (      
 @ClientNo INT = 0,    
 @ImportFileId INT = 0
)    
AS    
    
BEGIN    
	SELECT 
	offerImport.OfferTempId, 
	CASE 
		WHEN ISNULL(offerImport.MFR, '') = '' THEN '_Blank_'  
		ELSE offerImport.MFR  
	END AS [MFR],
	offerImport.MFRCount
	FROM (
		SELECT MFR, MAX(OfferTempId) AS OfferTempId, COUNT(MFR) AS MFRCount
		FROM [BorisGlobalTraderImports].[dbo].[tbOfferImportByExcelTemp] WITH (NOLOCK)
		WHERE ClientNo = @ClientNo AND HUBOfferImportLargeFileID = @ImportFileId
		GROUP BY MFR
	) offerImport
	OUTER APPLY (
			SELECT TOP 1 * FROM dbo.tbManufacturer manu 
			WHERE manu.ManufacturerName = offerImport.MFR AND manu.Inactive = 0
		) manu
	WHERE ISNULL(manu.ManufacturerName, '') = ''
	ORDER BY OfferTempId
END 
GO


