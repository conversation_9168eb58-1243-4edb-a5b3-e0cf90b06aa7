///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//----------------------------------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//----------------------------------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.ItemSearch");

Rebound.GlobalTrader.Site.Controls.ItemSearch.SalesOrders = function(element) { 
	Rebound.GlobalTrader.Site.Controls.ItemSearch.SalesOrders.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.ItemSearch.SalesOrders.prototype = {

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.ItemSearch.SalesOrders.callBaseMethod(this, "initialize");
		this.addSetupData(Function.createDelegate(this, this.doSetupData));
		this.addGetDataComplete(Function.createDelegate(this, this.doGetDataComplete));
	},

	dispose: function() { 
		if (this.isDisposed) return;
		Rebound.GlobalTrader.Site.Controls.ItemSearch.SalesOrders.callBaseMethod(this, "dispose");
	},
	
	doSetupData: function () {
		this._objData.set_PathToData("controls/ItemSearch/SalesOrders");
		debugger;

		this._objData.set_DataObject("SalesOrders");
		this._objData.set_DataAction("GetData");
		this._objData.addParameter("Contact", this.getFieldValue("ctlContact"));
		this._objData.addParameter("Partno", this.getFieldValue("crlPartno"));
		this._objData.addParameter("CMName", this.getFieldValue("ctlCompany"));
		this._objData.addParameter("IncludeClosed", this.getFieldValue("ctlIncludeClosed"));
		this._objData.addParameter("Salesman", this.getFieldValue("ctlSalesman"));
		this._objData.addParameter("CustomerPO", this.getFieldValue("ctlCustomerPO"));
		this._objData.addParameter("SalesOrderNoLo", this.getFieldValue_Min("ctlSalesOrderNo"));
		this._objData.addParameter("SalesOrderNoHi", this.getFieldValue_Max("ctlSalesOrderNo"));
		this._objData.addParameter("DateOrderedFrom", this.getFieldValue("ctlDateOrderedFrom"));
		this._objData.addParameter("DateOrderedTo", this.getFieldValue("ctlDateOrderedTo"));
	},
	
	doGetDataComplete: function() {
		for (var i = 0, l = this._objResult.Results.length; i < l; i++) {
			var row = this._objResult.Results[i];
			debugger
			var aryData = [
				row.No,
				$R_FN.setCleanTextValue(row.Partno),
				$R_FN.setCleanTextValue(row.CMName),				
				$R_FN.setCleanTextValue(row.Contact),
				$R_FN.setCleanTextValue(row.Date),
				$R_FN.setCleanTextValue(row.Salesman),
				$R_FN.setCleanTextValue(row.CustomerPO)
			];
			this._tblResults.addRow(aryData, row.ID, false);
			aryData = null; row = null;
		}
	}
};

Rebound.GlobalTrader.Site.Controls.ItemSearch.SalesOrders.registerClass("Rebound.GlobalTrader.Site.Controls.ItemSearch.SalesOrders", Rebound.GlobalTrader.Site.Controls.ItemSearch.Base, Sys.IDisposable);
