﻿
/*   
===========================================================================================  
TASK			UPDATED BY      DATE			ACTION		DESCRIPTION  
[US-229094]		Phuc Hoang		20-Jan-2025		Create		Quote - Add automatic Task Reminder based on Quote Status
===========================================================================================  
*/

IF OBJECT_ID (N'tbToDoCategory', N'U') IS NULL 
	CREATE TABLE [dbo].[tbToDoCategory](
		[ToDoCategoryId] [int] IDENTITY(1,1) NOT NULL,
		[ToDoCategoryName] [nvarchar](100) NULL,
		[Inactive] [bit] NULL,
		[UpdatedBy] [int] NULL,
		[DLUP] [datetime] NOT NULL,
	 CONSTRAINT [PK_tbToDoCategory] PRIMARY KEY CLUSTERED 
	(
		[ToDoCategoryId] ASC
	)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
	) ON [PRIMARY]
	GO

	ALTER TABLE [dbo].[tbToDoCategory] ADD  CONSTRAINT [DF_tbToDoCategory_Inactive]  DEFAULT ((0)) FOR [Inactive]
	GO

	ALTER TABLE [dbo].[tbToDoCategory] ADD  CONSTRAINT [DF_tbToDoCategory_DLUP]  DEFAULT (getdate()) FOR [DLUP]

GO

INSERT INTO [dbo].[tbToDoCategory]([ToDoCategoryName],[UpdatedBy]) VALUES ('Company', NULL);
INSERT INTO [dbo].[tbToDoCategory]([ToDoCategoryName],[UpdatedBy]) VALUES ('Quote', NULL); 
INSERT INTO [dbo].[tbToDoCategory]([ToDoCategoryName],[UpdatedBy]) VALUES ('HUBRFQ', NULL);
INSERT INTO [dbo].[tbToDoCategory]([ToDoCategoryName],[UpdatedBy]) VALUES ('Sales Orders', NULL); 
INSERT INTO [dbo].[tbToDoCategory]([ToDoCategoryName],[UpdatedBy]) VALUES ('Certificate', NULL);