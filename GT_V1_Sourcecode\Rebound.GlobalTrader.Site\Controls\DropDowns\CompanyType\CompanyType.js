Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.CompanyType=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.CompanyType.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DropDowns.CompanyType.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.DropDowns.CompanyType.callBaseMethod(this,"initialize");this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK))},dispose:function(){this.isDisposed||Rebound.GlobalTrader.Site.Controls.DropDowns.CompanyType.callBaseMethod(this,"dispose")},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/CompanyType");this._objData.set_DataObject("CompanyType");this._objData.set_DataAction("GetData")},dataCallOK:function(){var t=this._objData._result,n;if(t.Types)for(n=0;n<t.Types.length;n++)this.addOption(t.Types[n].Name,t.Types[n].ID)}};Rebound.GlobalTrader.Site.Controls.DropDowns.CompanyType.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.CompanyType",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);