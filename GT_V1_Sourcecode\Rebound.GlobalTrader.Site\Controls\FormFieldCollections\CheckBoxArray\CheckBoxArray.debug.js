///<reference name="MicrosoftAjax.js" />
///<reference path="~/Common/Data/Data.js" />
///<reference path="~/Common/Functions/Functions.js" />
///<reference path="~/Controls/IconButton/IconButton.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - dispose everything fully
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.FormFieldCollections");

Rebound.GlobalTrader.Site.Controls.FormFieldCollections.CheckBoxArray = function(element) {
    Rebound.GlobalTrader.Site.Controls.FormFieldCollections.CheckBoxArray.initializeBase(this, [element]);
    this._aryComponents = [];
    this._intCheckBoxNo = 0;
    this._intHeadingNo = -1;
    this._intMyTabHeading = -1;
};

Rebound.GlobalTrader.Site.Controls.FormFieldCollections.CheckBoxArray.prototype = {

    get_tbl: function() { return this._tbl; }, set_tbl: function(v) { if (this._tbl !== v) this._tbl = v; },

    initialize: function() {
        Rebound.GlobalTrader.Site.Controls.FormFieldCollections.CheckBoxArray.callBaseMethod(this, "initialize");
    },

    dispose: function() {
        if (this.isDisposed) return;
        if (this.get_element()) $clearHandlers(this.get_element());
        this._tbl = null;
        this._aryComponents = null;
        this._intCheckBoxNo = null;
        this._intHeadingNo = null;
        this._intMyTabHeading = null;
        Rebound.GlobalTrader.Site.Controls.FormFieldCollections.CheckBoxArray.callBaseMethod(this, "dispose");
    },

    addItem: function (varID, strTitle, blnChecked, varID2, strTitle2, blnChecked2) {
            var tr = this._tbl.insertRow(-1);
            this.addItemCells(tr, varID, strTitle, blnChecked, false);
            if (varID2) {
                this.addItemCells(tr, varID2, strTitle2, blnChecked2, true);
            } else {
                for (var i = 0; i < 3; i++) {
                    var td = document.createElement("td");
                    td.innerHTML = "&nbsp;";
                    tr.appendChild(td);
                    td = null;
                }
            }
            tr = null;
       
        
    },
    addItemCells: function (tr, varID, strTitle, blnChecked, blnSecondCol) {
            var td = [];
            if (blnSecondCol) {
                td[0] = document.createElement("td");
                td[0].className = "sep";
                td[0].innerHTML = "&nbsp;";
                tr.appendChild(td[0]);
            }
            td[1] = document.createElement("td");
            td[1].className = "label";
            td[1].innerHTML = $R_FN.setCleanTextValue(strTitle);
            tr.appendChild(td[1]);
            td[2] = document.createElement("td");
            td[2].className = "item";
            td[2].innerHTML = this.writeCheckbox(varID, this._intCheckBoxNo, blnChecked);
            tr.appendChild(td[2]);
            this.registerCheckBox(varID, this._intCheckBoxNo, blnChecked, true);
            this._intCheckBoxNo += 1;
            td = null;
        
    },
    addItemForTabSecurity: function(varID, strTitle, blnMyTab, blnTeamTab, blnDivisionTab, blnCompanyTab) {
        var tr = this._tbl.insertRow(-1);
        this.addItemCellsForTabSecurity(tr, varID, strTitle, blnMyTab, blnTeamTab, blnDivisionTab, blnCompanyTab);
        tr = null;
    },
    addItemCellsForTabSecurity: function(tr, varID, strTitle, blnMyTab, blnTeamTab, blnDivisionTab, blnCompanyTab) {
        var td = [];
        this._intMyTabHeading += 1;
        td[1] = document.createElement("td");
        td[1].className = "itemTab";
        td[1].innerHTML = $R_FN.setCleanTextValue(strTitle);
        tr.appendChild(td[1]);

        td[2] = document.createElement("td");
        td[2].className = "itemTab";
        var myTabCheckBox = this._intCheckBoxNo;
        td[2].innerHTML = this.writeSecurityCheckbox(varID, this._intCheckBoxNo, blnMyTab);
        tr.appendChild(td[2]);
        this.registerCheckBox(varID, this._intCheckBoxNo, blnMyTab, true);
        var chk = this.getCheckBox(this._intCheckBoxNo);
        chk._element.setAttribute("onclick", String.format("$find(\"{0}\").clickMyTabCheckBox({1}, {2});", this._element.id, this._intCheckBoxNo, this._intMyTabHeading));
        chk = null;
        this._intCheckBoxNo += 1;


        td[3] = document.createElement("td");
        td[3].className = "itemTab";
        td[3].innerHTML = this.writeSecurityCheckbox(varID, this._intCheckBoxNo, blnTeamTab);
        tr.appendChild(td[3]);
        this.registerCheckBox(varID, this._intCheckBoxNo, blnTeamTab, true);
        var chk = this.getCheckBox(this._intCheckBoxNo);
        chk._element.setAttribute("onclick", String.format("$find(\"{0}\").clickExceptMyTabCheckBox({1}, {2});", this._element.id, myTabCheckBox, this._intCheckBoxNo));
        chk = null;
        this._intCheckBoxNo += 1;

        td[4] = document.createElement("td");
        td[4].className = "itemTab";
        td[4].innerHTML = this.writeSecurityCheckbox(varID, this._intCheckBoxNo, blnDivisionTab);
        tr.appendChild(td[4]);
        this.registerCheckBox(varID, this._intCheckBoxNo, blnDivisionTab, true);
        var chk = this.getCheckBox(this._intCheckBoxNo);
        chk._element.setAttribute("onclick", String.format("$find(\"{0}\").clickExceptMyTabCheckBox({1}, {2});", this._element.id, myTabCheckBox, this._intCheckBoxNo));
        chk = null;
        this._intCheckBoxNo += 1;

        td[5] = document.createElement("td");
        td[5].className = "itemTab";
        td[5].innerHTML = this.writeSecurityCheckbox(varID, this._intCheckBoxNo, blnCompanyTab);
        tr.appendChild(td[5]);
        this.registerCheckBox(varID, this._intCheckBoxNo, blnCompanyTab, true);
        var chk = this.getCheckBox(this._intCheckBoxNo);
        chk._element.setAttribute("onclick", String.format("$find(\"{0}\").clickExceptMyTabCheckBox({1}, {2});", this._element.id, myTabCheckBox, this._intCheckBoxNo));
        chk = null;
        this._intCheckBoxNo += 1;
        td = null;
        myTabCheckBox = null;
    },
    addItemCellsForTabSecurityHeader: function(strHeader1, strHeader2, strHeader3, strHeader4, strHeader5) {
        var tr = this._tbl.insertRow(-1);
        var td = [];

        td[1] = document.createElement("td");
        td[1].className = "itemTabHeader";
        td[1].innerHTML = $R_FN.setCleanTextValue(strHeader1);
        tr.appendChild(td[1]);

        td[2] = document.createElement("td");
        td[2].className = "itemTabHeader";
        td[2].innerHTML = $R_FN.setCleanTextValue(strHeader2);
        tr.appendChild(td[2]);

        td[3] = document.createElement("td");
        td[3].className = "itemTabHeader";
        td[3].innerHTML = $R_FN.setCleanTextValue(strHeader3);
        tr.appendChild(td[3]);

        td[4] = document.createElement("td");
        td[4].className = "itemTabHeader";
        td[4].innerHTML = $R_FN.setCleanTextValue(strHeader4);
        tr.appendChild(td[4]);

        td[5] = document.createElement("td");
        td[5].className = "itemTabHeader";
        td[5].innerHTML = $R_FN.setCleanTextValue(strHeader5);
        tr.appendChild(td[5]);

        td = null;
        tr = null;
    },
    addHeading: function(intLevel, str, blnAddCheckBox) {
        this._intHeadingNo += 1;
        var tr = this._tbl.insertRow(-1);
        var td = document.createElement("td");
        var strID = String.format("heading{0}", this._intHeadingNo);
        td.colSpan = 5;
        var strOut = String.format("<h{0}{1}>", intLevel, (this._tbl.rows.length == 1) ? " class=\"first\"" : "");
        if (blnAddCheckBox) strOut += String.format("{0}&nbsp;", this.writeCheckbox(strID, this._intCheckBoxNo, false));
        strOut += String.format("{0}</h{1}>", str, intLevel);
        td.innerHTML = strOut;
        tr.appendChild(td);
        tr = null; td = null;
        if (blnAddCheckBox) {
            this.registerCheckBox(strID, this._intCheckBoxNo, false, false);
            var chk = this.getCheckBox(this._intCheckBoxNo);
            chk._element.setAttribute("onclick", String.format("$find(\"{0}\").clickHeadingCheckBox({1}, {2});", this._element.id, this._intCheckBoxNo, this._intHeadingNo));
            this._intCheckBoxNo += 1;
        }
        return this._intCheckBoxNo - 1;
    },

    clickHeadingCheckBox: function(intCheckBox, intGroup) {
        var chk = this.getCheckBox(intCheckBox);
        for (var i = 0, l = this._aryComponents.length; i < l; i++) {
            var chk2 = $find(this._aryComponents[i].ControlID);
            if (chk2._element.getAttribute("bgt_GroupingNo") == intGroup) chk2.setChecked(chk._blnChecked);
            chk2 = null;
        }
        chk = null;
    },
    clickMyTabCheckBox: function(intCheckBox, intSecurityGroup) {
        var chk = this.getCheckBox(intCheckBox);
        if (!chk._blnChecked) {
            for (var i = 0, l = this._aryComponents.length; i < l; i++) {
                var chk2 = $find(this._aryComponents[i].ControlID);
                if (chk2._element.getAttribute("bgt_SecurityGroupNo") == intSecurityGroup) chk2.setChecked(chk._blnChecked);
                chk2 = null;
            }
        }
        chk = null;
    },
    clickExceptMyTabCheckBox: function(intMyCheckBox, ExceptMyTabCheckBox) {
        var chk = this.getCheckBox(intMyCheckBox);
        var chk2 = this.getCheckBox(ExceptMyTabCheckBox);
        if (!chk._blnChecked) {
            chk2.setChecked(false);
        }
        chk = null;
        chk2 = null;
    },

    getControlID: function(str, i) {
        return String.format("{0}_{1}{2}", this._element.id, str, i);
    },

    clearItems: function() {
        for (var i = this._tbl.rows.length - 1; i >= 0; i--) { this._tbl.deleteRow(i); }
        for (i = 0, l = this._aryComponents.length; i < l; i++) {
            var cmp = $find(this._aryComponents[i].ControlID);
            if (cmp) cmp.dispose();
            cmp = null;
        }
        Array.clear(this._aryComponents);
    },

    getIDs: function() {
        var ary = [];
        for (var i = 0, l = this._aryComponents.length; i < l; i++) {
            Array.add(ary, this._aryComponents[i].ID);
        }
        return ary;
    },

    getIDsAsString: function() {
        return $R_FN.arrayToSingleString(this.getIDs());
    },

    getValues: function() {
        var ary = [];
        for (var i = 0, l = this._aryComponents.length; i < l; i++) {
            var cmp = $find(this._aryComponents[i].ControlID);
            Array.add(ary, cmp._blnChecked);
            cmp = null;
        }
        return ary;
    },

    getValuesAsString: function() {
        return $R_FN.arrayToSingleString(this.getValues());
    },

    writeCheckbox: function(varID, intIndex, blnChecked) {
        var strChkID = this.getControlID("chk", intIndex);
        var strChkImageID = this.getControlID("chkImg", intIndex);
        var str = String.format("<div class=\"imageCheckBoxDisabled\" id=\"{0}\" bgt_GroupingNo=\"{3}\"><img id=\"{1}\" class=\"{2}\" src=\"images/x.gif\" style=\"border-width: 0px;\" /></div>", strChkID, strChkImageID, (blnChecked) ? "on" : "off", this._intHeadingNo);
        return str;
    },
    writeSecurityCheckbox: function(varID, intIndex, blnChecked) {
        var strChkID = this.getControlID("chk", intIndex);
        var strChkImageID = this.getControlID("chkImg", intIndex);
        var str = String.format("<div class=\"imageCheckBoxDisabled\" id=\"{0}\" bgt_GroupingNo=\"{3}\" bgt_SecurityGroupNo=\"{4}\" ><img id=\"{1}\" class=\"{2}\" src=\"images/x.gif\" style=\"border-width: 0px;\" /></div>", strChkID, strChkImageID, (blnChecked) ? "on" : "off", this._intHeadingNo, this._intMyTabHeading);
        return str;
    },

    registerCheckBox: function(varID, intIndex, blnChecked, blnAddToComponents) {
        var strChkID = this.getControlID("chk", intIndex);
        var strChkImageID = this.getControlID("chkImg", intIndex);
        eval($R_FN.getCreateStatement("Rebound.GlobalTrader.Site.Controls.ImageCheckBox", [["blnChecked", blnChecked], ["blnEnabled", "true"], ["img", String.format("$get(\"{0}\")", strChkImageID)]], strChkID));
        if (blnAddToComponents) Array.add(this._aryComponents, { ID: varID, ControlID: strChkID });
    },

    getCheckBox: function(intCheckBox) {
        return $find(this.getControlID("chk", intCheckBox));
    },

    setCheckBoxValue: function(intCheckBox, blnValue) {
        var chk = $find(this.getControlID("chk", intCheckBox));
        if (chk) chk.setChecked(blnValue);
        chk = null;
    }

};

Rebound.GlobalTrader.Site.Controls.FormFieldCollections.CheckBoxArray.registerClass("Rebound.GlobalTrader.Site.Controls.FormFieldCollections.CheckBoxArray", Rebound.GlobalTrader.Site.Controls.Forms.LabelFormField, Sys.IDisposable);