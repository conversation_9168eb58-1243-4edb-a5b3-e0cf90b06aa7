    <%@ Control Language="C#" CodeBehind="CRMALines.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Nuggets.CRMALines" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" TitleText="Lines" BoxType="Standard" >
	<Links>
		<ReboundUI:IconButton ID="ibtnAdd" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="Add" IconCSSType="Add" />
		<ReboundUI:IconButton ID="ibtnEdit" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="Edit" IconCSSType="Edit" IsInitiallyEnabled="false" />
		<ReboundUI:IconButton ID="ibtnDelete" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="Delete" IconCSSType="Delete" IsInitiallyEnabled="false" />
		<ReboundUI:IconButton ID="ibtnClose" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="Close" IsInitiallyVisible="false" IsInitiallyEnabled="false"/>

	</Links>
	<Content>
		<ReboundUI:TabStrip id="ctlTabs" runat="server">
		
			<TabsTemplate>
				<ReboundUI:Tab ID="ctlTabAll" runat="server" RelatedContentPanelID="pnlTabAll" IsSelected="true" TitleText="All" /><ReboundUI:Tab ID="ctlTabOpen" runat="server" TitleText="Open" RelatedContentPanelID="pnlTabOpen" /><ReboundUI:Tab ID="ctlTabClosed" TitleText="Closed" runat="server" RelatedContentPanelID="pnlTabClosed" />
			</TabsTemplate>
			
			<TabsContent>
				<asp:Panel ID="pnlTabAll" runat="server">
					<ReboundUI:FlexiDataTable ID="tblAll" runat="server" AllowSelection="true" PanelHeight="160" />
				</asp:Panel>
				<asp:Panel ID="pnlTabOpen" runat="server">
					<ReboundUI:FlexiDataTable ID="tblOpen" runat="server" AllowSelection="true" PanelHeight="160" />
				</asp:Panel>
				<asp:Panel ID="pnlTabClosed" runat="server">
					<ReboundUI:FlexiDataTable ID="tblClosed" runat="server" AllowSelection="true" PanelHeight="160" />
				</asp:Panel>
			</TabsContent>
			
		</ReboundUI:TabStrip>
		
		<asp:Panel ID="pnlLoadingLineDetail" runat="server" CssClass="subInfoLoading invisible"><%=Functions.GetGlobalResource("Misc", "Loading")%></asp:Panel>
		<asp:Panel ID="pnlLineDetailError" runat="server" CssClass="error invisible" />
		<asp:Panel id="pnlLineDetail" runat="server" CssClass="invisible">
			<h4 class="extraTopMargin"><asp:HyperLink ID="hypPrev" runat="server" NavigateUrl="javascript:void(0);" CssClass="linePagingNextPrev linePagingPrev">&laquo;</asp:HyperLink><asp:Label ID="lblLineNumber" runat="server" /><asp:HyperLink ID="hypNext" runat="server" NavigateUrl="javascript:void(0);" CssClass="linePagingNextPrev linePagingNext">&raquo;</asp:HyperLink></h4>
			<table class="twoCols">
				<tr>
					<td class="col1">
						<table cellpadding="0" cellspacing="0" border="0" class="dataItems">
							<ReboundUI:DataItemRow id="ctlPartNo" runat="server" ResourceTitle="PartNo" />
						    <ReboundUI:DataItemRow id="hidPartNo" runat="server" FieldType="Hidden" />
							<ReboundUI:DataItemRow id="ctlROHS" runat="server" ResourceTitle="ROHS" />
							<ReboundUI:DataItemRow id="hidROHS" runat="server" FieldType="Hidden" />
 							<ReboundUI:DataItemRow id="ctlDateCode" runat="server" ResourceTitle="DateCode" />
							<ReboundUI:DataItemRow id="ctlCustomerPart" runat="server" ResourceTitle="CustomerPart" />
						    <ReboundUI:DataItemRow id="ctlQuantity" runat="server" ResourceTitle="Quantity" />
						    <ReboundUI:DataItemRow id="ctlQuantityReceived" runat="server" ResourceTitle="QuantityReceived" />
						    					
						    <ReboundUI:DataItemRow id="ctlCreditNoteNos" runat="server" ResourceTitle="Credit" />
						    <ReboundUI:DataItemRow id="ctlLineNotes" runat="server" ResourceTitle="Notes" />	
							<ReboundUI:DataItemRow id="ctlAS6081" runat="server" ResourceTitle="AS6081Filter" />	
						</table>
					</td>
					<td class="col2">
						<table cellpadding="0" cellspacing="0" border="0" class="dataItems">
							<ReboundUI:DataItemRow id="ctlReturnDate" runat="server" ResourceTitle="ReturnDate" />
							<ReboundUI:DataItemRow id="ctlManufacturer" runat="server" ResourceTitle="Manufacturer" />
							<ReboundUI:DataItemRow id="hidManufacturer" runat="server" FieldType="Hidden" />
							<ReboundUI:DataItemRow id="hidManufacturerNo" runat="server" FieldType="Hidden" />
							<%--<ReboundUI:DataItemRow id="ctlProduct" runat="server" ResourceTitle="Product" />--%>
                             <ReboundUI:DataItemRow id="ctlProduct" runat="server" FieldType="Hidden"  />
                             <ReboundUI:DataItemRow id="ctlProductDis" runat="server" ResourceTitle="Product" />
							<ReboundUI:DataItemRow id="hidProductNo" runat="server" FieldType="Hidden" />
							<ReboundUI:DataItemRow id="ctlPackage" runat="server" ResourceTitle="Package" />
							<ReboundUI:DataItemRow id="hidPackageNo" runat="server" FieldType="Hidden" />						
							<ReboundUI:DataItemRow id="ctlReason" runat="server" ResourceTitle="Reason" />			
							<ReboundUI:DataItemRow id="ctlReason2" runat="server" ResourceTitle="Reason2" />
							<ReboundUI:DataItemRow id="ctlRootCause" runat="server" ResourceTitle="RootCause" />								
                            <ReboundUI:DataItemRow id="ctlIsAvoidable" runat="server" FieldType="CheckBox" ResourceTitle="IsAvoidable" />							
                            <ReboundUI:DataItemRow id="hidPrintHaza" runat="server" FieldType="Hidden" />
                            <ReboundUI:DataItemRow id="hidProductHazar" runat="server" FieldType="Hidden" />								
						</table>
					</td>
				</tr>
			</table>
		    <ReboundUI:FieldSet ID="fldAllocations" runat="server" FieldSetType="CRMAAllocations">
				<Title><%=Functions.GetGlobalResource("Misc", "Allocations")%></Title>
				<Buttons>
					<ReboundUI:IconButton ID="ibtnDeallocate" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="Deallocate" IsInitiallyEnabled="false" />
				</Buttons>
				<Content><ReboundUI:FlexiDataTable ID="tblAllocations" runat="server" AllowMultipleSelection="true" /></Content>
			</ReboundUI:FieldSet>
		    <ReboundUI:FieldSet ID="fldReceived" runat="server" FieldSetType="CRMAReceived">
				<Title><%=Functions.GetGlobalResource("Misc", "Received")%></Title>
				<Content><ReboundUI:FlexiDataTable ID="tblReceived" runat="server" AllowSelection="false" /></Content>
			</ReboundUI:FieldSet>
		</asp:Panel>
	</Content>
	
	<Forms>
		<ReboundForm:CRMALines_Add id="frmAdd" runat="server" />
		<ReboundForm:CRMALines_Edit id="frmEdit" runat="server" />
		<ReboundForm:CRMALines_Delete id="frmDelete" runat="server" />
		<ReboundForm:CRMALines_Deallocate id="frmDeallocate" runat="server" />
		<ReboundForm:CRMALines_Close id="frmClose" runat="server" />
	</Forms>
	
</ReboundUI_Nugget:DesignBase>
