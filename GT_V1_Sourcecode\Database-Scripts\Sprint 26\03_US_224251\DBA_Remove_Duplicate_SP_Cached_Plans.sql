/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-224251]		NgaiTo				17-Apr-2024		Create			GT SP Cache - Optimization Execution
===========================================================================================
*/

USE [msdb]
GO

BEGIN TRANSACTION
DECLARE @ReturnCode INT
SELECT @ReturnCode = 0

IF NOT EXISTS (SELECT name FROM msdb.dbo.syscategories WHERE name=N'[Uncategorized (Local)]' AND category_class=1)
BEGIN
EXEC @ReturnCode = msdb.dbo.sp_add_category @class=N'JOB', @type=N'LOCAL', @name=N'[Uncategorized (Local)]'
IF (@@ERROR <> 0 OR @ReturnCode <> 0) GOTO QuitWithRollback

END

DECLARE @jobId BINARY(16)
IF NOT EXISTS (SELECT job_id FROM msdb.dbo.sysjobs_view WHERE name = N'DBA_Remove_Duplicate_SP_Cached_Plans')
BEGIN
	EXEC @ReturnCode =  msdb.dbo.sp_add_job @job_name=N'DBA_Remove_Duplicate_SP_Cached_Plans', 
			@enabled=1, 
			@notify_level_eventlog=2, 
			@notify_level_email=0, 
			@notify_level_netsend=0, 
			@notify_level_page=0, 
			@delete_level=0, 
			@description=N'No description available.', 
			@category_name=N'[Uncategorized (Local)]', 
			@owner_login_name=N'sa', @job_id = @jobId OUTPUT
	IF (@@ERROR <> 0 OR @ReturnCode <> 0) GOTO QuitWithRollback

	EXEC @ReturnCode = msdb.dbo.sp_add_jobstep @job_id=@jobId, @step_name=N'Clear_Cache_SP', 
			@step_id=1, 
			@cmdexec_success_code=0, 
			@on_success_action=1, 
			@on_success_step_id=0, 
			@on_fail_action=2, 
			@on_fail_step_id=0, 
			@retry_attempts=0, 
			@retry_interval=0, 
			@os_run_priority=0, @subsystem=N'TSQL', 
			@command=N'DECLARE @object_id VARCHAR(256);
DECLARE @SP_Name VARCHAR(256);
DECLARE @PlanHandle VARBINARY(64);

DECLARE @dbcursor CURSOR SET @dbcursor = CURSOR
FOR
SELECT deps.object_id AS [object_id],
		deps.plan_handle AS PlanHandle,
		ISNull(OBJECT_NAME(deps.object_id), '''') AS SP_Name
FROM sys.dm_exec_procedure_stats AS deps
WHERE deps.object_id IN (
		SELECT deps.object_id AS [object_id]
		FROM sys.dm_exec_procedure_stats AS deps
		WHERE deps.database_id IN (
				SELECT database_id
				FROM sys.databases
				WHERE database_id > 4
				)
		GROUP BY deps.object_id,
			deps.database_id
		HAVING count(deps.plan_handle) > 1
		)

OPEN @dbcursor

FETCH NEXT
FROM @dbcursor
INTO @object_id,
	@PlanHandle,
	@SP_Name

WHILE @@FETCH_STATUS = 0
BEGIN
	PRINT ''Start clear '' + CONVERT(VARCHAR(MAX), @SP_Name);
	DBCC FREEPROCCACHE (@PlanHandle);
	FETCH NEXT
	FROM @dbcursor
	INTO @object_id,
		@PlanHandle,
		@SP_Name
END

CLOSE @dbcursor

DEALLOCATE @dbcursor
	', 
			@database_name=N'master', 
			@flags=0
	IF (@@ERROR <> 0 OR @ReturnCode <> 0) GOTO QuitWithRollback
	EXEC @ReturnCode = msdb.dbo.sp_update_job @job_id = @jobId, @start_step_id = 1
	IF (@@ERROR <> 0 OR @ReturnCode <> 0) GOTO QuitWithRollback
	EXEC @ReturnCode = msdb.dbo.sp_add_jobschedule @job_id=@jobId, @name=N'Schedule_Clear_Cache_SP', 
			@enabled=1, 
			@freq_type=16, 
			@freq_interval=1, 
			@freq_subday_type=1, 
			@freq_subday_interval=0, 
			@freq_relative_interval=0, 
			@freq_recurrence_factor=1, 
			@active_start_date=20250417, 
			@active_end_date=99991231, 
			@active_start_time=0, 
			@active_end_time=235959
	IF (@@ERROR <> 0 OR @ReturnCode <> 0) GOTO QuitWithRollback
	EXEC @ReturnCode = msdb.dbo.sp_add_jobserver @job_id = @jobId, @server_name = N'(local)'
END

IF (@@ERROR <> 0 OR @ReturnCode <> 0) GOTO QuitWithRollback
COMMIT TRANSACTION
GOTO EndSave
QuitWithRollback:
    IF (@@TRANCOUNT > 0) ROLLBACK TRANSACTION
EndSave:
GO


