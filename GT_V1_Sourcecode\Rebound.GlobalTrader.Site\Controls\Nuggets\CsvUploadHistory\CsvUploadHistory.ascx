<%@ Control Language="C#" CodeBehind="CsvUploadHistory.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Nuggets.CsvUploadHistory" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" BoxType="Standard" >

	
	<Content>
		<table class="twoCols">
			<tr>
				<td class="col3">
					<ReboundUI:FlexiDataTable ID="tblUploadHistory" runat="server" PanelHeight="150"  />
				</td>
			</tr>
		</table>
	</Content>
	
	
</ReboundUI_Nugget:DesignBase>
