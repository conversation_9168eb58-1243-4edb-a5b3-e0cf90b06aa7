<%--[001]      Vinay           14/06/2018    [REB-11304]: CHG-570795 Hazarders product type--%>
<%--[002]      Bhooma&Sunil    29/07/2021    To Edit The PO & GI Variance--%>
<%--[003]      Ravi            20-03-2023    [RP-968] Barcode dropdown box 
<%--[004]      Ravi            26-05-2023    [RP-162] Remove AutoSplit button from packaging split div --%>
<%@ Control Language="C#" CodeBehind="GILines_Edit.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.GILines_Edit" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<link href="css/jquery-ui.css.css" rel="stylesheet" />
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
<script src="js/jquery-ui.js"></script>
<%--<script src="css/jquery-ui.js" integrity="sha256-xI/qyl9vpwWFOXz7+x/9WkG5j/SVnSw21viy8fWwbeE=" crossorigin="anonymous"></script>--%>
<script src="js/GILine.js"></script>
<link href="https://cdnjs.cloudflare.com/ajax/libs/chosen/1.8.7/chosen.min.css" rel="stylesheet" />
<script src="https://cdnjs.cloudflare.com/ajax/libs/chosen/1.8.7/chosen.jquery.min.js" defer></script>
<link href ="Controls/Nuggets/GiLines/Edit/GILines_Edit.css" rel="stylesheet" type="text/css" />

<script>
    $(function () {
        $('.tabs-nav a').click(function () { // Check for active
            $('.tabs-nav li').removeClass('active');
            $(this).parent().addClass('active'); // Display active tab

            let currentTab = $(this).attr('href');
            $('#tab1').hide();
            $('#tab2').hide();
            $('#tab3').hide();
            $('#tab4').hide();
            $(currentTab).show(); return false;
        });
        $('#tab1').show();
        $('#tab2').hide();
        $('#tab3').hide();
        $('#tab4').hide();
    });
    // $(function(){
    //    $('#ddlCCUsers').multiSelect();
    //});
</script>
<script>
    //Try to get tbody first with jquery children. works faster!
    var tbody = $('#breakdowntable').children('tbody');

    //Then if no tbody just select your table 
    var table = tbody.length ? tbody : $('#breakdowntable');


    //$('button').click(function () {
    //    //Add row
    //    table.append('<tr><td><input type="checkbox" value=""></td><td><input type="text" value=""></td><td><input type="text" value=""></td><td><input type="text" value=""></td><td><input type="text" value=""></td></tr>');
    //})
</script>
<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="true" ShowQuickHelp="false">

    <Links>
        <div style="display: none">
            <ReboundUI:IconButton ID="ibtnSaveRelease" runat="server" IconGroup="Nugget" IconTitleResource="SaveAndRelease" IconCSSType="Save" IconButtonMode="HyperLink" Href="javascript:void(0);" IsInitiallyEnabled="false" Alignment="Left" />
        </div>
        <ReboundUI:IconButton ID="ibtnSave" runat="server" IconGroup="Nugget" IconTitleResource="SaveAndExit" IconCSSType="Save" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
        <ReboundUI:IconButton ID="ibtnSendQuery" runat="server" IconGroup="Nugget" IconTitleResource="SendQuery" IconCSSType="Save" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
        <ReboundUI:IconButton ID="ibtnCancel" runat="server" IconGroup="Nugget" IconTitleResource="Cancel" IconCSSType="Cancel" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />

    </Links>
    <Explanation>
    </Explanation>
    <Content>
        <ReboundUI_Table:Form ID="frmStep1" runat="server" class="partsreq">
            <asp:TableRow>
                <asp:TableCell RowSpan="2" Style="width: 100%">
                    <div class="container-4tab">
                        <div class="refstrip">
                            <div class="refstripcol">
                                <label><%=Functions.GetGlobalResource("FormFields", "GoodsInNumber")%> </label>
                                -<span><asp:Label ID="lblHeaderGoodsIn" runat="server" /></span>
                            </div>
                            <div class="refstripcol">
                                <label><%=Functions.GetGlobalResource("FormFields", "PartNo")%></label>
                                -<span><asp:Label ID="lblHeaderPartNo" runat="server" /></span>
                            </div>
                            <div class="refstripcol">
                                <label><%=Functions.GetGlobalResource("FormFields", "EnhancedInspection")%></label>
                                -<span class="redflag"><asp:Label ID="lblHeaderEnhancedInpection" runat="server" /></span>
                            </div>
                            <div class="refstripcol">
                                <label><%=Functions.GetGlobalResource("FormFields", "PONumber")%></label>
                                -<span><asp:Label ID="lblPONumber" runat="server" /></span>
                            </div>
                            <div class="refstripcol">
                                <label><%=Functions.GetGlobalResource("FormFields", "SONumber")%></label>
                                -<span><asp:Label ID="lblSONumber" runat="server" /></span>
                            </div>
                            <div class="refstripcol" style="border-right:none">
                                <label><%=Functions.GetGlobalResource("FormFields", "Manufacturer")%></label>
                                -<span><asp:Label ID="lblManufacturerhd" runat="server" /></span>
                            </div>
                        </div>
                        <header class="tabs-nav">
                            <ul>
                                <li class="active"><a href="#tab1" id="aGIScreenInfo">GI Screen Info</a></li>
                                <li><a href="#tab2" id="aQueryMessages">Query Messages</a></li>
                                <li><a href="#tab3" id="aApprovals">Approvals</a></li>
                                <li><a href="#tab4" id="aAttachments">Attachments</a></li>
                            </ul>
                        </header>

                        <section class="tabs-content">
                            <div id="tab1">
                                <h3>Receive Info</h3>
                                <div class="tab1data">
                                    <table>
                                        <tr>
                                            <td id="GoodsInUpdateTypeError">
                                                <label><%=Functions.GetGlobalResource("FormFields", "GoodsInUpdateType")%></label>
                                            </td>
                                            <td id="GoodsInUpdateTypeError1">
                                                <%--<ReboundDropDown:GoodsInUpdate ID="ddlUpdateType" runat="server" />--%>
                                                <asp:Label ID="lblUpdateType" runat="server" />
                                            </td>
                                            <td></td>
                                            <td></td>
                                        </tr>
                                       
                                        <tr>
                                            <td>
                                                <label><%=Functions.GetGlobalResource("FormFields", "SupplierType")%></label>

                                            </td>
                                            <td>
                                                <asp:Label ID="lblSupplierType" runat="server" /></td>
                                            <td></td>
                                            <td></td>
                                        </tr>
                                        <%--<tr>
                                            <td>
                                               <label><%=Functions.GetGlobalResource("FormFields", "AirWayBill")%></label>

                                            </td>
                                            <td>
                                               <asp:Label ID="lblAirWayBill" runat="server" />

                                            </td>
                                            <td></td>
                                            <td></td>
                                        </tr>--%>
                                        <%--<tr>
                                            <td>
                                               <label><%=Functions.GetGlobalResource("FormFields", "Reference")%></label>

                                            </td>
                                            <td>
                                                <asp:Label ID="lblReference" runat="server" />

                                            </td>
                                            <td></td>
                                            <td></td>
                                        </tr>--%>
                                        <tr>
                                            <td id="LocationError">
                                                <label><%=Functions.GetGlobalResource("FormFields", "Location")%><span> * </span></label>
                                            </td>
                                            <td id="LocationError1">
                                                <ReboundUI:ReboundTextBox ID="txtLocation" runat="server" Width="150" MaxLength="10" UppercaseOnly="true" />
                                            </td>
                                            <td></td>
                                            <td></td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <label><%=Functions.GetGlobalResource("FormFields", "Lot")%></label></td>
                                            <td>
                                                <ReboundDropDown:Lot ID="ddlLot" runat="server" />
                                            </td>
                                            <td></td>
                                            <td></td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <label><%=Functions.GetGlobalResource("FormFields", "InsToQualityControlNotes")%></label></td>
                                            <td>
                                                <ReboundUI:ReboundTextBox ID="txtQualityControlNotes" runat="server" Width="350" TextMode="MultiLine" Rows="3" Enabled="false" ReadOnly="true" /></td>
                                            <td></td>
                                            <td></td>
                                        </tr>
                                    </table>
                                </div>
                                <h3>Main Inspection</h3>
                                <div class="tab2data">
                                    <table>
                                        <tr>
                                            <th colspan="2">Customer PO</th>
                                            <th colspan="6">Goods In</th>
                                        </tr>
                                        <tr>
                                            <td>
                                                <label><%=Functions.GetGlobalResource("FormFields", "PartNumber")%></label></td>
                                            <td>
                                                <asp:Label ID="lblPartNo" runat="server" /></td>
                                            <td>
                                                <label><%=Functions.GetGlobalResource("FormFields", "IsPartNoCorrect")%></label></td>
                                            <td>
                                                <ReboundUI:ImageCheckBox ID="chkPartNumberCorrect" runat="server" Enabled="true" />
                                            </td>
                                            <td id="CorrectPartNoError">
                                                <label><%=Functions.GetGlobalResource("FormFields", "RecvCorrectPartNo")%></label>
                                            </td>
                                            <td id="CorrectPartNoError1">
                                                <ReboundUI:ReboundTextBox ID="txtCorrectPartNo" runat="server" Width="200" UppercaseOnly="true" />
                                            </td>
                                            <td style="border-left: 1px #2E6D38 solid;">
                                              <label style="Font-weight: 600;"><%=Functions.GetGlobalResource("FormFields", "CorrectPartNo")%></label>
                                            </td>
                                            <td>
                                             <ReboundUI:ReboundTextBox ID="txtQueryPartNo" runat="server" MaxLength="150" Width="150" TextMode="multiLine" Rows="3" CssClass=".QueryBox"/>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <label><%=Functions.GetGlobalResource("FormFields", "Manufacturer")%></label></td>
                                            <td>
                                                <asp:Label ID="lblManufacturer" runat="server" /></td>
                                            <td>
                                                <label><%=Functions.GetGlobalResource("FormFields", "IsManufacturerCorrect")%></label></td>
                                            <td>
                                                <ReboundUI:ImageCheckBox ID="chkManufacturerCorrect" runat="server" Enabled="true" />
                                            </td>
                                            <td>
                                                <label><%=Functions.GetGlobalResource("FormFields", "RecvCorrectManufacturer")%></label></td>
                                            <td>
                                                <ReboundUI:ComboNew ID="cmbManufacturer" runat="server" AutoSearchControlAssembly="Rebound.GlobalTrader.Site" AutoSearchControlType="Manufacturers" ParentControlID="cmbManufacturer" />
                                            </td>
                                            <td style="border-left: 1px #2E6D38 solid;">
                                              <label style="Font-weight: 600;"><%=Functions.GetGlobalResource("FormFields", "CorrectManufacturer")%></label>
                                            </td>
                                            <td>
                                             <ReboundUI:ReboundTextBox ID="txtQueryManfacturer" runat="server" Width="150" MaxLength="150" TextMode="multiLine" Rows="3" CssClass=".QueryBox"/>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <label><%=Functions.GetGlobalResource("FormFields", "PackagingType")%></label></td>
                                            <td>
                                                <asp:Label ID="lblPackage" runat="server" /></td>
                                            <td>
                                                <label><%=Functions.GetGlobalResource("FormFields", "IsPackageCorrect")%></label></td>
                                            <td>
                                                <ReboundUI:ImageCheckBox ID="chkPackageCorrect" runat="server" Enabled="true" />
                                            </td>
                                            <td>
                                                <label><%=Functions.GetGlobalResource("FormFields", "RecvCorrectPackage")%></label></td>
                                            <td>
                                                <ReboundUI:ComboNew ID="cmbPackage" runat="server" AutoSearchControlAssembly="Rebound.GlobalTrader.Site" AutoSearchControlType="Packages" ParentControlID="cmbPackage" />
                                            </td>
                                             <td style="border-left: 1px #2E6D38 solid;">
                                              <label style="Font-weight: 600;"><%=Functions.GetGlobalResource("FormFields", "CorrectPackage")%></label>
                                            </td>
                                            <td>
                                             <ReboundUI:ReboundTextBox ID="txtPackageTypeQuery" runat="server" MaxLength="150" Width="150" TextMode="multiLine" Rows="3" CssClass=".QueryBox"/>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <label><%=Functions.GetGlobalResource("FormFields", "MSLLevel")%></label></td>
                                            <td>
                                                <asp:Label ID="lblMSL" runat="server" /></td>
                                            <td>
                                                <label><%=Functions.GetGlobalResource("FormFields", "IsMSLCorrect")%></label></td>
                                            <td>
                                                <ReboundUI:ImageCheckBox ID="chkMSLCorrect" runat="server" Enabled="true" />
                                            </td>
                                            <td id="CorrectMSLError">
                                                <label><%=Functions.GetGlobalResource("FormFields", "RecvCorrectMSL")%></label>
                                            </td>
                                            <td id="CorrectMSLError1">
                                                <ReboundDropDown:MSLLevel ID="ddlMsl" runat="server" />
                                            </td>
                                            <td style="border-left: 1px #2E6D38 solid;">
                                              <label style="Font-weight: 600;"><%=Functions.GetGlobalResource("FormFields", "CorrectMSL")%></label>
                                            </td>
                                            <td>
                                             <ReboundUI:ReboundTextBox ID="txtMslQuery" runat="server" MaxLength="150" Width="150" TextMode="multiLine" Rows="3" CssClass=".QueryBox"/>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <label></label>
                                            </td>
                                            <td>
                                                <label></label>
                                            </td>
                                            <td>
                                                <label><%=Functions.GetGlobalResource("FormFields", "HICStatus")%></label></td>
                                            <td style="padding-right: 30px;">
                                                <ReboundDropDown:HICStatus ID="HICStatus" runat="server" />
                                            </td>
                                            <td></td>
                                            <td></td>
                                            <td style="border-left: 1px #2E6D38 solid;">
                                                <label style="Font-weight: 600;"><%=Functions.GetGlobalResource("FormFields", "CorrectHIC")%></label></td>
                                            <td>
                                                <%-- <ReboundUI:ReboundTextBox ID="txtCorrectHIC" runat="server" Width="200" />--%>
                                                <ReboundDropDown:QueryHICStatus ID="QueryHICStatus" runat="server" />
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <label><%=Functions.GetGlobalResource("FormFields", "RohsStatusGI")%></label></td>
                                            <td>
                                                <asp:Label ID="lblRohsStatus" runat="server" /></td>
                                            <td>
                                                <label><%=Functions.GetGlobalResource("FormFields", "IsRohsStatusCorrect")%></label></td>
                                            <td>
                                                <ReboundUI:ImageCheckBox ID="chkRohsStatusCorrect" runat="server" Enabled="true" />
                                            </td>
                                            <td>
                                                <label><%=Functions.GetGlobalResource("FormFields", "RecvCorrectRohsStatus")%></label></td>
                                            <td>
                                                <ReboundDropDown:ROHSStatus ID="ddlROHSStatus" runat="server" IncludeNoValue="true" NoValue_Value="" InitialValue="" />
                                            </td>
                                            <td style="border-left: 1px #2E6D38 solid;">
                                              <label style="Font-weight: 600;"><%=Functions.GetGlobalResource("FormFields", "CorrectRohsStatus")%></label>
                                            </td>
                                            <td>
                                             <ReboundUI:ReboundTextBox ID="txtRohsQuery" runat="server" MaxLength="150" Width="150" TextMode="multiLine" Rows="3" CssClass=".QueryBox"/>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td id="QuantityError">
                                                <label><%=Functions.GetGlobalResource("FormFields", "Quantity")%><span> * </span></label>
                                            </td>
                                            <%--<td>
                                                <asp:Label ID="lblQuantityOrdered" runat="server" /></td>--%>
                                            <td id="QuantityError1">
                                                <ReboundUI:ReboundTextBox ID="txtQuantity" runat="server" Width="150" TextBoxMode="Numeric" Style="width: 200px" />
                                            <td></td>
                                            <td></td>
                                            <td></td>
                                            <td></td>
                                            <td></td>
                                            <td></td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <label><%=Functions.GetGlobalResource("FormFields", "DateCode")%></label></td>
                                            <td>
                                                <asp:Label ID="lblDateCode" runat="server" />
                                            </td>
                                            <td>
                                                <label><%=Functions.GetGlobalResource("FormFields", "PrintDateCode")%></label>

                                            </td>
                                            <td>
                                                <ReboundUI:ReboundTextBox ID="txtPrintDateCode" runat="server" Width="150" Style="width: 200px" MaxLength="5" UppercaseOnly="true" />
                                            </td>
                                            <td></td>
                                            <td></td>
                                            <td></td>
                                            <td></td>
                                        </tr>
                                        <td>
                                            <label><%=Functions.GetGlobalResource("FormFields", "PackagingBreakdown")%></label></td>
                                        <td colspan="5" class="threecol">
                                            <table class="insidetable1">
                                                <!-- [004] start -->
                                                <%--<tr>
                                                    <th colspan="2" style="text-align: left;">
                                                        <div style="margin-right: 375px;">
                                                            <button id="btnAutoSplit" class="btnAutoSplit" onclick="return false">Auto Split</button>
                                                        </div>
                                                    </th>
                                                    <th colspan="2" style="text-align: right;">
                                                        <div class="totalq">
                                                            <%=Functions.GetGlobalResource("FormFields", "PackagingTotal")%>
                                                            <asp:Label ID="lblPackageBreakTotal" Text="0" runat="server" />
                                                        </div>
                                                    </th>
                                                </tr>--%>

                                                <!-- Commented above code and applied below code. to rollback, remove below <tr> tag and its conntent 
                                                    and un-comment above lies -->
                                                <tr>
                                                    <th colspan="4" style="text-align: right;">
                                                        <div class="totalq">
                                                            <%=Functions.GetGlobalResource("FormFields", "PackagingTotal")%>
                                                            <asp:Label ID="lblPackageBreakTotal" Text="0" runat="server" />
                                                        </div>
                                                    </th>
                                                </tr>
                                                <!-- [004] close -->

                                            </table>
                                            <div class="tablescroll1" style="background-color: #68ba92; width: 750px;">
                                                <table id="BreakdownTable" style="width: 750px;">
                                                    <thead>
                                                        <tr>
                                                            <th style="width: 56px; background-color: #016660;"><%=Functions.GetGlobalResource("FormFields", "Factory")%><br>
                                                                <%=Functions.GetGlobalResource("FormFields", "Sealed")%></th>
                                                            <th style="background-color: #016660;"><%=Functions.GetGlobalResource("FormFields", "NumberOfPacks")%></th>
                                                            <th style="background-color: #016660;"><%=Functions.GetGlobalResource("FormFields", "PackSize")%></th>
                                                            <th style="background-color: #016660;"><%=Functions.GetGlobalResource("FormFields", "DateCode")%></th>
                                                            <th style="background-color: #016660;"><%=Functions.GetGlobalResource("FormFields", "PackagingType")%></th>
                                                            <th style="background-color: #016660;"><%=Functions.GetGlobalResource("FormFields", "BatchCode")%></th>
                                                            <th style="background-color: #016660;"><%=Functions.GetGlobalResource("FormFields", "MFRLabel")%></th>
                                                            <th style="background-color: #016660;"><%=Functions.GetGlobalResource("FormFields", "Total")%></th>
                                                            <th style="background-color: #016660;"></th>
                                                        </tr>
                                                    </thead>
                                                    <tbody id="tbBreakdownTable" class="data-Breakdown-GI">
                                                    </tbody>

                                                </table>
                                            </div>
                                            <div class="addpackbtn" id="dvAddPack" style="width: 748px">
                                                <button id="btnAddPack" onclick="return false">Add Pack</button>
                                            </div>
                                        </td>

                                        <td colspan="2">
                                            <div style="display: flex; flex-direction: row; justify-content: center; align-items: center">
                                                <label style="display: none"><%=Functions.GetGlobalResource("FormFields", "QueryQuantity")%></label>
                                                <asp:Label ID="lblQuantity" Style="color: Red; background: Yellow; width: 256%;" runat="server" Visible="false" />
                                            </div>
                                            <asp:TableCell class="lableTD" ID="tcQuantity">
                                                <%--<label><%=Functions.GetGlobalResource("FormFields", "QueryDateCode")%></label>--%>
                                               <%-- <ReboundUI:ReboundTextBox ID="txtCorrectDateCode" runat="server" Style="margin-left: 111px; margin-top: -19px;" Width="200" />--%>
                                            </asp:TableCell>

                                            <%--<label><%=Functions.GetGlobalResource("FormFields", "PackageBreakdownInfo")%></label>--%>
                                            <%--<asp:Label ID="lblPackageBreakdownInfo" Style="color: Red; background: Yellow; width: 62%;" runat="server" />--%>
                                            <%--<ReboundUI:ReboundTextBox ID="txtPackageBreakdownInfo" runat="server" Width="200" Style="width: 200px; margin-top: -19px; margin-left: 111px;" />--%>
                                            <%--<ReboundUI:ReboundTextBox ID="txtQuantity" runat="server" Width="150" TextBoxMode="Numeric" Style="width: 200px" Visible="false" />--%>
                                        </td>
                                        </tr>
			<tr>
                <td>
                    <%--<label><%=Functions.GetGlobalResource("FormFields", "IsFullQuantityReceived")%></label>--%>
                    <label><%=Functions.GetGlobalResource("FormFields", "ActeoneTest")%></label>

                </td>
                <td class="onerowcheckbox" colspan="3">
                    <%--<ReboundUI:ImageCheckBox ID="chkFullQuantityReceived" runat="server" Enabled="true" />--%>
                    <label><%=Functions.GetGlobalResource("FormFields", "NA")%></label>
                    <ReboundUI:ImageCheckBox ID="chkActeoneTestNA" runat="server" Enabled="true" />
                    <label><%=Functions.GetGlobalResource("FormFields", "Pass")%></label>
                    <ReboundUI:ImageCheckBox ID="chkActeoneTestPass" runat="server" Enabled="true" />
                    <label><%=Functions.GetGlobalResource("FormFields", "Fail")%></label>
                    <ReboundUI:ImageCheckBox ID="chkActeoneTestFail" runat="server" Enabled="true" />
                    <ReboundUI:ReboundTextBox ID="txtActeoneTest" runat="server" Width="150" MaxLength="150" TextMode="multiLine" Rows="3" />
                </td>
                <td>
                    <label><%=Functions.GetGlobalResource("FormFields", "Isopropryle")%></label>

                </td>
                <td class="onerowcheckbox" colspan="3">
                    <label><%=Functions.GetGlobalResource("FormFields", "NA")%></label>
                    <ReboundUI:ImageCheckBox ID="chkIsopropryleNA" runat="server" Enabled="true" />
                    <label><%=Functions.GetGlobalResource("FormFields", "Pass")%></label>
                    <ReboundUI:ImageCheckBox ID="chkIsoproprylePass" runat="server" Enabled="true" />
                    <label><%=Functions.GetGlobalResource("FormFields", "Fail")%></label>
                    <ReboundUI:ImageCheckBox ID="chkIsopropryleFail" runat="server" Enabled="true" />
                    <ReboundUI:ReboundTextBox ID="txtIsopropryle" runat="server" Width="150" MaxLength="150" TextMode="multiLine" Rows="3" />

                </td>
            </tr>
                                        <tr>
                                            <td>
                                                <label><%=Functions.GetGlobalResource("FormFields", "BakingLevelAdded")%></label></td>
                                            <td colspan="5">
                                                <span class="inlinelable">
                                                    <ReboundUI:ImageCheckBox ID="chkbakingYes" runat="server" Enabled="true" />
                                                    <label for="html">Yes</label></span>
                                                <span class="inlinelable">
                                                    <ReboundUI:ImageCheckBox ID="chkbakingNo" runat="server" Enabled="true" />
                                                    <label for="html">No</label></span>
                                                <span class="inlinelable">
                                                    <ReboundUI:ImageCheckBox ID="chkbakingNA" runat="server" Enabled="true" />
                                                    <label for="html">NA</label></span>
                                            </td>
                                            <td>
                                                <label><%=Functions.GetGlobalResource("FormFields", "QueryBakingLevel")%></label>
                                            </td>
                                            <td>
                                                <ReboundUI:ReboundTextBox ID="txtQueryBakingLevel" runat="server" Width="150" />
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <label><%=Functions.GetGlobalResource("FormFields", "countryOforigin")%></label></td>
                                            <td>
                                                <%--<ReboundDropDown:GlobalCountryList ID="ddlCountryOfManufacture" runat="server" />--%>
                                                <ReboundUI:ComboNew ID="cmbCountryOfManufacture" runat="server" AutoSearchControlAssembly="Rebound.GlobalTrader.Site" PanelWidth="350" PanelHeight="250" AutoSearchControlType="AllCountries" ParentControlID="cmbAllCountries" />
                                            </td>
                                            <td></td>
                                            <td></td>
                                            <td></td>
                                            <td colspan="3"></td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <label><%=Functions.GetGlobalResource("FormFields", "CountingMethod")%></label></td>
                                            <td>
                                                <ReboundDropDown:CountingMethod ID="ddlCountingMethod" runat="server" />
                                            </td>
                                            <td></td>
                                            <td></td>
                                            <td></td>
                                            <td colspan="3"></td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <label><%=Functions.GetGlobalResource("FormFields", "ReqSerailNo")%></label></td>
                                            <td colspan="7">
                                                <ReboundUI:ImageCheckBox ID="chkReqSerailNo" runat="server" Enabled="true" />
                                            </td>

                                        </tr>
                                        <tr>
                                            <td>
                                                <label><%=Functions.GetGlobalResource("FormFields", "LotCodeReq")%></label></td>
                                            <td colspan="7">
                                                <ReboundUI:ImageCheckBox ID="chkLotCodeReq" runat="server" Enabled="true" />
                                            </td>

                                        </tr>
                                        <tr>
                                            <td>
                                                <label><%=Functions.GetGlobalResource("FormFields", "EnhancedInpectionRequired")%></label></td>
                                            <td colspan="7">
                                                <%--<ReboundUI:ImageCheckBox ID="chkEnhancedInpectionRequired" runat="server" Enabled="true" />--%>
                                                <ReboundDropDown:EnhancedInspection ID="ddlEnhancedInspection" runat="server" />
                                            </td>

                                        </tr>
                                        <%-- [003] start --%>
                                       
                                        <tr id="barcodeRemarError">
                                            <td colspan="1">
                                                <label><%=Functions.GetGlobalResource("FormFields", "GiBarcodeScan")%>
                                                    
                                                </label>
                                            </td>
                                            <td colspan="1" >
                                                <ReboundDropDown:GILineBarcodesStatus ID="ddlGILineBarcodesStatus" runat="server" Width="200px" />
                                            </td>
                                            <td colspan="1">
                                                <label style="display:inline; text-align:center">Remarks
                                                <span id="BarcodeMandatory"> * </span></label>
                                            </td>
                                            <td colspan="5">
                                                <ReboundUI:ReboundTextBox ID="txtBarcodeRemarks" runat="server" Width="350" MaxLength="1000" TextMode="multiLine" Rows="3" CssClass=".textArea" />
                                                 
                                            </td>
                                            
                                                
                                            
                                        </tr>
                                        <%-- [003] end --%>
                                        <tr>
                                            <td>
                                                <label><%=Functions.GetGlobalResource("FormFields", "GeneralInspectionNotes")%></label></td>
                                            <td colspan="3">
                                                <ReboundUI:ReboundTextBox ID="txtGeneralInspectionNotes" runat="server" Width="350" MaxLength="1500" TextMode="multiLine" Rows="3" />

                                            </td>
                                            <td colspan="4">
                                                <ReboundUI:ImageCheckBox ID="ChkGeneralInspectionNote" runat="server" Enabled="true" />
                                                <label style="margin-top: -15px;margin-left: 20px;">Raise General Inspection Query</label>
                                            </td>

                                        </tr>

                                        <tr>
                                            <td colspan="4" id="EnhancedInspectiontd1">
                                                <label><%=Functions.GetGlobalResource("FormFields", "IsInspectionConducted")%> *</label>&nbsp; &nbsp; &nbsp;&nbsp;
                                            </td>
                                            <td colspan="4" id="EnhancedInspectiontd2">
                                                <ReboundUI:ImageCheckBox ID="chkInspectionConducted" runat="server" Enabled="true" />
                                            </td>

                                        </tr>
                                    </table>
                                </div>
                                <h3>Other Info</h3>
                                <div class="tab3data">
                                    <table>

                                        <%--  <tr>
                                            <td>
                                                <label><%=Functions.GetGlobalResource("FormFields", "ReceivingNotes")%></label></td>
                                            <td>
                                                <asp:Label ID="lblReceivingNotes" runat="server" /></td>

                                        </tr>--%>
                                        <tr>
                                            <td>
                                                <label><%=Functions.GetGlobalResource("FormFields", "SupplierPartNo")%></label></td>
                                            <td>
                                                <ReboundUI:ReboundTextBox ID="txtSupplierPart" runat="server" Width="150" UppercaseOnly="true" Enabled="false" /></td>

                                        </tr>
                                        <tr>
                                            <td id="ProductsError">
                                                <label><%=Functions.GetGlobalResource("FormFields", "Product")%><span> * </span></label>
                                            </td>
                                            <td id="ProductsError1">
                                                <ReboundUI:ComboNew ID="cmbProducts" runat="server" AutoSearchControlAssembly="Rebound.GlobalTrader.Site" PanelWidth="350" PanelHeight="250" AutoSearchControlType="Products" ParentControlID="cmbProducts" />
                                            </td>

                                        </tr>
                                        <tr>
                                            <td>
                                                <label><%=Functions.GetGlobalResource("FormFields", "PurchasePrice")%></label></td>
                                            <td>
                                                <ReboundUI:ReboundTextBox ID="txtPrice" runat="server" TextBoxMode="currency" FormatDecimalPlaces="true" Width="100" />
                                                <asp:Label ID="lblCurrency_Price" runat="server" />
                                                <ReboundUI:ReboundTextBox ID="txtPrice_IPO" runat="server" TextBoxMode="currency" FormatDecimalPlaces="true" Width="100" />
                                                <asp:Label ID="lblCurrency_Price_IPO" runat="server" />
                                                <img height="15px" width="15px" id="imgPriceinfo" title="Please note the landed cost for this GI is not editable as it is a stock transfer from DMCC. The landed cost is locked and set by the Dubai client." style="margin-left: 14%;margin-top: -2%;" src="../../../App_Themes/Original/images/hazardous/ihspartstatuspng.png">
                                                <asp:Label ID="lblPrice" runat="server" />
                                                <asp:Label ID="lblCurrency_PriceLabel" runat="server" />
                                                <asp:Label ID="lblPrice_IPO" runat="server" />
                                                <asp:Label ID="lblCurrency_PriceLabel_IPO" runat="server" /></td>

                                        </tr>
                                        <tr>
                                            <td id="ShipInCostError">
                                                <label><%=Functions.GetGlobalResource("FormFields", "ShipInCost")%><span> * </span></label>
                                            </td>
                                            <td id="ShipInCostError1">
                                                <ReboundUI:ReboundTextBox ID="txtShipInCost" runat="server" TextBoxMode="currency" FormatDecimalPlaces="true" Width="100" />
                                                <asp:Label ID="lblShipInCost" runat="server" />
                                                <%=Rebound.GlobalTrader.Site.SessionManager.ClientBaseCurrencyCode%>
                                            </td>

                                        </tr>
                                        <tr>
                                            <td>
                                                <label><%=Functions.GetGlobalResource("FormFields", "BatchReference")%></label></td>
                                            <td>
                                                <ReboundUI:ReboundTextBox ID="txtPartMarkings" runat="server" Width="200" /></td>

                                        </tr>
                                        <tr>
                                            <td>
                                                <label><%=Functions.GetGlobalResource("FormFields", "Notes")%></label></td>
                                            <td>
                                                <ReboundUI:ReboundTextBox ID="txtAccountNotes" runat="server" Width="350" MaxLength="200" TextMode="multiLine" Rows="3" /></td>

                                        </tr>
                                    </table>
                                </div>
                            </div>
                            <div id="tab2">

                                <div class="discussionbox" id="IsInitailMessage">
                                </div>
                                <div class="discussionbox" id="IsApprovalMessage">
                                </div>
                                <div id="RequestApprovalModel" class="modal">
                                    <!-- Modal content -->
                                    <div class="modal-content">
                                        <span class="close">&times;</span>
                                        <div class="replytoselect">
                                            <h4>
                                                <label><%=Functions.GetGlobalResource("FormFields", "QMSendTO")%></label></h4>
                                            <table>
                                                <tr>
                                                    <td style="width:89px"><label style="font-weight:bold">Sales Approver:</label></td>
                                                    <td id="tdSApproverName"><label id="lblSapproverName"></label></td>
                                                    <td style="padding-left: 10px;width: 122px;"><label style="font-weight:bold">Purchasing Approver:</label></td>
                                                    <td id="tdPApproverName"><label id="lblPapproverName"></label></td>
                                                    <td style="padding-left: 10px;width: 100px;"><label style="font-weight: bold">Quality Approver:</label></td>
                                                    <td id="tdQApproverName"><label id="lblQapproverName"></label></td>
                                                </tr>
                                            </table>
                                            <table class="mailoption">
                                                
                                                <tr>

                                                    <td style="width: 40px;">
                                                        <label><%=Functions.GetGlobalResource("FormFields", "EmailToSales")%></label>
                                                    </td>
                                                    <td style="width: 24px;">
                                                        <ReboundUI:ImageCheckBox ID="chkSales" runat="server" Enabled="true" />
                                                    </td>
                                                    <td style="width: 10px;">
                                                        <label><%=Functions.GetGlobalResource("FormFields", "EmailToPurchasing")%></label>
                                                    </td>
                                                    <td style="width: 10px;">
                                                        <ReboundUI:ImageCheckBox ID="chkPurchasing" runat="server" Enabled="true" />
                                                    </td>
                                                    <td style="width: 90px;">
                                                        <label><%=Functions.GetGlobalResource("FormFields", "EmailToQualityApproval")%>s</label>
                                                    </td>
                                                    <td>
                                                        <ReboundUI:ImageCheckBox ID="chkQualityApproval" runat="server" Enabled="true" />
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td colspan="6">
                                                        <ReboundFormFieldCollection:AddCCUsersRequest ID="ddlCCUsersRequest" runat="server" />
                                                    </td>
                                                </tr>

                                            </table>

                                        </div>

                                        <div class="commentarea" style="float: none">
                                            <ReboundUI:ReboundTextBox ID="TxtMessageBox" runat="server" Width="45%" TextMode="MultiLine" Rows="4" placeholder="Type Warehouse remark here.." />

                                            <%--<div style="float: right;">--%>
                                            <span class="postbtn" style="margin-top: -58px;">
                                                <button id="btnSendQuery" onclick="return false" title="Send">
                                                    <img src="../../../../images/sendbtn-old.png">
                                                    <label style="font-weight: bold;color: #006800;font-size: 11px;">Send Query</label>
                                                </button>

                                            </span>
                                            <span class="postbtn" style="margin-top: -53px;margin-right: 22px;">
                                                <button id="btnDraftQueryMessage" onclick="return false" title="Draft">
                                                    <img src="../../../../images/DraftIcon.png" style="padding-top: 7px;">
                                                    <label style="font-weight: bold;color: #006800;font-size: 11px;">Save as Draft</label>
                                                </button>
                                            </span>
                                            <%--</div>--%>
                                        </div>
                                    </div>
                                </div>
                                <div id="ResponceApprovalModel" class="modal">
                                    <!-- Modal content -->
                                    <div class="modal-content">
                                        <span class="close">&times;</span>
                                        <div class="replytoselect">
                                            <h4>Please provide your approval for Warehouse to release the Line</h4>
                                            <label id="lblPermissionMessage" style="font-size: 16px; color: #FFFFFF; margin-left: 5px; background: red;"></label>
                                            <label style="padding-left: 89px;font-weight: bold;font-style: italic;margin-top:15px">Please check the tickbox to validate the information.</label>
                                            <p id="QueryMessageP" style="width: 913px;margin-left:-27px;"></p>
                                            <table style="padding-top: 10px;">
                                                <tr>
                                                    <td id="tdsalesLbl" style="width: 90px;"><b>
                                                        <label><%=Functions.GetGlobalResource("FormFields", "QMIsSalesApproved")%></label></b></td>
                                                    <td id="tdsalesCheck" style="width: 100px;">
                                                        <ReboundDropDown:GILineQueryStatus ID="ddlSalesApproved" runat="server" />
                                                    </td>
                                                    <td id="tdQualityLbl" style="width: 100px;"><b>
                                                        <label><%=Functions.GetGlobalResource("FormFields", "QMIsQualityApproved")%></label></b></td>
                                                    <td id="tdQualityCheck" style="width: 100px;">
                                                        <ReboundDropDown:GILineQueryStatus ID="ddlQualityApproved" runat="server"/>
                                                    </td>
                                                    <td id="tdPurchasingLbl" style="width: 120px;"><b>
                                                        <label><%=Functions.GetGlobalResource("FormFields", "QMIsPurchassingApproved")%></label></b></td>
                                                    <td id="tdPurchasingCheck">
                                                        <ReboundDropDown:GILineQueryStatus ID="ddlPurchasingApproved" runat="server" />
                                                    </td>

                                                </tr>
                                                <tr>
                                                    <td id="tdCCLbl" colspan="6">
                                                        <ReboundFormFieldCollection:AddCCUsers ID="ddlCCUsers" runat="server" />
                                                    </td>
                                                </tr>
                                            </table>

                                        </div>
                                        <div class="commentarea">
                                            <ReboundUI:ReboundTextBox ID="txtResponceBox" runat="server" Width="45%" TextMode="MultiLine" Rows="3" placeholder="Type your message here.." />
                                            <span class="postbtn" style="margin-top: -43px;">
                                                <button id="btnSendResponce" onclick="return false" title="Send">
                                                    <img src="../../../../images/sendbtn-old.png">
                                                    <label style="font-weight: bold;color: #006800;font-size: 11px;">Send Query</label>
                                                </button>
                                                
                                            </span>
                                            <span class="postbtn" style="margin-top: -39px;margin-right: 22px;">

                                                <button id="btnResponseDraftQueryMessage" onclick="return false" title="Draft">
                                                    <img src="../../../../images/DraftIcon.png" style="padding-top: 7px;">
                                                    <label style="font-weight: bold;color: #006800;font-size: 11px;">Save as Draft</label>
                                                </button>
                                            </span>
                                        </div>


                                    </div>

                                </div>

                            </div>
                            <div id="tab3">
                                <span class="approverdiv">
                                    <table class="approverblock">
                                        <tr>
                                            <td></td>
                                            <td>
                                                <img src="images/profilepic2.png"><br>
                                                <span>Sales</span></td>
                                            <td>
                                                <img src="images/profilepic2.png"><br>
                                                <span>Purchasing</span></td>
                                            <td>
                                                <img src="images/profilepic2.png"><br>
                                                <span>Quality</span></td>
                                        </tr>
                                        <tr class="even">
                                            <td>
                                                <label><%=Functions.GetGlobalResource("FormFields", "CurrentApprover")%></label></td>
                                            <td>
                                                <label id="lblSalesUser"></label>
                                            </td>
                                            <td>
                                                <label id="lblPurchasingUser"></label>
                                            </td>
                                            <td>
                                                <label id="lblQualityUser"></label>
                                            </td>
                                        </tr>
                                        <tr class="odd">
                                            <td>
                                                <label><%=Functions.GetGlobalResource("FormFields", "QMStatus")%></label></td>
                                            <td>
                                                <label id="lblSalesStatus"></label>
                                            </td>
                                            <td>
                                                <label id="lblPurchasingStatus"></label>
                                            </td>
                                            <td>
                                                <label id="lblQualityStatus"></label>
                                            </td>
                                        </tr>
                                        <tr class="">
                                            <td colspan="4">
                                                <span id="spnApprovalsChange" class="ManageApprovers" style="padding-right: 8px">
                                                    <a id="btnApprovalsChange">Manage Approvers<img src='../../../../images/ConfigureGi.png'></a>
                                                </span>
                                            </td>
                                        </tr>
                                    </table>
                                </span>
                                <table class="approvaltable">
                                    <tr class="headrow">
                                        <th><%=Functions.GetGlobalResource("FormFields", "QMRaisedBy")%></th>
                                        <th><%=Functions.GetGlobalResource("FormFields", "QMApprovalName")%></th>
                                        <th><%=Functions.GetGlobalResource("FormFields", "QMApprovedDate")%></th>
                                        <th><%=Functions.GetGlobalResource("FormFields", "QMStatus")%></th>
                                    </tr>
                                    <tbody id="ApprovalBody">
                                    </tbody>
                                </table>
                            </div>
                            <div id="tab4">
                                <style>
      .documentsizeGI{display: flex;
    left: 320px;
    position: absolute;
    margin-top: -3px;
    color: #000;
    font-family:'Lucida Sans', 'Lucida Sans Regular', 'Lucida Grande', 'Lucida Sans Unicode', Geneva, Verdana, sans-serif;
    font-size:12px !important;
      }

      #DocumentFileSizeGIPDF, #DocumentFileSizeGIImage{margin:0px 0px 0px 5px;}
      .bulkdlt_1{ margin-left:0px !important; float:right !important;}

			</style>
                                <asp:TableRow>
                                    <span class="selectoptions">
                                        <select id="ddlUpload" onchange="ChangeUploadControl()" class="typeselect">
                                            <option value="1">PDF Documents</option>
                                            <option value="2">Images</option>
                                        </select>
                                        <div class="documentsizeGI" ><%=Functions.GetGlobalResource("FormFields", "MaxDocumentSize")%><span id="DocumentFileSizeGIPDF"></span></div>
                                        <span class="uploadcontrol" id="uploadPdf">
                                             
                                      
                                            <div id="Imagesingleupload11">Upload</div>

                                           
                                            <script type="text/javascript">
                                                $(document).ready(function () {
                                                    var PDFFileSize;
                                                    var SectionMod;
                                                    $.ajax({
                                                        processData: false,
                                                        contentType: 'application/json',
                                                        type: 'POST',
                                                        async: false,
                                                        url: "DocImage.ashx?type=DOCUMENTFILESIZE&DocumentType=1",
                                                        success: function (data) {
                                                            var obj = JSON.parse(data);
                                                            PDFFileSize = obj.PDFFileSize;
                                                            PDFFileSizeMB = obj.PDFFileSizeMB;
                                                            $("#DocumentFileSizeGIPDF").text('(' + PDFFileSizeMB + ' MB)');
                                                            PDFDragDrop(PDFFileSize);
                                                        },
                                                        error: function () {
                                                            alert("The system has encountered a network connectivity issue. Please check your connectivity and try again.");

                                                        },
                                                    });

                                                    function PDFDragDrop(PDFFileSize) {
                                                        var dragdropObj = $("#Imagesingleupload11").uploadFile({
                                                            url: "DocImage.ashx?mxs=1&type=GOODSINLINESPDF&docId=101&IsDragDrop=false",
                                                            allowedTypes: "pdf",
                                                            fileName: "myfile",
                                                            autoSubmit: true,
                                                            multiple: true,
                                                            //maxFileSize: 7900000,
                                                            maxFileSize: PDFFileSize,
                                                            showStatusAfterSuccess: false,
                                                            showCancel: true,
                                                            showDone: true,
                                                            uploadDiv: "excelipload",
                                                            dynamicFormData: function () {
                                                                var data = { DocId: $find("<%=this.ClientID%>")._intLineID }
                                                                return data;
                                                            },
                                                            onSuccess: function (files, data, xhr) {

                                                                var originalFilename = '';
                                                                var generatedFilename = '';
                                                                originalFilename = files[0];
                                                                var json = Sys.Serialization.JavaScriptSerializer.deserialize(data);
                                                                generatedFilename = json.FileName;
                                                                $find("<%=this.ClientID%>").GILineUploadPDF(originalFilename, generatedFilename);
                                                            },
                                                            onSelect: function (fup) {
                                                                var result = true;
                                                                $find("<%=this.ClientID%>")._dragobj = dragdropObj;

                                                                return result;
                                                            }
                                                        });
                                                    }
                                                });
                                            </script>
                                        </span>
                                        <div class="documentsizeGI" ><%=Functions.GetGlobalResource("FormFields", "MaxDocumentSize")%><span id="DocumentFileSizeGIImage"></span></div>
                                        <span class="uploadcontrol" id="uploadImages">
                                             
                                            <div id="Imagesingleupload12">Upload</div>
                                           
                                            <script type="text/javascript">
                                                $(document).ready(function () {
                                                    var PDFFileSize;
                                                    var SectionMod;
                                                    $.ajax({
                                                        processData: false,
                                                        contentType: 'application/json',
                                                        type: 'POST',
                                                        async: false,
                                                        url: "DocImage.ashx?type=DOCUMENTFILESIZE&DocumentType=3",
                                                        success: function (data) {
                                                            var obj = JSON.parse(data);
                                                            PDFFileSize = obj.PDFFileSize;
                                                            PDFFileSizeMB = obj.PDFFileSizeMB;
                                                            $("#DocumentFileSizeGIImage").text('(' + PDFFileSizeMB + ' MB)');
                                                            PDFDragDrop(PDFFileSize);
                                                        },
                                                        error: function () {
                                                            alert("The system has encountered a network connectivity issue. Please check your connectivity and try again.");

                                                        },
                                                    });

                                                    function PDFDragDrop(PDFFileSize) {
                                                        var dragdropObj = $("#Imagesingleupload12").uploadFile({
                                                            url: "DocImage.ashx?mxs=1&type=IMAGEUPLOAD&IsDragDrop=true",
                                                            allowedTypes: "jpg,jpeg,bmp",
                                                            fileName: "myfile",
                                                            autoSubmit: true,
                                                            multiple: true,
                                                            //maxFileSize: 7900000,
                                                            maxFileSize: PDFFileSize,
                                                            showStatusAfterSuccess: false,
                                                            showCancel: true,
                                                            showDone: true,
                                                            uploadDiv: "excelipload",
                                                            dynamicFormData: function () {
                                                                var data = { DocId: $find("<%=this.ClientID%>")._intLineID }
                                                                return data;
                                                            },
                                                            onSuccess: function (files, data, xhr) {
                                                                var originalFilename = '';
                                                                var generatedFilename = '';
                                                                originalFilename = files[0];
                                                                var json = Sys.Serialization.JavaScriptSerializer.deserialize(data);
                                                                generatedFilename = json.FileName;
                                                                $find("<%=this.ClientID%>").saveGILineImage(originalFilename, generatedFilename);
                                                            },
                                                            onSelect: function (fup) {
                                                                var result = true;
                                                                $find("<%=this.ClientID%>")._dragobj = dragdropObj;

                                                                return result;
                                                            }
                                                        });
                                                    }
                                                });
                                            </script>
                                        </span>
                                       
                                        <button id="btnBulkAttacheDelete" class="BuklDelete bulkdlt_1" title="Delete Attachments" onclick="return false">
                                            <%--<img style="margin-left: 5px;margin-right: 5px;" src="../../../../images/imgdelete.png" />--%>
                                            Delete Selected
                                        </button>
                                    </span>
                                    <span class="thumbimgblock">
                                        <span class="tabtitle">PDF Documents</span>
                                        <span id="pdfAttachments"></span>
                                    </span>
                                    <span class="thumbimgblock">
                                        <span class="tabtitle">Images</span>

                                        <span id="imageAttachements"></span>
                                    </span>
                                    <div class="LoaderPopup" id="divLoader" style="display: none; margin-top: 20%;">
                                        <div>
                                            <div class="cssload-loader">Deleting..</div>
                                        </div>
                                    </div>
                                   
                                        
                                        
                            </div>
                            <div id="ConfigureApproverModel" class="modal">
                                <!-- Modal content -->
                                <div class="modal-content">
                                    <span class="close">&times;</span>
                                    <div class="replytoselect">
                                        <h4>
                                            <label><%=Functions.GetGlobalResource("FormFields", "QMSConfigApprover")%></label></h4>
                                        <table class="ConfigureApprovertable" width="100%" cellpadding="0" cellspacing="0">
                                            <tr>

                                                <td>
                                                    <label><b><%=Functions.GetGlobalResource("FormFields", "QMSCurrentSalesApprover")%></b></label>
                                                </td>
                                                <td>
                                                    <label id="lblCurrentsalesApprover"></label>
                                                </td>
                                                <td>
                                                    <label><b><%=Functions.GetGlobalResource("FormFields", "QMSCurrentPurchasingApprover")%></b></label>
                                                </td>
                                                <td>
                                                    <label id="lblCurrentPurchasingApprover"></label>
                                                </td>

                                            </tr>
                                            <tr>
                                                <td>
                                                    <label><b><%=Functions.GetGlobalResource("FormFields", "QMSInviteSalesApprover")%></b></label>
                                                </td>
                                                <td>
                                                    <ReboundDropDown:Employee ID="ddlNewSalesApprover" runat="server" />
                                                </td>
                                                <td>
                                                    <label><b><%=Functions.GetGlobalResource("FormFields", "QMSInvitePurchasingApprover")%></b></label>
                                                </td>
                                                <td>
                                                    <ReboundDropDown:Employee ID="ddlNewPurchasingApprover" runat="server" />
                                                </td>
                                            </tr>
                                        </table>
                                    </div>
                                    <div class="Approverbtn btnapprove">
                                        <span>
                                            <button id="btnSaveNewApprover" onclick="return false">Save</button>
                                        </span>
                                        <span>
                                            <button id="btnCancelApprover" onclick="return false">Cancel</button>
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div id="RenameModel" class="modal">
                                <!-- Modal content -->
                                <div class="modalRename-content">
                                    <span id="closeRename" class="close">&times;</span>
                                    <div class="replytoselect">
                                        <h4>
                                            <label><%=Functions.GetGlobalResource("FormFields", "GIRename")%></label></h4>
                                        <table class="ConfigureApprovertable" width="100%" cellpadding="0" cellspacing="0">
                                            <tr>
                                                <td>
                                                    <label><b><%=Functions.GetGlobalResource("FormFields", "Caption")%></b></label>
                                                </td>
                                                <td>
                                                    <ReboundUI:ReboundTextBox ID="txtRenameCaption" runat="server" Width="350" MaxLength="1500" TextMode="multiLine" Rows="3" />
                                                </td>
                                            </tr>
                                        </table>
                                    </div>
                                    <div class="Approverbtn btnapprove">
                                        <span>
                                            <button id="btnSaveRename" onclick="return false">Save</button>
                                        </span>
                                        <span>
                                            <button id="btnCancelRename" onclick="return false">Cancel</button>
                                        </span>
                                    </div>
                                </div>
                            </div>

                        </section>
                    </div>
                </asp:TableCell>
            </asp:TableRow>
        </ReboundUI_Table:Form>
    </Content>
</ReboundUI_Form:DesignBase>

<div id="AutoSplitModel" class="Splitmodal">
    <!-- Modal content -->
    <div class="Splitmodal-content">
        <span class="close" id="closeAutoSplit">&times;</span>
        <div class="replytoselect">
            <h4>Auto Split Package Breakdown</h4>
            <table>
                <tr>
                    <td colspan="2">
                        <label style="color: red; background: yellow;" id="spliterror"></label>
                    </td>
                </tr>
                <tr>
                    <td style="width: 90px;"><b>
                        <label><%=Functions.GetGlobalResource("FormFields", "NumberOfPacks")%> *</label></b>
                    </td>
                    <td style="width: 100px;">
                        <ReboundUI:ReboundTextBox ID="txtNoOfPcks" TextBoxMode="Numeric" runat="server" Width="100" />
                    </td>
                </tr>
                <tr>
                    <td style="width: 100px;"><b>
                        <label><%=Functions.GetGlobalResource("FormFields", "PackSize")%> *</label></b>
                    </td>
                    <td style="width: 100px;">
                        <ReboundUI:ReboundTextBox ID="txtPckSize" TextBoxMode="Numeric" runat="server" Width="100" />
                    </td>
                </tr>
                <tr>
                    <td style="width: 120px;"><b>
                        <label><%=Functions.GetGlobalResource("FormFields", "BreakdownLines")%> *</label></b>
                    </td>
                    <td>
                        <ReboundUI:ReboundTextBox ID="txtBrkDwnLines" TextBoxMode="Numeric" runat="server" Width="100" />
                    </td>
                </tr>
            </table>
            <h4></h4>
            <div>
                <button id="ibtnSplitBreakDown" class="btnSplitSave" onclick="return false">
                    <%=Functions.GetGlobalResource("Buttons", "GISplitSave")%>
                </button>
            </div>
        </div>
    </div>
</div>