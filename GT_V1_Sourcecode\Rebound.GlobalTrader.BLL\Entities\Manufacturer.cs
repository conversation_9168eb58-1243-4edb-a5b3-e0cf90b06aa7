﻿using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using Rebound.GlobalTrader.DAL;
using Rebound.GlobalTrader.BLL;
using System.Linq;

namespace Rebound.GlobalTrader.BLL {
		public partial class Manufacturer : BizObject {
		
		#region Properties

		protected static DAL.ManufacturerElement Settings {
			get { return Globals.Settings.Manufacturers; }
		}

		/// <summary>
		/// ManufacturerId
		/// </summary>
		public System.Int32 ManufacturerId { get; set; }		
		/// <summary>
		/// ManufacturerName
		/// </summary>
		public System.String ManufacturerName { get; set; }		
		/// <summary>
		/// Notes
		/// </summary>
		public System.String Notes { get; set; }		
		/// <summary>
		/// ManufacturerCode
		/// </summary>
		public System.String ManufacturerCode { get; set; }		
		/// <summary>
		/// Inactive
		/// </summary>
		public System.Boolean Inactive { get; set; }		
		/// <summary>
		/// UpdatedBy
		/// </summary>
		public System.Int32? UpdatedBy { get; set; }		
		/// <summary>
		/// DLUP
		/// </summary>
		public System.DateTime DLUP { get; set; }		
		/// <summary>
		/// URL
		/// </summary>
		public System.String URL { get; set; }		
		/// <summary>
		/// FullName
		/// </summary>
		public System.String FullName { get; set; }		
		/// <summary>
		/// RowNum
		/// </summary>
		public System.Int64? RowNum { get; set; }		
		/// <summary>
		/// RowCnt
		/// </summary>
		public System.Int32? RowCnt { get; set; }

        /// <summary>
        /// IsPDFAvailable
        /// </summary>
        public System.Boolean IsPDFAvailable { get; set; }
        /// <summary>
        /// ConflictResource
        /// </summary>
        public System.String ConflictResource { get; set; }

		public System.String GroupName { get; set; }

		public System.String GroupCode { get; set; }

		/// <summary>
		/// SystemManufacturer
		/// </summary>
		public System.Int32? SystemManufacturer { get; set; }
		public string AdvisoryNotes { get; set; }
		public bool? IsDisplayAdvisory { get; set; }
		#endregion

		#region Methods

		/// <summary>
		/// AutoSearch
		/// Calls [usp_autosearch_Manufacturer]
		/// </summary>
		public static List<Manufacturer> AutoSearch(System.String nameSearch,Boolean? showInactive) {
            List<ManufacturerDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.Manufacturer.AutoSearch(nameSearch, showInactive);
			if (lstDetails == null) {
				return new List<Manufacturer>();
			} else {
				List<Manufacturer> lst = new List<Manufacturer>();
				foreach (ManufacturerDetails objDetails in lstDetails) {
					Rebound.GlobalTrader.BLL.Manufacturer obj = new Rebound.GlobalTrader.BLL.Manufacturer();
					obj.ManufacturerId = objDetails.ManufacturerId;
					obj.ManufacturerName = objDetails.ManufacturerName;
					obj.FullName = objDetails.FullName;
					lst.Add(obj);
					obj = null;
				}
				lstDetails = null;
				return lst;
			}
		}


		public static List<DAL.HUBOfferImportLargeFileTempDetails> GetIncorrectMfrToOfferImport(System.Int32? clientNo, System.Int32 importFileId)
		{
			List<DAL.HUBOfferImportLargeFileTempDetails> lstDetails = DAL.SiteProvider.Manufacturer.GetIncorrectMfrToOfferImport(clientNo, importFileId);
			if (lstDetails == null)
			{
				return new List<DAL.HUBOfferImportLargeFileTempDetails>();
			}
			else
			{
				List<DAL.HUBOfferImportLargeFileTempDetails> lst = new List<DAL.HUBOfferImportLargeFileTempDetails>();
				foreach (DAL.HUBOfferImportLargeFileTempDetails objDetails in lstDetails)
				{
					DAL.HUBOfferImportLargeFileTempDetails obj = new DAL.HUBOfferImportLargeFileTempDetails();
					obj.OfferTempId = objDetails.OfferTempId;
					obj.MFR = objDetails.MFR;
					obj.MFRCount = objDetails.MFRCount;
					lst.Add(obj);
					obj = null;
				}
				lstDetails = null;
				return lst;
			}
		}

		/// <summary>
		/// AutoSearch
		/// Calls [usp_autosearch_Manufacturer]
		/// </summary>
		public static List<Manufacturer> LyticaAutoSearch(System.String partNo, System.String nameSearch, Boolean? showInactive)
		{
			List<ManufacturerDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.Manufacturer.LyticaAutoSearch(partNo, nameSearch, showInactive);
			if (lstDetails == null)
			{
				return new List<Manufacturer>();
			}
			else
			{
				List<Manufacturer> lst = new List<Manufacturer>();
				foreach (ManufacturerDetails objDetails in lstDetails)
				{
					Rebound.GlobalTrader.BLL.Manufacturer obj = new Rebound.GlobalTrader.BLL.Manufacturer();
					obj.ManufacturerId = objDetails.ManufacturerId;
					obj.ManufacturerName = objDetails.ManufacturerName;
					lst.Add(obj);
					obj = null;
				}
				lstDetails = null;
				return lst;
			}
		}

		/// <summary>
		/// Count
		/// Calls [usp_count_Manufacturer]
		/// </summary>
		public static Int32 Count() {
			return Rebound.GlobalTrader.DAL.SiteProvider.Manufacturer.Count();
		}		/// <summary>
		/// DataListNugget
		/// Calls [usp_datalistnugget_Manufacturer]
		/// </summary>
		public static List<Manufacturer> DataListNugget(System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, 
								System.String nameSearch, System.String codeSearch,System.String manufGroupCode, System.String groupName, int clientId) {
			List<ManufacturerDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.Manufacturer.DataListNugget(orderBy, sortDir, pageIndex, pageSize, 
				nameSearch, codeSearch, manufGroupCode, groupName, clientId);
			if (lstDetails == null) {
				return new List<Manufacturer>();
			} else {
				List<Manufacturer> lst = new List<Manufacturer>();
				foreach (ManufacturerDetails objDetails in lstDetails) {
					Rebound.GlobalTrader.BLL.Manufacturer obj = new Rebound.GlobalTrader.BLL.Manufacturer();
					obj.ManufacturerId = objDetails.ManufacturerId;
					obj.ManufacturerCode = objDetails.ManufacturerCode;
					obj.ManufacturerName = objDetails.ManufacturerName;
                    obj.Inactive = objDetails.Inactive;
					obj.URL = objDetails.URL;
					obj.RowNum = objDetails.RowNum;
					obj.RowCnt = objDetails.RowCnt;
                    obj.ConflictResource = objDetails.ConflictResource;
					obj.GroupName = objDetails.GroupName;
					obj.GroupCode = objDetails.GroupCode;
					obj.SystemManufacturer = objDetails.SystemManufacturer;
					obj.AdvisoryNotes = objDetails.AdvisoryNotes;
					lst.Add(obj);
					obj = null;
				}
				lstDetails = null;
				return lst;
			}
		}
		/// <summary>
		/// Delete
		/// Calls [usp_delete_Manufacturer]
		/// </summary>
		public static bool Delete(System.Int32? manufacturerId) {
			return Rebound.GlobalTrader.DAL.SiteProvider.Manufacturer.Delete(manufacturerId);
		}
		/// <summary>
		/// DropDown
		/// Calls [usp_dropdown_Manufacturer]
		/// </summary>
		public static List<Manufacturer> DropDown() {
			List<ManufacturerDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.Manufacturer.DropDown();
			if (lstDetails == null) {
				return new List<Manufacturer>();
			} else {
				List<Manufacturer> lst = new List<Manufacturer>();
				foreach (ManufacturerDetails objDetails in lstDetails) {
					Rebound.GlobalTrader.BLL.Manufacturer obj = new Rebound.GlobalTrader.BLL.Manufacturer();
					obj.ManufacturerId = objDetails.ManufacturerId;
					obj.ManufacturerName = objDetails.ManufacturerName;
					lst.Add(obj);
					obj = null;
				}
				lstDetails = null;
				return lst;
			}
		}
		/// <summary>
		/// Insert
		/// Calls [usp_insert_Manufacturer]
		/// </summary>
		public static Int32 Insert(System.String manufacturerName, System.String notes, System.String manufacturerCode, System.Int32? updatedBy, System.String url) {
			Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.Manufacturer.Insert(manufacturerName, notes, manufacturerCode, updatedBy, url);
			return objReturn;
		}
		/// <summary>
		/// Insert (without parameters)
		/// Calls [usp_insert_Manufacturer]
		/// </summary>
		public Int32 Insert() {
			return Rebound.GlobalTrader.DAL.SiteProvider.Manufacturer.Insert(ManufacturerName, Notes, ManufacturerCode, UpdatedBy, URL);
		}
		/// <summary>
		/// Get
		/// Calls [usp_select_Manufacturer]
		/// </summary>
		public static Manufacturer Get(System.Int32? manufacturerId) {
			Rebound.GlobalTrader.DAL.ManufacturerDetails objDetails = Rebound.GlobalTrader.DAL.SiteProvider.Manufacturer.Get(manufacturerId);
			if (objDetails == null) {
				return null;
			} else {
				Manufacturer obj = new Manufacturer();
				obj.ManufacturerId = objDetails.ManufacturerId;
				obj.ManufacturerName = objDetails.ManufacturerName;
				obj.Notes = objDetails.Notes;
				obj.ManufacturerCode = objDetails.ManufacturerCode;
				obj.Inactive = objDetails.Inactive;
				obj.UpdatedBy = objDetails.UpdatedBy;
				obj.DLUP = objDetails.DLUP;
				obj.URL = objDetails.URL;
                obj.IsPDFAvailable = objDetails.IsPDFAvailable;
                obj.ConflictResource = objDetails.ConflictResource;
				obj.SystemManufacturer = objDetails.SystemManufacturer;
				obj.AdvisoryNotes = objDetails.AdvisoryNotes;
				obj.IsDisplayAdvisory = objDetails.IsDisplayAdvisory;
				objDetails = null;
				return obj;
			}
		}
		/// <summary>
		/// Update
		/// Calls [usp_update_Manufacturer]
		/// </summary>
		public static bool Update(System.Int32? manufacturerId,
							System.String manufacturerName,
							System.String notes,
							System.String manufacturerCode,
							System.Boolean? inactive,
							System.Int32? updatedBy,
							System.String url,
							System.String conflictResource,
							string advisoryNotes,
							bool? isDisplayAdvisory)
		{
			return SiteProvider.Manufacturer.Update(manufacturerId,
													manufacturerName,
													notes,
													manufacturerCode,
													inactive,
													updatedBy,
													url,
													conflictResource,
													advisoryNotes,
													isDisplayAdvisory);
		}
		/// <summary>
		/// Update (without parameters)
		/// Calls [usp_update_Manufacturer]
		/// </summary>
		//public bool Update() {
		//	return Rebound.GlobalTrader.DAL.SiteProvider.Manufacturer.Update(ManufacturerId, ManufacturerName, Notes, ManufacturerCode, Inactive, UpdatedBy, URL,ConflictResource);
		//}

        private static Manufacturer PopulateFromDBDetailsObject(ManufacturerDetails obj) {
            Manufacturer objNew = new Manufacturer();
			objNew.ManufacturerId = obj.ManufacturerId;
			objNew.ManufacturerName = obj.ManufacturerName;
			objNew.Notes = obj.Notes;
			objNew.ManufacturerCode = obj.ManufacturerCode;
			objNew.Inactive = obj.Inactive;
			objNew.UpdatedBy = obj.UpdatedBy;
			objNew.DLUP = obj.DLUP;
			objNew.URL = obj.URL;
			objNew.FullName = obj.FullName;
			objNew.RowNum = obj.RowNum;
			objNew.RowCnt = obj.RowCnt;
            return objNew;
        }

        
        /// <summary>
        /// GetPDFListForManufacturer
        /// Calls [usp_selectAll_PDF_for_Manufacturer]
        /// </summary>
        public static List<PDFDocument> GetPDFListForManufacturer(System.Int32? ManufacturerNo)
        {
            List<PDFDocumentDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.Manufacturer.GetPDFListForManufacturer(ManufacturerNo);
            if (lstDetails == null)
            {
                return new List<PDFDocument>();
            }
            else
            {
                List<PDFDocument> lst = new List<PDFDocument>();
                foreach (PDFDocumentDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.PDFDocument obj = new Rebound.GlobalTrader.BLL.PDFDocument();
                    obj.PDFDocumentId = objDetails.PDFDocumentId;
                    obj.PDFDocumentRefNo = objDetails.PDFDocumentRefNo;
                    obj.Caption = objDetails.Caption;
                    obj.FileName = objDetails.FileName;
                    obj.UpdatedBy = objDetails.UpdatedBy;
                    obj.DLUP = objDetails.DLUP;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }


        //code add for iHS document
        /// <summary>
        /// GetPDFListForManufacturer
        /// Calls [usp_selectAll_PDF_for_Manufacturer]
        /// </summary>
        public static List<PDFDocument> GetPDFListForIHS(System.Int32? IHSPartNo)
        {
            List<PDFDocumentDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.Manufacturer.GetPDFListForIHS(IHSPartNo);
            if (lstDetails == null)
            {
                return new List<PDFDocument>();
            }
            else
            {
                List<PDFDocument> lst = new List<PDFDocument>();
                foreach (PDFDocumentDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.PDFDocument obj = new Rebound.GlobalTrader.BLL.PDFDocument();
                    obj.PDFDocumentId = objDetails.PDFDocumentId;
                    obj.PDFDocumentRefNo = objDetails.PDFDocumentRefNo;
                    obj.Caption = objDetails.Caption;
                    obj.FileName = objDetails.FileName;
                    obj.UpdatedBy = objDetails.UpdatedBy;
                    obj.DLUP = objDetails.DLUP;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        //code end


        /// <summary>
        /// GetExcelListForManufacturer
        /// Calls [usp_selectAll_Excel_for_Manufacturer]
        /// </summary>
        public static List<PDFDocument> GetExcelListForManufacturer(System.Int32? ManufacturerNo)
        {
            List<PDFDocumentDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.Manufacturer.GetExcelListForManufacturer(ManufacturerNo);
            if (lstDetails == null)
            {
                return new List<PDFDocument>();
            }
            else
            {
                List<PDFDocument> lst = new List<PDFDocument>();
                foreach (PDFDocumentDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.PDFDocument obj = new Rebound.GlobalTrader.BLL.PDFDocument();
                    obj.PDFDocumentId = objDetails.PDFDocumentId;
                    obj.PDFDocumentRefNo = objDetails.PDFDocumentRefNo;
                    obj.Caption = objDetails.Caption;
                    obj.FileName = objDetails.FileName;
                    obj.UpdatedBy = objDetails.UpdatedBy;
                    obj.DLUP = objDetails.DLUP;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }

        /// <summary>
        /// Calls [usp_insert_ManufacturerPDF]
        /// </summary>
        /// <param name="ManufacturerNo"></param>
        /// <param name="Caption"></param>
        /// <param name="FileName"></param>
        /// <param name="UpdatedBy"></param>
        /// <returns></returns>
        public static Int32 Insert(System.Int32? ManufacturerNo, System.String Caption, System.String FileName, System.Int32? UpdatedBy)
        {
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.Manufacturer.Insert(ManufacturerNo, Caption, FileName, UpdatedBy);
            return objReturn;
        }

        //add ihs pdf document details to db
        /// <summary>
        /// Calls [usp_insert_IHSPDFDocument]
        /// </summary>
        /// <param name="IHSPartNo"></param>
        /// <param name="Caption"></param>
        /// <param name="FileName"></param>
        /// <param name="UpdatedBy"></param>
        /// <returns></returns>
        public static Int32 InsertIHS(System.Int32? IHSPartNo, System.String Caption, System.String FileName, System.Int32? UpdatedBy)
        {
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.Manufacturer.InsertIHS(IHSPartNo, Caption, FileName, UpdatedBy);
            return objReturn;
        }
        //code end

        /// <summary>
        /// Calls [usp_insert_ManufacturerExcel]
        /// </summary>
        /// <param name="ManufacturerNo"></param>
        /// <param name="Caption"></param>
        /// <param name="FileName"></param>
        /// <param name="UpdatedBy"></param>
        /// <returns></returns>
        public static Int32 InsertExcel(System.Int32? ManufacturerNo, System.String Caption, System.String FileName, System.Int32? UpdatedBy)
        {
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.Manufacturer.InsertExcel(ManufacturerNo, Caption, FileName, UpdatedBy);
            return objReturn;
        }




        /// <summary>
        /// Calls [usp_delete_ManufacturerPDF]
        /// </summary>
        /// <param name="InvoicePdfId"></param>
        /// <returns></returns>
        public static bool DeleteManufacturerPDF(System.Int32? ManufacturerPdfId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Manufacturer.DeleteManufacturerPDF(ManufacturerPdfId);
        }

        //code adeded for IHS pdf document delete
        /// <summary>
        /// Calls [usp_delete_IHSDocumentPDF]
        /// </summary>
        /// <param name="IHSPartNo"></param>
        /// <returns></returns>
        public static bool DeleteIHSPDF(System.Int32? IHSPDFID)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Manufacturer.DeleteIHSPDF(IHSPDFID);
        }
        //code end


        /// <summary>
        /// Calls [usp_delete_ManufacturerExcel]
        /// </summary>
        /// <param name="InvoicePdfId"></param>
        /// <returns></returns>
        public static bool DeleteManufacturerExcel(System.Int32? ManufacturerExcelId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Manufacturer.DeleteManufacturerExcel(ManufacturerExcelId);
        }

		/// <summary>
		/// Method to check the existence of manufacturer
		/// </summary>
		/// <param name="manufacturerName"></param>
		/// <param name="updatedBy"></param>
		/// <returns></returns>
		public static Int32 CheckIsExistsManufacturer(System.String manufacturerName, System.Int32? updatedBy)
		{
			Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.Manufacturer.CheckIsExistsManufacturer(manufacturerName, updatedBy);
			return objReturn;
		}

		public static List<Rebound.GlobalTrader.BLL.ContactGroup> GetDataByNameorCode(System.String nameSearch, System.String codeSearch,string type)
		{
			List<Rebound.GlobalTrader.DAL.ContactGroup> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.Manufacturer.GetDataByNameorCode(nameSearch, codeSearch, type);
			if (lstDetails == null)
			{
				return new List<ContactGroup>();
			}
			else
			{
				List<Rebound.GlobalTrader.BLL.ContactGroup> lst = new List<ContactGroup>();
				foreach (Rebound.GlobalTrader.DAL.ContactGroup objDetails in lstDetails)
				{
					Rebound.GlobalTrader.BLL.ContactGroup obj = new Rebound.GlobalTrader.BLL.ContactGroup();
					obj.Id = objDetails.Id;
					obj.Code = objDetails.Code;
					obj.ContactName = objDetails.ContactName;
					lst.Add(obj);
					obj = null;
				}
				lstDetails = null;
				return lst;
			}
		}

		public static int SaveGroupData(System.String groupName, System.String groupCode, string groupType, System.String commaseperatedIds)
		{
            try
            {
				int result = Rebound.GlobalTrader.DAL.SiteProvider.Manufacturer.SaveGroupData(groupName, groupCode, groupType, commaseperatedIds);
				return result;
			}
            catch (Exception ex)
            {
                throw ex;
            }
		}


		public static DataTable AutoSearch(System.String groupName,System.String groupType)
		{
			return Rebound.GlobalTrader.DAL.SiteProvider.Manufacturer.AutoGroupSearch(groupName, groupType);
		}

        public static List<Rebound.GlobalTrader.BLL.ContactGroup> GetDataByLot(System.Int32 LotNo, System.Int32 ClientNo)
        {
            List<Rebound.GlobalTrader.DAL.ContactGroup> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.Manufacturer.GetDataByLot(LotNo, ClientNo);
            if (lstDetails == null)
            {
                return new List<ContactGroup>();
            }
            else
            {
                List<Rebound.GlobalTrader.BLL.ContactGroup> lst = new List<ContactGroup>();
                foreach (Rebound.GlobalTrader.DAL.ContactGroup objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.ContactGroup obj = new Rebound.GlobalTrader.BLL.ContactGroup();
                    obj.ClientNo = objDetails.ClientNo;
                    obj.StockId = objDetails.StockId;
                    obj.Part = objDetails.Part;
                    obj.SupplierPart = objDetails.SupplierPart;
                    obj.QuantityInStock = objDetails.QuantityInStock;
                    obj.QuantityOnOrder = objDetails.QuantityOnOrder;
                    obj.QuantityAvailable = objDetails.QuantityAvailable;
                    obj.QuantityAllocated = objDetails.QuantityAllocated;
                    obj.LandedCost = objDetails.LandedCost;
                    obj.Unavailable = objDetails.Unavailable;
                    obj.SupplierName = objDetails.SupplierName;
                    obj.PODeliveryDate = objDetails.PODeliveryDate;
                    obj.PurchaseOrderNumber = objDetails.PurchaseOrderNumber;
                    obj.CustomerRMANumber = objDetails.CustomerRMANumber;
                    obj.CustomerRMADate = objDetails.CustomerRMADate;
                    obj.WarehouseName = objDetails.WarehouseName;
                    obj.Location = objDetails.Location;
                    obj.POSerialNo = objDetails.POSerialNo;
                    obj.LotCode = objDetails.LotCode;
                    obj.LotName = objDetails.LotName;
                    obj.ManufacturerCode = objDetails.ManufacturerCode;
                    obj.ManufacturerName = objDetails.ManufacturerName;
                    obj.ProductDescription = objDetails.ProductDescription;
                    obj.BookedLotQuoteNo = objDetails.BookedLotQuoteNo;
                    obj.QuoteNumber = objDetails.QuoteNumber;
                    obj.IsBookedLotQuote = objDetails.IsBookedLotQuote;

					//lot by so line
					obj.BookedLotSONo = objDetails.BookedLotSONo;
					obj.SONumber = objDetails.SONumber;
					obj.IsBookedLotSO = objDetails.IsBookedLotSO;

					lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }

		public static string GetAdvisoryNotes(int manufacturerId, int clientId)
        {
			if (manufacturerId <= 0) return string.Empty;
			return SiteProvider.Manufacturer.GetAdvisoryNotes(manufacturerId, clientId);
		}

		public static List<Manufacturer> GetAdvisoryNotes(List<int?> lstIDs, int clientId)
		{
			string[] mfrIDs = lstIDs.Where(x => x.HasValue).Select(x => x.ToString()).ToArray();
			string IDs = string.Join(",", mfrIDs);
			var lst = new List<Manufacturer>();
			var lstDetails = SiteProvider.Manufacturer.GetAdvisoryNotes(IDs, clientId);
			if (lstDetails == null)
			{
				return lst;
			}
			foreach (var objDetails in lstDetails)
			{
				var obj = new Manufacturer()
				{
					ManufacturerId = objDetails.ManufacturerId,
					AdvisoryNotes = objDetails.AdvisoryNotes
				};

				lst.Add(obj);
			}
			return lst;
		}
		#endregion

	}
}