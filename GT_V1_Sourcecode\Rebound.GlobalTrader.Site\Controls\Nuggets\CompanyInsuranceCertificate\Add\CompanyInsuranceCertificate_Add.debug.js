///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.CompanyInsuranceCertificate_Add = function(element) {
    Rebound.GlobalTrader.Site.Controls.Forms.CompanyInsuranceCertificate_Add.initializeBase(this, [element]);
	this._intCompanyID = 0;
};

Rebound.GlobalTrader.Site.Controls.Forms.CompanyInsuranceCertificate_Add.prototype = {

    get_intCompanyID: function() { return this._intCompanyID; }, set_intCompanyID: function(v) { if (this._intCompanyID !== v) this._intCompanyID = v; },

    initialize: function() {
        Rebound.GlobalTrader.Site.Controls.Forms.CompanyInsuranceCertificate_Add.callBaseMethod(this, "initialize");
        this.addShown(Function.createDelegate(this, this.formShown));
    },

    dispose: function() {
        if (this.isDisposed) return;
        this._intCompanyID = null;
        Rebound.GlobalTrader.Site.Controls.Forms.CompanyInsuranceCertificate_Add.callBaseMethod(this, "dispose");
    },

    formShown: function() {
        if (this._blnFirstTimeShown) {
            //form events
            this.addSave(Function.createDelegate(this, this.saveClicked));
            $find(this.getField("ctlCategory").ControlID).addChanged(Function.createDelegate(this, this.getCertificateByCategory));

            //data
            this._strPathToData = "controls/Nuggets/CompanyInsuranceCertificate";
            this._strDataObject = "CompanyInsuranceCertificate";
        }

        this.getFieldDropDownData("ctlCategory");
        this.getCertificateByCategory();
        this.setFormFieldsToDefaults();
    },


    getCertificateByCategory: function() {
        this.showCertificateFieldsLoading(true);
        this.getFieldComponent("ctlCertificate")._intCategoryID = this.getFieldValue("ctlCategory");
        this.getFieldDropDownData("ctlCertificate");
        this.showCertificateFieldsLoading(false);
    },
    showCertificateFieldsLoading: function(bln) {
        this.showFieldLoading("ctlCertificate", bln);
    },
    saveClicked: function() {
        this.resetFormFields();
        if (this.validateForm()) this.addNew();
    },

    validateForm: function() {
        var blnOK = true;
        blnOK = this.autoValidateFields();
        if (!blnOK) this.showError(true);
        return blnOK;
    },

    addNew: function() {
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData(this._strPathToData);
        obj.set_DataObject(this._strDataObject);
        obj.set_DataAction("AddNew");
        obj.addParameter("id", this._intCompanyID);
        obj.addParameter("Category", this.getFieldValue("ctlCategory"));
        obj.addParameter("Certificate", this.getFieldValue("ctlCertificate"));
        obj.addParameter("Number", this.getFieldValue("ctlCertificateNumbre"));
        obj.addParameter("StartDate", this.getFieldValue("ctlStartDate"));
        obj.addParameter("ExpiryDate", this.getFieldValue("ctlExpiryDate"));
        obj.addParameter("InActive", this.getFieldValue("ctlInActive"));
        obj.addParameter("Desc", this.getFieldValue("ctlDescription"));
        obj.addDataOK(Function.createDelegate(this, this.saveEditOK));
        obj.addError(Function.createDelegate(this, this.saveEditError));
        obj.addTimeout(Function.createDelegate(this, this.saveEditError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    saveEditError: function(args) {
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    saveEditOK: function(args) {
        if (args._result.Result == true) {
            this._intLineID = args._result.NewID;
            this.onSaveComplete();
        } else {
            this.saveEditError(args);
        }
    }

};

Rebound.GlobalTrader.Site.Controls.Forms.CompanyInsuranceCertificate_Add.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CompanyInsuranceCertificate_Add", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
