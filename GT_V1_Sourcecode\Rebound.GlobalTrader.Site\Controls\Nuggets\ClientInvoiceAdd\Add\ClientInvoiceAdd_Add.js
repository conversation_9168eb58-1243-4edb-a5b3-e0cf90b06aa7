Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.ClientInvoiceAdd_Add=function(n){Rebound.GlobalTrader.Site.Controls.Forms.ClientInvoiceAdd_Add.initializeBase(this,[n]);this._intNewID=0;this._intCompanyID=0;this._strCompanyName="";this._aryGoodsInLineIds=[];this._lnsSeperator="/";this._SupplierCode="";this._intGoodsInID=0;this._CurrencyCode="";this._TaxRate=0;this._isClientInvoice=!1;this._intInvoiceClientNo=-1};Rebound.GlobalTrader.Site.Controls.Forms.ClientInvoiceAdd_Add.prototype={get_ctlSelectCompany:function(){return this._ctlSelectCompany},set_ctlSelectCompany:function(n){this._ctlSelectCompany!==n&&(this._ctlSelectCompany=n)},get_ibtnSend:function(){return this._ibtnSend},set_ibtnSend:function(n){this._ibtnSend!==n&&(this._ibtnSend=n)},get_ibtnSend_Footer:function(){return this._ibtnSend_Footer},set_ibtnSend_Footer:function(n){this._ibtnSend_Footer!==n&&(this._ibtnSend_Footer=n)},get_ibtnContinue:function(){return this._ibtnContinue},set_ibtnContinue:function(n){this._ibtnContinue!==n&&(this._ibtnContinue=n)},get_ibtnContinue_Footer:function(){return this._ibtnContinue_Footer},set_ibtnContinue_Footer:function(n){this._ibtnContinue_Footer!==n&&(this._ibtnContinue_Footer=n)},get_intCompanyID:function(){return this._intCompanyID},set_intCompanyID:function(n){this._intCompanyID!==n&&(this._intCompanyID=n)},get_intGoodsInID:function(){return this._intGoodsInID},set_intGoodsInID:function(n){this._intGoodsInID!==n&&(this._intGoodsInID=n)},get_strCompanyName:function(){return this._strCompanyName},set_strCompanyName:function(n){this._strCompanyName!==n&&(this._strCompanyName=n)},get_ctlSelectSIGILines:function(){return this._ctlSelectSIGILines},set_ctlSelectSIGILines:function(n){this._ctlSelectSIGILines!==n&&(this._ctlSelectSIGILines=n)},get_dtFromDate:function(){return this._dtFromDate},set_dtFromDate:function(n){this._dtFromDate!==n&&(this._dtFromDate=n)},get_dtToDate:function(){return this._dtToDate},set_dtToDate:function(n){this._dtToDate!==n&&(this._dtToDate=n)},get_lblCurrency_InvoiceAmount:function(){return this._lblCurrency_InvoiceAmount},set_lblCurrency_InvoiceAmount:function(n){this._lblCurrency_InvoiceAmount!==n&&(this._lblCurrency_InvoiceAmount=n)},get_lblCurrency_GoodsInValue:function(){return this._lblCurrency_GoodsInValue},set_lblCurrency_GoodsInValue:function(n){this._lblCurrency_GoodsInValue!==n&&(this._lblCurrency_GoodsInValue=n)},get_lblCurrency_Tax:function(){return this._lblCurrency_Tax},set_lblCurrency_Tax:function(n){this._lblCurrency_Tax!==n&&(this._lblCurrency_Tax=n)},get_lblCurrency_DeliveryCharge:function(){return this._lblCurrency_DeliveryCharge},set_lblCurrency_DeliveryCharge:function(n){this._lblCurrency_DeliveryCharge!==n&&(this._lblCurrency_DeliveryCharge=n)},get_lblCurrency_BankFee:function(){return this._lblCurrency_BankFee},set_lblCurrency_BankFee:function(n){this._lblCurrency_BankFee!==n&&(this._lblCurrency_BankFee=n)},get_lblCurrency_CreditCardFee:function(){return this._lblCurrency_CreditCardFee},set_lblCurrency_CreditCardFee:function(n){this._lblCurrency_CreditCardFee!==n&&(this._lblCurrency_CreditCardFee=n)},get_SelectedGI:function(){return this._SelectedGI},set_SelectedGI:function(n){this._SelectedGI!==n&&(this._SelectedGI=n)},initialize:function(){var n,t;Rebound.GlobalTrader.Site.Controls.Forms.ClientInvoiceAdd_Add.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown));this.addCancel(Function.createDelegate(this,this.cancelClicked));this.addSave(Function.createDelegate(this,this.saveHeaderThenLine));n=Function.createDelegate(this,this.finishedForm);$R_IBTN.addClick(this._ibtnContinue,n);$R_IBTN.addClick(this._ibtnContinue_Footer,n);t=Function.createDelegate(this,this.sendMail);$R_IBTN.addClick(this._ibtnSend,t);$R_IBTN.addClick(this._ibtnSend_Footer,t);this._ctlMail=$find(this.getField("ctlSendMailMessage").ID);this._ctlMail._ctlRelatedForm=this;this._ctlMultiStep.addStepChanged(Function.createDelegate(this,this.stepChanged));this._ctlSelectCompany.addItemSelected(Function.createDelegate(this,this.selectCompany));this.addFieldCheckBoxClickEvent("ctlSendMail",Function.createDelegate(this,this.chooseIfSendMail));this._ctlSelectSIGILines.addPotentialStatusChange(Function.createDelegate(this,this.ctlSelectSIGILines_PotentialStatusChange))},dispose:function(){this.isDisposed||(this._ibtnContinue&&$R_IBTN.clearHandlers(this._ibtnContinue),this._ibtnContinue_Footer&&$R_IBTN.clearHandlers(this._ibtnContinue_Footer),this._ibtnSend&&$R_IBTN.clearHandlers(this._ibtnSend),this._ibtnSend_Footer&&$R_IBTN.clearHandlers(this._ibtnSend_Footer),this._ctlMail&&this._ctlMail.dispose(),this._ctlSelectCompany&&this._ctlSelectCompany.dispose(),this._ctlSelectSIGILines&&this._ctlSelectSIGILines.dispose(),this._ctlMail=null,this._ctlSelectCompany=null,this._ctlSelectSIGILines=null,this._ibtnSend=null,this._ibtnSend_Footer=null,this._ibtnContinue=null,this._ibtnContinue_Footer=null,this._intCompanyID=null,this._strCompanyName=null,this._intNewID=null,this._dtFromDate=null,this._dtToDate=null,this._GoodsInLineIds=null,this._lnsSeperator=null,this._intGoodsInID=null,this._SupplierCode=null,this._lblCurrency_InvoiceAmount=null,this._lblCurrency_GoodsInValue=null,this._lblCurrency_Tax=null,this._lblCurrency_DeliveryCharge=null,this._lblCurrency_BankFee=null,this._lblCurrency_CreditCardFee=null,this._SelectedGI=null,this._CurrencyCode=null,this._TaxRate=null,Rebound.GlobalTrader.Site.Controls.Forms.ClientInvoiceAdd_Add.callBaseMethod(this,"dispose"))},formShown:function(){this._blnFirstTimeShown&&($find(this.getField("ctlCurrency").ControlID).addChanged(Function.createDelegate(this,this.updateCurrency)),$find(this.getField("ctlddlTax").ControlID).addChanged(Function.createDelegate(this,this.updateTaxRate)),$find(this.getField("ctlClient").ControlID).addChanged(Function.createDelegate(this,this.SelectClient)));this.gotoStep(2)},cancelClicked:function(){$R_FN.navigateBack()},stepChanged:function(){var n=2;$R_IBTN.enableButton(this._ibtnSave,n==2);$R_IBTN.enableButton(this._ibtnSave_Footer,n==2);$R_IBTN.showButton(this._ibtnSave,n<3);$R_IBTN.showButton(this._ibtnSave_Footer,n<3);$R_IBTN.showButton(this._ibtnCancel,n<3);$R_IBTN.showButton(this._ibtnCancel_Footer,n<3);$R_IBTN.showButton(this._ibtnSend,n==3);$R_IBTN.showButton(this._ibtnSend_Footer,n==3);$R_IBTN.showButton(this._ibtnContinue,n==3);$R_IBTN.showButton(this._ibtnContinue_Footer,n==3);this._ctlMultiStep.showSteps(n!=3);n==1&&this._ctlSelectCompany.resizeColumns();n==2&&(this.getFieldDropDownData("ctlClient"),this.getFieldDropDownData("ctlCurrency"),this.getFieldDropDownData("ctlddlTax"),this._intGoodsInID>0&&(this._ctlSelectSIGILines._intGoodsInID=this._intGoodsInID),this._ctlSelectSIGILines._CompanyNo=this._intCompanyID,this.showField("ctlSupplier",!1),this.showField("ctlSupplierInvoice",!1),this._ctlSelectSIGILines.setFieldValue("ctlGIDateFrom",this._dtFromDate),this._ctlSelectSIGILines.setFieldValue("ctlGIDateTo",this._dtToDate),this._ctlSelectSIGILines.searchClicked());n==3&&(this.getMessageText(),this.setFieldValue("ctlSendMail",!1),this.showMailButtons())},SelectClient:function(){this._ctlSelectSIGILines._isClientInvoice=!0;this._ctlSelectSIGILines._intInvoiceClientNo=this.getFieldValue("ctlClient");this._ctlSelectSIGILines.searchClicked()},selectCompany:function(){this._intCompanyID=this._ctlSelectCompany.getSelectedID();this._strCompanyName=this._ctlSelectCompany._tblResults.getSelectedCellValue(0);this.nextStep()},getCompanyDefaults:function(){this.showCompanyUpdateFieldsLoading(!0);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/CompanyPurchasingInfo");n.set_DataObject("CompanyPurchasingInfo");n.set_DataAction("GetData");n.addParameter("ID",this._intCompanyID);n.addDataOK(Function.createDelegate(this,this.getCompanyOK));n.addError(Function.createDelegate(this,this.getCompanyError));n.addTimeout(Function.createDelegate(this,this.getCompanyError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getCompanyOK:function(n){var t=n._result;this._SupplierCode=t.SupplierCode;this.setFieldValue("ctlCurrency",t.GlobalCurrencyNo);this._CurrencyCode=t.POCurrencyCode;this.selectDefaultCurrency();this._intGoodsInID>0&&this.getDefaultGoodsIn();this.showCompanyUpdateFieldsLoading(!1)},showCompanyUpdateFieldsLoading:function(n){this.showFieldLoading("ctlCurrency",n)},getCompanyError:function(n){this.showCompanyUpdateFieldsLoading(!1);this._strErrorMessage=n._errorMessage;this.onSaveError()},getDefaultGoodsIn:function(){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/GIMainInfo");n.set_DataObject("GIMainInfo");n.set_DataAction("GetForPage");n.addParameter("id",this._intGoodsInID);n.addDataOK(Function.createDelegate(this,this.getDefaultGoodsInOK));n.addError(Function.createDelegate(this,this.getDefaultGoodsInError));n.addTimeout(Function.createDelegate(this,this.getDefaultGoodsInError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getDefaultGoodsInOK:function(n){var t=n._result;t.GlobalCurrencyNo>0&&(this.setFieldValue("ctlCurrency",t.GlobalCurrencyNo),this._CurrencyCode=t.CurrencyCode,this.selectDefaultCurrency());t.TaxNo>0&&(this.setFieldValue("ctlddlTax",t.TaxNo),this.getTaxRate(t.TaxNo))},getDefaultGoodsInError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},ctlSelectSIGILines_PotentialStatusChange:function(){this.setFieldValue("ctlSecondRef",$R_FN.arrayToSingleString(this._ctlSelectSIGILines._aryPONumber,this._lnsSeperator));this.setFieldValue("ctlNarrative",$R_FN.arrayToSingleString(this._ctlSelectSIGILines._aryGINumber,this._lnsSeperator));document.getElementById(this._SelectedGI).value=$R_FN.formatCurrency(this._ctlSelectSIGILines._floatTotalSelectedValue,null,2,!1);this._aryGoodsInLineIds=this._ctlSelectSIGILines._aryGoodsInLineIds},saveHeaderThenLine:function(){if(this.validateForm()){this.showSaving(!0);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/ClientInvoiceAdd");n.set_DataObject("ClientInvoiceAdd");n.set_DataAction("AddNew");n.addParameter("ClientID",this.getFieldValue("ctlClient"));n.addParameter("Amount",this.getFieldValue("ctlInvoiceAmount"));n.addParameter("GoodsValue",this.getFieldValue("ctlGoodsValue"));n.addParameter("Tax",this.getFieldValue("ctlTax"));n.addParameter("TaxNo",this.getFieldValue("ctlddlTax"));n.addParameter("TaxCode",this.getFieldDropDownExtraText("ctlddlTax"));n.addParameter("DeliveryCharge",this.getFieldValue("ctlDeliveryCharge"));n.addParameter("BankFee",this.getFieldValue("ctlBankFee"));n.addParameter("CreditCardFee",this.getFieldValue("ctlCreditCardFee"));n.addParameter("Notes",this.getFieldValue("ctlNotes"));n.addParameter("SecondRef",this.getFieldValue("ctlSecondRef"));n.addParameter("Narrative",this.getFieldValue("ctlNarrative"));n.addParameter("CurrencyNo",this.getFieldValue("ctlCurrency"));n.addParameter("CurrencyCode",this._CurrencyCode);n.addDataOK(Function.createDelegate(this,this.saveHeaderThenLineComplete));n.addError(Function.createDelegate(this,this.saveHeaderThenLineError));n.addTimeout(Function.createDelegate(this,this.saveHeaderThenLineError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null}},saveHeaderThenLineError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},saveHeaderThenLineComplete:function(n){n._result.NewID>0?(this._intNewID=n._result.NewID,this.saveLine()):(this._strErrorMessage=n._errorMessage,this.onSaveError())},saveLine:function(){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/ClientInvoiceAdd");n.set_DataObject("ClientInvoiceAdd");n.set_DataAction("SaveLine");n.addParameter("id",this._intNewID);n.addParameter("GoodsInLineIDs",$R_FN.arrayToSingleString(this._aryGoodsInLineIds));n.addDataOK(Function.createDelegate(this,this.saveLineOK));n.addError(Function.createDelegate(this,this.saveLineError));n.addTimeout(Function.createDelegate(this,this.saveLineError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},saveLineError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},saveLineOK:function(n){n._result.Result?(this.showSaving(!1),this.showInnerContent(!0),this.finishedForm()):(this._strErrorMessage=n._errorMessage,this.onSaveError())},validateForm:function(){this.onValidate();var n=!0;if(this._ctlMultiStep._intCurrentStep==2){if(this.checkFieldEntered("ctlInvoiceDate")||(n=!1),this.checkFieldEntered("ctlCurrency")||(n=!1),this.checkFieldEntered("ctlInvoiceAmount")||(n=!1),this.checkFieldEntered("ctlGoodsValue")||(n=!1),this.checkFieldEntered("ctlTax")||(n=!1),this.checkFieldEntered("ctlddlTax")||(n=!1),this.checkFieldEntered("ctlClient")||(n=!1),this.getFieldValue("ctlSecondRef").length>16)return this.showError(!0,$R_RES.SecondRefMessage),!1;if(this.getFieldValue("ctlNarrative").length>41)return this.showError(!0,$R_RES.NarrativeMessage),!1}return this._ctlMultiStep._intCurrentStep==3&&(this._ctlMail.validateFields()||(n=!1)),n||this.showError(!0),n},showMailButtons:function(){var n=this.getFieldValue("ctlSendMail");this.showField("ctlSendMailMessage",n);$R_IBTN.showButton(this._ibtnSend,n);$R_IBTN.showButton(this._ibtnSend_Footer,n);$R_IBTN.showButton(this._ibtnContinue,!n);$R_IBTN.showButton(this._ibtnContinue_Footer,!n)},chooseIfSendMail:function(){this.showMailButtons()},getMessageText:function(){Rebound.GlobalTrader.Site.WebServices.GetMailMessage_NewSupplierInvoice(this._intNewID,Function.createDelegate(this,this.getMessageTextComplete))},getMessageTextComplete:function(n){this._ctlMail.setValue_Body(n);this._ctlMail.setValue_Subject($R_RES.NewSupplierInvoiceAdded)},validateMailForm:function(){var n=this._ctlMail.validateFields();return n||this.showError(!0),n},sendMail:function(){this.validateMailForm()&&(Rebound.GlobalTrader.Site.WebServices.SendMessage($R_FN.arrayToSingleString(this._ctlMail._aryRecipientLoginIDs),$R_FN.arrayToSingleString(this._ctlMail._aryRecipientGroupIDs),this._ctlMail.getValue_Subject(),this._ctlMail.getValue_Body(),this._intCompanyID,Function.createDelegate(this,this.sendMailComplete)),$R_IBTN.showButton(this._ibtnSave,!1),$R_IBTN.showButton(this._ibtnSave_Footer,!1),$R_IBTN.showButton(this._ibtnSend,!1),$R_IBTN.showButton(this._ibtnSend_Footer,!1))},sendMailComplete:function(){this.finishedForm()},finishedForm:function(){this._ctlMultiStep.showExplainLabel(!1);this._ctlMultiStep.showSteps(!1);$R_IBTN.showButton(this._ibtnSave,!1);$R_IBTN.showButton(this._ibtnSave_Footer,!1);$R_IBTN.showButton(this._ibtnSend,!1);$R_IBTN.showButton(this._ibtnSend_Footer,!1);this.showSavedOK(!0);this.onSaveComplete()},updateCurrency:function(){this._CurrencyCode=this.getFieldDropDownExtraText("ctlCurrency");$R_FN.setInnerHTML(this._lblCurrency_InvoiceAmount,this._CurrencyCode);$R_FN.setInnerHTML(this._lblCurrency_GoodsInValue,this._CurrencyCode);$R_FN.setInnerHTML(this._lblCurrency_Tax,this._CurrencyCode);$R_FN.setInnerHTML(this._lblCurrency_DeliveryCharge,this._CurrencyCode);$R_FN.setInnerHTML(this._lblCurrency_BankFee,this._CurrencyCode);$R_FN.setInnerHTML(this._lblCurrency_CreditCardFee,this._CurrencyCode)},selectDefaultCurrency:function(){$R_FN.setInnerHTML(this._lblCurrency_InvoiceAmount,this._CurrencyCode);$R_FN.setInnerHTML(this._lblCurrency_GoodsInValue,this._CurrencyCode);$R_FN.setInnerHTML(this._lblCurrency_Tax,this._CurrencyCode);$R_FN.setInnerHTML(this._lblCurrency_DeliveryCharge,this._CurrencyCode);$R_FN.setInnerHTML(this._lblCurrency_BankFee,this._CurrencyCode);$R_FN.setInnerHTML(this._lblCurrency_CreditCardFee,this._CurrencyCode)},updateTaxRate:function(){this.getTaxRate(this.getFieldValue("ctlddlTax"))},getTaxRate:function(n){var t=new Rebound.GlobalTrader.Site.Data;t.set_PathToData("controls/Nuggets/SupplierInvoiceMainInfo");t.set_DataObject("SupplierInvoiceMainInfo");t.set_DataAction("GetTaxRate");t.addParameter("TaxNo",n);t.addDataOK(Function.createDelegate(this,this.getTaxRateComplete));t.addError(Function.createDelegate(this,this.getTaxRateError));t.addTimeout(Function.createDelegate(this,this.getTaxRateError));$R_DQ.addToQueue(t);$R_DQ.processQueue();t=null},getTaxRateError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},getTaxRateComplete:function(n){n._result&&(this._TaxRate=n._result.Rate)},validateTaxRate:function(){var n=!0;return parseFloat(this._TaxRate)<=0?(n=parseFloat(this.getFieldValue("ctlTax"))==0,n||(n=confirm($R_RES.TaxValueMessage))):(n=parseFloat(this.getFieldValue("ctlTax"))>0,n||(n=confirm($R_RES.TaxValueMessage))),n||this.showError(!0),n}};Rebound.GlobalTrader.Site.Controls.Forms.ClientInvoiceAdd_Add.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.ClientInvoiceAdd_Add",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);