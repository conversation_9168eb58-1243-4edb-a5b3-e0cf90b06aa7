Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.CRMAMainInfo_Edit=function(n){Rebound.GlobalTrader.Site.Controls.Forms.CRMAMainInfo_Edit.initializeBase(this,[n]);this._intCRMAID=-1;this._hidRaisedByNo=0;this._IsHubAutoCRMA=!1;this._hidShipViaNo=0;this._ShipVia=-1;this._RaisedBy=-1;this._clientWarehouseNo=-1};Rebound.GlobalTrader.Site.Controls.Forms.CRMAMainInfo_Edit.prototype={get_intCRMAID:function(){return this._intCRMAID},set_intCRMAID:function(n){this._intCRMAID!==n&&(this._intCRMAID=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.CRMAMainInfo_Edit.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown))},formShown:function(){this._blnFirstTimeShown&&this.addSave(Function.createDelegate(this,this.saveClicked));this.getFieldDropDownData("ctlDivision");this.getFieldDropDownData("ctlWarehouse");this.getFieldDropDownData("ctlShipVia");this.getFieldDropDownData("ctlAuthorisedBy");this.getFieldDropDownData("ctlIncoterm")},dispose:function(){this.isDisposed||(this._intCRMAID=null,Rebound.GlobalTrader.Site.Controls.Forms.CRMAMainInfo_Edit.callBaseMethod(this,"dispose"))},saveClicked:function(){if(this.validateForm()){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/CRMAMainInfo");n.set_DataObject("CRMAMainInfo");n.set_DataAction("SaveEdit");n.addParameter("id",this._intCRMAID);n.addParameter("DivisionNo",this.getFieldValue("ctlDivision"));this._clientWarehouseNo<=0?n.addParameter("WarehouseNo",this.getFieldValue("ctlWarehouse")):n.addParameter("WarehouseNo",this._clientWarehouseNo);this._hidRaisedByNo<=0?n.addParameter("AuthorisedBy",this.getFieldValue("ctlAuthorisedBy")):n.addParameter("AuthorisedBy",this._hidRaisedByNo);n.addParameter("RMADate",this.getFieldValue("ctlRMADate"));this._hidShipViaNo<=0?n.addParameter("ShipVia",this.getFieldValue("ctlShipVia")):n.addParameter("ShipVia",this._hidShipViaNo);n.addParameter("ShippingAccount",this.getFieldValue("ctlShippingAccount"));n.addParameter("Notes",this.getFieldValue("ctlNotes"));n.addParameter("Instructions",this.getFieldValue("ctlInstructions"));n.addParameter("Incoterm",this.getFieldValue("ctlIncoterm"));n.addParameter("CustomerRejectionNo",this.getFieldValue("ctlCustomerRejectionNo"));n.addDataOK(Function.createDelegate(this,this.saveEditComplete));n.addError(Function.createDelegate(this,this.saveEditError));n.addTimeout(Function.createDelegate(this,this.saveEditError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null}},saveEditError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},saveEditComplete:function(n){n._result.Result==!0?this.onSaveComplete():(this._strErrorMessage=n._errorMessage,this.onSaveError())},validateForm:function(){this.onValidate();var n=!0;return this.checkFieldEntered("ctlDivision")||(n=!1),this.checkFieldEntered("ctlRMADate")||(n=!1),this._clientWarehouseNo<=0&&(this.checkFieldEntered("ctlWarehouse")||(n=!1)),this._hidShipViaNo<=0&&(this.checkFieldEntered("ctlShipVia")||(n=!1)),this._hidRaisedByNo<=0&&(this.checkFieldEntered("ctlAuthorisedBy")||(n=!1)),this.checkFieldEntered("ctlIncoterm")||(n=!1),n||this.showError(!0),n}};Rebound.GlobalTrader.Site.Controls.Forms.CRMAMainInfo_Edit.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CRMAMainInfo_Edit",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);