///<reference name="MicrosoftAjax.js" />
///<reference path="~/Common/Data/Data.js" />
///<reference path="~/Common/Functions/Functions.js" />
///<reference path="~/Controls/IconButton/IconButton.js" />
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls");

Rebound.GlobalTrader.Site.Controls.TimeSelect = function(element) { 
	Rebound.GlobalTrader.Site.Controls.TimeSelect.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.TimeSelect.prototype = {

	get_ddlMinutes: function() { return this._ddlMinutes; }, 	set_ddlMinutes: function(value) { if (this._ddlMinutes !== value)  this._ddlMinutes = value; }, 
	get_ddlHour: function() { return this._ddlHour; }, 	set_ddlHour: function(value) { if (this._ddlHour !== value)  this._ddlHour = value; }, 

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.TimeSelect.callBaseMethod(this, "initialize");
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this.get_element()) $clearHandlers(this.get_element());
		this._ddlMinutes = null;
		this._ddlHour = null;
		Rebound.GlobalTrader.Site.Controls.TimeSelect.callBaseMethod(this, "dispose");
		this.isDisposed = true;
	},
	
	setValue: function(strValue) {
		var strHour = strValue.substr(0, 2);
		for (var i = 0; i < this._ddlHour.options.length; i++) {
			this._ddlHour.options[i].selected = (this._ddlHour.options[i].value == strHour);
		}
		var strMins = strValue.substring(3);
		for (i = 0; i < this._ddlMinutes.options.length; i++) {
			this._ddlMinutes.options[i].selected = (this._ddlMinutes.options[i].value == strMins);
		}
	},
	
	getValue: function() {
		return String.format("{0}:{1}", this.getHours(), this.getMinutes());
	},
	
	getHours: function() {
		return this._ddlHour.options[this._ddlHour.selectedIndex].value;
	},

	getMinutes: function() {
		return this._ddlMinutes.options[this._ddlMinutes.selectedIndex].value;
	}	
};

Rebound.GlobalTrader.Site.Controls.TimeSelect.registerClass("Rebound.GlobalTrader.Site.Controls.TimeSelect", Sys.UI.Control, Sys.IDisposable);