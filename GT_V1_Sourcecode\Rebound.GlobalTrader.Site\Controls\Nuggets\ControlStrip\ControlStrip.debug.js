///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");

Rebound.GlobalTrader.Site.Controls.Nuggets.ControlStrip = function(element) { 
	Rebound.GlobalTrader.Site.Controls.Nuggets.ControlStrip.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.Nuggets.ControlStrip.prototype = {
		
	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.Nuggets.ControlStrip.callBaseMethod(this, "initialize");	
		this.showContent(true);
	},

	dispose: function() { 
		if (this.isDisposed) return;
		Rebound.GlobalTrader.Site.Controls.Nuggets.ControlStrip.callBaseMethod(this, "dispose");
	}	
};

Rebound.GlobalTrader.Site.Controls.Nuggets.ControlStrip.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.ControlStrip", Rebound.GlobalTrader.Site.Controls.Nuggets.Base);
