﻿SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
/*   
===========================================================================================  
TASK			UPDATED BY		DATE			ACTION		DESCRIPTION  
[US-216072]		Trung Pham		11-Dec-2024		CREATE		Insert star rating configuration and Recalculate the Star
[US-216072]		Trung Pham		02-Jan-2024		UPDATE		Remove check supplier to update existing
===========================================================================================  
*/ 
CREATE OR ALTER PROCEDURE usp_insert_StarRating_Config
	@NumberOfPO INT,
	@CreatedBy INT
AS
BEGIN
	DECLARE @InsertedData TABLE (NumOfPO INT);

	INSERT INTO tbStarRatingConfig (NumOfPO, CreatedBy)
	OUTPUT INSERTED.NumOfPO
	INTO @InsertedData
	VALUES (@NumberOfPO, @CreatedBy);

	-- Recalculate the Star for all Supplier - Manufacturer relationship based on Purchase Order every time a new Start Rating setup
	exec usp_insert_ManufacturerLink_All_Client
END
GO