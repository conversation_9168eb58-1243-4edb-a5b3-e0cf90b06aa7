Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.ItemSearch");Rebound.GlobalTrader.Site.Controls.ItemSearch.SIDebitNotes=function(n){Rebound.GlobalTrader.Site.Controls.ItemSearch.SIDebitNotes.initializeBase(this,[n]);this._CompanyNo=0;this._intCount=0;this._arrDebitNumber=[];this._selectedDebits=""};Rebound.GlobalTrader.Site.Controls.ItemSearch.SIDebitNotes.prototype={addPotentialStatusChange:function(n){this.get_events().addHandler("PotentialStatusChange",n)},removePotentialStatusChange:function(n){this.get_events().removeHandler("PotentialStatusChange",n)},onPotentialStatusChange:function(){var n=this.get_events().getHandler("PotentialStatusChange");n&&n(this,Sys.EventArgs.Empty)},initialize:function(){Rebound.GlobalTrader.Site.Controls.ItemSearch.SIDebitNotes.callBaseMethod(this,"initialize");this.addSetupData(Function.createDelegate(this,this.doSetupData));this.addGetDataComplete(Function.createDelegate(this,this.doGetDataComplete))},dispose:function(){this.isDisposed||(this._CompanyNo=null,this._arrDebitNumber=null,this._selectedDebits=null,Rebound.GlobalTrader.Site.Controls.ItemSearch.SIDebitNotes.callBaseMethod(this,"dispose"))},doSetupData:function(){this._objData.set_PathToData("controls/ItemSearch/SIDebitNotes");this._objData.set_DataObject("SIDebitNotes");this._objData.set_DataAction("GetData");this._objData.addParameter("CompanyNo",this._CompanyNo);this._objData.addParameter("AddedDebit",this._selectedDebits);this._objData.addParameter("PurchaseOrderNumber",this.getFieldValue("ctlPurchaseOrderNo"));this._objData.addParameter("DebitDateFrom",this.getFieldValue("ctlDebitNoteDateFrom"));this._objData.addParameter("DebitDateTo",this.getFieldValue("ctlDebitNoteDateTo"));this._intCount+=1},doGetDataComplete:function(){var t,u,n,i,r;for(Array.clear(this._arrDebitNumber),this._tblResults.clearTable(),t=0,u=this._objResult.Results.length;t<u;t++)n=this._objResult.Results[t],i=[this.writeCheckbox(n.ID,t,n.IsDebitNoteSelected),n.DebitNoteNumber,$R_FN.setCleanTextValue(n.DebitAmount),$R_FN.setCleanTextValue(n.Date),n.PurchaseOrderNumber,n.IPONumber,n.SupplierRMANo,$R_FN.setCleanTextValue(n.SupplierNotes)],this._tblResults.addRow(i,n.ID,!1),this.registerCheckBox(n.ID,t,n.IsDebitNoteSelected,!0),r=this.getCheckBox(t),r._element.setAttribute("onClick",String.format('$find("{0}").getCheckedCellValue({1});',this._element.id,t)),n.IsDebitNoteSelected&&this.getCheckedCellValue(t),r=null,i=null,n=null;this.onPotentialStatusChange();this._tblResults.resizeColumns()},writeCheckbox:function(n,t,i){var r=this.getControlID("chk",t),u=this.getControlID("chkImg",t);return String.format('<div class="imageCheckBoxDisabled" id="{0}" ><img id="{1}" class="{2}" src="images/x.gif" style="border-width: 0px;" /> <\/div>',r,u,i?"on":"off")},getControlID:function(n,t){return String.format("{0}_{1}{2}",this._tblResults._element.id,n,t)},registerCheckBox:function(n,t,i,r){var f=this.getControlID("chk",t),e=this.getControlID("chkImg",t),u=this.getCheckBox(t);u&&(u.dispose(),u=null);eval($R_FN.getCreateStatement("Rebound.GlobalTrader.Site.Controls.ImageCheckBox",[["blnChecked",i],["blnEnabled",r],["img",String.format('$get("{0}")',e)]],f))},getCheckBox:function(n){return $find(this.getControlID("chk",n))},getCheckedCellValue:function(n){var r,t,i;this._tblResults&&this._tblResults!="undefined"&&this._tblResults._tbl&&this._tblResults._tbl!="undefined"&&(r=this.getCheckBox(n),t=this._tblResults._tbl.rows[n],t)&&(i=t.cells[1].innerHTML,r._blnChecked?this.addDebitLine(i):this.removeDebitLine(i),this._selectedDebits=$R_FN.arrayToSingleString(this._arrDebitNumber,"/"),this.onPotentialStatusChange())},addDebitLine:function(n){Array.add(this._arrDebitNumber,n)},removeDebitLine:function(n){Array.remove(this._arrDebitNumber,n)}};Rebound.GlobalTrader.Site.Controls.ItemSearch.SIDebitNotes.registerClass("Rebound.GlobalTrader.Site.Controls.ItemSearch.SIDebitNotes",Rebound.GlobalTrader.Site.Controls.ItemSearch.Base,Sys.IDisposable);