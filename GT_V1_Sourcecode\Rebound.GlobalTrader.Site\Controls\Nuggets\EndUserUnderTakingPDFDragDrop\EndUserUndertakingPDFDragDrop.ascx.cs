﻿using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site.Controls;
using Rebound.GlobalTrader.Site;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets
{
    public partial class EndUserUndertakingPDFDragDrop : Base
    {
        #region Locals

        protected Panel _pnlPDFDocuments;
        // protected IconButton _ibtnAdd;

        protected HiddenField _hidSection;

        #endregion

        #region Properties

        private int _sectionID;
        public int sectionID
        {
            get { return _sectionID; }
            set { _sectionID = value; }
        }

        private bool _blnCanAdd = true;
        public bool CanAdd
        {
            get { return _blnCanAdd; }
            set { _blnCanAdd = value; }
        }

        private bool _blnCanDelete = true;
        public bool CanDelete
        {
            get { return _blnCanDelete; }
            set { _blnCanDelete = value; }
        }

        private string _strSectionName;
        public string SectionName
        {
            get { return _strSectionName; }
            set { _strSectionName = value; }
        }
        private bool _blnIsPDFAvailable;
        public bool IsPDFAvailable
        {
            get { return _blnIsPDFAvailable; }
            set { _blnIsPDFAvailable = value; }
        }

        #endregion

        #region Overrides

        /// <summary>
        /// Oninit
        /// </summary>
        /// <param name="e"></param>
        protected override void OnInit(EventArgs e)
        {
            base.OnInit(e);
            WireUpControls();
            AddScriptReference("Controls.Nuggets.EndUserUndertakingPDFDragDrop.EndUserUndertakingPDFDragDrop.js");
            TitleText = Functions.GetGlobalResource("Nuggets", "EUUPDFTitle");
        }

        protected override void OnPreRender(EventArgs e)
        {
            //_ibtnAdd.Visible = _blnCanAdd;
            SetupScriptDescriptors();
            base.OnPreRender(e);
        }

        #endregion

        /// <summary>
        /// Sets up AJAX script descriptors
        /// </summary>
        private void SetupScriptDescriptors()
        {
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Nuggets.EndUserUndertakingPDFDragDrop", ctlDesignBase.ClientID);
            _scScriptControlDescriptor.AddProperty("sectionID", _sectionID);
            _scScriptControlDescriptor.AddElementProperty("pnlPDFDocuments", _pnlPDFDocuments.ClientID);
            // if (_blnCanAdd) _scScriptControlDescriptor.AddElementProperty("ibtnAdd", _ibtnAdd.ClientID);
            _scScriptControlDescriptor.AddProperty("blnCanDelete", _blnCanDelete);
            _scScriptControlDescriptor.AddProperty("blnCanAdd", _blnCanAdd);
            _scScriptControlDescriptor.AddProperty("intMaxPDFDocuments", SettingsManager.GetSetting_Int(SettingItem.List.MaxPDFDocuments));
            _scScriptControlDescriptor.AddProperty("IsPDFAvailable", _blnIsPDFAvailable);  // [001]
            _scScriptControlDescriptor.AddProperty("strSectionName", SectionName);
            _hidSection.Value = SectionName;

        }

        /// <summary>
        /// Wire up controls from the ascx
        /// </summary>
        private void WireUpControls()
        {
            _pnlPDFDocuments = (Panel)FindContentControl("pnlPDFDocuments");
            _hidSection = (HiddenField)FindContentControl("hidSection");
            // _ibtnAdd = FindIconButton("ibtnAdd");

        }
    }
}