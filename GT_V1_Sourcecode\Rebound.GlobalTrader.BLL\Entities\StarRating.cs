﻿using Rebound.GlobalTrader.DAL;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Rebound.GlobalTrader.BLL
{
    public partial class StarRating : BizObject
    {
        #region Properties

        protected static DAL.StarRatingElement Settings
        {
            get { return Globals.Settings.StarRating; }
        }
        /// <summary>
        /// Number of Purchase Order
        /// </summary>
        public int NumOfPO { get; set; }
        /// <summary>
        /// Counted Star
        /// </summary>
        public byte CountedStar { get; set; }
        /// <summary>
        /// Created Date
        /// </summary>
        public DateTime? CreatedDate { get; set; }
        /// <summary>
        /// Created by
        /// </summary>
        public string CreatedBy { get; set; }
        #endregion

        #region Methods

        /// <summary>
        /// Calls [usp_insert_StarRating_Config]
        /// </summary>
        /// <param name="numOfPO"></param>
        /// <returns></returns>
        public static void Insert(int numOfPO, int createdBy)
        {
            Rebound.GlobalTrader.DAL.SiteProvider.StarRating.Insert(numOfPO, createdBy);
        }

        /// <summary>
        /// Get List Star Rating Configuration
        /// Calls [usp_selectAll_StarRating_Configs]
        /// </summary>
        public static List<StarRating> GetList()
        {
            List<StarRatingDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.StarRating.GetList();
            if (lstDetails == null)
            {
                return new List<StarRating>();
            }
            else
            {
                return lstDetails.Select(objDetails => new StarRating
                {
                    NumOfPO = objDetails.NumOfPO,
                    CountedStar = objDetails.CountedStar,
                    CreatedDate = objDetails.CreatedDate,
                    CreatedBy = objDetails.CreatedBy
                }).ToList();
            }
        }
        #endregion
    }
}
