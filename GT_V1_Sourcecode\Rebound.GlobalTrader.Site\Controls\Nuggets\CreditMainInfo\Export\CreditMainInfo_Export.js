Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.CreditMainInfo_Export=function(n){Rebound.GlobalTrader.Site.Controls.Forms.CreditMainInfo_Export.initializeBase(this,[n]);this._intCreditID=0;this._strTitle_Export=null;this._strTitle_Release=null;this._lblExplainExport=null;this._lblExplainRelease=null;this._ctlConfirm=null};Rebound.GlobalTrader.Site.Controls.Forms.CreditMainInfo_Export.prototype={get_intCreditID:function(){return this._intCreditID},set_intCreditID:function(n){this._intCreditID!==n&&(this._intCreditID=n)},get_strTitle_Export:function(){return this._strTitle_Export},set_strTitle_Export:function(n){this._strTitle_Export!==n&&(this._strTitle_Export=n)},get_strTitle_Release:function(){return this._strTitle_Release},set_strTitle_Release:function(n){this._strTitle_Release!==n&&(this._strTitle_Release=n)},get_lblExplainExport:function(){return this._lblExplainExport},set_lblExplainExport:function(n){this._lblExplainExport!==n&&(this._lblExplainExport=n)},get_lblExplainRelease:function(){return this._lblExplainRelease},set_lblExplainRelease:function(n){this._lblExplainRelease!==n&&(this._lblExplainRelease=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.CreditMainInfo_Export.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown))},formShown:function(){this._blnFirstTimeShown&&(this._ctlConfirm=this.getFieldComponent("ctlConfirm"),this._ctlConfirm.addClickYesEvent(Function.createDelegate(this,this.yesClicked)),this._ctlConfirm.addClickNoEvent(Function.createDelegate(this,this.noClicked)));this.checkMode()},dispose:function(){this.isDisposed||(this._ctlConfirm&&this._ctlConfirm.dispose(),this._ctlConfirm=null,this._intInvoiceID=null,this._strTitle_Export=null,this._strTitle_Release=null,this._lblExplainExport=null,this._lblExplainRelease=null,Rebound.GlobalTrader.Site.Controls.Forms.CreditMainInfo_Export.callBaseMethod(this,"dispose"))},yesClicked:function(){var n,t;this.showSaving(!0);n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("Controls/Nuggets/CreditMainInfo");n.set_DataObject("CreditMainInfo");n.set_DataAction("ExportRelease");n.addParameter("id",this._intCreditID);t=this._mode=="EXPORT";n.addParameter("Exported",t);n.addDataOK(Function.createDelegate(this,this.saveComplete));n.addError(Function.createDelegate(this,this.saveError));n.addTimeout(Function.createDelegate(this,this.saveError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null;this.onSave()},noClicked:function(){this.showSaving(!1);this.onNotConfirmed()},saveError:function(n){this.showSaving(!1);this._strErrorMessage=n._errorMessage;this.onSaveError()},saveComplete:function(n){this.showSaving(!1);n._result.Result==!0?this.onSaveComplete():(this._strErrorMessage=n._errorMessage,this.onSaveError())},checkMode:function(){switch(this._mode){case"EXPORT":this.changeTitle(this._strTitle_Export);break;case"RELEASE":this.changeTitle(this._strTitle_Release)}$R_FN.showElement(this._lblExplainExport,this._mode=="EXPORT");$R_FN.showElement(this._lblExplainRelease,this._mode=="RELEASE")}};Rebound.GlobalTrader.Site.Controls.Forms.CreditMainInfo_Export.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CreditMainInfo_Export",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);