///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// add comment here on any change
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");

Rebound.GlobalTrader.Site.Controls.DropDowns.EnhancedInspection = function (element) {
    Rebound.GlobalTrader.Site.Controls.DropDowns.EnhancedInspection.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.DropDowns.EnhancedInspection.prototype = {

    initialize: function () {
        Rebound.GlobalTrader.Site.Controls.DropDowns.EnhancedInspection.callBaseMethod(this, "initialize");
        this.addSetupDataCall(Function.createDelegate(this, this.setupDataCall));
        this.addGetDataOK(Function.createDelegate(this, this.dataCallOK));
    },

    dispose: function () {
        if (this.isDisposed) return;
        Rebound.GlobalTrader.Site.Controls.DropDowns.EnhancedInspection.callBaseMethod(this, "dispose");
    },

    setupDataCall: function () {
        this._objData.set_PathToData("controls/DropDowns/EnhancedInspection");
        this._objData.set_DataObject("EnhancedInspection");
        this._objData.set_DataAction("GetData");
    },

    dataCallOK: function () {
        var result = this._objData._result;
        if (result != null) {
            if (result.Types) {
                for (var i = 0; i < result.Types.length; i++) {
                    this.addOption(result.Types[i].Name, result.Types[i].ID);
                }
            }
        }
    }

};

Rebound.GlobalTrader.Site.Controls.DropDowns.EnhancedInspection.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.EnhancedInspection", Rebound.GlobalTrader.Site.Controls.DropDowns.Base);
