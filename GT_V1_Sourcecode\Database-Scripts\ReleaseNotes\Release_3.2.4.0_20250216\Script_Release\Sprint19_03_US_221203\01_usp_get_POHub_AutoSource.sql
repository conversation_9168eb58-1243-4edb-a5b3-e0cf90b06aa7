﻿  
/*   
===========================================================================================  
TASK        UPDATED BY       DATE          ACTION    DESCRIPTION  
[US-214615]  An.TranTan   14-Nov-2024  CREATE   Replace usp_get_Top2_AutoSource:   
                 - add filter  
                 - add new column SourcingResultNo: if auto source added to requirement  
[US-214615]  An.TranTan   26-Nov-2024  UPDATE   Update date range filter condition for SS/RL/Offer  
[US-214615]  An.TranTan   27-Nov-2024  UPDATE   Update get supplier name from tbCompany 
[US-221203]  CuongDoX   7-Jan-2025  UPDATE   Update Lytica from selected  
===========================================================================================  
*/  
CREATE   OR ALTER    PROCEDURE [dbo].[usp_get_POHub_AutoSource]  
 @ClientNo INT = 114 --DMCC  
 ,@CustomerRequirementId INT  
 ,@TableLength INT = 10  
 ,@SortIndex INT = 2 --sort by sourcing type as default  
 ,@SortDir INT = 1 -- 1: ASC, 2: DESC  
 ,@LoginID INT = 0  
WITH RECOMPILE  
AS  
BEGIN  
 SET NOCOUNT ON  
 --Declare variables  
 DECLARE @TargetManufacturerNo INT,  
   @TargetManufacturerName NVARCHAR(256) = NULL,  
   @IsRestrictMfr INT = 0,  
   @TargetQuantity INT,  
   @MaxRecordRequired INT = 10, --get max 10 records  
   @FromDate DATE = dbo.ufn_get_date_from_datetime(DATEADD(month, -12, GETDATE())),  
   @ToDate DATE = GETDATE(),  
   @CurrencyCode NVARCHAR(5) = NULL,  
   @TargetFullPart NVARCHAR(30),
   @LyticaManufacturerRef NVARCHAR(200)

 DECLARE @IHSAveragePrice FLOAT = NULL,     
   @IHSPartStatus NVARCHAR(60) = NULL,    
   @IHSResult NVARCHAR(MAX) = NULL;    
 DECLARE @LyticaAveragePrice FLOAT = NULL,     
   @LyticaMarketLeading FLOAT = NULL,    
   @LyticaTargetPrice FLOAT = NULL,    
   @LyticaStatus VARCHAR(200) = NULL,    
   @LyticaResult NVARCHAR(MAX) = NULL;  
   
 --Get DMCC currency  
 SELECT @CurrencyCode = cr.CurrencyCode  
 FROM tbCurrency cr WITH(NOLOCK)  
 JOIN tbClient cl WITH(NOLOCK) ON cl.CurrencyNo = cr.CurrencyId  
 WHERE cl.ClientId = @ClientNo;  
  
 --Get target part no  
 SELECT @TargetFullPart = FullPart
 ,@LyticaManufacturerRef = LyticaManufacturerRef 
 FROM tbCustomerRequirement WITH(NOLOCK) WHERE CustomerRequirementId = @CustomerRequirementId;  
  
 IF OBJECT_ID('tempdb..#finalResults') IS NOT NULL  
  DROP TABLE #finalResults  
 IF OBJECT_ID('tempdb..#tempOffer') IS NOT NULL  
  DROP TABLE #tempOffer  
 CREATE TABLE #tempOffer  
 (  
  ID INT,  
  ClientNo INT,  
  PartNo NVARCHAR(30),  
  ManufacturerNo INT,  
  ManufacturerCode NVARCHAR(10),  
  ManufacturerName NVARCHAR(256),  
  ProductNo INT,  
  ProductName NVARCHAR(100),  
  ProductDescription NVARCHAR(256),  
  SupplierName NVARCHAR(256),  
  DateOrdered DATETIME,  
  Quantity INT,  
  BuyPrice FLOAT,  
  SellPrice FLOAT,  
  CurrencyCode NVARCHAR(5),  
  DivisionStatus INT, --for stock  
  IsSourcingHub BIT, --for strategic stock/Reverse Logistic Stock/ Offer  
  ROHS TINYINT,  
  SourcingType VARCHAR(10),  
  SourcingTypeDescription VARCHAR(50), --Stock/ Strategic Stock/ Reverse Logistic Stock/ Offer  
  SourcingTypeOrder INT  
 );  
  
 --get the target manufacturer and quantity,   
 SELECT   
  @TargetManufacturerNo = ISNULL(cr.ManufacturerNo, 0),  
  @TargetManufacturerName = ISNULL(mf.ManufacturerName, '')  
  --@TargetQuantity = Quantity  
 FROM tbCustomerRequirement cr WITH(NOLOCK)  
 LEFT JOIN tbManufacturer mf WITH(NOLOCK) on mf.ManufacturerId = cr.ManufacturerNo  
 WHERE cr.CustomerRequirementId = @CustomerRequirementId;  
  
 --check if manufacturer is restricted (for add to requirement action)  
 IF EXISTS (SELECT TOP 1 1 FROM tbRestrictedManufacturer WHERE ManufacturerNo = @TargetManufacturerNo)  
  SET @IsRestrictMfr = CAST(1 AS BIT);  
  
 --get stocks matching part, manufacturer within 12 months (refer usp_source_Stock)  
 ;WITH cteMatchingStock AS (  
  SELECT   
   st.StockId,  
   st.Part,  
   st.StockDate,  
   cu.CurrencyId,  
   st.ManufacturerNo,  
   st.ProductNo,  
   st.ClientNo,  
   st.ROHS,  
   (CASE st.Unavailable WHEN 0  
    THEN (  
      st.QuantityInStock + st.QuantityOnOrder - isnull((  
        SELECT sum(QuantityAllocated)  
        FROM tbAllocation al  
        WHERE al.StockNo = st.StockId  
        ), 0)  
      )  
    ELSE 0 END  
   ) AS QuantityAvailable,  
   dbo.ufn_convert_to_HUB_currency(st.ResalePrice, cu.CurrencyId, st.StockDate) AS ConvertedBuyPrice,  
   CASE WHEN isnull(st.ClientUpLiftPrice, 0) = 0 THEN isnull(st.landedCost, 0)  
    ELSE st.ClientUpLiftPrice  
   END AS ClientUpLiftPrice  
   , co.CompanyName as SupplierName  
  FROM tbStock st WITH(NOLOCK)  
  JOIN dbo.tbClient cl WITH(NOLOCK) ON cl.ClientId = st.ClientNo  
  JOIN dbo.tbCurrency cu WITH(NOLOCK) ON cu.CurrencyId = cl.CurrencyNo  
  LEFT JOIN dbo.tbPurchaseOrder AS po WITH(NOLOCK) ON po.PurchaseOrderId = st.PurchaseOrderNo  
  LEFT JOIN dbo.tbCompany AS co WITH(NOLOCK) ON po.CompanyNo = co.CompanyId  
  WHERE   
   (  
    st.ClientNo = @ClientNo   
    OR (cl.OwnDataVisibleToOthers = 1 AND st.ClientNo <> 109)  
    OR (isnull(st.ClientUpLiftPrice, 0) > 0)  
   )  
   --AND (st.FullPart LIKE @PartSearch OR st.FullSupplierPart LIKE @PartSearch)  
   AND (st.FullPart = @TargetFullPart OR st.FullSupplierPart = @TargetFullPart)  
   AND (st.QuantityInStock > 0 OR st.QuantityOnOrder > 0)  
   AND (@TargetManufacturerNo = 0 OR ISNULL(st.ManufacturerNo, 0) = @TargetManufacturerNo)  
   AND (dbo.ufn_get_date_from_datetime(st.StockDate) between @FromDate AND @ToDate)  
 )  
 INSERT INTO #tempOffer  
 (  
  ID,  
  ClientNo,  
  PartNo,  
  ManufacturerNo,  
  ManufacturerCode,  
  ManufacturerName,  
  ProductNo,  
  ProductName,  
  ProductDescription,  
  SupplierName,  
  DateOrdered,  
  Quantity,  
  BuyPrice,  
  SellPrice,  
  CurrencyCode,  
  DivisionStatus,  
  ROHS,  
  SourcingType,  
  SourcingTypeDescription,  
  SourcingTypeOrder  
 )  
 --SELECT TOP (@MaxRecordRequired)  
 SELECT  
  cte.StockId,  
  cte.ClientNo,  
  cte.Part,  
  mf.ManufacturerId,  
  mf.ManufacturerCode,  
  mf.ManufacturerName,  
  pr.ProductId,  
  pr.ProductName,  
  pr.ProductDescription,  
  cte.SupplierName,  
  cte.StockDate, --DateOrdered  
  cte.QuantityAvailable,  
  cte.ConvertedBuyPrice,  
  dbo.ufn_convert_to_HUB_currency(cte.ClientUpLiftPrice, cte.CurrencyId, cte.StockDate), --sale price  
  @CurrencyCode,  
  CASE WHEN cte.ClientNo = @ClientNo AND cte.QuantityAvailable > 0 THEN 1  
   ELSE 0  
  END AS DivisionStatus, --Division Status  
  cte.ROHS,  
  'HUBSTK',  
  'Stock',  
  1 AS SourcingTypeOrder  
 FROM cteMatchingStock cte  
 LEFT JOIN dbo.tbProduct pr WITH(NOLOCK) ON cte.ProductNo = pr.ProductId  
 LEFT JOIN dbo.tbManufacturer mf WITH(NOLOCK) ON cte.ManufacturerNo = mf.ManufacturerId  
 --ORDER BY cte.ConvertedBuyPrice ASC,  
 -- DivisionStatus DESC;  
  
 --get matching strategic stock: part, manufacturer, quantity within 12 months (refer: usp_ipobom_source_Epo)  
 --IF (SELECT COUNT(*) FROM #finalResults) < @MaxRecordRequired  
 --BEGIN  
  ;WITH cteMatchingStrategicStock AS(  
   SELECT   
    o.EpoId,  
    o.Part,  
    o.OriginalEntryDate,  
    o.Quantity,  
    o.CurrencyNo,  
    o.ManufacturerNo,  
    o.ProductNo,  
    o.ClientNo,  
    o.ROHS,  
    CASE WHEN o.ClientNo = @ClientNo THEN 1 ELSE 0  
    END AS IsCurrentClient,  
    dbo.ufn_convert_to_HUB_currency(o.Price, o.CurrencyNo, o.OriginalEntryDate) AS ConvertedBuyPrice,  
    dbo.ufn_convert_to_HUB_currency(o.UpliftPrice, o.CurrencyNo, o.OriginalEntryDate) AS ConvertedSellPrice  
    , o.SupplierName  
    , o.SupplierNo  
   FROM [BorisGlobalTraderImports].dbo.tbEpo o WITH (NOLOCK)  
   JOIN tbClient cl WITH (NOLOCK)  
              ON o.ClientNo = cl.ClientId  
   WHERE (o.ClientNo = @ClientNo OR cl.OwnDataVisibleToOthers = 1)  
    --AND o.FullPart LIKE @PartSearch  
    AND o.FullPart = @TargetFullPart  
    AND (@TargetManufacturerNo = 0 OR ISNULL(o.ManufacturerNo, 0) = @TargetManufacturerNo)   
    AND (dbo.ufn_get_date_from_datetime(ISNULL(o.EpoStatusChangeDate, o.OriginalEntryDate)) between @FromDate AND @ToDate)  
  )  
  INSERT INTO #tempOffer  
  (  
   ID,  
   ClientNo,  
   PartNo,  
   ManufacturerNo,  
   ManufacturerCode,  
   ManufacturerName,  
   ProductNo,  
   ProductName,  
   ProductDescription,  
   SupplierName,  
   DateOrdered,  
   Quantity,  
   BuyPrice,  
   SellPrice,  
   CurrencyCode,  
   IsSourcingHub,  
   ROHS,  
   SourcingType,   
   SourcingTypeDescription, --Stock/ Strategic Stock/ Reverse Logistic Stock/ Offer  
   SourcingTypeOrder  
  )  
  --SELECT TOP (@MaxRecordRequired)  
  SELECT  
   cte.EpoId,  
   cte.ClientNo,  
   cte.Part,  
   mf.ManufacturerId,  
   mf.ManufacturerCode,  
   mf.ManufacturerName,  
   pr.ProductId,  
   pr.ProductName,  
   pr.ProductDescription,  
   ISNULL(co.CompanyName, cte.SupplierName),  
   cte.OriginalEntryDate,  
   cte.Quantity,  
   cte.ConvertedBuyPrice,  
   cte.ConvertedSellPrice,  
   @CurrencyCode,  
   CAST(0 AS BIT),  
   cte.ROHS,  
   'EPPH',  
   'Strategic Stock',  
   2  
  FROM cteMatchingStrategicStock cte  
  LEFT JOIN dbo.tbManufacturer mf WITH (NOLOCK)  
   ON cte.ManufacturerNo = mf.ManufacturerId  
  LEFT JOIN dbo.tbProduct pr WITH(NOLOCK) ON cte.ProductNo = pr.ProductId  
  LEFT JOIN dbo.tbCompany co WITH(NOLOCK) ON co.CompanyId = cte.SupplierNo  
  --ORDER BY cte.ConvertedBuyPrice ASC,  
  -- cte.IsCurrentClient DESC;   
 --END  
  
 --get matching Reverse Logistic Stock: part, manufacturer, quantity within 12 months  
 --refer: [usp_ipobom_source_ReverseLogistic]  
 --IF (SELECT COUNT(*) FROM #finalResults) < @MaxRecordRequired  
 --BEGIN  
  ;WITH cteMatchingRL AS(  
   SELECT   
    o.ReverseLogisticId,  
    o.Part,  
    o.OriginalEntryDate,  
    o.Quantity,  
    o.CurrencyNo,  
    o.ManufacturerNo,  
    o.ProductNo,  
    o.ClientNo,  
    o.ROHS,  
    CASE WHEN o.ClientNo = @ClientNo THEN 1 ELSE 0  
    END AS IsCurrentClient,  
    dbo.ufn_convert_to_HUB_currency(o.Price, o.CurrencyNo, o.OriginalEntryDate) AS ConvertedBuyPrice,  
    dbo.ufn_convert_to_HUB_currency(o.UpliftPrice, o.CurrencyNo, o.OriginalEntryDate) AS ConvertedSellPrice  
    ,o.SupplierName  
    ,o.SupplierNo  
   FROM [BorisGlobalTraderImports].dbo.tbReverseLogistic o WITH (NOLOCK)  
   JOIN tbClient cl WITH (NOLOCK)   
    ON o.ClientNo = cl.ClientId  
   WHERE (o.ClientNo = @ClientNo OR cl.OwnDataVisibleToOthers = 1)  
    --AND o.FullPart LIKE @PartSearch  
    AND o.FullPart = @TargetFullPart  
    AND (@TargetManufacturerNo = 0 OR ISNULL(o.ManufacturerNo, 0) = @TargetManufacturerNo)  
    AND (dbo.ufn_get_date_from_datetime(ISNULL(o.ReverseLogisticStatusChangeDate, o.OriginalEntryDate)) between @FromDate AND @ToDate)  
  )  
  INSERT INTO #tempOffer  
  (  
   ID,  
   ClientNo,  
   PartNo,  
   ManufacturerNo,  
   ManufacturerCode,  
   ManufacturerName,  
   ProductNo,  
   ProductName,  
   ProductDescription,  
   SupplierName,  
   DateOrdered,  
   Quantity,  
   BuyPrice,  
   SellPrice,  
   CurrencyCode,  
   IsSourcingHub,  
   ROHS,  
   SourcingType,  
   SourcingTypeDescription, --Stock/ Strategic Stock/ Reverse Logistic Stock/ Offer  
   SourcingTypeOrder  
  )  
  --SELECT TOP (@MaxRecordRequired)  
  SELECT  
   cte.ReverseLogisticId,  
   cte.ClientNo,  
   cte.Part,  
   mf.ManufacturerId,  
   mf.ManufacturerCode,  
   mf.ManufacturerName,  
   pr.ProductId,  
   pr.ProductName,  
   pr.ProductDescription,  
   ISNULL(co.CompanyName, cte.SupplierName),  
   cte.OriginalEntryDate,  
   cte.Quantity,  
   cte.ConvertedBuyPrice,  
   cte.ConvertedSellPrice,  
   @CurrencyCode,  
   CAST(0 AS BIT),  
   cte.ROHS,  
   'RLPH',  
   'Reverse Logistic',  
   3  
  FROM cteMatchingRL cte  
  LEFT JOIN dbo.tbManufacturer mf WITH (NOLOCK)  
   ON cte.ManufacturerNo = mf.ManufacturerId  
  LEFT JOIN dbo.tbProduct pr WITH(NOLOCK) ON cte.ProductNo = pr.ProductId  
  LEFT JOIN dbo.tbCompany co WITH(NOLOCK) ON co.CompanyId = cte.SupplierNo  
  ORDER BY cte.ConvertedBuyPrice ASC, cte.IsCurrentClient DESC;  
 --END  
  
 --get matching offer: part, manufacturer, quantity within 12 months  
 --refer usp_IPOBOM_Source_OfferPH  
 --IF (SELECT COUNT(*) FROM #finalResults) < @MaxRecordRequired  
 --BEGIN  
  ;WITH cteMatchingOffer AS(  
   SELECT   
    o.OfferId,  
    o.Part,  
    dbo.ufn_convert_to_HUB_currency(o.Price, o.CurrencyNo, o.OriginalEntryDate) AS ConvertedBuyPrice,  
    o.ManufacturerNo,  
    o.ProductNo,  
    o.OriginalEntryDate,  
    o.Quantity,  
    o.ClientNo,  
    CAST(0 AS BIT) AS IsHub,  
    o.ROHS,  
    CASE WHEN o.ClientNo = @ClientNo THEN 1 ELSE 0  END AS IsCurrentClient  
    ,o.SupplierName  
    ,o.SupplierNo  
   FROM [BorisGlobalTraderImports].dbo.tbOffer o WITH (NOLOCK)  
   WHERE   
    --o.FullPart LIKE @PartSearch  
    o.FullPart = @TargetFullPart  
    AND (@TargetManufacturerNo = 0 OR ISNULL(o.ManufacturerNo, 0) = @TargetManufacturerNo)  
    AND (dbo.ufn_get_date_from_datetime(ISNULL(o.OfferStatusChangeDate, o.OriginalEntryDate)) between @FromDate AND @ToDate)  
   UNION ALL  
   SELECT   
    vw.OfferId,  
    vw.Part,  
    dbo.ufn_convert_to_HUB_currency(vw.Price, vw.CurrencyNo, vw.OriginalEntryDate) AS ConvertedBuyPrice,  
    vw.ManufacturerNo,  
    vw.ProductNo,  
    vw.OriginalEntryDate,  
    vw.Quantity,  
    vw.ClientNo,  
    vw.ishub AS IsHub,  
    vw.ROHS,  
    CASE WHEN vw.ClientNo = @ClientNo THEN 1 ELSE 0  END AS IsCurrentClient  
    ,vw.SupplierName  
    ,vw.SupplierNo  
   FROM [vwSourcingData] vw  
   WHERE  
    --vw.FullPart LIKE @PartSearch  
    vw.FullPart  = @TargetFullPart  
    AND (@TargetManufacturerNo = 0 OR ISNULL(vw.ManufacturerNo, 0) = @TargetManufacturerNo)  
    AND (dbo.ufn_get_date_from_datetime(ISNULL(vw.OfferStatusChangeDate, vw.OriginalEntryDate)) between @FromDate AND @ToDate)  
  )  
  INSERT INTO #tempOffer  
  (  
   ID,  
   ClientNo,  
   PartNo,  
   ManufacturerNo,  
   ManufacturerCode,  
   ManufacturerName,  
   ProductNo,  
   ProductName,  
   ProductDescription,  
   SupplierName,  
   DateOrdered,  
   Quantity,  
   BuyPrice,  
   SellPrice,  
   CurrencyCode,  
   IsSourcingHub,  
   ROHS,  
   SourcingType,  
   SourcingTypeDescription, --Stock/ Strategic Stock/ Reverse Logistic Stock/ Offer  
   SourcingTypeOrder  
  )  
  SELECT TOP (@MaxRecordRequired)  
   cte.OfferId,  
   cte.ClientNo,  
   cte.Part,  
   mf.ManufacturerId,  
   mf.ManufacturerCode,  
   mf.ManufacturerName,  
   pr.ProductId,  
   pr.ProductName,  
   pr.ProductDescription,  
   ISNULL(co.CompanyName, cte.SupplierName),  
   cte.OriginalEntryDate,  
   cte.Quantity,  
   cte.ConvertedBuyPrice,  
   0,  
   @CurrencyCode,  
   cte.IsHub,  
   cte.ROHS,  
   'OFPH',  
   'Offer',  
   4  
  FROM cteMatchingOffer cte  
  LEFT JOIN tbManufacturer mf WITH(NOLOCK) ON mf.ManufacturerId = cte.ManufacturerNo  
  LEFT JOIN tbProduct pr WITH(NOLOCK) ON pr.ProductId = cte.ProductNo  
  LEFT JOIN dbo.tbCompany co WITH(NOLOCK) ON co.CompanyId = cte.SupplierNo  
  ORDER BY cte.ConvertedBuyPrice ASC, cte.IsCurrentClient DESC;  
 --END  
  
 --IHS  
 SELECT TOP 1   
  @IHSAveragePrice = ISNULL(AveragePrice, dbo.ufn_extract_IHS_AvgPrice(Descriptions)),    
  @IHSPartStatus = PartStatus    
 FROM tbIHSparts WITH (NOLOCK)    
 --WHERE FullPart LIKE @PartSearch    
 WHERE FullPart = @TargetFullPart  
  AND (@TargetManufacturerNo = 0 OR ISNULL(ManufacturerNo, 0) = @TargetManufacturerNo)    
  AND ISNULL(Inactive, 0) = 0    
 ORDER BY DLUP DESC  
  
 SET @IHSResult = 'AV Price: ' + CASE WHEN @IHSAveragePrice IS NULL THEN '0.00'    
         ELSE CAST(@IHSAveragePrice AS NVARCHAR(25))    
        END    
    + '&nbsp;&nbsp; P/Status: ' + ISNULL(@IHSPartStatus, 'N/A');   
  
 --Lytica  

 SELECT TOP 1   
  @LyticaAveragePrice = AveragePrice,    
     @LyticaMarketLeading = MarketLeading,    
     @LyticaTargetPrice = TargetPrice,    
     @LyticaStatus = lifeCycleStatus    
 FROM tbLyticaAPI WITH (NOLOCK)    
 --WHERE dbo.ufn_get_fullpart(OriginalPartSearched) LIKE @PartSearch    
 WHERE dbo.ufn_get_fullpart(OriginalPartSearched) = @TargetFullPart    
  AND Manufacturer = CASE 
                        WHEN @LyticaManufacturerRef IS NOT NULL AND @LyticaManufacturerRef <> '' 
                        THEN @LyticaManufacturerRef
                        ELSE @TargetManufacturerName
                     END  
  AND ISNULL(Inactive, 0) = 0     
 ORDER BY DLUP DESC  
  
 SET @LyticaResult = 'AV Price: ' + CASE WHEN @LyticaAveragePrice IS NULL THEN '0.00'    
          ELSE CAST(FORMAT(@LyticaAveragePrice,'N5') AS NVARCHAR(25))    
         END    
     + '&nbsp;&nbsp; M/Leading: ' + CASE WHEN @LyticaMarketLeading IS NULL THEN '0.00'    
              ELSE CAST(FORMAT(@LyticaMarketLeading,'N5') AS NVARCHAR(25))    
               END    
     + '&nbsp;&nbsp; Target: ' + CASE WHEN @LyticaTargetPrice IS NULL THEN '0.00'    
             ELSE CAST(FORMAT(@LyticaTargetPrice,'N5') AS NVARCHAR(25))    
            END    
     + '&nbsp;&nbsp; P/Status: ' + ISNULL(NULLIF(@LyticaStatus, ''), 'N/A'); 
    
 SELECT TOP (@TableLength)  
  ID,  
  ClientNo,  
  PartNo,  
  ManufacturerNo,  
  ManufacturerCode,  
  ManufacturerName,  
  ProductNo,  
  ProductName,  
  ProductDescription,  
  SupplierName,  
  DateOrdered,  
  Quantity,  
  CONVERT(DECIMAL(16,5),ISNULL(BuyPrice, 0)) AS BuyPrice,  
  CONVERT(DECIMAL(16,5),ISNULL(SellPrice, 0)) AS SellPrice,  
  CurrencyCode,  
  ISNULL(DivisionStatus, 0) AS DivisionStatus,  
  @IsRestrictMfr AS IsRestrictMfr,  
  ISNULL(IsSourcingHub, 0) AS IsSourcingHub,  
  CAST(ISNULL(ROHS, 0) AS TINYINT) AS ROHS,  
  SourcingType,  
  SourcingTypeDescription  
  , CAST(0 AS INT) AS SourcingResultId  
  , CAST(0 AS BIT) AS AllowRemoveOffer  
 INTO #finalResults  
 FROM #tempOffer  
 ORDER BY  
  --default: sourcing type  
  CASE WHEN (@SortIndex = 2 AND @SortDir = 1) THEN SourcingTypeOrder END ASC,  
  CASE WHEN (@SortIndex = 2 AND @SortDir = 2) THEN SourcingTypeOrder END DESC,  
  --Supplier  
  CASE WHEN (@SortIndex = 3 AND @SortDir = 1) THEN SupplierName END ASC,  
  CASE WHEN (@SortIndex = 3 AND @SortDir = 2) THEN SupplierName END DESC,  
  --product  
  CASE WHEN (@SortIndex = 5 AND @SortDir = 1) THEN ProductName END ASC,  
  CASE WHEN (@SortIndex = 5 AND @SortDir = 2) THEN ProductName END DESC,  
  --date offer  
  CASE WHEN (@SortIndex = 6 AND @SortDir = 1) THEN DateOrdered END ASC,  
  CASE WHEN (@SortIndex = 6 AND @SortDir = 2) THEN DateOrdered END DESC,  
  --quantity  
  CASE WHEN (@SortIndex = 7 AND @SortDir = 1) THEN Quantity END ASC,  
  CASE WHEN (@SortIndex = 7 AND @SortDir = 2) THEN Quantity END DESC,  
  --buy price  
  CASE WHEN (@SortIndex = 8 AND @SortDir = 1) THEN BuyPrice END ASC,  
  CASE WHEN (@SortIndex = 8 AND @SortDir = 2) THEN BuyPrice END DESC,  
  --sell price  
  CASE WHEN (@SortIndex = 9 AND @SortDir = 1) THEN SellPrice END ASC,  
  CASE WHEN (@SortIndex = 9 AND @SortDir = 2) THEN SellPrice END DESC,  
  
  --if sort column is not buy/sell price => one more sort condition: order by buy price asc  
  CASE WHEN (@SortIndex < 8) THEN BuyPrice END ASC  
  
 --get SourcingResult ID and check if allow to delete offer  
 --re-use logic of function: enableButtons in BOMCusReqSourcingResults.debug.js  
 UPDATE fr  
 SET   
  fr.SourcingResultId = sr.SourcingResultId  
  ,fr.AllowRemoveOffer = CASE WHEN   
         ISNULL(sr.SourcingResultId,0) > 0   
         AND ISNULL(sr.IsReleased, 0) = 0  
         AND ISNULL(sr.SourceRef,'') <> 'Q' AND ISNULL(sr.SourceRef,'') <> 'S'  
         AND ISNULL(sr.PartWatchMatchHUBIPO,0) = 0  
         AND EXISTS (SELECT TOP 1 1 FROM tbSession se WITH(NOLOCK)   
          WHERE se.LoginNo = @LoginID  
           AND se.SessionTimestamp >= CONVERT(DATE, GETDATE()))  
        THEN CAST(1 AS BIT)  
       ELSE CAST(0 AS BIT)  
      END  
 FROM #finalResults fr  
 LEFT JOIN (  
  SELECT   
   SourcingResultId  
   ,SourcingTableItemNo  
   ,SourcingTable  
   ,IsReleased  
   ,SourceRef  
   ,PartWatchMatchHUBIPO  
  FROM dbo.tbSourcingResult WITH(NOLOCK)  
  WHERE CustomerRequirementNo = @CustomerRequirementId  
 ) sr ON sr.SourcingTableItemNo = fr.ID AND sr.SourcingTable = fr.SourcingType;   
  
 --final results  
 SELECT * FROM #finalResults;  
 SELECT @IHSResult AS IHSResult;  
 SELECT @LyticaResult AS LyticaResult;  
  
 DROP TABLE #finalResults  
 DROP TABLE #tempOffer  
 SET NOCOUNT OFF  
END  