//----------------------------------------------------------------------------------------------------------------
// RP 27.01.2010:
// - pass through SupplierRMANo to data call
//
// RP 08.12.2009:
// - add more columns
//Marker      ChangedBy     Date            Remarks
//[001]       A<PERSON><PERSON>   30-Aug-2018     apply global security login on allocate and ipoallocate.
//----------------------------------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site.Enumerations;

namespace Rebound.GlobalTrader.Site.Controls.ItemSearch.Data {
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class IpoStock : Rebound.GlobalTrader.Site.Data.ItemSearch.Base {

		protected override void GetData() {
            //[001] start
            int? intGlobalLoginClientNo;
            intGlobalLoginClientNo = GetFormValue_NullableInt("GlobalLoginClientNo");
            //[001] end
            int clientID = (intGlobalLoginClientNo.HasValue && intGlobalLoginClientNo.Value > 0) ? intGlobalLoginClientNo.Value : SessionManager.ClientID ?? 0;
            List<BLL.Stock> lst = null;
            try {
                lst = BLL.Stock.IpoItemSearch(
                    clientID
                    //SessionManager.ClientID
                    , GetFormValue_NullableInt("Order", 0)
                    , GetFormValue_NullableInt("SortDir", SortColumnDirection.ASC)
                    , GetFormValue_NullableInt("PageIndex", 0)
                    , GetFormValue_NullableInt("PageSize", 10)
                    //, GetFormValue_StringForFullPartSearch("PartNo")
                    , GetFormValue_StringForNameSearchDecode("PartNo")
                    , GetFormValue_Boolean("ForRMAs")
                    , GetFormValue_Int("SupplierRMANo")
                    , GetFormValue_Boolean("IncludeQuarantined")
                    , GetFormValue_Boolean("IncludeLotsOnHold")
                    , GetFormValue_NullableInt("PONoLo")
                    , GetFormValue_NullableInt("PONoHi")
                    , GetFormValue_NullableInt("CRMANoLo")
                    , GetFormValue_NullableInt("CRMANoHi")
                    , GetFormValue_NullableInt("WarehouseNo")
                    , GetFormValue_String("Location")
                    , GetFormValue_NullableInt("IncLockCust")
                    , GetFormValue_NullableInt("SalesOrderNo")
                    , GetFormValue_Boolean("StopNonIPOStock")
                );
                JsonObject jsn = new JsonObject();
                JsonObject jsnItems = new JsonObject(true);
                for (int i = 0; i < lst.Count; i++) {
                    JsonObject jsnItem = new JsonObject();
                    jsnItem.AddVariable("ID", lst[i].StockId);
                    jsnItem.AddVariable("Part", lst[i].Part);
                    jsnItem.AddVariable("ROHS", lst[i].ROHS);
                    jsnItem.AddVariable("QtyStock", Functions.FormatNumeric(lst[i].QuantityInStock));
                    jsnItem.AddVariable("QtyOrder", Functions.FormatNumeric(lst[i].QuantityOnOrder));
                    jsnItem.AddVariable("QtyAvailable", Functions.FormatNumeric(lst[i].QuantityAvailable));
                    jsnItem.AddVariable("QtyAllocated", Functions.FormatNumeric(lst[i].QuantityAllocated));
                    jsnItem.AddVariable("Landed", Functions.FormatCurrency(lst[i].LandedCost, SessionManager.ClientCurrencyCode, 2));
                    if (lst[i].PurchaseOrderNumber != null) {
                        jsnItem.AddVariable("PO", lst[i].PurchaseOrderNumber);
                        jsnItem.AddVariable("PODelivDate", Functions.FormatDate(lst[i].PODeliveryDate));
                    }
                    if (lst[i].CustomerRMANumber != null) {
                        jsnItem.AddVariable("CRMA", lst[i].CustomerRMANumber);
                        jsnItem.AddVariable("CRMADate", Functions.FormatDate(lst[i].CustomerRMADate));
                    }
                    jsnItem.AddVariable("IPONo", lst[i].IPONo);
                    jsnItem.AddVariable("Product", lst[i].ProductName);
                    jsnItem.AddVariable("Unavailable", Functions.GetYesOrNo(lst[i].Unavailable));
                    jsnItem.AddVariable("Supplier", lst[i].SupplierName);
                    jsnItem.AddVariable("Warehouse", lst[i].WarehouseName);
                    jsnItem.AddVariable("Location", lst[i].Location);
                    jsnItem.AddVariable("LineNo", Functions.FormatNumeric(lst[i].POSerialNo));
                    jsnItem.AddVariable("SupplierPart", lst[i].SupplierPart);
                    jsnItems.AddVariable(jsnItem);
                    jsnItem.Dispose(); jsnItem = null;
                }
                jsn.AddVariable("Count", (lst.Count > 0) ? lst[0].RowCnt : 0);
                jsn.AddVariable("Results", jsnItems);
                OutputResult(jsn);
                jsnItems.Dispose(); jsnItems = null;
                jsn.Dispose(); jsn = null;
            } catch (Exception ex) {
                WriteError(ex);
            } finally {
                lst = null;
            }
			base.GetData();
        }

    }
}
