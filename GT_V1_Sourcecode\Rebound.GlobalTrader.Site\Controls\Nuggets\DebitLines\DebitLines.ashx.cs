//-----------------------------------------------------------------------------------------
// RP 12.10.2009:
// - Retrofit of changes from v3.0.34, add LoginID for DeleteDebitLines (part of task 345)
//-----------------------------------------------------------------------------------------
//Marker     Changed by      Date         Remarks
//[001]      Anand Gupta     17/08/2021   (GTDP-198) Controlling specific product groups for purchasing

using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using System.Linq;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets.Data {
	[WebService(Namespace = "http://tempuri.org/")]
	[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
	public class DebitLines : Rebound.GlobalTrader.Site.Data.Base {

		public override void ProcessRequest(HttpContext context) {
			if (base.init(context)) {
				switch (Action) {
					case "GetLines": GetLines(); break;
					case "GetData": GetData(); break;
					case "AddNew": AddNew(); break;
					case "SaveEdit": SaveEdit(); break;
					case "Delete": Delete(); break;
					case "GetPOLines": GetPOLines(); break;
					case "GetPOLineForNew": GetPOLineForNew(); break;
					case "GetServiceForNew": GetServiceForNew(); break;
					default: WriteErrorActionNotFound(); break;
				}
			}
		}

		/// <summary>
		/// get all debitLines for specified debit
		/// </summary>
		private void GetLines() {
			try {
				Debit db = Debit.Get(ID);
				if (db != null) {
					List<DebitLine> lst = DebitLine.GetListForDebit(ID);
					var listMfrNotes = Manufacturer.GetAdvisoryNotes(lst.Select(x => x.ManufacturerNo).ToList(), (int)SessionManager.ClientID);

					double dblSubTotal = 0;
					double dblTax = 0;
					JsonObject jsn = new JsonObject();
					JsonObject jsnItems = new JsonObject(true);
					foreach (DebitLine ln in lst) {
						string mfrNotes = !Functions.HasNumbericValue(ln.ManufacturerNo) ? "" : listMfrNotes.Find(x => x.ManufacturerId == (int)ln.ManufacturerNo).AdvisoryNotes;
						JsonObject jsnItem = new JsonObject();
						double dblLineTotal = (double)ln.Price * (double)ln.Quantity;
						double dblLineTax = (ln.Taxable) ? (dblLineTotal * ((db.TaxRate == null) ? 0 : (double)db.TaxRate / 100)) : 0;
						jsnItem.AddVariable("ID", ln.DebitLineId);
						jsnItem.AddVariable("Part", ln.Part);
						jsnItem.AddVariable("ManufacturerNo", ln.ManufacturerNo);
						jsnItem.AddVariable("Manufacturer", ln.ManufacturerCode);
						jsnItem.AddVariable("MfrAdvisoryNotes", Functions.ReplaceLineBreaks(mfrNotes));
						//jsnItem.AddVariable("Product", ln.ProductName);
						jsnItem.AddVariable("Product", ln.ProductDescription);
                        jsnItem.AddVariable("Package", ln.PackageName);
						jsnItem.AddVariable("DC", ln.DateCode);
						jsnItem.AddVariable("Quantity", Functions.FormatNumeric(ln.Quantity));
						jsnItem.AddVariable("SupplierPart", ln.SupplierPart);
						jsnItem.AddVariable("ROHS", ln.ROHS);
						jsnItem.AddVariable("Price", Functions.FormatCurrency(ln.Price, ln.CurrencyCode,true));
						jsnItem.AddVariable("LC", Functions.FormatCurrency(ln.LandedCost, SessionManager.ClientCurrencyCode,true));
						jsnItem.AddVariable("Total", Functions.FormatCurrency(dblLineTotal, ln.CurrencyCode, 2,true));
						jsnItem.AddVariable("Tax", Functions.FormatCurrency(dblLineTax, ln.CurrencyCode, 2,true));
						jsnItem.AddVariable("StockNo", ln.StockNo);
                        jsnItem.AddVariable("IsParentDebitLineNo", ln.ParentDebitLineNo.HasValue && ln.ParentDebitLineNo.Value > 0);
						jsnItems.AddVariable(jsnItem);
						jsnItem.Dispose(); jsnItem = null;
						dblSubTotal += dblLineTotal;
						dblTax += dblLineTax;
					}
					jsn.AddVariable("Lines", jsnItems);
					db.Freight = (db.Freight == null) ? 0 : db.Freight;
					dblTax += (double)db.Freight * ((double)db.TaxRate / 100);
					jsn.AddVariable("SubTotal", Functions.FormatCurrency(dblSubTotal, db.CurrencyCode, 2,true));
					jsn.AddVariable("Freight", Functions.FormatCurrency(db.Freight, db.CurrencyCode, 2,true));
					jsn.AddVariable("Tax", Functions.FormatCurrency(dblTax, db.CurrencyCode, 2,true));
					jsn.AddVariable("Total", Functions.FormatCurrency(dblSubTotal + dblTax + db.Freight, db.CurrencyCode, 2,true));
					jsn.AddVariable("FreightRaw", Functions.FormatCurrency(db.Freight, null,2,true));
					OutputResult(jsn);
					jsn.Dispose(); jsn = null;
				}
				db = null;
			} catch (Exception e) {
				WriteError(e);
			}
		}

		/// <summary>
		/// get debitLine by key
		/// </summary>
		private void GetData() {
			DebitLine ln = null;
			try {
				ln = DebitLine.Get(ID);
				if (ln == null) {
					WriteErrorDataNotFound();
				} else {
					JsonObject jsn = new JsonObject();
					jsn.AddVariable("DebitNo", ln.DebitNo);
					jsn.AddVariable("Part", ln.Part);
					jsn.AddVariable("ROHS", ln.ROHS);
					jsn.AddVariable("DC", ln.DateCode);
					jsn.AddVariable("ManufacturerNo", ln.ManufacturerNo);
					jsn.AddVariable("Manufacturer", ln.ManufacturerCode);
					string mfrNotes = Manufacturer.GetAdvisoryNotes(ln.ManufacturerNo ?? 0, (int)SessionManager.ClientID);
					jsn.AddVariable("MfrAdvisoryNotes", Functions.ReplaceLineBreaks(mfrNotes));
					jsn.AddVariable("Package", ln.PackageName);
					jsn.AddVariable("Quantity", Functions.FormatNumeric(ln.Quantity));
					jsn.AddVariable("Price", Functions.FormatCurrency(ln.Price, ln.CurrencyCode));
					jsn.AddVariable("PriceRaw", Functions.FormatCurrency(ln.Price));
					string strLandedCost = Functions.FormatCurrency(ln.LandedCost, SessionManager.ClientCurrencyCode);
					if (ln.CurrencyNo != SessionManager.ClientCurrencyID) strLandedCost = string.Format("{0} ({1})", strLandedCost, Functions.FormatCurrency(BLL.Currency.ConvertValueFromBaseCurrency(ln.LandedCost, ln.CurrencyNo, ln.DebitDate), ln.CurrencyCode));
					jsn.AddVariable("LandedCost", strLandedCost);
					jsn.AddVariable("ProductNo", ln.ProductNo);
					jsn.AddVariable("ProductName", ln.ProductName);
					jsn.AddVariable("Product", ln.ProductDescription);
					jsn.AddVariable("PackageNo", ln.PackageNo);
					jsn.AddVariable("PackageName", ln.PackageName);
					jsn.AddVariable("Package", ln.PackageDescription);
					jsn.AddVariable("SupplierPart", ln.SupplierPart);
					jsn.AddVariable("Taxable", ln.Taxable);
					jsn.AddVariable("StockNo", ln.StockNo);
					jsn.AddVariable("ServiceNo", ln.ServiceNo);
					jsn.AddVariable("IsService", ln.IsService);
					jsn.AddVariable("LineNotes", Functions.ReplaceLineBreaks(ln.LineNotes));
                    jsn.AddVariable("IsProdHaz", Convert.ToBoolean(ln.IsProdHazardous));
                    jsn.AddVariable("IsPrintHaz", Convert.ToBoolean(ln.PrintHazardous));
                    //[001] code start
                    jsn.AddVariable("IsOrderViaIPOonly", Convert.ToBoolean(ln.IsOrderViaIPOonly));
                    //[001] code end
                    jsn.AddVariable("IsOrderViaIPOonly", Convert.ToBoolean(ln.IsOrderViaIPOonly));
                    Product objcReq = Product.GetHazardousProductStatusMessage(ln.ProductNo, Convert.ToBoolean(ln.IsProdHazardous), Convert.ToBoolean(ln.IsOrderViaIPOonly), SessionManager.ClientID);
                    if (objcReq != null)
                        jsn.AddVariable("ProductMessage", Functions.ReplaceLineBreaks(objcReq.ProductMessage));
                    else
                        jsn.AddVariable("ProductMessage", "");
                    OutputResult(jsn);
					jsn.Dispose();
					jsn = null;
				}
			} catch (Exception e) {
				WriteError(e);
			} finally {
				ln = null;
			}
		}

		/// <summary>
		/// Add new debitLine
		/// </summary>
		public void AddNew() {
			try {
				DebitLine ln = new DebitLine();
				ln.DebitNo = ID;
				if (GetFormValue_Boolean("LineIsService")) {
					ln.ServiceNo = GetFormValue_NullableInt("ServiceNo");
					ln.Part = GetFormValue_String("Service");
					ln.SupplierPart = GetFormValue_String("ServiceDescription");
				} else {
					ln.Part = GetFormValue_String("Part");
					ln.SupplierPart = GetFormValue_String("SupplierPart");
					ln.ManufacturerNo = GetFormValue_NullableInt("MfrNo");
					ln.DateCode = GetFormValue_String("DateCd");
					ln.PackageNo = GetFormValue_NullableInt("PackageNo");
					ln.ProductNo = GetFormValue_NullableInt("ProductNo");
					ln.ROHS = GetFormValue_Byte("ROHS");
					ln.LandedCost = GetFormValue_NullableDouble("LandedCost");
					ln.PurchaseOrderLineNo = GetFormValue_NullableInt("PurchaseOrderLineNo");
					ln.StockNo = GetFormValue_NullableInt("StockNo");
				}
				ln.Quantity = GetFormValue_Int("Quantity");
				ln.Price = GetFormValue_Double("Price");
				ln.Taxable = GetFormValue_Boolean("Taxable");
				ln.SupplierRMALineNo = GetFormValue_NullableInt("SupplierRMALineNo");
				ln.Notes = GetFormValue_String("LineNotes");
				ln.UpdatedBy = LoginID;
                ln.PrintHazardous = GetFormValue_NullableBoolean("PrintHazWar",0);
				int intNewDebitLineID = ln.Insert();
                if (ln.DebitId > 0)
                {
                    WebServices service = new WebServices();
                    service.NotifyDebitNotesToPoHub("", (SessionManager.POHubMailGroupId ?? 0).ToString(), Functions.GetGlobalResource("MailTemplates", "DebitNotesToPoHub"), ln.DebitId);
                }
				JsonObject jsn = new JsonObject();
				jsn.AddVariable("Result", intNewDebitLineID > 0);
				jsn.AddVariable("NewID", intNewDebitLineID);
				OutputResult(jsn);
				jsn.Dispose();
				jsn = null;
			} catch (Exception e) {
				WriteError(e);
			}
		}

		/// <summary>
		/// Update an existing debitLine
		/// </summary>
		public void SaveEdit() {
			try {
				DebitLine ln = DebitLine.Get(ID);
				if (GetFormValue_Boolean("LineIsService")) {
					ln.Part = GetFormValue_String("Service");
					ln.SupplierPart = GetFormValue_String("ServiceDescription");
				}
				ln.Quantity = GetFormValue_Int("Quantity");
				ln.Price = GetFormValue_Double("Price");
				ln.Notes = GetFormValue_String("LineNotes");
				ln.UpdatedBy = LoginID;
                ln.PrintHazardous = GetFormValue_NullableBoolean("PrintHazWar",0);
				JsonObject jsn = new JsonObject();
				jsn.AddVariable("Result", ln.Update());
				OutputResult(jsn);
				jsn.Dispose(); jsn = null;
				ln = null;
			} catch (Exception e) {
				WriteError(e);
			}
		}

		/// <summary>
		/// Delete
		/// </summary>
		public void Delete() {
			try {
				JsonObject jsn = new JsonObject();
				jsn.AddVariable("Result", DebitLine.Delete(ID, LoginID));
				OutputResult(jsn);
				jsn.Dispose(); jsn = null;
			} catch (Exception e) {
				WriteError(e);
			}
		}

		private void GetPOLines() {
			try {
				List<PurchaseOrderLine> lst = PurchaseOrderLine.GetListForPurchaseOrder(ID);
				JsonObject jsn = new JsonObject();
				JsonObject jsnItems = new JsonObject(true);
				foreach (PurchaseOrderLine ln in lst) {
					JsonObject jsnItem = new JsonObject();
					jsnItem.AddVariable("ID", ln.PurchaseOrderLineId);
					jsnItem.AddVariable("Part", ln.Part);
					jsnItem.AddVariable("Mfr", ln.ManufacturerCode);
					jsnItem.AddVariable("Product", ln.ProductName);
					jsnItem.AddVariable("QuantityOrdered", Functions.FormatNumeric(ln.Quantity));
                    if (ln.InternalPurchaseOrderNo.HasValue && ln.InternalPurchaseOrderNo.Value > 0)
                        jsnItem.AddVariable("Price", Functions.FormatCurrency(ln.ClientPrice, ln.CurrencyCode));
                    else
                        jsnItem.AddVariable("Price", Functions.FormatCurrency(ln.Price, ln.CurrencyCode));

					jsnItem.AddVariable("ShipInCost", Functions.FormatCurrency(ln.ShipInCost, SessionManager.ClientCurrencyCode));
					jsnItems.AddVariable(jsnItem);
					jsnItem.Dispose();
					jsnItem = null;
				}
				jsn.AddVariable("Lines", jsnItems);
				jsnItems.Dispose(); jsnItems = null;
				OutputResult(jsn);
			} catch (Exception e) {
				WriteError(e);
			}
		}

		/// <summary>
		/// Get PO Line for a new Debit Line
		/// </summary>
		private void GetPOLineForNew() {
			try {
				PurchaseOrderLine ln = PurchaseOrderLine.Get(ID);
				if (ln != null) {
					JsonObject jsn = new JsonObject();
					jsn.AddVariable("Quantity", Functions.FormatNumeric(ln.Quantity));
					jsn.AddVariable("Part", ln.Part);
					jsn.AddVariable("SupplierPart", ln.SupplierPart);
					jsn.AddVariable("DateCode", ln.DateCode);
					jsn.AddVariable("MfrNo", ln.ManufacturerNo);
					jsn.AddVariable("Mfr", ln.ManufacturerName);
					jsn.AddVariable("ProductNo", ln.ProductNo);
					jsn.AddVariable("PackageNo", ln.PackageNo);
					jsn.AddVariable("ROHS", ln.ROHS);

					//convert price to Debit currency if required
                    if (ln.InternalPurchaseOrderNo.HasValue && ln.InternalPurchaseOrderNo.Value > 0)
                    {
                        int intDebitCurrencyID = GetFormValue_Int("DebitCurrencyNo");
                        if (intDebitCurrencyID != ln.ClientCurrencyNo) ln.ClientPrice = BLL.Currency.ConvertValueBetweenTwoCurrencies(ln.ClientPrice, (int)ln.ClientCurrencyNo, intDebitCurrencyID, GetFormValue_DateTime("DebitDate"));
                        jsn.AddVariable("Price", Functions.FormatCurrency(ln.ClientPrice));
                    }
                    else
                    {
                        int intDebitCurrencyID = GetFormValue_Int("DebitCurrencyNo");
                        if (intDebitCurrencyID != ln.CurrencyNo) ln.Price = BLL.Currency.ConvertValueBetweenTwoCurrencies(ln.Price, ln.CurrencyNo, intDebitCurrencyID, GetFormValue_DateTime("DebitDate"));
                        jsn.AddVariable("Price", Functions.FormatCurrency(ln.Price));
                    }
                    jsn.AddVariable("ProductDescription", ln.ProductDescription);
                    jsn.AddVariable("PackageDescription", ln.PackageDescription);
                    OutputResult(jsn);
					jsn.Dispose(); jsn = null;
				}
				ln = null;
			} catch (Exception e) {
				WriteError(e);
			}
		}

		/// <summary>
		/// Gets data for a new Line based on a service item
		/// </summary>
		public void GetServiceForNew() {
			try {
				Service svc = Service.Get(ID);
				JsonObject jsn = new JsonObject();
				jsn.AddVariable("ServiceName", svc.ServiceName);
				jsn.AddVariable("ServiceDescription", svc.ServiceDescription);

				//convert price to Debit currency if required
				int intDebitCurrencyID = GetFormValue_Int("DebitCurrencyNo");
				if (intDebitCurrencyID != SessionManager.ClientCurrencyID) svc.Price = BLL.Currency.ConvertValueFromBaseCurrency(svc.Price, intDebitCurrencyID, GetFormValue_DateTime("DebitDate"));
				jsn.AddVariable("Price", Functions.FormatCurrency(svc.Price));

				OutputResult(jsn);
				jsn.Dispose(); jsn = null;
				svc = null;
			} catch (Exception e) {
				WriteError(e);
			}
		}

	}
}