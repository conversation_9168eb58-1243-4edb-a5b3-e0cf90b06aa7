using System;
using System.Collections.Generic;
using System.Text;
using System.Collections.Specialized;

namespace Rebound.GlobalTrader.Site.Settings {
	class ConfigSettings {

		private static object objLockingObject = new object();
		private static ReboundUISettingsSection objSettings = null;

		[System.Diagnostics.DebuggerNonUserCode()]
		public static NameValueCollection ReboundUISettings {
			get {
				lock (objLockingObject) {
					if (objSettings == null) {
						objSettings = new ReboundUISettingsSection();
						objSettings.GetSettings();
					}
				}
				return (NameValueCollection)objSettings;
			}
		}

		#region Private ReboundUISettingsSection Class

		private class ReboundUISettingsSection : NameValueCollection {
			[System.Diagnostics.DebuggerNonUserCode()]
			public void GetSettings() {
				//If the settings collection is not populated, populate it.
				if (base.Count == 0) {
					//Load the config settings from the .Config file.
					Settings.ReboundUIConfigSection cfg = Rebound.GlobalTrader.Site.Settings.ReboundUIConfigSection.GetConfig();
				}
			}
			#region Overrides
			/// <summary>
			/// This configuration is read only and calling this method will throw an exception.
			/// </summary>
			[System.Diagnostics.DebuggerNonUserCode()]
			public override void Clear() {
				throw new Exception("The configuration is read only.");
			}
			/// <summary>
			/// This configuration is read only and calling this method will throw an exception.
			/// </summary>
			[System.Diagnostics.DebuggerNonUserCode()]
			public override void Add(string name, string value) {
				throw new Exception("The configuration is read only.");
			}
			/// <summary>
			/// This configuration is read only and calling this method will throw an exception.
			/// </summary>
			[System.Diagnostics.DebuggerNonUserCode()]
			public override void Remove(string name) {
				throw new Exception("The configuration is read only.");
			}
			/// <summary>
			/// This configuration is read only and calling this method will throw an exception.
			/// </summary>
			[System.Diagnostics.DebuggerNonUserCode()]
			public override void Set(string name, string value) {
				throw new Exception("The configuration is read only.");
			}
			#endregion
		}
		#endregion

	}
}
