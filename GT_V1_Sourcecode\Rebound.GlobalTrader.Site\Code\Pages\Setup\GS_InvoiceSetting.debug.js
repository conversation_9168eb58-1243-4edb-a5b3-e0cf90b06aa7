///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Setup");

Rebound.GlobalTrader.Site.Pages.Setup.GS_InvoiceSetting = function(el) { 
	Rebound.GlobalTrader.Site.Pages.Setup.GS_InvoiceSetting.initializeBase(this, [el]);
};

Rebound.GlobalTrader.Site.Pages.Setup.GS_InvoiceSetting.prototype = {

    get_ctlInvoiceSetting: function() { return this._ctlInvoiceSetting; }, set_ctlInvoiceSetting: function(v) { if (this._ctlInvoiceSetting !== v) this._ctlInvoiceSetting = v; },
    get_ctlEightDSubCategory: function() { return this._ctlEightDSubCategory; }, set_ctlEightDSubCategory: function(v) { if (this._ctlEightDSubCategory !== v) this._ctlEightDSubCategory = v; },

    initialize: function() {
        Rebound.GlobalTrader.Site.Pages.Setup.GS_InvoiceSetting.callBaseMethod(this, "initialize");
    },

    goInit: function() {
    
        // this._ctlInvoiceSetting.addSelectCategory(Function.createDelegate(this, this.ctlInvoiceSetting_SelectCategory));
        // if (this._ctlCertificate) this._ctlCertificate.addChangedData(Function.createDelegate(this, this.ctlCurrencyRates_ChangedData));
        Rebound.GlobalTrader.Site.Pages.Setup.GS_InvoiceSetting.callBaseMethod(this, "goInit");
    },

    dispose: function() {
        if (this.isDisposed) return;
        if (this._ctlInvoiceSetting) this._ctlInvoiceSetting.dispose();
        if (this._ctlEightDSubCategory) this._ctlEightDSubCategory.dispose();
        this._ctlInvoiceSetting = null;
        this._ctlEightDSubCategory = null;
        Rebound.GlobalTrader.Site.Pages.Setup.GS_InvoiceSetting.callBaseMethod(this, "dispose");
    },

    ctlInvoiceSetting_SelectCategory: function() {
   
        this._ctlEightDSubCategory._intcategoryID = this._ctlInvoiceSetting._intCategoryID;
        this._ctlEightDSubCategory._tbl.resizeColumns();
        this._ctlEightDSubCategory.show(true);
        this._ctlEightDSubCategory.refresh();
    }

};

Rebound.GlobalTrader.Site.Pages.Setup.GS_InvoiceSetting.registerClass("Rebound.GlobalTrader.Site.Pages.Setup.GS_InvoiceSetting", Rebound.GlobalTrader.Site.Pages.Content, Sys.IDisposable);
