Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.SellTerms=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.SellTerms.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DropDowns.SellTerms.prototype={get_intGlobalLoginClientNo:function(){return this._intGlobalLoginClientNo},set_intGlobalLoginClientNo:function(n){this._intGlobalLoginClientNo!==n&&(this._intGlobalLoginClientNo=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.DropDowns.SellTerms.callBaseMethod(this,"initialize");this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK))},dispose:function(){this.isDisposed||(this._intGlobalLoginClientNo=null,Rebound.GlobalTrader.Site.Controls.DropDowns.SellTerms.callBaseMethod(this,"dispose"))},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/SellTerms");this._objData.set_DataObject("SellTerms");this._objData.set_DataAction("GetData");this._objData.addParameter("GlobalLoginClientNo",this._intGlobalLoginClientNo)},dataCallOK:function(){var t=this._objData._result,n;if(t.Terms)for(n=0;n<t.Terms.length;n++)this.addOption(t.Terms[n].Name,t.Terms[n].ID)}};Rebound.GlobalTrader.Site.Controls.DropDowns.SellTerms.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.SellTerms",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);