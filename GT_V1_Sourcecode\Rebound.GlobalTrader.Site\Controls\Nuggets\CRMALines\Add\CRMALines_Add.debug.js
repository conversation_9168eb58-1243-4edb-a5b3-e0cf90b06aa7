///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
//  [003]      Suhail          15/05/2018   Added Avoidable on CRMA Line
// [RP-2339]	Ravi		11-10-2023		AS6081 GT Documents - Show AS6081 on detail screens
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.CRMALines_Add = function(element) { 
	Rebound.GlobalTrader.Site.Controls.Forms.CRMALines_Add.initializeBase(this, [element]);
	this._intCRMAID = 0;
	this._intLineID = 0;
	this._intNewID = 0;
	this._intLineAllocationID = 0;
	this._strPathToData = "";
	this._strDataObject = "";
	this._intQuantityAvailable = 0;
	this._intStockNo = -1;
};

Rebound.GlobalTrader.Site.Controls.Forms.CRMALines_Add.prototype = {

    get_intCRMAID: function() { return this._intCRMAID; }, set_intCRMAID: function(v) { if (this._intCRMAID !== v) this._intCRMAID = v; },
    get_ctlSelectInvoiceLine: function() { return this._ctlSelectInvoiceLine; }, set_ctlSelectInvoiceLine: function(v) { if (this._ctlSelectInvoiceLine !== v) this._ctlSelectPOLine = v; },
    get_lblSelectInvoiceLine: function() { return this._lblSelectInvoiceLine; }, set_lblSelectInvoiceLine: function(v) { if (this._lblSelectInvoiceLine !== v) this._lblSelectPOLine = v; },
    get_trSelectInvoiceLine: function() { return this._trSelectInvoiceLine; }, set_trSelectInvoiceLine: function(v) { if (this._trSelectInvoiceLine !== v) this._trSelectPOLine = v; },
    get_pnlLines: function() { return this._pnlLines; }, set_pnlLines: function(v) { if (this._pnlLines !== v) this._pnlLines = v; },
    get_tblLines: function() { return this._tblLines; }, set_tblLines: function(v) { if (this._tblLines !== v) this._tblLines = v; },
    get_pnlLinesError: function() { return this._pnlLinesError; }, set_pnlLinesError: function(v) { if (this._pnlLinesError !== v) this._pnlLinesError = v; },
    get_lblLinesError: function() { return this._lblLinesError; }, set_lblLinesError: function(v) { if (this._lblLinesError !== v) this._lblLinesError = v; },
    get_pnlLinesLoading: function() { return this._pnlLinesLoading; }, set_pnlLinesLoading: function(v) { if (this._pnlLinesLoading !== v) this._pnlLinesLoading = v; },
    get_pnlLinesNoneFound: function() { return this._pnlLinesNoneFound; }, set_pnlLinesNoneFound: function(v) { if (this._pnlLinesNoneFound !== v) this._pnlLinesNoneFound = v; },
    get_pnlLinesNotAvailable: function() { return this._pnlLinesNotAvailable; }, set_pnlLinesNotAvailable: function(v) { if (this._pnlLinesNotAvailable !== v) this._pnlLinesNotAvailable = v; },

    initialize: function() {
        Rebound.GlobalTrader.Site.Controls.Forms.CRMALines_Add.callBaseMethod(this, "initialize");
        this.addShown(Function.createDelegate(this, this.formShown));
    },

    dispose: function() {
        if (this.isDisposed) return;
        if (this._tblLines) this._tblLines.dispose();
        if (this._ctlSelectInvoiceLine) this._ctlSelectInvoiceLine.dispose();
        this._intCRMAID = null;
        this._intLineID = null;
        this._intNewID = null;
        this._intLineAllocationID = null;
        this._strPathToData = null;
        this._strDataObject = null;
        this._intQuantityAvailable = null;
        this._ctlSelectInvoiceLine = null;
        this._lblSelectInvoiceLine = null;
        this._trSelectInvoiceLine = null;
        this._pnlLines = null;
        this._tblLines = null;
        this._pnlLinesError = null;
        this._lblLinesError = null;
        this._pnlLinesLoading = null;
        this._pnlLinesNoneFound = null;
        this._pnlLinesNotAvailable = null;
        this._intStockNo = null;
        Rebound.GlobalTrader.Site.Controls.Forms.CRMALines_Add.callBaseMethod(this, "dispose");
    },

    formShown: function() {
        if (this._blnFirstTimeShown) {
            //form events
            this.addSave(Function.createDelegate(this, this.saveClicked));

            //other controls
            $R_IBTN.enableButton(this._ibtnSave, false);
            $R_IBTN.enableButton(this._ibtnSave_Footer, false);
            this._tblLines.addSelectedIndexChanged(Function.createDelegate(this, this.selectInvoiceLine));
            $R_IBTN.enableButton(this._ibtnSave, false);
            $R_IBTN.enableButton(this._ibtnSave_Footer, false);
            this._ctlMultiStep.addStepChanged(Function.createDelegate(this, this.stepChanged));


            //data
            this._strPathToData = "controls/Nuggets/CRMALines";
            this._strDataObject = "CRMALines";


            this._ctlItemsReason1 = $find(this.getField("ctlItemsReason1").ID);
            this._ctlItemsReason1.addItem();
          

            this._ctlItemsReason2 = $find(this.getField("ctlItemsReason2").ID);
            this._ctlItemsReason2.addItem();


        }
        this._ctlItemsReason2.blankReason();
        this._ctlItemsReason1.blankReason();
        this.gotoStep(1);

        this.setFieldValue("ctlPrintHazWar", false);
        //this.enableFieldCheckBox("ctlPrintHazWar", false);
    },

    setFieldsFromHeader: function(strCRMANumber, strCustomer) {
        this.setFieldValue("ctlCustomerRMA", strCRMANumber);
        this.setFieldValue("ctlCustomer", strCustomer);
    },

    stepChanged: function() {
        var intStep = this._ctlMultiStep._intCurrentStep;
        if (intStep == 1) this.getInvoiceLines();
        $R_IBTN.enableButton(this._ibtnSave, intStep == 2);
        $R_IBTN.enableButton(this._ibtnSave_Footer, intStep == 2);
    },

    getInvoiceLines: function() {
        this._tblLines.clearTable();
        $R_FN.showElement(this._pnlLines, false);
        $R_FN.showElement(this._pnlLinesNotAvailable, false);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/CRMALines");
        obj.set_DataObject("CRMALines");
        obj.set_DataAction("GetInvoiceLineAllocationCandidates");
        obj.addParameter("ID", this._intCRMAID);
        obj.addDataOK(Function.createDelegate(this, this.getInvoiceLinesOK));
        obj.addError(Function.createDelegate(this, this.getInvoiceLinesError));
        obj.addTimeout(Function.createDelegate(this, this.getInvoiceLinesError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getInvoiceLinesOK: function(args) {
        var result = args._result;
        var blnFoundLines = false;
        if (result.Lines) {
            $R_FN.showElement(this._pnlLines, true);
            $R_FN.showElement(this._pnlLinesNotAvailable, false);
            for (var i = 0, l = result.Lines.length; i < l; i++) {
                var row = result.Lines[i];
                var aryData = [
					$R_FN.writePartNo(row.Part, row.ROHS)
					, row.Qty
					, row.QtyAllocated
					, row.QtyRemaining
					, $R_FN.setCleanTextValue(row.Price)
					, $R_FN.showSerialNumber(row.SO, row.SoLineNo)
					, $R_FN.setCleanTextValue(row.InvoiceDate)
				];
                this._tblLines.addRow(aryData, row.ID, false);
                aryData = null; row = null;
                blnFoundLines = true;
            }
        }
        this._ctlMultiStep.showSteps(blnFoundLines);
        this._tblLines.resizeColumns();
        $R_FN.showElement(this._pnlLines, blnFoundLines);
        $R_FN.showElement(this._pnlLinesNotAvailable, !blnFoundLines);
    },

    getInvoiceLinesError: function(args) {
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    selectInvoiceLine: function() {
        this._intLineAllocationID = this._tblLines._varSelectedValue;
        this.getItemData();
        this.nextStep();
    },

    getItemData: function () {
        this._intStockNo = -1;
        this.setFormFieldsToDefaults();
        this.showLoading(true);
        $R_FN.showElement(this._pnlLoadingItemDetail, true);
        $R_FN.showElement(this._pnlItemDetailError, false);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/CRMALines");
        obj.set_DataObject("CRMALines");
        obj.set_DataAction("GetLineAllocationData");
        obj.addParameter("ID", this._intLineAllocationID);
        obj.addParameter("CRMAID", this._intCRMAID);
        obj.addDataOK(Function.createDelegate(this, this.getItemDataOK));
        obj.addError(Function.createDelegate(this, this.getItemDataError));
        obj.addTimeout(Function.createDelegate(this, this.getItemDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getItemDataError: function(args) {
        $R_FN.showElement(this._pnlLoadingItemDetail, false);
        $R_FN.showElement(this._pnlItemDetailError, true);
        $R_FN.setInnerHTML(this._pnlItemDetailError, args.get_ErrorMessage());
    },

    getItemDataOK: function(args) {
        $R_FN.showElement(this._pnlItemDetailError, false);
        var result = args._result;
        this.setFieldValue("ctlPartNo", result.Part);
        this.setFieldValue("ctlQuantityShipped", result.Quantity);
        this.setFieldValue("ctlQuantityAllocated", result.QuantityCRMAAlloc);
        this._intQuantityAvailable = Math.max(0, Number.parseLocale(result.Quantity.toString()) - Number.parseLocale(result.QuantityCRMAAlloc.toString()));
        this.setFieldValue("ctlQuantity", this._intQuantityAvailable);
        this.setFieldValue("ctlReturnDate", $R_FN.shortDate());
        this._intLineID = result.InvoiceLineNo;
        this._intStockNo = result.StockNo;
        //[RP-2339]
        debugger;
        this.setFieldValue("ctlAS6081", result.AS6081 == true ? "Yes" : "No");
        $R_FN.highlightBackgroundColorOfText("ctl00_cphMain_ctlLines_ctlDB_ctl14_frmAdd_ctlDB_ctlAS6081_ctl03_lblAS6081", res.AS6081);
        //[RP-2339]
        $R_FN.showElement(this._pnlItemDetail, true);
        $R_FN.showElement(this._pnlLoadingItemDetail, false);
        this.showLoading(false);
        this.showInnerContent(true);
    },

    saveClicked: function() {
        this.resetFormFields();
        if (this.validateForm()) this.saveEdit();
    },

    validateForm: function() {
        var blnOK = this.autoValidateFields();
        if (!this.checkNumericFieldLessThanOrEqualTo("ctlQuantity", this._intQuantityAvailable)) blnOK = false;
        if (!this.checkNumericFieldGreaterThan("ctlQuantity", 0)) blnOK = false;
        if (!blnOK) this.showError(true);
        return blnOK;
    },
     validateReason: function() {
        var blnOK = true;
        if (this._ctlItemsReason1._txtReason.value == "") {
            blnOK = false;
            this.showError(true, $R_RES.ResReason1Value);
        }
        return blnOK;
    },

    saveEdit: function() {
    if (!this.validateForm()) return;
    if (!this.validateReason()) return;  
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData(this._strPathToData);
        obj.set_DataObject(this._strDataObject);
        obj.set_DataAction("AddNewWithAllocation");
        obj.addParameter("id", this._intCRMAID);
        obj.addParameter("InvoiceLineNo", this._intLineID);
        obj.addParameter("InvoiceLineAllocationNo", this._intLineAllocationID);
        obj.addParameter("ReturnDate", this.getFieldValue("ctlReturnDate"));
        obj.addParameter("Reason", "");
        obj.addParameter("Reason1", this._ctlItemsReason1.getSubCategory());
        obj.addParameter("Reason2", this._ctlItemsReason2.getSubCategory());
        obj.addParameter("Quantity", this.getFieldValue("ctlQuantity"));
        obj.addParameter("LineNotes", this.getFieldValue("ctlLineNotes"));
        obj.addParameter("RootCause", this.getFieldValue("ctlRootCause"));
        obj.addParameter("StockNo", this._intStockNo);
        //[003] code start
        obj.addParameter("Avoidable", this.getFieldValue("ctlIsAvoidable"));
        //[003] code end
        obj.addParameter("PrintHazWar", this.getFieldValue("ctlPrintHazWar"));
        obj.addParameter("AS6081", this.connvertAs6081ToBoolean()); //[RP-2339]
        obj.addDataOK(Function.createDelegate(this, this.saveEditOK));
        obj.addError(Function.createDelegate(this, this.saveEditError));
        obj.addTimeout(Function.createDelegate(this, this.saveEditError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    saveEditError: function(args) {
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    saveEditOK: function(args) {
        if (args._result.Result == true) {
            this._intNewID = args._result.NewID;
            this.onSaveComplete();
        } else {
            this.saveEditError(args);
        }
    },
    //[RP-2339] start
    connvertAs6081ToBoolean: function () {
        selectedvalue = this.getFieldValue("ctlAS6081");
        this._AS6081 = selectedvalue == "Yes" ? true : false;
        return this._AS6081;
    }
    //[RP-2339] end




};

Rebound.GlobalTrader.Site.Controls.Forms.CRMALines_Add.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CRMALines_Add", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
