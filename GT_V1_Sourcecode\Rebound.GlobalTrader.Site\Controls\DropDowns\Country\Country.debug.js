///<reference name="MicrosoftAjax.js" />
//-----------------------------------------------------------------------------------------
// RP 06.10.2009:
// - Changes due to changes in base class
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");

Rebound.GlobalTrader.Site.Controls.DropDowns.Country = function(element) { 
	Rebound.GlobalTrader.Site.Controls.DropDowns.Country.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.DropDowns.Country.prototype = {
    get_intPOHubClientNo: function () { return this._intPOHubClientNo; }, set_intPOHubClientNo: function (v) { if (this._intPOHubClientNo !== v) this._intPOHubClientNo = v; },
    get_intGlobalLoginClientNo: function () { return this._intGlobalLoginClientNo; }, set_intGlobalLoginClientNo: function (v) { if (this._intGlobalLoginClientNo !== v) this._intGlobalLoginClientNo = v; },

	
	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.DropDowns.Country.callBaseMethod(this, "initialize");
		this.addSetupDataCall(Function.createDelegate(this, this.setupDataCall));
		this.addGetDataOK(Function.createDelegate(this, this.dataCallOK));
	},

	dispose: function() { 
		if (this.isDisposed) return;
		this._intPOHubClientNo = null;
		this._intGlobalLoginClientNo = null;
		Rebound.GlobalTrader.Site.Controls.DropDowns.Country.callBaseMethod(this, "dispose");
	},
	
	setupDataCall: function () {
	   	this._objData.set_PathToData("controls/DropDowns/Country");
		this._objData.set_DataObject("Country");
		this._objData.set_DataAction("GetData");
		this._objData.addParameter("POHubClientNo", this._intPOHubClientNo);
		this._objData.addParameter("GlobalLoginClientNo", this._intGlobalLoginClientNo);
	},

	dataCallOK: function() { 
		var result = this._objData._result;
		if (result.Countries) {
			for (var i = 0; i < result.Countries.length; i++) {
				this.addOption(result.Countries[i].Name, result.Countries[i].ID);
			}
		}
	}
	
};

Rebound.GlobalTrader.Site.Controls.DropDowns.Country.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.Country", Rebound.GlobalTrader.Site.Controls.DropDowns.Base);
