﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/*
===========================================================================================
TASK      			UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-210151]			Phuc Hoang			05-Aug-2024		CREATE          OGEL Lines Screen to remove ECCN Description
[US-223393]			NgaiTo		 		15-Jan-2025		UPDATE			223393: OGEL - Enhance visible logic in OGEL Lines screen
===========================================================================================
*/
CREATE OR ALTER PROCEDURE [dbo].[USP_OGEL_PackingExport] 
    @ClientId INT,
	@TeamId INT = NULL,
	@DivisionId INT = NULL,
	@LoginId INT = NULL,
	@OrderBy INT = 1,
	@SortDir INT = 1,
	@PageIndex INT = 0,
	@PageSize INT = 10,
	@CountrySearch INT = NULL,
	@PartNo NVARCHAR(256) = NULL,
	@InvoiceNoLo INT = NULL,
	@InvoiceNoHi INT = NULL,
	@SoNoLo NVARCHAR(50) = NULL,
	@SoNoHi NVARCHAR(50) = NULL,
	@RecentOnly BIT = 1,
	@DateOrderedFrom DATETIME = NULL,
	@DateOrderedTo DATETIME = NULL
AS
BEGIN
	DECLARE @RecentDate DATETIME,
		@StartPage INT,
		@EndPage INT

	SET @RecentDate = dbo.ufn_get_date_from_datetime(dateadd(mm, - 3, getdate()))
	SET @StartPage = (@PageIndex * @PageSize + 1)
	SET @EndPage = ((@PageIndex + 1) * @PageSize)

	IF (NOT @DateOrderedFrom IS NULL)
		SET @DateOrderedFrom = dbo.ufn_get_start_of_day_for_date(@DateOrderedFrom)

	IF (NOT @DateOrderedTo IS NULL)
		SET @DateOrderedTo = dbo.ufn_get_end_of_day_for_date(@DateOrderedTo)

	DECLARE @ShippedFromDt DATETIME,
		@ShippedToDt DATETIME

	SET @ShippedFromDt = dbo.ufn_get_date_from_datetime(dateadd(WW, - 2, getdate()))
	SET @ShippedToDt = dbo.ufn_get_date_from_datetime(dateadd(WW, 2, getdate()))
	--DROP TABLE #tempOGELLines
	CREATE TABLE #tempOGELLines (
		salesorderId INT,
		salesordernumber INT,
		SOSerialNo INT,
		AirWayBill VARCHAR(100),
		CommodityCode VARCHAR(100),
		ECCNCode VARCHAR(100),
		Notes VARCHAR(1000),
		OGELNumber VARCHAR(100),
		OGEL_MilitaryUse INT,
		OGEL_EndDestinationCountry INT,
		PartNumber NVARCHAR(256),
		CountryName VARCHAR(100),
		RowNum INT,
		CompanyName NVARCHAR(128),
		CompanyNo INT,
		CustomerPO NVARCHAR(50),
		DatePromised DATETIME,
		SalesOrderLineId INT,
		AllocationID INT,
		CreditStatus NVARCHAR(100)
		)

	IF (@RecentOnly = 1)
	BEGIN
		INSERT INTO #tempOGELLines
		SELECT salesorderId,
			salesordernumber,
			SOSerialNo,
			AirWayBill,
			vw.ECCNCode AS 'CommodityCode',
			vw.ECCNCode,
			[dbo].[RemoveNewLineAndExtraSpace](eccn.Notes) AS Notes,
			OGELNumber,
			OGEL_MilitaryUse,
			OGEL_EndDestinationCountry,
			PartNumber,
			tbc.CountryName
			--(select top 1 airwaybill from tbinvoice where salesorderno = tbso.salesorderid )  
			,
			ROW_NUMBER() OVER (
				ORDER BY CASE 
						WHEN @OrderBy = 1
							AND @SortDir = 2
							THEN salesordernumber
						END DESC,
					CASE 
						WHEN @OrderBy = 1
							THEN salesordernumber
						END,
					CASE 
						WHEN @OrderBy = 2
							AND @SortDir = 2
							THEN SOSerialNo
						END DESC,
					CASE 
						WHEN @OrderBy = 2
							THEN SOSerialNo
						END,
					CASE 
						WHEN @OrderBy = 3
							AND @SortDir = 2
							THEN AirWayBill
						END DESC,
					CASE 
						WHEN @OrderBy = 3
							THEN AirWayBill
						END,
					CASE 
						WHEN @OrderBy = 4
							AND @SortDir = 2
							THEN CommodityCode
						END DESC,
					CASE 
						WHEN @OrderBy = 4
							THEN CommodityCode
						END,
					CASE 
						WHEN @OrderBy = 5
							AND @SortDir = 2
							THEN FullPart
						END DESC,
					CASE 
						WHEN @OrderBy = 5
							THEN FullPart
						END,
					CASE 
						WHEN @OrderBy = 6
							AND @SortDir = 2
							THEN eccn.Notes
						END DESC,
					CASE 
						WHEN @OrderBy = 6
							THEN eccn.Notes
						END,
					CASE 
						WHEN @OrderBy = 7
							AND @SortDir = 2
							THEN OGELNumber
						END DESC,
					CASE 
						WHEN @OrderBy = 7
							THEN OGELNumber
						END,
					CASE 
						WHEN @OrderBy = 8
							AND @SortDir = 2
							THEN OGEL_MilitaryUse
						END DESC,
					CASE 
						WHEN @OrderBy = 8
							THEN OGEL_MilitaryUse
						END,
					CASE 
						WHEN @OrderBy = 9
							AND @SortDir = 2
							THEN OGEL_EndDestinationCountry
						END DESC,
					CASE 
						WHEN @OrderBy = 9
							THEN OGEL_EndDestinationCountry
						END
					--, case WHEN @OrderBy = 10 AND @SortDir = 2 THEN @DateOrderedTo END DESC                      
					--, case WHEN @OrderBy = 10 THEN @DateOrderedTo END    
				) AS RowNum,
				co.CompanyName,
				CompanyNo,
				CustomerPO,
				DatePromised,
				SalesOrderLineId,
				NULL AS AllocationID,
				CASE 
				WHEN co.CreditStatus = 'M'
					AND (
						NOT Month(DatePromised) = Month(GETDATE())
						AND YEAR(DatePromised) = YEAR(GETDATE())
						)
					THEN '[Stop Status : M]'
				ELSE ''
				END AS CreditStatus
		FROM VW_GetOGELLine vw
		LEFT JOIN dbo.tbeccn eccn ON vw.ECCNCode = eccn.ECCNCode
		LEFT JOIN tbcountry tbc ON tbc.countryid = vw.OGEL_EndDestinationCountry
		--LEFT JOIN dbo.tbAllocation al ON vw.SalesOrderLineId = al.SalesOrderLineNo  
		LEFT JOIN dbo.tbCompany co ON vw.CompanyNo = co.CompanyId   
		WHERE vw.clientno = @ClientId
			AND ((@CountrySearch IS NULL) OR (NOT @CountrySearch IS NULL AND OGEL_EndDestinationCountry = @CountrySearch))
			AND ((@PartNo IS NULL) OR (NOT @PartNo IS NULL AND vw.FullPart LIKE dbo.ufn_get_fullpart(@PartNo) + '%'))
			--AND ((@InvoiceNumber IS NULL) OR (NOT @InvoiceNumber IS NULL  AND InvoiceNumber = @InvoiceNumber)) 
			AND ((@InvoiceNoLo IS NULL) OR (NOT @InvoiceNoLo IS NULL AND InvoiceNumber >= @InvoiceNoLo))
			AND ((@InvoiceNoHi IS NULL) OR (NOT @InvoiceNoHi IS NULL AND InvoiceNumber <= @InvoiceNoHi))
			--AND ((@OGELNumber IS NULL) OR (NOT @OGELNumber IS NULL  AND OGELNumber = @OGELNumber)) 
			--AND (@OGELNumber IS NULL OR (NOT @OGELNumber IS NULL AND REPLACE(REPLACE(REPLACE(REPLACE(salesordernumber,' ', ''),'/',''),'&',''),'\','')             
			--LIKE REPLACE(REPLACE(REPLACE(REPLACE(@OGELNumber,' ', ''),'/',''),'&',''),'\',''))) 
			AND ((@SoNoLo IS NULL) OR (NOT @SoNoLo IS NULL AND SalesOrderNumber >= @SoNoLo))
			AND ((@SoNoHi IS NULL) OR (NOT @SoNoHi IS NULL AND SalesOrderNumber <= @SoNoHi))
			--AND ((@RecentOnly = 0) OR (@RecentOnly = 1 AND  DatePromised >= @RecentDate))  
			AND ((@ShippedFromDt IS NULL) OR (NOT @ShippedFromDt IS NULL AND DatePromised >= @ShippedFromDt))
			AND ((@ShippedToDt IS NULL) OR (NOT @ShippedToDt IS NULL AND DatePromised <= @ShippedToDt))
			AND ((@DateOrderedFrom IS NULL) OR (NOT @DateOrderedFrom IS NULL AND DatePromised >= @DateOrderedFrom))
			AND ((@DateOrderedTo IS NULL) OR (NOT @DateOrderedTo IS NULL AND DatePromised <= @DateOrderedTo))
			--and  salesordernumber = 992732
			--and salesordernumber not in (1045315)
	END
	ELSE
	BEGIN
		INSERT INTO #tempOGELLines
		SELECT salesorderId,
			salesordernumber,
			SOSerialNo,
			AirWayBill,
			vw.ECCNCode AS 'CommodityCode',
			vw.ECCNCode,
			[dbo].[RemoveNewLineAndExtraSpace](eccn.Notes) AS Notes,
			OGELNumber,
			OGEL_MilitaryUse,
			OGEL_EndDestinationCountry,
			PartNumber,
			tbc.CountryName
			--(select top 1 airwaybill from tbinvoice where salesorderno = tbso.salesorderid )  
			,
			ROW_NUMBER() OVER (
				ORDER BY CASE 
						WHEN @OrderBy = 1
							AND @SortDir = 2
							THEN salesordernumber
						END DESC,
					CASE 
						WHEN @OrderBy = 1
							THEN salesordernumber
						END,
					CASE 
						WHEN @OrderBy = 2
							AND @SortDir = 2
							THEN SOSerialNo
						END DESC,
					CASE 
						WHEN @OrderBy = 2
							THEN SOSerialNo
						END,
					CASE 
						WHEN @OrderBy = 3
							AND @SortDir = 2
							THEN AirWayBill
						END DESC,
					CASE 
						WHEN @OrderBy = 3
							THEN AirWayBill
						END,
					CASE 
						WHEN @OrderBy = 4
							AND @SortDir = 2
							THEN CommodityCode
						END DESC,
					CASE 
						WHEN @OrderBy = 4
							THEN CommodityCode
						END,
					CASE 
						WHEN @OrderBy = 5
							AND @SortDir = 2
							THEN FullPart
						END DESC,
					CASE 
						WHEN @OrderBy = 5
							THEN FullPart
						END,
					CASE 
						WHEN @OrderBy = 6
							AND @SortDir = 2
							THEN eccn.Notes
						END DESC,
					CASE 
						WHEN @OrderBy = 6
							THEN eccn.Notes
						END,
					CASE 
						WHEN @OrderBy = 7
							AND @SortDir = 2
							THEN OGELNumber
						END DESC,
					CASE 
						WHEN @OrderBy = 7
							THEN OGELNumber
						END,
					CASE 
						WHEN @OrderBy = 8
							AND @SortDir = 2
							THEN OGEL_MilitaryUse
						END DESC,
					CASE 
						WHEN @OrderBy = 8
							THEN OGEL_MilitaryUse
						END,
					CASE 
						WHEN @OrderBy = 9
							AND @SortDir = 2
							THEN OGEL_EndDestinationCountry
						END DESC,
					CASE 
						WHEN @OrderBy = 9
							THEN OGEL_EndDestinationCountry
						END
					--, case WHEN @OrderBy = 10 AND @SortDir = 2 THEN @DateOrderedTo END DESC                      
					--, case WHEN @OrderBy = 10 THEN @DateOrderedTo END    
				) AS RowNum,
				co.CompanyName,
				CompanyNo,
				CustomerPO,
				DatePromised,
				SalesOrderLineId,
				NULL AS AllocationID,
				CASE 
				WHEN co.CreditStatus = 'M'
					AND (
						NOT Month(DatePromised) = Month(GETDATE())
						AND YEAR(DatePromised) = YEAR(GETDATE())
						)
					THEN '[Stop Status : M]'
				ELSE ''
				END AS CreditStatus
		FROM VW_GetOGELLine vw
		LEFT JOIN dbo.tbeccn eccn ON vw.ECCNCode = eccn.ECCNCode
		LEFT JOIN tbcountry tbc ON tbc.countryid = vw.OGEL_EndDestinationCountry
		--LEFT JOIN dbo.tbAllocation al ON vw.SalesOrderLineId = al.SalesOrderLineNo  
		LEFT JOIN dbo.tbCompany co ON vw.CompanyNo = co.CompanyId
		WHERE vw.clientno = @ClientId
			AND ((@CountrySearch IS NULL) OR (NOT @CountrySearch IS NULL AND OGEL_EndDestinationCountry = @CountrySearch))
			AND ((@PartNo IS NULL) OR (NOT @PartNo IS NULL AND vw.FullPart LIKE dbo.ufn_get_fullpart(@PartNo) + '%'))
			--AND ((@InvoiceNumber IS NULL) OR (NOT @InvoiceNumber IS NULL  AND InvoiceNumber = @InvoiceNumber)) 
			AND ((@InvoiceNoLo IS NULL) OR (NOT @InvoiceNoLo IS NULL AND InvoiceNumber >= @InvoiceNoLo))
			AND ((@InvoiceNoHi IS NULL) OR (NOT @InvoiceNoHi IS NULL AND InvoiceNumber <= @InvoiceNoHi))
			--AND ((@OGELNumber IS NULL) OR (NOT @OGELNumber IS NULL  AND OGELNumber = @OGELNumber)) 
			--AND (@OGELNumber IS NULL OR (NOT @OGELNumber IS NULL AND REPLACE(REPLACE(REPLACE(REPLACE(salesordernumber,' ', ''),'/',''),'&',''),'\','')             
			--LIKE REPLACE(REPLACE(REPLACE(REPLACE(@OGELNumber,' ', ''),'/',''),'&',''),'\',''))) 
			AND ((@SoNoLo IS NULL) OR (NOT @SoNoLo IS NULL AND SalesOrderNumber >= @SoNoLo))
			AND ((@SoNoHi IS NULL) OR (NOT @SoNoHi IS NULL AND SalesOrderNumber <= @SoNoHi))
			--AND ((@RecentOnly = 0) OR (@RecentOnly = 1 AND  DatePromised >= @RecentDate))  
			AND ((@DateOrderedFrom IS NULL) OR (NOT @DateOrderedFrom IS NULL AND DatePromised >= @DateOrderedFrom))
			AND ((@DateOrderedTo IS NULL) OR (NOT @DateOrderedTo IS NULL AND DatePromised <= @DateOrderedTo))
			--and  salesordernumber = 992732
			--and salesordernumber not in (1045315)
	END

	SELECT *, (SELECT count(*) FROM #tempOGELLines ) AS RowCnt,
		dbo.ufn_get_salesOrder_statusNo(salesordernumber) AS [Status]
	FROM #tempOGELLines
	WHERE RowNum BETWEEN @StartPage
			AND @EndPage
	ORDER BY RowNum --salesordernumber,SOSerialNo, RowNum   
END

GO


