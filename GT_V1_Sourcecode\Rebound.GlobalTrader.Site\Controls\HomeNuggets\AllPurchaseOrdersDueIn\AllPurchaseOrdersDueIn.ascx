<%@ Control Language="C#" CodeBehind="AllPurchaseOrdersDueIn.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.HomeNuggets.AllPurchaseOrdersDueIn" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Nugget:DesignBase id="ctlDB" runat="server">
	<Content>
		<div class="homepageNugget">
		    <asp:Panel ID="pnlDueIn" runat="server">
				<ReboundUI:SimpleDataTable ID="tblDueIn" runat="server" AllowSelection="false" />
			</asp:Panel>
		    <asp:Panel ID="pnlMore" runat="server" CssClass="homeNuggetMoreLink">
				<ReboundUI:PageHyperLink id="lnkMore" runat="server" PageType="Warehouse_ReceivePurchaseOrderBrowse" OverrideTextResource="MorePurchaseOrdersDueIn" CssClass="nubButton nubButtonAlignLeft" />
			</asp:Panel>
		</div>
	</Content>
</ReboundUI_Nugget:DesignBase>
