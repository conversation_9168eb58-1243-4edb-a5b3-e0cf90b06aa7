﻿  
CREATE OR ALTER PROCEDURE [dbo].[usp_select_Invoice_for_Page]              
--************************************************************************************************              
--* Gets just the information needed for the Invoice Detail page              
--*              
--* RP 14.12.2009:              
--* - new proc              
--*Marker     Changed by      Date         Remarks              
--*[001]      Vinay           15/10/2012   Upload PDF document for invoices      
--*[002]      Vinay           21/06/2021   Upload PDF document for POD invoices            
--************************************************************************************************              
    @InvoiceId int              
AS              
    SELECT  iv.InvoiceId              
          , iv.ClientNo              
          , iv.InvoiceNumber              
          , iv.CompanyNo              
          --, co.CompanyName              
    , co.CompanyName + (CASE WHEN LTRIM(RTRIM(ISNULL(co.CustomerCode, ''))) <> '' THEN ' ( '+ co.CustomerCode + ' ) ' ELSE '' END) as CompanyName    
          , case iv.InvoicePaid              
              WHEN 1 THEN 'Paid'              
              ELSE 'Open'              
            END AS StatusText             
            --[001] code start            
          , iv.IsPDFAvailable             
            --[001] code end            
          , lg.TeamNo            
          , iv.DivisionNo               
          , iv.Salesman       
    ,c.ClientName        
    , iv.Exported  
  --[001] code start            
          , iv.IsPODPDFAvailable             
            --[001] code end         
    FROM    dbo.tbInvoice iv              
    JOIN    tbCompany co ON iv.CompanyNo = co.CompanyId            
    LEFT JOIN tbLogin lg on lg.LoginId=iv.Salesman            
 left join tbClient c on iv.ClientNo= c.ClientId        
    WHERE   InvoiceId = @InvoiceId     
    
  
  