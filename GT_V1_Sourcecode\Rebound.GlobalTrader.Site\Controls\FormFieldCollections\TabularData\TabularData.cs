using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Collections;
using Rebound.GlobalTrader.Site.Controls.Forms;

namespace Rebound.GlobalTrader.Site.Controls.FormFieldCollections {
	[DefaultProperty("")]
	[ToolboxData("<{0}:TabularData runat=server></{0}:TabularData>")]
	public class TabularData : LabelFormField, INamingContainer {

		#region Locals

		private HtmlTable _tbl;
		private List<string> _lstColumnHeaderResources = new List<string>();
		//private List<Unit> _lstColumnWidths;

		#endregion

		#region Properties

		private int _intTextBoxWidth = 0;
		public int TextBoxWidth {
			get { return _intTextBoxWidth; }
			set { _intTextBoxWidth = value; }
		}

		private ReboundTextBox.TextBoxModeList _enmTextBoxMode = ReboundTextBox.TextBoxModeList.Normal;
		public ReboundTextBox.TextBoxModeList TextBoxMode {
			get { return _enmTextBoxMode; }
			set { _enmTextBoxMode = value; }
		}


		#endregion

		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
			AddScriptReference("Controls.FormFieldCollections.TabularData.TabularData");
			RemoveCSSClass = true;
		}

		protected override void OnLoad(EventArgs e) {
			_tbl = new HtmlTable();
			_tbl.CellPadding = 0;
			_tbl.CellSpacing = 0;
			_tbl.Border = 0;
			_tbl.Attributes["class"] = "tabularData";
			AddControl(_tbl);
			SetupScriptDescriptors();
			base.OnLoad(e);
		}

		protected override void OnPreRender(EventArgs e) {
			//add column headings
			HtmlTableRow tr = new HtmlTableRow();
			for (int i = 0; i < _lstColumnHeaderResources.Count; i++) {
				HtmlTableCell th = new HtmlTableCell("th");
				th.InnerText = Functions.GetGlobalResource("FormFields", _lstColumnHeaderResources[i]);
				//th.Width = _lstColumnWidths[i];
				tr.Cells.Add(th);
			}
			_tbl.Rows.Add(tr);
			base.OnPreRender(e);
		}

		private void SetupScriptDescriptors() {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.FormFieldCollections.TabularData", this.ClientID);
			_scScriptControlDescriptor.AddElementProperty("tbl", _tbl.ClientID);
		}

		public void AddColumn(string strFormFieldResource) {
			_lstColumnHeaderResources.Add(strFormFieldResource);
			//_lstColumnWidths.Add(untWidth);
		}

	}
}