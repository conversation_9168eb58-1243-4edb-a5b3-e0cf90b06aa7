using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets {
	public partial class CRMAReceivingInfo : Base {

		#region Locals

		protected FieldSet _fldReceived;
		protected FlexiDataTable _tblReceived;

		#endregion

		#region Properties

		private int _intCRMAID = -1;
		public int CRMAID {
			get {
				return _intCRMAID;
			}
			set {
				_intCRMAID = value;
			}
		}

		#endregion

		#region Overrides

		/// <summary>
		/// Oninit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
			WireUpControls();
			AddScriptReference("Controls.Nuggets.CRMAReceivingInfo.CRMAReceivingInfo.js");
			TitleText = Functions.GetGlobalResource("Nuggets", "CRMAReceivingInfo");
			if (_objQSManager.CRMAID > 0) _intCRMAID = _objQSManager.CRMAID;
			SetupTables();
		}

		protected override void OnLoad(EventArgs e) {
			SetupScriptDescriptors();
			base.OnLoad(e);
		}

		#endregion

		/// <summary>
		/// Sets up AJAX script descriptors
		/// </summary>
		private void SetupScriptDescriptors() {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Nuggets.CRMAReceivingInfo", ctlDesignBase.ClientID);
			_scScriptControlDescriptor.AddProperty("intCRMAID", _intCRMAID);
			_scScriptControlDescriptor.AddComponentProperty("fldReceived", _fldReceived.ClientID);
			_scScriptControlDescriptor.AddComponentProperty("tblReceived", _tblReceived.ClientID);
		}

		private void SetupTables() {
			_tblReceived.Columns.Add(new FlexiDataColumn("GoodsInNo", WidthManager.GetWidth(WidthManager.ColumnWidth.SystemDocumentNumber)));
			_tblReceived.Columns.Add(new FlexiDataColumn("PartNo", "SupplierPart", WidthManager.GetWidth(WidthManager.ColumnWidth.PartNo)));
			_tblReceived.Columns.Add(new FlexiDataColumn("ManufacturerAbbreviation", "DateCode", WidthManager.GetWidth(WidthManager.ColumnWidth.ManufacturerCode)));
			_tblReceived.Columns.Add(new FlexiDataColumn("Product", "Package", WidthManager.GetWidth(WidthManager.ColumnWidth.Product)));
			_tblReceived.Columns.Add(new FlexiDataColumn("QuantityReceived", "Location"));
			_tblReceived.Columns.Add(new FlexiDataColumn("Receiver", "ReceivedDate", WidthManager.GetWidth(WidthManager.ColumnWidth.LoginName)));
		}

		/// <summary>
		/// Wire up controls from the ascx
		/// </summary>
		private void WireUpControls() {
			_fldReceived = (FieldSet)Functions.FindControlRecursive(this, "fldReceived");
			_tblReceived = (FlexiDataTable)_fldReceived.FindContentControl("tblReceived");
		}

	}
}