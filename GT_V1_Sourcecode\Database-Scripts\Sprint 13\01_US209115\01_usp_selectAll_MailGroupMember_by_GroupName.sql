﻿SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
/*   
===========================================================================================  
TASK			UPDATED BY			DATE			ACTION		DESCRIPTION  
[US-209115]		Trung Pham			23-Oct-2024		CREATE      Get LoginId
===========================================================================================  
*/
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

CREATE OR ALTER PROCEDURE [dbo].[usp_selectAll_MailGroupMember_by_GroupName]   
--  
    @MailGroupName nvarchar(250)  
AS   
    SELECT   lg.EmployeeName,lg.EMail,lg.LoginId
    FROM    tbMailGroupMember mm  
    JOIN    tbLogin lg ON mm.LoginNo = lg.LoginId  
    left join tbMailGroup mg on mg.MailGroupId=mm.MailGroupNo
    WHERE   mg.Name =@MailGroupName