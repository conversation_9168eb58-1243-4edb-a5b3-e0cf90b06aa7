///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.AutoSearch");

Rebound.GlobalTrader.Site.Controls.AutoSearch.PartSearch = function(element) { 
	Rebound.GlobalTrader.Site.Controls.AutoSearch.PartSearch.initializeBase(this, [element]);
	this._searchType = null;
};

Rebound.GlobalTrader.Site.Controls.AutoSearch.PartSearch.prototype = {

	get_searchType: function() { return this._searchType; }, set_searchType: function(value) { if (this._searchType !== value) this._searchType = value; }, 
	
	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.AutoSearch.PartSearch.callBaseMethod(this, "initialize");
		this.addDataReturnedEvent(Function.createDelegate(this, this.dataReturned));
		this.setupDataObject("PartSearch");
	},
	
	dispose: function() { 
		if (this.isDisposed) return;
		this._searchType = null;
		Rebound.GlobalTrader.Site.Controls.AutoSearch.PartSearch.callBaseMethod(this, "dispose");
	},

	dataReturned: function() {
		if (!this._result) return;
		if (this._result.TotalRecords > 0) {
			for (var i = 0, l = this._result.Results.length; i < l; i++) {
				var res = this._result.Results[i];
				var strHTML = "";
				if (this._enmResultsActionType != $R_ENUM$AutoSearchResultsActionType.Navigate) {
					strHTML = $R_FN.setCleanTextValue(res.Name);
				}
				this.addResultItem(strHTML, $R_FN.setCleanTextValue(res.Name), res.ID);
				strHTML = null; res = null;
			}
		}
	}
};

Rebound.GlobalTrader.Site.Controls.AutoSearch.PartSearch.registerClass("Rebound.GlobalTrader.Site.Controls.AutoSearch.PartSearch", Rebound.GlobalTrader.Site.Controls.AutoSearch.Base, Sys.IDisposable);
