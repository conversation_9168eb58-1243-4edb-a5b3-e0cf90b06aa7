﻿@*@{
        Layout = null;
    }

    <!DOCTYPE html>

    <html>
    <head>
        <meta name="viewport" content="width=device-width" />
        <title>Index</title>
    </head>
    <body>
        <div>
            This is BOM Manager page in BOMManagerController.
        </div>
    </body>
    </html>*@


<!DOCTYPE html>

<html lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta charset="utf-8" />
    <script src='https://code.jquery.com/jquery-2.2.4.min.js'></script>
    <script src='https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/js/bootstrap.min.js'></script>
    <!-- Latest compiled and minified CSS -->
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/css/bootstrap.min.css" integrity="sha384-1q8mTJOASx8j1Au+a5WDVnPi2lkFfwwEAa8hDDdjZlpLegxhjVME1fgjWPGmkzs7" crossorigin="anonymous">

    <script src="https://unpkg.com/jquery@2.2.4/dist/jquery.js"></script>


    <link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/4.1.3/css/bootstrap.min.css'>
    <script src='https://cdnjs.cloudflare.com/ajax/libs/jquery/3.4.1/jquery.min.js'></script>
    <script src='https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.15.0/popper.min.js'></script>
    <script src='https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/4.3.1/js/bootstrap.min.js'></script>



    <script src="https://unpkg.com/jquery@2.2.4/dist/jquery.js"></script>

    <!--jQueryUI version 1.11.4 -->
    <link rel="stylesheet" href="https://code.jquery.com/ui/1.11.4/themes/smoothness/jquery-ui.css" />
    <script src="https://code.jquery.com/ui/1.11.4/jquery-ui.min.js"></script>
    <link rel="stylesheet" href="//code.jquery.com/ui/1.13.2/themes/base/jquery-ui.css">
    @*<script src="https://code.jquery.com/jquery-3.6.0.js"></script>
        <script src="https://code.jquery.com/ui/1.13.2/jquery-ui.js"></script>*@

    <!--ParamQuery Grid css files-->
    <link rel="stylesheet" href="~/paramquery-8.1.0/pqgrid.dev.css" />

    <!--add pqgrid.ui.css for jQueryUI theme support-->
    <link rel="stylesheet" href="~/paramquery-8.1.0/pqgrid.ui.dev.css" />

    <!--ParamQuery Grid custom theme e.g., office, bootstrap, rosy, chocolate, etc (optional)-->
    @*<link rel="stylesheet" href="~/paramquery-8.1.0/themes/bootstrap/pqgrid.css" />*@

    <!--ParamQuery Grid js files-->
    <script type="text/javascript" src="~/paramquery-8.1.0/pqgrid.dev.js"></script>

    <!--ParamQuery Grid localization file-->
    <script src="~/paramquery-8.1.0/localize/pq-localize-en.js"></script>

    <!--Include pqTouch file to provide support for touch devices (optional)-->
    <script type="text/javascript" src="~/paramquery-8.1.0/pqTouch/pqtouch.min.js"></script>

    <!--Include jsZip file to support xlsx and zip export (optional)-->
    <script type="text/javascript" src="~/paramquery-8.1.0/jsZip-2.5.0/jszip.min.js"></script>
    @*<script type="text/javascript" src="~/Areas/BOM/js/BOMSearch.js"></script>*@

    <title></title>

    <style>
        body {
            font-family: Tahoma;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

            table tr {
                vertical-align: top;
                float: left;
                margin-right: 20px;
            }


                .table tr th, table tr td {
                    text-align: left;
                    font-size: 11px;
                    float: left;
                }

        .desc {
            width: 95px;
            padding-right: 15px;
            padding-bottom: 5px;
            color: #009900;
            font-family: Tahoma !important;
        }

        .filter_container input, .filter_container select {
            opacity: 0.5;
            border: none;
        }

            .filter_container input[type=checkbox] {
                opacity: 1 !important;
            }

        .filter_container select {
            padding: 2px;
        }

            .filter_container input:focus, .filter_container select:focus {
                opacity: 1;
                border: none;
            }

                .filter_container select:focus > .ui-datepicker-trigger {
                    opacity: 1;
                }

        .filter_container select {
            float: left;
        }


        .main_container {
            background-color: #fff;
            color: #fff;
            font-size: 11px;
            font-family: tahoma;
            padding: 2px 2px 2px;
            position: relative;
        }

        .top_head {
            background-color: #BBF2B3;
            color: #fff;
            font-size: 11px;
            padding: 5px 5px 5px;
            position: relative;
        }

        h5 {
            font-size: 10px;
            border-bottom: dotted 1px #90db89;
            margin: 10px 0px;
            font-weight: bold;
            color: #009900;
            padding-bottom: 3px;
            font-family: Lucida Sans, Arial;
            text-transform: uppercase;
        }
        /*   .ui-datepicker-calendar {
            background-repeat:no-repeat; background-position:left center;
            font-size: 10px;
            background-image: url( http://localhost:49861/App_Themes/Original/images/calendar/bg.jpg);
            background-size:cover;
        }*/
        .ui-datepicker-calendar tr {
            float: none !important;
        }

            .ui-datepicker-calendar tr td {
                float: none !important;
            }

        .ui-datepicker {
            width: 19em;
            padding: 0px !important;
        }

            .ui-datepicker td span, .ui-datepicker td a {
                text-align: center;
            }

        .ui-state-default {
            color: #575779 !important;
            border: none !important;
            background: none !important;
        }

        .ui-widget.ui-widget-content {
            border: solid 2px #50508e;
            background-repeat: no-repeat;
            background-position: left center;
            font-size: 10px;
            background-image: url( http://localhost:49861/App_Themes/Original/images/calendar/bg.jpg);
            background-size: cover;
        }

        .ui-datepicker table {
            font-size: 10px;
            font-weight: bold;
        }

        .ui-widget-header {
            border-bottom: solid 1px #50508e;
            background: none !important;
        }

        .ui-corner-all, .ui-corner-bottom, .ui-corner-left, .ui-corner-bl {
            border-bottom-left-radius: none !important;
        }

        .ui-datepicker-title {
            font-size: 12px;
        }

        .ui-datepicker-week-end {
            color: #8888bb;
        }

        .icon_header span a {
            background-image: url(../../../App_Themes/Original/images/IconButton/filters/off.gif);
            background-position: left center;
            background-repeat: no-repeat;
            padding: 8px 0px 7px 21px;
            margin-right: 8px;
            color: #009900 !important;
        }

            .icon_header span a:hover {
                text-decoration: underline !important;
                cursor: pointer;
            }

        .pq-cont-inner span a {
            color: #0000ff !important;
            background-image: url(http://localhost:49861/App_Themes/Original/images/nubs/nub.gif);
            background-repeat: no-repeat;
            background-position: left 2px;
            padding-left: 13px;
            height: 12px;
        }


        .reset_icon {
            background-image: url(../../../App_Themes/Original/images/IconButton/filters/reset.gif) !important;
        }

        .apply_icon {
            background-image: url(../../../App_Themes/Original/images/IconButton/filters/apply.gif) !important;
        }

        .icon_header {
            position: absolute;
            top: 10px;
            right: 10px;
        }

        .tbl_grid {
            background: none !important;
            border: none !important;
            border-right: 1px solid #BBF2B3 !important;
            border-left: 1px solid #BBF2B3 !important;
        }



        .ui-corner-top {
            display: none;
        }

        .ui-datepicker-trigger {
            opacity: 0.5;
            margin-left: 2px;
        }

        .pq-grid-header-table {
            background-image: url(../../../App_Themes/Original/images/FlexiDataTable/th_bg.png);
            background-position: bottom;
            background-repeat: repeat-x;
            border-bottom: solid 2px #e0e0e0;
            background-color: #eeeeee;
        }


            .pq-grid-header-table span {
                font-weight: bold !important;
                font-size: 11px;
                color: #999999;
                font-family: Tahoma;
            }

        .pq-grid-col {
            border-color: #bbb;
        }



            .pq-grid-col:hover {
                background-color: #e0e0e0;
                background-image: url(../../../App_Themes/Original/images/FlexiDataTable/th_bg.png);
                background-position: bottom;
                background-repeat: repeat-x;
            }

        .ui-widget-header .ui-icon {
            visibility: visible;
        }

        .pq-col-sort-icon {
            background-image: url(http://code.jquery.com/ui/1.13.2/themes/base/images/ui-icons_444444_256x240.png);
            background-position: left -15px;
            padding: 2px 17px 0px 0px;
            display: inline;
        }

        .ui-icon, .ui-widget-content .ui-icon {
            background-position: -64px -15px !important;
        }

        .page_tittle {
            width: 100%;
            float: left;
            font-family: 'Lucida Sans', 'Lucida Sans Regular', 'Lucida Grande', 'Lucida Sans Unicode', Geneva, Verdana, sans-serif;
            display: contents;
        }

            .page_tittle h3 {
                font-size: 22px;
                background-image: url(../../../App_Themes/Original/images/Nuggets/titlebar/bg.png);
                background-repeat: repeat-x;
                background-position: bottom;
                padding: 15px 5px 5px;
                border-bottom: solid 1px #bbbbbb;
            }

        .pq-grid-overlay {
            background: none;
            border-style: none;
        }

        .ui-draggable {
            background: none;
            border: none !important;
        }

        .pq-grid-row.pq-striped {
            background: none !important;
            font-size: 11px;
            border-bottom: 1px dotted #bbb !important;
        }

        .pq-grid-row {
            font-size: 11px !important;
            line-height: 1.6px;
            border-bottom: 1px dotted #bbb !important;
        }

        .pq-grid-cell {
            border-right: 1px solid #bbb !important;
        }

        .pq-grid-row.pq-striped:hover {
            background-color: #e0ffe0 !important;
        }

        .pq-grid-row:hover {
            background-color: #e0ffe0;
        }

        .pq-grid-cell > div {
            line-height: 18px;
        }



        /*//styling form tabs.*/


        .tab .tablinks {
            float: left;
            height: 20px;
            padding: 5px 5px 0px 5px;
            background-image: url(../../../App_Themes/Original/images/Nuggets/allwhite/tab_off_bg.gif);
            background-repeat: repeat-x;
            position: relative;
            top: -1px;
            font-family: Lucida Sans Unicode, Arial;
            font-size: 11px;
            line-height: 11px;
            color: #b0b0b0;
            font-weight: normal;
            text-decoration: none;
            margin-right: 3px;
        }
        /* Style the buttons inside the tab */
        .tab button {
            background-color: inherit;
            float: left;
            border: none;
            outline: none;
            cursor: pointer;
            font-size: 11px;
            border-radius: 3px;
        }

            /* Change background color of buttons on hover */
            .tab button:hover {
                background-color: #ddd;
            }

            /* Create an active/current tablink class */
            .tab button.active {
                top: -5px;
                color: #248717;
                height: 25px;
                background-image: url(../../../App_Themes/Original/images/Nuggets/allwhite/tab_on_bg.gif);
                padding: 8px 15px 0px 15px;
            }

                .tab button.active::after {
                    float: right;
                    top: -8px;
                    color: #248717;
                    height: 25px;
                    background-image: url(../../../App_Themes/Original/images/Nuggets/allwhite/tab_on_r.gif);
                    right: -17px;
                }

                .tab button.active::before {
                    top: -8px;
                    color: #248717;
                    height: 25px;
                    float: left;
                    background-image: url(../../../App_Themes/Original/images/Nuggets/allwhite/tab_on_l.gif);
                    left: -17px;
                }

        /* Style the tab content */

        .tab {
            position: relative;
            bottom: 25px;
            right: 5px;
            padding: 0px;
            margin: 0px;
            float: right;
        }

            .tab .tablinks::after {
                content: '';
                background: url(../../../App_Themes/Original/images/Nuggets/allwhite/tab_off_r.gif);
                float: right;
                width: 5px;
                height: 20px;
                background-repeat: no-repeat;
                position: relative;
                top: -5px;
                right: -7px;
            }

            .tab .tablinks::before {
                content: '';
                background: url(../../../App_Themes/Original/images/Nuggets/allwhite/tab_off_l.gif);
                float: left;
                width: 5px;
                height: 20px;
                background-repeat: no-repeat;
                position: relative;
                top: -5px;
                left: -7px;
            }


        .pq-grid-bottom {
            padding: 10px 0px !important;
            float: left;
            display: block;
            width: 100%;
        }

            .pq-grid-bottom table {
                background-color: #b6f2ac;
                height: 40px !important;
                color: #1fa00e;
                font-size: 11px;
                font-weight: bold;
            }

                .pq-grid-bottom table tr {
                    padding: 6px 0px;
                }

        .ui-corner-bottom .pq-pager {
            padding: 0px !important;
        }

        .uploadlink {
            float: right;
            width: 100%;
            text-align: right;
            padding: 10px;
        }

        .uploadanchor {
            background-image: url(../../../App_Themes/Original/images/nubs/nub.gif);
            background-repeat: no-repeat;
            background-position: left center;
            padding-left: 13px;
            font-size: 11px;
            font-family: Tahoma !important;
            color: #0000ff;
        }

        .topTL {
            background-image: url(../../../App_Themes/Original/images/Nuggets/list/top.gif);
            margin-right: 6px;
            height: 6px;
            font-size: 2px;
        }

        .topTR {
            background-image: url(../../../App_Themes/Original/images/Nuggets/list/top.gif);
            margin-top: -6px;
            margin-left: 6px;
            background-position: 100% 0px;
            height: 6px;
            font-size: 2px;
        }

        .top_inner h4 {
            font-family: Lucida Sans Unicode, Arial;
            font-weight: normal;
            font-size: 12px;
            line-height: 12px;
            color: #000000;
            margin: 0px;
            padding: 2px 0px 0px 0px;
        }

        .refreshButton {
            display: block;
            visibility: visible;
            background-image: url(../../../App_Themes/Original/images/Nuggets/standard/refresh.gif);
        }

        .refreshButton {
            background-repeat: no-repeat;
            height: 10px;
            width: 11px;
            position: absolute;
            top: 3px;
            right: 24px;
            margin: 0px;
            padding: 0px;
            cursor: pointer;
        }

        .head_in {
            height: 68px;
            border-width: 0px;
            border-style: solid;
            border-color: #bbbbbb;
            height: 37px;
            padding: 0px 5px 5px 5px;
            position: relative;
            background-color: #ffffff;
            position: relative;
            background-image: url(../../../App_Themes/Original/images/Nuggets/standard/bg_top.gif);
            background-repeat: repeat-x;
            border-bottom: solid 1px #AAE2A5;
        }



        .showHide {
            background-image: url(../../../App_Themes/Original/images/Nuggets/list/hide.gif);
            position: absolute;
            top: 0px;
            right: 5px;
            margin: 0px;
            padding: 0px;
            height: 15px;
            width: 16px;
            cursor: pointer;
            background-repeat: no-repeat;
            background-position: center center;
        }

        .export_link {
            position: relative;
            top: 8px;
        }

        .exp_icon {
            background-image: url(../../../App_Themes/Original/images/IconButton/nuggets/excel.png);
            width: 20px;
            height: 17px;
            text-align: left;
            margin-right: 8px;
            background-repeat: no-repeat;
            padding: 2px 0px 5px 21px;
            position: relative;
            top: 1px;
            color: #009900;
            font-size: 11px;
            text-decoration: none;
        }

            .exp_icon:hover {
                color: #009900;
            }


        .col1, .col2 {
            width: 50%;
            border-width: 0px;
            padding: 0px;
        }

            .col1 tr, .col2 tr {
                width: 100%;
                margin-bottom: 5px;
            }

        .company_name select {
            display: none;
        }




        .resulttable {
            height: 450px !important;
        }



        .pq-cont-inner {
            overflow-y: scroll;
            overflow-x: hidden;
        }

        .pq-header-outer {
            border-bottom: solid 2px #e0e0e0;
        }

        .data_filter {
            background-color: #FFFFFF;
            border-color: #AAE2A0;
            border-style: solid;
            border-width: 0px 1px;
            left: 0px;
            padding: 1px 1px 0px 1px;
            position: relative;
            top: 0px;
        }


        .pq-grid-center-o {
            padding: 10px 5px 12px 5px;
        }

        .head_effect {
            height: 21px;
        }

        .invisible {
            visibility: visible !important;
        }
    </style>

</head>
<body>
    <div class="uploadlink"><a href="../../Utility_BOMManagerImport.aspx" target="_top" class="uploadanchor">Upload BOM Sheet</a></div>

    <div class="page_tittle"> <h3>BOM MANAGER</h3></div>
    <div class="tab">
        <button class="tablinks" id="tabMy" onclick="openCity(event, 'divMy')">My</button>
        <button class="tablinks" id="tabTeam" onclick="openCity(event, 'divTeam')">Team</button>
        <button class="tablinks" id="tabDivision" onclick="openCity(event, 'divDivision')">Division</button>
        <button class="tablinks" id="tabCompany" onclick="openCity(event, 'divCompany')">Company</button>
    </div>
    <div class="main_container">
        <div class="topTL"></div>
        <div class="topTR"></div>
        <div class="head_in">
            <div class="top_inner">
                <h4>BOM MANAGER</h4>

                <img class="refreshButton" src="/images/x.gif" style="height:10px;width:11px;border-width:0px;">
                <a role="button" class="showHide" onclick="myFunction();"><img id="icon_id" class="invisible" src="/images/x.gif" style="height:15px;width:16px;border-width:0px;"></a>

                <div class="export_link" style="margin-left: 4px;" id="Export_exl">

                    <a href="" class="exp_icon">Export to Excel</a>

                </div>
            </div>
        </div>
        <div id="myDIV">

            <div class="data_filter">
                <div class="top_head">

                    <div class="formHeader">

                        <h5>Filter Results</h5>
                        <div class="icon_header">
                            <span><a href="">Off</a></span>
                            <span><a href="" class="reset_icon">Reset</a></span>
                            <span><a id="btnSearch" class="apply_icon">Apply</a></span>
                            @*<input type="button" value="abc" id="btnSearch"/>*@

                        </div>


                    </div>

                    <div class="filter_container">
                        <table>
                            <tbody>
                                <tr>
                                    <td class="col1">

                                        <table>
                                            <tbody>
                                                <tr>
                                                    <td class="desc">BOM ID</td>
                                                    <td><input type="checkbox" id="checkboxBOMId" /> <input type="text" id="txtBOMId" style=" color: black;" /></td>
                                                </tr>
                                                <tr>
                                                    <td class="desc">
                                                        Company Name

                                                    </td>
                                                    <td class="company_name">
                                                        <input type="checkbox" id="checkboxCompName" />
                                                        <input type="text" id="txtCompName" style=" color: black;" />
                                                        <input type="hidden" id="HiddenCompName" />
                                                        @*@Html.DropDownList("SelectedItem", new SelectList((System.Collections.IEnumerable)ViewData["CustomerName"], "ClientID", "ClientName"))*@
                                                    </td>
                                                </tr>



                                            </tbody>

                                        </table>


                                    </td>
                                    <td class="col2">
                                        <table>
                                            <tbody>
                                                <tr>
                                                    <td class="desc">From Date:</td>
                                                    <td><input type="checkbox" id="checkboxFromDate" /> <input type="text" id="FromDate" style=" color: black;"></td>
                                                </tr>
                                                <tr>
                                                    <td class="desc">To Date:</td>
                                                    <td><input type="checkbox" id="checkboxToDate" /> <input type="text" id="ToDate" style=" color: black;"></td>
                                                </tr>

                                            </tbody>


                                        </table>



                                    </td>

                                </tr>


                            </tbody>

                        </table>










                    </div>
                </div>

            </div>






            @*<div class="box boxStandard" id="section_hide">
                <div class="boxInner">
                    <div class="boxTL"></div>
                    <div class="boxTR"></div>
                    <div class="boxHeader">
                    </div>
                    <div class="boxContent">
                        <div class="resulttable">
                            <div class="tbl_grid" id="grid_md2"></div>
                        </div>
                    </div>
                    <div class="boxBL"></div>
                    <div class="boxBR"></div>

                </div>
            </div>*@
            <div id="divMy" class="tabcontent">
                @*<h3 style="color:black">This is My Data</h3>
                    <p>This is My Data</p>*@
                <div class="box boxStandard">
                    <div class="boxInner">
                        <div class="boxTL"></div>
                        <div class="boxTR"></div>
                        <div class="boxHeader">
                        </div>
                        <div class="boxContent">
                            <div class="resulttable">
                                <div class="tbl_grid" id="gridMy"></div>
                            </div>
                        </div>
                        <div class="boxBL"></div>
                        <div class="boxBR"></div>

                    </div>
                </div>
            </div>

            <div id="divTeam" class="tabcontent">
                @*<h3 style="color:black">This is Team Data</h3>
                    <p>This is Team Data</p>*@
                <div class="box boxStandard">
                    <div class="boxInner">
                        <div class="boxTL"></div>
                        <div class="boxTR"></div>
                        <div class="boxHeader">
                        </div>
                        <div class="boxContent">
                            <div class="resulttable">
                                <div class="tbl_grid" id="gridTeam"></div>
                            </div>
                        </div>
                        <div class="boxBL"></div>
                        <div class="boxBR"></div>

                    </div>
                </div>
            </div>

            <div id="divDivision" class="tabcontent">
                @*<h3 style="color:black">This is Team Division</h3>
                    <p>This is Division Data.</p>*@
                <div class="box boxStandard">
                    <div class="boxInner">
                        <div class="boxTL"></div>
                        <div class="boxTR"></div>
                        <div class="boxHeader">
                        </div>
                        <div class="boxContent">
                            <div class="resulttable">
                                <div class="tbl_grid" id="gridDivision"></div>
                            </div>
                        </div>
                        <div class="boxBL"></div>
                        <div class="boxBR"></div>

                    </div>
                </div>
            </div>
            <div id="divCompany" class="tabcontent">
                @*<h3 style="color:black">This is Team Company</h3>
                    <p>This is Company Data.</p>*@
                <div class="box boxStandard">
                    <div class="boxInner">
                        <div class="boxTL"></div>
                        <div class="boxTR"></div>
                        <div class="boxHeader">
                        </div>
                        <div class="boxContent">
                            <div class="resulttable">
                                <div class="tbl_grid" id="gridCompany"></div>
                            </div>
                        </div>
                        <div class="boxBL"></div>
                        <div class="boxBR"></div>

                    </div>
                </div>
            </div>


            <script type="text/javascript">
                var searchtypevalue;
                function openCity(evt, cityName) {
                    var i, tabcontent, tablinks;
                    tabcontent = document.getElementsByClassName("tabcontent");
                    for (i = 0; i < tabcontent.length; i++) {
                        tabcontent[i].style.display = "none";
                    }
                    tablinks = document.getElementsByClassName("tablinks");
                    for (i = 0; i < tablinks.length; i++) {
                        tablinks[i].className = tablinks[i].className.replace(" active", "");
                    }
                    document.getElementById(cityName).style.display = "block";
                    evt.currentTarget.className += " active";
                    if (cityName == 'divMy') {
                        searchtypevalue = 0;
                        LoadMyGridData(0, 1);
                    }
                    else if (cityName == 'divTeam') {
                        searchtypevalue = 1;
                        LoadTeamGridData(1, 1);
                    }
                    else if (cityName == 'divDivision') {
                        searchtypevalue = 2;
                        LoadDivisionGridData(2, 1);
                    }
                    else {
                        searchtypevalue = 3;
                        LoadCompanyGridData(3, 1);
                    }
                }
                var $gridPrj;
                $(document).ready(function () {
                    console.log("ready!");
                    $("#FromDate").datepicker({
                        showOn: "button",
                        buttonImage: "/App_Themes/Original/images/calendar/cal.gif",
                        buttonImageOnly: true,
                        buttonText: "Select date"
                    });
                    $("#ToDate").datepicker({
                        showOn: "button",
                        buttonImage: "/App_Themes/Original/images/calendar/cal.gif",
                        buttonImageOnly: true,
                        buttonText: "Select date"
                    });

                    //var abc = GetBOMSearchData();
                    //LoadMyGridData();

                    var gg = 'check';
                    //debugger;

                    /*document.getElementById("tabMy").click()*/
                    openCity(event, 'divMy')
                    $('#tabMy').addClass("active");
                    //LoadMyGridData(0, 0);
                });
                $('#btnSearch').click(function () {

                    if (searchtypevalue == 0)
                        LoadMyGridData(searchtypevalue, 1);
                    if (searchtypevalue == 2)
                        LoadTeamGridData(searchtypevalue, 1);
                    if (searchtypevalue == 3)
                        LoadDivisionGridData(searchtypevalue, 1);
                    if (searchtypevalue == 4)
                        LoadCompanyGridData(searchtypevalue, 1);


                });
                function LoadPagingGrid() {
                    var colModel = [
                        {
                            title: "BOMId", //title of column.
                            width: "10%", //initial width of column
                            //dataType: "integer", //data type of column
                            dataIndx: "BOMManagerId", //should match one of the keys in row data.
                            render: function (ui) {
                                var htmlstr = "<span><a href='../../ord_BOMManagerDetail.aspx?BOM=" + ui.rowData.BOMManagerId + "' target='_top'>" + ui.rowData.BOMManagerId.toString() + "</a></span ><br/>";
                                return htmlstr;
                            }
                        },
                        {
                            title: "BOM Name", //title of column.
                            width: "15%", //initial width of column
                            //dataType: "integer", //data type of column
                            dataIndx: "BOMManagerName", //should match one of the keys in row data.
                        },
                        {
                            title: "Part No <br/> Manufacturer",
                            width: "10%",
                            //dataType: "string",
                            dataIndx: "Part",
                            hidden: true,
                            render: function (ui) {
                                //return ui.rowData.BOMManagerNo + '-<br/>' + ui.rowData.Part
                                debugger;
                                return "<span>" + ui.rowData.Part.toString() + "</span ><br/><span><a  href='../../Con_ManufacturerDetail.aspx?mfr=" + ui.rowData.ManufacturerNo + "' target='_top'>" + ui.rowData.ManufacturerCode.toString() + "</a></span>";
                            }
                        },
                        {
                            title: "Quantity",
                            width: "20%",
                            //dataType: "float",
                            //align: "right",
                            dataIndx: "Quantity",
                            hidden: true
                        },
                        {
                            title: "Company <br/> Contact",
                            width: "25%",
                            /*dataType: "float",*/
                            //align: "right",
                            dataIndx: "CompanyName",
                            render: function (ui) {
                                //return ui.rowData.BOMManagerNo + '-<br/>' + ui.rowData.Part
                                //debugger;
                                var htmlstr = "<span><a href='../../Con_CompanyDetail.aspx?cm=" + ui.rowData.CompanyNo + "' target='_top'>" + ui.rowData.CompanyName.toString() + "</a></span ><br/>";
                                htmlstr = htmlstr + " <span><a href='../../Con_ContactDetail.aspx?con=" + ui.rowData.ContactNo + "' target='_top'>" + ui.rowData.ContactName.toString() + "</a></span > ";
                                return htmlstr;
                            }
                        },
                        {
                            title: "Salesperson",
                            width: "20%",
                            /*dataType: "float",*/
                            //align: "right",
                            dataIndx: "SalesmanName"
                        },
                        {
                            title: "Received Date <br/> Promised",
                            width: "10%",
                            dataType: "Date",
                            //align: "right",
                            dataIndx: "ReceivedDate",
                            render: function (ui) {
                                var d = ui.rowData.DLUP;
                                var dt = new Date(parseInt(d.replace("/Date(", "").replace(")/", "")))
                                var RDate = dt.getDate() + "/" + (dt.getMonth() + 1) + "/" + dt.getFullYear();
                                var htmlstr = "<span>" + RDate + "</span ><br/>";
                                var d1 = ui.rowData.DLUP;
                                var dt1 = new Date(parseInt(d1.replace("/Date(", "").replace(")/", "")))
                                var Pdate = dt1.getDate() + "/" + (dt1.getMonth() + 1) + "/" + dt1.getFullYear();
                                htmlstr = htmlstr + "<span>" + Pdate + "</span ><br/>";
                                return htmlstr;
                            }
                        },
                        {
                            title: "Status",
                            width: "10%",
                            /*dataType: "float",*/
                            //align: "right",
                            dataIndx: "BOMManagerStatus"
                        },
                        {
                            title: "Total Value",
                            width: "10%",
                            /*dataType: "float",*/
                            //align: "right",
                            dataIndx: "TotalValueStr",
                            render: function (ui) {
                                var htmlstr = "<span>" + ui.rowData.TotalValueStr + "</span ><br/>";
                                //htmlstr = htmlstr + "<span>" + ui.rowData.TotalInBaseStr + "</span ><br/>";
                                return htmlstr;
                            }
                        }
                    ];
                    var BOMID = $('#txtBOMId').val();
                    var clientid = $("#SelectedItem").val();
                    //var BomDateVal = $('#FromDate').val();
                    var todayFromDate = new Date($('#FromDate').val());
                    var ddFromDate = todayFromDate.getDate();
                    var mmFromDate = (todayFromDate.getMonth() + 1);
                    var yyyyFromDate = todayFromDate.getFullYear();
                    var BomDateValFromDate = ddFromDate + '/' + mmFromDate + '/' + yyyyFromDate;
                    if (BomDateValFromDate == 'NaN/NaN/NaN') { BomDateValFromDate = ''; }
                    var bomdata;
                    //BOMID = 1;
                    //clientid = 118,
                    //    BomDateVal = '2012-01-01';

                    if (BOMID == undefined || BOMID == "")
                        BOMID = 0;
                    if (clientid == undefined || clientid == "")
                        clientid = 0;
                    if (BomDateValFromDate == undefined || BomDateValFromDate == "")
                        BomDateValFromDate = "";

                    var todayToDate = new Date($('#ToDate').val());
                    var ddToDate = todayToDate.getDate();
                    var mmToDate = (todayToDate.getMonth() + 1);
                    var yyyyToDate = todayToDate.getFullYear();
                    var BomDateValToDate = ddToDate + '/' + mmToDate + '/' + yyyyToDate;
                    if (BomDateValToDate == 'NaN/NaN/NaN') { BomDateValToDate = ''; }
                    var bomdata;
                    //BOMID = 1;
                    //clientid = 118,
                    //    BomDateVal = '2012-01-01';

                    if (BOMID == undefined || BOMID == "")
                        BOMID = 0;
                    if (clientid == undefined || clientid == "")
                        clientid = 0;
                    if (BomDateValToDate == undefined || BomDateValToDate == "")
                        BomDateValToDate = "";


                    /*ToDate*/
                    var companynoval = $("#HiddenCompName").val();
                    //var dataModel = GetBOMSearchData1();
                    var dataModel = {
                        location: "remote",
                        dataType: "JSON",
                        method: "GET",
                        //url: 'Paging?BOMId=' + BOMID + '&clientId=' + clientid + '&BOMDate=' + BomDateVal,
                        url: "Paging",
                        data: { BOMId: BOMID, clientId: clientid, BOMDate: BomDateValFromDate },
                        //url: "/pro/invoice.php",//for PHP
                        getData: function (dataJSON) {
                            debugger;
                            var data = dataJSON.data;
                            return { curPage: dataJSON.curPage, totalRecords: dataJSON.totalRecords, data: data };
                        }
                    };
                    //$gridPrj = $("#grid_md").pqGrid(obj).pqGrid('refreshDataAndView');
                    var grid1 = $("#grid_md").pqGrid({
                        width: "auto", maxheight: 700,
                        dataModel: dataModel,
                        colModel: colModel,

                        //freezeCols: 2,
                        pageModel: { type: "remote", rPP: 20, strRpp: "{0}" },
                        numberCell: { show: false },
                        //sortable: false,
                        //selectionModel: { swipe: false },
                        //wrap: false, hwrap: false,
                        //virtualX: true, virtualY: true,
                        //numberCell: { resizable: true, width: 30, title: "#" },
                        //title: "Shipping Orders",
                        resizable: true
                    });//.pqGrid('refreshDataAndView');
                }
                function LoadGridData2(searchType) {
                                           // var data = GetBOMSearchData();
                                                                                        /*debugger*/;
                    //[
                    //    { "SaleBOMManagerImportId": 1, "StockCode": 251648, "Description": "SAMSUNG", "Part": "CL05B104KO5NNNC", "RFQ": 380000, "UnitPrice": 0.0013, "LineTotal": 494, "Clientid": 118, "ClientName": "Rebound Electronics (Hong Kong) Limited Local Test", "StatusId": 1, "Islock": true, "ProductInactive": null },
                    //    { "SaleBOMManagerImportId": 2, "StockCode": 251649, "Description": "SAMSUNG", "Part": "CL05B104KO5NNNd", "RFQ": 380000, "UnitPrice": 0.0013, "LineTotal": 494, "Clientid": 118, "ClientName": "Rebound Electronics (Hong Kong) Limited Local Test", "StatusId": 1, "Islock": true, "ProductInactive": null },
                    //    { "SaleBOMManagerImportId": 3, "StockCode": 251649, "Description": "SAMSUNG", "Part": "CL05B104KO5NNNd", "RFQ": 380000, "UnitPrice": 0.0013, "LineTotal": 494, "Clientid": 118, "ClientName": "Rebound Electronics (Hong Kong) Limited Local Test", "StatusId": 1, "Islock": true, "ProductInactive": null }
                    //];
                    //array of columns.
                    var colModel = [
                        {
                            title: "BOMId", //title of column.
                            width: "10%", //initial width of column
                            //dataType: "integer", //data type of column
                            dataIndx: "BOMManagerId", //should match one of the keys in row data.
                            render: function (ui) {
                                var htmlstr = "<span><a href='../../ord_BOMManagerDetail.aspx?BOM=" + ui.rowData.BOMManagerId + "' target='_top'>" + ui.rowData.BOMManagerId.toString() + "</a></span ><br/>";
                                return htmlstr;
                            }
                        },
                        {
                            title: "BOM Name", //title of column.
                            width: "15%", //initial width of column
                            //dataType: "integer", //data type of column
                            dataIndx: "BOMManagerName", //should match one of the keys in row data.
                        },
                        {
                            title: "Part No <br/> Manufacturer",
                            width: "10%",
                            //dataType: "string",
                            dataIndx: "Part",
                            hidden: true,
                            render: function (ui) {
                                //return ui.rowData.BOMManagerNo + '-<br/>' + ui.rowData.Part
                                //debugger;
                                return "<span>" + ui.rowData.Part.toString() + "</span ><br/><span><a  href='../../Con_ManufacturerDetail.aspx?mfr=" + ui.rowData.ManufacturerNo + "' target='_top'>" + ui.rowData.ManufacturerCode.toString() + "</a></span>";
                            }
                        },
                        {
                            title: "Quantity",
                            width: "20%",
                            //dataType: "float",
                            //align: "right",
                            dataIndx: "Quantity",
                            hidden: true
                        },
                        {
                            title: "Company <br/> Contact",
                            width: "25%",
                            /*dataType: "float",*/
                            //align: "right",
                            dataIndx: "CompanyName",
                            render: function (ui) {
                                //return ui.rowData.BOMManagerNo + '-<br/>' + ui.rowData.Part
                                //debugger;
                                var htmlstr = "<span><a href='../../Con_CompanyDetail.aspx?cm=" + ui.rowData.CompanyNo + "' target='_top'>" + ui.rowData.CompanyName.toString() + "</a></span ><br/>";
                                htmlstr = htmlstr + " <span><a href='../../Con_ContactDetail.aspx?con=" + ui.rowData.ContactNo + "' target='_top'>" + ui.rowData.ContactName.toString() + "</a></span > ";
                                return htmlstr;
                            }
                        },
                        {
                            title: "Salesperson",
                            width: "20%",
                            /*dataType: "float",*/
                            //align: "right",
                            dataIndx: "SalesmanName"
                        },
                        {
                            title: "Received Date <br/> Promised",
                            width: "10%",
                            dataType: "Date",
                            //align: "right",
                            dataIndx: "ReceivedDate",
                            render: function (ui) {
                                var d = ui.rowData.DLUP;
                                var dt = new Date(parseInt(d.replace("/Date(", "").replace(")/", "")))
                                var RDate = dt.getDate() + "/" + (dt.getMonth() + 1) + "/" + dt.getFullYear();
                                var htmlstr = "<span>" + RDate + "</span ><br/>";
                                var d1 = ui.rowData.DLUP;
                                var dt1 = new Date(parseInt(d1.replace("/Date(", "").replace(")/", "")))
                                var Pdate = dt1.getDate() + "/" + (dt1.getMonth() + 1) + "/" + dt1.getFullYear();
                                htmlstr = htmlstr + "<span>" + Pdate + "</span ><br/>";
                                return htmlstr;
                            }
                        },
                        {
                            title: "Status",
                            width: "10%",
                            /*dataType: "float",*/
                            //align: "right",
                            dataIndx: "BOMManagerStatus"
                        },
                        {
                            title: "Total Value",
                            width: "10%",
                            /*dataType: "float",*/
                            //align: "right",
                            dataIndx: "TotalValueStr",
                            render: function (ui) {
                                var htmlstr = "<span>" + ui.rowData.TotalValueStr + "</span ><br/>";
                                //htmlstr = htmlstr + "<span>" + ui.rowData.TotalInBaseStr + "</span ><br/>";
                                return htmlstr;
                            }
                        }
                    ];

                    //main object to be passed to pqGrid constructor.
                    var BOMID = $('#txtBOMId').val();
                    var clientid = $("#SelectedItem").val();
                    var companynoval = $("#HiddenCompName").val();
                    //var BomDateVal = $('#FromDate').val();

                    var todayFromDate = new Date($('#FromDate').val());
                    var ddFromDate = todayFromDate.getDate();
                    var mmFromDate = (todayFromDate.getMonth() + 1);
                    var yyyyFromDate = todayFromDate.getFullYear();
                    var BomDateValFromDate = ddFromDate + '/' + mmFromDate + '/' + yyyyFromDate;;
                    if (BomDateValFromDate == 'NaN/NaN/NaN') { BomDateValFromDate = ''; }
                    var bomdata;
                    //if (BOMID == undefined || BOMID == "")
                    //    BOMID = 0;
                    //if (clientid == undefined || clientid == "")
                    //    clientid = 0;
                    if (BomDateValFromDate == undefined || BomDateValFromDate == "")
                        BomDateValFromDate = "";
                    var todayToDate = new Date($('#ToDate').val());
                    var ddToDate = todayToDate.getDate();
                    var mmToDate = (todayToDate.getMonth() + 1);
                    var yyyyToDate = todayToDate.getFullYear();
                    var BomDateValToDate = ddToDate + '/' + mmToDate + '/' + yyyyToDate;;
                    if (BomDateValToDate == 'NaN/NaN/NaN') { BomDateValToDate = ''; }
                    var bomdata;
                    //BOMID = 1;
                    //clientid = 118,
                    //    BomDateVal = '2012-01-01';
                    //if (BOMID == undefined || BOMID == "")
                    //    BOMID = 0;
                    if (clientid == undefined || clientid == "")
                        clientid = 0;
                    if (BomDateValToDate == undefined || BomDateValToDate == "")
                        BomDateValToDate = "";
                    //var dataModel = GetBOMSearchData1();
                    var dataModel = {
                        location: "remote",
                        dataType: "JSON",
                        method: "GET",
                        //url: 'SearchByLogin?BOMId=' + BOMID + '&clientId=' + clientid + '&FromDate=' + BomDateValFromDate + '&ToDate=' + BomDateValToDate + '&SearchType=' + searchType + '&CompanyNo=' + companynoval ,
                        //url: 'GetBOMSearch',
                        url: 'GetBOMSearch?BOMId=' + BOMID + '&clientId=' + clientid + '&FromDate=' + BomDateValFromDate + '&ToDate=' + BomDateValToDate + '&SearchType=' + searchType + '&CompanyNo=' + companynoval,
                        //data: { BOMId: BOMID, clientId: clientid, BOMDate: BomDateValToDate },
                        data: { BOMId: BOMID, clientId: clientid, BOMDate: BomDateValFromDate, SearchType: companynoval, FromDate: BomDateValFromDate, ToDate: BomDateValToDate, CompanyNo: companynoval },
                        //url: "/pro/invoice.php",//for PHP
                        getData: function (dataJSON) {
                            //debugger;
                            var data = dataJSON.data;
                            return { curPage: dataJSON.curPage, totalRecords: dataJSON.totalRecords, data: data };
                        }
                    };
                    //$gridPrj = $("#grid_md").pqGrid(obj).pqGrid('refreshDataAndView');
                    var grid1 = $("#grid_md2").pqGrid({
                        width: "auto", height: 450,
                        dataModel: dataModel,
                        colModel: colModel,
                        //freezeCols: 2,
                        //pq_curPage: 1,
                        pageModel: { type: "remote", rPP: 20, strRpp: "{0}" },
                        numberCell: { show: false },
                        //sortable: false,
                        //selectionModel: { swipe: false },
                        //wrap: false, hwrap: false,
                        //virtualX: true, virtualY: true
                        //numberCell: { resizable: true, width: 30, title: "#" },
                        //title: "Shipping Orders",
                        //resizable: true
                    }).pqGrid('refreshDataAndView');
                    //var obj = {
                    //    width: "auto", //width of grid
                    //    height: 400, //height of grid
                    //    numberCell: { show: false },
                    //    colModel: colModel,
                    //    dataModel: { data: data },
                    //    pageModel: { type: "remote", rPP: 20, strRpp: "{0}" },
                    //    sortable: false,
                    //    selectionModel: { swipe: false },
                    //    wrap: false, hwrap: false,
                    //    virtualX: true, virtualY: true,
                    //    numberCell: { resizable: true, width: 30, title: "#" },
                    //    title: "Shipping Orders",
                    //    resizable: true
                    //};
                    //$gridPrj = $("#grid_md").pqGrid(obj).pqGrid('refreshDataAndView');
                    //$("#grid_md").pqGrid(obj);
                    //$("grid_md").pqGrid("refreshDataAndView");
                }

                function LoadMyGridData(searchType, callertype) {

                           // alert('method hits')
                                           // var data = GetBOMSearchData();
                                                                                        /*debugger*/;
                    //[
                    //    { "SaleBOMManagerImportId": 1, "StockCode": 251648, "Description": "SAMSUNG", "Part": "CL05B104KO5NNNC", "RFQ": 380000, "UnitPrice": 0.0013, "LineTotal": 494, "Clientid": 118, "ClientName": "Rebound Electronics (Hong Kong) Limited Local Test", "StatusId": 1, "Islock": true, "ProductInactive": null },
                    //    { "SaleBOMManagerImportId": 2, "StockCode": 251649, "Description": "SAMSUNG", "Part": "CL05B104KO5NNNd", "RFQ": 380000, "UnitPrice": 0.0013, "LineTotal": 494, "Clientid": 118, "ClientName": "Rebound Electronics (Hong Kong) Limited Local Test", "StatusId": 1, "Islock": true, "ProductInactive": null },
                    //    { "SaleBOMManagerImportId": 3, "StockCode": 251649, "Description": "SAMSUNG", "Part": "CL05B104KO5NNNd", "RFQ": 380000, "UnitPrice": 0.0013, "LineTotal": 494, "Clientid": 118, "ClientName": "Rebound Electronics (Hong Kong) Limited Local Test", "StatusId": 1, "Islock": true, "ProductInactive": null }
                    //];

                    //array of columns.
                    var colModel = [
                        {
                            title: "BOMId", //title of column.
                            width: "10%", //initial width of column
                            //dataType: "integer", //data type of column
                            dataIndx: "BOMManagerId", //should match one of the keys in row data.
                            render: function (ui) {
                                var htmlstr = "<span><a href='../../ord_BOMManagerDetail.aspx?BOM=" + ui.rowData.BOMManagerId + "' target='_top'>" + ui.rowData.BOMManagerId.toString() + "</a></span ><br/>";
                                return htmlstr;
                            }
                        },
                        {
                            title: "BOM Name", //title of column.
                            width: "15%", //initial width of column
                            //dataType: "integer", //data type of column
                            dataIndx: "BOMManagerName", //should match one of the keys in row data.
                        },
                        {
                            title: "Part No <br/> Manufacturer",
                            width: "10%",
                            //dataType: "string",
                            dataIndx: "Part",
                            hidden: true,
                            render: function (ui) {
                                //return ui.rowData.BOMManagerNo + '-<br/>' + ui.rowData.Part
                                //debugger;
                                return "<span>" + ui.rowData.Part.toString() + "</span ><br/><span><a  href='../../Con_ManufacturerDetail.aspx?mfr=" + ui.rowData.ManufacturerNo + "' target='_top'>" + ui.rowData.ManufacturerCode.toString() + "</a></span>";

                            }
                        },
                        {
                            title: "Quantity",
                            width: "20%",
                            //dataType: "float",
                            //align: "right",
                            dataIndx: "Quantity",
                            hidden: true
                        },
                        {
                            title: "Company <br/> Contact",
                            width: "25%",
                            /*dataType: "float",*/
                            //align: "right",
                            dataIndx: "CompanyName",
                            render: function (ui) {
                                //return ui.rowData.BOMManagerNo + '-<br/>' + ui.rowData.Part
                                //debugger;
                                var htmlstr = "<span><a href='../../Con_CompanyDetail.aspx?cm=" + ui.rowData.CompanyNo + "' target='_top'>" + ui.rowData.CompanyName.toString() + "</a></span ><br/>";
                                htmlstr = htmlstr + " <span><a href='../../Con_ContactDetail.aspx?con=" + ui.rowData.ContactNo + "' target='_top'>" + ui.rowData.ContactName.toString() + "</a></span > ";
                                return htmlstr;
                            }
                        },
                        {
                            title: "Salesperson",
                            width: "20%",
                            /*dataType: "float",*/
                            //align: "right",
                            dataIndx: "SalesmanName"
                        },
                        {
                            title: "Received Date <br/> Promised",
                            width: "10%",
                            dataType: "Date",
                            //align: "right",
                            dataIndx: "ReceivedDate",
                            render: function (ui) {
                                var d = ui.rowData.DLUP;
                                var dt = new Date(parseInt(d.replace("/Date(", "").replace(")/", "")))
                                var RDate = dt.getDate() + "/" + (dt.getMonth() + 1) + "/" + dt.getFullYear();
                                var htmlstr = "<span>" + RDate + "</span ><br/>";
                                var d1 = ui.rowData.DLUP;
                                var dt1 = new Date(parseInt(d1.replace("/Date(", "").replace(")/", "")))
                                var Pdate = dt1.getDate() + "/" + (dt1.getMonth() + 1) + "/" + dt1.getFullYear();
                                htmlstr = htmlstr + "<span>" + Pdate + "</span ><br/>";
                                return htmlstr;
                            }
                        },
                        {
                            title: "Status",
                            width: "10%",
                            /*dataType: "float",*/
                            //align: "right",
                            dataIndx: "BOMManagerStatus"
                        },
                        {
                            title: "Total Value",
                            width: "10%",
                            /*dataType: "float",*/
                            //align: "right",
                            dataIndx: "TotalValueStr",
                            render: function (ui) {
                                var htmlstr = "<span>" + ui.rowData.TotalValueStr + "</span ><br/>";
                                //htmlstr = htmlstr + "<span>" + ui.rowData.TotalInBaseStr + "</span ><br/>";
                                return htmlstr;
                            }
                        }
                    ];

                    //main object to be passed to pqGrid constructor.
                    var BOMID = $('#txtBOMId').val();
                    var clientid = $("#SelectedItem").val();
                    var companynoval = $("#HiddenCompName").val();
                    //var BomDateVal = $('#FromDate').val();

                    var todayFromDate = new Date($('#FromDate').val());
                    var ddFromDate = todayFromDate.getDate();
                    var mmFromDate = (todayFromDate.getMonth() + 1);
                    var yyyyFromDate = todayFromDate.getFullYear();
                    var BomDateValFromDate = ddFromDate + '/' + mmFromDate + '/' + yyyyFromDate;;
                    if (BomDateValFromDate == 'NaN/NaN/NaN') { BomDateValFromDate = ''; }
                    var bomdata;

                    //if (BOMID == undefined || BOMID == "")
                    //    BOMID = 0;
                    //if (clientid == undefined || clientid == "")
                    //    clientid = 0;
                    if (BomDateValFromDate == undefined || BomDateValFromDate == "")
                        BomDateValFromDate = "";


                    var todayToDate = new Date($('#ToDate').val());
                    var ddToDate = todayToDate.getDate();
                    var mmToDate = (todayToDate.getMonth() + 1);
                    var yyyyToDate = todayToDate.getFullYear();
                    var BomDateValToDate = ddToDate + '/' + mmToDate + '/' + yyyyToDate;;
                    if (BomDateValToDate == 'NaN/NaN/NaN') { BomDateValToDate = ''; }
                    var bomdata;
                    //BOMID = 1;
                    //clientid = 118,
                    //    BomDateVal = '2012-01-01';

                    //if (BOMID == undefined || BOMID == "")
                    //    BOMID = 0;
                    // console.log('abcdef');
                    if (clientid == undefined || clientid == "")
                        clientid = 0;
                    if (BomDateValToDate == undefined || BomDateValToDate == "")
                        BomDateValToDate = "";
                    //var dataModel = GetBOMSearchData1();
                    var pageval = $('.pq-page-current').val();
                    var serviceUrl = '';
                    if (callertype == 1) {
                        serviceUrl = 'GetBOMSearch?pq_curPage=1&BOMId=' + BOMID + '&clientId=' + clientid + '&FromDate=' + BomDateValFromDate + '&ToDate=' + BomDateValToDate + '&SearchType=' + searchType + '&CompanyNo=' + companynoval;
                    }
                    else
                        serviceUrl = 'GetBOMSearch?&BOMId=' + BOMID + '&clientId=' + clientid + '&FromDate=' + BomDateValFromDate + '&ToDate=' + BomDateValToDate + '&SearchType=' + searchType + '&CompanyNo=' + companynoval;

                    //if (pageval == undefined) {
                    //    pageval = 1
                    //}


                    //alert(pageval);
                    var dataModel = {
                        location: "remote",
                        dataType: "JSON",
                        method: "GET",
                        url: serviceUrl,
                        //url: 'GetBOMSearch?pq_curPage=' + pageval+'&BOMId=' + BOMID + '&clientId=' + clientid + '&FromDate=' + BomDateValFromDate + '&ToDate=' + BomDateValToDate + '&SearchType=' + searchType + '&CompanyNo=' + companynoval ,
                        //url: 'GetBOMSearch',

                        //data: { BOMId: BOMID, clientId: clientid, BOMDate: BomDateValToDate },
                        data: { BOMId: BOMID, clientId: clientid, BOMDate: BomDateValFromDate, SearchType: companynoval, FromDate: BomDateValFromDate, ToDate: BomDateValToDate, CompanyNo: companynoval },
                        //url: "/pro/invoice.php",//for PHP
                        getData: function (dataJSON) {
                            //debugger;
                            var data = dataJSON.data;
                            return { curPage: dataJSON.curPage, totalRecords: dataJSON.totalRecords, data: data };
                        }
                    };
                    //$gridPrj = $("#grid_md").pqGrid(obj).pqGrid('refreshDataAndView');
                    var grid1 = $("#gridMy").pqGrid({
                        width: "auto", height: 450,
                        dataModel: dataModel,
                        colModel: colModel,
                        //freezeCols: 2,
                        pageModel: { type: "remote", rPP: 10, rPPOptions: [5, 10, 25, 50], strRpp: "{0}" },
                        numberCell: { show: false },
                        //create: function (ui) {
                        //    var $pager = this.pager().widget();
                        //    if ($pager && $pager.length) {
                        //        $pager = $pager.detach();
                        //        this.widget().find(".pq-grid-top").append($pager);
                        //    }
                        //},
                        //sortable: false,
                        //selectionModel: { swipe: false },
                        //wrap: false, hwrap: false,
                        //virtualX: true, virtualY: true
                        //numberCell: { resizable: true, width: 30, title: "#" },
                        //title: "Shipping Orders",
                        //resizable: true
                    }).pqGrid('refreshDataAndView');
                    //var obj = {
                    //    width: "auto", //width of grid
                    //    height: 400, //height of grid
                    //    numberCell: { show: false },
                    //    colModel: colModel,
                    //    dataModel: { data: data },
                    //    pageModel: { type: "remote", rPP: 20, strRpp: "{0}" },
                    //    sortable: false,
                    //    selectionModel: { swipe: false },
                    //    wrap: false, hwrap: false,
                    //    virtualX: true, virtualY: true,
                    //    numberCell: { resizable: true, width: 30, title: "#" },
                    //    title: "Shipping Orders",
                    //    resizable: true
                    //};
                    //$gridPrj = $("#grid_md").pqGrid(obj).pqGrid('refreshDataAndView');
                    //$("#grid_md").pqGrid(obj);
                    //$("grid_md").pqGrid("refreshDataAndView");
                }
                function LoadTeamGridData(searchType, callertype) {
                                           // var data = GetBOMSearchData();
                                                                                        /*debugger*/;
                    //[
                    //    { "SaleBOMManagerImportId": 1, "StockCode": 251648, "Description": "SAMSUNG", "Part": "CL05B104KO5NNNC", "RFQ": 380000, "UnitPrice": 0.0013, "LineTotal": 494, "Clientid": 118, "ClientName": "Rebound Electronics (Hong Kong) Limited Local Test", "StatusId": 1, "Islock": true, "ProductInactive": null },
                    //    { "SaleBOMManagerImportId": 2, "StockCode": 251649, "Description": "SAMSUNG", "Part": "CL05B104KO5NNNd", "RFQ": 380000, "UnitPrice": 0.0013, "LineTotal": 494, "Clientid": 118, "ClientName": "Rebound Electronics (Hong Kong) Limited Local Test", "StatusId": 1, "Islock": true, "ProductInactive": null },
                    //    { "SaleBOMManagerImportId": 3, "StockCode": 251649, "Description": "SAMSUNG", "Part": "CL05B104KO5NNNd", "RFQ": 380000, "UnitPrice": 0.0013, "LineTotal": 494, "Clientid": 118, "ClientName": "Rebound Electronics (Hong Kong) Limited Local Test", "StatusId": 1, "Islock": true, "ProductInactive": null }
                    //];

                    //array of columns.
                    var colModel = [
                        {
                            title: "BOMId", //title of column.
                            width: "10%", //initial width of column
                            //dataType: "integer", //data type of column
                            dataIndx: "BOMManagerId", //should match one of the keys in row data.
                            render: function (ui) {
                                var htmlstr = "<span><a href='../../ord_BOMManagerDetail.aspx?BOM=" + ui.rowData.BOMManagerId + "' target='_top'>" + ui.rowData.BOMManagerId.toString() + "</a></span ><br/>";
                                return htmlstr;
                            }
                        },
                        {
                            title: "BOM Name", //title of column.
                            width: "15%", //initial width of column
                            //dataType: "integer", //data type of column
                            dataIndx: "BOMManagerName", //should match one of the keys in row data.
                        },
                        {
                            title: "Part No <br/> Manufacturer",
                            width: "10%",
                            //dataType: "string",
                            dataIndx: "Part",
                            hidden: true,
                            render: function (ui) {
                                //return ui.rowData.BOMManagerNo + '-<br/>' + ui.rowData.Part
                                //debugger;
                                return "<span>" + ui.rowData.Part.toString() + "</span ><br/><span><a  href='../../Con_ManufacturerDetail.aspx?mfr=" + ui.rowData.ManufacturerNo + "' target='_top'>" + ui.rowData.ManufacturerCode.toString() + "</a></span>";

                            }
                        },
                        {
                            title: "Quantity",
                            width: "20%",
                            //dataType: "float",
                            //align: "right",
                            dataIndx: "Quantity",
                            hidden: true
                        },
                        {
                            title: "Company <br/> Contact",
                            width: "25%",
                            /*dataType: "float",*/
                            //align: "right",
                            dataIndx: "CompanyName",
                            render: function (ui) {
                                //return ui.rowData.BOMManagerNo + '-<br/>' + ui.rowData.Part
                                //debugger;
                                var htmlstr = "<span><a href='../../Con_CompanyDetail.aspx?cm=" + ui.rowData.CompanyNo + "' target='_top'>" + ui.rowData.CompanyName.toString() + "</a></span ><br/>";
                                htmlstr = htmlstr + " <span><a href='../../Con_ContactDetail.aspx?con=" + ui.rowData.ContactNo + "' target='_top'>" + ui.rowData.ContactName.toString() + "</a></span > ";
                                return htmlstr;
                            }
                        },
                        {
                            title: "Salesperson",
                            width: "20%",
                            /*dataType: "float",*/
                            //align: "right",
                            dataIndx: "SalesmanName"
                        },
                        {
                            title: "Received Date <br/> Promised",
                            width: "10%",
                            dataType: "Date",
                            //align: "right",
                            dataIndx: "ReceivedDate",
                            render: function (ui) {
                                var d = ui.rowData.DLUP;
                                var dt = new Date(parseInt(d.replace("/Date(", "").replace(")/", "")))
                                var RDate = dt.getDate() + "/" + (dt.getMonth() + 1) + "/" + dt.getFullYear();
                                var htmlstr = "<span>" + RDate + "</span ><br/>";
                                var d1 = ui.rowData.DLUP;
                                var dt1 = new Date(parseInt(d1.replace("/Date(", "").replace(")/", "")))
                                var Pdate = dt1.getDate() + "/" + (dt1.getMonth() + 1) + "/" + dt1.getFullYear();
                                htmlstr = htmlstr + "<span>" + Pdate + "</span ><br/>";
                                return htmlstr;
                            }
                        },
                        {
                            title: "Status",
                            width: "10%",
                            /*dataType: "float",*/
                            //align: "right",
                            dataIndx: "BOMManagerStatus"
                        },
                        {
                            title: "Total Value",
                            width: "10%",
                            /*dataType: "float",*/
                            //align: "right",
                            dataIndx: "TotalValueStr",
                            render: function (ui) {
                                var htmlstr = "<span>" + ui.rowData.TotalValueStr + "</span ><br/>";
                                //htmlstr = htmlstr + "<span>" + ui.rowData.TotalInBaseStr + "</span ><br/>";
                                return htmlstr;
                            }
                        }
                    ];

                    //main object to be passed to pqGrid constructor.
                    var BOMID = $('#txtBOMId').val();
                    var clientid = $("#SelectedItem").val();
                    var companynoval = $("#HiddenCompName").val();
                    //var BomDateVal = $('#FromDate').val();

                    var todayFromDate = new Date($('#FromDate').val());
                    var ddFromDate = todayFromDate.getDate();
                    var mmFromDate = (todayFromDate.getMonth() + 1);
                    var yyyyFromDate = todayFromDate.getFullYear();
                    var BomDateValFromDate = ddFromDate + '/' + mmFromDate + '/' + yyyyFromDate;;
                    if (BomDateValFromDate == 'NaN/NaN/NaN') { BomDateValFromDate = ''; }
                    var bomdata;

                    //if (BOMID == undefined || BOMID == "")
                    //    BOMID = 0;
                    //if (clientid == undefined || clientid == "")
                    //    clientid = 0;
                    if (BomDateValFromDate == undefined || BomDateValFromDate == "")
                        BomDateValFromDate = "";


                    var todayToDate = new Date($('#ToDate').val());
                    var ddToDate = todayToDate.getDate();
                    var mmToDate = (todayToDate.getMonth() + 1);
                    var yyyyToDate = todayToDate.getFullYear();
                    var BomDateValToDate = ddToDate + '/' + mmToDate + '/' + yyyyToDate;;
                    if (BomDateValToDate == 'NaN/NaN/NaN') { BomDateValToDate = ''; }
                    var bomdata;
                    //BOMID = 1;
                    //clientid = 118,
                    //    BomDateVal = '2012-01-01';

                    //if (BOMID == undefined || BOMID == "")
                    //    BOMID = 0;
                    if (clientid == undefined || clientid == "")
                        clientid = 0;
                    if (BomDateValToDate == undefined || BomDateValToDate == "")
                        BomDateValToDate = "";

                    var pageval = $('.pq-page-current').val();
                    var serviceUrl = '';
                    if (callertype == 1) {
                        serviceUrl = 'GetBOMSearch?pq_curPage=1&BOMId=' + BOMID + '&clientId=' + clientid + '&FromDate=' + BomDateValFromDate + '&ToDate=' + BomDateValToDate + '&SearchType=' + searchType + '&CompanyNo=' + companynoval;
                    }
                    else
                        serviceUrl = 'GetBOMSearch?&BOMId=' + BOMID + '&clientId=' + clientid + '&FromDate=' + BomDateValFromDate + '&ToDate=' + BomDateValToDate + '&SearchType=' + searchType + '&CompanyNo=' + companynoval;


                    //var dataModel = GetBOMSearchData1();
                    var dataModel = {
                        location: "remote",
                        dataType: "JSON",
                        method: "GET",
                        //url: 'SearchByLogin?BOMId=' + BOMID + '&clientId=' + clientid + '&FromDate=' + BomDateValFromDate + '&ToDate=' + BomDateValToDate + '&SearchType=' + searchType + '&CompanyNo=' + companynoval ,
                        //url: 'GetBOMSearch',
                        url: serviceUrl,
                        //url: 'GetBOMSearch?BOMId=' + BOMID + '&clientId=' + clientid + '&FromDate=' + BomDateValFromDate + '&ToDate=' + BomDateValToDate + '&SearchType=' + searchType + '&CompanyNo=' + companynoval,
                        //data: { BOMId: BOMID, clientId: clientid, BOMDate: BomDateValToDate },
                        data: { BOMId: BOMID, clientId: clientid, BOMDate: BomDateValFromDate, SearchType: companynoval, FromDate: BomDateValFromDate, ToDate: BomDateValToDate, CompanyNo: companynoval },
                        //url: "/pro/invoice.php",//for PHP
                        getData: function (dataJSON) {
                            //debugger;
                            var data = dataJSON.data;
                            return { curPage: dataJSON.curPage, totalRecords: dataJSON.totalRecords, data: data };
                        }
                    };
                    //$gridPrj = $("#grid_md").pqGrid(obj).pqGrid('refreshDataAndView');
                    var grid1 = $("#gridTeam").pqGrid({
                        width: "auto", height: 450,
                        dataModel: dataModel,
                        colModel: colModel,
                        //freezeCols: 2,
                        pageModel: { type: "remote", rPP: 10, rPPOptions: [5, 10, 25, 50], strRpp: "{0}" },
                        numberCell: { show: false },
                        //create: function (ui) {
                        //    var $pager = this.pager().widget();
                        //    if ($pager && $pager.length) {
                        //        $pager = $pager.detach();
                        //        this.widget().find(".pq-grid-top").append($pager);
                        //    }
                        //},
                        //sortable: false,
                        //selectionModel: { swipe: false },
                        //wrap: false, hwrap: false,
                        //virtualX: true, virtualY: true
                        //numberCell: { resizable: true, width: 30, title: "#" },
                        //title: "Shipping Orders",
                        //resizable: true
                    }).pqGrid('refreshDataAndView');
                    //var obj = {
                    //    width: "auto", //width of grid
                    //    height: 400, //height of grid
                    //    numberCell: { show: false },
                    //    colModel: colModel,
                    //    dataModel: { data: data },
                    //    pageModel: { type: "remote", rPP: 20, strRpp: "{0}" },
                    //    sortable: false,
                    //    selectionModel: { swipe: false },
                    //    wrap: false, hwrap: false,
                    //    virtualX: true, virtualY: true,
                    //    numberCell: { resizable: true, width: 30, title: "#" },
                    //    title: "Shipping Orders",
                    //    resizable: true
                    //};
                    //$gridPrj = $("#grid_md").pqGrid(obj).pqGrid('refreshDataAndView');
                    //$("#grid_md").pqGrid(obj);
                    //$("grid_md").pqGrid("refreshDataAndView");
                }
                function LoadDivisionGridData(searchType, callertype) {
                                           // var data = GetBOMSearchData();
                                                                                        /*debugger*/;
                    //[
                    //    { "SaleBOMManagerImportId": 1, "StockCode": 251648, "Description": "SAMSUNG", "Part": "CL05B104KO5NNNC", "RFQ": 380000, "UnitPrice": 0.0013, "LineTotal": 494, "Clientid": 118, "ClientName": "Rebound Electronics (Hong Kong) Limited Local Test", "StatusId": 1, "Islock": true, "ProductInactive": null },
                    //    { "SaleBOMManagerImportId": 2, "StockCode": 251649, "Description": "SAMSUNG", "Part": "CL05B104KO5NNNd", "RFQ": 380000, "UnitPrice": 0.0013, "LineTotal": 494, "Clientid": 118, "ClientName": "Rebound Electronics (Hong Kong) Limited Local Test", "StatusId": 1, "Islock": true, "ProductInactive": null },
                    //    { "SaleBOMManagerImportId": 3, "StockCode": 251649, "Description": "SAMSUNG", "Part": "CL05B104KO5NNNd", "RFQ": 380000, "UnitPrice": 0.0013, "LineTotal": 494, "Clientid": 118, "ClientName": "Rebound Electronics (Hong Kong) Limited Local Test", "StatusId": 1, "Islock": true, "ProductInactive": null }
                    //];

                    //array of columns.
                    var colModel = [
                        {
                            title: "BOMId", //title of column.
                            width: "10%", //initial width of column
                            //dataType: "integer", //data type of column
                            dataIndx: "BOMManagerId", //should match one of the keys in row data.
                            render: function (ui) {
                                var htmlstr = "<span><a href='../../ord_BOMManagerDetail.aspx?BOM=" + ui.rowData.BOMManagerId + "' target='_top'>" + ui.rowData.BOMManagerId.toString() + "</a></span ><br/>";
                                return htmlstr;
                            }
                        },
                        {
                            title: "BOM Name", //title of column.
                            width: "15%", //initial width of column
                            //dataType: "integer", //data type of column
                            dataIndx: "BOMManagerName", //should match one of the keys in row data.
                        },
                        {
                            title: "Part No <br/> Manufacturer",
                            width: "10%",
                            //dataType: "string",
                            dataIndx: "Part",
                            hidden: true,
                            render: function (ui) {
                                //return ui.rowData.BOMManagerNo + '-<br/>' + ui.rowData.Part
                                //debugger;
                                return "<span>" + ui.rowData.Part.toString() + "</span ><br/><span><a  href='../../Con_ManufacturerDetail.aspx?mfr=" + ui.rowData.ManufacturerNo + "' target='_top'>" + ui.rowData.ManufacturerCode.toString() + "</a></span>";

                            }
                        },
                        {
                            title: "Quantity",
                            width: "20%",
                            //dataType: "float",
                            //align: "right",
                            dataIndx: "Quantity",
                            hidden: true
                        },
                        {
                            title: "Company <br/> Contact",
                            width: "25%",
                            /*dataType: "float",*/
                            //align: "right",
                            dataIndx: "CompanyName",
                            render: function (ui) {
                                //return ui.rowData.BOMManagerNo + '-<br/>' + ui.rowData.Part
                                //debugger;
                                var htmlstr = "<span><a href='../../Con_CompanyDetail.aspx?cm=" + ui.rowData.CompanyNo + "' target='_top'>" + ui.rowData.CompanyName.toString() + "</a></span ><br/>";
                                htmlstr = htmlstr + " <span><a href='../../Con_ContactDetail.aspx?con=" + ui.rowData.ContactNo + "' target='_top'>" + ui.rowData.ContactName.toString() + "</a></span > ";
                                return htmlstr;
                            }
                        },
                        {
                            title: "Salesperson",
                            width: "20%",
                            /*dataType: "float",*/
                            //align: "right",
                            dataIndx: "SalesmanName"
                        },
                        {
                            title: "Received Date <br/> Promised",
                            width: "10%",
                            dataType: "Date",
                            //align: "right",
                            dataIndx: "ReceivedDate",
                            render: function (ui) {
                                var d = ui.rowData.DLUP;
                                var dt = new Date(parseInt(d.replace("/Date(", "").replace(")/", "")))
                                var RDate = dt.getDate() + "/" + (dt.getMonth() + 1) + "/" + dt.getFullYear();
                                var htmlstr = "<span>" + RDate + "</span ><br/>";
                                var d1 = ui.rowData.DLUP;
                                var dt1 = new Date(parseInt(d1.replace("/Date(", "").replace(")/", "")))
                                var Pdate = dt1.getDate() + "/" + (dt1.getMonth() + 1) + "/" + dt1.getFullYear();
                                htmlstr = htmlstr + "<span>" + Pdate + "</span ><br/>";
                                return htmlstr;
                            }
                        },
                        {
                            title: "Status",
                            width: "10%",
                            /*dataType: "float",*/
                            //align: "right",
                            dataIndx: "BOMManagerStatus"
                        },
                        {
                            title: "Total Value",
                            width: "10%",
                            /*dataType: "float",*/
                            //align: "right",
                            dataIndx: "TotalValueStr",
                            render: function (ui) {
                                var htmlstr = "<span>" + ui.rowData.TotalValueStr + "</span ><br/>";
                                //htmlstr = htmlstr + "<span>" + ui.rowData.TotalInBaseStr + "</span ><br/>";
                                return htmlstr;
                            }
                        }
                    ];

                    //main object to be passed to pqGrid constructor.
                    var BOMID = $('#txtBOMId').val();
                    var clientid = $("#SelectedItem").val();
                    var companynoval = $("#HiddenCompName").val();
                    //var BomDateVal = $('#FromDate').val();

                    var todayFromDate = new Date($('#FromDate').val());
                    var ddFromDate = todayFromDate.getDate();
                    var mmFromDate = (todayFromDate.getMonth() + 1);
                    var yyyyFromDate = todayFromDate.getFullYear();
                    var BomDateValFromDate = ddFromDate + '/' + mmFromDate + '/' + yyyyFromDate;;
                    if (BomDateValFromDate == 'NaN/NaN/NaN') { BomDateValFromDate = ''; }
                    var bomdata;

                    //if (BOMID == undefined || BOMID == "")
                    //    BOMID = 0;
                    //if (clientid == undefined || clientid == "")
                    //    clientid = 0;
                    if (BomDateValFromDate == undefined || BomDateValFromDate == "")
                        BomDateValFromDate = "";


                    var todayToDate = new Date($('#ToDate').val());
                    var ddToDate = todayToDate.getDate();
                    var mmToDate = (todayToDate.getMonth() + 1);
                    var yyyyToDate = todayToDate.getFullYear();
                    var BomDateValToDate = ddToDate + '/' + mmToDate + '/' + yyyyToDate;;
                    if (BomDateValToDate == 'NaN/NaN/NaN') { BomDateValToDate = ''; }
                    var bomdata;
                    //BOMID = 1;
                    //clientid = 118,
                    //    BomDateVal = '2012-01-01';

                    //if (BOMID == undefined || BOMID == "")
                    //    BOMID = 0;
                    if (clientid == undefined || clientid == "")
                        clientid = 0;
                    if (BomDateValToDate == undefined || BomDateValToDate == "")
                        BomDateValToDate = "";

                    var pageval = $('.pq-page-current').val();
                    var serviceUrl = '';
                    if (callertype == 1) {
                        serviceUrl = 'GetBOMSearch?pq_curPage=1&BOMId=' + BOMID + '&clientId=' + clientid + '&FromDate=' + BomDateValFromDate + '&ToDate=' + BomDateValToDate + '&SearchType=' + searchType + '&CompanyNo=' + companynoval;
                    }
                    else
                        serviceUrl = 'GetBOMSearch?&BOMId=' + BOMID + '&clientId=' + clientid + '&FromDate=' + BomDateValFromDate + '&ToDate=' + BomDateValToDate + '&SearchType=' + searchType + '&CompanyNo=' + companynoval;

                    //var dataModel = GetBOMSearchData1();
                    var dataModel = {
                        location: "remote",
                        dataType: "JSON",
                        method: "GET",
                        //url: 'SearchByLogin?BOMId=' + BOMID + '&clientId=' + clientid + '&FromDate=' + BomDateValFromDate + '&ToDate=' + BomDateValToDate + '&SearchType=' + searchType + '&CompanyNo=' + companynoval ,
                        //url: 'GetBOMSearch',
                        url: serviceUrl,
                        //url: 'GetBOMSearch?BOMId=' + BOMID + '&clientId=' + clientid + '&FromDate=' + BomDateValFromDate + '&ToDate=' + BomDateValToDate + '&SearchType=' + searchType + '&CompanyNo=' + companynoval,
                        //data: { BOMId: BOMID, clientId: clientid, BOMDate: BomDateValToDate },
                        data: { BOMId: BOMID, clientId: clientid, BOMDate: BomDateValFromDate, SearchType: companynoval, FromDate: BomDateValFromDate, ToDate: BomDateValToDate, CompanyNo: companynoval },
                        //url: "/pro/invoice.php",//for PHP
                        getData: function (dataJSON) {
                            //debugger;
                            var data = dataJSON.data;
                            return { curPage: dataJSON.curPage, totalRecords: dataJSON.totalRecords, data: data };
                        }
                    };
                    //$gridPrj = $("#grid_md").pqGrid(obj).pqGrid('refreshDataAndView');
                    var grid1 = $("#gridDivision").pqGrid({
                        width: "auto", height: 450,
                        dataModel: dataModel,
                        colModel: colModel,
                        //freezeCols: 2,
                        pageModel: { type: "remote", rPP: 10, rPPOptions: [5, 10, 25, 50], strRpp: "{0}" },
                        numberCell: { show: false },
                        //create: function (ui) {
                        //    var $pager = this.pager().widget();
                        //    if ($pager && $pager.length) {
                        //        $pager = $pager.detach();
                        //        this.widget().find(".pq-grid-top").append($pager);
                        //    }
                        //},
                        //sortable: false,
                        //selectionModel: { swipe: false },
                        //wrap: false, hwrap: false,
                        //virtualX: true, virtualY: true
                        //numberCell: { resizable: true, width: 30, title: "#" },
                        //title: "Shipping Orders",
                        //resizable: true
                    }).pqGrid('refreshDataAndView');
                    //var obj = {
                    //    width: "auto", //width of grid
                    //    height: 400, //height of grid
                    //    numberCell: { show: false },
                    //    colModel: colModel,
                    //    dataModel: { data: data },
                    //    pageModel: { type: "remote", rPP: 20, strRpp: "{0}" },
                    //    sortable: false,
                    //    selectionModel: { swipe: false },
                    //    wrap: false, hwrap: false,
                    //    virtualX: true, virtualY: true,
                    //    numberCell: { resizable: true, width: 30, title: "#" },
                    //    title: "Shipping Orders",
                    //    resizable: true
                    //};
                    //$gridPrj = $("#grid_md").pqGrid(obj).pqGrid('refreshDataAndView');
                    //$("#grid_md").pqGrid(obj);
                    //$("grid_md").pqGrid("refreshDataAndView");
                }
                function LoadCompanyGridData(searchType, callertype) {
                                           // var data = GetBOMSearchData();
                                                                                        /*debugger*/;
                    //[
                    //    { "SaleBOMManagerImportId": 1, "StockCode": 251648, "Description": "SAMSUNG", "Part": "CL05B104KO5NNNC", "RFQ": 380000, "UnitPrice": 0.0013, "LineTotal": 494, "Clientid": 118, "ClientName": "Rebound Electronics (Hong Kong) Limited Local Test", "StatusId": 1, "Islock": true, "ProductInactive": null },
                    //    { "SaleBOMManagerImportId": 2, "StockCode": 251649, "Description": "SAMSUNG", "Part": "CL05B104KO5NNNd", "RFQ": 380000, "UnitPrice": 0.0013, "LineTotal": 494, "Clientid": 118, "ClientName": "Rebound Electronics (Hong Kong) Limited Local Test", "StatusId": 1, "Islock": true, "ProductInactive": null },
                    //    { "SaleBOMManagerImportId": 3, "StockCode": 251649, "Description": "SAMSUNG", "Part": "CL05B104KO5NNNd", "RFQ": 380000, "UnitPrice": 0.0013, "LineTotal": 494, "Clientid": 118, "ClientName": "Rebound Electronics (Hong Kong) Limited Local Test", "StatusId": 1, "Islock": true, "ProductInactive": null }
                    //];

                    //array of columns.
                    var colModel = [
                        {
                            title: "BOMId", //title of column.
                            width: "10%", //initial width of column
                            //dataType: "integer", //data type of column
                            dataIndx: "BOMManagerId", //should match one of the keys in row data.
                            render: function (ui) {
                                var htmlstr = "<span><a href='../../ord_BOMManagerDetail.aspx?BOM=" + ui.rowData.BOMManagerId + "' target='_top'>" + ui.rowData.BOMManagerId.toString() + "</a></span ><br/>";
                                return htmlstr;
                            }
                        },
                        {
                            title: "BOM Name", //title of column.
                            width: "15%", //initial width of column
                            //dataType: "integer", //data type of column
                            dataIndx: "BOMManagerName", //should match one of the keys in row data.
                        },
                        {
                            title: "Part No <br/> Manufacturer",
                            width: "10%",
                            //dataType: "string",
                            dataIndx: "Part",
                            hidden: true,
                            render: function (ui) {
                                //return ui.rowData.BOMManagerNo + '-<br/>' + ui.rowData.Part
                                //debugger;
                                return "<span>" + ui.rowData.Part.toString() + "</span ><br/><span><a  href='../../Con_ManufacturerDetail.aspx?mfr=" + ui.rowData.ManufacturerNo + "' target='_top'>" + ui.rowData.ManufacturerCode.toString() + "</a></span>";

                            }
                        },
                        {
                            title: "Quantity",
                            width: "20%",
                            //dataType: "float",
                            //align: "right",
                            dataIndx: "Quantity",
                            hidden: true
                        },
                        {
                            title: "Company <br/> Contact",
                            width: "25%",
                            /*dataType: "float",*/
                            //align: "right",
                            dataIndx: "CompanyName",
                            render: function (ui) {
                                //return ui.rowData.BOMManagerNo + '-<br/>' + ui.rowData.Part
                                //debugger;
                                var htmlstr = "<span><a href='../../Con_CompanyDetail.aspx?cm=" + ui.rowData.CompanyNo + "' target='_top'>" + ui.rowData.CompanyName.toString() + "</a></span ><br/>";
                                htmlstr = htmlstr + " <span><a href='../../Con_ContactDetail.aspx?con=" + ui.rowData.ContactNo + "' target='_top'>" + ui.rowData.ContactName.toString() + "</a></span > ";
                                return htmlstr;
                            }
                        },
                        {
                            title: "Salesperson",
                            width: "20%",
                            /*dataType: "float",*/
                            //align: "right",
                            dataIndx: "SalesmanName"
                        },
                        {
                            title: "Received Date <br/> Promised",
                            width: "10%",
                            dataType: "Date",
                            //align: "right",
                            dataIndx: "ReceivedDate",
                            render: function (ui) {
                                var d = ui.rowData.DLUP;
                                var dt = new Date(parseInt(d.replace("/Date(", "").replace(")/", "")))
                                var RDate = dt.getDate() + "/" + (dt.getMonth() + 1) + "/" + dt.getFullYear();
                                var htmlstr = "<span>" + RDate + "</span ><br/>";
                                var d1 = ui.rowData.DLUP;
                                var dt1 = new Date(parseInt(d1.replace("/Date(", "").replace(")/", "")))
                                var Pdate = dt1.getDate() + "/" + (dt1.getMonth() + 1) + "/" + dt1.getFullYear();
                                htmlstr = htmlstr + "<span>" + Pdate + "</span ><br/>";
                                return htmlstr;
                            }
                        },
                        {
                            title: "Status",
                            width: "10%",
                            /*dataType: "float",*/
                            //align: "right",
                            dataIndx: "BOMManagerStatus"
                        },
                        {
                            title: "Total Value",
                            width: "10%",
                            /*dataType: "float",*/
                            //align: "right",
                            dataIndx: "TotalValueStr",
                            render: function (ui) {
                                var htmlstr = "<span>" + ui.rowData.TotalValueStr + "</span ><br/>";
                                //htmlstr = htmlstr + "<span>" + ui.rowData.TotalInBaseStr + "</span ><br/>";
                                return htmlstr;
                            }
                        }
                    ];

                    //main object to be passed to pqGrid constructor.
                    var BOMID = $('#txtBOMId').val();
                    var clientid = $("#SelectedItem").val();
                    var companynoval = $("#HiddenCompName").val();
                    //var BomDateVal = $('#FromDate').val();

                    var todayFromDate = new Date($('#FromDate').val());
                    var ddFromDate = todayFromDate.getDate();
                    var mmFromDate = (todayFromDate.getMonth() + 1);
                    var yyyyFromDate = todayFromDate.getFullYear();
                    var BomDateValFromDate = ddFromDate + '/' + mmFromDate + '/' + yyyyFromDate;;
                    if (BomDateValFromDate == 'NaN/NaN/NaN') { BomDateValFromDate = ''; }
                    var bomdata;

                    //if (BOMID == undefined || BOMID == "")
                    //    BOMID = 0;
                    //if (clientid == undefined || clientid == "")
                    //    clientid = 0;
                    if (BomDateValFromDate == undefined || BomDateValFromDate == "")
                        BomDateValFromDate = "";


                    var todayToDate = new Date($('#ToDate').val());
                    var ddToDate = todayToDate.getDate();
                    var mmToDate = (todayToDate.getMonth() + 1);
                    var yyyyToDate = todayToDate.getFullYear();
                    var BomDateValToDate = ddToDate + '/' + mmToDate + '/' + yyyyToDate;;
                    if (BomDateValToDate == 'NaN/NaN/NaN') { BomDateValToDate = ''; }
                    var bomdata;
                    //BOMID = 1;
                    //clientid = 118,
                    //    BomDateVal = '2012-01-01';

                    //if (BOMID == undefined || BOMID == "")
                    //    BOMID = 0;
                    if (clientid == undefined || clientid == "")
                        clientid = 0;
                    if (BomDateValToDate == undefined || BomDateValToDate == "")
                        BomDateValToDate = "";

                    var pageval = $('.pq-page-current').val();
                    var serviceUrl = '';
                    if (callertype == 1) {
                        serviceUrl = 'GetBOMSearch?pq_curPage=1&BOMId=' + BOMID + '&clientId=' + clientid + '&FromDate=' + BomDateValFromDate + '&ToDate=' + BomDateValToDate + '&SearchType=' + searchType + '&CompanyNo=' + companynoval;
                    }
                    else
                        serviceUrl = 'GetBOMSearch?&BOMId=' + BOMID + '&clientId=' + clientid + '&FromDate=' + BomDateValFromDate + '&ToDate=' + BomDateValToDate + '&SearchType=' + searchType + '&CompanyNo=' + companynoval;


                    //var dataModel = GetBOMSearchData1();
                    var dataModel = {
                        location: "remote",
                        dataType: "JSON",
                        method: "GET",
                        //url: 'SearchByLogin?BOMId=' + BOMID + '&clientId=' + clientid + '&FromDate=' + BomDateValFromDate + '&ToDate=' + BomDateValToDate + '&SearchType=' + searchType + '&CompanyNo=' + companynoval ,
                        //url: 'GetBOMSearch',
                        url: serviceUrl,
                        //url: 'GetBOMSearch?BOMId=' + BOMID + '&clientId=' + clientid + '&FromDate=' + BomDateValFromDate + '&ToDate=' + BomDateValToDate + '&SearchType=' + searchType + '&CompanyNo=' + companynoval,
                        //data: { BOMId: BOMID, clientId: clientid, BOMDate: BomDateValToDate },
                        data: { BOMId: BOMID, clientId: clientid, BOMDate: BomDateValFromDate, SearchType: companynoval, FromDate: BomDateValFromDate, ToDate: BomDateValToDate, CompanyNo: companynoval },
                        //url: "/pro/invoice.php",//for PHP
                        getData: function (dataJSON) {
                            //debugger;
                            var data = dataJSON.data;
                            return { curPage: dataJSON.curPage, totalRecords: dataJSON.totalRecords, data: data };
                        }
                    };
                    //$gridPrj = $("#grid_md").pqGrid(obj).pqGrid('refreshDataAndView');
                    var grid1 = $("#gridCompany").pqGrid({
                        width: "auto", height: 450,
                        dataModel: dataModel,
                        colModel: colModel,
                        //freezeCols: 2,
                        pageModel: { type: "remote", rPP: 10, rPPOptions: [5, 10, 25, 50], strRpp: "{0}" },
                        numberCell: { show: false },
                        //create: function (ui) {
                        //    var $pager = this.pager().widget();
                        //    if ($pager && $pager.length) {
                        //        $pager = $pager.detach();
                        //        this.widget().find(".pq-grid-top").append($pager);
                        //    }
                        //},
                        //sortable: false,
                        //selectionModel: { swipe: false },
                        //wrap: false, hwrap: false,
                        //virtualX: true, virtualY: true
                        //numberCell: { resizable: true, width: 30, title: "#" },
                        //title: "Shipping Orders",
                        //resizable: true
                    }).pqGrid('refreshDataAndView');
                    //var obj = {
                    //    width: "auto", //width of grid
                    //    height: 400, //height of grid
                    //    numberCell: { show: false },
                    //    colModel: colModel,
                    //    dataModel: { data: data },
                    //    pageModel: { type: "remote", rPP: 20, strRpp: "{0}" },
                    //    sortable: false,
                    //    selectionModel: { swipe: false },
                    //    wrap: false, hwrap: false,
                    //    virtualX: true, virtualY: true,
                    //    numberCell: { resizable: true, width: 30, title: "#" },
                    //    title: "Shipping Orders",
                    //    resizable: true
                    //};
                    //$gridPrj = $("#grid_md").pqGrid(obj).pqGrid('refreshDataAndView');
                    //$("#grid_md").pqGrid(obj);
                    //$("grid_md").pqGrid("refreshDataAndView");
                }
                function GetBOMSearchData() {
                    var BOMID = $('#txtBOMId').val();
                    var clientid = $("#SelectedItem").val();
                    var companynoval = $("#HiddenCompName").val();
                    //var BomDateVal = $('#FromDate').val();
                    var todayFromDate = new Date($('#FromDate').val());
                    var ddFromDate = todayFromDate.getDate();
                    var mmFromDate = (todayFromDate.getMonth() + 1);
                    var yyyyFromDate = todayFromDate.getFullYear();
                    var BomDateValFromDate = ddFromDate + '/' + mmFromDate + '/' + yyyyFromDate;;
                    if (BomDateValFromDate == 'NaN/NaN/NaN') { BomDateValFromDate = ''; }
                    var bomdata;
                    //BOMID = 1;
                    //clientid = 118,
                    //    BomDateVal = '2012-01-01';
                    var data2;
                    //if (BOMID == undefined || BOMID == "")
                    //    BOMID = 0;
                    if (clientid == undefined || clientid == "")
                        clientid = 0;
                    if (BomDateValFromDate == undefined || BomDateValFromDate == "")
                        BomDateValFromDate = "";
                    var todayToDate = new Date($('#ToDate').val());
                    var ddToDate = todayToDate.getDate();
                    var mmToDate = (todayToDate.getMonth() + 1);
                    var yyyyToDate = todayToDate.getFullYear();
                    var BomDateValToDate = ddToDate + '/' + mmToDate + '/' + yyyyToDate;;
                    if (BomDateValToDate == 'NaN/NaN/NaN') { BomDateValToDate = ''; }
                    var bomdata;
                    //BOMID = 1;
                    //clientid = 118,
                    //    BomDateVal = '2012-01-01';
                    if (BOMID == undefined || BOMID == "")
                        BOMID = 0;
                    if (clientid == undefined || clientid == "")
                        clientid = 0;
                    if (BomDateValToDate == undefined || BomDateValToDate == "")
                        BomDateValToDate = "";
                    $.ajax({
                        type: 'GET',
                        url: 'SearchByLogin',
                        data: { BOMId: BOMID, clientId: clientid, FromDate: BomDateValFromDate, ToDate: BomDateValToDate, CompanyNo: companynoval },
                        contentType: 'application/json', // this
                        datatype: 'json',
                        async: false,
                        //data: { clients: JSON.stringify(clients) }, // and this
                        success: function (dataJSON) {
                                            //bomdata = data;
                                            /*debugger*/;
                            console.log('aa');
                            console.log(dataJSON);
                            console.log(dataJSON.curPage);
                            console.log(dataJSON.totalRecords);
                            //alert("Getting Data");
                            //return { data: data };
                            var dd = JSON.parse(dataJSON);
                            var data = { curPage: dd.curPage, totalRecords: dd.totalRecords, data: dd.data };
                            console.log('breaking');
                            console.log(data);
                            data2 = data;
                        }
                    });
                    return data2;
                    //return bomdata;
                    //return dataModel;
                }
                $("#txtCompName").autocomplete({
                    source: function (request, response) {
                        $.ajax({
                            url: "GetCompanyNames",
                            type: "POST",
                            dataType: "json",
                            data: { searchString: request.term + '%' },
                            success: function (data) {
                                //            console.log(data);
                                response($.map(data, function (item) {
                                    //debugger;
                                    return { label: item.CompanyNo, value: item.CompanyName, recieverid: item.CompanyId };
                                    //return { label: item.EmployeeName, value: item.LoginId };
                                }))
                            }
                        })
                    },
                    //messages: {
                    //    noResults: "", results: ""
                    //}
                    select: function (e, i) {
                        //$("#txtCompName").val('');
                        //console.log(i.item.value);
                        //debugger;
                        var hiddenReceiver = $("#HiddenCompName").val();
                        //console.log(hiddenReceiver);
                        //if (hiddenReceiver == '' || hiddenReceiver == undefined) {
                        //    $("#HiddenMailReceiver").val(i.item.recieverid);
                        //    $("#divMailReceiver").append(i.item.recieverid);
                        //}
                        //else {
                        //var div = $("<div id='mydiv'" + i.item.recieverid+" />");
                        //div.html(GenerateTextbox(""));
                        //$("#divMailReceiver").append(div);
                        var total_element = $(".element").length;
                        // last <div> with element class id
                        //var lastid = $(".element:last").attr("id");
                        //var split_id = lastid.split("_");
                        //var nextindex = Number(split_id[1]) + 1;
                        var max = 5;
                        // Check total number elements
                        //if (total_element < max) {
                        // Adding new div container after last occurance of element class
                        //$(".element:last").after("<div class='element' id='div_" + i.item.CompanyNo + "'></div>");
                        // Adding element to <div>
                        //$("#div_" + i.item.recieverid).append("<label class='dynamiclabelid' id='" + i.item.CompanyNo + "' >" + i.item.CompanyNo + "</label>&nbsp;<span id='remove_" + i.item.CompanyNo + "' class='remove'>X</span>");
                        //}
                        //if (hiddenReceiver == '' || hiddenReceiver == undefined) {
                        console.log(i.item.recieverid);
                        $("#HiddenCompName").val(i.item.recieverid);
                        //}
                        //else {
                        //    $("#HiddenMailReceiver").val(hiddenReceiver + ',' + i.item.recieverid);
                        //}
                        //var divval = $("#divMailReceiver").html();
                        //$("#divMailReceiver").html('');
                        //$("#divMailReceiver").append(divval +'<br/>'+ i.item.recieverid);
                        //$("#HiddenMailReceiver").val(hiddenReceiver + ',' + i.item.recieverid);
                        //}
                        //$("#txtBuyerCC").val('');
                    }, minLength: 2
                });
                $('#checkboxBOMId').change(function () {
                    if (this.checked) {
                        $('#txtBOMId').css('opacity', '1.5');
                    }
                    else {
                        $('#txtBOMId').css('opacity', '1');
                    }
                });
                $('#txtBOMId').change(function () {
                    if ($('#txtBOMId').val().trim() != '') {
                        $('#txtBOMId').css('opacity', '1.5');
                        $('#checkboxBOMId').prop('checked', true);
                    }

                    else {
                        $('#txtBOMId').css('opacity', '0.5');
                        $('#checkboxBOMId').prop('checked', false);
                    }
                });
                    $('#txtCompName').change(function () {
                        if ($('#txtCompName').val().trim() != '') {
                            $('#txtCompName').css('opacity', '1.5');
                            $('#checkboxCompName').prop('checked', true);
                        }
                        else {
                            $('#txtCompName').css('opacity', '0.5');
                            $('#checkboxCompName').prop('checked', false);
                            $('#HiddenCompName').val('');
                        }
                    });
                $('#FromDate').change(function () {
                    if ($('#FromDate').val().trim() != '') {
                        $('#FromDate').css('opacity', '1.5');
                        $('#checkboxFromDate').prop('checked', true);
                    }
                    else {
                        $('#FromDate').css('opacity', '0.5');
                        $('#checkboxFromDate').prop('checked', false);
                        
                    }
                });
                $('#ToDate').change(function () {
                    if ($('#ToDate').val().trim() != '') {
                        $('#ToDate').css('opacity', '1.5');
                        $('#checkboxToDate').prop('checked', true);
                    }
                    else {
                        $('#ToDate').css('opacity', '0.5');
                        $('#checkboxToDate').prop('checked', false);

                    }
                });

                //$('#FromDate').change(function () {
                //    if (this.checked) {
                //        $('#FromDate').css('opacity', '1.5');
                //    }
                //    else {
                //        $('#FromDate').css('opacity', '0');
                //    }
                //});
                
                $('#checkboxFromDate').change(function () {
                    if (this.checked) {
                        $('#FromDate').css('opacity', '1.5');
                    }
                    else {
                        $('#FromDate').css('opacity', '0.5');
                    }
                });
                $('#checkboxToDate').change(function () {
                    if (this.checked) {
                        $('#ToDate').css('opacity', '1.5');
                    }
                    else {
                        $('#ToDate').css('opacity', '0.5');
                    }
                });

                //$('#txtCompName').change(function () {
                //    if ($('#txtCompName').val().trim() == '')
                //        $('#HiddenCompName').val('');
                //    //HiddenCompName
                //});

            </script>
            <script>
                function myFunction() {
                    /* $("#icon_id").attr("src", "http://localhost:49861/App_Themes/Original/images/Nuggets/list/show.gif");*/
                    /*jQuery('#icon_id img').attr('src', "http://localhost:49861/App_Themes/Original/images/Nuggets/list/show.gif");*/
                    var x = document.getElementById("myDIV");

                    if (x.style.display === "none") {
                        x.style.display = "block";
                        $(".head_in").removeClass("head_effect");
                        $("#icon_id").attr("src", "http://localhost:49861/App_Themes/Original/images/Nuggets/list/hide.gif");


                    } else {
                        x.style.display = "none";
                        $(".head_in").addClass("head_effect");

                        $("#icon_id").attr("src", "http://localhost:49861/App_Themes/Original/images/Nuggets/list/show.gif");

                    }
                    var x = document.getElementById("Export_exl");
                    if (x.style.display === "none") {
                        x.style.display = "block";

                    } else {
                        x.style.display = "none";
                    }



                };



            </script>

</body>
</html>
