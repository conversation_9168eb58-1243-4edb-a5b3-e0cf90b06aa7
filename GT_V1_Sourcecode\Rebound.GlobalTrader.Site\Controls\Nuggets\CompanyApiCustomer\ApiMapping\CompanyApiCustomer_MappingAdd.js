Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.CompanyApiCustomer_MappingAdd=function(n){Rebound.GlobalTrader.Site.Controls.Forms.CompanyApiCustomer_MappingAdd.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.Forms.CompanyApiCustomer_MappingAdd.prototype={get_ibtnSend:function(){return this._ibtnSend},set_ibtnSend:function(n){this._ibtnSend!==n&&(this._ibtnSend=n)},get_ibtnSend_Footer:function(){return this._ibtnSend_Footer},set_ibtnSend_Footer:function(n){this._ibtnSend_Footer!==n&&(this._ibtnSend_Footer=n)},get_intMasterLoginNo:function(){return this._intMasterLoginNo},set_intMasterLoginNo:function(n){this._intMasterLoginNo!==n&&(this._intMasterLoginNo=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.CompanyApiCustomer_MappingAdd.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown))},getFormControlID:function(n,t){return String.format("{0}_{1}",n,t)},formShown:function(){this._blnFirstTimeShown&&($("#ctl00_cphMain_ctlNotify_ctlDB_ctl14_ctlNotify_ctlDB_ddlRequirementforTraceability_ctl02").hide(),$("#ctl00_cphMain_ctlNotify_ctlDB_ctl14_ctlNotify_ctlDB_ddlType_ctl02").hide(),$find(this.getFormControlID(this._element.id,"ddlRequirementforTraceability")).getData(),$find(this.getFormControlID(this._element.id,"ddlType")).getData(),$get(this.getFormControlID(this._element.id,"txtDateRequired")).value=$R_FN.shortDate(),this.getContact())},getContact:function(){getCompanyAndOtherMasterData.call(this)},dispose:function(){this.isDisposed||(this._ibtnSend&&$R_IBTN.clearHandlers(this._ibtnSend),this._ibtnSend_Footer&&$R_IBTN.clearHandlers(this._ibtnSend_Footer),this._ctlMail&&this._ctlMail.dispose(),this._ctlMail=null,this._ibtnSend=null,this._ibtnSend_Footer=null,this._intMasterLoginNo=null,Rebound.GlobalTrader.Site.Controls.Forms.CompanyApiCustomer_MappingAdd.callBaseMethod(this,"dispose"))},getMessageText:function(){Rebound.GlobalTrader.Site.WebServices.GetMailMessage_NotifyNPR(this._intNPRID,Function.createDelegate(this,this.getMessageTextComplete))},getMessageTextComplete:function(n){this._ctlMail.setValue_Body(n);this._ctlMail.setValue_Subject(String.format($R_RES.NotifyNPR,this._strNPRNo));this._ctlMail.addNewLoginRecipient(this._intBuyerId,this._strBuyerName)},sendMail:function(){this.validateForm()&&(this.showLoading(!0),this.enableButton(!1),Rebound.GlobalTrader.Site.WebServices.NotifyNPRMessage($R_FN.arrayToSingleString(this._ctlMail._aryRecipientLoginIDs),$R_FN.arrayToSingleString(this._ctlMail._aryRecipientGroupIDs),this._ctlMail.getValue_Subject(),this._ctlMail.getValue_Body(),"",this._intNPRID,this._intGoodsInLineId,$R_FN.arrayToSingleString(this._ctlMail._aryRecipientLoginNames,"/"),$R_FN.arrayToSingleString(this._ctlMail._aryRecipientGroupNames,"/"),Function.createDelegate(this,this.sendMailComplete)))},validateForm:function(){var n=this._ctlMail.validateFields();return n||this.showError(!0),n},sendMailComplete:function(){this.showLoading(!1);this.showSavedOK(!0);location.href=$RGT_gotoURL_GoodsIn(this._intGoodsIn);this.enableButton(!0)},cancelClicked:function(){window.location.href=window.location.href},enableButton:function(n){$R_IBTN.enableButton(this._ibtnSend,n);$R_IBTN.enableButton(this._ibtnSend_Footer,n)},impotExcelData:function(n,t,i,r,u,f,e){var o;$("#divLoader").show();i=$("#hdnClient").val();var h=n,c=h.includes(".json"),s=!1;e=="json"&&(s=!0);i>0&&c==s?(DefaultCurrency=$("#ddlMainCurrency option:selected").text(),$("#txtBomName").val()==""&&$("#txtBomName").val(n),o=new Rebound.GlobalTrader.Site.Data,o._intTimeoutMilliseconds=2e5,o.set_PathToData("controls/Nuggets/CompanyApiCustomer/"),o.set_DataObject("CompanyApiCustomer"),o.set_DataAction("ImportData"),o.addParameter("originalFilename",n),o.addParameter("generatedFilename",t),o.addParameter("ClientId",i),o.addParameter("ColumnHeader",r),o.addParameter("FormatId",u),o.addParameter("Delimiter",f),o.addParameter("DefaultCurrency",DefaultCurrency),o.addParameter("strfiletype",e),o.addDataOK(Function.createDelegate(this,this.importExcelDataOK)),o.addError(Function.createDelegate(this,this.importExcelDataError)),o.addTimeout(Function.createDelegate(this,this.importExcelDataError)),$R_DQ.addToQueue(o),$R_DQ.processQueue()):(alert("Please Check the file type."),$("#divLoader").hide());o=null},importExcelDataOK:function(n){flogId=n._result.FileLogId;$("#divLoader").hide();$("#btnDisplayCsvData").prop("disabled",!1).css("opacity",5.5);$("#excelipload").prop("disabled",!0).css("opacity",.5);$('input:checkbox[id="chkFileCCH"]').prop("disabled",!0);$("input:file").filter(function(){return this.files.length==0}).prop("disabled",!0);var t=n._result.IsLimitExceeded;t==!0?(alert(n._result.LimitErrorMessage),$("#btnDisplayCsvData").prop("disabled",!0).css("opacity",.5),this.clearPreviousUploadedData()):$("#btnDisplayCsvData").prop("disabled",!1).css("opacity",5.5)},importExcelDataError:function(n){alert(n._errorMessage.split("<br/>")[0]);$("#divLoader").hide()},clearPreviousUploadedData:function(){var t=$("#ddlClient option:selected").val(),n=window.location.pathname.replace("Utility_BOMManagerImport.aspx","");handlerUrl=window.location.origin+(n.length==1?"":n)+"/controls/Nuggets/CompanyApiCustomer/ApiMapping//UtilityBOMManagerImport2.ashx";$.ajax({processData:!1,contentType:"application/json",type:"POST",url:handlerUrl+"?action=DeleteRecord&SelectedclientId="+t,async:!1,success:function(){},error:function(){}})}};Rebound.GlobalTrader.Site.Controls.Forms.CompanyApiCustomer_MappingAdd.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CompanyApiCustomer_MappingAdd",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);