<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="AllPurchaseOrdersDueIn" xml:space="preserve">
    <value>There are no Purchase Orders due in</value>
  </data>
  <data name="ApprovedCustomersOnStop" xml:space="preserve">
    <value>There are currently no approved customers on stop</value>
  </data>
  <data name="Company" xml:space="preserve">
    <value>Sorry, that Company cannot be found or you do not have the permissions to see it.</value>
  </data>
  <data name="Contact" xml:space="preserve">
    <value>Sorry, that Contact cannot be found or you do not have the permissions to see it.</value>
  </data>
  <data name="CreditNote" xml:space="preserve">
    <value>Sorry, that Credit Note cannot be found or you do not have the permissions to see it.</value>
  </data>
  <data name="CRMANotAvailable" xml:space="preserve">
    <value>No invoice lines are available for adding to this Customer RMA. Use the "Edit" facility to change any line information.</value>
  </data>
  <data name="CRMAReceivingLines" xml:space="preserve">
    <value>This CRMA currently has no lines awaiting receipt</value>
  </data>
  <data name="CusReqSourcingResults" xml:space="preserve">
    <value>This Customer Requirement has no Sourcing Results</value>
  </data>
  <data name="CustomerRequirement" xml:space="preserve">
    <value>Sorry, that Customer Requirement cannot be found</value>
  </data>
  <data name="CustomerRMA" xml:space="preserve">
    <value>Sorry, that Customer RMA cannot be found or you do not have the permissions to see it.</value>
  </data>
  <data name="DebitNote" xml:space="preserve">
    <value>Sorry, that Debit Note cannot be found or you do not have the permissions to see it.</value>
  </data>
  <data name="DivisionDocHeaderImage" xml:space="preserve">
    <value>This Division has no document header image</value>
  </data>
  <data name="DivisionMembers" xml:space="preserve">
    <value>This Division has no members</value>
  </data>
  <data name="DocHeaderImage" xml:space="preserve">
    <value>There is currently no Document Header Image</value>
  </data>
  <data name="Document" xml:space="preserve">
    <value>Sorry, that document cannot be found or you do not have the permissions to see it.</value>
  </data>
  <data name="EnterCompanyName" xml:space="preserve">
    <value>Please enter the Company Name</value>
  </data>
  <data name="FormProblems" xml:space="preserve">
    <value>There were some problems with your form&lt;br /&gt;Please check the form and try again.</value>
  </data>
  <data name="Generic" xml:space="preserve">
    <value>Sorry, no data was found</value>
  </data>
  <data name="GoodsIn" xml:space="preserve">
    <value>Sorry, that Goods In Note cannot be found or you do not have the permissions to see it.</value>
  </data>
  <data name="Invoice" xml:space="preserve">
    <value>Sorry, that Invoice cannot be found or you do not have the permissions to see it.</value>
  </data>
  <data name="InvoiceNotAvailable" xml:space="preserve">
    <value>No sales order service lines are available for adding to this Invoice.</value>
  </data>
  <data name="Lot" xml:space="preserve">
    <value>Sorry, that Lot cannot be found or you do not have the permissions to see it.</value>
  </data>
  <data name="MailMessage" xml:space="preserve">
    <value>Sorry, that message does not exist or you do not have permissions to see it</value>
  </data>
  <data name="MailMessageGroupMembers" xml:space="preserve">
    <value>The Mail Group has no members</value>
  </data>
  <data name="Manufacturer" xml:space="preserve">
    <value>Sorry, that Manufacturer cannot be found or you do not have the permissions to see it.</value>
  </data>
  <data name="MyApprovedCustomersOnStop" xml:space="preserve">
    <value>I currently have no approved customers on stop</value>
  </data>
  <data name="MyApprovedCustomersOnStop_ForUser" xml:space="preserve">
    <value>{0} currently has no approved customers on stop</value>
  </data>
  <data name="MyMessages" xml:space="preserve">
    <value>I currently have no Messages</value>
  </data>
  <data name="MyOpenPurchaseOrders" xml:space="preserve">
    <value>I currently have no open Purchase Orders</value>
  </data>
  <data name="MyOpenPurchaseOrders_ForUser" xml:space="preserve">
    <value>{0} currently has no open Purchase Orders</value>
  </data>
  <data name="MyOpenQuotes" xml:space="preserve">
    <value>I currently have no open Quotes</value>
  </data>
  <data name="MyOpenQuotes_ForUser" xml:space="preserve">
    <value>{0} currently has no open Quotes</value>
  </data>
  <data name="MyOpenRequirements" xml:space="preserve">
    <value>I currently have no open Requirements</value>
  </data>
  <data name="MyOpenRequirements_ForUser" xml:space="preserve">
    <value>{0} currently has no open Requirements</value>
  </data>
  <data name="MyOpenSalesOrders" xml:space="preserve">
    <value>I currently have no open Sales Orders</value>
  </data>
  <data name="MyOpenSalesOrders_ForUser" xml:space="preserve">
    <value>{0} currently has no open Sales Orders</value>
  </data>
  <data name="MyRecentActivity" xml:space="preserve">
    <value>I have no recent activity</value>
  </data>
  <data name="MyRecentActivity_ForUser" xml:space="preserve">
    <value>{0} has no recent activity</value>
  </data>
  <data name="OrdersDueOut_ForUser" xml:space="preserve">
    <value>{0} has no Orders Due Out</value>
  </data>
  <data name="MyRecentlyShippedOrders" xml:space="preserve">
    <value>I currently have no Recently Shipped Orders</value>
  </data>
  <data name="MyRecentlyShippedOrders_ForUser" xml:space="preserve">
    <value>{0} currently has no Recently Shipped Orders</value>
  </data>
  <data name="MyScheduledTasks" xml:space="preserve">
    <value>I currently have no Scheduled Tasks</value>
  </data>
  <data name="MyScheduledTasks_ForUser" xml:space="preserve">
    <value>{0}  currently has no Scheduled Tasks</value>
  </data>
  <data name="MyToDoList" xml:space="preserve">
    <value>I currently have no items on my To Do list</value>
  </data>
  <data name="Page" xml:space="preserve">
    <value>Sorry, that page cannot be found or you do not have the permissions to see it.</value>
  </data>
  <data name="PartsBeingOrderedToday" xml:space="preserve">
    <value>There have been no parts ordered today</value>
  </data>
  <data name="PartSearch" xml:space="preserve">
    <value>Sorry, no parts could be found to match your search</value>
  </data>
  <data name="POReceivingLines" xml:space="preserve">
    <value>This Purchase Order has no lines awaiting receipt</value>
  </data>
  <data name="PurchaseOrder" xml:space="preserve">
    <value>Sorry, that Purchase Order cannot be found or you do not have the permissions to see it.</value>
  </data>
  <data name="Quote" xml:space="preserve">
    <value>Sorry, that Quote cannot be found or you do not have the permissions to see it.</value>
  </data>
  <data name="ReceivedOrders" xml:space="preserve">
    <value>There are currently no Received Orders</value>
  </data>
  <data name="Report" xml:space="preserve">
    <value>Sorry, that Report cannot be found or you do not have the permissions to see it.</value>
  </data>
  <data name="ReportData" xml:space="preserve">
    <value>This report has no data for the parameters specified</value>
  </data>
  <data name="ReportParameters" xml:space="preserve">
    <value>This Report has no parameters</value>
  </data>
  <data name="Reports" xml:space="preserve">
    <value>There are currently no installed reports</value>
  </data>
  <data name="SalesOrder" xml:space="preserve">
    <value>Sorry, that Sales Order cannot be found or you do not have the permissions to see it.</value>
  </data>
  <data name="SalesOrdersReadyToShip" xml:space="preserve">
    <value>There are no Sales Orders ready to ship</value>
  </data>
  <data name="SecurityGroupMembers" xml:space="preserve">
    <value>This Security Group has no members</value>
  </data>
  <data name="SecurityGroupPermissionsGeneral" xml:space="preserve">
    <value>There are currently no permissions that can be set</value>
  </data>
  <data name="SecurityGroupPermissionsReports" xml:space="preserve">
    <value>There are currently no reports having permissions that can be set</value>
  </data>
  <data name="SecurityUserGroups" xml:space="preserve">
    <value>This User is not a member of any Security Groups - this will give them full access to the system</value>
  </data>
  <data name="Service" xml:space="preserve">
    <value>Sorry, that Service cannot be found or you do not have the permissions to see it.</value>
  </data>
  <data name="ShipSOMainInfo_ShippedLines" xml:space="preserve">
    <value>This Sales Order has no shipped lines</value>
  </data>
  <data name="SOLineAllocations" xml:space="preserve">
    <value>This Sales Order Line has no allocations</value>
  </data>
  <data name="SOLinesShipped" xml:space="preserve">
    <value>This Sales Order Line has no shipped lines</value>
  </data>
  <data name="SOShippingLines" xml:space="preserve">
    <value>This Sales Order has no lines awaiting shipment</value>
  </data>
  <data name="SourcingLinks" xml:space="preserve">
    <value>No Sourcing Links</value>
  </data>
  <data name="SRMANotAvailable" xml:space="preserve">
    <value>No purchase order lines are available for adding to this Supplier RMA. Use the "Edit" facility to change any line information.</value>
  </data>
  <data name="SRMAShippingLines" xml:space="preserve">
    <value>This SRMA currently has no lines awaiting shipment</value>
  </data>
  <data name="Stock" xml:space="preserve">
    <value>Sorry, that Stock Item cannot be found or you do not have the permissions to see it.</value>
  </data>
  <data name="SupplierRMA" xml:space="preserve">
    <value>Sorry, that Supplier RMA cannot be found or you do not have the permissions to see it.</value>
  </data>
  <data name="TeamMembers" xml:space="preserve">
    <value>This Team has no members</value>
  </data>
  <data name="TodaysShippedOrders" xml:space="preserve">
    <value>There are no Shipped Orders today</value>
  </data>
  <data name="StockImage" xml:space="preserve">
    <value>Sorry, that image was not found</value>
  </data>
  <data name="TopSalespersons" xml:space="preserve">
    <value>The Top Salespeople cannot be established </value>
  </data>
  <data name="StockImages" xml:space="preserve">
    <value>This Stock has no images</value>
  </data>
  <data name="PurchaseRequisition" xml:space="preserve">
    <value>Sorry, that Purchase Requisition cannot be found or you do not have the permissions to see it.</value>
  </data>
  <data name="CommunicationLog" xml:space="preserve">
    <value>No communication log items</value>
  </data>
  <data name="CompanyManufacturers" xml:space="preserve">
    <value>No manufacturers supplied</value>
  </data>
  <data name="ManufacturerCompanies" xml:space="preserve">
    <value>This Manufacturer has no related companies</value>
  </data>
  <data name="ManufacturerSuppliers" xml:space="preserve">
    <value>This Manufacturer has no suppliers</value>
  </data>
  <data name="StockAllocations" xml:space="preserve">
    <value>This Stock has no allocations</value>
  </data>
  <data name="CompanyPDF" xml:space="preserve">
    <value>This Company has no PDF document</value>
  </data>
  <data name="CustomerRMAPDF" xml:space="preserve">
    <value>This CRMA has no PDF document</value>
  </data>
  <data name="GoodsInPDF" xml:space="preserve">
    <value>This GoodsIn has no PDF document</value>
  </data>
  <data name="PurchaseOrderPDF" xml:space="preserve">
    <value>This Purchase Order has no PDF document</value>
  </data>
  <data name="SalesOrderPDF" xml:space="preserve">
    <value>This Sales Order has no PDF document</value>
  </data>
  <data name="StockPDF" xml:space="preserve">
    <value>This Stock has no PDF document</value>
  </data>
  <data name="SupplierRMAPDF" xml:space="preserve">
    <value>This SRMA has no PDF document</value>
  </data>
  <data name="EmailStatus" xml:space="preserve">
    <value>Sorry, that email status cannot be found or you do not have the permissions to see it.</value>
  </data>
  <data name="InvoicePDF" xml:space="preserve">
    <value>This invoice has no pdf document</value>
  </data>
  <data name="SupplierInvoiceDetail" xml:space="preserve">
    <value>Sorry, that Supplier Invoice cannot be found or you do not have the permissions to see it.</value>
  </data>
  <data name="SO_SORPDF" xml:space="preserve">
    <value>This Sales Order has no Customer PO</value>
  </data>
  <data name="NPR" xml:space="preserve">
    <value>Sorry, that NPR cannot be found or you do not have the permissions to see it.</value>
  </data>
  <data name="NPRlog" xml:space="preserve">
    <value>Sorry, that NPR Log cannot be found or you do not have the permissions to see it.</value>
  </data>
  <data name="ManufaturePDF" xml:space="preserve">
    <value>This manufacturer has no pdf document</value>
  </data>
  <data name="EPRlog" xml:space="preserve">
    <value>Sorry, that EPR Log cannot be found or you do not have the permissions to see it.</value>
  </data>
  <data name="ManufatureEXCEL" xml:space="preserve">
    <value>This manufacturer has no Excel document</value>
  </data>
  <data name="PONotAvailable" xml:space="preserve">
    <value>Sorry, no data was found</value>
  </data>
  <data name="SO_SORPDFNew" xml:space="preserve">
    <value>This Sales Order has no SOR</value>
  </data>
  <data name="PartsBeingOrderedToday_ForUser" xml:space="preserve">
    <value>{0} There have been no parts ordered today</value>
  </data>
  <data name="POQuote" xml:space="preserve">
    <value>Sorry, that Purchase Quote cannot be found or you do not have the permissions to see it.</value>
  </data>
  <data name="BOM" xml:space="preserve">
    <value>Sorry, that BOM cannot be found or you do not have the permissions to see it.</value>
  </data>
  <data name="PurchaseQuoteCSV" xml:space="preserve">
    <value>This Price Request has no CSV document</value>
  </data>
  <data name="BOMCSV" xml:space="preserve">
    <value>This HUBRFQ has no CSV documents</value>
  </data>
  <data name="TodayOpenPurchaseOrders" xml:space="preserve">
    <value>Today Open Purchase Orders</value>
  </data>
  <data name="UnProcessSalesOrders" xml:space="preserve">
    <value>Un Process Sales Orders</value>
  </data>
  <data name="ClientInvoiceDetail" xml:space="preserve">
    <value>Sorry, that Client Invoice cannot be found or you do not have the permissions to see it.</value>
  </data>
  <data name="UploadExcelDragDrop" xml:space="preserve">
    <value>Sorry, No data was found</value>
  </data>
  <data name="UncheckedIPO" xml:space="preserve">
    <value>There have been no IPO created today</value>
  </data>
  <data name="OpenSupplierPOApproval" xml:space="preserve">
    <value>Sorry, No data was found</value>
  </data>
  <data name="PrintEmaillog" xml:space="preserve">
    <value>Sorry, No data was found</value>
  </data>
  <data name="QuoteToClientImages" xml:space="preserve">
    <value>Sorry, No data was found</value>
  </data>
  <data name="OrdersDueOut" xml:space="preserve">
    <value>Sorry, No data was found</value>
  </data>
  <data name="SOShowPaymentFiles" xml:space="preserve">
    <value>This Sales Order has no payment file attached.</value>
  </data>
  <data name="NoAttachement" xml:space="preserve">
    <value>No attachment found.</value>
  </data>
  <data name="PODeliveryStatus" xml:space="preserve">
    <value>There are currently no item to show.</value>
  </data>
  <data name="IHSDocumnetPDF" xml:space="preserve">
    <value>This ihs has no pdf document</value>
  </data>
  <data name="EUUDocumnetPDF" xml:space="preserve">
    <value>This Sales Order Line does not have any End User Undertaking document</value>
  </data>
  <data name="SupplierInvoicePDF" xml:space="preserve">
    <value>This Supplier Invoice has no PDF document</value>
  </data>
  <data name="InvoicePODPDF" xml:space="preserve">
    <value>This invoice has no POD pdf document</value>
  </data>
  <data name="GILineDocumnetImage" xml:space="preserve">
    <value>Sorry, No data was found</value>
  </data>
  <data name="SOImages" xml:space="preserve">
    <value>Sorry, No data was found</value>
  </data>
  <data name="SOEXCELDOC" xml:space="preserve">
    <value>This SO has no Excel or  doc file</value>
  </data>
  <data name="PO_PORPDFNew" xml:space="preserve">
    <value>This Purchase Order has no POR</value>
  </data>
  <data name="MyGIQueries" xml:space="preserve">
    <value>Sorry, No data was found</value>
  </data>
  <data name="GT" xml:space="preserve">
    <value>GT</value>
  </data>
  <data name="PowerBIActivity" xml:space="preserve">
    <value>Sorry, No activity was found</value>
  </data>
  <data name="PowerBIActivity_ForUser" xml:space="preserve">
    <value>Sorry, No activity was found</value>
  </data>
  <data name="MyQualityGIQueries" xml:space="preserve">
    <value>No GI Quality Approval pending at your end</value>
  </data>
  <data name="CompanyGlobalSalesNoData" xml:space="preserve">
    <value>No global sales person assigned</value>
  </data>
  <data name="GSA" xml:space="preserve">
    <value>No global sales access users selected</value>
  </data>
  <data name="ClientInvoiceHeaderImage" xml:space="preserve">
    <value>There is currently no Client Invoice Header Image</value>
  </data>
  <data name="CIPDocumnetPDF" xml:space="preserve">
    <value>Certificate has no pdf document</value>
  </data>
</root>