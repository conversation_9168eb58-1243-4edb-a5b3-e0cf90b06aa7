/****** Object:  UserDefinedFunction [dbo].[ufn_get_TaxMatrix_Id_For_ClientInvoice]    Script Date: 4/25/2024 10:13:54 AM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE FUNCTION [dbo].[ufn_get_TaxMatrix_Id_For_ClientInvoice] (  
      @GoodsInNo int  
    )  
RETURNS int  
AS BEGIN  
/*
 * Action: Created		Date: 19-12-2023		By: Ab<PERSON>av <PERSON>		Comment: For RP-2765.
 */   
    DECLARE @TaxMatrixNo INT,@ClientNo INT,@GlobalCountryNo INT  
      
  
    SELECT top 1 @GlobalCountryNo = co.GlobalCountryNo, @ClientNo = gi.ClientNo  
                      FROM tbGoodsIn gi JOIN tbCountry co ON gi.PurchaseCountryNo = co.CountryId  
                     
                      
    WHERE gi.GoodsInId = @GoodsInNo                  
      
    IF((@ClientNo=101) OR (@ClientNo=117) OR (@ClientNo=108) OR (@ClientNo=119))
	BEGIN
	SELECT @TaxMatrixNo = ISNULL(TaxMatrixId,0) FROM tbTaxMatrix    
    WHERE ClientNo = @ClientNo
	END
	ELSE
	BEGIN
    SELECT @TaxMatrixNo = ISNULL(TaxMatrixId,0) FROM tbTaxMatrix    
    WHERE ClientNo = @ClientNo   and GlobalCountryNo =   @GlobalCountryNo       
    END  
      
    RETURN isnull(@TaxMatrixNo,0)  
   END 
GO


