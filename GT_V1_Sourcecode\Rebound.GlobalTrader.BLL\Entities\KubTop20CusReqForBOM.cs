﻿using Rebound.GlobalTrader.DAL;
using Rebound.GlobalTrader.DAL.SQLClient;
using System;
using System.Collections.Generic;

namespace Rebound.GlobalTrader.BLL
{
    public partial class KubTop20CusReqForBOM : BizObject
    {
        #region Properties
        public System.String BOMId { get; set; }
        public System.String BOMName { get; set; }
        public System.String Price { get; set; }
        public System.String Quantity { get; set; }
        public System.String CurrencyCode { get; set; }
        public System.String PriceConverted { get; set; }
        public System.String QuoteId { get; set; }
        public System.String QuoteNumber { get; set; }
        public System.String DLUP { get; set; }
        public System.String Quoted { get; set; }
        #endregion


        #region Methods
        /// <summary>
        /// getKubPO Details
        /// Calls [sp_Kub]
        /// </summary>
        public static List<KubTop20CusReqForBOM> GetListKubTop20CusReqForBOM(System.String PartNo, System.Int32 ClientID)
        {
            List<KubTop20CusReqForBOM> lts = new List<KubTop20CusReqForBOM>();
            List<KubTop20CustomeRequirementForBOM> lstkubPODetails = new List<KubTop20CustomeRequirementForBOM>();
            SQLKubProvider objSQLKubProvider = new SQLKubProvider();
            try
            {
                lstkubPODetails = objSQLKubProvider.ListKubTop20CustomerRequirementForBOM(PartNo, ClientID);
                if (lstkubPODetails == null)
                {
                    return new List<KubTop20CusReqForBOM>();
                }
                else
                {
                    foreach (var objDetails in lstkubPODetails)
                    {
                        KubTop20CusReqForBOM obj = new KubTop20CusReqForBOM
                        {
                            BOMName = objDetails.BOMName,
                            Price = objDetails.Price,
                            Quantity = objDetails.Quantity,
                            Quoted = objDetails.Quoted
                        };
                        lts.Add(obj);
                    }
                }
                return lts;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        #endregion
    }

    public partial class KubTop10QuoteForBOM : BizObject
    {
        #region Properties
        public System.String QuoteId { get; set; }
        public System.String QuoteNumber { get; set; }
        public System.String Quantity { get; set; }
        public System.String Price { get; set; }
        public System.String QuoteLineId { get; set; }
        public System.String SalesOrderId { get; set; }
        public System.String SalesOrderNumber { get; set; }
        public System.String CurrencyCode { get; set; }
        public System.String PriceConverted { get; set; }
        public System.String DLUP { get; set; }
        public System.String ConvertedToSO { get; set; }
        #endregion


        #region Methods
        /// <summary>
        /// getKubPO Details
        /// Calls [sp_Kub]
        /// </summary>
        public static List<KubTop10QuoteForBOM> GetListKubTop10QuoteForBOM(System.String PartNo, System.Int32 ClientID)
        {
            List<KubTop10QuoteForBOM> lts = new List<KubTop10QuoteForBOM>();
            List<SqlKubTop10QuoteForBOM> lstkubPODetails = new List<SqlKubTop10QuoteForBOM>();
            SQLKubProvider objSQLKubProvider = new SQLKubProvider();
            try
            {
                lstkubPODetails = objSQLKubProvider.ListKubTop10QuoteForBOM(PartNo, ClientID);
                if (lstkubPODetails == null)
                {
                    return new List<KubTop10QuoteForBOM>();
                }
                else
                {
                    foreach (var objDetails in lstkubPODetails)
                    {
                        KubTop10QuoteForBOM obj = new KubTop10QuoteForBOM
                        {
                            QuoteNumber = objDetails.QuoteNumber,
                            Price = objDetails.Price,
                            Quantity = objDetails.Quantity,
                            ConvertedToSO = objDetails.ConvertedToSO
                        };
                        lts.Add(obj);
                    }
                }
                return lts;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        #endregion
    }

    public partial class AllowedEnabledForHUB : BizObject
    {
        #region Properties
        public System.Boolean? IsAllowedEnable { get; set; }
        #endregion


        #region Methods
        /// <summary>
        /// getKubPO Details
        /// Calls [sp_Kub]
        /// </summary>
        public static AllowedEnabledForHUB IsAllowedEnabledForHUB(System.String PartNo, System.Int32 ClientID, int bomId, int manufacturerId, string manufacturerName, bool isHubRFQ)
        {
            AllowedEnabledForHUB obj = new AllowedEnabledForHUB();
            SQLAllowedEnabledForHUB sqlObj = new SQLAllowedEnabledForHUB();
            SQLKubProvider objSQLKubProvider = new SQLKubProvider();
            try
            {
                sqlObj = objSQLKubProvider.IsAllowedEnabledForHUB(PartNo, ClientID, bomId, manufacturerId, manufacturerName, isHubRFQ);
                if (sqlObj == null)
                {
                    return new AllowedEnabledForHUB();
                }
                else
                {
                    obj.IsAllowedEnable = sqlObj.IsAllowedEnable;
                }
                return obj;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        #endregion
    }
}
