using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site;

namespace Rebound.GlobalTrader.Site.Controls.HomeNuggets.Data {
	/// <summary>
	/// Summary description for $codebehindclassname$
	/// </summary>
	[WebService(Namespace = "http://tempuri.org/")]
	[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
	public class TableActivity : Rebound.GlobalTrader.Site.Data.Base {

		public override void ProcessRequest(HttpContext context) {
			if (base.init(context)) {
				switch (Action) {
					case "GetData": GetData(); break;
					default: WriteErrorActionNotFound(); break;
				}
			}
		}

		/// <summary>
		/// Gets the main data
		/// </summary>
		private void GetData() {
            try
            {
                List<Activity> lstCRActivity = Activity.ListByClientWithFilter(SessionManager.ClientID, 0, 5, "CustomerReq", null, null, null);
                List<Activity> lstGIActivity = Activity.ListByClientWithFilter(SessionManager.ClientID, 0, 5, "Good", null, null, null);
                List<Activity> lstQUActivity = Activity.ListByClientWithFilter(SessionManager.ClientID, 0, 5, "Quot", null, null, null);
                List<Activity> lstPOActivity = Activity.ListByClientWithFilter(SessionManager.ClientID, 0, 5, "Purc", null, null, null);
                List<Activity> lstSOActivity = Activity.ListByClientWithFilter(SessionManager.ClientID, 0, 5, "Sale", null, null, null);
                List<Activity> lstCRMAActivity = Activity.ListByClientWithFilter(SessionManager.ClientID, 0, 5, "CustomerRMA", null, null, null);
                List<Activity> lstSRMAActivity = Activity.ListByClientWithFilter(SessionManager.ClientID, 0, 5, "Supp", null, null, null);
                List<Activity> lstCreditActivity = Activity.ListByClientWithFilter(SessionManager.ClientID, 0, 5, "Cred", null, null, null);
                List<Activity> lstDebitActivity = Activity.ListByClientWithFilter(SessionManager.ClientID, 0, 5, "Deb", null, null, null);
                List<Activity> lstInvActivity = Activity.ListByClientWithFilter(SessionManager.ClientID, 0, 5, "Invoice", null, null, null);
                if (lstCRActivity == null && lstGIActivity == null && lstQUActivity == null && lstPOActivity == null && lstSOActivity == null && lstCRMAActivity == null && lstSRMAActivity == null && lstCreditActivity == null && lstDebitActivity == null && lstInvActivity == null)
                {
                    WriteErrorDataNotFound();
                }
                else
                {
                    //customer requirements
                    JsonObject jsn = new JsonObject();
                    JsonObject jsnItems = new JsonObject(true);
                    for (int i = 0; i < lstCRActivity.Count; i++)
                    {
                        JsonObject jsnItem = new JsonObject();
                        jsnItem.AddVariable("ID", lstCRActivity[i].RowId);
                        jsnItem.AddVariable("No", lstCRActivity[i].RowNumber);
                        jsnItem.AddVariable("Date", Functions.FormatDate(lstCRActivity[i].RowDate));
                        jsnItem.AddVariable("CM", lstCRActivity[i].CompanyName);
                        jsnItem.AddVariable("CMNo", lstCRActivity[i].CompanyNo);
                        jsnItems.AddVariable(jsnItem);
                        jsnItem.Dispose();
                        jsnItem = null;
                    }
                    jsn.AddVariable("CRActivity", jsnItems);
                    jsnItems.Dispose();
                    jsnItems = null;

                    //goods in
                    jsnItems = new JsonObject(true);
                    for (int i = 0; i < lstGIActivity.Count; i++)
                    {
                        JsonObject jsnItem = new JsonObject();
                        jsnItem.AddVariable("ID", lstGIActivity[i].RowId);
                        jsnItem.AddVariable("No", lstGIActivity[i].RowNumber);
                        jsnItem.AddVariable("Date", Functions.FormatDate(lstGIActivity[i].RowDate));
                        jsnItem.AddVariable("CM", lstGIActivity[i].CompanyName);
                        jsnItem.AddVariable("CMNo", lstGIActivity[i].CompanyNo);
                        jsnItems.AddVariable(jsnItem);
                        jsnItem.Dispose();
                        jsnItem = null;
                    }
                    jsn.AddVariable("GIActivity", jsnItems);
                    jsnItems.Dispose();
                    jsnItems = null;

                    //quotes
                    jsnItems = new JsonObject(true);
                    for (int i = 0; i < lstQUActivity.Count; i++)
                    {
                        JsonObject jsnItem = new JsonObject();
                        jsnItem.AddVariable("ID", lstQUActivity[i].RowId);
                        jsnItem.AddVariable("No", lstQUActivity[i].RowNumber);
                        jsnItem.AddVariable("Date", Functions.FormatDate(lstQUActivity[i].RowDate));
                        jsnItem.AddVariable("CM", lstQUActivity[i].CompanyName);
                        jsnItem.AddVariable("CMNo", lstQUActivity[i].CompanyNo);
                        jsnItems.AddVariable(jsnItem);
                        jsnItem.Dispose();
                        jsnItem = null;
                    }
                    jsn.AddVariable("QUActivity", jsnItems);
                    jsnItems.Dispose();
                    jsnItems = null;

                    //purchase orders
                    jsnItems = new JsonObject(true);
                    for (int i = 0; i < lstPOActivity.Count; i++)
                    {
                        JsonObject jsnItem = new JsonObject();
                        jsnItem.AddVariable("ID", lstPOActivity[i].RowId);
                        jsnItem.AddVariable("No", lstPOActivity[i].RowNumber);
                        jsnItem.AddVariable("Date", Functions.FormatDate(lstPOActivity[i].RowDate));
                        jsnItem.AddVariable("CM", lstPOActivity[i].CompanyName);
                        jsnItem.AddVariable("CMNo", lstPOActivity[i].CompanyNo);
                        jsnItems.AddVariable(jsnItem);
                        jsnItem.Dispose();
                        jsnItem = null;
                    }
                    jsn.AddVariable("POActivity", jsnItems);
                    jsnItems.Dispose();
                    jsnItems = null;

                    //sales orders
                    jsnItems = new JsonObject(true);
                    for (int i = 0; i < lstSOActivity.Count; i++)
                    {
                        JsonObject jsnItem = new JsonObject();
                        jsnItem.AddVariable("ID", lstSOActivity[i].RowId);
                        jsnItem.AddVariable("No", lstSOActivity[i].RowNumber);
                        jsnItem.AddVariable("Date", Functions.FormatDate(lstSOActivity[i].RowDate));
                        jsnItem.AddVariable("CM", lstSOActivity[i].CompanyName);
                        jsnItem.AddVariable("CMNo", lstSOActivity[i].CompanyNo);
                        jsnItems.AddVariable(jsnItem);
                        jsnItem.Dispose();
                        jsnItem = null;
                    }
                    jsn.AddVariable("SOActivity", jsnItems);
                    jsnItems.Dispose();
                    jsnItems = null;

                    //customer rmas
                    jsnItems = new JsonObject(true);
                    for (int i = 0; i < lstCRMAActivity.Count; i++)
                    {
                        JsonObject jsnItem = new JsonObject();
                        jsnItem.AddVariable("ID", lstCRMAActivity[i].RowId);
                        jsnItem.AddVariable("No", lstCRMAActivity[i].RowNumber);
                        jsnItem.AddVariable("Date", Functions.FormatDate(lstCRMAActivity[i].RowDate));
                        jsnItem.AddVariable("CM", lstCRMAActivity[i].CompanyName);
                        jsnItem.AddVariable("CMNo", lstCRMAActivity[i].CompanyNo);
                        jsnItems.AddVariable(jsnItem);
                        jsnItem.Dispose();
                        jsnItem = null;
                    }
                    jsn.AddVariable("CRMAActivity", jsnItems);
                    jsnItems.Dispose();
                    jsnItems = null;

                    //supplier rmas
                    jsnItems = new JsonObject(true);
                    for (int i = 0; i < lstSRMAActivity.Count; i++)
                    {
                        JsonObject jsnItem = new JsonObject();
                        jsnItem.AddVariable("ID", lstSRMAActivity[i].RowId);
                        jsnItem.AddVariable("No", lstSRMAActivity[i].RowNumber);
                        jsnItem.AddVariable("Date", Functions.FormatDate(lstSRMAActivity[i].RowDate));
                        jsnItem.AddVariable("CM", lstSRMAActivity[i].CompanyName);
                        jsnItem.AddVariable("CMNo", lstSRMAActivity[i].CompanyNo);
                        jsnItems.AddVariable(jsnItem);
                        jsnItem.Dispose();
                        jsnItem = null;
                    }
                    jsn.AddVariable("SRMAActivity", jsnItems);
                    jsnItems.Dispose();
                    jsnItems = null;


                    //credits
                    jsnItems = new JsonObject(true);
                    for (int i = 0; i < lstCreditActivity.Count; i++)
                    {
                        JsonObject jsnItem = new JsonObject();
                        jsnItem.AddVariable("ID", lstCreditActivity[i].RowId);
                        jsnItem.AddVariable("No", lstCreditActivity[i].RowNumber);
                        jsnItem.AddVariable("Date", Functions.FormatDate(lstCreditActivity[i].RowDate));
                        jsnItem.AddVariable("CM", lstCreditActivity[i].CompanyName);
                        jsnItem.AddVariable("CMNo", lstCreditActivity[i].CompanyNo);
                        jsnItems.AddVariable(jsnItem);
                        jsnItem.Dispose();
                        jsnItem = null;
                    }
                    jsn.AddVariable("CreditActivity", jsnItems);
                    jsnItems.Dispose();
                    jsnItems = null;


                    //debits
                    jsnItems = new JsonObject(true);
                    for (int i = 0; i < lstDebitActivity.Count; i++)
                    {
                        JsonObject jsnItem = new JsonObject();
                        jsnItem.AddVariable("ID", lstDebitActivity[i].RowId);
                        jsnItem.AddVariable("No", lstDebitActivity[i].RowNumber);
                        jsnItem.AddVariable("Date", Functions.FormatDate(lstDebitActivity[i].RowDate));
                        jsnItem.AddVariable("CM", lstDebitActivity[i].CompanyName);
                        jsnItem.AddVariable("CMNo", lstDebitActivity[i].CompanyNo);
                        jsnItems.AddVariable(jsnItem);
                        jsnItem.Dispose();
                        jsnItem = null;
                    }
                    jsn.AddVariable("DebitActivity", jsnItems);
                    jsnItems.Dispose();
                    jsnItems = null;

                    //invoices
                    jsnItems = new JsonObject(true);
                    for (int i = 0; i < lstInvActivity.Count; i++)
                    {
                        JsonObject jsnItem = new JsonObject();
                        jsnItem.AddVariable("ID", lstInvActivity[i].RowId);
                        jsnItem.AddVariable("No", lstInvActivity[i].RowNumber);
                        jsnItem.AddVariable("Date", Functions.FormatDate(lstInvActivity[i].RowDate));
                        jsnItem.AddVariable("CM", lstInvActivity[i].CompanyName);
                        jsnItem.AddVariable("CMNo", lstInvActivity[i].CompanyNo);
                        jsnItems.AddVariable(jsnItem);
                        jsnItem.Dispose();
                        jsnItem = null;
                    }
                    jsn.AddVariable("InvoiceActivity", jsnItems);
                    jsn.AddVariable("Count", lstCRActivity.Count + lstGIActivity.Count + lstQUActivity.Count + lstPOActivity.Count + lstSOActivity.Count + lstCRMAActivity.Count + lstSRMAActivity.Count + lstCreditActivity.Count + lstDebitActivity.Count + lstInvActivity.Count);
                    jsnItems.Dispose();
                    jsnItems = null;

                    OutputResult(jsn);
                    jsn.Dispose();
                    jsn = null;
                }
                lstCRActivity = null;
                lstGIActivity = null;
                lstQUActivity = null;
                lstPOActivity = null;
                lstSOActivity = null;
                lstCRMAActivity = null;
                lstSRMAActivity = null;
                lstCreditActivity = null;
                lstDebitActivity = null;
                lstInvActivity = null;
            }
            catch (Exception ex)
            {
                Errorlog obj = new Errorlog();
                obj.LogMessage(Convert.ToString("Exception- " + ex.Message + " " + ex.StackTrace));
            }
        }

	}
}
