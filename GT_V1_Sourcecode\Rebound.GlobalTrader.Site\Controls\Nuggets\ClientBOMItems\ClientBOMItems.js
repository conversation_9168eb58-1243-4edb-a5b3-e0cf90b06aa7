Rebound.GlobalTrader.Site.Controls.Nuggets.ClientBOMItems=function(n){Rebound.GlobalTrader.Site.Controls.Nuggets.ClientBOMItems.initializeBase(this,[n]);this._intBOMID=-1;this._intCountStock=0;this._isEnable=!1;this._intCountTab=0;this._intCustomerRequirementID=-1;this._blnRequirementClosed=!1;this._intCompanyID=-1;this._hasSourcingResult=!1;this._blnRequirementReleased=!1;this._allExistInSourcingResult=!1;this._intSelectedLineNo=0;this._BomCode="";this._BomName="";this._BomCompanyName="";this._BomCompanyNo=0;this._SalesManNo=0;this._SalesManName=0;this._lineLength=0;this._isRequestToPurchaseQuote=!1;this._blnAllHasDelDate=!1;this._blnAllItemHasDelDate=!1;this._blnAllHasProduct=!1;this._blnAllItemHasProduct=!1;this._blnCanRelease=!1;this._blnCanRecal=!0;this._isClosed=!1;this._CustReqNo=-1;this._blnCanNoBid=!0;this._isNoBid=!1;this._ReqIds=[];this._ReqNos=[];this._BOMNo=-1;this._RequestToPOHubBy=-1;this._UpdateByPH=-1;this._intContact2No=-1;this._intClientStatus=-1;this._intClientCompanyId=-1;this._IsSelectedRequirementRow=!1};Rebound.GlobalTrader.Site.Controls.Nuggets.ClientBOMItems.prototype={get_intBOMID:function(){return this._intBOMID},set_intBOMID:function(n){this._intBOMID!==n&&(this._intBOMID=n)},get_tblStock:function(){return this._tblStock},set_tblStock:function(n){this._tblStock!==n&&(this._tblStock=n)},get_ctlTabStrip:function(){return this._ctlTabStrip},set_ctlTabStrip:function(n){this._ctlTabStrip!==n&&(this._ctlTabStrip=n)},get_ctlTabStock:function(){return this._ctlTabStock},set_ctlTabStock:function(n){this._ctlTabStock!==n&&(this._ctlTabStock=n)},get_IsEnable:function(){return this._isEnable},set_isEnable:function(n){this._isEnable=n},get_ibtnRelease:function(){return this._ibtnRelease},set_ibtnRelease:function(n){this._ibtnRelease!==n&&(this._ibtnRelease=n)},get_blnPOHub:function(){return this._blnPOHub},set_blnPOHub:function(n){this._blnPOHub!==n&&(this._blnPOHub=n)},get_ibtnAdd:function(){return this._ibtnAdd},set_ibtnAdd:function(n){this._ibtnAdd!==n&&(this._ibtnAdd=n)},get_ibtnAddLineItem:function(){return this._ibtnAddLineItem},set_ibtnAddLineItem:function(n){this._ibtnAddLineItem!==n&&(this._ibtnAddLineItem=n)},get_ibtnDelete:function(){return this._ibtnDelete},set_ibtnDelete:function(n){this._ibtnDelete!==n&&(this._ibtnDelete=n)},get_ibtnUnRelease:function(){return this._ibtnUnRelease},set_ibtnUnRelease:function(n){this._ibtnUnRelease!==n&&(this._ibtnUnRelease=n)},get_pnlLineDetail:function(){return this._pnlLineDetail},set_pnlLineDetail:function(n){this._pnlLineDetail!==n&&(this._pnlLineDetail=n)},get_pnlLoadingLineDetail:function(){return this._pnlLoadingLineDetail},set_pnlLoadingLineDetail:function(n){this._pnlLoadingLineDetail!==n&&(this._pnlLoadingLineDetail=n)},get_pnlLineDetailError:function(){return this._pnlLineDetailError},set_pnlLineDetailError:function(n){this._pnlLineDetailError!==n&&(this._pnlLineDetailError=n)},get_ibtnNoBid:function(){return this._ibtnNoBid},set_ibtnNoBid:function(n){this._ibtnNoBid!==n&&(this._ibtnNoBid=n)},get_ibtnRecallNoBid:function(){return this._ibtnRecallNoBid},set_ibtnRecallNoBid:function(n){this._ibtnRecallNoBid!==n&&(this._ibtnRecallNoBid=n)},get_ibtnNote:function(){return this._ibtnNote},set_ibtnNote:function(n){this._ibtnNote!==n&&(this._ibtnNote=n)},get_ibtnExportPurchaseHUB:function(){return this._ibtnExportPurchaseHUB},set_ibtnExportPurchaseHUB:function(n){this._ibtnExportPurchaseHUB!==n&&(this._ibtnExportPurchaseHUB=n)},addStartGetData:function(n){this.get_events().addHandler("StartGetData",n)},removeStartGetData:function(n){this.get_events().removeHandler("StartGetData",n)},onStartGetData:function(){var n=this.get_events().getHandler("StartGetData");n&&n(this,Sys.EventArgs.Empty)},addGotDataOK:function(n){this.get_events().addHandler("GotDataOK",n)},removeGotDataOK:function(n){this.get_events().removeHandler("GotDataOK",n)},onGotDataOK:function(){var n=this.get_events().getHandler("GotDataOK");n&&n(this,Sys.EventArgs.Empty)},addPartSelected:function(n){this.get_events().addHandler("PartSelected",n)},removePartSelected:function(n){this.get_events().removeHandler("PartSelected",n)},onPartSelected:function(){var n=this.get_events().getHandler("PartSelected");n&&n(this,Sys.EventArgs.Empty)},addCallBeforeRelease:function(n){this.get_events().addHandler("CallBeforeRelease",n)},removeCallBeforeRelease:function(n){this.get_events().removeHandler("CallBeforeRelease",n)},onCallBeforeRelease:function(){var n=this.get_events().getHandler("CallBeforeRelease");n&&n(this,Sys.EventArgs.Empty)},addRefereshAfterRelease:function(n){this.get_events().addHandler("RefereshAfterRelease",n)},removeRefereshAfterRelease:function(n){this.get_events().removeHandler("RefereshAfterRelease",n)},onRefereshAfterRelease:function(){var n=this.get_events().getHandler("RefereshAfterRelease");n&&n(this,Sys.EventArgs.Empty)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Nuggets.ClientBOMItems.callBaseMethod(this,"initialize");this.addRefreshEvent(Function.createDelegate(this,this.getData));this._tblStock.addSelectedIndexChanged(Function.createDelegate(this,this.tbl_SelectedIndexChanged));this._ibtnRelease&&($R_IBTN.addClick(this._ibtnRelease,Function.createDelegate(this,this.showReleaseForm)),this._frmConfirm=$find(this._aryFormIDs[0]),this._frmConfirm._BomCode=this._BomCode,this._frmConfirm._BomName=this._BomName,this._frmConfirm._BomCompanyName=this._BomCompanyName,this._frmConfirm._BomCompanyNo=this._BomCompanyNo,this._frmConfirm._SalesManNo=this._SalesManNo,this._frmConfirm._SalesManName=this._SalesManName,this._frmConfirm._CustReqNo=this._CustReqNo,this._frmConfirm.addCancel(Function.createDelegate(this,this.hideConfirmForm)),this._frmConfirm.addSaveComplete(Function.createDelegate(this,this.saveCeaseComplete)),this._frmConfirm.addNotConfirmed(Function.createDelegate(this,this.hideConfirmForm)));this._ibtnAdd&&($R_IBTN.addClick(this._ibtnAdd,Function.createDelegate(this,this.showAddForm)),this._frmAdd=$find(this._aryFormIDs[0]),this._frmAdd.addCancel(Function.createDelegate(this,this.hideAddForm)),this._frmAdd.addSaveComplete(Function.createDelegate(this,this.saveAddComplete)),this._frmAdd.addNotConfirmed(Function.createDelegate(this,this.hideAddForm)),this._frmAdd._intBOMID=this._intBOMID);this._ibtnAddLineItem&&($R_IBTN.addClick(this._ibtnAddLineItem,Function.createDelegate(this,this.showAddLineItemForm)),this._frmAddLineItem=$find(this._aryFormIDs[1]),this._frmAddLineItem.addCancel(Function.createDelegate(this,this.hideAddLineItemForm)),this._frmAddLineItem._intBOMID=this._intBOMID);this._ibtnExportPurchaseHUB&&($R_IBTN.addClick(this._ibtnExportPurchaseHUB,Function.createDelegate(this,this.showAddToHUBRFQForm)),this._frmAddToHUBRFQ=$find(this._aryFormIDs[2]),this._frmAddToHUBRFQ.addCancel(Function.createDelegate(this,this.hideAddToHUBRFQForm)),this._frmAddToHUBRFQ.addSaveComplete(Function.createDelegate(this,this.saveAddToHUBRFQComplete)),this._frmAddToHUBRFQ.addNotConfirmed(Function.createDelegate(this,this.hideAddToHUBRFQForm)),this._frmAddToHUBRFQ._intClientCompanyId=this._intClientCompanyId);this.getData()},dispose:function(){this.isDisposed||(this._frmTransfer&&this._frmTransfer.dispose(),this._ctlTabStrip&&this._ctlTabStrip.dispose(),this._ctlTabStock&&this._ctlTabStock.dispose(),this._tblStock&&this._tblStock.dispose(),this._frmConfirm&&this._frmConfirm.dispose(),this._intBOMID=null,this._ctlTabStrip=null,this._tblStock=null,this._ibtnExportCSV=null,this._pnlSummary=null,this._ibtnRelease=null,this._intCustomerRequirementID=null,this._blnRequirementClosed=null,this._blnRequirementReleased=null,this._blnPOHub=null,this._ibtnAdd=null,this._ibtnAddLineItem=null,this._intCompanyID=null,this._intSelectedLineNo=null,this._blnAllHasDelDate=null,this._blnAllItemHasDelDate=null,this._blnAllHasProduct=null,this._blnAllItemHasProduct=null,this._blnCanRelease=null,this._isClosed=null,this._ibtnNoBid=null,this._isNoBid=null,this._ibtnRecallNoBid=null,this._intClientStatus=null,this._intClientCompanyId=null,this._ibtnExportPurchaseHUB=null,Rebound.GlobalTrader.Site.Controls.Nuggets.ClientBOMItems.callBaseMethod(this,"dispose"))},getData:function(){this.showLoading(!0);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/ClientBOMItems");n.set_DataObject("ClientBOMItems");n.set_DataAction("GetData");n.addParameter("id",this._intBOMID);n.addDataOK(Function.createDelegate(this,this.getDataOK));n.addError(Function.createDelegate(this,this.getDataError));n.addTimeout(Function.createDelegate(this,this.getDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getDataOK:function(n){var i,f,r,t,o,u,e;if(this.showLoading(!1),i=n._result,this._tblStock.clearTable(),this._intCustomerRequirementID=-1,this._blnRequirementReleased=!1,this._intCompanyID=-1,this.enableButtons(!0),this._ctlTabStock.addCountToTitle(0),i.Items.length==0?$R_IBTN.enableButton(this._ibtnExportPurchaseHUB,!1):$R_IBTN.enableButton(this._ibtnExportPurchaseHUB,!0),i.Items)for(r=0;r<i.Items.length;r++)t=i.Items[r],t.BOMNo!=null?$R_IBTN.enableButton(this._ibtnExportPurchaseHUB,!1):this._IsSelectedRequirementRow==!0?$R_IBTN.enableButton(this._ibtnExportPurchaseHUB,!0):$R_IBTN.enableButton(this._ibtnExportPurchaseHUB,!1),this._lineLength=i.Items.length,this._BomCode=t.BOMCode,this._BomName=t.BOMFullName,this._BomCompanyName=t.Company,this._BomCompanyNo=t.CompanyNo,this._SalesManNo=t.Salesman,this._SalesManName=t.SalesmanName,this._RequestToPOHubBy=t.RequestToPOHubBy,this._UpdateByPH=t.UpdateByPH,o=t.IsRequestToPurchaseQuote&&!this._isClosed,u="cusReqMainPart",t.Alt||(u="cusReqMainPart"),t.Released&&(u="readyToShip"),t.Released==!1&&t.HasSourcingResult==!0&&(u="allocated"),t.Released&&(u="readyToShip"),this._allExistInSourcingResult==!1&&(this._allExistInSourcingResult=t.HasSourcingResult),this._isRequestToPurchaseQuote=t.IsRequestToPurchaseQuote,f=[$R_FN.writeDoubleCellValue($R_FN.writePartNo(t.Part,t.ROHS),$R_FN.setCleanTextValue(t.CustomerPart)),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue($R_FN.setCleanTextValue($RGT_nubButton_CustomerRequirement(t.ID,t.CustReqNo))),t.Quantity),$R_FN.writeDoubleCellValue($RGT_nubButton_Manufacturer(t.MfrNo,t.Mfr),$R_FN.setCleanTextValue(t.DC)),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(t.Product),$R_FN.setCleanTextValue(t.Package)),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(t.Company),$R_FN.setCleanTextValue(t.Date)),$R_FN.writeDoubleCellValue(t.TPriceInBom,t.SalesmanName),$R_FN.setCleanTextValue(t.Instructions),$R_FN.writeDoubleCellValue(t.MSL,t.FactorySealed)],e={Part:$R_FN.setCleanTextValue(t.PartNo),Closed:t.Closed,Released:t.Released,CMNo:t.CMNo,HasSourcingResult:t.HasSourcingResult,SourcingResult:t.SourcingResult,IPOClientNo:t.IPOClientNo,CustomerRequirementNumber:t.CustReqNo,IsNoBid:t.IsNoBid,BOMNo:t.BOMNo},this._tblStock.addRow(f,t.ID,!1,e),bomStatus=t.BOMStatus,chk=null,t=null,f=null,e=null,this._ctlTabStock.addCountToTitle(r+1);this._tblStock.resizeColumns();this.showContent(!0);this.showContentLoading(!1)},getDataError:function(n){this.showError(!0,n.get_ErrorMessage())},enableButtons:function(n){n?(this._ibtnAdd&&$R_IBTN.enableButton(this._ibtnAdd,!(this._intClientStatus===4||this._intClientStatus===3)),this._ibtnAddLineItem&&$R_IBTN.enableButton(this._ibtnAddLineItem,!(this._intClientStatus===4||this._intClientStatus===3))):(this._ibtnAdd&&$R_IBTN.enableButton(this._ibtnAdd,!1),this._ibtnAddLineItem&&$R_IBTN.enableButton(this._ibtnAddLineItem,!1))},saveError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},showExportCSV:function(){this.getData_Start();var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/ClientBOMItems");n.set_DataObject("ClientBOMItems");n.set_DataAction("ExportToCSV");n.addParameter("id",this._intBOMID);n.addDataOK(Function.createDelegate(this,this.exportCSV_OK));n.addError(Function.createDelegate(this,this.exportCSV_Error));n.addTimeout(Function.createDelegate(this,this.exportCSV_Error));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null;this.onSave()},exportCSV_OK:function(n){var i=n._result,t;i.Filename&&(t=new Date,location.href=String.format("{0}?t={1}",i.Filename,t.getTime()),t=null);this.getDataOK_End()},exportCSV_Error:function(n){this.showError(!0,n.get_ErrorMessage())},showAddForm:function(){this.showForm(this._frmAdd,!0)},hideAddForm:function(){this.showForm(this._frmAdd,!1)},showAddLineItemForm:function(){GetBOMInfo.call(this);GetAllColumn.call(this);GetMappedColumn.call(this);deRegisterClick.call(this);$("#btnLockMapping").click(function(){LockMapping();event.preventDefault()});$("#btnProcess").click(function(){Process();event.preventDefault()});$("#btnCancel").click(function(){closeForm()});$("#btnSubmit").click(function(){SaveUpdateRecord()});$("#btnReset").click(function(){resetBomData()});$(".popupCloseButton").click(function(){closeForm()});this.showForm(this._frmAddLineItem,!0);bindGridData.call(this);this.showLeftMenu()},showLeftMenu:function(){$link=$("#ctl00_pnlLeftButton a:first");$link[0].click()},hideLeftMenu:function(){$link=$("#ctl00_pnlLeftButton a:first");$link[0].click()},hideAddLineItemForm:function(){deleteTempMapping.call(this);refreshHeader.call(this);refreshUploadedFilesLog.call(this);this.showForm(this._frmAddLineItem,!1);this.getData();this.hideLeftMenu()},saveAddComplete:function(){this.hideAddForm();this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully);this.getData()},showReleaseForm:function(){this.onCallBeforeRelease();this._blnCanRelease&&(this._frmConfirm._intRequirementLineID=this._intCustomerRequirementID,this._frmConfirm._intBOMID=this._intBOMID,this._frmConfirm._BomCode=this._BomCode,this._frmConfirm._BomName=this._BomName,this._frmConfirm._BomCompanyName=this._BomCompanyName,this._frmConfirm._BomCompanyNo=this._BomCompanyNo,this._frmConfirm._SalesManNo=this._SalesManNo,this._frmConfirm._SalesManName=this._SalesManName,this._frmConfirm._CustReqNo=this._CustReqNo,this.showForm(this._frmConfirm,!0))},hideConfirmForm:function(){this.showForm(this._frmConfirm,!1)},saveCeaseComplete:function(){this.hideConfirmForm();this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully);this.onRefereshAfterRelease()},enableBOMProvision:function(n){this._ibtnStockProvision&&$R_IBTN.enableButton(this._ibtnStockProvision,n&&this._blnProvisionLoaded)},tbl_SelectedIndexChanged:function(){this._IsSelectedRequirementRow=!0;$R_IBTN.enableButton(this._ibtnExportPurchaseHUB,!0);this.enableDisableItemDeleteButton(this._isRequestToPurchaseQuote);this._intCustomerRequirementID=this._tblStock._varSelectedValue;this._intSelectedLineNo=this._tblStock._varSelectedValue;this._blnRequirementClosed=this._tblStock.getSelectedExtraData().Closed;this._blnRequirementReleased=this._tblStock.getSelectedExtraData().Released;this._intCompanyID=this._tblStock.getSelectedExtraData().CMNo;this._hasSourcingResult=this._tblStock.getSelectedExtraData().HasSourcingResult;this._CustReqNo=this._tblStock.getSelectedExtraData().CustomerRequirementNumber;this._isNoBid=this._tblStock.getSelectedExtraData().IsNoBid;this.enableButtons(!0);this.onPartSelected();this.onGotDataOK();this.getLineData()},getSelectedPartNo:function(){return this._tblStock.getSelectedExtraData().Part},getIPOClientNo:function(){return this._tblStock.getSelectedExtraData().IPOClientNo},enableItemReleaseButton:function(n){this._ibtnRelease&&$R_IBTN.enableButton(this._ibtnRelease,this._intCustomerRequirementID>0&&!this._blnRequirementReleased&&this._blnPOHub&&n&&!this._isClosed)},disableItemAddButton:function(){},enableDisableItemDeleteButton:function(n){this._ibtnDelete&&$R_IBTN.enableButton(this._ibtnDelete,this._lineLength>0&&!n&&!this._isClosed)},getLineData:function(){this._blnLineLoaded=!1;var n=new Rebound.GlobalTrader.Site.Data;this.showLoading(!0);$R_FN.showElement(this._pnlLineDetail,!1);$R_FN.showElement(this._pnlLoadingLineDetail,!0);$R_FN.showElement(this._pnlLineDetailError,!1);n.set_PathToData("controls/Nuggets/ClientBOMItems");n.set_DataObject("ClientBOMItems");n.set_DataAction("GetItem");n.addParameter("id",this._intCustomerRequirementID);n.addDataOK(Function.createDelegate(this,this.getLineDataOK));n.addError(Function.createDelegate(this,this.getLineDataError));n.addTimeout(Function.createDelegate(this,this.getLineDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getLineDataOK:function(n){var t=n._result;this.setFieldValue("hidCompanyID",t.CustomerNo);this.setFieldValue("hidCompanyName",$R_FN.setCleanTextValue(t.CustomerName));this.setFieldValue("hidContactID",t.ContactNo);this.setFieldValue("hidContactName",$R_FN.setCleanTextValue(t.Contact));this.setFieldValue("ctlQuantity",t.Quantity);this.setFieldValue("ctlPartNo",t.Part);this.setFieldValue("ctlCustomerPart",$R_FN.setCleanTextValue(t.CustomerPart));this.setFieldValue("ctlManufacturer",$RGT_nubButton_Manufacturer(t.ManufacturerNo,t.Manufacturer));this.setFieldValue("hidManufacturer",$R_FN.setCleanTextValue(t.Manufacturer));this.setFieldValue("hidManufacturerNo",t.ManufacturerNo);this.setFieldValue("ctlDateCode",t.DateCode);this.setFieldValue("ctlProduct",$R_FN.setCleanTextValue(t.Product));this.setFieldValue("hidProductID",t.ProductNo);this.setFieldValue("ctlPackage",$R_FN.setCleanTextValue(t.Package));this.setFieldValue("hidPackageID",t.PackageNo);this.setFieldValue("ctlTargetPrice",t.Price);this.setFieldValue("hidPrice",t.PriceRaw);this.setFieldValue("ctlCurrency",$R_FN.setCleanTextValue(t.Currency));this.setFieldValue("hidCurrencyID",t.CurrencyNo);this.setFieldValue("ctlDateRequired",t.DatePromised);this.setFieldValue("ctlUsage",$R_FN.setCleanTextValue(t.Usage));this.setFieldValue("hidUsageID",$R_FN.setCleanTextValue(t.UsageNo));this.setFieldValue("ctlNotes",$R_FN.setCleanTextValue(t.Notes));this.setFieldValue("ctlInstructions",$R_FN.setCleanTextValue(t.Instructions));this.setFieldValue("ctlROHS",$R_FN.writeROHS(t.ROHS));this.setFieldValue("hidROHS",t.ROHS);this.setFieldValue("ctlClosedReason",t.ClosedReason);this.setFieldValue("hidDisplayStatus",$R_FN.setCleanTextValue(t.DisplayStatus));this.setFieldValue("ctlPartWatch",t.PartWatch);this.setFieldValue("ctlBOM",t.BOM);this.setFieldValue("ctlBOMName",t.BOMName);this.setFieldValue("hidBOMID",t.BOMId);this.setFieldValue("hidBOMHeaderDisplayStatus",t.RequestToPOHubBy==null?!0:!1);this.setFieldValue("ctlMSL",t.MSL);this.setFieldValue("ctlFactorySealed",t.FactorySealed);this.setFieldValue("ctlPQA",t.PQA);this.setFieldValue("ctlObsolete",t.Obsolete);this.setFieldValue("ctlLastTimeBuy",t.LastTimeBuy);this.setFieldValue("ctlRefirbsAcceptable",t.RefirbsAcceptable);this.setFieldValue("ctlTestingRequired",t.TestingRequired);this.setFieldValue("ctlTargetSellPrice",t.TargetSellPrice);this.setFieldValue("ctlCompetitorBestoffer",t.CompetitorBestOffer);this.setFieldValue("ctlCustomerDecisionDate",t.CustomerDecisionDate);this.setFieldValue("ctlRFQClosingDate",t.RFQClosingDate);this.setFieldValue("ctlQuoteValidityRequiredHid",t.QuoteValidityRequired);this.setFieldValue("ctlQuoteValidityRequired",t.QuoteValidityText);this.setFieldValue("ctlTypeHid",t.Type);this.setFieldValue("ctlType",t.ReqTypeText);this.setFieldValue("ctlOrderToPlace",t.OrderToPlace);this.setFieldValue("ctlRequirementforTraceability",t.ReqForTraceabilityText);this.setFieldValue("ctlRequirementforTraceabilityHid",t.RequirementforTraceability);this.setFieldValue("ctlTargetSellPriceHidden",t.hidTargetSellPrice);this.setFieldValue("ctlCompetitorBestofferHidden",t.hidCompetitorBestOffer);this.setFieldValue("ctlEAU",t.EAU);this.setFieldValue("ctlIsNoBid",$R_FN.showLargeFonts(t.IsNoBidStatus));this.showField("ctlIsNoBid",t.IsNoBid);this.setFieldValue("ctlIsNoBidNotes",t.NoBidNotes);this.showField("ctlIsNoBidNotes",t.IsNoBid);this.setFieldValue("ctlClosed",t.Closed==!1?$R_FN.showLargeFontsWithColor("No"):$R_FN.showLargeFonts("Yes"));this.setFieldValue("ctlAlternativesAccepted",t.AlternativesAccepted);this.setFieldValue("ctlRepeatBusiness",t.RepeatBusiness);this.setFieldValue("ctlPrdDutyCodeRate",t.DutyCodeAndRate);this._isNoBid=t.IsNoBid;this._blnCanRecal=t.SourcingResult;this.setFieldValue("hidMSL",t.MSLLevelNo);this.setFieldValue("ctlBOMHeader",$RGT_nubButton_BOM(t.BOMId,$R_FN.setCleanTextValue(t.BOMHeader)));t.BOMId>0&&$R_IBTN.enableButton(this._ibtnExportPurchaseHUB,!1);this.enableButtons(!0);$R_FN.showElement(this._pnlLineDetail,!0);$R_FN.showElement(this._pnlLoadingLineDetail,!1);this.showLoading(!1);this.onGotDataOK()},getLineDataError:function(n){this.showLoading(!1);$R_FN.showElement(this._pnlLoadingLineDetail,!1);$R_FN.showElement(this._pnlLineDetailError,!0);$R_FN.setInnerHTML(this._pnlLineDetailError,n.get_ErrorMessage());Array.clear(this._ReqIds)},writeCheckbox:function(n,t,i){var r=this.getControlID("chk",t,i),u=this.getControlID("chkImg",t,i);return String.format('<div class="imageCheckBoxDisabled" id="{0}" ><img id="{1}" class="{2}" src="images/x.gif" style="border-width: 0px;" /> <\/div>',r,u,"off")},getControlID:function(n,t,i){return String.format("{0}_{1}{2}",i._element.id,n,t)},getCheckBox:function(n,t){return $find(this.getControlID("chk",n,t))},registerCheckBox:function(n,t,i,r,u){var e=this.getControlID("chk",t,u),o=this.getControlID("chkImg",t,u),f=this.getCheckBox(t,u);f&&(f.dispose(),f=null);eval($R_FN.getCreateStatement("Rebound.GlobalTrader.Site.Controls.ImageCheckBox",[["blnChecked",i],["blnEnabled",r],["img",String.format('$get("{0}")',o)]],e))},getCheckedCellValue:function(n,t){var i=this._tblStock,r=this.getCheckBox(n,i),u=r._blnChecked,f=i._tbl.rows[n];f&&(u?($R_IBTN.enableButton(this._ibtnNote,!0),Array.add(this._ReqIds,t)):($R_IBTN.enableButton(this._ibtnNote,!1),Array.remove(this._ReqIds,t)),this._ReqIds.length==0?$R_IBTN.enableButton(this._ibtnNote,!1):$R_IBTN.enableButton(this._ibtnNote,!0))},enableItemUnReleaseButton:function(){},showAddToHUBRFQForm:function(){this._frmAddToHUBRFQ._intClientCompanyId=this._intClientCompanyId;this._frmAddToHUBRFQ._CustReqNo=this._CustReqNo;this.showForm(this._frmAddToHUBRFQ,!0)},hideAddToHUBRFQForm:function(){this.showForm(this._frmAddToHUBRFQ,!1)},saveAddToHUBRFQComplete:function(){this.hideAddToHUBRFQForm();this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully);this.getData()}};Rebound.GlobalTrader.Site.Controls.Nuggets.ClientBOMItems.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.ClientBOMItems",Rebound.GlobalTrader.Site.Controls.Nuggets.Base);