
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.ClientImportBOMMainInfo_Edit = function(element) { 
    Rebound.GlobalTrader.Site.Controls.Forms.ClientImportBOMMainInfo_Edit.initializeBase(this, [element]);
	this._intBOMID = -1;
	this._blnRequestedToPoHub = false;
	this._intGlobalCurrencyNo = -1; this._intPOCurrencyNo = -1;//by umendra
};

Rebound.GlobalTrader.Site.Controls.Forms.ClientImportBOMMainInfo_Edit.prototype = {

    get_intBOMID: function() { return this._intBOMID; }, set_intBOMID: function(value) { if (this._intBOMID !== value) this._intBOMID = value; },
    get_BomCode: function() { return this._BomCode; }, set_BomCode: function(value) { if (this._BomCode !== value) this._BomCode = value; },
    get_BomName: function() { return this._BomName; }, set_BomName: function(value) { if (this._BomName !== value) this._BomName = value; },
    get_BomCompanyName: function() { return this._BomCompanyName; }, set_BomCompanyName: function(value) { if (this._BomCompanyName !== value) this._BomCompanyName = value; },
    get_BomCompanyNo: function () { return this._BomCompanyNo; }, set_BomCompanyNo: function (value) { if (this._BomCompanyNo !== value) this._BomCompanyNo = value; },
   
    initialize: function() {
        Rebound.GlobalTrader.Site.Controls.Forms.ClientImportBOMMainInfo_Edit.callBaseMethod(this, "initialize");
        this.addShown(Function.createDelegate(this, this.formShown));
    },

    formShown: function () {
        this._intGlobalCurrencyNo = -1;
        if (this._blnFirstTimeShown) {
            this.addSave(Function.createDelegate(this, this.saveClicked));
        }
        //this.getFieldDropDownData("ctlCompany");
        //this.getFieldDropDownData("ctlContact");
        
        if (this._blnRequestedToPoHub) {
            this.showField("ctlCode", false);
            this.showField("ctlName", false);
            this.showField("ctlCompany", false);
            this.showField("ctlContact", false);
            this.showField("ctlInActive", false);
            this.showField("ctlNotes", false);
            this.showField("ctlCurrency", false);
            this.showField("ctlSalesman", false);
        }
        this.getCurrency();
    },
    getCurrency: function () {
        this.getBOMData();

    },
    getBOMData: function () {
        var obj = new Rebound.GlobalTrader.Site.Data();
        this._strPath = "controls/Nuggets/CompanySalesInfo";
        this._strData = "CompanySalesInfo";
        obj.set_PathToData(this._strPath);
        obj.set_DataObject(this._strData);
        obj.set_DataAction("GetDefaultSalesInfo");
        obj.addParameter("id", this._BomCompanyNo);
        obj.addDataOK(Function.createDelegate(this, this.getBOMDataOK));
        obj.addError(Function.createDelegate(this, this.getBOMDataError));
        obj.addTimeout(Function.createDelegate(this, this.getBOMDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getBOMDataOK: function (args) {
        var res = args._result;
        this._intGlobalCurrencyNo = res.GlobalCurrencyNo;
        this._intPOCurrencyNo = res.CurrencyNo;
        this.bindCurrency();
      
    },
    bindCurrency: function () {
        this.getFieldControl("ctlCurrency")._intGlobalCurrencyNo = this._intGlobalCurrencyNo;
        this.getFieldControl("ctlCurrency")._blnIsBuy = false;
        this.getFieldDropDownData("ctlCurrency");
        
    },

    getBOMDataError: function (args) {
        this.showError(true, args.get_ErrorMessage());
    },


    dispose: function() {
        if (this.isDisposed) return;
        this._intBOMID = null;
        
        Rebound.GlobalTrader.Site.Controls.Forms.ClientImportBOMMainInfo_Edit.callBaseMethod(this, "dispose");
    },

    saveClicked: function () {
        if (this._blnRequestedToPoHub==false) {
            if (!this.validateForm()) return;
        }
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/ClientImportBOMMainInfo");
        obj.set_DataObject("ClientImportBOMMainInfo");
        obj.set_DataAction("SaveEdit");
        obj.addParameter("id", this._intBOMID);
        obj.addParameter("Code", this.getFieldValue("ctlCode"));
        obj.addParameter("Name", this.getFieldValue("ctlName"));
        obj.addParameter("Company", this.getFieldValue("ctlCompany"));
        obj.addParameter("Contact", this.getFieldValue("ctlContact"));
        obj.addParameter("InActive", this.getFieldValue("ctlInActive"));
        obj.addParameter("Notes", this.getFieldValue("ctlNotes"));
        obj.addParameter("Currency", this.getFieldValue("ctlCurrency"));
        obj.addParameter("SalesPersion", this.getFieldValue("ctlSalespersion"));
 
        //[002] code end
        obj.addDataOK(Function.createDelegate(this, this.saveEditComplete));
        obj.addError(Function.createDelegate(this, this.saveEditError));
        obj.addTimeout(Function.createDelegate(this, this.saveEditError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    saveEditError: function(args) {
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    saveEditComplete: function(args) {
        if (args._result.Result == true) {
            this.showSavedOK(true);
            this.onSaveComplete();
        } else {
            this._strErrorMessage = args._errorMessage;
            this.onSaveError();
        }
    },

    validateForm: function() {
        this.onValidate();
        var blnOK = this.autoValidateFields();
        
        return blnOK;
    },
    showFieldsLoading: function(bln) {
        this.showFieldLoading("ctlCompany", bln);
    }

};

Rebound.GlobalTrader.Site.Controls.Forms.ClientImportBOMMainInfo_Edit.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.ClientImportBOMMainInfo_Edit", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
