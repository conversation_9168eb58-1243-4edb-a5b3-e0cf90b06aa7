///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Warehouse");

Rebound.GlobalTrader.Site.Pages.Warehouse.LotDetail = function(el) { 
	Rebound.GlobalTrader.Site.Pages.Warehouse.LotDetail.initializeBase(this, [el]);
};

Rebound.GlobalTrader.Site.Pages.Warehouse.LotDetail.prototype = {

	get_ctlPageTitle: function() { return this._ctlPageTitle; }, 	set_ctlPageTitle: function(v) { if (this._ctlPageTitle !== v)  this._ctlPageTitle = v; }, 
	get_ctlLotItems: function() { return this._ctlLotItems; }, 	set_ctlLotItems: function(v) { if (this._ctlLotItems !== v)  this._ctlLotItems = v; }, 
	get_ctlMainInfo: function() { return this._ctlMainInfo; }, 	set_ctlMainInfo: function(v) { if (this._ctlMainInfo !== v)  this._ctlMainInfo = v; }, 

	initialize: function() {
		Rebound.GlobalTrader.Site.Pages.Warehouse.LotDetail.callBaseMethod(this, "initialize");
	},
	
	goInit: function() {
		if (this._ctlMainInfo) this._ctlMainInfo.addGotData(Function.createDelegate(this, this.ctlMainInfo_GotData));
		Rebound.GlobalTrader.Site.Pages.Warehouse.LotDetail.callBaseMethod(this, "goInit");
	},
	
	dispose: function() { 
		if (this.isDisposed) return;
		if (this._element) $clearHandlers(this._element);
		if (this._ctlPageTitle) this._ctlPageTitle.dispose();
		if (this._ctlLotItems) this._ctlLotItems.dispose();
		if (this._ctlMainInfo) this._ctlMainInfo.dispose();
		this._ctlPageTitle = null;
		this._ctlLotItems = null;
		this._ctlMainInfo = null;
		Rebound.GlobalTrader.Site.Pages.Warehouse.LotDetail.callBaseMethod(this, "dispose");
	},
	
	ctlMainInfo_GotData: function() {
		this._ctlPageTitle.updateTitle(this._ctlMainInfo.getFieldValue("ctlName"));
	}
	
};

Rebound.GlobalTrader.Site.Pages.Warehouse.LotDetail.registerClass("Rebound.GlobalTrader.Site.Pages.Warehouse.LotDetail", Rebound.GlobalTrader.Site.Pages.Content, Sys.IDisposable);
