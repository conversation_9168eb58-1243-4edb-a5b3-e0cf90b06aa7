﻿using System;
using System.Collections.Generic;
using System.Data.Common;
using System.Data.SqlClient;
using System.Data;

namespace Rebound.GlobalTrader.DAL.SqlClient
{
    public class SqlStarRatingProvider : StarRatingProvider
    {
        public override List<StarRatingDetails> GetList()
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_selectAll_StarRating_Configs", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<StarRatingDetails> lst = new List<StarRatingDetails>();
                while (reader.Read())
                {
                    StarRatingDetails obj = new StarRatingDetails();
                    obj.NumOfPO = GetReaderValue_Int32(reader, "NumOfPO", 0);
                    obj.CountedStar = GetReaderValue_Byte(reader, "CountedStar", 1);
                    obj.CreatedDate = GetReaderValue_DateTime(reader, "CreatedDate", DateTime.MinValue);
                    obj.CreatedBy = GetReaderValue_String(reader, "CreatedBy", "");
                    lst.Add(obj);
                }
                return lst;
            }

            catch (SqlException sqlex)
            {
                throw new Exception("Failed to get Star Rating configurations", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override void Insert(int numOfPO, int? createdBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_insert_StarRating_Config", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@NumberOfPO", SqlDbType.Int).Value = numOfPO;
                cmd.Parameters.Add("@CreatedBy", SqlDbType.Int).Value = createdBy;
                cn.Open();
                ExecuteNonQuery(cmd);
            }

            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert Star Rating configuration", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
    }
}
