Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.Team=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.Team.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DropDowns.Team.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.DropDowns.Team.callBaseMethod(this,"initialize");this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK))},dispose:function(){this.isDisposed||Rebound.GlobalTrader.Site.Controls.DropDowns.Team.callBaseMethod(this,"dispose")},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/Team");this._objData.set_DataObject("Team");this._objData.set_DataAction("GetData")},dataCallOK:function(){var t=this._objData._result,n;if(t.Teams)for(n=0;n<t.Teams.length;n++)this.addOption(t.Teams[n].Name,t.Teams[n].ID)}};Rebound.GlobalTrader.Site.Controls.DropDowns.Team.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.Team",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);