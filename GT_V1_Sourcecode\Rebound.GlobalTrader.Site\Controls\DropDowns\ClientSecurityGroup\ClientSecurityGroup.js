Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.ClientSecurityGroup=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.ClientSecurityGroup.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DropDowns.ClientSecurityGroup.prototype={initialize:function(){this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK));Rebound.GlobalTrader.Site.Controls.DropDowns.ClientSecurityGroup.callBaseMethod(this,"initialize")},dispose:function(){this.isDisposed||Rebound.GlobalTrader.Site.Controls.DropDowns.ClientSecurityGroup.callBaseMethod(this,"dispose")},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/ClientSecurityGroup");this._objData.set_DataObject("ClientSecurityGroup");this._objData.set_DataAction("GetData")},dataCallOK:function(){var t=this._objData._result,n;if(t.Clients)for(n=0;n<t.Clients.length;n++)this.addOption(t.Clients[n].Name,t.Clients[n].ID)},datatestOK:function(){}};Rebound.GlobalTrader.Site.Controls.DropDowns.ClientSecurityGroup.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.ClientSecurityGroup",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);