<%--
Marker     Changed by      Date         Remarks
[001]      Shashi Keshar 17/11/2017   Added for Warehouse Security implementation
--%>
<%@ Control Language="C#" CodeBehind="Debits.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.DataListNuggets.Debits" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" BoxType="List">
	<Filters>
		<ReboundUI_DataListNugget:Filter ID="ctlFilter" runat="server">
			<FieldsLeft>
				<ReboundUI_FilterDataItemRow:Numerical id="ctlDebitNo" runat="server" ResourceTitle="DebitNo" FilterField="DebitNo" />
				<ReboundUI_FilterDataItemRow:TextBox id="ctlPart" runat="server" ResourceTitle="PartNo" FilterField="Part" />
				<ReboundUI_FilterDataItemRow:TextBox id="ctlCompanyName" runat="server" ResourceTitle="CompanyName" FilterField="CMName" />
				<ReboundUI_FilterDataItemRow:CheckBox id="ctlPohubOnly" runat="server" ResourceTitle="PohubOnly" FilterField="PohubOnly" DefaultValue="true"  />
				<ReboundUI_FilterDataItemRow:Numerical id="ctlPONo" runat="server" ResourceTitle="PurchaseOrderNo" FilterField="PONo" />
				<ReboundUI_FilterDataItemRow:Numerical id="ctlSRMANo" runat="server" ResourceTitle="SRMANo" FilterField="SRMANo" />
				<ReboundUI_FilterDataItemRow:TextBox id="ctlSupplierInvoice" runat="server" ResourceTitle="SupplierInvoice" FilterField="SupplierInvoice" />
			</FieldsLeft>
			<FieldsRight>
				<ReboundUI_FilterDataItemRow:TextBox id="ctlContactName" runat="server" ResourceTitle="ContactName" FilterField="Contact" />
				<ReboundUI_FilterDataItemRow:DropDown id="ctlBuyerName" runat="server" ResourceTitle="BuyerName" DropDownType="Employee" DropDownAssembly="Rebound.GlobalTrader.Site" FilterField="Buyer" />
				<ReboundUI_FilterDataItemRow:TextBox id="ctlDebitNotes" runat="server" ResourceTitle="Notes" InitialSearchType="Contains" FilterField="DebitNotes" />
				<ReboundUI_FilterDataItemRow:DateSelect id="ctlDebitDateFrom" runat="server" ResourceTitle="DebitDateFrom" FilterField="DebitDateFrom" />
				<ReboundUI_FilterDataItemRow:DateSelect id="ctlDebitDateTo" runat="server" ResourceTitle="DebitDateTo" FilterField="DebitDateTo" />
			<%--[001]Code Start--%>
				<ReboundUI_FilterDataItemRow:DropDown id="ctlClientName" runat="server" ResourceTitle="ClientName" DropDownType="Client" DropDownAssembly="Rebound.GlobalTrader.Site" FilterField="Client"  />				
				<%--[001]Code End--%>
                	</FieldsRight>
		</ReboundUI_DataListNugget:Filter>
	</Filters>
</ReboundUI_Nugget:DesignBase>
