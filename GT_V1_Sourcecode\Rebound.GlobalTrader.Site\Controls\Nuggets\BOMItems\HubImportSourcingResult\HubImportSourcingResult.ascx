﻿<%@ Control Language="C#" CodeBehind="HubImportSourcingResult.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.HubImportSourcingResult" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<%@ Register TagPrefix="cc1" Namespace="Rebound.GlobalTrader.Site" Assembly="Rebound.GlobalTrader.Site" %>
<script src="js/jquery.uploadfile.js"></script>
<script src="js/jquery.dataTables.min.js"></script>
<script src="js/HubSourcingResultImport.js"></script>
<link href="css/jquery-ui.css.css" rel="stylesheet" />
<script src="js/jquery-ui.js"></script>
<!--ParamQuery Grid css files-->
<link rel="stylesheet" href="paramquery-8.1.0/pqgrid.dev.css" />

<!--add pqgrid.ui.css for jQueryUI theme support-->
<link rel="stylesheet" href="paramquery-8.1.0/pqgrid.ui.dev.css" />

<!--ParamQuery Grid custom theme e.g., office, bootstrap, rosy, chocolate, etc (optional)-->
<link rel="stylesheet" href="paramquery-8.1.0/themes/bootstrap/pqgrid.css" />

<!--ParamQuery Grid js files-->
<script type="text/javascript" src="paramquery-8.1.0/pqgrid.dev.js"></script>

<!--ParamQuery Grid localization file-->
<script src="paramquery-8.1.0/localize/pq-localize-en.js"></script>

<!--Include pqTouch file to provide support for touch devices (optional)-->
<script type="text/javascript" src="paramquery-8.1.0/pqTouch/pqtouch.min.js"></script>

<!--Include jsZip file to support xlsx and zip export (optional)-->
<script type="text/javascript" src="paramquery-8.1.0/jsZip-2.5.0/jszip.min.js"></script>
<style>
    #ctl00_cphMain_ctlBOMItems_ctlDB_ctl14_ctlHubImportSR_ctlDB_pnlContentInner {
        width: 100%;
    }

    #ctl00_cphMain_ctlBOMItems_ctlDB_ctl14_ctlHubImportSR_ctlDB table.dataTable tr th {
        background-color: #424241 !important;
        color: #fff;
        border-bottom: solid 1px #515050 !important;
    }

    #ctl00_cphMain_ctlBOMItems_ctlDB_ctl14_ctlHubImportSR_ctlDB .boxForms table.dataTable td {
        color: #000;
        font-weight: normal;
        white-space: nowrap;
    }

    #ctl00_cphMain_ctlBOMItems_ctlDB_ctl14_ctlHubImportSR_ctlDB table.formRows td.title {
        color: #fff !important;
    }

    #ctl00_cphMain_ctlBOMItems_ctlDB_ctl14_ctlHubImportSR_ctlDB div.topMenu {
        z-index: 10000;
    }

    #ctl00_cphMain_ctlBOMItems_ctlDB_ctl14_ctlHubImportSR_ctlDB .mainArea_Off .mainRightInner {
        width: 1045px;
        margin: 0 auto;
    }

    #ctl00_cphMain_ctlBOMItems_ctlDB_ctl14_ctlHubImportSR_ctlDB .mainArea_On .mainRightInner {
        width: 1045px;
    }

    #ctl00_cphMain_ctlBOMItems_ctlDB_ctl14_ctlHubImportSR_ctlDB .spanBorder {
        margin: 0px 0 0 3px;
        color: #c1cbce;
    }

    #ctl00_cphMain_ctlBOMItems_ctlDB_ctl14_ctlHubImportSR_ctlDB fieldset {
        font-size: 12px;
        padding: 5px;
        width: 100%;
        line-height: 1.8;
    }

    #ctl00_cphMain_ctlBOMItems_ctlDB_ctl14_ctlHubImportSR_ctlDB .dataTables_wrapper .dataTables_paginate .paginate_button {
        width: 88%;
        text-align: right;
        height: 60px;
        border-radius: 10%;
        background-color: #C0C0C0;
        vertical-align: central;
        color: black;
        margin: 0 5px;
        border: 0 !important;
        line-height: 17px;
        box-shadow: none !important;
        padding: 2px 5px;
    }

    #ctl00_cphMain_ctlBOMItems_ctlDB_ctl14_ctlHubImportSR_ctlDB * {
        box-sizing: border-box;
    }

    #ctl00_cphMain_ctlBOMItems_ctlDB_ctl14_ctlHubImportSR_ctlDB .ajax-file-upload {
        padding: 7px 25px !important;
        width: auto !important;
        background-color: #158684 !important;
        color: #fff !important;
        margin: 0 !important;
    }

    #ctl00_cphMain_ctlBOMItems_ctlDB_ctl14_ctlHubImportSR_ctlDB .ajax-file-upload {
        height: 24px !important;
    }

    #table1_length, #table1_info {
        padding: 10px;
        /*background: #3a6c34;*/
        color: black;
        font-weight: bold
    }

        #table1_length label {
            color: black;
        }

    #table1_paginate {
        margin-top: 5px;
    }

        #table1_paginate a {
            padding: 5px;
            margin: 5px;
            background: #3a6c34;
            color: #fff;
        }

    #divhistory {
        border: 1px solid #3a6c34;
        overflow: scroll;
        width: 96%;
        height: 300px;
        display: block;
        margin-top: 36px;
        padding: 2px;
    }

    #tbshowhistory_wrapper .view-filter {
        background: #3a6c34;
        padding: 5px;
        width: 103%;
    }

    #ctl00_cphMain_ctlBOMItems_ctlDB_ctl14_ctlHubImportSR_ctlDB .container-greenbase {
        background-color: #56954e;
        color: #fff;
        font-size: 11px;
        font-family: tahoma;
        /*padding: 10px;*/
    }

    #ctl00_cphMain_ctlBOMItems_ctlDB_ctl14_ctlHubImportSR_ctlDB fieldset {
        border: 1px #6cab63 solid;
        margin-bottom: 10px;
    }

    #ctl00_cphMain_ctlBOMItems_ctlDB_ctl14_ctlHubImportSR_ctlDB legend {
        display: block;
        padding-left: 1px;
        padding-right: 5px;
        color: #fff;
        font-family: Tahoma;
    }

    #ctl00_cphMain_ctlBOMItems_ctlDB_ctl14_ctlHubImportSR_ctlDB table td {
        vertical-align: top;
    }

        #ctl00_cphMain_ctlBOMItems_ctlDB_ctl14_ctlHubImportSR_ctlDB table td.firstcol {
            width: 85%;
        }

    #ctl00_cphMain_ctlBOMItems_ctlDB_ctl14_ctlHubImportSR_ctlDB .radio-option {
        margin-bottom: 10px;
    }

    #ctl00_cphMain_ctlBOMItems_ctlDB_ctl14_ctlHubImportSR_ctlDB select {
        min-width: 133px;
        margin-right: 20px;
        border: 0px #fff solid;
        font-size: 11px;
        padding: 2px;
    }

    #ctl00_cphMain_ctlBOMItems_ctlDB_ctl14_ctlHubImportSR_ctlDB .space {
        padding: 10px;
    }

    #ctl00_cphMain_ctlBOMItems_ctlDB_ctl14_ctlHubImportSR_ctlDB .show {
        background-color: #009491;
        color: #fff;
        margin-right: 10px;
        border: 1px #009491 solid;
        padding: 6px !important;
    }

    #ctl00_cphMain_ctlBOMItems_ctlDB_ctl14_ctlHubImportSR_ctlDB .reset {
        background-color: #85d279;
        color: #2c6e23;
        border: 1px #2c6e23 solid;
        padding: 6px !important;
        border-radius: 5px;
        width: 70px;
    }

    #ctl00_cphMain_ctlBOMItems_ctlDB_ctl14_ctlHubImportSR_ctlDB .exlsfile {
        width: 1200px;
        background: #d5d5d5;
        /*height: 110px;*/
        margin-top: 5px;
    }

        #ctl00_cphMain_ctlBOMItems_ctlDB_ctl14_ctlHubImportSR_ctlDB .exlsfile .header {
            background: #434343;
            padding: 5px 10px;
        }

    #ctl00_cphMain_ctlBOMItems_ctlDB_ctl14_ctlHubImportSR_ctlDB .clientblock .row {
        float: left;
        width: 100%;
        padding: 5px 0;
    }

        #ctl00_cphMain_ctlBOMItems_ctlDB_ctl14_ctlHubImportSR_ctlDB .clientblock .row .label {
            display: block;
            float: left;
            width: 13%;
        }

    #ctl00_cphMain_ctlBOMItems_ctlDB_ctl14_ctlHubImportSR_ctlDB .row .label {
        display: block;
        float: left;
        width: 4%;
    }

    #ctl00_cphMain_ctlBOMItems_ctlDB_ctl14_ctlHubImportSR_ctlDB .clientblock .row .radio-option {
        float: left;
    }

    #ctl00_cphMain_ctlBOMItems_ctlDB_ctl14_ctlHubImportSR_ctlDB .radio-option select {
        min-width: 240px;
        margin-right: 20px;
    }

    #ctl00_cphMain_ctlBOMItems_ctlDB_ctl14_ctlHubImportSR_ctlDB input[type='radio'] {
        vertical-align: bottom;
        margin: 0 5px 0 5px;
    }

    #ctl00_cphMain_ctlBOMItems_ctlDB_ctl14_ctlHubImportSR_ctlDB .gfg {
        font-size: 40px;
        color: green;
        font-weight: bold;
    }

    #ctl00_cphMain_ctlBOMItems_ctlDB_ctl14_ctlHubImportSR_ctlDB .col2 {
        width: 48%;
        margin: 0% 1%;
        float: left;
    }

    #ctl00_cphMain_ctlBOMItems_ctlDB_ctl14_ctlHubImportSR_ctlDB .file {
        position: relative;
        display: inline-block;
        cursor: pointer;
        margin-right: 105px;
    }

        #ctl00_cphMain_ctlBOMItems_ctlDB_ctl14_ctlHubImportSR_ctlDB .file input {
            min-width: 14rem;
            margin: 0;
            filter: alpha(opacity=0);
            opacity: 0;
        }

    #ctl00_cphMain_ctlBOMItems_ctlDB_ctl14_ctlHubImportSR_ctlDB .file-custom {
        position: absolute;
        top: 0;
        right: 0;
        left: 0;
        z-index: 5;
        line-height: 1.5;
        color: #555;
        background-color: #fff;
        border: .075rem solid #ddd;
    }

        #ctl00_cphMain_ctlBOMItems_ctlDB_ctl14_ctlHubImportSR_ctlDB .file-custom::before {
            position: absolute;
            top: -.075rem;
            right: -2.075rem;
            bottom: -.075rem;
            z-index: 6;
            display: block;
            content: "";
            padding: .1rem 0.9rem;
            line-height: 1.5;
            background-color: #85ce7a;
            border: 1px #93c58b solid;
        }

        #ctl00_cphMain_ctlBOMItems_ctlDB_ctl14_ctlHubImportSR_ctlDB .file-custom::after {
            content: "Choose file...";
        }

    #ctl00_cphMain_ctlBOMItems_ctlDB_ctl14_ctlHubImportSR_ctlDB .colright {
        float: right;
    }

    #ctl00_cphMain_ctlBOMItems_ctlDB_ctl14_ctlHubImportSR_ctlDB .three-col td {
        width: 31%;
        text-align: right;
    }

    #ctl00_cphMain_ctlBOMItems_ctlDB_ctl14_ctlHubImportSR_ctlDB table.formRows input {
        vertical-align: middle;
    }

    #ctl00_cphMain_ctlBOMItems_ctlDB_ctl14_ctlHubImportSR_ctlDB .reset {
        margin-right: 5px;
        cursor: pointer;
    }

    #sourcingExcelUpload {
        display: block;
        margin-top: 0px;
    }

    #divLoader {
        width: 100%;
        top: 0;
        left: 0;
        z-index: 10001;
    }

    .btn-import:hover,
    .btn-import:focus {
        box-shadow: none;
        transform: none;
    }

    #table1 tbody td {
        color: black;
        font-weight: normal;
    }

    .incorrect-title {
        width: 110px;
        float: left;
        padding-left: 5px;
        margin-top: 5px;
    }

    .incorrect-control {
        float: left;
        margin-top: 5px;
    }

    .select-incorrect {
        width: 180px !important;
        margin-right: 10px !important;
        height: 25px;
    }

    .upload-note {
        font-style: italic;
        padding: 5px 0px;
        text-align: left;
    }

    .txt-incorrect {
        max-width: 180px !important;
        float: left;
        margin-right: 5px;
        height: 25px;
    }

    .lbl-incorrect {
        max-width: 180px;
        float: left;
        margin-right: 0px !important;
        font-weight: initial;
    }

    .AutoCompleteHeader {
        color: #fff !important;
        background-color: #000000;
        padding: 4px;
        font-size: 11px;
        font-weight: bold;
    }

    .ui-autocomplete-loading {
        background: url('../../../../App_Themes/Original/images/autosearch/loading.gif') no-repeat right center;
        background-color: white;
    }

    .ui-widget.ui-widget-content {
        z-index: 10000;
    }

    .ui-autocomplete {
        background: #fff !important;
        font-family: Tahoma !important;
        color: #006600 !important;
        max-height: 200px !important;
        overflow-y: auto;
        overflow-x: hidden;
        background-color: #eeffee !important;
    }

        .ui-autocomplete .ui-menu-item {
            padding: 2px !important;
            border-bottom: dotted 1px #bbbbbb !important;
            margin-bottom: 1px !important;
        }

        .ui-autocomplete .ui-state-focus {
            padding: 2px !important;
            border-bottom: dotted 1px #bbbbbb !important;
            background-color: #eeffee !important;
            color: #006600 !important
        }

    .ui-autocomplete-input {
        width: 250px;
    }

    a.quick-reselect {
        text-decoration: none;
        color: #064500;
        font-weight: bold;
        font-size: 11px;
        cursor: pointer;
        width: 55px;
        background: none;
        float: left;
        margin: 0px 8px 0px 6px;
        display: none;
    }

    .pq-loading-mask {
        padding: 5px !important;
        height: fit-content !important;
    }

    .btn-gray {
        background: #cacaca;
        color: #000;
        padding: 6px 20px !important;
        white-space: normal;
        font-weight: bold !important;
        cursor: pointer;
        border-radius: 5px;
    }

        .btn-gray:hover,
        .btn-gray:focus {
            box-shadow: none;
            transform: none;
        }

        .btn-gray:disabled {
            cursor: not-allowed;
            opacity: 0.5;
        }

    .right {
        float: right;
    }

    .pq-title-span{
        font-weight:bold;
    }
    .pq-title-span:hover{
        text-decoration:none !important;
        cursor:default !important;
    }

    .ui-state-error {
        border: none !important;
        background: none !important;
    }

    .pq-grid-footer table{
        width: 100%;
    }

    .pq-grid-footer table td select.pq-page-select{
        padding: 5px !important;
    }

    .pq-grid-header-table{
        background-image: url(../../../App_Themes/Original/images/FlexiDataTable/th_bg.png);
        background-position: bottom;
        background-repeat: repeat-x;
        border-bottom: solid 2px #e0e0e0;
        background-color: #eeeeee;
    }

    span.pq-title-span{
        color:#999999;
    }

    .pq-grid-bottom{
        background-image: url(../../../App_Themes/Original/images/Nuggets/standard/bg_top.gif) !important;
        background-position: left top !important;
        background-repeat: repeat-x !important;
    }

    .pq-grid-center-o{
        padding:5px 5px 0px;
    }

    .prevent-upload{
        pointer-events:none;
    }
</style>
<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="true" ShowQuickHelp="false">
    <Links>
        <ReboundUI:IconButton ID="ibtnCancel" runat="server" IconGroup="Nugget" IconTitleResource="Cancel" IconCSSType="Cancel" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
    </Links>
    <Explanation><%=Functions.GetGlobalResource("FormExplanations", "UtilityOffer_Import")%></Explanation>
    <Content>
        <ReboundUI_Table:Form ID="frm" runat="server" style="table-layout:fixed">
            <asp:TableRow>
                <asp:TableCell class="title" RowSpan="2">
                    <span style="margin-left: 100px; text-align: right;">
                        <p style="color: white; font-style: italic;" title='Use "Export to Excel" function to have import template'>
                           Use "Export to Excel" function to have import template
                        </p>
                    </span>
                    <div class="container-greenbase">
                        <!--csv data-->
                        <fieldset class="space">
                            <legend>CSV Data</legend>
                            <div class="row" style="display:none">
                                <label for="file contains">
                                    <input type="checkbox" name="FileCCH" value="FileCCH" checked="checked" id="chkFileCCH">File contains column header</label>
                            </div>
                            <br>
                            <div class="row" style="margin-bottom: 10px;">
                                <div class="label">Files</div>
                                <label class="file" id="fileUploadWraper">
                                    <div class="lableopt" style="width: 250px;">
                                        <div id="HubImportSourcing">Upload</div>
                                    </div>
                                </label>

                                <div class="colright" style="display: flex">
                                    <input class="btn reset btn-import" type="button" id="btnReset" value="Reset">
                                    <input type="button" class="btn-gray" id="btnDisplayCsvData" value="Display Raw CSV Data">
                                </div>
                                <div class="LoaderPopup" id="divLoader">
                                    <div>
                                        <div class="cssload-loader">Loading..</div>
                                    </div>
                                </div>
                            </div>

                            <div class="row" style="">
                                <label class="file" style="">
                                    <div class="lableopt" style="width: 283px;"></div>
                                </label>
                                <label style="display: none;">Delimiter : </label>
                                &nbsp;<input type="text" style="width: 25px; display: none" name="txtdelimiter" id="txtdelimiter" />
                            </div>
                            <div class="lableNotes" style="width: 315px;">
                                Note: Maximum limit should not exceed <%= ConfigurationManager.AppSettings["MaxUploadRowCount"]%> rows. 
                            </div>

                            <%--<div class="cssload-loader">Loading..</div>--%>
                            <script type="text/javascript">
                                $(function () {
                                    var chkhead = "YES"
                                    var section = "BOM_BomImport_Stock";
                                    formControlId = "<%=this.ClientID%>";
                                    loginId = "<%=SessionManager.LoginID%>";
                                    var dragdropObj = $("#HubImportSourcing").uploadFile({
                                        url: "DocImage.ashx?mxs=1&type=EXCELUPLOADSOURCINGRESULT&IsDragDrop=true",
                                        allowedTypes: "csv,xlsx",
                                        fileName: "myfile",
                                        section: section,
                                        autoSubmit: true,
                                        multiple: false,
                                        //maxFileSize: 79000000,
                                        maxFileSize: 5253366,
                                        showStatusAfterSuccess: false,
                                        showCancel: true,
                                        showDone: true,
                                        async: false,
                                        uploadDiv: "sourcingExcelUpload",
                                        timeout: 6000000,
                                        dynamicFormData: function () {
                                            var data = { section: section }
                                            return data;
                                        },
                                        onSuccess: function (files, data, xhr) {
                                            var originalFilename = '';
                                            var generatedFilename = '';
                                            originalFilename = files[0];
                                            var json = Sys.Serialization.JavaScriptSerializer.deserialize(data);
                                            generatedFilename = json.FileName;
                                            $find("<%=this.ClientID%>").importExcelData(originalFilename, generatedFilename, "YES");
                                        },
                                        onSelect: function (fup) {
                                            var result = true;
                                            $find("<%=this.ClientID%>")._dragobj = dragdropObj;

                                            return result;
                                        }
                                    });
                                });
                            </script>

                            <div class="row correction-container">
                                <table id="tblCorrection" style="border: 1px #6cab63 solid; width: 100%">
                                    <tr style="background-color: #434343;">
                                        <td colspan="2" style="padding-left: 5px;">Correction</td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <div class="incorrect-title">Incorrect MFR:</div>
                                            <div class="incorrect-control">
                                                <select id="SelectIncorrectMFR" class="select-incorrect">
                                                    <option value="0">&lt; Select &gt;</option>
                                                </select>
                                                <label id="lblMFRCount" style="color: #b20000;font-weight: initial;"></label>
                                                <input type="hidden" id="HDMFRCount" />
                                            </div>
                                        </td>
                                        <td>
                                            <div class="incorrect-title">Incorrect Supplier:</div>
                                            <div class="incorrect-control" style="padding-left: 10px;">
                                                <select id="SelectIncorrectVendor" class="select-incorrect">
                                                    <option value="0">&lt; Select &gt;</option>
                                                </select>
                                                <label id="lblVendorCount" style="color: #b20000;font-weight: initial;"></label>
                                                <input type="hidden" id="HDVendorCount" />
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="width: 50%">
                                            <div class="incorrect-title">GT MFR:</div>
                                            <div class="incorrect-control">
                                                <input type="text" id="TxtIncorrectMFR" class="txt-incorrect" placeholder="Type char to search"/>
                                                <label id="LblIncorrectMFR" class="lbl-incorrect"></label>
                                                <a id="RemoveIncorrectMFR" class="quick-reselect" style="display:none;">[Reselect]</a>
                                                <input type="button" id="btnReplaceMFR" class="btn-gray" value ="Replace All" />
                                                
                                            </div>
                                        </td>
                                        <td style="width: 50%">
                                            <div class="incorrect-title">GT Supplier:</div>
                                            <div class="incorrect-control" style="padding-left: 10px;">
                                                <input type="text" id="TxtIncorrectVendor" class="txt-incorrect" placeholder="Type char to search"/>
                                                <label id="LblIncorrectVendor" class="lbl-incorrect"></label>
                                                <a id="RemoveIncorrectVendor" class="quick-reselect" style="display:none;">[Reselect]</a>
                                                <input type="button" id="btnReplaceVendor" class="btn-gray" value ="Replace All" />
                                                
                                            </div>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                            <div class="row correction-container">
                                <div style="padding-top: 10px">
                                    <label class="upload-note">Please see below data to be bulk imported into GT. Please note line: highlighted in red indicate a data mismatch with GT and will require correction before being able to successfully import into GT</label>
                                </div>
                                <div style="padding: 5px 0px 10px 0px; margin-left: -4px;">
                                    <span>
                                        <input type="checkbox" id="chkShowMismatch" checked="">
                                        <label style="margin-right: 3px;">Show only data row with a data mismatch</label>
                                    </span>
                                </div>
                                <div id="gridRawData"></div>
                                <div style="text-align: center;padding-top: 10px;">
                                    <input type ="button" id="btnSave" class="btn-gray" value ="Save & Check"> 
                                    <input type ="button" id="btnImport" class="btn-gray" value ="Import" disabled>                                           
                                </div>
                            </div>
                    <%--</div>--%>
                    </fieldset>
		            <!--csv data end-->
                    </div>
                </asp:TableCell>
            </asp:TableRow>
        </ReboundUI_Table:Form>
    </Content>
</ReboundUI_Form:DesignBase>


