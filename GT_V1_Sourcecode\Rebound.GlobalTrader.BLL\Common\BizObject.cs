using System;
using System.Security.Principal;
using System.Web;
using System.Text.RegularExpressions;
using System.Globalization;

namespace Rebound.GlobalTrader.BLL
{
    public abstract class BizObject {
		protected const int MAXROWS = int.MaxValue;
		protected const int TOPTORETURN = 20;

		public static int MaxRows = MAXROWS;

		protected static string CurrentUserName {
			get {
				string userCode = "";
				userCode = "Bob";
				return userCode;
			}
		}

        

		protected static IPrincipal CurrentUser {
			get { return HttpContext.Current.User; }
		}
		protected static string CurrentUserLogin {
			get {
				string userName = "";
				if (HttpContext.Current.User.Identity.IsAuthenticated)
					userName = HttpContext.Current.User.Identity.Name;
				return userName;
			}
		}
		protected static string CurrentUserIP {
			get { return HttpContext.Current.Request.UserHostAddress; }
		}
		protected static int GetPageIndex(int startRowIndex, int maximumRows) {
			if (maximumRows <= 0)
				return 0;
			else
				return (int)Math.Floor((double)startRowIndex / (double)maximumRows);
		}
		protected static string ConvertNullToEmptyString(string input) {
			return (input == null ? "" : input);
		}
		protected static int ConvertNullToIntZero(int? input) {
			return (input == null ? 0 : (int)input);
		}
		protected static float ConvertNullToFloatZero(float? input) {
			return (input == null ? 0 : (float)input);
		}
		protected static DateTime ConvertNullToMinDate(DateTime? input) {
			return (input == null ? DateTime.MinValue : (DateTime)input);
		}
		protected static DateTime? ConvertMinDateToNull(DateTime input) {
			return (input == DateTime.MinValue ? null : (DateTime?)input);
		}

        /// <summary>
        /// Create By Vinay: 1st Dec 2017: It is a common property for all module
        /// Need to assign if the product is Inacive
        /// </summary>
        public System.Boolean? ProductInactive { get; set; }

		private static string FormatStringForDatabase(string str)
		{
			str = str.Replace("<br>", "\r\n");
			str = str.Replace("<br/>", "\r\n");
			str = str.Replace("<br />", "\r\n");
			str = str.Replace(@":QUOTE:", @"""");
			str = str.Replace(@":PLUS:", @"+");
			str = str.Replace(@":AND:", @"&");
			return str;
		}

		public static string RemovePunctuation(string strIn)
		{
			return Regex.Replace(strIn.Trim(), @"\W*", "");
		}

		/// <summary>
		/// Removes punctuation but retains an initial percent so we can do a begins with search
		/// </summary>
		private static string RemovePunctuationRetainingPercentSigns(string strIn)
		{
			strIn = strIn.Trim();
			string strOut = RemovePunctuation(strIn);
			if (strIn.StartsWith("%")) strOut = string.Format("%{0}", strOut);
			if (strIn.EndsWith("%")) strOut = string.Format("{0}%", strOut);
			return strOut;
		}

		public static string GetFormValue_StringForPartSearch(string strIndex)
		{
			string strOut = RemovePunctuationRetainingPercentSigns(FormatStringForDatabase(strIndex));
			if (strOut == "") strOut = null;
			return strOut;
		}

        public static string CheckDayLightSaving(DateTime ukTime)
        {
            TimeZoneInfo timeZone = TimeZoneInfo.FindSystemTimeZoneById("GMT Standard Time");
            bool isDaylightSavingTime = timeZone.IsDaylightSavingTime(ukTime);
            string msg = isDaylightSavingTime == true ? "BST" : "UTC";
            return msg;
        }

        public static string FormatBSTDate(DateTime date)
        {
            var isBST = CheckDayLightSaving(date);
            if (isBST == "BST")
            {
                return date.DayOfWeek + " " + date.Day + " " + CultureInfo.CurrentCulture.DateTimeFormat.GetMonthName(date.Month)
                    + " " + date.Year + " " + date.Hour.ToString("D2") + ":" + date.Minute.ToString("D2");
            }
            return "";
        }
    }
}