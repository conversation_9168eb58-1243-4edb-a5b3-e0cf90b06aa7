﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/*   
===========================================================================================  
TASK			UPDATED BY      DATE			ACTION		DESCRIPTION  
[US-214760]		An.TranTan		13-Jan-2025		Create		Insert BOM Import Sourcing Results column mapping
===========================================================================================  
*/   
CREATE OR ALTER Procedure [dbo].[usp_save_BOMSourcing_ColumnMapping]    
    @TargetColumns NVARCHAR(max),
    @SelectedColumns NVARCHAR(max),
    @UserId INT = 1
AS    
BEGIN    
    SET NOCOUNT ON;
	--Clear previous data
	DELETE BorisGlobalTraderimports.dbo.tbBOMSourcing_ColumnMapping;
	
	DECLARE @DynamicSQL NVARCHAR(MAX);
	SET @DynamicSQL = 'INSERT INTO BorisGlobalTraderimports.dbo.tbBOMSourcing_ColumnMapping(' + @TargetColumns + ',CreatedBy) '
					+ 'VALUES( ' + @SelectedColumns + ',' + CAST(@UserId AS NVARCHAR(10)) + ')'
	PRINT(@DynamicSQL);
	EXEC (@DynamicSQL);
END  
GO


