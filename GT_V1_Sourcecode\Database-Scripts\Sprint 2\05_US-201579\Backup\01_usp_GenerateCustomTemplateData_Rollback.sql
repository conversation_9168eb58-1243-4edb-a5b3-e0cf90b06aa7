
GO

        
ALTER Procedure [dbo].[usp_GenerateCustomTemplateData]        
(        
@QuoteId int,        
@ColumnString varchar(2000)     
,@ReqFrom int = 1    
)        
as        
begin        
        
  /*    
  @ReqFrom = 1 => calling from Quotation    
  @ReqFrom = 2 => calling from SalesOrder    
  */    
        
select distinct val into #temp from SplitString(@ColumnString,'|')        
        
delete from #temp where isnull(val,'')=''        
--select * from #temp         
        
--select   case when CHARINDEX('=',val)>0         
--         then SUBSTRING(val,1,CHARINDEX('=',val)-1)         
--         else val end Colname,        
--   CASE WHEN CHARINDEX('=',val)>0         
--         THEN SUBSTRING(val,CHARINDEX('=',val)+1,len(val))          
--         ELSE NULL END as indexval        
--   ,        
--   CASE WHEN CHARINDEX('~',SUBSTRING(val,CHARINDEX('=',val)+1,len(val)) )>0         
--         THEN SUBSTRING(val,CHARINDEX('~',val)+1,len(val))          
--         ELSE NULL END as fileColumn        
--   into #tempColumnList        
--   from #temp         
        
        
select         
PARSENAME(replace(val,'=','.'),2) as Colname        
--,PARSENAME(replace(name,'=','.'),1) as firstname        
,PARSENAME(replace(PARSENAME(replace(val,'=','.'),1),'~','.'),2) as indexval         
,PARSENAME(REPLACE(PARSENAME(replace(val,'~','.'),1) ,'~',1),1) as fileColumn        
into #tempColumnList        
from #temp        
        
--select * from #tempColumnList order by convert(int,indexval)         
--return        
if(@ReqFrom = 1)    
begin     
    
select d.Sequence as SNo, d.customerpart as CustomerPart,d.ManufacturerNo,tbmfr.ManufacturerName as Mfr,d.part as Part,convert(decimal(18,4),b.Price) as QuotedPrice,d.productno,         
tbprd.productname as Product,b.Quantity as QuotedQuantity ,        
null MOQ,c.SPQ,        
 convert(decimal(18,4),(b.Quantity*b.Price),2) as TotalPrice         
--(select sum(tbqtl.price) from tbQuoteLine tbqtl where tbqtl.quoteno = a.quoteid) as TotalPrice        
,d.Datecode as DC, d.notes,c.PackageNo,tbpck.PackageName as Pack, b.ECCNCode DutyCode, b.ETA ETA        
,convert(decimal(18,4),d.Price) as UnitPrice      
,a.QuoteId as QuoteId    
into #tempData        
from tbQuote a join tbQuoteLine b on a.QuoteId = b.QuoteNo        
join tbSourcingResult c on b.SourcingResultNo = c.SourcingResultId         
join tbCustomerRequirement d on c.CustomerRequirementNo = d.CustomerRequirementId        
join tbBOMManager e on d.BOMManagerNo = e.BOMManagerId        
left join tbmanufacturer tbmfr on d.ManufacturerNo = tbmfr.Manufacturerid        
left join tbproduct tbprd on d.productno = tbprd.productid        
left join tbpackage tbpck on c.packageno = tbpck.packageid        
where a.QuoteId = @quoteId        
end    
else if (@ReqFrom=2)    
begin     
select  d.customerpart as CustomerPart,d.ManufacturerNo,tbmfr.ManufacturerName as Mfr,d.part as Part,tbqtl.Price as QuotedPrice,d.productno,         
tbprd.productname as Product,tbqtl.Quantity as QuotedQuantity ,        
null MOQ,c.SPQ,        
 convert(float,(tbqtl.Quantity*tbqtl.Price),2) as TotalPrice         
--(select sum(tbqtl.price) from tbQuoteLine tbqtl where tbqtl.quoteno = a.quoteid) as TotalPrice        
,d.Datecode as DC, d.notes,c.PackageNo,tbpck.PackageName as Pack, tbqtl.ECCNCode DutyCode, tbqtl.ETA ETA        
,d.Price as UnitPrice      
,tbqtl.QuoteNo as QuoteId    
from tbSalesOrderLine tbsol join tbQuoteLine tbqtl    
on tbsol.QuoteLineNo = tbqtl.QuoteLineId    
join tbSourcingResult c on tbqtl.SourcingResultNo = c.SourcingResultId         
join tbCustomerRequirement d on c.CustomerRequirementNo = d.CustomerRequirementId        
join tbBOMManager e on d.BOMManagerNo = e.BOMManagerId        
left join tbmanufacturer tbmfr on d.ManufacturerNo = tbmfr.Manufacturerid        
left join tbproduct tbprd on d.productno = tbprd.productid        
left join tbpackage tbpck on c.packageno = tbpck.packageid        
where tbsol.SalesOrderNo = @quoteId     
end     
        
        
--select * from #tempData        
        
declare  @StrColName varchar(2000)        
set @StrColName =''        
select         
case when Colname ='CustomerPart' then  'customerpart as '''+fileColumn+''''        
when Colname='Mfr' then 'ManufacturerName as '''+fileColumn+''''        
when Colname='Part' then 'Part as '''+fileColumn+''''         
when Colname='UnitPrice' then 'Price as '''+fileColumn+''''         
when Colname='Product' then 'productname as '''+fileColumn+''''         
when Colname='QuotedQuantity' then 'Quantity as '''+fileColumn+''''          
when Colname='MOQ' then 'MOQ as '''+fileColumn+''''         
when Colname='SPQ' then 'SPQ as '''+fileColumn+''''         
when Colname='TotalPrice' then 'TotalPrice as '''+fileColumn+''''          
when Colname='DC' then 'Datecode as '''+fileColumn+''''         
when Colname='Notes' then 'Notes as '''+fileColumn+''''         
when Colname='Pack' then 'PackageName as '''+fileColumn+''''         
when Colname='DutyCode' then 'DutyCode as '''+fileColumn+''''         
when Colname='ETA' then 'ETA as '''+fileColumn+''''         
else  '' end as 'ColList', indexval  into #tempColList        
from #tempColumnList order by convert(int,indexval)         
        
--print'check1'        
--select 'rahil'        
--select * from #tempColList  order by convert(int,indexval)         
        
DECLARE @CSV VARCHAR(MAX)         
SELECT @CSV = COALESCE(@CSV + ',', ' ') + ColList from #tempColList          
--SELECT @CSV AS Result        
--print'check2'        
declare @myquery varchar(2000)        
set @myquery = ''        
        
--select *  from #tempData        
--select         
--customerpart as CustomerPart, Datecode as DC, DutyCode as DutyCode, ETA as ETA, ManufacturerName as Mfr, MOQ as MOQ, Notes as Notes,       
--PackageName as Pack, Part as Part, productname as Product, Quantity as QuotedQuantity, SPQ as SPQ, TotalPrice as TotalPrice, Price as UnitPrice        
--from #tempData        
        
select @myquery = 'select '+@CSV+' from #tempData'        
--print @myquery        
--exec (@myquery)        
select * from #tempData        
end   
GO


