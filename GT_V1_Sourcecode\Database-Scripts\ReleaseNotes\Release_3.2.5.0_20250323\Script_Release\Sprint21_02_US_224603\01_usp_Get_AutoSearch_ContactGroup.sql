﻿
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/* 
========================================================================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-224603]		Phuc Hoang			05-Feb-2025		CREATE			IPO- Sourcing - Check Supplier/ MFR Data- Addition of MFR Group Name
========================================================================================================================================
*/

CREATE OR ALTER PROCEDURE [dbo].[usp_Get_AutoSearch_ContactGroup](
	@ContactGroupType NVARCHAR (100) = NULL,
	@AutoSearch  NVARCHAR (100) = NULL,
	@IsForDropdown BIT = NULL
)
AS

BEGIN
	SELECT * FROM dbo.tbContactGroup
	WHERE ContactName LIKE '%'+@AutoSearch + '%' 
	AND ((ISNULL(@ContactGroupType, '') = '') OR (ContactGroupType = @ContactGroupType))
	AND ((@IsForDropdown = 1 AND Inactive = 0) OR (@IsForDropdown = 0))
	ORDER BY Inactive, ContactName
END
GO


