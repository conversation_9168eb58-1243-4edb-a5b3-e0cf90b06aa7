Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.LineManagerContact=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.LineManagerContact.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DropDowns.LineManagerContact.prototype={get_intBuyerID:function(){return this._intBuyerID},set_intBuyerID:function(n){this._intBuyerID!==n&&(this._intBuyerID=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.DropDowns.LineManagerContact.callBaseMethod(this,"initialize");this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK))},dispose:function(){this.isDisposed||(Rebound.GlobalTrader.Site.Controls.DropDowns.LineManagerContact.callBaseMethod(this,"dispose"),this._intBuyerID=null)},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/LineManagerContact");this._objData.set_DataObject("LineManagerContact");this._objData.set_DataAction("GetData");this._objData.addParameter("id",this._intBuyerID);this._objData.addParameter("ClientNo",this._intClientID);this._objData.addParameter("UpdateManager",this._UpdateManager)},dataCallOK:function(){var t=this._objData._result,n;if(t.Types)for(n=0;n<t.Types.length;n++)this.addOption(t.Types[n].Name,t.Types[n].ID)}};Rebound.GlobalTrader.Site.Controls.DropDowns.LineManagerContact.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.LineManagerContact",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);