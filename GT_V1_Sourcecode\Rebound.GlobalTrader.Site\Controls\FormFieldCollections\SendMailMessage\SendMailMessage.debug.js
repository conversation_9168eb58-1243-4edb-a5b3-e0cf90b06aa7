///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.FormFieldCollections");

Rebound.GlobalTrader.Site.Controls.FormFieldCollections.SendMailMessage = function(element) { 
	Rebound.GlobalTrader.Site.Controls.FormFieldCollections.SendMailMessage.initializeBase(this, [element]);
	this._aryRecipientLoginIDs = [];
	this._aryRecipientLoginNames = [];
	this._aryRecipientGroupIDs = [];
	this._aryRecipientGroupNames = [];
	this._intNumberRecipients = 0;
};

Rebound.GlobalTrader.Site.Controls.FormFieldCollections.SendMailMessage.prototype = {

    get_pnlSelected: function() { return this._pnlSelected; }, set_pnlSelected: function(value) { if (this._pnlSelected !== value) this._pnlSelected = value; },
    get_lblSelected: function() { return this._lblSelected; }, set_lblSelected: function(value) { if (this._lblSelected !== value) this._lblSelected = value; },
    get_autLoginOrGroup: function() { return this._autLoginOrGroup; }, set_autLoginOrGroup: function(value) { if (this._autLoginOrGroup !== value) this._autLoginOrGroup = value; },

    initialize: function () {
        this._autLoginOrGroup._intGlobalLoginClientNo =  this._intGlobalClientNo;
        this._autLoginOrGroup.addSelectionMadeEvent(Function.createDelegate(this, this.newRecipientSelected));
        this.showSelected();
        Rebound.GlobalTrader.Site.Controls.FormFieldCollections.SendMailMessage.callBaseMethod(this, "initialize");
    },

    dispose: function() {
        if (this.isDisposed) return;
        if (this._autLoginOrGroup) this._autLoginOrGroup.dispose();
        this._pnlSelected = null;
        this._lblSelected = null;
        this._autLoginOrGroup = null;
        this._aryRecipientLoginIDs = null;
        this._aryRecipientLoginNames = null;
        this._aryRecipientGroupIDs = null;
        this._aryRecipientGroupNames = null;
        this._intNumberRecipients = null;
        Rebound.GlobalTrader.Site.Controls.FormFieldCollections.SendMailMessage.callBaseMethod(this, "dispose");
    },

    newRecipientSelected: function(blnShow) {
        if (this._autLoginOrGroup._varSelectedExtraData.toUpperCase() == "GROUP") {
            this.addNewGroupRecipient(this._autLoginOrGroup._varSelectedID, this._autLoginOrGroup._varSelectedValue);
        } else {
            this.addNewLoginRecipient(this._autLoginOrGroup._varSelectedID, this._autLoginOrGroup._varSelectedValue);
        }
        
    },

    addNewLoginRecipient: function(intID, strName) {
        if (!Array.contains(this._aryRecipientLoginIDs, intID)) {
            Array.add(this._aryRecipientLoginIDs, intID);
            Array.add(this._aryRecipientLoginNames, strName);
            this._intNumberRecipients += 1;
        }
        this.showSelected();
        this._autLoginOrGroup.reselect();
    },

    addNewGroupRecipient: function(intID, strName) {
        if (!Array.contains(this._aryRecipientGroupIDs, intID)) {
            Array.add(this._aryRecipientGroupIDs, intID);
            Array.add(this._aryRecipientGroupNames, strName);
            this._intNumberRecipients += 1;
        }
        this.showSelected();
        this._autLoginOrGroup.reselect();
    },

    showSelected: function() {
        $R_FN.showElement(this._pnlSelected, (this._intNumberRecipients > 0));
        var strFN = "";
        var strHTML = "";
        for (var i = 0, l = this._aryRecipientGroupIDs.length; i < l; i++) {
            strHTML += '<div class="mailRecipient mailRecipientGroup">';
            strHTML += "<b>" + this._aryRecipientGroupNames[i] + "</b>";
            strFN = String.format("$find('{0}').removeGroupRecipient({1});", this._element.id, i);
            strHTML += String.format('&nbsp;&nbsp;&nbsp;<a href="javascript:void(0);" class="quickSearchReselect" onclick="{0}">[x]</a>', strFN);
            strHTML += "</div>";
        }
        for (i = 0, l = this._aryRecipientLoginIDs.length; i < l; i++) {
            strHTML += '<div class="mailRecipient">';
            strHTML += this._aryRecipientLoginNames[i];
            strFN = String.format("$find('{0}').removeLoginRecipient({1});", this._element.id, i);
            strHTML += String.format('&nbsp;&nbsp;&nbsp;<a href="javascript:void(0);" class="quickSearchReselect" onclick="{0}">[x]</a>', strFN);
            strHTML += "</div>";
        }
        $R_FN.setInnerHTML(this._lblSelected, strHTML);
    },

    removeLoginRecipient: function(i) {
        Array.removeAt(this._aryRecipientLoginIDs, i);
        Array.removeAt(this._aryRecipientLoginNames, i);
        this._intNumberRecipients -= 1;
        this.showSelected();
    },

    removeGroupRecipient: function(i) {
        Array.removeAt(this._aryRecipientGroupIDs, i);
        Array.removeAt(this._aryRecipientGroupNames, i);
        this._intNumberRecipients -= 1;
        this.showSelected();
    },

    setValue_Subject: function(strValue) {
        this._ctlRelatedForm.setFieldValue("ctlSubject", strValue);
    },

    getValue_Subject: function() {
        return this._ctlRelatedForm.getFieldValue("ctlSubject");
    },

    setValue_Body: function(strValue) {
        this._ctlRelatedForm.setFieldValue("ctlBody", strValue);
    },


    getValue_Body: function() {
        return this._ctlRelatedForm.getFieldValue("ctlBody");
    },

    setValue_To: function(strValue) {
        this._ctlRelatedForm.setFieldValue("ctlTo", strValue);
    },
    getValue_To: function() {
        return this._ctlRelatedForm.getFieldValue("ctlTo");
    },

    validateFields: function() {
        var blnOK = true;
        this._ctlRelatedForm.resetFormFields();
        if (!this._ctlRelatedForm.checkFieldEntered("ctlBody")) blnOK = false;
        if (!this._ctlRelatedForm.checkFieldEntered("ctlSubject")) blnOK = false;
        if (this._intNumberRecipients == 0) {
            this._ctlRelatedForm.setFieldInError("ctlTo", true, $R_RES.RequiredFieldMissingMessage);
            blnOK = false;
        }
        return blnOK;
    },

    resetFields: function() {
        this.setValue_Subject("");
        this.setValue_Body("");
        Array.clear(this._aryRecipientLoginIDs);
        Array.clear(this._aryRecipientLoginNames);
        Array.clear(this._aryRecipientGroupIDs);
        Array.clear(this._aryRecipientGroupNames);
        this._intNumberRecipients = 0;
        this.showSelected();
    }

};

Rebound.GlobalTrader.Site.Controls.FormFieldCollections.SendMailMessage.registerClass("Rebound.GlobalTrader.Site.Controls.FormFieldCollections.SendMailMessage", Rebound.GlobalTrader.Site.Controls.Forms.LabelFormField, Sys.IDisposable);
