DECLARE @ConstraintName NVARCHAR(128)

SELECT @ConstraintName = dc.name
FROM sys.default_constraints dc
JOIN sys.columns c ON c.default_object_id = dc.object_id
JOIN sys.tables t ON t.object_id = c.object_id
WHERE t.name = 'tbIHSparts'
  AND c.name = 'APIImportedData'

IF @ConstraintName IS NOT NULL
BEGIN
    EXEC('ALTER TABLE tbIHSparts DROP CONSTRAINT ' + @ConstraintName)
END
IF EXISTS (
    SELECT 1
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_NAME = 'tbIHSparts'
      AND COLUMN_NAME = 'APIImportedData'
)
BEGIN
    ALTER TABLE tbIHSparts
    DROP COLUMN APIImportedData
END
