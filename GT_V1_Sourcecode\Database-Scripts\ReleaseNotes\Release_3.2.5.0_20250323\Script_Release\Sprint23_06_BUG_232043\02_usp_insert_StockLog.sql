﻿/*
============================================================================================================================ 
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-232043]     CuongDox		 12-Mar-2025		CREATE		Bug 232043: [PROD Bug] Stock is Quarantined until release
============================================================================================================================  
*/
CREATE OR ALTER PROCEDURE [dbo].[usp_insert_StockLog]                        
--********************************************************************************************                        
--* RP 08.01.2010:                        
--* - add PartMarkings and CountingMethodNo                        
--*                        
--* RP 05.01.2010:                        
--* - source CountryOfManufacture value from tbStock                        
--                        
--* RP 16.10.2009:                        
--* - add display of PurchasePrice being changed                        
--                        
--* RP 12.10.2009:                        
--* - add DebitNo                        
--*                        
--* RP 30.09.2009:                        
--* - allow null to be passed in quantities so we can get them from the updated Stock row                        
--*                        
--* RP 17.08.2009:                        
--* - add all fields as changed on the first stock log entry                        
--*                        
--* RP 06.08.2009:                        
--* - process changed fields to show what they were changed to                        
--*                        
--* RP 09.06.2009:                        
--* - add GoodsInNo                        
--*                        
--* RP 04.06.2009:                        
--* - ensure no negative stock values              
            
--* RP 23.10.2021:                        
--* - added new field for stock log                      
--********************************************************************************************                        
    @StockLogTypeNo int                        
  , @StockNo int                        
  , @StockLogReasonNo int = NULL                        
  , @QuantityInStock int = NULL                        
  , @QuantityOnOrder int = NULL                        
  , @Detail nvarchar(max) = NULL                        
  , @ChangeNotes nvarchar(max) = NULL                        
  , @InvoiceNo int = NULL                        
  , @PurchaseOrderNo int = NULL                        
  , @GoodsInLineNo int = NULL                        
  , @GoodsInNo int = NULL                        
  , @SalesOrderLineNo int = NULL                        
  , @SalesOrderNo int = NULL                        
  , @SRMALineNo int = NULL                        
  , @CRMALineNo int = NULL                        
  , @DebitNo int = NULL                        
  , @RelatedStockNo int = NULL                        
  , @ActionQuantity int = NULL                        
  , @UpdatedBy int = NULL
  ,	@StockReferenceNo int = NULL
  , @StockLogId int OUTPUT                        
AS                         
    BEGIN                        
                            
        IF NOT @StockNo IS NULL                         
            BEGIN                        
                                    
                DECLARE @Stock_Part nvarchar(30)                        
                  , @Stock_ROHS int                        
                  , @Stock_SupplierPart nvarchar(30)                        
                  , @Stock_ManufacturerName nvarchar(128)                        
                  , @Stock_DateCode nvarchar(5)                        
                  , @Stock_ProductDescription nvarchar(128)                        
                  , @Stock_PackageDescription nvarchar(128)                        
                  , @Stock_WarehouseName nvarchar(128)                        
                  , @Stock_Location nvarchar(10)                        
                  , @Stock_ResalePrice float                        
                  , @Stock_LandedCost float                        
                  , @Stock_LotName nvarchar(128)                        
                  , @Stock_StockKeepingUnit int                        
                  , @Stock_PackageUnit int                        
                  , @Stock_QualityControlNotes nvarchar(max)    
  , @Stock_CountryOfManufacture nvarchar(50)                        
                  , @Stock_ShipInCost float       
                  , @IncludeBlank bit                        
                  , @Stock_PurchasePrice float                        
                  , @Stock_PartMarkings nvarchar(50)                        
                  , @Stock_CountingMethod nvarchar(128)                     
                  , @Stock_DivisionName nvarchar(128)             
      , @Stock_MSLLevel nvarchar(128)             
      , @CorrectPackage nvarchar(128)                  
      ,@CorrectManufacturer nvarchar(128)             
    ,@CorrectMSL nvarchar(128)             
      ,@CorrectHIC nvarchar(128)               
                  ,@CorrectRohsStatus nvarchar(128)                        
                  ,@QueryDateCode nvarchar(128)               
      ,@PackageBreakdownInfo nvarchar(128)               
      ,@CorrectPartNo  nvarchar(128)             
      ,@countryOforigin nvarchar(128)       
   ,@Quantity int      
   ,@AccountNotes nvarchar(MAX)            
                SET @IncludeBlank = 1                         
                                      
    --get changed details of Stock                        
         SELECT TOP 1                        
                        @Stock_Part = st.Part                        
                      , @Stock_ROHS = st.ROHS                        
                      , @Stock_SupplierPart = st.SupplierPart                        
                      , @Stock_ManufacturerName = mf.ManufacturerName                        
        , @Stock_DateCode = st.DateCode                        
                      , @Stock_ProductDescription = pr.ProductDescription                        
                      , @Stock_PackageDescription = pk.PackageDescription                   
                     , @Stock_WarehouseName = wh.WarehouseName                        
                      , @Stock_Location = st.Location                        
                      , @Stock_ResalePrice = st.ResalePrice                        
                      , @Stock_LandedCost = isnull(st.ClientLandedCost, st.LandedCost)                
                      , @Stock_LotName = lt.LotName                        
                      , @Stock_StockKeepingUnit = st.StockKeepingUnit                        
                      , @Stock_PackageUnit = st.PackageUnit                        
                      , @Stock_QualityControlNotes = st.QualityControlNotes                        
                      , @Stock_CountryOfManufacture = cn.GlobalCountryName                                          
       , @Stock_ShipInCost = gil.ShipInCost                        
                      , @Stock_PurchasePrice = isnull(gil.ClientPrice , gil.Price)                        
                      , @QuantityInStock = isnull(@QuantityInStock, st.QuantityInStock)                        
                      , @QuantityOnOrder = isnull(@QuantityOnOrder, st.QuantityOnOrder)                        
                      , @Stock_PartMarkings = st.PartMarkings                        
                      , @Stock_CountingMethod = cnm.CountingMethodDescription                    
                      , @Stock_DivisionName = dv.DivisionName                    
       , @Stock_MSLLevel = st.MSLLevel                   
                   ,@CorrectPackage=pk1.PackageName             
       ,@CorrectManufacturer=mf1.ManufacturerName            
       ,@CorrectMSL=gil.CorrectMSLLevel            
       ,@CorrectHIC=HICS.HICStatus            
       ,@CorrectRohsStatus=ROHSsts.[Description]            
       ,@QueryDateCode=gil.CorrectDateCode            
       ,@PackageBreakdownInfo=gil.PKGBreakdownMismatch            
       ,@CorrectPartNo=gil.CorrectPartNo            
       ,@countryOforigin=ctry.GlobalCountryName       
    ,@Quantity=gil.Quantity    
 ,@AccountNotes= ''--gil.Notes          
                FROM    tbStock st       
                LEFT JOIN tbManufacturer mf ON st.ManufacturerNo = mf.ManufacturerId                        
                LEFT JOIN tbProduct pr ON st.ProductNo = pr.ProductID                        
                LEFT JOIN tbPackage pk ON st.PackageNo = pk.PackageID                        
                LEFT JOIN tbWarehouse wh ON st.WarehouseNo = wh.WarehouseId                        
                LEFT JOIN tbLot lt ON st.LotNo = lt.LotId                        
                LEFT JOIN tbGoodsInLine gil ON st.GoodsInLineNo = gil.GoodsInLineId                
                LEFT JOIN tbGlobalCountryList cn ON cn.GlobalCountryId = st.CountryOfManufacture                        
                LEFT JOIN tbCountingMethod cnm ON cnm.CountingMethodId = st.CountingMethodNo                     
                LEFT JOIN tbDivision dv on dv.DivisionId = st.DivisionNo              
    LEFT JOIN tbPackage pk1 ON gil.CorrectPackageType = pk1.PackageID                 
    LEFT JOIN tbManufacturer mf1 ON gil.CorrectManufacturer = mf1.ManufacturerId             
    LEFT JOIN tbRohsstatus ROHSsts on   gil.CorrectROHSStatus= ROHSsts.ROHSStatusId            
    LEFT JOIN tbGlobalCountryList ctry on ctry.GlobalCountryId=gil.CountryOfManufacture           
 LEFT JOIN tbHICstatus HICS on HICS.HICId= gil.CorrectHICStatus          
                WHERE   StockId = @StockNo                                      
                        
                            
    --if there's no detail set and this is the first Stock Log entry for the stock, show all fields as changed                        
        IF len(rtrim(ltrim(isnull(@Detail, '')))) = 0                        
                    AND (SELECT count(*)                        
                         FROM   tbStockLog                        
                         WHERE  StockNo = @StockNo                        
                        ) = 0                         
                    BEGIN                        
                        SET @Detail = 'PartNo||ROHS||SupplierPartNo||Manufacturer||DateCode||Product||Package||Warehouse||Location||ResalePrice||LandedCost||Lot||StockKeepingUnit||PackageUnit||QualityControlNotes||CountryOfManufacture||ShipInCost||BatchR
eference||CountingMethod||Division||MSLLevel||CorrectPartNo||CorrectManufacturer||CorrectPackage||CorrectMSL||CorrectHIC||CorrectRohsStatus||QueryDateCode||PackageBreakdownInfo||countryOforigin||Quantity||AccountNotes'                        
                        SET @IncludeBlank = 0                         
                    END                        
                        
                DECLARE @DetailString nvarchar(max)                        
                SET @DetailString = @Detail                        
                        
    -- find values for Detail                        
                IF NOT rtrim(ltrim(@Detail)) = ''                         
                    BEGIN                        
                        SET @DetailString = ''                        
                        DECLARE @ChangedField nvarchar(150)                        
                        DECLARE curChangedFields CURSOR FAST_FORWARD READ_ONLY                        
                            FOR SELECT  String                        
                                FROM    dbo.ufn_splitString(@Detail, '||')                        
                        OPEN curChangedFields                        
                FETCH NEXT FROM curChangedFields INTO @ChangedField                        
      WHILE @@FETCH_STATUS = 0                        
                            BEGIN                                     
                                DECLARE @ChangedFieldValue nvarchar(max)                        
                        
                                IF @ChangedField = 'Quantity'                       
                 SET @ChangedField = 'QuantityInStock'                        
            
     SET @ChangedFieldValue = case @ChangedField                        
                                                           WHEN 'QuantityInStock' THEN isnull(cast((SELECT  @QuantityInStock                    
   ) AS nvarchar(max)), 0)                        
                                                           WHEN 'PartNo' THEN isnull(cast((SELECT   @Stock_Part                        
                                ) AS nvarchar(max)), '')                        
                                                           WHEN 'ROHS' THEN isnull(cast((SELECT @Stock_ROHS                        
                                          ) AS nvarchar(max)), '')                        
                                                           WHEN 'SupplierPartNo' THEN isnull(cast((SELECT   @Stock_SupplierPart                        
        ) AS nvarchar(max)), '')                        
                                                           WHEN 'Manufacturer' THEN isnull(cast((SELECT @Stock_ManufacturerName                                                                                                                  ) AS nvarchar(
  
  
    
      
max)), '')                        
                                  WHEN 'DateCode' THEN isnull(cast((SELECT @Stock_DateCode                        
                                                                                            ) AS nvarchar(max)), '')                        
                             WHEN 'Product' THEN isnull(cast((SELECT  @Stock_ProductDescription                        
                                                                                           ) AS nvarchar(max)), '')                        
                                                           WHEN 'Package' THEN isnull(cast((SELECT  @Stock_PackageDescription                        
                                                       ) AS nvarchar(max)), '')                        
                                                           WHEN 'Warehouse' THEN isnull(cast((SELECT    @Stock_WarehouseName                        
                                                                                             ) AS nvarchar(max)), '')                        
                                                           WHEN 'Location' THEN isnull(cast((SELECT @Stock_Location                        
                                                                                            ) AS nvarchar(max)), '')                        
                                                           WHEN 'ResalePrice' THEN isnull(cast((SELECT  @Stock_ResalePrice                        
                                                                                               ) AS nvarchar(max)), '')                        
                                                           WHEN 'LandedCost' THEN isnull(cast((SELECT   @Stock_LandedCost                        
                                                                                              ) AS nvarchar(max)), 0)                        
                                                           WHEN 'Lot' THEN isnull(cast((SELECT  @Stock_LotName                        
                                                                                       ) AS nvarchar(max)), '')                        
                                                           WHEN 'StockKeepingUnit' THEN isnull(cast((SELECT @Stock_StockKeepingUnit                        
                                                                     ) AS nvarchar(max)), '')                    
              WHEN 'PackageUnit' THEN isnull(cast((SELECT  @Stock_PackageUnit                        
                                                                                       ) AS nvarchar(max)), '')                        
                                                           WHEN 'QualityControlNotes' THEN isnull(cast((SELECT  @Stock_QualityControlNotes                  
                                                                                                       ) AS nvarchar(max)), '')                        
                                                           WHEN 'CountryOfManufacture' THEN isnull(cast((SELECT @Stock_CountryOfManufacture                        
                                   ) AS nvarchar(max)), '')                        
                                                           WHEN 'ShipInCost' THEN isnull(cast((SELECT   @Stock_ShipInCost                        
                                                                                              ) AS nvarchar(max)), 0)                      
                                            WHEN 'PurchasePrice' THEN isnull(cast((SELECT    @Stock_PurchasePrice                        
                                                                                                 ) AS nvarchar(max)), 0)                        
                                                           WHEN 'BatchReference' THEN isnull(cast((SELECT @Stock_PartMarkings                        
                       ) AS nvarchar(max)), '')                        
                                                           WHEN 'CountingMethod' THEN isnull(cast((SELECT   @Stock_CountingMethod                        
                                                                                                  ) AS nvarchar(max)), '')                      
                                                           WHEN 'Division' THEN isnull(cast((SELECT  @Stock_DivisionName                        
                                                                                           ) AS nvarchar(max)), '')                  
              WHEN 'MSL' THEN isnull(cast((SELECT  @Stock_MSLLevel                        
                                                        ) AS nvarchar(max)), '')            
     WHEN 'CorrectPartNo' THEN isnull(cast((SELECT  @CorrectPartNo                        
                           ) AS nvarchar(max)), '')               
    WHEN 'CorrectPackage' THEN isnull(cast((SELECT  @CorrectPackage                        
                                                        ) AS nvarchar(max)), '')             
    WHEN 'CorrectManufacturer' THEN isnull(cast((SELECT  @CorrectManufacturer                        
                                                        ) AS nvarchar(max)), '')            
   WHEN 'CorrectMSL' THEN isnull(cast((SELECT  @CorrectMSL                        
                                                        ) AS nvarchar(max)), '')              
   WHEN 'CorrectHIC' THEN isnull(cast((SELECT  @CorrectHIC                       
                                                        ) AS nvarchar(max)), '')            
   WHEN 'CorrectRohsStatus' THEN isnull(cast((SELECT  @CorrectRohsStatus                       
                                                        ) AS nvarchar(max)), '')            
   WHEN 'QueryDateCode' THEN isnull(cast((SELECT  @QueryDateCode                       
                                                        ) AS nvarchar(max)), '')            
   WHEN 'PackageBreakdownInfo' THEN isnull(cast((SELECT  @PackageBreakdownInfo                       
                                                        ) AS nvarchar(max)), '')                       
   WHEN 'countryOforigin' THEN isnull(cast((SELECT  @countryOforigin                      
                                                        ) AS nvarchar(max)), '')         
    WHEN 'Quantity' THEN isnull(cast((SELECT  @Quantity                        
                                                        ) AS nvarchar(max)), '')      
 WHEN 'AccountNotes' THEN isnull(cast((SELECT  @AccountNotes                        
                                                        ) AS nvarchar(max)), '')                  
                       ELSE ''                      
                                                       END                        
                                
                                IF @IncludeBlank = 1                        
                                    OR (@IncludeBlank = 0                        
             AND len(isnull(@ChangedFieldValue, '')) > 0)                         
                                    BEGIN                                
                                        IF (len(@DetailString) > 0)                         
                                            SET @DetailString = @DetailString + '||'                        
                        
                                        SET @DetailString = @DetailString + @ChangedField + '¦¦' + @ChangedFieldValue                        
                                    END                        
                                  
                                FETCH NEXT FROM curChangedFields INTO @ChangedField                    
                            END                        
                                                
                        CLOSE curChangedFields                        
                        DEALLOCATE curChangedFields                        
                    END                        
                        
                        
                INSERT  INTO dbo.tbStockLog (                        
                          StockLogTypeNo                        
                        , StockNo                        
                        , StockLogReasonNo                        
                        , QuantityInStock                        
                        , QuantityOnOrder                    
                        , Detail                        
                        , ChangeNotes                        
                        , InvoiceNo                        
                        , PurchaseOrderNo                        
 , RelatedStockNo                        
          , GoodsInLineNo                        
               , GoodsInNo                        
                        , SalesOrderNo                       
                        , SalesOrderLineNo                        
                        , SRMALineNo                        
   , CRMALineNo                        
                 , DebitNo                        
                        , ActionQuantity                        
                        , UpdatedBy                        
                 , DLUP,
				 StockReferenceNo
                        )                        
                VALUES  (                        
                         @StockLogTypeNo                        
                       , @StockNo                        
                     , @StockLogReasonNo                        
                       , isnull(dbo.ufn_max(@QuantityInStock, 0), 0)                        
           , isnull(dbo.ufn_max(@QuantityOnOrder, 0), 0)                        
                       , @DetailString                        
                       , @ChangeNotes                        
                       , @InvoiceNo                        
                       , @PurchaseOrderNo                        
                       , @RelatedStockNo                        
                       , @GoodsInLineNo                        
                       , @GoodsInNo                        
                       , @SalesOrderNo                        
                       , @SalesOrderLineNo                        
                       , @SRMALineNo                        
                       , @CRMALineNo                        
                       , @DebitNo                        
                       , @ActionQuantity                        
                       , @UpdatedBy                        
                       , CURRENT_TIMESTAMP
					   , @StockReferenceNo)                         
            END                   
                        
        SET @StockLogId = scope_identity()                        
                        
    END   
  