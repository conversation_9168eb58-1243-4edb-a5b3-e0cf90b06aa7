﻿
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/* 
==========================================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[Bug-232042]	Ngai To				20-Feb-2025		Update			Bug 232042: [PROD Bug] RL Import History not working Prod BUG
==========================================================================================================
*/

CREATE OR ALTER PROCEDURE [dbo].[usp_select_ReverseLogistic_ImportHistory]                                       
 @DisplayLength int=0                                      
,@DisplayStart int=0                                      
,@SortCol int=0                                    
,@SortDir nvarchar(10)                                        
,@Search nvarchar(255) = NULL                   
,@ClientType int=0                     
,@selectedclientid int=0                   
,@CreateBy int=0                     
,@InactiveId bit=0                 
as                                        
begin                         
                                   
    Declare @FirstRec int, @LastRec int                                        
    Set @FirstRec = @DisplayStart;                                        
    Set @LastRec = @DisplayStart + @DisplayLength;                                        
                                           
    With CTE_Stock as                                        
    (Select ROW_NUMBER() over (order by importdate  desc) as RowNum,                                        
 COUNT(*) over() as TotalCount,      
 ImportDate ,ImportName  ,RowsAffected ,Target ,  
 (case when ClientType=1 then 'HUB' when ClientType=2 then 'UK' when ClientType=3 then 'HK' else '' end) ClientTypeText ,  
 ClientType, ImportId ,InactiveDate    
 from BorisGlobalTraderimports.dbo.tbReverseLogistic_ImportHistory              
 where isnull(ClientType,2)=@ClientType                                 
    )  
   
 Select RowNum,TotalCount,  
 ImportDate AS Date,
 --CONVERT(varchar,ImportDate,9) as Date ,                     
  --CONVERT(VARCHAR(30), ImportDate, 22) as Date ,                     
 ImportName as Source ,RowsAffected as Rows ,Target  as Status ,ClientTypeText as Client  ,  
 ImportId,
  CONVERT(VARCHAR(30), InactiveDate, 22) as InactiveDate 
 into #tbtemp          
 from CTE_Stock                               
 --where  RowNum > @FirstRec and RowNum <= @LastRec                                       
 ORDER BY            
 ImportDate desc                        
          
  select b.ImportId, b.inactive,b.UpdatedBy,count(1) as cnt into #tbtemp2 from #tbtemp a join BorisGlobalTraderImports..tbReverseLogistic b WITH (NOLOCK) on a.ImportId =b.importid  
  group by  b.ImportId, b.inactive,b.UpdatedBy  
  
  --select * from #tbtemp2  
  select ImportId,UpdatedBy,count(1) as cnt into #tbtemp3 from #tbtemp2 group by ImportId,UpdatedBy  
  
  --select * from #tbtemp3   
  select a.*, case when b.cnt >1 then 0 --partial -- active  
  when b.cnt=1 then (  
  case when (select count(1) from BorisGlobalTraderImports..tbReverseLogistic tbrv WITH (NOLOCK) where tbrv.ImportId=b.importid and  Inactive = 1) > 0   
   then 1 --Inactive  
   else 0 --active  
   end  
       )  
  else '' end as Inactiveflag 
  ,b.UpdatedBy into #temp4  
  from #tbtemp a left join #tbtemp3 b on a.ImportId=b.ImportId   
  
  --select * from #temp4  
    
  declare @totalcount int  
  select @totalcount = count(1) from #temp4 where Inactiveflag =  @InactiveId   
  --select @totalcount  
  update #temp4 set totalcount  = @totalcount   
  
  select  a.*,b.EmployeeName,  
  case when Inactiveflag =0 then 'Active'   
  when Inactiveflag = 1  then 'InActive'  
  else 'Partial Active' end as InactiveStatus 
  --,ROW_NUMBER() over (order by Date  desc) as RowNumNew
  into #temp5
  from #temp4 a left join tblogin b WITH (NOLOCK) on a.UpdatedBy=b.LoginId  
  where Inactiveflag =  @InactiveId   --order by RowNumNew 
  --and RowNumNew > @FirstRec and RowNumNew <= @LastRec 
  select RowNum,TotalCount,CONVERT(VARCHAR(30), Date, 22) as Date ,
  Source,Rows,Status,Client,ImportId,InactiveDate,Inactiveflag,UpdatedBy,EmployeeName,InactiveStatus
  ,ROW_NUMBER() over (order by Date  desc) as RowNumNew from #temp5   order by RowNumNew -- where RowNumNew > @FirstRec and RowNumNew <= @LastRec  
end       
  

GO


