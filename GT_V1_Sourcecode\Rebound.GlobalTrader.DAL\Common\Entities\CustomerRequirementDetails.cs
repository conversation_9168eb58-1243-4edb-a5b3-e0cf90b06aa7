﻿/*
 Marker     Changed By      Changed Date        Remarks
 [001]      <PERSON><PERSON><PERSON>     27-Nov-2018         Show customer requirement all info in tree view.
 */
/*

Marker     changed by      date         Remarks
[001]      <PERSON><PERSON><PERSON>   04-Dec-2018    [REB-13584]: Link Requirement to SO Line
[002]      <PERSON><PERSON> 17-Dec-2018    Customer Requirement Import
[003]      <PERSON><PERSON>   18-Mar-2019    Showing Records Processed and Records Remaining.
[004]      Tanbirakhtar    13/06/2023    RP-37  Filter Added for industry type and Also added industry type on grid of UI
[004]		<PERSON>	29-08-2023	RP-2227 (AS6081) Counterfeit Electronic Part

*/
using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

namespace Rebound.GlobalTrader.DAL {
	
	public class CustomerRequirementDetails {
		
		#region Constructors
		
		public CustomerRequirementDetails() { }
		
		#endregion
		
		#region Properties
        public System.Boolean isIncludeAltPart { get; set; }
		/// <summary>
		/// CustomerRequirementId (from Table)
		/// </summary>
		public System.Int32 CustomerRequirementId { get; set; }
		/// <summary>
		/// CustomerRequirementNumber (from Table)
		/// </summary>
		public System.Int32 CustomerRequirementNumber { get; set; }
        public System.String IHSProduct { get; set; }
        public System.String IHSHTSCode { get; set; }
        public System.Int32? IHSProductNo { get; set; }
        public System.String IHSProductName { get; set; }
        public System.String IHSDutyCode { get; set; }
        public System.String PurchaseRequestId { get; set; }
        public System.String PurchaseRequestNumber { get; set; }
        public System.String ECCNCode { get; set; }
        public System.String REQStatusName { get; set; }
        public System.Int32? ParentRequirementNo { get; set; }
        public System.Int32? ParentRequirementId { get; set; }
       
        /// <summary>
        /// ClientNo (from Table)
        /// </summary>
        public System.Int32 ClientNo { get; set; }
        /// <summary>
        /// SupportTeamMemberNo
        /// </summary>
        public System.Int32? SupportTeamMemberNo { get; set; }
        /// <summary>
        /// SupportTeamMemberName
        /// </summary>
        public System.String SupportTeamMemberName { get; set; }
		/// <summary>
		/// FullPart (from Table)
		/// </summary>
		public System.String FullPart { get; set; }
		/// <summary>
		/// Part (from Table)
		/// </summary>
		public System.String Part { get; set; }
		/// <summary>
		/// ManufacturerNo (from Table)
		/// </summary>
		public System.Int32? ManufacturerNo { get; set; }
		/// <summary>
		/// DateCode (from Table)
		/// </summary>
		public System.String DateCode { get; set; }
		/// <summary>
		/// PackageNo (from Table)
		/// </summary>
		public System.Int32? PackageNo { get; set; }
		/// <summary>
		/// Quantity (from Table)
		/// </summary>
		public System.Int32 Quantity { get; set; }
		/// <summary>
		/// Price (from Table)
		/// </summary>
		public System.Double Price { get; set; }
		/// <summary>
		/// CurrencyNo (from Table)
		/// </summary>
		public System.Int32? CurrencyNo { get; set; }
		/// <summary>
		/// ReceivedDate (from Table)
		/// </summary>
		public System.DateTime ReceivedDate { get; set; }
		/// <summary>
		/// Salesman (from Table)
		/// </summary>
		public System.Int32 Salesman { get; set; }
		/// <summary>
		/// DatePromised (from Table)
		/// </summary>
		public System.DateTime DatePromised { get; set; }
		/// <summary>
		/// Notes (from Table)
		/// </summary>
		public System.String Notes { get; set; }
		/// <summary>
		/// Instructions (from Table)
		/// </summary>
		public System.String Instructions { get; set; }
		/// <summary>
		/// Shortage (from Table)
		/// </summary>
		public System.Boolean Shortage { get; set; }
		/// <summary>
		/// CompanyNo (from Table)
		/// </summary>
		public System.Int32 CompanyNo { get; set; }
		/// <summary>
		/// ContactNo (from Table)
		/// </summary>
		public System.Int32 ContactNo { get; set; }
		/// <summary>
		/// Alternate (from Table)
		/// </summary>
		public System.Boolean Alternate { get; set; }
		/// <summary>
		/// OriginalCustomerRequirementNo (from Table)
		/// </summary>
		public System.Int32? OriginalCustomerRequirementNo { get; set; }
		/// <summary>
		/// ReasonNo (from Table)
		/// </summary>
		public System.Int32? ReasonNo { get; set; }
		/// <summary>
		/// ProductNo (from Table)
		/// </summary>
		public System.Int32? ProductNo { get; set; }
		/// <summary>
		/// CustomerPart (from Table)
		/// </summary>
		public System.String CustomerPart { get; set; }
		/// <summary>
		/// Closed (from Table)
		/// </summary>
		public System.Boolean Closed { get; set; }
		/// <summary>
		/// ROHS (from Table)
		/// </summary>
		public System.Byte? ROHS { get; set; }
		//Codestart[004]
		/// <summary>
		/// IndustryName(From table)
		/// </summary>
		public System.String IndustryName { get; set; }
		/// <summary>
		/// IndustryType(From table)
		/// </summary>

		public System.Int32? IndustryType { get; set; }
//CodeEnd[004]
		/// <summary>
		/// UpdatedBy (from Table)
		/// </summary>
		public System.Int32? UpdatedBy { get; set; }
		/// <summary>
		/// DLUP (from Table)
		/// </summary>
		public System.DateTime DLUP { get; set; }
		/// <summary>
		/// UsageNo (from Table)
		/// </summary>
		public System.Int32? UsageNo { get; set; }
		/// <summary>
		/// FullCustomerPart (from Table)
		/// </summary>
		public System.String FullCustomerPart { get; set; }
		/// <summary>
		/// BOM (from Table)
		/// </summary>
		public System.Boolean? BOM { get; set; }


		public System.DateTime? RequiredDate { get; set; }
		public System.String RequiredDateStatus { get; set; }

		public System.String ExpediteNotes { get; set; }
		/// <summary>
		/// BOMName (from Table)
		/// </summary>
		public System.String BOMName { get; set; }
        /// <summary>
		/// BomStatus (from Table)
		/// </summary>
		public System.String BomStatus { get; set; }
        
        /// <summary>
        /// PartWatch (from Table)
        /// </summary>
        public System.Boolean? PartWatch { get; set; }
		/// <summary>
		/// SalesmanName (from usp_select_Credit)
		/// </summary>
		public System.String SalesmanName { get; set; }
		/// <summary>
		/// ManufacturerCode (from usp_datalistnugget_CustomerRequirement)
		/// </summary>
		public System.String ManufacturerCode { get; set; }
		/// <summary>
		/// CompanyName (from usp_select_Credit)
		/// </summary>
		public System.String CompanyName { get; set; }
		/// <summary>
		/// ContactName (from usp_select_Credit)
		/// </summary>
		public System.String ContactName { get; set; }
		/// <summary>
		/// RowNum (from usp_datalistnugget_CustomerRequirement)
		/// </summary>
		public System.Int64? RowNum { get; set; }
		/// <summary>
		/// RowCnt (from usp_datalistnugget_CustomerRequirement)
		/// </summary>
		public System.Int32? RowCnt { get; set; }
		/// <summary>
		/// CurrencyCode (from usp_select_Credit)
		/// </summary>
		public System.String CurrencyCode { get; set; }
		/// <summary>
		/// DisplayStatus (from usp_select_CustomerRequirement)
		/// </summary>
		public System.String DisplayStatus { get; set; }
		/// <summary>
		/// DivisionNo (from Table)
		/// </summary>
		public System.Int32? DivisionNo { get; set; }
		/// <summary>
		/// TeamNo (from usp_select_Credit)
		/// </summary>
		public System.Int32? TeamNo { get; set; }
		/// <summary>
		/// CompanyOnStop (from usp_select_CustomerRequirement)
		/// </summary>
		public System.Boolean? CompanyOnStop { get; set; }
		/// <summary>
		/// CurrencyDescription (from usp_select_Credit)
		/// </summary>
		public System.String CurrencyDescription { get; set; }
		/// <summary>
		/// ProductName (from usp_select_CustomerRequirement)
		/// </summary>
		public System.String ProductName { get; set; }
		/// <summary>
		/// ProductDescription (from usp_select_CustomerRequirement)
		/// </summary>
		public System.String ProductDescription { get; set; }
		/// <summary>
		/// ManufacturerName (from usp_select_CustomerRequirement)
		/// </summary>
		public System.String ManufacturerName { get; set; }
		/// <summary>
		/// PackageName (from usp_select_CustomerRequirement)
		/// </summary>
		public System.String PackageName { get; set; }
		/// <summary>
		/// PackageDescription (from usp_select_CustomerRequirement)
		/// </summary>
		public System.String PackageDescription { get; set; }
		/// <summary>
		/// UsageName (from usp_select_CustomerRequirement)
		/// </summary>
		public System.String UsageName { get; set; }
		/// <summary>
		/// CustomerRequirementValue (from usp_select_CustomerRequirement)
		/// </summary>
		public System.Double CustomerRequirementValue { get; set; }
		/// <summary>
		/// ClosedReason (from usp_select_CustomerRequirement)
		/// </summary>
		public System.String ClosedReason { get; set; }
		/// <summary>
		/// DivisionName (from usp_select_Credit)
		/// </summary>
		public System.String DivisionName { get; set; }
		/// <summary>
		/// Status (from usp_selectAll_CustomerRequirement_open_for_Login)
		/// </summary>
		public System.String Status { get; set; }
		/// <summary>
		/// CreditLimit (from usp_selectAll_CustomerRequirement_open_for_Login)
		/// </summary>
		public System.Double? CreditLimit { get; set; }
		/// <summary>
		/// Balance (from usp_selectAll_CustomerRequirement_open_for_Login)
		/// </summary>
		public System.Double? Balance { get; set; }
		/// <summary>
		/// DaysOverdue (from usp_selectAll_CustomerRequirement_open_for_Login)
		/// </summary>
		public System.Int32? DaysOverdue { get; set; }
		/// <summary>
		/// ClientName (from usp_source_CustomerRequirement)
		/// </summary>
		public System.String ClientName { get; set; }
        /// <summary>
        /// Traceability
        /// </summary>
        public System.Boolean? Traceability { get; set; }

        /// <summary>
        /// BOMNo (from Table)
        /// </summary>
        public System.Int32? BOMNo { get; set; }
        /// <summary>
        /// BOMHeader
        /// </summary>
        public System.String BOMHeader { get; set; }
        /// <summary>
        /// BOMCode
        /// </summary>
        public System.String BOMCode { get; set; }
        public string BOMFullName { get; set; }
        public System.Int32? POHubReleaseBy { get; set; }
        public System.Int32? RequestToPOHubBy { get; set; }
        public int? SourcingResultId { get; set; }
        /// <summary>
        /// Price (from Table)
        /// </summary>
        public System.Double ConvertedTargetValue { get; set; }
        public System.String BOMCurrencyCode { get; set; }
        public int? PurchaseQuoteNumber { get; set; }
        public int? PurchaseQuoteId { get; set; }
        public System.String BOMStatus { get; set; }
        public System.Double PHPrice { get; set; }
        public System.String PHCurrencyCode { get; set; }
        public int? POHubCompany { get; set; }
        public System.Boolean? FactorySealed { get; set; }
        public System.String MSL { get; set; }
        public System.Int32 AllSorcingHasDelDate { get; set; }
        public int AllSorcingHasProduct { get; set; }
        public System.Boolean? AS9120 { get; set; }
        public System.Int32 SourcingResult { get; set; }
        public int? SourcingResultNo { get; set; }

        public System.Boolean? PQA { get; set; }
        public System.Boolean? Obsolete { get; set; }
        public System.Boolean? LastTimeBuy { get; set; }
        public System.Boolean? RefirbsAcceptable { get; set; }
        public System.Boolean? TestingRequired { get; set; }
        public System.Double? TargetSellPrice { get; set; }
        public System.Double? CompetitorBestOffer { get; set; }
        public System.DateTime? CustomerDecisionDate { get; set; }
        public System.DateTime? RFQClosingDate { get; set; }
        public System.Int32? QuoteValidityRequired { get; set; }
        public System.Int32? Type { get; set; }
        public System.Boolean? OrderToPlace { get; set; }
        public System.Int32? RequirementforTraceability { get; set; }
        public System.String QuoteValidityText { get; set; }
        public System.String ReqTypeText { get; set; }
        public System.String ReqForTraceabilityText { get; set; }
        public System.Boolean? IsGlobalCurrencySame { get; set; }
        public System.Boolean? HasClientSourcingResult { get; set; }
        public System.Boolean? HasHubSourcingResult { get; set; }
        public System.String EAU { get; set; }
        public System.Int32? ClientGlobalCurrencyNo { get; set; }
        public System.Int32? ReqGlobalCurrencyNo { get; set; }
        public System.String ClientCurrencyCode { get; set; }
        public System.Int32? ClientCurrencyNo { get; set; }
        public System.String ReqNotes { get; set; }
        /// ClientCode
        /// </summary>
        public System.String ClientCode { get; set; }
        /// <summary>
        public Boolean? IsNoBid { get; set; }
        public System.String NoBidNotes { get; set; }
        /// <summary>
        /// IsCurrencyInSameFaimly
        /// </summary>
        public System.Boolean? IsCurrencyInSameFaimly { get; set; }
        public System.Boolean? AlternativesAccepted { get; set; }
        public System.Boolean? RepeatBusiness { get; set; }
        public System.DateTime? DateRequestToPOHub { get; set; }
        public System.Int32 POCurrencyNo { get; set; }

        public System.DateTime? ExpeditDate { get; set; }
        public System.Int32 UpdateByPH { get; set; }
        public System.Boolean? ProductInactive { get; set; }
        public System.String DutyCode { get; set; }
        public System.Double? DutyRate { get; set; }
        public System.String ValidateMessage { get; set; }
        public System.Int32? MSLLevelNo { get; set; }
        public System.Boolean? IsProdHaz { get; set; }
        public System.Double? TotalValue { get; set; }
        public System.Double? TotalInBase { get; set; }
        /// <summary>
        /// AlternateStatus (from Table)
        /// </summary>
        public System.Byte? AlternateStatus { get; set; }
        //[001] start
        public string SalesOrderNumber { get; set; }
        //[001] end
        
        //[002] start
        public System.Int32 ClientBOMId { get; set; }
        public System.String ClientBOMCode { get; set; }
        public string ClientBOMName { get; set; }
        public System.DateTime? ImportDateFrom { get; set; }
        public System.DateTime? ImportDateTo { get; set; }
        public System.DateTime? ImportDate { get; set; }
        public System.Int32 NoOfRequirements { get; set; }
        //[002] end

        //[003] start
        public System.Int32? RecordsProcessed { get; set; }
        public System.Int32? RecordsRemaining { get; set; }
        //[003] end

        //[001] start
        public System.Int32 ID{ get; set; }
        public System.String Number { get; set; }
        public System.String ResultType { get; set; }
        //[001] end
        public System.Int32? TotalCount { get; set; }

        /// <summary>
        /// CountryOfOrigin
        /// </summary>
        public System.String CountryOfOrigin { get; set; }
        /// <summary>
        /// CountryOfOriginNo
        /// </summary>
        public System.Int32? CountryOfOriginNo { get; set; }
        /// <summary>
        /// LifeCycleStage
        /// </summary>
        public System.String LifeCycleStage { get; set; }
        /// <summary>
        /// HTSCode
        /// </summary>
        public System.String HTSCode { get; set; }
        /// <summary>
        /// AveragePrice
        /// </summary>
        public System.Double? AveragePrice { get; set; }
        /// <summary>
        /// Packaging
        /// </summary>
        public System.String Packaging { get; set; }
        /// <summary>
        /// PackagingSize
        /// </summary>
        public System.String PackagingSize { get; set; }
        public System.String Descriptions { get; set; }
        public System.String IHSCurrencyCode { get; set; }
        public System.Boolean? IsOrderViaIPOonly { get; set; }
		public System.Boolean? IsRestManufaturer { get; set; }
        public System.Int32? PartEditStatus { get; set; }
        public System.String IHSECCNCodeDefination { get; set; }
        

		public System.Boolean? PriceIssueBuyAndSell { get; set; }
        /// <summary>
        /// IsRestrictedProduct
        /// </summary>
        public System.Boolean? IsRestrictedProduct { get; set; }
        /// <summary>
        /// ECCNNotify
        /// </summary>
        public System.Boolean? ECCNNotify { get; set; }
        /// <summary>
        /// EccnSubject
        /// </summary>
        public System.String EccnSubject { get; set; }
        /// <summary>
        /// EccnMessage
        /// </summary>
        public System.String EccnMessage { get; set; }
		public System.String BlankECCNCode { get; set; }
		public System.String StockAvailableDetail { get; set; }
        public System.String WarningMessage { get; set; }

        public System.String StockAlerturl { get; set; }
        public System.Int32? InStock { get; set; }
        public System.Int32? OnOrder { get; set; }
        public System.Int32? Allocated { get; set; }
        public System.Int32? Available { get; set; }
        public System.String UserName { get; set; }
        public System.String ToEmail { get; set; }
        public System.Int32? stockId { get; set; }
        public System.String IsAs6081Required { get; set; }
        public System.String AssignedTo { get; set; }
        public System.String AssigneeId { get; set; }
        public System.Boolean? PartWatchHUBIPO { get; set; }
		/// <summary>
		/// ReverseLogisticid
		/// </summary>
		public System.Int32 ReverseLogisticid { get; set; }
		/// <summary>
		/// Partcount
		/// </summary>
		public System.Int32 Partcount { get; set; }

        public System.Boolean? AS6081 { get; set; } //[004]
		/// <summary>
		/// PurchasingNotes
		/// </summary>
		public System.String PurchasingNotes { get; set; }
		public System.Boolean? IsPDFAvailable { get; set; }
		public System.Int32 IHSPartsId { get; set; }
		public System.Double IHSAveragePrice {  get; set; }
		#endregion

		public System.String LyticaManufacturerRef { get; set; }
		public System.Double LyticaAveragePrice { get; set; }
		public System.Double LyticaTargetPrice { get; set; }
		public System.Double LyticaMarketLeading { get; set; }
		public System.String CompanyAdvisoryNotes { get; set; }
		public System.String MfrAdvisoryNotes { get; set; }
		public System.Double? NewOfferPriceFromProspective { get; set; }
		public string CustomerRefNo { get; set; }

    }





}
