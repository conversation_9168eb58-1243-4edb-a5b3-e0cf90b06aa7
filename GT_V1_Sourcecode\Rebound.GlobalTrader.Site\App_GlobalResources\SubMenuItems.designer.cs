//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option or rebuild the Visual Studio project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.VisualStudio.Web.Application.StronglyTypedResourceProxyBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class SubMenuItems {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal SubMenuItems() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Resources.SubMenuItems", global::System.Reflection.Assembly.Load("App_GlobalResources"));
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Address.
        /// </summary>
        internal static string Address {
            get {
                return ResourceManager.GetString("Address", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Company.
        /// </summary>
        internal static string Company {
            get {
                return ResourceManager.GetString("Company", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Company Letters.
        /// </summary>
        internal static string CompanyLetters {
            get {
                return ResourceManager.GetString("CompanyLetters", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Company Profile.
        /// </summary>
        internal static string CompanyProfile {
            get {
                return ResourceManager.GetString("CompanyProfile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Consolidate Email SO.
        /// </summary>
        internal static string ConsolidateEmailSO {
            get {
                return ResourceManager.GetString("ConsolidateEmailSO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Consolidate Print SO.
        /// </summary>
        internal static string ConsolidatePrintSO {
            get {
                return ResourceManager.GetString("ConsolidatePrintSO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contact.
        /// </summary>
        internal static string Contact {
            get {
                return ResourceManager.GetString("Contact", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contact Letters.
        /// </summary>
        internal static string ContactLetters {
            get {
                return ResourceManager.GetString("ContactLetters", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Credit Note.
        /// </summary>
        internal static string CreditNote {
            get {
                return ResourceManager.GetString("CreditNote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Credit Notes.
        /// </summary>
        internal static string CreditNotes {
            get {
                return ResourceManager.GetString("CreditNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer RMA.
        /// </summary>
        internal static string CRMA {
            get {
                return ResourceManager.GetString("CRMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer RMA.
        /// </summary>
        internal static string CustomerRMA {
            get {
                return ResourceManager.GetString("CustomerRMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer RMAs.
        /// </summary>
        internal static string CustomerRMAs {
            get {
                return ResourceManager.GetString("CustomerRMAs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer Template.
        /// </summary>
        internal static string CustomTemplate {
            get {
                return ResourceManager.GetString("CustomTemplate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Data Entry.
        /// </summary>
        internal static string DataEntry {
            get {
                return ResourceManager.GetString("DataEntry", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Debit Note.
        /// </summary>
        internal static string DebitNote {
            get {
                return ResourceManager.GetString("DebitNote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Debit Notes.
        /// </summary>
        internal static string DebitNotes {
            get {
                return ResourceManager.GetString("DebitNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email.
        /// </summary>
        internal static string Email {
            get {
                return ResourceManager.GetString("Email", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email Certificate Of Conformity.
        /// </summary>
        internal static string EmailCertificateOfConformance {
            get {
                return ResourceManager.GetString("EmailCertificateOfConformance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email Commercial Invoice.
        /// </summary>
        internal static string EmailCommercialInvoice {
            get {
                return ResourceManager.GetString("EmailCommercialInvoice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email Credit.
        /// </summary>
        internal static string EmailCreditNote {
            get {
                return ResourceManager.GetString("EmailCreditNote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email Credit (XML Format).
        /// </summary>
        internal static string EmailCreditXML {
            get {
                return ResourceManager.GetString("EmailCreditXML", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email Credit (HTML Format).
        /// </summary>
        internal static string EmailCRHTML {
            get {
                return ResourceManager.GetString("EmailCRHTML", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email CRMA.
        /// </summary>
        internal static string EmailCRMA {
            get {
                return ResourceManager.GetString("EmailCRMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email Debit (HTML Format).
        /// </summary>
        internal static string EmailDBHTML {
            get {
                return ResourceManager.GetString("EmailDBHTML", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email Debit.
        /// </summary>
        internal static string EmailDebitNote {
            get {
                return ResourceManager.GetString("EmailDebitNote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email HUB Debit.
        /// </summary>
        internal static string EmailHUBDebit {
            get {
                return ResourceManager.GetString("EmailHUBDebit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email HUB Debit (HTML Format).
        /// </summary>
        internal static string EmailHUBDebitHtml {
            get {
                return ResourceManager.GetString("EmailHUBDebitHtml", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email HUB SRMA.
        /// </summary>
        internal static string EmailHUBSRMA {
            get {
                return ResourceManager.GetString("EmailHUBSRMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email Invoice.
        /// </summary>
        internal static string EmailInvoice {
            get {
                return ResourceManager.GetString("EmailInvoice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email Invoice include CofC.
        /// </summary>
        internal static string EmailInvoiceWithCoC {
            get {
                return ResourceManager.GetString("EmailInvoiceWithCoC", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email IPO.
        /// </summary>
        internal static string EmailIPO {
            get {
                return ResourceManager.GetString("EmailIPO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email Packing Slip.
        /// </summary>
        internal static string EmailPackingSlip {
            get {
                return ResourceManager.GetString("EmailPackingSlip", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email Packing Slip include invoice and CofC.
        /// </summary>
        internal static string EmailPackingSlipWithCoc {
            get {
                return ResourceManager.GetString("EmailPackingSlipWithCoc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email PO.
        /// </summary>
        internal static string EmailPO {
            get {
                return ResourceManager.GetString("EmailPO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email PO (HTML format).
        /// </summary>
        internal static string EmailPOHTML {
            get {
                return ResourceManager.GetString("EmailPOHTML", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email Pro Forma.
        /// </summary>
        internal static string EmailProFormaInvoice {
            get {
                return ResourceManager.GetString("EmailProFormaInvoice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email Quote (HTML Format).
        /// </summary>
        internal static string EmailQOHTML {
            get {
                return ResourceManager.GetString("EmailQOHTML", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email Quote.
        /// </summary>
        internal static string EmailQuote {
            get {
                return ResourceManager.GetString("EmailQuote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email SO.
        /// </summary>
        internal static string EmailSO {
            get {
                return ResourceManager.GetString("EmailSO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email SRMA.
        /// </summary>
        internal static string EmailSRMA {
            get {
                return ResourceManager.GetString("EmailSRMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Export To Excel.
        /// </summary>
        internal static string ExportToExcel {
            get {
                return ResourceManager.GetString("ExportToExcel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Goods In.
        /// </summary>
        internal static string GoodsIN {
            get {
                return ResourceManager.GetString("GoodsIN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invoice.
        /// </summary>
        internal static string Invoice {
            get {
                return ResourceManager.GetString("Invoice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invoices.
        /// </summary>
        internal static string Invoices {
            get {
                return ResourceManager.GetString("Invoices", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Letter.
        /// </summary>
        internal static string Letter {
            get {
                return ResourceManager.GetString("Letter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lot.
        /// </summary>
        internal static string Lot {
            get {
                return ResourceManager.GetString("Lot", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Manufacturer.
        /// </summary>
        internal static string Manufacturer {
            get {
                return ResourceManager.GetString("Manufacturer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Packing Slip.
        /// </summary>
        internal static string PackingSlip {
            get {
                return ResourceManager.GetString("PackingSlip", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Print POR.
        /// </summary>
        internal static string POR {
            get {
                return ResourceManager.GetString("POR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Print.
        /// </summary>
        internal static string Print {
            get {
                return ResourceManager.GetString("Print", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Print Certificate Of Conformity.
        /// </summary>
        internal static string PrintCertificateOfConformance {
            get {
                return ResourceManager.GetString("PrintCertificateOfConformance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Print Commercial Invoice.
        /// </summary>
        internal static string PrintCommercialInvoice {
            get {
                return ResourceManager.GetString("PrintCommercialInvoice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Print Credit.
        /// </summary>
        internal static string PrintCreditNote {
            get {
                return ResourceManager.GetString("PrintCreditNote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Print Credit (XML Format).
        /// </summary>
        internal static string PrintCreditXML {
            get {
                return ResourceManager.GetString("PrintCreditXML", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Print CRMA.
        /// </summary>
        internal static string PrintCRMA {
            get {
                return ResourceManager.GetString("PrintCRMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Print Debit.
        /// </summary>
        internal static string PrintDebitNote {
            get {
                return ResourceManager.GetString("PrintDebitNote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Division Header Log.
        /// </summary>
        internal static string PrintDivisionHeaaderLog {
            get {
                return ResourceManager.GetString("PrintDivisionHeaaderLog", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Print HUB Debit.
        /// </summary>
        internal static string PrintHUBDebit {
            get {
                return ResourceManager.GetString("PrintHUBDebit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Print HUB SRMA.
        /// </summary>
        internal static string PrintHUBSRMA {
            get {
                return ResourceManager.GetString("PrintHUBSRMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Print Invoice.
        /// </summary>
        internal static string PrintInvoice {
            get {
                return ResourceManager.GetString("PrintInvoice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Print Invoice include CofC.
        /// </summary>
        internal static string PrintInvoiceWithCoC {
            get {
                return ResourceManager.GetString("PrintInvoiceWithCoC", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Print IPO.
        /// </summary>
        internal static string PrintIPO {
            get {
                return ResourceManager.GetString("PrintIPO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Log.
        /// </summary>
        internal static string PrintLog {
            get {
                return ResourceManager.GetString("PrintLog", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Print Packing Slip.
        /// </summary>
        internal static string PrintPackingSlip {
            get {
                return ResourceManager.GetString("PrintPackingSlip", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Print Packing Slip include invoice and CofC.
        /// </summary>
        internal static string PrintPackingSlipWithCoc {
            get {
                return ResourceManager.GetString("PrintPackingSlipWithCoc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Print PO.
        /// </summary>
        internal static string PrintPO {
            get {
                return ResourceManager.GetString("PrintPO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Print Pro Forma.
        /// </summary>
        internal static string PrintProFormaInvoice {
            get {
                return ResourceManager.GetString("PrintProFormaInvoice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Print Quote.
        /// </summary>
        internal static string PrintQuote {
            get {
                return ResourceManager.GetString("PrintQuote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Print SO.
        /// </summary>
        internal static string PrintSO {
            get {
                return ResourceManager.GetString("PrintSO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Print SO Report.
        /// </summary>
        internal static string PrintSOReport {
            get {
                return ResourceManager.GetString("PrintSOReport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Print SRMA.
        /// </summary>
        internal static string PrintSRMA {
            get {
                return ResourceManager.GetString("PrintSRMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchase Order.
        /// </summary>
        internal static string PurchaseOrder {
            get {
                return ResourceManager.GetString("PurchaseOrder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchase Orders.
        /// </summary>
        internal static string PurchaseOrders {
            get {
                return ResourceManager.GetString("PurchaseOrders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchase Requisitions.
        /// </summary>
        internal static string PurchaseRequisitions {
            get {
                return ResourceManager.GetString("PurchaseRequisitions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quotation.
        /// </summary>
        internal static string Quotation {
            get {
                return ResourceManager.GetString("Quotation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quotations.
        /// </summary>
        internal static string Quotations {
            get {
                return ResourceManager.GetString("Quotations", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Received Customer RMAs.
        /// </summary>
        internal static string ReceivedCustomerRMAs {
            get {
                return ResourceManager.GetString("ReceivedCustomerRMAs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Received Purchase Orders.
        /// </summary>
        internal static string ReceivedPurchaseOrders {
            get {
                return ResourceManager.GetString("ReceivedPurchaseOrders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Requirement.
        /// </summary>
        internal static string Requirement {
            get {
                return ResourceManager.GetString("Requirement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Requirements.
        /// </summary>
        internal static string Requirements {
            get {
                return ResourceManager.GetString("Requirements", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sales Order.
        /// </summary>
        internal static string SalesOrder {
            get {
                return ResourceManager.GetString("SalesOrder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sales Orders.
        /// </summary>
        internal static string SalesOrders {
            get {
                return ResourceManager.GetString("SalesOrders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Schedule Call.
        /// </summary>
        internal static string ScheduleCall {
            get {
                return ResourceManager.GetString("ScheduleCall", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Scheduled Calls.
        /// </summary>
        internal static string ScheduledCalls {
            get {
                return ResourceManager.GetString("ScheduledCalls", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Service.
        /// </summary>
        internal static string Service {
            get {
                return ResourceManager.GetString("Service", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipped Supplier RMAs.
        /// </summary>
        internal static string ShippedSupplierRMAs {
            get {
                return ResourceManager.GetString("ShippedSupplierRMAs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier RMA.
        /// </summary>
        internal static string SRMA {
            get {
                return ResourceManager.GetString("SRMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stock.
        /// </summary>
        internal static string Stock {
            get {
                return ResourceManager.GetString("Stock", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stock Item.
        /// </summary>
        internal static string StockItem {
            get {
                return ResourceManager.GetString("StockItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier Invoice.
        /// </summary>
        internal static string SupplierInvoice {
            get {
                return ResourceManager.GetString("SupplierInvoice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier RMA.
        /// </summary>
        internal static string SupplierRMA {
            get {
                return ResourceManager.GetString("SupplierRMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier RMAs.
        /// </summary>
        internal static string SupplierRMAs {
            get {
                return ResourceManager.GetString("SupplierRMAs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Transfer Data.
        /// </summary>
        internal static string TransferData {
            get {
                return ResourceManager.GetString("TransferData", resourceCulture);
            }
        }
    }
}
