///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.StarRating_Add = function(element) {
    Rebound.GlobalTrader.Site.Controls.Forms.StarRating_Add.initializeBase(this, [element]);
    this._intItemID = -1;
};

Rebound.GlobalTrader.Site.Controls.Forms.StarRating_Add.prototype = {
    initialize: function() {
        Rebound.GlobalTrader.Site.Controls.Forms.StarRating_Add.callBaseMethod(this, "initialize");
        this.addShown(Function.createDelegate(this, this.formShown));
    },

    dispose: function() {
        if (this.isDisposed) return;
        this._intItemID = null;
        Rebound.GlobalTrader.Site.Controls.Forms.StarRating_Add.callBaseMethod(this, "dispose");
    },

    formShown: function() {
        if (this._blnFirstTimeShown) {
            this.addSave(Function.createDelegate(this, this.saveClicked));
            this._strPathToData = "controls/SetupNuggets/StarRating";
            this._strDataObject = "StarRating";
        }
        this.setFormFieldsToDefaults();
    },

    saveClicked: function() {
        this.resetFormFields();
        if (this.validateForm()) this.saveEdit();
    },

    validateForm: function() {
        var blnOK = this.autoValidateFields();
        return blnOK;
    },

    saveEdit: function () {
        if (!this.validateForm())
            return;
        if (this.getFieldValue("ctlNumOfPO") == 0) {
            this.setFieldInError(null, true, "Please input number greater than 0!", this.getField("ctlNumOfPO"));
            this.showError(true);
        } else {
            this.showError(false);
            var obj = new Rebound.GlobalTrader.Site.Data();
            obj.set_PathToData(this._strPathToData);
            obj.set_DataObject(this._strDataObject);
            obj.set_DataAction("AddNew");
            obj.addParameter("NumOfPO", this.getFieldValue("ctlNumOfPO"));
            obj.addDataOK(Function.createDelegate(this, this.saveEditOK));
            obj.addError(Function.createDelegate(this, this.saveEditError));
            obj.addTimeout(Function.createDelegate(this, this.saveEditError));
            $R_DQ.addToQueue(obj);
            $R_DQ.processQueue();
            obj = null;
        }
    },

    saveEditOK: function () {
        this.onSaveComplete();
    },

    saveEditError: function (args) {
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },
};

Rebound.GlobalTrader.Site.Controls.Forms.StarRating_Add.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.StarRating_Add", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
