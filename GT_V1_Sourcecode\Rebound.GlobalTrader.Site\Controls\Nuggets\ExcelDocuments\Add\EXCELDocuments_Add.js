Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.EXCELDocuments_Add=function(n){Rebound.GlobalTrader.Site.Controls.Forms.EXCELDocuments_Add.initializeBase(this,[n]);this._intSectionID=-1;this._ctlFileUpload=null;this._strSectionName=null;this._strExcel=null};Rebound.GlobalTrader.Site.Controls.Forms.EXCELDocuments_Add.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.EXCELDocuments_Add.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown))},dispose:function(){this.isDisposed||(this._ctlFileUpload&&this._ctlFileUpload.dispose(),this._intSectionID=-1,this._ctlFileUpload=null,this._strSectionName=null,Rebound.GlobalTrader.Site.Controls.Forms.EXCELDocuments_Add.callBaseMethod(this,"dispose"))},formShown:function(){this._blnFirstTimeShown&&(this.addSave(Function.createDelegate(this,this.saveClicked)),this._ctlFileUpload=$find(this.getField("ctlFile").ControlID),this._ctlFileUpload._intTimeoutSeconds=60,this._ctlFileUpload._strSectionName=this._strSectionName,this._ctlFileUpload._strExcel=this._strExcel,this._ctlFileUpload.addUploadComplete(Function.createDelegate(this,this.uploadComplete)),this._ctlFileUpload.addUploadFailed(Function.createDelegate(this,this.uploadFatalError)),this._ctlFileUpload.addTimeout(Function.createDelegate(this,this.uploadFatalError)),this._ctlFileUpload.addFileTooBig(Function.createDelegate(this,this.uploadError)),this._ctlFileUpload.addFileNotAllowedType(Function.createDelegate(this,this.uploadError)));this.setFormFieldsToDefaults();this._ctlFileUpload.reset();this.showInnerContent(!0)},saveClicked:function(){this.validateForm()&&(this.showSaving(!0),this._ctlFileUpload.doUpload())},uploadError:function(){this.showError(!0);this.showInnerContent(!0);this.setFieldInError("ctlFile",!0,this._ctlFileUpload._strErrorMessage)},uploadFatalError:function(){this.showNuggetError(!0,this._ctlFileUpload._strErrorMessage);this.showInnerContent(!0);this._ctlFileUpload.reset()},uploadComplete:function(){this.saveEdit()},saveEdit:function(){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/EXCELDocuments");n.set_DataObject("EXCELDocuments");n.set_DataAction("AddNew");n._intTimeoutMilliseconds=3e4;n.addParameter("id",this._intSectionID);n.addParameter("Caption",this.getFieldValue("ctlCaption"));n.addParameter("TempFile",this._ctlFileUpload._strUploadedFilename);n.addParameter("Section",this._strSectionName);n.addDataOK(Function.createDelegate(this,this.saveEditComplete));n.addError(Function.createDelegate(this,this.saveEditError));n.addTimeout(Function.createDelegate(this,this.saveEditError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},saveEditError:function(n){this._strErrorMessage=n._errorMessage;this.showNuggetError(!0,this._strErrorMessage);this.onSaveError()},saveEditComplete:function(n){n._result.Result==!0?this.onSaveComplete():(n._result.Message&&(this._strErrorMessage=n._result.Message),this.onSaveError())},validateForm:function(){this.onValidate();return this.autoValidateFields()}};Rebound.GlobalTrader.Site.Controls.Forms.EXCELDocuments_Add.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.EXCELDocuments_Add",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);