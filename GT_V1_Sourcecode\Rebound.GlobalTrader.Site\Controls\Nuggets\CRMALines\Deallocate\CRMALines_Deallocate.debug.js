///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.CRMALines_Deallocate = function(element) { 
	Rebound.GlobalTrader.Site.Controls.Forms.CRMALines_Deallocate.initializeBase(this, [element]);
	this._aryLineIDs = [];
	this._intCRMAID = 0;
};

Rebound.GlobalTrader.Site.Controls.Forms.CRMALines_Deallocate.prototype = {

	get_intCRMAID: function() { return this._intCRMAID; }, set_intCRMAID: function(v) { if (this._intCRMAID !== v)  this._intCRMAID = v; }, 
	get_aryLineIDs: function() { return this._aryLineIDs; }, set_aryLineIDs: function(value) { if (this._aryLineIDs !== value)  this._aryLineIDs = value; }, 

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.Forms.CRMALines_Deallocate.callBaseMethod(this, "initialize");
		this.addShown(Function.createDelegate(this, this.formShown));
	},

	dispose: function() { 
		if (this.isDisposed) return;
		this._aryLineIDs = null;
		this._intCRMAID = null;
		Rebound.GlobalTrader.Site.Controls.Forms.CRMALines_Deallocate.callBaseMethod(this, "dispose");
	},

	formShown: function() {
		if (this._blnFirstTimeShown) {
			this._ctlConfirm = this.getFieldComponent("ctlConfirm");
			this._ctlConfirm.addClickYesEvent(Function.createDelegate(this, this.yesClicked));
			this._ctlConfirm.addClickNoEvent(Function.createDelegate(this, this.noClicked));
		}
	},
	
	setFieldsFromHeader: function(strNo, strCustomer) {
		this.setFieldValue("ctlCustomerRMA", strNo);
		this.setFieldValue("ctlCustomer", strCustomer);
	},

	yesClicked: function() {
		this.showSaving(true);
		var obj = new Rebound.GlobalTrader.Site.Data();
		obj.set_PathToData("Controls/Nuggets/CRMALines");
		obj.set_DataObject("CRMALines");
		obj.set_DataAction("DeleteAllocation");
		obj.addParameter("LineIDs", $R_FN.arrayToSingleString(this._aryLineIDs));
		obj.addDataOK(Function.createDelegate(this, this.saveComplete));
		obj.addError(Function.createDelegate(this, this.saveError));
		obj.addTimeout(Function.createDelegate(this, this.saveError));
		$R_DQ.addToQueue(obj);
		$R_DQ.processQueue();
		obj = null;
		this.onSave();
	},

	noClicked: function() {
		this.showSaving(false);
		this.onNotConfirmed();
	},

	saveError: function(args) {
		this.showSaving(false);
		this._strErrorMessage = args._errorMessage;
		this.onSaveError();
	},

	saveComplete: function(args) {
		this.showSaving(false);
		if (args._result.Result == true) {
			this.onSaveComplete();
		} else {
			this._strErrorMessage = args._errorMessage;
			this.onSaveError();
		}
	}
	
};

Rebound.GlobalTrader.Site.Controls.Forms.CRMALines_Deallocate.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CRMALines_Deallocate", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
