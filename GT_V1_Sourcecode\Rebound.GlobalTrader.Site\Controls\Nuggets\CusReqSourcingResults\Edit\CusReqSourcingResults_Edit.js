Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.CusReqSourcingResults_Edit=function(n){Rebound.GlobalTrader.Site.Controls.Forms.CusReqSourcingResults_Edit.initializeBase(this,[n]);this._intCustomerRequirementID=-1;this._intSourcingResultID=-1;this._intSupplierId=-1;this._intCurrencyNo=-1;this._supplierAdvisoryNotes="";this._mfrAdvisoryNotes=""};Rebound.GlobalTrader.Site.Controls.Forms.CusReqSourcingResults_Edit.prototype={get_intCustomerRequirementID:function(){return this._intCustomerRequirementID},set_intCustomerRequirementID:function(n){this._intCustomerRequirementID!==n&&(this._intCustomerRequirementID=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.CusReqSourcingResults_Edit.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown))},formShown:function(){this._blnFirstTimeShown&&this.addSave(Function.createDelegate(this,this.saveClicked));this._intSupplierId=-1;this.showField("ctlPartWatchMatch",!1);this.setFormFieldsToDefaults();this.getData();$find(this.getField("ctlSupplier").ControlID)&&$find(this.getField("ctlSupplier").ControlID)._aut.addSelectionMadeEvent(Function.createDelegate(this,this.supplierChangeEvent));$find(this.getField("ctlManufacturer").ControlID)&&$find(this.getField("ctlManufacturer").ControlID)._aut.addSelectionMadeEvent(Function.createDelegate(this,this.getMfrNotes));$find(this.getField("ctlManufacturer").ControlID)&&$find(this.getField("ctlManufacturer").ControlID)._aut.addSelectionMadeEvent(Function.createDelegate(this,this.getMfrNotes));$find(this.getField("ctlSupplier").ControlID)&&$find(this.getField("ctlSupplier").ControlID)._aut.addSelectionMadeEvent(Function.createDelegate(this,this.getSupplierNotes))},dispose:function(){this.isDisposed||(this._intCustomerRequirementID=null,this._intSourcingResultID=null,this._supplierAdvisoryNotes=null,this._mfrAdvisoryNotes=null,Rebound.GlobalTrader.Site.Controls.Forms.CusReqSourcingResults_Edit.callBaseMethod(this,"dispose"))},getData:function(){this.showLoading(!0);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/CusReqSourcingResults");n.set_DataObject("CusReqSourcingResults");n.set_DataAction("GetItem");n.addParameter("id",this._intSourcingResultID);n.addDataOK(Function.createDelegate(this,this.getDataComplete));n.addError(Function.createDelegate(this,this.finishShowForm));n.addTimeout(Function.createDelegate(this,this.finishShowForm));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getDataComplete:function(n){var t=n._result;this.setFieldValue("ctlPartNo",$R_FN.setCleanTextValue(t.Part));this.setFieldValue("ctlDateCode",$R_FN.setCleanTextValue(t.DateCode));this.setFieldValue("ctlQuantity",t.Quantity);this.setFieldValue("ctlPrice",t.Price);this.setFieldValue("ctlPackage",t.PackageNo,null,t.PackageDescription);this.setFieldValue("ctlProduct",t.ProductNo,null,t.ProductDescription);this.setFieldValue("ctlManufacturer",t.MfrNo,null,$R_FN.setCleanTextValue(t.Mfr));this.setFieldValue("ctlOfferStatus",t.OfferStatus);this.setFieldValue("ctlROHS",t.ROHS);this.setFieldValue("ctlNotes",t.Notes);this.setFieldValue("ctlSupplier",t.SupplierNo,null,$R_FN.setCleanTextValue(t.Supplier));this._intCurrencyNo=t.CurrencyNo;t.PartWatchMatch==!0?(this.showField("ctlPartWatchMatch",!0),this.setFieldValue("ctlPartWatchMatch",t.PartWatchMatch)):this.showField("ctlPartWatchMatch",!1);this.finishShowForm();this.setFieldValue("ctlMSL",t.MSLLevelNo)},finishShowForm:function(){this.showLoading(!1);this.showInnerContent(!0);this.getFieldDropDownData("ctlSupplier");this.getFieldDropDownData("ctlROHS");this.getFieldDropDownData("ctlOfferStatus");this.getFieldDropDownData("ctlMSL")},saveClicked:function(){if(this.validateForm()){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/CusReqSourcingResults");n.set_DataObject("CusReqSourcingResults");n.set_DataAction("SaveEdit");n.addParameter("id",this._intSourcingResultID);n.addParameter("Quantity",this.getFieldValue("ctlQuantity"));n.addParameter("Part",this.getFieldValue("ctlPartNo"));n.addParameter("Manufacturer",this.getFieldValue("ctlManufacturer"));n.addParameter("DateCode",this.getFieldValue("ctlDateCode"));n.addParameter("Product",this.getFieldValue("ctlProduct"));n.addParameter("Package",this.getFieldValue("ctlPackage"));n.addParameter("Price",this.getFieldValue("ctlPrice"));n.addParameter("Currency",this.getFieldValue("ctlCurrency"));n.addParameter("Supplier",this.getFieldValue("ctlSupplier"));n.addParameter("ROHS",this.getFieldValue("ctlROHS"));n.addParameter("OfferStatus",this.getFieldValue("ctlOfferStatus"));n.addParameter("Notes",this.getFieldValue("ctlNotes"));n.addParameter("MSL",this.getFieldValue("ctlMSL"));n.addParameter("PartWatchMatch",this.getFieldValue("ctlPartWatchMatch"));n.addDataOK(Function.createDelegate(this,this.saveEditComplete));n.addError(Function.createDelegate(this,this.saveEditError));n.addTimeout(Function.createDelegate(this,this.saveEditError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null}},saveEditError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},saveEditComplete:function(n){n._result.Result==!0?this.onSaveComplete():(this._strErrorMessage=n._errorMessage,this.onSaveError())},validateForm:function(){this.onValidate();return this.autoValidateFields()},supplierChangeEvent:function(){this._intSupplierId!=this.getFieldValue("ctlSupplier")&&(this._intSupplierId=this.getFieldValue("ctlSupplier"),this.getPurchaseData())},getPurchaseData:function(){var n=new Rebound.GlobalTrader.Site.Data;this._strPath="controls/Nuggets/PurchaseRequestLineDetail";this._strData="PurchaseRequestLineDetail";n.set_PathToData(this._strPath);n.set_DataObject(this._strData);n.set_DataAction("GetDefaultPurchasingInfo");n.addParameter("id",this.getFieldValue("ctlSupplier"));n.addDataOK(Function.createDelegate(this,this.getPurchaseDataOK));n.addError(Function.createDelegate(this,this.getPurchaseDataError));n.addTimeout(Function.createDelegate(this,this.getPurchaseDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getPurchaseDataOK:function(n){var t=n._result;this._intGlobalCurrencyNo=t.GlobalCurrencyNo;this._intPOCurrencyNo=t.CurrencyNo;$("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlEdit_ctlDB_ctlSupplier_ctl04_aut_ctl03").parent().find(".advisory-notes").remove();$("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlEdit_ctlDB_ctlSupplier_ctl04_aut_ctl03").append($R_FN.createAdvisoryNotesIcon(t.CompanyAdvisoryNotes,"margin-left-10"));this.bindCurrency()},getPurchaseDataError:function(n){this.showError(!0,n.get_ErrorMessage())},bindCurrency:function(){this.getFieldControl("ctlCurrency")._intGlobalCurrencyNo=this._intGlobalCurrencyNo;this.getFieldDropDownData("ctlCurrency");this.setFieldValue("ctlCurrency",this._intCurrencyNo)},getMfrNotes:function(){var n=new Rebound.GlobalTrader.Site.Data;this._strPath="controls/Nuggets/ManufacturerMainInfo";this._strData="ManufacturerMainInfo";n.set_PathToData(this._strPath);n.set_DataObject(this._strData);n.set_DataAction("GetAdvisoryNotes");n.addParameter("ID",this.getFieldValue("ctlManufacturer"));n.addDataOK(Function.createDelegate(this,this.getMfrNotesOK));n.addError(Function.createDelegate(this,this.getMfrNotesError));n.addTimeout(Function.createDelegate(this,this.getMfrNotesError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getMfrNotesOK:function(n){var i=n._result,t=$find(this.getField("ctlManufacturer").ControlID)._aut._lblSelectedValue;$(t).parent().find(".advisory-notes").remove();$(t).append($R_FN.createAdvisoryNotesIcon(i.MfrAdvisoryNotes,"margin-left-10"))},getMfrNotesError:function(n){this.showError(!0,n.get_ErrorMessage())},getSupplierNotes:function(){var n=new Rebound.GlobalTrader.Site.Data;this._strPath="controls/Nuggets/CompanyMainInfo";this._strData="CompanyMainInfo";n.set_PathToData(this._strPath);n.set_DataObject(this._strData);n.set_DataAction("GetAdvisoryNotes");n.addParameter("ID",this.getFieldValue("ctlSupplier"));n.addDataOK(Function.createDelegate(this,this.getSupplierNotesOK));n.addError(Function.createDelegate(this,this.getSupplierNotesError));n.addTimeout(Function.createDelegate(this,this.getSupplierNotesError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getSupplierNotesOK:function(n){var i=n._result,t=$find(this.getField("ctlSupplier").ControlID)._aut._lblSelectedValue;$(t).parent().find(".advisory-notes").remove();$(t).append($R_FN.createAdvisoryNotesIcon(i.AdvisoryNotes,"margin-left-10"))},getSupplierNotesError:function(n){this.showError(!0,n.get_ErrorMessage())}};Rebound.GlobalTrader.Site.Controls.Forms.CusReqSourcingResults_Edit.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CusReqSourcingResults_Edit",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);