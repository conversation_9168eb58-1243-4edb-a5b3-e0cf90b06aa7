﻿
@{
    Layout = null;
}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>BOM Manager Order Details</title>
    <link rel="stylesheet" href="~/Areas/BOM/css/BOMManagerDetail.css" />
    @*<script src='https://code.jquery.com/jquery-2.2.4.min.js'></script>*@
    @*<script src='https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/js/bootstrap.min.js'></script>*@   
    <script src="~/Areas/BOM/js/libs/jquery-2.2.4.min.js"></script>
    <script src="~/Areas/BOM/js/libs/bootstrap.min.js"></script>

    @*<link href="~/Areas/BOM/css/libs/bootstrap.min.css" rel="stylesheet" />*@
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/css/bootstrap.min.css" integrity="sha384-1q8mTJOASx8j1Au+a5WDVnPi2lkFfwwEAa8hDDdjZlpLegxhjVME1fgjWPGmkzs7" crossorigin="anonymous">

    <!-- Latest compiled and minified CSS -->
    @*<link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">*@
    @*<script src="https://unpkg.com/jquery@2.2.4/dist/jquery.js"></script>*@
    <link href="~/Areas/BOM/css/libs/icon.css" rel="stylesheet" />
    <script src="~/Areas/BOM/js/libs/jquery.js"></script>

    <!--jQueryUI version 1.11.4 -->
    @*<link rel="stylesheet" href="https://code.jquery.com/ui/1.11.4/themes/smoothness/jquery-ui.css" />
        <script src="https://code.jquery.com/ui/1.11.4/jquery-ui.min.js"></script>
        <link rel="stylesheet" href="//code.jquery.com/ui/1.13.2/themes/base/jquery-ui.css">*@
    <link href="~/Areas/BOM/css/libs/jquery-ui.css" rel="stylesheet" />
    <script src="~/Areas/BOM/js/libs/jquery-ui.min.js"></script>
    @*<link href="~/Areas/BOM/css/libs/1.13.2.jquery-ui.css" rel="stylesheet" />*@
    <link rel="stylesheet" href="//code.jquery.com/ui/1.13.2/themes/base/jquery-ui.css">

    <script src="~/Areas/BOM/js/MVCCalendar.js"></script>
    <script src="~/Areas/BOM/js/QuoteDatePicker.js"></script>
    <script src="~/Areas/BOM/js/EditBOMDeliveryDate.js"></script>
    <script src="~/Areas/BOM/js/EditBOMRFQClosingDate.js"></script>
    <script src="~/Areas/BOM/js/EditBOMCustomerDecisionDate.js"></script>

    <!--ParamQuery Grid css files-->
    <link rel="stylesheet" href="~/paramquery-8.1.0/pqgrid.dev.css" />

    <!--add pqgrid.ui.css for jQueryUI theme support-->
    <link rel="stylesheet" href="~/paramquery-8.1.0/pqgrid.ui.dev.css" />

    <!--ParamQuery Grid custom theme e.g., office, bootstrap, rosy, chocolate, etc (optional)-->
    <link rel="stylesheet" href="~/paramquery-8.1.0/themes/bootstrap/pqgrid.css" />

    <!--ParamQuery Grid js files-->
    <script type="text/javascript" src="~/paramquery-8.1.0/pqgrid.dev.js"></script>

    <!--ParamQuery Grid localization file-->
    <script src="~/paramquery-8.1.0/localize/pq-localize-en.js"></script>

    <!--Include pqTouch file to provide support for touch devices (optional)-->
    <script type="text/javascript" src="~/paramquery-8.1.0/pqTouch/pqtouch.min.js"></script>

    <!--Include jsZip file to support xlsx and zip export (optional)-->
    <script type="text/javascript" src="~/paramquery-8.1.0/jsZip-2.5.0/jszip.min.js"></script>
    @*<script type="text/javascript" src="~/Areas/BOM/js/BOMSearch.js"></script>*@

    <link href="~/Areas/BOM/css/bom-manager-order.css" rel="stylesheet" />

</head>
<body>
    <input type="hidden" id="HdnvalidateMessage" />
    <input type="hidden" id="BOMItemsCount" />
    <!-- partial:index.partial.html -->
    <div class="container">
        <div class="head_content">
            <div class="abovePageTitle">
            </div>
            <div class="page_tittle"> <h3>BOM Manager Order Details</h3></div>
            <div class="BomLink ">
                <a class="itemTitle" id="BomName"><span></span></a>
            </div>
            <div class="pageTitleItem ">
                <span class="itemTitle">Status:</span>&nbsp; <label id="BOMStatusDesc"></label>
                <label style="float:right" id="ItemsQuotedCount"></label>
                <input type="hidden" id="BOMStatus" />
                <input type="hidden" id="PriceUplifted" />
            </div>
        </div>
        <div class="panel-group" id="accordion" role="tablist" aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading" role="tab" id="headingOne">
                    <div class="topTL"></div>
                    <div class="topTR"></div>
                    <div class="head_in">
                        <h4 class="panel-title">
                            Main Information
                        </h4>
                        <img id="RefreshMainInfo" class="refresh" src="/Areas/BOM/Images/refresh.gif" style="width:11px; height:10px; float:right; position:absolute; right:24px; top:4px;">
                        <a id="ShowBOMDataDiv" role="button" class="sec_tab">

                        </a>
                        <div class="boxlink" id="divBOMDataButtons">
                            <a id="linkEdit"><span id="EditBomLink" class="linkbuttoncss edit"> Edit</span></a>

                            <a>@*<span class="edit export"> Export</span>*@</a>
                            <a id="linkPurchaseHub"><span id="SendToHubLink" class="linkbuttoncss purchase_hub"> Send to Purchase Hub</span></a>
                            @*<a><span class="edit supplier"> Send to supplier</span></a>*@
                            <a id="linkCloseHub"><span id="BtnCloseBOM" class="linkbuttoncss close_icon"> Close</span></a>
                            @*<a id="LinkUpliftPriceAll"><span id="BtnUpliftPriceAll" class="linkbuttoncss notes_disabled"> Uplift Price All</span></a>
                        <a id="LinkResetUpliftPriceAll"><span id="BtnResetUpliftPriceAll" class="linkbuttoncss notes_disabled">Reset Uplift Price All</span></a>*@
                            <a><span class="linkbuttoncss quoteSeletedItem_disabled" id="QouteAllItems">Quote All Items</span></a>
                            @*<a><span class="linkbuttoncss note"> View tree</span></a>*@

                        </div>
                    </div>
                    @*<div class="boxBL"></div>
                <div class="boxBR"></div>*@


                </div>
                <div id="divBOMDataGrid">


                    <div id="collapseOne" class="panel-collapse collapse in accordion" role="tabpanel" aria-labelledby="headingOne">
                        <div class="panel-body">
                            <table class="top_tbl">
                                <tbody>
                                    <tr>
                                        <td width="33%">
                                            <table class="first_tbl">
                                                <tbody>

                                                    <tr>
                                                        <td class="desc">
                                                            Code
                                                        </td>
                                                        <td class="item"><Label id="lblBomCode"></Label> </td>


                                                    </tr>
                                                    <tr>
                                                        <td class="desc">
                                                            Name
                                                        </td>
                                                        <td class="item">
                                                            <Label id="lblBomName"></Label>
                                                            <input type="hidden" id="HiddenBOMManagerName" />
                                                        </td>


                                                    </tr>
                                                    <tr>
                                                        <td class="desc">
                                                            Quote Required
                                                        </td>
                                                        <td class="item">
                                                            <Label id="lblBomQoute"></Label>
                                                            <input type="hidden" id="HiddenBOMQuote" />
                                                        </td>


                                                    </tr>
                                                    <tr>
                                                        <td class="desc">
                                                            Request by
                                                        </td>
                                                        <td class="item"><Label id="lblBomRequestedBy"></Label></td>


                                                    </tr>
                                                    <tr>
                                                        <td class="desc">
                                                            Salesperson
                                                        </td>
                                                        <td class="item">
                                                            <Label id="lblBomSalesperson"></Label>
                                                            <input type="hidden" id="HiddenBomSalesperson" />
                                                        </td>

                                                    </tr>
                                                    <tr>
                                                        <td class="desc">
                                                            CC Communication Notes to
                                                        </td>
                                                        <td class="item">
                                                            <Label id="lblBomCC"></Label>
                                                            <input type="hidden" id="HiddenBOMCC" />
                                                        </td>


                                                    </tr>


                                                </tbody>
                                            </table>


                                        </td>
                                        <td width="33%" style=" word-break:break-word;">
                                            <!--class needs to add in table--> <table class="first_tbl">
                                                <tbody>

                                                    <tr>
                                                        <td class="desc">
                                                            Company
                                                        </td>
                                                        <td class="item">
                                                            <Label id="lblBomCompany"></Label>
                                                            <label id="lblAdvisoryNotes" class="advisory-notes" style="display: none"></label>
                                                            <input type="hidden" id="HiddenBomCompanyNo" />
                                                            <input type="hidden" id="HiddenBomRequestToPOHubBy" />
                                                            <input type="hidden" id="HiddenBomUpdateByPH" />

                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td class="desc">
                                                            Currency
                                                        </td>
                                                        <td class="item">
                                                            <Label id="lblBomCurrency"></Label>
                                                            <input type="hidden" id="HiddenBomCurrency" />
                                                        </td>


                                                    </tr>
                                                    <tr>
                                                        <td class="desc">
                                                            Contact
                                                        </td>
                                                        <td class="item">
                                                            <Label id="lblBomContact"></Label>
                                                            <input type="hidden" id="HiddenBomContact" />
                                                            <input type="hidden" id="HiddenBomContact2No" />
                                                        </td>


                                                    </tr>
                                                    @*<tr>
                                                    <td class="desc">
                                                        Current supplier
                                                    </td>
                                                    <td class="item"><Label id="lblBomCurrentSupplier"></Label></td>
                                                </tr>*@
                                                    <tr>
                                                        <td class="desc">
                                                            Source of Supply Required
                                                        </td>
                                                        <td class="item">
                                                            <input type="checkbox" class="sb-checkbox__input" name="check1" id="chkSupplyRequired" />
                                                            <label class="sb-checkbox__label sb-checkbox__label--green" for="check1"></label>
                                                        </td>


                                                    </tr>


                                                </tbody>
                                            </table>


                                        </td>
                                        <td width="33%">
                                            <table class="first_tbl">
                                                <tbody>

                                                    <tr>
                                                        <td class="desc">
                                                            Inactive?
                                                        </td>
                                                        <td class="item">

                                                            <input type="checkbox" class="sb-checkbox__input" name="check1" id="chkInactive" />
                                                            <label class="sb-checkbox__label sb-checkbox__label--green" for="check1"></label>
                                                        </td>


                                                    </tr>
                                                    <tr>
                                                        <td class="desc">

                                                            Released by
                                                        </td>
                                                        <td class="item"><Label id="lblBomReleasedBy"></Label></td>


                                                    </tr>
                                                    <tr>
                                                        <td class="desc">
                                                            Assign To
                                                        </td>
                                                        <td class="item"><Label id="lblBomAssignTo"></Label></td>


                                                    </tr>
                                                    <tr>
                                                        <td class="desc">
                                                            Notes
                                                        </td>
                                                        <td class="item">

                                                            <Label id="lblBomNotes" class="more">


                                                            </Label>



                                                        </td>


                                                    </tr>


                                                </tbody>
                                            </table>


                                        </td>


                                    </tr>


                                </tbody>

                            </table>
                            <div class="update_label"><label id="LastUpdated" style="float:right"></label></div>
                        </div>
                    </div>

                </div>
            </div>
            <div class="panel panel-default bom_item_sec">
                <div class="panel-heading" role="tab" id="headingThree">
                    <div class="topTL"></div>
                    <div class="topTR"></div>
                    <div class="head_in">
                        <h4 class="panel-title">
                            Bom Items
                        </h4>

                        <img id="refreshBOMItem" class="refresh" src="/Areas/BOM/Images/refresh.gif" style="width:11px; height:10px; float:right; position:absolute; right:24px; top:4px;">
                        <a id="ShowBOMItemDiv" role="button" class="sec_tab">

                        </a>
                        <div class="boxlink" id="divBOMItemButtons">
                            <a><span class="linkbuttoncss delete_disabled" id="DeleteBOMItem">Delete</span></a>
                            <a><span class="linkbuttoncss edit_disabled" id="EditBOMItem">Edit</span></a>
                            <a><span class="linkbuttoncss quoteSeletedItem_disabled" id="QouteSeletedItem">Quote Checked Item</span></a>
                            <a><span class="linkbuttoncss add_comm_note_disabled" id="AddCommunicationNote">Add new communication note</span></a>
                            @*<a id="LinkUpliftPriceSelected"><span id="BtnUpliftPriceSelected" class="linkbuttoncss notes_disabled"> Uplift Checked Item</span></a>*@
                            @*<a><span class="linkbuttoncss release"> Recall</span></a>
                        <a><span class="linkbuttoncss note"> Add</span></a>
                        <a><span class="linkbuttoncss note"> Add new communication note</span></a>*@
                        </div>
                    </div>
                </div>
                <div id="divBOMItemGrid">

                    <div id="collapseThree" class="accordion" role="tabpanel" aria-labelledby="headingThree" style="width:100%;">
                        <div class="box boxStandard">
                            <div class="boxInner">
                                <div class="boxTL"></div>
                                <div class="boxTR"></div>
                                <div class="boxHeader">
                                </div>
                                <div class="boxContent">
                                    <div class="resulttable">
                                        <div id="grid_md"></div>
                                    </div>
                                </div>


                            </div>
                        </div>

                    </div>
                </div>
            </div>

            <div class="panel panel-default bom_item_sec">
                <div class="panel-heading" role="tab" id="headingThreeNote">
                    <div class="topTL"></div>
                    <div class="topTR"></div>
                    <div class="head_in">
                        <h4 class="panel-title">Communication Note</h4>
                        <img id="refreshNotesItem" class="refresh" src="/Areas/BOM/Images/refresh.gif" style="width:11px; height:10px; float:right; position:absolute; right:24px; top:4px;">
                        <a id="ShowNotesItemDiv" role="button" class="sec_tab"></a>
                    </div>
                </div>
                <div id="divNotesItemGrid">
                    <div id="collapseThree" class="accordion" role="tabpanel" aria-labelledby="headingThree" style="width:100%;">
                        <div class="box boxStandard">
                            <div class="boxInner">
                                <div class="boxTL"></div>
                                <div class="boxTR"></div>
                                <div class="boxHeader">
                                </div>
                                <div class="boxContent">
                                    <div class="resulttable">
                                        <div id="grid_md_notes"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="panel panel-default" style="display:none;">
                <div class="panel-heading" role="tab" id="headingfour">
                    <div class="head_in">
                        <div class="topTL"></div>
                        <div class="topTR"></div>
                        <h4 class="panel-title">
                            Upload Document
                        </h4>

                        <img class="refresh" src="/Areas/BOM/Images/refresh.gif" / style="width:10px; height:10px; float:right; position:absolute; right:24px; top:10px;">
                        <a role="button" class="sec_tab" data-toggle="collapse" data-parent="#accordion" href="#collapsefour" aria-expanded="false" aria-controls="collapsefour">

                        </a>
                    </div>
                </div>
                <div id="collapsefour" class="panel-collapse collapse" role="tabpanel" aria-labelledby="headingfour" style="width:100%;">
                    <div class="panel-body">
                        <div class="noneFound">
                            This BOM has no CSV documents
                        </div>

                    </div>

                </div>
            </div>

            <div class="panel panel-default bom_item_sec" style="display:none" id="BOMManagerSourcingGrid">
                <div class="panel-heading" role="tab" id="headingThree">
                    <div class="topTL"></div>
                    <div class="topTR"></div>
                    <div class="head_in">
                        <h4 class="panel-title">
                            BOM Manager Sourcing
                        </h4>

                        <img id="refreshBOMSourcing" class="refresh" src="/Areas/BOM/Images/refresh.gif" style="width:11px; height:10px; float:right; position:absolute; right:24px; top:4px;">
                        <a id="ShowBOMItemDiv" role="button" class="sec_tab">

                        </a>
                        <div class="boxlink" id="divBOMItemButtons">
                            @*<a><span class="linkbuttoncss edit_disabled" id="EditSourcing">Edit</span></a>*@
                            @*<a><span class="linkbuttoncss quote_disabled" id="Quote">Quote</span></a>*@
                            <a><span class="linkbuttoncss quote_disabled" id="PrimarySource">Reset Primary Source</span></a>
                            <input type="hidden" id="hdnPrimarySource" />
                            <input type="hidden" id="hdnCustomerRequirementId" />
                            <input type="hidden" id="hdnMultiQuote" />
                        </div>
                    </div>
                </div>
                <div class="add_text">
                    <label style="padding:12px 0px 0px 0px;">** Please Select Primary Sourcing if Multiple Sourcing are available</label>
                </div>
                <div id="grid_as"></div>
            </div>
            @*Sourcing Result extra information BEGIN*@
            <div id="SRDetailsGrid" class="panel panel-default bom_item_sec" style="display:none">
                <div class="panel-heading" role="tab" id="SRDetailsHeading">
                    <div class="topTL"></div>
                    <div class="topTR"></div>
                    <div class="head_in">
                        <h4 class="panel-title">
                            Sourcing Result Details
                        </h4>
                    </div>
                </div>
                <div id="SRDetailsBody" class="panel-collapse collapse in accordion" role="tabpanel" aria-labelledby="SRDetailsHeading" style="width:100%;">
                    <div class="panel-body">
                        <table class="top_tbl">
                            <tbody>
                                <tr>
                                    <td width="33%">
                                        <table class="first_tbl">
                                            <tbody>
                                                <tr>
                                                    <td class="desc">Manufacturer Name</td>
                                                    <td class="item"><Label id="lblSRManufactuer"></Label></td>
                                                </tr>
                                                <tr>
                                                    <td class="desc">Date Code</td>
                                                    <td class="item"><Label id="lblSRDateCode"></Label></td>
                                                </tr>
                                                <tr>
                                                    <td class="desc">Package Type</td>
                                                    <td class="item"><Label id="lblSRPackageType"></Label></td>
                                                </tr>
                                                <tr>
                                                    <td class="desc">Product Type</td>
                                                    <td class="item"><Label id="lblSRProductType"></Label></td>
                                                </tr>
                                                <tr>
                                                    <td class="desc">Supplier Warranty</td>
                                                    <td class="item"><Label id="lblSRSupplierWarrantly"></Label></td>
                                                </tr>
                                                <tr>
                                                    <td class="desc">Testing Recommended</td>
                                                    <td class="item"><Label id="lblSRTestingRecommended"></Label></td>
                                                </tr>
                                                <tr>
                                                    <td class="desc">Country Of Origin</td>
                                                    <td class="item"><Label id="lblSRCountryOfOrigin"></Label></td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </td>
                                    <td width="33%">
                                        <table class="first_tbl">
                                            <tbody>
                                                <tr>
                                                    <td class="desc">Minimum Order Quantity (MOQ)</td>
                                                    <td class="item"><Label id="lblSRMoq"></Label></td>
                                                </tr>
                                                <tr>
                                                    <td class="desc">Total quantity of stock available</td>
                                                    <td class="item"><Label id="lblSRTotalQuantity"></Label></td>
                                                </tr>
                                                <tr>
                                                    <td class="desc">Last time buy (LTB) - (Y/N)</td>
                                                    <td class="item"><Label id="lblSRLastTimeBuy"></Label></td>
                                                </tr>
                                                <tr>
                                                    <td class="desc">Notes</td>
                                                    <td class="item"><Label id="lblSRNotes"></Label></td>
                                                </tr>
                                                <tr>
                                                    <td class="desc">Images attached</td>
                                                    <td class="item"><Label id="lblSRImagesAttached"></Label></td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </td>
                                    <td width="33%">
                                        <table class="first_tbl">
                                            <tbody>
                                                <tr>
                                                    <td class="desc">Standard Pack Quantity (SPQ)</td>
                                                    <td class="item"><Label id="lblSrSPQ"></Label></td>
                                                </tr>
                                                <tr>
                                                    <td class="desc">Lead Time</td>
                                                    <td class="item"><Label id="lblSrLeadTime"></Label></td>
                                                </tr>
                                                <tr>
                                                    <td class="desc">RoHS</td>
                                                    <td class="item"><Label id="lblSrRoHS"></Label></td>
                                                </tr>
                                                <tr>
                                                    <td class="desc">Factory Sealed (Y/N)</td>
                                                    <td class="item"><Label id="lblSrFactorySealed"></Label></td>
                                                </tr>
                                                <tr>
                                                    <td class="desc">MSL</td>
                                                    <td class="item"><Label id="lblSrMSL"></Label></td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            @*Sourcing Result extra information END*@
        </div>
    </div>
    <div id="EditModal" class="modal">

        <!-- Modal content -->
        <div class="modal-content">
            <div class="main_header">
                <h6>Main Information</h6>
                <a id="BtnSaveEdit" class="BtnSubmitEdit" rel="modal:close">Save</a>
                <a id="BtnCancelEdit" class="BtnCloseEdit" rel="modal:close">Cancel</a>

            </div>

            <div class="form_container">
                <div class="header_text">
                    <h4>Edit Main Information</h4>
                    <div class="formInstructions">Edit the details and press Save</div>
                </div>
                <div style="display:none; margin-bottom:20px" id="EditBOMValidation" class="errorSummary">
                    There were some problems with your form<br>Please check below and try again.
                </div>
                <table class="top_tbl modal_table">
                    <tbody>
                        <tr id="RowBOMName">
                            <td>Name*</td>
                            <td><input type="text" id="txtName" class="input_feild"><!--class added input_feild --> </td>
                        </tr>
                        <tr>
                            <td>Company*</td>
                            <td><label id="lblCmpny"></label></td>
                        </tr>
                        <tr id="RowBOMContact">
                            <td>Contact*</td>

                            <td><select id="ddlContact"><option value="0">-- Select --</option></select><a id="ddlContact_refresh_icon" href="" class="refresh_icon"></a>  </td>
                        </tr>
                        <tr id="RowBOMSalesperson">
                            <td>Salesperson</td>

                            <td><select id="ddlSalesperson"><option value="0">-- Select --</option></select><a href="" class="refresh_icon"></a>  </td>
                        </tr>
                        <tr id="RowBOMCurrency">
                            <td>Currency*</td>
                            <td><select id="ddlCurrency"><option value="0">Select</option></select><a id="ddlCurrency_refresh_icon" href="" class="refresh_icon"> </a> </td>
                        </tr>
                        @*<tr>
                                <td>Current Supplier</td>
                                <td><input type="text" id="txtCurrentSup" /></td>
                            </tr>*@
                        <tr id="RowBOMQuoteReq">
                            <td>Quote Required*</td>
                            <td> <input type="text" id="mydatepicker" autocomplete="off"></td>
                        </tr>
                        <tr>
                            <td>Source of Supply Required</td>
                            <td><input type="checkbox" id="chkSupplyRequiredEdit" /></td>
                        </tr>
                        <tr>
                            <td>Inactive?</td>
                            <td><input type="checkbox" id="chkInactiveEdit" /></td>
                        </tr>

                        <tr>
                            <td>Notes</td>
                            <td><textarea id="textNotes"></textarea></td>
                        </tr>
                        <tr>
                            <td class="txt"> <span>Optional: Select an additional salesperson to receive BOM communication notes from Purchase Hub</span></td>

                        </tr>
                        <tr>
                            <td>CC communication notes to</td>

                            <td><select id="ddlEmployee"><option value="0">-- Select --</option></select>  </td>
                        </tr>
                    </tbody>
                </table>
            </div>


            <!--changes for version.3 starts --> <div class="footer_area">
                <a id="BtnSubmitEdit" class="BtnSubmitEdit" rel="modal:close">Save</a> <!--changes for version.3-->
                <a id="BtnCloseEdit" class="BtnCloseEdit" rel="modal:close">Cancel</a><!--changes for version.3-->
            </div><!--changes for version.3 ends here -->
        </div>
    </div>
    <div id="PurchaseHubModal" class="modal">
        <!--Modal content-->
        <div class="modal-content">
            <!--changes for version.3 starts-->
            <div class="main_header">
                <h6>Send To Purchase HUB</h6>
                <a id="BtnBackPH" class="BtnBackPH" rel="modal:close">Back</a>

            </div>
            <!--changes for version.3 ends-->
            <!--changes for form container version.3 starts-->
            <div class="form_container">

                <!--changes for version.3 starts-->  <div class="header_text">
                    <h4 id="SendToHubHeader">Are you sure you would like to send this to Purchase Hub?</h4>

                </div> <!--changes for version.3 ends-->
                <div id="SendToHubErrorMessageDiv" style="display:none">
                    <div id="showmessagediv" style=" padding:2px; display:none">
                        <div id="TableErrorDivBOM" class="col">

                            <label style="color: #FFFFFF" id="showMessage"></label>
                        </div>
                    </div>
                </div>
                <div id="SendToHubNoErrorDiv">
                    <table class="top_tbl modal_table" style="margin-top:20px;">
                        <!--changes for version.3 starts-->
                        <tbody>
                            <tr>
                                <td>
                                    <input type="radio" id="radioIndividualBuyer" name="SendToHubAssignee" value="Individual" checked="checked">
                                    <label for="html">Individual Buyer</label><br>
                                </td>
                                <td>
                                    <input type="radio" id="radioGroup" name="SendToHubAssignee" value="Group">
                                    <label for="css">Group</label><br>
                                </td>
                            </tr>
                        </tbody>
                        <tbody>
                            <tr id="rowIndividualBuyer">
                                <td>Buyer*</td>
                                <td>
                                    <select id="ddlBuyers"><option value="0">-- Select --</option></select><a style="cursor:pointer;" id="RefreshBuyer" class="refresh_icon"></a> <!--changes for version.3 starts-->
                                </td>
                            </tr>
                            <tr id="rowGroupBuyer" style="display:none">
                                <td>Group*</td>
                                <td>
                                    <select id="ddlGroup"><option value="0">-- Select --</option></select><a style="cursor:pointer;" id="RefreshGroup" class="refresh_icon"></a> <!--changes for version.3 starts-->
                                </td>
                            </tr>
                            <tr>
                                <td>CC</td>
                                <td class="buyerfld_imput">
                                    <div class="lablearea">
                                        <div class='element' id='div_2'>
                                        </div>
                                    </div>
                                    <input type="text" id="txtBuyerCC" />
                                    <div id="divMailReceiver"></div>
                                    <input type="hidden" id="HiddenMailReceiver" class="cc_input" /> <!--changes for version.3 starts-->

                                </td>
                            </tr>
                            <tr>
                                <td>Please confirm</td>
                                <td><a id="btnSubmitHubRFG" class="yes_icon">Yes</a><a id="btnCancelHubRFQ" class="n_icon">No</a></td> <!--changes for version.3 starts-->
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div> <!--changes for form_container div ends here version.3 starts-->
            <!--changes for version.3 starts-->
            <div class="footer_area">
                <a id="BtnBackPH" class="BtnBackPH" rel="modal:close">Back</a>
            </div>
            <!--changes for version.3 ends-->

        </div>
    </div>

    <div id="CloseHubModal" class="modal">

        <!-- Modal content -->
        <div class="modal-content">


            <!--changes for version.3 starts -->  <div class="main_header">
                <h6>Main Information</h6>
                <a id="BtnBackPH" class="BtnBackPH" rel="modal:close">Back</a>
            </div>
            <!--changes for version.3 ends -->
            <!--changes for form_container version.3 starts -->   <div class="form_container">
                <div class="header_text">
                    <h4>Are you sure you would like to close this BOM?</h4>
                </div>
                <div class="form_container">
                    <!--changes for version.3 starts --><table class="top_tbl modal_table">
                        <tbody>

                            <tr>
                                <td>Please confirm</td>
                                <td><a id="btnCloseHubRFGYes" class="yes_icon">Yes</a><a id="btnCloseHubRFGNo" class="n_icon">No</a><!--changes for version.3 starts --></td>
                            </tr>
                        </tbody>
                    </table>
                </div>

            </div>
            <!--changes for form_container version.3 ends -->
            <!--changes for version.3 starts --> <div class="footer_area" style="padding:10px 0px">

            </div>
            <!--changes for version.3 ends -->
        </div>
    </div>
    <div id="EditSourcingModal" class="modal">

        <!-- Modal content -->
        <div class="modal-content">
            <div class="main_header">
                <h6>Edit Sourcing Information</h6>
                <a class="BtnSaveSourcingEdit" rel="modal:close">Save</a>
                <a class="BtnCancelSourcingEdit" rel="modal:close">Cancel</a>

            </div>

            <div class="form_container">
                <div class="header_text">

                    <div class="formInstructions"></div>
                </div>
                <input type="hidden" id="EditSourcing_Customer_ReqID" value="" />
                <table class="top_tbl modal_table">
                    <tbody>
                        <tr>
                            <td>Supplier</td>
                            <td><label type="text" id="SupplierName" / class="input_feild"></label><!--class added input_feild --> </td>
                        </tr>
                        <tr>
                            <td>Part</td>
                            <td><label id="PartNo"></label></td>
                        </tr>
                        <tr>
                            <td>Manufacturer</td>
                            <td><label id="ManufacturerName"></label></td>
                        </tr>
                        <tr>
                            <td>Product</td>
                            <td><label id="ProductName"></label></td>
                        </tr>
                        <tr>
                            <td>Package</td>
                            <td><label id="PackageName"></label></td>
                        </tr>
                        <tr>
                            <td>Quantity</td>
                            <td><label type="number" id="Quantity"></label></td>
                        <tr>
                        <tr>
                            <td>Uplift Price %</td>
                            <td><input type="number" id="UnitPrice" /></td>
                        <tr>
                            <td>Notes</td>
                            <td class="text_inp"><textarea style="height:50px;" type="" id="Notes"></textarea></td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="footer_area">
                <a class="BtnSaveSourcingEdit">Save</a>
                <a class="BtnCancelSourcingEdit" rel="modal:close">Cancel</a>
            </div>
        </div>
    </div>
    <div id="QuoteModal" class="modal">

        <!-- Modal content -->
        <div class="modal-content">
            <div class="main_header">
                <h6>Add New Quote</h6>
                <a class="BtnSaveQuote" rel="modal:close">Save</a>
                <a class="BtnCancelQuote" rel="modal:close">Cancel</a>

            </div>

            <div class="form_container">
                <div class="header_text">
                    <h4></h4>
                    <div class="formInstructions">Enter the details of the new Quote and press Save</div>
                </div>
                <div style="display:none; margin-bottom:20px" id="QuoteValidationError" class="errorSummary">
                    There were some problems with your form<br>Please check below and try again.
                </div>
                <input type="hidden" id="Quote_Customer_ReqID" value="" />
                <input type="hidden" id="Quote_Customer_SourceId" value="" />
                <table class="top_tbl modal_table">
                    <tbody>
                        <tr>
                            <td>Company</td>
                            <td>
                                <label id="QuoteCompany"></label>
                                <input type="hidden" id="QuoteCompanyNo" value="" />
                            </td>
                        </tr>
                        <tr id="RowQuoteBuyer">
                            <td>Buyer *</td>
                            <td><select id="QuoteBuyer"><option value="0">-- Select --</option></select><a id="refreshQuoteBuyer" class="refresh_icon"></a></td>
                        </tr>
                        <tr style="display:none" id="RowQuoteBuyerError">
                            <td></td>
                            <td class="leftsec">
                                <div id="ValidationErrorBuyer" class="formMessages">Please select a value</div>
                            </td>
                        </tr>
                        <tr id="RowQuoteSalesPerson">
                            <td>Salesperson *</td>
                            <td><select id="QuoteSalesPerson"><option value="0">Select</option></select><a id="refreshQuoteSalesPerson" class="refresh_icon"> </a> </td>
                        </tr>
                        <tr style="display:none" id="RowQuoteSalesPersonError">
                            <td></td>
                            <td class="leftsec">
                                <div id="ValidationErrorSalesPerson" class="formMessages">Please select a value</div>
                            </td>
                        </tr>
                        <tr>
                            <td>Division Sales</td>
                            <td>
                                <label id="QuoteDivisionSales"></label>
                                <input type="hidden" id="HeaderImageNameQuote" value="" />
                                <input type="hidden" id="QuoteDivisionNo" value="" />
                            </td>
                        </tr>
                        <tr id="RowQuoteDivisionHeader">
                            <td>Division Header *</td>
                            <td><select id="QuoteDivisionHeader"><option value="0">-- Select --</option></select><a id="refreshQuoteDivisionHeader" class="refresh_icon"></a></td>
                        </tr>
                        <tr style="display:none" id="RowQuoteDivisionHeaderError">
                            <td></td>
                            <td class="leftsec">
                                <div id="ValidationErrorDivisionHeader" class="formMessages">Please select a value</div>
                            </td>
                        </tr>
                        <tr id="RowQuoteTerms">
                            <td>Terms *</td>
                            <td><select id="QuoteTerms"><option value="0">-- Select --</option></select><a id="refreshQuoteTerms" class="refresh_icon"></a></td>
                        </tr>
                        <tr style="display:none" id="RowQuoteTermsError">
                            <td></td>
                            <td class="leftsec">
                                <div id="ValidationErrorTerms" class="formMessages">Please select a value</div>
                            </td>
                        </tr>
                        <tr id="RowQuoteCurrency">
                            <td>Currency *</td>
                            <td><select id="QuoteCurrency"><option value="0">-- Select --</option></select><a id="refreshQuoteCurrency" class="refresh_icon"></a></td>
                        </tr>
                        <tr style="display:none" id="RowQuoteCurrencyError">
                            <td></td>
                            <td class="leftsec">
                                <div id="ValidationErrorCurrency" class="formMessages">Please select a value</div>
                            </td>
                        </tr>
                        <tr id="RowQuoteDate">
                            <td>Date Quoted *</td>
                            <td> <input type="text" id="QuoteDate"></td>
                        </tr>
                        <tr style="display:none" id="RowQuoteDateError">
                            <td></td>
                            <td class="leftsec">
                                <div id="ValidationErrorQuoteDate" class="formMessages">Please enter a value</div>
                            </td>
                        </tr>
                        <tr>
                            <td>Estimated Freight</td>
                            <td><input type="text" id="QuoteEstFreight" /> <label id="QuoteEstFreightCurrencyCode"></label></td>
                        </tr>
                        <tr id="RowQuoteIncoterms">
                            <td>Incoterms *</td>
                            <td><select id="QuoteIncoterms"><option value="0">-- Select --</option></select><a id="refreshQuoteIncoterms" class="refresh_icon"></a></td>
                        </tr>
                        <tr style="display:none" id="RowQuoteIncotermsError">
                            <td></td>
                            <td class="leftsec">
                                <div id="ValidationErrorIncoterms" class="formMessages">Please select a value</div>
                            </td>
                        </tr>
                        <tr>
                            <td>Notes to Customer</td>
                            <td class="text_inp"><textarea id="QuoteCustomerNotes" maxlength="2000"></textarea><label>(2000 chars max)</label></td>
                        </tr>
                        <tr>
                            <td>Internal Notes</td>
                            <td class="text_inp"><textarea id="QuoteInternalNotes" maxlength="2000"></textarea><label>(2000 chars max)</label></td>
                        </tr>
                        <tr>
                            <td>Source of Supply Required</td>
                            <td><input type="checkbox" id="QuoteSupplyRequired" /></td>
                        </tr>
                        <tr>
                            <td>Mark as Important</td>
                            <td><input type="checkbox" id="QuoteMarkImportant" /></td>
                        </tr>
                        <tr>
                            <td>Support Team Member to update</td>
                            <td>
                                <div class="row">
                                    <div class="col">
                                        <input type="text" id="QuoteSupportMember" placeholder="Type 2 chars to search" />
                                        <input type="hidden" id="QuoteSupportMemberID" />
                                        <div class="row">
                                            <div class="col">
                                                <label id="LabelQuoteSupportMember"></label>
                                                <a id="RemoveQuoteSupportMember" style="display:none">[Reselect]</a>
                                            </div>

                                        </div>

                                    </div>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="footer_area">
                <a class="BtnSaveQuote">Save</a>
                <a class="BtnCancelQuote" rel="modal:close">Cancel</a>
            </div>
        </div>
    </div>
    <div class="modal bd-example-modal-lg" id="DeleteConfirmationBoxModal" role="dialog">
        <div>
            <div class="modal-content" style="background-color: #AAE2A0;">
                <div class="main_header">
                    <h6> Confirmation</h6>
                    <a id="btnConfirmModal1" class="ConfirmYes BtnConfirmDelete" rel="modal:close">Confirm</a>

                    <a id="btnCancelModal1" class="ConfirmNo BtnCancelDelete" rel="modal:close">Cancel</a>

                </div>
                <div class="form_container">
                    <div class="header_text">

                        <h4>Confirmation to Delete BOM Item</h4>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col conf_message">
                                <label>Are You Sure Want to Delete Selected(Checkbox) BOM Items</label><br>
                                <label>(If all the Items are selected and Deleted then BOM will be Closed)</label>
                                <input type="hidden" id="HDCustReqIDs" value="" />
                            </div>
                        </div>
                    </div>
                </div>
                <div class="footer_area">
                    <a id="btnConfirmModal" class="ConfirmYes BtnConfirmDelete" rel="modal:close">Confirm</a>
                    <a id="btnCancelModal" class="ConfirmNo BtnCancelDelete" rel="modal:close">Cancel</a>
                </div>
            </div>
        </div>
    </div>

    <div class="modal bd-example-modal-lg" id="EditBOMItemModal" role="dialog">
        <div>
            <div class="modal-content edit_modal" style="background-color: #AAE2A0;">
                <div class="main_header">
                    <h6> Edit Information</h6>
                    <a id="btnConfirmModal1" class="BtnSubmitEdit BtnSaveBOMEdit" rel="modal:close">Save</a>
                    &nbsp;&nbsp;&nbsp;
                    <a id="btnCancelModal1" class="BtnCloseEdit BtnCancelBOMEdit" rel="modal:close">Cancel</a>

                </div>
                <div class="form_container">
                    <div class="header_text">
                        <h4>EDIT MAIN CUSTOMER REQUIREMENT INFORMATION</h4>
                        <div class="formInstructions">Amend the details of the Requirement and press <b>Save</b></div>
                        <div style="display:none; margin-bottom:20px" id="EditBOMValidationError" class="errorSummary">
                            There were some problems with your form<br>Please check below and try again.
                        </div>
                    </div>
                    <div class="formContent">
                        <input type="hidden" id="HDCustomerRequirementID" />
                        <table class="formRows tblmain_area" style="border-style:None;border-collapse:collapse;">
                            <tr>
                                <td class="lableTD">
                                    <label>Customer</label>
                                </td>
                                <td>
                                    <label id="LabelEditBOMCustomer"></label>
                                </td>
                            </tr>
                            <tr>
                                <td class="lableTD">
                                    <label>Contact</label>
                                </td>
                                <td>
                                    <label id="LabelEditBOMContact"></label>
                                </td>
                            </tr>
                            <tr id="RowEditBOMTraceability">
                                <td class="lableTD">
                                    <label>Requirement for Traceability *</label>
                                </td>
                                <td class="fieldTD">
                                    <select id="SelectEditBOMTraceability"></select>
                                </td>
                            </tr>
                            @*<tr id="RowEditBOMSalesPerson">
                                    <td class="lableTD">
                                        <label>Salesperson *</label>
                                    </td>
                                    <td class="fieldTD">
                                        <select id="SelectEditBOMSalesPerson"></select>
                                    </td>
                                </tr>*@
                            <tr id="RowEditBOMSalesPerson">
                                <td class="lableTD">
                                    <label>Salesperson</label>
                                </td>
                                <td>
                                    <input type="hidden" id="HiddenEditBOMSalesPerson" />
                                    <label id="LabelEditBOMSalesPerson"></label>
                                </td>
                            </tr>
                            <tr id="RowEditBOMQuantity">
                                <td class="lableTD">
                                    <label>Quantity *</label>
                                </td>
                                <td class="fieldTD" colspan="5">
                                    <input type="number" id="TextEditBOMQuantity" style="width:110px;" />


                                    <span class="sndcol"><label>Usage</label> </span>

                                    <span><select id="SelectEditBOMUsage" style="width:150px !important"></select></span>

                                </td>
                            </tr>
                            <tr id="RowEditBOMType">
                                <td class="lableTD">
                                    <label>Type *</label>
                                </td>
                                <td class="fieldTD" colspan="5">
                                    <select id="SelectEditBOMType" style="width: 110px !important"></select>

                                    <span class="sndcol"><label>Estimated Annual Usage</label> </span>
                                    <span><input type="text" id="TextEditBOMAnnualUsage" style="width: 150px !important" /></span>

                                </td>
                            </tr>
                            @*<tr>
                                    <td>
                                        Partial Quantity Acceptable
                                    </td>
                                    <td>
                                        <input type="checkbox" id="CheckEditBOMPartialQuantity" />
                                    </td>
                                </tr>*@
                            <tr>
                                <td class="lableTD">
                                    <label>Part No *</label>
                                </td>
                                <td>
                                    <label id="LabelEditBOMPartNo"></label>
                                </td>
                            </tr>
                            @*<tr>
                                    <td style="width:max-content">
                                        Add all suggested Rebound alternatives
                                    </td>
                                    <td>
                                        <input type="checkbox" id="CheckEditBOMAddAlternative" />
                                    </td>
                                </tr>*@
                            <tr id="RowEditBOMManufacturer">
                                <td class="lableTD">
                                    <label>Manufacturer *</label>
                                </td>
                                <td class="fieldTD" colspan="5">
                                    <input type="text" id="TextEditBOMManufacturer" style="display:none" />
                                    <input type="hidden" id="HDEditBOMManufacturerID" />

                                    <label id="LabelEditBOMManufacturer"></label><a id="RemoveEditBOMManufacturer" class="quickSearchReselect">[Reselect]</a>

                                </td>
                            </tr>
                            <tr>
                                <td class="lableTD">
                                    <label>Cust Part No</label>
                                </td>
                                <td class="fieldTD">
                                    <input type="text" id="TextEditBOMCustomerPart" />
                                </td>
                            </tr>
                            <tr id="RowEditBOMTargetPrice">
                                <td class="lableTD">
                                    <label>Customer Target Price *</label>
                                </td>
                                <td class="fieldTD">
                                    <input type="number" id="TextEditBOMTargetPrice" />
                                </td>
                            </tr>
                            @*<tr id="RowEditBOMCurrency">
                                    <td class="lableTD">
                                        <label>Currency *</label>
                                    </td>
                                    <td class="fieldTD">
                                        <select id="SelectEditBOMCurrency"></select>
                                    </td>
                                </tr>*@
                            <tr id="RowEditBOMCurrency">
                                <td class="lableTD">
                                    <label>Currency</label>
                                </td>
                                <td>
                                    <input type="hidden" id="HiddenEditBOMCurrency" />
                                    <label id="LabelEditBOMCurrency"></label>
                                </td>
                            </tr>
                            <tr>
                                <td class="lableTD">
                                    <label>RoHS</label>
                                </td>
                                <td class="fieldTD">
                                    <select id="SelectEditBOMROHS"></select>
                                </td>
                            </tr>
                            @*<tr>
                                    <td>
                                        Part Status
                                    </td>
                                    <td>
                                        <label id="LabelEditBOMPartStatus"></label>
                                    </td>
                                </tr>*@
                            <tr>
                                <td class="lableTD">
                                    <label>Date Code</label>
                                </td>
                                <td class="fieldTD">
                                    <input type="text" id="TextEditBOMDateCode" />
                                </td>
                            </tr>
                            <tr id="RowEditBOMProduct">
                                <td class="lableTD">
                                    <label>Product *</label>
                                </td>
                                <td class="fieldTD" colspan="5">
                                    <input type="text" id="TextEditBOMProduct" style="display:none" />
                                    <input type="hidden" id="HDEditBOMProductID" />


                                    <label id="LabelEditBOMProduct"></label><a id="RemoveEditBOMProduct" class="quickSearchReselect">[Reselect]</a>


                                </td>
                            </tr>
                            <tr>
                                <td class="lableTD">
                                    <label>Package</label>
                                </td>
                                <td class="fieldTD" colspan="5">
                                    <input type="text" id="TextEditBOMPackage" />
                                    <input type="hidden" id="HDEditBOMPackageID" />

                                    <label id="LabelEditBOMPackage"></label>
                                    <a id="RemoveEditBOMPackage" class="quickSearchReselect">[Reselect]</a>

                                </td>
                            </tr>
                            <tr>
                                <td class="lableTD">
                                    <label>MSL</label>
                                </td>
                                <td class="fieldTD">
                                    <select id="SelectEditBOMMSL"></select>
                                </td>
                            </tr>
                            <tr id="RowEditBOMDeliveryDate">
                                <td class="lableTD">
                                    <label>Delivery Date Required *</label>
                                </td>
                                <td class="fieldTD">
                                    <input type="text" id="TextEditBOMDeliveryDate">
                                </td>
                            </tr>
                            <tr>
                                <td class="lableTD">
                                    <label>RFQ Closing Date</label>
                                </td>
                                <td class="fieldTD">
                                    <input type="text" id="TextEditBOMRFQClosingDate">
                                </td>
                            </tr>
                            <tr>
                                <td class="lableTD">
                                    <label>Customer Decision Date</label>
                                </td>
                                <td class="fieldTD">
                                    <input type="text" id="TextEditBOMCustomerDecisionDate">
                                </td>
                            </tr>
                            <tr>
                                <td class="lableTD">
                                    <label>Quote Validity Required</label>
                                </td>
                                <td class="fieldTD">
                                    <select id="SelectEditBOMQuoteValidity"></select>
                                </td>
                            </tr>
                            <tr>
                                <td class="lableTD">
                                    <label>BOM Name</label>
                                </td>
                                <td>
                                    <input type="text" id="TextEditBOMBOMName">
                                </td>
                            </tr>
                            <tr>
                                <td class="lableTD"><label>Internal Notes</label></td>
                                <td colspan="2">
                                    <textarea id="EditBOMInternalNotes" maxlength="2000" style="width:260px; height:100px; float:left;"></textarea>
                                    <div style="float:left;display: inline-block; clear:both;"><label>Character Count (2000 chrs max) : </label></div>
                                </td>
                                <td class="lableTD">
                                    <label> Notes to Customer</label>
                                </td>
                                <td colspan="2">
                                    <textarea id="EditBOMCustomerNotes" maxlength="2000" style="width: 260px; height: 100px; float: left;"></textarea>
                                    <div style="float: left; display: inline-block; clear: both;"><label>Character Count (2000 chrs max) : </label></div>

                                </td>
                            </tr>
                            <tr>
                                <td class="lableTD"><label>Support Team Member to update</label></td>
                                <td class="fieldTD" colspan="5">

                                    <input type="text" id="TextEditBOMSupportMember" placeholder="Type 2 chars to search" />
                                    <input type="hidden" id="HDEditBOMSupportMemberID" />

                                    <label id="LabelEditBOMSupportMember"></label>
                                    <a id="RemoveEditBOMSupportMember" style="display:none" class="quickSearchReselect">[Reselect]</a>



                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
                <div class="footer_area">
                    <a id="btnConfirmModal" class="BtnSubmitEdit BtnSaveBOMEdit" rel="modal:close">Save</a>
                    &nbsp;&nbsp;&nbsp;&nbsp;
                    <a id="btnCancelModal" class="BtnCloseEdit BtnCancelBOMEdit" rel="modal:close">Cancel</a>
                </div>
            </div>
        </div>
    </div>
    <div class="modal bd-example-modal-lg" id="UpliftPriceModal" role="dialog">
        <div>
            <div class="modal-content" style="background-color: #AAE2A0;">
                <div class="main_header">
                    <h6> Uplift Price</h6>
                    <a id="btnConfirmModal1" class="ConfirmYes BtnConfirmUplift" rel="modal:close">Confirm</a>

                    <a id="btnCancelModal1" class="ConfirmNo BtnCancelUplift" rel="modal:close">Cancel</a>

                </div>
                <div class="form_container">
                    <div class="header_text">

                        <h4>Uplift Price of all items by Percentage</h4>
                    </div>
                    <div class="modal-body">
                        @*<div class="row">
                                <div class="col conf_message" style="width:200px !important">
                                    <label>Remove Uplift Percentage :</label>
                                </div>
                                <div class="col">
                                    <a id="btnConfirmModalRemoveUpliftAll" class="yes_icon" rel="modal:close">Yes</a>&nbsp;<a id="btnSBCancelModalRemoveUpliftAll" class="n_icon" rel="modal:close">No</a>
                                </div>
                            </div>*@
                        <div class="row">
                            <div class="col conf_message">
                                <label>Uplift Percentage : </label>
                                <input type="number" id="UpliftAllPercentage" /> <label> %</label> <button id="btnUpliftCalculate">Calculate</button>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col conf_message">
                                <label>Sourcing To Uplift : </label>
                                <div id="grid_up"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="footer_area">
                    <a id="btnConfirmModal" class="ConfirmYes BtnConfirmUplift" rel="modal:close">Confirm</a>
                    <a id="btnCancelModal" class="ConfirmNo BtnCancelUplift" rel="modal:close">Cancel</a>
                </div>
            </div>
        </div>
    </div>
    <div class="modal bd-example-modal-lg" id="BOMItemDetailModal" role="dialog">
        <div>
            <div class="modal-content" style="background-color: #AAE2A0;">
                <div class="main_header">
                    <h6>BOM Details</h6>
                    <a id="btnCancelModalDetail" class="ConfirmNo BtnCancelBOMDetail rel="modal:close">Close</a>

                </div>
                <div class="form_container form_details">
                    <div class="header_text">


                    </div>
                    <div class="modal-body bomitem_details">
                        <div id="bomItemDetails" class="item_details">
                            @*<label id="lblitembomcode"></label>*@
                            <table class="top_tbl">
                                <tbody>
                                    <tr>
                                        <td width="31%" style="float:left;">
                                            <table class="details">
                                                <tbody>

                                                    <tr>
                                                        <td class="desc">
                                                            Quantity
                                                        </td>
                                                        <td class="item"><label id="lblItemQuantity"></label></td>


                                                    </tr>
                                                    <tr>
                                                        <td class="desc">
                                                            Part no.
                                                        </td>
                                                        <td class="item"><label id="lblItemPartNo"></label></td>


                                                    </tr>
                                                    <tr>
                                                        <td class="desc">
                                                            ROHS
                                                        </td>
                                                        <td class="item"><label id="lblItemROHS"></label></td>


                                                    </tr>
                                                    <tr>
                                                        <td class="desc">
                                                            Cust Part No
                                                        </td>
                                                        <td class="item"><label id="lblItemCustPartNo"></label></td>


                                                    </tr>
                                                    <tr>
                                                        <td class="desc">
                                                            Manufacturer
                                                        </td>
                                                        <td class="item"><a href="" class="icon"><label id="lblItemManufacturer"></label></a></td>


                                                    </tr>
                                                    <tr>
                                                        <td class="desc">
                                                            Date Code
                                                        </td>
                                                        <td class="item"><label id="lblItemDateCode"></label></td>


                                                    </tr>
                                                    <tr>
                                                        <td class="desc">
                                                            Product
                                                        </td>
                                                        <td class="item"><label id="lblItemProduct"></label></td>


                                                    </tr>
                                                    <tr>
                                                        <td class="desc">
                                                            Duty Code(Rate %)
                                                        </td>
                                                        <td class="item"><label id="lblItemDutyCode"></label></td>


                                                    </tr>
                                                    <tr>
                                                        <td class="desc">
                                                            Package
                                                        </td>
                                                        <td class="item"><label id="lblItemPackage"></label></td>


                                                    </tr>
                                                    <tr>
                                                        <td class="desc">
                                                            Part Watch
                                                        </td>
                                                        <td class="item">

                                                            <input type="checkbox" class="sb-checkbox__input" name="check1" id="checkboxPartWatch" />
                                                            <label class="sb-checkbox__label sb-checkbox__label--green" for="check1"></label>

                                                        </td>

                                                    </tr>
                                                    <tr>
                                                        <td class="desc">
                                                            Factory Sealed (Y/N)
                                                        </td>
                                                        <td class="item">
                                                            <input type="checkbox" class="sb-checkbox__input" name="check1" id="checkboxFactory" />
                                                            <label class="sb-checkbox__label sb-checkbox__label--green" for="check1"></label>
                                                        </td>


                                                    </tr>
                                                    <tr>
                                                        <td class="desc">
                                                            MSL
                                                        </td>
                                                        <td class="item"><label id="lblItemMSL"></label></td>


                                                    </tr>
                                                    <tr>
                                                        <td class="desc">
                                                            Country of Origin
                                                        </td>
                                                        <td class="item"><label id="lblItemCountryOrigin"></label></td>


                                                    </tr>
                                                    <tr>
                                                        <td class="desc">
                                                            Part Status
                                                        </td>
                                                        <td class="item"><label id="lblItemPartStatus"></label></td>


                                                    </tr>
                                                    <tr>
                                                        <td class="desc">
                                                            IHS Product
                                                        </td>
                                                        <td class="item"><label id="lblItemIHSProduct"></label></td>


                                                    </tr>
                                                    <tr>
                                                        <td class="desc">
                                                            HTS Code
                                                        </td>
                                                        <td class="item"><label id="lblItemHTSCode"></label></td>


                                                    </tr>
                                                    <tr>
                                                        <td class="desc">
                                                            ECCN Code
                                                        </td>
                                                        <td class="item"><label id="lblItemECCNCode"></label><label id="imgECCNCodeWarning" class="ECCNIcon" style="display:none;"></label></td>


                                                    </tr>
                                                    <tr>
                                                        <td class="desc">
                                                            Packaging Size
                                                        </td>
                                                        <td class="item"><label id="lblItemPackagingSize"></label></td>


                                                    </tr>
                                                    <tr>
                                                        <td class="desc">
                                                            Description
                                                        </td>
                                                        <td class="item"><label id="lblItemDescription"></label></td>


                                                    </tr>



                                                </tbody>
                                            </table>


                                        </td>
                                        <td width="38%" style="float:left;">
                                            <table class="details">
                                                <tbody>

                                                    <tr>
                                                        <td class="desc">
                                                            Customer Target Price
                                                        </td>
                                                        <td class="item"><label id="lblItemCustomerTargetPrice"></label><a href="" class="blackbox"><img src="~/images/blackbox.gif" /></a></td>


                                                    </tr>
                                                    <tr>
                                                        <td class="desc">
                                                            Currency
                                                        </td>
                                                        <td class="item"><label id="lblItemCurrency"></label> (GBP)</td>


                                                    </tr>
                                                    <tr>
                                                        <td class="desc">
                                                            Customer Date Required
                                                        </td>
                                                        <td class="item"><label id="lblItemCustomerDateReq"></label></td>


                                                    </tr>
                                                    <tr>
                                                        <td class="desc">
                                                            Closed
                                                        </td>
                                                        <td class="item larger_font">
                                                            <input type="checkbox" class="sb-checkbox__input" name="checkBOMClosed" id="lblItemClosed" />
                                                            <label class="sb-checkbox__label sb-checkbox__label--green" for="checkBOMClosed"></label>
                                                        </td>


                                                    </tr>
                                                    @*<tr>
                                                            <td class="desc">
                                                                Reason 1
                                                            </td>
                                                            <td class="item"><label id="lblItemReason1"></label></td>


                                                        </tr>*@
                                                    <tr>
                                                        <td class="desc">
                                                            Usage
                                                        </td>
                                                        <td class="item"><label id="lblItemUsage"></label></td>


                                                    </tr>
                                                    <tr>
                                                        <td class="desc">
                                                            BOM
                                                        </td>
                                                        <td class="item">
                                                            <input type="checkbox" class="sb-checkbox__input" name="check1" id="checkboxBom" />
                                                            <label class="sb-checkbox__label sb-checkbox__label--green" for="check1"></label>

                                                        </td>


                                                    </tr>
                                                    <tr>
                                                        <td class="desc">
                                                            BOM Name
                                                        </td>
                                                        <td class="item"><label id="lblItemBOMName"></label></td>


                                                    </tr>
                                                    <tr>
                                                        <td class="desc">
                                                            Notes to customer
                                                        </td>
                                                        <td class="item"><label id="lblItemNoteToCustomer" class="more2"></label></td>


                                                    </tr>
                                                    <tr>
                                                        <td class="desc">
                                                            Internal Notes
                                                        </td>
                                                        <td class="item"><label id="lblItemINternalNotes" class="more3"></label></td>


                                                    </tr>
                                                    <tr>
                                                        <td class="desc">
                                                            Partial Quantity Acceptable
                                                        </td>
                                                        <td class="item">
                                                            <input type="checkbox" class="sb-checkbox__input" name="check1" id="checkboxPartial" />
                                                            <label class="sb-checkbox__label sb-checkbox__label--green" for="check1"></label>

                                                        </td>

                                                    </tr>
                                                    <tr>
                                                        <td class="desc">
                                                            Obsolete
                                                        </td>
                                                        <td class="item">
                                                            <input type="checkbox" class="sb-checkbox__input" name="check1" id="checkboxObsolete" />
                                                            <label class="sb-checkbox__label sb-checkbox__label--green" for="check1"></label>
                                                        </td>


                                                    </tr>
                                                    <tr>
                                                        <td class="desc">
                                                            Last time buy
                                                        </td>
                                                        <td class="item">
                                                            <input type="checkbox" class="sb-checkbox__input" name="check1" id="checkboxLast" />
                                                            <label class="sb-checkbox__label sb-checkbox__label--green" for="check1"></label>


                                                        </td>


                                                    </tr>
                                                    <tr>
                                                        <td class="desc">
                                                            Price Request
                                                        </td>
                                                        <td class="item"><label id="lblItemPriceRequest"></label></td>


                                                    </tr>


                                                </tbody>
                                            </table>


                                        </td>
                                        <td width="28%" style="float:right;">
                                            <table class="details">
                                                <tbody>

                                                    <tr>
                                                        <td class="desc">
                                                            Refurbs Acceptable
                                                        </td>
                                                        <td class="item">
                                                            <input type="checkbox" class="sb-checkbox__input" name="check1" id="checkboxRefurb" />
                                                            <label class="sb-checkbox__label sb-checkbox__label--green" for="check1"></label>


                                                        </td>


                                                    </tr>
                                                    <tr>
                                                        <td class="desc">

                                                            Testing Required
                                                        </td>
                                                        <td class="item">
                                                            <input type="checkbox" class="sb-checkbox__input" name="check1" id="checkboxTesting" />
                                                            <label class="sb-checkbox__label sb-checkbox__label--green" for="check1"></label>

                                                        </td>


                                                    </tr>
                                                    <tr>
                                                        <td class="desc">
                                                            Alternatives Accepted
                                                        </td>
                                                        <td class="item">
                                                            <input type="checkbox" class="sb-checkbox__input" name="check1" id="checkboxAlternative" />
                                                            <label class="sb-checkbox__label sb-checkbox__label--green" for="check1"></label>

                                                        </td>

                                                    </tr>
                                                    <tr>
                                                        <td class="desc">
                                                            Regular/Repeat business
                                                        </td>
                                                        <td class="item">
                                                            <input type="checkbox" class="sb-checkbox__input" name="check1" id="checkboxRegular" />
                                                            <label class="sb-checkbox__label sb-checkbox__label--green" for="check1"></label>


                                                        </td>


                                                    </tr>
                                                    <tr>
                                                        <td class="desc">
                                                            Competitor Best offer
                                                        </td>
                                                        <td class="item"><label id="lblItemCompetitor"></label></td>


                                                    </tr>
                                                    <tr>
                                                        <td class="desc">
                                                            Customer Decision Date
                                                        </td>
                                                        <td class="item"><label id="lblItemCustomerDecisionDate"></label></td>


                                                    </tr>
                                                    <tr>
                                                        <td class="desc">
                                                            RFQ Closing Date
                                                        </td>
                                                        <td class="item"><label id="lblItemRFQClosingDate"></label></td>


                                                    </tr>
                                                    <tr>
                                                        <td class="desc">
                                                            Quote Validity Required
                                                        </td>
                                                        <td class="item">
                                                            @*<input type="checkbox" class="sb-checkbox__input" name="check1"  />*@
                                                            <label id="checkboxQuoteValidity"></label>

                                                        </td>


                                                    </tr>
                                                    <tr>
                                                        <td class="desc">
                                                            Type
                                                        </td>
                                                        <td class="item"><label id="lblItemType"></label></td>


                                                    </tr>
                                                    <tr>
                                                        <td class="desc">
                                                            Order To Place
                                                        </td>
                                                        <td class="item">
                                                            <input type="checkbox" class="sb-checkbox__input" name="check1" id="checkboxOrder" />
                                                            <label class="sb-checkbox__label sb-checkbox__label--green" for="check1"></label>


                                                        </td>


                                                    </tr>
                                                    <tr>
                                                        <td class="desc">
                                                            Requirement for Traceability
                                                        </td>
                                                        <td class="item"><label id="lblItemRequirementTrace"></label></td>


                                                    </tr>
                                                    <tr>
                                                        <td class="desc">
                                                            Estimated Annual Usage
                                                        </td>
                                                        <td class="item"><label id="lblItemEstimated"></label></td>


                                                    </tr>


                                                </tbody>
                                            </table>


                                        </td>


                                    </tr>


                                </tbody>

                            </table>
                        </div>

                    </div>
                </div>
                <div class="footer_area">
                    <a id="btnCancelModalDetail1" class="ConfirmNo BtnCancelBOMDetail" rel="modal:close">Close</a>
                </div>
            </div>
        </div>
    </div>

    <div class="modal bd-example-modal-lg" id="AddCommunicationNoteModal">
        <div class="modal-content edit_modal" style="background-color: #AAE2A0;">
            <div class="main_header">
                <h6>BOM Items</h6>
                <a rel="modal:close"><span class="BtnSubmitEdit BtnSaveCommunicationNode">Save</span></a>
                &nbsp;&nbsp;&nbsp;
                <a class="BtnCloseEdit" id="BtnCamcelCommunicationNode" onclick="CancelCommunicationNode()" rel="modal:close">Cancel</a>
            </div>
            <div class="form_container">
                <div class="header_text">
                    <h4>ADD BOM COMMUNICATION NOTE</h4>
                    <div class="formInstructions">Add Communication Note</div>
                    <div style="display:none; margin-bottom:20px" id="CommunicationNoteValidationError" class="errorSummary">
                        There were some problems with your form<br>Please check below and try again.
                    </div>
                </div>
                <div class="formContent">
                    <input type="hidden" id="HDCustomerRequirementID" />
                    <table class="formRows tblmain_area" style="border-style:None;border-collapse:collapse;">
                        <tbody>
                            <tr>
                                <td>
                                    <label for="css">Send to</label><br>
                                </td>
                                <td>
                                    <div style="width: 100px; float: left;">
                                        <input type="radio" id="radioGroupNotes" name="CommunicationNodeRadio" value="Group" checked="checked">
                                        <label for="radioGroupNotes">Group</label>
                                    </div>
                                    &nbsp; &nbsp;
                                    <div style="width: 100px; float: left;">
                                        <input type="radio" id="radioIndividualNotes" name="CommunicationNodeRadio" value="Individual">
                                        <label for="radioIndividualNotes">Individual</label>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                        <tbody>
                            <tr id="RowSelectCommunicationNoteGroup">
                                <td class="lableTD">
                                    <label>Group *</label>
                                </td>
                                <td class="fieldTD">
                                    <select id="SelectCommunicationNoteGroup">
                                        <option value="0">-- Select --</option>
                                    </select>
                                </td>
                            </tr>

                            <tr id="RowCommunicationNoteIndividual">
                                <td class="lableTD">
                                    <label>To *</label>
                                </td>
                                <td class="fieldTD">
                                    <select id="SelectNoteIndividual">
                                        <option value="0">-- Select --</option>
                                    </select><a style="cursor:pointer;" id="RefreshNoteIndividual" class="refresh_icon"></a>
                                    <input type="text" id="txtIndividualNoteTo" style="width: 250px; display: none;" />
                                </td>
                            </tr>

                            <tr id="RowTxtCommunicationNotes">
                                <td class="lableTD"><label>Add Notes *</label></td>
                                <td colspan="2">
                                    <textarea id="txtCommunicationNotes" maxlength="2000" style="width:250px; height:100px; float:left;"></textarea>
                                    <div style="float:left;display: inline-block; clear:both;"><label>Character Count (2000 chrs max) : </label></div>
                                </td>
                            </tr>

                            <tr id="RowTxtCommunicationNoteCC">
                                <td><label>CC</label></td>
                                <td class="buyerfld_imput">
                                    <div class="lablearea" style="display: grid;">
                                        <div class='element-notes' id='div_2_BuyerCC'>
                                        </div>
                                    </div>
                                    <div>
                                        <input type="text" id="txtCommunicationNoteCC" />
                                    </div>

                                    <input type="hidden" id="HiddenCommunicationNoteMailReceiver" class="cc_input" />
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="footer_area">
                <a rel="modal:close"><span class="BtnSubmitEdit BtnSaveCommunicationNode">Save</span></a>
                &nbsp;&nbsp;&nbsp;
                <a class="BtnCloseEdit" id="BtnCamcelCommunicationNode" onclick="CancelCommunicationNode()" rel="modal:close">Cancel</a>
            </div>
        </div>
    </div>

    <!-- partial -->
    @*<script src="./script.js"></script>*@
<script type="text/javascript">
        var defaultOption = '<option value="0">&lt; Select &gt;</option>';
        var refershOption = '<option selected="true" value="Loading">loading</option>';
        var optionStart = "<option value='";
        var optionEnd = "</option>";
        var handlerUrl = window.location.origin + "/Controls/Nuggets/CrossMatch/CrossMatch.ashx";
        var RowIndexPartBOMGrid = -1;
        var RowIndexCommunicationNoteGrid = -1;
        var ReopenSendtoHubDialog = 0;
        $('#ShowBOMItemDiv').click(function () {
            if ($('#divBOMItemGrid').css("display") == 'none') {
                $('#divBOMItemGrid').css("display", 'block');
                $('#refreshBOMItem').css("display", 'block');
                $('#divBOMItemButtons').css("display", 'block');
                $('#headingThree').removeClass('headingOneCorner');
                $('#ShowBOMItemDiv').css("background-image", "url(/Areas/BOM/Images/hide.gif)");
            }
            else {
                $('#divBOMItemGrid').css("display", 'none');
                $('#refreshBOMItem').css("display", 'none');
                $('#divBOMItemButtons').css("display", 'none');

                $('#headingThree').addClass('headingOneCorner');
                $('#ShowBOMItemDiv').css("background-image", "url(/Areas/BOM/Images/show.gif)");
            }
        });

        $('#ShowNotesItemDiv').click(function () {
            if ($('#divNotesItemGrid').css("display") == 'none') {
                ExpandCommunicationNoteGrid();
            }
            else {
                CollapseCommunicationNoteGrid();
            }
        });

        function ExpandCommunicationNoteGrid() {
            $('#divNotesItemGrid').css("display", 'block');
            $('#refreshNotesItem').css("display", 'block');
            $('#headingThreeNote').removeClass('headingOneCorner');
            $('#ShowNotesItemDiv').css("background-image", "url(/Areas/BOM/Images/hide.gif)");
        }

        function CollapseCommunicationNoteGrid() {
            $('#divNotesItemGrid').css("display", 'none');
            $('#refreshNotesItem').css("display", 'none');
            $('#headingThreeNote').addClass('headingOneCorner');
            $('#ShowNotesItemDiv').css("background-image", "url(/Areas/BOM/Images/show.gif)");
        }


        $('#ShowBOMDataDiv').click(function () {
            if ($('#divBOMDataGrid').css("display") == 'none') {
                $('#divBOMDataGrid').css("display", 'block');
                $('#RefreshMainInfo').css("display", 'block');
                $('#divBOMDataButtons').css("display", 'block');
                $('#headingOne').removeClass('headingOneCorner');
                $('#ShowBOMDataDiv').css("background-image", "url(/Areas/BOM/Images/hide.gif)");
            } else {
                $('#divBOMDataGrid').css("display", 'none');
                $('#RefreshMainInfo').css("display", 'none');
                $('#divBOMDataButtons').css("display", 'none');

                $('#headingOne').addClass('headingOneCorner');
                $('#ShowBOMDataDiv').css("background-image", "url(/Areas/BOM/Images/show.gif)");
            }
        });

        $(document).ready(function () {
            var isPoHub = '@ViewBag.IsPoHub';
            var acc = document.getElementsByClassName("accordion");
            var i;
            $('#bomItemDetails').hide();
            var BOMManagerData;

            GetBOMManagerStatus();
            BindBOMManagerData();
            LoadBOMItemData();
            LoadCurrency();
            LoadContact();
            LoadEmployee();

            LoadPHBuyers();
            LoadMailGroup();
            LoadCommunicationNotesItemData();
            LoadGroupCommunicationNotes();
            LoadCommunicationNoteBuyers();
            LoadSalesperson();
            //$('#DeleteBOMItem').off('click');
            //$('#EditBOMItem').off('click');
            $("#grid_md .pq-page-next").click(function () {
                var gridInstanceRPP = $('#grid_md').pqGrid('instance');
                var rpp = gridInstanceRPP.options.pageModel.rPP;
                var totalRecords = gridInstanceRPP.options.pageModel.totalRecords;
                var curPage = gridInstanceRPP.options.pageModel.curPage;
                var recordsLeft = totalRecords - (rpp * curPage);
                BOMItemGridHeightChange(recordsLeft, rpp);

            });
            $("#grid_md .pq-page-prev").click(function () {
                var gridInstanceRPP = $('#grid_md').pqGrid('instance');
                var rpp = gridInstanceRPP.options.pageModel.rPP;
                var totalRecords = gridInstanceRPP.options.pageModel.totalRecords;
                var curPage = gridInstanceRPP.options.pageModel.curPage;
                var recordsLeft = totalRecords - (rpp * curPage);
                BOMItemGridHeightChange(recordsLeft, rpp);
            });
            $("#grid_md .pq-page-first").click(function () {
                var gridInstanceRPP = $('#grid_md').pqGrid('instance');
                var rpp = gridInstanceRPP.options.pageModel.rPP;
                var totalRecords = gridInstanceRPP.options.pageModel.totalRecords;
                var curPage = 1;
                var recordsLeft = totalRecords - (rpp * curPage);
                BOMItemGridHeightChange(recordsLeft, rpp);
            });
            $("#grid_md .pq-page-last").click(function () {
                var gridInstanceRPP = $('#grid_md').pqGrid('instance');
                var rpp = gridInstanceRPP.options.pageModel.rPP;
                var totalRecords = gridInstanceRPP.options.pageModel.totalRecords;
                var curPage = gridInstanceRPP.options.pageModel.totalPages;
                var recordsLeft = totalRecords - (rpp * curPage);
                BOMItemGridHeightChange(recordsLeft, rpp);
            });
            $("#grid_md .pq-page-select").change(function () {

                var gridInstanceRPP = $('#grid_md').pqGrid('instance');
                var rpp = gridInstanceRPP.options.pageModel.rPP;
                var totalRecords = gridInstanceRPP.options.pageModel.totalRecords;
                var curPage = gridInstanceRPP.options.pageModel.curPage;
                var recordsLeft = totalRecords - (rpp * curPage);
                BOMItemGridHeightChange(recordsLeft, rpp);
            });
            $("#grid_md .pq-page-current").change(function () {

                var gridInstanceRPP = $('#grid_md').pqGrid('instance');
                var rpp = gridInstanceRPP.options.pageModel.rPP;
                var totalRecords = gridInstanceRPP.options.pageModel.totalRecords;
                var curPage = $('#grid_md .pq-page-current').val();
                var recordsLeft = totalRecords - (rpp * curPage);
                BOMItemGridHeightChange(recordsLeft, rpp);
            });

            $("#grid_md_notes .pq-page-next").click(function () {
                var gridInstanceRPP = $('#grid_md_notes').pqGrid('instance');
                var rpp = gridInstanceRPP.options.pageModel.rPP;
                var totalRecords = gridInstanceRPP.options.pageModel.totalRecords;
                var curPage = gridInstanceRPP.options.pageModel.curPage;
                var recordsLeft = totalRecords - (rpp * curPage);
                NotesItemGridHeightChange(recordsLeft, rpp);

            });
            $("#grid_md_notes .pq-page-prev").click(function () {
                var gridInstanceRPP = $('#grid_md_notes').pqGrid('instance');
                var rpp = gridInstanceRPP.options.pageModel.rPP;
                var totalRecords = gridInstanceRPP.options.pageModel.totalRecords;
                var curPage = gridInstanceRPP.options.pageModel.curPage;
                var recordsLeft = totalRecords - (rpp * curPage);
                NotesItemGridHeightChange(recordsLeft, rpp);
            });
            $("#grid_md_notes .pq-page-first").click(function () {
                var gridInstanceRPP = $('#grid_md_notes').pqGrid('instance');
                var rpp = gridInstanceRPP.options.pageModel.rPP;
                var totalRecords = gridInstanceRPP.options.pageModel.totalRecords;
                var curPage = 1;
                var recordsLeft = totalRecords - (rpp * curPage);
                NotesItemGridHeightChange(recordsLeft, rpp);
            });
            $("#grid_md_notes .pq-page-last").click(function () {
                var gridInstanceRPP = $('#grid_md_notes').pqGrid('instance');
                var rpp = gridInstanceRPP.options.pageModel.rPP;
                var totalRecords = gridInstanceRPP.options.pageModel.totalRecords;
                var curPage = gridInstanceRPP.options.pageModel.totalPages;
                var recordsLeft = totalRecords - (rpp * curPage);
                NotesItemGridHeightChange(recordsLeft, rpp);
            });
            $("#grid_md_notes .pq-page-select").change(function () {

                var gridInstanceRPP = $('#grid_md_notes').pqGrid('instance');
                var rpp = gridInstanceRPP.options.pageModel.rPP;
                var totalRecords = gridInstanceRPP.options.pageModel.totalRecords;
                var curPage = gridInstanceRPP.options.pageModel.curPage;
                var recordsLeft = totalRecords - (rpp * curPage);
                NotesItemGridHeightChange(recordsLeft, rpp);
            });
            $("#grid_md_notes .pq-page-current").change(function () {

                var gridInstanceRPP = $('#grid_md_notes').pqGrid('instance');
                var rpp = gridInstanceRPP.options.pageModel.rPP;
                var totalRecords = gridInstanceRPP.options.pageModel.totalRecords;
                var curPage = $('#grid_md_notes .pq-page-current').val();
                var recordsLeft = totalRecords - (rpp * curPage);
                NotesItemGridHeightChange(recordsLeft, rpp);
            });
            DisableControl();
        });
        function DisableControl() {
            if ('@ViewBag.IsPoHub' == 'True') {
                $('#linkEdit').off('click');
                $('#EditBomLink').removeClass("edit");
                $('#EditBomLink').addClass("edit_disabled");

                $('#linkPurchaseHub').off('click');
                $('#SendToHubLink').removeClass("purchase_hub");
                $('#SendToHubLink').addClass("purchase_hub_disabled");


                $('#linkCloseHub').off('click');
                $('#BtnCloseBOM').off('click');
                $('#BtnCloseBOM').removeClass("close_icon");
                $('#BtnCloseBOM').addClass("close_icon_disabled");

                $('#QouteAllItems').off('click');
                $('#QouteAllItems').removeClass("quoteSeletedItem");
                $('#QouteAllItems').addClass("quoteSeletedItem_disabled");

                $('#DeleteBOMItem').off('click');
                $('#DeleteBOMItem').removeClass("delete");
                $('#DeleteBOMItem').addClass("delete_disabled");

                $('#EditBOMItem').off('click');
                $('#EditBOMItem').removeClass("edit");
                $('#EditBOMItem').addClass("edit_disabled");

                $('#QouteSeletedItem').off('click');
                $('#QouteSeletedItem').removeClass("quoteSeletedItem");
                $('#QouteSeletedItem').addClass("quoteSeletedItem_disabled");

                $('#PrimarySource').off('click');
                $('#PrimarySource').removeClass("quote");
                $('#PrimarySource').addClass("quote_disabled");
            }
        }
        function LoadBOMManagerData() {
            var bomjsondata;
            var qrystr = new URLSearchParams(window.location.search)
            var qrystrkey = qrystr.get("BOM");

            $.ajax({
                type: 'POST',
                url: 'GetBomManagerById',
                data: JSON.stringify({ BOMManagerId: qrystrkey }),
                contentType: 'application/json', // this
                datatype: 'json',
                async: false,
                //data: { clients: JSON.stringify(clients) }, // and this
                success: function (dataJSON) {
                    bomjsondata = dataJSON;
                },
                error: function (jqXhr, textStatus, errorMessage) {
                    console.log('Something went wrong');
                }

            });
            return bomjsondata;
        }
        function BindBOMManagerData() {
            BOMManagerData = LoadBOMManagerData();
            // console.log(BOMManagerData);
            if (BOMManagerData != "" || BOMManagerData != undefined) {
                $('#HdnvalidateMessage').val(BOMManagerData.ValidationMessage);
                $('#BOMItemsCount').val(BOMManagerData.BOMItemsCount);
                $('#lblBomCode').text(BOMManagerData.BOMManagerCode);
                $('#lblBomName').text(BOMManagerData.BOMManagerName);
                $('#HiddenBOMManagerName').val(BOMManagerData.BOMManagerName);
                $('#txtName').val(BOMManagerData.BOMManagerName);
                $('#textNotes').val(BOMManagerData.Notes);
                //var QouteDate = BOMManagerData.QuoteRequired;
                //var dt = new Date(parseInt(QouteDate.replace("/Date(", "").replace(")/", "")))
                /*var RDate = dt.getDate() + "/" + (dt.getMonth() + 1) + "/" + dt.getFullYear();*/
                var RDate = getCustomDate(BOMManagerData.QuoteRequired);
                $('#lblBomQoute').text(RDate);
                $('#HiddenBOMQuote').val(RDate);
                $('#mydatepicker').val(RDate);
                $('#lblBomQoute').text(RDate);
                $('#lblBomRequestedBy').text(BOMManagerData.Requestedby);
                $('#lblBomSalesperson').text(BOMManagerData.ReqSalesPersonName);
                $('#HiddenBomSalesperson').val(BOMManagerData.ReqSalesPersonName);

                $('#lblBomCC').text(BOMManagerData.Contact2Name);
                $('#HiddenBomCC').val(BOMManagerData.Contact2Id);
                $('#lblBomCompany').text(BOMManagerData.CompanyName);
                if (BOMManagerData.CompanyAdvisoryNotes) {
                    $('#lblAdvisoryNotes').attr('title', BOMManagerData.CompanyAdvisoryNotes.replace(/(<br>)/g, "&#10;"));
                    $('#lblAdvisoryNotes').css('display', 'inline');
                } else {
                    $('#lblAdvisoryNotes').css('display', 'none');
                }
                $('#HiddenBomCompanyNo').val(BOMManagerData.CompanyNo);
                $('#HiddenBomRequestToPOHubBy').val(BOMManagerData.RequestToPOHubBy);
                $('#HiddenBomUpdateByPH').val(BOMManagerData.UpdateByPH);

                $('#lblCmpny').text(BOMManagerData.CompanyName);
                var lastUpdatedDate = new Date(parseInt(BOMManagerData.DLUP.substr(6)));
                if (BOMManagerData.DLUPstr != null && BOMManagerData.DLUPstr != "") {
                    $('#LastUpdated').text("Last updated: " + BOMManagerData.DLUPstr + " by " + BOMManagerData.UpdateByName);
                }
                else {
                    $('#LastUpdated').text("Last updated: " + lastUpdatedDate.toDateString() + " " + lastUpdatedDate.getHours() + ":" + lastUpdatedDate.getMinutes() + " by " + BOMManagerData.UpdateByName);
                }

                $('#lblBomCurrency').text(BOMManagerData.CurrencyCode);
                $('#HiddenBomCurrency').val(BOMManagerData.CurrencyNo);
                //$("input[id=HiddenBomCurrency]").val(BOMManagerData.CurrencyNo)
                $('#lblBomContact').text(BOMManagerData.ContactName);
                $('#HiddenBomContact').val(BOMManagerData.ContactNo);
                $('#HiddenBomContact2No').val(BOMManagerData.Contact2Id);
                //$('#lblBomCurrentSupplier').text(BOMManagerData.CurrentSupplier);
                //$('#txtCurrentSup').val(BOMManagerData.CurrentSupplier);

                //source of supply required missing
                if (BOMManagerData.AS9120 == true) {
                    $('#chkSupplyRequired').prop("checked", true);
                    $('#chkSupplyRequiredEdit').prop("checked", true);
                }
                else {
                    $('#chkSupplyRequired').prop("checked", false);
                    $('#chkSupplyRequiredEdit').prop("checked", false);
                }
                $('#chkSupplyRequired').prop("disabled", true);



                if (BOMManagerData.Inactive == "true") {
                    $('#chkInactive').prop('checked', true);
                    $('#chkInactiveEdit').prop('checked', true);
                }
                else {
                    $('#chkInactive').prop('checked', false);
                    $('#chkInactiveEdit').prop('checked', false);
                }
                $('#chkInactive').text(BOMManagerData.Inactive);// checkbox inactive
                $('#chkInactive').prop("disabled", true);
                $('#lblBomReleasedBy').text(BOMManagerData.Releasedby);
                $('#lblBomAssignTo').text(BOMManagerData.AssignedUser);
                $('#lblBomNotes').text(BOMManagerData.Notes);
                OnNotesTextChange(); $(".morelink").trigger("click");
                if (BOMManagerData.StatusValue > 1) {
                    //$('#linkEdit').off('click');
                    //$('#EditBomLink').removeClass("edit");
                    //$('#EditBomLink').addClass("edit_disabled");

                    $('#linkPurchaseHub').off('click');
                    $('#SendToHubLink').removeClass("purchase_hub");
                    $('#SendToHubLink').addClass("purchase_hub_disabled");
                }
                if (BOMManagerData.StatusValue >= 6) {
                    $('#linkCloseHub').off('click');
                    $('#BtnCloseBOM').removeClass("close_icon");
                    $('#BtnCloseBOM').addClass("close_icon_disabled");
                }
                //if (BOMManagerData.StatusValue < 4 || BOMManagerData.StatusValue > 5) {
                //    //$('#LinkUpliftPriceAll').off('click');
                //    $('#BtnUpliftPriceAll').removeClass("notes");
                //    $('#BtnUpliftPriceAll').addClass("notes_disabled");
                //    //$('#LinkUpliftPriceAll').addClass("notes_disabled");
                //}

            }
        }
        var customerrequirementidval;

        function LoadBOMItemData(CId, refreshGrid) {
            var colModel = [

                {
                    dataIndx: 'ISPrimarySource',
                    dataType: 'bool',
                    hidden: true,
                    cb: { select: true, header: true }
                },
                {
                    title: "<label>Select All</label><br/><input type='checkbox' />",
                    cb: { header: true, select: false, all: false },
                    //type: 'checkbox',
                    width: "6%",
                    dataIndx: 'ISPrimarySource',
                    type: 'checkBoxSelection',
                    //cls: 'ui-state-default',
                    sortable: false,
                    //editable: true,
                    editable: function (ui) {
                        var BOMstatus = $('#BOMStatus').val();
                        if ('@ViewBag.IsPoHub' == 'True') {
                            return false;
                        }

                        if (BOMstatus > 3) {
                            if (ui.rowData.QuoteGenerated)
                                return false;
                            else {
                                return ui.rowData.ISPrimarySourceActual;
                            }
                        }
                        if (BOMstatus == 1) {
                            return true;
                        }
                    },

                    dataType: 'bool'//,
                    //cb: { header: false, select: true, all: true }
                },

                {
                    //add offer button
                    title: "",
                    width: "5%",
                    sortable: false,
                    render: function (ui) {
                        return "<button type='button' class='edit_btn'>Details</button>";
                    },
                    postRender: function (ui) {
                        var rowIndx = ui.rowIndx,
                            grid = this,
                            $cell = grid.getCell(ui);
                        if (grid.hasClass({ rowData: ui.rowData, cls: 'pq-row-edit' })) {
                            //Edit button
                            $cell.find(".edit_btn")
                                .button({ label: "Edit" })
                        }
                        else {
                            //edit button
                            $cell.find(".edit_btn").button()
                                .off("click")
                                .on("click", function (evt) {
                                    ViewBOMItemDetails(rowIndx, grid, ui);
                                });
                        }
                    }
                },
                {
                    title: "CustomerRequirementId", //title of column.
                    width: "0%", //initial width of column
                    //dataType: "integer", //data type of column
                    dataIndx: "CustomerRequirementId",
                    hidden: true,
                    //should match one of the keys in row data.
                    //render: function (ui) {
                    //    customerrequirementidval = ui.rowData.CustomerRequirementId.toString();
                    //    console.log(customerrequirementidval + '-----');
                    //}
                },
                {
                    title: "", //title of column.
                    width: "0%", //initial width of column
                    //dataType: "integer", //data type of column
                    dataIndx: "BOMManagerNo", //should match one of the keys in row data.
                    hidden: true,
                    //render: function (ui) {
                    //    customerrequirementidval = ui.rowData.CustomerRequirementId.toString();
                    //    console.log(customerrequirementidval + '-----');
                    //}
                },
                {
                    title: "Part No <br/> Quote No",
                    width: "17%",
                    //dataType: "string",
                    dataIndx: "Part",
                    render: function (ui) {

                        var htmlstr = "<span>" + ui.rowData.Part.toString() + "</span ><br/>";
                        if ('@ViewBag.IsPoHub' == 'True')
                            return htmlstr;
                        if (ui.rowData.QuoteId != null)
                            htmlstr = htmlstr + "<span><a href='/Ord_QuoteDetail.aspx?qt=" + ui.rowData.QuoteId + "' target='_top'>" + "Quote : " + ui.rowData.QuoteNumber + "</a></span>";
                        return htmlstr;
                    }
                },
                {
                    title: "Status",
                    width: "13%",
                    //dataType: "string",
                    dataIndx: "ReqStatusText",
                    filter: { crules: [{ condition: 'range' }] },
                    menuIcon: true,
                    render: function (ui) {
                        var htmlstr;
                        if (ui.rowData.ReqStatusText.toString() == 'Recalled') {
                            htmlstr = "<div class='BOMItemStatus' style='background-color:#FFBF00;position: absolute;width: 99.5%;left: 0px;padding: 0px 5px;height: 33px;top: 0px;'> <span style='padding: 3px 3px;display:block;' class='OfferAddedBOMItem'>" + ui.rowData.ReqStatusText.toString() + "</span ></Div>";
                        }
                        else {
                            htmlstr = " <span title='" + ui.rowData.ReqStatusText.toString() + "'>" + ui.rowData.ReqStatusText.toString() + "</span >";

                        }
                        if (parseInt(ui.rowData.OfferCount) > 0 && parseInt(ui.rowData.ReqStatus) > 3) {
                            htmlstr = htmlstr + " <span title='" + ui.rowData.OfferCount.toString() + " Offer(s)" + "'>-" + ui.rowData.OfferCount.toString() + " Offer(s)" + "</span >  </br>";
                            if (ui.rowData.ISPrimarySource == true)
                                htmlstr = htmlstr + " <span style='color: green;' title='Primary Sourcing Selected'>" + "Primary Sourcing Selected" + "</span > ";
                            else
                                htmlstr = htmlstr + " <span style='color: red;' title='Primary Sourcing not Selected'>" + "Primary Sourcing not Selected" + "</span > ";
                        }
                        return htmlstr;
                    }
                },
                {
                    title: "Quantity",
                    width: "6%",
                    //dataType: "float",
                    //align: "right",
                    dataIndx: "Quantity"
                },
                {
                    title: "Manufacturer <br/> Date Code",
                    width: "10%",
                    /*dataType: "float",*/
                    //align: "right",
                    dataIndx: "ManufacturerCode",
                    render: function (ui) {
                        var htmlstr = "<span><a  href='../../Con_ManufacturerDetail.aspx?mfr=" + ui.rowData.ManufacturerNo + "' target='_top'>" + ui.rowData.ManufacturerCode + "</a></span>";
                        if (ui.rowData.MfrAdvisoryNotes) {
                            htmlstr += '<span class="advisory-notes" title="' + ui.rowData.MfrAdvisoryNotes + '"></span><br>';
                        } else {
                            htmlstr += '<br>';
                        }

                        htmlstr = htmlstr + " <span>" + ui.rowData.DateCode.toString() + "</span > ";
                        return htmlstr;
                    }
                },
                {
                    title: "Product <br/> Package",
                    width: "10%",
                    dataType: "Date",
                    //align: "right",
                    dataIndx: "ProductName",
                    render: function (ui) {
                        var htmlstr = '<span title="' + ui.rowData.ProductName + '">' + ui.rowData.ProductName.toString() + "</span ><br/>";
                        htmlstr = htmlstr + '<span title="' + ui.rowData.PackageName + '">' + ui.rowData.PackageName.toString() + "</span > ";
                        return htmlstr;
                    }
                },
                //{
                //    title: "Customer <br/> Required",
                //    width: "10%",
                //    /*dataType: "float",*/
                //    //align: "right",
                //    dataIndx: "CompanyName",
                //    render: function (ui) {
                //        var htmlstr = "<span>" + ui.rowData.CompanyName.toString() + "</span ><br/>";
                //        htmlstr = htmlstr + " <span>" + getCustomDate(ui.rowData.DatePromised.toString()) + "</span > ";
                //        return htmlstr;
                //    }
                //},
                {
                    title: "Release By",
                    width: "9%",
                    filter: { crules: [{ condition: 'range' }] },
                    menuIcon: true,
                    dataIndx: "Releasedby",
                    render: function (ui) {
                        var htmlstr = '<span title="' + ui.rowData.Releasedby + '">' + ui.rowData.Releasedby + "</span ><br/>";
                        return htmlstr;
                    }
                },
                {
                    title: "Target Price <br/> MSL",
                    width: "9%",
                    /*dataType: "float",*/
                    //align: "right",
                    dataIndx: "MSL",
                    render: function (ui) {
                        var htmlstr = "<span>" + parseFloat(ui.rowData.ConvertedTargetValue).toFixed(4) + " " + ui.rowData.CurrencyCode + "</span ><br/>";
                        htmlstr += "<span>" + ui.rowData.MSL + "</span ><br/>";
                        return htmlstr;
                    }
                },
                {
                    title: "Release Notes",
                    width: "8%",
                    /*dataType: "float",*/
                    //align: "right",
                    dataIndx: "ReleaseNote",
                    render: function (ui) {
                        var htmlstr = '<span title="' + ui.rowData.ReleaseNote + '">' + ui.rowData.ReleaseNote + "</span ><br/>";
                        return htmlstr;
                    }
                },
                {
                    title: "Notes",
                    width: "9%",
                    /*dataType: "float",*/
                    //align: "right",
                    dataIndx: "Notes",
                    render: function (ui) {
                        var htmlstr = '<span title="' + ui.rowData.Notes + '">' + ui.rowData.Notes + "</span ><br/>";
                        return htmlstr;
                    }
                }

            ];

            //main object to be passed to pqGrid constructor.
            debugger;
            var qrystr = new URLSearchParams(window.location.search);
            var qrystrkey = qrystr.get("BOM");

            //var dataModel = GetBOMSearchData1();
            var dataModel = {
                location: "remote",
                dataType: "JSON",
                method: "POST",
                url: 'GetBOMItem?BOMManagerId=' + qrystrkey + '&clientId=' + 0,
                //data: { BOMId: BOMID, clientId: clientid, BOMDate: BomDateVal },
                //url: "/pro/invoice.php",//for PHP
                getData: function (dataJSON) {
                    var data = dataJSON;
                    if ($('#BOMStatus').val() > 3) {
                        $.each(data, function (data1, value) {
                            if (value.ISPrimarySource == true && value.QuoteGenerated == false) {
                                //debugger;
                                $('#QouteSeletedItem').on('click', function () { QouteSeletedItemClickEvent(); });
                                $('#QouteSeletedItem').removeClass("quoteSeletedItem_disabled");
                                $('#QouteSeletedItem').addClass("quoteSeletedItem");
                                return false;
                            }
                            else {
                                //debugger;
                                $('#QouteSeletedItem').off('click');
                                $('#QouteSeletedItem').removeClass("quoteSeletedItem");
                                $('#QouteSeletedItem').addClass("quoteSeletedItem_disabled");
                            }
                        });
                        // to set Quote All button
                        var checkallitemsourced = false;
                        $.each(data, function (data1, value) {
                            if (value.ISPrimarySource == true && value.QuoteGenerated == false) {
                                //debugger;

                                $('#QouteAllItems').removeClass("quoteSeletedItem_disabled");
                                $('#QouteAllItems').addClass("quoteSeletedItem");
                                checkallitemsourced = true;
                            }
                            else {
                                //debugger;
                                $('#QouteAllItems').off('click');
                                $('#QouteAllItems').removeClass("quoteSeletedItem");
                                $('#QouteAllItems').addClass("quoteSeletedItem_disabled");
                                checkallitemsourced = false;
                                return false;
                            }
                        });
                        if (checkallitemsourced == true)
                            $('#QouteAllItems').on('click', function () { QouteAllItemClickEvent(); });
                        console.log(data);
                        $('#PrimarySource').removeClass("quote");
                        $('#PrimarySource').addClass("quote_disabled");
                    }

                    var totalRecords = 0;
                    if (data != 0) {
                        totalRecords = data[0].TotalCount;
                    }
                    var cur_page = 1;
                    if (dataJSON.length != 0) {
                        cur_page = dataJSON[0].curpage;
                    }
                    return { curPage: cur_page, totalRecords: totalRecords, data: data };
                }
            };
            //$gridPrj = $("#grid_md").pqGrid(obj).pqGrid('refreshDataAndView');
            var grid1 = $("#grid_md").pqGrid({
                width: "auto", height: 272,
                selectionModel: { type: 'row', mode: 'single', native: true },
                dataModel: dataModel,
                rowHt: 35,

                selectChange: function (evt, ui) {
                    var address = ui.selection.address();
                    $("#select_change_div").html(JSON.stringify(address));
                },
                colModel: colModel,
                editable: false,
                postRenderInterval: -1,
                //freezeCols: 2,
                rowHt: 35,
                hwrap: false,
                wrap: false,
                hoverMode: 'cell',
                pageModel: { type: "local", rPP: 5, strRpp: "{0}", rPPOptions: [5, 10, 15, 20, 25] },
                numberCell: { show: false },
                complete: function (event, ui) {
                    var gridInstance = grid1.pqGrid('getInstance');
                    if (RowIndexPartBOMGrid > -1) {
                        gridInstance.grid.SelectRow().add({ rowIndx: RowIndexPartBOMGrid });
                        RowIndexPartBOMGrid = -1;
                    }

                    var gridInstanceRPP = $('#grid_md').pqGrid('instance');
                    var rpp = gridInstanceRPP.options.pageModel.rPP;
                    var totalRecords = gridInstanceRPP.options.pageModel.totalRecords;
                    var curPage = gridInstanceRPP.options.pageModel.curPage;
                    var recordsLeft = totalRecords - (rpp * curPage);
                    BOMItemGridHeightChange(recordsLeft, rpp);
                    DisableControl();
                },
                rowSelect: function (evt, ui) {

                    var BOMStatus = $('#BOMStatus').val();
                    if (ui.addList[0] != undefined) {
                        var CustomerRequirementIddata = ui.addList[0].rowData.CustomerRequirementId;
                        $('#HDCustomerRequirementID').val(CustomerRequirementIddata);
                        //GetBOMItemwiseDetails(CustomerRequirementIddata);
                        if (BOMStatus > 3)
                            $('#BOMManagerSourcingGrid').css("display", "block");
                        LoadAutoSourceData(CustomerRequirementIddata);
                        $('#EditSourcing').removeClass("edit");
                        $('#Quote').removeClass("quote");
                        $('#EditSourcing').addClass("edit_disabled");
                        $('#Quote').addClass("quote_disabled");
                        $('#PrimarySource').removeClass("quote");
                        $('#PrimarySource').addClass("quote_disabled");
                        ResetQuoteForm();
                        ResetEditSourcingForm();
                        LoadCommunicationNotesItemData(ui.addList[0].rowData.CustomerRequirementNumber, true, true);


                        if (BOMStatus < 7) {
                            $('#AddCommunicationNote').removeClass("add_comm_note_disabled");
                            $('#AddCommunicationNote').addClass("add_comm_note");
                        } else {
                            $('#AddCommunicationNote').off('click');
                            $('#AddCommunicationNote').removeClass("add_comm_note");
                            $('#AddCommunicationNote').addClass("add_comm_note_disabled");
                        }

                        if (BOMStatus == 1) {
                            if ('@ViewBag.IsPoHub' == 'True') return;

                            $('#DeleteBOMItem').removeClass("delete_disabled");
                            $('#DeleteBOMItem').addClass("delete");
                            //$('#DeleteConfirmationPartNo').text(ui.addList[0].rowData.Part);
                            //$('#HDCustReqID').val(CustomerRequirementIddata);
                            $('#EditBOMItem').removeClass("edit_disabled");
                            $('#EditBOMItem').addClass("edit");

                            ResetEditBOMForm();
                        }
                        else {
                            $('#DeleteBOMItem').removeClass("delete");
                            $('#DeleteBOMItem').addClass("delete_disabled");
                            $('#EditBOMItem').removeClass("edit");
                            $('#EditBOMItem').addClass("edit_disabled");
                        }

                        //load KUB assistant
                        var kubIFrame = window.parent.frames['ctl00_cphMain_kubIframe'];
                        kubIFrame.contentWindow.LoadKUBDetails(ui.addList[0].rowData, qrystrkey);
                    }
                    else {
                        $('#DeleteBOMItem').removeClass("delete");
                        $('#DeleteBOMItem').addClass("delete_disabled");
                        $('#DeleteBOMItem').off('click');
                        $('#EditBOMItem').removeClass("edit");
                        $('#EditBOMItem').addClass("edit_disabled");
                        $('#EditBOMItem').off('click');
                    }
                }
            });

            $("#grid_md").pqGrid({
                beforeCheck: function (event, ui) {

                }
            });
        }

        function LoadCommunicationNotesItemData(CusReqNo, refreshGrid, hasRowSelected) {
            var colModel = [
                {
                    title: "Line#",
                    width: "4%",
                    /*dataType: "float",*/
                    //align: "right",
                    dataIndx: "Line",
                    render: function (ui) {
                        var htmlstr = ui.rowData["Line"];
                        return htmlstr;
                    }
                },
                {
                    title: "Notes", //title of column.
                    width: "42%", //initial width of column
                    //dataType: "integer", //data type of column
                    dataIndx: "Note",
                    render: function (ui) {
                        var htmlstr = '<span title="' + ui.rowData.Note + '">' + ui.rowData.Note + "</span ><br/>";
                        return htmlstr;
                    }
                },
                {
                    title: "Date",
                    width: "13%",
                    dataIndx: "Date",
                    render: function (ui) {
                        var htmlstr = '<span title="' + ui.rowData.DateTimeNote + '">' + ui.rowData.DateTimeNote + "</span ><br/>";
                        return htmlstr;
                    }
                },
                {
                    title: "By",
                    width: "13%",
                    dataIndx: "EmployeeName",
                    render: function (ui) {
                        var htmlstr = '<span title="' + ui.rowData.EmployeeName + '">' + ui.rowData.EmployeeName + "</span ><br/>";
                        return htmlstr;
                    }
                },
                {
                    title: "To <br/> CC",
                    width: "18%",
                    dataIndx: "NoteTo",
                    render: function (ui) {
                        var htmlstr = '<span title="' + ui.rowData.NoteTo + '">' + ui.rowData.NoteTo + "</span ><br/>";
                        htmlstr += '<span title="' + ui.rowData.CCUserID + '">' + ui.rowData.CCUserID + "</span ><br/>"
                        return htmlstr;
                    }
                },
                {
                    title: "Send To Group",
                    width: "10%",
                    dataIndx: "SendToGroup",
                    render: function (ui) {
                        var htmlstr = '<span title="' + ui.rowData.SendToGroup + '">' + ui.rowData.SendToGroup + "</span ><br/>";
                        return htmlstr;
                    }
                }
            ];

            //main object to be passed to pqGrid constructor.
            var qrystr = new URLSearchParams(window.location.search);
            var qrystrkey = qrystr.get("BOM");
            var noteNo = qrystr.get("Note");
            CusReqNo = parseInt(CusReqNo) > 0 ? CusReqNo : 0;

            var dataModel = {
                location: "remote",
                dataType: "JSON",
                method: "POST",
                url: 'GetComunicationNotes?BOMManagerId=' + qrystrkey + '&CustomerReqNo=' + CusReqNo,
                //data: { BOMId: BOMID, clientId: clientid, BOMDate: BomDateVal },
                getData: function (dataJSON) {
                    var data = dataJSON;
                    var totalRecords = 0;
                    if (data != 0) {
                        totalRecords = data[0].TotalCount;
                    }

                    if (hasRowSelected == true) {
                        ExpandCommunicationNoteGrid();
                    }
                    else {
                        if (totalRecords <= 0) {
                            CollapseCommunicationNoteGrid();
                        }
                        else {
                            ExpandCommunicationNoteGrid();
                        }
                    }


                    var cur_page = 1;
                    if (dataJSON.length != 0) {
                        cur_page = dataJSON[0].curpage;
                    }
                    return { curPage: cur_page, totalRecords: totalRecords, data: data };
                }
            };
            //$gridPrj = $("#grid_md").pqGrid(obj).pqGrid('refreshDataAndView');
            var grid1 = $("#grid_md_notes").pqGrid({
                width: "auto", height: 272,
                selectionModel: { type: 'row', mode: 'single', native: true },
                dataModel: dataModel,
                rowHt: 35,

                colModel: colModel,
                editable: false,
                postRenderInterval: -1,
                //freezeCols: 2,
                rowHt: 35,
                hwrap: false,
                wrap: false,
                hoverMode: 'cell',
                pageModel: { type: "local", rPP: 5, strRpp: "{0}", rPPOptions: [5, 10, 15, 20, 25] },
                numberCell: { show: false },
                complete: function (event, ui) {
                    var gridInstance = grid1.pqGrid('getInstance');
                    if (RowIndexCommunicationNoteGrid > -1) {

                        gridInstance.grid.SelectRow().add({ rowIndx: RowIndexCommunicationNoteGrid });
                        RowIndexCommunicationNoteGrid = -1;
                    }

                    // Communication Note line highlighted while clicking the BOM Item hyperlink from Email.
                    if (parseInt(noteNo) > 0) {
                        $.each(gridInstance.grid.pdata, function (index, value) {
                            if (value.ID == noteNo) {
                                gridInstance.grid.SelectRow().add({ rowIndx: index });
                            }
                        });
                    }
                    var gridInstanceRPP = $('#grid_md_notes').pqGrid('instance');
                    var rpp = gridInstanceRPP.options.pageModel.rPP;
                    var totalRecords = gridInstanceRPP.options.pageModel.totalRecords;
                    var curPage = gridInstanceRPP.options.pageModel.curPage;
                    var recordsLeft = totalRecords - (rpp * curPage);
                    NotesItemGridHeightChange(recordsLeft, rpp);
                }
            });

            if (refreshGrid) {
                grid1.pqGrid('refreshDataAndView');
            }
        }

        function QouteSeletedItemClickEvent() {
            //alert('QouteSeletedItem');
            $('#hdnMultiQuote').val("1");
            var selectedCustomerRequirementIds = '';
            var grid = $('#grid_md').pqGrid('instance');
            //debugger;
            //var checked = grid.Checkbox('ISPrimarySource').getCheckedNodes().map(function (rd) {
            //    //console.log(rd.CustomerRequirementId);
            //    selectedCustomerRequirementIds = selectedCustomerRequirementIds + rd.CustomerRequirementId + '|';
            //});
            //console.log(selectedCustomerRequirementIds);
            //debugger;
            //if (selectedCustomerRequirementIds == '') {
            //    alert('Plesae select any item.')
            //    return;
            //}
            $.each(grid.pdata, function (data1, value) {
                //console.log(data1 + '___________' + value.ISPrimarySource + '++++' + value.QuoteGenerated);
                if (value.ISPrimarySource == true && value.QuoteGenerated == false) { selectedCustomerRequirementIds = selectedCustomerRequirementIds + value.CustomerRequirementId + '|'; }

            });
            console.log(selectedCustomerRequirementIds);
            if (selectedCustomerRequirementIds == '') {
                alert('Plesae select any item.')
                return;
            }

            //if ($('#Quote_Customer_ReqID').val() == "" || $('#Quote_Customer_ReqID').val() == null) {
            //    console.log("Please Select Sourcing");
            //}
            //else {
            LoadQuoteGenerationData($('#Quote_Customer_ReqID').val());
            var modal = document.getElementById("QuoteModal");
            modal.style.display = "block"
            $('html, body', window.parent.document).animate({ scrollTop: 240 }, "fast");
            //}
        };
        function QouteAllItemClickEvent() {
            //alert('QouteSeletedItem');
            $('#hdnMultiQuote').val("1");
            var selectedCustomerRequirementIds = '';
            var grid = $('#grid_md').pqGrid('instance');
            grid.Checkbox('ISPrimarySource').checkAll();
            var checked = grid.Checkbox('ISPrimarySource').getCheckedNodes().map(function (rd) {
                //console.log(rd.CustomerRequirementId);
                selectedCustomerRequirementIds = selectedCustomerRequirementIds + rd.CustomerRequirementId + '|';
            });
            console.log(selectedCustomerRequirementIds);

            //if ($('#Quote_Customer_ReqID').val() == "" || $('#Quote_Customer_ReqID').val() == null) {
            //    console.log("Please Select Sourcing");
            //}
            //else {
            LoadQuoteGenerationData($('#Quote_Customer_ReqID').val());
            var modal = document.getElementById("QuoteModal");
            modal.style.display = "block"
            //$('html, body', window.parent.document).animate({ scrollTop: 240 }, "fast");
            //}
        };
        function BindBOMItemwiseDetails(CusReqId) {
            var returnjsondata;
            $.ajax({
                type: "POST",
                url: "GetBOMManagerItemDetails",
                data: JSON.stringify({ CustomerRequirementId: CusReqId }),
                dataType: "json",
                contentType: "application/json",
                async: false,
                success: function (Jsondata) {
                    returnjsondata = Jsondata;
                }

            });
            return returnjsondata;
        }

        function getCustomDate(dtinput) {
            var ParamDate = dtinput;
            if (dtinput != null && dtinput != "/Date(-62135596800000)/") {
                var dt = new Date(parseInt(ParamDate.replace("/Date(", "").replace(")/", "")))
                return AppendZero(dt.getDate()) + "/" + AppendZero(dt.getMonth() + 1) + "/" + AppendZero(dt.getFullYear());
            }
            else {
                return '';
            }
        }

        function AppendZero(n) {
            return (n < 10) ? '0' + n : n;
        }

        function GetBOMItemwiseDetails(CusReqId) {
            var CusReqItem = BindBOMItemwiseDetails(CusReqId)
            // console.log(CusReqItem);
            //$('#bomItemDetails').hide();
            //Binding bommanager line items
            $('#lblItemQuantity').text(CusReqItem.Quantity);
            $('#lblItemPartNo').text(CusReqItem.FullPart);
            $('#lblItemROHS').text(writeROHS(CusReqItem.ROHS));
            $('#lblItemCustPartNo').text(CusReqItem.CustomerPart);
            $('#lblItemManufacturer').text(CusReqItem.ManufacturerName);
            $('#lblItemDateCode').text(CusReqItem.DateCode);
            $('#lblItemProduct').text(CusReqItem.ProductName);
            $('#lblItemDutyCode').text(CusReqItem.DutyCode);
            $('#lblItemPackage').text(CusReqItem.PackageName);


            $('#checkboxPartWatch').prop("checked", CusReqItem.PartWatch);
            $('#checkboxFactory').prop("checked", CusReqItem.FactorySealed);
            $('#lblItemMSL').text(CusReqItem.MSL);

            $('#lblItemCountryOrigin').text(CusReqItem.CountryOfOrigin);
            $('#lblItemPartStatus').text(CusReqItem.PartEditStatus);
            $('#lblItemIHSProduct').text(CusReqItem.IHSProduct);
            $('#lblItemHTSCode').text(CusReqItem.HTSCode);
            $('#lblItemECCNCode').text(CusReqItem.ECCNCode);
            if (CusReqItem.IHSECCNCodeDefination != "") {
                $('#imgECCNCodeWarning').css("display", "inline");
                $('#imgECCNCodeWarning').prop("title", CusReqItem.IHSECCNCodeDefination);
            }
            $('#lblItemPackagingSize').text(CusReqItem.PackagingSize);
            $('#lblItemDescription').text(CusReqItem.Descriptions);
            $('#lblItemCustomerTargetPrice').text(parseFloat(CusReqItem.Price).toFixed(4) + " " + CusReqItem.CurrencyCode);
            $('#lblItemCurrency').text(CusReqItem.CurrencyDescription);

            $('#lblItemCustomerDateReq').text(getCustomDate(CusReqItem.DatePromised));
            if ($('#BOMStatus').val() == 7) {
                $('#lblItemClosed').prop("checked", true);// true false then yes no
            }
            else {
                $('#lblItemClosed').prop("checked", false);// true false then yes no
            }
            $('#lblItemReason1').text(CusReqItem.ClosedReason);
            $('#lblItemUsage').text(CusReqItem.UsageName);
            $('#checkboxBom').prop("checked", CusReqItem.BOM);
            //$('#lblItemBOMName').text(CusReqItem.BOMName);
            $('#lblItemNoteToCustomer').text(CusReqItem.Notes);
            OnNotesCustomerChange(); $(".morelink2").trigger("click");

            $('#lblItemINternalNotes').text(CusReqItem.Instructions);
            OnInternalNoteChange(); $(".morelink3").trigger("click");


            //$('#checkboxPartial').text(CusReqItem.Quantity);// note found
            $('#checkboxObsolete').prop("checked", CusReqItem.Obsolete);
            $('#checkboxLast').prop("checked", CusReqItem.LastTimeBuy);
            $('#lblItemPriceRequest').text(parseFloat(CusReqItem.Price).toFixed(4) + " " + CusReqItem.CurrencyCode);
            $('#checkboxRefurb').prop("checked", CusReqItem.RefirbsAcceptable);
            $('#checkboxTesting').prop("checked", CusReqItem.TestingRequired);
            $('#checkboxAlternative').prop("checked", CusReqItem.Alternate);
            //$('#checkboxRegular').prop("checked", CusReqItem.Quantity);//note found
            $('#lblItemCompetitor').text(parseFloat(CusReqItem.CompetitorBestOffer).toFixed(4) + " " + CusReqItem.CurrencyCode);
            $('#lblItemCustomerDecisionDate').text(getCustomDate(CusReqItem.CustomerDecisionDate));
            $('#lblItemRFQClosingDate').text(getCustomDate(CusReqItem.RFQClosingDate));
            $('#lblItemRFQClosingDate').text(getCustomDate(CusReqItem.RFQClosingDate));
            $('#checkboxQuoteValidity').text(writeQuoteValidity(CusReqItem.QuoteValidityRequired));
            $('#lblItemType').text(CusReqItem.ReqTypeText);
            $('#checkboxOrder').prop("checked", CusReqItem.OrderToPlace);
            $('#lblItemRequirementTrace').text(CusReqItem.ReqForTraceabilityText);
            $('#lblItemEstimated').text(CusReqItem.EAU);
            //Binding bommanager line items


            //console.log('rahil');
            //console.log(returnjsondata);
            $('#lblitembomcode').text('This is div content. The customer requirement id is ' + CusReqId);
            $('#bomItemDetails').show();
        }
        function LoadCurrency() {
            var Crncyval = $('#HiddenBomCurrency').val();
            var valmatch = false;
            $.ajax({
                type: "POST", url: "GetCurrencyList",
                dataType: "json",
                contentType: "application/json",
                success: function (Jsondata) {
                    $.each(Jsondata, function (data, value) {
                        if (Crncyval == value.CurrencyId) {
                            valmatch = true;
                        }
                        $("#ddlCurrency").append($("<option></option>").val(value.CurrencyId).html(value.CurrencyCode + ' - ' + value.CurrencyDescription));
                    });
                    if (valmatch == true)
                        $('#ddlCurrency').val(Crncyval);
                    else
                        $('#ddlCurrency').val(0);
                }

            });
        }

        function LoadContact() {
            var Cntctval = $('#HiddenBomContact').val();
            var valmatch = false;
            $.ajax({
                type: "GET", url: "GetCompanyNameList",
                dataType: "json",
                contentType: "application/json",
                success: function (Jsondata) {
                    $.each(Jsondata, function (data, value) {
                        if (Cntctval == value.ContactId) {
                            valmatch = true;
                        }
                        $("#ddlContact").append($("<option></option>").val(value.ContactId).html(value.ContactName));
                    });
                    if (valmatch == true)
                        $('#ddlContact').val(Cntctval);
                    else
                        $('#ddlContact').val(0);
                }

            });
        }

        function LoadEmployee() {
            var CCval = $('#HiddenBOMCC').val();
            var valmatch = false;
            $.ajax({
                type: "GET", url: "GetEmployeeList",
                dataType: "json",
                contentType: "application/json",
                success: function (Jsondata) {
                    $.each(Jsondata, function (data, value) {
                        if (CCval == value.LoginId) {
                            valmatch = true;
                        }
                        $("#ddlEmployee").append($("<option></option>").val(value.LoginId).html(value.EmployeeName));
                    });
                    if (valmatch == true)
                        $('#ddlEmployee').val(CCval);
                    //else
                    //    $('#ddlEmployee').val(0);
                }

            });
        }

        function LoadSalesperson() {
            var salesval = $('#HiddenBomSalesperson').val();
            var salespersonSelectedval = "";

            var valmatch = false;
            $.ajax({
                type: "GET", url: "GetEmployeeList",
                dataType: "json",
                contentType: "application/json",
                success: function (Jsondata) {
                    $.each(Jsondata, function (data, value) {

                        if (salesval == value.EmployeeName) {

                            salespersonSelectedval = value.LoginId;
                            valmatch = true;
                        }
                        $("#ddlSalesperson").append($("<option></option>").val(value.LoginId).html(value.EmployeeName));
                    });
                    if (valmatch == true) {
                        $('#ddlSalesperson').val(salespersonSelectedval);
                    }

                    //else
                    //    $('#ddlSalesperson').val(0);
                }

            });
        }

        $('#linkEdit').click(function () {
            if ($('#BOMStatus').val() > 1) {
                $('#txtName').prop('disabled', true);
                $('#ddlContact').prop('disabled', true);
                $('#ddlCurrency').prop('disabled', true);
                //$('#txtCurrentSup').prop('disabled', true);
                $('#mydatepicker').prop('disabled', true);
                $('.ui-datepicker-trigger').css('pointer-events', 'none');
                $('#ddlContact_refresh_icon').css('pointer-events', 'none');
                $('#ddlCurrency_refresh_icon').css('pointer-events', 'none');
                $('#chkSupplyRequiredEdit').prop('disabled', true);
                $('#chkInactiveEdit').prop('disabled', true);
                $('#textNotes').prop('disabled', true);
                $('#ddlEmployee').prop('disabled', true);
            } else {
                $('#txtName').prop('disabled', false);
                $('#ddlContact').prop('disabled', false);
                $('#ddlCurrency').prop('disabled', false);
                //$('#txtCurrentSup').prop('disabled', false);
                $('#mydatepicker').prop('disabled', false);
                $('.ui-datepicker-trigger').css("pointer-events", "");
                $('#ddlContact_refresh_icon').css("pointer-events", "");
                $('#ddlCurrency_refresh_icon').css('pointer-events', 'none');
                $('#chkSupplyRequiredEdit').prop('disabled', false);
                $('#chkInactiveEdit').prop('disabled', false);
                $('#textNotes').prop('disabled', false);
                $('#ddlEmployee').prop('disabled', false);
            }
            var modal = document.getElementById("EditModal");
            var btn = document.getElementById("linkEdit");
            var span = document.getElementsByClassName("close")[0];
            modal.style.display = "block"
            $('html, body', window.parent.document).animate({ scrollTop: 240 }, "fast");
        });

        $('.BtnCloseEdit').click(function () {
            var modal = document.getElementById("EditModal");
            modal.style.display = "none";
            $('html, body', window.parent.document).animate({ scrollTop: 0 }, "fast");
            ResetEditBOM();
            BindBOMManagerData();
        });

        $('#BtnSubmitEdit').click(function () {
            //var modal = document.getElementById("EditModal");
            //modal.style.display = "none";
            if (!ValidateEditBOM()) {
                $("#EditBOMValidation").css("display", "block");
                return;
            }
            else {
                $("#EditBOMValidation").css("display", "none");
            }
            var qrystr = new URLSearchParams(window.location.search);
            var qrystrkey = qrystr.get("BOM");
            var _BOMManagerId = qrystrkey;
            var _ClientNo = BOMManagerData.ClientNo;
            var _BomManagerName = $('#txtName').val();
            var _Notes = $('#textNotes').val();
            var _BomManagerCode = $('#lblBomCode').text();
            var _Inactive = $('#chkInactiveEdit').is(":checked");
            var _UpdatedBy = '';
            var _CompanyId = BOMManagerData.CompanyNo;
            var _ContactId = $('#ddlContact').val();

            var _SalespersonId = $('#ddlSalesperson').val();
            var _CurrencyNo = $('#ddlCurrency').val();
            var _CurrentSupplier = "";//$('#txtCurrentSup').val();
            var _QuoteRequired = BOMManagerData.QuoteRequired;
            var dt = new Date(parseInt(_QuoteRequired.replace("/Date(", "").replace(")/", "")))
            var RDate = dt.getDate() + "/" + (dt.getMonth() + 1) + "/" + dt.getFullYear();
            _QuoteRequired = $('#mydatepicker').val();
            var _AS9120 = $('#chkSupplyRequiredEdit').is(":checked");
            var _Contact2Id = BOMManagerData.Contact2Id;

            //var dataparam = {
            //    bomId: _BOMManagerId, clientNo: _ClientNo, bomName: _BomManagerName, notes: _Notes, bomCode: _BomManagerCode, inactive: _Inactive,
            //    updatedBy: _UpdatedBy, companyId: _CompanyId, contactId: _ContactId, currencyNo: _CurrencyNo, currentSupplier: _CurrentSupplier,
            //    quoteRequired: _QuoteRequired, AS9120: _AS9120, contact2Id: _Contact2Id
            //};
            var dataparam = {
                'bomId': _BOMManagerId, 'clientNo': _ClientNo, 'bomName': _BomManagerName, 'notes': _Notes, 'bomCode': _BomManagerCode, 'inactive': _Inactive,
                'updatedBy': _UpdatedBy, 'companyId': _CompanyId, 'contactId': _ContactId, 'currencyNo': _CurrencyNo, 'currentSupplier': _CurrentSupplier,
                'quoteRequired': _QuoteRequired, 'AS9120': _AS9120, 'contact2Id': _Contact2Id, 'salespersonId': _SalespersonId
            };
            //var dataparam = JSON.stringify({
            //    bomId: 1, clientNo: 2, bomName: 3, notes: '4', bomCode: '5', inactive: false,
            //    updatedBy: 7, companyId: 8, contactId: 9, currencyNo: 10, currentSupplier: '11',
            //    quoteRequired: '01/01/2022', AS9120: true, contact2Id: 14
            //});

            $.ajax({
                type: "POST",
                url: "UpdateBOMManager",

                //data: "{'bomId': 1,'clientNo': 2,'selectedclient_Id': 3, 'notes': '4', 'bomCode': 5, 'inactive': " + false + ", 'updatedBy': 7,'companyId': 8, 'contactId': 9, 'currencyNo': 10, 'currentSupplier': '11', 'quoteRequired': '01/01/2022', 'AS9120': " + true + ", 'contact2Id': 14}",
                data: JSON.stringify(dataparam),
                contentType: "application/json",
                dataType: "json",
                async: false,
                success: function (Jsondata) {
                    //        console.log(Jsondata);
                    ResetEditBOM();
                    var modal = document.getElementById("EditModal");
                    modal.style.display = "none";
                    //$('#linkEdit').off('click');
                    $('html, body', window.parent.document).animate({ scrollTop: 0 }, "fast");
                    BindBOMManagerData();
                }

            });
        });

        $('#linkPurchaseHub').click(function () {
            //alert('Edit click');

            //alert('Some info missing : ' + validatemessage);
            var modal = document.getElementById("PurchaseHubModal");
            var btn = document.getElementById("linkPurchaseHub");
            var span = document.getElementsByClassName("close")[0];
            modal.style.display = "block"
            $('html, body', window.parent.document).animate({ scrollTop: 240 }, "fast");
            var validatemessage = $('#HdnvalidateMessage').val();
            if (validatemessage != '') {
                $('#tblPurchaseHub').css("display", 'none');
                $('#showmessagediv').css("display", 'block');
                $('#SendToHubErrorMessageDiv').css("display", 'block');
                $('#SendToHubNoErrorDiv').css("display", 'none');
                //$('#showMessage').text(validatemessage);
                var ValidationErrorList = validatemessage.split(',|,');
                $('#SendToHubHeader').text('There are error in below ' + (parseInt(ValidationErrorList.length) - 1) + ' Item(s)' + ' Click on Part to Edit and Fix Details');
                    var content = "<table class='BOMErrorTable'>";
                    content += '<tr>';
                    content += '<th> Part </th>';
                    content += '<th> Error </th>';

                    content += '</tr>';
                    for (i = 0; i < ValidationErrorList.length - 1; i++) {
                        var row = ValidationErrorList[i].split('~|');
                        var flag = 0;
                        content += '<tr>';
                        //content += '<td>' + '<button class="ErrorReqEdit" id="'+ 'EditRequirement_' + row[0] +'" onclick="PurchaseHUBErrorEdit(event)">Edit</button>' + '</td>';
                        content += '<td>' + '<a id="' + 'EditRequirement_' + row[0] + '" onclick="PurchaseHUBErrorEdit(event)" class="SpanErrorPart">' + row[2] + '</a></td>';
                        content += '<td>';

                        if (row[3] != "") {
                            content += row[3];
                            flag = 1;
                        }
                        if (row[5] != "") {
                            content += ',' + row[5];
                            flag = 1;
                        }
                        if (row[6] != "") {
                            content += ',' + row[6];
                            flag = 1;
                        }
                        if (row[7] != "") {
                            content += ',' + row[7];
                            flag = 1;
                        }
                        if (row[8] != "") {
                            content += ',' + row[8];
                            flag = 1;
                        }
                        if (row[9] != "") {
                            content += ',' + row[9];
                            flag = 1;
                        }
                        if (row[9] != "") {
                            content += ',' + row[9];
                            flag = 1;
                        }
                        if (flag == 1) {
                            content += '- Missing';
                        }
                        if (row[4] != "") {
                            if (flag == 1) {
                                content += ' And ' + row[4];
                            }
                            else {
                                content += row[4];
                            }
                        }

                        content += '</td>';
                        content += '</tr>';
                    }
                    content += "</table>";
                    $('#TableErrorDivBOM').html(content);

            }
            else {
                $('#SendToHubHeader').text('Are you sure you would like to send this to Purchase Hub?');
                $('#tblPurchaseHub').css("display", 'block');
                $('#showmessagediv').css("display", 'none');
                $('#showMessage').val('');
            }
        });
        $('.BtnBackPH').click(function () {
            var modal = document.getElementById("PurchaseHubModal");
            modal.style.display = "none";
            $('html, body', window.parent.document).animate({ scrollTop: 0 }, "fast");
        });

        $('#linkCloseHub').click(function () {
            //alert('Edit click');
            var modal = document.getElementById("CloseHubModal");
            var btn = document.getElementById("linkCloseHub");
            var span = document.getElementsByClassName("close")[0];
            modal.style.display = "block"
            $('html, body', window.parent.document).animate({ scrollTop: 240 }, "fast");
        });

        $('#btnCloseHubRFGNo').click(function () {
            var modal = document.getElementById("CloseHubModal");
            modal.style.display = "none";
            $('html, body', window.parent.document).animate({ scrollTop: 0 }, "fast");
        });


        function LoadPHBuyers() {
            //var Crncyval = $('#HiddenBomCurrency').val();
            //var valmatch = false;
            $.ajax({
                type: "POST", url: "GetBuyersList",
                dataType: "json",
                contentType: "application/json",
                success: function (Jsondata) {
                    //$.each(Jsondata, function (data, value) {

                    //    $("#ddlBuyers").append($("<option></option>").val(value.LoginId).html(value.EmployeeName));
                    //});
                    listItems = '';
                    listItems = defaultOption;
                    for (var i = 0; i < Jsondata.length; i++) {
                        listItems += optionStart + Jsondata[i].LoginId + "'>" + Jsondata[i].EmployeeName + optionEnd;
                    }
                    $("#ddlBuyers").html(listItems);
                    $('#ddlBuyers').val(0);
                }

            });
        }

        function LoadCommunicationNoteBuyers() {
            //var Crncyval = $('#HiddenBomCurrency').val();
            //var valmatch = false;
            $.ajax({
                type: "POST", url: "GetLoginListForClient",
                dataType: "json",
                contentType: "application/json",
                success: function (Jsondata) {
                    //$.each(Jsondata, function (data, value) {

                    //    $("#ddlBuyers").append($("<option></option>").val(value.LoginId).html(value.EmployeeName));
                    //});
                    listItems = '';
                    listItems = defaultOption;
                    for (var i = 0; i < Jsondata.length; i++) {
                        listItems += optionStart + Jsondata[i].LoginId + "'>" + Jsondata[i].EmployeeName + optionEnd;
                    }
                    $("#SelectNoteIndividual").html(listItems);
                    $('#SelectNoteIndividual').val(0);
                }

            });
        }

        $("#RefreshBuyer").click(function () {
            LoadPHBuyers();
        });

        $("#RefreshNoteIndividual").click(function () {
            LoadCommunicationNoteBuyers();
        });


        $("#txtBuyerCC").autocomplete({
            source: function (request, response) {
                $.ajax({
                    url: "GetMailToList",
                    type: "POST",
                    dataType: "json",
                    data: { searchString: request.term + '%' },
                    success: function (data) {
                        //            console.log(data);
                        response($.map(data, function (item) {
                            //debugger;
                            //console.log(item.MailMessageAddressType);
                            return { label: item.EmployeeName, value: item.EmployeeName, recieverid: item.LoginId, MailMessageAddressType: item.MailMessageAddressType };
                            //return { label: item.EmployeeName, value: item.LoginId };
                        }))

                    }
                })
            },
            select: function (e, i) {
                e.preventDefault();
                $("#txtBuyerCC").val('');
                var hiddenReceiver = $("#HiddenMailReceiver").val();

                var total_element = $(".element").length;

                // last <div> with element class id
                var lastid = $(".element:last").attr("id");
                var split_id = lastid.split("_");
                var nextindex = Number(split_id[1]) + 1;
                console.log("Split element-id");
                console.log(split_id);
                var max = 5;
                // Check total number elements
                //if (total_element < max) {
                // Adding new div container after last occurance of element class
                $(".element:last").after("<div class='element' id='div_" + i.item.recieverid + "'></div>");
                // Adding element to <div>
                if (i.item.MailMessageAddressType == "Group")
                    $("#div_" + i.item.recieverid).append("<label class='dynamiclabelid mailRecipientGroup' id='" + i.item.recieverid + "_MailGroup' >" + i.item.value + "</label>&nbsp;<span id='remove_" + i.item.recieverid + "_MailGroup' style='color: black;' class='remove'>X</span>");
                else
                    $("#div_" + i.item.recieverid).append("<label class='dynamiclabelid mailRecipient' id='" + i.item.recieverid + "' >" + i.item.value + "</label>&nbsp;<span id='remove_" + i.item.recieverid + "' style='color: black;' class='remove'>X</span>");
                console.log('Add CC: div_' + i.item.recieverid);

                if (hiddenReceiver == '' || hiddenReceiver == undefined) {
                    $("#HiddenMailReceiver").val(i.item.recieverid);
                }
                else {
                    $("#HiddenMailReceiver").val(hiddenReceiver + ',' + i.item.recieverid);
                }
                //}
                $("#txtBuyerCC").val('');
                $('.remove').click(function () {
                    var id = this.id;
                    var split_id = id.split("_");
                    var deleteindex = split_id[1];

                    // Remove <div> with id
                    $("#div_" + deleteindex).remove();
                });
            }, minLength: 0
        }).autocomplete("instance")._renderItem = function (ul, item) {
            if (item.MailMessageAddressType == "Group")
                return $("<li>").append("<div class='mailRecipientGroup'>" + item.label + "</div>").appendTo(ul);
            else
                return $("<li>").append("<div class='mailRecipient'>" + item.label + "</div>").appendTo(ul);
            //console.log('this is event firing');
        };


        $("#txtCommunicationNoteCC").autocomplete({
            source: function (request, response) {
                $.ajax({
                    url: "GetMailToList",
                    type: "POST",
                    dataType: "json",
                    data: { searchString: request.term + '%' },
                    success: function (data) {
                        //            console.log(data);
                        response($.map(data, function (item) {
                            //debugger;
                            //console.log(item.MailMessageAddressType);
                            return { label: item.EmployeeName, value: item.EmployeeName, recieverid: item.LoginId, MailMessageAddressType: item.MailMessageAddressType };
                            //return { label: item.EmployeeName, value: item.LoginId };
                        }))

                    }
                })
            },
            select: function (e, i) {
                e.preventDefault();
                $("#txtCommunicationNoteCC").val('');
                var hiddenReceiver = $("#HiddenCommunicationNoteMailReceiver").val();
                var total_element = $(".element-notes").length;
                // last <div> with element class id
                var lastid = $(".element-notes:last").attr("id");
                var split_id = lastid.split("_");
                var nextindex = Number(split_id[1]) + 1;
                var max = 5;
                // Check total number elements
                //if (total_element < max) {
                // Adding new div container after last occurance of element class
                $(".element-notes:last").after("<div class='element-notes' id='div_" + i.item.recieverid + "'></div>");
                // Adding element to <div>
                if (i.item.MailMessageAddressType == "Group")
                    $("#div_" + i.item.recieverid).append("<label class='dynamiclabelid-note mailRecipientGroup' id='" + i.item.recieverid + "_MailGroup' >" + i.item.value + "</label>&nbsp;<span id='remove_" + i.item.recieverid + "_MailGroup' style='color: black; cursor: default;' class='remove'>X</span>");
                else
                    $("#div_" + i.item.recieverid).append("<label class='dynamiclabelid-note mailRecipient' id='" + i.item.recieverid + "' >" + i.item.value + "</label>&nbsp;<span id='remove_" + i.item.recieverid + "' style='color: black; cursor: default;' class='remove'>X</span>");

                if (hiddenReceiver == '' || hiddenReceiver == undefined) {
                    $("#HiddenCommunicationNoteMailReceiver").val(i.item.recieverid);
                }
                else {
                    $("#HiddenCommunicationNoteMailReceiver").val(hiddenReceiver + ',' + i.item.recieverid);
                }
                //}
                $("#txtCommunicationNoteCC").val('');
                $('.remove').click(function () {
                    var id = this.id;
                    var split_id = id.split("_");
                    var deleteindex = split_id[1];

                    // Remove <div> with id
                    $("#div_" + deleteindex).remove();
                });
            }, minLength: 0
        }).autocomplete("instance")._renderItem = function (ul, item) {
            if (item.MailMessageAddressType == "Group")
                return $("<li>").append("<div class='mailRecipientGroup'>" + item.label + "</div>").appendTo(ul);
            else
                return $("<li>").append("<div class='mailRecipient'>" + item.label + "</div>").appendTo(ul);
        };

        $('.container').on('click', '.remove', function () {

            var id = this.id;
            var split_id = id.split("_");
            var deleteindex = split_id[1];

            // Remove <div> with id
            $("#div_" + deleteindex).remove();

        });
        //function GenerateTextbox(value) {
        //    var idcode = new Date().getTime();
        //    return '<div id="' + idcode +'"><Label id="lblBomCode"></Label><input type="button" value="X" id="'+idcode+'">' +
        //       '</div>'
        //}

        $('#btnSubmitHubRFG').click(function () {
            //var modal = document.getElementById("EditModal");
            //modal.style.display = "none";
            var qrystr = new URLSearchParams(window.location.search);
            var qrystrkey = qrystr.get("BOM");
            var _BOMManagerId = qrystrkey;
            var _ClientNo = BOMManagerData.ClientNo;
            var _BomManagerName = $('#txtName').val();
            var _Notes = $('#textNotes').val();
            var _BomManagerCode = $('#lblBomCode').text();
            var _Inactive = chkInactive.checked;
            var _UpdatedBy = '';
            var _CompanyId = BOMManagerData.CompanyNo;
            var _ContactId = BOMManagerData.ContactNo;
            var _CurrencyNo = BOMManagerData.CurrencyNo;
            var _CurrentSupplier = BOMManagerData.CurrentSupplier;
            var _QuoteRequired = BOMManagerData.QuoteRequired;
            var dt = new Date(parseInt(_QuoteRequired.replace("/Date(", "").replace(")/", "")))
            var RDate = dt.getDate() + "/" + (dt.getMonth() + 1) + "/" + dt.getFullYear();
            _QuoteRequired = RDate;
            var _AS9120 = BOMManagerData.AS9120;
            var _Contact2Id = BOMManagerData.Contact2Id;
            var _CompanyName = BOMManagerData.CompanyName;
            var _AssignedUserType = $('input[name="SendToHubAssignee"]:checked').val();
            var _AssignUserNo;
            if (_AssignedUserType == "Individual") {
                if ($('#ddlBuyers').val() != 0) {
                    _AssignUserNo = $('#ddlBuyers').val();
                    $('#rowIndividualBuyer').css("background-color", "#56954E");
                }
                else {
                    alert("Buyer is Mandatory while Sending To Purchase HUB");
                    $('#rowIndividualBuyer').css("background-color", "#990000");
                    return;
                }
            }
            else if (_AssignedUserType == "Group") {
                if ($('#ddlGroup').val() != 0) {
                    _AssignUserNo = $('#ddlGroup').val();
                    $('#rowGroupBuyer').css("background-color", "#56954E");
                }
                else {
                    alert("Mail Group is Mandatory while Sending To Purchase HUB");
                    $('#rowGroupBuyer').css("background-color", "#990000");
                    return;
                }
            }
            var _aryRecipientLoginIDsCC = '';

            //$(".dynamiclabelid").each(function (item) {
            //    // Test if the div element is empty
            //    console.log($(this).attr("id"));
            //    console.log($('#' + $(this).attr("id")).text());
            //    _aryRecipientLoginIDsCC = _aryRecipientLoginIDsCC + $(this).attr("id") + '||';
            //});
            var _aryRecipientLoginIDsCC = $(".dynamiclabelid").map(function () {
                return this.id;
            }).get().join("|");

            //var dataparam = {
            //    bomId: _BOMManagerId, clientNo: _ClientNo, bomName: _BomManagerName, notes: _Notes, bomCode: _BomManagerCode, inactive: _Inactive,
            //    updatedBy: _UpdatedBy, companyId: _CompanyId, contactId: _ContactId, currencyNo: _CurrencyNo, currentSupplier: _CurrentSupplier,
            //    quoteRequired: _QuoteRequired, AS9120: _AS9120, contact2Id: _Contact2Id
            //};
            var dataparam = {
                'bomId': _BOMManagerId, 'BOMName': _BomManagerName, 'BOMCode': _BomManagerCode, 'BomCompanyName': _CompanyName, 'BomCompanyNo': _CompanyId,
                'AssignUserNo': _AssignUserNo,
                'aryRecipientLoginIDsCC': _aryRecipientLoginIDsCC, 'Contact2No': _Contact2Id, 'AssignedUserType': _AssignedUserType,
            };
            //var dataparam = JSON.stringify({
            //    bomId: 1, clientNo: 2, bomName: 3, notes: '4', bomCode: '5', inactive: false,
            //    updatedBy: 7, companyId: 8, contactId: 9, currencyNo: 10, currentSupplier: '11',
            //    quoteRequired: '01/01/2022', AS9120: true, contact2Id: 14
            //});

            $.ajax({
                type: "POST",
                url: "SendtoPurchaseHub",

                //data: "{'bomId': 1,'clientNo': 2,'selectedclient_Id': 3, 'notes': '4', 'bomCode': 5, 'inactive': " + false + ", 'updatedBy': 7,'companyId': 8, 'contactId': 9, 'currencyNo': 10, 'currentSupplier': '11', 'quoteRequired': '01/01/2022', 'AS9120': " + true + ", 'contact2Id': 14}",
                data: JSON.stringify(dataparam),
                contentType: "application/json",
                dataType: "json",
                async: false,
                success: function (Jsondata) {
                    //        console.log(Jsondata);
                    var modal = document.getElementById("PurchaseHubModal");
                    modal.style.display = "none";
                    $('#linkPurchaseHub').off('click');
                    $('html, body', window.parent.document).animate({ scrollTop: 0 }, "fast");
                    BindBOMManagerData();
                    location.reload();
                }

            });
        });
        $('#btnCancelHubRFQ').click(function () {
            var modal = document.getElementById("PurchaseHubModal");
            modal.style.display = "none";
            $('html, body', window.parent.document).animate({ scrollTop: 0 }, "fast");
        });
        $('#btnCloseHubRFGYes').click(function () {

            var bomjsondata;
            var qrystr = new URLSearchParams(window.location.search)
            var qrystrkey = qrystr.get("BOM");

            $.ajax({
                type: 'POST',
                url: 'UpdateBOMStatusToClosed',
                data: JSON.stringify({ BOMId: qrystrkey }),
                contentType: 'application/json', // this
                datatype: 'json',
                async: false,
                //data: { clients: JSON.stringify(clients) }, // and this
                success: function (dataJSON) {
                    //bomdata = data;

                    //var dd = JSON.parse(dataJSON);
                    //var data = { curPage: dd.curPage, totalRecords: dd.totalRecords, data: dd.data };
                    //console.log('breaking');
                    //        console.log(dataJSON);
                    //data2 = data;
                    var modal = document.getElementById("CloseHubModal");
                    modal.style.display = "none";
                    $('#linkCloseHub').off('click');
                    $('html, body', window.parent.document).animate({ scrollTop: 0 }, "fast");
                    BindBOMManagerData();
                    //return bomjsondata;
                },
                error: function (jqXhr, textStatus, errorMessage) {
                    console.log('Something went wrong');
                }

            });
        });

        $('#RefreshMainInfo').click(function () {
            //alert('click');
            BindBOMManagerData();
        });
        //$('#refreshBOMItem').click(function () {
        //    //alert('click');
        //    LoadBOMItemData();
        //    $('#bomItemDetails').hide();
        //});


        function LoadAutoSourceData(CR_ID) {
            var colModel = [
                {
                    title: 'primary<br>Source',
                    align: 'center',
                    dataIndx: "RadioButton",
                    width: "6%",
                    editable: false,
                    render: function (ui) {
                        if ('@ViewBag.IsPoHub' == 'True')
                            return '';
                        return "<input type='radio' name='radio100'/>";

                    },
                    postRender: function (ui) {
                        //debugger;
                        var grid = this;
                        $(ui.cell).find('input').prop('checked', ui.rowData.IsPrimarySourceActual).on('change', function (evt) {
                            grid.SelectRow().replace({ rowIndx: ui.rowIndx });
                        });
                        if (ui.rowData.IsPrimarySourceActual == true) {
                            this.setSelection({ rowIndx: ui.rowIndx, focus: true });
                        }
                        if (ui.rowData.QuoteID != null) {
                            $("input[type=radio]").attr('disabled', true);
                        }
                    }
                },
                {
                    title: "CustomerRequirementId", //title of column.
                    width: "0%", //initial width of column
                    dataIndx: "CustomerRequirementId",
                    hidden: true,
                },
                {
                    title: "BOMStatus", //title of column.
                    width: "0%", //initial width of column
                    dataIndx: "BOMStatus",
                    hidden: true,
                },
                {
                    title: "Supplier <br> Related Quotes",
                    width: "14.5%",
                    //dataType: "string",
                    dataIndx: "Supplier",
                    render: function (ui) {
                        var supplierHtmlText = "<span><a href='/Con_CompanyDetail.aspx?cm=" + ui.rowData.SupplierNo + "&clt=2' target='_top'>" + ui.rowData.SupplierName + "</a></span>";
                        var quoteHtmlText;
                        if ((ui.rowData.QuoteID == null && ui.rowData.QuoteNumber == null) || '@ViewBag.IsPoHub' == 'True') {
                            quoteHtmlText = '';
                        } else {
                            quoteHtmlText = "<span><a href='/Ord_QuoteDetail.aspx?qt=" + ui.rowData.QuoteID + "' target='_top'>" + "Quote : " + ui.rowData.QuoteNumber + "</a></span>";
                        }

                        return {
                            text: supplierHtmlText + "<br>" + quoteHtmlText,
                            cls: "supplier-and-quote"
                        }
                    }
                },
                {
                    title: "Part No. <br/> Notes",
                    width: "15%",
                    /*dataType: "float",*/
                    //align: "right",
                    dataIndx: "Part",
                    render: function (ui) {

                        var htmlstr = "<span>" + ui.rowData.Part + "</span ><br/>";
                        htmlstr = htmlstr + '<span title="' + ui.rowData.Notes + '">' + ui.rowData.Notes + "</span >";
                        return htmlstr;
                    }
                },
                {
                    title: "Manufacturer <br/> Date Code",
                    width: "15%",
                    /*dataType: "float",*/
                    //align: "right",
                    dataIndx: "ManufacturerName",
                    render: function (ui) {
                        var htmlstr = /*"<span><a  href='../../Con_ManufacturerDetail.aspx?mfr=" + ui.rowData.ManufacturerNo + "' target='_top'>" +*/ ui.rowData.ManufacturerName /*+ "</a></span>";*/
                        htmlstr = htmlstr + "<br> <span>" + ui.rowData.DateCode + "</span > ";
                        return htmlstr;
                    }
                },
                {
                    title: "Product <br> Package",
                    width: "12%",
                    dataType: "Date",
                    //align: "right",
                    dataIndx: "ProductName",
                    render: function (ui) {
                        var htmlstr = "<span>" + ui.rowData.ProductName + "</span ><br>";
                        htmlstr = htmlstr + " <span>" + ui.rowData.PackageName + "</span > ";
                        return htmlstr;
                    }
                },
                {
                    title: "Offered <br> By",
                    width: "8%",
                    /*dataType: "float",*/
                    //align: "right",
                    dataIndx: "SalesmanName",
                    render: function (ui) {
                        var htmlstr = "<span>" + getCustomDate(ui.rowData.OfferDate.toString()) + "</span ><br>";
                        htmlstr = htmlstr + " <span>" + ui.rowData.SalesmanName + "</span > ";
                        return htmlstr;
                    }
                },
                {
                    title: "Quantity <br/> Delivery Date",
                    width: "10%",
                    /*dataType: "float",*/
                    //align: "right",
                    dataIndx: "Quantity",
                    render: function (ui) {
                        var htmlstr = "<span>" + ui.rowData.Quantity + "</span ><br/>";
                        htmlstr = htmlstr + "<span>" + getCustomDate(ui.rowData.DeliveryDate.toString()) + "</span ><br/>";
                        return htmlstr;
                    }
                },
                {
                    title: "Unit Price <br> Base Currency",
                    width: "9%",
                    /*dataType: "float",*/
                    //align: "right",
                    //dataIndx: "MSL",
                    render: function (ui) {
                        var htmlstr = "<span>" + parseFloat(ui.rowData.ActualPrice).toFixed(4) + ' ' + ui.rowData.ActualCurrencyCode + "</span ><br/>";
                        htmlstr = htmlstr + "<span>" + parseFloat(ui.rowData.Price).toFixed(4) + ' ' + ui.rowData.CurrencyCode + "</span ><br/>";
                        return htmlstr;
                    }
                },
                {
                    title: "Supplier Type <br> Region",
                    width: "10%",
                    dataIndx: "SupplierType",
                    render: function (ui) {
                        var htmlstr = '<span title="' + ui.rowData.SupplierType + '">' + ui.rowData.SupplierType + "</span ><br>";
                        htmlstr = htmlstr + "<span>" + ui.rowData.Region + "</span ><br/>";
                        return htmlstr;
                    }
                },
                //{
                //    title: "Uplift Percentage <br> Uplift Price <br> Base Currency",
                //    width: "10%",
                //    /*dataType: "float",*/
                //    //align: "right",
                //    dataIndx: "Region",
                //    render: function (ui) {

                //        var htmlstr;
                //        if (ui.rowData.UpliftPercentage != 0) {
                //            htmlstr = "<span>" + ui.rowData.UpliftPercentage + "%" + "</span ><br/>";
                //            htmlstr = htmlstr + "<span>" + (ui.rowData.UpliftPrice) + ' ' + ui.rowData.ClientCurrencyCode + "</span ><br/>";
                //            htmlstr = htmlstr + "<span>" + parseFloat((parseFloat(ui.rowData.BasePriceFigure) + parseFloat(ui.rowData.BasePriceFigure * parseFloat(ui.rowData.UpliftPercentage) / 100))).toFixed(4) + ' ' + ui.rowData.CurrencyCode + "</span ><br/>";
                //        }
                //        return htmlstr;
                //    }
                //}
                //{
                //    title: "EST Shipping Cost <br> Base Currency",
                //    width: "10%",
                //    /*dataType: "float",*/
                //    //align: "right",
                //    dataIndx: "Shipping",
                //    render: function (ui) {
                //        var htmlstr = "<span>" + /*ui.rowData.SupplierPrice*/ 'Dicuss Shipping' + "</span ><br/>";
                //        htmlstr = htmlstr + "<span>" + 'Discuss Base Currency' + "</span ><br/>";
                //        return htmlstr;
                //    }
                //}
            ];
            var qrystr = new URLSearchParams(window.location.search);
            var qrystrkey = qrystr.get("BOM");
            var dataModel = {
                location: "remote",
                dataType: "JSON",
                method: "POST",
                url: 'GetBOMManagerAutoSourcing?BOMManagerId=' + qrystrkey + '&CustomerReqID=' + CR_ID,
                beforeSend: function () {
                    ClearSourcingResultDetails();
                },
                getData: function (dataJSON) {
                    var data = dataJSON;
                    var totalRecords = 0;
                    $('#PrimarySource').removeClass("quote");
                    $('#PrimarySource').addClass("quote_disabled");

                    if (dataJSON.length != 0) {
                        totalRecords = dataJSON[0].TotalRecords;
                    }
                    var cur_page = 1;
                    if (dataJSON.length != 0) {
                        cur_page = dataJSON[0].curpage;
                    }
                    return { curPage: cur_page, totalRecords: totalRecords, data: data };
                }
            };
            var grid2 = $("#grid_as").pqGrid({
                width: "auto", height: 272,
                selectionModel: { type: null, native: true },
                dataModel: dataModel,
                colModel: colModel,
                rowHt: 35,
                hwrap: false,
                wrap: false,
                editable: false,
                numberCell: { show: false },
                complete: function (event, ui) {
                    var gridInstanceRPP = $('#grid_as').pqGrid('instance');
                    var rpp = gridInstanceRPP.options.pageModel.rPP;
                    var totalRecords = gridInstanceRPP.options.pageModel.totalRecords;
                    var curPage = gridInstanceRPP.options.pageModel.curPage;
                    var recordsLeft = totalRecords - (rpp * curPage);
                    SourcingGridHeightChange(recordsLeft, rpp);
                },
                pageModel: { type: "remote", rPP: 5, strRpp: "{0}", rPPOptions: [5, 10] },
                postRenderInterval: -1, //to make the column.postRender work.
                complete: function (event, ui) {
                    DisableControl();
                },
                rowSelect: function (evt, ui) {
                    $('#hdnPrimarySource').val(ui.addList[0].rowData.SourceId);
                    $('#hdnCustomerRequirementId').val(ui.addList[0].rowData.CustomerRequirementId);
                    if (ui.addList.length > 0) {
                        if (ui.addList[0].rowData.BOMStatus == 4) {
                            $('#hdnPrimarySource').val(ui.addList[0].rowData.SourceId);
                            $('#EditSourcing_Customer_ReqID').val(ui.addList[0].rowData.CustomerRequirementId);
                            $('#Quote_Customer_ReqID').val(ui.addList[0].rowData.CustomerRequirementId);
                            $('#SupplierName').text(ui.addList[0].rowData.SupplierName);
                            $('#PartNo').text(ui.addList[0].rowData.Part);
                            $('#ManufacturerName').text(ui.addList[0].rowData.ManufacturerName);
                            $('#ProductName').text(ui.addList[0].rowData.ProductName);
                            $('#PackageName').text(ui.addList[0].rowData.PackageName);
                            $('#Quantity').text(ui.addList[0].rowData.Quantity);
                            $('#UnitPrice').val(ui.addList[0].rowData.UnitPrice);
                            $('#Notes').val(ui.addList[0].rowData.Notes);
                            $('#EditSourcing').removeClass("edit_disabled");
                            $('#Quote').removeClass("quote_disabled");
                            $('#EditSourcing').addClass("edit");
                            $('#Quote').addClass("quote");
                            $('#PrimarySource').removeClass("quote_disabled");
                            $('#PrimarySource').addClass("quote");
                            if (!ui.addList[0].rowData.IsPrimarySourceActual) {
                                SetPrimarySource();
                            }
                        }
                        else {
                            $('#EditSourcing').removeClass("edit");
                            $('#Quote').removeClass("quote");
                            $('#EditSourcing').addClass("edit_disabled");
                            $('#Quote').addClass("quote_disabled");
                            $('#PrimarySource').removeClass("quote");
                            $('#PrimarySource').addClass("quote_disabled");
                            ResetQuoteForm();
                            ResetEditSourcingForm();
                        }
                    }

                    //Show extra information of selected row
                    ShowSourcingResultDetails(ui.addList[0].rowData)
                }
            });
            if (CR_ID != '' || CR_ID != null) {
                grid2.pqGrid('refreshDataAndView');
            }

        }
        function SetPrimarySource() {
            var qrystr = new URLSearchParams(window.location.search);
            var BomManagerID = qrystr.get("BOM");
            $.ajax({
                processData: true,
                contentType: 'application/json',
                type: 'POST',
                url: 'Save_PrimarySourcing?ClientId=101&SourcingResultId=' + $('#hdnPrimarySource').val() + '&CustomerRequirementId=' + $('#hdnCustomerRequirementId').val() + '&UpdatedBy=0',
                dataType: "json",

                success: function (data) {
                    alert('Primary Source added successfully.');
                    $('#refreshBOMItem').click();
                    $('#BOMManagerSourcingGrid').hide();
                },
                error: function (err) {
                    alert('Error while saving Primary Source.');

                }
            });
        }

        function ShowSourcingResultDetails(selectedSourcing) {
            $("#lblSRManufactuer").text(selectedSourcing.ManufacturerName);
            $("#lblSRDateCode").text(selectedSourcing.DateCode);
            $("#lblSRPackageType").text(selectedSourcing.PackageName);
            $("#lblSRProductType").text(selectedSourcing.ProductName);
            var supplierWarrantlyText = selectedSourcing.SupplierWarranty > 0 ? (selectedSourcing.SupplierWarranty + ' days') : '';
            $("#lblSRSupplierWarrantly").text(supplierWarrantlyText);
            $("#lblSRTestingRecommended").text(selectedSourcing.IsTestingRecommended == true ? "Yes" : "No");
            $("#lblSRCountryOfOrigin").text(selectedSourcing.CountryOfOrigin);
            $("#lblSRMoq").text(selectedSourcing.MOQ);
            $("#lblSRTotalQuantity").text(selectedSourcing.SupplierTotalQSA);
            $("#lblSRLastTimeBuy").text(selectedSourcing.SupplierLTB);
            $("#lblSRNotes").text(SetCleanText(selectedSourcing.Notes));
            $("#lblSRImagesAttached").text(selectedSourcing.IsImageAvailable == true ? "Yes" : "No");
            $("#lblSrSPQ").text(selectedSourcing.SPQ);
            $("#lblSrLeadTime").text(selectedSourcing.LeadTime);
            $("#lblSrRoHS").text(selectedSourcing.ROHSDescription);
            $("#lblSrFactorySealed").text(selectedSourcing.FactorySealed);
            $("#lblSrMSL").text(selectedSourcing.MSLLevelNo ? selectedSourcing.MSLLevelText : selectedSourcing.MSL);

            $('#SRDetailsGrid').css('display', 'block');
        }

        function ClearSourcingResultDetails() {
            $("#lblSRManufactuer").text("");
            $("#lblSRDateCode").text("");
            $("#lblSRPackageType").text("");
            $("#lblSRProductType").text("");
            $("#lblSRSupplierWarrantly").text("");
            $("#lblSRTestingRecommended").text("");
            $("#lblSRCountryOfOrigin").text("");
            $("#lblSRMoq").text("");
            $("#lblSRTotalQuantity").text("");
            $("#lblSRLastTimeBuy").text("");
            $("#lblSRNotes").text("");
            $("#lblSRImagesAttached").text("");
            $("#lblSrSPQ").text("");
            $("#lblSrLeadTime").text("");
            $("#lblSrRoHS").text("");
            $("#lblSrFactorySealed").text("");
            $("#lblSrMSL").text("");
            $("#lblSrTypeOfSupplier").text("");
            $("#lblSrRFCS").text("");
            $("#lblSrROS").text("");

            $('#SRDetailsGrid').css('display', 'none');
        }

        $('#EditSourcing').click(function () {

            if ($('#EditSourcing_Customer_ReqID').val() == "" || $('#EditSourcing_Customer_ReqID').val() == null) {
                console.log("Please Select Sourcing");
            }
            else {
                var modal = document.getElementById("EditSourcingModal");
                modal.style.display = "block"
                $('html, body', window.parent.document).animate({ scrollTop: 240 }, "fast");
            }
        });

        $('.BtnCancelSourcingEdit').click(function () {
            //alert('Edit click');
            var modal = document.getElementById("EditSourcingModal");
            modal.style.display = "none"
            $('html, body', window.parent.document).animate({ scrollTop: 240 }, "fast");
            ResetEditSourcingForm();
        });

        function ResetEditSourcingForm() {
            $('#EditSourcing_Customer_ReqID').val("");
            $('#SupplierName').text("");
            $('#PartNo').text("");
            $('#ManufacturerName').text("");
            $('#ProductName').text("");
            $('#PackageName').text("");
            $('#Quantity').text("");
            $('#UnitPrice').val("");
            $('#Notes').val("");
        }

        function EditSourcing(CRID, UnitPrice, Notes) {
            $.ajax({
                processData: true,
                contentType: 'application/json',
                type: 'POST',
                url: 'EditSourcingResultsSalesman?UnitPrice=' + UnitPrice + '&Notes=' + Notes + '&CRID=' + CRID,
                dataType: "json",

                success: function (data) {
                    LoadAutoSourceData(CRID);
                },
                error: function (err) {
                    /*alert(err);*/
                    console.log('Error in Edit Sourcing by Salesman');
                }
            });
        }

        $('.BtnSaveSourcingEdit').click(function () {
            EditSourcing($('#EditSourcing_Customer_ReqID').val(), $('#UnitPrice').val(), $('#Notes').val())
            var modal = document.getElementById("EditSourcingModal");
            modal.style.display = "none"
            $('html, body', window.parent.document).animate({ scrollTop: 240 }, "fast");
            ResetEditSourcingForm();
        });
        $('#Quote').click(function () {
            $('#hdnMultiQuote').val("0");
            if ($('#Quote_Customer_ReqID').val() == "" || $('#Quote_Customer_ReqID').val() == null) {
                console.log("Please Select Sourcing");
            }
            else {
                LoadQuoteGenerationData($('#Quote_Customer_ReqID').val());
                var modal = document.getElementById("QuoteModal");
                modal.style.display = "block"
                $('html, body', window.parent.document).animate({ scrollTop: 240 }, "fast");
            }
        });

        $('.BtnCancelQuote').click(function () {
            var modal = document.getElementById("QuoteModal");
            modal.style.display = "none"
            $('html, body', window.parent.document).animate({ scrollTop: 240 }, "fast");
            ResetQuoteForm();
        });

        function LoadQuoteGenerationData(CRID) {
            if (CRID == undefined || CRID == null || CRID == '')
                CRID = 0;
            var qrystr = new URLSearchParams(window.location.search);
            var BOMManagerID = qrystr.get("BOM");
            $.ajax({
                processData: true,
                contentType: 'application/json',
                type: 'POST',
                url: 'LoadQuoteGenerationDataBOMManager?&CustomerReqID=' + CRID + '&BOMManagerNo=' + BOMManagerID,
                dataType: "json",

                success: function (data) {
                    var date = new Date();
                    $('#QuoteCompany').text(data.CompanyName);
                    $('#QuoteCompanyNo').val(data.CompanyNo);
                    $('#HeaderImageNameQuote').val(data.HeaderImageNameQuote);
                    $('#QuoteDivisionNo').val(data.DivisionNo);
                    $('#QuoteDivisionSales').text(data.DivisionName);
                    $('#QuoteDate').val(date.toLocaleDateString());
                    $('#Quote_Customer_SourceId').val(data.SourcingResultId);
                    GetCompanySalesInfo(data.CompanyNo);
                    LoadContactsForCompany(data.CompanyNo, data.ContactNo);
                    LoadSalesman(data.Salesman);
                    LoadCurrencyForQuote(data.CurrencyNo);
                    //LoadAutoSourceData(CRID);
                },
                error: function (err) {
                    /*alert(err);*/
                    console.log('Error in Loading Quote Generation Data');
                }
            });
        }
        function LoadContactsForCompany(CompanyID, ContactNo) {
            $.ajax({
                processData: true,
                contentType: 'application/json',
                type: 'POST',
                url: 'LoadContactsForCompany?&CompanyID=' + CompanyID,
                dataType: "json",

                success: function (data) {
                    listItems = '';
                    listItems = defaultOption;
                    for (var i = 0; i < data.length; i++) {
                        listItems += optionStart + data[i].ContactId + "'>" + data[i].ContactName + optionEnd;
                    }
                    $("#QuoteBuyer").html(listItems);
                    $("#QuoteBuyer").val(ContactNo);
                },
                error: function (err) {
                    /*alert(err);*/
                    console.log('Error in Loading Contacts For Company');
                }
            });
        }
        function LoadSalesman(SalesmanNo) {
            $.ajax({
                processData: true,
                contentType: 'application/json',
                type: 'POST',
                url: 'GetEmployeeList',
                dataType: "json",

                success: function (data) {
                    listItems = '';
                    listItems = defaultOption;
                    for (var i = 0; i < data.length; i++) {
                        listItems += optionStart + data[i].LoginId + "'>" + data[i].EmployeeName + optionEnd;
                    }
                    $("#QuoteSalesPerson").html(listItems);
                    $("#QuoteSalesPerson").val(SalesmanNo);
                },
                error: function (err) {
                    /*alert(err);*/
                    console.log('Error in Loading Employee List');
                }
            });
        }
        function LoadCurrencyForQuote(CurrencyNo) {
            $.ajax({
                processData: true,
                contentType: 'application/json',
                type: 'POST',
                url: 'GetCurrencyList',
                dataType: "json",

                success: function (data) {
                    listItems = '';
                    listItems = defaultOption;
                    for (var i = 0; i < data.length; i++) {
                        listItems += optionStart + data[i].CurrencyId + "'>" + data[i].CurrencyCode + '-' + data[i].CurrencyDescription + optionEnd;
                    }
                    $("#QuoteCurrency").html(listItems);
                    $("#QuoteCurrency").val(CurrencyNo);
                },
                error: function (err) {
                    /*alert(err);*/
                    console.log('Error in Loading Loading Currency for Quote');
                }
            });
        }
        function GetCompanySalesInfo(CompanyID) {
            $.ajax({
                processData: true,
                contentType: 'application/json',
                type: 'POST',
                url: 'GetCompanySalesInfo?&CompanyID=' + CompanyID,
                dataType: "json",

                success: function (data) {
                    LoadDivisionHeader(data.DefaultBillingAddress.DivisionHeaderNo);
                    LoadQuoteTerms(data.SOTermsNo);
                    LoadQuoteIncoterm(data.DefaultShippingAddress.IncotermNo);
                },
                error: function (err) {
                    /*alert(err);*/
                    console.log('Error in Loading Company Sale info');
                }
            });
        }
        function LoadDivisionHeader(DivisionHeaderNo) {
            $.ajax({
                processData: true,
                contentType: 'application/json',
                type: 'POST',
                url: 'LoadDivisionHeader',
                dataType: "json",

                success: function (data) {
                    listItems = '';
                    listItems = defaultOption;
                    for (var i = 0; i < data.length; i++) {
                        listItems += optionStart + data[i].DivisionId + "'>" + data[i].DivisionName + optionEnd;
                    }
                    $("#QuoteDivisionHeader").html(listItems);
                    $("#QuoteDivisionHeader").val(DivisionHeaderNo);
                },
                error: function (err) {
                    /*alert(err);*/
                    console.log('Error in Loading Loading Division Header Quote');
                }
            });
        }
        function LoadQuoteTerms(TermsNo) {
            $.ajax({
                processData: true,
                contentType: 'application/json',
                type: 'POST',
                url: 'LoadQuoteTerms',
                dataType: "json",

                success: function (data) {
                    listItems = '';
                    listItems = defaultOption;
                    for (var i = 0; i < data.length; i++) {
                        listItems += optionStart + data[i].TermsId + "'>" + data[i].TermsName + optionEnd;
                    }
                    $("#QuoteTerms").html(listItems);
                    $("#QuoteTerms").val(TermsNo);
                },
                error: function (err) {
                    /*alert(err);*/
                    console.log('Error in Loading Loading Division Header Quote');
                }
            });
        }
        function LoadQuoteIncoterm(IncoternNo) {
            $.ajax({
                processData: true,
                contentType: 'application/json',
                type: 'POST',
                url: 'LoadQuoteIncoterm',
                dataType: "json",

                success: function (data) {
                    listItems = '';
                    listItems = defaultOption;
                    for (var i = 0; i < data.length; i++) {
                        listItems += optionStart + data[i].IncotermId + "'>" + data[i].Name + optionEnd;
                    }
                    $("#QuoteIncoterms").html(listItems);
                    $("#QuoteIncoterms").val(IncoternNo);
                },
                error: function (err) {
                    /*alert(err);*/
                    console.log('Error in Loading Loading Division Header Quote');
                }
            });
        }


        $("#QuoteSupportMember").autocomplete({
            source: function (request, response) {
                $.ajax({
                    url: "GetMailToList",
                    type: "POST",
                    dataType: "json",
                    data: { searchString: request.term + '%' },
                    success: function (data) {
                        response($.map(data, function (item) {
                            return { label: item.EmployeeName, value: item.LoginId };
                        }))

                    }
                })
            },
            select: function (e, ui) {
                //debugger;
                $("#QuoteSupportMember").css("display", "none");
                $("#QuoteSupportMemberID").val(ui.item.value);
                $("#LabelQuoteSupportMember").text(SetCleanText(ui.item.label, true));
                $("#RemoveQuoteSupportMember").css("display", "block");
            }, minLength: 2
        });

        $("#RemoveQuoteSupportMember").click(function () {
            $("#QuoteSupportMember").css("display", "block");
            $("#QuoteSupportMember").val(null);
            $("#QuoteSupportMemberID").val(null);
            $("#LabelQuoteSupportMember").text(null);
            $("#RemoveQuoteSupportMember").css("display", "none");
        });

        function SetCleanText(strIn, blnReplaceLineBreaks) {
            if (typeof (strIn) == "undefined") strIn = "";
            strIn = (strIn + "").trim();
            strIn = strIn.replace(/(:PLUS:)/g, "+");
            strIn = strIn.replace(/(:QUOTE:)/g, '"');
            strIn = strIn.replace(/((:AND:)|(&amp;))/g, "&");
            if (blnReplaceLineBreaks) strIn = strIn.replace(/(\n)/g, "<br />");
            return strIn;
        };


        function AddNewQuote() {
            var qrystr = new URLSearchParams(window.location.search);
            var BOMManagerID = qrystr.get("BOM");
            /*code to get selected checkbox ids*/
            var selectedCustomerRequirementIds = '';
            var grid = $('#grid_md').pqGrid('instance');
            var checked = grid.Checkbox('ISPrimarySource').getCheckedNodes().map(function (rd) {
                //console.log(rd.CustomerRequirementId);
                selectedCustomerRequirementIds = selectedCustomerRequirementIds + rd.CustomerRequirementId + '|';
            });
            console.log(selectedCustomerRequirementIds);
            var MultiQuote = false;
            if ($('#hdnMultiQuote').val() == "1")
                MultiQuote = true;
            /*code to get selected checkbox ids*/
            $.ajax({
                processData: true,
                contentType: 'application/json',
                type: 'POST',
                url: 'AddNewQuote?&CustomerNotes=' + $('#QuoteCustomerNotes').val() + '&InternalNotes=' + $('#QuoteInternalNotes').val() + '&companyNo=' + $('#QuoteCompanyNo').val()
                    + '&contactNo=' + $('#QuoteBuyer').val() + '&dateQuoted=' + formattedDate($('#QuoteDate').val()) + '&currencyNo=' + $('#QuoteCurrency').val() + '&salesman=' + $('#QuoteSalesPerson').val() + '&termsNo=' + $('#QuoteTerms').val()
                    + '&divisionNo=' + $('#QuoteDivisionNo').val() + '&freight=' + $('#QuoteEstFreight').val() + '&incotermNo=' + $('#QuoteIncoterms').val() + '&SourceSupplyRequired=' + $('#QuoteSupplyRequired').is(":checked")
                    + '&isImportant=' + $('#QuoteMarkImportant').is(":checked") + '&SupportTeamMemberNo=' + $('#QuoteSupportMemberID').val() + '&DivisionHeaderNo=' + $('#QuoteDivisionHeader').val()
                    + '&CustomerRequirementID=' + $('#Quote_Customer_ReqID').val() + '&DocumentHeaderImageName=' + $('#HeaderImageNameQuote').val() + '&sourceId=' + $('#Quote_Customer_SourceId').val() + '&BOMManagerID=' + BOMManagerID
                    + '&MultiQuote=' + MultiQuote + '&CustomerRequirementIds=' + selectedCustomerRequirementIds,
                dataType: "json",

                success: function (data) {
                    console.log("QuoteID: " + data);
                    ResetQuoteForm();
                    ResetEditSourcingForm();
                    if (data > 0) {
                        location.reload();
                    }
                },
                error: function (err) {
                    console.log('Error in Adding New Quote');
                }
            });
        }
        $(".BtnSaveQuote").click(function () {
            if (ValidateAddQuoteForm()) {
                AddNewQuote();
                $("#QuoteValidationError").css("display", "none");
                var modal = document.getElementById("QuoteModal");
                modal.style.display = "none"
                $('html, body', window.parent.document).animate({ scrollTop: 240 }, "fast");
            }
            else {
                $("#QuoteValidationError").css("display", "block");
            }
        });

        function formattedDate(inputDate) {
            const [day, month, year] = inputDate.split('/');
            return month + '/' + day + '/' + year;
        }

        function ValidateAddQuoteForm() {
            var formIsValid = true;
            if ($('#QuoteBuyer').val() == 0 || $('#QuoteBuyer').val() == null) {
                $("#RowQuoteBuyerError").css("display", "block");
                $("#RowQuoteBuyer").css("background-color", "#990000");
                formIsValid = false;
            }
            else {
                $("#RowQuoteBuyer").css("background-color", "#56954E");
                $("#RowQuoteBuyerError").css("display", "none");
                if (formIsValid)
                    formIsValid = true;
            }
            if ($('#QuoteSalesPerson').val() == 0 || $('#QuoteSalesPerson').val() == null) {
                $("#RowQuoteSalesPersonError").css("display", "block");
                $("#RowQuoteSalesPerson").css("background-color", "#990000");
                formIsValid = false;
            }
            else {
                $("#RowQuoteSalesPerson").css("background-color", "#56954E");
                $("#RowQuoteSalesPersonError").css("display", "none");
                if (formIsValid)
                    formIsValid = true;
            }
            if ($('#QuoteDivisionHeader').val() == 0 || $('#QuoteDivisionHeader').val() == null) {
                $("#RowQuoteDivisionHeaderError").css("display", "block");
                $("#RowQuoteDivisionHeader").css("background-color", "#990000");
                formIsValid = false;
            }
            else {
                $("#RowQuoteDivisionHeader").css("background-color", "#56954E");
                $("#RowQuoteDivisionHeaderError").css("display", "none");
                if (formIsValid)
                    formIsValid = true;
            }
            if ($('#QuoteTerms').val() == 0 || $('#QuoteTerms').val() == null) {
                $("#RowQuoteTermsError").css("display", "block");
                $("#RowQuoteTerms").css("background-color", "#990000");
                formIsValid = false;
            }
            else {
                $("#RowQuoteTerms").css("background-color", "#56954E");
                $("#RowQuoteTermsError").css("display", "none");
                if (formIsValid)
                    formIsValid = true;
            }
            if ($('#QuoteCurrency').val() == 0 || $('#QuoteCurrency').val() == null) {
                $("#RowQuoteCurrencyError").css("display", "block");
                $("#RowQuoteCurrency").css("background-color", "#990000");
                formIsValid = false;
            }
            else {
                $("#RowQuoteCurrency").css("background-color", "#56954E");
                $("#RowQuoteCurrencyError").css("display", "none");
                if (formIsValid)
                    formIsValid = true;
            }
            if ($('#QuoteDate').val() == '' || $('#QuoteDate').val() == null) {
                $("#RowQuoteDateError").css("display", "block");
                $("#RowQuoteDate").css("background-color", "#990000");
                formIsValid = false;
            }
            else {
                $("#RowQuoteDate").css("background-color", "#56954E");
                $("#RowQuoteDateError").css("display", "none");
                if (formIsValid)
                    formIsValid = true;
            }
            if ($('#QuoteIncoterms').val() == 0 || $('#QuoteIncoterms').val() == null) {
                $("#RowQuoteIncotermsError").css("display", "block");
                $("#RowQuoteIncoterms").css("background-color", "#990000");
                formIsValid = false;
            }
            else {
                $("#RowQuoteIncoterms").css("background-color", "#56954E");
                $("#RowQuoteIncotermsError").css("display", "none");
                if (formIsValid)
                    formIsValid = true;
            }
            return formIsValid;
        }
        function ResetQuoteForm() {
            $("#RowQuoteIncoterms").css("background-color", "#56954E");
            $("#RowQuoteIncotermsError").css("display", "none");
            $("#RowQuoteDate").css("background-color", "#56954E");
            $("#RowQuoteDateError").css("display", "none");
            $("#RowQuoteCurrency").css("background-color", "#56954E");
            $("#RowQuoteCurrencyError").css("display", "none");
            $("#RowQuoteTerms").css("background-color", "#56954E");
            $("#RowQuoteTermsError").css("display", "none");
            $("#RowQuoteDivisionHeader").css("background-color", "#56954E");
            $("#RowQuoteDivisionHeaderError").css("display", "none");
            $("#RowQuoteSalesPerson").css("background-color", "#56954E");
            $("#RowQuoteSalesPersonError").css("display", "none");
            $("#RowQuoteBuyer").css("background-color", "#56954E");
            $("#RowQuoteBuyerError").css("display", "none");

            $("#Quote_Customer_ReqID").val("");
            $("#QuoteCompanyNo").val("");
            $("#Quote_Customer_SourceId").val("");
            $("#QuoteBuyer").val(0);
            $("#QuoteSalesPerson").val(0);
            $("#HeaderImageNameQuote").val("");
            $("#QuoteDivisionNo").val("");
            $("#QuoteDivisionHeader").val(0);
            $("#QuoteTerms").val(0);
            $("#QuoteCurrency").val(0);
            $("#QuoteIncoterms").val(0);
        }

        $("#refreshQuoteBuyer").click(function () {
            LoadContactsForCompany($("#QuoteCompanyNo").val(), $("#QuoteBuyer").val());
        });

        $("#refreshQuoteSalesPerson").click(function () {
            LoadSalesman($("#QuoteSalesPerson").val());
        });

        $("#refreshQuoteDivisionHeader").click(function () {
            LoadDivisionHeader($("#QuoteDivisionHeader").val());
        });

        $("#refreshQuoteTerms").click(function () {
            LoadQuoteTerms($("#QuoteTerms").val());
        });

        $("#refreshQuoteCurrency").click(function () {
            LoadQuoteTerms($("#QuoteCurrency").val());
        });

        $("#refreshQuoteIncoterms").click(function () {
            LoadQuoteTerms($("#QuoteIncoterms").val());
        });

        function GetBOMManagerStatus() {
            var qrystr = new URLSearchParams(window.location.search)
            var qrystrkey = qrystr.get("BOM");

            $.ajax({
                processData: true,
                contentType: 'application/json',
                type: 'POST',
                url: 'GetBOMManagerStatus?BOM=' + qrystrkey,
                dataType: "json",

                success: function (data) {
                    //debugger;
                    //console.log('BOM status call');
                    console.log(data);
                    $('#BomName').text(" BOM Name: " + data.BOMName);
                    $('#lblItemBOMName').text(data.BOMName);
                    $('#BOMStatusDesc').text(data.BOMStatus);
                    $('#BOMStatus').val(data.Status);
                    $('#PriceUplifted').val(data.PriceUplifted);
                    $('#ItemsQuotedCount').text("Items Quoted: " + data.ItemsQuotedCount + "/" + data.TotalItemsCount);
                    if ((parseInt(data.Status) == 4 || parseInt(data.Status) == 5)) {
                        if (data.PriceUplifted == 0) {
                            $('#BtnUpliftPriceAll').removeClass("notes");
                            $('#BtnUpliftPriceAll').addClass("notes_disabled");
                        }
                        if (data.PriceUplifted > 0 && data.UpliftExist) {
                            $('#BtnResetUpliftPriceAll').removeClass("notes_disabled");
                            $('#BtnResetUpliftPriceAll').addClass("notes");
                        }
                    }
                    else {
                        $('#BtnUpliftPriceAll').removeClass("notes");
                        $('#BtnUpliftPriceAll').addClass("notes_disabled");
                        $('#BtnResetUpliftPriceAll').removeClass("notes");
                        $('#BtnResetUpliftPriceAll').addClass("notes_disabled");
                    }
                },
                error: function (err) {
                    console.log('Error in fetching BOM Status');
                }
            });
        }

        $("#DeleteBOMItem").click(function () {
            if ($('#BOMStatus').val() == 1) {
                var selectedCustomerRequirementIds = '';
                var grid = $('#grid_md').pqGrid('instance');
                //grid.Checkbox('ISPrimarySource').checkAll();
                var checked = grid.Checkbox('ISPrimarySource').getCheckedNodes().map(function (rd) {
                    selectedCustomerRequirementIds = selectedCustomerRequirementIds + rd.CustomerRequirementId + '|';
                });
                debugger;
                var CRIDs = selectedCustomerRequirementIds.split('|');
                console.log(CRIDs.length);
                if (CRIDs.length <= 1) {
                    alert("Please select Check Box(s) to Delete Single/Multiple Items");
                    return;
                }
                $('#HDCustReqIDs').val(selectedCustomerRequirementIds)
                var modal = document.getElementById("DeleteConfirmationBoxModal");
                modal.style.display = "block"
                $('html, body', window.parent.document).animate({ scrollTop: 240 }, "fast");
            }
        });

        $(".BtnCancelDelete").click(function () {

            var modal = document.getElementById("DeleteConfirmationBoxModal");
            modal.style.display = "none"
            $('html, body', window.parent.document).animate({ scrollTop: 240 }, "fast");
        });

        $(".BtnConfirmDelete").click(function () {
            if ($("#BOMItemsCount").val() == 1 && !confirm("This BOM has only 1 item left.\n Are you sure want to Continue, This will Close the BOM")) {
                return;
            }
            var qrystr = new URLSearchParams(window.location.search)
            var qrystrkey = qrystr.get("BOM");

            $.ajax({
                processData: true,
                contentType: 'application/json',
                type: 'POST',
                url: 'DeleteBOMManagerItem?BOMManagerID=' + qrystrkey + '&CustReqIDs=' + $('#HDCustReqIDs').val(),
                dataType: "json",

                success: function (data) {
                    console.log(data);
                    location.reload();
                },
                error: function (err) {
                    console.log('Error in fetching BOM Status');
                }
            });
        });

        $("#EditBOMItem").click(function () {
            if ($('#BOMStatus').val() == 1) {
                LoadEditBOMData();
                var modal = document.getElementById("EditBOMItemModal");
                modal.style.display = "block";
                $('html, body', window.parent.document).animate({ scrollTop: 240 }, "fast");
            }
        });

        $(".BtnCancelBOMEdit").click(function () {
            var modal = document.getElementById("EditBOMItemModal");
            modal.style.display = "none";
            $('html, body', window.parent.document).animate({ scrollTop: 240 }, "fast");
            ResetEditBOMForm();
        });

        function ValidateCommunicationNodeForm() {
            var formIsValid = true;
            if (($('#SelectCommunicationNoteGroup').val() == 0 || $('#SelectCommunicationNoteGroup').val() == null)
                && $('#SelectNoteIndividual').val() == 0 || $('#SelectNoteIndividual').val() == null) {
                $("#RowSelectCommunicationNoteGroup").css("background-color", "#990000");
                $("#RowCommunicationNoteIndividual").css("background-color", "#990000");
                formIsValid = false;
            }
            else {
                $("#RowSelectCommunicationNoteGroup").css("background-color", "#56954E");
                $("#RowCommunicationNoteIndividual").css("background-color", "#56954E");
                if (formIsValid)
                    formIsValid = true;
            }

            if ($('#txtCommunicationNotes').val() == '' || $('#txtCommunicationNotes').val() == null) {
                $("#RowTxtCommunicationNotes").css("background-color", "#990000");
                formIsValid = false;
            }
            else {
                $("#RowTxtCommunicationNotes").css("background-color", "#56954E");
                if (formIsValid)
                    formIsValid = true;
            }
            return formIsValid;
        }

        $("#AddCommunicationNote").click(function () {
            $('.BtnSaveCommunicationNode').removeClass("save_comm_note_disabled");
            $('.BtnSaveCommunicationNode').addClass("save_comm_note");

            var modal = document.getElementById("AddCommunicationNoteModal");
            modal.style.display = "block";
            $('html, body', window.parent.document).animate({ scrollTop: 240 }, "fast");
        });

        $(".BtnSaveCommunicationNode").click(function (event) {
            if (ValidateCommunicationNodeForm()) {
                $('.BtnSaveCommunicationNode').off('click');
                $('.BtnSaveCommunicationNode').removeClass("save_comm_note");
                $('.BtnSaveCommunicationNode').addClass("save_comm_note_disabled");
                $('#CommunicationNoteValidationError').css("display", "none");
                event.preventDefault();
                SaveCommunicationNodeData();
            }
            else {
                $("#CommunicationNoteValidationError").css("display", "block");
            }
        });

        function CancelCommunicationNode() {
            var modal = document.getElementById("AddCommunicationNoteModal");
            modal.style.display = "none";
            //$("#AddCommunicationNoteModal").css("display", "none");
            $('html, body', window.parent.document).animate({ scrollTop: 240 }, "fast");
            ResetCommunicationNodeForm();
        }

        function LoadGroupCommunicationNotes() {
            ResetCommunicationNodeForm();
            $.ajax({
                type: "POST", url: "GetGroupForCommunicationNotes",
                dataType: "json",
                contentType: "application/json",
                success: function (Jsondata) {
                    $.each(Jsondata, function (data, value) {
                        $("#SelectCommunicationNoteGroup").append($("<option></option>").val(value.Id).html(value.Name));
                    });
                }

            });
        }

        function ResetCommunicationNodeForm() {
            $("#RowSelectCommunicationNoteGroup").css("background-color", "#56954E");
            $("#RowTxtCommunicationNotes").css("background-color", "#56954E");
            $("#RowTxtCommunicationNoteCC").css("background-color", "#56954E");
            $("#CommunicationNoteValidationError").css("display", "none");
            $('#RowCommunicationNoteIndividual').css("display", "none");

            $('#SelectCommunicationNoteGroup').val(0);
            $('#SelectNoteIndividual').val(0);
            $('#txtIndividualNoteTo').val('');
            $('#txtCommunicationNotes').val('');
            $('#txtCommunicationNoteCC').val('');
            $('#HiddenCommunicationNoteMailReceiver').val('');
            $('input:radio[name=CommunicationNodeRadio][value=Group]').attr('checked', true)
            $('input:radio[name=CommunicationNodeRadio][value=Individual]').attr('checked', false)
        }

        function SaveCommunicationNodeData() {

            var qrystr = new URLSearchParams(window.location.search);
            var BOMManagerID = qrystr.get("BOM");
            var selectedCusReqIds = [];

            var gridInstance = $('#grid_md').pqGrid('getInstance');
            $.each(gridInstance.grid.SelectRow().getSelection(), function (index, value) {
                selectedCusReqIds.push(value.rowData.CustomerRequirementId);
            });

            var cCUserIds = $(".dynamiclabelid-note").map(function () {
                return this.id;
            }).get().join("||");
            var requestToPOHubBy = $('#HiddenBomRequestToPOHubBy').val() == null || $('#HiddenBomRequestToPOHubBy').val() == '' ? 0 : $('#HiddenBomRequestToPOHubBy').val();
            var updateByPH = $('#HiddenBomUpdateByPH').val() == null || $('#HiddenBomUpdateByPH').val() == '' ? 0 : $('#HiddenBomUpdateByPH').val();
            var companyNo = $('#HiddenBomCompanyNo').val() == null || $('#HiddenBomCompanyNo').val() == '' ? 0 : $('#HiddenBomCompanyNo').val();
            var contact2No = $('#HiddenBomContact2No').val() == null || $('#HiddenBomContact2No').val() == '' ? 0 : $('#HiddenBomContact2No').val();

            $.ajax({
                processData: true,
                contentType: 'application/json',
                type: 'POST',
                url: 'SaveCommunicationNotesData?&BOMManagerID=' + BOMManagerID
                    + '&cusReqIds=' + selectedCusReqIds.join(',')
                    + '&sendToGroup=' + $('#SelectCommunicationNoteGroup').val()
                    + '&notes=' + $('#txtCommunicationNotes').val()
                    + '&cCUserIds=' + cCUserIds
                    + '&BOMManagerName=' + $('#HiddenBOMManagerName').val()
                    + '&sendTo=' + $('#SelectNoteIndividual').val()
                    + '&companyNo=' + companyNo
                    + '&contact2No=' + contact2No
                    + '&requestToPOHubBy=' + requestToPOHubBy
                    + '&updateByPH=' + updateByPH,
                dataType: "json",

                success: function (data) {
                    ResetCommunicationNodeForm();
                    if (data) {
                        location.reload();
                    }
                },
                error: function (err) {
                    console.log('Error in Updating BOM Line Item');
                }
            });
        }

        function LoadEditBOMData() {

            $.ajax({
                processData: true,
                contentType: 'application/json',
                type: 'POST',
                url: 'LoadEditBOMData?CustReqID=' + $("#HDCustomerRequirementID").val(),
                dataType: "json",

                success: function (data) {
                    console.log(data);
                    ResetEditBOMForm();
                    $("#LabelEditBOMCustomer").text(data.CompanyName);
                    $("#LabelEditBOMContact").text(data.ContactName);
                    $("#TextEditBOMQuantity").val(data.Quantity);
                    $("#LabelEditBOMPartNo").text(data.Part);
                    if (data.ManufacturerNo != null && data.ManufacturerNo != 0) {
                        $("#LabelEditBOMManufacturer").text(data.ManufacturerName);
                        $("#HDEditBOMManufacturerID").val(data.ManufacturerNo);
                        $("#TextEditBOMManufacturer").css("display", "none");
                        $("#RemoveEditBOMManufacturer").css("display", "block");
                    }
                    else {
                        $("#TextEditBOMManufacturer").css("display", "block");
                        $("#TextEditBOMManufacturer").val(null);
                        $("#HDEditBOMManufacturerID").val(null);
                        $("#LabelEditBOMManufacturer").text(null);
                        $("#RemoveEditBOMManufacturer").css("display", "none");
                    }
                    if (data.PackageNo != null && data.PackageNo != 0) {
                        $("#LabelEditBOMPackage").text(data.PackageName);
                        $("#HDEditBOMPackageID").val(data.PackageNo);
                        $("#TextEditBOMPackage").css("display", "none");
                        $("#RemoveEditBOMPackage").css("display", "block");
                    }
                    else {
                        $("#TextEditBOMPackage").css("display", "block");
                        $("#TextEditBOMPackage").val(null);
                        $("#HDEditBOMPackageID").val(null);
                        $("#LabelEditBOMPackage").text(null);
                        $("#RemoveEditBOMPackage").css("display", "none");
                    }
                    if (data.ProductNo != null && data.ProductNo != 0) {
                        $("#LabelEditBOMProduct").text(data.ProductDescription);
                        $("#HDEditBOMProductID").val(data.ProductNo);
                        $("#TextEditBOMProduct").css("display", "none");
                        $("#RemoveEditBOMProduct").css("display", "block");
                    }
                    else {
                        $("#TextEditBOMProduct").css("display", "block");
                        $("#TextEditBOMProduct").val(null);
                        $("#HDEditBOMProductID").val(null);
                        $("#LabelEditBOMProduct").text(null);
                        $("#RemoveEditBOMProduct").css("display", "none");
                    }
                    if (data.SupportTeamMemberNo != null && data.SupportTeamMemberNo != 0) {
                        $("#LabelEditBOMSupportMember").text(data.SupportTeamMemberName);
                        $("#HDEditBOMSupportMemberID").val(data.SupportTeamMemberNo);
                        $("#TextEditBOMSupportMember").css("display", "none");
                        $("#RemoveEditBOMSupportMember").css("display", "block");
                    }
                    else {
                        $("#TextEditBOMSupportMember").css("display", "block");
                        $("#TextEditBOMSupportMember").val(null);
                        $("#HDEditBOMSupportMemberID").val(null);
                        $("#LabelEditBOMSupportMember").text(null);
                        $("#RemoveEditBOMSupportMember").css("display", "none");
                    }
                    $("#TextEditBOMDateCode").val(data.DateCode);
                    //LoadEditBOMSalesman(data.Salesman);
                    $('#LabelEditBOMSalesPerson').text(data.SalesmanName);
                    $('#HiddenEditBOMSalesPerson').val(data.Salesman);
                    $('#LabelEditBOMCurrency').text(data.CurrencyDescription);
                    $('#HiddenEditBOMCurrency').val(data.CurrencyNo);
                    LoadReqDropdown("RTrace", data.RequirementforTraceability)
                    LoadReqDropdown("RType", data.ReqTypeText)
                    LoadReqDropdown("day", data.QuoteValidityText)
                    $("#TextEditBOMTargetPrice").val(data.Price);
                    //LoadCurrencyForEditBOM(data.CurrencyNo);
                    LoadMSLForEditBOM(data.MSL);
                    LoadUsageDropdown(data.UsageNo);
                    $("#TextEditBOMDeliveryDate").val(getCustomDate(data.DatePromised));
                    $("#TextEditBOMRFQClosingDate").val(getCustomDate(data.RFQClosingDate));
                    $("#TextEditBOMCustomerDecisionDate").val(getCustomDate(data.CustomerDecisionDate));
                    $("#TextEditBOMBOMName").val(data.BOMName);
                    $("#TextEditBOMCustomerPart").val(data.CustomerPart);
                    $("#TextEditBOMAnnualUsage").val(data.EAU);
                    $("#EditBOMCustomerNotes").val(data.Notes);
                    $("#EditBOMInternalNotes").val(data.Instructions);
                    LoadROHS(data.ROHS);

                },
                error: function (err) {
                    console.log('Error in Loading Edit BOM Data');
                }
            });
        }


        function LoadEditBOMSalesman(SalesmanNo) {
            $.ajax({
                processData: true,
                contentType: 'application/json',
                type: 'POST',
                url: 'GetEmployeeList',
                dataType: "json",

                success: function (data) {
                    listItems = '';
                    listItems = defaultOption;
                    for (var i = 0; i < data.length; i++) {
                        listItems += optionStart + data[i].LoginId + "'>" + data[i].EmployeeName + optionEnd;
                    }
                    $("#SelectEditBOMSalesPerson").html(listItems);
                    $("#SelectEditBOMSalesPerson").val(SalesmanNo);
                },
                error: function (err) {
                    console.log('Error in Loading Employee List for Edit BOM');
                }
            });
        }
        function LoadReqDropdown(ReqType, value) {
            $.ajax({
                processData: true,
                contentType: 'application/json',
                type: 'POST',
                url: 'GetReqDropDownData?ReqType=' + ReqType,
                dataType: "json",

                success: function (data) {
                    listItems = '';
                    listItems = defaultOption;
                    for (var i = 0; i < data.length; i++) {
                        listItems += optionStart + data[i].UsageId + "'>" + data[i].Name + optionEnd;
                    }
                    if (ReqType == "RTrace") {
                        $("#SelectEditBOMTraceability").html(listItems);
                        $("#SelectEditBOMTraceability").val(value);
                    }
                    if (ReqType == "RType") {
                        $("#SelectEditBOMType").html(listItems);
                        $("#SelectEditBOMType").val($('#SelectEditBOMType option').filter(function () { return $(this).html() == value; }).val());
                    }
                    if (ReqType == "day") {
                        $("#SelectEditBOMQuoteValidity").html(listItems);
                        if (value == null || value == "") {
                            $("#SelectEditBOMQuoteValidity").val(0);
                        }
                        else {
                            $("#SelectEditBOMQuoteValidity").val($('#SelectEditBOMQuoteValidity option').filter(function () { return $(this).html() == value; }).val());
                        }
                    }
                },
                error: function (err) {
                    console.log('Error in Loading Req Dropdowns List for Edit BOM');
                }
            });
        }

        function LoadCurrencyForEditBOM(CurrencyNo) {
            $.ajax({
                processData: true,
                contentType: 'application/json',
                type: 'POST',
                url: 'GetCurrencyList',
                dataType: "json",

                success: function (data) {
                    listItems = '';
                    listItems = defaultOption;
                    for (var i = 0; i < data.length; i++) {
                        listItems += optionStart + data[i].CurrencyId + "'>" + data[i].CurrencyCode + '-' + data[i].CurrencyDescription + optionEnd;
                    }
                    $("#SelectEditBOMCurrency").html(listItems);
                    $("#SelectEditBOMCurrency").val(CurrencyNo);
                },
                error: function (err) {
                    /*alert(err);*/
                    console.log('Error in Loading Loading Currency for Edit BOM');
                }
            });
        }

        function LoadMSLForEditBOM(MSL) {
            $.ajax({
                type: 'GET',
                contentType: 'application/json',
                //dataType: 'json',
                url: handlerUrl + '?action=GetMSL',
                async: true,
                success: function (data) {
                    var jsonData = JSON.parse(data);
                    listItems = '';
                    listItems = defaultOption;
                    for (var i = 0; i < jsonData.length; i++) {
                        listItems += optionStart + jsonData[i].MSLLevels + "'>" + jsonData[i].MSLLevels + optionEnd;
                    }
                    $("#SelectEditBOMMSL").html(listItems);
                    $("#SelectEditBOMMSL").val(MSL);

                },
                error: function () { console.log("Error in fetching MSL Dropdown detail"); }
            });
        }

        function LoadUsageDropdown(value) {
            $.ajax({
                processData: true,
                contentType: 'application/json',
                type: 'POST',
                url: 'GetUsageDropDownData',
                dataType: "json",

                success: function (data) {
                    listItems = '';
                    listItems = defaultOption;
                    for (var i = 0; i < data.length; i++) {
                        listItems += optionStart + data[i].UsageId + "'>" + data[i].Name + optionEnd;
                    }

                    $("#SelectEditBOMUsage").html(listItems);
                    if (value == null) {
                        $("#SelectEditBOMUsage").val(0);
                    }
                    else {
                        $("#SelectEditBOMUsage").val(value);
                    }
                },
                error: function (err) {
                    console.log('Error in Loading Usage Dropdown List for Edit BOM');
                }
            });
        }

        function LoadROHS(ROHS) {

            $.ajax({
                type: 'GET',
                contentType: 'application/json',
                //dataType: 'json',
                url: handlerUrl + '?action=GetROHSStatus',
                async: true,
                success: function (data) {
                    var jsonData = JSON.parse(data);
                    listItems = '';
                    listItems = defaultOption;
                    for (var i = 0; i < jsonData.length; i++) {
                        listItems += optionStart + jsonData[i].ID + "'>" + jsonData[i].Name + optionEnd;
                    }
                    $("#SelectEditBOMROHS").html(listItems);
                    $("#SelectEditBOMROHS").val(ROHS);

                },
                error: function () { console.log("Error in fetching ROHS Status Dropdown detail"); }
            });
        }

        $("#TextEditBOMSupportMember").autocomplete({
            source: function (request, response) {
                $.ajax({
                    url: "GetMailToList",
                    type: "POST",
                    dataType: "json",
                    data: { searchString: request.term + '%' },
                    success: function (data) {
                        response($.map(data, function (item) {
                            return { label: item.EmployeeName, value: item.LoginId };
                        }))

                    }
                })
            },
            select: function (e, ui) {
                $("#TextEditBOMSupportMember").css("display", "none");
                $("#HDEditBOMSupportMemberID").val(ui.item.value);
                $("#LabelEditBOMSupportMember").text(SetCleanText(ui.item.label, true));
                $("#RemoveEditBOMSupportMember").css("display", "block");
            }, minLength: 2
        });

        $("#RemoveEditBOMSupportMember").click(function () {
            $("#TextEditBOMSupportMember").css("display", "block");
            $("#TextEditBOMSupportMember").val(null);
            $("#HDEditBOMSupportMemberID").val(null);
            $("#LabelEditBOMSupportMember").text(null);
            $("#RemoveEditBOMSupportMember").css("display", "none");
        });

        $("#TextEditBOMManufacturer").autocomplete({
            minLength: 2,
            source: function (request, response) {
                $.ajax({
                    processData: true,
                    contentType: 'application/json',
                    type: 'GET',
                    url: handlerUrl + '?action=GetManufacturer&MrfSearch=' + $("#TextEditBOMManufacturer").val(),
                    dataType: "json",

                    success: function (data) {
                        autocompleteCount = data.length;
                        response(data);
                    },
                    error: function (err) {
                    }
                });
            },
            open: function (event, ui) {
                $("ul").prepend('<li class ="AutoCompleteHeader""><div>' + autocompleteCount + ' result(s)</div></li>');
                autocompleteCount = 0;
            },
            select: function (event, ui) {
                $("#TextEditBOMManufacturer").css("display", "none");
                $("#HDEditBOMManufacturerID").val(ui.item.value);
                $("#LabelEditBOMManufacturer").text(SetCleanText(ui.item.label, true));
                $("#RemoveEditBOMManufacturer").css("display", "block");
                return false;
            }
        })
            .autocomplete("instance")._renderItem = function (ul, item) {
                return $("<li>")
                    .append("<div>" + SetCleanText(item.label, true) + "</div>")
                    .appendTo(ul);
            };

        $("#RemoveEditBOMProduct").click(function () {
            $("#TextEditBOMProduct").css("display", "block");
            $("#TextEditBOMProduct").val(null);
            $("#HDEditBOMProductID").val(null);
            $("#LabelEditBOMProduct").text(null);
            $("#RemoveEditBOMProduct").css("display", "none");
        });

        $("#RemoveEditBOMManufacturer").click(function () {
            $("#TextEditBOMManufacturer").css("display", "block");
            $("#TextEditBOMManufacturer").val(null);
            $("#HDEditBOMManufacturerID").val(null);
            $("#LabelEditBOMManufacturer").text(null);
            $("#RemoveEditBOMManufacturer").css("display", "none");
        });
        $("#RemoveEditBOMPackage").click(function () {
            $("#TextEditBOMPackage").css("display", "block");
            $("#TextEditBOMPackage").val(null);
            $("#HDEditBOMPackageID").val(null);
            $("#LabelEditBOMPackage").text(null);
            $("#RemoveEditBOMPackage").css("display", "none");
        });

        $("#TextEditBOMProduct").autocomplete({
            minLength: 2,
            source: function (request, response) {
                $.ajax({
                    processData: true,
                    contentType: 'application/json',
                    type: 'GET',
                    url: handlerUrl + '?action=GetProduct&ProdSearch=' + $("#TextEditBOMProduct").val(),
                    dataType: "json",
                    success: function (data) {
                        autocompleteCount = data.length;
                        response(data);
                    },
                    error: function (err) {
                    }
                });
            },
            open: function (event, ui) {
                $("ul").prepend('<li class ="AutoCompleteHeader""><div>' + autocompleteCount + ' result(s)</div></li>');
                autocompleteCount = 0;
            },
            select: function (event, ui) {
                $("#TextEditBOMProduct").css("display", "none");
                $("#HDEditBOMProductID").val(ui.item.value);
                $("#LabelEditBOMProduct").text(SetCleanText(ui.item.label, true));
                $("#RemoveEditBOMProduct").css("display", "block");
                return false;
            }
        })
            .autocomplete("instance")._renderItem = function (ul, item) {
                return $("<li>")
                    .append("<div>" + SetCleanText(item.label, true) + "</div>")
                    .appendTo(ul);
            };

        $("#TextEditBOMPackage").autocomplete({
            minLength: 2,
            source: function (request, response) {
                $.ajax({
                    processData: true,
                    contentType: 'application/json',
                    type: 'GET',
                    url: 'GetPackages?search=' + $("#TextEditBOMPackage").val() + '%',
                    dataType: "json",
                    success: function (data) {
                        autocompleteCount = data.length;
                        response($.map(data, function (item) {
                            return { label: item.PackageDescription, value: item.PackageId };
                        }))
                    },
                    error: function (err) {
                    }
                });
            },
            open: function (event, ui) {
                $("ul").prepend('<li class ="AutoCompleteHeader""><div>' + autocompleteCount + ' result(s)</div></li>');
                autocompleteCount = 0;
            },
            select: function (event, ui) {
                //debugger;
                $("#TextEditBOMPackage").css("display", "none");
                $("#HDEditBOMPackageID").val(ui.item.value);
                $("#LabelEditBOMPackage").text(SetCleanText(ui.item.label, true));
                $("#RemoveEditBOMPackage").css("display", "block");
                return false;
            }
        })
            .autocomplete("instance")._renderItem = function (ul, item) {
                return $("<li>")
                    .append("<div>" + SetCleanText(item.label, true) + "</div>")
                    .appendTo(ul);
            };


        function ValidateEditBOMForm() {
            var formIsValid = true;
            if ($('#SelectEditBOMTraceability').val() == 0 || $('#SelectEditBOMTraceability').val() == null) {
                $("#RowEditBOMTraceability").css("background-color", "#990000");
                formIsValid = false;
            }
            else {
                $("#RowEditBOMTraceability").css("background-color", "#56954E");
                if (formIsValid)
                    formIsValid = true;
            }
            //if ($('#SelectEditBOMSalesPerson').val() == 0 || $('#SelectEditBOMSalesPerson').val() == null) {
            //    $("#RowEditBOMSalesPerson").css("background-color", "#990000");
            //    formIsValid = false;
            //}
            //else {
            //    $("#RowEditBOMSalesPerson").css("background-color", "#56954E");
            //    if (formIsValid)
            //        formIsValid = true;
            //}
            if ($('#TextEditBOMQuantity').val() == "" || $('#TextEditBOMQuantity').val() == null || (parseInt($('#TextEditBOMQuantity').val()) <= 0)) {
                $("#RowEditBOMQuantity").css("background-color", "#990000");
                formIsValid = false;
            }
            else {
                $("#RowEditBOMQuantity").css("background-color", "#56954E");
                if (formIsValid)
                    formIsValid = true;
            }
            if ($('#SelectEditBOMType').val() == 0 || $('#SelectEditBOMType').val() == null) {
                $("#RowEditBOMType").css("background-color", "#990000");
                formIsValid = false;
            }
            else {
                $("#RowEditBOMType").css("background-color", "#56954E");
                if (formIsValid)
                    formIsValid = true;
            }
            if ($('#HDEditBOMManufacturerID').val() == "" || $('#HDEditBOMManufacturerID').val() == null) {
                $("#RowEditBOMManufacturer").css("background-color", "#990000");
                formIsValid = false;
            }
            else {
                $("#RowEditBOMManufacturer").css("background-color", "#56954E");
                if (formIsValid)
                    formIsValid = true;
            }
            //if ($('#SelectEditBOMCurrency').val() == 0 || $('#SelectEditBOMCurrency').val() == null) {
            //    $("#RowEditBOMCurrency").css("background-color", "#990000");
            //    formIsValid = false;
            //}
            //else {
            //    $("#RowEditBOMCurrency").css("background-color", "#56954E");
            //    if (formIsValid)
            //        formIsValid = true;
            //}
            if ($('#HDEditBOMProductID').val() == "" || $('#HDEditBOMProductID').val() == null) {
                $("#RowEditBOMProduct").css("background-color", "#990000");
                formIsValid = false;
            }
            else {
                $("#RowEditBOMProduct").css("background-color", "#56954E");
                if (formIsValid)
                    formIsValid = true;
            }
            if ($('#TextEditBOMDeliveryDate').val() == "" || $('#TextEditBOMDeliveryDate').val() == null) {
                $("#RowEditBOMDeliveryDate").css("background-color", "#990000");
                formIsValid = false;
            }
            else {
                $("#RowEditBOMDeliveryDate").css("background-color", "#56954E");
                if (formIsValid)
                    formIsValid = true;
            }
            if ($('#TextEditBOMTargetPrice').val() == "" || $('#TextEditBOMTargetPrice').val() == null) {
                $("#RowEditBOMTargetPrice").css("background-color", "#990000");
                formIsValid = false;
            }
            else {
                $("#RowEditBOMTargetPrice").css("background-color", "#56954E");
                if (formIsValid)
                    formIsValid = true;
            }
            return formIsValid;
        }

        $(".BtnSaveBOMEdit").click(function () {
            if (ValidateEditBOMForm()) {
                SaveEditBOMItemData();
            }
            else {
                $("#EditBOMValidationError").css("display", "block");
            }
        });

        function ResetEditBOMForm() {
            $("#RowEditBOMTraceability").css("background-color", "#56954E");
            $("#RowEditBOMSalesPerson").css("background-color", "#56954E");
            $("#RowEditBOMQuantity").css("background-color", "#56954E");
            $("#RowEditBOMType").css("background-color", "#56954E");
            $("#RowEditBOMManufacturer").css("background-color", "#56954E");
            $("#RowEditBOMCurrency").css("background-color", "#56954E");
            $("#RowEditBOMProduct").css("background-color", "#56954E");
            $("#RowEditBOMDeliveryDate").css("background-color", "#56954E");
            $("#RowEditBOMTargetPrice").css("background-color", "#56954E");
            $("#EditBOMValidationError").css("display", "none");
        }

        function SaveEditBOMItemData() {
            var qrystr = new URLSearchParams(window.location.search);
            var BOMManagerID = qrystr.get("BOM");
            $.ajax({
                processData: true,
                contentType: 'application/json',
                type: 'POST',
                url: 'SaveEditBOMItemData?&customerRequirementId=' + $('#HDCustomerRequirementID').val() + '&RequirementforTraceability=' + $('#SelectEditBOMTraceability').val() + '&salesman=' + $('#HiddenEditBOMSalesPerson').val()
                    + '&quantity=' + $('#TextEditBOMQuantity').val() + '&Usage=' + $('#SelectEditBOMUsage').val() + '&Type=' + $('#SelectEditBOMType').val() + '&EAU=' + $('#TextEditBOMAnnualUsage').val() + '&manufacturerNo=' + $('#HDEditBOMManufacturerID').val()
                    + '&customerPart=' + $('#TextEditBOMCustomerPart').val() + '&TargetSellPrice=' + $('#TextEditBOMTargetPrice').val() + '&currencyNo=' + $('#HiddenEditBOMCurrency').val() + '&rohs=' + $('#SelectEditBOMROHS').val()
                    + '&dateCode=' + $('#TextEditBOMDateCode').val() + '&productNo=' + $('#HDEditBOMProductID').val() + '&PackageNo=' + $('#HDEditBOMPackageID').val() + '&MSL=' + $('#SelectEditBOMMSL').val() + '&DatePromised=' + formattedDate($('#TextEditBOMDeliveryDate').val())
                    + '&CustomerDecisionDate=' + formattedDate($('#TextEditBOMCustomerDecisionDate').val()) + '&RFQClosingDate=' + formattedDate($('#TextEditBOMRFQClosingDate').val()) + '&QuoteValidityRequired=' + $('#SelectEditBOMQuoteValidity').val() + '&BOMManagerName=' + $('#TextEditBOMBOMName').val()
                    + '&notes=' + $('#EditBOMCustomerNotes').val() + '&instructions=' + $('#EditBOMInternalNotes').val() + '&SupportTeamMemberNo=' + $('#HDEditBOMSupportMemberID').val() + '&BOMManagerID=' + BOMManagerID,
                dataType: "json",

                success: function (data) {
                    ResetEditBOMForm();
                    if (data) {
                        location.reload();
                    }
                },
                error: function (err) {
                    console.log('Error in Updating BOM Line Item');
                }
            });
        }

        document.querySelector('#TextEditBOMQuantity').addEventListener('keydown', function (evt) {
            !/(^\d*\.?\d*$)|(Backspace|Control|Meta|Left|Right|Shift|Tab)/.test(evt.key) && evt.preventDefault()
        })

        document.querySelector('#TextEditBOMTargetPrice').addEventListener('keydown', function (evt) {
            !/(^\d*\.?\d*$)|(Backspace|Control|Meta|Left|Right|Shift|Tab)/.test(evt.key) && evt.preventDefault()
        })

        document.querySelector('#UpliftAllPercentage').addEventListener('keydown', function (evt) {
            !/(^\d*\.?\d*$)|(Backspace|Control|Meta|Left|Right|Shift|Tab)/.test(evt.key) && evt.preventDefault()
        })

        $("#LinkUpliftPriceAll").click(function () {
            UpliftPrice();
        });

        $("#LinkUpliftPriceSelected").click(function () {
            var selectedCustomerRequirementIds = '';
            var grid = $('#grid_md').pqGrid('instance');
            //debugger;
            //var checked = grid.Checkbox('ISPrimarySource').getCheckedNodes().map(function (rd) {
            //    debugger;
            //    //console.log(rd.CustomerRequirementId);
            //    selectedCustomerRequirementIds = selectedCustomerRequirementIds + rd.CustomerRequirementId + '|';
            //});
            //grid.SelectRow().getSelection().map(function (rowList) {
            //    debugger;
            //    console.log(rowList.rowData +'+_+_+_+_+_+_');
            //    //return rowList.rowData.id;

            //})
            //console.log(selectedCustomerRequirementIds);
            //grid.pdata[1].ISPrimarySource;
            $.each(grid.pdata, function (data1, value) {
                //console.log(data1 + '___________' + value.ISPrimarySource + '++++' + value.QuoteGenerated);
                if (value.ISPrimarySource == true && value.QuoteGenerated == false) { selectedCustomerRequirementIds = selectedCustomerRequirementIds + value.CustomerRequirementId + '|'; }

            });
            console.log(selectedCustomerRequirementIds);
            if (selectedCustomerRequirementIds == '') {
                alert('Plesae select BOM item.')
                return;
            }
            UpliftPrice(selectedCustomerRequirementIds);
        });

        function UpliftPrice(CustomerRequirementIds) {
            if (CustomerRequirementIds == undefined)
                CustomerRequirementIds = ''
            /*if (($('#BOMStatus').val() == 4 || $('#BOMStatus').val() == 5) && $('#PriceUplifted').val() != "TRUE") {*/
            if (($('#BOMStatus').val() == 4 || $('#BOMStatus').val() == 5)) {
                //GetUpliftPercentageAll();
                var modal = document.getElementById("UpliftPriceModal");
                modal.style.display = "block"
                $('html, body', window.parent.document).animate({ scrollTop: 240 }, "fast");
                LoadBOMManagerSourcingForUplift(CustomerRequirementIds);
                $("#grid_up").pqGrid("refreshDataAndView");
            }
        }

        $(".BtnCancelUplift").click(function () {

            var modal = document.getElementById("UpliftPriceModal");
            modal.style.display = "none"
            $('html, body', window.parent.document).animate({ scrollTop: 240 }, "fast");
            $("#UpliftAllPercentage").val('');
        });
        $(".BtnConfirmUplift").click(function () {
            SaveUpliftAllPrice();
        });

        function SaveUpliftAllPrice() {
            if (!confirm("Once price is uplifted cannot be uplifted again for that sourcing \n Are you sure want to Continue")) {
                return;
            }
            var qrystr = new URLSearchParams(window.location.search);
            var BOMManagerID = qrystr.get("BOM");
            $.ajax({
                processData: true,
                contentType: 'application/json',
                type: 'POST',
                url: 'SaveUpliftAllPrice?BOMManagerID=' + BOMManagerID + '&UpliftPercentage=' + $('#UpliftAllPercentage').val(),
                dataType: "json",

                success: function (data) {
                    if (data) {
                        location.reload();
                    }
                },
                error: function (err) {
                    console.log('Error in Saving Uplift Percentage');
                }
            });
        }
        function GetUpliftPercentageAll() {
            var qrystr = new URLSearchParams(window.location.search)
            var qrystrkey = qrystr.get("BOM");

            $.ajax({
                processData: true,
                contentType: 'application/json',
                type: 'POST',
                url: 'GetUpliftPercentageAll?BOM=' + qrystrkey,
                dataType: "json",

                success: function (data) {
                    $('#UpliftAllPercentage').val(data.UpliftPercentage);
                },
                error: function (err) {
                    console.log('Error in fetching Uplift Percentage');
                }
            });
        }
        $("#btnSBCancelModalRemoveUpliftAll").click(function () {

            var modal = document.getElementById("UpliftPriceModal");
            modal.style.display = "none"
            $('html, body', window.parent.document).animate({ scrollTop: 240 }, "fast");
        });
        $("#btnConfirmModalRemoveUpliftAll").click(function () {
            RemoveUpliftPriceAll();
        });
        function RemoveUpliftPriceAll() {
            var qrystr = new URLSearchParams(window.location.search);
            var BOMManagerID = qrystr.get("BOM");
            $.ajax({
                processData: true,
                contentType: 'application/json',
                type: 'POST',
                url: 'RemoveUpliftPriceAll?BOMManagerID=' + BOMManagerID,
                dataType: "json",

                success: function (data) {
                    if (data) {
                        location.reload();
                    }
                },
                error: function (err) {
                    console.log('Error in Saving Uplift Percentage');
                }
            });
        }
        function LoadBOMManagerSourcingForUplift(CustomerRequirementIds) {
            if (CustomerRequirementIds == undefined)
                CustomerRequirementIds = ''
            var colModel = [
                {
                    title: "Part No. <br/> Notes",
                    width: "15%",
                    /*dataType: "float",*/
                    //align: "right",
                    dataIndx: "Part",
                    render: function (ui) {

                        var htmlstr = "<span>" + ui.rowData.Part + "</span ><br/>";
                        htmlstr = htmlstr + " <span>" + ui.rowData.Notes + "</span > ";
                        return htmlstr;
                    }
                },
                {
                    title: "CustomerRequirementId", //title of column.
                    width: "0%", //initial width of column
                    dataIndx: "CustomerRequirementId",
                    hidden: true,
                },
                {
                    title: "BOMStatus", //title of column.
                    width: "0%", //initial width of column
                    dataIndx: "BOMStatus",
                    hidden: true,
                },
                {
                    title: "Supplier <br> Related Quotes",
                    width: "12%",
                    //dataType: "string",
                    dataIndx: "Supplier",
                    render: function (ui) {
                        if (ui.rowData.QuoteID == null && ui.rowData.QuoteNumber == null) {
                            return "<span><a href='#'>" + ui.rowData.SupplierName + "</a></span>" + "<br>";
                        }
                        else {
                            return "<span><a href='#'>" + ui.rowData.SupplierName + "</a></span>" + "<br>" +
                                "<span><a href='/Ord_QuoteDetail.aspx?qt=" + ui.rowData.QuoteID + "' target='_top'>" + "Quote : " + ui.rowData.QuoteNumber + "</a></span>";
                        }
                    }
                },
                {
                    title: "Manufacturer <br/> Date Code",
                    width: "12%",
                    /*dataType: "float",*/
                    //align: "right",
                    dataIndx: "ManufacturerName",
                    render: function (ui) {
                        var htmlstr = /*"<span><a  href='../../Con_ManufacturerDetail.aspx?mfr=" + ui.rowData.ManufacturerNo + "' target='_top'>" +*/ ui.rowData.ManufacturerName /*+ "</a></span>";*/
                        htmlstr = htmlstr + "<br> <span>" + ui.rowData.DateCode + "</span > ";
                        return htmlstr;
                    }
                },
                {
                    title: "Product <br> Package <br> Quantity",
                    width: "12%",
                    dataType: "Date",
                    //align: "right",
                    dataIndx: "ProductName",
                    render: function (ui) {
                        var htmlstr = "<span>" + ui.rowData.ProductName + "</span ><br>";
                        htmlstr = htmlstr + " <span>" + ui.rowData.PackageName + "</span ><br> ";
                        htmlstr = htmlstr + "<span>" + ui.rowData.Quantity + "</span ><br/>";
                        return htmlstr;
                    }
                },
                //{
                //    title: "Offered <br> By",
                //    width: "8%",
                //    /*dataType: "float",*/
                //    //align: "right",
                //    dataIndx: "SalesmanName",
                //    render: function (ui) {
                //        var htmlstr = "<span>" + getCustomDate(ui.rowData.OfferDate.toString()) + "</span ><br>";
                //        htmlstr = htmlstr + " <span>" + ui.rowData.SalesmanName + "</span > ";
                //        return htmlstr;
                //    }
                //},
                {
                    title: "Unit Price <br> Base Currency <br> Line Value",
                    width: "15%",
                    /*dataType: "float",*/
                    //align: "right",
                    dataIndx: "MSL",
                    render: function (ui) {
                        var htmlstr = "<span>" + parseFloat(ui.rowData.UnitPrice).toFixed(4) + ' ' + ui.rowData.ClientCurrencyCode + "</span ><br/>";
                        htmlstr = htmlstr + "<span>" + parseFloat(ui.rowData.BasePriceFigure).toFixed(4) + ' ' + ui.rowData.CurrencyCode + "</span ><br/>";
                        htmlstr = htmlstr + "<span>" + parseFloat(parseFloat(ui.rowData.BasePriceFigure) * parseInt(ui.rowData.Quantity)).toFixed(4) + ' ' + ui.rowData.CurrencyCode + "</span ><br/>";
                        return htmlstr;
                    }
                },
                {
                    title: "Uplift Price <br> Base Currency <br> Uplift Line Value",
                    width: "15%",
                    dataIndx: "Region",
                    render: function (ui) {

                        var htmlstr;
                        if ($('#UpliftAllPercentage').val() != "" && $('#UpliftAllPercentage').val() != undefined) {
                            htmlstr = "<span>" + parseFloat(parseFloat(ui.rowData.UnitPrice) + parseFloat(ui.rowData.UnitPrice * parseFloat($('#UpliftAllPercentage').val()) / 100)).toFixed(4) + ' ' + ui.rowData.ClientCurrencyCode + "</span ><br/>";
                            htmlstr = htmlstr + "<span>" + parseFloat((parseFloat(ui.rowData.BasePriceFigure) + parseFloat(ui.rowData.BasePriceFigure * parseFloat($('#UpliftAllPercentage').val()) / 100))).toFixed(4) + ' ' + ui.rowData.CurrencyCode + "</span ><br/>";
                            htmlstr = htmlstr + "<span>" + parseFloat((parseFloat(ui.rowData.BasePriceFigure) + parseFloat(ui.rowData.BasePriceFigure * parseFloat($('#UpliftAllPercentage').val()) / 100)) * ui.rowData.Quantity).toFixed(4) + ' ' + ui.rowData.CurrencyCode + "</span ><br/>";
                        }
                        return htmlstr;
                    }
                },
                {
                    title: "<br>Profit Per Unit <br> Line Profit",
                    width: "14%",
                    dataIndx: "Region",
                    render: function (ui) {

                        var htmlstr;
                        if ($('#UpliftAllPercentage').val() != "" && $('#UpliftAllPercentage').val() != undefined) {
                            htmlstr = "<br><span>" + parseFloat((parseFloat(ui.rowData.BasePriceFigure) * parseFloat($('#UpliftAllPercentage').val()) / 100)).toFixed(4) + ' ' + ui.rowData.CurrencyCode + "</span ><br/>";
                            htmlstr = htmlstr + "<span>" + parseFloat(parseFloat((parseFloat(ui.rowData.BasePriceFigure) * parseFloat($('#UpliftAllPercentage').val()) / 100)) * ui.rowData.Quantity).toFixed(4) + ' ' + ui.rowData.CurrencyCode + "</span ><br/>";
                        }
                        return htmlstr;
                    }
                }

            ];
            var qrystr = new URLSearchParams(window.location.search);
            var qrystrkey = qrystr.get("BOM");
            var dataModel = {
                location: "remote",
                dataType: "JSON",
                method: "POST",
                url: 'GetBOMManagerSourcingForUplift?BOMManagerId=' + qrystrkey + '&CustomerRequirementIds=' + CustomerRequirementIds,
                getData: function (dataJSON) {
                    var data = dataJSON;
                    return { curPage: dataJSON.curPage, totalRecords: Object.keys(dataJSON).length, data: data };
                }
            };
            var grid2 = $("#grid_up").pqGrid({
                width: "auto", height: 368,
                /*selectionModel: { type: 'row', mode: 'single' },*/
                dataModel: dataModel,
                colModel: colModel,
                editable: false,
                numberCell: { show: false }
            });
        }
        function ViewBOMItemDetails(rowIndx, grid, ui) {
            grid.addClass({ rowIndx: rowIndx, cls: 'pq-row-delete' });
            //debugger;
            var CustomerRequirementIddata = ui.rowData.CustomerRequirementId;
            GetBOMItemwiseDetails(CustomerRequirementIddata);
            //$('#BOMManagerSourcingGrid').css("display", "block");
            var modal = document.getElementById("BOMItemDetailModal");
            modal.style.display = "block"
            $('html, body', window.parent.document).animate({ scrollTop: 240 }, "fast");
        }
        $(".BtnCancelBOMDetail").click(function () {

            var modal = document.getElementById("BOMItemDetailModal");
            modal.style.display = "none"
            $('html, body', window.parent.document).animate({ scrollTop: 240 }, "fast");
        });

        function ValidateEditBOM() {
            var formIsValid = true;
            if ($('#txtName').val().trim() == "" || $('#txtName').val() == null) {
                $("#RowBOMName").css("background-color", "#990000");
                formIsValid = false;
            }
            else {
                $("#RowBOMName").css("background-color", "#56954E");
                if (formIsValid)
                    formIsValid = true;
            }
            if ($('#ddlContact').val() == 0 || $('#ddlContact').val() == null) {
                $("#RowBOMContact").css("background-color", "#990000");
                formIsValid = false;
            }
            else {
                $("#RowBOMContact").css("background-color", "#56954E");
                if (formIsValid)
                    formIsValid = true;
            }
            if ($('#ddlCurrency').val() == 0 || $('#ddlCurrency').val() == null) {
                $("#RowBOMCurrency").css("background-color", "#990000");
                formIsValid = false;
            }
            else {
                $("#RowBOMCurrency").css("background-color", "#56954E");
                if (formIsValid)
                    formIsValid = true;
            }
            if ($('#mydatepicker').val() == "" || $('#mydatepicker').val() == null) {
                $("#RowBOMQuoteReq").css("background-color", "#990000");
                formIsValid = false;
            }
            else {
                $("#RowBOMQuoteReq").css("background-color", "#56954E");
                if (formIsValid)
                    formIsValid = true;
            }
            return formIsValid;
        }

        function ResetEditBOM() {
            $("#RowBOMQuoteReq").css("background-color", "#56954E");
            $("#RowBOMCurrency").css("background-color", "#56954E");
            $("#RowBOMContact").css("background-color", "#56954E");
            $("#RowBOMName").css("background-color", "#56954E");
            $("#EditBOMValidation").css("display", "none");
        }

        $("#RefreshMainInfo").click(function () {
            BindBOMManagerData();
        });

        $("#refreshBOMItem").click(function () {
            $('#BOMManagerSourcingGrid').css("display", "none");
            var gridInstance = $('#grid_md').pqGrid('getInstance');
            if (gridInstance.grid.SelectRow().getSelection().length > 0) {
                RowIndexPartBOMGrid = gridInstance.grid.SelectRow().getSelection()[0].rowIndx;
            }
            $('#grid_md').pqGrid('refreshDataAndView');
        });

        $("#refreshNotesItem").click(function () {
            var gridInstance = $('#grid_md_notes').pqGrid('getInstance');
            if (gridInstance.grid.SelectRow().getSelection().length > 0) {
                RowIndexCommunicationNoteGrid = gridInstance.grid.SelectRow().getSelection()[0].rowIndx;
            }
            $('#grid_md_notes').pqGrid('refreshDataAndView');
        });


        $("#btnUpliftCalculate").click(function () {
            $("#grid_up").pqGrid("refreshDataAndView");
        });

        $("#LinkResetUpliftPriceAll").click(function () {
            if (($('#BOMStatus').val() == 4 || $('#BOMStatus').val() == 5) && $('#PriceUplifted').val() == 0) {
                if (!confirm("Are you Sure you want to revert Uplpift Price for Non-Quoted Items")) {
                    return;
                }
                ResetUpliftPriceAll();
            }
        });
        function ResetUpliftPriceAll() {
            var qrystr = new URLSearchParams(window.location.search)
            var qrystrkey = qrystr.get("BOM");

            $.ajax({
                type: 'POST',
                url: 'ResetUpliftPriceAll',
                data: JSON.stringify({ BOMManagerId: qrystrkey }),
                contentType: 'application/json',
                datatype: 'json',
                async: false,
                success: function (dataJSON) {
                    alert("Price Uplift Reset Successful")
                    console.log(dataJSON);
                    location.reload();
                },
                error: function (jqXhr, textStatus, errorMessage) {
                    console.log('Error in Uplift Price Reset');
                }
            });
        }

        //$("#lblBomNotes").change(function () {
        //    OnNotesTextChange();
        //});

        function OnNotesTextChange() {
            var showChar = 200;
            var moretext = "Read more";
            var lesstext = "Read less";
            $('.more').each(function () {
                var content = $(this).html();

                if (content.length > showChar) {

                    var c = content.substr(0, showChar);
                    var h = content.substr(showChar - 1, content.length - showChar);

                    var html = c + '</span>&nbsp;<span class="more"><span>' + h + '</span>&nbsp;&nbsp;<a href="" class="morelink">' + lesstext + '</a></span>';

                    $(this).html(html);
                }

            });

            $(".morelink").click(function () {
                if ($(this).hasClass("lesslink")) {
                    $(this).removeClass("lesslink");
                    $(this).html(lesstext);
                } else {
                    $(this).addClass("lesslink");
                    $(this).html(moretext);
                }
                $(this).parent().prev().toggle();
                $(this).prev().toggle();
                return false;
            });
        }

        function OnNotesCustomerChange() {
            var showChar = 200;
            var moretext2 = "Read more";
            var lesstext2 = "Read less";
            $('.more2').each(function () {
                var content = $(this).html();

                if (content.length > showChar) {

                    var c = content.substr(0, showChar);
                    var h = content.substr(showChar - 1, content.length - showChar);

                    var html = c + '</span>&nbsp;<span class="more2"><span>' + h + '</span>&nbsp;&nbsp;<a href="" class="morelink2">' + lesstext2 + '</a></span>';

                    $(this).html(html);
                }

            });

            $(".morelink2").click(function () {
                if ($(this).hasClass("lesslink2")) {
                    $(this).removeClass("lesslink2");
                    $(this).html(lesstext2);
                } else {
                    $(this).addClass("lesslink2");
                    $(this).html(moretext2);
                }
                $(this).parent().prev().toggle();
                $(this).prev().toggle();
                return false;
            });
        }

        function OnInternalNoteChange() {
            var showChar = 200;
            var moretext3 = "Read more";
            var lesstext3 = "Read less";
            $('.more3').each(function () {
                var content = $(this).html();

                if (content.length > showChar) {

                    var c = content.substr(0, showChar);
                    var h = content.substr(showChar - 1, content.length - showChar);

                    var html = c + '</span>&nbsp;<span class="more3"><span>' + h + '</span>&nbsp;&nbsp;<a href="" class="morelink3">' + lesstext3 + '</a></span>';

                    $(this).html(html);
                }

            });

            $(".morelink3").click(function () {
                if ($(this).hasClass("lesslink3")) {
                    $(this).removeClass("lesslink3");
                    $(this).html(lesstext3);
                } else {
                    $(this).addClass("lesslink3");
                    $(this).html(moretext3);
                }
                $(this).parent().prev().toggle();
                $(this).prev().toggle();
                return false;
            });
        }

        $('#PrimarySource').click(function () {
            $.ajax({
                processData: true,
                contentType: 'application/json',
                type: 'POST',
                url: 'Reset_PrimarySourcing?ClientId=101&SourcingResultId=' + $('#hdnPrimarySource').val() + '&CustomerRequirementId=' + $('#hdnCustomerRequirementId').val() + '&UpdatedBy=0',
                dataType: "json",

                success: function (data) {
                    alert('Primary Source Reset successfull');
                    $('#refreshBOMItem').click();
                    $('#BOMManagerSourcingGrid').hide();
                },
                error: function (err) {
                    alert('Error while Reset Primary Source.');

                }
            });
        });
        function PurchaseHUBErrorEdit(event) {
            debugger;
            var source = event.target || event.srcElement;
            var CRID = source.id.split('_')[1];
            $("#HDCustomerRequirementID").val(CRID);
            LoadEditBOMData();
            ReopenSendtoHubDialog = 1;
            var modal = document.getElementById("EditBOMItemModal");
            modal.style.display = "block";
            $('html, body', window.parent.document).animate({ scrollTop: 240 }, "fast");
        }
        function BOMItemGridHeightChange(recordsLeft, rpp) {

            var height;

            if (recordsLeft < 0) {
                recordsLeft = rpp + recordsLeft;
                height = recordsLeft * 35 + 40;
            }
            else {
                height = rpp * 35 + 40;
            }
            $('#grid_md .pq-grid-center-o').css('height', height + 7);
            $('#grid_md .pq-grid-center').css('height', height + 7);
            $('#grid_md .pq-body-outer').css('height', height + 7);
            $('#grid_md').pqGrid('option', 'height', height + 50).pqGrid('refresh');
        }

        function NotesItemGridHeightChange(recordsLeft, rpp) {

            var height;

            if (recordsLeft < 0) {
                recordsLeft = rpp + recordsLeft;
                height = recordsLeft * 35 + 40;
            }
            else {
                height = rpp * 35 + 40;
            }
            $('#grid_md_notes .pq-grid-center-o').css('height', height + 7);
            $('#grid_md_notes .pq-grid-center').css('height', height + 7);
            $('#grid_md_notes .pq-body-outer').css('height', height + 7);
            $('#grid_md_notes').pqGrid('option', 'height', height + 50).pqGrid('refresh');
        }

        function SourcingGridHeightChange(recordsLeft, rpp) {

            var height;

            if (recordsLeft < 0) {
                recordsLeft = rpp + recordsLeft;
                height = recordsLeft * 35 + 40;
            }
            else {
                height = rpp * 35 + 40;
            }
            $('#grid_as .pq-grid-center-o').css('height', height + 7);
            $('#grid_as .pq-grid-center').css('height', height + 7);
            $('#grid_as .pq-body-outer').css('height', height + 7);
            $('#grid_as').pqGrid('option', 'height', height + 50).pqGrid('refresh');
        }
        function writeROHS(ROHS) {
            var str = "";
                switch (ROHS) {
                    case 0: str = "ROHS Unknown"; break;
                    case 1: str = "ROHS Compliant"; break;
                    case 2: str = "ROHS Non-Compliant"; break;
                    case 3: str = "ROHS Exempt"; break;
                    case 4: str = "ROHS Not Applicable"; break;
                    case 5: str = "ROHS 2"; break;
                    case 6: str = "ROHS 5/6"; break;
                    case 7: str = "ROHS 6/6"; break;
                    default: str = ""; break;
            }
            return str;
        }
        function writeQuoteValidity(QuoteValidityValue) {
            var str = "";
            switch (QuoteValidityValue) {
                case 0: str = ""; break;
                case 1: str = "1 day"; break;
                case 2: str = "7 day"; break;
                case 3: str = "30 day"; break;
                case 4: str = "90 day"; break;
                default: str = ""; break;
            }
            return str;
        }
        $('input[type=radio][name=SendToHubAssignee]').change(function () {
            if ($(this).val() == 'Group') {
                $('#rowIndividualBuyer').css("display", "none");
                $('#rowGroupBuyer').css("display", "block");
                $('#rowGroupBuyer').css("background-color", "#56954E");
                //LoadMailGroup();
            }
            else if ($(this).val() == 'Individual') {
                $('#rowGroupBuyer').css("display", "none");
                $('#rowIndividualBuyer').css("display", "block");
                $('#rowIndividualBuyer').css("background-color", "#56954E");
                //LoadPHBuyers();
            }
        });

        $('input[type=radio][name=CommunicationNodeRadio]').change(function () {
            if ($(this).val() == 'Group') {
                $('#RowCommunicationNoteIndividual').css("display", "none");
                $('#RowSelectCommunicationNoteGroup').css("display", "table-row");
                $('#RowSelectCommunicationNoteGroup').css("background-color", "#56954E");
            }
            else if ($(this).val() == 'Individual') {
                $('#RowSelectCommunicationNoteGroup').css("display", "none");
                $('#RowCommunicationNoteIndividual').css("display", "table-row");
                $('#RowCommunicationNoteIndividual').css("background-color", "#56954E");
            }
            $('#SelectCommunicationNoteGroup').val(0);
            $('#SelectNoteIndividual').val(0);
        });

        function LoadMailGroup() {
            var val = $('#ddlGroup').val();
            debugger;
            $.ajax({
                type: "POST", url: "GetMailGroup",
                dataType: "json",
                contentType: "application/json",
                success: function (Jsondata) {
                    listItems = '';
                    listItems = defaultOption;
                    for (var i = 0; i < Jsondata.length; i++) {
                        listItems += optionStart + Jsondata[i].MailGroupId + "'>" + Jsondata[i].Name + optionEnd;
                    }
                    $("#ddlGroup").html(listItems);
                    $('#ddlGroup').val(val);
                }

            });
        }
        $("#RefreshGroup").click(function () {
            LoadMailGroup();
        });
</script>

</body>
</html>

