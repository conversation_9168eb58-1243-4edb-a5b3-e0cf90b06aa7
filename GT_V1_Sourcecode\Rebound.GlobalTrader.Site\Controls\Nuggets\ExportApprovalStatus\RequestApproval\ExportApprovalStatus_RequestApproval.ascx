<%@ Control Language="C#" CodeBehind="ExportApprovalStatus_RequestApproval.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.ExportApprovalStatus_RequestApproval" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>

<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="false" AllowQuickHelp="false">

	<Links>
		<ReboundUI:IconButton ID="ibtnSend" runat="server" IconGroup="Nugget" IconTitleResource="Send" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
		<ReboundUI:IconButton ID="ibtnCancel" runat="server" IconGroup="Nugget" IconTitleResource="Cancel" IconCSSType="Cancel" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
	</Links>

	<Explanation><%=Functions.GetGlobalResource("FormExplanations", "SendExportApprovalRequest")%></Explanation>

	<Content>
	
		<ReboundUI_Table:Form id="frm" runat="server">
            <ReboundUI_Form:FormField id="ctlSendMail" runat="server" FieldID="chkSendMail" ResourceTitle="NotifySales">
				<Field>
                    <ReboundUI:ImageCheckBox ID="chkSendMail" runat="server" Enabled="true" Checked="true" />
                    <div id="lblsoNotifyer" style="margin-left: 40px;margin-top: -15px;"></div>
				</Field>
			</ReboundUI_Form:FormField>
			<ReboundFormFieldCollection:SendApprovalRequest id="ctlRequestApproval" runat="server" />
		</ReboundUI_Table:Form>
		
	</Content>
		
</ReboundUI_Form:DesignBase>
