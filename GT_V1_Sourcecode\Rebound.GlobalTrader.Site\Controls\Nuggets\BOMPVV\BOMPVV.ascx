<%--
Marker     Changed by      Date         Remarks
[001]      Umendra         21/01/2019   Adding View Tree Button
--%>
<%@ Control Language="C#" CodeBehind="BOMPVV.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Nuggets.BOMPVV" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" BoxType="Standard">
    <Links>
     
        <ReboundUI:IconButton ID="ibtnEdit" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget"
            IconTitleResource="AddEdit" IconCSSType="Edit" />
       <asp:Label ID="lblPVVBOMIHS" runat="server" class="ihspartstatusdoc"/>&nbsp;
      <ReboundUI:IconButton ID="ibtnDelete" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget"
            IconTitleResource="Delete" IconCSSType="Delete" />

        <ReboundUI:IconButton ID="ibtnView" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget"
            IconTitleResource="View" IconCSSType="Add" />
        </Links>
    
   <Content>
        <ReboundUI:FlexiDataTable ID="tblPVVBOM" runat="server" PanelHeight="100" />
		
	</Content>
    <Forms>
        <ReboundForm:BOMPVV_Edit ID="ctlEdit" runat="server" />
        <ReboundForm:BOMPVV_Delete ID="ctlDelete" runat="server" />
        
        
    </Forms>

    
</ReboundUI_Nugget:DesignBase>
