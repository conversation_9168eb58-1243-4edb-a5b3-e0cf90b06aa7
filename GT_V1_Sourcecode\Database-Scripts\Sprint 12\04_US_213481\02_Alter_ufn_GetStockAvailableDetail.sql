﻿

GO

/*
--===========================================================================================  
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-213481]     Phuc Hoang		 06-Oct-2024		UPDATE		Show Stock Alert on HUBRFQ page as Requirement page
=============================================================================================  
*/

CREATE OR ALTER FUNCTION [dbo].[ufn_GetStockAvailableDetail]                        
(                          
 @PartNo nvarchar(50)= null,                          
 @ClientNo int = 0,                
 @stockid int =0                               
)                                      
RETURNS NVARCHAR(400)                                       
AS                                      
BEGIN                                      
 Declare @StockAvailableMessage nvarchar(400)                                    
 Declare @QuantityInStock nvarchar(100)            
 Declare @QuantityOnOrder nvarchar(100)            
 Declare @QuantityAllocated nvarchar(100)            
 Declare @QuantityAvailable nvarchar(100)             
 Declare @stockNo nvarchar(100)             
 SET @StockAvailableMessage=''
 
IF (@StockAvailableMessage is not null)                                  
  BEGIN                                  
	IF(@stockid!=0)          
		begin          
			if exists(select top 1 QuantityInStock,QuantityOnOrder,QuantityAllocated,QuantityAvailable from vwStock where stockid in (select stockid from tbStock where stockid=@stockid) order by dlup desc )                                
			begin              
			  select top 1 @stockNo=stockid, @QuantityInStock=QuantityInStock,@QuantityOnOrder=QuantityOnOrder,@QuantityAllocated=QuantityAllocated,@QuantityAvailable=QuantityAvailable   
			  from vwStock            
			  where stockid in (select stockid from tbStock where stockid=@stockid) order by dlup desc                     
			end
		end 
	ELSE
		begin          
		if exists(select top 1 QuantityInStock,QuantityOnOrder,QuantityAllocated,QuantityAvailable from vwStock where Part=@PartNo order by dlup desc )                                
			begin 
			  select top 1 @stockNo=stockid from vwStock where Part=@PartNo and QuantityAvailable > 0 and ClientNo = @ClientNo order by dlup desc

			  select @QuantityInStock=SUM(QuantityInStock),@QuantityOnOrder=SUM(QuantityOnOrder),@QuantityAllocated=SUM(QuantityAllocated),@QuantityAvailable=SUM(QuantityAvailable) 
			  from vwStock            
			  where Part=@PartNo                   
			end 
		end                              
   --SET @StockAvailableMessage = @StockAvailableMessage +  @QuantityInStock +' In Stock -'+ @QuantityOnOrder +' On Order -'+ @QuantityAllocated +' Allocated -'+ @QuantityAvailable +' Available -' + @stockNo            
	IF(@QuantityAvailable>0)  
	BEGIN     
		SET @StockAvailableMessage = @StockAvailableMessage +' In Stock : '+@QuantityInStock+' -'+' On Order : '+@QuantityOnOrder+'-' +' Allocated : '+@QuantityAllocated+'-' + ' Available : '+@QuantityAvailable+'-'+ ISNULL(@stockNo, 0)     
    END  
  END                                 
  Return @StockAvailableMessage                                      
END  
GO


