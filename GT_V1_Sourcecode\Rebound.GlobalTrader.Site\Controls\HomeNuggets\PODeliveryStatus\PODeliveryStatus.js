Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.HomeNuggets");Rebound.GlobalTrader.Site.Controls.HomeNuggets.PODeliveryStatus=function(n){Rebound.GlobalTrader.Site.Controls.HomeNuggets.PODeliveryStatus.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.HomeNuggets.PODeliveryStatus.prototype={get_tblPODeliveryStatus:function(){return this._tblPODeliveryStatus},set_tblPODeliveryStatus:function(n){this._tblPODeliveryStatus!==n&&(this._tblPODeliveryStatus=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.HomeNuggets.PODeliveryStatus.callBaseMethod(this,"initialize");this.addRefreshEvent(Function.createDelegate(this,this.getData))},dispose:function(){this.isDisposed||(this._tblPODeliveryStatus&&this._tblPODeliveryStatus.dispose(),this._tblPODeliveryStatus=null,Rebound.GlobalTrader.Site.Controls.HomeNuggets.PODeliveryStatus.callBaseMethod(this,"dispose"))},setupLoadingState:function(){this._tblPODeliveryStatus.show(!1);Rebound.GlobalTrader.Site.Controls.HomeNuggets.PODeliveryStatus.callBaseMethod(this,"setupLoadingState")},getData:function(){this.setupLoadingState();var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/HomeNuggets/PODeliveryStatus");n.set_DataObject("PODeliveryStatus");n.set_DataAction("GetData");n.addDataOK(Function.createDelegate(this,this.getDataComplete));n.addError(Function.createDelegate(this,this.homeNuggetDataError));n.addTimeout(Function.createDelegate(this,this.homeNuggetDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getDataComplete:function(n){var r,u,i,t,f;for(this.showNoneFoundOrContent(n._result.Count),r=n._result,u=n._result.IsPOHub,this._tblPODeliveryStatus.clearTable(),this._tblPODeliveryStatus.show(r.PODeliveryDetail.length>0),i=0;i<r.PODeliveryDetail.length;i++)t=r.PODeliveryDetail[i],f=[$RGT_nubButton_Company(t.CompanyId,t.CompanyName),!u&&t.InternalPurchaseOrderId>0?$RGT_nubButton_InternalPurchaseOrder(t.InternalPurchaseOrderId,t.InternalPurchaseOrderNumber):$RGT_nubButton_PurchaseOrder(t.PurchaseOrderId,t.PurchaseOrderNumber),t.Part,t.PromiseDate],this._tblPODeliveryStatus.addRow(f,null),this._tblPODeliveryStatus.RowColor(i+1,t.RowCSS);this.hideLoading()}};Rebound.GlobalTrader.Site.Controls.HomeNuggets.PODeliveryStatus.registerClass("Rebound.GlobalTrader.Site.Controls.HomeNuggets.PODeliveryStatus",Rebound.GlobalTrader.Site.Controls.HomeNuggets.Base,Sys.IDisposable);