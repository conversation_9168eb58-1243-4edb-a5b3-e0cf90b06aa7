
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [dbo].[usp_KUB_GPCalculationDetails_Cache]  
@ClientID INT=101,  
@PartNumber NVARCHAR(100)=''  
AS  
BEGIN  
IF((SELECT COUNT(1) FROM tbKUB_GPCalculationDetails WHERE Part=@PartNumber AND ClientNo=@ClientID   
AND DLUP>DATEAdd(DAY,-3,GETDATE()))=0)  
BEGIN  
SET NOCOUNT ON  
DELETE FROM tbKUB_GPCalculationDetails WHERE Part=@PartNumber AND ClientNo=@ClientID   
---Total GP based on the last 12 months(Rolling) calculation------                    
    CREATE TABLE #Invoices (                    
          InvoiceID INT    
  , InvoiceNumber INT                   
        , CurrencyRate DECIMAL(16,5)                    
        , ShippingCost DECIMAL(16,5)                    
        , Freight DECIMAL(16,5)   
  , InvoiceDate DATETIME                   
        )                    
    CREATE TABLE #InvoicePreSummary (                    
          InvoiceID INT  
  , InvoiceNumber INT                    
        , ShippingCost DECIMAL(16,5)                    
        , Freight DECIMAL(16,5)                    
        , LandedCost DECIMAL(16,5)  
  , Quantity INT                    
        , InvoiceValue DECIMAL(16,5)                 
        , ClientNo INT  
  , InvoiceDate DATETIME                    
        )                    
                    
    INSERT  INTO #Invoices                    
            SELECT  InvoiceID   
         , InvoiceNumber                   
                  , dbo.ufn_get_exchange_rate(a.CurrencyNo, a.InvoiceDate)                    
                  , isnull(a.ShippingCost,0)                    
                  , isnull(a.Freight,0)  
      , a.InvoiceDate                    
            FROM    dbo.tbInvoice a                    
            WHERE   a.ClientNo = @ClientID                
            AND CAST(a.InvoiceDate AS DATE)>CAST(DATEADD(MONTH,-12,GETDATE()) AS DATE)        
    INSERT  INTO #InvoicePreSummary                    
            SELECT                 
                   i.InvoiceID   
      , i.InvoiceNumber                    
                  , CONVERT(DECIMAL(16,5),i.ShippingCost)                    
                  , i.Freight / i.CurrencyRate                    
                  , isnull(sum(CONVERT(DECIMAL(16,5),ila.LandedCost)), 0)  
      , isnull(ila.Quantity,0)                    
                  , isnull(sum((ila.Price * ila.Quantity) / i.CurrencyRate), 0)                
                  , ila.ClientNo  
      , i.InvoiceDate                    
            FROM    #Invoices i                    
            LEFT JOIN dbo.tbInvoiceLine il ON il.InvoiceNo = i.InvoiceId                    
            LEFT JOIN dbo.vwInvoiceLineAllocation ila ON ila.InvoiceLineNo = il.InvoiceLineId                    
            Where ila.ClientNo=@ClientID AND il.FullPart=@PartNumber                
            GROUP BY                 
                    i.InvoiceID                    
                  , i.ShippingCost                    
                  , i.Freight / i.CurrencyRate                   
      , ila.ClientNo,i.InvoiceNumber,ila.Quantity,i.InvoiceDate  
                    
INSERT INTO tbKUB_GPCalculationDetails                             
SELECT   
@PartNumber AS PART,  
i.ClientNo,  
InvoiceID,  
InvoiceNumber,  
Quantity,  
CAST(ShippingCost AS NVARCHAR(25))+' '+cr.CurrencyCode,  
CAST(Freight AS NVARCHAR(25))+' '+cr.CurrencyCode,  
CAST(LandedCost AS NVARCHAR(25))+' '+cr.CurrencyCode,  
CAST(InvoiceValue AS NVARCHAR(25)) +' '+cr.CurrencyCode,  
CAST(CONVERT(DECIMAL(16,2),((InvoiceValue+Freight)-((LandedCost*Quantity)+ShippingCost))) AS NVARCHAR(25))+' '+cr.CurrencyCode AS TOTAL,  
GETDATE(),  
InvoiceDate  
 FROM #InvoicePreSummary i   
LEFT JOIN tbClient cl ON i.ClientNo=cl.ClientId                
LEFT JOIN tbCurrency cr ON cl.CurrencyNo=cr.CurrencyId     
             
DROP TABLE #Invoices                    
DROP TABLE #InvoicePreSummary  
------------END---------------------   
END   
SET NOCOUNT OFF  
END 
GO


