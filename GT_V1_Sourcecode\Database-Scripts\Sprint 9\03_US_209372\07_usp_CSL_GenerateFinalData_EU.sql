﻿/*   
===========================================================================================  
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[BUG-209372]    cuongdx			21-AUG-2024			Create		compare company from EU Sanctioned list
===========================================================================================  
*/  
-- Create the new procedure  
CREATE OR ALTER PROCEDURE [dbo].[usp_CSL_GenerateFinalData_EU]   
AS  
BEGIN  

 DROP TABLE IF EXISTS #tempCSVImport;  
 CREATE TABLE #tempCSVImport  
 (  
  [name] [nvarchar](max) NULL,  
  [addresses] [nvarchar](max) NULL,  
  [NameShort] [nvarchar](400) NULL,  
 )  
  
 --create index for name and altnames  
 create index idx_csv_company_name_short on #tempCSVImport ([NameShort])  
  
 --insert data of today to#tempCSVImport  
 insert into #tempCSVImport  
 select NameAliasWholeName as name,  
     CONCAT(AddressStreet,' ', AddressCity, ' ',AddressRegion, ' ',AddressPoBox , ' ',AddressZipCode ,' ',AddressPlace) as addresses,
     dbo.GetFirstNWords(NameAliasWholeName, 3)
 from tbCSVImportEU
 where cast(CreatedOn as date) = cast(CURRENT_TIMESTAMP as date)  and EntitySubjectType = 'E'
  
 --create table #tbCompanyTemp to have company short name  
 DROP TABLE IF EXISTS #tbCompanyTemp;  
 CREATE TABLE #tbCompanyTemp  
 (  
  [CompanyId] [int] NOT NULL,  
  [CustomerCode] [nvarchar](15) NULL,  
  [ClientNo] [int] NOT NULL,  
  [Notes] [nvarchar](max) NULL,  
  [ImportantNotes] [nvarchar](max) NULL,  
  [ERAIReported] [bit] NULL,  
  [CompanyName] [nvarchar](128) NOT NULL,  
  [NameShort] [nvarchar](400) NULL,  
 )  
 --create index name short  
 create index idx_company_name_short on #tbCompanyTemp (NameShort)  
  
 --insert data to #tbCompanyTemp  
 insert into #tbCompanyTemp  
 select [CompanyId],  
     [CustomerCode],  
     [ClientNo],  
     [Notes],  
     [ImportantNotes],  
     [ERAIReported],  
     [CompanyName],  
     dbo.GetFirstNWords([CompanyName], 3) --,len(dbo.GetFirstNWords([CompanyName],3))  
 from tbCompany src  
 where (  
     len(src.CompanyName) > 3  
    )  
  
 --[001] SOORYA VYAS 16-JAN-2024  RP-2845 --  Changes in CSL_Sanctioned sheet (Added new column IsSanctioned)      
  
 --- delete tbCSL_Address_Comparision table as this table will be recreated everytime when this procedure runs                        
 --- prepare the company complete address                        
 IF OBJECT_ID(N'tbCSL_Address_Comparision_EU', N'U') IS NOT NULL  
  drop table tbCSL_Address_Comparision_EU  
 ------ delete the data from final table for running process day                      
 delete from tbCSLGTComparisionEU
 where CAST(Insertedon as date) = cast(CURRENT_TIMESTAMP as date)  
  
 ---Use below step to recreate the tbCSL_Address_Comparision          
 select ca.CompanyNo,  
     src.CompanyName,  
     src.CustomerCode,  
     replace(  
       Replace(  
         Stuff(  
            Coalesce(', ' + a.AddressName, '') + Coalesce(', ' + a.Line1, '')  
            + Coalesce(', ' + a.Line2, '') + Coalesce(', ' + a.line3, '')  
            + Coalesce(', ' + a.county, '') + Coalesce(', ' + a.city, '')  
            + Coalesce(', ' + a.State, '') + Coalesce(', ' + a.city, '')  
            + Coalesce(', ' + cntry.CountryName, '') + Coalesce(',' + [Zip], ''),  
            1,  
            1,  
            ''  
           ),  
         ',',  
         ''  
        ),  
       '-',  
       ''  
      ) AS [Address],  
     a.AddressName,  
     a.Line1,  
     a.Line2,  
     a.Line3,  
     a.county,  
     a.City,  
     a.State,  
     a.CountryNo,  
     cntry.CountryName,  
     a.ZIP,  
     src.ClientNo  
 into tbCSL_Address_Comparision_EU  
 from tbCompany src with (nolock)  
  join tbCompanyAddress ca  
   on src.CompanyId = ca.CompanyNo  
  join tbAddress a  
   on ca.AddressNo = a.AddressId  
  left join tbCountry cntry  
   on a.CountryNo = cntry.CountryId ---- 163503,159264                        
 order by ca.CompanyNo  
  
 --insert into tbcslgtcomparision  
 insert into tbCSLGTComparisionEU  
 (  
  companyid,  
  CustomerCode,  
  ClientNo,  
  ClientName,  
  CompanyName,  
  GT_Company_Address,  
  CSL_Name,  
  CSL_Address,  
  Insertedon,  
  Notes,  
  ImportantNotes,  
  ERAIReported  
 )  
 (select src.CompanyId,  
     IsNUll(src.CustomerCode, '') CustomerCode,  
     src.ClientNo,  
     (  
      select tbClient.ClientName  
      from tbclient with (nolock)  
      where tbclient.ClientId = src.ClientNo  
     ) ClientName,  
     src.CompanyName,  
     gtcmpAdd.Address [GT Company Address],  
     dest.name [CSL Name],  
     IsNUll(dest.addresses, '') [CSL Address],  
     CURRENT_TIMESTAMP,  
     src.notes,  
     src.ImportantNotes,  
     case src.ERAIReported  
      when 1 then  
       'true'  
      else  
       'false'  
     end  
 from #tbCompanyTemp src with (nolock)  
  inner join #tempCSVImport dest with (nolock)  
   on (  
       dest.NameShort like src.NameShort + '%'  
      )  
  left join tbCSL_Address_Comparision_EU gtcmpAdd with (nolock)  
   on gtcmpAdd.CompanyNo = src.CompanyId  
 )  
 order by src.CompanyId  
  
 --drop table after imported  
 DROP TABLE IF EXISTS #tempCSVImport;  
 DROP TABLE IF EXISTS #tbCompanyTemp;  
 --select data to export excel  
    select CSLGT.companyid,  
           CSLGT.CustomerCode,  
           CSLGT.ClientNo,  
           CSLGT.ClientName,  
           CSLGT.CompanyName,  
           CSLGT.Notes as [General_customer_info],  
           CSLGT.ImportantNotes as Accounts_notes,  
           CSLGT.GT_Company_Address,  
           CSL_Name,  
           CSL_Address,  
           CSL_ALT_Name,  
           CSLGT.ERAIReported,  
           CASE  
               WHEN IsSanctioned = 1 THEN  
                   'True'  
               ELSE  
                   'False'  
           END as IsSanctioned --[001]                    
    from tbCSLGTComparisionEU CSLGT  
        Left join tbCompany comp  
            on CSLGT.companyid = comp.companyid  
        LEFT Join tbClient cl  
            on cl.ClientId = CSLGT.ClientNo  
    where cast(Insertedon as date) = cast(CURRENT_TIMESTAMP as date)  
          AND comp.Inactive != 1  
          AND cl.Inactive != 1  
END  