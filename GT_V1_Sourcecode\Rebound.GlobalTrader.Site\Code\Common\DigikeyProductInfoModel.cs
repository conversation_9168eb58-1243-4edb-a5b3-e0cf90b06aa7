﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace Rebound.GlobalTrader.Site.Code.Common
{
    public class KeywordSearchRequest
    {
        [JsonProperty(PropertyName = "Keywords")]
        public string Keywords { get; set; }

        [JsonProperty(PropertyName = "Limit")]
        public int RecordCount { get; set; }

        [JsonProperty(PropertyName = "Offset")]
        public int Offset { get; set; }
    }

    public class DigikeyProductInfoResponse
    {

        [JsonProperty(PropertyName = "ProductsCount")]
        public int ProductsCount { get; set; }

        [JsonProperty(PropertyName = "Products")]
        public List<DigikeyProductItem> Products { get; set; }
    }
    
    public class DigikeyProductItem
    {
        [JsonProperty(PropertyName = "Description")]
        public DescriptionInfo Description { get; set; }

        [JsonProperty(PropertyName = "Manufacturer")]
        public ProductManufacturer Manufacturer { get; set; }

        [JsonProperty(PropertyName = "ManufacturerProductNumber")]
        public string ManufacturerProductNumber { get; set; }

        [JsonProperty(PropertyName = "UnitPrice")]
        public decimal UnitPrice { get; set; }

        [JsonProperty(PropertyName = "ProductUrl")]
        public string ProductUrl { get; set; }

        [JsonProperty(PropertyName = "DatasheetUrl")]
        public string DatasheetUrl { get; set; }

        [JsonProperty(PropertyName = "PhotoUrl")]
        public string PhotoUrl { get; set; }

        [JsonProperty(PropertyName = "ProductVariations")]
        public List<ProductVariations> ProductVariations { get; set; }

        [JsonProperty(PropertyName = "QuantityAvailable")]
        public string QuantityAvailable { get; set; }

        [JsonProperty(PropertyName = "ProductStatus")]
        public ProductStatus ProductStatus { get; set; }

        [JsonProperty(PropertyName = "BackOrderNotAllowed")]
        public bool BackOrderNotAllowed { get; set; }

        [JsonProperty(PropertyName = "NormallyStocking")]
        public Boolean NormallyStocking { get; set; }

        [JsonProperty(PropertyName = "Discontinued")]
        public Boolean Discontinued { get; set; }

        [JsonProperty(PropertyName = "EndOfLife")]
        public Boolean EndOfLife { get; set; }

        [JsonProperty(PropertyName = "Ncnr")]
        public Boolean Ncnr { get; set; }

        [JsonProperty(PropertyName = "PrimaryVideoUrl")]
        public string PrimaryVideoUrl { get; set; }

        [JsonProperty(PropertyName = "BaseProductNumber")]
        public string BaseProductNumber { get; set; }

        [JsonProperty(PropertyName = "Category")]
        public Category Category { get; set; }

        [JsonProperty(PropertyName = "ManufacturerLeadWeeks")]
        public string ManufacturerLeadWeeks { get; set; }

        [JsonProperty(PropertyName = "ManufacturerPublicQuantity")]
        public int ManufacturerPublicQuantity { get; set; }

        [JsonProperty(PropertyName = "Series")]
        public Series Series { get; set; }

        [JsonProperty(PropertyName = "Classifications")]
        public Series Classifications { get; set; }
    }

    public class DescriptionInfo
    {
        [JsonProperty(PropertyName = "ProductDescription")]
        public string ProductDescription { get; set; }

        [JsonProperty(PropertyName = "DetailedDescription")]
        public string DetailedDescription { get; set; }
    }

    public class ProductManufacturer
    {
        [JsonProperty(PropertyName = "Id")]
        public int Id { get; set; }

        [JsonProperty(PropertyName = "Name")]
        public string Name { get; set; }
    }

    public class ProductVariations
    {
        [JsonProperty(PropertyName = "DigiKeyProductNumber")]
        public string DigiKeyProductNumber { get; set; }

        [JsonProperty(PropertyName = "PackageType")]
        public PackageType PackageType { get; set; }

        [JsonProperty(PropertyName = "StandardPricing")]
        public List<StandardPricing> StandardPricing { get; set; }

        [JsonProperty(PropertyName = "QuantityAvailableforPackageType")]
        public int QuantityAvailableforPackageType { get; set; }

        [JsonProperty(PropertyName = "MaxQuantityForDistribution")]
        public int MaxQuantityForDistribution { get; set; }

        [JsonProperty(PropertyName = "MinimumOrderQuantity")]
        public int MinimumOrderQuantity { get; set; }

        [JsonProperty(PropertyName = "StandardPackage")]
        public int StandardPackage { get; set; }

        [JsonProperty(PropertyName = "DigiReelFee")]
        public int DigiReelFee { get; set; }
    }

    public class PackageType
    {
        [JsonProperty(PropertyName = "Id")]
        public int Id { get; set; }

        [JsonProperty(PropertyName = "Name")]
        public string Name { get; set; }
    }

    public class StandardPricing
    {
        [JsonProperty(PropertyName = "BreakQuantity")]
        public int BreakQuantity { get; set; }

        [JsonProperty(PropertyName = "UnitPrice")]
        public decimal UnitPrice { get; set; }

        [JsonProperty(PropertyName = "TotalPrice")]
        public decimal TotalPrice { get; set; }
    }

    public class ProductStatus
    {
        [JsonProperty(PropertyName = "Id")]
        public int Id { get; set; }

        [JsonProperty(PropertyName = "Status")]
        public string Status { get; set; }
    }

    public class BaseProductNumber
    {
        [JsonProperty(PropertyName = "Id")]
        public int Id { get; set; }

        [JsonProperty(PropertyName = "Name")]
        public string Name { get; set; }
    }

    public class Category
    {
        [JsonProperty(PropertyName = "CategoryId")]
        public int CategoryId { get; set; }

        [JsonProperty(PropertyName = "Name")]
        public string Name { get; set; }
    }

    public class Series
    {
        [JsonProperty(PropertyName = "Id")]
        public int Id { get; set; }

        [JsonProperty(PropertyName = "Name")]
        public string Name { get; set; }
    }

    public class Classifications
    {
        [JsonProperty(PropertyName = "ReachStatus")]
        public string ReachStatus { get; set; }

        [JsonProperty(PropertyName = "RohsStatus")]
        public string RohsStatus { get; set; }

        [JsonProperty(PropertyName = "MoistureSensitivityLevel")]
        public string MoistureSensitivityLevel { get; set; }

        [JsonProperty(PropertyName = "ExportControlClassNumber")]
        public string ExportControlClassNumber { get; set; }

        [JsonProperty(PropertyName = "HtsusCode")]
        public string HtsusCode { get; set; }
    }
}