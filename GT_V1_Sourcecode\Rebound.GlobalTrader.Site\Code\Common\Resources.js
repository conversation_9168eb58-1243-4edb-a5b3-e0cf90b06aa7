Type.registerNamespace("Rebound.GlobalTrader.Site");Rebound.GlobalTrader.Site.Resources=function(){this.DatabaseTimeout=Rebound.GlobalTrader.Site.Res.DatabaseTimeout;this.DatabaseError=Rebound.GlobalTrader.Site.Res.DatabaseError;this.Unfiltered=Rebound.GlobalTrader.Site.Res.Unfiltered;this.Yes=Rebound.GlobalTrader.Site.Res.Yes;this.No=Rebound.GlobalTrader.Site.Res.No;this.Error=Rebound.GlobalTrader.Site.Res.Error;this.Loading=Rebound.GlobalTrader.Site.Res.Loading;this.AddressTypeAlreadyEntered=Rebound.GlobalTrader.Site.Res.AddressTypeAlreadyEntered;this.ChangesSavedSuccessfully=Rebound.GlobalTrader.Site.Res.ChangesSavedSuccessfully;this.DataNotFound=Rebound.GlobalTrader.Site.Res.DataNotFound;this.DateTimeMustBeInFuture=Rebound.GlobalTrader.Site.Res.DateTimeMustBeInFuture;this.SourcingNuggetTitle=Rebound.GlobalTrader.Site.Res.SourcingNuggetTitle;this.LineXOfY=Rebound.GlobalTrader.Site.Res.LineXOfY;this.ItemXOfY=Rebound.GlobalTrader.Site.Res.ItemXOfY;this.Inbox=Rebound.GlobalTrader.Site.Res.Inbox;this.SentMessages=Rebound.GlobalTrader.Site.Res.SentMessages;this.MessageSent=Rebound.GlobalTrader.Site.Res.MessageSent;this.MessageDeleted=Rebound.GlobalTrader.Site.Res.MessageDeleted;this.FolderDeleted=Rebound.GlobalTrader.Site.Res.FolderDeleted;this.MessageMoved=Rebound.GlobalTrader.Site.Res.MessageMoved;this.FolderAdded=Rebound.GlobalTrader.Site.Res.FolderAdded;this.Today=Rebound.GlobalTrader.Site.Res.Today;this.None=Rebound.GlobalTrader.Site.Res.None;this.Inactive=Rebound.GlobalTrader.Site.Res.Inactive;this.ContactsForCompany=Rebound.GlobalTrader.Site.Res.ContactsForCompany;this.NewQuoteAdded=Rebound.GlobalTrader.Site.Res.NewQuoteAdded;this.NewCustomerRequirementAdded=Rebound.GlobalTrader.Site.Res.NewCustomerRequirementAdded;this.NewCustomerRMAAdded=Rebound.GlobalTrader.Site.Res.NewCustomerRMAAdded;this.NewSupplierRMAAdded=Rebound.GlobalTrader.Site.Res.NewSupplierRMAAdded;this.NewGoodsInAdded=Rebound.GlobalTrader.Site.Res.NewGoodsInAdded;this.NewSalesOrderAdded=Rebound.GlobalTrader.Site.Res.NewSalesOrderAdded;this.NewPurchaseOrderAdded=Rebound.GlobalTrader.Site.Res.NewPurchaseOrderAdded;this.NewInvoiceAdded=Rebound.GlobalTrader.Site.Res.NewInvoiceAdded;this.NewCreditNoteAdded=Rebound.GlobalTrader.Site.Res.NewCreditNoteAdded;this.NewDebitNoteAdded=Rebound.GlobalTrader.Site.Res.NewDebitNoteAdded;this.ApplicationError=Rebound.GlobalTrader.Site.Res.ApplicationError;this.RequiredFieldMissingMessage=Rebound.GlobalTrader.Site.Res.RequiredFieldMissingMessage;this.NumericFieldError=Rebound.GlobalTrader.Site.Res.NumericFieldError;this.EmailInvalidMessage=Rebound.GlobalTrader.Site.Res.EmailInvalidMessage;this.URLInvalidMessage=Rebound.GlobalTrader.Site.Res.URLInvalidMessage;this.FeedbackSent=Rebound.GlobalTrader.Site.Res.FeedbackSent;this.ROHSCompliant=Rebound.GlobalTrader.Site.Res.ROHSCompliant;this.ROHSNonCompliant=Rebound.GlobalTrader.Site.Res.ROHSNonCompliant;this.ROHSExempt=Rebound.GlobalTrader.Site.Res.ROHSExempt;this.ROHSUnknown=Rebound.GlobalTrader.Site.Res.ROHSUnknown;this.ROHSNotApplicable=Rebound.GlobalTrader.Site.Res.ROHSNotApplicable;this.RetainOneAdmin=Rebound.GlobalTrader.Site.Res.RetainOneAdmin;this.EnterFieldGeneric=Rebound.GlobalTrader.Site.Res.EnterFieldGeneric;this.DuplicateCurrencyCode=Rebound.GlobalTrader.Site.Res.DuplicateCurrencyCode;this.OldPasswordIncorrect=Rebound.GlobalTrader.Site.Res.OldPasswordIncorrect;this.DuplicateLoginName=Rebound.GlobalTrader.Site.Res.DuplicateLoginName;this.EmailTo=Rebound.GlobalTrader.Site.Res.EmailTo;this.TooMuchStockAllocated=Rebound.GlobalTrader.Site.Res.TooMuchStockAllocated;this.FileUploadNotAllowedType=Rebound.GlobalTrader.Site.Res.FileUploadNotAllowedType;this.FileUploadFailed=Rebound.GlobalTrader.Site.Res.FileUploadFailed;this.AppTitle=Rebound.GlobalTrader.Site.Res.AppTitle;this.NumericFieldLessThanError=Rebound.GlobalTrader.Site.Res.NumericFieldLessThanError;this.NumericFieldGreaterThanError=Rebound.GlobalTrader.Site.Res.NumericFieldGreaterThanError;this.NumericFieldLessThanOrEqualToError=Rebound.GlobalTrader.Site.Res.NumericFieldLessThanOrEqualToError;this.NumericFieldGreaterThanOrEqualToError=Rebound.GlobalTrader.Site.Res.NumericFieldGreaterThanOrEqualToError;this.NotifySalesOrder=Rebound.GlobalTrader.Site.Res.NotifySalesOrder;this.Sending=Rebound.GlobalTrader.Site.Res.Sending;this.Saving=Rebound.GlobalTrader.Site.Res.Saving;this.ReceivedPurchaseOrder=Rebound.GlobalTrader.Site.Res.ReceivedPurchaseOrder;this.PopupLoggedOut=Rebound.GlobalTrader.Site.Res.PopupLoggedOut;this.NotifyGoodsIn=Rebound.GlobalTrader.Site.Res.NotifyGoodsIn;this.OnStop=Rebound.GlobalTrader.Site.Res.OnStop;this.Units_Kg=Rebound.GlobalTrader.Site.Res.Units_Kg;this.Units_Pounds=Rebound.GlobalTrader.Site.Res.Units_Pounds;this.ShipInCostChanged=Rebound.GlobalTrader.Site.Res.ShipInCostChanged;this.ClosePO=Rebound.GlobalTrader.Site.Res.ClosePO;this.QuantityOrderedAsEntered=Rebound.GlobalTrader.Site.Res.QuantityOrderedAsEntered;this.QuantityOrderedResetToOriginal=Rebound.GlobalTrader.Site.Res.QuantityOrderedResetToOriginal;this.FreightChargeWillBeChanged=Rebound.GlobalTrader.Site.Res.FreightChargeWillBeChanged;this.FreightChargeLeft=Rebound.GlobalTrader.Site.Res.FreightChargeLeft;this.FreightChargeAndShippingChanged=Rebound.GlobalTrader.Site.Res.FreightChargeAndShippingChanged;this.SOFreightChargeOption=Rebound.GlobalTrader.Site.Res.SOFreightChargeOption;this.ConfirmQuantityOrderedGreaterThanRequired=Rebound.GlobalTrader.Site.Res.ConfirmQuantityOrderedGreaterThanRequired;this.ShippingWaived=Rebound.GlobalTrader.Site.Res.ShippingWaived;this.CloseSO=Rebound.GlobalTrader.Site.Res.CloseSO;this.ClosePOLine=Rebound.GlobalTrader.Site.Res.ClosePOLine;this.CloseSOLine=Rebound.GlobalTrader.Site.Res.CloseSOLine;this.CloseCRMALine=Rebound.GlobalTrader.Site.Res.CloseCRMALine;this.CloseSOAndResetLines=Rebound.GlobalTrader.Site.Res.CloseSOAndResetLines;this.SearchCancelled=Rebound.GlobalTrader.Site.Res.SearchCancelled;this.QuotedValue=Rebound.GlobalTrader.Site.Res.QuotedValue;this.NotifyPurchaseOrder=Rebound.GlobalTrader.Site.Res.NotifyPurchaseOrder;this.OverShipmentMessage=Rebound.GlobalTrader.Site.Res.OverShipmentMessage;this.OverShipmentConfirmed=Rebound.GlobalTrader.Site.Res.OverShipmentConfirmed;this.OverShipmentCancelled=Rebound.GlobalTrader.Site.Res.OverShipmentCancelled;this.QuantitiesAllocatedAndInStock=Rebound.GlobalTrader.Site.Res.QuantitiesAllocatedAndInStock;this.QuantityAllocated=Rebound.GlobalTrader.Site.Res.QuantityAllocated;this.DateMustBeInFuture=Rebound.GlobalTrader.Site.Res.DateMustBeInFuture;this.ContactEmailMessage=Rebound.GlobalTrader.Site.Res.ContactEmailMessage;this.InvoiceProgressMessage=Rebound.GlobalTrader.Site.Res.InvoiceProgressMessage;this.CreditProgressMessage=Rebound.GlobalTrader.Site.Res.CreditProgressMessage;this.DebitProgressMessage=Rebound.GlobalTrader.Site.Res.DebitProgressMessage;this.FinanceContactNotFoundMessage=Rebound.GlobalTrader.Site.Res.FinanceContactNotFoundMessage;this.CreditFinanceContactNotFoundMessage=Rebound.GlobalTrader.Site.Res.CreditFinanceContactNotFoundMessage;this.DebitFinanceContactNotFoundMessage=Rebound.GlobalTrader.Site.Res.DebitFinanceContactNotFoundMessage;this.BankFeeMessage=Rebound.GlobalTrader.Site.Res.BankFeeMessage;this.CustomerNoMessage=Rebound.GlobalTrader.Site.Res.CustomerNoMessage;this.ROHS2=Rebound.GlobalTrader.Site.Res.ROHS2;this.AlternatePartMessage=Rebound.GlobalTrader.Site.Res.AlternatePartMessage;this.HistoryMessage=Rebound.GlobalTrader.Site.Res.HistoryMessage;this.NoDataFound=Rebound.GlobalTrader.Site.Res.NoDataFound;this.SourcingSixMonth=Rebound.GlobalTrader.Site.Res.SourcingSixMonth;this.SourcingTwelveMonth=Rebound.GlobalTrader.Site.Res.SourcingTwelveMonth;this.SourcingFinish=Rebound.GlobalTrader.Site.Res.SourcingFinish;this.CreditLimitMessage=Rebound.GlobalTrader.Site.Res.CreditLimitMessage;this.SECTION_NAME=Rebound.GlobalTrader.Site.Res.SECTION_NAME;this.MY=Rebound.GlobalTrader.Site.Res.MY;this.TEAM=Rebound.GlobalTrader.Site.Res.TEAM;this.DIVISION=Rebound.GlobalTrader.Site.Res.DIVISION;this.COMPANY=Rebound.GlobalTrader.Site.Res.COMPANY;this.AvailableCreditBalanceToShipSO=Rebound.GlobalTrader.Site.Res.AvailableCreditBalanceToShipSO;this.OverCreditLimitOfCompanyMessage=Rebound.GlobalTrader.Site.Res.OverCreditLimitOfCompanyMessage;this.LocalCurrencyMessage=Rebound.GlobalTrader.Site.Res.LocalCurrencyMessage;this.NewSupplierInvoiceAdded=Rebound.GlobalTrader.Site.Res.NewSupplierInvoiceAdded;this.NotifySupplierInvoice=Rebound.GlobalTrader.Site.Res.NotifySupplierInvoice;this.SecondRefMessage=Rebound.GlobalTrader.Site.Res.SecondRefMessage;this.NarrativeMessage=Rebound.GlobalTrader.Site.Res.NarrativeMessage;this.InvoiceAmountMessage=Rebound.GlobalTrader.Site.Res.InvoiceAmountMessage;this.TaxValueMessage=Rebound.GlobalTrader.Site.Res.TaxValueMessage;this.SupplierCodeMessage=Rebound.GlobalTrader.Site.Res.SupplierCodeMessage;this.DebitNoteRefMessage=Rebound.GlobalTrader.Site.Res.DebitNoteRefMessage;this.Document=Rebound.GlobalTrader.Site.Res.Document;this.NotifyNPR=Rebound.GlobalTrader.Site.Res.NotifyNPR;this.SalesPersonCompare=Rebound.GlobalTrader.Site.Res.SalesPersonCompare;this.Salesman2Message=Rebound.GlobalTrader.Site.Res.Salesman2Message;this.Salesman2PercentMessage=Rebound.GlobalTrader.Site.Res.Salesman2PercentMessage;this.ShippedQuantityMessage=Rebound.GlobalTrader.Site.Res.ShippedQuantityMessage;this.NonPreferred=Rebound.GlobalTrader.Site.Res.NonPreferred;this.Traceable=Rebound.GlobalTrader.Site.Res.Traceable;this.Trusted=Rebound.GlobalTrader.Site.Res.Trusted;this.ProductSourceMessage=Rebound.GlobalTrader.Site.Res.ProductSourceMessage;this.AS9120LineMessage=Rebound.GlobalTrader.Site.Res.AS9120LineMessage;this.AS9120AllLineMessage=Rebound.GlobalTrader.Site.Res.AS9120AllLineMessage;this.EARIReported=Rebound.GlobalTrader.Site.Res.EARIReported;this.ROHS56=Rebound.GlobalTrader.Site.Res.ROHS56;this.ROHS66=Rebound.GlobalTrader.Site.Res.ROHS66;this.CustomerMessage=Rebound.GlobalTrader.Site.Res.CustomerMessage;this.LabelPrintMessage=Rebound.GlobalTrader.Site.Res.LabelPrintMessage;this.QuoteProduct=Rebound.GlobalTrader.Site.Res.QuoteProduct;this.QuoteAllProduct=Rebound.GlobalTrader.Site.Res.QuoteAllProduct;this.QualityApproval=Rebound.GlobalTrader.Site.Res.QualityApproval;this.CompanyApproveSubject=Rebound.GlobalTrader.Site.Res.CompanyApproveSubject;this.landedCostValidation1=Rebound.GlobalTrader.Site.Res.landedCostValidation1;this.landedCostValidation2=Rebound.GlobalTrader.Site.Res.landedCostValidation2;this.landedCostValidation3=Rebound.GlobalTrader.Site.Res.landedCostValidation3;this.landedCostValidation4=Rebound.GlobalTrader.Site.Res.landedCostValidation4;this.LotStockProvisionMessage=Rebound.GlobalTrader.Site.Res.LotStockProvisionMessage;this.ResReason1Value=Rebound.GlobalTrader.Site.Res.ResReason1Value;this.NotifyEPR=Rebound.GlobalTrader.Site.Res.NotifyEPR;this.BankPOFeeMessage=Rebound.GlobalTrader.Site.Res.BankPOFeeMessage;this.RequestToPurchaseHubSavedSuccessfully=Rebound.GlobalTrader.Site.Res.RequestToPurchaseHubSavedSuccessfully;this.SalesOrder=Rebound.GlobalTrader.Site.Res.SalesOrder;this.Part=Rebound.GlobalTrader.Site.Res.Part;this.Quantity=Rebound.GlobalTrader.Site.Res.Quantity;this.CustomerPart=Rebound.GlobalTrader.Site.Res.CustomerPart;this.DateCode=Rebound.GlobalTrader.Site.Res.DateCode;this.Manufacturer=Rebound.GlobalTrader.Site.Res.Manufacturer;this.DatePicked=Rebound.GlobalTrader.Site.Res.DatePicked;this.InspectedBy=Rebound.GlobalTrader.Site.Res.InspectedBy;this.MSLLevel=Rebound.GlobalTrader.Site.Res.MSLLevel;this.ROHS=Rebound.GlobalTrader.Site.Res.ROHS;this.Location=Rebound.GlobalTrader.Site.Res.Location;this.Date=Rebound.GlobalTrader.Site.Res.Date;this.GoodsInNo=Rebound.GlobalTrader.Site.Res.GoodsInNo;this.GIN=Rebound.GlobalTrader.Site.Res.GIN;this.NPRNo=Rebound.GlobalTrader.Site.Res.NPRNo;this.IPOAllocateMessage=Rebound.GlobalTrader.Site.Res.IPOAllocateMessage;this.IPOCreationMsg=Rebound.GlobalTrader.Site.Res.IPOCreationMsg;this.ConsolidateSOConfirmMSG=Rebound.GlobalTrader.Site.Res.ConsolidateSOConfirmMSG;this.ConsolidateMailSOConfirmMSG=Rebound.GlobalTrader.Site.Res.ConsolidateMailSOConfirmMSG;this.ShowInspect=Rebound.GlobalTrader.Site.Res.ShowInspect;this.SerialNoLimit=Rebound.GlobalTrader.Site.Res.SerialNoLimit;this.Important=Rebound.GlobalTrader.Site.Res.Important;this.SupplierWarranty=Rebound.GlobalTrader.Site.Res.SupplierWarranty;this.QuoteAllMSL=Rebound.GlobalTrader.Site.Res.QuoteAllMSL;this.HazardousMessage=Rebound.GlobalTrader.Site.Res.HazardousMessage;this.HoldReasonMessage=Rebound.GlobalTrader.Site.Res.HoldReasonMessage;this.AddNewCustomerRequirement=Rebound.GlobalTrader.Site.Res.AddNewCustomerRequirement;this.InsuredCurrencyAmount=Rebound.GlobalTrader.Site.Res.InsuredCurrencyAmount;this.OrderViaIPOonlyMessage=Rebound.GlobalTrader.Site.Res.OrderViaIPOonlyMessage;this.SUpplierApprovalMailMessage=Rebound.GlobalTrader.Site.Res.SUpplierApprovalMailMessage;this.SUpplierApprovalMailSubject=Rebound.GlobalTrader.Site.Res.SUpplierApprovalMailSubject;this.ShipFreightChargeAndShippingChanged=Rebound.GlobalTrader.Site.Res.ShipFreightChargeAndShippingChanged;this.ShipFreightChargeWillBeChanged=Rebound.GlobalTrader.Site.Res.ShipFreightChargeWillBeChanged;this.InvoiceAmountnotEqualToGoodsValueMessage=Rebound.GlobalTrader.Site.Res.InvoiceAmountnotEqualToGoodsValueMessage;this.InvoiceAmountnotEqualToGoodsValueMessageV1=Rebound.GlobalTrader.Site.Res.InvoiceAmountnotEqualToGoodsValueMessageV1;this.QuoteMinReminderDate=Rebound.GlobalTrader.Site.Res.QuoteMinReminderDate};Rebound.GlobalTrader.Site.Resources.registerClass("Rebound.GlobalTrader.Site.Resources");