Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.HomeNuggets");Rebound.GlobalTrader.Site.Controls.HomeNuggets.ApprovedCustomersOnStop=function(n){Rebound.GlobalTrader.Site.Controls.HomeNuggets.ApprovedCustomersOnStop.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.HomeNuggets.ApprovedCustomersOnStop.prototype={get_pnlItems:function(){return this._pnlItems},set_pnlItems:function(n){this._pnlItems!==n&&(this._pnlItems=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.HomeNuggets.ApprovedCustomersOnStop.callBaseMethod(this,"initialize");this.addRefreshEvent(Function.createDelegate(this,this.getData))},dispose:function(){this.isDisposed||(this._pnlItems=null,Rebound.GlobalTrader.Site.Controls.HomeNuggets.ApprovedCustomersOnStop.callBaseMethod(this,"dispose"))},setupLoadingState:function(){$R_FN.showElement(this._pnlItems,!1);Rebound.GlobalTrader.Site.Controls.HomeNuggets.ApprovedCustomersOnStop.callBaseMethod(this,"setupLoadingState")},getData:function(){this.setupLoadingState();$R_FN.setInnerHTML(this._pnlItems,"");var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/HomeNuggets/ApprovedCustomersOnStop");n.set_DataObject("ApprovedCustomersOnStop");n.set_DataAction("GetData");n.addParameter("rowcount",this._intRowCount);n.addDataOK(Function.createDelegate(this,this.getDataComplete));n.addError(Function.createDelegate(this,this.homeNuggetDataError));n.addTimeout(Function.createDelegate(this,this.homeNuggetDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getDataComplete:function(n){var t=n._result,i;for(this.showNoneFoundOrContent(t.Count),i=0;i<t.Items.length;i++)this.addHTMLContentItem($RGT_nubButton_Company(t.Items[i].CompanyID,t.Items[i].CompanyName),this._pnlItems);$R_FN.showElement(this._pnlItems,t.Count>0);this.hideLoading()}};Rebound.GlobalTrader.Site.Controls.HomeNuggets.ApprovedCustomersOnStop.registerClass("Rebound.GlobalTrader.Site.Controls.HomeNuggets.ApprovedCustomersOnStop",Rebound.GlobalTrader.Site.Controls.HomeNuggets.Base,Sys.IDisposable);