﻿using Rebound.GlobalTrader;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Rebound.GlobalTrader.DAL;



namespace Rebound.GlobalTrader.BLL
{
	public partial class TeamTarget : BizObject
	{

        #region Properties

        protected static TeamTargetElement Settings
        {
            get { return Globals.Settings.TeamTargets; }
        }
		public System.Int32 TeamId { get; set; }
        public string TeamName { get; set; }
		public string PersonType { get; set; }
		public System.Double? JanTarget { get; set; }
		public System.Double? AllocatedPer { get; set; }
		public System.Double? TotalTarget { get; set; }
		public System.Double? FebTarget { get; set; }
		public System.Double? MarchTarget { get; set; }
		public System.Double? AprTarget { get; set; }
		public System.Double? MayTarget { get; set; }
		public System.Double? JuneTarget { get; set; }
		public System.Double? JulyTarget { get; set; }
		public System.Double? AugTarget { get; set; }
		public System.Double? SepTarget { get; set; }
		public System.Double? OctTarget { get; set; }
		public System.Double? NovTarget { get; set; }
		public System.Double? DecTarget { get; set; }
		public System.Int32 RowId { get; set; }

		/// <summary>
		/// To mark if sales person moved or not
		/// </summary>
		public System.Int32 IsSPMoved { get; set; }

		#endregion

		#region Methods

		/// <summary>
		/// KPI_GetTeamSalesTarget
		/// </summary>
		/// <param name="teamNo"></param>
		/// <param name="managerNo"></param>
		/// <param name="yearNo"></param>
		/// <param name="table"></param>
		/// <returns></returns>
		public static List<TeamTarget> GetTeamAndSalesTarget(System.Int32? teamNo,System.Int32? managerNo,System.Int32 yearNo,System.String table)
		{
			List<TeamTargetDetails> lstDetails = SiteProvider.TeamTarget.GetTeamAndSalesTarget(teamNo, managerNo, yearNo, table);
			if (lstDetails == null)
			{
				return new List<TeamTarget>();
			}
			else
			{
				List<TeamTarget> lst = new List<TeamTarget>();
				foreach (TeamTargetDetails objDetails in lstDetails)
				{
					BLL.TeamTarget obj = new BLL.TeamTarget();
					obj.TeamId = objDetails.TeamId;
					obj.TeamName = objDetails.TeamName;
					obj.PersonType = objDetails.PersonType;
					obj.JanTarget = objDetails.JanTarget;
					obj.FebTarget = objDetails.FebTarget;
					obj.MarchTarget = objDetails.MarchTarget;
					obj.AprTarget = objDetails.AprTarget;
					obj.MayTarget = objDetails.MayTarget;
					obj.JuneTarget = objDetails.JuneTarget;
					obj.JulyTarget = objDetails.JulyTarget;
					obj.AugTarget = objDetails.AugTarget;
					obj.SepTarget = objDetails.SepTarget;
					obj.OctTarget = objDetails.OctTarget;
					obj.NovTarget = objDetails.NovTarget;
					obj.DecTarget = objDetails.DecTarget;
					obj.TotalTarget = objDetails.TotalTarget;
					obj.AllocatedPer = objDetails.AllocatedPer;
					obj.RowId = objDetails.RowId;
					obj.IsSPMoved = objDetails.IsSPMoved;
					lst.Add(obj);
					obj = null;
				}
				lstDetails = null;
				return lst;
			}
		}

		/// <summary>
		/// Update
		/// Calls [usp_update_TeamSalesTarget]
		/// </summary>
		public static bool Update(System.Int32? rowId, System.String rowType, System.String columnName, System.Double? targetValue,System.Int32? updatedBy,System.Int32? Year, System.Int32? teamNo)
		{
			return SiteProvider.TeamTarget.Update(rowId, rowType, columnName, targetValue, updatedBy, Year, teamNo);
		}

		/// <summary>
		/// Update
		/// Calls [usp_saveAllTeamSalesTarget]
		/// </summary>
		public static bool SaveAllTeamSalesData(System.Int32? Year, System.Int32? divisionNo, System.Int32? updatedBy)
		{
			return SiteProvider.TeamTarget.SaveAllTeamSalesData(Year, divisionNo, updatedBy);
		}

		#endregion

	}
}