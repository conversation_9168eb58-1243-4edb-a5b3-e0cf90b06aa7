///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.CompanyApiCustomer_Add = function (element) {
    Rebound.GlobalTrader.Site.Controls.Forms.CompanyApiCustomer_Add.initializeBase(this, [element]);
    this._intCompanyID = 0;
    this.txtemailcheck = false;
    this.txtphncheck = false;
    this.txtpswrd1check = false;
    this.txtpswrd2check = false;
    this.confirmpswrd = false;
    this.txtContactPerson = false;
    this.BOMCheck = false;
    this.checkflag = false;
    this.name = false;

};

Rebound.GlobalTrader.Site.Controls.Forms.CompanyApiCustomer_Add.prototype = {

    get_intCompanyID: function () { return this._intCompanyID; }, set_intCompanyID: function (v) { if (this._intCompanyID !== v) this._intCompanyID = v; },
    initialize: function () {
        Rebound.GlobalTrader.Site.Controls.Forms.CompanyApiCustomer_Add.callBaseMethod(this, "initialize");
        this.addShown(Function.createDelegate(this, this.formShown));
    },

    dispose: function () {
        if (this.isDisposed) return;
        this._intCompanyID = null;
        Rebound.GlobalTrader.Site.Controls.Forms.CompanyApiCustomer_Add.callBaseMethod(this, "dispose");
    },

    formShown: function () {
        if (this._blnFirstTimeShown) {
            //form events
            this.addSave(Function.createDelegate(this, this.saveClicked));
            //$find(this.getField("ctlCategory").ControlID).addChanged(Function.createDelegate(this, this.getCertificateByCategory));

            //data
            this._strPathToData = "controls/Nuggets/CompanyApiCustomer";
            this._strDataObject = "CompanyApiCustomer";
        }

        //this.getFieldDropDownData("ctlCategory");
        // this.getCertificateByCategory();
        this.setFormFieldsToDefaults();
    },


    //getCertificateByCategory: function() {
    //    this.showCertificateFieldsLoading(true);
    //    this.getFieldComponent("ctlCertificate")._intCategoryID = this.getFieldValue("ctlCategory");
    //    this.getFieldDropDownData("ctlCertificate");
    //    this.showCertificateFieldsLoading(false);
    //},
    //showCertificateFieldsLoading: function(bln) {
    //    this.showFieldLoading("ctlCertificate", bln);
    //},
    saveClicked: function () {
        debugger;
        //var test = this.validatedetails();
        var chk1 = this.getFieldValue("ctlBomUser");
        var chk2 = this.getFieldValue("ctlSupplierUser");
        var test = this.validatedetails();

        if (chk1 || chk2) {
            this.BOMCheck = true;
        } else {
            this.BOMCheck = false;
            alert("Please select atleast one from BOM and Supplier");
            this.showError(true);
        }


        if (this.txtemailcheck == true && this.txtphncheck == true && this.txtpswrd1check == true && this.BOMCheck == true && this.checkflag == true && this.name == true) {


            //if (test == true ) {
            //if (this.validateForm())
            this.addNew();
            //this.resetFormFields();
        } else {
            this.showError(true);
            /* this.alertmassage();*/
            //this.resetFormFields();
        }
        //if (this.validateForm()) this.addNew();
        /*this.resetFormFields();*/
    },
    alertmassage: function () {
        alert('please fill the correct value');
    },

    resetFormFields: function () {
        $("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlPassword").css("background-color", "#56954E");
        $("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlConfirmPassword").css("background-color", "#56954E");
        $("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlEmail").css("background-color", "#56954E");
        $("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlMobile").css("background-color", "#56954E");
        $("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlContactPerson").css("background-color", "#56954E");

    },

    validatedetails: function () {
        debugger;
        // var checkflag = false;
        var pswrd1 = $("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlPassword_ctl03_txtPassword").val();
        var pswrd2 = $("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlConfirmPassword_ctl03_txtConfirmPassword").val();
        var txtemail = $("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlEmail_ctl04_txtEmail").val();
        var txtphn = $("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlMobile_ctl03_txtMobile").val();
        var txtname = $("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlMobile_ctl03_txtMobile").val();
        var contactPerson = $("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlContactPerson_ctl03_txtContactPerson").val();

        if (txtemail.length == 0) {
            debugger;
            this.checkflag = false;
            this.txtemailcheck = false;
            $("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlEmail").css("background-color", "#990000");
            alert("Please enter email address!");
        } else {
            if (this.validateEmail(txtemail) == true) {
                this.checkflag = true;
                this.txtemailcheck = true;
                $("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlEmail").css("background-color", "#56954E");
            }
            else {
                this.checkflag = false;
                this.txtemailcheck = false;
                $("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlEmail").css("background-color", "#990000");
            }
        }


        if (this.validateName(contactPerson) == true) {
            this.checkflag = true;
            this.name = true;
            this.txtContactPerson = true;
            // $("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlContactPerson").css("background-color", "#56954E");
        } else {
            this.checkflag = false;
            this.name = false;
            this.txtContactPerson = false;
            // $("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlContactPerson").css("background-color", "#990000");
        }

        //if (txtphn != "" && txtphn != " " && txtphn != undefined)
        if (this.validate10digitnumber(txtphn) == true) {
            this.checkflag = true;
            this.txtphncheck = true;
            $("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlMobile").css("background-color", "#56954E");
        }
        else {
            this.checkflag = false;
            this.txtphncheck = false;
            $("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlMobile").css("background-color", "#990000");
        }
        if (contactPerson != "" && contactPerson != undefined && contactPerson.length > 2) {
            this.checkflag = true;
            this.txtContactPerson = true;
            //$("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlContactPerson").css("background-color", "#56954E");
        }
        else {
            this.checkflag = false;
            this.txtContactPerson = false;
            $("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlContactPerson").css("background-color", "#990000");
        }
        if (pswrd1 != "" && pswrd1 != " " && pswrd1 != undefined && pswrd1.length >= 16) {
            this.checkflag = true;
            this.txtpswrd1check = true;
            $("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlPassword").css("background-color", "#56954E");
        }
        else {
            this.checkflag = false;
            this.txtpswrd2check = true;
            $("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlPassword").css("background-color", "#990000");
        }
        //if (pswrd2 != "" && pswrd2 != " " && pswrd2 != undefined && pswrd1.length >= 16) {
        //    checkflag = true;
        //    this.txtpswrd2check = true;
        //    $("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlPassword").css("background-color", "#56954E");
        //}
        //else {
        //    checkflag = false;
        //    this.txtpswrd2check = false;
        //    $("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlConfirmPassword").css("background-color", "#990000");
        //}
        if (pswrd1 != "" && pswrd1 != " " && pswrd1 != undefined && pswrd1.length >= 16) {
            this.checkflag = true;
            this.confirmpswrd = true;
            $("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlPassword").css("background-color", "#56954E");
            $("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlConfirmPassword").css("background-color", "#56954E");
        }
        else {
            this.checkflag = false;
            this.confirmpswrd = false;
            $("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlPassword").css("background-color", "#990000");
            $("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlConfirmPassword").css("background-color", "#990000");
        }
        //if (txtemail != "" && txtemail != " " && txtemail != undefined)

        //return checkflag;

        /*var phoneno = /[0-9]/g;*/
        /* var phoneno = /^[\+]?[(]?[0-9]{3}[)]?[-\s\.]?[0-9]{3}[-\s\.]?[0-9]{4,6}$/im;*/
        //var temp = 0;
        //var password1 = $("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlPassword_ctl03_txtPassword").val();
        //var password2 = $("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlConfirmPassword_ctl03_txtConfirmPassword").val();
        //var textbox = $("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlEmail_ctl04_txtEmail").val();
        //var textbox2 = $("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlMobile_ctl03_txtMobile").val();
        //if (password1 === '') {

        //    $("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlConfirmPassword").css("background-color", "#990000");
        //    $("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlPassword").css("background-color", "#990000");
        //}
        //var test2 = this.validate10digitnumber(textbox2);
        //var email = this.validateEmail(textbox);
        //if (password1 == password2) {
        //    temp++;
        //}
        //else {
        //    $("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlConfirmPassword_pnlFieldControls").css("background-color", "#990000");
        //    temp--;
        //}
        ///* !isNaN(textbox2) && Number.isInteger(textbox2) && textbox2.trim() !== '' && textbox2 === textbox2.toString() && !textbox2.includes('.')*/
        //if (test2 == true && textbox2 !== '') {
        //    //$('#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlMobile_ctl03_txtMobile').next().show();
        //    //return false;
        //    temp++
        //}
        //else {
        //    temp--;
        //}
        //if (email == true) {
        //    //$('#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlMobile_ctl03_txtMobile').next().show();
        //    //return false;
        //    temp++
        //}
        //else {
        //    temp--;
        //}
        ////if (textbox = '@') {
        ////    //$('#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlEmail_ctl04_txtEmail').next().show();
        ////    //return false;
        ////    temp++
        ////}
        //return temp;

    },
    validateEmail: function (string) {
        debugger;
        // if ((/^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)*$/).test(string)) {
        if ((/^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/).test(string)) {
            this.checkflag = true;
            $("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlEmail").css("background-color", "#56954E");
            return true;
        } else {
            this.checkflag = false;
            $("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlEmail").css("background-color", "#990000");
            alert("You have entered an invalid email address!");
            return false;
        }
    },
    //validate10digitnumber: function (string) {
    //    if (!/^[\+]?[(]?[0-9]{3}[)]?[-\s\.]?[0-9]{3}[-\s\.]?[0-9]{4,6}$/im.test(string)) {
    //        $("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlMobile").css("background-color", "#990000");
    //        return false;
    //    }
    //    $("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlMobile").css("background-color", "#56954E");
    //    return true;
    //},

    validateName: function (string) {
        debugger;
        if (!(/^[a-zA-Z\s]+$/.test(string))) {
            $("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlContactPerson").css("background-color", "#990000 !important");
            this.checkflag = false;
            this.name = false;
            alert("Only Alphabets are allowed");
            return false;
        } else {
            this.checkflag = true;
            this.name = true;
            $("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlContactPerson").css("background-color", "#56954E !important");
            return true;
        }
    },

    //validate10digitnumber: function (string_or_number) {
    //    var mobile = string_or_number;
    //    if (mobile.length != 10) {
    //        return false;
    //    }
    //    intRegex = /[0-9 -()+]+$/;
    //    $("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlMobile").css("background-color", "#56954E");
    //    is_mobile = true;
    //    for (var i = 0; i < 10; i++) {
    //        if (intRegex.test(mobile[i])) {
    //            continue;
    //        }
    //        else {
    //            $("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlMobile").css("background-color", "#990000");
    //            is_mobile = false;
    //            break;
    //        }
    //    }
    //    return is_mobile;

    //},
    validate10digitnumber: function (string_or_number) {
        var mobile = string_or_number;
        if (mobile.length != 10) {
            return false;
        }
        // intRegex = /[0-9 -()+]+$/;
        intRegex = /^((?!(0))[0-9]{10})$/;

        // is_mobile = true;

        if (!(intRegex.test(mobile))) {
            is_mobile = false;
            this.checkflag = false;
            $("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlMobile").css("background-color", "#990000");
            alert("Invalid mobile no");
        } else {
            is_mobile = true;
            this.checkflag = true;
            $("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlMobile").css("background-color", "#56954E");
        }


        return is_mobile;

    },


    validateForm: function () {
        var blnOK = true;
        blnOK = this.autoValidateFields();
        if (!blnOK) this.showError(true);
        return blnOK;
    },

    addNew: function () {
        debugger;
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData(this._strPathToData);
        obj.set_DataObject(this._strDataObject);
        obj.set_DataAction("AddNew");
        obj.addParameter("id", this._intCompanyID);
        obj.addParameter("Email", this.getFieldValue("ctlEmail"));
        obj.addParameter("ContactName", this.getFieldValue("ctlContactPerson"));
        var countryCodeVal = $("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlMobile_ctl03_txtCountryCode").val();
        obj.addParameter("CountryCode", countryCodeVal);
        obj.addParameter("Mobile", this.getFieldValue("ctlMobile"));
        obj.addParameter("InActive", this.getFieldValue("ctlInActive"));
        obj.addParameter("IsBomUser", this.getFieldValue("ctlBomUser"));
        obj.addParameter("InSupUser", this.getFieldValue("ctlSupplierUser"));
        obj.addParameter("Password", this.getFieldValue("ctlPassword"));
        obj.addDataOK(Function.createDelegate(this, this.saveEditOK));
        obj.addError(Function.createDelegate(this, this.saveEditError));
        obj.addTimeout(Function.createDelegate(this, this.saveEditError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },



    saveEditError: function (args) {
        debugger;
        alert("Person Exist");
        //this._strErrorMessage = args._errorMessage;
        //this.onSaveError();
        this.onSaveComplete();
    },

    saveEditOK: function (args) {
        if (args._result.Result == true) {
            this._intLineID = args._result.NewID;
            this.onSaveComplete();
        } else {
            this.saveEditError(args);
        }
    }

};

$(document).ready(function () {


    $("#checkbox").click(function () {
        debugger;
        var dp = $('#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlConfirmPassword_ctl03_txtConfirmPassword').attr('type');
        if (dp == 'password') {
            $('#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlConfirmPassword_ctl03_txtConfirmPassword').attr('type', 'text');
        } else {
            $('#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlConfirmPassword_ctl03_txtConfirmPassword').attr('type', 'password');

        }
    });

    $("#btngenpass").click(function () {
        var pq = generateP();
        /* alert(pq);*/
        /* document.getElementById("ctlPassword").setAttribute('value', pq);*/
        $("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlPassword_ctl03_txtPassword").val(pq);
        /* $('#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlPassword_ctl03_txtPassword').attr('type', 'text');*/
        /* this.setFieldValue("#ctlPassword", generateP());*/
    });


    function generateP() {
        var pass = '';
        var str = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ' +
            'abcdefghijklmnopqrstuvwxyz123456789';

        for (let i = 1; i < 18; i++) {
            var char = Math.floor(Math.random()
                * str.length + 1);

            pass += str.charAt(char)
        }

        return pass;
    };
    //    $.validator.addMethod
    //    $("#frmCertificate").validate({
    //        rules: {
    //            //This section we need to place our custom rule for the control.  
    //            <%=txtEmail.UniqueID %>:(
    //        required: true
    //    )
    //        },
    //        messages: {
    //            //This section we need to place our custom validation message for each control.  
    //             <%=txtName.UniqueID %>: {
    //    required: "Name is required."
    //},
    //        },
    //    });
    //});

});


Rebound.GlobalTrader.Site.Controls.Forms.CompanyApiCustomer_Add.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CompanyApiCustomer_Add", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);