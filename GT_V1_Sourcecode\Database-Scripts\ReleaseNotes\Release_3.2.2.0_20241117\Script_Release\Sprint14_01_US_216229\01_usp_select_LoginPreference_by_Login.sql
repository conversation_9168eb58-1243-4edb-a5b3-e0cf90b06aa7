﻿
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/*===========================================================================================  
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-216229]     NgaiTo		 	 31-Oct-2024		UPDATE		216229: Login - Remove duplicated fields in Stored Procedure usp_select_LoginPreference_by_Login
===========================================================================================  
*/

CREATE OR ALTER  PROCEDURE [dbo].[usp_select_LoginPreference_by_Login]         
@LoginNo int        
AS        
SELECT p.*         
, l.Code as DefaultSiteLanguageCode   
, pr.PrinterName          
FROM dbo.tbLoginPreference p        
JOIN dbo.tbSiteLanguage l ON p.DefaultSiteLanguageNo = l.SiteLanguageId        
Left Join tbPrinter pr ON p.PrinterNo = pr.PrinterId      
WHERE p.LoginNo    = @LoginNo 


GO


