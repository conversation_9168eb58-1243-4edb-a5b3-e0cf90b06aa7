﻿$(window).load(function () {

    var inputDate = $("#QuoteDate");

    var changeYearButtons = function () {
        setTimeout(function () {
            var widgetHeader = inputDate.datepicker("widget").find(".ui-datepicker-header");
            var widgetTitle = inputDate.datepicker("widget").find(".ui-datepicker-title");

            var widgetPrev = inputDate.datepicker("widget").find(".ui-datepicker-prev");
            var widgetNext = inputDate.datepicker("widget").find(".ui-datepicker-next");

            // document.getElementById(".ui-datepicker-div").style.width = 350 + "px";
            //you can opt to style up these simple buttons tho
            var prevYrBtn = $('<button class="PrevYr">&lt;&lt;</button>');
            prevYrBtn.bind("click", function () {
                $.datepicker._adjustDate(inputDate, -1, 'Y');
            });
            var nextYrBtn = $('<button class="NextYr">&gt;&gt;</button>');
            nextYrBtn.bind("click", function () {
                $.datepicker._adjustDate(inputDate, +1, 'Y');

            });
            prevYrBtn.prependTo(widgetHeader);
            nextYrBtn.appendTo(widgetHeader);
            widgetTitle.insertAfter(widgetPrev);
            //widgetTitle.remove();


            //CSS
            widgetPrev.css({ "left": "45px", "top": "2px" });
            widgetNext.css({ "right": "45px", "top": "2px" });
            $(".NextYr").css({ "position": "absolute", "top": "2px", "height": "1.8em", "right": "0px" });
            $(".PrevYr").css({ "position": "absolute", "top": "2px", "height": "1.8em", "left": "0px" });
            $(".ui-datepicker .ui-datepicker-month").css({ "position": "absolute", "top": "5px", "left": "90px" });
            $(".ui-datepicker .ui-datepicker-year").css({ "position": "absolute", "top": "5px", "left": "200px" });
            $(".ui-datepicker").css({ "width": "20em" });
            $("td.ui-datepicker-week-col").css({ "background": "#e6e6e6" });
            $("td.ui-datepicker-week-col").css({ "position": "absolute" });
            $(".ui-state-default").css({ "background": "#ffffff", "border": "0px " });

        }, 1);
    };

    var dates = $("#QuoteDate").datepicker({
        closeText: "None",
        showWeek: true,
        weekHeader: "Week",
        //weekHeader: "Nr",
        dayNamesMin: ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Week"],
        monthNames: ["Jan", "Feb", "Mar", "Apr", "May", "Jun",
            "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"],
        // beforeShowDay: $.datepicker.noWeekends,
        //firstDay: 1,
        showOtherMonths: true,
        beforeShow: changeYearButtons,
        showOn: "both",
        buttonImage: "/App_Themes/Original/images/calendar/cal.gif",
        buttonImageOnly: true,
        buttonText: "Select date",
        dateFormat: 'dd/mm/yy',
        altField: "#alternate",
        //altFormat: "dd/mm/yy",
        onChangeMonthYear: changeYearButtons,
        changeMonth: true,
        changeYear: true,
        showButtonPanel: true,
        onSelect: function (dateText, inst) {
            //alert("1234");
            //debugger;
            //var alternateDate = $("#alternate").val();
            //var weekNumber = $.datepicker.iso8601Week(new Date(dateText));
            //$(this).val("Week " + weekNumber + " - " + alternateDate);
            inputDate.val(dateText);
        }
    }).focus(function () {
        $('.ui-datepicker-close').click(function () {
            $('#QuoteDate').val('');
        });

        $.datepicker._gotoToday = function (id) {
            $(id).datepicker('setDate', new Date().toLocaleDateString()).datepicker('hide').blur().change();
        };

    });


});
