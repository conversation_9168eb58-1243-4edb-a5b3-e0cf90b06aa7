CREATE OR ALTER PROCEDURE [dbo].[usp_source_CustomerRequirement]                    
--********************************************************************************************                      
--* RP 09.03.2011:                      
--* - add recompile option                      
--*                      
--* RP 25.05.2010:                      
--* - remove UNIONS, process Clients in code                      
--* - add Package, Product, CustomerPart, DateCode                      
--*                      
--* SK 20.01.2010:                      
--* - add ClientId to parameters and predicate: if equal display data as now, if not show                        
--*   C<PERSON><PERSON><PERSON> as customer  - with no hyperlink - and do not show any price                        
--*                      
--* RP 01.06.2009:                      
--* - add search on CustomerPart                      
--*                      
--* SK 01.06.2009:                      
--* - add order by clause                      
--********************************************************************************************                          
    @ClientId INT                      
  , @PartSearch NVARCHAR(50) = '%%'                      
  , @Index int=1                
  , @StartDate datetime = NULL                      
  , @FinishDate datetime = NULL                               
--WITH RECOMPILE  
AS                      
 --DECLARE VARIABLE                  
     DECLARE @Month int                    
     DECLARE @FROMDATE DATETIME                    
     DECLARE @ENDDATE DATETIME                    
     DECLARE @OutPutDate DATETIME                  
       SET @Month=6                    
     /*                  
        When we get index 1 then we find the maximum date from matching record                  
        and decsrease no of month for the start date.                  
     */                  
     IF @Index=1                    
  BEGIN   
                     
  SELECT @FinishDate=MAX(tbCustomerRequirement.ReceivedDate) FROM  tbCustomerRequirement WITH (NoLock)  
  JOIN tbClient ON tbCustomerRequirement.ClientNo = tbClient.ClientId                   
  WHERE     
  --((tbCustomerRequirement.ClientNo = @ClientId) OR (tbCustomerRequirement.ClientNo <> @ClientId AND tbClient.OwnDataVisibleToOthers = 1))  
  ((tbCustomerRequirement.ClientNo = @ClientId) OR (tbCustomerRequirement.ClientNo <> @ClientId   
  --AND tbClient.OwnDataVisibleToOthers = 1  
  ))     
  and (tbCustomerRequirement.FullPart LIKE @PartSearch                      
  OR tbCustomerRequirement.FullCustomerPart LIKE @PartSearch)               
                       
  SET @FROMDATE=dbo.ufn_get_date_from_datetime(DATEADD(month,-@Month,@FinishDate))                    
  SET @ENDDATE =dbo.ufn_get_date_from_datetime(@FinishDate)                    
     END                    
    ELSE                   
     BEGIN                    
       SET @FROMDATE=dbo.ufn_get_date_from_datetime(@StartDate)                    
       SET @ENDDATE =dbo.ufn_get_date_from_datetime(@FinishDate)                    
     END                
                  
      --SET THE OUTPUT DATE                  
      SET @OutPutDate=DATEADD(month,-@Month,@FinishDate)                 
                       
                
    SELECT  cr.ClientNo                      
          , cl.ClientName                      
          , cr.CustomerRequirementId                      
          , cr.CustomerRequirementNumber                      
          , cr.Part                      
          , cr.Quantity                      
          , cr.ROHS                      
          , cr.CompanyNo                      
          , co.CompanyName                      
          , cr.ReceivedDate                      
          , cr.Price                      
          , cr.CurrencyNo                      
          , cu.CurrencyCode                      
          , cr.ManufacturerNo                      
          , mf.ManufacturerCode                      
       , pr.ProductName                      
          , pk.PackageName                      
          , cr.CustomerPart                      
          , cr.DateCode          
          , cr.Salesman          
          , lo.EmployeeName     
          , VM.BOMID as  BOMID    
          , VM.BOMName as BOMName       
    ,cl.ClientCode         
    FROM    dbo.tbCustomerRequirement cr WITH (NoLock)  
    JOIN    dbo.tbCompany co WITH (NoLock) ON cr.CompanyNo = co.CompanyId                      
    JOIN    dbo.tbClient cl WITH (NoLock) ON cr.ClientNo = cl.ClientId                      
    LEFT JOIN dbo.tbManufacturer mf WITH (NoLock) ON cr.ManufacturerNo = mf.ManufacturerId                      
    LEFT JOIN dbo.tbCurrency cu WITH (NoLock) ON cu.CurrencyId = cr.CurrencyNo                      
    LEFT JOIN dbo.tbProduct pr WITH (NoLock) ON pr.ProductId = cr.ProductNo                      
    LEFT JOIN dbo.tbPackage pk WITH (NoLock) ON pk.PackageId = cr.PackageNo          
   LEFT JOIN dbo.tbLogin lo WITH (NoLock) ON lo.LoginId = cr.Salesman    
   LEFT JOIN VWBOM VM WITH (NoLock) ON VM.BOMID =  cr.BOMNO           
    WHERE  --((cr.ClientNo = @ClientId) OR (cr.ClientNo <> @ClientId AND cl.OwnDataVisibleToOthers = 1))    
        ((cr.ClientNo = @ClientId) OR (cr.ClientNo <> @ClientId   
     --AND cl.OwnDataVisibleToOthers = 1  
     ))           
            AND (cr.FullPart LIKE @PartSearch                      
                 OR cr.FullCustomerPart LIKE @PartSearch)                      
            AND (dbo.ufn_get_date_from_datetime(cr.ReceivedDate) between  @FROMDATE AND  @ENDDATE)                        
    ORDER BY cr.ReceivedDate DESC                      
          , cr.CustomerRequirementNumber ASC          
                                
      --SELECT THE OUT DATE                   
    SELECT @OutPutDate AS OutPutDate       