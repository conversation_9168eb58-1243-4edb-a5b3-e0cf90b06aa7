using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
//using Microsoft.ApplicationInsights;

namespace Rebound.GlobalTrader.Site.Controls.DropDowns.Data
{
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class BomManagerStatus : Rebound.GlobalTrader.Site.Data.DropDowns.Base
    {

        public override void ProcessRequest(HttpContext context)
        {
            SetDropDownType("BomManagerStatus");
            base.ProcessRequest(context);
        }

        protected override void GetData()
        {
            try
            {

                JsonObject jsn = new JsonObject();
                JsonObject jsnList = new JsonObject(true);
                JsonObject jsnItem = null;

                jsnItem = new JsonObject();
                jsnItem.AddVariable("ID", (int) Rebound.GlobalTrader.BLL.BomManagerStatus.List.New );
                jsnItem.AddVariable("Name", Functions.GetGlobalResource("misc", "New"));
                jsnList.AddVariable(jsnItem);

                jsnItem = new JsonObject();
                jsnItem.AddVariable("ID", (int)Rebound.GlobalTrader.BLL.BomManagerStatus.List.SendToHub);
                jsnItem.AddVariable("Name", Functions.GetGlobalResource("misc", "SendToPurchaseHub"));
                jsnList.AddVariable(jsnItem);

                jsnItem = new JsonObject();
                jsnItem.AddVariable("ID", (int)Rebound.GlobalTrader.BLL.BomManagerStatus.List.OffersAdded);
                jsnItem.AddVariable("Name", Functions.GetGlobalResource("misc", "OffersAdded"));
                jsnList.AddVariable(jsnItem);

                jsnItem = new JsonObject();
                jsnItem.AddVariable("ID", (int)Rebound.GlobalTrader.BLL.BomManagerStatus.List.PartialReleased);
                jsnItem.AddVariable("Name", Functions.GetGlobalResource("misc", "PartialReleased"));
                jsnList.AddVariable(jsnItem);

                jsnItem = new JsonObject();
                jsnItem.AddVariable("ID", (int)Rebound.GlobalTrader.BLL.BomManagerStatus.List.Released);
                jsnItem.AddVariable("Name", Functions.GetGlobalResource("misc", "Released"));
                jsnList.AddVariable(jsnItem);

                jsnItem = new JsonObject();
                jsnItem.AddVariable("ID", (int)Rebound.GlobalTrader.BLL.BomManagerStatus.List.CustomerQuoted);
                jsnItem.AddVariable("Name", Functions.GetGlobalResource("misc", "CustomerQuoted"));
                jsnList.AddVariable(jsnItem);

                //Add new code for Closed Status by Prakash on 07-06-2016
                jsnItem = new JsonObject();
                jsnItem.AddVariable("ID", (int)Rebound.GlobalTrader.BLL.BomManagerStatus.List.Closed);
                jsnItem.AddVariable("Name", Functions.GetGlobalResource("misc", "Closed"));
                jsnList.AddVariable(jsnItem);

                jsnItem.Dispose(); jsnItem = null;

                jsn.AddVariable("BomManagerStatus", jsnList);
                jsnList.Dispose(); jsnList = null;
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch (Exception ex)
            {
                //var ai = new TelemetryClient(); // or re-use an existing instance
                //ai.TrackTrace("BomManagerStatus : GetData");
                //ai.TrackException(ex);

                new Errorlog().LogMessage("Error Occured at BomManagerStatus handler " + ex.InnerException.Message);
            }
        }
    }
}
