<%@ Control Language="C#" CodeBehind="Confirm.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.Confirm" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="false" AllowQuickHelp="false">
      <Links>
		<ReboundUI:IconButton ID="ibtnBack" runat="server" IconGroup="Nugget" IconTitleResource="Back" IconCSSType="Back" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
	</Links>

<%--<Explanation><%=Functions.GetGlobalResource("FormExplanations", "Confirm")%></Explanation>--%>

	<Explanation><label id="Exp1"><%=Functions.GetGlobalResource("FormExplanations", "Confirm")%></label><label id="Exp2"></label></Explanation>
	<Content>
		
		<ReboundUI_Table:Form id="frm" runat="server">
			 
			<ReboundUI_Form:FormField id="ctlSalesperson" runat="server" FieldID="ddlSalesperson" ResourceTitle="Buyer" IsRequiredField="true">
				<Field><ReboundDropDown:PoHubBuyer ID="ddlSalesperson" runat="server" />
				</Field>
			</ReboundUI_Form:FormField>

			<ReboundFormFieldCollection:NotifyMailMessageReqToHUB id="ctlSendMailMessage" runat="server" />

			<ReboundUI_Form:FormField id="ctlConfirm" runat="server" FieldID="ctlConfirmation" ResourceTitle="Confirm">
				<Field><ReboundUI:Confirmation ID="ctlConfirmation" runat="server" /></Field>
			</ReboundUI_Form:FormField>
		</ReboundUI_Table:Form>
	</Content>
</ReboundUI_Form:DesignBase>
