///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference name="~/Controls/IconButton/IconButton.js" />
///<reference name="~/Controls/ReboundTextBox/ReboundTextBox.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
/*
Marker     Changed by      Date         Remarks
[001]      Raushan          26/07/2015   This need to drag and drop pdf document for manufacturer section
*/
Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Contact");

Rebound.GlobalTrader.Site.Pages.Contact.ManufacturerDetail = function(el) { 
	Rebound.GlobalTrader.Site.Pages.Contact.ManufacturerDetail.initializeBase(this, [el]);
	this._intManufacturerID = 0;
};

Rebound.GlobalTrader.Site.Pages.Contact.ManufacturerDetail.prototype = {

    get_ctlPageTitle: function() { return this._ctlPageTitle; }, set_ctlPageTitle: function(v) { if (this._ctlPageTitle !== v) this._ctlPageTitle = v; },
    get_ctlMainInfo: function() { return this._ctlMainInfo; }, set_ctlMainInfo: function(v) { if (this._ctlMainInfo !== v) this._ctlMainInfo = v; },
    get_lblAbbreviation: function() { return this._lblAbbreviation; }, set_lblAbbreviation: function(v) { if (this._lblAbbreviation !== v) this._lblAbbreviation = v; },
    get_ctlManufacturerPDF: function() { return this._ctlManufacturerPDF; }, set_ctlManufacturerPDF: function(v) { if (this._ctlManufacturerPDF !== v) this._ctlManufacturerPDF = v; },
    get_ctlManufacturerEXCEL: function() { return this._ctlManufacturerEXCEL; }, set_ctlManufacturerEXCEL: function(v) { if (this._ctlManufacturerEXCEL !== v) this._ctlManufacturerEXCEL = v; },
    // [001] code start
    get_ctlManufacturerPDFDragDrop: function() { return this._ctlManufacturerPDFDragDrop; }, set_ctlManufacturerPDFDragDrop: function(v) { if (this._ctlManufacturerPDFDragDrop !== v) this._ctlManufacturerPDFDragDrop = v; },
    //[001] code end
    get_ctlManufacturerEXCELDragDrop: function() { return this._ctlManufacturerEXCELDragDrop; }, set_ctlManufacturerEXCELDragDrop: function(v) { if (this._ctlManufacturerEXCELDragDrop !== v) this._ctlManufacturerEXCELDragDrop = v; },
    initialize: function() {
        Rebound.GlobalTrader.Site.Pages.Contact.ManufacturerDetail.callBaseMethod(this, "initialize");
    },

    goInit: function() {
        if (this._ctlMainInfo) this._ctlMainInfo.addSaveEditComplete(Function.createDelegate(this, this.ctlMainInfo_EditComplete));
        if (this._ctlManufacturerPDF) this._ctlManufacturerPDF.getData();
        if (this._ctlManufacturerEXCEL) this._ctlManufacturerEXCEL.getData();
        //[001] code start
        if (this._ctlManufacturerPDFDragDrop) this._ctlManufacturerPDFDragDrop.getData();
        //[001] code end

        if (this._ctlManufacturerEXCELDragDrop) this._ctlManufacturerEXCELDragDrop.getData();
        Rebound.GlobalTrader.Site.Pages.Contact.ManufacturerDetail.callBaseMethod(this, "goInit");
    },

    dispose: function() {
        if (this.isDisposed) return;
        if (this._ctlPageTitle) this._ctlPageTitle.dispose();
        if (this._ctlMainInfo) this._ctlMainInfo.dispose();
        if (this._ctlManufacturerPDF) this._ctlManufacturerPDF.dispose();
        if (this._ctlManufacturerEXCEL) this._ctlManufacturerEXCEL.dispose();
        if (this._ctlManufacturerEXCELDragDrop) this._ctlManufacturerEXCELDragDrop.dispose();
        this._intManufacturerID = null;
        this._ctlPageTitle = null;
        this._ctlMainInfo = null;
        this._lblAbbreviation = null;
        this._ctlManufacturerPDF = null;
        this._ctlManufacturerEXCEL = null;
        this._ctlManufacturerEXCELDragDrop = null;
        Rebound.GlobalTrader.Site.Pages.Contact.ManufacturerDetail.callBaseMethod(this, "dispose");
    },

    ctlMainInfo_EditComplete: function() {
        this._ctlPageTitle.updateTitle(this._ctlMainInfo._frmEdit.getFieldValue("ctlName"));
        $R_FN.setInnerHTML(this._lblAbbreviation, this._ctlMainInfo._frmEdit.getFieldValue("ctlAbbreviation"));

    }

};

Rebound.GlobalTrader.Site.Pages.Contact.ManufacturerDetail.registerClass("Rebound.GlobalTrader.Site.Pages.Contact.ManufacturerDetail", Rebound.GlobalTrader.Site.Pages.Content, Sys.IDisposable);
