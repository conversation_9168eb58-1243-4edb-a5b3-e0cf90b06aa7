using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.Site.Controls.DropDowns.Data
{
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class ClientBomStatus : Rebound.GlobalTrader.Site.Data.DropDowns.Base
    {

        public override void ProcessRequest(HttpContext context)
        {
            SetDropDownType("ClientBomStatus");
            base.ProcessRequest(context);
        }

        protected override void GetData()
        {
            //JsonObject jsn = new JsonObject();
            //JsonObject jsnList = new JsonObject(true);
            //JsonObject jsnItem = null;

            //jsnItem = new JsonObject();
            //jsnItem.AddVariable("ID", (int)BOMStatus.List.New);
            //jsnItem.AddVariable("Name", Functions.GetGlobalResource("misc", "New"));
            //jsnList.AddVariable(jsnItem);

            //jsnItem = new JsonObject();
            //jsnItem.AddVariable("ID", (int)BOMStatus.List.Open);
            //jsnItem.AddVariable("Name", Functions.GetGlobalResource("misc", "Open"));
            //jsnList.AddVariable(jsnItem);

            //jsnItem = new JsonObject();
            //jsnItem.AddVariable("ID", (int)BOMStatus.List.RPQ);
            //jsnItem.AddVariable("Name", Functions.GetGlobalResource("misc", "RPQ"));
            //jsnList.AddVariable(jsnItem);

            //jsnItem = new JsonObject();
            //jsnItem.AddVariable("ID", (int)BOMStatus.List.PartialReleased);
            //jsnItem.AddVariable("Name", Functions.GetGlobalResource("misc", "PartialReleased"));
            //jsnList.AddVariable(jsnItem);

            //jsnItem = new JsonObject();
            //jsnItem.AddVariable("ID", (int)BOMStatus.List.Released);
            //jsnItem.AddVariable("Name", Functions.GetGlobalResource("misc", "Released"));
            //jsnList.AddVariable(jsnItem); 
           
            ////Add new code for Closed Status by Prakash on 07-06-2016
            //jsnItem = new JsonObject();
            //jsnItem.AddVariable("ID", (int)BOMStatus.List.Closed);
            //jsnItem.AddVariable("Name", Functions.GetGlobalResource("misc", "Closed"));
            //jsnList.AddVariable(jsnItem); 

            //jsnItem.Dispose(); jsnItem = null;

            //jsn.AddVariable("ClientBomStatus", jsnList);
            //jsnList.Dispose(); jsnList = null;
            //OutputResult(jsn);
            //jsn.Dispose(); jsn = null;

            string strSection = "";
            if (GetFormValue_String("section") != "") strSection = GetFormValue_String("section");
            string strCachedData = CacheManager.GetDropDownData(_objDropDown.ID, strSection);
            if (string.IsNullOrEmpty(strCachedData))
            {
                JsonObject jsn = new JsonObject();
                JsonObject jsnList = new JsonObject(true);
                List<BLL.BOM> lst = BLL.BOM.GetDropDownStatus(strSection);
                for (int i = 0; i < lst.Count; i++)
                {
                    JsonObject jsnItem = new JsonObject();
                    jsnItem.AddVariable("BOMStatusId", lst[i].BOMStatusId);
                    jsnItem.AddVariable("BOMStatusName", lst[i].BOMStatusName);
                    jsnList.AddVariable(jsnItem);
                    jsnItem.Dispose(); jsnItem = null;
                }
                lst.Clear(); lst = null;
                jsn.AddVariable("ClientBomStatus", jsnList);
                jsnList.Dispose(); jsnList = null;
                CacheManager.StoreDropDown(_objDropDown.ID, jsn.Result, CacheManager.CacheExpiryType.OneWorkingDay);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            else
            {
                _context.Response.Write(strCachedData);
            }
            strCachedData = null;
        }
    }
}
