﻿/*     
===========================================================================================    
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION    
[US-225812]     Trung Pham		 20-Apr-2025		UPDATE		Get country and related warning message 
===========================================================================================    
*/    
CREATE OR ALTER PROCEDURE usp_update_SystemWarningMessage       
/*      
Action: Created        Action By: Abhinav <PERSON>xena                    Dated: 20-10-2021      
*/      
    @SystemWarningMessageId int        
  , @ClientNo int        
  , @WarningText nvarchar(max) = NULL        
  , @UpdatedBy int = NULL      
  , @InActive BIT=NULL       
  , @ApplyToCountryId INT = NULL
  , @RowsAffected int = NULL OUTPUT        
AS        
begin     
    UPDATE  dbo.tbSystemWarningMessage        
    SET     ClientNo = @ClientNo         
          , WarningText = @WarningText        
          , UpdatedBy = @UpdatedBy        
    , InActive=ISNULL(@InActive,0)      
	, ApplyTo = CASE WHEN WarningNo = 9 THEN @ApplyToCountryId ELSE ApplyTo END
    WHERE   SystemWarningMessageId = @SystemWarningMessageId        
                    
    SELECT  @RowsAffected = @@ROWCOUNT        
-------------RestrictedManufacturer---------Start--------     
if(@RowsAffected is not null)      
begin      
declare @WarningNo int                 
declare @ApplyCatagoryNo int    
declare @ApplyedMFRTo int    
SELECT @WarningNo=WarningNo,@ApplyCatagoryNo=ApplyToCatagoryNo,@ApplyedMFRTo=ApplyTo FROM dbo.tbSystemWarningMessage WHERE ApplyToCatagoryNo= 1      
AND ClientNo=@ClientNo AND WarningNo=5 AND InActive=0 and SystemWarningMessageId=@SystemWarningMessageId    
if(@WarningNo=5 and @ApplyCatagoryNo=1 )             
begin        
declare @Mfrcheckdublicate int=0             
set @Mfrcheckdublicate=(select count(*) from tbRestrictedManufacturer where ManufacturerNo=@ApplyedMFRTo and ClientNo=@ClientNo)                
if(@Mfrcheckdublicate=0)                
begin        
INSERT                      
INTO dbo.tbRestrictedManufacturer  (ClientNo,ManufacturerNo,Notes,Inactive,UpdatedBy,DLUP)                      
VALUES (@ClientNo,@ApplyedMFRTo,@WarningText,0,@UpdatedBy,CURRENT_TIMESTAMP)         
end      
else    
begin    
update dbo.tbRestrictedManufacturer set Notes=@WarningText,Inactive=ISNULL(@InActive,0),UpdatedBy=@UpdatedBy,DLUP=CURRENT_TIMESTAMP    
where ManufacturerNo=@ApplyedMFRTo and ClientNo=@ClientNo    
end      
end        
------------RestrictedManufacturer end----------------      
end      
end    
  