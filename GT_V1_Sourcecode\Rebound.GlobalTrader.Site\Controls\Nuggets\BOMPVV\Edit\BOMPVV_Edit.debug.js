///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference name="~/Controls/IconButton/IconButton.js" />
///<reference name="~/Controls/ReboundTextBox/ReboundTextBox.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//Marker     Changed by      Date         Remarks
//[001]      Vinay           12/06/2014   EMS Ticket No:	165
//[002]      Vinay           28/07/2015   EMS Ticket No:	254
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.BOMPVV_Edit = function(element) {
    Rebound.GlobalTrader.Site.Controls.Forms.BOMPVV_Edit.initializeBase(this, [element]);
	this._intBOMID = -1;
    this._blnRequestedToPoHub = false;
    this._IsView = false;
    this._IsFromBOMAdd = false;
    this._strGeneratedID = "";

};

Rebound.GlobalTrader.Site.Controls.Forms.BOMPVV_Edit.prototype = {

    get_intBOMID: function() { return this._intBOMID; }, set_intBOMID: function(value) { if (this._intBOMID !== value) this._intBOMID = value; },
    get_BomCode: function() { return this._BomCode; }, set_BomCode: function(value) { if (this._BomCode !== value) this._BomCode = value; },
    get_BomName: function() { return this._BomName; }, set_BomName: function(value) { if (this._BomName !== value) this._BomName = value; },
    get_BomCompanyName: function() { return this._BomCompanyName; }, set_BomCompanyName: function(value) { if (this._BomCompanyName !== value) this._BomCompanyName = value; },
    get_BomCompanyNo: function () { return this._BomCompanyNo; }, set_BomCompanyNo: function (value) { if (this._BomCompanyNo !== value) this._BomCompanyNo = value; },
   
    initialize: function() {
        Rebound.GlobalTrader.Site.Controls.Forms.BOMPVV_Edit.callBaseMethod(this, "initialize");
        this.addShown(Function.createDelegate(this, this.formShown));
    },

    formShown: function() {
        if (this._blnFirstTimeShown) {
            this.addSave(Function.createDelegate(this, this.saveClicked));
            this.GetListData();
            
        }
        this.GetListData();
        
    },

    dispose: function() {
        if (this.isDisposed) return;
        this._intBOMID = null;
        this._IsView = null;
        this._IsFromBOMAdd = null;
        this._strGeneratedID = null;
        Rebound.GlobalTrader.Site.Controls.Forms.BOMPVV_Edit.callBaseMethod(this, "dispose");
    },
    GetListData: function () {
        this._blnRequestedToPoHub = false;
       // this.getData_Start();
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/BOMPVV");
        obj.set_DataObject("BOMPVV");
        if (this._IsFromBOMAdd) {
            obj.set_DataAction("GetListDataTemp");
            obj.addParameter("idGenerated", this._strGeneratedID);
        }
        else {
            obj.set_DataAction("GetListData");
            obj.addParameter("id", this._intBOMID);
        }
        
        obj.addDataOK(Function.createDelegate(this, this.getDataOK));
        obj.addError(Function.createDelegate(this, this.getDataError));
        obj.addTimeout(Function.createDelegate(this, this.getDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
        //this.onSave();
    },

    getDataOK: function (args) {
        
        var strreadonly = "";
        $("#ctl00_cphMain_ctlBOMPVV_ctlDB_ctl17_ibtnSave_hyp").show();
        $("#ctl00_cphMain_ctlBOMPVV_ctlDB_ctl18_ibtnSave_hyp").show();
        $("#ctl00_cphMain_ctlAdd_ctlDB_ctl20_ibtnSave").show();
        $("#ctl00_cphMain_ctlAdd_ctlDB_ctl19_ibtnSave").show();

        
        if (this._IsView == true) {
            strreadonly = "readonly"
            $("#ctl00_cphMain_ctlBOMPVV_ctlDB_ctl17_ibtnSave_hyp").hide();
            $("#ctl00_cphMain_ctlBOMPVV_ctlDB_ctl18_ibtnSave_hyp").hide();
            $("#ctl00_cphMain_ctlAdd_ctlDB_ctl20_ibtnSave").hide();
            $("#ctl00_cphMain_ctlAdd_ctlDB_ctl19_ibtnSave").hide();
        }
        var res = args._result;
        if (res != null) {
            //var counter = 1;
            var content = "<table id='PPVData' class='LinkedItems'>";
            content += '<tr>';
            for (i = 0; i < res.Results.length; i++) {
                content += '<td>';
                //content += '<tr><td>( '+ counter +' )</td> <td style="width:555px;">' + $R_FN.setCleanTextValue(res.Results[i].PVVQuestionName) + '</td>' + '<td style="width:50px;" class="item"> <input type="hidden" id="custId" name="custId" value="' + res.Results[i].PVVQuestionId + '"><textarea id="' + res.Results[i].PVVQuestionId + '" class="PVVAnswer" rows="4" cols="80" maxlength="500" placeholder="500 Character max" ' + strreadonly+' value="' + res.Results[i].PVVAnswerName + '">' + $R_FN.setCleanTextValue(res.Results[i].PVVAnswerName) + '</textarea></td></tr>';
                content += '<tr><td style="width:55%;">' + $R_FN.setCleanTextValue(res.Results[i].PVVQuestionName) + '</td>' + '<td class="item"> <input type="hidden" id="custId" name="custId" value="' + res.Results[i].PVVQuestionId + '"><textarea id="' + res.Results[i].PVVQuestionId + '" class="PVVAnswer" rows="4" cols="80" maxlength="500" placeholder="500 Character max" ' + strreadonly + ' value="' + res.Results[i].PVVAnswerName + '">' + $R_FN.setCleanTextValue(res.Results[i].PVVAnswerName) + '</textarea></td></tr>';
                content += '</td>';
                //counter++;
            }
            content += '</tr>';
            content += "</table>";
            $('#PVVBOMDiv').html(content);
          
        }
       
    },

    getDataError: function (args) {
        this.showError(true, args.get_ErrorMessage());
    },

    saveClicked: function () {
        var PVVQ = "";
        var PVVAnswer = "";
        $(".PVVAnswer").each(function () {
           
            //PVVQ += this.id + '|';
            //PVVQ += $("'#" + this.id + "'").val() + "||";
            PVVQ += this.id + '|'+ $("#" + this.id).val() + ",|,";
            
        });
        PVVAnswer = PVVQ.substr(0, PVVQ.lastIndexOf(',|,') - 0);
        //if (this._blnRequestedToPoHub==false) {
        //    if (!this.validateForm()) return;
        //}
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/BOMPVV");
        obj.set_DataObject("BOMPVV");
        obj.set_DataAction("SaveEdit");
        if (this._IsFromBOMAdd) {
            obj.set_DataAction("SaveEditTemp");
            obj.addParameter("idGenerated", this._strGeneratedID);
        }
        obj.addParameter("BOMNo", this._intBOMID);
        obj.addParameter("PVVAnswers", PVVAnswer);
        obj.addDataOK(Function.createDelegate(this, this.saveEditComplete));
        obj.addError(Function.createDelegate(this, this.saveEditError));
        obj.addTimeout(Function.createDelegate(this, this.saveEditError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    saveEditError: function(args) {
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    saveEditComplete: function(args) {
        if (args._result.Result == true) {
            this.showSavedOK(true);
            this.onSaveComplete();
          
        } else {
            this._strErrorMessage = args._errorMessage;
            this.onSaveError();
        }
    },

   

    validateForm: function() {
        this.onValidate();
        var blnOK = this.autoValidateFields();
        
        return blnOK;
    },
    showFieldsLoading: function(bln) {
       // this.showFieldLoading("ctlCompany", bln);
    }

};

Rebound.GlobalTrader.Site.Controls.Forms.BOMPVV_Edit.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.BOMPVV_Edit", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
