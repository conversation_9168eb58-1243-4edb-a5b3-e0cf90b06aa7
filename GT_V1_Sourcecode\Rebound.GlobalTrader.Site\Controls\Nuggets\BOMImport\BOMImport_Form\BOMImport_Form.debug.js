///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
// Marker   Changed by   Changed Date   Remarks
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.BOMImport_Form = function (element) {
    Rebound.GlobalTrader.Site.Controls.Forms.BOMImport_Form.initializeBase(this, [element]);
    this._intBOMID = 0;
};

Rebound.GlobalTrader.Site.Controls.Forms.BOMImport_Form.prototype = {
    get_intBOMID: function () { return this._intBOMID; }, set_intBOMID: function (value) { if (this._intBOMID !== value) this._intBOMID = value; },
    get_tblPartdetails: function () { return this._tblPartdetails; }, set_tblPartdetails: function (v) { if (this._tblPartdetails !== v) this._tblPartdetails = v; },
    get_btn1: function () { return this._btn1; }, set_btn1: function (v) { if (this._btn1 !== v) this._btn1 = v; },
    get_btn2: function () { return this._btn2; }, set_btn2: function (v) { if (this._btn2 !== v) this._btn2 = v; },
    get_lblError: function () { return this._lblError; }, set_lblError: function (v) { if (this._lblError !== v) this._lblError = v; },
    get_intLoginID: function () { return this._intLoginID; }, set_intLoginID: function (v) { if (this._intLoginID !== v) this._intLoginID = v; },
    initialize: function () {
        Rebound.GlobalTrader.Site.Controls.Forms.BOMImport_Form.callBaseMethod(this, "initialize");
        this._tblPartdetails.addSelectedIndexChanged(Function.createDelegate(this, this.getParSearch));
        if (this._btn1) $addHandler(this._btn1, "click", Function.createDelegate(this, this.Toggle1));
        if (this._btn2) $addHandler(this._btn2, "click", Function.createDelegate(this, this.Toggle2));
        $R_FN.showElement(this._lblError, false);
        $R_FN.showElement(this._btn2, false);
        this.showField("ctlPartDetail", false);
    },

    dispose: function () {
        if (this.isDisposed) return;
        //this._ibtnNote = null;   
        this._tblPartdetails = null;
        this._btn1 = null;
        this._btn2 = null;
        this._lblError = null;
        this._intLoginID = null;
        Rebound.GlobalTrader.Site.Controls.Forms.BOMImport_Form.callBaseMethod(this, "dispose");
    },
    impotExcelData: function (originalFilename, generatedFilename, bomid,excelColumns) {
        $('#divLoader').show();
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/BOMImport");
        obj.set_DataObject("BOMImport");
        obj.set_DataAction("ImportExcelData");
        obj.addParameter("originalFilename", originalFilename);
        obj.addParameter("generatedFilename", generatedFilename);
        obj.addParameter("BOMId", bomid);
        obj.addParameter("ExcelColumns", excelColumns);
        //obj.addParameter("percentage", this._decPercentage);
        obj.addDataOK(Function.createDelegate(this, this.importExcelDataOK));
        obj.addError(Function.createDelegate(this, this.importExcelDataError));
        obj.addTimeout(Function.createDelegate(this, this.importExcelDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },
    importExcelDataOK: function (args) {
        flogId = args._result.FileLogId;
        bindGridData.call(this);
        $('#divLoader').hide();
    },
    importExcelDataError: function (args) {
        alert(args._errorMessage.split('<br/>')[0]);
        $('#divLoader').hide();
        //this.showError(true, args.get_ErrorMessage());
        //this._strErrorMessage = args._errorMessage;
        //this.onSaveError();
    },
    //Auto Search Method
    getParSearch: function () {
        
        //this.setFieldValue("ctlDateCode", "");
        //this.setFieldValue("ctlProduct", 0, null, "");
        //this.setFieldValue("ctlPackage", -1);
        //this.setFieldValue("ctlROHS", -1);
        //this.setFieldValue("ctlManufacturer", 0, null, "");
        $("#ddlPackage").val(0);
        $("#ddlRoHS").val(0);
        $("#txtManufacturer").val("");
        $("#txtDateCode").val("");
        $("#txtProduct").val("");

        var PartNo = this._tblPartdetails._varSelectedValue;
        if (PartNo.length > 0) {
            this.getValuesByParts();

        }
    },
    getValuesByParts: function () {
        var obj = this._tblPartdetails.getSelectedExtraData(); if (!obj) return;
        var PartName = obj.PartName;
        $R_FN.showElement(this._btn2, true);
        //this.setFieldValue("ctlDateCode", obj.DateCodeOriginal);
        if (!obj.ProductInactive) {
            //$("#txtProduct").val(obj.ProductDescription);
            this.setFieldValue("ctlProduct", obj.ProductNo, null, obj.ProductDescription);
        }
        else {
            this.setFieldValue("ctlProduct", 0, null, ""); //Disable due to inactive product
            //$("#txtProduct").val("");
        }
        $("#ddlPackage").val(obj.PackageNo);
        $("#ddlRoHS").val(obj.ROHSNo);
        //$("#txtManufacturer").val(obj.Manufacturer);
        $("#txtDateCode").val(obj.DateCodeOriginal);
        
        this.setFieldValue("ctlManufacturer", obj.ManufacturerNo, null, obj.Manufacturer);
        this.setFieldValue("ctlPartNo", PartName);

    },
    getControlValue: function (controlId) {
      return this.getFieldValue(controlId);
    },
    setComboValue: function (controlId, Id, Text) {
        
        this.setFieldValue(controlId, Id, null, Text);
    },
    setPartNo: function (partName) {
        this.setFieldValue("ctlPartNo", partName);
    },
    clearAutoSearchField: function () {
        this.setFieldValue("ctlProduct", 0, null, "");
        this.setFieldValue("ctlManufacturer", 0, null, "");
        this.setFieldValue("ctlPartNo", "");
    },
    Toggle2: function () {

        this.clearAutoSearchField();

        $("#ddlPackage").val(0);
        $("#ddlRoHS").val(0);
        $("#txtDateCode").val("");
        
        this._tblPartdetails.clearTable();
        this.showField("ctlPartDetail", false);
        $R_FN.showElement(this._btn1, true);
        $R_FN.showElement(this._btn2, false);

    },
    Toggle1: function () {

        if (this.getFieldValue("ctlPartNo").length < 3) {
            $R_FN.showElement(this._lblError, true);
            return;
        } else {
            $R_FN.showElement(this._lblError, false);
        }
        var PartNo = this.getFieldValue("ctlPartNo");
        if (PartNo.length > 0) {
            this.showProductLoading(true);
            var obj = new Rebound.GlobalTrader.Site.Data();
            obj.set_PathToData("controls/Nuggets/CusReqAdd");
            obj.set_DataObject("CusReqAdd");
            obj.set_DataAction("GetDataGrid");
            obj.addParameter("searchType", this.getFieldValue("ctlPartNo"));

            obj.addDataOK(Function.createDelegate(this, this.getDataGrid));
            obj.addError(Function.createDelegate(this, this.getDataGridError));
            obj.addTimeout(Function.createDelegate(this, this.getDataGridError));
            $R_DQ.addToQueue(obj);
            $R_DQ.processQueue();
            obj = null;
        }

    },
    getDataGridError: function (args) {

    },
    getDataGrid: function (args) {

        res = args._result;
        //  if (res.PartDetails.length > 0) {
        this.showField("ctlPartDetail", res.PartDetails.length > 0);

        this._tblPartdetails.clearTable();
        for (var i = 0; i < res.PartDetails.length; i++) {
            var row = res.PartDetails[i];
            var aryData = [
                row.ID,
              row.Manufacturer,
               row.Product,
             row.Package,
              row.DateCode,

            ];
            var objExtraData = {
                PartName: row.ID,
                DateCodeOriginal: row.DateCodeOriginal,
                ManufacturerNo: row.ManufacturerNo,
                ProductNo: row.ProductNo,
                PackageNo: row.PackageNo,
                Manufacturer: row.Manufacturer,
                ROHSNo: row.ROHSNo,
                ProductDescription: row.ProductDescription,
                ProductInactive: row.PrdInactive
            };
            this._tblPartdetails.addRow(aryData, row.Ids, false, objExtraData);
            aryData = null;
            row = null;
            this.showProductLoading(false);
        }

        this._tblPartdetails.resizeColumns();
        //  }
    },
    showProductLoading: function (bln) {
        this.showFieldLoading("ctlPartNo", bln);
    }
};
Rebound.GlobalTrader.Site.Controls.Forms.BOMImport_Form.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.BOMImport_Form", Rebound.GlobalTrader.Site.Controls.Forms.Base);
