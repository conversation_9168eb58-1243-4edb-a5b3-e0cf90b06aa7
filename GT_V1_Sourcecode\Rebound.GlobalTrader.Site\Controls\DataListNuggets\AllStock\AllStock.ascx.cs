//----------------------------------------------------------------------------------------------------------------
// RP 23.12.2009:
// - render state on server
//
// RP 30.11.2009:
// - allow passing of Part No
/* Marker     changed by      date         Remarks
   [001]      Vikas kumar     17/11/2011  ESMS Ref:23 - PO No. and Crma No. should also be displayed */
//----------------------------------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.DataListNuggets
{
    public partial class AllStock : Base
    {
        private bool _IsGlobalLogin = false;
        public bool IsGlobalLogin
        {
            get { return _IsGlobalLogin; }
            set { _IsGlobalLogin = value; }
        }

        #region Overrides

        /// <summary>
        /// OnInit
        /// </summary>
        /// <param name="e"></param>
        protected override void OnInit(EventArgs e)
        {
            //IsGlobalLogin = SessionManager.IsGlobalUser.Value;
            SetDataListNuggetType("AllStock");
            base.OnInit(e);
            AddScriptReference("Controls.DataListNuggets.AllStock.AllStock.js");
            TitleText = Functions.GetGlobalResource("Nuggets", "Stock");
            SetupTable();
        }

        protected override void OnLoad(EventArgs e)
        {
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.DataListNuggets.AllStock", ctlDesignBase.ClientID);
            _intCurrentTab = ((Pages.Content)Page).CurrentTab;
            _scScriptControlDescriptor.AddProperty("blnPOHub", Convert.ToBoolean(SessionManager.IsPOHub));
            _scScriptControlDescriptor.AddProperty("IsGlobalLogin", SessionManager.IsGlobalUser.Value);
            base.OnLoad(e);
        }

        protected override void GetSavedState()
        {
            base.GetSavedState();

            //don't render state if we are searching for a part
            if (!string.IsNullOrEmpty(_objQSManager.SearchPartNo))
            {
                ResetAllState();
                this.SetFilterValue("Part", _objQSManager.SearchPartNo);
                _intCurrentTab = 0;
            }
        }

        protected override void RenderAdditionalState()
        {
            var strCallType = this.GetSavedStateValue("CallType").ToUpper();
            if (string.IsNullOrEmpty(strCallType)) strCallType = "ALL";
            if (strCallType == null) strCallType = "ALL";
            if (strCallType == "ALL") _intCurrentTab = 0;
            if (strCallType == "AVAILABLE") _intCurrentTab = 1;
            if (strCallType == "QUARANTINED") _intCurrentTab = 2;
            base.RenderAdditionalState();
        }

        protected override void OnPreRender(EventArgs e)
        {
            base.OnPreRender(e);
            _scScriptControlDescriptor.AddProperty("intCurrentTab", _intCurrentTab);
            ((Pages.Content)Page).CurrentTab = _intCurrentTab;
            this.OnAskPageToChangeTab();
        }

        #endregion

        private void SetupTable()
        {
            _tbl.AllowSelection = false;
            _tbl.Columns.Add(new FlexiDataColumn("PartNo", "SupplierPart", WidthManager.GetWidth(WidthManager.ColumnWidth.PartNo), true));
            _tbl.Columns.Add(new FlexiDataColumn("QuantityInStock", "QuantityOnOrder", WidthManager.GetWidth(WidthManager.ColumnWidth.Quantity), true));
            _tbl.Columns.Add(new FlexiDataColumn("QuantityAllocated", "QuantityAvailable", WidthManager.GetWidth(WidthManager.ColumnWidth.Quantity), true));
            _tbl.Columns.Add(new FlexiDataColumn("Warehouse", "Location", WidthManager.GetWidth(WidthManager.ColumnWidth.Warehouse), true));
            //[001]Code Start 
            _tbl.Columns.Add(new FlexiDataColumn("Lot", "PurchaseOrder", WidthManager.GetWidth(WidthManager.ColumnWidth.Lot), true));
            _tbl.Columns.Add(new FlexiDataColumn("Status", "CRMA", Unit.Empty, false));
            if (SessionManager.IsPOHub == true || SessionManager.IsGlobalUser.Value)
            {
                _tbl.Columns.Add(new FlexiDataColumn("Client", WidthManager.GetWidth(WidthManager.ColumnWidth.CompanyName), false));
            }
            //[001]Code End
        }
    }
}
