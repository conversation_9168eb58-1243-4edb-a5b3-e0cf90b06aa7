Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyTransactions=function(n){Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyTransactions.initializeBase(this,[n]);this._intCompanyID=-1;this._aryAlreadyGotTabData=[];this._blnCancelAddingData=!1;this._objTabIndexes={Requirements:0,BOMs:1,Quotes:2,SalesOrders:3,Invoices:4,PurchaseOrders:5,CRMAs:6,SRMAs:7,CreditNotes:8,DebitNotes:9}};Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyTransactions.prototype={get_intCompanyID:function(){return this._intCompanyID},set_intCompanyID:function(n){this._intCompanyID!==n&&(this._intCompanyID=n)},get_ctlTabStrip:function(){return this._ctlTabStrip},set_ctlTabStrip:function(n){this._ctlTabStrip!==n&&(this._ctlTabStrip=n)},get_tblRequirements:function(){return this._tblRequirements},set_tblRequirements:function(n){this._tblRequirements!==n&&(this._tblRequirements=n)},get_tblBOMs:function(){return this._tblBOMs},set_tblBOMs:function(n){this._tblBOMs!==n&&(this._tblBOMs=n)},get_tblQuotes:function(){return this._tblQuotes},set_tblQuotes:function(n){this._tblQuotes!==n&&(this._tblQuotes=n)},get_tblSOs:function(){return this._tblSOs},set_tblSOs:function(n){this._tblSOs!==n&&(this._tblSOs=n)},get_tblPOs:function(){return this._tblPOs},set_tblPOs:function(n){this._tblPOs!==n&&(this._tblPOs=n)},get_tblInvoices:function(){return this._tblInvoices},set_tblInvoices:function(n){this._tblInvoices!==n&&(this._tblInvoices=n)},get_tblSRMAs:function(){return this._tblSRMAs},set_tblSRMAs:function(n){this._tblSRMAs!==n&&(this._tblSRMAs=n)},get_tblCRMAs:function(){return this._tblCRMAs},set_tblCRMAs:function(n){this._tblCRMAs!==n&&(this._tblCRMAs=n)},get_tblCreditNotes:function(){return this._tblCreditNotes},set_tblCreditNotes:function(n){this._tblCreditNotes!==n&&(this._tblCreditNotes=n)},get_tblDebitNotes:function(){return this._tblDebitNotes},set_tblDebitNotes:function(n){this._tblDebitNotes!==n&&(this._tblDebitNotes=n)},get_blnIncludeClosed:function(){return this._blnIncludeClosed},set_blnIncludeClosed:function(n){this._blnIncludeClosed!==n&&(this._blnIncludeClosed=n)},get_chkIncludeClosed:function(){return this._chkIncludeClosed},set_chkIncludeClosed:function(n){this._chkIncludeClosed!==n&&(this._chkIncludeClosed=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyTransactions.callBaseMethod(this,"initialize");this.addRefreshEvent(Function.createDelegate(this,this.doRefresh));this._strPathToData="controls/Nuggets/CompanyTransactions";this._strDataObject="CompanyTransactions";this._ctlTabStrip.addTabIndexChanged(Function.createDelegate(this,this.tabChanged));this._ctlTabStrip.showContent(!1);this._chkIncludeClosed.addClick(Function.createDelegate(this,this.includeClosedChanged));$("#ctl00_cphMain_ctlTransactions_ctlDB_ctl06").append("<br/><br/><span style='color:red;background-color: yellow;'>Requirement tab showing upto 2 years of data only.<\/span>")},dispose:function(){this.isDisposed||(this._ctlTabStrip&&this._ctlTabStrip.dispose(),this._chkIncludeClosed&&this._chkIncludeClosed.dispose(),this._intCompanyID=null,this._aryAlreadyGotTabData=null,this._blnCancelAddingData=null,this._ctlTabStrip=null,this._chkIncludeClosed=null,this._objTabIndexes=null,Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyTransactions.callBaseMethod(this,"dispose"))},initialGetData:function(){this.showContentLoading(!1);this.showContent(!0);this.tabChanged();this.getCounts()},doRefresh:function(){this.getCounts();this.getTabData()},getCounts:function(){this.showLoading(!0);this._ctlTabStrip.resetTabTitles();var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("GetCounts");n.addParameter("id",this._intCompanyID);n.addParameter("IncludeClosed",this._blnIncludeClosed);n.addDataOK(Function.createDelegate(this,this.getCountsOK));n.addError(Function.createDelegate(this,this.dataCallError));n.addTimeout(Function.createDelegate(this,this.dataCallError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getCountsOK:function(n){var i,r,u,t;for(this.showLoading(!1),i=n._result,r=0,u=this._ctlTabStrip._aryTabIDs.length;r<u;r++){if(t=$find(this._ctlTabStrip._aryTabIDs[r]),t)switch(r){case this._objTabIndexes.Requirements:t.addCountToTitle(i.Requirements);break;case this._objTabIndexes.BOMs:t.addCountToTitle(i.BOMs);break;case this._objTabIndexes.Quotes:t.addCountToTitle(i.Quotes);break;case this._objTabIndexes.SalesOrders:t.addCountToTitle(i.SalesOrders);break;case this._objTabIndexes.Invoices:t.addCountToTitle(i.Invoices);break;case this._objTabIndexes.PurchaseOrders:t.addCountToTitle(i.PurchaseOrders);break;case this._objTabIndexes.CRMAs:t.addCountToTitle(i.CustomerRMAs);break;case this._objTabIndexes.SRMAs:t.addCountToTitle(i.SupplierRMAs);break;case this._objTabIndexes.CreditNotes:t.addCountToTitle(i.CreditNotes);break;case this._objTabIndexes.DebitNotes:t.addCountToTitle(i.DebitNotes)}t=null}},getCurrentTable:function(){switch(this._ctlTabStrip._selectedTabIndex){case this._objTabIndexes.Requirements:return this._tblRequirements;case this._objTabIndexes.BOMs:return this._tblBOMs;case this._objTabIndexes.Quotes:return this._tblQuotes;case this._objTabIndexes.SalesOrders:return this._tblSOs;case this._objTabIndexes.Invoices:return this._tblInvoices;case this._objTabIndexes.PurchaseOrders:return this._tblPOs;case this._objTabIndexes.CRMAs:return this._tblCRMAs;case this._objTabIndexes.SRMAs:return this._tblSRMAs;case this._objTabIndexes.CreditNotes:return this._tblCreditNotes;case this._objTabIndexes.DebitNotes:return this._tblDebitNotes}},clearAllTables:function(){this._tblRequirements.clearTable();this._tblBOMs.clearTable();this._tblQuotes.clearTable();this._tblSOs.clearTable();this._tblInvoices.clearTable();this._tblPOs.clearTable();this._tblCRMAs.clearTable();this._tblSRMAs.clearTable();this._tblCreditNotes.clearTable();this._tblDebitNotes.clearTable()},tabChanged:function(){this._aryAlreadyGotTabData[this._ctlTabStrip._selectedTabIndex]||this.getTabData();this.getCurrentTable().resizeColumns()},includeClosedChanged:function(){this._blnCancelAddingData=!0;this._blnIncludeClosed=this._chkIncludeClosed._blnChecked;this._aryAlreadyGotTabData=[];this.getCounts();this.clearAllTables();this.tabChanged()},getTabData:function(){this.showContent(!0);switch(this._ctlTabStrip._selectedTabIndex){case this._objTabIndexes.Requirements:this.getTabData_Requirements();break;case this._objTabIndexes.BOMs:this.getTabData_BOMs();break;case this._objTabIndexes.Quotes:this.getTabData_Quotes();break;case this._objTabIndexes.SalesOrders:this.getTabData_SalesOrder();break;case this._objTabIndexes.Invoices:this.getTabData_Invoice();break;case this._objTabIndexes.PurchaseOrders:this.getTabData_PurchaseOrder();break;case this._objTabIndexes.CRMAs:this.getTabData_CRMAs();break;case this._objTabIndexes.SRMAs:this.getTabData_SRMAs();break;case this._objTabIndexes.CreditNotes:this.getTabData_CreditNote();break;case this._objTabIndexes.DebitNotes:this.getTabData_DebitNote()}},doDataCall:function(n,t){this.showLoading(!0);this._ctlTabStrip.showLoading(!0);this._objData&&this._objData.cancel();this._objData=new Rebound.GlobalTrader.Site.Data;this._objData.set_PathToData(this._strPathToData);this._objData.set_DataObject(this._strDataObject);this._objData.set_DataAction(n);this._objData.addParameter("id",this._intCompanyID);this._objData.addParameter("IncludeClosed",this._blnIncludeClosed);this._objData.addDataOK(t);this._objData.addError(Function.createDelegate(this,this.dataCallError));this._objData.addTimeout(Function.createDelegate(this,this.dataCallError));$R_DQ.addToQueue(this._objData);$R_DQ.processQueue()},dataCallError:function(n){this.showError(!0,n.get_ErrorMessage())},dataCallFinished:function(){this.showLoading(!1);this._ctlTabStrip.showContent(!0)},getTabData_Requirements:function(){this.doDataCall("GetCustomerRequirementData",Function.createDelegate(this,this.getTabDataComplete_Requirements))},getTabDataComplete_Requirements:function(n){var t=n._result;this._tblRequirements.clearTable();this._blnCancelAddingData=!1;t!=null&&t.Items&&this.addData_Requirements(t,0);this._tblRequirements.resizeColumns();this.dataCallFinished()},addData_Requirements:function(n,t){var i,r,u,f;this._blnCancelAddingData||(t<n.Items.length?(i=n.Items[t],r=[$RGT_nubButton_CustomerRequirement(i.ID,i.No),i.ReceivedDate,$R_FN.setCleanTextValue(i.Part),i.Quantity,i.Price],this._tblRequirements.addRow(r,i.ID,!1),i=null,t+=1,u=this._element.id,f=function(){$find(u).addData_Requirements(n,t)},setTimeout(f,0)):(this._aryAlreadyGotTabData[this._objTabIndexes.Requirements]=!0,this._tblRequirements.resizeColumns()))},getTabData_BOMs:function(){this.doDataCall("GetCustomerBOMsData",Function.createDelegate(this,this.getTabDataComplete_BOMs))},getTabDataComplete_BOMs:function(n){var t=n._result;this._tblBOMs.clearTable();this._blnCancelAddingData=!1;t!=null&&t.Items&&this.addData_BOMs(t,0);this._tblBOMs.resizeColumns();this.dataCallFinished()},addData_BOMs:function(n,t){var i,r,u,f;this._blnCancelAddingData||(t<n.Items.length?(i=n.Items[t],r=[$RGT_nubButton_BMM(i.ID,i.BOMCode),i.BOMName,i.ReceivedDate,i.Salesperson,i.Status,i.TotalValue],this._tblBOMs.addRow(r,i.ID,!1),i=null,t+=1,u=this._element.id,f=function(){$find(u).addData_BOMs(n,t)},setTimeout(f,0)):(this._aryAlreadyGotTabData[this._objTabIndexes.BOMs]=!0,this._tblBOMs.resizeColumns()))},getTabData_Quotes:function(){this.doDataCall("GetQuotesData",Function.createDelegate(this,this.getTabDataComplete_Quotes))},getTabDataComplete_Quotes:function(n){var t=n._result;this._tblQuotes.clearTable();this._blnCancelAddingData=!1;t!=null&&t.Items&&this.addData_Quotes(t,0);this._tblQuotes.resizeColumns();this.dataCallFinished()},addData_Quotes:function(n,t){var i,r,u,f;this._blnCancelAddingData||(t<n.Items.length?(i=n.Items[t],r=[$RGT_nubButton_Quote(i.ID,i.No),i.Date,i.Value,$R_FN.setCleanTextValue(i.Salesman)],this._tblQuotes.addRow(r,i.ID,!1),i=null,t+=1,u=this._element.id,f=function(){$find(u).addData_Quotes(n,t)},setTimeout(f,0)):(this._aryAlreadyGotTabData[this._objTabIndexes.Quotes]=!0,this._tblQuotes.resizeColumns()))},getTabData_SalesOrder:function(){this.doDataCall("GetSalesOrderData",Function.createDelegate(this,this.getTabDataComplete_SalesOrder))},getTabDataComplete_SalesOrder:function(n){var t=n._result;this._tblSOs.clearTable();this._blnCancelAddingData=!1;t!=null&&t.Items&&this.addData_SalesOrder(t,0);this._tblSOs.resizeColumns();this.dataCallFinished()},addData_SalesOrder:function(n,t){var i,r,u,f;this._blnCancelAddingData||(t<n.Items.length?(i=n.Items[t],r=[$RGT_nubButton_SalesOrder(i.ID,i.No),i.Date,i.Value,$R_FN.setCleanTextValue(i.CustomerPO),$R_FN.setCleanTextValue(i.Salesman)],this._tblSOs.addRow(r,i.ID,!1),i=null,t+=1,u=this._element.id,f=function(){$find(u).addData_SalesOrder(n,t)},setTimeout(f,0)):(this._aryAlreadyGotTabData[this._objTabIndexes.SalesOrders]=!0,this._tblSOs.resizeColumns()))},getTabData_PurchaseOrder:function(){this.doDataCall("GetPurchaseOrderData",Function.createDelegate(this,this.getTabDataComplete_PurchaseOrder))},getTabDataComplete_PurchaseOrder:function(n){var t=n._result;this._tblPOs.clearTable();this._blnCancelAddingData=!1;t!=null&&t.Items&&this.addData_PurchaseOrder(t,0);this._tblPOs.resizeColumns();this.dataCallFinished()},addData_PurchaseOrder:function(n,t){var i,r,u,f;this._blnCancelAddingData||(t<n.Items.length?(i=n.Items[t],r=[$RGT_nubButton_PurchaseOrder(i.ID,i.No),i.Date,i.Value,$R_FN.setCleanTextValue(i.Buyer)],this._tblPOs.addRow(r,i.ID,!1),i=null,t+=1,u=this._element.id,f=function(){$find(u).addData_PurchaseOrder(n,t)},setTimeout(f,0)):(this._aryAlreadyGotTabData[this._objTabIndexes.PurchaseOrders]=!0,this._tblPOs.resizeColumns()))},getTabData_CRMAs:function(){this.doDataCall("GetCustomerRMAData",Function.createDelegate(this,this.getTabDataComplete_CRMAs))},getTabDataComplete_CRMAs:function(n){var t=n._result;this._tblCRMAs.clearTable();this._blnCancelAddingData=!1;t!=null&&t.Items&&this.addData_CRMAs(t,0);this._tblCRMAs.resizeColumns();this.dataCallFinished()},addData_CRMAs:function(n,t){var i,r,u,f;this._blnCancelAddingData||(t<n.Items.length?(i=n.Items[t],r=[$RGT_nubButton_CRMA(i.ID,i.No),i.Date,$RGT_nubButton_Invoice(i.InvoiceNo,i.InvoiceNumber),$R_FN.setCleanTextValue(i.Authoriser),$RGT_nubButton_Contact(i.ContactNo,i.Contact)],this._tblCRMAs.addRow(r,i.ID,!1),i=null,t+=1,u=this._element.id,f=function(){$find(u).addData_CRMAs(n,t)},setTimeout(f,0)):(this._aryAlreadyGotTabData[this._objTabIndexes.CRMAs]=!0,this._tblCRMAs.resizeColumns()))},getTabData_SRMAs:function(){this.doDataCall("GetSupplierRMAData",Function.createDelegate(this,this.getTabDataComplete_SRMAs))},getTabDataComplete_SRMAs:function(n){var t=n._result;this._tblSRMAs.clearTable();this._blnCancelAddingData=!1;t!=null&&t.Items&&this.addData_SRMAs(t,0);this._tblSRMAs.resizeColumns();this.dataCallFinished()},addData_SRMAs:function(n,t){var i,r,u,f;this._blnCancelAddingData||(t<n.Items.length?(i=n.Items[t],r=[$RGT_nubButton_SRMA(i.ID,i.No),i.Date,$RGT_nubButton_PurchaseOrder(i.PONo,i.PONumber),$R_FN.setCleanTextValue(i.Authoriser),$RGT_nubButton_Contact(i.ContactNo,i.Contact)],this._tblSRMAs.addRow(r,i.ID,!1),i=null,t+=1,u=this._element.id,f=function(){$find(u).addData_SRMAs(n,t)},setTimeout(f,0)):(this._aryAlreadyGotTabData[this._objTabIndexes.SRMAs]=!0,this._tblSRMAs.resizeColumns()))},getTabData_Invoice:function(){this.doDataCall("GetInvoiceData",Function.createDelegate(this,this.getTabDataComplete_Invoice))},getTabDataComplete_Invoice:function(n){var t=n._result;this._tblInvoices.clearTable();this._blnCancelAddingData=!1;t!=null&&t.Items&&this.addData_Invoice(t,0);this._tblInvoices.resizeColumns();this.dataCallFinished()},addData_Invoice:function(n,t){var i,r,u,f;this._blnCancelAddingData||(t<n.Items.length?(i=n.Items[t],r=[$RGT_nubButton_Invoice(i.ID,i.No),i.Date,i.Value,$RGT_nubButton_SalesOrder(i.SONo,i.SO),$R_FN.setCleanTextValue(i.CustomerPO)],this._tblInvoices.addRow(r,i.ID,!1),i=null,t+=1,u=this._element.id,f=function(){$find(u).addData_Invoice(n,t)},setTimeout(f,0)):(this._aryAlreadyGotTabData[this._objTabIndexes.Invoices]=!0,this._tblInvoices.resizeColumns()))},getTabData_CreditNote:function(){this.doDataCall("GetCreditNoteDate",Function.createDelegate(this,this.getTabDataComplete_CreditNote))},getTabDataComplete_CreditNote:function(n){var t=n._result;this._tblCreditNotes.clearTable();this._blnCancelAddingData=!1;t!=null&&t.Items&&this.addData_CreditNote(t,0);this._tblCreditNotes.resizeColumns();this.dataCallFinished()},addData_CreditNote:function(n,t){var i,r,u,f;this._blnCancelAddingData||(t<n.Items.length?(i=n.Items[t],r=[$RGT_nubButton_CreditNote(i.ID,i.No),i.Date,$RGT_nubButton_Contact(i.ContactNo,i.Contact),i.Value,$R_FN.setCleanTextValue(i.Raiser),$RGT_nubButton_SalesOrder(i.SONo,i.SO)],this._tblCreditNotes.addRow(r,i.ID,!1),i=null,t+=1,u=this._element.id,f=function(){$find(u).addData_CreditNote(n,t)},setTimeout(f,0)):(this._aryAlreadyGotTabData[this._objTabIndexes.CreditNotes]=!0,this._tblCreditNotes.resizeColumns()))},getTabData_DebitNote:function(){this.doDataCall("GetDebitNoteData",Function.createDelegate(this,this.getTabDataComplete_DebitNote))},getTabDataComplete_DebitNote:function(n){var t=n._result;this._tblDebitNotes.clearTable();this._blnCancelAddingData=!1;t!=null&&t.Items&&this.addData_DebitNote(t,0);this._tblDebitNotes.resizeColumns();this.dataCallFinished()},addData_DebitNote:function(n,t){var i,r,u,f;this._blnCancelAddingData||(t<n.Items.length?(i=n.Items[t],r=[$RGT_nubButton_DebitNote(i.ID,i.No),$RGT_nubButton_Contact(i.ContactNo,i.Contact),i.Date,i.Value,$R_FN.setCleanTextValue(i.Raiser),$RGT_nubButton_PurchaseOrder(i.PONo,i.PO)],this._tblDebitNotes.addRow(r,i.ID,!1),i=null,t+=1,u=this._element.id,f=function(){$find(u).addData_DebitNote(n,t)},setTimeout(f,0)):(this._aryAlreadyGotTabData[this._objTabIndexes.DebitNotes]=!0,this._tblDebitNotes.resizeColumns()))}};Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyTransactions.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyTransactions",Rebound.GlobalTrader.Site.Controls.Nuggets.Base);