﻿-- Drop the procedure if it already exists
IF OBJECT_ID('dbo.usp_UpsertLyticaAPI', 'P') IS NOT NULL
BEGIN
    DROP PROCEDURE dbo.usp_UpsertLyticaAPI;
END

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-208862]		Trung Pham			15-Jul-2024		CREATE			Refresh lytica data when create requirements/Upload BOM
===========================================================================================
*/

CREATE PROCEDURE [dbo].[usp_UpsertLyticaAPI]
(
@JSON VARCHAR(MAX),
@UpdatedBy INT
)    
AS    
BEGIN
	SELECT ROW_NUMBER() OVER (ORDER BY (SELECT 1)) AS RowNum, commodity, mpn, manufacturer, avgPrice, targetPrice, marketLeading,
	alternateParts, lifecycle, lifecycleStatus, overallRisk, mpnBreadth, mfrBreadth, dueDiligence, mpnConcentration , 'N' as flag 
	INTO #temp1
	FROM OPENJSON(@JSON)
	WITH (
	commodity VARCHAR(200) '$.commodity',
	mpn VARCHAR(200) '$.mpn',
	manufacturer VARCHAR(200) '$.manufacturer',
	avgPrice FLOAT '$.avgPrice',
	targetPrice FLOAT '$.targetPrice',
	marketLeading FLOAT '$.marketLeading',
	alternateParts NVARCHAR(max) '$.alternateParts' AS JSON,
	lifecycle VARCHAR(200) '$.lifecycle',
	lifecycleStatus VARCHAR(200) '$.lifecycleStatus',
	overallRisk VARCHAR(200) '$.overallRisk',
	mpnBreadth VARCHAR(200) '$.mpnBreadth',
	mfrBreadth VARCHAR(200) '$.mfrBreadth',
	dueDiligence VARCHAR(200) '$.dueDiligence',
	mpnConcentration VARCHAR(200) '$.mpnConcentration')

	DECLARE @rowNum INT

	WHILE ((SELECT count(1) FROM #temp1 WHERE flag ='N') > 0) 
	BEGIN
		SELECT top 1 @rowNum = rowNum FROM #temp1 WHERE flag = 'N' 
		DECLARE @LyticaId INT

		IF (NOT EXISTS(SELECT 1 FROM tbLyticaAPI t INNER JOIN #temp1 d ON t.OriginalPartSearched = d.mpn AND t.Manufacturer = d.manufacturer))
		BEGIN
			INSERT INTO tbLyticaAPI
			SELECT ISNULL(commodity,'N/A'), ISNULL(mpn,'N/A'), ISNULL(manufacturer,'N/A'), ISNULL(avgPrice,0), ISNULL(targetPrice,0), ISNULL(marketLeading,0), 
				ISNULL(lifecycle,'N/A'), ISNULL(lifecycleStatus,'N/A'), ISNULL(overallRisk,'N/A'), ISNULL(mpnBreadth,'N/A'),
				ISNULL(mfrBreadth,'N/A'), ISNULL(dueDiligence,'N/A'), ISNULL(mpnConcentration,'N/A'), @UpdatedBy, GETDATE(), GETDATE(), 0
				FROM #temp1 WHERE rowNum= @rowNum
			SELECT @LyticaId = SCOPE_IDENTITY()
		END
		ELSE
		BEGIN
			UPDATE tbLyticaAPI
			SET Commodity = ISNULL(t.commodity,'N/A'),
				AveragePrice = t.avgPrice,
				TargetPrice = t.targetPrice,
				MarketLeading = t.marketLeading,
				LifeCycle = ISNULL(t.lifecycle,'N/A'),
				lifeCycleStatus = ISNULL(t.lifecycleStatus,'N/A'),
				OverAllRisk = ISNULL(t.overallRisk,'N/A'),
				PartBreadth = ISNULL(t.mpnBreadth,'N/A'),
				ManufacturerBreadth = ISNULL(t.mfrBreadth,'N/A'),
				DueDiligence = ISNULL(t.dueDiligence,'N/A'),
				PartConcentration = ISNULL(t.mpnConcentration,'N/A'),
				UpdatedBy = @UpdatedBy,
				DLUP = GETDATE(),
				InActive = 0
			FROM tbLyticaAPI l
			INNER JOIN #temp1 t ON l.OriginalPartSearched = t.mpn AND l.Manufacturer = t.manufacturer
			SELECT @LyticaId = SCOPE_IDENTITY()
		END
	
		-- Insert AlternatePartList
		SELECT ROW_NUMBER() OVER (ORDER BY AlternateParts) AS RowId, AlternateParts, 'N' flag
		INTO #tempAlternatePartList 
		FROM #temp1 WHERE AlternateParts <> '[]' AND rowNum = @rowNum

		DECLARE @rowidAlt INT
		WHILE ((SELECT COUNT(1) FROM #tempAlternatePartList WHERE flag ='N') > 0)
		BEGIN
			DECLARE @json2 VARCHAR(MAX)
			SELECT TOP 1 @rowidAlt = rowid, @json2 = AlternateParts FROM #tempAlternatePartList WHERE flag = 'N'
			SELECT ROW_NUMBER() OVER (ORDER BY [key]) AS RowId, *, 'N' flag INTO #tempAlternatePart FROM OPENJSON(@json2)
			UPDATE #tempAlternatePart SET flag = 'N'

			DECLARE @rowid INT
			DECLARE @json3 VARCHAR(MAX)
			WHILE ((SELECT COUNT(1) FROM #tempAlternatePart WHERE flag ='N') > 0)
			BEGIN
				SELECT TOP 1 @rowid = rowid, @json3 = [value] FROM #tempAlternatePart WHERE flag = 'N'
				INSERT INTO tbLyticaAlternatePart
				SELECT @LyticaId, mpn, manufacturer, lifecycleStatus, @UpdatedBy, getdate(), getdate(), 0 FROM OPENJSON(@json3)
				WITH (mpn VARCHAR(200) '$.mpn', manufacturer VARCHAR(200) '$.manufacturer', lifecycleStatus VARCHAR(200) '$.lifecycleStatus')

				UPDATE #tempAlternatePart SET flag = 'Y' WHERE RowId = @rowid
			END

			DROP TABLE #tempAlternatePart
			UPDATE #tempAlternatePartList SET flag = 'Y' WHERE RowId = @rowidAlt
		END

		DROP TABLE #tempAlternatePartList
		UPDATE #temp1 SET flag='Y' WHERE RowNum = @rowNum
	END
	DROP TABLE #temp1
END