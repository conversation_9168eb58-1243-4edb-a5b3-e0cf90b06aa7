//-----------------------------------------------------------------------------------------
// RP 23.12.2009:
// - render state on server
//-----------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.DataListNuggets
{
    public partial class SalesOrdersShip : Base
    {

        #region Properties

        private bool _blnShowAllOrders = false;
        public bool ShowAllOrders
        {
            get { return _blnShowAllOrders; }
            set { _blnShowAllOrders = value; }
        }


        private bool _IsGlobalLogin = false;
        public bool IsGlobalLogin
        {
            get { return _IsGlobalLogin; }
            set { _IsGlobalLogin = value; }
        }
        #endregion

        #region Overrides

        /// <summary>
        /// OnInit
        /// </summary>
        /// <param name="e"></param>
        protected override void OnInit(EventArgs e)
        {
            SetDataListNuggetType("SalesOrdersShip");
            base.OnInit(e);
            TitleText = Functions.GetGlobalResource("Nuggets", "SalesOrdersShip");
            AddScriptReference("Controls.DataListNuggets.SalesOrdersShip.SalesOrdersShip");
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.DataListNuggets.SalesOrdersShip", ctlDesignBase.ClientID);
            IsGlobalLogin = SessionManager.IsGlobalUser.Value;
            SetupTable();
        }

        protected override void OnPreRender(EventArgs e)
        {
            base.OnPreRender(e);
            _scScriptControlDescriptor.AddProperty("blnShowAllOrders", _blnShowAllOrders);
            _scScriptControlDescriptor.AddProperty("IsGlobalLogin", _IsGlobalLogin);
        }

        protected override void RenderAdditionalState()
        {
            int intTab = 0;
            if (string.IsNullOrEmpty(_objQSManager.SearchPartNo))
            {
                var strCallType = this.GetSavedStateValue("CallType").ToUpper();
                if (string.IsNullOrEmpty(strCallType)) strCallType = "READY";
                if (strCallType == "READY") intTab = 0;
                if (strCallType == "ALL") intTab = 1;
            }
            this._blnShowAllOrders = (intTab == 1);
            ((Pages.Content)Page).CurrentTab = intTab;
            this.OnAskPageToChangeTab();
            base.RenderAdditionalState();
        }

        #endregion

        private void SetupTable()
        {
            _tbl.AllowSelection = false;
            _tbl.SortColumnDirection = Rebound.GlobalTrader.Site.Enumerations.SortColumnDirection.DESC;
            _tbl.Columns.Add(new FlexiDataColumn("SalesOrder", WidthManager.GetWidth(WidthManager.ColumnWidth.SystemDocumentNumber), true));
            _tbl.Columns.Add(new FlexiDataColumn("PartNo", "CustomerPart", WidthManager.GetWidth(WidthManager.ColumnWidth.PartNo), true));
            _tbl.Columns.Add(new FlexiDataColumn("QuantityAllocated", WidthManager.GetWidth(WidthManager.ColumnWidth.Quantity), true));
            _tbl.Columns.Add(new FlexiDataColumn("Company", "CustomerPurchaseOrderNo", WidthManager.GetWidth(WidthManager.ColumnWidth.CompanyName), true));
            _tbl.Columns.Add(new FlexiDataColumn("DateOrdered", "DatePromised", WidthManager.GetWidth(WidthManager.ColumnWidth.Date), true));
            _tbl.Columns.Add(new FlexiDataColumn("DateRequired", "ShipAsap", WidthManager.GetWidth(WidthManager.ColumnWidth.Date), true));
            _tbl.Columns.Add(new FlexiDataColumn("ShipStatus", WidthManager.GetWidth(WidthManager.ColumnWidth.Quantity), false));
            _tbl.Columns.Add(new FlexiDataColumn("ExportApprovalStatus", WidthManager.GetWidth(WidthManager.ColumnWidth.Quantity), false));
            _tbl.Columns.Add(new FlexiDataColumn("SOLineNumber"));
            if (_IsGlobalLogin == true)
            {
                _tbl.Columns.Add(new FlexiDataColumn("Client", WidthManager.GetWidth(WidthManager.ColumnWidth.CompanyName), true));
            }
        }

    }
}
