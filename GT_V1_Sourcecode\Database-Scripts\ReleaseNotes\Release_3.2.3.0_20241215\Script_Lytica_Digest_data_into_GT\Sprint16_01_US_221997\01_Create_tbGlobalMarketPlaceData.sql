﻿/*
===========================================================================================================================  
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-221997]     Phuc Hoang		 25-Nov-2024		CREATE		Product Backlog Item 221997: Lytica - Digest data into GT
===========================================================================================================================  
*/
GO
IF OBJECT_ID('dbo.tbGlobalMarketPlaceData') IS NOT NULL
	DROP TABLE dbo.tbGlobalMarketPlaceData;
	
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[tbGlobalMarketPlaceData](
	[LyticaDataId] [int] IDENTITY(1,1) NOT NULL,
	[MPNMatched] [nvarchar](max) NULL,
	[ManufacturerMatched] [nvarchar](max) NULL,
	[LyticaCommodity] [nvarchar](max) NULL,
	[50thPercentile] [nvarchar](max) NULL,
	[MarketLeading] [nvarchar](max) NULL,
	[70thPercentile] [nvarchar](max) NULL,
	[30thPercentile] [nvarchar](max) NULL,
	[AchievedPrice] [nvarchar](max) NULL,
	[Lifecycle] [nvarchar](max) NULL,
	[OverallRisk] [nvarchar](max) NULL,
	[MPNBreadthRisk] [nvarchar](max) NULL,
	[MFRBreadthRisk] [nvarchar](max) NULL,
	[DueDiligence] [nvarchar](max) NULL,
	[MPNConcentration] [nvarchar](max) NULL,
	[LifecycleRisk] [nvarchar](max) NULL,
	[AlternativePartNumber] [nvarchar](max) NULL,
	[AlternativeManufacturer] [nvarchar](max) NULL,
	[Date] [datetime] NULL,
	[Updated] [bit] NULL,
 CONSTRAINT [PK_tbGlobalMarketPlaceData] PRIMARY KEY CLUSTERED 
(
	[LyticaDataId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO


CREATE NONCLUSTERED INDEX [tbGlobalMarketPlaceData_Date] ON [dbo].[tbGlobalMarketPlaceData]
(
	[Date] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO