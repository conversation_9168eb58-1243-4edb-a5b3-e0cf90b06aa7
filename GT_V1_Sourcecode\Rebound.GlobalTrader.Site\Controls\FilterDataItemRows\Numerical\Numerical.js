Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.FilterDataItemRows");Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.Numerical=function(n){Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.Numerical.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.Numerical.prototype={get_txt:function(){return this._txt},set_txt:function(n){this._txt!==n&&(this._txt=n)},get_ddl:function(){return this._ddl},set_ddl:function(n){this._ddl!==n&&(this._ddl=n)},addEnterPressed:function(n){$R_TXTBOX.addEnterPressedEvent(this._txt,n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.Numerical.callBaseMethod(this,"initialize");$addHandler(this._txt,"focus",Function.createDelegate(this,this.onFocus));$addHandler(this._txt,"blur",Function.createDelegate(this,this.onBlur));$addHandler(this._txt,"keyup",Function.createDelegate(this,this.onChange));$addHandler(this._ddl,"focus",Function.createDelegate(this,this.onFocus));$addHandler(this._ddl,"blur",Function.createDelegate(this,this.onBlur))},dispose:function(){this.isDisposed||(this.get_element()&&$clearHandlers(this.get_element()),this._txt&&$clearHandlers(this._txt),this._ddl&&$clearHandlers(this._ddl),this._txt=null,this._ddl=null,Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.Numerical.callBaseMethod(this,"dispose"))},onFocus:function(){this.enableField(!0)},onBlur:function(){this.enableField(this.isEntered())},onChange:function(){this.enableField(this.isEntered())},getValue:function(){return this._txt.value},getMinValue:function(){var n=$R_FN.parseComparisonToMinMax(this._ddl.value,this._txt.value);return n.Min},getMaxValue:function(){var n=$R_FN.parseComparisonToMinMax(this._ddl.value,this._txt.value);return n.Max},setValue:function(n){(typeof n=="undefined"||n==null)&&(n="");this._txt.value=n;this.enableField(this._txt.value.trim().length>0)},reset:function(){this._txt.value="";this._ddl.value=$R_ENUM$NumericalComparisonType.EqualTo;this.enableField(!1)},setComparisonType:function(n){this._ddl.value=n},isEntered:function(){return $R_FN.isEntered(this._txt.value)}};Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.Numerical.registerClass("Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.Numerical",Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.Base,Sys.IDisposable);