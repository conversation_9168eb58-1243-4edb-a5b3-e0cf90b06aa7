///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//
// RP 23.12.2009:
// - render state on server
//
// RP 16.10.2009:
// - Changes due to changes in base class
//-----------------------------------------------------------------------------------------
//Marker     Changed by      Date         Remarks
//[001]      <PERSON><PERSON>  28/08/2018    Add SalesPerson dropdown on the basis of client.
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DataListNuggets");

Rebound.GlobalTrader.Site.Controls.DataListNuggets.BOM = function(element) {
    Rebound.GlobalTrader.Site.Controls.DataListNuggets.BOM.initializeBase(this, [element]);
    this._frmConfirm = null;
    var getSelected = null;
    var getSelectedGroup = null;
    this._blnAS6081Tab = false;
};

Rebound.GlobalTrader.Site.Controls.DataListNuggets.BOM.prototype = {
    get_intCompanyID: function () { return this._intCompanyID; }, set_intCompanyID: function (v) { if (this._intCompanyID !== v) this._intCompanyID = v; },//[001]
    get_blnPOHub: function() { return this._blnPOHub; }, set_blnPOHub: function(value) { if (this._blnPOHub !== value) this._blnPOHub = value; },
    initialize: function() {
       
        this._ibtnPrint = $get(this._aryButtonIDs[0]);
        getSelected = $get("ctl00_cphMain_ctlBOMResults_ctlDB_ctl26_ddl");
        getSelectedGroup = $get("ctl00_cphMain_ctlBOMResults_ctlDB_ctl28_ddl");
        
        $find(this._aryButtonIDs[1]).getData();
        $find(this._aryButtonIDs[2]).getData();
        //this.getFieldDropDownData(this._aryButtonIDs[1]);
        $R_IBTN.enableButton(this._ibtnPrint, false);
        if (this._ibtnPrint)
            this.getFilterField("ctlClient").show(false);
        $R_IBTN.addClick(this._ibtnPrint, Function.createDelegate(this, this.showConfirmForm));
        this._table.addMultipleSelectionChanged(Function.createDelegate(this, this.selectionMade));
        this._frmConfirm = $find(this._aryFormIDs[0]);
        this._frmConfirm.addCancel(Function.createDelegate(this, this.hideConfirmForm));
        this._frmConfirm.addSaveComplete(Function.createDelegate(this, this.saveCeaseComplete));
        this._frmConfirm.addNotConfirmed(Function.createDelegate(this, this.hideConfirmForm));
        this.addSetupDataCallEvent(Function.createDelegate(this, this.setupDataCall));
        this.addGetDataOKEvent(Function.createDelegate(this, this.getDataOK));
        this.addInitCompleteEvent(Function.createDelegate(this, this.initAfterBaseIsReady));
        this._strPathToData = "controls/DataListNuggets/BOM";
        this._strDataObject = "BOM";
       
        //$find(this.getFilterField("ctlClient")._element.id).addChanged(Function.createDelegate(this, this.getSalesPersonByClient));//[001] 
        $find(this.getFilterField("ctlClient").get_id())._element.setAttribute("onchange", String.format("$find(\"{0}\").getSalesPersonByClient()", this._element.id));
        Rebound.GlobalTrader.Site.Controls.DataListNuggets.BOM.callBaseMethod(this, "initialize");
        $("#ctl00_cphMain_ctlBOMResults_ctlDB_txtLimitResults").val(50);
        $("#ctl00_cphMain_ctlBOMResults_ctlDB_pnlLinks").css('top', '-17px');
        $("#ctl00_cphMain_ctlBOMResults_ctlDB_ctl24_lblDisabled").hide();
        $("#ctl00_cphMain_ctlBOMResults_ctlDB_ctl24").hide();
        $("#ctl00_cphMain_ctlBOMResults_ctlDB_ctl28").hide();
        $("#ctl00_cphMain_ctlBOMResults_ctlDB_ctl26").hide();
    },

    initAfterBaseIsReady: function() {
        this.addPageTabChangedEvent(Function.createDelegate(this, this.pageTabChanged));
        this.updateFilterVisibility();
        this.getSalesPersonDefault();
        this.getData();
    },

    dispose: function() {
        if (this.isDisposed) return;
        this._blnPOHub = null;
        Rebound.GlobalTrader.Site.Controls.DataListNuggets.BOM.callBaseMethod(this, "dispose");
    },
    pageTabChanged: function () {
        debugger;
        this._table._intCurrentPage = 1;
        this._enmViewLevel = this._intCurrentTab;
        this.updateFilterVisibility();
        this._blnAS6081Tab = (this._intCurrentTab == 1);
        this.getData();
    },

    setupDataCall: function () {

        this._objData.addParameter("ViewLevel", this._enmViewLevel);
        this._objData.addParameter("IsAS6081Tab", this._blnAS6081Tab);
        this._objData.addParameter("MyPageSize", $("#ctl00_cphMain_ctlBOMResults_ctlDB_txtLimitResults").val());
        this._objData.addParameter("SelectedRadio", this.getRadioButtonValue());
    },

    getDataOK: function () {
        //$("#ctl00_cphMain_ctlBOMResults_ctlDB_txtLimitResults").val(this._objResult.Results[0].MyPageSize);
        for (var i = 0, l = this._objResult.Results.length; i < l; i++) {
            var row = this._objResult.Results[i];

            var aryData = [
				$RGT_nubButton_BOM(row.ID, row.Name)
                , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.ClientCode), $R_FN.setCleanTextValue(row.RequestedBy))
                , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.AssignedUser), $R_FN.setCleanTextValue(row.Quantity))
                , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Code), $R_FN.setCleanTextValue(row.Part))
				, $R_FN.setCleanTextValue(row.Company)
				, row.BOMStatus
				, row.Date
				, row.Value
			];
            //var strCSS = (row.Inactive) ? "ceased" : "";
            if (this.getRadioButtonValue() == "Detail") {
                this._table.addRow(aryData, row.CustomerRequirementId, false);
            }
            else {
                this._table.addRow(aryData, row.ID, false);
            }

            //this._table.addRow(aryData, row.ID, false, null, strCSS);
            aryData = null; row = null;
            
        }     
       
    },
    updateFilterVisibility: function() {
        this.getFilterField("ctlClient").show(this._blnPOHub);
    }
    ,
    showConfirmForm: function() {
       
        this._frmConfirm._strBOM = this._table._aryCurrentValues;
        if (GetGroupValue() == true) {
            this._frmConfirm._selectedUse = getSelectedGroup.value;
            this._frmConfirm._selectedUseName = getSelectedGroup.options[getSelectedGroup.selectedIndex].text;
            this._frmConfirm._selectedIndex = getSelectedGroup.options[getSelectedGroup.selectedIndex].value;
            this._frmConfirm._IsGroupAssignment = GetGroupValue();
            this._frmConfirm._strType = this.getRadioButtonValue();
        } else {
            this._frmConfirm._selectedUse = getSelected.value;
            this._frmConfirm._selectedUseName = getSelected.options[getSelected.selectedIndex].text;
            this._frmConfirm._selectedIndex = getSelected.options[getSelected.selectedIndex].value;
            this._frmConfirm._IsGroupAssignment = GetGroupValue();
            this._frmConfirm._strType = this.getRadioButtonValue();
        }

        this.showForm(this._frmConfirm, true);
    },
    hideConfirmForm: function() {
        this.showForm(this._frmConfirm, false);
    },
    saveCeaseComplete: function() {
        this.hideConfirmForm();
        //this._table.refresh();
        this.getData();
    }
    ,
    selectionMade: function() {
    if (this._table._arySelectedIndexes.length > 0) {
            $R_IBTN.enableButton(this._ibtnPrint, true);
        }
    },
    //start [001]
    getSalesPersonByClient: function () {
        var intClientNo = 999;
        if (this.getFilterField("ctlClient").getValue() != null)
            intClientNo = this.getFilterField("ctlClient").getValue();

        this.getFilterField("ctlClientSalesperson")._ddl._intGlobalLoginClientNo = intClientNo;
        //this.getFilterField("ctlClientSalesperson")._ddl.addParameter("GlobalLoginClientNo", this._intGlobalLoginClientNo);
        this.getFilterField("ctlClientSalesperson")._ddl.getData();
       
    },
    getSalesPersonDefault: function () {
        this.getFilterField("ctlClientSalesperson")._ddl._intGlobalLoginClientNo = 999;
        //this.getFilterField("ctlClientSalesperson")._ddl.addParameter("GlobalLoginClientNo", this._intGlobalLoginClientNo);
        this.getFilterField("ctlClientSalesperson")._ddl.getData();

    },
    getRadioButtonValue: function () {
        var result = "";
        var list = document.getElementById("ctl00_cphMain_ctlBOMResults_ctlDB_ctl21_ctlFilter_ctl11_ctl02_radHeaderDetail"); //Client ID of the radiolist
        var inputs = list.getElementsByTagName("input");
        var selected;
        for (var i = 0; i < inputs.length; i++) {
            if (inputs[i].checked) {
                selected = inputs[i];
                result = selected.value;
                break;
            }
        }
        return result;
    }
    
};
Rebound.GlobalTrader.Site.Controls.DataListNuggets.BOM.registerClass("Rebound.GlobalTrader.Site.Controls.DataListNuggets.BOM", Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base, Sys.IDisposable);
