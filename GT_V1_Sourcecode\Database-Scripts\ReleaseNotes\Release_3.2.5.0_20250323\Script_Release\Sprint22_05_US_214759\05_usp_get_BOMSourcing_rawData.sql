﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/*   
===========================================================================================  
TASK			UPDATED BY      DATE			ACTION		DESCRIPTION  
[US-214760]		An.TranTan		13-Jan-2025		Create		Get BOM Sourcing Result raw data
===========================================================================================  
*/ 
CREATE OR ALTER PROCEDURE [dbo].[usp_get_BOMSourcing_rawData] 
	@DisplayLength INT
	,@DisplayStart INT
	,@SortCol INT
	,@SortDir NVARCHAR(10)
	,@UserId INT
AS
BEGIN
	SET NOCOUNT OFF;

	DECLARE @FirstRec INT ,@LastRec INT;

	SET @FirstRec = @DisplayStart;
	SET @LastRec = @DisplayStart + @DisplayLength;

	WITH CTE_Stock
	AS (
		SELECT ROW_NUMBER() OVER (
				ORDER BY CASE 
						WHEN (
								@SortCol = 0
								AND @SortDir = 'asc'
								)
							THEN BOMSourcingDataId
						END ASC
				) AS RowNum
			,COUNT(*) OVER () AS TotalCount
			,Column1
			,Column2
			,Column3
			,Column4
			,Column5
			,Column6
			,Column7
			,Column8
			,Column9
			,Column10
			,Column11
			,Column12
			,Column13
			,Column14
			,Column15
			,Column16
			,Column17
			,Column18
			,Column19
			,Column20
			,Column21
			,Column22
			,Column23
			,Column24
			,Column25
			,Column26
			,Column27
			,Column28
			,Column29
			,Column30
			,Column31
			,CreatedDate
			,OriginalFilename
		FROM BorisGlobalTraderImports.dbo.tbBOMSourcing_tempData WITH (NOLOCK)
		WHERE CreatedBy = @UserId
		)
	SELECT RowNum
		,TotalCount
		,Column1
		,Column2
		,Column3
		,Column4
		,Column5
		,Column6
		,Column7
		,Column8
		,Column9
		,Column10
		,Column11
		,Column12
		,Column13
		,Column14
		,Column15
		,Column16
		,Column17
		,Column18
		,Column19
		,Column20
		,Column21
		,Column22
		,Column23
		,Column24
		,Column25
		,Column26
		,Column27
		,Column28
		,Column29
		,Column30
		,Column31
		,CreatedDate
		,OriginalFilename
	FROM CTE_Stock
	WHERE RowNum > @FirstRec
		AND RowNum <= @LastRec
	ORDER BY CreatedDate DESC
END


/*
EXEC usp_get_BOMSourcing_rawData
	@DisplayLength =  10
	,@DisplayStart = 0
	,@SortCol = 0
	,@SortDir ='ASC'
	,@UserId = 6670
*/
GO


