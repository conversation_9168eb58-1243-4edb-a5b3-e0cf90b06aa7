﻿using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;

namespace Rebound.GlobalTrader.Site.Controls.DropDowns {
	public partial class GlobalCurrencyList : Base {

		public bool IncludeSelected { get; set; }

		protected override void OnLoad(EventArgs e) {
            SetDropDownType("GlobalCurrencyList");
            AddScriptReference("Controls.DropDowns.GlobalCurrencyList.GlobalCurrencyList");
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.DropDowns.GlobalCurrencyList", ClientID);
			_scScriptControlDescriptor.AddProperty("blnIncludeSelected", IncludeSelected);
			base.OnLoad(e);
		}

	}
}