﻿using System;
using System.Data;
using System.Web;
using System.Web.Services;
using System.Collections.Generic;
using System.Text;
using Rebound.GlobalTrader.BLL;
using System.IO;
using System.Data.OleDb;
using System.Web.Script.Serialization;
using System.Text.RegularExpressions;
using System.Linq;
//[001] add namespace for Azure Blob storage
using Microsoft.Azure;
using Microsoft.Azure.Storage;
using Microsoft.Azure.Storage.Blob;
using Microsoft.Azure.KeyVault;
using Microsoft.Azure.Storage.Shared;
using Microsoft.Azure.Storage.Auth;
using Microsoft.Azure.Storage.Auth.Protocol;
using Microsoft.Azure.Storage.RetryPolicies;
using Microsoft.Azure.KeyVault.Core;
using System.Net;
//[001]
using System.Configuration;
using Rebound.GlobalTrader.Site.Code.Common;
using System.Collections.Concurrent;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets.Data
{
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    /// <summary>
    /// Summary description for BOMImportSourcingResult
    /// </summary>
    public class BOMImportSourcingResult : Rebound.GlobalTrader.Site.Data.Base
    {
        public override void ProcessRequest(HttpContext context)
        {
            if (base.init(context))
            {

                if (string.IsNullOrEmpty(Action))
                {
                    Action = context.Request.QueryString["action"];
                }
                switch (Action)
                {
                    case "ImportExcelData": ImportExcelData(context); break;
                    case "GetBomImportNotMatchDataLeft": GetBomImportNotMatchDataLeft(context); break;
                    case "GetBomImportNotMatchDataRight": GetBomImportNotMatchDataRight(context); break;
                    case "DeleteLeftAndRightRecord": DeleteLeftAndRightRecord(context); break;
                    case "ReProcessNotMatchData": ReProcessNotMatchData(context); break;

                    case "SaveUpdateRecord": SaveUpdateRecord(context); break;
                    case "SaveUpdateSourcingRecord": SaveUpdateSourcingRecord(context); break;
                    case "GetMappedColumn": GetMappedColumn(context); break;
                    case "GetManufacturer": GetManufacturer(context); break;
                    case "UpdateCell": UpdateCell(context); break;
                    case "GetSuppliers": GetSuppliers(context); break;
                    case "UpdateRightRecordQuotedMfrCell": UpdateRightRecordQuotedMfrCell(context); break;
                    case "UpdateLeftRecordMfrCell": UpdateLeftRecordMfrCell(context); break;


                    default: WriteErrorActionNotFound(); break;
                }
            }
        }
        private void UpdateCell(HttpContext context)
        {
            try
            {
                string json = new StreamReader(context.Request.InputStream).ReadToEnd();
                json = HttpUtility.HtmlDecode(json);
                DataTable dt = (new ConvertJsonStringToDataTable()).JsonStringToDataTable(json);
                CustomerRequirement.UpdateCell(dt, SessionManager.LoginID ?? 0);
            }
            catch (Exception)
            {

            }
        }
        protected void GetManufacturer(HttpContext context)
        {
            List<Manufacturer> lst = null;
            try
            {
                lst = Manufacturer.AutoSearch(GetFormValue_StringForNameSearch("search"), false);//GetFormValue_NullableBoolean("blnShowInactive")  GetFormValue_Boolean("ShowInactive")
                DataTable dt = createTable();
                for (int i = 0; i < lst.Count; i++)
                {

                    dt.Rows.Add(lst[i].ManufacturerId.ToString(), lst[i].ManufacturerName.ToString());

                }
                context.Response.Write(ConvertDataTableToJSON(dt));

            }
            catch (Exception e)
            {
                WriteError(e);
            }
            finally
            {
                lst = null;
            }
        }

        protected void GetSuppliers(HttpContext context)
        {

            List<Company> lst = null;
            try
            {
                lst = Company.AutoSearchForAllSuppliers(SessionManager.ClientID, GetFormValue_StringForNameSearch("search"));
                DataTable dt = createTable();
                for (int i = 0; i < lst.Count; i++)
                {

                    dt.Rows.Add(lst[i].CompanyId.ToString(), lst[i].CompanyName.ToString());

                }
                context.Response.Write(ConvertDataTableToJSON(dt));

            }
            catch (Exception e)
            {
                WriteError(e);
            }
            finally
            {
                lst = null;
            }
        }

        private DataTable createTable()
        {
            DataTable dt = new DataTable();
            dt.Columns.Add("Id", typeof(string));
            dt.Columns.Add("Text", typeof(string));
            return dt;
        }
        public void GetMappedColumn(HttpContext context)
        {
            try
            {
                int bomId = string.IsNullOrEmpty(context.Request.QueryString["BOMId"]) ? 0 : int.Parse(context.Request.QueryString["BOMId"]);
                DataTable dtResult = CustomerRequirement.GetMappedColumn(bomId, SessionManager.LoginID ?? 0);

                context.Response.Write(ConvertDataTableToJSON(dtResult));

            }
            catch (Exception ex)
            {
                WriteError(ex);
            }
        }
        private string ConvertDataTableToJSON(DataTable dt)
        {
            System.Web.Script.Serialization.JavaScriptSerializer serializer = new System.Web.Script.Serialization.JavaScriptSerializer();
            List<ConcurrentDictionary<string, object>> rows = new List<ConcurrentDictionary<string, object>>();
            ConcurrentDictionary<string, object> row;
            foreach (DataRow dr in dt.Rows)
            {
                row = new ConcurrentDictionary<string, object>();
                foreach (DataColumn col in dt.Columns)
                {
                    row.TryAdd(col.ColumnName, dr[col]);
                }
                rows.Add(row);
            }
            return serializer.Serialize(rows);
        }
        private void SaveUpdateRecord(HttpContext context)
        {
            try
            {
                int tempid = 0;
                string manufacturename = "";
                int bomid = 0;
                int clientid = 0;
                tempid = int.Parse(context.Request.QueryString["tempid"]);
                manufacturename = context.Request.QueryString["manufacturename"];
                bomid = int.Parse(context.Request.QueryString["bomid"]);
                clientid = int.Parse(context.Request.QueryString["clientid"]);
                //string json = new StreamReader(context.Request.InputStream).ReadToEnd();
                //json = HttpUtility.HtmlDecode(json);
                //DataTable dt = (new ConvertJsonStringToDataTable()).JsonStringToDataTable(json);
                CustomerRequirement.SaveUpdateTempCusReqData(tempid, bomid, clientid, manufacturename);
            }
            catch (Exception)
            {

            }
        }
        private void SaveUpdateSourcingRecord(HttpContext context)
        {
            try
            {
                int sourcingresultid = 0;
                string manufacturename = "";
                string suppliername = "";
                int clientid = 0;
                sourcingresultid = int.Parse(context.Request.QueryString["sourcingid"]);
                manufacturename = context.Request.QueryString["manufacturename"];
                suppliername = context.Request.QueryString["suppliername"];
                clientid = int.Parse(context.Request.QueryString["clientid"]);
                //string json = new StreamReader(context.Request.InputStream).ReadToEnd();
                //json = HttpUtility.HtmlDecode(json);
                //DataTable dt = (new ConvertJsonStringToDataTable()).JsonStringToDataTable(json);

                CustomerRequirement.SaveUpdateTempSourcingData(sourcingresultid, suppliername, clientid, manufacturename);
            }
            catch (Exception)
            {

            }
        }
        #region Multi Vender Bom Import Tool
        public DataTable ReadCsvFile(string filepath, string chkhead, string FileName)
        {
            DataTable dtCsv = new DataTable();
            dtCsv.Clear();
            Regex CSVParser = new Regex(",(?=(?:[^\"]*\"[^\"]*\")*(?![^\"]*\"))");
            try
            {
                string Fulltext;
                WebClient web = new WebClient();
                System.IO.Stream stream = web.OpenRead(filepath);
                using (StreamReader sr = new StreamReader(stream, Encoding.Default))
                {
                    while (!sr.EndOfStream)
                    {
                        Fulltext = sr.ReadToEnd().ToString(); //read full file text  
                        string[] rows = Fulltext.Split('\n'); //split full file text into rows  
                        if (chkhead == "YES")
                        {
                            for (int i = 0; i < rows.Length - 1; i++)
                            {
                                // string[] rowValues = rows[i].Split(','); //split each row with comma to get individual values  
                                string[] rowValues = CSVParser.Split(rows[i]); //split each row with comma to get individual values  
                                {
                                    if (i == 0)
                                    {
                                        for (int j = 0; j < rowValues.Length; j++)
                                        {

                                            // dtCsv.Columns.Add(Functions.CleanDatabaseFilter(Functions.ReplaceLineBreaks(Functions.CleanJunkCharInCSV(Functions.FormatStringForDatabase((rowValues[j].ToString()))))));
                                            dtCsv.Columns.Add(rowValues[j]); //add headers  


                                        }

                                    }
                                    else
                                    {

                                        DataRow dr = dtCsv.NewRow();
                                        for (int k = 0; k < rowValues.Length; k++)
                                        {

                                            //dr[k] =Functions.CleanJunkCharInCSV(Functions.CleanDatabaseForStockTool((rowValues[k].ToString())));// Functions.CleanJunkCharInCSV((rowValues[k].ToString()));
                                            dr[k] = Functions.FormatStringForDatabase(Functions.CleanDatabaseForStockTool(Functions.CleanJunkCharInCSV((rowValues[k].ToString()))));
                                        }
                                        dtCsv.Rows.Add(dr); //add other rows 

                                    }
                                }
                            }
                        }
                        else
                        {
                            for (int i = 0; i < rows.Length - 1; i++)
                            {
                                //string[] rowValues = rows[i].Split(','); //split each row with comma to get individual values  
                                string[] rowValues = CSVParser.Split(rows[i]); //split each row with comma to get individual values  
                                {
                                    if (i == 0)
                                    {
                                        int counter = 1;

                                        for (int j = 0; j < rowValues.Length; j++)
                                        {
                                            dtCsv.Columns.Add("F" + counter); //Add header if not have header like F1
                                            counter++;

                                        }
                                        DataRow dr = dtCsv.NewRow();
                                        for (int k = 0; k < rowValues.Length; k++)
                                        {

                                            dr[k] = Functions.CleanJunkCharInCSV(Functions.CleanDatabaseForStockTool((rowValues[k].ToString())));
                                            //dr[k] = Functions.FormatStringForDatabase(Functions.CleanDatabaseForStockTool(Functions.CleanJunkCharInCSV((rowValues[k].ToString()))));
                                        }
                                        dtCsv.Rows.Add(dr); //add other rows 

                                    }
                                    else
                                    {

                                        DataRow dr = dtCsv.NewRow();
                                        for (int k = 0; k < rowValues.Length; k++)
                                        {

                                            dr[k] = Functions.CleanJunkCharInCSV(Functions.CleanDatabaseForStockTool((rowValues[k].ToString())));
                                            //dr[k] = Functions.FormatStringForDatabase(Functions.CleanDatabaseForStockTool(Functions.CleanJunkCharInCSV((rowValues[k].ToString()))));
                                        }
                                        dtCsv.Rows.Add(dr); //add other rows 

                                    }
                                }
                            }
                        }
                    }
                }
                //Vinay: 05 May 2021: Dispose unused object
                stream.Dispose();
                web.Dispose();
                web = null;
                CSVParser = null;
                string Deletetempfolderfile = FileUploadManager.GetTemporaryUploadFilePath() + FileName;
                System.IO.File.Delete(Deletetempfolderfile);
            }
            catch (Exception)
            {
                string Deletetempfolderfile = FileUploadManager.GetTemporaryUploadFilePath() + FileName;
                System.IO.File.Delete(Deletetempfolderfile);
                throw new Exception("Error Occured while processing the csv file.Kindly review the data in the csv file.", new Exception("CSVDataError"));
            }
            return dtCsv;
        }
        public DataTable ConvertExcelToDataTable(string FilePath, string chkhead,string FileName)
        {
            DataTable dt = new DataTable();
            FileInfo fi = new FileInfo(FilePath);
            try
            {
                if (fi.Exists)
                {
                    string Extension = Path.GetExtension(FilePath);
                    var connectionString = "";
                    // connectionString = "Provider=Microsoft.ACE.OLEDB.12.0;Data Source=" + FilePath + ";" + "Extended Properties='Excel 12.0 Xml;HDR=" + chkhead + ";IMEX=1;MAXSCANROWS=0'";
                    switch (Extension)
                    {
                        case ".xls": //Excel 97-03

                            connectionString = "Provider=Microsoft.Jet.OLEDB.4.0;Data Source=" + FilePath + ";" + "Extended Properties='Excel 8.0;HDR=" + chkhead + ";'";
                            break;
                        case ".xlsx": //Excel 07
                            connectionString = "Provider=Microsoft.ACE.OLEDB.12.0;Data Source=" + FilePath + ";" + "Extended Properties='Excel 12.0 Xml;HDR=" + chkhead + ";IMEX=1;MAXSCANROWS=0'";
                            break;
                    }
                    //var connectionString = "Provider=Microsoft.ACE.OLEDB.12.0;Data Source=" + FilePath + ";" + "Extended Properties='Excel 12.0 Xml;HDR=YES;IMEX=1;MAXSCANROWS=0'";
                    using (var conn = new OleDbConnection(connectionString))
                    {
                        conn.Open();

                        var sheets = conn.GetOleDbSchemaTable(System.Data.OleDb.OleDbSchemaGuid.Tables, new object[] { null, null, null, "TABLE" });
                        using (var cmd = conn.CreateCommand())
                        {
                            cmd.CommandText = "SELECT * FROM [" + sheets.Rows[0]["TABLE_NAME"].ToString() + "] ";

                            var adapter = new OleDbDataAdapter(cmd);

                            adapter.Fill(dt);
                        }

                    }
                    string Deletetempfolderfile = FileUploadManager.GetTemporaryUploadFilePath() + FileName;
                    System.IO.File.Delete(Deletetempfolderfile);
                }

            }
            catch (Exception)
            {
                throw new Exception("Error Occured while processing the excel file.Kindly review the data in the excel file.", new Exception("ExcelDataError"));
            }
            return dt;
        }
        public DataTable ConvertExcelToDataTableNew(string FilePath, string chkhead, string FileName)
        {
            DataTable dt = new DataTable();
            FileInfo fi = new FileInfo(FilePath);
            try
            {
                if (fi.Exists)
                {
                    List<string> sheets = ExcelAdapter.GetSheet(FilePath);
                    if (sheets.Count > 0)
                        dt = ExcelAdapter.ReadExcel(FilePath, sheets[0]);

                }
                string Deletetempfolderfile = FileUploadManager.GetTemporaryUploadFilePath() + FileName;
                System.IO.File.Delete(Deletetempfolderfile);

            }
            catch (Exception)
            {
                string Deletetempfolderfile = FileUploadManager.GetTemporaryUploadFilePath() + FileName;
                System.IO.File.Delete(Deletetempfolderfile);
                throw new Exception("Error Occured while processing the excel file.Kindly review the data in the excel file.", new Exception("ExcelDataError"));
            }
            return dt;
        }
        int filelogid = 0;
        bool fileformate = true;
        public void ImportExcelData(HttpContext context)
        {
            try
            {
                int bomId = int.Parse(string.IsNullOrEmpty(GetFormValue_String("BOMId")) ? "0" : GetFormValue_String("BOMId"));
                string originalFilename = GetFormValue_String("originalFilename");
                DataTable dtFileExitOrNot = Stock.GetStatusFileExitOrNot(bomId, SessionManager.ClientID ?? 0, SessionManager.LoginID ?? 0, originalFilename);
                bool exists = dtFileExitOrNot.Select().ToList().Exists(row => row["DublicateCount"].ToString().ToUpper() == "0");
                if (exists == true)
                {

                    string generatedFilename = GetFormValue_String("generatedFilename");
                    string chkcolumnheader = GetFormValue_String("ColumnHeader");
                    int clientType_con = GetFormValue_Int("SelectedClientType");
                    string filepathtempfolder = FileUploadManager.GetTemporaryUploadFilePath() + generatedFilename;
                    //string filepath = string.Format(Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["ExcelImportSourcePhysicalURL"])) + @"BOM\" + generatedFilename;
                    string filepath = ConfigurationManager.AppSettings["ExcelDocumentPhysicalURLForAzureBlob"].ToString() + @"BOM/" + generatedFilename;
                    // String strorageconn = ConfigurationManager.AppSettings.Get("StorageConnectionString");
                    //  CloudStorageAccount storageacc = CloudStorageAccount.Parse(strorageconn);
                    //  CloudBlobClient client = storageacc.CreateCloudBlobClient();
                    string accountname = ConfigurationManager.AppSettings.Get("StorageName");
                    string accesskey = ConfigurationManager.AppSettings.Get("StorageKey1");
                    StorageCredentials creden = new StorageCredentials(accountname, accesskey);
                    CloudStorageAccount acc = new CloudStorageAccount(creden, useHttps: true);
                    CloudBlobClient client = acc.CreateCloudBlobClient();
                    CloudBlobContainer cont = client.GetContainerReference("gtdocmgmt");
                    if (cont.Exists())
                    {
                        CloudBlobDirectory directory = cont.GetDirectoryReference("BOM");
                        CloudBlockBlob cblob = directory.GetBlockBlobReference(GetFormValue_String("generatedFilename"));
                        if (cblob.Exists())
                        {
                            string fileExtension = Path.GetExtension(filepath);
                            //DataTable dt = fileExtension == ".csv" ? ReadCsvFile(filepath, chkcolumnheader) : ConvertExcelToDataTable(filepathtempfolder, chkcolumnheader, generatedFilename);
                            //DataTable dt = fileExtension == ".csv" ? ReadCsvFile(filepath, chkcolumnheader) : ConvertExcelToDataTableNew(filepathtempfolder, chkcolumnheader, generatedFilename);
                            DataTable dt = fileExtension == ".csv" ? ReadCsvFile(filepathtempfolder, chkcolumnheader, generatedFilename) : ConvertExcelToDataTableNew(filepathtempfolder, chkcolumnheader, generatedFilename);
                            DataColumnCollection col = dt.Columns;
                            
                            foreach (var column in dt.Columns.Cast<DataColumn>().ToArray())
                            {
                                if (column.ColumnName.Equals(""))
                                {
                                    dt.Columns.Remove(column);
                                }
                                if (column.ColumnName.Equals("Column1"))
                                {
                                    dt.Columns.Remove(column);
                                }
                                if (column.ColumnName.Equals("Column2"))
                                {
                                    dt.Columns.Remove(column);
                                }
                                if (column.ColumnName == " QTY ")
                                {
                                    dt.Columns[" QTY "].ColumnName = "QTY";
                                }
                                if (column.ColumnName== "  Price  ")
                                {
                                    dt.Columns["  Price  "].ColumnName = "Price";
                                }
                                if (column.ColumnName== " Cost $ ")
                                {
                                    dt.Columns[" Cost $ "].ColumnName = "Cost $";
                                }
                                if (column.ColumnName== " SPQ ")
                                {
                                    dt.Columns[" SPQ "].ColumnName = "SPQ";
                                }
                                if (column.ColumnName== " MOQ ")
                                {
                                    dt.Columns[" MOQ "].ColumnName = "MOQ";
                                }
                                if (column.ColumnName== " Pack ")
                                {
                                    dt.Columns[" Pack "].ColumnName = "Pack";
                                }
                                if (column.ColumnName== "Vendor")
                                {
                                    dt.Columns["Vendor"].ColumnName = "Vendor";
                                }
                               
                            }
                            if (!col.Contains("Mfr"))
                            {
                                fileformate = false;
                            }
                            if (!col.Contains("Part"))
                            {
                                fileformate = false;
                            }
                            if (!col.Contains("QTY"))
                            {
                                fileformate = false;
                            }
                            if (!col.Contains("Price"))
                            {
                                fileformate = false;
                            }
                            if (!col.Contains("Quoted MPN"))
                            {
                                fileformate = false;
                            }
                            if (!col.Contains("Quoted MFR"))
                            {
                                fileformate = false;
                            }
                            if (!col.Contains("Cost $"))
                            {
                                fileformate = false;
                            }
                            if (!col.Contains("Lead Time"))
                            {
                                fileformate = false;
                            }
                            if (!col.Contains("SPQ"))
                            {
                                fileformate = false;
                            }
                            if (!col.Contains("MOQ"))
                            {
                                fileformate = false;
                            }
                            if (!col.Contains("Pack"))
                            {
                                fileformate = false;
                            }
                            if (!col.Contains("Vendor"))
                            {
                                fileformate = false;
                            }
                            if (!col.Contains("IF"))
                            {
                                fileformate = false;
                            }
                            if (!col.Contains("%"))
                            {
                                fileformate = false;
                            }
                            if (!col.Contains("Quoted Cost"))
                            {
                                fileformate = false;
                            }
                            if (!col.Contains("Date Quoted"))
                            {
                                fileformate = false;
                            }
                            if (fileExtension != ".csv")
                            {
                                if (!col.Contains("Project / Site"))
                                {
                                    fileformate = false;
                                }
                            }
                            else
                            {
                                if (!col.Contains("Project / Site\r"))
                                    fileformate = false;
                                else
                                    dt.Columns["Project / Site\r"].ColumnName = "ProjectSite";

                            }
                            if (fileformate == true)
                            {

                                string[] selectedColumns1 = new[] { "Mfr", "Part", "QTY", "Price" };
                                DataTable dt1 = new DataView(dt).ToTable(false, selectedColumns1);
                                dt.Columns[16].ColumnName = "ProjectSite";
                                string[] selectedColumns2 = new[] { "Quoted MPN", "Quoted MFR", "Cost $", "Lead Time", "SPQ", "MOQ", "Pack", "Vendor", "IF", "%", "Quoted Cost", "Date Quoted", "ProjectSite" };
                                DataTable dt2 = new DataView(dt).ToTable(false, selectedColumns2);
                                this.LeftSideExcelSourcingResule(dt1, originalFilename, generatedFilename, bomId);
                                this.RightSideExcelSourcingResule(dt2, originalFilename, generatedFilename, bomId);
                                Stock.BomReProcessData(bomId, SessionManager.LoginID ?? 0, SessionManager.ClientID ?? 0);
                            }
                            else
                            {
                                throw new Exception("Uploaded file not matched with required format. Kindly upload the correct file.", new Exception("FileNotFound"));

                            }

                            JsonObject jsn = new JsonObject();
                            jsn.AddVariable("FileLogId", filelogid);
                            jsn.AddVariable("Result", true);
                            OutputResult(jsn);
                            jsn.Dispose(); jsn = null;
                        }
                    }
                    else
                    {
                        throw new Exception("File not uploaded successfully.", new Exception("FileNotFound"));

                    }
                    //Vinay: 05 May 2021: Dispose unused object
                    cont = null;
                    client = null;
                    acc = null;
                }
                else
                {
                    throw new Exception("This file allready uploaded. Kindly try with diffrent file.", new Exception("FileNotFound"));

                }
            }
            catch (Exception ex)
            {
                WriteError(ex);

            }
        }
        private void LeftSideExcelSourcingResule(DataTable dtData, string originalFilename, string generatedFilename, int bomId)
        {
            try
            {
                DataTable tempEpo = new DataTable("BorisGlobalTrader.dbo.tbCustReqToBeImported_Temp");
                // Copy the DataTable to SQL Server using SqlBulkCopy
                BLL.Stock.LeftSideExcelSourcingResuleBulkSave(tempEpo, dtData, originalFilename, generatedFilename, SessionManager.LoginID ?? 0, SessionManager.ClientID ?? 0, bomId);
            }
            catch (Exception ex)
            {
                WriteError(ex);
            }



        }
        private void RightSideExcelSourcingResule(DataTable dtData, string originalFilename, string generatedFilename, int bomId)
        {
            try
            {
                DataTable tempEpo = new DataTable("BorisGlobalTrader.dbo.tbSourcingOffersToBeImported_Temp");
                // Copy the DataTable to SQL Server using SqlBulkCopy
                BLL.Stock.RightSideExcelSourcingResuleBulkSave(tempEpo, dtData, originalFilename, generatedFilename, SessionManager.LoginID ?? 0, SessionManager.ClientID ?? 0, bomId);
            }
            catch (Exception ex)
            {
                WriteError(ex);
            }



        }
        public void GetBomImportNotMatchDataLeft(HttpContext context)
        {

            try
            {

                int displayLength = int.Parse(context.Request.Params["Length"]);
                int displayStart = int.Parse(context.Request.Params["Start"]);
                int sortCol = 0;// int.Parse(context.Request.Params["order[0][column]"]);
                string sortDir = "asc";// context.Request.Params["order[0][dir]"];
                string search = context.Request.Params["search[value]"];
                int bomId = string.IsNullOrEmpty(context.Request.QueryString["BOMId"]) ? 0 : int.Parse(context.Request.QueryString["BOMId"]);
                DataTable dtLeftSideResult = Stock.GetLeftSideExcelSourcingResult(displayLength, displayStart, sortCol, sortDir, search, SessionManager.ClientID ?? 0, SessionManager.LoginID ?? 0, bomId);
                int total = Convert.ToInt32(dtLeftSideResult.Rows.Count == 0 ? "0" : dtLeftSideResult.Rows[0]["totalcount"].ToString());
                context.Response.Write(DataTableToJsonObj(dtLeftSideResult, total, total));
            }
            catch (Exception ex)
            {
                WriteError(ex);
            }
        }
        public void GetBomImportNotMatchDataRight(HttpContext context)
        {

            try
            {

                int displayLength = int.Parse(context.Request.Params["Length"]);
                int displayStart = int.Parse(context.Request.Params["Start"]);
                int sortCol = 0;// int.Parse(context.Request.Params["order[0][column]"]);
                string sortDir = "asc";// context.Request.Params["order[0][dir]"];
                string search = context.Request.Params["search[value]"];
                int bomId = string.IsNullOrEmpty(context.Request.QueryString["BOMId"]) ? 0 : int.Parse(context.Request.QueryString["BOMId"]);
                DataTable dtRightSideResult = Stock.GetRightSideExcelSourcingResult(displayLength, displayStart, sortCol, sortDir, search, SessionManager.ClientID ?? 0, SessionManager.LoginID ?? 0, bomId);
                int total = Convert.ToInt32(dtRightSideResult.Rows.Count == 0 ? "0" : dtRightSideResult.Rows[0]["totalcount"].ToString());
                context.Response.Write(DataTableToJsonObj(dtRightSideResult, total, total));
            }
            catch (Exception ex)
            {
                WriteError(ex);
            }
        }
        private string DataTableToJsonObj(DataTable dt, int iTotalRecords, int iTotalDisplayRecords)
        {
            StringBuilder JsonString = new StringBuilder();
            if (dt != null && dt.Rows.Count > 0)
            {
                //JsonString.Append("[");
                JsonString.Append("{\"iTotalRecords\":" + iTotalRecords + ",");
                JsonString.Append("\"iTotalDisplayRecords\":" + iTotalDisplayRecords + ",");
                JsonString.Append("\"data\":");
                JsonString.Append("[");
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    JsonString.Append("{");
                    for (int j = 0; j < dt.Columns.Count; j++)
                    {
                        if (j < dt.Columns.Count - 1)
                        {
                            //JsonString.Append("\"" + dt.Columns[j].ColumnName.ToString() + "\":" + "\"" + dt.Rows[i][j].ToString().Replace(@"\r", "").Replace(@"\n", "").Replace(@"\", "") + "\",");
                            JsonString.Append("\"" + dt.Columns[j].ColumnName.ToString() + "\":" + "\"" + dt.Rows[i][j].ToString() + "\",");
                        }
                        else if (j == dt.Columns.Count - 1)
                        {
                            //JsonString.Append("\"" + dt.Columns[j].ColumnName.ToString() + "\":" + "\"" + dt.Rows[i][j].ToString().Replace(@"\r", "").Replace(@"\n", "").Replace(@"\", "") + "\"");
                            JsonString.Append("\"" + dt.Columns[j].ColumnName.ToString() + "\":" + "\"" + dt.Rows[i][j].ToString() + "\"");
                        }
                    }
                    if (i == dt.Rows.Count - 1)
                    {
                        JsonString.Append("}");
                    }
                    else
                    {
                        JsonString.Append("},");
                    }
                }
                JsonString.Append("]");
                JsonString.Append("}");
                //JsonString.Append("]");


            }
            else
            {
                JsonString.Append("{\"iTotalRecords\":" + iTotalRecords + ",");
                JsonString.Append("\"iTotalDisplayRecords\":" + iTotalDisplayRecords + ",");
                JsonString.Append("\"data\":");
                JsonString.Append("[]}");
            }
            return Regex.Replace(JsonString.ToString(), @"\r\n?|\n", "");
        }

        private void DeleteLeftAndRightRecord(HttpContext context)
        {
            try
            {
                int bomId = string.IsNullOrEmpty(context.Request.QueryString["BOMId"]) ? 0 : int.Parse(context.Request.QueryString["BOMId"]);
                Stock.DeleteLeftAndRightData(SessionManager.LoginID ?? 0, SessionManager.ClientID ?? 0, bomId);
            }
            catch (Exception)
            {

            }
        }
        private void UpdateRightRecordQuotedMfrCell(HttpContext context)
        {
            try
            {

                System.Int32? SourcingResultId = int.Parse(context.Request.Form["SourcingResultId"]);
                System.String updatestatus = context.Request.Form["mfrvendercellup"];
                System.String manufacturename = context.Request.Form["manufacturename"];
                System.String Vendor = context.Request.Form["Vendor"];
                Stock.updatecellvalueMfrVender(SessionManager.LoginID ?? 0, SessionManager.ClientID ?? 0, SourcingResultId, updatestatus, manufacturename, Vendor);

            }
            catch (Exception)
            {

            }
        }
        private void UpdateLeftRecordMfrCell(HttpContext context)
        {
            try
            {
                System.Int32? SourcingResultId = int.Parse(context.Request.Form["SourcingResultId"]);
                System.String manufacturename = context.Request.Form["manufacturename"];
                Stock.updatecellvalueMfr(SessionManager.LoginID ?? 0, SessionManager.ClientID ?? 0, SourcingResultId, manufacturename);
            }
            catch (Exception)
            {

            }
        }
        private void ReProcessNotMatchData(HttpContext context)
        {
            try
            {
                int bomId = string.IsNullOrEmpty(context.Request.QueryString["BOMId"]) ? 0 : int.Parse(context.Request.QueryString["BOMId"]);
                Stock.BomReProcessData(bomId, SessionManager.LoginID ?? 0, SessionManager.ClientID ?? 0);
            }
            catch (Exception)
            {

            }
        }

        #endregion

    }

}