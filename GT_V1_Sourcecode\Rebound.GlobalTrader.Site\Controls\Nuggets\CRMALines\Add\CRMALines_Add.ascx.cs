using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.Forms {
	public partial class CRMALines_Add : Base {

		#region Locals

		protected TableRow _trSelectInvoiceLine;
		protected Panel _pnlLines;
		protected Panel _pnlLinesError;
		protected Label _lblLinesError;
		protected Panel _pnlLinesLoading;
		protected Panel _pnlLinesNoneFound;
		protected Panel _pnlLinesNotAvailable;
		protected FlexiDataTable _tblLines;

		#endregion

		#region Properties

		#endregion

		#region Overrides

		/// <summary>
		/// OnInit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
			TitleText = Functions.GetGlobalResource("FormTitles", "CRMALines_Add");
			AddScriptReference("Controls.Nuggets.CRMALines.Add.CRMALines_Add.js");
		}

		/// <summary>
		/// OnPreRender
		/// </summary>
		/// <param name="e"></param>
		protected override void OnPreRender(EventArgs e) {
			SetFieldDefault("ctlReturnDate", Functions.FormatDate(Functions.GetUKLocalTime()));
			WireUpControls();
			SetupLinesTable();
			SetupScriptDescriptors();
			base.OnPreRender(e);
		}

		#endregion

		private void SetupLinesTable() {
			_tblLines.Columns.Add(new FlexiDataColumn("PartNo"));
			_tblLines.Columns.Add(new FlexiDataColumn("QuantityShipped", WidthManager.GetWidth(WidthManager.ColumnWidth.Quantity)));
			_tblLines.Columns.Add(new FlexiDataColumn("QuantityAllocated", WidthManager.GetWidth(WidthManager.ColumnWidth.Quantity)));
			_tblLines.Columns.Add(new FlexiDataColumn("QuantityRemaining", WidthManager.GetWidth(WidthManager.ColumnWidth.Quantity)));
			_tblLines.Columns.Add(new FlexiDataColumn("Price", WidthManager.GetWidth(WidthManager.ColumnWidth.CurrencyValue)));

            _tblLines.Columns.Add(new FlexiDataColumn("SalesOrderSerialNo", WidthManager.GetWidth(WidthManager.ColumnWidth.CurrencyValue)));
			_tblLines.Columns.Add(new FlexiDataColumn("InvoiceDate", WidthManager.GetWidth(WidthManager.ColumnWidth.Date)));
		}

		/// <summary>
		/// Wire up controls to the ascx
		/// </summary>
		private void WireUpControls() {
			_pnlLines = (Panel)FindContentControl("pnlLines");
			_pnlLinesNotAvailable = (Panel)FindContentControl("pnlLinesNotAvailable");
			_tblLines = (FlexiDataTable)Functions.FindControlRecursive(this, "tblLines");
			_trSelectInvoiceLine = (TableRow)FindContentControl("trSelectInvoiceLine");
			_pnlLinesError = (Panel)Functions.FindControlRecursive(_pnlLines, "pnlLinesError");
			_lblLinesError = (Label)Functions.FindControlRecursive(_pnlLines, "lblLinesError");
			_pnlLinesLoading = (Panel)Functions.FindControlRecursive(_pnlLines, "pnlLinesLoading");
			_pnlLinesNoneFound = (Panel)Functions.FindControlRecursive(_pnlLines, "pnlLinesNoneFound");
			_pnlLinesNotAvailable = (Panel)Functions.FindControlRecursive(_pnlLinesNotAvailable, "pnlLinesNotAvailable");
		}

		/// <summary>
		/// Setup Script Descriptors
		/// </summary>
		private void SetupScriptDescriptors() {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Forms.CRMALines_Add", ctlDesignBase.ClientID);
			//_scScriptControlDescriptor.AddElementProperty("ibtnSave", FindIconButton("ibtnSave").ClientID);
			_scScriptControlDescriptor.AddElementProperty("trSelectInvoiceLine", _trSelectInvoiceLine.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlLines", _pnlLines.ClientID);
			_scScriptControlDescriptor.AddComponentProperty("tblLines", _tblLines.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlLinesError", _pnlLinesError.ClientID);
			_scScriptControlDescriptor.AddElementProperty("lblLinesError", _lblLinesError.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlLinesLoading", _pnlLinesLoading.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlLinesNoneFound", _pnlLinesNoneFound.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlLinesNotAvailable", _pnlLinesNotAvailable.ClientID);
		}
	}
}
