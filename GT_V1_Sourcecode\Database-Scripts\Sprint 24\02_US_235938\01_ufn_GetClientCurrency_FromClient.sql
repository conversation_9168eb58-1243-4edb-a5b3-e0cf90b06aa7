﻿
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/*
--====================================================================================================================================
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-235938]     Phuc Hoang		 20-Mar-2025		CREATE		[PROD Bug] Wrong Price Conversion of Sourcing between Client to Client
======================================================================================================================================  
*/

CREATE OR ALTER FUNCTION [dbo].[ufn_GetClientCurrency_FromClient]  
(
 @ClientNo INT, 
 @OfferCurrencyNo INT,
 @FromClientNo INT
)  
RETURNS INT  
AS  
BEGIN  
	DECLARE @ClientCurrencyNo int  
	SELECT @ClientCurrencyNo = l.SupplierCurrencyNo    
    FROM tbCurrency c left join tbLinkMultiCurrency l   on l.GlobalCurrencyNo = c.GlobalCurrencyNo 
    WHERE l.ClientNo = @ClientNo 
		AND c.ClientNo = @FromClientNo 
		AND c.CurrencyId = @OfferCurrencyNo; 

 RETURN @ClientCurrencyNo  
  
END  
GO


