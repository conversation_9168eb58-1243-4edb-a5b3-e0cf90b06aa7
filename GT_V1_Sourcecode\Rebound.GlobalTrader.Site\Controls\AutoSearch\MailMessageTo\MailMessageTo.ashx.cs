using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.Site.Controls.AutoSearch.Data {

	[WebService(Namespace = "http://tempuri.org/")]
	[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
	public class MailMessageTo : Rebound.GlobalTrader.Site.Controls.AutoSearch.Data.Base {

		protected override void GetData() {
			List<BLL.Login> lst = null;

            int? intGlobalLoginClientNo;
            intGlobalLoginClientNo = GetFormValue_NullableInt("GlobalLoginClientNo"); //SessionManager.GlobalClientNo;

			try {
				lst = BLL.Login.AutoSearchForMail((intGlobalLoginClientNo.HasValue && intGlobalLoginClientNo.Value>0)?intGlobalLoginClientNo.Value: SessionManager.ClientID, SessionManager.LoginID, GetFormValue_String("search"));
				JsonObject jsn = new JsonObject();
				jsn.AddVariable("TotalRecords", lst.Count);
				JsonObject jsnRows = new JsonObject(true);
				for (int i = 0; i < lst.Count; i++) {
					if (i < lst.Count) {
						JsonObject jsnRow = new JsonObject();
						jsnRow.AddVariable("ID", lst[i].LoginId);
						jsnRow.AddVariable("Name", lst[i].EmployeeName);
						jsnRow.AddVariable("Type", lst[i].MailMessageAddressType);
						jsnRows.AddVariable(jsnRow);
						jsnRow.Dispose();
						jsnRow = null;
					}
				}
				jsn.AddVariable("Results", jsnRows);
				OutputResult(jsn);
				jsnRows.Dispose(); jsnRows = null;
				jsn.Dispose(); jsn = null;
			} catch (Exception e) {
				WriteError(e);
			} finally {
				lst = null;
			}
			base.GetData();
		}


	}
}