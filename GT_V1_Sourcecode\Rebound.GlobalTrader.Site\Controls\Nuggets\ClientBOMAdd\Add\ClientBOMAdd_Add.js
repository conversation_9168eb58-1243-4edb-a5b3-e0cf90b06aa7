Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.ClientBOMAdd_Add=function(n){Rebound.GlobalTrader.Site.Controls.Forms.ClientBOMAdd_Add.initializeBase(this,[n]);this._intNewID=0;this._intCompanyID=0;this._strCompanyName="";this._intGlobalCurrencyNo=-1;this._intPOCurrencyNo=-1;this._intSalesPersionID=0;this._strSalesPersionName=""};Rebound.GlobalTrader.Site.Controls.Forms.ClientBOMAdd_Add.prototype={get_intCompanyID:function(){return this._intCompanyID},set_intCompanyID:function(n){this._intCompanyID!==n&&(this._intCompanyID=n)},get_strCompanyName:function(){return this._strCompanyName},set_strCompanyName:function(n){this._strCompanyName!==n&&(this._strCompanyName=n)},get_intSalesPersionID:function(){return this._intSalesPersionID},set_intSalesPersionID:function(n){this._intSalesPersionID!==n&&(this._intSalesPersionID=n)},get_strSalesPersionName:function(){return this._strSalesPersionName},set_strSalesPersionName:function(n){this._strSalesPersionName!==n&&(this._strSalesPersionName=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.ClientBOMAdd_Add.callBaseMethod(this,"initialize");this.addCancel(Function.createDelegate(this,this.cancelClicked));this.addSave(Function.createDelegate(this,this.saveClicked));this.addShown(Function.createDelegate(this,this.formShown))},dispose:function(){this.isDisposed||(this._intNewID=null,this._intCompanyID=null,this._strCompanyName=null,this._intSalesPersionID=null,this._strSalesPersionName=null,Rebound.GlobalTrader.Site.Controls.Forms.ClientBOMAdd_Add.callBaseMethod(this,"dispose"))},cancelClicked:function(){$R_FN.navigateBack()},formShown:function(){this._intGlobalCurrencyNo=-1;this._intCompanyID>0?(this.setFieldValue("ctlCompany",this._intCompanyID,null,$R_FN.setCleanTextValue(this._strCompanyName)),this.setFieldValue("ctlSalespersion",this._intSalesPersionID,null,$R_FN.setCleanTextValue(this._strSalesPersionName)),this.getContact()):$find(this.getField("ctlCompany").ControlID)&&$find(this.getField("ctlCompany").ControlID)._aut.addSelectionMadeEvent(Function.createDelegate(this,this.getContact));this.getFieldDropDownData("ctlCurrency");this.getFieldDropDownData("ctlSalesman")},getContact:function(){this.getBOMData();this.getFieldControl("ctlContact")._intCompanyID=this.getFieldValue("ctlCompany");this.getFieldDropDownData("ctlContact")},getBOMData:function(){var n=new Rebound.GlobalTrader.Site.Data;this._strPath="controls/Nuggets/CompanySalesInfo";this._strData="CompanySalesInfo";n.set_PathToData(this._strPath);n.set_DataObject(this._strData);n.set_DataAction("GetDefaultSalesInfo");n.addParameter("id",this.getFieldValue("ctlCompany"));n.addParameter("id",this.getFieldValue("ctlSalespersion"));n.addDataOK(Function.createDelegate(this,this.getBOMDataOK));n.addError(Function.createDelegate(this,this.getBOMDataError));n.addTimeout(Function.createDelegate(this,this.getBOMDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getBOMDataOK:function(n){var t=n._result;this._intGlobalCurrencyNo=t.GlobalCurrencyNo;this._intPOCurrencyNo=t.CurrencyNo;this.bindCurrency()},bindCurrency:function(){this.getFieldControl("ctlCurrency")._intGlobalCurrencyNo=this._intGlobalCurrencyNo;this.getFieldControl("ctlCurrency")._blnIsBuy=!1;this.getFieldDropDownData("ctlCurrency");this.setFieldValue("ctlCurrency",this._intPOCurrencyNo)},getBOMDataError:function(n){this.showError(!0,n.get_ErrorMessage())},saveClicked:function(){if(this.validateForm()){this.showSaving(!0);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/ClientBOMAdd");n.set_DataObject("ClientBOMAdd");n.set_DataAction("AddNew");n.addParameter("Name",this.getFieldValue("ctlName"));n.addParameter("Company",this.getFieldValue("ctlCompany"));n.addParameter("Contact",this.getFieldValue("ctlContact"));n.addParameter("InActive",this.getFieldValue("ctlInActive"));n.addParameter("Notes",this.getFieldValue("ctlNotes"));n.addParameter("CurrencyNo",this.getFieldValue("ctlCurrency"));n.addParameter("SalesPersion",this.getFieldValue("ctlSalespersion"));n.addDataOK(Function.createDelegate(this,this.saveEditComplete));n.addError(Function.createDelegate(this,this.saveEditError));n.addTimeout(Function.createDelegate(this,this.saveEditError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null}},saveEditError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},saveEditComplete:function(n){n._result.Result?n._result.NewID>0&&(this._intNewID=n._result.NewID,this.showSavedOK(!0),this.onSaveComplete()):this.showError(!0,n._result.ValidationMessage)},validateForm:function(){this.onValidate();return this.autoValidateFields()}};Rebound.GlobalTrader.Site.Controls.Forms.ClientBOMAdd_Add.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.ClientBOMAdd_Add",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);