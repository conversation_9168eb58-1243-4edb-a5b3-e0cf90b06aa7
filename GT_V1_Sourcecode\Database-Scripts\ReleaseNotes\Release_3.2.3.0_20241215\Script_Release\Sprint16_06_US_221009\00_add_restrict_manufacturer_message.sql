﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/*   
===========================================================================================  
TASK         UPDATED BY   DATE			ACTION		DESCRIPTION  
[US-221009]  An.TranTan   29-Nov-2024	Create		Add restricted message for manufacturer
===========================================================================================  
*/  

IF EXISTS (SELECT 1 FROM dbo.tbSystemWarningMessage
	WHERE WarningNo = 5				--Restricted Manufacturer, refer tbWarningMessage
		AND ApplyToCatagoryNo = 1	--refer tbWarningMessage
		AND ClientNo = 0			--apply for all clients
		AND InActive = 0
)BEGIN
	UPDATE tbSystemWarningMessage
	SET WarningText = 'The manufacturer you have selected is subject to global sanctions and cannot be used.'
		,DLUP = GETDATE()
		,UpdatedBy = 1
	WHERE WarningNo = 5		
		AND ApplyToCatagoryNo = 1	
		AND ClientNo = 0	
		AND InActive = 0
END
ELSE BEGIN
	INSERT INTO dbo.tbSystemWarningMessage
	(
		[ClientNo]
		,[WarningNo]
		,[WarningText]
		,[ApplyToCatagoryNo]
		,[ApplyTo]
		,[InActive]
		,[UpdatedBy]
		,[DLUP]
		,[GlobalProductCategoryNo]
	)VALUES(
		0		--ClientNo]
		,5		--[WarningNo]
		,'The manufacturer you have selected is subject to global sanctions and cannot be used.'	--[WarningText]
		,1		--[ApplyToCatagoryNo]
		,NULL	--[ApplyTo]
		,0		--[InActive]
		,1		--[UpdatedBy]: sys admin
		,GETDATE()
		,NULL	--[GlobalProductCategoryNo]
	)
END


