//-----------------------------------------------------------------------------------------
// RP 22.12.2009:
// - populate filters server-side
//
// RP 26.10.2009:
// - new control
//-----------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site;
using Rebound.GlobalTrader.Site.Controls;
using Rebound.GlobalTrader.Site.Enumerations;

namespace Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists {

	public partial class Stock : Base {

		protected override void OnInit(EventArgs e) {
			SetLeftNuggetType("DataList_Stock");
			SetDataListNuggetType("AllStock");
			LoadDataListNugget(_objDataListNugget.Name);
			TitleResource = "QuickBrowse";
			base.OnInit(e);
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Stock", ctlDesignBase.ClientID);
			_scScriptControlDescriptor.AddProperty("strQuantityAt", Functions.GetGlobalResource("Misc", "StockQuantityAvailableAt"));
			_scScriptControlDescriptor.AddProperty("strAvailable", Functions.GetGlobalResource("Misc", "StockQuantityAvailable"));
			AddScriptReference("Controls.LeftNuggets.DataLists.Stock.Stock");
			SetupTable();
		}

		private void SetupTable() {
			_tbl.AllowSelection = false;
			_tbl.SortColumnDirection = SortColumnDirection.ASC;
			_tbl.Columns.Add(new FlexiDataColumn("PartNo", Unit.Empty, true));
		}

		protected override void AddNewFilterItemsToStartOfList() {
			AddNewDropDownFilter("ViewLevel", "Rebound.GlobalTrader.Site", "ViewLevel_Stock", "ViewLevel", "ViewLevel", 0);
			ShowFilter("ViewLevel", true);
			SetFilterValue("ViewLevel", 0);
			base.AddNewFilterItemsToStartOfList();
		}

		protected override void RenderAdditionalState() {
			string strCallType = this.GetSavedStateValue("CallType").ToUpper();
			if (string.IsNullOrEmpty(strCallType)) strCallType = "ALL";
			switch (strCallType) {
				case "ALL": this.SetFilterValue("ViewLevel", 0); break;
				case "AVAILABLE": this.SetFilterValue("ViewLevel", 1); break;
				case "QUARANTINED": this.SetFilterValue("ViewLevel", 2); break;
			}
			base.RenderAdditionalState();
		}

	}

}