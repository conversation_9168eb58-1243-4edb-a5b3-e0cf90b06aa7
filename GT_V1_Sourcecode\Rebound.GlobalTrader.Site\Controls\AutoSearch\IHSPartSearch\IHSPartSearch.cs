using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Collections;

namespace Rebound.GlobalTrader.Site.Controls.AutoSearch {
	[DefaultProperty("")]
	[ToolboxData("<{0}:IHSPartSearch runat=server></{0}:IHSPartSearch>")]
	public class IHSPartSearch : Base {

		#region Properties

		private string _searchType = "FULL";
		public string SearchType {
			get { return _searchType; }
			set { _searchType = value; }
		}

		#endregion

		#region Overrides
		/// <summary>
		/// OnInit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
			CharactersToEnterBeforeSearch = 4;
			AddScriptReference("Controls.AutoSearch.IHSPartSearch.IHSPartSearch.js");
			SetAutoSearchType("PartSearch");
		}
		/// <summary>
		/// OnLoad
		/// </summary>
		/// <param name="e"></param>
		protected override void OnLoad(EventArgs e) {
			SetupScriptDescriptors();
			base.OnLoad(e);
		}
		#endregion

		/// <summary>
		/// SetupScriptDescriptors
		/// </summary>
		private void SetupScriptDescriptors() {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.AutoSearch.IHSPartSearch", ClientID);
			_scScriptControlDescriptor.AddProperty("searchType", SearchType.ToUpper());
		}
	}
}