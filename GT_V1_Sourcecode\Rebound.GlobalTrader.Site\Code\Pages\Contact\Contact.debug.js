///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference name="~/Controls/IconButton/IconButton.js" />
///<reference name="~/Controls/ReboundTextBox/ReboundTextBox.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Contact");

Rebound.GlobalTrader.Site.Pages.Contact.Contact = function(el) { 
	Rebound.GlobalTrader.Site.Pages.Contact.Contact.initializeBase(this, [el]);
	this._strPathToData = "Code/Pages/Contact";
	this._strDataObject = "Contact";
};

Rebound.GlobalTrader.Site.Pages.Contact.Contact.prototype = {

	get_ctlEllipses_AllCompanies: function() { return this._ctlEllipses_AllCompanies; }, 	set_ctlEllipses_AllCompanies: function(v) { if (this._ctlEllipses_AllCompanies !== v)  this._ctlEllipses_AllCompanies = v; }, 
	get_ctlEllipses_Manufacturers: function() { return this._ctlEllipses_Manufacturers; }, 	set_ctlEllipses_Manufacturers: function(v) { if (this._ctlEllipses_Manufacturers !== v)  this._ctlEllipses_Manufacturers = v; }, 
	get_ctlEllipses_Prospects: function() { return this._ctlEllipses_Prospects; }, 	set_ctlEllipses_Prospects: function(v) { if (this._ctlEllipses_Prospects !== v)  this._ctlEllipses_Prospects = v; }, 
	get_ctlEllipses_Customers: function() { return this._ctlEllipses_Customers; }, 	set_ctlEllipses_Customers: function(v) { if (this._ctlEllipses_Customers !== v)  this._ctlEllipses_Customers = v; }, 
	get_ctlEllipses_Suppliers: function() { return this._ctlEllipses_Suppliers; }, 	set_ctlEllipses_Suppliers: function(v) { if (this._ctlEllipses_Suppliers !== v)  this._ctlEllipses_Suppliers = v; }, 
	get_ctlEllipses_Contacts: function() { return this._ctlEllipses_Contacts; }, 	set_ctlEllipses_Contacts: function(v) { if (this._ctlEllipses_Contacts !== v)  this._ctlEllipses_Contacts = v; }, 

	initialize: function() {
		Rebound.GlobalTrader.Site.Pages.Contact.Contact.callBaseMethod(this, "initialize");
	},
	
	goInit: function() {
		Rebound.GlobalTrader.Site.Pages.Contact.Contact.callBaseMethod(this, "goInit");
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this._ctlEllipses_AllCompanies) this._ctlEllipses_AllCompanies.dispose();
		if (this._ctlEllipses_Manufacturers) this._ctlEllipses_Manufacturers.dispose();
		if (this._ctlEllipses_Prospects) this._ctlEllipses_Prospects.dispose();
		if (this._ctlEllipses_Customers) this._ctlEllipses_Customers.dispose();
		if (this._ctlEllipses_Suppliers) this._ctlEllipses_Suppliers.dispose();
		if (this._ctlEllipses_Contacts) this._ctlEllipses_Contacts.dispose();
		this._ctlEllipses_AllCompanies = null;
		this._ctlEllipses_Manufacturers = null;
		this._ctlEllipses_Prospects = null;
		this._ctlEllipses_Customers = null;
		this._ctlEllipses_Suppliers = null;
		this._ctlEllipses_Contacts = null;
		this._strPathToData = null;
		this._strDataObject = null;
		Rebound.GlobalTrader.Site.Pages.Contact.Contact.callBaseMethod(this, "dispose");
	},
	
	countAllCompanies: function() {
		var obj = this._ctlEllipses_AllCompanies._objData;
		obj.set_PathToData(this._strPathToData);
		obj.set_DataObject(this._strDataObject);
		obj.set_DataAction("CountAllCompanies");
	},
	
	countManufacturers: function() {
		var obj = this._ctlEllipses_Manufacturers._objData;
		obj.set_PathToData(this._strPathToData);
		obj.set_DataObject(this._strDataObject);
		obj.set_DataAction("CountManufacturers");
	},
	
	countProspects: function() {
		var obj = this._ctlEllipses_Prospects._objData;
		obj.set_PathToData(this._strPathToData);
		obj.set_DataObject(this._strDataObject);
		obj.set_DataAction("CountProspects");
	},
	
	countCustomers: function() {
		var obj = this._ctlEllipses_Customers._objData;
		obj.set_PathToData(this._strPathToData);
		obj.set_DataObject(this._strDataObject);
		obj.set_DataAction("CountCustomers");
	},
	
	countSuppliers: function() {
		var obj = this._ctlEllipses_Suppliers._objData;
		obj.set_PathToData(this._strPathToData);
		obj.set_DataObject(this._strDataObject);
		obj.set_DataAction("CountSuppliers");
	},
	
	countContacts: function() {
		var obj = this._ctlEllipses_Contacts._objData;
		obj.set_PathToData(this._strPathToData);
		obj.set_DataObject(this._strDataObject);
		obj.set_DataAction("CountContacts");
	}	

};

Rebound.GlobalTrader.Site.Pages.Contact.Contact.registerClass("Rebound.GlobalTrader.Site.Pages.Contact.Contact", Rebound.GlobalTrader.Site.Pages.Content, Sys.IDisposable);
