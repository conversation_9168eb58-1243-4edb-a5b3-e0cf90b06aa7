﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/*   
===========================================================================================  
TASK         UPDATED BY   DATE          ACTION    DESCRIPTION  
[US-210037]  An.TranTan   23-Oct-2024	create    Get Manufacturer advisory notes  
===========================================================================================  
*/ 
CREATE OR ALTER   PROCEDURE [dbo].[usp_selectAll_ManufacturerLink_for_Supplier]  
@SupplierCompanyNo int  
AS  
SELECT a.ManufacturerLinkId  
,  a.<PERSON>  
,  b.<PERSON>urer<PERSON>ame  
,  a.Supp<PERSON>o   
,  c.CompanyName  As SupplierName   
,  a.ManufacturerRating  
,  a.SupplierRating  
,  a.UpdatedBy  
,  a.DLUP
, CASE WHEN ISNULL(b.IsDisplayAdvisory, 0) = 1 THEN b.AdvisoryNotes ELSE '' END AS AdvisoryNotes
FROM  dbo.tbManufacturerLink a  
JOIN dbo.tbManufacturer b  
 ON a.ManufacturerNo = b.ManufacturerId  
JOIN dbo.tbCompany c  
 ON a.SupplierCompanyNo = c.CompanyId  
WHERE a.SupplierCompanyNo = @SupplierCompanyNo  
ORDER BY SupplierName  
  
  
  
  
GO


