///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//
// RP 06.10.2009:
// - Changes due to changes in base class
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");

Rebound.GlobalTrader.Site.Controls.DropDowns.Year = function(element) { 
    Rebound.GlobalTrader.Site.Controls.DropDowns.Year.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.DropDowns.Year.prototype = {

	initialize: function() {
        Rebound.GlobalTrader.Site.Controls.DropDowns.Year.callBaseMethod(this, "initialize");
		this.addSetupDataCall(Function.createDelegate(this, this.setupDataCall));
		this.addGetDataOK(Function.createDelegate(this, this.dataCallOK));
	},
	
	dispose: function() { 
		if (this.isDisposed) return;
        Rebound.GlobalTrader.Site.Controls.DropDowns.Year.callBaseMethod(this, "dispose");
	},
	
	setupDataCall: function() { 
        this._objData.set_PathToData("controls/DropDowns/Year");
        this._objData.set_DataObject("Year");
		this._objData.set_DataAction("GetData");
	},
	
	dataCallOK: function() { 
		var result = this._objData._result;
		if (result.Types) {
			for (var i = 0; i < result.Types.length; i++) {
				this.addOption(result.Types[i].Name, result.Types[i].ID);
			}
		}
	}

};

Rebound.GlobalTrader.Site.Controls.DropDowns.Year.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.Year", Rebound.GlobalTrader.Site.Controls.DropDowns.Base);
