﻿/* 
===========================================================================================
TASK      			UPDATED BY     		DATE         		ACTION 			DESCRIPTION
[BUG-204859]		Cuong.DoX			12-JULY-2024		Update			Change workflow after quarantine
																			- quarantine stock
																			- not to set quarantine GI line any more, release instead
																			- unallocate stock
																			- response to decline all query
																			- release GILine
===========================================================================================
*/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
IF OBJECT_ID('usp_update_GILine_Quarantined','P') IS NOT NULL
    DROP PROC [dbo].[usp_update_GILine_Quarantined]
GO
CREATE PROCEDURE [dbo].[usp_update_GILine_Quarantined]
    @GoodsInLineId int,
    @Quarantine bit,
    @UpdatedBy int,
	@ClientNo INT = 101,
    @RowsAffected int = NULL OUTPUT
AS
BEGIN
    /*       
    Marker            Owner             Date                Remarks       
    [001]             Ravi Bhushan      28-04-2023          [RP-967] Deallocate any sales order from that line        
*/
    BEGIN TRANSACTION

    --declare variables                                          
    DECLARE @Location nvarchar(10) = NULL,
            @PurchaseOrderLineNo int,
            @PurchaseOrderNo int,
            @StockId int,
            @GoodsInNo int,
            @QuantityInStock int,
            @QuantityOnOrder int,
            @StockLogId int

    --get the stockId to use on the stockLog and the new values (CRMA)                                          
    SELECT @StockId = st.StockId,
           @GoodsInNo = gil.GoodsInNo,
           @Location = st.Location,
           @QuantityInStock = st.QuantityInStock,
           @QuantityOnOrder = st.QuantityOnOrder
    FROM dbo.tbStock st
        JOIN tbGoodsInLine gil
            ON gil.PurchaseOrderLineNo = st.PurchaseOrderLineNo
               AND gil.GoodsInLineId = st.GoodsInLineNo
    WHERE GoodsInLineId = @GoodsInLineId

    --update @Quarantine status In tbGoodsInLine table 
	--remove due to [BUG-204859] not set quarantine any more, release instead
    /*UPDATE tbGoodsInline
    set Unavailable = @Quarantine
    WHERE GoodsInLineId = @GoodsInLineId*/

    DECLARE @StockLogDetail nvarchar(50)
    SET @StockLogDetail = ''
    IF @Location IS NULL
        SET @Location = ''
    IF NOT @Location =
       (
           SELECT isnull(Location, '') FROM tbStock WHERE StockId = @StockId
       )
        SET @StockLogDetail = 'Location'

    UPDATE dbo.tbStock
    SET Unavailable = @Quarantine,
        UpdatedBy = @UpdatedBy,
        DLUP = CURRENT_TIMESTAMP
    WHERE StockId = @StockId

    IF @Quarantine = 1
    BEGIN
        EXEC usp_insert_StockLog @StockLogTypeNo = 6,                 --Quarantined          
                                 @StockNo = @StockId,                 --          
                                 @QuantityInStock = @QuantityInStock, --          
                                 @QuantityOnOrder = @QuantityOnOrder, --          
                                 @UpdatedBy = @UpdatedBy,             --          
                                 @Detail = @StockLogDetail,           --          
                                 @StockLogId = @StockLogId OUTPUT
    --          
    END
    ELSE
    BEGIN
        EXEC usp_insert_StockLog @StockLogTypeNo = 17,                --Remove from quarantine          
                                 @StockNo = @StockId,                 --          
                                 @QuantityInStock = @QuantityInStock, --          
                                 @QuantityOnOrder = @QuantityOnOrder, --          
                                 @Detail = @StockLogDetail,           --          
                                 @UpdatedBy = @UpdatedBy,             --          
                                 @StockLogId = @StockLogId OUTPUT
    END

    /* ----   [001] start  ----- */
    /*       
            1. @GoodsInLineId is passed as argument, based on this @GoodsInNumber is fetched.  -- already done above       
            2. Based on @GoodsInNumber, search for @StockID in tbStock  -- already done above       
            3. Based on @StockId search for @stockNo in 'tbAllocation'  -- already done above       
            4. Using this @StockId, delete the record from 'tbAllocation' -- already done above       
        */
    --delete from tbAllocation where StockNo = @StockId  --(Rollback RP-967 for 18-06-2023 release)      

    /* ----   [001] End  ----- */

	/*---- BUG-204859 start ---------*/
	EXEC usp_update_GILine_Quarantined_From_Stock @GoodsInLineId = @GoodsInLineId,
												  @UpdatedBy = @UpdatedBy,
												  @ClientNo = @ClientNo
    COMMIT TRANSACTION

END
--          
SELECT @RowsAffected = @@ROWCOUNT