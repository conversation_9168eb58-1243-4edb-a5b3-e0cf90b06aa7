Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.ItemSearch");Rebound.GlobalTrader.Site.Controls.ItemSearch.RequiredBomItem=function(n){Rebound.GlobalTrader.Site.Controls.ItemSearch.RequiredBomItem.initializeBase(this,[n]);this._intBOMID=0};Rebound.GlobalTrader.Site.Controls.ItemSearch.RequiredBomItem.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.ItemSearch.RequiredBomItem.callBaseMethod(this,"initialize");this.addSetupData(Function.createDelegate(this,this.doSetupData));this.addGetDataComplete(Function.createDelegate(this,this.doGetDataComplete))},dispose:function(){this.isDisposed||Rebound.GlobalTrader.Site.Controls.ItemSearch.RequiredBomItem.callBaseMethod(this,"dispose")},doSetupData:function(){this._objData.set_PathToData("controls/ItemSearch/RequiredBomItem");this._objData.set_DataObject("RequiredBomItem");this._objData.set_DataAction("GetData");this._objData.addParameter("Part",this.getFieldValue("ctlPartNo"));this._objData.addParameter("ReqNoLo",this.getFieldValue_Min("ctlReqNo"));this._objData.addParameter("ReqNoHi",this.getFieldValue_Max("ctlReqNo"));this._objData.addParameter("DateReceivedFrom",this.getFieldValue("ctlDateReceivedFrom"));this._objData.addParameter("DateReceivedTo",this.getFieldValue("ctlDateReceivedTo"));this._objData.addParameter("BOM",this._intBOMID);this._objData.addParameter("BOMName",this.getFieldValue("ctlBOM"))},doGetDataComplete:function(){for(var n,i,t=0,r=this._objResult.Results.length;t<r;t++)n=this._objResult.Results[t],i=[n.No,$R_FN.setCleanTextValue(n.CMName),$R_FN.writePartNo(n.Part,n.ROHS),$R_FN.setCleanTextValue(n.Date),n.Quantity,n.Price,n.BOMName],this._tblResults.addRow(i,n.ID,n.ID==this._intInitialID),i=null,n=null}};Rebound.GlobalTrader.Site.Controls.ItemSearch.RequiredBomItem.registerClass("Rebound.GlobalTrader.Site.Controls.ItemSearch.RequiredBomItem",Rebound.GlobalTrader.Site.Controls.ItemSearch.Base,Sys.IDisposable);