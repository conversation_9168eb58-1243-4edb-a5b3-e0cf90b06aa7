﻿//Marker     Changed by               Date         Remarks
//[001]      A<PERSON><PERSON><PERSON>           18/05/2022   Generate Power App Token.
using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using System.Data.Common;

namespace Rebound.GlobalTrader.DAL
{
    public abstract class PowerAppTokenProvider : DataAccess
    {
        static private PowerAppTokenProvider _instance = null;
        static public PowerAppTokenProvider Instance
        {
            get
            {
                if (_instance == null) _instance = (PowerAppTokenProvider)Activator.CreateInstance(Type.GetType(Globals.Settings.PowerAppTokens.ProviderType));
                return _instance;
            }
        }
        public PowerAppTokenProvider()
        {
            this.ConnectionString = Globals.Settings.LoginPreferences.ConnectionString;
        }

        public abstract PowerAppTokenDetails GetTokenForPowerApp(System.Int32? loginId, System.String WorkFlowType,System.Boolean? IsNotifySO);
    }
}
