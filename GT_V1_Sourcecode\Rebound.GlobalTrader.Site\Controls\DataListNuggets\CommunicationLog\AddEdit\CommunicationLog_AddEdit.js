Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.CommunicationLog_AddEdit=function(n){Rebound.GlobalTrader.Site.Controls.Forms.CommunicationLog_AddEdit.initializeBase(this,[n]);this._intCompanyID=-1;this._intContactID=-1;this._intLogItemID=-1;this._intSchedulerID=-1;this._intDataListNuggetID=-1};Rebound.GlobalTrader.Site.Controls.Forms.CommunicationLog_AddEdit.prototype={get_intCompanyID:function(){return this._intCompanyID},set_intCompanyID:function(n){this._intCompanyID!==n&&(this._intCompanyID=n)},get_strTitleEdit:function(){return this._strTitleEdit},set_strTitleEdit:function(n){this._strTitleEdit!==n&&(this._strTitleEdit=n)},get_strTitleAdd:function(){return this._strTitleAdd},set_strTitleAdd:function(n){this._strTitleAdd!==n&&(this._strTitleAdd=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.CommunicationLog_AddEdit.callBaseMethod(this,"initialize");this.addModeChanged(Function.createDelegate(this,this.changeTitleOnMode));this.addShown(Function.createDelegate(this,this.formShown))},dispose:function(){this.isDisposed||(this._intCompanyID=null,this._strTitleEdit=null,this._strTitleAdd=null,this._intCompanyID=null,this._intContactID=null,this._intLogItemID=null,this._intSchedulerID=null,this._intDataListNuggetID=null,Rebound.GlobalTrader.Site.Controls.Forms.CommunicationLog_AddEdit.callBaseMethod(this,"dispose"))},formShown:function(){this._blnFirstTimeShown&&(this._strPathToData="controls/DataListNuggets/CommunicationLog",this._strDataObject="CommunicationLog",this.addSaveClick(Function.createDelegate(this,this.saveClicked)),this.addCancelClick(Function.createDelegate(this,this.cancelClicked)));this.resetFormFields();this._mode=="ADD"&&this.setFormFieldsToDefaults();this.updateDropDowns();this.showError(!1)},saveClicked:function(){if(this.resetFormFields(),this.validateForm()){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);this._mode=="ADD"?(n.set_DataAction("AddLogItem"),n.addParameter("TypeNo",this.getFieldValue("ctlLogType")),n.addParameter("Notes",this.getFieldValue("ctlNotes")),n.addParameter("ContactNo",this.getFieldValue("ctlContact")),n.addParameter("CMNo",this._intCompanyID)):(n.set_DataAction("UpdateLogItem"),n.addParameter("ID",this._intLogItemID),n.addParameter("TypeNo",this.getFieldValue("ctlLogType")),n.addParameter("Notes",this.getFieldValue("ctlNotes")),n.addParameter("ContactNo",this.getFieldValue("ctlContact")),n.addParameter("CMNo",this._intCompanyID));n.addParameter("DLNID",this._intDataListNuggetID);n.addDataOK(Function.createDelegate(this,this.saveEditComplete));n.addError(Function.createDelegate(this,this.saveEditError));n.addTimeout(Function.createDelegate(this,this.saveEditError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null;this.onSave()}},saveEditError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},saveEditComplete:function(n){n._result.Result==!0?this.onSaveComplete():this.saveEditError(n)},validateForm:function(){var n=!0;return this.checkFieldEntered("ctlContact")||(n=!1),this.checkFieldEntered("ctlLogType")||(n=!1),n||this.showError(!0),n},cancelClicked:function(){this.onCancel()},changeTitleOnMode:function(){switch(this._mode){case"ADD":this.changeTitle(this._strTitleAdd);break;case"EDIT":this.changeTitle(this._strTitleEdit)}},updateDropDowns:function(){this.getFieldControl("ctlContact")._intCompanyID=this._intCompanyID;this.setFieldValue("ctlContact",this._intContactID);this.getFieldDropDownData("ctlContact");this.getFieldDropDownData("ctlLogType")}};Rebound.GlobalTrader.Site.Controls.Forms.CommunicationLog_AddEdit.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CommunicationLog_AddEdit",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);