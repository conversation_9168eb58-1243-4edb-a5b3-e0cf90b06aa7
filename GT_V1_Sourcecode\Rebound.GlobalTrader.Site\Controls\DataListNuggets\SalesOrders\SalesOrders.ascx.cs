/*
 Marker     ChangedBy       Date            Remarks
 [001]      <PERSON><PERSON><PERSON>     17-Aug-2018     Provision to add Global Security in Sales Order
 */
using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.DataListNuggets {
	public partial class SalesOrders : Base {
        //[001] start
        #region Properties
        private bool _IsGlobalLogin = false;
        public bool IsGlobalLogin
        {
            get { return _IsGlobalLogin; }
            set { _IsGlobalLogin = value; }
        }
        private bool _IsGSA = false;
        public bool IsGSA
        {
            get { return _IsGSA; }
            set { _IsGSA = value; }
        }

        protected IconButton _ibtnExportCSV;
        protected IconButton _ibtnViewTask;

        #endregion
        //[001] end

        #region Overrides

        /// <summary>
        /// OnInit
        /// </summary>
        /// <param name="e"></param>
        protected override void OnInit(EventArgs e) {
            //[001] start
            IsGlobalLogin = SessionManager.IsGlobalUser.Value;
            IsGSA = SessionManager.IsGSA.Value;
            //[001] end
            SetDataListNuggetType("SalesOrders");
			base.OnInit(e);
            WireUpControls();
            TitleText = Functions.GetGlobalResource("Nuggets", "SalesOrders");
			AddScriptReference("Controls.DataListNuggets.SalesOrders.SalesOrders");
			SetupTable();
		}

		protected override void OnLoad(EventArgs e) {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.DataListNuggets.SalesOrders", ctlDesignBase.ClientID);
            _scScriptControlDescriptor.AddProperty("intSalesPersonID", _objQSManager.SalesPersonID > 0 ? _objQSManager.SalesPersonID : 0);
            //[001] start
            _scScriptControlDescriptor.AddProperty("IsGlobalLogin", _IsGlobalLogin);
            _scScriptControlDescriptor.AddProperty("IsGSA", _IsGSA);
            //[001] end
            _scScriptControlDescriptor.AddElementProperty("ibtnExportCSV", _ibtnExportCSV.ClientID);
            _scScriptControlDescriptor.AddElementProperty("ibtnViewTask", _ibtnViewTask.ClientID);
            base.OnLoad(e);
		}

		protected override void RenderAdditionalState() {
            string strViewLevel = this.GetSavedStateValue("ViewLevel");
            if (!string.IsNullOrEmpty(strViewLevel)) {
                ((Pages.Content)Page).CurrentTab = Convert.ToInt32(strViewLevel);
                _enmViewLevel = (ViewLevelList)Convert.ToInt32(strViewLevel);
                this.OnAskPageToChangeTab();
            }
            base.RenderAdditionalState();
		}

		#endregion

		private void SetupTable() {
			_tbl.AllowSelection = false;
			_tbl.SortColumnDirection = Rebound.GlobalTrader.Site.Enumerations.SortColumnDirection.DESC;
			_tbl.Columns.Add(new FlexiDataColumn("Number", WidthManager.GetWidth(WidthManager.ColumnWidth.SystemDocumentNumber), true));
			_tbl.Columns.Add(new FlexiDataColumn("PartNo", "CustomerPurchaseOrderNo", WidthManager.GetWidth(WidthManager.ColumnWidth.PartNo), true));
            _tbl.Columns.Add(new FlexiDataColumn("QuantityOrdered", "QuantityShipped", "QuantityInStockForSO", WidthManager.GetWidth(WidthManager.ColumnWidth.Quantity), true));
			_tbl.Columns.Add(new FlexiDataColumn("Company","Contact", Unit.Empty, true));
			_tbl.Columns.Add(new FlexiDataColumn("DateOrdered", WidthManager.GetWidth(WidthManager.ColumnWidth.Date), true));
            _tbl.Columns.Add(new FlexiDataColumn("DatePromised", WidthManager.GetWidth(WidthManager.ColumnWidth.Date), true));
			_tbl.Columns.Add(new FlexiDataColumn("Status", WidthManager.GetWidth(WidthManager.ColumnWidth.Date), false));
            _tbl.Columns.Add(new FlexiDataColumn("ContractNo", WidthManager.GetWidth(WidthManager.ColumnWidth.SystemDocumentNumber), true));
			_tbl.Columns.Add(new FlexiDataColumn("ToDoList", WidthManager.GetWidth(WidthManager.ColumnWidth.DateAndTime), false));
        }
        private void WireUpControls()
        {
            _ibtnExportCSV = (IconButton)FindIconButton("ibtnExportCSV");
            _ibtnViewTask = (IconButton)FindIconButton("ibtnViewTask");
        }
    }
}
