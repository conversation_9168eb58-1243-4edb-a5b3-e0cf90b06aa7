﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Rebound.GlobalTrader.BLL.Entities
{
    public partial class EndUserUndertaking : BizObject
    {
        public static Int32 Insert(System.Int32? SalesOrderId, System.String Caption, System.String FileName, System.Int32? UpdatedBy, System.String FileType)
        {
            Int32 objReturn = Rebound.GlobalTrader.DAL.Common.Entities.EndUserUndertakingProvider.Insert(SalesOrderId, Caption, FileName, UpdatedBy, FileType);
            return objReturn;
        }
    }
}
