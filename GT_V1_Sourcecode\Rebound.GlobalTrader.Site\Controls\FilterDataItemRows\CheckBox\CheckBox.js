Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.FilterDataItemRows");Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.CheckBox=function(n){Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.CheckBox.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.CheckBox.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.CheckBox.callBaseMethod(this,"initialize")},dispose:function(){this.isDisposed||(this.get_element()&&$clearHandlers(this.get_element()),this._blnOn=!1,Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.CheckBox.callBaseMethod(this,"dispose"))},getValue:function(){return this._blnOn},setValue:function(n){this.enableField(n)},reset:function(){this.enableField(!1)}};Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.CheckBox.registerClass("Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.CheckBox",Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.Base,Sys.IDisposable);