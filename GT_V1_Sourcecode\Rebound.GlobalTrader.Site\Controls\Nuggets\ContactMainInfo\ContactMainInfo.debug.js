///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//Marker     changed by      date         Remarks
//[001]      Vinay          09/07/2012   This need for Rebound- Invoice bulk Emailer
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");

Rebound.GlobalTrader.Site.Controls.Nuggets.ContactMainInfo = function(element) { 
	Rebound.GlobalTrader.Site.Controls.Nuggets.ContactMainInfo.initializeBase(this, [element]);
	this._intCompanyID = -1;
	this._intContactID = -1;
};

Rebound.GlobalTrader.Site.Controls.Nuggets.ContactMainInfo.prototype = {

    get_intCompanyID: function() { return this._intCompanyID; }, set_intCompanyID: function(value) { if (this._intCompanyID !== value) this._intCompanyID = value; },
    get_intContactID: function() { return this._intContactID; }, set_intContactID: function(value) { if (this._intContactID !== value) this._intContactID = value; },
    get_ibtnEdit: function() { return this._ibtnEdit; }, set_ibtnEdit: function(value) { if (this._ibtnEdit !== value) this._ibtnEdit = value; },

    initialize: function() {
        Rebound.GlobalTrader.Site.Controls.Nuggets.ContactMainInfo.callBaseMethod(this, "initialize");
        this.addRefreshEvent(Function.createDelegate(this, this.getData));

        //setup forms and their events
        if (this._ibtnEdit) {
            $R_IBTN.addClick(this._ibtnEdit, Function.createDelegate(this, this.showEditForm));
            this._frmEdit = $find(this._aryFormIDs[0]);
            this._frmEdit.addSave(Function.createDelegate(this, this.saveEdit));
            this._frmEdit.addCancel(Function.createDelegate(this, this.cancelEdit));
            this._frmEdit.addSaveComplete(Function.createDelegate(this, this.saveEditComplete));
        }
    },

    dispose: function() {
        if (this.isDisposed) return;
        if (this._ibtnEdit) $R_IBTN.clearHandlers(this._ibtnEdit);
        if (this._frmEdit) this._frmEdit.dispose();
        this._ibtnEdit = null;
        this._frmEdit = null;
        this._intCompanyID = null;
        this._intContactID = null;
        Rebound.GlobalTrader.Site.Controls.Nuggets.ContactMainInfo.callBaseMethod(this, "dispose");
    },

    getData: function() {
        this.getData_Start();
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/ContactMainInfo");
        obj.set_DataObject("ContactMainInfo");
        obj.set_DataAction("GetData");
        obj.addParameter("id", this._intContactID);
        obj.addDataOK(Function.createDelegate(this, this.getDataOK));
        obj.addError(Function.createDelegate(this, this.getDataError));
        obj.addTimeout(Function.createDelegate(this, this.getDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getDataOK: function(args) {
        var res = args._result;
        this.setFieldValue("ctlFirstName", res.FirstName);
        this.setFieldValue("ctlSurname", res.Surname);
        this.setFieldValue("ctlJobTitle", res.JobTitle);
        this.setFieldValue("ctlTel", res.Tel);
        this.setFieldValue("ctlFax", res.Fax);
        this.setFieldValue("ctlTelExt", res.TelExt);
        this.setFieldValue("ctlHomeTel", res.HomeTel);
        this.setFieldValue("ctlMobileTel", res.MobileTel);
        this.setFieldValueEmail("ctlEmail", res.Email);
        this.setFieldValue("ctlIsEmailTextOnly", res.IsEmailTextOnly);
        this.setFieldValue("ctlNickname", res.Nickname);
        this.setFieldValue("ctlPersonalAddress", res.CompanyAdd); // Have to Show Address on Main Contact Info Tab
        //this.setFieldValue("ctlPersonalAddress", res.PersonalAddress);
        //this.setFieldValue("hidAddressName", res.AddressName);
        //this.setFieldValue("hidAddressID", res.AddressID);
        //this.setFieldValue("hidAddress1", res.Address1);
        //this.setFieldValue("hidAddress2", res.Address2);
        //this.setFieldValue("hidAddress3", res.Address3);
        //this.setFieldValue("hidTown", res.Town);
        //this.setFieldValue("hidCounty", res.County);
        //this.setFieldValue("hidCountryNo", res.CountryNo);
        //this.setFieldValue("hidPostcode", res.Postcode);
        //[001] code start
        this.setFieldValue("hidFinanceContacts", res.FinanceContact);
        this.setFieldValue("hidCompanyAddress", res.CompanyAddress);
        this.setFieldValue("hidInactive", res.Inactive);
        //this.setFieldValue("hidComapnyNo", res.CompanyNo);
        //[001] code end
        this.setFieldValue("ctlIsSendShipmentNotification", res.IsSendShipmentNotification);
        this.setDLUP(res.DLUP);
        this.getDataOK_End();
    },

    getDataError: function(args) {
        this.showError(true, args.get_ErrorMessage());
    },

    saveEdit: function() {
    },

    cancelEdit: function() {
        this.hideEditForm();
        this.showContent(true);
    },

    showEditForm: function() {
        this._frmEdit._intCompanyNo = this._intCompanyID;
   
        this._frmEdit._intContactID = this._intContactID;
        this._frmEdit.setFieldValue("ctlFirstName", this.getFieldValue("ctlFirstName"));
        this._frmEdit.setFieldValue("ctlSurname", this.getFieldValue("ctlSurname"));
        this._frmEdit.setFieldValue("ctlJobTitle", this.getFieldValue("ctlJobTitle"));
        this._frmEdit.setFieldValue("ctlTel", this.getFieldValue("ctlTel"));
        this._frmEdit.setFieldValue("ctlFax", this.getFieldValue("ctlFax"));
        this._frmEdit.setFieldValue("ctlExtension", this.getFieldValue("ctlTelExt"));
        this._frmEdit.setFieldValue("ctlHomeTel", this.getFieldValue("ctlHomeTel"));
        this._frmEdit.setFieldValue("ctlMobileTel", this.getFieldValue("ctlMobileTel"));
        this._frmEdit.setFieldValue("ctlEmail", this.getFieldValue("ctlEmail"));
        this._frmEdit.setFieldValue("ctlTextOnlyEmail", this.getFieldValue("ctlIsEmailTextOnly"));
        this._frmEdit.setFieldValue("ctlNickname", this.getFieldValue("ctlNickname"));
        this._frmEdit.setFieldValue("ctlCompanyAddress", this.getFieldValue("hidCompanyAddress"));
        //this._frmEdit._ctlAddress._intAddressID = this.getFieldValue("hidAddressID");
        //this._frmEdit.setFieldValue("ctlAddressName", this.getFieldValue("hidAddressName"));
        //this._frmEdit.setFieldValue("ctlLine1", this.getFieldValue("hidAddress1"));
        //this._frmEdit.setFieldValue("ctlLine2", this.getFieldValue("hidAddress2"));
        //this._frmEdit.setFieldValue("ctlLine3", this.getFieldValue("hidAddress3"));
        //this._frmEdit.setFieldValue("ctlTown", this.getFieldValue("hidTown"));
        //this._frmEdit.setFieldValue("ctlCounty", this.getFieldValue("hidCounty"));
        //this._frmEdit.setFieldValue("ctlCountry", this.getFieldValue("hidCountryNo"));
        //this._frmEdit.setFieldValue("ctlPostcode", this.getFieldValue("hidPostcode"));
        //[001] code start
        //alert(this._intCompanyID);
        this._frmEdit.setFieldValue("ctlFinanceContacts", Boolean.parse(this.getFieldValue("hidFinanceContacts")));
        this._frmEdit.setFieldValue("ctlInactive", Boolean.parse(this.getFieldValue("hidInactive")));
        this._frmEdit.setFieldValue("ctlSendShipmentNotification", this.getFieldValue("ctlIsSendShipmentNotification"));
        this.showForm(this._frmEdit, true);
        //[001] code end
    },

    hideEditForm: function() {
        this.showForm(this._frmEdit, false);
    },

    saveEditComplete: function() {
        this.hideEditForm();
        this.showContentLoading(false);
        this.getData();
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
        this.onSaveEditComplete();
    },

    saveEditError: function() {
        this.showError(true, this._frmEdit._strErrorMessage);
    }



};

Rebound.GlobalTrader.Site.Controls.Nuggets.ContactMainInfo.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.ContactMainInfo", Rebound.GlobalTrader.Site.Controls.Nuggets.Base);
