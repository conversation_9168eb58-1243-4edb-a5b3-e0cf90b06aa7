﻿IF OBJECT_ID('dbo.usp_select_SalesOrder_for_Print', 'P') IS NOT NULL
BEGIN
	DROP PROCEDURE dbo.usp_select_SalesOrder_for_Print
END
GO
/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-207777]		An.TranTan			24-Jul-2024		UPDATE			Get Rebate Account for customer
===========================================================================================
*/
CREATE PROCEDURE  [dbo].[usp_select_SalesOrder_for_Print]                    
-- EXEC   usp_select_SalesOrder_for_Print 243919                                
--********************************************************************************************                                    
--* SK 13.05.2010:                                    
--* - include Salesman2 name for use on SOR                                    
--*                                     
--* SK 09.11.2009:                                    
--* - include Incoterms and description                                    
--*                                     
--* SK 25.06.2009:                                    
--* - allow for currency date on SO                                     
--* Marker     Changed by      Date         Remarks                                  
--* [001]      Vinay           23/05/2012   This need to add currency notes field                                  
--* [002]      Abhinav         15/01/2013   Add Bank Fee Term                   
--* [003]      Anand Gupta     17/08/2020   Print so add header                
--********************************************************************************************                                    
@SalesOrderId int                                    
AS                                     
                                    
     --[002] code start                                    
                                       
          DECLARE @InvoiceBankFee float                                  
          DECLARE @ExchangeRate float                                      
          DECLARE @CurrencyNo INT                                
          DECLARE @TERMNO INT                                
          declare @SalesOrderDate date                                
                                          
          SELECT @TERMNO= so.TermsNo, @CurrencyNo =so.CurrencyNo , @SalesOrderDate= so.DateOrdered                                
                                  FROM  dbo.tbSalesOrder so WHERE so.SalesOrderId = @SalesOrderId                                 
                                        
          SET @InvoiceBankFee=(SELECT BankFee FROM tbTerms WHERE IsApplyBankFee=1                                
        AND TermsId = @TERMNO)                                   
          SET @ExchangeRate= dbo.ufn_get_exchange_rate(@CurrencyNo, @SalesOrderDate)                                  
          SET @InvoiceBankFee=(ISNULL(@InvoiceBankFee,0)*ISNULL(@ExchangeRate,1))                                  
    --[002] code end                 
   --[003] code start  Need to change hear                
DECLARE @AppliedDivisionNoForWHS int                
    
--Espire: 16 Nov 2021: As suggested by simon, No need to apply the warehose division header in SO         
/*        
DECLARE @WHSNo int       
select top 1 @WHSNo = po.WarehouseNo from tbAllocation a join tbStock s  on a.StockNo = s.StockId                
           left join tbPurchaseOrderLine pol on pol.PurchaseOrderLineId = s.PurchaseOrderLineNo                
     left join tbPurchaseOrder po on po.PurchaseOrderId = pol.PurchaseOrderNo  where a.SalesOrderLineNo in                 
     (select sol.SalesOrderLineId from tbSalesOrderLine sol where sol.SalesOrderNo=@SalesOrderId)  order by a.DLUP desc                 
                
select @AppliedDivisionNoForWHS = ApplyDivisionHeaderNo from tbwarehouse where warehouseid=@WHSNo          
*/     
    
set @AppliedDivisionNoForWHS=null                           
--[003] code end  Need to change hear                
                                    
SELECT  so.*                       
                              
      , co.CompanyName                                    
      , co.CustomerCode                                    
      , co.Telephone AS CompanyTelephone                                    
      , co.Fax AS CompanyFax                                    
      , co.ShippingCharge       
      , co.Tax AS VATNo                              
      , co.CustomerCode
	  , ISNULL(co.IsRebateAccount, CAST(0 AS BIT)) AS IsRebateAccount
      , cn.ContactName                                    
      , cn.EMail AS ContactEmail           
      , cu.CurrencyCode                   
      , cu.CurrencyDescription          
      , cu.Notes As CurrencyNotes                                  
      , lg.EmployeeName AS SalesmanName                  
      , tm.TermsName                                    
      , sh.ShipViaName                                    
      , tx.TaxName                                    
      , dbo.ufn_get_taxrate(so.TaxNo, so.ClientNo, IsNull(so.CurrencyDate, so.DateOrdered)) AS TaxRate                                
      , so.Closed                                    
      , so.DateAuthorised                                    
      , so.AuthorisedBy                           
      , so.UpdatedBy                                    
      , so.DLUP                                    
      , soval.LineSubTotal                      
      , soval.TotalTax                                    
      , soval.TotalValue                                    
      , inc.Name AS IncotermName                                       
      , lg2.EmployeeName AS Salesman2Name                                  
      , co.CompanyRegNo                 
      , tm.IsApplyBankFee                                
      , @InvoiceBankFee as InvoiceBankFee                             
      , so.AS9120                              
      , ct.Name as CompanyType                         
      --, ct.IsTraceability                      
   , isnull(so.AS9120,0) as IsTraceability                  
      , [dbo].[ufn_get_DivisionStatus_value](so.DivisionNo) as IsAgency                    
      , so.UKAuthorisedBy                    
      , so.UKAuthorisedDate                     
      , lgAuth.EmployeeName AS UKAuthoriserName                      
      , co.OnStop AS CompanyOnStop                     
   ,so.ShipToAddressNo                   
   --[003] code start  Need to change hear                
   , @AppliedDivisionNoForWHS as AppliedDivisionNoForWHS                        
   --[003] code end  Need to change hear                
   ,so.DivisionHeaderNo                 
  ,dh.DivisionName as DivisionHeaderName              
   ,so.HeaderImageName                  
 -----------          
, FHstry.FooterText as FooterText         
,(select FooterText from tbSystemDocumentFooterHistory FTH where FTH.SystemDocumentFooterHistoryId=so.SysDocAS9120HistoryNo)  as SysDocAS9120HistoryText        
,(select FooterText from tbSystemDocumentFooterHistory FTH where FTH.SystemDocumentFooterHistoryId=so.SysDocHazardousHistoryNo)  as SysDocHazardousHistoryText        
,(select FooterText from tbSystemDocumentFooterHistory FTH where FTH.SystemDocumentFooterHistoryId=so.SysDocCOOHistoryNo)  as SysDocCOOHistoryText       
,(select FooterText from tbSystemDocumentFooterHistory FTH where FTH.SystemDocumentFooterHistoryId=so.SysDocProFormaHistoryNo)  as SysDocProFormaHistoryText  
,(select top 1  (select  LabelTypeName from tblabeltype where labeltypeid=ca.LabelTypeNo) from tbCompanyAddress ca where ca.companyno=so.CompanyNo and ca.defaultbilling=1)as dBillLabelTypeName        
 ,(select top 1  (select  LabelTypeName from tblabeltype where labeltypeid=ca.LabelTypeNo) from tbCompanyAddress ca where ca.companyno=so.CompanyNo and ca.DefaultShipping=1)as dShipLabelTypeName        
----------------             
FROM    dbo.tbSalesOrder so                                    
JOIN    dbo.ufn_get_salesOrder_values(@SalesOrderId) soval ON so.SalesOrderId = soval.SalesOrderId                                    
JOIN    dbo.tbCompany co ON so.CompanyNo = co.CompanyId                                    
LEFT JOIN dbo.tbContact cn ON so.ContactNo = cn.ContactId                                    
JOIN    dbo.tbCurrency cu ON so.CurrencyNo = cu.CurrencyId                                    
LEFT JOIN dbo.tbLogin lg ON so.Salesman = lg.LoginId                                    
JOIN    dbo.tbTerms tm ON so.TermsNo = tm.TermsId                                    
JOIN    dbo.tbTax tx ON so.TaxNo = tx.TaxId                                    
LEFT JOIN dbo.tbShipVia sh ON so.ShipViaNo = sh.ShipViaId                                    
LEFT JOIN dbo.tbIncoterm inc ON so.IncotermNo = inc.IncotermId                       
LEFT JOIN dbo.tbLogin lg2 ON so.Salesman2 = lg2.LoginId                           
LEFT JOIN tbCompanyType ct ON co.TypeNo=ct.CompanyTypeId                    
LEFT JOIN dbo.tbLogin lgAuth ON so.UKAuthorisedBy = lgAuth.LoginId      
LEFT JOIN dbo.tbDivision dh ON so.DivisionHeaderNo = dh.DivisionId            
LEFT JOIN tbSystemDocumentFooterHistory FHstry on FHstry.SystemDocumentFooterHistoryId=so.[SystemDocumentFooterHistoryNo]            
          
WHERE   so.SalesOrderId = @SalesOrderId     
    
    
    
    
   