/*
Marker      Date        Changed By       Remarks
[001]      <PERSON><PERSON>   21-Jan-2019   Add View Tree Button.
 */
using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets {
	public partial class BOMPVV : Base {

		#region Locals
		protected IconButton _ibtnEdit;
        protected IconButton _ibtnExportCSV;
        protected IconButton _ibtnExportPurchaseHUB;
		protected IconButton _ibtnDelete;
        protected IconButton _ibtnNotify;
        protected IconButton _ibtnRelease;
        protected IconButton _ibtnClose;
        protected IconButton _ibtnNoBid;
        protected IconButton _ibtnNote;
        protected IconButton _ibtnView;
        protected IconB<PERSON><PERSON> _ibtnCrossMatch;
        protected FlexiDataTable _tblPVVBOM;

        
        #endregion

        #region Properties

        private int _intBOMID = -1;
		public int BOMID {
			get { return _intBOMID; }
			set { _intBOMID = value; }
		}

		private bool _blnCanEdit = true;
		public bool CanEdit {
			get { return _blnCanEdit; }
			set { _blnCanEdit = value; }
		}

		private bool _blnCanDelete = true;
		public bool CanDelete {
			get { return _blnCanDelete; }
			set { _blnCanDelete = value; }
		}

        private bool _blnCanNotify = true;
        public bool CanNotify
        {
            get { return _blnCanNotify; }
            set { _blnCanNotify = value; }
        }

        private bool _blnCanRelease = true;
        public bool CanRelease
        {
            get { return _blnCanRelease; }
            set { _blnCanRelease = value; }
        }

        private bool _blnCanClosed = true;
        public bool CanClosed
        {
            get { return _blnCanClosed; }
            set { _blnCanClosed = value; }
        }
        private bool _blnCanNobid = true;
        public bool CanNoBid
        {
            get { return _blnCanNobid; }
            set { _blnCanNobid = value; }
        }
        private bool _blnCanNote = true;
        public bool CanNote
        {
            get { return _blnCanNote; }
            set { _blnCanNote = value; }
        }
        private bool _IsGSAEditPermission = true;
        public bool IsGSAEditPermission
        {
            get { return _IsGSAEditPermission; }
            set { _IsGSAEditPermission = value; }
        }
        private bool _IsDiffrentClient = false;
        public bool IsDiffrentClient
        {
            get { return _IsDiffrentClient; }
            set { _IsDiffrentClient = value; }
        }
        private bool _IsGSA = false;
        public bool IsGSA
        {
            get { return _IsGSA; }
            set { _IsGSA = value; }
        }
        #endregion

        #region Overrides


        private void SetupTable()
        {
            _tblPVVBOM.AllowSelection = false;
            _tblPVVBOM.Columns.Add(new FlexiDataColumn("Question", WidthManager.GetWidth(WidthManager.ColumnWidth.HUBRFQBOMItemsNotes)));
            _tblPVVBOM.Columns.Add(new FlexiDataColumn("Answer", WidthManager.GetWidth(WidthManager.ColumnWidth.HUBRFQBOMItemsNotes)));

        }
        /// <summary>
        /// Oninit
        /// </summary>
        /// <param name="e"></param>
        protected override void OnInit(EventArgs e) {
			base.OnInit(e);
			WireUpControls();
			AddScriptReference("Controls.Nuggets.BOMPVV.BOMPVV.js");
			TitleText = Functions.GetGlobalResource("Nuggets", "BOMPVV");
			if (_objQSManager.BOMID > 0) _intBOMID = _objQSManager.BOMID;

            SetupTable();
        }

		protected override void OnLoad(EventArgs e) {
			SetupScriptDescriptors();
           
            base.OnLoad(e);
		}

        protected override void OnPreRender(EventArgs e)
        {
            _ibtnEdit.Visible = true;
            _ibtnDelete.Visible = true;
            _ibtnView.Visible = true;
            base.OnPreRender(e);
        }

		#endregion

		/// <summary>
		/// Sets up AJAX script descriptors
		/// </summary>
		private void SetupScriptDescriptors() {
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Nuggets.BOMPVV", ctlDesignBase.ClientID);
			 _scScriptControlDescriptor.AddElementProperty("ibtnEdit", _ibtnEdit.ClientID);
			 _scScriptControlDescriptor.AddElementProperty("ibtnDelete", _ibtnDelete.ClientID);
             _scScriptControlDescriptor.AddElementProperty("ibtnView", _ibtnView.ClientID);


            _scScriptControlDescriptor.AddProperty("intBOMID", _intBOMID);
            _scScriptControlDescriptor.AddProperty("blnPOHub", Convert.ToBoolean(SessionManager.IsPOHub));
            _scScriptControlDescriptor.AddProperty("IsDiffrentClient", _IsDiffrentClient);
            _scScriptControlDescriptor.AddProperty("ClientId", SessionManager.ClientID);
            _scScriptControlDescriptor.AddComponentProperty("tblPVVBOM", _tblPVVBOM.ClientID);
        }

		/// <summary>
		/// Wire up controls from the ascx
		/// </summary>
		private void WireUpControls() {
			_ibtnEdit = FindIconButton("ibtnEdit");
			_ibtnDelete = FindIconButton("ibtnDelete");
            _ibtnView = FindIconButton("ibtnView");
            _tblPVVBOM = (FlexiDataTable)ctlDesignBase.FindContentControl("tblPVVBOM");


        }

    }
}
