﻿/* 
===========================================================================================
TASK      			UPDATED BY     		DATE         		ACTION 			DESCRIPTION
[BUG-231988]		Cuong.DoX			09-April-2025		Create			Add APIImportedData
===========================================================================================
*/
CREATE OR ALTER PROCEDURE ups_Insert_Manual_IHS
    @Part NVARCHAR(100),
    @PartStatus NVARCHAR(100),
    @ManufacturerNo NVARCHAR(MAX),
    @OriginalEntryDate DATETIME,
    @PackageNo INT,
    @Descriptions NVARCHAR(50) = NULL,
    @HTSCode NVARCHAR(100) = NULL,
    @CountryOfOriginNo INT = NULL,
    @PackageCode NVARCHAR(100) = NULL,
    @MSLLevel NVARCHAR(100) = NULL,
    @ECCNCode NVARCHAR(50) = NULL,
    @UpdatedBy NVARCHAR(100),
    @IHSId INT OUT
AS
BEGIN
	DECLARE @ManufacturerName NVARCHAR(250) = ''
	SELECT @ManufacturerName = ManufacturerName FROM dbo.tbManufacturer manu 
	WHERE manu.ManufacturerId = @ManufacturerNo AND manu.Inactive = 0

	DECLARE @CountryOfOrigin NVARCHAR(200)
	SELECT @CountryOfOrigin = GlobalCountryName FROM dbo.tbGlobalCountryList WHERE GlobalCountryId = @CountryOfOriginNo

	DECLARE @PackageName NVARCHAR(200)
	SELECT @PackageName = PackageDescription FROM tbPackage WHERE PackageId = @PackageNo
    
	DECLARE @ECCN NVARCHAR(100)
	SELECT @ECCN = ECCNCode    
	FROM    dbo.tbeccn                        
	WHERE   ECCNId   = @ECCNCode     and  Inactive = 0   

	INSERT INTO tbIHSparts (
        Part
		, FullPart
		, ManufacturerName
		, ManufacturerNo
		, Descriptions
		, CountryOfOrigin
		, CountryOfOriginNo
		, MSL
		, HTSCode
		, Packaging
		, UpdatedBy
		, OriginalEntryDate
		, DLUP
		, PartStatus
		, ManufacturerFullName
		, PackageCode
		, ECCNCode
		, APIImportedData
    )
    VALUES (
        @Part
		, dbo.ufn_get_fullpart(@Part)
		, @ManufacturerName
		, @ManufacturerNo
		, @Descriptions
		, @CountryOfOrigin
		, @CountryOfOriginNo
		, REPLACE(REPLACE(@MSLLevel, 'MSL ', ''), 'MSL', '')
		, @HTSCode
		, @PackageName
		, @UpdatedBy
		, @OriginalEntryDate
		, GETDATE()
		, @PartStatus
		, @ManufacturerName
		, @PackageCode
		, @ECCN
		, 0
    )

	    SET @IHSId = scope_identity()                  
END
