///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.GILines_CloseInspection = function (element) {
    Rebound.GlobalTrader.Site.Controls.Forms.GILines_CloseInspection.initializeBase(this, [element]);
    this._intLineID = -1;
    this._intGIID = -1;
    this._strStartInspection = null;
    this._lblExplainStartInspection = null;
   
    this._aryUnpostedLineIDs = [];
    this._aryPostedLineIDs = [];
    this._intPurchaseOrderID = 0;
    this._ctlConfirm = null;
    this._intValidationCount = 0;
};

Rebound.GlobalTrader.Site.Controls.Forms.GILines_CloseInspection.prototype = {

    get_intPurchaseOrderID: function () { return this._intPurchaseOrderID; }, set_intPurchaseOrderID: function (v) { if (this._intPurchaseOrderID !== v) this._intPurchaseOrderID = v; },
    get_intLineID: function () { return this._intLineID; }, set_intLineID: function (value) { if (this._intLineID !== value) this._intLineID = value; },
    get_strStartInspection: function () { return this._strStartInspection; }, set_strStartInspection: function (value) { if (this._strStartInspection !== value) this._strStartInspection = value; },
   
    get_lblExplainStartInspection: function () { return this._lblExplainStartInspection; }, set_lblExplainStartInspection: function (value) { if (this._lblExplainStartInspection !== value) this._lblExplainStartInspection = value; },
   
    initialize: function () {
        Rebound.GlobalTrader.Site.Controls.Forms.GILines_CloseInspection.callBaseMethod(this, "initialize");
        this.addShown(Function.createDelegate(this, this.formShown));
    },

    formShown: function () {
        if (this._blnFirstTimeShown)
        {
            this._ctlConfirm = this.getFieldComponent("ctlConfirm");
            this._ctlConfirm.addClickYesEvent(Function.createDelegate(this, this.yesClicked));
            this._ctlConfirm.addClickNoEvent(Function.createDelegate(this, this.noClicked));

            //this._chkPartTicked = $find("ctl00_cphMain_ctlLines_ctlDB_ctl14_GILines_CloseInspection_ctlDB_chkPartTicked");
            //this._chkPartTicked.addClick(Function.createDelegate(this, this.EnableYesButtonPart));

            //this._chkPBComplete = $find("ctl00_cphMain_ctlLines_ctlDB_ctl14_GILines_CloseInspection_ctlDB_chkPBComplete");
            //this._chkPBComplete.addClick(Function.createDelegate(this, this.EnableYesButtonPB));

            //this._chkPhotosAttached = $find("ctl00_cphMain_ctlLines_ctlDB_ctl14_GILines_CloseInspection_ctlDB_chkPhotosAttached");
            //this._chkPhotosAttached.addClick(Function.createDelegate(this, this.EnableYesButtonPhotoAttached));

            //this._chkBarCodeScan = $find("ctl00_cphMain_ctlLines_ctlDB_ctl14_GILines_CloseInspection_ctlDB_chkBarCodeScan");
            //this._chkBarCodeScan.addClick(Function.createDelegate(this, this.EnableYesButtonBarCode));

            //this._chkQueryFormat = $find("ctl00_cphMain_ctlLines_ctlDB_ctl14_GILines_CloseInspection_ctlDB_chkQueryFormat");
            //this._chkQueryFormat.addClick(Function.createDelegate(this, this.EnableYesButtonQueryFormat));

        }
        //$R_IBTN.enableButton(this._ctlConfirm._ibtnYes, false);
        //$find("ctl00_cphMain_ctlLines_ctlDB_ctl14_GILines_CloseInspection_ctlDB_chkPartTicked").enableButton(false);
        //$find("ctl00_cphMain_ctlLines_ctlDB_ctl14_GILines_CloseInspection_ctlDB_chkPBComplete").enableButton(false);
        //$find("ctl00_cphMain_ctlLines_ctlDB_ctl14_GILines_CloseInspection_ctlDB_chkPhotosAttached").enableButton(false);
        //$find("ctl00_cphMain_ctlLines_ctlDB_ctl14_GILines_CloseInspection_ctlDB_chkBarCodeScan").enableButton(false);
        //$find("ctl00_cphMain_ctlLines_ctlDB_ctl14_GILines_CloseInspection_ctlDB_chkQueryFormat").enableButton(false);

        //$find("ctl00_cphMain_ctlLines_ctlDB_ctl14_GILines_CloseInspection_ctlDB_chkPartTicked").setChecked(false);
        //$find("ctl00_cphMain_ctlLines_ctlDB_ctl14_GILines_CloseInspection_ctlDB_chkPBComplete").setChecked(false);
        //$find("ctl00_cphMain_ctlLines_ctlDB_ctl14_GILines_CloseInspection_ctlDB_chkPhotosAttached").setChecked(false);
        //$find("ctl00_cphMain_ctlLines_ctlDB_ctl14_GILines_CloseInspection_ctlDB_chkBarCodeScan").setChecked(false);
        //$find("ctl00_cphMain_ctlLines_ctlDB_ctl14_GILines_CloseInspection_ctlDB_chkQueryFormat").setChecked(false);

        this._intValidationCount = 0;
        this.GetCloseInspectionData();
        this.checkMode();
    },
    GetCloseInspectionData: function () {
        $R_FN.showElement(this._pnlLines, false);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/GILines");
        obj.set_DataObject("GILines");
        obj.set_DataAction("GetCloseInspectionData");
        obj.addParameter("ID", this._intLineID);
        obj.addDataOK(Function.createDelegate(this, this.GetCloseInspectionDataOK));
        obj.addError(Function.createDelegate(this, this.GetCloseInspectionDataError));
        obj.addTimeout(Function.createDelegate(this, this.GetCloseInspectionDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    GetCloseInspectionDataOK: function (args) {
        var res = args._result;
        
        $("#lblPartTicked").text(res.PartNumberFilled);
        $("#lblPBComplete").text(res.PackageBreakdownComplete);
        $("#lblPhotosAttached").text(res.PhotosAttached);
        $("#lblBarCodeScan").text(res.BarcodeScannedTicked);
        $("#lblQueryFormat").text(res.QueryFormatUse);

        //$find("ctl00_cphMain_ctlLines_ctlDB_ctl14_GILines_CloseInspection_ctlDB_chkPartTicked").enableButton(res.PartNumberFilledChkEnable);
        //$find("ctl00_cphMain_ctlLines_ctlDB_ctl14_GILines_CloseInspection_ctlDB_chkPBComplete").enableButton(res.PackageBreakdownCompleteChkEnable);
        //$find("ctl00_cphMain_ctlLines_ctlDB_ctl14_GILines_CloseInspection_ctlDB_chkPhotosAttached").enableButton(res.PhotosAttachedChkEnable);
        //$find("ctl00_cphMain_ctlLines_ctlDB_ctl14_GILines_CloseInspection_ctlDB_chkBarCodeScan").enableButton(res.BarcodeScannedTickedChkEnable);
        //$find("ctl00_cphMain_ctlLines_ctlDB_ctl14_GILines_CloseInspection_ctlDB_chkQueryFormat").enableButton(res.QueryFormatUseChkEnable);
    },
    GetCloseInspectionDataError: function (args) {
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },
    
    dispose: function () {
        if (this.isDisposed) return;
        if (this._ctlConfirm) this._ctlConfirm.dispose();
        this._ctlConfirm = null;
        this._intLineID = null;
        this._strStartInspection = null;
       
        this._lblExplainStartInspection = null;
        
        this._aryUnpostedLineIDs = null;
        this._aryPostedLineIDs = null;
        this._intPurchaseOrderID = null;
        this._intValidationCount = null;
        Rebound.GlobalTrader.Site.Controls.Forms.POLines_Post.callBaseMethod(this, "dispose");
    },

    yesClicked: function () {
        this.showSaving(true);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("Controls/Nuggets/GILines");
        obj.set_DataObject("GILines");
        obj.set_DataAction("CloseInspection");
        
        obj.addParameter("id", this._intLineID);
        obj.addParameter("Comment", this.getFieldValue("ctlComment"))
        
        obj.addDataOK(Function.createDelegate(this, this.saveComplete));
        obj.addError(Function.createDelegate(this, this.saveError));
        obj.addTimeout(Function.createDelegate(this, this.saveError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
        this.onSave();
    },

    noClicked: function () {
        this.showSaving(false);
        this.onNotConfirmed();
        this.setFieldValue("ctlComment", "");
    },

    saveError: function (args) {
        this.showSaving(false);
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    saveComplete: function (args) {
        this.showSaving(false);
        if (args._result.Result == true) {
            this.onSaveComplete();
            this.setFieldValue("ctlComment", "");
            setTimeout(function () {
                $("#ctl00_cphMain_ctlLines_ctlDB_imgRefresh").trigger('click');
            }, 900);
        } else {
            this._strErrorMessage = args._errorMessage;
            this.onSaveError();
        }
    },

    checkMode: function () {
          this.changeTitle(this._strStartInspection);
          $R_FN.showElement(this._lblExplainStartInspection, true);   
    },
    EnableYesButtonPart: function () {
        var chkPartTicked = this.getControlValue('ctl00_cphMain_ctlLines_ctlDB_ctl14_GILines_CloseInspection_ctlDB_chkPartTicked', 'CheckBox');
        if (chkPartTicked == true) {
            this._intValidationCount += 1;
        }
        else {
            this._intValidationCount -= 1;
        }
       
        if (this._intValidationCount == 5) {
            $R_IBTN.enableButton(this._ctlConfirm._ibtnYes, true);
        } else {
            $R_IBTN.enableButton(this._ctlConfirm._ibtnYes, false);
        }
    },
    EnableYesButtonPB: function () { 
        var chkPBComplete = this.getControlValue('ctl00_cphMain_ctlLines_ctlDB_ctl14_GILines_CloseInspection_ctlDB_chkPBComplete', 'CheckBox');
        if (chkPBComplete == true) {
            this._intValidationCount += 1;
        }
        else {
            this._intValidationCount -= 1;
        }
       
        if (this._intValidationCount == 5) {
            $R_IBTN.enableButton(this._ctlConfirm._ibtnYes, true);
        } else {
            $R_IBTN.enableButton(this._ctlConfirm._ibtnYes, false);
        }
    },
    EnableYesButtonPhotoAttached: function () {
        var chkPhotosAttached = this.getControlValue('ctl00_cphMain_ctlLines_ctlDB_ctl14_GILines_CloseInspection_ctlDB_chkPhotosAttached', 'CheckBox');
        if (chkPhotosAttached  == true) {
            this._intValidationCount += 1;
        }
        else {
            this._intValidationCount -= 1;
        }

        if (this._intValidationCount == 5) {
            $R_IBTN.enableButton(this._ctlConfirm._ibtnYes, true);
        } else {
            $R_IBTN.enableButton(this._ctlConfirm._ibtnYes, false);
        }
    },
    EnableYesButtonBarCode: function () {
        
        var chkBarCodeScan = this.getControlValue('ctl00_cphMain_ctlLines_ctlDB_ctl14_GILines_CloseInspection_ctlDB_chkBarCodeScan', 'CheckBox');
        if (chkBarCodeScan == true) {
            this._intValidationCount += 1;
        }
        else {
            this._intValidationCount -= 1;
        }
        
        if (this._intValidationCount == 5) {
            $R_IBTN.enableButton(this._ctlConfirm._ibtnYes, true);
        } else {
            $R_IBTN.enableButton(this._ctlConfirm._ibtnYes, false);
        }
    },
    EnableYesButtonQueryFormat: function () {
        var chkQueryFormat = this.getControlValue('ctl00_cphMain_ctlLines_ctlDB_ctl14_GILines_CloseInspection_ctlDB_chkQueryFormat', 'CheckBox');
        if (chkQueryFormat == true) {
            this._intValidationCount += 1;
        }
        else {
            this._intValidationCount -= 1;
        }
        if (this._intValidationCount == 5) {
            $R_IBTN.enableButton(this._ctlConfirm._ibtnYes, true);
        } else {
            $R_IBTN.enableButton(this._ctlConfirm._ibtnYes, false);
        }
    }
    

};

Rebound.GlobalTrader.Site.Controls.Forms.GILines_CloseInspection.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.GILines_CloseInspection", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
