//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option or rebuild the Visual Studio project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.VisualStudio.Web.Application.StronglyTypedResourceProxyBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class StockLogType {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal StockLogType() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Resources.StockLogType", global::System.Reflection.Assembly.Load("App_GlobalResources"));
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add.
        /// </summary>
        internal static string Add {
            get {
                return ResourceManager.GetString("Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} part(s) added from cancelled partial receipt.
        /// </summary>
        internal static string AddedFromCancelledPartialReceipts {
            get {
                return ResourceManager.GetString("AddedFromCancelledPartialReceipts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Added {0} part(s) on Customer RMA {1}.
        /// </summary>
        internal static string AddedFromCRMA {
            get {
                return ResourceManager.GetString("AddedFromCRMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Created {0} pieces from Partial Receipt on Goods In {1}.
        /// </summary>
        internal static string AddedFromPartialReceipt {
            get {
                return ResourceManager.GetString("AddedFromPartialReceipt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Added {0} part(s) on Internal Purchase Order {1}.
        /// </summary>
        internal static string AddFromIPOLine {
            get {
                return ResourceManager.GetString("AddFromIPOLine", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Moved {0} part(s) to client Purchase Order {1}.
        /// </summary>
        internal static string AddFromPOLine {
            get {
                return ResourceManager.GetString("AddFromPOLine", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} part(s) allocated to Sales Order {1}.
        /// </summary>
        internal static string AllocatedToSalesOrder {
            get {
                return ResourceManager.GetString("AllocatedToSalesOrder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} part(s) allocated to Supplier RMA {1}.
        /// </summary>
        internal static string AllocatedToSRMA {
            get {
                return ResourceManager.GetString("AllocatedToSRMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} part(s) removed from allocation to Sales Order {1}.
        /// </summary>
        internal static string AllocationRemovedFromSalesOrder {
            get {
                return ResourceManager.GetString("AllocationRemovedFromSalesOrder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} part(s) removed from allocation to Supplier RMA {1}.
        /// </summary>
        internal static string AllocationRemovedFromSRMA {
            get {
                return ResourceManager.GetString("AllocationRemovedFromSRMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Automatically created from cancelled receipt.
        /// </summary>
        internal static string AutoCreatedFromCancelledReceipt {
            get {
                return ResourceManager.GetString("AutoCreatedFromCancelledReceipt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cancel.
        /// </summary>
        internal static string Cancel {
            get {
                return ResourceManager.GetString("Cancel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cancelled receipt.
        /// </summary>
        internal static string CancelledReceipt {
            get {
                return ResourceManager.GetString("CancelledReceipt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cancelled shipment of {0} parts for Debit Note {1}.
        /// </summary>
        internal static string CancelledShipmentForDebit {
            get {
                return ResourceManager.GetString("CancelledShipmentForDebit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cancelled shipment of {0} parts for Invoice {1}.
        /// </summary>
        internal static string CancelledShipmentForInvoice {
            get {
                return ResourceManager.GetString("CancelledShipmentForInvoice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Changes made from Received Purchase Order {0}.
        /// </summary>
        internal static string ChangesFromReceivePO {
            get {
                return ResourceManager.GetString("ChangesFromReceivePO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Changes made on Goods In {0}.
        /// </summary>
        internal static string ChangesOnGoodsIn {
            get {
                return ResourceManager.GetString("ChangesOnGoodsIn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} part(s) has been returned by Customer RMA {1} through Stock {2}.
        /// </summary>
        internal static string CMRAsStock {
            get {
                return ResourceManager.GetString("CMRAsStock", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to CRMA line item deleted.
        /// </summary>
        internal static string CRMALineDeleted {
            get {
                return ResourceManager.GetString("CRMALineDeleted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to (from CRMA {0}).
        /// </summary>
        internal static string CRMALineDeleted_FromCRMA {
            get {
                return ResourceManager.GetString("CRMALineDeleted_FromCRMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} part(s) has been returned from Stock {1}.
        /// </summary>
        internal static string CRMAsInitialStock {
            get {
                return ResourceManager.GetString("CRMAsInitialStock", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete.
        /// </summary>
        internal static string Delete {
            get {
                return ResourceManager.GetString("Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit.
        /// </summary>
        internal static string Edit {
            get {
                return ResourceManager.GetString("Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit of Customer RMA {0} adding {1} part(s).
        /// </summary>
        internal static string EditCRMA {
            get {
                return ResourceManager.GetString("EditCRMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit of Customer RMA {0} adding {1} part(s).
        /// </summary>
        internal static string EditCRMALine {
            get {
                return ResourceManager.GetString("EditCRMALine", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit of Customer RMA {0} removing {1} part(s).
        /// </summary>
        internal static string EditCRMALine_NegativeQuantity {
            get {
                return ResourceManager.GetString("EditCRMALine_NegativeQuantity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit of Customer RMA {0}.
        /// </summary>
        internal static string EditCRMALine_NoQuantityChange {
            get {
                return ResourceManager.GetString("EditCRMALine_NoQuantityChange", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit of Purchase Order {0} adding {1} part(s).
        /// </summary>
        internal static string EditPOLine {
            get {
                return ResourceManager.GetString("EditPOLine", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit of Purchase Order {0} removing {1} part(s).
        /// </summary>
        internal static string EditPOLine_NegativeQuantity {
            get {
                return ResourceManager.GetString("EditPOLine_NegativeQuantity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit of Purchase Order {0}.
        /// </summary>
        internal static string EditPOLine_NoQuantityChange {
            get {
                return ResourceManager.GetString("EditPOLine_NoQuantityChange", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Split from GoodsIn {0} pieces from this entry to {1}.
        /// </summary>
        internal static string GISplit {
            get {
                return ResourceManager.GetString("GISplit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Imported From CSV.
        /// </summary>
        internal static string ImportedFromCSV {
            get {
                return ResourceManager.GetString("ImportedFromCSV", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Released.
        /// </summary>
        internal static string Inspected {
            get {
                return ResourceManager.GetString("Inspected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Complete Inspection.
        /// </summary>
        internal static string InspectionCompletedDetails {
            get {
                return ResourceManager.GetString("InspectionCompletedDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reopen Inspection.
        /// </summary>
        internal static string InspectionReopenDetails {
            get {
                return ResourceManager.GetString("InspectionReopenDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Start Inspection.
        /// </summary>
        internal static string InspectionstartedDetails {
            get {
                return ResourceManager.GetString("InspectionstartedDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Linked to Purchase Order {0}.
        /// </summary>
        internal static string LinkPO {
            get {
                return ResourceManager.GetString("LinkPO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Linked to Purchase Order.
        /// </summary>
        internal static string LinkPO_UnknownPO {
            get {
                return ResourceManager.GetString("LinkPO_UnknownPO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Manually added stock.
        /// </summary>
        internal static string ManuallyAdded {
            get {
                return ResourceManager.GetString("ManuallyAdded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Physically Inspected.
        /// </summary>
        internal static string PhysicalInspect {
            get {
                return ResourceManager.GetString("PhysicalInspect", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchase order line item deleted.
        /// </summary>
        internal static string POLineDeleted {
            get {
                return ResourceManager.GetString("POLineDeleted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to (from Purchase Order {0}).
        /// </summary>
        internal static string POLineDeleted_FromPO {
            get {
                return ResourceManager.GetString("POLineDeleted_FromPO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quarantined.
        /// </summary>
        internal static string Quarantine {
            get {
                return ResourceManager.GetString("Quarantine", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Removed from quarantine.
        /// </summary>
        internal static string QuarantineRemoved {
            get {
                return ResourceManager.GetString("QuarantineRemoved", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Received {0} part(s).
        /// </summary>
        internal static string Received {
            get {
                return ResourceManager.GetString("Received", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Received {0} part(s) on Goods In {1}.
        /// </summary>
        internal static string ReceivedOnGoodsIn {
            get {
                return ResourceManager.GetString("ReceivedOnGoodsIn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GI Line released to Quarantine.
        /// </summary>
        internal static string ReleasedWhenQuarantined {
            get {
                return ResourceManager.GetString("ReleasedWhenQuarantined", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipped.
        /// </summary>
        internal static string Ship {
            get {
                return ResourceManager.GetString("Ship", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipping cost updated with PO bank fee.
        /// </summary>
        internal static string ShipInCostUpdateWithBankFee {
            get {
                return ResourceManager.GetString("ShipInCostUpdateWithBankFee", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipped {0} part(s) on Invoice {1}.
        /// </summary>
        internal static string ShippedInvoice {
            get {
                return ResourceManager.GetString("ShippedInvoice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipped {0} part(s) on Supplier RMA {1}.
        /// </summary>
        internal static string ShippedSRMA {
            get {
                return ResourceManager.GetString("ShippedSRMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Split.
        /// </summary>
        internal static string Split {
            get {
                return ResourceManager.GetString("Split", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Created by splitting {0} parts from {1}.
        /// </summary>
        internal static string SplitNew {
            get {
                return ResourceManager.GetString("SplitNew", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to an existing stock entry.
        /// </summary>
        internal static string SplitNew_UnknownSourceStock {
            get {
                return ResourceManager.GetString("SplitNew_UnknownSourceStock", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Split {0} pieces from this entry to {1}.
        /// </summary>
        internal static string SplitOriginal {
            get {
                return ResourceManager.GetString("SplitOriginal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Split {0} pieces from this entry.
        /// </summary>
        internal static string SplitOriginal_UnknownSourceStock {
            get {
                return ResourceManager.GetString("SplitOriginal_UnknownSourceStock", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stock Landed Cost Update From GI.
        /// </summary>
        internal static string StockLandedCostUpdated {
            get {
                return ResourceManager.GetString("StockLandedCostUpdated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stock Written Down.
        /// </summary>
        internal static string StockWrittenDown {
            get {
                return ResourceManager.GetString("StockWrittenDown", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unlinked from Purchase Order {0}.
        /// </summary>
        internal static string UnlinkPO {
            get {
                return ResourceManager.GetString("UnlinkPO", resourceCulture);
            }
        }
    }
}
