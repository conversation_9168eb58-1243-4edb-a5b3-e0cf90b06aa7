<%@ Control Language="C#" CodeBehind="BOMItems_ConfirmPartwatch.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_ConfirmPartwatch" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="false" AllowQuickHelp="false">
<Explanation><%=Functions.GetGlobalResource("FormExplanations", "BOMItem_ApplyPartwatch")%></Explanation>
	<Content>
		<ReboundUI_Table:Form id="frm" runat="server">		     
	        <ReboundUI_Form:FormField id="ctlConfirm" runat="server" FieldID="ctlConfirmation" ResourceTitle="SaveConfirm">
				<Field><ReboundUI:Confirmation ID="ctlConfirmation" runat="server" />

					 <div class="cssload-loader" id="partwatchLoad">Processing..</div>
				</Field>
			</ReboundUI_Form:FormField>
		</ReboundUI_Table:Form>		 
	</Content>
</ReboundUI_Form:DesignBase>
<style>
    
        .disable-click {
            pointer-events: none;
        }
	 .cssload-loader {
                            width: 244px;
                            height: 49px;
                            line-height: 49px;
                            text-align: center;
                            position: absolute;
                            left: 146px;
                            top: -102%;
                            transform: translate(-50%, -50%);
                            -o-transform: translate(-50%, -50%);
                            -ms-transform: translate(-50%, -50%);
                            -webkit-transform: translate(-50%, -50%);
                            -moz-transform: translate(-50%, -50%);
                            font-family: helvetica, arial, sans-serif;
                            text-transform: uppercase;
                            font-weight: 900;
                            font-size: 18px;
                            color: rgb(206, 66, 51);
                            letter-spacing: 0.2em;
                            background-color: aliceblue;
                        }
</style>
