/* main elements */
/********************************************************/
html, body, form {
	margin: 0px;
}
body {
	background-color: #ffffff;
	padding: 0px;
	background-repeat: no-repeat;
	background-position: top left;
	text-shadow: rgba(0,0,0,0.01) 0 0 0;
}
body, div, p, td  {
	font-family: Segoe UI, Tahoma, Arial, Geneva, Sans-Serif;
	font-size: 12px;
}
select, input, textarea {
	font-family: Segoe UI, Tahoma, Arial, Geneva, Sans-Serif;
	font-size: 12px;
	-moz-border-radius: 2px;
	-webkit-border-radius: 2px;
	border-style: none;
	padding: 2px;
}
form {
	margin: 0px;
	padding: 0px;
}
div.clearing {
	clear: both;
	padding: 0px;
	margin: 0px;
	height: 1px;
	font-size: 1px;
	line-height: 1px;
	visibility: hidden;
}
a {
	color: #0000ff;
	cursor: pointer;
}
h4 {
	margin: 0px 0px 5px 0px;
	font-size: 12px;
}
h4.extraTopMargin {
	margin-top: 15px;
}
h5 {
	font-size: 10px;
	margin: 0px 0px 2px;
	text-transform: uppercase;
}
.invisible {
	display: none !important;
	visibility: hidden !important;
	padding: 0px !important;
	margin: 0px !important;
}
.noneFound {
	color: #999999;
	font-size: 11px;
	font-style: italic;
	font-weight: normal;
}
.lowZIndex {
	z-index: -1;
}
.floatLeft {
	float: left;
}
.floatRight {
	float: right;
}
.rightSpacing {
	margin-right: 10px;
}
.leftSpacing {
	margin-left: 10px;
}
.modalBackground {
	background-color: #000000;
	height: 20000px;
	width: 100%;
	position: absolute;
	top: 0px;
	left: 220px;
	z-index: 9999;
	filter: alpha(opacity=85);
	-moz-opacity: .85;
	opacity: 0.85;
}

/* sub title bar */
/********************************************************/
.subTitleBar {
	background-image: url(images/overlays/black75.png);
	border-top: solid 1px #333333;
	border-bottom: solid 1px #333333;
	padding: 0px;
	height: 32px;
	position: relative;
	z-index: 3000;
}
.subTitleBarInner {
	padding: 0px;
	height: 35px;
	position: relative;
}
h2 {
	font-family: Lucida Sans Unicode, Arial;
	font-size: 15px;
	font-weight: bold;
	color: #eeeeee;
	margin: 0px;
	position: absolute;
	bottom: 6px;
	left: 10px;
}
.subTitleBarButtons {
	position: absolute;
	bottom: 5px;
	right: 3px;
}
.subDropDown {
	position: relative;
	float: left;
	width: 145px;
	height: 22px;
	padding-top: 5px;
	background-image: url(images/structure/sub/buttonbg.gif);
	background-position: center;
	background-repeat: no-repeat;
	top: 2px;
	margin-left: 1px;
}
.subDropDown .button {
	position: relative;
	text-align: left;
	left: -10px;
}
.subDropDown .menu {
	top: 25px;
	right: 0px;
	width: 145px;
	position: absolute;
	filter: alpha(opacity=90);
	-moz-opacity: .9;
	opacity: 0.9;
}
.subDropDown .menu .menuItems {
	background-color: #191919;
	padding: 8px 10px 2px;
	text-align: left;
	margin: 0px;
}
.subDropDown .menu .menuFooter {
	background-image: url(images/structure/sub/buttonbg.gif);
	background-repeat: no-repeat;
	background-position: center bottom;
	height: 3px;
}
.subDropDown .menuOff {
	visibility: hidden;
	position: absolute;
}
.subDropDown .line {
	height: 1px;
	margin: 3px 0px;
	border-bottom: dotted 1px #e0e0e0;
}
.subDropDown .menu div {
	margin-bottom: 5px;
}
.subDropDown .menu div a {
	text-decoration: none;
	font-size: 11px;
	color: #bbbbbb;
}
.subDropDown .menu div a:hover {
	text-decoration: underline;
}
/* main structure */
/********************************************************/
.mainAreaOuter {
	background-image: url(images/structure/bg_content.png);
	background-repeat: repeat-x;
	margin: 0px;
	padding: 0px;
	width: 100%;
}
.mainAreaInner {
	margin: 0px;
	padding: 0px;
	width: 100%;
	z-index: 4000;
}
.mainArea_On, .mainArea_Off {
	overflow: hidden;
	position: relative;
	width: 100%;
	margin: 0px;
	padding: 0px;
	min-height: 100%;
	background-repeat: repeat-y; /*** This is our faux columns ***/
	text-align: left;
}
.mainArea_On {
	background-image: url(images/structure/left/bg_on.png);
}
.mainArea_Off {
	background-image: url(images/structure/left/bg_off.gif);
}
.mainArea_On .mainLeft, .mainArea_Off .mainLeft {
	float: left;
	overflow: visible;
	cursor: default;
	z-index: 4000;
	min-height: 1500px;
}
.mainArea_On .mainLeft {
	width: 220px;
	margin-right: -220px;
}
.mainArea_Off .mainLeft {
	width: 11px;
	margin-right: -11px;
}
.mainRight {
	position: relative;
	width: 100%;
	cursor: default;
	min-height: 1500px;
	float:left;
}
.mainRightInner {
	padding: 10px 10px 200px;
}
.mainArea_On .mainRightInner {
	margin-left: 220px;
}
.mainArea_Off .mainRightInner {
	margin-left: 11px;
}
.mainAreaShadow {
	height: 6px;
	width: 245px;
	position: relative;
	top: 0px;
	left: 11px;
	margin-bottom: -6px;
}
.leftbarButton_On, .leftbarButton_Off {
	width: 11px;
	height: 250px;
	position: absolute;
	top: 0px;
	background-repeat: no-repeat;
	cursor: pointer;
	z-index: 1;
}
.leftbarButton_On {
	background-image: url(images/structure/left/on.gif);
	left: 210px;
}
.leftbarButton_Off {
	background-image: url(images/structure/left/off.gif);
}
.leftbarContent {
	min-height: 190px;
	padding-bottom: 5px;
	width: 100%;
	position: relative;
	overflow: visible;
	z-index: 1;
}
.footer {
	clear: both;
	background-image: url(images/structure/footer/bg.jpg);
	background-repeat: repeat-x;
	background-position: left top;
	border-top: solid #999999 1px;
	padding: 10px 5px;
}
.footer p {
	color: #aaaaaa;
	font-size: 11px;
	margin: 0px 0px 4px 0px;
	padding: 0px;
}
.footer a {
	color: #aaaaaa;
	text-decoration: none;
}
.footer a:hover {
	color: #666666;
	text-decoration: underline;
}
.footerRebound {
	float: right;
	padding-right: 5px;
	padding-bottom:10px;
}
/* breadcrumb */
/********************************************************/
.breadcrumb, .breadcrumb a {
	color: #cccccc;
	font-size: 11px;
}
.breadcrumb {
	font-weight: bold;
	margin-bottom: 10px;
	padding: 9px 5px 0px;
}
.breadcrumb a {
	font-weight: normal;
	text-decoration: none;
}
.breadcrumb a:hover {
	text-decoration: underline;
}

/* nub buttons */
/* ******************************************* */
.nubButton {
	background-image: url(images/nubs/nub.gif);
	background-repeat: no-repeat;
	background-position: left 2px;
	margin-left: 20px;
	padding-left: 13px;
	height: 12px;
	text-decoration: none;
	color: #0000ff;
}
.nubButton:hover {
	text-decoration: underline;
	color: #0000ff;
}
.nubButtonAlignLeft {
	margin-left: 0px;
	margin-right: 15px;
}
.nubButtonFloatLeft {
	margin-left: 0px;
	float: left;
	padding-right: 20px;
	margin-bottom: 10px;
}
.nubButtonInline {
	margin: 0px;
	padding-left: 11px;
}

/* big icons */
/* ******************************************* */
.bigIconHalfFloat {
	width: 50%;
	float: left;
}
.bigIconFull {
	width: 100%;
	float: left;
}
.bigIconHalfNoFloat {
	width: 50%;
	clear: right;
}
.bigIcon {
	background-repeat: no-repeat;
	background-position: 10px 10px;
	padding: 35px 10px 10px 100px;
	border: dotted 1px #cccccc;
	margin: 0px 10px 10px 0px;
	background-color: #ffffff;
	position: relative;
	-moz-border-radius:4px; 
	-webkit-border-radius:4px; 
}
.bigIcon .title {
	font-family: Lucida Sans Unicode, Arial;
	font-size: 16px;
	font-weight: normal;
	text-decoration: none;
	color: #000000;
	display: block;
	margin-bottom: 5px;
}
.bigIcon .explain {
	color: #666666;
	font-family: Lucida Sans Unicode, Arial;
	font-size: 11px;
	padding-bottom: 5px;
}
.bigIcon .buttons {
	margin-bottom: 5px;
	line-height: 20px;
}
.bigIcon .quickSearchTitle {
	margin-bottom: 2px;
}


/* Line Paging Next */
/********************************************************/
.linePagingNextPrev {
	text-decoration: none;
	color: #009900;
	font-size: 14px;
}
.linePagingNext {
	margin-left: 5px;
}
.linePagingPrev {
	margin-right: 5px;
}
.linePagingInactive {
	text-decoration: none;
	color: #999999;
	font-size: 12px;
}

/* header image */
/********************************************************/
.headerImage {
	border: dotted 1px #dddddd;
	padding: 5px;
	margin: 0px 10px 10px 0px;
	float: left;
}
.headerImage .title {
	font-weight: bold;
	font-size: 11px;
}
.headerImage .image {
}

/* Ellipses - for getting more data */
/********************************************************/
a.ellipses {
	text-decoration: none;
}
a.ellipses:hover {
	text-decoration: underline;
}
.ellipsesLoading {
	background-image: url("images/loading/loading7.gif");
	background-position: left center;
	background-repeat: no-repeat;
	display: inline-block;
	height: 12px;
	width: 12px;
}
.ellipsesError {
	color: #ff0000;
	display: inline;
}

/* messages */
/********************************************************/
.warning {
	margin-bottom: 10px;
	border: 1px solid #663399;
	padding: 8px 7px 7px 30px;
	background-color: #f0f0f0;
	background-image: url(images/warning.gif);
	background-repeat: no-repeat;
	background-position: 5px 5px;
}
.warning h4 {
	font-weight: normal;
	font-family: Lucida Sans Unicode, Arial;
	border-bottom: dotted 1px #cccccc;
}
.loginError {
	margin-top: 10px;
	border: 1px solid #000000;
	color: #ffffff;
	padding: 10px;
	background-color: #ff0000;
}
.error h4 {
	font-weight: normal;
	font-family: Lucida Sans Unicode, Arial;
	border-bottom: dotted 1px #cccccc;
	color: #ff0000;
}

/* messages */
/********************************************************/
.newMessages {
	background-color: #ffffcc;
	padding: 5px;
	border: solid 3px #ffff00;
	margin-bottom: 10px;
	position: relative;
}
.newMessages h4 {
	margin-bottom: 5px;
}
.newMessagesClose {
	position: absolute;
	right: 5px;
	top: 2px;
}
.newMessagesClose a {
	text-decoration: none;
	color: #ff9900;
	font-size: 12px;
	font-weight: bold;
}
.newMessagesClose a:hover {
	color: #bb6600;
	font-size: 12px;
}
.mailFolder, .mailFolderOpen {
	background-position: 0px 2px;
	background-repeat: no-repeat;
	padding: 2px 0px 2px 20px;
	font-size: 11px;
	margin-top: 5px;
}
.mailFolder {
	background-image: url(images/mail/unread.gif);
}
.mailFolderOpen {
	background-image: url(images/mail/folder_open.gif);
	background-color: #eeeeee;
}
.mailFolderWithNewMessages {
	font-weight: bold;
}
.mailFolder a, .mailFolderOpen a {
	text-decoration: none;
	color: #333333;
}
.mailFolder a:hover, .mailFolderOpen a:hover {
	text-decoration: underline;
}
h4.mail {
	background-color: #ccffc3;
	border-width: 0px !important;
	padding: 2px;
	position: relative;
}
a.mailMessageClose {
	color: #000000;
	font-size: 13px;
	position: absolute;
	right: 5px;
	text-decoration: none;
	top: 0px;
}
div.mailGroup, div.mailRecipientGroup {
	background-image: url(images/mail/group_small.gif);
	background-position: 1px;
	background-repeat: no-repeat;
	font-size: 11px;
	font-weight: bold;
	padding-left: 14px;
}
div.mailRecipient {
	padding-bottom: 8px;
}
div.mailRecipientGroup {
	background-position: 1px 1px;
}
div.mailInstructions {
	font-size: 11px;
	color: #666666;
	padding: 0px 0px 5px 2px;
}
div.quotedMail {
	background-color: #F0F0FF;
	border: 2px solid #CFCFFF;
	color: #0000FF;
	padding: 5px;
}
/* To Do Alert */
/********************************************************/
.toDoAlert {
	position: absolute;
	width: 100%;
	text-align: center;
	margin: 0px auto;
	z-index: 10000;
}
.toDoAlertInner {
	text-align: left;
	margin: 0px auto;
	background-color: #ffffcc;
	border: 2px solid #000000;
	width: 400px;
	padding: 20px;
}
.toDoAlert h4 {
	border-bottom: dotted 1px #000000;
	padding-bottom: 2px;
	margin-bottom: 5px;
}
.toDoAlertButtons {
	margin-top: 10px;
}
.toDoAlertSnoozeMessage {
	margin: 5px 0px;
}
/* login screen */
/********************************************************/
#loginContainer {
	height: 100%;
	width: 100%;
	text-align: center;
	margin: 12% auto 0px;
	top: 0px;
	left: 0px;
}
#loginOuter {
	margin: 0px auto;
	width: 350px;
	text-align: left;
}
#loginContainer h1 {
	text-indent: -9000px;
	background-repeat: no-repeat;
	width: 247px;
	height: 20px;
	margin: 0px;
	position: relative;
	top:9px;
	left:5px;
}
#loginContainer h2 {
	color: #000000;
	font-weight: normal;
	font-family: Lucida Sans Unicode;
	font-size: 12px;
	text-transform: uppercase;
	margin: 0px;
}
#loginContainer input {
	border-style:none;
	padding:2px;
}
#loginGo {
	text-align: right;
	margin-bottom: 10px;
}
#loginContainer table.formRows {
	margin:10px 0px;
}
#loginContainer table.formRows tr.formRow {
	margin: 0px;
	padding: 0px;
}
#loginContainer table.formRows td.title {
	color:#FFFFFF;
	font-size:12px;
	font-weight:normal;
	width:100px;
}
#loginFooter {
	width: 350px;
	margin: 20px auto;
	text-align: left;
	clear: both;
	position:relative;
	top:-36px;
	left:5px;
	z-index:-1;
}
#loginFooter p {
	color: #cccccc;
	font-size: 10px;
	margin: 0px 0px 2px 0px;
	padding: 0px;
	font-weight: bold;
}
#loginFooter a {
	color: #cccccc;
	text-decoration: none;
}
#loginFooter a:hover {
	color: #999999;
	text-decoration: underline;
}
.loginBox {
	margin-top: 20px;
}
#loginContainer .formRows td {
	padding-bottom:5px;
	padding-top:3px;
}

/* Database Check */
/********************************************************/
.loginDBCheck {
	height: 128px;
	margin: 10px 0px 51px 0px;
}
.loginDBCheck div {
	height: 100%;
	font-family: Lucida Sans Unicode;
	font-size: 14px;
	color: #000000;
}
.loginDBCheck .dbChecking {
	background-image: url(images/login/db.jpg);
	background-repeat: no-repeat;
}
.loginDBCheck .dbLoading {
	background-image: url(images/login/loading_db.gif);
	background-repeat: no-repeat;
	background-position: 6px 54px;
	padding: 61px 0px 0px 40px;
}
.loginDBCheck .dbOK {
	background-image: url(images/login/db_ok.jpg);
	background-repeat: no-repeat;
	padding: 61px 0px 0px 10px;
}
.loginDBCheck .dbNotFound {
	background-image: url(images/login/db_error.jpg);
	background-repeat: no-repeat;
	padding: 28px 120px 0px 10px;
	font-size: 12px;
	line-height: 14px;
}
.loginDBCheck .dbNotFound a {
	color: #0000cc;
	text-decoration: none;
}
.loginDBCheck .dbNotFound a:hover {
	text-decoration: underline;
}
/* License Check */
/********************************************************/
.licenseInvalid, .licenseWarning {
	background-repeat: no-repeat;
	height: 83px;
	margin-top: 30px;
}
.licenseInvalid {
	background-image: url(images/login/license_error.jpg);
}
.licenseWarning {
	background-image: url(images/login/license_warn.jpg);
	margin-bottom:-5px;
}
.licenseInvalid h4, .licenseWarning h4 {
	color: #FFFFFF;
	font-family: Lucida Sans;
	font-size: 12px;
	font-weight: bold;
	margin-bottom: 17px;
	padding: 9px 0px 0px 10px;
	text-transform: uppercase;
}
.licenseInvalid .text, .licenseWarning .text {
	color: #ffffff;
	padding: 0px 80px 0px 10px;
	line-height: 16px;
}
.licenseWarning h4, .licenseWarning .text {
	color: #bb6600;
}
.licenseInvalid a {
	color:#cccccc;
	text-decoration:none;
}
.licenseInvalid a:hover {
	color:#ffffff;
	text-decoration:underline;
}
/* Nub buttons */
/********************************************************/
.nubButton {
	background-image: url(images/nubs/nub.gif);
	background-repeat: no-repeat;
	background-position: left 2px;
	margin-left: 20px;
	padding-left: 13px;
	height: 12px;
	text-decoration: none;
	color: #0000ff;
}
.nubButton.todo_red {
	color: red;
}
.nubButton:hover {
	text-decoration: underline;
	color: #0000ff;
}
.nubButtonAlignLeft {
	margin-left: 0px;
	margin-right: 15px;
}
.nubButtonFloatLeft {
	margin-left: 0px;
	float: left;
	padding-right: 20px;
	margin-bottom: 10px;
}
.nubButtonInline {
	margin: 0px;
	padding-left: 11px;
}

/* misc */
/********************************************************/
div.inPageActions {
	line-height: 16px;
}
a.inPageAction {
	background-color: #c2ffb8;
	padding: 0px 3px;
	margin: 0px 5px 2px 0px;
	color: #72b058;
	font-weight: bold;
	text-decoration: none;
	font-size: 11px;
	float: left;
}
a.inPageAction:hover {
	background-color: #82c068;
	color: #ffffff;
}
.getData {
	background-color: #f0f0f0;
	padding: 10px;
}
.getData a {
	text-decoration: none;
	background-image: url(images/structure/icons/go.gif);
	background-position: left center;
	background-repeat: no-repeat;
	padding-left: 17px;
	color: #009900;
}
.getData a:hover {
	text-decoration: underline;
}
.searchType_StartsWith, .searchType_Contains, .searchType_EndsWith {
	background-repeat: no-repeat;
	background-position: left center;
	padding: 2px;
}
.searchType_StartsWith {
	background-image: url(images/FilterDataItemRows/startswith.gif);
}
.searchType_Contains {
	background-image: url(images/FilterDataItemRows/contains.gif);
}
.searchType_EndsWith {
	background-image: url(images/FilterDataItemRows/endswith.gif);
}
.searchType_ExactWith {
	background-image: url(images/FilterDataItemRows/endswith.gif);
}
a.quickSearchReselect {
	text-decoration: none;
	color: #064500;
	font-weight: bold;
	font-size: 11px;
	margin-left: 20px;
}
a.quickSearchReselect:hover {
	color: #000000;
}
/* misc */
/********************************************************/
.loading, .subInfoLoading {
	color: #ff9900;
	font-weight: bold;
	font-size: 10px;
	margin-bottom: 5px;
	padding-left: 20px;
	background-image: url(images/loading/loading.gif);
	background-repeat: no-repeat;
	background-position: 0px center;
	text-transform: uppercase;
	line-height: 20px;
}
.subInfoLoading {
	margin: 10px 0px;
}
.line {
	height: 1px;
	margin: 10px 0px;
	border-bottom: dotted 1px #aae2a5;
}
/* message box */
/********************************************************/
.messageBox .boxContent {
	background-color: #ffffff;
	padding: 25px 10px;
	background-image: url(images/Nuggets/allwhite/bg.jpg);
	background-repeat: repeat-x;
	background-position: bottom;
	border-color: #bbbbbb;
}
.messageBox .boxContent div {
	font-size: 14px;
}
.messageBox h4 {
	font-family: Lucida Sans Unicode, Arial;
	font-weight: normal;
	font-size: 14px;
	color: #000000;
	margin: 0px 0px 15px;
	padding: 2px 0px 2px 0px;
	border-bottom: dotted 1px #cccccc;
}
.messageBox .messageBoxError, .messageBox .messageBoxWarning, .messageBox .messageBoxInformation {
	background-position: 3px top;
	background-repeat: no-repeat;
	padding: 0px 0px 12px 42px;
}
.messageBox .messageBoxError {
	background-image: url(images/messages/error.png);
	color: #ff0000;
}
.messageBox .messageBoxInformation {
	background-image: url(images/messages/info.png);
	color: #666666;
}
.messageBox .messageBoxWarning {
	background-image: url(images/messages/warning.png);
	color: #663399;
}
.nubButtonAlignLeftForImp {
	margin-left: 0px;
	margin-right: 1px;
}
.tooltip .popupShipStatus {
    visibility: hidden;
    width: 150px;
    background-color: black;
    color: #fff;
    text-align: left;
    padding: 5px 10px;
    border-radius: 3px;
    font-size: 10px;
    margin-left:30px;
    float:left;
    
    /* Position the tooltip text - see examples below! */
    position: absolute;
    z-index: 1;
}
.tooltip:hover .popupShipStatus {
    visibility: visible;
}


.toggle-box {
  display: none;
}

.toggle-box + label {
  cursor: pointer;
  display: block;
  font-weight: bold;
  line-height: 21px;
  margin-bottom: 5px;
}

.toggle-box + label + div {
  display: none;
  margin-bottom: 10px;
  padding-left:50px;
}

.toggle-box:checked + label + div {
  display: block;
}

.toggle-box + label:before {
  background-color: #4F5150;
  -webkit-border-radius: 10px;
  -moz-border-radius: 10px;
  border-radius: 10px;
  color: #FFFFFF;
  content: "+";
  display: block;
  float: left;
  font-weight: bold;
  height: 20px;
  line-height: 20px;
  margin-right: 5px;
  text-align: center;
  width: 20px;
}

.toggle-box:checked + label:before {
  content: "\2212";
  }




