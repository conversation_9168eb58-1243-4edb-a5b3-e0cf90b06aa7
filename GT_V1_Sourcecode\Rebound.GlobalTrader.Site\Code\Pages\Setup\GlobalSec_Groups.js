Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Setup");Rebound.GlobalTrader.Site.Pages.Setup.GlobalSec_Groups=function(n){Rebound.GlobalTrader.Site.Pages.Setup.GlobalSec_Groups.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Pages.Setup.GlobalSec_Groups.prototype={get_ctlSecurityGroups:function(){return this._ctlSecurityGroups},set_ctlSecurityGroups:function(n){this._ctlSecurityGroups!==n&&(this._ctlSecurityGroups=n)},get_ctlSecurityGroupMembers:function(){return this._ctlSecurityGroupMembers},set_ctlSecurityGroupMembers:function(n){this._ctlSecurityGroupMembers!==n&&(this._ctlSecurityGroupMembers=n)},get_ctlSecurityGroupPermissionsOrders:function(){return this._ctlSecurityGroupPermissionsOrders},set_ctlSecurityGroupPermissionsOrders:function(n){this._ctlSecurityGroupPermissionsOrders!==n&&(this._ctlSecurityGroupPermissionsOrders=n)},get_ctlSecurityGroupPermissionsWarehouse:function(){return this._ctlSecurityGroupPermissionsWarehouse},set_ctlSecurityGroupPermissionsWarehouse:function(n){this._ctlSecurityGroupPermissionsWarehouse!==n&&(this._ctlSecurityGroupPermissionsWarehouse=n)},initialize:function(){Rebound.GlobalTrader.Site.Pages.Setup.GlobalSec_Groups.callBaseMethod(this,"initialize")},goInit:function(){this._ctlSecurityGroups.addSelectGroup(Function.createDelegate(this,this.ctlSecurityGroups_SelectGroup));this._ctlSecurityGroupMembers.addSaveEditComplete(Function.createDelegate(this,this.ctlSecurityGroupMembers_SaveEditComplete));this._ctlSecurityGroups.addDeleteComplete(Function.createDelegate(this,this.ctlSecurityGroupMembers_DeleteComplete));Rebound.GlobalTrader.Site.Pages.Setup.GlobalSec_Groups.callBaseMethod(this,"goInit")},dispose:function(){this.isDisposed||(this._ctlSecurityGroups&&this._ctlSecurityGroups.dispose(),this._ctlSecurityGroupMembers&&this._ctlSecurityGroupMembers.dispose(),this._ctlSecurityGroupPermissionsOrders&&this._ctlSecurityGroupPermissionsOrders.dispose(),this._ctlSecurityGroupPermissionsWarehouse&&this._ctlSecurityGroupPermissionsWarehouse.dispose(),this._ctlSecurityGroups=null,this._ctlSecurityGroupMembers=null,this._ctlSecurityGroupPermissionsOrders=null,this._ctlSecurityGroupPermissionsWarehouse=null,Rebound.GlobalTrader.Site.Pages.Setup.GlobalSec_Groups.callBaseMethod(this,"dispose"))},ctlSecurityGroups_SelectGroup:function(){this._ctlSecurityGroupMembers._intSecurityGroupID=this._ctlSecurityGroups._intSecurityGroupID;this._ctlSecurityGroupMembers.refresh();this._ctlSecurityGroupPermissionsOrders._intSecurityGroupID=this._ctlSecurityGroups._intSecurityGroupID;this._ctlSecurityGroupPermissionsOrders.refresh();this._ctlSecurityGroupPermissionsWarehouse._intSecurityGroupID=this._ctlSecurityGroups._intSecurityGroupID;this._ctlSecurityGroupPermissionsWarehouse.refresh();this.showNuggets(!0)},ctlSecurityGroupMembers_SaveEditComplete:function(){this._ctlSecurityGroups.refresh()},ctlSecurityGroupMembers_DeleteComplete:function(){this.showNuggets(!1)},showNuggets:function(n){this._ctlSecurityGroupMembers.show(n);this._ctlSecurityGroupPermissionsOrders.show(n);this._ctlSecurityGroupPermissionsWarehouse.show(n)}};Rebound.GlobalTrader.Site.Pages.Setup.GlobalSec_Groups.registerClass("Rebound.GlobalTrader.Site.Pages.Setup.GlobalSec_Groups",Rebound.GlobalTrader.Site.Pages.Content,Sys.IDisposable);