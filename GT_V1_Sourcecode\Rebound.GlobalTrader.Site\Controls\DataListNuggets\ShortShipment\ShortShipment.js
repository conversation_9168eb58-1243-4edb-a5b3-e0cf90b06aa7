Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DataListNuggets");Rebound.GlobalTrader.Site.Controls.DataListNuggets.ShortShipment=function(n){Rebound.GlobalTrader.Site.Controls.DataListNuggets.ShortShipment.initializeBase(this,[n]);this._LoginClientId=-1};Rebound.GlobalTrader.Site.Controls.DataListNuggets.ShortShipment.prototype={get_ibtnExportCSV:function(){return this._ibtnExportCSV},set_ibtnExportCSV:function(n){this._ibtnExportCSV!==n&&(this._ibtnExportCSV=n)},get_sortIndex:function(){return this._sortIndex},set_sortIndex:function(n){this._sortIndex!==n&&(this._sortIndex=n)},get_sortDir:function(){return this._sortDir},set_sortDir:function(n){this._sortDir!==n&&(this._sortDir=n)},get_pageIndex:function(){return this._pageIndex},set_pageIndex:function(n){this._pageIndex!==n&&(this._pageIndex=n)},get_pageSize:function(){return this._pageSize},set_pageSize:function(n){this._pageSize!==n&&(this._pageSize=n)},get_IsCanViewClient:function(){return this._IsCanViewClient},set_IsCanViewClient:function(n){this._IsCanViewClient!==n&&(this._IsCanViewClient=n)},get_LoginClientId:function(){return this._LoginClientId},set_LoginClientId:function(n){this._LoginClientId!==n&&(this._LoginClientId=n)},initialize:function(){this.addGetDataOKEvent(Function.createDelegate(this,this.getDataOK));this.addInitCompleteEvent(Function.createDelegate(this,this.initAfterBaseIsReady));this._strPathToData="controls/DataListNuggets/ShortShipment";this._strDataObject="ShortShipment";Rebound.GlobalTrader.Site.Controls.DataListNuggets.ShortShipment.callBaseMethod(this,"initialize");this._ibtnExportCSV&&$R_IBTN.addClick(this._ibtnExportCSV,Function.createDelegate(this,this.exportCSV))},initAfterBaseIsReady:function(){this.updateFilterVisibility();this.getData()},dispose:function(){this.isDisposed||(this._ibtnExportCSV&&$R_IBTN.clearHandlers(this._ibtnExportCSV),this._ibtnExportCSV=null,this._IsCanViewClient=null,this._LoginClientId=null,Rebound.GlobalTrader.Site.Controls.DataListNuggets.ShortShipment.callBaseMethod(this,"dispose"))},getDataOK:function(){for(var n,i,t=0,r=this._objResult.Results.length;t<r;t++)n=this._objResult.Results[t],i=[$R_FN.writeDoubleCellValue($RGT_nubButton_ShortShipmentDetails(n.ShortShipmentId,$R_FN.setCleanTextValue(n.ShortShipmentId)),$RGT_nubButton_DebitNote(n.DebitNoteNo,n.DebitNumber)),$R_FN.writeDoubleCellValue($RGT_nubButton_GoodsIn(n.GoodsInId,n.GoodsInNo),$R_FN.setCleanTextValue(n.PartNo)),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(n.Raisedby),$R_FN.setCleanTextValue(n.PurchaseOrderNo)),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(n.Supplier),$R_FN.setCleanTextValue(n.ManufacturerName)),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(n.Buyer),$R_FN.setCleanTextValue(n.IsShortageRefundIssue)),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(n.QuantityOrdered),$R_FN.setCleanTextValue(n.QuantityReceived)),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(n.ShortageQuantity),$R_FN.setCleanTextValue(n.ShortageValue.toFixed(2))),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(n.DateReceived),n.Status)],this._table.addRow(i,n.ShortShipmentId,!1),i=null,n=null},exportCSV:function(){this.getData_Start();var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/DataListNuggets/ShortShipment");n.set_DataObject("ShortShipment");n.set_DataAction("ExportToCSV");n._intTimeoutMilliseconds=5e5;n.addParameter("SortIndex",this._sortIndex);n.addParameter("SortDir",this._sortDir);n.addParameter("PageIndex",this._pageIndex);n.addParameter("PageSize",this._pageSize);n.addParameter("Supplier",this.getFilterFieldValue("ctlSupplier"));n.addParameter("PurchaseOrderNoLo",this.getFilterFieldValue_Min("ctlPurchaseOrderNo"));n.addParameter("PurchaseOrderNoHi",this.getFilterFieldValue_Max("ctlPurchaseOrderNo"));n.addParameter("DateReceived",this.getFilterFieldValue("ctlDateReceived"));n.addParameter("QuantityOrderedLo",this.getFilterFieldValue_Min("ctlQuantityOrdered"));n.addParameter("QuantityOrderedHi",this.getFilterFieldValue_Max("ctlQuantityOrdered"));n.addParameter("QuantityReceivedLo",this.getFilterFieldValue_Min("ctlQuantityReceived"));n.addParameter("QuantityReceivedHi",this.getFilterFieldValue_Max("ctlQuantityReceived"));n.addParameter("ShortageQuantityLo",this.getFilterFieldValue_Min("ctlShortageQuantity"));n.addParameter("ShortageQuantityHi",this.getFilterFieldValue_Max("ctlShortageQuantity"));n.addParameter("ShortageValueLo",this.getFilterFieldValue_Min("ctlShortageValue"));n.addParameter("ShortageValueHi",this.getFilterFieldValue_Max("ctlShortageValue"));n.addParameter("IsShortageRefundIssue",this.getFilterFieldValue("ctlIsShortageRefundIssue"));n.addParameter("ShortShipmentStatus",this.getFilterFieldValue("ctlStatus"));n.addParameter("GINumberLo",this.getFilterFieldValue_Min("ctlGINumber"));n.addParameter("GINumberHi",this.getFilterFieldValue_Max("ctlGINumber"));n.addParameter("Buyer",this.getFilterFieldValue("ctlBuyer"));n.addParameter("IsCanViewClient",this._IsCanViewClient);n.addParameter("ClientByMaster",this.getFilterFieldValue("ctlClient"));n.addParameter("IsRecentOnly",this.getFilterFieldValue("ctlRecentOnly"));n.addDataOK(Function.createDelegate(this,this.exportCSV_OK));n.addError(Function.createDelegate(this,this.exportCSV_Error));n.addTimeout(Function.createDelegate(this,this.exportCSV_Error));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},exportCSV_OK:function(n){var i=n._result,t;i.Filename&&(t=new Date,location.href=String.format("{0}?t={1}",i.Filename,t.getTime()),t=null);this.getDataOK_End()},exportCSV_Error:function(n){this.showError(!0,n.get_ErrorMessage())},updateFilterVisibility:function(){this.getFilterField("ctlClient").show(this._IsCanViewClient);this._IsCanViewClient==!1&&this.setFilterFieldValue("ctlClient",this._LoginClientId)}};Rebound.GlobalTrader.Site.Controls.DataListNuggets.ShortShipment.registerClass("Rebound.GlobalTrader.Site.Controls.DataListNuggets.ShortShipment",Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base,Sys.IDisposable);