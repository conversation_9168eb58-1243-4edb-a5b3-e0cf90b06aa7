﻿/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-222518]		An.TranTan			09-Mar-2024		CREATE			Allow user to delete temp upload file during addd HUBRFQ
===========================================================================================
*/
CREATE OR ALTER PROCEDURE dbo.usp_delete_BOMTempData
	@UserID INT
	,@ClientID INT = null
	,@SelectedClientID INT = null
	,@GeneratedFileName NVARCHAR(1000)
	,@RowsAffected INT = NULL OUTPUT
AS
BEGIN
	DELETE FROM BorisGlobalTraderImports.dbo.tbExcelBOMColumnHeading 
	WHERE 
		CreatedBy = @UserId 
		AND ClientId= @ClientId 
		AND SelectedClientId = @SelectedClientID;                  
	DELETE FROM BorisGlobalTraderImports.dbo.tbTempBomData 
	WHERE CreatedBy = @UserId AND GeneratedFilename = @GeneratedFileName;

	SELECT @RowsAffected = @@ROWCOUNT;
END
GO
