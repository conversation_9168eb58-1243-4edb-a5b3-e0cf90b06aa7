Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.ItemSearch");Rebound.GlobalTrader.Site.Controls.ItemSearch.SIGILines=function(n){Rebound.GlobalTrader.Site.Controls.ItemSearch.SIGILines.initializeBase(this,[n]);this._CompanyNo=0;this._aryPONumber=[];this._aryGINumber=[];this._aryGoodsInLineIds=[];this._floatTotalSelectedValue=0;this._intGoodsInID=0;this._aryAddedGoodsInLineIds=[];this._aryTotalLineValue=[];this._intCount=0;this._isClientInvoice=!1;this._intInvoiceClientNo=-1;this._aryTotalShipLiveValue=[];this._floatTotalSelShipValue=0;this._intGILineNo=0;this._intGINo=0};Rebound.GlobalTrader.Site.Controls.ItemSearch.SIGILines.prototype={get_ShowIncludeInvoice:function(){return this._ShowIncludeInvoice},set_ShowIncludeInvoice:function(n){this._ShowIncludeInvoice!==n&&(this._ShowIncludeInvoice=n)},addPotentialStatusChange:function(n){this.get_events().addHandler("PotentialStatusChange",n)},removePotentialStatusChange:function(n){this.get_events().removeHandler("PotentialStatusChange",n)},onPotentialStatusChange:function(){var n=this.get_events().getHandler("PotentialStatusChange");n&&n(this,Sys.EventArgs.Empty)},initialize:function(){Rebound.GlobalTrader.Site.Controls.ItemSearch.SIGILines.callBaseMethod(this,"initialize");this.addSetupData(Function.createDelegate(this,this.doSetupData));this.addGetDataComplete(Function.createDelegate(this,this.doGetDataComplete));$R_FN.showElement(this.getField("ctlIncludeInvoiced")._element,this._ShowIncludeInvoice);document.getElementById("ibtnSaveLog")!=null&&document.getElementById("ibtnSaveLog").addEventListener("click",Function.createDelegate(this,this.SaveUnReleasedGILog))},dispose:function(){this.isDisposed||(this._CompanyNo=null,this._aryPONumber=null,this._aryGINumber=null,this._aryGoodsInLineIds=null,this._floatTotalSelectedValue=null,this._intGoodsInID=null,this._intGILineNo=null,this._intGINo=null,this._aryAddedGoodsInLineIds=null,this._aryTotalLineValue=null,this._intCount=null,this._isClientInvoice=null,this._aryTotalShipLiveValue=null,this._floatTotalSelShipValue=null,Rebound.GlobalTrader.Site.Controls.ItemSearch.SIGILines.callBaseMethod(this,"dispose"))},doSetupData:function(){this._objData.set_PathToData("controls/ItemSearch/SIGILines");this._objData.set_DataObject("SIGILines");this._objData.set_DataAction("GetData");this._objData.addParameter("CompanyNo",this._CompanyNo);this._objData.addParameter("IncludeInvoiced",this._intCount==0&&this._intGoodsInID>0?"":this.getFieldValue("ctlIncludeInvoiced"));this._objData.addParameter("GIDateFrom",this._intCount==0&&this._intGoodsInID>0?"":this.getFieldValue("ctlGIDateFrom"));this._objData.addParameter("GIDateTo",this._intCount==0&&this._intGoodsInID>0?"":this.getFieldValue("ctlGIDateTo"));this._objData.addParameter("GoodsInNo",this._intCount==0&&this._intGoodsInID>0?this._intGoodsInID:"");this._objData.addParameter("IsClientInvoice",this._isClientInvoice);this._objData.addParameter("InvoiceClientNo",this._intInvoiceClientNo);this._objData.addParameter("PONoLo",this.getFieldValue("ctlPurchaseOrderNo"));this._objData.addParameter("PONoHi",this.getFieldValue("ctlPurchaseOrderNo"));this._objData.addParameter("ShowReleaseGI",this.getFieldValue("ctlReleasedGI"));this._intCount+=1},doGetDataComplete:function(){var t,u,r;for(Array.clear(this._aryPONumber),Array.clear(this._aryGINumber),Array.clear(this._aryGoodsInLineIds),Array.clear(this._aryTotalLineValue),Array.clear(this._aryTotalShipLiveValue),this._floatTotalSelectedValue=0,this._floatTotalSelShipValue=0,this._tblResults.clearTable(),t=0,u=this._objResult.Results.length;t<u;t++){var n=this._objResult.Results[t],i=Array.contains(this._aryAddedGoodsInLineIds,n.ID),f=[this.writeCheckbox(n.ID,t,i||n.blnFromGoodsIn),n.GINumber,$R_FN.setCleanTextValue(n.GIDate),n.PONumber,$R_FN.setCleanTextValue(n.Part),n.DebitId>0?$RGT_nubButton_DebitNote(n.DebitId,n.DebitNumber):"",n.QtyReceived,n.Price,n.LineTotal,n.ShipInCost,n.NPRPrinted?$R_FN.showRedBoldText($R_RES.Yes):"-"];this._tblResults.addRow(f,n.ID,!1,{TotalLineValue:n.LineTotalValue,ShipInCostVal:n.ShipInCostVal});this.registerCheckBox(n.ID,t,i||n.blnFromGoodsIn,!i);r=this.getCheckBox(t);r._element.setAttribute("onClick",i?"javascript:void(0);":String.format('$find("{0}").getCheckedCellValue({1},{2},{3},{4});',this._element.id,t,n.IsUnReleasedLine,n.ID,n.GoodsInNo));r=null;n.blnFromGoodsIn&&!i&&this.getCheckedCellValue(t);f=null;n=null}this.onPotentialStatusChange()},getSelectedPOLines:function(n){Array.add(this._aryPONumber,n)},getSelectedGILines:function(n,t,i,r){Array.add(this._aryGINumber,n);Array.add(this._aryGoodsInLineIds,t);Array.add(this._aryTotalLineValue,i);Array.add(this._aryTotalShipLiveValue,r)},removeSelectedPOLines:function(n){Array.remove(this._aryPONumber,n)},removeSelectedGILines:function(n,t,i,r){Array.remove(this._aryGINumber,n);Array.remove(this._aryGoodsInLineIds,t);Array.remove(this._aryTotalLineValue,i);Array.remove(this._aryTotalShipLiveValue,r)},getSelectedTotal:function(){this._floatTotalSelectedValue=0;for(var n=0,t=this._aryTotalLineValue.length;n<t;n++)this._floatTotalSelectedValue+=parseFloat(this._aryTotalLineValue[n])},getSelectedTotalShip:function(){this._floatTotalSelShipValue=0;for(var n=0,t=this._aryTotalShipLiveValue.length;n<t;n++)this._floatTotalSelShipValue+=parseFloat(this._aryTotalShipLiveValue[n])},writeCheckbox:function(n,t,i){var r=this.getControlID("chk",t),u=this.getControlID("chkImg",t);return String.format('<div class="imageCheckBoxDisabled" id="{0}" ><img id="{1}" class="{2}" src="images/x.gif" style="border-width: 0px;" /> <\/div>',r,u,i?"on":"off")},getControlID:function(n,t){return String.format("{0}_{1}{2}",this._tblResults._element.id,n,t)},registerCheckBox:function(n,t,i,r){var f=this.getControlID("chk",t),e=this.getControlID("chkImg",t),u=this.getCheckBox(t);u&&(u.dispose(),u=null);eval($R_FN.getCreateStatement("Rebound.GlobalTrader.Site.Controls.ImageCheckBox",[["blnChecked",i],["blnEnabled",r],["img",String.format('$get("{0}")',e)]],f))},getCheckBox:function(n){return $find(this.getControlID("chk",n))},getCheckedCellValue:function(n,t,i,r){var f,e;if(this._tblResults&&this._tblResults!="undefined"&&this._tblResults._tbl&&this._tblResults._tbl!="undefined"&&(f=this.getCheckBox(n),e=this._tblResults._tbl.rows[n],e)){var o=e.cells[1].innerHTML,u=e.cells[3].innerHTML,s=this._tblResults._aryValues[n];f._blnChecked?(this.getSelectedGILines(o,s,this._tblResults.getSelectedExtraData(n).TotalLineValue,this._tblResults.getSelectedExtraData(n).ShipInCostVal),u!="&nbsp;"&&this.getSelectedPOLines(u)):(this.removeSelectedGILines(o,s,this._tblResults.getSelectedExtraData(n).TotalLineValue,this._tblResults.getSelectedExtraData(n).ShipInCostVal),u!="&nbsp;"&&this.removeSelectedPOLines(u));this.getSelectedTotal();this.getSelectedTotalShip();s=null;this.onPotentialStatusChange();t&&f._blnChecked&&this.AddCommentOnGI(i,r);o=null;u=null;f=null}},AddCommentOnGI:function(n,t){ShowCommentForUnreleasedGI();this._intGILineNo=n;this._intGINo=t},SaveUnReleasedGILog:function(){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/SupplierInvoiceAdd");n.set_DataObject("SupplierInvoiceAdd");n.set_DataAction("SaveUnReleasedGILog");n.addParameter("GILIneNo",this._intGILineNo);n.addParameter("GINo",this._intGINo);$("#ctl00_cphMain_ctlAdd_ctlDB_ctl14_ctlAdd_txtWarehoueseNotes").val()!=undefined?n.addParameter("ReasontoAdd",$("#ctl00_cphMain_ctlAdd_ctlDB_ctl14_ctlAdd_txtWarehoueseNotes").val()):n.addParameter("ReasontoAdd",$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmAdd_txtWarehoueseNotes").val());n.addDataOK(Function.createDelegate(this,this.SaveUnReleasedGILogComplete));n.addError(Function.createDelegate(this,this.SaveUnReleasedGILogError));n.addTimeout(Function.createDelegate(this,this.SaveUnReleasedGILogError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},SaveUnReleasedGILogError:function(n){this._strErrorMessage=n._errorMessage},SaveUnReleasedGILogComplete:function(n){n._result.Result==!0?(HideCommentForUnreleasedGI(),$("#ctl00_cphMain_ctlAdd_ctlDB_ctl14_ctlAdd_txtWarehoueseNotes").val("")):this._strErrorMessage=n._errorMessage}};Rebound.GlobalTrader.Site.Controls.ItemSearch.SIGILines.registerClass("Rebound.GlobalTrader.Site.Controls.ItemSearch.SIGILines",Rebound.GlobalTrader.Site.Controls.ItemSearch.Base,Sys.IDisposable);