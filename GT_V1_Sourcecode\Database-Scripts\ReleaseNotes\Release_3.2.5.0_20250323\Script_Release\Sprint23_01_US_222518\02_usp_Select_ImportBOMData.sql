﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/*   
===========================================================================================  
TASK          UPDATED BY   DATE			ACTION		DESCRIPTION  
[BUG-220811]  CuongDoX     04-Dec-2024	Create		Add column to fix log issue
[US-222518]   An.TranTan   05-Mar-2024	UPDATE		Update case import bom data from creation HUBRFQ process
[US-222518]   An.TranTan   13-Mar-2024	UPDATE		Import BOM Log for action Create/Update HUBRFQ
===========================================================================================  
*/
CREATE OR ALTER PROCEDURE [dbo].[usp_Select_ImportBOMData] 
	@UserId INT
	,@ClientId INT
	,@SelectedClientId INT
	,@list_Label_name NVARCHAR(max)
	,@list_column_name NVARCHAR(max)
	,@insertDataList NVARCHAR(max)
	,@fileColName NVARCHAR(100)
	,@CurrencyName NVARCHAR(255) = NULL
	,@RecordCount INT OUTPUT
	,@ErrorMessage VARCHAR(MAX) OUTPUT
	,@BomName NVARCHAR(500)
	,@CompanyName NVARCHAR(500)
	,@ContactName NVARCHAR(500) = NULL
	,@SalesmanId INT
	,@CompanyId INT
	,@ContactId INT
	,@PartWatch BIT
	,@DefaultCurrencyId INT
	,@OverRideCurrency BIT
	,@ImportOrHubRFQ NVARCHAR(30) = NULL
	,@ReqforTraceabilityId INT
	,@TypeId INT
	,@DateRequired DATETIME
	,@UpdateBomID INT = NULL
	,@OriginalFileName NVARCHAR(1000) = NULL
	,@GeneratedFilename NVARCHAR(1000) = NULL
	,@NewBomID INT OUTPUT
	,@NewBomCode VARCHAR(MAX) OUTPUT
AS
BEGIN
	SET NOCOUNT ON;
	SET @ErrorMessage = NULL
	SET @RecordCount = 0
	SET @NewBomID = 0
	SET @NewBomCode = NULL

	BEGIN TRY
		BEGIN TRANSACTION Tran1

		-------------------Save Header to tbbom table for Save as HUBRFQ--Command----------Start----------------------------------          
		SET @ContactName = replace(@ContactName, '''', '''''');

		DECLARE @BOMId INT
			,@POCurrencyNo INT

		SELECT TOP 1 @POCurrencyNo = POCurrencyNo
		FROM tbCompany
		WHERE ClientNo = @ClientId
			AND ISNULL(IsPOHub, 0) = 1

		DECLARE @AS9120 BIT

		SELECT TOP 1 @AS9120 = isnull((
					SELECT IsTraceability
					FROM tbCompanyType cmt
					WHERE cmt.CompanyTypeId = comy.TypeNo
					), 0)
		FROM tbcompany comy
		WHERE comy.companyid = @CompanyId

		IF (@ImportOrHubRFQ = 'SaveasHUBRFQ')
		BEGIN
			--PRINT('From Save as HUBRFQ action in Utility BOM Import tool');
			INSERT INTO dbo.tbBOM (
				ClientNo
				,BOMName
				--,Notes                                                                                                              
				--,BOMCode                                                                                 
				,UpdatedBy
				,CompanyNo
				,ContactNo
				,Inactive
				,[Status]
				,CurrencyNo
				,CurrentSupplier
				,QuoteRequired
				,AS9120 --0                                                                                                        
				,UpdateByPH -- LOGINID                                                                                                  
				,ClientCurrencyNo --@CurrencyNo                                                                                                  
				,Salesman2 --@Salesman                                                                                                   
				)
			VALUES (
				@SelectedClientId
				,@BomName
				--,@Notes                               
				--,@ClientBOMCode--@BOMCode                                                                                        
				,@UserId
				,@CompanyId
				,@ContactId
				,0
				,1 --,@Status                                                         
				,@DefaultCurrencyId
				,NULL --currentsupplier                                                                                                                
				,getdate() --QuateRequired                                                                                                             
				,@AS9120
				,@UserId
				,@POCurrencyNo
				,@SalesmanId
				)

			SET @BOMId = SCOPE_IDENTITY()

			UPDATE dbo.tbBOM
			SET BOMCode = CONVERT(NVARCHAR(10), @SelectedClientId) + '-' + CONVERT(NVARCHAR(10), @BOMId)
			WHERE bomId = @BOMId
		END
		ELSE IF(@ImportOrHubRFQ = 'UpdateHUBRFQ' AND ISNULL(@UpdateBomID,0) > 0)
		BEGIN
			--PRINT('From Update HUBRFQ action in Utility BOM Import tool');
			SET @BOMId = @UpdateBomID;
			UPDATE dbo.tbBOM
			SET BOMName = @BomName
				,UpdatedBy = @UserId
				--,CompanyNo = @CompanyId
				,ContactNo = @ContactId
				,Salesman2 = @SalesmanId
				,CurrencyNo = @DefaultCurrencyId
				,QuoteRequired = @DateRequired
			WHERE bomId = @BOMId;
		END
		ELSE IF(@ImportOrHubRFQ = 'CreateHUBRFQ' AND ISNULL(@UpdateBomID,0) > 0)
		BEGIN
			--PRINT('From Create HUBRFQ action in HUBRFQ page');
			SET @BOMId = @UpdateBomID;
			SELECT	@CompanyName = CompanyName
					, @SalesmanId = CASE WHEN ISNULL(@SalesmanId,0) = 0 THEN Salesman ELSE @SalesmanId END
			FROM tbCompany WHERE CompanyId = @CompanyId;

			SELECT @ContactName =ContactName FROM tbContact WHERE ContactId = @ContactId;
			SELECT @CurrencyName = CurrencyDescription FROM tbCurrency WHERE CurrencyId = @DefaultCurrencyId;
			SELECT @ReqforTraceabilityId = ID from tbRequirementDropDownData WHERE Stype = 'RTrace' AND ServiceName = 'No traceability required';
			SELECT @TypeId = ID from tbRequirementDropDownData WHERE Stype = 'RType' AND ServiceName = 'BOM UPLOAD';
		END



		-------------------Save Header to tbbomm table for Save as HUBRFQ--Command----------Start----------------------------------                                                                        
		-------------------Update--tbTempbomdata--Command----------Start-----------------------------------------------------------                                                                              
		DECLARE @Label_name VARCHAR(MAX)
			,@column_name VARCHAR(MAX)
		DECLARE @partColumn NVARCHAR(30) = 'Column1'
		DECLARE @mfrColumn NVARCHAR(30) = 'Column6'

		DECLARE cursor_SaveStockData CURSOR
		FOR
		WITH T1
		AS (
			SELECT val AS Label_name
				,ROW_NUMBER() OVER (ORDER BY (SELECT 1)) AS ID
			FROM dbo.[SplitString](@list_Label_name, ',')
			)
			,T2
		AS (
			SELECT val AS Column_name
				,ROW_NUMBER() OVER (ORDER BY (SELECT 1)) AS ID
			FROM dbo.[SplitString](@list_column_name, ',')
			)
		SELECT T1.Label_name
			,T2.Column_name
		FROM T1
		FULL JOIN T2 ON (T1.ID = T2.ID)

		OPEN cursor_SaveStockData;

		FETCH NEXT
		FROM cursor_SaveStockData
		INTO @Label_name
			,@column_name

		WHILE @@FETCH_STATUS = 0
		BEGIN
			DECLARE @strScript NVARCHAR(max)

			SET @strScript = ''

			--ManufacturerName--1                                              
			IF @Label_name = 'ManufacturerName'
			BEGIN
				SET @mfrColumn = @column_name
				SET @strScript = 'update BorisGlobalTraderimports.dbo.tbTempBOMData set ' + @column_name + '=' + '' + '(case    when len( ' + @column_name + ')>50 then dbo.stripAlphahtestingMfr(SUBSTRING(' + @column_name + ',0, 50))                                                                                                                                  
 
              else dbo.stripAlphahtestingMfr( ' + @column_name + ' )  end)' + '' + ' WHERE  ClientId= ' + CONVERT(NVARCHAR(10), @ClientId) + ' and SelectedClientId= ' + CONVERT(NVARCHAR(10), @SelectedClientId) + '  and CreatedBy= ' + CONVERT(NVARCHAR(10), @UserId) + ''

				EXEC (@strScript)
					--PRINT 'ManufacturerName  ' + @Label_name + ' Column name ' + @column_name                                                                                                               
			END

			--Part--2                                                                                                                
			IF @Label_name = 'Part'
			BEGIN
				SET @partColumn = @column_name
				SET @strScript = 'update BorisGlobalTraderimports.dbo.tbTempBOMData set ' + @column_name + '=' + '' + '(case    when len( ' + @column_name + ')>30 then dbo.stripAlphahnumeric(SUBSTRING(' + @column_name + ',1, 30))                                                                                                      
   else dbo.stripAlphahnumeric( ' + @column_name + ' )  end)' + '' + ' WHERE  ClientId= ' + CONVERT(NVARCHAR(10), @ClientId) + ' and SelectedClientId= ' + CONVERT(NVARCHAR(10), @SelectedClientId) + '  and CreatedBy= ' + CONVERT(NVARCHAR(10), @UserId) + ''

				EXEC (@strScript)
			END

			--Quantity--3                                                                          
			IF @Label_name = 'Quantity'
			BEGIN
				SET @strScript = 'update BorisGlobalTraderimports.dbo.tbTempBOMData set ' + @column_name + '=' + ' cast(ROUND(dbo.stripNumeric(' + @column_name + '),0) as int )                                                                                      
  
    
     
       
        
        
 ' + ' WHERE  ClientId= ' + CONVERT(NVARCHAR(10), @ClientId) + ' and SelectedClientId= ' + CONVERT(NVARCHAR(10), @SelectedClientId) + '  and CreatedBy= ' + CONVERT(NVARCHAR(10), @UserId) + ''

				EXEC (@strScript)
			END

			--Price--4                                                                                                                      
			IF @Label_name = 'Price'
			BEGIN
				SET @strScript = 'update BorisGlobalTraderimports.dbo.tbTempBOMData set ' + @column_name + '=' + ' dbo.stripNumeric(' + @column_name + ')                                                    
              ' + ' WHERE  ClientId= ' + CONVERT(NVARCHAR(10), @ClientId) + ' and SelectedClientId= ' + CONVERT(NVARCHAR(10), @SelectedClientId) + '  and CreatedBy= ' + CONVERT(NVARCHAR(10), @UserId) + ''

				EXEC (@strScript)
			END

			--Description--5                                                                                                                                              
			IF @Label_name = 'Instructions'
			BEGIN
				SET @strScript = 'update BorisGlobalTraderimports.dbo.tbTempBOMData set ' + @column_name + '=' + '' + '(case    when len( ' + @column_name + ')>128 then dbo.stripAlphahnumeric2(SUBSTRING(' + @column_name + ',0, 128))                                                            
             else dbo.stripAlphahnumeric2( ' + @column_name + ' )  end)' + '' + ' WHERE  ClientId= ' + CONVERT(NVARCHAR(10), @ClientId) + ' and SelectedClientId= ' + CONVERT(NVARCHAR(10), @SelectedClientId) + '  and CreatedBy= ' + CONVERT(NVARCHAR(10), @UserId) + ''

				EXEC (@strScript)
			END

			--AlternatePart--6                                                                                                                                              
			IF @Label_name = 'Notes'
			BEGIN
				SET @strScript = 'update BorisGlobalTraderimports.dbo.tbTempBOMData set ' + @column_name + '=' + '' + '(case    when len( ' + @column_name + ')>128 then dbo.stripAlphahnumeric2(SUBSTRING(' + @column_name + ',0, 128))                                                                                                          
else dbo.stripAlphahnumeric2( ' + @column_name + ' )  end)' + '' + ' WHERE  ClientId= ' + CONVERT(NVARCHAR(10), @ClientId) + ' and SelectedClientId= ' + CONVERT(NVARCHAR(10), @SelectedClientId) + '  and CreatedBy= ' + CONVERT(NVARCHAR(10), @UserId) + ''

				EXEC (@strScript)
			END

			--DateCode--7                                                                                                                                              
			IF @Label_name = 'DateCode'
			BEGIN
				SET @strScript = 'update BorisGlobalTraderimports.dbo.tbTempBOMData set ' + @column_name + '=' + '' + '(case    when len( ' + @column_name + ')>5 then dbo.stripAlphahnumeric(SUBSTRING(' + @column_name + ',0, 5))                                                                                                                                   
              else dbo.stripAlphahnumeric( ' + @column_name + ' )  end)' + '' + ' WHERE  ClientId= ' + CONVERT(NVARCHAR(10), @ClientId) + ' and SelectedClientId= ' + CONVERT(NVARCHAR(10), @SelectedClientId) + '  and CreatedBy= ' + CONVERT(NVARCHAR(10), @UserId) + ''

				EXEC (@strScript)
			END --ProductName--8                                                                                                   

			IF @Label_name = 'ProductName'
			BEGIN
				SET @strScript = 'update BorisGlobalTraderimports.dbo.tbTempBOMData set ' + @column_name + '=' + '' + '(case when len( ' + @column_name + ')>128 then dbo.stripAlphahnumeric(SUBSTRING(' + @column_name + ',0, 128))                                                                                             
              else dbo.stripAlphahnumeric( ' + @column_name + ' )  end)' + '' + ' WHERE  ClientId= ' + CONVERT(NVARCHAR(10), @ClientId) + ' and SelectedClientId= ' + CONVERT(NVARCHAR(10), @SelectedClientId) + '  and CreatedBy= ' + CONVERT(NVARCHAR(10), @UserId) + ''

				EXEC (@strScript)
			END

			--PackageName--9                                                                                                                           
			IF @Label_name = 'PackageName'
			BEGIN
				SET @strScript = 'update BorisGlobalTraderimports.dbo.tbTempBOMData set ' + @column_name + '=' + '' + '(case    when len( ' + @column_name + ')>50 then dbo.stripAlphahnumeric(SUBSTRING(' + @column_name + ',0, 50))                                         
else dbo.stripAlphahnumeric( ' + @column_name + ' )  end)' + '' + ' WHERE  ClientId= ' + CONVERT(NVARCHAR(10), @ClientId) + ' and SelectedClientId= ' + CONVERT(NVARCHAR(10), @SelectedClientId) + '  and CreatedBy= ' + CONVERT(NVARCHAR(10), @UserId) + ''

				EXEC (@strScript)
			END

			IF (@OverRideCurrency = 0)
			BEGIN
				IF @Label_name = 'Currency'
				BEGIN
					SET @strScript = 'update BorisGlobalTraderimports.dbo.tbTempBOMData set ' + @column_name + '=' + '' + '(case    when len( ' + @column_name + ')>50 then dbo.stripAlphahnumeric(SUBSTRING(' + @column_name + ',0, 50))                                                                                        
        else dbo.stripAlphahnumeric( ' + @column_name + ' )  end)' + '' + ' WHERE  ClientId= ' + CONVERT(NVARCHAR(10), @ClientId) + ' and SelectedClientId= ' + CONVERT(NVARCHAR(10), @SelectedClientId) + '  and CreatedBy= ' + CONVERT(NVARCHAR(10), @UserId) + ''

					EXEC (@strScript)
				END
			END

			--ROHS--10                          
			IF @Label_name = 'ROHS'
			BEGIN
				SET @strScript = 'update BorisGlobalTraderimports.dbo.tbTempBOMData set ' + @column_name + '=' + '(case   when (len(' + @column_name + ')>30) then dbo.stripAlphahnumeric2(SUBSTRING(' + @column_name + ',0, 30))                                                            
    when LOWER(' + @column_name + ')=''compliant'' then ''1'' when LOWER(' + @column_name + ')=''compliance'' then ''1'' when LOWER(' + @column_name + ')=''comp'' then ''1''                                                                              
    when LOWER(' + @column_name + ')=''yes'' then ''1''                                                                                                
    when LOWER(' + @column_name + ')=''rohs compliant'' then ''1'' when LOWER(' + @column_name + ')=''rohscompliant'' then ''1'' when LOWER(' + @column_name + ')=''rohs'' then ''1''                                                                                      
 
     
     
      
        
         
when LOWER(' + @column_name + 
					')=''noncompliant'' then ''2'' when LOWER(' + @column_name + ')=''uncompliant'' then ''2'' when LOWER(' + @column_name + ')=''false'' then ''2''                                                                                             
  
    
    
when LOWER(' + @column_name + ')=''no'' then ''2'' when LOWER(' + @column_name + ')=''rohs non-compliant'' then ''2''                                                         
    when LOWER(' + @column_name + ')=''rohs noncompliant'' then ''2'' when LOWER(' + @column_name + ')=''rohsnoncompliant'' then ''2''                                                                                                                                 
  
    
     
    when LOWER(' + @column_name + ')=''non-compliant'' then ''2'' when LOWER(' + @column_name + ')=''exempt'' then ''3'' when LOWER(' + @column_name + ')=''rohs exempt'' then ''3''                                                           
    when LOWER(' + @column_name + 
					')=''rohsexempt'' then ''3'' when LOWER(' + @column_name + ')=''na'' then ''4''                                                                                                                                    
    when LOWER(' + @column_name + ')=''n/a'' then ''4'' when LOWER(' + @column_name + ')=''rohs not applicable'' then ''4''                                                      
    when LOWER(' + @column_name + ')=''rohs notapplicable'' then ''4''                                                                                                                                    
    when LOWER(' + @column_name + ')=''rohsnotapplicable'' then ''4''                                                                                                                                     
    when LOWER(' + @column_name + ')=''rohs2'' then ''5'' when LOWER(' + @column_name + ')=''rohs 2'' then ''5''  when LOWER(' + @column_name + 
					')=''rohs 5/6'' then ''6''                                                         
    when LOWER(' + @column_name + ')=''rohs5/6'' then ''6'' when LOWER(' + @column_name + ')=''rohs56'' then ''6''                                                                                                                                     
    when LOWER(' + @column_name + ')=''rohs 56'' then ''6'' when LOWER(' + @column_name + ')=''rohs 6/6'' then ''7''                 
    when LOWER(' + @column_name + ')=''rohs6/6'' then ''7'' when LOWER(' + @column_name + ')=''rohs66'' then ''7''                                                                                                     
    when LOWER(' + @column_name + ')=''rohs 66'' then ''7''  else ''0'' end) ' + ' WHERE  ClientId= ' + CONVERT(NVARCHAR(10), @ClientId) + ' and SelectedClientId= ' + CONVERT(NVARCHAR(10), @SelectedClientId) + '  and CreatedBy= ' + CONVERT(NVARCHAR(10), @UserId) + ''

				EXEC (@strScript)
			END

			--SupplierPart--11                                                                                                                                              
			IF @Label_name = 'CustomerPart'
			BEGIN
				SET @strScript = 'update BorisGlobalTraderimports.dbo.tbTempBOMData set ' + @column_name + '=' + '' + '(case    when len( ' + @column_name + ')>30 then dbo.stripAlphahnumeric(SUBSTRING(' + @column_name + ',1, 30))                                                                                                                                   
   else dbo.stripAlphahnumeric( ' + @column_name + ' )  end)' + '' + ' WHERE  ClientId= ' + CONVERT(NVARCHAR(10), @ClientId) + ' and SelectedClientId= ' + CONVERT(NVARCHAR(10), @SelectedClientId) + '  and CreatedBy= ' + CONVERT(NVARCHAR(10), @UserId) + ''

				EXEC (@strScript)
			END

			FETCH NEXT
			FROM cursor_SaveStockData
			INTO @Label_name
				,@column_name
		END;

		CLOSE cursor_SaveStockData;

		DEALLOCATE cursor_SaveStockData;

		-------------------Update--tbTempStockDa--Command----------end-----------------------------------------------------------                                                                                                                                  
		DECLARE @DynamicQuery NVARCHAR(max)

		SET @list_Label_name = REPLACE(@list_Label_name, 'PackageName', 'Package');
		SET @list_Label_name = REPLACE(@list_Label_name, 'ManufacturerName', 'Manufacturer');
		SET @list_Label_name = REPLACE(@list_Label_name, 'ProductName', 'Product');
		SET @CompanyName = replace(@CompanyName, '''', '''''');
		SET @ContactName = replace(@ContactName, '''', '''''');

		DECLARE @lastInsertedValue INT

		IF (@OverRideCurrency = 1)
		BEGIN
			SET @DynamicQuery = 'insert  into BorisGlobalTraderimports.dbo.tbCusReqToBeImported                                                                         
 (GeneratedFileName,SequenceNo,ECCNCode,IHSMSLId,IHSCountryOfOriginNo,IHSLifeCycleStage,IHSHTSCode,IHSAveragePrice                                                        
   ,IHSPacking,IHSPackagingSize,IHSDescriptions,IHSPartsNo,IHSCurrencyCode,IHSProduct,                                                         
   ClientNo,UserId,PartWatch,CurrencyNo,Salesman,CompanyNo,ContactNo,Currency,BomName,Company,Contact,ReqForTraceability,ReqType,DatePromised, ' + @list_Label_name + ' ' + ') ' + 
				' select GeneratedFileName,ROW_NUMBER() OVER(ORDER BY ID ASC), isnull(ec.ECCNCode,ihsp.ECCNCode) as ECCNCode                                                        
   ,msl.MSLLevelid                                                        
   ,ihsp.CountryOfOriginNo as IHSCountryOfOriginNo                                                       
   ,ihsp.PartStatus as IHSLifeCycleStage                                                          
   ,(case  when len(REPLACE(ihsp.HTSCode, ' + '''' + '.' + '''' + ', ' + '''' + '' + '''' + '))>0 then REPLACE(ihsp.HTSCode, ' + '''' + '.' + '''' + ', ' + '''' + '' + '''' + 
				')   else null   end)     as  IHSHTSCode                                                          
   , isnull(ihsp.colprice,0)as IHSAveragePrice                                           
   ,( select top 1 (case  when len(pack.PackageId)>0 then pack.PackageName   else null   end)                                  
    from tbPackage pack where (pack.PackageName=ihsp.Packaging or pack.PackageDescription=ihsp.Packaging))  as  IHSPacking                                                        
   , (case  when len( ihsp.packagecode)>0 then ihsp.packagecode   else null   end) as  IHSPackagingSize                                                           
   ,ihsp.Descriptions as IHSDescriptions                                                          
   ,ihsp.IHSPartsId as IHSPartsNo                                                          
   , (case  when len( ihsp.ColPriceCurrency)>0 then ihsp.ColPriceCurrency   else null   end) as  IHSCurrencyCode                                                          
   ,ihsp.IHSProduct,                                                        
   SelectedClientId ,' 
				+ CONVERT(NVARCHAR(10), @UserId) + ',' + CONVERT(NVARCHAR(10), @PartWatch) + ',' + CONVERT(NVARCHAR(10), @DefaultCurrencyId) + ',' + CONVERT(NVARCHAR(10), @SalesmanId) + ',' + CONVERT(NVARCHAR(10), @CompanyId) + ',' + CONVERT(NVARCHAR(10), @ContactId) + ',' + '''' + @CurrencyName + '''' + ',' + '''' + @BomName + '''' + ',' + '''' + @CompanyName + '''' + ',' + '''' + @ContactName + '''' + ',' + CONVERT(NVARCHAR(10), @ReqforTraceabilityId) + ',' + CONVERT(NVARCHAR(10), @TypeId) + ',' + '''' + convert(VARCHAR(10), @DateRequired, 120) + '''' + ',' + @insertDataList + ' from BorisGlobalTraderimports.dbo.tbTempBOMData                                          
                                                
    left join tbPartEccnMapped ec  on  ec.Part=BorisGlobalTraderimports.dbo.tbTempBOMData.' + @partColumn + '                     
   left join tbIHSparts ihsp on ihsp.Part=BorisGlobalTraderimports.dbo.tbTempBOMData.' + @partColumn + '                       
    and  ihsp.ManufacturerFullName=BorisGlobalTraderimports.dbo.tbTempBOMData.' + @mfrColumn + 
				'                   
 left  join  tbMSLLevel msl on case when (ihsp.MSL=''Exempt'' or ihsp.MSL= ''N/A'') then ihsp.MSL else ''MSL ''+ ihsp.msl end = MSLLevel                                                      
   ' + 'WHERE  ClientId=' + CONVERT(NVARCHAR(10), @ClientId) + ' and SelectedClientId=' + CONVERT(NVARCHAR(10), @SelectedClientId) + '  and CreatedBy=' + CONVERT(NVARCHAR(10), @UserId) + ' order by BorisGlobalTraderimports.dbo.tbTempBOMData.Id '
		END
		ELSE
		BEGIN
			SET @DynamicQuery = 'insert  into BorisGlobalTraderimports.dbo.tbCusReqToBeImported                                                                         
   (GeneratedFileName,SequenceNo,ECCNCode,IHSMSLId,IHSCountryOfOriginNo,IHSLifeCycleStage,IHSHTSCode,IHSAveragePrice                                                        
   ,IHSPacking,IHSPackagingSize,IHSDescriptions,IHSPartsNo,IHSCurrencyCode,IHSProduct,Currency,CurrencyNo,                                                       
   ClientNo,UserId,PartWatch,Salesman,CompanyNo,ContactNo,BomName,Company,Contact,ReqForTraceability,ReqType,DatePromised, ' + @list_Label_name + ' ' + ') ' + 
				' select GeneratedFileName,ROW_NUMBER() OVER(ORDER BY ID ASC), isnull(ec.ECCNCode,ihsp.ECCNCode) as ECCNCode                                                        
   ,msl.MSLLevelid                                                        
   ,ihsp.CountryOfOriginNo as IHSCountryOfOriginNo                                   
   ,ihsp.PartStatus as IHSLifeCycleStage                                                          
   ,(case  when len(REPLACE(ihsp.HTSCode, ' + '''' + '.' + '''' + ', ' + '''' + '' + '''' + '))>0 then REPLACE(ihsp.HTSCode, ' + '''' + '.' + '''' + ', ' + '''' + '' + '''' + 
				')   else null   end)     as  IHSHTSCode                                                          
   , isnull(ihsp.colprice,0)as IHSAveragePrice                                                        
   ,( select top 1 (case  when len(pack.PackageId)>0 then pack.PackageName   else null   end)                                  
    from tbPackage pack where (pack.PackageName=ihsp.Packaging or pack.PackageDescription=ihsp.Packaging))  as  IHSPacking                                                          
   , (case  when len( ihsp.packagecode)>0 then ihsp.packagecode   else null   end) as  IHSPackagingSize                                                           
   ,ihsp.Descriptions as IHSDescriptions                                                          
   ,ihsp.IHSPartsId as IHSPartsNo                                    
   , (case  when len( ihsp.ColPriceCurrency)>0 then ihsp.ColPriceCurrency   else null   end) as  IHSCurrencyCode                                                          
   ,ihsp.IHSProduct,  ExcelCurrency,cast(ISNULL(FinalCurrency,0) as int),                                                      
   SelectedClientId ,' 
				+ CONVERT(NVARCHAR(10), @UserId) + ',' + CONVERT(NVARCHAR(10), @PartWatch) + ',' + CONVERT(NVARCHAR(10), @SalesmanId) + ',' + CONVERT(NVARCHAR(10), @CompanyId) + ',' + CONVERT(NVARCHAR(10), @ContactId) + ',' + '''' + @BomName + '''' + ',' + '''' + @CompanyName + '''' + ',' + '''' + @ContactName + '''' + ',' + CONVERT(NVARCHAR(10), @ReqforTraceabilityId) + ',' + CONVERT(NVARCHAR(10), @TypeId) + ',' + '''' + convert(VARCHAR(10), @DateRequired, 120) + '''' + ',' + @insertDataList + ' from BorisGlobalTraderimports.dbo.tbTempBOMData                                                         
   left join tbPartEccnMapped ec  on  ec.Part=BorisGlobalTraderimports.dbo.tbTempBOMData.' + @partColumn + '            
   left join tbIHSparts ihsp on ihsp.Part=BorisGlobalTraderimports.dbo.tbTempBOMData.' + @partColumn + '                                                          
    and  ihsp.ManufacturerFullName=BorisGlobalTraderimports.dbo.tbTempBOMData.' + @mfrColumn + 
				'                               
  left  join  tbMSLLevel msl on case when (ihsp.MSL=''Exempt'' or ihsp.MSL= ''N/A'') then ihsp.MSL else ''MSL ''+ ihsp.msl end = MSLLevel                                                         
   ' + 'WHERE  ClientId=' + CONVERT(NVARCHAR(10), @ClientId) + ' and SelectedClientId=' + CONVERT(NVARCHAR(10), @SelectedClientId) + '  and CreatedBy=' + CONVERT(NVARCHAR(10), @UserId)
				----------------------------------------                                                                                                 
		END

		EXEC (@DynamicQuery)

		--SET @RecordCount= @@ROWCOUNT               
		EXEC [usp_Import_BOM_By_User] @UserId
			,@BOMId
			,@RecordCount OUT

		IF((@ImportOrHubRFQ = 'CreateHUBRFQ' OR @ImportOrHubRFQ = 'UpdateHUBRFQ') AND @GeneratedFilename IS NOT NULL)
		BEGIN
			DECLARE @LogMessage NVARCHAR(2000) = NULL;
			EXEC dbo.[usp_insert_BOMCSV_ForHUBRFQCreation]
				@LoginID = @UserId
				,@BOMID = @BOMId
				,@Status = 1
				,@ImportType = @ImportOrHubRFQ
				,@OriginalFilename = @OriginalFilename
				,@GeneratedFilename = @GeneratedFilename
				,@OutputMessage = @LogMessage OUT
		END

		SELECT @NewBomID = bomid
			,@NewBomCode = Bomname
		FROM dbo.tbBOM
		WHERE bomId = @BOMId

		COMMIT TRANSACTION Tran1
			-------------------------Import History------------------------------------                                         
	END TRY

	BEGIN CATCH
		ROLLBACK TRANSACTION Tran1

		SET @RecordCount = 0
		SET @NewBomID = 0
		SET @NewBomCode = NULL

		SELECT @ErrorMessage = ERROR_MESSAGE();
	END CATCH
END
GO


