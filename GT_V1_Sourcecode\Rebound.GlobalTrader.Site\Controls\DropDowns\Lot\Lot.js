Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.Lot=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.Lot.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DropDowns.Lot.prototype={get_intSourceLotToExclude:function(){return this._intSourceLotToExclude},set_intSourceLotToExclude:function(n){this._intSourceLotToExclude!==n&&(this._intSourceLotToExclude=n)},get_intGlobalLoginClientNo:function(){return this._intGlobalLoginClientNo},set_intGlobalLoginClientNo:function(n){this._intGlobalLoginClientNo!==n&&(this._intGlobalLoginClientNo=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.DropDowns.Lot.callBaseMethod(this,"initialize");this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK))},dispose:function(){this.isDisposed||(this._intSourceLotToExclude=null,this._intGlobalLoginClientNo=null,Rebound.GlobalTrader.Site.Controls.DropDowns.Lot.callBaseMethod(this,"dispose"))},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/Lot");this._objData.set_DataObject("Lot");this._objData.set_DataAction("GetData");this._objData.addParameter("SourceLotToExclude",this._intSourceLotToExclude);this._objData.addParameter("GlobalLoginClientNo",this._intGlobalLoginClientNo)},dataCallOK:function(){var n=this._objData._result,t;if(n!=null&&n.Lots)for(t=0;t<n.Lots.length;t++)this.addOption(n.Lots[t].Name,n.Lots[t].ID)}};Rebound.GlobalTrader.Site.Controls.DropDowns.Lot.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.Lot",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);