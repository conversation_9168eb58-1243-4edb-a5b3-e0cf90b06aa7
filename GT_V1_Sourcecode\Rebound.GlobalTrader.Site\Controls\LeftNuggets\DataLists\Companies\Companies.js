Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists");Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Companies=function(n){Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Companies.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Companies.prototype={get_enmCompanyListType:function(){return this._enmCompanyListType},set_enmCompanyListType:function(n){this._enmCompanyListType!==n&&(this._enmCompanyListType=n)},initialize:function(){this.addGetDataOKEvent(Function.createDelegate(this,this.getDataOK));this.addSetupDataCallEvent(Function.createDelegate(this,this.setupDataCall));this._strPathToData="controls/DataListNuggets/Companies";this._strDataObject="Companies";Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Companies.callBaseMethod(this,"initialize")},dispose:function(){this.isDisposed||(this._enmCompanyListType=null,Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Companies.callBaseMethod(this,"dispose"))},setupDataCall:function(){var n="GetData_";switch(this._enmCompanyListType){case $R_ENUM$CompanyListType.AllCompanies:n+="Companies";break;case $R_ENUM$CompanyListType.Customers:n+="Customers";break;case $R_ENUM$CompanyListType.Suppliers:n+="Suppliers";break;case $R_ENUM$CompanyListType.Prospects:n+="Prospects"}this._objData.set_DataAction(n);this._objData.addParameter("CallType",this._enmCompanyListType)},getDataOK:function(){for(var t,n=0,i=this._objResult.Results.length;n<i;n++)t=this._objResult.Results[n],this._tbl.addRow([String.format('<a href="{0}">{1}<\/a>',$RGT_gotoURL_Company(t.ID,null,this._enmCompanyListType),$R_FN.setCleanTextValue(t.Name))]),strData=null,t=null}};Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Companies.registerClass("Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Companies",Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Base,Sys.IDisposable);