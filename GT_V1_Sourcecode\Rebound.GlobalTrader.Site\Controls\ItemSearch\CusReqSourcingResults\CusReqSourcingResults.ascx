<%@ Control Language="C#" CodeBehind="CusReqSourcingResults.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.ItemSearch.CustomerRequirementSourcingResults" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>

<ReboundUI_ItemSearch:DesignBase id="ctlDB" runat="server" InitialSortDirection="DESC">

	<FieldsLeft>
		<ReboundUI_FilterDataItemRow:Numerical id="ctlReqNo" runat="server" ResourceTitle="RequirementNo" />
		<ReboundUI_FilterDataItemRow:TextBox id="ctlPartNo" runat="server" ResourceTitle="PartNo" />
		<ReboundUI_FilterDataItemRow:CheckBox id="ctlIsPoHub" runat="server" ResourceTitle="IsPoHub" />
	</FieldsLeft>
	<FieldsRight>
		<ReboundUI_FilterDataItemRow:TextBox id="ctlSupplier" runat="server" ResourceTitle="Supplier" />
		<ReboundUI_FilterDataItemRow:TextBox id="ctlCompany" runat="server" ResourceTitle="Company" />
		<ReboundUI_FilterDataItemRow:TextBox id="ctlBOM" runat="server" ResourceTitle="IPOBOMName" FilterField="BOM"/>
		
	</FieldsRight>
	
</ReboundUI_ItemSearch:DesignBase>
