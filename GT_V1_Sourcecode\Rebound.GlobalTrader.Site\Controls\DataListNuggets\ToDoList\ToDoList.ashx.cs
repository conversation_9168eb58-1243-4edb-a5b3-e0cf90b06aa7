﻿//Marker Changed by Date               Remarks
//[001] Bhooma          18/Feb/2022        CR:- Create New DataList Nugget for view to do list
using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site.Enumerations;
using Rebound.GlobalTrader.Site.Code.Common;

namespace Rebound.GlobalTrader.Site.Controls.DataListNuggets.Data
{

    public class ToDoList : Base
    {

        public override void ProcessRequest(HttpContext context)
        {
            base.ProcessRequest(context);
            if (Action == "ExportToCSV") ExportToCSV();
        }
        protected override void GetData()
        {
            List<Rebound.GlobalTrader.BLL.ToDo> lst = Rebound.GlobalTrader.BLL.ToDo.DataListNugget(
                GetFormValue_NullableDateTime("CreatedDateFrom"),
                GetFormValue_NullableDateTime("CreatedDateTo"),
                GetFormValue_NullableDateTime("TaskDateFrom"),
                GetFormValue_NullableDateTime("TaskDateTo"),

                 GetFormValue_NullableInt("TaskType"),
                  GetFormValue_StringForNameSearchDecode("TaskStatus"),
                   GetFormValue_StringForLikeSearchCompany("CustomerName"),
                  GetFormValue_NullableInt("Salesman"),
                  SessionManager.LoginID,
                  SessionManager.ClientID,
                  GetFormValue_NullableInt("SortIndex"),
                 GetFormValue_NullableInt("SortDir", SortColumnDirection.ASC),
                 GetFormValue_NullableInt("PageIndex", 0),
                 GetFormValue_NullableInt("PageSize", 10)
                 , GetFormValue_Boolean("ReviewOnly")
                 , GetFormValue_NullableDateTime("TaskReminderDate")
                 ,ID
                 , GetFormValue_NullableInt("TaskCategory")
                 , GetFormValueQuery_StringForNameSearch("QuoteNumber")

            );
            JsonObject jsn = new JsonObject();
            jsn.AddVariable("TotalRecords", (lst.Count > 0) ? lst[0].RowCnt : 0);
            JsonObject jsnRowsArray = new JsonObject(true);
            for (int i = 0; i < lst.Count; i++)
            {
                if (i < lst.Count)
                {
                    JsonObject jsnRow = new JsonObject();
                    jsnRow.AddVariable("ToDoListId", lst[i].ToDoListId);
                    jsnRow.AddVariable("CreatedDateFrom", Functions.FormatDate(lst[i].CreatedDateFrom));
                    jsnRow.AddVariable("CreatedDateTo", Functions.FormatDate(lst[i].CreatedDateTo));
                    jsnRow.AddVariable("TaskDateFrom", Functions.FormatDate(lst[i].TaskDateFrom));
                    jsnRow.AddVariable("TaskDateTo", Functions.FormatDate(lst[i].TaskDateTo));
                    jsnRow.AddVariable("TaskType", lst[i].TaskType);
                    jsnRow.AddVariable("TaskStatus", lst[i].TaskStatus);
                    jsnRow.AddVariable("CustomerName", lst[i].CustomerName);
                    jsnRow.AddVariable("SalesPersonName", lst[i].SalesPersonName);
                    jsnRow.AddVariable("IsComplete", lst[i].IsComplete);
                    jsnRow.AddVariable("HasReminder", (lst[i].ReminderDate > DateTime.Now));
                    jsnRow.AddVariable("TaskTitle", lst[i].TaskTitle);
                    jsnRow.AddVariable("TaskReminderDate", Functions.FormatDate(lst[i].TaskReminderDate));
                    jsnRow.AddVariable("TaskCategoryNo", lst[i].ToDoCategoryNo);
                    jsnRow.AddVariable("TaskCategory", lst[i].ToDoCategoryName);
                    jsnRow.AddVariable("QuoteNo", lst[i].QuoteNo ?? 0);
                    jsnRow.AddVariable("QuoteNumber", lst[i].QuoteNumber ?? 0);
                    jsnRowsArray.AddVariable(jsnRow);
                    jsnRow.Dispose();
                    jsnRow = null;
                }
            }
            jsn.AddVariable("Results", jsnRowsArray);
            OutputResult(jsn);
            jsnRowsArray.Dispose();
            jsnRowsArray = null;
            jsn.Dispose();
            jsn = null;
            lst = null;
            base.GetData();
        }

        protected override void AddFilterStates()
        {
            //AddFilterState("CreatedDateFrom");
            //AddFilterState("CreatedDateTo");
            AddFilterState("TaskDateFrom");
            AddFilterState("TaskDateTo");
            AddFilterState("ReviewOnly");
            AddFilterState("TaskType");
            AddFilterState("TaskStatus");
            AddFilterState("CustomerName");
            AddFilterState("Salesman");
            AddFilterState("TaskReminderDate");
            base.AddFilterStates();
        }
        public void ExportToCSV()
        {
            JsonObject jsn = new JsonObject();
            try
            {
                string filePath = string.Empty;
                string strFilename = String.Format("report_u{0}r{1}.xlsx", LoginID, 0);
                DataTable dtResult = Rebound.GlobalTrader.BLL.ToDo.DataListNugget_Export(
                     GetFormValue_NullableDateTime("CreatedDateFrom"),
                GetFormValue_NullableDateTime("CreatedDateTo"),
                GetFormValue_NullableDateTime("TaskDateFrom"),
                GetFormValue_NullableDateTime("TaskDateTo"),

                 GetFormValue_StringForNameSearchDecode("TaskType"),
                  GetFormValue_StringForNameSearchDecode("TaskStatus"),
                   GetFormValue_StringForNameSearchDecode("CustomerName"),
                  GetFormValue_NullableInt("Salesman"),
                  SessionManager.LoginID,
                  SessionManager.ClientID,
                  GetFormValue_NullableInt("SortIndex"),
                 GetFormValue_NullableInt("SortDir", SortColumnDirection.ASC),
                 GetFormValue_NullableInt("PageIndex", 0),
                 GetFormValue_NullableInt("PageSize", 10)
                 , GetFormValue_Boolean("ReviewOnly")
                 , GetFormValue_NullableDateTime("TaskReminderDate")
                );
                filePath = (new EPPlusExportUtility()).ExportDataTableToCSV(dtResult, strFilename, "ToDoResult");
                //return saved filename to the page
                jsn.AddVariable("Filename", filePath);
                OutputResult(jsn);

            }
            catch (Exception e)
            {
                WriteError(e);
            }
            finally
            {
                jsn.Dispose();
                jsn = null;
            }
        }
    }
}
