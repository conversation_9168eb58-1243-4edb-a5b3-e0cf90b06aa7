﻿/*===========================================================================================  
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-230712]     An.TranTan		 07-FEB-2025		CREATE		Revert permission
===========================================================================================  
*/
--delete site page
DELETE tbSitePage where SitePageId = 2001107 and ShortName = 'Orders_BOMDetail'

IF EXISTS(SELECT TOP 1 1 FROM tbSecurityFunction WHERE SecurityFunctionId = 20010034 AND FunctionName = 'Orders_BOMDetail_Import_Export_SourcingResult')
BEGIN
	--delete permission value of groups
	DELETE tbSecurityGroupSecurityFunctionPermission WHERE SecurityFunctionNo = 20010034
	
	--delete security function
	DELETE tbSecurityFunction WHERE SecurityFunctionId = 20010034 AND FunctionName = 'Orders_BOMDetail_Import_Export_SourcingResult'
END
