///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.AutoSearch");

Rebound.GlobalTrader.Site.Controls.AutoSearch.MailMessageTo = function(element){
	Rebound.GlobalTrader.Site.Controls.AutoSearch.MailMessageTo.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.AutoSearch.MailMessageTo.prototype = {

	initialize: function(){
		Rebound.GlobalTrader.Site.Controls.AutoSearch.MailMessageTo.callBaseMethod(this, "initialize");
		this.addDataReturnedEvent(Function.createDelegate(this, this.dataReturned));
		this.setupDataObject("MailMessageTo");
	},
	
	dispose: function(){
		if (this.isDisposed) return;
		Rebound.GlobalTrader.Site.Controls.AutoSearch.MailMessageTo.callBaseMethod(this, "dispose");
	},
	
	dataReturned: function(){
		if (!this._result) return;
		if (this._result.TotalRecords > 0) {
			for (var i = 0, l = this._result.Results.length; i < l; i++) {
				var res = this._result.Results[i];
				strHTML = "";
				if (res.Type.toUpperCase() == "GROUP") strHTML += '<div class="mailGroup">';
				strHTML += res.Name;
				if (res.Type.toUpperCase() == "GROUP") strHTML += "</div>";
				this.addResultItem(strHTML, res.Name, res.ID, res.Type);
				strHTML = null;
				res = null;
			}
		}
	}
};
Rebound.GlobalTrader.Site.Controls.AutoSearch.MailMessageTo.registerClass("Rebound.GlobalTrader.Site.Controls.AutoSearch.MailMessageTo", Rebound.GlobalTrader.Site.Controls.AutoSearch.Base, Sys.IDisposable);
