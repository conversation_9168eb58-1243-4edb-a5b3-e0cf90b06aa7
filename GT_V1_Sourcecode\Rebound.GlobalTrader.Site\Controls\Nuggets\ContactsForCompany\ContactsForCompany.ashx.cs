//Marker     Changed by      Date         Remarks
//[001]      Vinay           09/07/2012   This need for Rebound- Invoice bulk Emailer
//[002]      Vinay           09/10/2012   Degete Ref:#26#  - Add two more columns contact to identify Default Purchase ledger and Default Sales ledger
using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets.Data {
	[WebService(Namespace = "http://tempuri.org/")]
	[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
	public class ContactsForCompany : Rebound.GlobalTrader.Site.Data.Base {

		public override void ProcessRequest(HttpContext context) {
			if (base.init(context)) {
				switch (Action) {
					case "GetContactList": GetContactList(); break;
					case "AddNew": AddNew(); break;
					case "Delete": Delete(); break;
					case "MakeDefaultSO": MakeDefaultSO(); break;
					case "MakeDefaultPO": MakeDefaultPO(); break;
                    //[002] code start
                    case "MakeDefaultSOLedger": MakeDefaultSOLedger(); break;
                    case "MakeDefaultPOLedger": MakeDefaultPOLedger(); break;
                    //[002] code end
					default: WriteErrorActionNotFound(); break;
				}
			}
		}

		private void GetContactList() {
			List<BLL.Contact> lst = BLL.Contact.GetListForCompany(ID);
			JsonObject jsn = new JsonObject();
			JsonObject jsnContacts = null;
			jsnContacts = new JsonObject(true);
			foreach (BLL.Contact con in lst) {
				JsonObject jsnContact = new JsonObject();
				jsnContact.AddVariable("ID", con.ContactId);
				jsnContact.AddVariable("Name", con.ContactName);
				jsnContact.AddVariable("Tel", con.Telephone);
				jsnContact.AddVariable("Email", con.EMail);
				jsnContact.AddVariable("Inactive", con.Inactive);
				jsnContact.AddVariable("DefaultSO", con.DefaultSO);
				jsnContact.AddVariable("DefaultPO", con.DefaultPO);
                //[001] code start
                jsnContact.AddVariable("FinanceContact",con.FinanceContact);
                //[001] code end

                //[002] code start
                jsnContact.AddVariable("DefaultPOLedger", con.DefaultPOLedger);
                jsnContact.AddVariable("DefaultSOLedger", con.DefaultSOLedger);
                //[002] code end
				jsnContacts.AddVariable(jsnContact);
				jsnContact.Dispose(); jsnContact = null;
			}
			jsn.AddVariable("Contacts", jsnContacts);
			OutputResult(jsn);
			jsn.Dispose(); jsn = null;
		}

		private void AddNew() {
			try {
				//add address is there is one
				int? intAddressID = null;
                //if (GetFormValue_Boolean("HasAddress")) {
                //    intAddressID = BLL.Address.Insert(
                //        GetFormValue_String("AddressName")
                //        , GetFormValue_String("Address1")
                //        , GetFormValue_String("Address2")
                //        , GetFormValue_String("Address3")
                //        , GetFormValue_String("County")
                //        , GetFormValue_String("Town")
                //        , GetFormValue_String("State")
                //        , GetFormValue_NullableInt("Country")
                //        , GetFormValue_String("Postcode")
                //        , LoginID
                //    );
                //}

				//add contact
				int intNewContactID = BLL.Contact.Insert(
						string.Format("{0} {1}", GetFormValue_String("FirstName").Trim(), GetFormValue_String("Surname").Trim())
						, GetFormValue_String("Nickname") //stored in salutation
						, GetFormValue_String("FirstName")
						, GetFormValue_String("Surname")
						, GetFormValue_String("Tel")
						, GetFormValue_String("Extension")
						, GetFormValue_String("Fax")
						, GetFormValue_String("JobTitle")
						, GetFormValue_String("Email")
						, GetFormValue_String("HomeTel")
						, GetFormValue_String("MobileTel")
						, ID
						, null
                        , GetFormValue_Int("CompanyAddress")
						, GetFormValue_NullableBoolean("TextOnlyEmail")
						, false
						, LoginID
                        //[001] code start
                        , GetFormValue_NullableBoolean("FinanceContact")
                        , GetFormValue_Boolean("IsSendShipmentNotification")
                    //[001] code end
                    );

				//output JSON
				JsonObject jsn = new JsonObject();
				jsn.AddVariable("Result", intNewContactID > 0);
				jsn.AddVariable("NewID", intNewContactID);
				OutputResult(jsn);
				jsn.Dispose(); jsn = null;
			} catch (Exception ex) {
				WriteError(ex);
			}
		}

		private void Delete() {
			try {
				BLL.Contact con = BLL.Contact.Get(ID);
				if (con != null) {
					con.Inactive = true;
					con.UpdatedBy = LoginID;
					JsonObject jsn = new JsonObject();
					jsn.AddVariable("Result", con.Update());
					con = null;
					OutputResult(jsn);
					jsn.Dispose(); jsn = null;
				}
			} catch (Exception ex) {
				WriteError(ex);
			}
		}

		private void MakeDefaultSO() {
			try {
				BLL.Company co = BLL.Company.Get(ID);
				if (co != null) {
                        bool blnResult = BLL.Company.UpdateDefaultSOContact(
                        ID
                        , GetFormValue_Int("ContactID")
                        , SessionManager.LoginID
                    );
                //write result to JSON
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Result", blnResult);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
                }
            } catch (Exception ex) {
				WriteError(ex);
			}
		}

		private void MakeDefaultPO() {
			try {
				BLL.Company co = BLL.Company.Get(ID);
				if (co != null) {
                        bool blnResult = BLL.Company.UpdateDefaultPOContact(
                        ID
                        , GetFormValue_Int("ContactID")
                        , SessionManager.LoginID
                    );
                //write result to JSON
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Result", blnResult);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
				}
			} catch (Exception ex) {
				WriteError(ex);
			}
		}
        //[002] code start
        private void MakeDefaultPOLedger()
        {
            try
            {
                BLL.Company co = BLL.Company.Get(ID);
                if (co != null)
                {
                    bool blnResult = BLL.Company.UpdateDefaultPOLedgerContact(
                    ID
                    , GetFormValue_Int("ContactID")
                    , SessionManager.LoginID
                );
                    //write result to JSON
                    JsonObject jsn = new JsonObject();
                    jsn.AddVariable("Result", blnResult);
                    OutputResult(jsn);
                    jsn.Dispose(); jsn = null;
                }
            }
            catch (Exception ex)
            {
                WriteError(ex);
            }
        }
        private void MakeDefaultSOLedger()
        {
            try
            {
                BLL.Company co = BLL.Company.Get(ID);
                if (co != null)
                {
                    bool blnResult = BLL.Company.UpdateDefaultSOLedgerContact(
                    ID
                    , GetFormValue_Int("ContactID")
                    , SessionManager.LoginID
                );
                    //write result to JSON
                    JsonObject jsn = new JsonObject();
                    jsn.AddVariable("Result", blnResult);
                    OutputResult(jsn);
                    jsn.Dispose(); jsn = null;
                }
            }
            catch (Exception ex)
            {
                WriteError(ex);
            }
        }
        //[002] code end
	}
}
