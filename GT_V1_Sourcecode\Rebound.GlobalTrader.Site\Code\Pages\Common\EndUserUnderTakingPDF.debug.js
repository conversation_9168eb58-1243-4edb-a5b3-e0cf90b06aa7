///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference name="~/Controls/IconButton/IconButton.js" />
///<reference name="~/Controls/ReboundTextBox/ReboundTextBox.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
/*
Marker     Changed by      Date         Remarks
[001]      <PERSON> Gupta     14/05/2021   Add PDF Document upload on ihs catloge
*/
Type.registerNamespace("Rebound.GlobalTrader.Site.Pages");

Rebound.GlobalTrader.Site.Pages.EndUserUnderTakingPDF = function(el) {
    Rebound.GlobalTrader.Site.Pages.EndUserUnderTakingPDF.initializeBase(this, [el]);
    this._intSalesOrderLineID = 0;
};

Rebound.GlobalTrader.Site.Pages.EndUserUnderTakingPDF.prototype = {

    get_ctlPageTitle: function() { return this._ctlPageTitle; }, set_ctlPageTitle: function(v) { if (this._ctlPageTitle !== v) this._ctlPageTitle = v; },
    get_ctlMainInfo: function() { return this._ctlMainInfo; }, set_ctlMainInfo: function(v) { if (this._ctlMainInfo !== v) this._ctlMainInfo = v; },
    get_ctlManufacturerPDFDragDrop: function() { return this._ctlManufacturerPDFDragDrop; }, set_ctlManufacturerPDFDragDrop: function(v) { if (this._ctlManufacturerPDFDragDrop !== v) this._ctlManufacturerPDFDragDrop = v; },
    initialize: function() {
        Rebound.GlobalTrader.Site.Pages.EndUserUnderTakingPDF.callBaseMethod(this, "initialize");
    },

    goInit: function() {
        if (this._ctlMainInfo) this._ctlMainInfo.addSaveEditComplete(Function.createDelegate(this, this.ctlMainInfo_EditComplete));
        if (this._ctlManufacturerPDFDragDrop) this._ctlManufacturerPDFDragDrop.getData();
        Rebound.GlobalTrader.Site.Pages.EndUserUnderTakingPDF.callBaseMethod(this, "goInit");
    },

    dispose: function() {
        if (this.isDisposed) return;
        if (this._ctlPageTitle) this._ctlPageTitle.dispose();
        if (this._ctlMainInfo) this._ctlMainInfo.dispose();
        if (this._ctlManufacturerEXCELDragDrop) this._ctlManufacturerEXCELDragDrop.dispose();
        this._intSalesOrderLineID = null;
        this._ctlPageTitle = null;
        this._ctlMainInfo = null;
        this._ctlManufacturerPDF = null;
        Rebound.GlobalTrader.Site.Pages.EndUserUnderTakingPDF.callBaseMethod(this, "dispose");
    },

    ctlMainInfo_EditComplete: function() {
        //this._ctlPageTitle.updateTitle(this._ctlMainInfo._frmEdit.getFieldValue("ctlName"));
        //$R_FN.setInnerHTML(this._lblAbbreviation, this._ctlMainInfo._frmEdit.getFieldValue("ctlAbbreviation"));

    }

};

Rebound.GlobalTrader.Site.Pages.EndUserUnderTakingPDF.registerClass("Rebound.GlobalTrader.Site.Pages.EndUserUnderTakingPDF", Rebound.GlobalTrader.Site.Pages.Content, Sys.IDisposable);
