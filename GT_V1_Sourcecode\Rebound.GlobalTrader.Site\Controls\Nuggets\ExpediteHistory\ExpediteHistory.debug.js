///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-------------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//
// RP 17.11.2009:
// - get the YTD / last year values in one hit
// - don't get data straight away because the Company Detail page now switches tabs
//   with javascript
//----------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");

Rebound.GlobalTrader.Site.Controls.Nuggets.ExpediteHistory = function (element) {
    Rebound.GlobalTrader.Site.Controls.Nuggets.ExpediteHistory.initializeBase(this, [element]);
	this._intPOQuoteID = -1;
    this._intBOMID = -1;
};

Rebound.GlobalTrader.Site.Controls.Nuggets.ExpediteHistory.prototype = {
    get_intBOMID: function() { return this._intBOMID; }, set_intBOMID: function(value) { if (this._intBOMID !== value) this._intBOMID = value; },
    get_tblCreditHistory: function() { return this._tblCreditHistory; }, set_tblCreditHistory: function(value) { if (this._tblCreditHistory !== value) this._tblCreditHistory = value; },
    get_intPOQuoteID: function() { return this._intPOQuoteID; }, set_intPOQuoteID: function(v) { if (this._intPOQuoteID !== v) this._intPOQuoteID = v; },
  
    initialize: function() {
     Rebound.GlobalTrader.Site.Controls.Nuggets.ExpediteHistory.callBaseMethod(this, "initialize");
     this._strPathToData = "controls/Nuggets/ExpediteHistory";
     this._strDataObject = "ExpediteHistory";
     this.addRefreshEvent(Function.createDelegate(this, this.GetExpediteHistory));
     this.showLoading(false);
     this.showContent(true);
     this.showContentLoading(false);
     this.GetExpediteHistory();
    },

    dispose: function() {
        if (this.isDisposed) return;
       
        if (this._tblCreditHistory) this._tblCreditHistory.dispose();
      
        this._tblCreditHistory = null;
       
        Rebound.GlobalTrader.Site.Controls.Nuggets.ExpediteHistory.callBaseMethod(this, "dispose");
    },
    GetExpediteHistory: function () {
       
          this.showContent(true);
        this.showCreditHistoryError(false);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData(this._strPathToData);
        obj.set_DataObject(this._strDataObject);
        obj.set_DataAction("GetExpediteHistory");
        obj.addParameter("ID", this._intBOMID);
        obj.addDataOK(Function.createDelegate(this, this.getCreditHistoryOK));
        obj.addError(Function.createDelegate(this, this.getCreditHistoryError));
        obj.addTimeout(Function.createDelegate(this, this.getCreditHistoryError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getCreditHistoryOK: function(args) {
        res = args._result;
        this.showLoadingCreditHistory(false);
        this.showCreditHistoryError(false);
        this._tblCreditHistory.clearTable();
        this.processExpediteHistory(this._tblCreditHistory);
        this._tblCreditHistory.resizeColumns();
    },

    getCreditHistoryError: function(args) {
        this.showLoadingCreditHistory(false);
        this.showCreditHistoryError(true, args.get_ErrorMessage());
    },

    showLoadingCreditHistory: function(blnShow) {
        $R_FN.showElement(this._pnlLoadingCreditHistory, blnShow);
        $R_FN.showElement(this._pnlCreditHistory, !blnShow);
        this.showLoading(blnShow);
        if (blnShow) $R_FN.showElement(this._pnlGetCreditHistory, false);
    },

    showCreditHistoryError: function(blnShow, strMessage) {
        $R_FN.showElement(this._pnlCreditHistoryError, blnShow);
        if (blnShow) {
            this.showLoading(false);
            $R_FN.showElement(this._pnlCreditHistory, false);
            $R_FN.showElement(this._pnlGetCreditHistory, false);
            $R_FN.setInnerHTML(this._pnlCreditHistoryError, strMessage);
        }
    },

    showCreditHistoryGetData: function(blnShow) {
        $R_FN.showElement(this._pnlGetCreditHistory, blnShow);
        if (blnShow) {
            this.showLoading(false);
            $R_FN.showElement(this._pnlLoadingCreditHistory, false);
            $R_FN.showElement(this._pnlCreditHistory, false);
            $R_FN.setInnerHTML(this._pnlCreditHistoryError, false);
        }
    },

    processExpediteHistory: function (tbl) {
        //debugger;
        if (res.ExpHist) {
            for (var i = 0; i < res.ExpHist.length; i++) {
                var row = res.ExpHist[i];
                var aryData = [
					$R_FN.setCleanTextValue(row.Note),
                    row.ReqNos,
					row.Date,
                    row.EmployeeName,
                    row.To,
                    row.CCUserID,
                    row.SendToGroup
                ];
                tbl.addRow(aryData, row.ID, false);
                row = null; aryData = null;
            }
        }
    }

 
};

Rebound.GlobalTrader.Site.Controls.Nuggets.ExpediteHistory.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.ExpediteHistory", Rebound.GlobalTrader.Site.Controls.Nuggets.Base);
