﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;

namespace Rebound.GlobalTrader.Site.Controls.DropDowns
{
    public class CounterfeitElectronicParts : Base
    {
        protected override void OnInit(EventArgs e)
        {
            CanAddTo = false;
            IncludeNoValue = false;
            NoValue_Value = "-1";
            InitialValue = "0";
            base.OnInit(e);
        }

        protected override void OnLoad(EventArgs e)
        {
            SetDropDownType("CounterfeitElectronicParts");
            AddScriptReference("Controls.DropDowns.CounterfeitElectronicParts.CounterfeitElectronicParts");
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.DropDowns.CounterfeitElectronicParts", ClientID);
            base.OnLoad(e);
        }
    }
}