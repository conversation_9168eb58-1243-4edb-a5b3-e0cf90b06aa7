<%@ Control Language="C#" CodeBehind="QuoteLines.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.ItemSearch.QuoteLines" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>

<ReboundUI_ItemSearch:DesignBase id="ctlDB" runat="server" InitialSortDirection="DESC">

	<FieldsLeft>
		<ReboundUI_FilterDataItemRow:Numerical id="ctlQuoteNo" runat="server" ResourceTitle="QuoteNo" />
		<ReboundUI_FilterDataItemRow:TextBox id="ctlPartNo" runat="server" ResourceTitle="PartNo" />
		<ReboundUI_FilterDataItemRow:TextBox id="ctlCompany" runat="server" ResourceTitle="Company" />
	</FieldsLeft>
	<FieldsRight>
		<ReboundUI_FilterDataItemRow:CheckBox id="ctlIncludeClosed" runat="server" ResourceTitle="IncludeClosed" />
		<ReboundUI_FilterDataItemRow:DateSelect id="ctlDateQuotedFrom" runat="server" ResourceTitle="DateQuotedFrom" />
		<ReboundUI_FilterDataItemRow:DateSelect id="ctlDateQuotedTo" runat="server" ResourceTitle="DateQuotedTo" />
	</FieldsRight>	
	
</ReboundUI_ItemSearch:DesignBase>
