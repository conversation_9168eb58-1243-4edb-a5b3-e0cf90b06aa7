﻿
GO

/****** Object:  StoredProcedure [dbo].[usp_selectExportApprovalData]    Script Date: 11/18/2024 9:35:56 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


/*
===========================================================================================
TASK      			UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-210625]			Phuc Hoang			09-Aug-2024		CREATE          Sanctioned manufacturers need to be highlighted in red on the SOR PDF and SO screens.
[US-222102]			Phuc Hoang		 	18-Nov-2024		UPDATE			Bug 222102: [PROD BUG] OGEL Status
===========================================================================================
*/

CREATE OR ALTER PROCEDURE [dbo].[usp_selectExportApprovalData]                                                                                            
--***************************************************************************************************                                                                                            
--[001]  Created  Abhinav Saxena  29-03-2023  Add new proc for getting export approval data.   
--[002]  Altered  Abhinav Saxena  25-08-2023  Make changes as suggested in RP-2219.                                                                                   
--***************************************************************************************************                                                                                            
    @SalesOrderId  INT                                                                                            
  , @ClientNo   INT                      
  , @LoginId   INT                      
  , @Tabid    INT                       
AS                                   
BEGIN                      
SET NOCOUNT ON                      
UPDATE easg SET                      
easg.ApprovalStatusId=CASE                  
WHEN (ISNULL(ct.OGEL,0)>0) AND(sol.ECCNCode !='NLR') AND(ISNULL(sol.ECCNCode,'[Blank]') !='[Blank]') AND ((SELECT ISNULL(ECCNClientNotify,0) FROM tbECCN t1 WHERE t1.ECCNCode=sol.ECCNCode)=1)    
THEN CASE WHEN easg.ApprovalStatusId=7 THEN 3 ELSE easg.ApprovalStatusId END ELSE                   
CASE WHEN(isnull((select top 1 m.ECCNStatus from dbo.tbECCN m   where m.ECCNCode= sol.ECCNCode ),0))>0 THEN CASE WHEN easg.ApprovalStatusId=7 THEN 3 ELSE easg.ApprovalStatusId END ELSE 7 END END                  
FROM tbSO_ExportApprovalStatusOGEL easg                    
LEFT JOIN tbSalesOrderLine sol On easg.SalesOrderLineNo=sol.SalesOrderLineId                      
LEFT JOIN dbo.tbManufacturer mf ON sol.ManufacturerNo = mf.ManufacturerId                       
LEFT JOIN dbo.tbSOExportApprovalStatusMaster easm ON easg.ApprovalStatusId=easm.ApprovalStatusId                      
LEFT JOIN tbLogin lg ON easg.ApprovedBy=lg.LoginId                       
LEFT OUTER JOIN tbSO_ExportApprovalDetails ead ON easg.ExportApprovalId=ead.ExportApprovalNo                      
LEFT OUTER JOIN tbCountry ct ON ead.EndDestinationCountryNo=ct.CountryId                   
WHERE easg.SalesOrderNo=@SalesOrderId                  
                
                
UPDATE easg SET                    
easg.ApprovalStatusId=CASE                
WHEN(isnull((select top 1 m.ECCNStatus from dbo.tbECCN m   where m.ECCNCode= ISNULL(sol.ECCNCode,'[Blank]') ),0))>0 AND                 
(SELECT COUNT(1) FROM tbSOLineEUUPDF WHERE ExportApprovalNo=easg.ExportApprovalId)=0             
AND easg.ApprovalStatusId !=8 AND easg.ApprovalStatusId !=4 AND ISNULL(easg.ISAwaitingEEUStatus,0)=0             
THEN 5              
WHEN(isnull((select top 1 m.ECCNStatus from dbo.tbECCN m   where m.ECCNCode= sol.ECCNCode ),0))>0 AND                 
(SELECT COUNT(1) FROM tbSOLineEUUPDF WHERE ExportApprovalNo=easg.ExportApprovalId)=0             
AND easg.ApprovalStatusId !=4 AND ISNULL(easg.ISAwaitingEEUStatus,0)>0            
THEN 8               
WHEN easg.ApprovalStatusId=5 AND (isnull((select top 1 m.ECCNStatus from dbo.tbECCN m   where m.ECCNCode= sol.ECCNCode ),0))>0 AND                 
(SELECT COUNT(1) FROM tbSOLineEUUPDF WHERE ExportApprovalNo=easg.ExportApprovalId)>0 THEN 3                
            
WHEN easg.ApprovalStatusId=8 AND ISNULL(easg.OGELNumber,0)>0 AND (isnull((select top 1 m.ECCNStatus from dbo.tbECCN m   where m.ECCNCode= sol.ECCNCode ),0))>0 AND                 
(SELECT COUNT(1) FROM tbSOLineEUUPDF WHERE ExportApprovalNo=easg.ExportApprovalId)>0 THEN 1            
WHEN easg.ApprovalStatusId=8 AND ISNULL(easg.OGELNumber,0)=0 AND (isnull((select top 1 m.ECCNStatus from dbo.tbECCN m   where m.ECCNCode= sol.ECCNCode ),0))>0 AND                 
(SELECT COUNT(1) FROM tbSOLineEUUPDF WHERE ExportApprovalNo=easg.ExportApprovalId)>0 THEN 2                
            
ELSE easg.ApprovalStatusId END              
               
FROM tbSO_ExportApprovalStatusOGEL easg                  
LEFT JOIN tbSalesOrderLine sol On easg.SalesOrderLineNo=sol.SalesOrderLineId                    
                
WHERE easg.SalesOrderNo=@SalesOrderId           
      
UPDATE easg SET       
easg.ApprovalStatusId=CASE WHEN ISNULL(easg.IsClosed,0)=1 THEN 7 ELSE easg.ApprovalStatusId END       
FROM tbSO_ExportApprovalStatusOGEL easg         
WHERE easg.SalesOrderNo=@SalesOrderId        
  
-----RP-2219--- Work----  
UPDATE easg SET       
easg.ApprovalStatusId=7     
FROM tbSO_ExportApprovalStatusOGEL easg   
INNER JOIN tbSalesOrderLine sol on easg.SalesOrderLineNo=sol.SalesOrderLineId  
WHERE easg.SalesOrderNo=@SalesOrderId AND sol.FullPart LIKE 'ENHANCEDINSPECTION%'  
--------END------------                 
CREATE TABLE #tmpStatus                      
(                      
ID  INT                      
)                      
DELETE FROM #tmpStatus                      
IF(@Tabid=1)             
BEGIN                      
INSERT INTO #tmpStatus VALUES(1)                      
INSERT INTO #tmpStatus VALUES(2)              
INSERT INTO #tmpStatus VALUES(3)                      
INSERT INTO #tmpStatus VALUES(4)                      
INSERT INTO #tmpStatus VALUES(5)                    
INSERT INTO #tmpStatus VALUES(6)                    
INSERT INTO #tmpStatus VALUES(7)                
INSERT INTO #tmpStatus VALUES(8)                 
END                      
ELSE IF(@Tabid=2)                      
BEGIN                      
INSERT INTO #tmpStatus VALUES(1)                      
INSERT INTO #tmpStatus VALUES(2)            
INSERT INTO #tmpStatus VALUES(6)            
INSERT INTO #tmpStatus VALUES(8)                       
END                      
ELSE IF(@Tabid=3)                      
BEGIN                      
INSERT INTO #tmpStatus VALUES(3)                      
INSERT INTO #tmpStatus VALUES(5)                      
END                      
CREATE TABLE #TmpExportApprovalData                      
(                      
ExportApprovalId   INT,                      
SalesOrderLineId   INT,                      
SOSerialNo     INT,                      
ROHS      TINYINT,                      
CustomerPart    NVARCHAR(MAX),                      
Part      NVARCHAR(MAX),                      
ManufacturerNo    INT,                      
ManufacturerCode   NVARCHAR(MAX),                      
DateCode     NVARCHAR(MAX),                      
ExportApprovalStatusId  INT,                      
ExportApprovalStatus  NVARCHAR(MAX),                      
OGELLicenseRequired   NVARCHAR(MAX),                      
EUUFormRequired    NVARCHAR(MAX),                      
Dated      DATETIME,                      
[By]      NVARCHAR(MAX),                      
Comment      NVARCHAR(MAX),                      
IsAllocationDone   BIT,          
IsExportDetailsFilled  BIT,        
ISOGELRequired  BIT,
RestrictedMfrNo  INT,
RestrictedMfrInactive BIT 
)                      
                      
INSERT INTO #TmpExportApprovalData                      
SELECT                       
easg.ExportApprovalId,                      
sol.SalesOrderLineId,                      
sol.SOSerialNo,                      
sol.ROHS ,                       
sol.CustomerPart,                       
sol.Part,                       
sol.ManufacturerNo,                       
mf.ManufacturerCode,                      
DateCode,                      
ISNULL(easg.ApprovalStatusId,0),                      
ISNULL(easm.ApprovalName,'Not Initiate') ,                      
CASE WHEN ISNULL(easg.OGELNumber,0)>0 THEN 'Yes' ELSE 'No' END,               
--ELSE CASE WHEN sol.ECCNCode='NLR' THEN 'No' ELSE CASE WHEN (ISNULL(ct.OGEL,0)>0) AND ((SELECT ISNULL(ECCNClientNotify,0) FROM tbECCN t1 WHERE t1.ECCNCode=sol.ECCNCode)=1) THEN 'Yes' ELSE 'No' END END END,

CASE WHEN(isnull((select top 1 m.ECCNStatus from dbo.tbECCN m   where m.ECCNCode= ISNULL(sol.ECCNCode,'[Blank]')),0))>0 THEN 'Yes' ELSE 'No' END,                      
easg.ApprovedDate,                      
lg.EmployeeName,                      
easg.ApprovalComment,  
CASE WHEN (SELECT COUNT(StockNo) FROM tbAllocation WHERE SalesOrderLineNo=easg.SalesOrderLineNo)>0 THEN 1 ELSE 0 END,          
CAST(ISNULL(ead.IsInformationFilled,0) AS BIT) ,        
CAST(ISNULL(so.OGEL_Required,0) AS BIT),
retr.ManufacturerNo AS RestrictedMfrNo, 
retr.Inactive AS RestrictedMfrInactive

FROM tbSO_ExportApprovalStatusOGEL easg            
LEFT OUTER JOIN tbSO_ExportApprovalDetails ead ON easg.ExportApprovalId=ead.ExportApprovalNo                    
LEFT JOIN tbSalesOrderLine sol On easg.SalesOrderLineNo=sol.SalesOrderLineId                      
LEFT JOIN dbo.tbManufacturer mf ON sol.ManufacturerNo = mf.ManufacturerId 
LEFT JOIN dbo.tbSOExportApprovalStatusMaster easm ON easg.ApprovalStatusId=easm.ApprovalStatusId                      
LEFT JOIN tbLogin lg ON easg.ApprovedBy=lg.LoginId                        
LEFT OUTER JOIN tbSO_ExportApprovalDetails ed ON easg.ExportApprovalId=ed.ExportApprovalNo                    
LEFT OUTER JOIN tbCountry ct ON ed.EndDestinationCountryNo=ct.CountryId          
LEFT OUTER JOIN tbSalesOrder so ON easg.SalesOrderNo=so.SalesOrderId 
LEFT JOIN dbo.tbRestrictedManufacturer retr ON retr.ManufacturerNo = mf.ManufacturerId AND retr.ClientNo = so.ClientNo
WHERE easg.SalesOrderNo=@SalesOrderId AND(easg.ApprovalStatusId IN(SELECT ID FROM #tmpStatus))                      
                    
                      
SELECT * FROM #TmpExportApprovalData  ORDER By SOSerialNo ASC                    
                      
DROP TABLE #TmpExportApprovalData                      
DROP TABLE #tmpStatus                      
SET NOCOUNT OFF                      
END 
GO


