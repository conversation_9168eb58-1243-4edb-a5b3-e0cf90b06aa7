﻿
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].[usp_datalistnugget_QuoteLine]                      
--********************************************************************************************                                              
--[008]   Ravi    19-09-2023      RP-2338  AS6081 Search/Filter functionality on different pages        
--[009]   Abhinav Saxena 09-11-2023  For RP-1564.  
--********************************************************************************************                      
    @ClientId int                      
  , @TeamId int = NULL                      
  , @DivisionId int = NULL                      
  , @LoginId int = NULL                      
  , @OrderBy int = 1                      
  , @SortDir int = 1                      
  , @PageIndex int = 0                      
  , @PageSize int = 10                      
  , @PartSearch nvarchar(50) = NULL                      
  , @ContactSearch nvarchar(50) = NULL                      
  , @CMSearch nvarchar(50) = NULL                      
  , @SalesmanSearch int = NULL                      
  , @IncludeClosed bit = 0                      
  , @QuoteNoLo int = NULL                      
  , @QuoteNoHi int = NULL                      
  , @DateQuotedFrom datetime = NULL                      
  , @DateQuotedTo datetime = NULL                      
  , @RecentOnly bit = 1                     
  , @Important bit = null                       
  , @TotalLo float = NULL                    
  , @TotalHi float = NULL                    
  , @QuoteStatus int = NULL                   
  , @TotalProfitLo float = null                    
  , @TotalProfitHi float = null         
  , @AS6081 BIT = NULL --[008]  
  , @SelectedClientNo INT=NULL  
  , @SelectedLoginID  INT=NULL      
    WITH RECOMPILE                      
AS                       
    DECLARE @RecentDate datetime                      
      , @StartPage int                      
      , @EndPage int                      
    SET @RecentDate = dbo.ufn_get_date_from_datetime(dateadd(mm, -3, getdate()))                      
    SET @StartPage = (@PageIndex * @PageSize + 1)                      
    SET @EndPage = ((@PageIndex + 1) * @PageSize)                      
                      
    IF (NOT @DateQuotedFrom IS NULL)                       
        SET @DateQuotedFrom = dbo.ufn_get_start_of_day_for_date(@DateQuotedFrom)                      
                      
    IF (NOT @DateQuotedTo IS NULL)                       
        SET @DateQuotedTo = dbo.ufn_get_end_of_day_for_date(@DateQuotedTo)                      
                      
    IF (NOT @LoginId IS NULL)                       
        SET @SalesmanSearch = NULL                      
                      
                      
-- semi-colon needed for WITH                      
;                      
    WITH    cteSearch                      
              AS (SELECT    qt.QuoteId                      
                          , qt.QuoteNumber                      
                          , qtl.Part                      
                          , qtl.Price                      
                          , cu.CurrencyCode                    
                          , qtl.Quantity                      
                          , qt.DateQuoted                      
                        , co.CompanyName                      
                          , qt.CompanyNo                      
                          , cn.ContactName                                   , qt.ContactNo                      
                          , qtl.ROHS                      
        , (isnull(qtl.Price,0)*isnull(qtl.Quantity,0))/dbo.ufn_get_exchange_rate(qt.CurrencyNo, getdate()) AS TotalInBase                    
   , isnull(qtl.Price,0)*isnull(qtl.Quantity,0) as TotalValue                    
        , qt.CurrencyNo                    
        , qt.Salesman                    
        , lg.EmployeeName AS SalesmanName                    
        , qs.Name as QuoteStatusName                   
--,round((isnull(qtl.Price,0)-(isnull(qtl.OriginalOfferPrice,0))/dbo.ufn_get_exchange_rate(qtl.OriginalOfferCurrencyNo, qt.DateQuoted)),5) as OfferProfit                   
, round((isnull(qtl.Price,0))-((isnull(qtl.OriginalOfferPrice,0)/dbo.ufn_get_exchange_rate(qtl.OriginalOfferCurrencyNo,          
isnull(qtl.OriginalOfferDate,qtl.dlup))*dbo.ufn_get_exchange_rate(qt.CurrencyNo, qt.DateQuoted))),5) * qtl.Quantity as OfferProfit               
                          , ROW_NUMBER() OVER (ORDER BY --                      
                                               case WHEN @OrderBy = 1                      
                                               AND @SortDir = 2 THEN qt.QuoteNumber                      
                                               END DESC                       
  , case WHEN @OrderBy = 1 THEN qt.QuoteNumber                      
                                       END                      
                                     , case WHEN @OrderBy = 2                      
                                                 AND @SortDir = 2 THEN qtl.Part                      
                                       END DESC                      
                                     , case WHEN @OrderBy = 2 THEN qtl.Part                      
  END                      
           , case WHEN @OrderBy = 3                      
                                                AND @SortDir = 2 THEN qtl.Price                      
                                       END DESC                      
                                     , case WHEN @OrderBy = 3 THEN qtl.Price                      
                                       END                      
                                     , case WHEN @OrderBy = 4                      
                                          AND @SortDir = 2 THEN qtl.Quantity                      
                                       END DESC                      
                                     , case WHEN @OrderBy = 4 THEN qtl.Quantity                      
                              END                      
                                     , case WHEN @OrderBy = 5                      
                                                 AND @SortDir = 2 THEN co.CompanyName                      
                                       END DESC                      
                                     , case WHEN @OrderBy = 5 THEN co.CompanyName                      
                                       END                      
                                     , case WHEN @OrderBy = 6                      
                                                 AND @SortDir = 2 THEN qt.DateQuoted                      
                                       END DESC                      
                                     , case WHEN @OrderBy = 6 THEN qt.DateQuoted                      
                                       END                    
          , case WHEN @OrderBy = 7                    
                                                 AND @SortDir = 2 THEN lg.EmployeeName                    
                                       END DESC                    
                                     , case WHEN @OrderBy = 7 THEN lg.EmployeeName                    
                                       END                    
             , case WHEN @OrderBy = 8                     
                                                 AND @SortDir = 2 THEN isnull(qtl.Price,0)*isnull(qtl.Quantity,0)                    
                                       END DESC                    
                                     , case WHEN @OrderBy = 8 THEN isnull(qtl.Price,0)*isnull(qtl.Quantity,0)                    
                  
          end           
           , case WHEN @OrderBy = 9                     
                                                 AND @SortDir = 2 THEN isnull(round((isnull(qtl.Price,0)-(isnull                  
             (qtl.OriginalOfferPrice,0))/dbo.ufn_get_exchange_rate(qtl.OriginalOfferCurrencyNo, qt.DateQuoted)),5),0)                    
                                       END DESC                    
           , case WHEN @OrderBy = 9 THEN isnull(round((isnull(qtl.Price,0)-(                  
          isnull(qtl.OriginalOfferPrice,0))/dbo.ufn_get_exchange_rate(qtl.OriginalOfferCurrencyNo, qt.DateQuoted)),5),0)                   
                  
                  
          END                    
            ) AS RowNum           
   , ISNULL(qt.AS6081,0) AS AS6081 --[008]        
                  FROM      dbo.tbQuote qt                      
                  LEFT JOIN dbo.tbQuoteLine qtl ON qtl.QuoteNo = qt.QuoteId                      
                  JOIN      dbo.tbCompany co ON qt.CompanyNo = co.CompanyId                      
                  LEFT JOIN dbo.tbLogin lg ON qt.Salesman = lg.LoginId                      
                  LEFT JOIN dbo.tbContact cn ON qt.ContactNo = cn.ContactId                      
                  JOIN      dbo.tbCurrency cu ON qt.CurrencyNo = cu.CurrencyId                      
                  LEFT JOIN tbQuoteStatus qs ON qs.QuoteStatusId = qt.QuoteStatus                    
                  WHERE     qt.ClientNo = CASE WHEN @SelectedClientNo IS NULL THEN @ClientId    
      ELSE @SelectedClientNo END                    
                            AND ((@TeamId IS NULL)                      
                                 OR (NOT @TeamId IS NULL                      
                                     AND lg.TeamNo = @TeamId))                      
                AND ((@DivisionId IS NULL)                      
                                 OR (NOT @DivisionId IS NULL                      
                                     AND qt.DivisionNo = @DivisionId))                      
                            AND ((@LoginId IS NULL)                      
                                 OR (NOT @LoginId IS NULL                      
                                     AND qt.Salesman = @LoginId))                      
                            AND ((@RecentOnly = 0)                      
                                 OR (@RecentOnly = 1                      
                                     AND DateQuoted >= @RecentDate))                      
                            AND ((@QuoteNoLo IS NULL)                      
                                 OR (NOT @QuoteNoLo IS NULL                      
                                     AND QuoteNumber >= @QuoteNoLo))                      
                            AND ((@QuoteNoHi IS NULL)                      
                                 OR (NOT @QuoteNoHi IS NULL                      
                                     AND QuoteNumber <= @QuoteNoHi))                      
                            AND ((@DateQuotedFrom IS NULL)                      
                                 OR (NOT @DateQuotedFrom IS NULL                      
                                     AND DateQuoted >= @DateQuotedFrom))                      
                            AND ((@DateQuotedTo IS NULL)                      
                                 OR (NOT @DateQuotedTo IS NULL                      
                               AND DateQuoted <= @DateQuotedTo))                      
                            AND ((@ContactSearch IS NULL)                      
                       OR (NOT @ContactSearch IS NULL                
                                     AND ContactName LIKE @ContactSearch))                      
                            AND ((@CMSearch IS NULL)                      
                                 OR (NOT @CMSearch IS NULL                      
                                     AND FullName LIKE @CMSearch))                      
                         AND ((@SalesmanSearch IS NULL)                      
                                 OR (NOT @SalesmanSearch IS NULL                      
                                     AND qt.Salesman = @SalesmanSearch))      
      AND ((@SelectedClientNo IS NULL)                      
                                 OR (NOT @SelectedClientNo IS NULL                      
                                     AND qt.companyNo IN(SELECT CompanyNo FROM dbo.ufn_GSA_GetMyCompnayIds (@SelectedLoginID,@ClientId))))                      
                            AND (qtl.Closed IS NULL                      
                                 OR (NOT qtl.Closed IS NULL                      
                                     AND qtl.Closed IN (0, @IncludeClosed)))                      
                            AND ((@PartSearch IS NULL)                      
                                 OR (NOT @PartSearch IS NULL                      
                                     AND (qtl.FullPart LIKE @PartSearch                      
                                          OR qtl.FullCustomerPart LIKE @PartSearch)))                     
                            AND (CASE WHEN @Important=1 THEN qt.isImportant ELSE 1 END  =                     
         CASE WHEN @Important=1 THEN @Important ELSE 1 END)                      
         -- Espire 28th aug                     
         AND ((@TotalLo IS NULL)                    
                                 OR (NOT @TotalLo IS NULL                    
                                     AND isnull(qtl.Price,0)*isnull(qtl.Quantity,0) >= @TotalLo))                    
                            AND ((@TotalHi IS NULL)                    
                                 OR (NOT @TotalHi IS NULL                    
                                     AND isnull(qtl.Price,0)*isnull(qtl.Quantity,0) <= @TotalHi))                      
          AND ((@QuoteStatus IS NULL)                      
                                 OR (NOT @QuoteStatus IS NULL                      
                                     AND isnull(qt.QuoteStatus,0) = @QuoteStatus))                      
                  
          --offer profit fillter added                 
     AND ((@TotalProfitLo IS NULL)                    
 OR (NOT @TotalProfitLo IS NULL                    
  AND (isnull(round((isnull(qtl.Price,0))-((isnull(qtl.OriginalOfferPrice,0)/dbo.ufn_get_exchange_rate(qtl.OriginalOfferCurrencyNo,               
isnull(qtl.OriginalOfferDate,qtl.dlup))*dbo.ufn_get_exchange_rate(qt.CurrencyNo, qt.DateQuoted))),5),0))*isnull(qtl.Quantity,0) >= @TotalProfitLo))                    
                    AND ((@TotalProfitHi IS NULL)                    
                         OR (NOT @TotalProfitHi IS NULL                    
AND (isnull(round((isnull(qtl.Price,0))-((isnull(qtl.OriginalOfferPrice,0)/dbo.ufn_get_exchange_rate(qtl.OriginalOfferCurrencyNo,              
 isnull(qtl.OriginalOfferDate,qtl.dlup))*dbo.ufn_get_exchange_rate(qt.CurrencyNo, qt.DateQuoted))),5),0))*isnull(qtl.Quantity,0) <= @TotalProfitHi))                  
    --[008] start        
  --AND ((@AS6081 IS NULL) OR (NOT @AS6081 IS NULL AND qt.AS6081 = @AS6081))    
  AND (        
      (@AS6081 = 1 and qt.AS6081 = 1)        
      OR        
      (@AS6081 IS NULL and (qt.AS6081 IS NULL OR qt.AS6081 = 1 or qt.AS6081 = 0))        
      OR        
      (@AS6081 = 0 and (qt.AS6081 IS NULL or qt.AS6081 = 0))        
     )      
   --[008] end              
                 )                      
        SELECT  *                      
              , (SELECT count(*)                      
                 FROM   cteSearch                      
                ) AS RowCnt                      
        FROM    cteSearch                      
        WHERE RowNum BETWEEN @StartPage AND @EndPage                      
        ORDER BY RowNum   
GO


