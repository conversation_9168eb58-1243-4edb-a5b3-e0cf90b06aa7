﻿CREATE OR ALTER PROCEDURE dbo.usp_ItemSearch_SupplierInvoice_DebitNotes
/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-204678]		An.TranTan			14-Aug-2024		CREATE			Search debit note in Supplier Invoice add/edit screen
===========================================================================================
*/
	@ClientNo INT
	--, @OrderBy int = 1                                              
	--, @SortDir int = 1                                              
	--, @PageIndex int = 0                                              
	--, @PageSize int = 10
	--, @SupplierName VARCHAR(200) = NULL
	, @SupplierNo INT
	, @PurchaseOrderNumber INT = NULL
	, @DebitDateFrom DATETIME = NULL
	, @DebitDateTo DATETIME = NULL
AS
BEGIN
	IF (NOT @DebitDateFrom IS NULL)                                               
        SET @DebitDateFrom = dbo.ufn_get_start_of_day_for_date(@DebitDateFrom)                                              
                                                      
    IF (NOT @DebitDateTo IS NULL)                                               
        SET @DebitDateTo = dbo.ufn_get_end_of_day_for_date(@DebitDateTo)

	SELECT 
		v.DebitId,
		v.DebitNumber,
		CAST((DebitValue + ISNULL(Freight,0) + (DebitValue + ISNULL(Freight,0))*(TaxRate/100)) AS FLOAT) as DebitValue,
		v.CurrencyCode,
		v.DebitDate,
		v.PurchaseOrderNumber,
		v.InternalPurchaseOrderNumber,
		v.SupplierRMANumber,
		v.Notes
	FROM dbo.vwDebit v
	WHERE v.ClientNo = @ClientNo
		AND v.CompanyNo = @SupplierNo
		AND (@DebitDateFrom IS NULL OR v.DebitDate >= @DebitDateFrom)
		AND (@DebitDateTo IS NULL OR v.DebitDate <= @DebitDateTo)
		AND (@PurchaseOrderNumber IS NULL OR v.PurchaseOrderNumber = @PurchaseOrderNumber)
	ORDER BY v.DebitId DESC
END
GO
/*========== Test script ==========
EXEC usp_ItemSearch_SupplierInvoice_DebitNotes
	@ClientNo = 101,
	@SupplierNo = 28887,
	@PurchaseOrderNumber = null,
	@DebitDateFrom = null,
	@DebitDateTo = null
*/