///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//
// RP 06.10.2009:
// - Changes due to changes in base class
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");

Rebound.GlobalTrader.Site.Controls.DropDowns.IPOList = function(element) { 
	Rebound.GlobalTrader.Site.Controls.DropDowns.IPOList.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.DropDowns.IPOList.prototype = {
    get_intDocNo: function () { return this._intDocNo; }, set_intDocNo: function (v) { if (this._intDocNo !== v) this._intDocNo = v; },

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.DropDowns.IPOList.callBaseMethod(this, "initialize");
		this.addSetupDataCall(Function.createDelegate(this, this.setupDataCall));
		this.addGetDataOK(Function.createDelegate(this, this.dataCallOK));
	},
	
	dispose: function() { 
	    if (this.isDisposed) return;
	    this._intDocNo = null;
		Rebound.GlobalTrader.Site.Controls.DropDowns.IPOList.callBaseMethod(this, "dispose");
	},
	
	setupDataCall: function() { 
		this._objData.set_PathToData("controls/DropDowns/IPOList");
		this._objData.set_DataObject("IPOList");
		this._objData.set_DataAction("GetData");
		this._objData.addParameter("intDocNo", this._intDocNo);
	},
	
	dataCallOK: function() { 
		var result = this._objData._result;
		if (result.IPOList) {
		    for (var i = 0; i < result.IPOList.length; i++) {
		        this.addOption(result.IPOList[i].Name, result.IPOList[i].ID, result.IPOList[i].Code);
			}
		}
	}
	
};

Rebound.GlobalTrader.Site.Controls.DropDowns.IPOList.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.IPOList", Rebound.GlobalTrader.Site.Controls.DropDowns.Base);
