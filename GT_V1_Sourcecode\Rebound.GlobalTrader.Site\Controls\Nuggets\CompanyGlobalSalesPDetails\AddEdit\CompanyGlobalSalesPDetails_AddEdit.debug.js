///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.CompanyGlobalSalesPDetails_AddEdit = function (element) {
    Rebound.GlobalTrader.Site.Controls.Forms.CompanyGlobalSalesPDetails_AddEdit.initializeBase(this, [element]);
    this._intSupplierID = -1;
    this._intManufacturerLinkID = -1;  
};

Rebound.GlobalTrader.Site.Controls.Forms.CompanyGlobalSalesPDetails_AddEdit.prototype = {

    get_intSupplierID: function () { return this._intSupplierID; }, set_intSupplierID: function (value) { if (this._intSupplierID !== value) this._intSupplierID = value; },
    get_intManufacturerLinkID: function () { return this._intManufacturerLinkID; }, set_intManufacturerLinkID: function (value) { if (this._intManufacturerLinkID !== value) this._intManufacturerLinkID = value; },
    get_autManufacturers: function () { return this._autManufacturers; }, set_autManufacturers: function (value) { if (this._autManufacturers !== value) this._autManufacturers = value; },
    get_strTitleEdit: function () { return this._strTitleEdit; }, set_strTitleEdit: function (value) { if (this._strTitleEdit !== value) this._strTitleEdit = value; },
    get_strTitleAdd: function () { return this._strTitleAdd; }, set_strTitleAdd: function (value) { if (this._strTitleAdd !== value) this._strTitleAdd = value; },

    initialize: function () {
        Rebound.GlobalTrader.Site.Controls.Forms.CompanyGlobalSalesPDetails_AddEdit.callBaseMethod(this, "initialize");
        this.addShown(Function.createDelegate(this, this.formShown));
        this.addModeChanged(Function.createDelegate(this, this.changeTitleOnMode));
    },

    dispose: function () {
        if (this.isDisposed) return;
        if (this._autManufacturers) this._autManufacturers.dispose();
        this._autManufacturers = null;
        this._intSupplierID = null;
        this._intManufacturerLinkID = null;
        this._strTitleEdit = null;
        this._strTitleAdd = null;
        Rebound.GlobalTrader.Site.Controls.Forms.CompanyGlobalSalesPDetails_AddEdit.callBaseMethod(this, "dispose");
    },

    formShown: function () {
        if (this._blnFirstTimeShown) {
            var fnSave = Function.createDelegate(this, this.validateDuplicate);
            $R_IBTN.addClick(this._ibtnSave, fnSave);
            $R_IBTN.addClick(this._ibtnSave_Footer, fnSave);
            var fnCancel = Function.createDelegate(this, this.cancelClicked);
            $R_IBTN.addClick(this._ibtnCancel, fnCancel);
            $R_IBTN.addClick(this._ibtnCancel_Footer, fnCancel);
        }
        this.showField("ctlManufacturer", this._mode == "ADD");
        this.showField("ctlManufacturerSelected", this._mode == "EDIT");
    },

    saveClicked: function () {
        //if (!this.validateForm()) return;
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/CompanyManufacturers");
        obj.set_DataObject("CompanyManufacturers");
        if (this._mode == "ADD") {
            obj.set_DataAction("SaveAddNew");
            obj.addParameter("SupplierNo", this._intSupplierID);
            obj.addParameter("ManufacturerNo", this._autManufacturers._varSelectedID);
            obj.addParameter("SupplierRating", this.getFieldValue("ctlRating"));
        } else {
            obj.set_DataAction("SaveEdit");
            obj.addParameter("ID", this._intManufacturerLinkID);
            obj.addParameter("SupplierRating", this.getFieldValue("ctlRating"));
        }
        obj.addDataOK(Function.createDelegate(this, this.saveEditComplete));
        obj.addError(Function.createDelegate(this, this.saveEditError));
        obj.addTimeout(Function.createDelegate(this, this.saveEditError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
        this.onSave();
    },

    saveEditError: function (args) {
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    saveEditComplete: function (args) {
        if ((this._mode == "EDIT" && args._result.Result == true)
            || (this._mode == "ADD" && args._result.NewID > 0)) {
            this.onSaveComplete();
        } else {
            this._strErrorMessage = args._errorMessage;
            this.onSaveError();
        }
    },

    validateForm: function () {
        var blnOK = true;
        if (this._mode == "ADD") {
            if (!this._autManufacturers._varSelectedID > 0) {
                this.setFieldInError("ctlManufacturer", true, $R_RES.RequiredFieldMissingMessage);
                $("#ctl00_cphMain_ctlManufacturersSupplied_ctlDB_ctl14_frmAddEdit_ctlDB_ctlManufacturer_pnlMessages").show();
                blnOK = false;
            }
        }
        if (!blnOK) this.showError(true);
        return blnOK;
    },

    cancelClicked: function () {
        this.onCancel();
    },


    changeTitleOnMode: function () {
        switch (this._mode) {
            case "ADD": this.changeTitle(this._strTitleAdd); break;
            case "EDIT": this.changeTitle(this._strTitleEdit); break;
        }
    },

    validateDuplicate: function () {
        if (!this.validateForm()) return;
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/CompanyManufacturers");
        obj.set_DataObject("CompanyManufacturers");
        obj.set_DataAction("validateDuplicate");
        obj.addParameter("ManufacturerNo", this._autManufacturers._varSelectedID);
        obj.addParameter("ID", this._intSupplierID);
        obj.addDataOK(Function.createDelegate(this, this.validateDuplicateComplete));
        obj.addError(Function.createDelegate(this, this.validateDuplicateError));
        obj.addTimeout(Function.createDelegate(this, this.validateDuplicateError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },
    validateDuplicateError: function (args) {
        return false;
    },

    validateDuplicateComplete: function (args) {
        if (this._mode == "ADD") {
            if (args._result.Result) {
                this.saveClicked();
            } else {
                this._strErrorMessage = "Manufacturers already exists for the company.";
                this.showError(true, this._strErrorMessage);
                $("#ctl00_cphMain_ctlManufacturersSupplied_ctlDB_ctl14_frmAddEdit_ctlDB_ctlManufacturer_pnlMessages").hide();
            }
        }
        else {
            this.saveClicked();
        }
        return args._result.Result;
    },

};

Rebound.GlobalTrader.Site.Controls.Forms.CompanyGlobalSalesPDetails_AddEdit.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CompanyGlobalSalesPDetails_AddEdit", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
