Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");Rebound.GlobalTrader.Site.Controls.Nuggets.ExportApprovalStatus=function(n){Rebound.GlobalTrader.Site.Controls.Nuggets.ExportApprovalStatus.initializeBase(this,[n]);this._intSalesOrderID=-1;this._intPurchaseOrderLineID=-1;this._intAllocationID=-1;this._blnPOClosed=!1;this._intLineDataCalls=0;this._blnDisableAllButtons=!1;this._blnRestrictedEdit=!1;this._enmPOStatus=0;this._aryUnpostedLineIDs=[];this._aryPostedLineIDs=[];this._blnLineLoaded=!1;this._ipoClientNo=0;this._blnClientPO=!1;this._InternalPurchaseOrderNumber=-1;this._PoLine=-1;this._Part="";this._Quantity=-1;this._arrPOLineIds=[];this._IsPOLineReceived=!1;this._intGlobalClientNo=-1;this._blnProdInactive=!1;this._reqSerialNo=!1;this._IsReleased=!1;this._IsAuthorised=!1;this._POLineSerialNo=[];this._PONumber=-1;this._IsPOHUB=!1;this.blnIsService=!1};Rebound.GlobalTrader.Site.Controls.Nuggets.ExportApprovalStatus.prototype={get_intSalesOrderID:function(){return this._intSalesOrderID},set_intSalesOrderID:function(n){this._intSalesOrderID!==n&&(this._intSalesOrderID=n)},get_intContactID:function(){return this._intContactID},set_intContactID:function(n){this._intContactID!==n&&(this._intContactID=n)},get_ibtnApproval:function(){return this._ibtnApproval},set_ibtnApproval:function(n){this._ibtnApproval!==n&&(this._ibtnApproval=n)},get_ibtnEditApproval:function(){return this._ibtnEditApproval},set_ibtnEditApproval:function(n){this._ibtnEditApproval!==n&&(this._ibtnEditApproval=n)},get_ibtnEditAllApproval:function(){return this._ibtnEditAllApproval},set_ibtnEditAllApproval:function(n){this._ibtnEditAllApproval!==n&&(this._ibtnEditAllApproval=n)},get_ibtnRequestApproval:function(){return this._ibtnRequestApproval},set_ibtnRequestApproval:function(n){this._ibtnRequestApproval!==n&&(this._ibtnRequestApproval=n)},get_ctlMultiSelectionCount:function(){return this._ctlMultiSelectionCount},set_ctlMultiSelectionCount:function(n){this._ctlMultiSelectionCount!==n&&(this._ctlMultiSelectionCount=n)},get_ibtnUnpostAll:function(){return this._ibtnUnpostAll},set_ibtnUnpostAll:function(n){this._ibtnUnpostAll!==n&&(this._ibtnUnpostAll=n)},get_ibtnDeallocate:function(){return this._ibtnDeallocate},set_ibtnDeallocate:function(n){this._ibtnDeallocate!==n&&(this._ibtnDeallocate=n)},get_ibtnAdd:function(){return this._ibtnAdd},set_ibtnAdd:function(n){this._ibtnAdd!==n&&(this._ibtnAdd=n)},get_ctlTabStrip:function(){return this._ctlTabStrip},set_ctlTabStrip:function(n){this._ctlTabStrip!==n&&(this._ctlTabStrip=n)},get_tblApproved:function(){return this._tblApproved},set_tblApproved:function(n){this._tblApproved!==n&&(this._tblApproved=n)},get_tblAwaiting:function(){return this._tblAwaiting},set_tblAwaiting:function(n){this._tblAwaiting!==n&&(this._tblAwaiting=n)},get_tblAll:function(){return this._tblAll},set_tblAll:function(n){this._tblAll!==n&&(this._tblAll=n)},get_hypPrev:function(){return this._hypPrev},set_hypPrev:function(n){this._hypPrev!==n&&(this._hypPrev=n)},get_hypNext:function(){return this._hypNext},set_hypNext:function(n){this._hypNext!==n&&(this._hypNext=n)},get_lblLineNumber:function(){return this._lblLineNumber},set_lblLineNumber:function(n){this._lblLineNumber!==n&&(this._lblLineNumber=n)},get_lblLineInactive:function(){return this._lblLineInactive},set_lblLineInactive:function(n){this._lblLineInactive!==n&&(this._lblLineInactive=n)},get_pnlLineDetail:function(){return this._pnlLineDetail},set_pnlLineDetail:function(n){this._pnlLineDetail!==n&&(this._pnlLineDetail=n)},get_pnlLoadingLineDetail:function(){return this._pnlLoadingLineDetail},set_pnlLoadingLineDetail:function(n){this._pnlLoadingLineDetail!==n&&(this._pnlLoadingLineDetail=n)},get_pnlLineDetailError:function(){return this._pnlLineDetailError},set_pnlLineDetailError:function(n){this._pnlLineDetailError!==n&&(this._pnlLineDetailError=n)},get_fldAllocations:function(){return this._fldAllocations},set_fldAllocations:function(n){this._fldAllocations!==n&&(this._fldAllocations=n)},get_tblAllocations:function(){return this._tblAllocations},set_tblAllocations:function(n){this._tblAllocations!==n&&(this._tblAllocations=n)},get_fldReceived:function(){return this._fldReceived},set_fldReceived:function(n){this._fldReceived!==n&&(this._fldReceived=n)},get_tblReceived:function(){return this._tblReceived},set_tblReceived:function(n){this._tblReceived!==n&&(this._tblReceived=n)},get_blnPOClosed:function(){return this._blnPOClosed},set_blnPOClosed:function(n){this._blnPOClosed!==n&&(this._blnPOClosed=n)},get_enmPOStatus:function(){return this._enmPOStatus},set_enmPOStatus:function(n){this._enmPOStatus!==n&&(this._enmPOStatus=n)},get_blnDisableAllButtons:function(){return this._blnDisableAllButtons},set_blnDisableAllButtons:function(n){this._blnDisableAllButtons!==n&&(this._blnDisableAllButtons=n)},get_intPurchaseOrderLineID:function(){return this._intPurchaseOrderLineID},set_intPurchaseOrderLineID:function(n){this._intPurchaseOrderLineID!==n&&(this._intPurchaseOrderLineID=n)},get_blnCanEditPriceWithoutUnpost:function(){return this._blnCanEditPriceWithoutUnpost},set_blnCanEditPriceWithoutUnpost:function(n){this._blnCanEditPriceWithoutUnpost!==n&&(this._blnCanEditPriceWithoutUnpost=n)},get_ibtnAddExpditeNote:function(){return this._ibtnAddExpditeNote},set_ibtnAddExpditeNote:function(n){this._ibtnAddExpditeNote!==n&&(this._ibtnAddExpditeNote=n)},get_ibtnEPR:function(){return this._ibtnEPR},set_ibtnEPR:function(n){this._ibtnEPR!==n&&(this._ibtnEPR=n)},get_pnlEPRTootTip:function(){return this._pnlEPRTootTip},set_pnlEPRTootTip:function(n){this._pnlEPRTootTip!==n&&(this._pnlEPRTootTip=n)},get_hypNewEPR:function(){return this._hypNewEPR},set_hypNewEPR:function(n){this._hypNewEPR!==n&&(this._hypNewEPR=n)},get_pnlEPR:function(){return this._pnlEPR},set_pnlEPR:function(n){this._pnlEPR!==n&&(this._pnlEPR=n)},get_ibtnRelease:function(){return this._ibtnRelease},set_ibtnRelease:function(n){this._ibtnRelease!==n&&(this._ibtnRelease=n)},addPotentialStatusChange:function(n){this.get_events().addHandler("PotentialStatusChange",n)},removePotentialStatusChange:function(n){this.get_events().removeHandler("PotentialStatusChange",n)},onPotentialStatusChange:function(){var n=this.get_events().getHandler("PotentialStatusChange");n&&n(this,Sys.EventArgs.Empty)},initialize:function(){$("#AllocationErrorMsg").hide();$("#dvtxt").html("");Rebound.GlobalTrader.Site.Controls.Nuggets.ExportApprovalStatus.callBaseMethod(this,"initialize");this._strDataPath="controls/Nuggets/ExportApprovalStatus";this._strDataObject="ExportApprovalStatus";$("#dvERAIMessage").hide();this._ctlTabStrip.addTabIndexChanged(Function.createDelegate(this,this.tabChanged));this.addRefreshEvent(Function.createDelegate(this,this.tabChanged));this._tblApproved.addSelectedIndexChanged(Function.createDelegate(this,this.tbl_SelectedIndexChangedForTradeRef));this._tblAll.addMultipleSelectionChanged(Function.createDelegate(this,this.tbl_SelectedIndexChangedForSupplierApproval));this._ctlMultiSelectionCount.registerTable(this._tblAll);$addHandler(this._hypPrev,"click",Function.createDelegate(this,this.prevLine));$addHandler(this._hypNext,"click",Function.createDelegate(this,this.nextLine));this._fldAllocations.addRefresh(Function.createDelegate(this,this.onRefreshAllocations));this._fldAllocations.addShown(Function.createDelegate(this,this.onShownAllocations));this._fldReceived.addRefresh(Function.createDelegate(this,this.onRefreshReceived));this._fldReceived.addShown(Function.createDelegate(this,this.onShownReceived));this._ibtnEditApproval&&($R_IBTN.addClick(this._ibtnEditApproval,Function.createDelegate(this,this.showEditForm)),this._frmEdit=$find(this._aryFormIDs[2]),this._frmEdit.addCancel(Function.createDelegate(this,this.hideEditForm)),this._frmEdit.addSaveComplete(Function.createDelegate(this,this.saveEditComplete)),this._frmEdit.addNotConfirmed(Function.createDelegate(this,this.hideEditForm)));this._ibtnEditAllApproval&&($R_IBTN.addClick(this._ibtnEditAllApproval,Function.createDelegate(this,this.showEditAllForm)),this._frmEditAll=$find(this._aryFormIDs[3]),this._frmEditAll.addCancel(Function.createDelegate(this,this.hideEditAllForm)),this._frmEditAll.addSaveComplete(Function.createDelegate(this,this.saveEditAllComplete)),this._frmEditAll.addNotConfirmed(Function.createDelegate(this,this.hideEditAllForm)));this._ibtnApproval&&($R_IBTN.addClick(this._ibtnApproval,Function.createDelegate(this,this.showApprovalForm)),this._frmApproval=$find(this._aryFormIDs[1]),this._frmApproval.addCancel(Function.createDelegate(this,this.hideApprovalForm)),this._frmApproval.addSaveComplete(Function.createDelegate(this,this.saveApprovalComplete)),this._frmApproval.addNotConfirmed(Function.createDelegate(this,this.hideApprovalForm)));this._ibtnAdd&&($R_IBTN.addClick(this._ibtnAdd,Function.createDelegate(this,this.showAddForm)),this._frmAdd=$find(this._aryFormIDs[2]),this._frmAdd.addCancel(Function.createDelegate(this,this.hideAddForm)),this._frmAdd.addSaveComplete(Function.createDelegate(this,this.saveAddComplete)));this._ibtnRequestApproval&&(this._ibtnRequestApproval&&$R_IBTN.addClick(this._ibtnRequestApproval,Function.createDelegate(this,this.showRequestApprovalForm)),this._frmRequestApproval=$find(this._aryFormIDs[0]),this._frmRequestApproval.addCancel(Function.createDelegate(this,this.hideRequestApprovalForm)),this._frmRequestApproval.addSaveComplete(Function.createDelegate(this,this.saveRequestApprovalComplete)),this._frmRequestApproval.addNotConfirmed(Function.createDelegate(this,this.hideRequestApprovalForm)));this.tabChanged()},dispose:function(){this.isDisposed||(this._ibtnEditApproval&&$R_IBTN.clearHandlers(this._ibtnEditApproval),this._ibtnEditAllApproval&&$R_IBTN.clearHandlers(this._ibtnEditAllApproval),this._ibtnApproval&&$R_IBTN.clearHandlers(this._ibtnApproval),this._ctlMultiSelectionCount&&this._ctlMultiSelectionCount.dispose(),this._ibtnAdd&&$R_IBTN.clearHandlers(this._ibtnAdd),this._ibtnDeallocate&&$R_IBTN.clearHandlers(this._ibtnDeallocate),this._ibtnRequestApproval&&$R_IBTN.clearHandlers(this._ibtnRequestApproval),this._ibtnUnpostAll&&$R_IBTN.clearHandlers(this._ibtnUnpostAll),this._tblApproved&&this._tblApproved.dispose(),this._tblAwaiting&&this._tblAwaiting.dispose(),this._tblAll&&this._tblAll.dispose(),this._fldAllocations&&this._fldAllocations.dispose(),this._tblAllocations&&this._tblAllocations.dispose(),this._fldReceived&&this._fldReceived.dispose(),this._tblReceived&&this._tblReceived.dispose(),this._ctlTabStrip&&this._ctlTabStrip.dispose(),this._frmDeallocate&&this._frmDeallocate.dispose(),this._frmClose&&this._frmClose.dispose(),this._frmDelete&&this._frmDelete.dispose(),this._frmAdd&&this._frmAdd.dispose(),this._frmApproval&&this._frmApproval.dispose(),this._frmEdit&&this._frmEdit.dispose(),this._frmDeallocate=null,this._frmClose=null,this._frmDelete=null,this._frmAdd=null,this._frmApproval=null,this._frmEdit=null,this._intSalesOrderID=null,this._intContactID=null,this._ibtnEditApproval=null,this._ibtnEditAllApproval=null,this._ibtnApproval=null,this._ibtnRequestApproval=null,this._ctlMultiSelectionCount=null,this._ibtnUnpostAll=null,this._ibtnDeallocate=null,this._ibtnAdd=null,this._ctlTabStrip=null,this._tblApproved=null,this._tblAwaiting=null,this._tblAll=null,this._hypPrev=null,this._hypNext=null,this._lblLineNumber=null,this._lblLineInactive=null,this._pnlLineDetail=null,this._pnlLoadingLineDetail=null,this._pnlLineDetailError=null,this._fldAllocations=null,this._tblAllocations=null,this._fldReceived=null,this._tblReceived=null,this._blnPOClosed=null,this._enmPOStatus=null,this._blnDisableAllButtons=null,this._intPurchaseOrderLineID=null,this._blnClientPO=null,this._ibtnAddExpditeNote=null,this._arrPOLineIds=null,this._intGlobalClientNo=null,this._blnProdInactive=null,this._reqSerialNo=!1,this._ibtnEPR=null,this._pnlEPRTootTip=null,this._hypNewEPR=null,this._pnlEPR=null,this.ibtnRelease=null,this._PONumber=null,this._IsPOHUB=null,Rebound.GlobalTrader.Site.Controls.Nuggets.ExportApprovalStatus.callBaseMethod(this,"dispose"))},tabChanged:function(){Array.clear(this._arrPOLineIds);this.clearPolineFormOnEdit();this.getTabData()},getTabData:function(){$("#AllocationErrorMsg").hide();$("#dvtxt").html("");this.enableEditButtons(!1);$R_FN.showElement(this._pnlLineDetail,!1);switch(this._ctlTabStrip._selectedTabIndex){case 0:this.getTabData_All();break;case 1:this.getTabData_Approved();break;case 2:this.getTabData_Awaiting()}},getTabData_Approved:function(){this.getData_Start();var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strDataPath);n.set_DataObject(this._strDataObject);n.set_DataAction("GetData_Approved");n.addParameter("id",this._intSalesOrderID);n.addDataOK(Function.createDelegate(this,this.getTabDataOK_Approved));n.addError(Function.createDelegate(this,this.getTabDataError));n.addTimeout(Function.createDelegate(this,this.getTabDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getTabDataOK_Approved:function(n){var s,u,i,o;Array.clear(this._arrPOLineIds);var c=n._result,f=n._result,r=this.getCurrentTable();if(r.enable(!0),r.clearTable(),s="app_themes/original/images/IconButton/pdficon.jpg",this._intPurchaseOrderLineID>0&&(this._intLineID=this._intPurchaseOrderLineID),this._ibtnAddExpditeNote&&$R_IBTN.enableButton(this._ibtnAddExpditeNote,!1),Array.clear(this._aryUnpostedLineIDs),Array.clear(this._aryPostedLineIDs),Array.clear(this._arrPOLineIds),f.Lines)for(u=0;u<f.Lines.length;u++){var t=f.Lines[u],h=[t.LineNo,$R_FN.writeDoubleCellValue($R_FN.writePartNo(t.Part,t.ROHS),$R_FN.setCleanTextValue(t.CustomerPart)),this.blnIsService?$R_FN.writeDoubleCellValue("",""):$R_FN.writeDoubleCellValue($RGT_nubButton_Manufacturer(t.MfrNo,t.Mfr,t.MfrAdvisoryNotes),$R_FN.setCleanTextValue(t.DC)),$R_FN.setCleanTextValue(t.ExportApprovalStatus),$R_FN.setCleanTextValue(t.OGELLicenseRequired),$R_FN.setCleanTextValue(t.EUUFormRequired),$R_FN.setCleanTextValue(t.Dated),$R_FN.setCleanTextValue(t.By),$R_FN.setCleanTextValue(t.Comment)],e={};t.IsReceived&&(this._IsPOLineReceived=t.IsReceived);i="unposted";t.IsPosted&&(i="posted");e.IsPartReceived&&(i="partReceived");e.IsReceived&&(i="received");t.Inactive&&(i="inactive");r.addRow(h,t.ExportApprovalId,t.ExportApprovalId==this._intLineID,e,i);!t.Inactive&&!t.IsAllocated&&!t.IsPosted&&t.QuantityReceived<1&&Array.add(this._aryUnpostedLineIDs,t.LineID);!t.Inactive&&!t.IsAllocated&&t.IsPosted&&t.QuantityReceived<1&&Array.add(this._aryPostedLineIDs,t.LineID);o=this.getCheckBox(u,r);o=null;t=null}this._ibtnUnpostAll&&$R_IBTN.showButton(this._ibtnUnpostAll,this._aryPostedLineIDs.length>0);r.resizeColumns();this._intPurchaseOrderLineID>0&&(this._intPurchaseOrderLineID=-1);this.getDataOK_End()},getTabData_Awaiting:function(){this.getData_Start();var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strDataPath);n.set_DataObject(this._strDataObject);n.set_DataAction("GetData_Awaiting");n.addParameter("id",this._intSalesOrderID);n.addDataOK(Function.createDelegate(this,this.getTabDataOK_Awaiting));n.addError(Function.createDelegate(this,this.getTabDataError));n.addTimeout(Function.createDelegate(this,this.getTabDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getTabDataOK_Awaiting:function(n){var u,i,o;Array.clear(this._arrPOLineIds);var h=n._result,f=n._result,r=this.getCurrentTable();if(r.enable(!0),r.clearTable(),this._intPurchaseOrderLineID>0&&(this._intLineID=this._intPurchaseOrderLineID),this._ibtnAddExpditeNote&&$R_IBTN.enableButton(this._ibtnAddExpditeNote,!1),Array.clear(this._aryUnpostedLineIDs),Array.clear(this._aryPostedLineIDs),Array.clear(this._arrPOLineIds),f.Lines)for(u=0;u<f.Lines.length;u++){var t=f.Lines[u],s=[t.LineNo,$R_FN.writeDoubleCellValue($R_FN.writePartNo(t.Part,t.ROHS),$R_FN.setCleanTextValue(t.CustomerPart)),this.blnIsService?$R_FN.writeDoubleCellValue("",""):$R_FN.writeDoubleCellValue($RGT_nubButton_Manufacturer(t.MfrNo,t.Mfr,t.MfrAdvisoryNotes),$R_FN.setCleanTextValue(t.DC)),$R_FN.setCleanTextValue(t.ExportApprovalStatus),$R_FN.setCleanTextValue(t.OGELLicenseRequired),$R_FN.setCleanTextValue(t.EUUFormRequired),$R_FN.setCleanTextValue(t.Dated),$R_FN.setCleanTextValue(t.By),$R_FN.setCleanTextValue(t.Comment)],e={};t.IsReceived&&(this._IsPOLineReceived=t.IsReceived);i="unposted";t.IsPosted&&(i="posted");e.IsPartReceived&&(i="partReceived");e.IsReceived&&(i="received");t.Inactive&&(i="inactive");r.addRow(s,t.ExportApprovalId,t.ExportApprovalId==this._intLineID,e,i);!t.Inactive&&!t.IsAllocated&&!t.IsPosted&&t.QuantityReceived<1&&Array.add(this._aryUnpostedLineIDs,t.LineID);!t.Inactive&&!t.IsAllocated&&t.IsPosted&&t.QuantityReceived<1&&Array.add(this._aryPostedLineIDs,t.LineID);o=this.getCheckBox(u,r);o=null;t=null}this._ibtnUnpostAll&&$R_IBTN.showButton(this._ibtnUnpostAll,this._aryPostedLineIDs.length>0);r.resizeColumns();this._intPurchaseOrderLineID>0&&(this._intPurchaseOrderLineID=-1);this.getDataOK_End()},getTabData_All:function(){this._ctlMultiSelectionCount.clearAll();this.getData_Start();var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strDataPath);n.set_DataObject(this._strDataObject);n.set_DataAction("GetData_All");n.addParameter("id",this._intSalesOrderID);n.addDataOK(Function.createDelegate(this,this.getTabDataOK_All));n.addError(Function.createDelegate(this,this.getTabDataError));n.addTimeout(Function.createDelegate(this,this.getTabDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getTabDataOK_All:function(n){var f,i,r,u,o;if(Array.clear(this._arrPOLineIds),f=n._result,i=this.getCurrentTable(),i.enable(!0),i.clearTable(),this._intPurchaseOrderLineID>0&&(this._intLineID=this._intPurchaseOrderLineID),this._ibtnAddExpditeNote&&$R_IBTN.enableButton(this._ibtnAddExpditeNote,!1),Array.clear(this._aryUnpostedLineIDs),Array.clear(this._aryPostedLineIDs),Array.clear(this._arrPOLineIds),f.Lines)for(r=0;r<f.Lines.length;r++){var t=f.Lines[r],s=[$R_FN.writeDoubleCellValue(t.LineNo,t.IsExportDetailsFilled==!0?"<img src='../App_Themes/Original/images/StarRating/filled.png' title='Export Approval Data filled' id='imgQuery"+r+"'/>":""),$R_FN.writeDoubleCellValue($R_FN.writePartNo(t.Part,t.ROHS),$R_FN.setCleanTextValue(t.CustomerPart)),this.blnIsService?$R_FN.writeDoubleCellValue("",""):$R_FN.writeDoubleCellValue($RGT_nubButton_Manufacturer(t.MfrNo,t.Mfr,t.MfrAdvisoryNotes),$R_FN.setCleanTextValue(t.DC)),$R_FN.setCleanTextValue(t.ExportApprovalStatus),$R_FN.setCleanTextValue(t.OGELLicenseRequired),$R_FN.setCleanTextValue(t.EUUFormRequired),$R_FN.setCleanTextValue(t.Dated),$R_FN.setCleanTextValue(t.By),$R_FN.setCleanTextValue(t.Comment)],e={ExportApprovalStatusId:t.ExportApprovalStatusId,IsAllocationDone:t.IsAllocationDone,IsExportDetailsFilled:t.IsExportDetailsFilled,OgelRequiredOnSO:t.OgelRequiredOnSO,IsExportControlHasPDF:t.IsExportControlHasPDF};t.IsReceived&&(this._IsPOLineReceived=t.IsReceived);u="unposted";t.IsPosted&&(u="posted");e.IsPartReceived&&(u="partReceived");e.IsReceived&&(u="received");t.Inactive&&(u="inactive");i.addRow(s,t.ExportApprovalId,t.ExportApprovalId==this._intLineID,e,u);!t.Inactive&&!t.IsAllocated&&!t.IsPosted&&t.QuantityReceived<1&&Array.add(this._aryUnpostedLineIDs,t.LineID);!t.Inactive&&!t.IsAllocated&&t.IsPosted&&t.QuantityReceived<1&&Array.add(this._aryPostedLineIDs,t.LineID);o=this.getCheckBox(r,i);o=null;t=null}this._ibtnUnpostAll&&$R_IBTN.showButton(this._ibtnUnpostAll,this._aryPostedLineIDs.length>0);i.resizeColumns();this._intPurchaseOrderLineID>0&&(this._intPurchaseOrderLineID=-1);this.getDataOK_End()},getTabDataError:function(n){this.showError(!0,n.get_ErrorMessage())},getCurrentTable:function(){var n;switch(this._ctlTabStrip._selectedTabIndex){case 0:n=this._tblAll;break;case 1:n=this._tblApproved;break;case 2:n=this._tblAwaiting}return n},tbl_SelectedIndexChangedForTradeRef:function(){var n=this.getCurrentTable();this._intLineID=n._varSelectedValue},tbl_SelectedIndexChangedForSupplierApproval:function(){var o=this.getCurrentTable(),i,r,t;if(this._tblAll._aryCurrentValues.length>0)if(this._tblAll.getSelectedExtraData(this._tblAll._arySelectedIndexes[0]).OgelRequiredOnSO==!0){if(this._intLineID=this._tblAll._aryCurrentValues[0],this._tblAll._aryCurrentValues.length==1){this._ibtnEditApproval&&$R_IBTN.enableButton(this._ibtnEditApproval,!0);var n=this._tblAll.getSelectedExtraData(this._tblAll._arySelectedIndexes[0]).ExportApprovalStatusId,u=this._tblAll.getSelectedExtraData(this._tblAll._arySelectedIndexes[0]).IsAllocationDone,f=this._tblAll.getSelectedExtraData(this._tblAll._arySelectedIndexes[0]).IsExportDetailsFilled,e=this._tblAll.getSelectedExtraData(this._tblAll._arySelectedIndexes[0]).IsExportControlHasPDF;(n==3||n==4||n==5)&&u==!0?(f==!0?(this._ibtnApproval&&$R_IBTN.enableButton(this._ibtnApproval,!0),this._ibtnRequestApproval&&$R_IBTN.enableButton(this._ibtnRequestApproval,!0)):(this._ibtnApproval&&$R_IBTN.enableButton(this._ibtnApproval,!1),this._ibtnRequestApproval&&$R_IBTN.enableButton(this._ibtnRequestApproval,!1)),e?(this._ibtnRequestApproval&&$R_IBTN.enableButton(this._ibtnRequestApproval,!0),$("#dvRequestApprovalDisabledReason").hide(),$("#dvRequestApprovalDisabledReason").html("")):(this._ibtnRequestApproval&&$R_IBTN.enableButton(this._ibtnRequestApproval,!1),$("#dvRequestApprovalDisabledReason").show(),$("#dvRequestApprovalDisabledReason").html("<img height='15px' width='15px' src='../../../App_Themes/Original/images/hazardous/ihspartstatuspng.png'>"),$("#dvRequestApprovalDisabledReason").append("<span class='tooltiptext'>Upload Export Control Document under PDF document<\/span>")),this._ibtnEditApproval&&$R_IBTN.updateText(this._ibtnEditApproval,"Edit Export Approval")):(this._ibtnApproval&&$R_IBTN.enableButton(this._ibtnApproval,!1),this._ibtnRequestApproval&&$R_IBTN.enableButton(this._ibtnRequestApproval,!1),n==7||n==3||n==4||n==5?(this._ibtnEditApproval&&$R_IBTN.enableButton(this._ibtnEditApproval,!0),this._ibtnEditApproval&&$R_IBTN.updateText(this._ibtnEditApproval,"Edit Export Approval")):(this._ibtnEditApproval&&$R_IBTN.updateText(this._ibtnEditApproval,"View Export Approval"),this._ibtnEditApproval&&$R_IBTN.enableButton(this._ibtnEditApproval,!0)));u==!1&&n!=7?($("#AllocationErrorMsg").show(),$("#dvtxt").html("Please do allocation for selected SO line before OGEL approval.")):f==!1?($("#AllocationErrorMsg").show(),$("#dvtxt").html('Please fill in required fields using "Edit Export Approval" screen before requesting approval.')):($("#AllocationErrorMsg").hide(),$("#dvtxt").html(""))}else this._ibtnEditApproval&&$R_IBTN.enableButton(this._ibtnEditApproval,!1),this._ibtnApproval&&$R_IBTN.enableButton(this._ibtnApproval,!1),this._ibtnRequestApproval&&$R_IBTN.enableButton(this._ibtnRequestApproval,!1);if(this._tblAll._aryCurrentValues.length>1){for(i=!1,r=0;r<this._tblAll._aryCurrentValues.length;r++)if(t=this._tblAll.getSelectedExtraData(this._tblAll._arySelectedIndexes[r]).ExportApprovalStatusId,t==1||t==2||t==6||t==8){i=!0;break}else i=!1;i==!0?this._ibtnEditAllApproval&&$R_IBTN.enableButton(this._ibtnEditAllApproval,!1):this._ibtnEditAllApproval&&$R_IBTN.enableButton(this._ibtnEditAllApproval,!0)}else this._ibtnEditAllApproval&&$R_IBTN.enableButton(this._ibtnEditAllApproval,!1)}else this._ibtnEditAllApproval&&$R_IBTN.enableButton(this._ibtnEditAllApproval,!1),this._ibtnEditApproval&&$R_IBTN.enableButton(this._ibtnEditApproval,!1),this._ibtnApproval&&$R_IBTN.enableButton(this._ibtnApproval,!1),this._ibtnRequestApproval&&$R_IBTN.enableButton(this._ibtnRequestApproval,!1)},enableEditButtons:function(n){var i,r,t,u;if(this._blnDisableAllButtons&&(n=!1),n){if(i=!0,this._IsAuthorised&&(i=!1),this._IsReleased&&(i=!0),r=this.getCurrentTable(),!r)return;if(t=r.getSelectedExtraData(),!t)return;u=!1;u=!t.IsReceived;t=null}},getCurrentTable:function(){var n;switch(this._ctlTabStrip._selectedTabIndex){case 0:n=this._tblAll;break;case 1:n=this._tblApproved;break;case 2:n=this._tblAwaiting}return n},prevLine:function(){var n=this.getCurrentTable(),t=n._intSelectedIndex-1;t<0||n.selectRow(t,!0)},nextLine:function(){var n=this.getCurrentTable(),t=n._intSelectedIndex+1;t>=this._intLineCount||n.selectRow(t,!0)},getLineData:function(){var t=this.getCurrentTable(),n;t.enable(!1);this._intLineDataCalls+=1;this._blnLineLoaded=!1;this.enableEditButtons(!1);this.showLoading(!0);$R_FN.showElement(this._pnlLoadingLineDetail,!0);$R_FN.showElement(this._pnlLineDetailError,!1);$R_FN.showElement(this._pnlLineDetail,!1);n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strDataPath);n.set_DataObject(this._strDataObject);n.set_DataAction("GetData");n.addParameter("id",t._varSelectedValue);n.addDataOK(Function.createDelegate(this,this.getLineDataOK));n.addError(Function.createDelegate(this,this.getLineDataError));n.addTimeout(Function.createDelegate(this,this.getLineDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getLineDataError:function(n){this.finishLineDataCall();$R_FN.showElement(this._pnlLoadingLineDetail,!1);$R_FN.showElement(this._pnlLineDetailError,!0);$R_FN.setInnerHTML(this._pnlLineDetailError,n.get_ErrorMessage())},getLineDataOK:function(n){var t,u,r,f,i;if(this.finishLineDataCall(),$R_FN.showElement(this._pnlLineDetailError,!1),t=n._result,this.setFieldValue("ctlIPOBom",$RGT_nubButton_BOM(t.BomNo,t.BomName)),this.setFieldValue("ctlRepeatOrdr",t.RepeatOrder),this.setFieldValue("ctlWeblinkEvidence",t.QuantityOrdered),this.setFieldValue("ctlQuantityAllocated",t.QuantityAllocated),this.setFieldValue("hidPartNo",t.Part),this.setFieldValue("ctlROHS",$R_FN.writeROHS(t.ROHS)),this.setFieldValue("hidROHS",t.ROHS),this.setFieldValue("ctlTradeRefOne",t.Price),this.setFieldValue("hidPrice",t.PriceVal),this.setFieldValue("hidCurrencyCode",t.CurrencyCode),this.setFieldValue("hidShipInCost",t.ShipInCostVal),this.setFieldValue("ctlSupplierPart",$R_FN.setCleanTextValue(t.SupplierPart)),this.setFieldValue("hidManufacturer",t.Mfr),this.setFieldValue("hidManufacturerNo",t.MfrNo),this.setFieldValue("ctlTradeRefTwo",$R_FN.setCleanTextValue(t.DateCode)),this.setFieldValue("ctlProductDis",$R_FN.showHazardous(t.Product,t.IsProdHaz)),this.setFieldValue("hidProductNo",t.ProductNo),this.setFieldValue("hidPackageNo",t.PackageNo),this.setFieldValue("ctlPrdDutyCodeRate",t.DutyCodeAndRate),this.setFieldValue("hidIPOClientNo",t.IPOClientNo),this.setFieldValue("ctlReqSeriaNo",t.ReqSerialNo),this.setFieldValue("ctlSupplierWarranty",t.SupplierWarranty!="0"?t.SupplierWarranty+" days":""),this._blnClientPO=t.IsClientPO,this._blnProdInactive=t.ProdInactive,this._reqSerialNo=t.ReqSerialNo,$R_FN.showElement(this._pnlLineDetail,!0),$R_FN.showElement(this._pnlLoadingLineDetail,!1),i=this.getCurrentTable(),$R_FN.setInnerHTML(this._lblLineNumber,String.format($R_RES.LineXOfY,i._intSelectedIndex+1,this._intLineCount)),$R_FN.setInnerHTML(this._lblLineInactive,String.format(t.Inactive?"***Inactive***":"")),this.setFieldValue("hidProductHazar",t.IsProdHaz),this.setFieldValue("hidPrintHaza",t.IsPrintHaz),this.setFieldValue("ctlCountryOfOrigin",t.CountryOfOrigin),this.setFieldValue("ctlLifeCycleStage",t.LifeCycleStage),this.setFieldValue("ctlHTSCode",t.HTSCode),this.setFieldValue("ctlECCNCode",t.ECCNCode),this.setFieldValue("ctlPackagingSize",t.PackagingSize),this.setFieldValue("ctlDescriptions",t.Descriptions),this.setFieldValue("ctlIHSProduct",t.IHSProduct),this._IsPOHub=t.IsPOHub,this._IsPOHub==!0?(this.showField("ctlAveragePrice",!1),this.setFieldValue("ctlAveragePrice",t.AveragePrice)):this.showField("ctlAveragePrice",!1),this._blnLineLoaded=!0,this._IsReleased=t.IsReleased,this._IsAuthorised=t.IsAuthorised,u="",t.EPRIds)for(r=0;r<t.EPRIds.length;r++)f=t.EPRIds[r],u+="<a href='javascript:void(0)'; onclick='$RGT_openEPRWindow("+this._intPurchaseOrderID+","+f.EPRId+");'>"+this._PONumber+"-"+f.EPRId+"<\/a>&nbsp;&nbsp;";this.setFieldValue("ctlEPR",u);i=this.getCurrentTable();i.enable(!0);this.enableEditButtons(!0)},getLineAllocations:function(){this._intLineDataCalls+=1;this.showLoading(!0);this._fldAllocations.showLoading(!0);this._tblAllocations.clearSelection();this.enableDeallocateButton(!1);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strDataPath);n.set_DataObject(this._strDataObject);n.set_DataAction("GetLines_Allocated");n.addParameter("id",this._intLineID);n.addDataOK(Function.createDelegate(this,this.getLineAllocationsOK));n.addError(Function.createDelegate(this,this.getLineAllocationsError));n.addTimeout(Function.createDelegate(this,this.getLineAllocationsError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getLineAllocationsError:function(n){this.finishLineDataCall();this._fldAllocations.showError(!0,n.get_ErrorMessage())},getLineAllocationsOK:function(n){var i,r,u,t,o,f,e;if(this.finishLineDataCall(),i=n._result,r="",this._fldAllocations.showContent(!0),this._fldAllocations.resetCount(),this._tblAllocations.clearTable(),this._fldAllocations.updateCount(i.Count),i.Lines)for(u=0;u<i.Lines.length;u++)r="",t=i.Lines[u],o=$R_FN.writePartNo(t.Part,t.ROHS),r=t.ShipASAP==!0?t.DatePromised+" (or ASAP)":t.DatePromised,f=[$R_FN.writeDoubleCellValue(t.IsPoHub==!0?$R_FN.setCleanTextValue(t.Part):$RGT_nubButton_Stock(t.StockNo,t.Part,t.ROHS),$R_FN.setCleanTextValue(t.CustomerPart)),$R_FN.writeDoubleCellValue(t.IsPoHub==!0?$R_FN.setCleanTextValue(t.Customer):$RGT_nubButton_Company(t.CustomerNo,t.Customer),$R_FN.setCleanTextValue(t.CustomerPO)),$R_FN.writeDoubleCellValue($R_FN.showSerialNumber(t.IsPoHub==!0?$R_FN.setCleanTextValue(t.SalesOrderNumber):$RGT_nubButton_POSO(t.SalesOrderNo,t.SalesOrderNumber,t.SalesOrderLineNo),t.LineNo),r),$R_FN.writeDoubleCellValue($RGT_nubButton_SRMA(t.SRMANo,t.SRMANumber),t.ReturnDate),$R_FN.writeDoubleCellValue(t.QuantityAllocated,t.Price),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(t.DateCode),t.DeliveryDate),$R_FN.writeDoubleCellValue(t.Margin,t.MarginValue)],e={shipped:t.IsShipped?!0:!1},this._tblAllocations.addRow(f,t.ID,!1,e),t=null,f=null;this._tblAllocations.resizeColumns()},onShownAllocations:function(){this._tblAllocations.resizeColumns()},onRefreshAllocations:function(){this.getLineAllocations()},getLineReceived:function(){this._intLineDataCalls+=1;this.showLoading(!0);this._fldReceived.showLoading(!0);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strDataPath);n.set_DataObject(this._strDataObject);n.set_DataAction("GetLines_Received");n.addParameter("id",this._intLineID);n.addDataOK(Function.createDelegate(this,this.getLineReceivedOK));n.addError(Function.createDelegate(this,this.getLineReceivedError));n.addTimeout(Function.createDelegate(this,this.getLineReceivedError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getLineReceivedError:function(n){this.finishLineDataCall();this._fldReceived.showError(!0,n.get_ErrorMessage())},getLineReceivedOK:function(n){var i,r,t,u;if(this.finishLineDataCall(),i=n._result,this._fldReceived.showContent(!0),this._fldReceived.resetCount(),this._tblReceived.clearTable(),this._fldReceived.updateCount(i.Count),i.LinesReceived)for(r=0;r<i.LinesReceived.length;r++)t=i.LinesReceived[r],u=[$RGT_nubButton_GoodsIn(t.GoodsInNo,t.GoodsInNumber),$R_FN.writeDoubleCellValue(t.IsPoHub==!0?$R_FN.setCleanTextValue(t.PartNo):$RGT_nubButton_Stock(t.StockNo,t.PartNo,t.ROHS),$R_FN.setCleanTextValue(t.SupplierPart)),$R_FN.writeDoubleCellValue(t.IsPoHub==!0?$R_FN.setCleanTextValue(t.Mfr):$RGT_nubButton_Manufacturer(t.MfrNo,t.Mfr,t.MfrAdvisoryNotes),$R_FN.setCleanTextValue(t.DC)),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(t.Product),$R_FN.setCleanTextValue(t.Package)),$R_FN.writeDoubleCellValue(t.Quantity,t.LandedCost),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(t.Location),$R_FN.setCleanTextValue(t.ReceivedDate))],this._tblReceived.addRow(u,t.ID,!1),t=null,u=null;this._tblReceived.resizeColumns()},onShownReceived:function(){this._tblReceived.resizeColumns()},onRefreshReceived:function(){this.getLineReceived()},showEditForm:function(){if(!(this._blnLineLoaded=!1)){var n=this.getCurrentTable();this._frmEdit._intLineID=this._tblAll._aryCurrentValues[0];this._frmEdit._blnCanEditQty=!(this._IsAuthorised&&!this._IsReleased);this._frmEdit._blnCanEditPrice=!(this._IsAuthorised&&!this._IsReleased);this.showForm(this._frmEdit,!0)}},hideEditForm:function(){this.showForm(this._frmEdit,!1);this.getCurrentTable().resizeColumns()},saveEditComplete:function(){this.hideEditForm();this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully);this.getTabData();this.onPotentialStatusChange()},showEditAllForm:function(){(this._blnLineLoaded=!1)||(this._frmEditAll._aryCurrentValues=$R_FN.arrayToSingleString(this._tblAll._aryCurrentValues),this._frmEditAll._intLineID=this._tblAll._aryCurrentValues[0],this._frmEditAll._blnCanEditQty=!(this._IsAuthorised&&!this._IsReleased),this._frmEditAll._blnCanEditPrice=!(this._IsAuthorised&&!this._IsReleased),this.showForm(this._frmEditAll,!0))},hideEditAllForm:function(){this.showForm(this._frmEditAll,!1);this.getCurrentTable().resizeColumns()},saveEditAllComplete:function(){this.hideEditForm();this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully);this.getTabData();this.onPotentialStatusChange()},showApprovalForm:function(){this._frmApproval._aryUnpostedLineIDs=this._aryUnpostedLineIDs;this.doShowApprovalForm("LINEMANAGER_APPROVE")},doShowApprovalForm:function(n){(this._blnLineLoaded=!1)||(this._frmApproval.changeMode(n),this._frmApproval._intExportApprovalID=this._intPurchaseOrderID,this._frmApproval._intLineID=this._tblAll._aryCurrentValues[0],this.showForm(this._frmApproval,!0))},hideApprovalForm:function(){this.showForm(this._frmApproval,!1);this.getCurrentTable().resizeColumns()},saveApprovalComplete:function(){this.hideApprovalForm();this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully);this.getTabData();this.onPotentialStatusChange()},showAddForm:function(){this._frmAdd._intPurchaseOrderID=this._intPurchaseOrderID;var n=this.getCurrentTable();this._frmAdd._intIPOClientNo=this._ipoClientNo;this.showForm(this._frmAdd,!0)},hideAddForm:function(){this.showForm(this._frmAdd,!1);this.getCurrentTable().resizeColumns()},saveAddComplete:function(){this.hideAddForm();this._intLineID=this._frmAdd._intNewPurchaseOrderLineID;this.getTabData();this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully);this.onPotentialStatusChange()},showDeleteForm:function(){this._frmDelete._intPurchaseOrderID=this._intPurchaseOrderID;this._frmDelete._intLineID=this._intLineID;this._frmDelete.setFieldValue("ctlQuantity",this.getFieldValue("ctlWeblinkEvidence"));this._frmDelete.setFieldValue("ctlTradeRefOne",this.getFieldValue("ctlTradeRefOne"));this.showForm(this._frmDelete,!0)},hideDeleteForm:function(){this.showForm(this._frmDelete,!1);this.getCurrentTable().resizeColumns()},deleteComplete:function(){this.hideDeleteForm();this.getTabData();this.onPotentialStatusChange()},showCloseForm:function(){this._frmClose._intPurchaseOrderID=this._intPurchaseOrderID;this._frmClose._intLineID=this._intLineID;this.showForm(this._frmClose,!0)},hideCloseForm:function(){this.showForm(this._frmClose,!1);this.getCurrentTable().resizeColumns()},closeComplete:function(){this.hideCloseForm();this.getTabData();this.onPotentialStatusChange()},showDeallocateForm:function(){this._frmDeallocate._intPurchaseOrderID=this._intPurchaseOrderID;this._frmDeallocate._aryLineIDs=this._tblAllocations._aryCurrentValues;this._frmDeallocate._intLineID=this._intLineID;this._frmDeallocate._InternalPurchaseOrderNumber=this._InternalPurchaseOrderNumber;this._frmDeallocate._PoLine=this._PoLine;this._frmDeallocate._Part=this._Part;this._frmDeallocate._Quantity=this._Quantity;this.showForm(this._frmDeallocate,!0)},hideDeallocateForm:function(){this.showForm(this._frmDeallocate,!1);this.getCurrentTable().resizeColumns()},deallocateComplete:function(){this.hideDeallocateForm();this.getTabData();this.onPotentialStatusChange()},finishLineDataCall:function(){this._intLineDataCalls-=1;this._intLineDataCalls<1&&this.showLoading(!1)},updateStatus:function(n){this._enmPOStatus=Number.parseInvariant(n.toString());this._blnDisableAllButtons=this._enmPOStatus==$R_ENUM$PurchaseOrderStatus.Received||this._enmPOStatus==$R_ENUM$PurchaseOrderStatus.Complete;this.enableEditButtons(!0)},enableDisableAddButton:function(){},clearPolineFormOnEdit:function(){this._frmEdit&&(this._frmEdit._intLineID=-1)},writeCheckbox:function(n,t,i){var r=this.getControlID("chk",t,i),u=this.getControlID("chkImg",t,i);return String.format('<div class="imageCheckBoxDisabled" id="{0}" ><img id="{1}" class="{2}" src="images/x.gif" style="border-width: 0px;" /> <\/div>',r,u,"off")},getControlID:function(n,t,i){return String.format("{0}_{1}{2}",i._element.id,n,t)},getCheckBox:function(n,t){return $find(this.getControlID("chk",n,t))},registerCheckBox:function(n,t,i,r,u){var e=this.getControlID("chk",t,u),o=this.getControlID("chkImg",t,u),f=this.getCheckBox(t,u);f&&(f.dispose(),f=null);eval($R_FN.getCreateStatement("Rebound.GlobalTrader.Site.Controls.ImageCheckBox",[["blnChecked",i],["blnEnabled",r],["img",String.format('$get("{0}")',o)]],e))},getCheckedCellValue:function(n,t,i){var r=this.getCurrentTable(),u=this.getCheckBox(n,r),f=u._blnChecked,e=r._tbl.rows[n];e&&(f==!0?(this.clearMessages(),Array.add(this._arrPOLineIds,t),Array.add(this._POLineSerialNo,i),$R_IBTN.enableButton(this._ibtnAddExpditeNote,!0)):($R_IBTN.enableButton(this._ibtnAddExpditeNote,!1),Array.remove(this._arrPOLineIds,t),Array.remove(this._POLineSerialNo,i)),this._arrPOLineIds.length==0?$R_IBTN.enableButton(this._ibtnAddExpditeNote,!1):$R_IBTN.enableButton(this._ibtnAddExpditeNote,!0),this.enableReleaseButton())},enableReleaseButton:function(){var n=!1,t=this.getCurrentTable(),r=0,u=this.getCurrentTable()._aryExtraData;for(i=0;i<t._tbl.rows.length;i++){var f=this.getCheckBox(i,t),o=t._tbl.rows[i],e=f._blnChecked;e&&!n&&(n=u[i].IsAuthorised&&!u[i].IsReleased,++r)}r==0&&(n=!1);$R_IBTN.enableButton(this._ibtnRelease,n)},clearCheckBoxes:function(){for(var t,i=this.getCurrentTable(),n=0;n<this._arrPOLineIds.length;n++)t=this.getCheckBox(n,i),t.setChecked(!1);$R_IBTN.enableButton(this._ibtnAddExpditeNote,!1);Array.clear(this._arrPOLineIds)},showExpediteForm:function(){this._frmExpediteAdd._intPurchaseOrderID=this._intPurchaseOrderID;this._frmExpediteAdd._IsFromLine=!0;this._frmExpediteAdd._IsFromIPO=!1;this._frmExpediteAdd._arrPOLineIds=this._arrPOLineIds;this._frmExpediteAdd.setFieldValue("ctlExpediteNotes","");this.showForm(this._frmExpediteAdd,!0)},hideExpediteForm:function(){this.showForm(this._frmExpediteAdd,!1)},cancelExpediteForm:function(){this.showForm(this._frmExpediteAdd,!1);this.showContent(!0)},saveExpediteComplete:function(){this.showForm(this._frmExpediteAdd,!1);this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully);this.onPotentialStatusChange()},resetPOLine:function(){this._intLineID=-1;this.setFieldValue("ctlIPOBom","");this.setFieldValue("ctlWeblinkEvidence","");this.setFieldValue("ctlQuantityAllocated","");this.setFieldValue("ctlApprovedOrderTwl","");this.setFieldValue("hidPartNo","");this.setFieldValue("ctlROHS","");this.setFieldValue("hidROHS","");this.setFieldValue("ctlTradeRefOne","");this.setFieldValue("hidPrice","");this.setFieldValue("hidCurrencyCode","");this.setFieldValue("hidShipInCost","");this.setFieldValue("ctlSupplierPart","");this.setFieldValue("hidManufacturer","");this.setFieldValue("hidManufacturerNo","");this.setFieldValue("ctlTradeRefTwo","");this.setFieldValue("hidProductNo","");this.setFieldValue("hidPackageNo","");this.setFieldValue("ctlPrdDutyCodeRate","");this.setFieldValue("hidIPOClientNo","")},showTopIcons:function(){clearTimeout(this._intTimeout);$R_FN.showElement(this._pnlEPRTootTip,!0);this._pnlEPRTootTip.style.top="0px";this._pnlEPRTootTip.style.left="0px";this.setToolTipLocation()},hideTopIcons:function(){clearTimeout(this._intTimeout);this._intTimeout=setTimeout(Function.createDelegate(this,this.finishHideTopIcons),100)},setToolTipLocation:function(){this._pnlEPRTootTip&&(this._ibtnEPR&&(this._pnlEPRTootTip.style.top=String.format("{0}px",Sys.UI.DomElement.getBounds(this._ibtnEPR).y-Sys.UI.DomElement.getBounds(this._pnlEPRTootTip).y+15)),this._ibtnEPR&&(this._pnlEPRTootTip.style.left=String.format("{0}px",Sys.UI.DomElement.getBounds(this._ibtnEPR).x-Sys.UI.DomElement.getBounds(this._pnlEPRTootTip).x)))},finishHideTopIcons:function(){$R_FN.showElement(this._pnlEPRTootTip,!1)},OpenEPR:function(){if(this._arrPOLineIds.length>0)$R_FN.openEPRWindow(this._intPurchaseOrderID,0,this._arrPOLineIds.toString(),this._POLineSerialNo.toString());else return alert("Please select line item to create EPR"),!1},ShowGridPopupTROne:function(){$RGT_openSupplierApprovalDoc(75)},ShowGridPopupTRTwo:function(){$RGT_openSupplierApprovalDoc(75)},ShowGridPopupTRThree:function(){$RGT_openSupplierApprovalDoc(75)},showReleaseForm:function(){this.showReleaseUnReleaseForm("Release")},showUnReleaseForm:function(){this.showReleaseUnReleaseForm("UnRelease")},showReleaseUnReleaseForm:function(n){n=="Release"?(this._frmRelease._arrPOLineIds=this._arrPOLineIds,this.showForm(this._frmRelease,!0)):(this._frmUnRelease._intPurchaseOrderLineID=this._intLineID,this.showForm(this._frmUnRelease,!0))},hideReleaseForm:function(){this.hideReleaseUnReleaseForm("Release")},hideUnReleaseForm:function(){this.hideReleaseUnReleaseForm("UnRelease")},hideReleaseUnReleaseForm:function(n){n=="Release"?this.showForm(this._frmRelease,!1):this.showForm(this._frmUnRelease,!1)},saveReleaseComplete:function(){$R_IBTN.enableButton(this._ibtnRelease,!1);this._arrPOLineIds=[];this.tabChanged();this.showForm(this._frmRelease,!1);this.showContentLoading(!1);this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully);this.onPotentialStatusChange()},saveUnReleaseComplete:function(){this.showForm(this._frmUnRelease,!1);this.tabChanged();this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully)},clearArray:function(n){while(n.length)n.remove()},showRequestApprovalForm:function(){this._frmRequestApproval._intSalesOrderLineID=this._tblAll._aryCurrentValues[0];this.showForm(this._frmRequestApproval,!0)},hideRequestApprovalForm:function(){this.showForm(this._frmRequestApproval,!1)},cancelRequestApprovalForm:function(){this.showForm(this._frmRequestApproval,!1);this.showContent(!0)},saveRequestApprovalComplete:function(){this.showForm(this._frmRequestApproval,!1);this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully)},printEmailLog:function(){var n=window.location.href,t=this.getCurrentTable(),i=t._varSelectedValue,r=new URL(n);$R_FN.openPrintLogWindow($R_ENUM$PrintObject.printTermEmailLog,i,!1,"SupplierApprovalEmailLog")}};Rebound.GlobalTrader.Site.Controls.Nuggets.ExportApprovalStatus.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.ExportApprovalStatus",Rebound.GlobalTrader.Site.Controls.Nuggets.Base);