using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site.Controls;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets{
    public partial class CustReqAllInfo : Base
    {

        #region Locals
        protected Panel _pnlCustReqAllDoc;
        #endregion
        #region Property
        private string _DocNo;
        public string DocNo
        {
            get { return _DocNo; }
            set { _DocNo = value; }
        }
        private string _actionType;
        public string ActionType
        {
            get { return _actionType; }
            set { _actionType = value; }
        }
        private string _DocId;
        public string DocId
        {
            get { return _DocId; }
            set { _DocId = value; }
        }
        #endregion 

        #region Overrides

        /// <summary>
        /// OnInit
        /// </summary>
        /// <param name="e"></param>
        protected override void OnInit(EventArgs e)
        {
            TitleText = Functions.GetGlobalResource("Nuggets", "AllDocumentInfo");
            base.OnInit(e);
        }
        protected override void OnLoad(EventArgs e)
        {
            AddScriptReference("Controls.Nuggets.CusReqMainInfo.CustReqAllInfo.CustReqAllInfo.js");
            base.OnLoad(e);
        }

        /// <summary>
        /// OnPreRender
        /// </summary>
        /// <param name="e"></param>
        protected override void OnPreRender(EventArgs e)
        {
            SetupScriptDescriptors();
            base.OnPreRender(e);
        }

        /// <summary>
        /// Wire up controls to the ascx
        /// </summary>
        private void WireUpControls()
        {
            //_pnlCustReqAllDoc = (Panel)FindContentControl("pnlCustReqAllDoc");
            //_pnlCustReqAllDoc = (Panel)FindControl("pnlCustReqAllDoc");
        }
        #endregion

        /// <summary>
        /// Setup Script Descriptors
        /// </summary>
        private void SetupScriptDescriptors()
        {
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Nuggets.CustReqAllInfo", ctlDesignBase.ClientID);
            _scScriptControlDescriptor.AddProperty("intDocNo", _DocNo);
            _scScriptControlDescriptor.AddProperty("actionType", _actionType);
            _scScriptControlDescriptor.AddProperty("intDocId", _DocId);
            _scScriptControlDescriptor.AddElementProperty("pnlCustReqAllDoc", FindContentControl("pnlCustReqAllDoc").ClientID);
            _scScriptControlDescriptor.AddElementProperty("pnlLoadingLineDetail", FindContentControl("pnlLoadingLineDetail").ClientID);

        }

    }
}