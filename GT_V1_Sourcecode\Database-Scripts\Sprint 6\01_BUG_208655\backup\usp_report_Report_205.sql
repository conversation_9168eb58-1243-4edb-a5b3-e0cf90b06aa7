﻿
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
ALTER PROCEDURE [dbo].[usp_report_Report_205]
	@ClientId			int			= null ,
	@StartDate			datetime	= null ,	
	@EndDate			datetime	= null ,
	@IncludeUnconfirmed	bit			= 0	,
	@<PERSON>MyReport bit = 0 ,
	@IntLoginId int 	= Null
  ,	@IntTeamNo int = NULL
  ,	@IntDivisionNo int = NULL
--********************************************************************************
--* SG 26.02.2020
--* - Added special section for Dubai
--*   vwPO only shows client rows that are not connected to an IPO
--*   so most of the Dubai POs were not showing
--* SG 26.02.2020
--* REB-18326 - Added Warehouse for Phil
--* SK 24.05.2010:
--* - new proc
--********************************************************************************
AS

--* Open Purchase Orders

IF	@ClientId IS NULL 
    BEGIN
    SET @ClientId = 101
    END

IF	@StartDate IS NULL 
    BEGIN
        SET @StartDate	= dbo.ufn_get_date_from_datetime((Select DateAdd(d,-1,GetDate ())) )
    END
IF	@EndDate IS NULL 
    BEGIN
		SET @EndDate	= dbo.ufn_get_date_from_datetime((Select DateAdd(d, 1,GetDate ())) )
    END

DECLARE @Unconfirmed bit

SET	@Unconfirmed		= 1
IF	@IncludeUnconfirmed = 1 
    SET @Unconfirmed	= 0
	
IF @ClientId = 9999 OR @ClientId = 114
	SELECT  po.PurchaseOrderNumber
		,	po.DateOrdered
		,	pol.DeliveryDate
		,	co.CompanyName						AS Supplier
		,	IsNull(ct.Name, ' ')				AS Type
		,	tm.TermsName						AS Terms
		,	CASE po.Confirmed
			WHEN 0 THEN 'No'
			ELSE		'Yes'
			END									AS Confirmed
		,	IsNull(mf.ManufacturerName, '')		AS Manufacturer
		,	pol.Part
		,	pol.Quantity
		,	pol.Price
		,	IsNull(pol.Quantity * pol.Price, 0)	As Value
		,	cu.CurrencyCode
		,	lg.EmployeeName						AS Buyer
		,	isnull(po.ExpediteNotes, '')		AS ExpediteNotes
		,	po.ExpediteDate
		,	w.WarehouseName						AS Warehouse
	FROM    dbo.tbPurchaseOrder po WITH (NoLock)  -- SG 24.10.2022 Use PO table not view for Dubai, because the view filters on InternalPurchaseOrder = NULL
	JOIN    dbo.vwPOline pol WITH (NoLock)
		ON	po.PurchaseOrderId	= pol.PurchaseOrderNo
	JOIN    dbo.tbCompany co WITH (NoLock)
		ON	po.CompanyNo		= co.CompanyId
	JOIN    dbo.tbCurrency cu WITH (NoLock)
		ON	po.CurrencyNo		= cu.CurrencyId
	LEFT 
	JOIN	dbo.tbLogin lg WITH (NoLock)
		ON	po.Buyer			= lg.LoginId
	JOIN    dbo.tbTerms tm WITH (NoLock)
		ON	po.TermsNo			= tm.TermsId
	LEFT 
	JOIN	dbo.tbManufacturer mf WITH (NoLock)
		ON	pol.ManufacturerNo	= mf.ManufacturerId
	LEFT 
	JOIN	dbo.tbCompanyType ct WITH (NoLock)
		ON	co.TypeNo				= ct.CompanyTypeId
	LEFT
	JOIN	dbo.tbWarehouse w WITH (NoLock)
		ON	po.WarehouseNo = w.WarehouseId
	WHERE	po.ClientNo				= @ClientId
	  --AND ((@ViewMyReport = 0)  OR (@ViewMyReport = 1  AND po.Buyer=@IntLoginId)) 
	  --Espire 04 Dec 2018:  REB12439 - Team and Division reports
	AND (	(@IntTeamNo IS NULL)          
		OR	(NOT @IntTeamNo IS NULL          
			AND lg.TeamNo = @IntTeamNo))          
	AND (	(@IntDivisionNo IS NULL)          
		OR	(NOT @IntDivisionNo IS NULL          
			AND lg.DivisionNo = @IntDivisionNo))          
	AND (	(@IntLoginId IS NULL)          
		OR	(NOT @IntLoginId IS NULL          
			AND po.Buyer = @IntLoginId)) 
	AND		po.DateOrdered			BETWEEN @StartDate
									AND		@EndDate
	AND		pol.Closed				= 0
	AND		pol.Inactive			= 0
	AND		IsNull(po.Confirmed, 0)	IN (@Unconfirmed, 1)
	ORDER BY po.PurchaseOrderNumber	
		,	po.DateOrdered
ELSE
	SELECT  po.PurchaseOrderNumber
		,	po.DateOrdered
		,	pol.DeliveryDate
		,	co.CompanyName						AS Supplier
		,	IsNull(ct.Name, ' ')				AS Type
		,	tm.TermsName						AS Terms
		,	CASE po.Confirmed
			WHEN 0 THEN 'No'
			ELSE		'Yes'
			END									AS Confirmed
		,	IsNull(mf.ManufacturerName, '')		AS Manufacturer
		,	pol.Part
		,	pol.Quantity
		,	pol.Price
		,	IsNull(pol.Quantity * pol.Price, 0)	As Value
		,	cu.CurrencyCode
		,	lg.EmployeeName						AS Buyer
		,	isnull(po.ExpediteNotes, '')		AS ExpediteNotes
		,	po.ExpediteDate
		,	w.WarehouseName						AS Warehouse
	FROM    dbo.vwPO po WITH (NoLock)						--dbo.tbPurchaseOrder po --GA
	JOIN    dbo.vwPOline pol WITH (NoLock)					--dbo.tbPurchaseOrderLine -- GA
		ON	po.PurchaseOrderId	= pol.PurchaseOrderNo
	JOIN    dbo.tbCompany co WITH (NoLock)
		ON	po.CompanyNo		= co.CompanyId
	JOIN    dbo.tbCurrency cu WITH (NoLock)
		ON	po.CurrencyNo		= cu.CurrencyId
	LEFT 
	JOIN	dbo.tbLogin lg WITH (NoLock)
		ON	po.Buyer			= lg.LoginId
	JOIN    dbo.tbTerms tm WITH (NoLock)
		ON	po.TermsNo			= tm.TermsId
	LEFT 
	JOIN	dbo.tbManufacturer mf WITH (NoLock)
		ON	pol.ManufacturerNo	= mf.ManufacturerId
	LEFT 
	JOIN	dbo.tbCompanyType ct WITH (NoLock)
		ON	co.TypeNo				= ct.CompanyTypeId
	LEFT
	JOIN	dbo.tbWarehouse w WITH (NoLock)
		ON	po.WarehouseNo = w.WarehouseId
	WHERE	po.ClientNo				= @ClientId
	  --AND ((@ViewMyReport = 0)  OR (@ViewMyReport = 1  AND po.Buyer=@IntLoginId)) 
	  --Espire 04 Dec 2018:  REB12439 - Team and Division reports
	AND (	(@IntTeamNo IS NULL)          
		OR	(NOT @IntTeamNo IS NULL          
			AND lg.TeamNo = @IntTeamNo))          
	AND (	(@IntDivisionNo IS NULL)          
		OR	(NOT @IntDivisionNo IS NULL          
			AND lg.DivisionNo = @IntDivisionNo))          
	AND (	(@IntLoginId IS NULL)          
		OR	(NOT @IntLoginId IS NULL          
			AND po.Buyer = @IntLoginId)) 
	AND		po.DateOrdered			BETWEEN @StartDate
									AND		@EndDate
	AND		pol.Closed				= 0
	AND		pol.Inactive			= 0
	AND		IsNull(po.Confirmed, 0)	IN (@Unconfirmed, 1)
	ORDER BY po.PurchaseOrderNumber	
