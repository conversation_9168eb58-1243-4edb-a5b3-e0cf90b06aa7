///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
//Marker     changed by      date         Remarks
//[001]      nagiTo           10/04/2024   Add/edit StarRating in setup screen
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.SetupNuggets");


Rebound.GlobalTrader.Site.Controls.SetupNuggets.StarRating = function(element) {
    Rebound.GlobalTrader.Site.Controls.SetupNuggets.StarRating.initializeBase(this, [element]);
	this._intItemID = -1;
};

Rebound.GlobalTrader.Site.Controls.SetupNuggets.StarRating.prototype = {

    get_intItemID: function() { return this._intItemID; }, set_intItemID: function(v) { if (this._intItemID !== v) this._intItemID = v; },
    get_ibtnAdd: function() { return this._ibtnAdd; }, set_ibtnAdd: function(v) { if (this._ibtnAdd !== v) this._ibtnAdd = v; },
    get_tbl: function() { return this._tbl; }, set_tbl: function(v) { if (this._tbl !== v) this._tbl = v; },

    initialize: function() {
        Rebound.GlobalTrader.Site.Controls.SetupNuggets.StarRating.callBaseMethod(this, "initialize");

        //data
        this._strDataPath = "controls/SetupNuggets/StarRating";
        this._strDataObject = "StarRating";

        //nugget events
        this.addRefreshEvent(Function.createDelegate(this, this.getData));

        //add form
        if (this._ibtnAdd) {
            $R_IBTN.addClick(this._ibtnAdd, Function.createDelegate(this, this.showAddForm));
            this._frmAdd = $find(this._aryFormIDs[0]);
            this._frmAdd.addCancel(Function.createDelegate(this, this.hideAddForm));
            this._frmAdd.addSaveComplete(Function.createDelegate(this, this.saveAddComplete));
        }

        this.getData();
    },

    dispose: function() {
        if (this.isDisposed) return;
        if (this._ibtnAdd) $R_IBTN.clearHandlers(this._ibtnAdd);
        if (this._frmAdd) this._frmAdd.dispose();
        if (this._tbl) this._tbl.dispose();
        this._frmAdd = null;
        this._ibtnAdd = null;
        this._tbl = null;
        this._intItemID = null;
        Rebound.GlobalTrader.Site.Controls.SetupNuggets.StarRating.callBaseMethod(this, "dispose");
    },

    getData: function () {
        this.showLoading(true);
        this.showContentLoading(true);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData(this._strDataPath);
        obj.set_DataObject(this._strDataObject);
        obj.set_DataAction("GetData");
        obj.addDataOK(Function.createDelegate(this, this.getDataOK));
        obj.addError(Function.createDelegate(this, this.getDataError));
        obj.addTimeout(Function.createDelegate(this, this.getDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },
    getDataOK: function(args) {
        this.showLoading(false);
        var blnHasData = false;
        this._tbl.clearTable();
        var result = args._result;
        if (result.Items) {
            for (var i = 0; i < result.Items.length; i++) {
                var row = result.Items[i];

                var aryData = [
                    row.NumOfPO
                    , row.CountedStar
                    , row.CreatedDate
                    , row.CreatedBy
                ];
                
                this._tbl.addRow(aryData, null);
                row = null;
                blnHasData = true;
            }
        }
        this._tbl.resizeColumns();
        this.showContent(true);
        this.showContentLoading(false);
        this.showNoData(!blnHasData);
    },

    getDataError: function(args) {
        this.showError(true, args.get_ErrorMessage());
    },

    showAddForm: function() {
        this.showForm(this._frmAdd, true);
    },

    hideAddForm: function() {
        this.showForm(this._frmAdd, false);
    },

    saveAddComplete: function() {
        this.hideAddForm();
        this._intItemID = this._frmAdd._intItemID;
        this.getData();
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
    }

};

Rebound.GlobalTrader.Site.Controls.SetupNuggets.StarRating.registerClass("Rebound.GlobalTrader.Site.Controls.SetupNuggets.StarRating", Rebound.GlobalTrader.Site.Controls.Nuggets.Base, Sys.IDisposable);
