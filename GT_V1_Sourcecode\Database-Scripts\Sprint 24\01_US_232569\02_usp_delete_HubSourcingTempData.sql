﻿SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-232569]		An.TranTan			20-Mar-2025		Create			Delete previous tbBomImportSourcingTemp
===========================================================================================
*/
CREATE OR ALTER  PROCEDURE [dbo].[usp_delete_HubSourcingTempData]
    @UserID INT = 0,             
	@DeleteCount INT OUTPUT
AS 
BEGIN
	DELETE BorisGlobalTraderImports.dbo.tbBomImportSourcingTemp WHERE CreatedBy = @UserID;
	SET @DeleteCount = @@ROWCOUNT;
END
GO