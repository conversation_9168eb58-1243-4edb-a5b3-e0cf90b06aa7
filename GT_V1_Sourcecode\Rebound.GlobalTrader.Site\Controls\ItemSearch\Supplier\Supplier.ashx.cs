using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site.Enumerations;

namespace Rebound.GlobalTrader.Site.Controls.ItemSearch.Data {
	[WebService(Namespace = "http://tempuri.org/")]
	[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class Supplier : Rebound.GlobalTrader.Site.Data.ItemSearch.Base
    {

		protected override void GetData() {
			List<BLL.Company> lst = null;
			try {

              //  bool? isClientInvoice = GetFormValue_NullableBoolean("isClientInvoice", null);

				lst = BLL.Company.ItemSearch(
					SessionManager.ClientID
					, GetFormValue_NullableInt("Order", 0)
					, GetFormValue_NullableInt("SortDir", SortColumnDirection.ASC)
					, GetFormValue_NullableInt("PageIndex", 0)
					, GetFormValue_NullableInt("PageSize", 10)
                   // , GetFormValue_StringForNameSearch("Name")
                    , GetFormValue_StringForNameSearchDecode("Name")
					, GetFormValue_NullableBoolean("POApproved")
					, GetFormValue_NullableBoolean("SOApproved")
					, false
                    , false
                    , GetFormValue_NullableInt("PONoLo")
                    , GetFormValue_NullableInt("PONoHi")
                    
				);
				JsonObject jsn = new JsonObject();
				JsonObject jsnItems = new JsonObject(true);
				foreach (BLL.Company cm in lst) {
					JsonObject jsnItem = new JsonObject();
					jsnItem.AddVariable("ID", cm.CompanyId);
					jsnItem.AddVariable("Name", cm.CompanyName);
					jsnItem.AddVariable("Type", cm.CompanyType);
					jsnItem.AddVariable("City", cm.City);
					jsnItem.AddVariable("Country", cm.Country);
					jsnItem.AddVariable("Tel", cm.Telephone);
					jsnItem.AddVariable("Salesperson", cm.SalesmanName);
                    jsnItem.AddVariable("SupplierCode", cm.SupplierCode);
					jsnItem.AddVariable("LastContact", Functions.FormatDaysAgo(cm.DaysSinceContact));
					jsnItems.AddVariable(jsnItem);
					jsnItem.Dispose(); jsnItem = null;
				}
				jsn.AddVariable("Count", (lst.Count > 0) ? lst[0].RowCnt : 0);
				jsn.AddVariable("Results", jsnItems);
				OutputResult(jsn);
				jsnItems.Dispose(); jsnItems = null;
				jsn.Dispose(); jsn = null;
			} catch (Exception ex) {
				WriteError(ex);
			} finally {
				lst = null;
			}
			base.GetData();
		}


	}
}
