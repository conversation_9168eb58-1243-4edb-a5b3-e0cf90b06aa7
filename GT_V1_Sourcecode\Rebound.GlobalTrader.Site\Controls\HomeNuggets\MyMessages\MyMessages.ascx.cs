using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.HomeNuggets {
	public partial class MyMessages : Base {

		#region Controls
		SimpleDataTable _tblMessages;
		#endregion

		protected override void OnInit(EventArgs e) {
			this.HomePageNuggetType = "MyMessages";
			base.OnInit(e);
			_tblMessages = (SimpleDataTable)FindContentControl("tblMessages");
			AddScriptReference("Controls.HomeNuggets.MyMessages.MyMessages.js");
		}

		protected override void OnLoad(EventArgs e) {
			//table headings
			_tblMessages.Columns.Add(new SimpleDataColumn("From"));
            _tblMessages.Columns.Add(new SimpleDataColumn("Subject"));
            _tblMessages.Columns.Add(new SimpleDataColumn("Date"));

			//setup javascript
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyMessages", this.ctlDesignBase.ClientID);
			_scScriptControlDescriptor.AddComponentProperty("tblMessages", _tblMessages.ClientID);
			base.OnLoad(e);
		}


	}
}