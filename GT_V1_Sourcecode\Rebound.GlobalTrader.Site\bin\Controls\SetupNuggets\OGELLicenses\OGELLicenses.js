Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.SetupNuggets");Rebound.GlobalTrader.Site.Controls.SetupNuggets.OGELLicenses=function(n){Rebound.GlobalTrader.Site.Controls.SetupNuggets.OGELLicenses.initializeBase(this,[n]);this._intItemID=-1};Rebound.GlobalTrader.Site.Controls.SetupNuggets.OGELLicenses.prototype={get_intItemID:function(){return this._intItemID},set_intItemID:function(n){this._intItemID!==n&&(this._intItemID=n)},get_ibtnAdd:function(){return this._ibtnAdd},set_ibtnAdd:function(n){this._ibtnAdd!==n&&(this._ibtnAdd=n)},get_ibtnEdit:function(){return this._ibtnEdit},set_ibtnEdit:function(n){this._ibtnEdit!==n&&(this._ibtnEdit=n)},get_tbl:function(){return this._tbl},set_tbl:function(n){this._tbl!==n&&(this._tbl=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.SetupNuggets.OGELLicenses.callBaseMethod(this,"initialize");this._strDataPath="controls/SetupNuggets/OGELLicenses";this._strDataObject="OGELLicenses";this.addRefreshEvent(Function.createDelegate(this,this.getData));this._tbl.addSelectedIndexChanged(Function.createDelegate(this,this.tbl_SelectedIndexChanged));this._ibtnEdit&&($R_IBTN.addClick(this._ibtnEdit,Function.createDelegate(this,this.showEditForm)),this._frmEdit=$find(this._aryFormIDs[1]),this._frmEdit.addCancel(Function.createDelegate(this,this.hideEditForm)),this._frmEdit.addSaveComplete(Function.createDelegate(this,this.saveEditComplete)));this._ibtnAdd&&($R_IBTN.addClick(this._ibtnAdd,Function.createDelegate(this,this.showAddForm)),this._frmAdd=$find(this._aryFormIDs[0]),this._frmAdd.addCancel(Function.createDelegate(this,this.hideAddForm)),this._frmAdd.addSaveComplete(Function.createDelegate(this,this.saveAddComplete)));this.getData()},dispose:function(){this.isDisposed||(this._ibtnAdd&&$R_IBTN.clearHandlers(this._ibtnAdd),this._ibtnEdit&&$R_IBTN.clearHandlers(this._ibtnEdit),this._frmEdit&&this._frmEdit.dispose(),this._frmAdd&&this._frmAdd.dispose(),this._tbl&&this._tbl.dispose(),this._frmEdit=null,this._frmAdd=null,this._ibtnAdd=null,this._ibtnEdit=null,this._tbl=null,this._intItemID=null,Rebound.GlobalTrader.Site.Controls.SetupNuggets.OGELLicenses.callBaseMethod(this,"dispose"))},enableEditButtons:function(n){this._ibtnEdit&&$R_IBTN.enableButton(this._ibtnEdit,n)},tbl_SelectedIndexChanged:function(){this.enableEditButtons(!0);this._intItemID=this._tbl._varSelectedValue},getData:function(){this.showLoading(!0);this.showContentLoading(!0);this.enableEditButtons(!1);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strDataPath);n.set_DataObject(this._strDataObject);n.set_DataAction("GetData");n.addDataOK(Function.createDelegate(this,this.getDataOK));n.addError(Function.createDelegate(this,this.getDataError));n.addTimeout(Function.createDelegate(this,this.getDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getDataOK:function(n){var u,i,r;if(this.showLoading(!1),u=!1,this._tbl.clearTable(),i=n._result,i.Items)for(r=0;r<i.Items.length;r++){var t=i.Items[r],f=t.Inactive===!1?"Yes":"No",e=[$R_FN.writeDoubleCellValue(t.ID),$R_FN.setCleanTextValue(t.Name),$R_FN.setCleanTextValue(t.Description),$R_FN.setCleanTextValue(f),t.UpdatedDate,$R_FN.setCleanTextValue(t.UpdatedBy)],o={Inactive:t.Inactive},s=t.Inactive?"ceased":"";this._tbl.addRow(e,t.ID,t.ID==this._intItemID,o,s);t=null;u=!0}this._tbl.resizeColumns();this.showContent(!0);this.showContentLoading(!1);this.showNoData(!u)},getDataError:function(n){this.showError(!0,n.get_ErrorMessage())},showEditForm:function(){this._frmEdit._intItemID=this._intItemID;this._frmEdit.setFieldValue("ctlOgelNumber",this._tbl.getSelectedCellValue(1));this._frmEdit.setFieldValue("ctlDescription",this._tbl.getSelectedCellValue(2));this._frmEdit.setFieldValue("ctlInActive",this._tbl.getSelectedExtraData().Inactive);this.showForm(this._frmEdit,!0)},hideEditForm:function(){this.showForm(this._frmEdit,!1)},saveEditComplete:function(){this.hideEditForm();this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully);this.getData()},showAddForm:function(){this.showForm(this._frmAdd,!0)},hideAddForm:function(){this.showForm(this._frmAdd,!1)},saveAddComplete:function(){this.hideAddForm();this._intItemID=this._frmAdd._intItemID;this.getData();this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully)}};Rebound.GlobalTrader.Site.Controls.SetupNuggets.OGELLicenses.registerClass("Rebound.GlobalTrader.Site.Controls.SetupNuggets.OGELLicenses",Rebound.GlobalTrader.Site.Controls.Nuggets.Base,Sys.IDisposable);