using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using System.Text;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site.Controls;
using Rebound.GlobalTrader.Site;
//Marker     Changed by      Date         Remarks
//[001]      Shashi Keshar  20/01/2016    added Insurance History in Detail Section
namespace Rebound.GlobalTrader.Site.Controls.Nuggets {
	public partial class CompanySalesInfo : Base {

		#region Locals

		protected IconButton _ibtnEdit;
		protected FlexiDataTable _tblOpenSOs;
		protected Panel _pnlLoadingOpenSOs;
		protected Panel _pnlOpenSOsError;
		protected Panel _pnlOpenSOs;
		protected Panel _pnlGetOpenSOs;
		protected HyperLink _hypGetOpenSOs;
		protected FlexiDataTable _tblOverdueSOs;
		protected Panel _pnlLoadingOverdueSOs;
		protected Panel _pnlOverdueSOsError;
		protected Panel _pnlOverdueSOs;
		protected Panel _pnlGetOverdueSOs;
		protected HyperLink _hypGetOverdueSOs;
        protected FlexiDataTable _tblCreditHistory;
        protected Panel _pnlLoadingCreditHistory;
        protected Panel _pnlCreditHistoryError;
        protected Panel _pnlCreditHistory;
        protected Panel _pnlGetCreditHistory;
        protected HyperLink _hypGetCreditHistory;
        //[001] Start Here 
        protected FlexiDataTable _tblInsuranceHistory;
        protected Panel _pnlLoadingInsuranceHistory;
        protected Panel _pnlInsuranceHistoryError;
        protected Panel _pnlInsuranceHistory;
        protected Panel _pnlGetInsuranceHistory;
        protected HyperLink _hypGetInsuranceHistory;

        //[001] End Here
		#endregion

		#region Properties

		private int _intCompanyID = -1;
		public int CompanyID {
			get { return _intCompanyID; }
			set { _intCompanyID = value; }
		}

		private bool _blnCanEdit = true;
		public bool CanEdit {
			get { return _blnCanEdit; }
			set { _blnCanEdit = value; }
		}

        private Boolean _Status = false;
        public Boolean Status
        {
            get { return _Status; }
            set { _Status = value; }
        }

		#endregion

		#region Overrides

		/// <summary>
		/// Oninit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
			WireUpControls();
			AddScriptReference("Controls.Nuggets.CompanySalesInfo.CompanySalesInfo.js");
			TitleText = Functions.GetGlobalResource("Nuggets", "CompanySalesInfo");
			if (_intCompanyID == -1) _intCompanyID = _objQSManager.CompanyID;
			SetupTable();
		}

		protected override void OnLoad(EventArgs e) {
			SetupScriptDescriptors();
			base.OnLoad(e);
		}

		protected override void OnPreRender(EventArgs e) {
            EnableControl();
			_ibtnEdit.Visible = _blnCanEdit;
			base.OnPreRender(e);
		}

		#endregion
        private void EnableControl()
        {
            _Status = SecurityManager.IsCompanyStatusStopEdit(Convert.ToInt32(SessionManager.LoginID));
           
        }
		private void SetupTable() {
			_tblOpenSOs.AllowSelection = false;
			_tblOpenSOs.Columns.Add(new FlexiDataColumn("Number", WidthManager.GetWidth(WidthManager.ColumnWidth.SystemDocumentNumber)));
			_tblOpenSOs.Columns.Add(new FlexiDataColumn("Date", WidthManager.GetWidth(WidthManager.ColumnWidth.Date)));
			_tblOpenSOs.Columns.Add(new FlexiDataColumn("Value"));

			_tblOverdueSOs.AllowSelection = false;
			_tblOverdueSOs.Columns.Add(new FlexiDataColumn("Number", WidthManager.GetWidth(WidthManager.ColumnWidth.SystemDocumentNumber)));
			_tblOverdueSOs.Columns.Add(new FlexiDataColumn("Date", WidthManager.GetWidth(WidthManager.ColumnWidth.Date)));
			_tblOverdueSOs.Columns.Add(new FlexiDataColumn("Value"));

            _tblCreditHistory.AllowSelection = false;
            _tblCreditHistory.Columns.Add(new FlexiDataColumn("From", WidthManager.GetWidth(WidthManager.ColumnWidth.CurrencyValue)));
            _tblCreditHistory.Columns.Add(new FlexiDataColumn("To", WidthManager.GetWidth(WidthManager.ColumnWidth.CurrencyValue)));
            _tblCreditHistory.Columns.Add(new FlexiDataColumn("Date", WidthManager.GetWidth(WidthManager.ColumnWidth.Date)));
            _tblCreditHistory.Columns.Add(new FlexiDataColumn("By"));
           
            //[001] Start Here
            _tblInsuranceHistory.AllowSelection = false;
            _tblInsuranceHistory.Columns.Add(new FlexiDataColumn("From", WidthManager.GetWidth(WidthManager.ColumnWidth.CurrencyValue)));
            _tblInsuranceHistory.Columns.Add(new FlexiDataColumn("To", WidthManager.GetWidth(WidthManager.ColumnWidth.CurrencyValue)));
            _tblInsuranceHistory.Columns.Add(new FlexiDataColumn("Date", WidthManager.GetWidth(WidthManager.ColumnWidth.Date)));
            _tblInsuranceHistory.Columns.Add(new FlexiDataColumn("By"));
            //[001] End Here
        }

		/// <summary>
		/// Sets up AJAX script descriptors
		/// </summary>
		private void SetupScriptDescriptors() {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Nuggets.CompanySalesInfo", ctlDesignBase.ClientID);
			if (_blnCanEdit) _scScriptControlDescriptor.AddElementProperty("ibtnEdit", _ibtnEdit.ClientID);
			_scScriptControlDescriptor.AddProperty("intCompanyID", _intCompanyID);
			_scScriptControlDescriptor.AddElementProperty("pnlLoadingOpenSOs", _pnlLoadingOpenSOs.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlOpenSOsError", _pnlOpenSOsError.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlOpenSOs", _pnlOpenSOs.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlGetOpenSOs", _pnlGetOpenSOs.ClientID);
			_scScriptControlDescriptor.AddElementProperty("hypGetOpenSOs", _hypGetOpenSOs.ClientID);
			_scScriptControlDescriptor.AddComponentProperty("tblOpenSOs", _tblOpenSOs.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlLoadingOverdueSOs", _pnlLoadingOverdueSOs.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlOverdueSOsError", _pnlOverdueSOsError.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlOverdueSOs", _pnlOverdueSOs.ClientID);
			_scScriptControlDescriptor.AddComponentProperty("tblOverdueSOs", _tblOverdueSOs.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlGetOverdueSOs", _pnlGetOverdueSOs.ClientID);
			_scScriptControlDescriptor.AddElementProperty("hypGetOverdueSOs", _hypGetOverdueSOs.ClientID);
            _scScriptControlDescriptor.AddElementProperty("pnlLoadingCreditHistory", _pnlLoadingCreditHistory.ClientID);
            _scScriptControlDescriptor.AddElementProperty("pnlCreditHistoryError", _pnlCreditHistoryError.ClientID);
            _scScriptControlDescriptor.AddElementProperty("pnlCreditHistory", _pnlCreditHistory.ClientID);
            _scScriptControlDescriptor.AddComponentProperty("tblCreditHistory", _tblCreditHistory.ClientID);
            _scScriptControlDescriptor.AddElementProperty("pnlGetCreditHistory", _pnlGetCreditHistory.ClientID);
            _scScriptControlDescriptor.AddElementProperty("hypGetCreditHistory", _hypGetCreditHistory.ClientID);
            //[001] Start Here
            _scScriptControlDescriptor.AddElementProperty("pnlLoadingInsuranceHistory", _pnlLoadingInsuranceHistory.ClientID);
            _scScriptControlDescriptor.AddElementProperty("pnlInsuranceHistoryError", _pnlInsuranceHistoryError.ClientID);
            _scScriptControlDescriptor.AddElementProperty("pnlInsuranceHistory", _pnlInsuranceHistory.ClientID);
            _scScriptControlDescriptor.AddComponentProperty("tblInsuranceHistory", _tblInsuranceHistory.ClientID);
            _scScriptControlDescriptor.AddElementProperty("pnlGetInsuranceHistory", _pnlGetInsuranceHistory.ClientID);
            _scScriptControlDescriptor.AddElementProperty("hypGetInsuranceHistory", _hypGetInsuranceHistory.ClientID);
            //[001] End Here
            _scScriptControlDescriptor.AddProperty("Status", _Status);
        }

		/// <summary>
		/// Wire up controls from the ascx
		/// </summary>
		private void WireUpControls() {
			_ibtnEdit = FindIconButton("ibtnEdit");
			_tblOpenSOs = (FlexiDataTable)ctlDesignBase.FindContentControl("tblOpenSOs");
			_pnlLoadingOpenSOs = (Panel)ctlDesignBase.FindContentControl("pnlLoadingOpenSOs");
			_pnlOpenSOsError = (Panel)ctlDesignBase.FindContentControl("pnlOpenSOsError");
			_pnlOpenSOs = (Panel)ctlDesignBase.FindContentControl("pnlOpenSOs");
			_pnlGetOpenSOs = (Panel)ctlDesignBase.FindContentControl("pnlGetOpenSOs");
			_hypGetOpenSOs = (HyperLink)ctlDesignBase.FindContentControl("hypGetOpenSOs");
			_tblOverdueSOs = (FlexiDataTable)ctlDesignBase.FindContentControl("tblOverdueSOs");
			_pnlLoadingOverdueSOs = (Panel)ctlDesignBase.FindContentControl("pnlLoadingOverdueSOs");
			_pnlOverdueSOsError = (Panel)ctlDesignBase.FindContentControl("pnlOverdueSOsError");
			_pnlOverdueSOs = (Panel)ctlDesignBase.FindContentControl("pnlOverdueSOs");
			_pnlGetOverdueSOs = (Panel)ctlDesignBase.FindContentControl("pnlGetOverdueSOs");
			_hypGetOverdueSOs = (HyperLink)ctlDesignBase.FindContentControl("hypGetOverdueSOs");
            _tblCreditHistory = (FlexiDataTable)ctlDesignBase.FindContentControl("tblCreditHistory");
            _pnlLoadingCreditHistory = (Panel)ctlDesignBase.FindContentControl("pnlLoadingCreditHistory");
            _pnlCreditHistoryError = (Panel)ctlDesignBase.FindContentControl("pnlCreditHistoryError");
            _pnlCreditHistory = (Panel)ctlDesignBase.FindContentControl("pnlCreditHistory");
            _pnlGetCreditHistory = (Panel)ctlDesignBase.FindContentControl("pnlGetCreditHistory");
            _hypGetCreditHistory = (HyperLink)ctlDesignBase.FindContentControl("hypGetCreditHistory");
            //[001] Start Here
            _tblInsuranceHistory = (FlexiDataTable)ctlDesignBase.FindContentControl("tblInsuranceHistory");
            _pnlLoadingInsuranceHistory = (Panel)ctlDesignBase.FindContentControl("pnlLoadingInsuranceHistory");
            _pnlInsuranceHistoryError = (Panel)ctlDesignBase.FindContentControl("pnlInsuranceHistoryError");
            _pnlInsuranceHistory = (Panel)ctlDesignBase.FindContentControl("pnlInsuranceHistory");
            _pnlGetInsuranceHistory = (Panel)ctlDesignBase.FindContentControl("pnlGetInsuranceHistory");
            _hypGetInsuranceHistory = (HyperLink)ctlDesignBase.FindContentControl("hypGetInsuranceHistory");
            //[001] End Here
        }

	}
}
