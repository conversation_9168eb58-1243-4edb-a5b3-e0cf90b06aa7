using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Collections;

namespace Rebound.GlobalTrader.Site {
	/// <summary>
	/// Helper functions for control building 
	/// </summary>
	public static class ControlBuilders {

		public static Panel CreatePanel() {
			return new Panel();
		}
		
		public static Panel CreatePanel(string strCssClass) {
			Panel pnl = CreatePanel();
			pnl.CssClass = strCssClass;
			return pnl;
		}
		
		public static Panel CreatePanelInsideParent(Control ctlParent) {
			Panel pnl = CreatePanel();
			ctlParent.Controls.Add(pnl);
			return pnl;
		}
		
		public static Panel CreatePanelInsideParent(Control ctlParent, string strCssClass) {
			Panel pnl = CreatePanel(strCssClass);
			ctlParent.Controls.Add(pnl);
			return pnl;
		}
		
		public static Literal CreateLiteral() {
			return new Literal();
		}
		
		public static Literal CreateLiteral(string strText) {
			Literal lit = CreateLiteral();
			lit.Text = strText;
			return lit;
		}
		
		public static Literal CreateLiteralInsideParent(Control ctlParent) {
			Literal lit = CreateLiteral();
			ctlParent.Controls.Add(lit);
			return lit;
		}
		
		public static Literal CreateLiteralInsideParent(Control ctlParent, string strText) {
			Literal lit = CreateLiteral(strText);
			ctlParent.Controls.Add(lit);
			return lit;
		}
		
		public static PlaceHolder CreatePlaceHolder() {
			return new PlaceHolder();
		}
		
		public static PlaceHolder CreatePlaceHolderInsideParent(Control ctlParent) {
			PlaceHolder plh = CreatePlaceHolder();
			ctlParent.Controls.Add(plh);
			return plh;
		}
		
		public static HyperLink CreateHyperLink() {
			return new HyperLink();
		}

		public static HyperLink CreateHyperLink(string strCssClass) {
			HyperLink hyp = CreateHyperLink();
			hyp.CssClass = strCssClass;
			return hyp;
		}

		public static HyperLink CreateHyperLink(string strCssClass, string strHref) {
			HyperLink hyp = CreateHyperLink(strCssClass);
			hyp.NavigateUrl = strHref;
			return hyp;
		}

		public static HyperLink CreateHyperLink(string strCssClass, string strHref, string strText) {
			HyperLink hyp = CreateHyperLink(strCssClass, strHref);
			hyp.Text = strText;
			return hyp;
		}

		public static HyperLink CreateHyperLinkInsideParent(Control ctlParent) {
			HyperLink hyp = CreateHyperLink();
			ctlParent.Controls.Add(hyp);
			return hyp;
		}

		public static HyperLink CreateHyperLinkInsideParent(Control ctlParent, string strCssClass) {
			HyperLink hyp = CreateHyperLink(strCssClass);
			ctlParent.Controls.Add(hyp);
			return hyp;
		}

		public static HyperLink CreateHyperLinkInsideParent(Control ctlParent, string strCssClass, string strHref) {
			HyperLink hyp = CreateHyperLink(strCssClass, strHref);
			ctlParent.Controls.Add(hyp);
			return hyp;
		}

		public static HyperLink CreateHyperLinkInsideParent(Control ctlParent, string strCssClass, string strHref, string strText) {
			HyperLink hyp = CreateHyperLink(strCssClass, strHref, strText);
			ctlParent.Controls.Add(hyp);
			return hyp;
		}
		
		public static HtmlGenericControl CreateHtmlGenericControl(string strType) {
			return new HtmlGenericControl(strType);
		}
		
		public static HtmlGenericControl CreateHtmlGenericControl(string strType, string strCssClass) {
			HtmlGenericControl ctl = CreateHtmlGenericControl(strType);
			ctl.Attributes.Add("class", strCssClass);
			return ctl;
		}
		
		public static HtmlGenericControl CreateHtmlGenericControlInsideParent(Control ctlParent, string strType) {
			HtmlGenericControl ctl = CreateHtmlGenericControl(strType);
			ctlParent.Controls.Add(ctl);
			return ctl;
		}
		
		public static HtmlGenericControl CreateHtmlGenericControlInsideParent(Control ctlParent, string strType, string strCssClass) {
			HtmlGenericControl ctl = CreateHtmlGenericControl(strType, strCssClass);
			ctlParent.Controls.Add(ctl);
			return ctl;
		}

		public static LinkButton CreateLinkButton() {
			return new LinkButton();
		}

		public static LinkButton CreateLinkButton(string strCssClass) {
			LinkButton lnk = CreateLinkButton();
			lnk.CssClass = strCssClass;
			return lnk;
		}

		public static LinkButton CreateLinkButton(string strCssClass, string strText) {
			LinkButton lnk = CreateLinkButton(strCssClass);
			lnk.Text = strText;
			return lnk;
		}

		public static LinkButton CreateLinkButtonInsideParent(Control ctlParent) {
			LinkButton lnk = CreateLinkButton();
			ctlParent.Controls.Add(lnk);
			return lnk;
		}

		public static LinkButton CreateLinkButtonInsideParent(Control ctlParent, string strCssClass) {
			LinkButton lnk = CreateLinkButton(strCssClass);
			ctlParent.Controls.Add(lnk);
			return lnk;
		}

		public static LinkButton CreateLinkButtonInsideParent(Control ctlParent, string strCssClass, string strText) {
			LinkButton lnk = CreateLinkButton(strCssClass, strText);
			ctlParent.Controls.Add(lnk);
			return lnk;
		}

		public static Label CreateLabel() {
			return new Label();
		}

		public static Label CreateLabel(string strCssClass) {
			Label lbl = CreateLabel();
			lbl.CssClass = strCssClass;
			return lbl;
		}

		public static Label CreateLabel(string strCssClass, string strText) {
			Label lbl = CreateLabel(strCssClass);
			lbl.Text = strText;
			return lbl;
		}

		public static Label CreateLabelInsideParent(Control ctlParent) {
			Label lbl = CreateLabel();
			ctlParent.Controls.Add(lbl);
			return lbl;
		}

		public static Label CreateLabelInsideParent(Control ctlParent, string strCssClass) {
			Label lbl = CreateLabel(strCssClass);
			ctlParent.Controls.Add(lbl);
			return lbl;
		}

		public static Label CreateLabelInsideParent(Control ctlParent, string strCssClass, string strText) {
			Label lbl = CreateLabel(strCssClass, strText);
			ctlParent.Controls.Add(lbl);
			return lbl;
		}

		public static Image CreateImage() {
			return new Image();
		}

		public static Image CreateImage(string strCssClass) {
			Image img = CreateImage();
			img.CssClass = strCssClass;
			return img;
		}

		public static Image CreateImage(string strCssClass, string strImageURL) {
			Image img = CreateImage(strCssClass);
			img.ImageUrl = strImageURL;
			return img;
		}

		public static Image CreateImage(string strCssClass, string strImageURL, int intWidth, int intHeight) {
			Image img = CreateImage(strCssClass, strImageURL);
			img.Width = intWidth;
			img.Height = intHeight;
			return img;
		}

		public static Image CreateImageInsideParent(Control ctlParent) {
			Image img = CreateImage();
			ctlParent.Controls.Add(img);
			return img;
		}

		public static Image CreateImageInsideParent(Control ctlParent, string strCssClass) {
			Image img = CreateImage(strCssClass);
			ctlParent.Controls.Add(img);
			return img;
		}

		public static Image CreateImageInsideParent(Control ctlParent, string strCssClass, string strImageURL) {
			Image img = CreateImage(strCssClass, strImageURL);
			ctlParent.Controls.Add(img);
			return img;
		}

		public static Image CreateImageInsideParent(Control ctlParent, string strCssClass, string strImageURL, int intWidth, int intHeight) {
			Image img = CreateImage(strCssClass, strImageURL, intWidth, intHeight);
			ctlParent.Controls.Add(img);
			return img;
		}

		public static Table CreateTable() {
			Table tbl = new Table();
			tbl.CellSpacing = 0;
			tbl.CellPadding = 0;
			tbl.BorderWidth = 0;
			return tbl;
		}

		public static Table CreateTable(string strCssClass) {
			Table tbl = CreateTable();
			tbl.CssClass = strCssClass;
			return tbl;
		}

		public static Table CreateTableInsideParent(Control ctlParent) {
			Table tbl = CreateTable();
			ctlParent.Controls.Add(tbl);
			return tbl;
		}

		public static Table CreateTableInsideParent(Control ctlParent, string strCssClass) {
			Table tbl = CreateTable(strCssClass);
			ctlParent.Controls.Add(tbl);
			return tbl;
		}
	}
}
