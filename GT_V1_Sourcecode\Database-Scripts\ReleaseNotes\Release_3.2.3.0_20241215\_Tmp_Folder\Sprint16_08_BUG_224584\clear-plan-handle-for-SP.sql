﻿DECLARE @PlanHandle VARBINARY(64);
DECLARE @SpName NVARCHAR(100) = 'dbo.usp_report_Report_123A';

--Find the ID of the cached plan for SP
SELECT  deps.plan_handle AS PlanHandle
INTO #tempPlanHandle
FROM    sys.dm_exec_procedure_stats AS deps
WHERE   deps.object_id = OBJECT_ID(@SpName) AND deps.database_id = DB_ID();

-- Declare a cursor for looping through the table
DECLARE PlanHandleCursor CURSOR FOR
SELECT PlanHandle
FROM #tempPlanHandle;

-- Open the cursor
OPEN PlanHandleCursor;

-- Fetch the first row from the cursor
FETCH NEXT FROM PlanHandleCursor INTO @PlanHandle;

-- Loop through each row
WHILE @@FETCH_STATUS = 0
BEGIN
    -- Perform operations with the current PlanHandle
    PRINT 'Start clear ' + CONVERT(VARCHAR(MAX), @PlanHandle);
	DBCC FREEPROCCACHE(@PlanHandle);
    -- Fetch the next row
    FETCH NEXT FROM PlanHandleCursor INTO @PlanHandle;
END;

-- Close and deallocate the cursor
CLOSE PlanHandleCursor;
DEALLOCATE PlanHandleCursor;
DROP TABLE #tempPlanHandle;