﻿
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].[usp_update_PurchaseOrder]                                               
--*****************************************************************************************                                              
--* SK 05.11.2009:                                              
--* - add IncotermNo                                              
--*                                              
--* SK: 24.08.2009                                              
--* - allow update of ContactNo                                              
--*                                               
--* SK: 30.06.2009                                              
--* - allow update of TermsNo                                              
--*[001]      Vinay           17/01/2014   ESMS Ticket No : 95                                           
--*[002]      Vinay           30/04/2015   ESMS Ticket No : 232                           
--*[003]      Abhinav <PERSON>  08/09/2021   Add logic for supplier Approval History                                         
--*****************************************************************************************                                              
@PurchaseOrderId  int ,                                              
@CurrencyNo    int ,                                              
@ContactNo    int ,                                              
@Buyer     int ,                                              
@ShipViaNo    int    = Null ,                                              
@Account    nvarchar(50) = Null ,                                              
@ExpediteNotes   nvarchar(MAX) = Null ,                                              
@ExpediteDate   datetime  = Null ,                                              
@TotalShipInCost  float   = Null ,                                              
@DivisionNo    int ,                                              
@TaxNo     int ,                                              
@TermsNo    int ,                                              
@Notes     nvarchar(MAX) = Null ,                                              
@Instructions   nvarchar(MAX) = Null ,                                              
@Paid     bit ,                                              
@Confirmed    bit =0,                                              
@ImportCountryNo  int    = Null ,                                              
@FreeOnBoard   nvarchar(25) = Null ,                                              
@StatusNo    int    = Null ,                                              
@Closed     bit ,                                              
@IncotermNo    int    = NULL ,                                              
@UpdatedBy    int    = Null ,                                              
@WarehouseNo   int ,                                              
--[001] code start                                            
@AirWayBillPO nvarchar(50) = NULL,                                             
--[001] code end                                    
@CompanyNo int = NULL,                                       
@SupportTeamMemberNo   int =null,                            
@DivisionHeaderNo int =null,                 
@EPRFlag    bit =0 ,               
@RowsAffected   int = NULL Output,                        
@ISSupplierChanged BIT =0 OUTPUT,                        
@PurchaseOrderNumber INT=0 OUTPUT,                        
@QualityGroupId  INT=0 OUTPUT ,                
@TempLogFlag bit =0 output                  
                                              
AS                                              
-------PurchaseOrder Update ----------------                 
            
            
if @EPRFlag =1              
begin              
 declare                     
  @PrevPurchaseOrderId int = 0,              
    @PrevCurrencyNo int = 0,              
    @PrevContactNo int = 0,              
    @PrevBuyer int = 0,              
    @PrevShipViaNo int = NULL,              
    @PrevAccount nvarchar(50) = NULL,              
    @PrevExpediteNotes nvarchar(MAX) = NULL,              
   @PrevExpediteDate datetime = NULL,              
    @PrevTotalShipInCost float = NULL,              
    @PrevDivisionNo int = 0,              
    @PrevTaxNo int = 0,              
    @PrevTermsNo int = 0,              
    @PrevNotes nvarchar(MAX) = NULL,              
    @PrevInstructions nvarchar(MAX) = NULL,              
  @PrevPaid bit = 0,              
    @PrevConfirmed bit = 0,              
    @PrevImportCountryNo int = NULL,              
    @PrevFreeOnBoard nvarchar(25) = NULL,           
    @PrevStatusNo int = NULL,              
    @PrevClosed bit = 0,              
    @PrevIncotermNo int = NULL,              
    @PrevUpdatedBy int = NULL,              
    @PrevWarehouseNo int = 0,              
    @PrevAirWayBillPO nvarchar(50) = NULL,              
    @PrevCompanyNo int = NULL,              
    @PrevDivisionHeaderNo int = NULL,              
    @PrevMailGroupType int = NULL,          
   @Confirmedval int = NULL,       
    @PrevSupportTeamMemberNo int = NULL,              
 @kPrevPurchaseOrderId int = 0,              
    @kPrevCurrencyNo int = 0,              
    @kPrevContactNo int = 0,              
    @kPrevBuyer int = 0,              
    @kPrevShipViaNo int = NULL,              
    @kPrevAccount nvarchar(50) = NULL,              
    @kPrevExpediteNotes nvarchar(MAX) = NULL,              
   @kPrevExpediteDate datetime = NULL,              
    @kPrevTotalShipInCost float = NULL,              
    @kPrevDivisionNo int = 0,              
    @kPrevTaxNo int = 0,              
    @kPrevTermsNo int = 0,              
    @kPrevNotes nvarchar(MAX) = NULL,              
    @kPrevInstructions nvarchar(MAX) = NULL,              
  @kPrevPaid bit = 0,              
    @kPrevConfirmed bit = 0,              
    @kPrevImportCountryNo int = NULL,              
    @kPrevFreeOnBoard nvarchar(25) = NULL,           
    @kPrevStatusNo int = NULL,              
    @kPrevClosed bit = 0,              
    @kPrevIncotermNo int = NULL,              
    @kPrevUpdatedBy int = NULL,              
    @kPrevWarehouseNo int = 0,              
    @kPrevAirWayBillPO nvarchar(50) = NULL,              
    @kPrevCompanyNo int = NULL,              
    @kPrevDivisionHeaderNo int = NULL,              
    @kPrevMailGroupType int = NULL,          
   @kConfirmedval int = NULL,       
    @kPrevSupportTeamMemberNo int = NULL,         
    @TempFlag     bit =0                  
                  
   select               
     @kPrevPurchaseOrderId = PurchaseOrderId,              
    @kPrevCurrencyNo = CurrencyNo,              
    @kPrevContactNo = ContactNo,              
    @kPrevBuyer = Buyer,              
    @kPrevShipViaNo = ISNULL(ShipViaNo, NULL),              
    @kPrevAccount = ISNULL(Account, NULL),              
    @kPrevExpediteNotes = ISNULL(ExpediteNotes, NULL),              
    @kPrevExpediteDate = ExpediteDate,              
    @kPrevTotalShipInCost = TotalShipInCost,              
    @kPrevDivisionNo = DivisionNo,              
    @kPrevTaxNo = TaxNo,              
    @kPrevTermsNo = TermsNo,              
    @kPrevNotes = ISNULL(Notes, NULL),              
    @kPrevInstructions = ISNULL(Instructions, NULL),              
    @kPrevPaid = Paid,              
    @kPrevConfirmed = Confirmed,              
    @kPrevImportCountryNo = ISNULL(ImportCountryNo, NULL),              
    @kPrevFreeOnBoard = ISNULL(FreeOnBoard, NULL),              
    @kPrevStatusNo = ISNULL(StatusNo, NULL),              
    @kPrevClosed = Closed,              
    @kPrevIncotermNo = ISNULL(IncotermNo, NULL),              
    @kPrevUpdatedBy = ISNULL(UpdatedBy, NULL),              
    @kPrevWarehouseNo = WarehouseNo,              
    @kPrevAirWayBillPO = ISNULL(AirWayBillPO, NULL),              
    @kPrevCompanyNo = ISNULL(CompanyNo, NULL),              
    @kPrevDivisionHeaderNo = ISNULL(DivisionHeaderNo, NULL),              
    --@PrevMailGroupType = ISNULL(MailGroupType, NULL),              
    @kPrevSupportTeamMemberNo = ISNULL(SupportTeamMemberNo, NULL)                
   from tbPurchaseOrder where PurchaseOrderId  = @PurchaseOrderId       
                        
                    
 select               
     @PrevPurchaseOrderId = PurchaseOrderId,              
    @PrevCurrencyNo = CurrencyNo,              
    @PrevContactNo = ContactNo,              
    @PrevBuyer = Buyer,              
    @PrevShipViaNo = ISNULL(ShipViaNo, NULL),              
    @PrevAccount = ISNULL(Account, NULL),              
    @PrevExpediteNotes = ISNULL(ExpediteNotes, NULL),              
    @PrevExpediteDate = ExpediteDate,              
    @PrevTotalShipInCost = TotalShipInCost,              
    @PrevDivisionNo = DivisionNo,              
    @PrevTaxNo = TaxNo,              
    @PrevTermsNo = TermsNo,              
    @PrevNotes = ISNULL(Notes, NULL),              
    @PrevInstructions = ISNULL(Instructions, NULL),              
    @PrevPaid = Paid,              
    @PrevConfirmed = Confirmed,              
    @PrevImportCountryNo = ISNULL(ImportCountryNo, NULL),              
    @PrevFreeOnBoard = ISNULL(FreeOnBoard, NULL),              
    @PrevStatusNo = ISNULL(StatusNo, NULL),              
    @PrevClosed = Closed,              
    @PrevIncotermNo = ISNULL(IncotermNo, NULL),              
    @PrevUpdatedBy = ISNULL(UpdatedBy, NULL),              
    @PrevWarehouseNo = WarehouseNo,              
    @PrevAirWayBillPO = ISNULL(AirWayBillPO, NULL),              
    @PrevCompanyNo = ISNULL(CompanyNo, NULL),              
    @PrevDivisionHeaderNo = ISNULL(DivisionHeaderNo, NULL),              
    --@PrevMailGroupType = ISNULL(MailGroupType, NULL),              
    @PrevSupportTeamMemberNo = ISNULL(SupportTeamMemberNo, NULL)                
   from tbPurchaseOrder where PurchaseOrderId  = @PurchaseOrderId       
       
   
    
    
     IF (ISNULL(@PrevCurrencyNo,'') = ISNULL(@CurrencyNo,''))                   
    BEGIN set  @PrevCurrencyNo = null  END ELSE   BEGIN  set  @PrevCurrencyNo = @CurrencyNo set  @TempFlag =1   END;                    
  IF (ISNULL(@PrevContactNo,'') = ISNULL(@ContactNo,''))                  
    BEGIN set  @PrevContactNo = null  END ELSE   BEGIN  set  @PrevContactNo = @ContactNo set  @TempFlag =1   END;                    
   IF (ISNULL(@PrevBuyer,'') = ISNULL(@Buyer,''))                 
    BEGIN set  @PrevBuyer = null  END ELSE   BEGIN  set @PrevBuyer = @Buyer set  @TempFlag =1  END;                    
   IF (ISNULL(@PrevShipViaNo,'') = ISNULL(@ShipViaNo,''))                  
    BEGIN set  @PrevShipViaNo = null  END ELSE   BEGIN  set  @PrevShipViaNo = @ShipViaNo set  @TempFlag =1  END;                    
 IF (ISNULL(@PrevAccount,'') = ISNULL(@Account,''))                    
    BEGIN set  @PrevAccount = null  END ELSE   BEGIN  set @PrevAccount = @Account set  @TempFlag =1  END;                    
  IF (ISNULL(@PrevExpediteDate,'') = ISNULL(@ExpediteDate,''))                  
    BEGIN set  @PrevExpediteDate = null  END ELSE   BEGIN  set   @PrevExpediteDate = @ExpediteDate set  @TempFlag =1   END;                    
  IF (ISNULL(@PrevTotalShipInCost,'') = ISNULL(@TotalShipInCost,''))                  
    BEGIN set  @PrevTotalShipInCost = null  END ELSE   BEGIN  set @PrevTotalShipInCost = @TotalShipInCost set  @TempFlag =1  END;                    
 IF (ISNULL(@PrevDivisionNo,'') = ISNULL(@DivisionNo,''))                   
    BEGIN set  @PrevDivisionNo = null  END ELSE   BEGIN  set  @PrevDivisionNo = @DivisionNo set  @TempFlag =1   END;                
 IF (ISNULL(@PrevTaxNo,'') = ISNULL(@TaxNo,''))                   
    BEGIN set  @PrevTaxNo = null  END ELSE   BEGIN  set  @PrevTaxNo = @TaxNo set  @TempFlag =1  END;                    
 IF (ISNULL(@PrevTermsNo,'') = ISNULL(@TermsNo,''))                   
    BEGIN set  @PrevTermsNo = null  END ELSE   BEGIN  set  @PrevTermsNo= @TermsNo set  @TempFlag =1 END;                    
   IF (ISNULL(@PrevNotes,'') = ISNULL(@Notes,''))                
    BEGIN set  @PrevNotes = null  END ELSE   BEGIN  set @PrevNotes = @Notes set  @TempFlag =1   END;                    
 IF (ISNULL(@PrevPaid,'') = ISNULL(@Paid,''))                  
 BEGIN set  @PrevPaid = null  END ELSE   BEGIN  set  @PrevPaid = @Paid set  @TempFlag =1   END;         
       
IF (ISNULL(@PrevConfirmed,'') = ISNULL(@Confirmed,''))              
    BEGIN set  @Confirmedval = 2  END ELSE   BEGIN  set  @Confirmedval = @Confirmed set  @TempFlag =1   END;         
       
  IF (ISNULL(@PrevImportCountryNo,'') = ISNULL(@ImportCountryNo,''))                 
    BEGIN set  @PrevImportCountryNo = null  END ELSE   BEGIN  set  @PrevImportCountryNo = @ImportCountryNo set  @TempFlag =1   END;                    
 IF (ISNULL(@PrevWarehouseNo,'') = ISNULL(@WarehouseNo,''))                   
    BEGIN set  @PrevWarehouseNo = null  END ELSE   BEGIN  set  @PrevWarehouseNo = @WarehouseNo set  @TempFlag =1  END;                    
 IF (ISNULL(@PrevIncotermNo,'') = ISNULL(@IncotermNo,''))                    
    BEGIN set  @PrevIncotermNo = null  END ELSE   BEGIN  set  @PrevIncotermNo = @IncotermNo set  @TempFlag =1   END;                    
   IF (ISNULL(@PrevAirWayBillPO,'') = ISNULL(@AirWayBillPO,''))                 
    BEGIN set  @PrevAirWayBillPO = null  END ELSE   BEGIN  set @PrevAirWayBillPO = @AirWayBillPO  set  @TempFlag =1  END;                    
IF (ISNULL(@PrevCompanyNo,'') = ISNULL(@PrevCompanyNo,''))                   
    BEGIN set  @PrevCompanyNo = null  END ELSE   BEGIN  set  @PrevCompanyNo = @CompanyNo set  @TempFlag =1   END;                    
 IF (ISNULL(@PrevSupportTeamMemberNo,'') = ISNULL(@SupportTeamMemberNo,''))                   
    BEGIN set  @PrevSupportTeamMemberNo = null  END ELSE   BEGIN  set  @PrevSupportTeamMemberNo = @SupportTeamMemberNo set  @TempFlag =1  END;                    
 IF (ISNULL(@PrevDivisionHeaderNo,'') = ISNULL(@DivisionHeaderNo,''))                  
    BEGIN set  @PrevDivisionHeaderNo = null  END ELSE   BEGIN  set  @PrevDivisionHeaderNo = @DivisionHeaderNo  set  @TempFlag =1      END;           
  IF (ISNULL( @PrevFreeOnBoard,'') = ISNULL(@FreeOnBoard,''))                  
    BEGIN set   @PrevFreeOnBoard = null  END ELSE   BEGIN  set   @PrevFreeOnBoard = @FreeOnBoard  set  @TempFlag =1      END;       
 IF (ISNULL( @PrevExpediteNotes,'') = ISNULL(@ExpediteNotes,''))                  
    BEGIN set   @PrevExpediteNotes = null  END ELSE   BEGIN  set   @PrevExpediteNotes = @ExpediteNotes  set  @TempFlag =1      END;       
 IF (ISNULL( @PrevInstructions,'') = ISNULL(@Instructions,''))                  
    BEGIN set   @PrevInstructions = null  END ELSE   BEGIN  set   @PrevInstructions = @Instructions  set  @TempFlag =1      END;       
      
  set @TempLogFlag =@TempFlag                  
 if @TempLogFlag=1                 
 begin     
   INSERT INTO tbprevPurchaseOrderLog (                    
    [PurchaseOrderId],                    
    [CurrencyNo],                    
    [ContactNo],                    
    [Buyer],                    
    [ShipViaNo],                    
    [ExpediteDate],                    
    [Account],                    
    [TotalShipInCost],                    
    [DivisionNo],                    
    [TaxNo],                    
    [TermsNo],                    
    [Notes],                    
    [Instructions],                    
    [UpdatedBy],                    
    [Paid],                    
    [Confirmed],                    
    [ImportCountryNo],                    
    [StatusNo],                    
    [Closed],       
 FreeOnBoard ,      
    [IncotermNo],                    
    [WarehouseNo],                    
    [AirWayBillPO],                    
    [CompanyNo],                    
    [DivisionHeaderNo],                    
    [SupportTeamMemberNo],      
 [Confirmedval],      
 ExpediteNotes      
)                    
VALUES (                    
   @PurchaseOrderId ,                    
    @kPrevCurrencyNo,                        @kPrevContactNo,                    
    @kPrevBuyer,                    
    @kPrevShipViaNo,                    
    @kPrevExpediteDate,                    
    @kPrevAccount,                    
    @kPrevTotalShipInCost,                    
    @kPrevDivisionNo,                    
    @kPrevTaxNo,             
    @kPrevTermsNo,                    
    @kPrevNotes,                    
    @kPrevInstructions,                    
    @kPrevUpdatedBy,                    
    @kPrevPaid,                    
    @kPrevConfirmed,                    
    @kPrevImportCountryNo,                    
    @kPrevStatusNo,                    
    @kPrevClosed,       
 @kPrevFreeOnBoard,      
    @kPrevIncotermNo,                    
    @kPrevWarehouseNo,                    
    @kPrevAirWayBillPO,                    
    @kPrevCompanyNo,                    
    @kPrevDivisionHeaderNo,                    
    @kPrevSupportTeamMemberNo,      
 @kConfirmedval,      
 @kPrevExpediteNotes      
);                            
    
  INSERT INTO tbPurchaseOrderLog (                    
    [PurchaseOrderId],                    
    [CurrencyNo],                    
    [ContactNo],                    
    [Buyer],                    
    [ShipViaNo],                    
    [ExpediteDate],                    
    [Account],                    
    [TotalShipInCost],                    
    [DivisionNo],                    
    [TaxNo],                    
    [TermsNo],                    
    [Notes],                    
    [Instructions],                    
    [UpdatedBy],                    
    [Paid],                    
    [Confirmed],                    
    [ImportCountryNo],                    
    [StatusNo],                    
    [Closed],       
 FreeOnBoard ,      
    [IncotermNo],                    
    [WarehouseNo],                    
    [AirWayBillPO],                    
    [CompanyNo],                    
    [DivisionHeaderNo],                    
    [SupportTeamMemberNo],      
 [Confirmedval],      
 ExpediteNotes      
)                    
VALUES (                    
   @PurchaseOrderId ,                    
    @PrevCurrencyNo,                    
    @PrevContactNo,                    
    @PrevBuyer,                    
    @PrevShipViaNo,                    
    @PrevExpediteDate,                    
    @PrevAccount,                    
    @PrevTotalShipInCost,                    
    @PrevDivisionNo,                    
    @PrevTaxNo,                    
    @PrevTermsNo,                    
    @PrevNotes,                    
    @PrevInstructions,                    
    @PrevUpdatedBy,                    
    @PrevPaid,                    
    @PrevConfirmed,                    
    @PrevImportCountryNo,                    
    @PrevStatusNo,                    
    @PrevClosed,       
 @PrevFreeOnBoard,      
    @PrevIncotermNo,                    
    @PrevWarehouseNo,                    
    @PrevAirWayBillPO,                    
    @PrevCompanyNo,                    
    @PrevDivisionHeaderNo,                    
    @PrevSupportTeamMemberNo,      
 @Confirmedval,      
 @PrevExpediteNotes      
);                    
    end;                 
 end;              
          
else          
begin          
set @TempLogFlag=0          
end          
  DECLARE @CurCompanyNo int                          
  DECLARE @ClientId INT                         
  DECLARE @SupplierApprovalNo INT                           
  SELECT @CurCompanyNo = CompanyNo,@PurchaseOrderNumber=PurchaseOrderNumber,@ClientId=ClientNo FROM tbPurchaseOrder WHERE PurchaseOrderId = @PurchaseOrderId                                
  select @QualityGroupId=MailGroupId from tbMailGroup where Name='Quality Approval' and ClientNo=@ClientId                              
          
  IF @CurCompanyNo <> @CompanyNo                                
  BEGIN                                
    UPDATE tbPurchaseOrder SET ApprovedBy = NULL,DateApproved = NULL                                 
    WHERE  PurchaseOrderId  = @PurchaseOrderId                          
                         
-----Add block for PO supplier approval history---                        
Insert INTO tbPO_PreviousSupplierApprovalHistory                        
(                        
  PurchaseOrderNo                        
, CompanyNo                        
, QualityApproveStatusId                        
, QualityUpdatedBy                        
, QualityDLUP                        
, LineManagerApproveStatusId                        
, LineManagerUpdatedBy                        
, LineManagerDLUP                         
)                        
select                         
  PurchaseOrderNO                        
, CompanyNo                        
, QualityApproveStatusId                        
, QualityUpdatedBy                        
, QualityDLUP                        
, LineManagerApproveStatusId                        
, LineManagerUpdatedBy                        
, LineManagerDLUP                 
FROM tbPO_SupplierApprovalStatus WHERE PurchaseOrderNO=@PurchaseOrderId                        
                        
DELETE FROM tbPO_SupplierApprovalStatus WHERE PurchaseOrderNO=@PurchaseOrderId                        
DELETE FROM tbPOSupplierApprovalDetails WHERE PurchaseOrderNo=@PurchaseOrderId                        
INSERT INTO tbPO_SupplierApprovalStatus(PurchaseOrderNO,CompanyNo) VALUES(@PurchaseOrderId,@CompanyNo)                      
SET @SupplierApprovalNo=@@IDENTITY;                      
                      
INSERT INTO tbPOSupplierApprovalDetails(SupplierApprovalNo,PurchaseOrderNo)VALUES(@SupplierApprovalNo,@PurchaseOrderId)                        
SET @ISSupplierChanged=0                    
  END                          
  ELSE                        
  BEGIN                        
  SET @ISSupplierChanged=0                        
 END                         
---------------END-------------------------------                                    
                             
                                   
UPDATE dbo.tbPurchaseOrder                                              
SET                                     
 CurrencyNo   = @CurrencyNo                                               
 , ContactNo   = @ContactNo                                               
 , Buyer    = @Buyer                                               
 , ShipViaNo   = @ShipViaNo                                        
 , Account    = @Account                                         
 --[002] code start                                              
 --, ExpediteNotes  = @ExpediteNotes                                          
 --[002] code end                                             
 , ExpediteDate  = @ExpediteDate                                               
 , TotalShipInCost  = @TotalShipInCost                                               
 , DivisionNo   = @DivisionNo                                              
 , TaxNo    = @TaxNo                                               
 , TermsNo    = @TermsNo                                               
 , Notes    = @Notes                                               
 , Instructions  = @Instructions                                              
 , Paid    = @Paid                                              
 , Confirmed   = @Confirmed                                               
 , ImportCountryNo  = @ImportCountryNo                                               
 , FreeOnBoard   = @FreeOnBoard                                               
 , StatusNo   = @StatusNo                                               
 , Closed    = @Closed                                              
 , WarehouseNo   = @WarehouseNo                                              
 , IncotermNo  = @IncotermNo                                              
 , UpdatedBy   = @UpdatedBy                                              
 , DLUP    = current_timestamp                                          
 --[001] code start                                            
 , AirWayBillPO=@AirWayBillPO                                     
 , CompanyNo = @CompanyNo                                 
 ,SupportTeamMemberNo=@SupportTeamMemberNo                            
 ,DivisionHeaderNo=@DivisionHeaderNo                                        
 --[001] code end                                            
WHERE PurchaseOrderId  = @PurchaseOrderId                                      
                                
                                
                                    
                                    
IF EXISTS (SELECT * FROM dbo.tbInternalPurchaseOrder WHERE PurchaseOrderNo  = @PurchaseOrderId)                                
BEGIN                                
 -------InternalPurchaseOrder Update ----------------                                   
                                 
                                 
                                   
                                
                                
                                  
                                
   DECLARE @GlobalCountryNo int                                 
   DECLARE @IPOClientNo INT                                 
   DECLARE @IPOImportCountryNo int                                 
   SELECT @GlobalCountryNo = cn.GlobalCountryNo FROM  dbo.tbPurchaseOrder po                                                               
   LEFT JOIN dbo.tbCountry cn ON cn.CountryId = po.ImportCountryNo                                   
   WHERE     po.PurchaseOrderId  = @PurchaseOrderId                                   
                                      
   SELECT @IPOClientNo = ClientNo FROM dbo.tbInternalPurchaseOrder WHERE PurchaseOrderNo  = @PurchaseOrderId                                
                                
   SELECT top 1  @IPOImportCountryNo = CountryId FROM  tbCountry WHERE                                  
   GlobalCountryNo = @GlobalCountryNo and ClientNo= @IPOClientNo and Inactive = 0                                 
                                     
 UPDATE dbo.tbInternalPurchaseOrder                                              
 SET                           
    --TotalShipInCost  = @TotalShipInCost                                               
  --, DivisionNo   = @DivisionNo                                              
  --, TaxNo    = @TaxNo                                                    
    Notes    = @Notes                                               
  , Instructions  = @Instructions                                               
  , Paid    = @Paid                                              
  , Confirmed   = @Confirmed                                
  , ImportCountryNo  = @IPOImportCountryNo                                               
  , FreeOnBoard   = @FreeOnBoard                                               
  , StatusNo   = @StatusNo                                               
  , Closed    = @Closed                                                     
  , UpdatedBy  = @UpdatedBy                                              
  , DLUP    = current_timestamp                                   
  , WarehouseNo   = @WarehouseNo                                
  ,SupportTeamMemberNo=@SupportTeamMemberNo                                  
  --[001] code start                                                  
  --[001] code end                                            
  WHERE PurchaseOrderNo  = @PurchaseOrderId                                     
                                 
 END                                   
                                      
 --[002] code start                                         
 IF RTRIM(LTRIM(ISNULL(@ExpediteNotes,''))) <> ''                                      
 BEGIN                                         
     INSERT INTO tbPurchaseOrderExpediteNotes                                         
     (                                        
     ExpediteNotes                                        
, DLUP                                        
   , UpdatedBy                  
   ,PurchaseOrderNo                                        
     )                         
     VALUES                                        
     (                                        
     @ExpediteNotes                                        
, current_timestamp                                        
    , @UpdatedBy                                        
    ,@PurchaseOrderId                               
                                        
      )                                         
 END                                          
  --[002] code end                                           
SELECT @RowsAffected  = @@ROWCOUNT 
GO


