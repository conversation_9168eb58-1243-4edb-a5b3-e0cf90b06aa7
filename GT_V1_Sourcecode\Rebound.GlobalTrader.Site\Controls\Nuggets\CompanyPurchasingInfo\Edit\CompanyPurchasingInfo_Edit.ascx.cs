//-----------------------------------------------------------------------------------------
// RP 17.05.2010:
// - [168]: Add new default setting for rating
//-----------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site;

namespace Rebound.GlobalTrader.Site.Controls.Forms {
	public partial class CompanyPurchasingInfo_Edit : Base {

		#region Overrides

		/// <summary>
		/// OnInit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
			TitleText = Functions.GetGlobalResource("FormTitles", "CompanyPurchasingInfo_Edit");
			AddScriptReference("Controls.Nuggets.CompanyPurchasingInfo.Edit.CompanyPurchasingInfo_Edit.js");
		}

		/// <summary>
		/// OnPreRender
		/// </summary>
		/// <param name="e"></param>
		protected override void OnPreRender(EventArgs e) {
			SetupScriptDescriptors();
			base.OnPreRender(e);
		}

		#endregion

		/// <summary>
		/// Setup Script Descriptors
		/// </summary>
		private void SetupScriptDescriptors() {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Forms.CompanyPurchasingInfo_Edit", ctlDesignBase.ClientID);
			_scScriptControlDescriptor.AddProperty("intCompanyID", _objQSManager.CompanyID);
			_scScriptControlDescriptor.AddProperty("intDefaultRating", SettingsManager.GetSetting_Int(BLL.SettingItem.List.DefaultPORating));
            _scScriptControlDescriptor.AddProperty("intMailGroupNo", BLL.MailGroup.GetQualityMailGroupNo(SessionManager.ClientID));
		}

	}
}