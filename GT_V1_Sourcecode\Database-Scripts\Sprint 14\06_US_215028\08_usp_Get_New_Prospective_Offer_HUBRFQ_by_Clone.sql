﻿SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
/*   
===========================================================================================  
TASK			UPDATED BY			DATE			ACTION		DESCRIPTION  
[US-213152]		Trung Pham Van		10-Oct-2024		CREATE		Get New Prospective Offer HUBRFQ by Clone
[US-213152]		Trung Pham Van		14-Oct-2024		UPDATE		Refactor code to check new HUBRFQ is cloned or not
[US-215028]		Trung Pham Van		05-Nov-2024		UPDATE		Add CustomerRequirementSourceId condition 
[US-215028]		Trung Pham Van		08-Nov-2024		UPDATE		Check New/RPQ status 
===========================================================================================  
*/
CREATE OR ALTER PROCEDURE [dbo].[usp_Get_New_Prospective_Offer_HUBRFQ_by_Clone]
	@ProspectiveOfferId INT,
	@CustomerRequirementId INT,
	@ExistingBomId INT,
	@BOMId INT OUTPUT
AS
BEGIN
	SELECT TOP 1 @BOMId = BOMId
	FROM tbBOM bom
	JOIN tbCustomerRequirement cr ON cr.BOMNo = bom.BOMId
	WHERE CustomerRequirementSourceId = @CustomerRequirementId
	AND ProspectiveOfferNo = @ProspectiveOfferId
	AND IsFromProspectiveOffer = 1
	AND bom.Status IN (1,2,3)
END
