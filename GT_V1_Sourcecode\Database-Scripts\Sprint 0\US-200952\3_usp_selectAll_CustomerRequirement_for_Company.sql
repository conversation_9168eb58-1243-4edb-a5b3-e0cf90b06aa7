IF OBJECT_ID('usp_selectAll_CustomerRequirement_for_Company', 'P') IS NOT NULL
DROP PROC usp_selectAll_CustomerRequirement_for_Company
GO
/*
-- ==========================================================================================
-- TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION                                    
--  				     				23-07-2009   	Create			new proc
-- US-200952		Phuc.HoangDinh		24-04-2024		Update			Update for US-200952
-- ==========================================================================================
*/
CREATE PROCEDURE [dbo].[usp_selectAll_CustomerRequirement_for_Company]      
    @CompanyId int      
  , @IncludeClosed bit = 0      
AS       
    SELECT   *      
    FROM    dbo.vwCustomerRequirementForCompany      
    WHERE   CompanyNo = @CompanyId
			AND BOM <> 1
            AND Closed IN (0, @IncludeClosed)      
            AND (OriginalCustomerRequirementNo IS NULL      
                 OR OriginalCustomerRequirementNo = 0)      
            AND ReceivedDate>=DATEADD(MONTH,-24,GETDATE())        
    ORDER BY ReceivedDate DESC      
          , CustomerRequirementNumber 
GO