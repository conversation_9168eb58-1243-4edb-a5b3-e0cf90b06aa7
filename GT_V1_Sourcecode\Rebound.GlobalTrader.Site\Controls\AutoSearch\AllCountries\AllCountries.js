Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.AutoSearch");Rebound.GlobalTrader.Site.Controls.AutoSearch.AllCountries=function(n){Rebound.GlobalTrader.Site.Controls.AutoSearch.AllCountries.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.AutoSearch.AllCountries.prototype={get_blnIncludeSelected:function(){return this._blnIncludeSelected},set_blnIncludeSelected:function(n){this._blnIncludeSelected!==n&&(this._blnIncludeSelected=n)},get_intGlobalLoginClientNo:function(){return this._intGlobalLoginClientNo},set_intGlobalLoginClientNo:function(n){this._intGlobalLoginClientNo!==n&&(this._intGlobalLoginClientNo=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.AutoSearch.AllCountries.callBaseMethod(this,"initialize");this.addDataReturnedEvent(Function.createDelegate(this,this.dataReturned));this.setupDataObject("AllCountries")},dispose:function(){this.isDisposed||Rebound.GlobalTrader.Site.Controls.AutoSearch.AllCountries.callBaseMethod(this,"dispose")},dataReturned:function(){var t,r,n,i;if(this._result&&this._result.TotalRecords>0)for(t=0,r=this._result.Results.length;t<r;t++)n=this._result.Results[t],i="",i=this._enmResultsActionType==$R_ENUM$AutoSearchResultsActionType.Navigate?$R_FN.setCleanTextValue(n.Name):$R_FN.setCleanTextValue(n.Name),this.addResultItem(i,$R_FN.setCleanTextValue(n.Name),n.ID),i=null,n=null}};Rebound.GlobalTrader.Site.Controls.AutoSearch.AllCountries.registerClass("Rebound.GlobalTrader.Site.Controls.AutoSearch.AllCountries",Rebound.GlobalTrader.Site.Controls.AutoSearch.Base,Sys.IDisposable);