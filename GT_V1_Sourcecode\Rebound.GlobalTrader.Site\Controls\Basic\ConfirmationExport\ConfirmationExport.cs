using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Collections;

namespace Rebound.GlobalTrader.Site.Controls {
	[DefaultProperty("")]
	[ToolboxData("<{0}:ConfirmationExport runat=server></{0}:ConfirmationExport>")]
	public class ConfirmationExport : Panel, IScriptControl, INamingContainer {

		#region Locals

		protected ScriptManager _sm;
		protected IconButton _ibtnYes;
		protected IconButton _ibtnNo;
        protected IconButton _ibtnCancel;


        #endregion

        #region Properties

        #endregion

        #region Overrides

        /// <summary>
        /// OnInit
        /// </summary>
        /// <param name="e"></param>
        protected override void OnInit(EventArgs e) {
			EnsureChildControls();
			base.OnInit(e);
		}

		/// <summary>
		/// create controls
		/// </summary>
		protected override void CreateChildControls() {

			CssClass = "confirmation";

			//'Yes' button
			_ibtnYes = new IconButton();
			_ibtnYes.ID = "ibtnYes";
			_ibtnYes.IconButtonMode = IconButton.IconButtonModeList.Hyperlink;
			_ibtnYes.IconGroup = IconButton.IconGroupList.FormBody;
			_ibtnYes.IconTitleResource = "Approve";
			Controls.Add(_ibtnYes);

			//'No' button
			_ibtnNo = new IconButton();
			_ibtnNo.ID = "ibtnNo";
			_ibtnNo.IconButtonMode = IconButton.IconButtonModeList.Hyperlink;
			_ibtnNo.IconGroup = IconButton.IconGroupList.FormBody;
			_ibtnNo.IconTitleResource = "Reject";
			Controls.Add(_ibtnNo);

            _ibtnCancel = new IconButton();
            _ibtnCancel.ID = "ibtnCancel";
            _ibtnCancel.IconButtonMode = IconButton.IconButtonModeList.Hyperlink;
            _ibtnCancel.IconGroup = IconButton.IconGroupList.FormBody;
            _ibtnCancel.IconTitleResource = "Cancel";
            Controls.Add(_ibtnCancel);
            base.CreateChildControls();
		}

		/// <summary>
		/// OnPreRender
		/// </summary>
		/// <param name="e"></param>
		protected override void OnPreRender(EventArgs e) {
			if (!this.DesignMode) {
				_sm = ScriptManager.GetCurrent(Page);
				if (_sm == null) throw new HttpException("A ScriptManager control must exist on the current page.");
				_sm.RegisterScriptControl(this);
			}
			base.OnPreRender(e);
		}

		/// <summary>
		/// Render
		/// </summary>
		/// <param name="writer"></param>
		protected override void Render(HtmlTextWriter writer) {
			if (!this.DesignMode) _sm.RegisterScriptDescriptors(this);
			base.Render(writer);
		}

		#endregion

		#region IScriptControl Members

		protected virtual IEnumerable<ScriptReference> GetScriptReferences() {
			bool blnDebug = false;
#if (DEBUG)
			blnDebug = true;
#endif
			return new ScriptReference[] { Functions.GetScriptReference(blnDebug, "Rebound.GlobalTrader.Site", "Controls.Basic.ConfirmationExport.ConfirmationExport", true) };
		}

		protected virtual IEnumerable<ScriptDescriptor> GetScriptDescriptors() {
			ScriptControlDescriptor descriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.ConfirmationExport", this.ClientID);
			descriptor.AddProperty("dummy", 0);
			descriptor.AddElementProperty("ibtnYes", _ibtnYes.ClientID);
			descriptor.AddElementProperty("ibtnNo", _ibtnNo.ClientID);
            descriptor.AddElementProperty("ibtnCancel", _ibtnCancel.ClientID);
            return new ScriptDescriptor[] { descriptor };
		}

		IEnumerable<ScriptReference> IScriptControl.GetScriptReferences() { return GetScriptReferences(); }

		IEnumerable<ScriptDescriptor> IScriptControl.GetScriptDescriptors() { return GetScriptDescriptors(); }

		#endregion

	}
}
