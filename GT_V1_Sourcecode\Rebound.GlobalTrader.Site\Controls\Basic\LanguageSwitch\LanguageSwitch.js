Type.registerNamespace("Rebound.GlobalTrader.Site.Controls");Rebound.GlobalTrader.Site.Controls.LanguageSwitch=function(n){Rebound.GlobalTrader.Site.Controls.LanguageSwitch.initializeBase(this,[n]);this._aryLinkIDs=[];this._blnSelected=!1;this._intHideTimeoutID=-1;this._strCurrentCulture=""};Rebound.GlobalTrader.Site.Controls.LanguageSwitch.prototype={get_aryLinkIDs:function(){return this._aryLinkIDs},set_aryLinkIDs:function(n){this._aryLinkIDs!==n&&(this._aryLinkIDs=n)},get_strCurrentCulture:function(){return this._strCurrentCulture},set_strCurrentCulture:function(n){this._strCurrentCulture!==n&&(this._strCurrentCulture=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.LanguageSwitch.callBaseMethod(this,"initialize");this._aryLinkIDs.length>1&&($addHandler(this.get_element(),"mouseover",Function.createDelegate(this,this.onMouseOver)),$addHandler(this.get_element(),"mouseout",Function.createDelegate(this,this.onMouseOut)),this.setupLinks())},dispose:function(){var n,i,t;if(!this.isDisposed){for(this.get_element()&&$clearHandlers(this.get_element()),n=0,i=this._aryLinkIDs.length;n<i;n++)t=$get(String.format("{0}_hyp",this._aryLinkIDs[n])),t&&$clearHandlers(t),t=null;this._aryLinkIDs=null;this._blnSelected=null;this._intHideTimeoutID=null;this._strCurrentCulture=null;Rebound.GlobalTrader.Site.Controls.LanguageSwitch.callBaseMethod(this,"dispose");this.isDisposed=!0}},setupLinks:function(){for(var t,n=0,i=this._aryLinkIDs.length;n<i;n++)t=$get(String.format("{0}_hyp",this._aryLinkIDs[n])),$addHandler(t,"click",Function.createDelegate(this,this.onLinkClick)),t=null},onMouseOver:function(){clearTimeout(this._intHideTimeoutID);Sys.UI.DomElement.addCssClass(this.get_element(),"languageSelectHover");this.setAllLinksVisible(!0)},onMouseOut:function(){clearTimeout(this._intHideTimeoutID);var n=String.format("$find('{0}').completeMouseOut()",this.get_element().id);this._intHideTimeoutID=setTimeout(n,100)},completeMouseOut:function(){Sys.UI.DomElement.removeCssClass(this.get_element(),"languageSelectHover");this.setAllLinksVisible(!1)},setAllLinksVisible:function(n){for(var t,i=0,r=this._aryLinkIDs.length;i<r;i++)t=$get(this._aryLinkIDs[i]),t._blnSelected||(n?Sys.UI.DomElement.addCssClass(t,"langSelect"):Sys.UI.DomElement.removeCssClass(t,"langSelect")),t=null},updateLanguageError:function(){},updateLanguageComplete:function(){location.reload(!0)},onLinkClick:function(n){this._strCurrentCulture!=n.target.getAttribute("bgt_culture")&&Rebound.GlobalTrader.Site.WebServices.SetCulture(n.target.getAttribute("bgt_culture"),Function.createDelegate(this,this.updateLanguageComplete),Function.createDelegate(this,this.updateLanguageError))}};Rebound.GlobalTrader.Site.Controls.LanguageSwitch.registerClass("Rebound.GlobalTrader.Site.Controls.LanguageSwitch",Sys.UI.Control,Sys.IDisposable);