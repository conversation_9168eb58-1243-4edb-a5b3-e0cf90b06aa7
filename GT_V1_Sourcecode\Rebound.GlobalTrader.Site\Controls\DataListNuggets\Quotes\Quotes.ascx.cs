using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.DataListNuggets {
	public partial class Quotes : Base {

		#region Properties
		private bool _IsGSA = false;
		public bool IsGSA
		{
			get { return _IsGSA; }
			set { _IsGSA = value; }
		}
		#endregion
		protected IconButton _ibtnViewTask;

		#region Overrides

		/// <summary>
		/// OnInit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			IsGSA = SessionManager.IsGSA.Value;
			SetDataListNuggetType("Quotes");
			base.OnInit(e);
			WireUpControls();
			TitleText = Functions.GetGlobalResource("Nuggets", "Quotes");
			AddScriptReference("Controls.DataListNuggets.Quotes.Quotes.js");
			SetupTable();
		}

		protected override void OnLoad(EventArgs e) {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.DataListNuggets.Quotes", ctlDesignBase.ClientID);
            _scScriptControlDescriptor.AddProperty("intSalesPersonID", _objQSManager.SalesPersonID > 0 ? _objQSManager.SalesPersonID : 0);
			_scScriptControlDescriptor.AddProperty("IsGSA", _IsGSA);
			_scScriptControlDescriptor.AddElementProperty("ibtnViewTask", _ibtnViewTask.ClientID);

			base.OnLoad(e);
		}

		protected override void RenderAdditionalState() {
			string strViewLevel = this.GetSavedStateValue("ViewLevel");
			if (!string.IsNullOrEmpty(strViewLevel)) {
				((Pages.Content)Page).CurrentTab = Convert.ToInt32(strViewLevel);
				_enmViewLevel = (ViewLevelList)Convert.ToInt32(strViewLevel);
				this.OnAskPageToChangeTab();
			}
			base.RenderAdditionalState();
		}

		#endregion

		private void SetupTable() {
			_tbl.AllowSelection = false;
			_tbl.SortColumnDirection = Rebound.GlobalTrader.Site.Enumerations.SortColumnDirection.DESC;
			_tbl.Columns.Add(new FlexiDataColumn("Number", Unit.Pixel(100), true));
			_tbl.Columns.Add(new FlexiDataColumn("PartNo", WidthManager.GetWidth(WidthManager.ColumnWidth.PartNo), true));
			_tbl.Columns.Add(new FlexiDataColumn("Price", WidthManager.GetWidth(WidthManager.ColumnWidth.CurrencyValue), true));
			_tbl.Columns.Add(new FlexiDataColumn("Quantity", WidthManager.GetWidth(WidthManager.ColumnWidth.Quantity), true));
			_tbl.Columns.Add(new FlexiDataColumn("Company", "Contact", Unit.Empty, true));
			_tbl.Columns.Add(new FlexiDataColumn("DateQuoted", WidthManager.GetWidth(WidthManager.ColumnWidth.Date), true));
			_tbl.Columns.Add(new FlexiDataColumn("QuoteOfferedDate", WidthManager.GetWidth(WidthManager.ColumnWidth.Date), true));
			_tbl.Columns.Add(new FlexiDataColumn("Salesman","Status", WidthManager.GetWidth(WidthManager.ColumnWidth.ContactName), true));
            _tbl.Columns.Add(new FlexiDataColumn("TotalValue", "TotalInBase", WidthManager.GetWidth(WidthManager.ColumnWidth.CurrencyValue), true));
            _tbl.Columns.Add(new FlexiDataColumn("Profit", WidthManager.GetWidth(WidthManager.ColumnWidth.CurrencyValue), true));
			_tbl.Columns.Add(new FlexiDataColumn("ToDoList", WidthManager.GetWidth(WidthManager.ColumnWidth.DateAndTime), false));
		}

		private void WireUpControls()
        {
			_ibtnViewTask = (IconButton)FindIconButton("ibtnViewTask");
		}

	}
}