﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

IF OBJECT_ID('dbo.usp_Import_PriceQuoteImport', 'P') IS NOT NULL
BEGIN
    DROP PROCEDURE dbo.usp_Import_PriceQuoteImport
END
GO

/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-201934]		An.TranTan			06-May-2024		Update			Change the comparison logic between import part and requirement part.
[US-205174]		An.TranTan			28-Jun-2024		Update			Specific company no and customer requirement using SelectedClientId from import data
[US-205174]		An.TranTan			02-Jul-2024		Update			Using CustomerRequirementId instead of CustomerRequirementNumber to prevent insert unwanted data
[US-205174]		An.TranTan			05-Jul-2024		Update			Remove logic check supplier name within client, allow get from all clients
[BUG-208825]	An.TranTan			16-Jul-2024		Update			Update new behavior:
																	- Remove logic update purchase request line detail, allow only insert new request
																	- Insert PurchaseRequestLine and PurchaseRequestLineDetail in this SP
																	(remove sp usp_insert_PurchaseRequestLine_PriceQuote)
[BUG-208826]	An.TranTan			17-Jul-2024		Update			Remove check part no and allow import price for requirement has multiple part no
===========================================================================================
*/
CREATE PROCEDURE [dbo].[usp_Import_PriceQuoteImport]
    @UserID INT,
    @PRequestId INT OUTPUT
AS
BEGIN
    IF EXISTS
    (
        SELECT (1)
        FROM BorisGlobalTraderImports.dbo.[tbPriceQuoteToBeImported]
        WHERE CreatedBy = @UserID
    )
    BEGIN

        SET NOCOUNT ON
        DECLARE @Msg NVARCHAR(100)

        CREATE TABLE #tmpPriceQuoteToBeImported
        (
            [SNO] INT IDENTITY(1, 1),
            [PriceQuoteImportId] [int] NOT NULL,
            [REQUIREMENTNo] Int NULL,
            [CustomerRequirementId] INT NULL,
            [FullPart] [nvarchar](40) NOT NULL,
            [Part] [nvarchar](40) NULL,
            [ManufacturerName] [nvarchar](100) NULL,
            [SupplierNo] INT,
            [SupplierFullName] [nvarchar](128),
            [SupplierCost] [float] NULL,
            [SupplierPart] [nvarchar](100) NULL,
            [LeadTime] [nvarchar](50) NULL,
            [SPQ] [nvarchar](10) NULL,
            [SupplierMOQ] [nvarchar](100) NULL,
            [Quantity] int NULL,
            [Qty_in_Stock] [nvarchar](20) NULL,
            [DateCode] [nvarchar](100) NULL,
            [PackageName] [nvarchar](100) NULL,
            [CurrencyCode] int NULL,
            [DescriptionNotes] [nvarchar](500) NULL,
            [ROHS] [nvarchar](200) NULL,
            [Region] [nvarchar](200) NULL,
            [FactorySealed] [nvarchar](MAX) NULL,
            [OfferStatus] [nvarchar](MAX) NULL,
            [OriginalFilename] [nvarchar](100) NULL,
            [GeneratedFilename] [nvarchar](100) NULL,
            [ClientId] [int] NULL,
            [SelectedClientId] [int] NULL,
            [CreatedBy] [int] NULL,
            [ImportDate] [datetime] NULL,
            [DLUP] [datetime] NULL,
                                /************ Added 5 new Columns START************/
            [LTB] [NVARCHAR](100) NULL,
            [Buyprice] [nvarchar](100) NULL,
            [Sellprice] [nvarchar](100) NULL,
            [ShippingCost] [nvarchar](100) NULL,
            [DeliveryDate] [nvarchar](100) NULL,
            [MSL] [nvarchar](100) NULL,
            [MSLLevelNo] INT NULL,
            IsRecordExists INT
        /************ END ************/
        )

        INSERT INTO #tmpPriceQuoteToBeImported
        SELECT epo.[PriceQuoteImportId],
               epo.[RequirementNo],
               cr.CustomerRequirementId AS [CustomerRequirementId],
               dbo.ufn_get_fullpart(Epo.SupplierPart),
               epo.[SupplierPart],
               epo.[ManufacturerName],
               (
                   select top 1
                       CompanyId
                   from tbcompany c
                   where lower(c.CompanyName) = lower(epo.SupplierName) COLLATE SQL_Latin1_General_CP1_CI_AI
               ) as [SupplierNo],
               epo.SupplierName,
               epo.[SupplierCost],
               epo.[SupplierPart],
               epo.[LeadTime],
               epo.[SPQ],
               epo.MOQ as [SupplierMOQ],
               epo.[Quantity],
               epo.[QtyInStock] as [Qty_in_Stock],
               epo.[DateCode],
               epo.[PackageName],
               (
                   SELECT TOP 1
                       CurrencyId
                   FROM dbo.tbCurrency tc
                   WHERE tc.CurrencyCode = epo.CurrencyCode COLLATE Latin1_General_CI_AS
                         AND tc.Buy = 1
                         AND ISNULL(Inactive, 0) = 0
                         AND ClientNo = 114
               ) as [CurrencyCode],
               epo.[Description] as [DescriptionNotes],
               epo.[ROHS],
               --,[Region]          
               (
                   SELECT TOP 1
                       RegionId
                   FROM tbRegion c
                   WHERE lower(c.RegionName) = lower(epo.Region) COLLATE SQL_Latin1_General_CP1_CI_AI
               ) AS [Region],
               epo.[FactorySealed],
               --,[OfferStatus]                                                
               (
                   SELECT TOP 1
                       OfferStatusId
                   FROM tbOfferStatus c
                   where lower(c.Name) = lower(epo.OfferStatus) COLLATE SQL_Latin1_General_CP1_CI_AI
               ) AS [OfferStatus],
               epo.[OriginalFilename],
               epo.[GeneratedFilename],
               epo.[ClientId],
               epo.[SelectedClientId],
               epo.[CreatedBy],
               epo.[ImportDate],
               GETDATE() as [DLUP],
               /************ Added 5 new Columns START************/
               epo.[LastTimeBuy] as [LTB],
               epo.[Buyprice],
               epo.[Sellprice],
               epo.[ShippingCost],
               epo.[DeliveryDate],
               epo.[MSL],
               (
                   SELECT TOP 1
                       MSLLevelId
                   FROM tbMSLLevel c
                   WHERE lower(c.MSLLevel) = lower(epo.MSL) COLLATE SQL_Latin1_General_CP1_CI_AI
               ) AS [MSLLevelNo],
               0
        /************ END ************/
        FROM BorisGlobalTraderImports.dbo.[tbPriceQuoteToBeImported] AS Epo
            INNER JOIN dbo.tbcustomerrequirement cr WITH (NOLOCK)
                ON EPO.RequirementNo = cr.CustomerRequirementNumber
                   AND EPO.SelectedClientId = cr.ClientNo
                   --and [dbo].ufn_get_supplier_part_status(dbo.ufn_get_fullpart(Epo.SupplierPart), cr.FullPart) = 'Y'
        WHERE ISNULL(LEN(Epo.[SupplierPart]), 0) > 0
              and epo.CreatedBy = @UserID

        /****************** To insert Purchase Request Header / tbPurchaseRequest ***************************/

        DECLARE @ClientNo INT,
                @Division INT,
                @Notes VARCHAR(500),
                @PurchaseRequestId INT = 0;
        SELECT TOP 1
            @ClientNo = [ClientId],
            @Notes = [DescriptionNotes]
        from #tmpPriceQuoteToBeImported ---WHERE IsRecordExists=0                                                       
        
        SELECT @Division = DivisionNo
        from tbLogin
        where loginid = @UserID

        EXEC usp_insert_PurchaseRequest @ClientNo,
                                        @Notes,
                                        @UserID,
                                        @UserID,
                                        @Division,
                                        @PurchaseRequestId OUTPUT,
                                        1
        /*********************************************/
        SET @PRequestId = @PurchaseRequestId;

        /************************ Insert to BorisGlobalTraderImports.dbo.[tbpriceQuoteImport] ************************/                                                                                            
        INSERT INTO BorisGlobalTraderImports.dbo.[tbpriceQuoteImport]
        (
            [RequirementNo],
            [FullPart],
            [Part],
            [ManufacturerName],
            [SupplierName],
            [SupplierCost],
            [SupplierPart],
            [LeadTime],
            [SPQ],
            [SupplierMOQ],
            [Quantity],
            [Qty_in_Stock],
            [DateCode],
            [PackageName],
            [CurrencyCode],
            [DescriptionNotes],
            [ROHS],
            [Region],
            [FactorySealed],
            [OfferStatus],
            [OriginalFilename],
            [GeneratedFilename],
            [ClientId],
            [SelectedClientId],
            [CreatedBy],
            [ImportDate],
            [DLUP],
            /************ Added 5 new Columns START************/
            [Buyprice],
            [Sellprice],
            [ShippingCost],
            [LastTimeBuy],
            [DeliveryDate],
            [MSL],
            [PurchaseRequestNo]
        /************ END ************/
        )
        SELECT [RequirementNo],
               dbo.ufn_get_fullpart(isnull(Epo.Part, '')),
               [Part],
               [ManufacturerName],
               --,[SupplierName]       
               [SupplierFullName],
               [SupplierCost],
               [SupplierPart],
               [LeadTime],
               [SPQ],
               [SupplierMOQ],
               [Quantity],
               [Qty_in_Stock],
               [DateCode],
               [PackageName],
               [CurrencyCode],
               [DescriptionNotes],
               [ROHS],
               [Region],
               [FactorySealed],
               [OfferStatus],
               [OriginalFilename],
               [GeneratedFilename],
               [ClientId],
               [SelectedClientId],
               [CreatedBy],
               [ImportDate],
               [DLUP],
               [Buyprice],
               [Sellprice],
               [ShippingCost],
               [LTB],
               [DeliveryDate],
               [MSL],
               @PRequestId
        FROM #tmpPriceQuoteToBeImported Epo
        /************************ END ************************/

		/************************ Insert PurchaseRequestLine and PurchaseRequestLineDetail ************************/
		DECLARE @InsertedPurchaseRequestLine TABLE (PurchaseRequestLineId INT);

		;WITH cteTargetRequirement AS
		(
			SELECT DISTINCT CustomerRequirementId
            FROM #tmpPriceQuoteToBeImported
		)
		INSERT INTO dbo.tbPurchaseRequestLine
	    (
	        PurchaseRequestNo,
	        CustomerRequirementNo,
	        BOMNo,
	        FullPart,
	        Part,
	        Price,
	        Closed,
	        UpdatedBy,
	        DLUP
	    )
		OUTPUT INSERTED.PurchaseRequestLineId INTO @InsertedPurchaseRequestLine(PurchaseRequestLineId)
	    SELECT @PurchaseRequestId,
	           cr.CustomerRequirementId,
	           cr.BOMNo,
	           cr.FullPart,
	           cr.Part,
	           0,
	           0,
	           @UserID,
	           CURRENT_TIMESTAMP
	    FROM dbo.tbCustomerRequirement cr
	        JOIN cteTargetRequirement t ON t.CustomerRequirementId = cr.CustomerRequirementId;

        INSERT INTO tbPurchaseRequestLineDetail
        (
            PurchaseRequestLineNo,
            CompanyNo,
            Price,
            SPQ,
            LeadTime,
            ROHSStatus,
            FactorySealed,
            MSL,
            UpdatedBy,
            DLUP,
            ManufacturerName,
            DateCode,
            PackageType,
            ProductType,
            MOQ,
            TotalQuantityAvailableInStock,
            LTB,
            Notes,
            CurrencyNo,
            MSLLevelNo,
            UpdateSource,
            UpdateDate,
            Quantity,
            ShippingCost,
            DeliveryDate,
            RegionNO,
            OfferStatusNo,
            --ROHSStatusNo,
            BuyPrice,
            SellPrice
        )
        SELECT tpl.PurchaseRequestLineId as PurchaseRequestLineNo,
               CAST(epo.SupplierNo as int),
               /*[001]*/
               --Epo.[SupplierCost],    
               Epo.BuyPrice,
               /*[001]*/
               Epo.[SPQ],
               Epo.[LeadTime],
			   CASE 
					WHEN ISNULL(Epo.[ROHS], '') = 'Y' THEN 'Y'
					ELSE 'N' 
			   END AS ROHSStatus,
               Epo.[FactorySealed],
               EPO.MSL as MSL,
               CreatedBy as UpdatedBy,
               Getdate() as DLUP,
               Epo.[ManufacturerName],
               Epo.[DateCode],
               Epo.[PackageName],
               NULL as [ProductType],
               Epo.[SupplierMOQ],
               Epo.[Qty_in_Stock],
               Epo.[LTB],
               Epo.[DescriptionNotes] as Notes,
               Epo.[currencycode] AS CurrencyNo,
               Epo.[MSLLevelNo] AS [MSLLevelNo],
               'Insert by PQ Utility' AS UpdateSource,
               GETDATE() AS UpdateDate,
               Epo.Quantity,
               Epo.ShippingCost,
               CONVERT(DATETIME, Epo.DeliveryDate, 103),
               Epo.Region,
               Epo.OfferStatus,
               --CAST(Epo.ROHS AS INT),
               Epo.BuyPrice,
               Epo.SellPrice
		FROM #tmpPriceQuoteToBeImported Epo
			INNER JOIN dbo.tbPurchaseRequestLine tpl
				on Epo.CustomerRequirementId = tpl.CustomerRequirementNo
			INNER JOIN @InsertedPurchaseRequestLine ipl
				on ipl.PurchaseRequestLineId = tpl.PurchaseRequestLineId;

        /************************ END ************************/
		
        /*[001]*/
        INSERT INTO tbPurchaseQuoteCSVLog
        (
            PurchaseQuoteNo,
            FileName,
            Status,
            Message,
            LogDate
        )
        SELECT TOP 1
            @PurchaseRequestId,
            GeneratedFilename,
            1,
            'Purchase request imported in GT',
            GETDATE()
        FROM #tmpPriceQuoteToBeImported
        INSERT INTO dbo.tbPurchaseQuoteCSV
        (
            PurchaseQuoteNo,
            Caption,
            FileName,
            UpdatedBy,
            DLUP
        )
        SELECT TOP 1
            @PurchaseRequestId,
            'Purchase request imported in GT',
            GeneratedFilename,
            @UserID,
            GETDATE()
        FROM #tmpPriceQuoteToBeImported
        /*[001]*/
        INSERT INTO tbPriceRequestLog
        (
            PurchaseRequestLineNo,
            CompanyNo,
            ReadedFileName,
            StatusType,
            UnitPrice,
            AddedDate,
            DLUP
        )
        SELECT tpl.PurchaseRequestLineID,
               Epo.SupplierNo,
               Epo.originalfilename,
               'Inserted',
               /*[001]*/
               --Epo.[SupplierCost],    
               Epo.BuyPrice,
               /*[001]*/
               GETDATE(),
               GETDATE()
		FROM #tmpPriceQuoteToBeImported Epo
			INNER JOIN dbo.tbPurchaseRequestLine tpl
				on Epo.CustomerRequirementId = tpl.CustomerRequirementNo
			INNER JOIN @InsertedPurchaseRequestLine ipl
				on ipl.PurchaseRequestLineId = tpl.PurchaseRequestLineId;

        /********* For insertion in Sourcing table RP-2210 ********/
        EXEC [usp_insert_SourcingResult_From_PurchaseRequestQuoteUtility] @PurchaseRequestId

        /********* END*************************************************/
		/*
        UPDATE TPD
        SET tpd.CompanyNo = epo.SupplierNo,
            /*[001]*/
            --tpd.[Price]=epo.[SupplierCost],     
            tpd.[Price] = epo.[BuyPrice],
            /*[001]*/
            tpd.[SPQ] = epo.[SPQ],
            tpd.[LeadTime] = epo.[LeadTime],
            tpd.ROHSStatus = IIF(ISNULL(Epo.[ROHS], '') = '', 'N', 'Y'),
            tpd.[FactorySealed] = epo.[FactorySealed],
            tpd.MSL = EPO.MSL,
            tpd.UpdatedBy = epo.CreatedBy,
            tpd.DLUP = GETDATE(),
            tpd.[ManufacturerName] = epo.[ManufacturerName],
            tpd.[DateCode] = epo.[DateCode],
            tpd.[PackageType] = epo.[PackageName],
            --NULL as [ProductType],                                                               
            tpd.[MOQ] = epo.[SupplierMOQ],
            tpd.[TotalQuantityAvailableInStock] = epo.[Qty_in_Stock],
            tpd.[LTB] = epo.[LTB],
            tpd.Notes = epo.[DescriptionNotes],
            tpd.CurrencyNo = epo.currencycode,
            tpd.[MSLLevelNo] = epo.MSLLevelNo,
            tpd.UpdateSource = 'Update by PQ Utility',
            tpd.UpdateDate = getdate(),
            tpd.Quantity = Epo.Quantity,
            tpd.ShippingCost = Epo.ShippingCost,
            tpd.DeliveryDate = CONVERT(DATETIME, Epo.DeliveryDate, 103),
            tpd.RegionNO = Epo.Region,
            tpd.OfferStatusNo = Epo.OfferStatus,
            tpd.ROHSStatusNo = CAST(Epo.ROHS AS INT),
            tpd.BuyPrice = Epo.BuyPrice,
            tpd.SellPrice = Epo.SellPrice
        FROM tbPurchaseRequestLineDetail tpd
            INNER JOIN tbPurchaseRequestLine tpl
                ON tpd.PurchaseRequestLineNo = tpl.PurchaseRequestLineId
            INNER JOIN #tmpPriceQuoteToBeImported Epo
                ON epo.[CustomerRequirementId] = tpl.CustomerRequirementNo
                   and tpd.CompanyNo = Epo.SupplierNo
        WHERE EPo.IsRecordExists = 1


        INSERT INTO tbPriceRequestLog
        (
            PurchaseRequestLineNo,
            CompanyNo,
            ReadedFileName,
            StatusType,
            UnitPrice,
            AddedDate,
            DLUP
        )
        SELECT tpl.PurchaseRequestLineID,
               Epo.SupplierNo,
               Epo.originalfilename,
               'Updated',
               Epo.BuyPrice,
               GETDATE(),
               GETDATE()
        FROM tbPurchaseRequestLineDetail tpd
            INNER JOIN tbPurchaseRequestLine tpl
                ON tpd.PurchaseRequestLineNo = tpl.PurchaseRequestLineId
            INNER JOIN #tmpPriceQuoteToBeImported Epo
                ON epo.[CustomerRequirementId] = tpl.CustomerRequirementNo
                   and tpd.CompanyNo = Epo.SupplierNo
        WHERE EPo.IsRecordExists = 1
		*/
        ---------------------------Import History------------------------------------                                                                                         
        DECLARE @RowCount int = 0
        --DECLARE  @ClientNo int = 0                                                                                               
        DECLARE @OriginalFilename nvarchar(200) = null
        SELECT @RowCount = COUNT(*),
               @OriginalFilename = imp.[OriginalFilename],
               @ClientNo = imp.ClientID
        from BorisGlobalTraderimports.dbo.[tbPriceQuoteToBeImported] imp
            INNER JOIN #tmpPriceQuoteToBeImported AS tmp
                ON tmp.[PriceQuoteImportId] = imp.[PriceQuoteImportId]
        where imp.CreatedBy = @UserID
        group by imp.[OriginalFilename],
                 imp.ClientID

        INSERT INTO BorisGlobalTraderImports.dbo.tbUtilityLog
        (
            FileName,
            UtilityType,
            Clientid,
            LoginNo,
            DLUP,
            iRowCount
        )
        VALUES
        (@OriginalFilename, 6, @ClientNo, @UserID, GETDATE(), @RowCount)
        --   --------------------------Import History End--------------------------------                                                                   

        INSERT INTO BorisGlobalTraderImports.dbo.[tbPriceQuote_ImportHistory]
        (
            ImportDate,
            ImportName,
            RowsAffected,
            Target,
            ClientType
        )
        SELECT GETDATE(),
               a.[OriginalFilename],
               count(*),
               'Import Success',
               1
        from #tmpPriceQuoteToBeImported a
        where a.CreatedBy = @UserID
        GROUP BY a.[OriginalFilename]

        DELETE BorisGlobalTraderImports.dbo.[tbPriceQuoteToBeImported]
        FROM BorisGlobalTraderImports.dbo.[tbPriceQuoteToBeImported] AS imp
            INNER JOIN #tmpPriceQuoteToBeImported AS tmp
                ON tmp.PriceQuoteImportId = imp.PriceQuoteImportId

        DROP TABLE #tmpPriceQuoteToBeImported

        SELECT @PRequestId AS PurchaseRequestId;
        SET NOCOUNT OFF
    END
END
GO