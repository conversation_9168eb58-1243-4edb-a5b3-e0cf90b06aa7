﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Rebound.GlobalTrader.DAL
{
    public class CSLMatchingCompanyDetails
    {

        public CSLMatchingCompanyDetails() { }

        #region Properties

        public int CompanyId { get; set; }
        public string CustomerCode { get; set; }
        public int ClientNo { get; set; }
        public string ClientName { get; set; }
        public string CompanyName { get; set; }
        public string GT_Company_Address { get; set; }
        public string CSL_Name { get; set; }
        public string CSL_Address { get; set; }
        public string CSL_ALT_Name { get; set; }

        public string CSL_DateInserted { get; set; }
        public string Notes { get; set; }
        public string ImportantNotes { get; set; }


        #endregion

    }
}
