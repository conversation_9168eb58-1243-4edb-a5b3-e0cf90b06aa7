//----------------------------------------------------------------------------------------------------
// RP 22.12.2009:
// - push rendering of saved state onto server
//----------------------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using Rebound.GlobalTrader.Site.Controls.DataListNuggets;
using Rebound.GlobalTrader.Site.Enumerations;
using System.Collections.Concurrent;

namespace Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists {
	public class Base : Rebound.GlobalTrader.Site.Controls.LeftNuggets.Base {

		#region Locals

		protected FlexiDataTable _tbl = new FlexiDataTable();
		protected DataListNuggets.Base _ctlSourceDataListNugget;
		protected DataListNugget _objDataListNugget;
		protected Panel _pnlPaging;
		protected Panel _pnlPagingControls;
		protected Panel _pnlPagingContent;
		protected Panel _pnlLock;
		protected Panel _pnlNoData;
		protected Panel _pnlLoading;
		protected Panel _pnlTable;
		protected Panel _pnlShowFilters;
		protected HyperLink _hypPrev;
		protected Label _lblPageNo;
		protected Label _lblTotalPages;
		protected HyperLink _hypNext;
		protected Panel _pnlFilters;
		protected ConcurrentDictionary<string, int> _dctFilterIndexes;
		protected ConcurrentDictionary<string, string> _dctFilterIDs;
		protected List<string> _lstFilterButtonIDs;
		protected List<string> _lstFilterIDs;
		protected List<bool> _lstFilterShown;
		protected Table _tblFilters;
		protected Panel _pnlSearch;
		protected TableRow _trSearch;
		protected string _strDataListNuggetSubType = "";
		protected bool _blnHasSavedState = false;

		#endregion

		#region Properties

		private int _intPageSize = 10;
		public int PageSize {
			get { return _intPageSize; }
			set { _intPageSize = value; }
		}

		private bool _blnAllowSavingState = true;
		public bool AllowSavingState {
			get { return _blnAllowSavingState; }
			set { _blnAllowSavingState = value; }
		}

		private bool _blnInitiallySaveState = Convert.ToBoolean(SessionManager.SaveDataListNuggetStateByDefault);
		public bool InitiallySaveState {
			get { return _blnInitiallySaveState; }
			set { _blnInitiallySaveState = value; }
		}

		private bool _blnBypassState = false;
		public bool BypassState {
			get { return _blnBypassState; }
			set { _blnBypassState = value; }
		}

		private DataListNuggetState _objSavedState;
		public DataListNuggetState SavedState {
			get { return _objSavedState; }
			set { _objSavedState = value; }
		}

		#endregion

		#region Overrides

		protected override void OnInit(EventArgs e) {
			DesignBase ctlDB = new DesignBase();
			ctlDB.ID = "ctlDB";
			Controls.Add(ctlDB);
			IconCssType = "List";
			base.OnInit(e);
			AddScriptReference("Controls.LeftNuggets.DataLists._Bases.Base");
		}

		protected override void OnLoad(EventArgs e) {
			GetSavedState(); //virtual sub called by subclass to get state from data store, populates _objSavedState
			if (_objSavedState != null) _blnHasSavedState = _objSavedState.HasState;
			base.OnLoad(e);
		}

		protected override void CreateChildControls() {
			//ensure all chld controls in base classes are created as we are going to need them
			base.CreateChildControls();
			ctlDesignBase.MakeChildControls();

			//make a dummy panel so all the controls are created for the passed DataListNugget
			Panel pnlDummy = ControlBuilders.CreatePanel();
			pnlDummy.Controls.Add(_ctlSourceDataListNugget);
			this.Controls.Add(pnlDummy);
			_ctlSourceDataListNugget.MakeChildControls();
			//this.ctlDesignBase.pnlContent.Controls.Add(_ctlSourceDataListNugget.ctlFilter);

			//Outer Panel
			Panel pnlOuter = ControlBuilders.CreatePanelInsideParent(ctlDesignBase.pnlContent, "leftNuggetDataList");

			//Paging controls
			_pnlPaging = ControlBuilders.CreatePanelInsideParent(pnlOuter, "leftNuggetPaging");
			_pnlPagingContent = ControlBuilders.CreatePanelInsideParent(_pnlPaging);
			_pnlShowFilters = ControlBuilders.CreatePanelInsideParent(_pnlPagingContent, "leftNuggetFilter invisible");
			ControlBuilders.CreateLiteralInsideParent(_pnlShowFilters, "&nbsp;");

			//lock
			_pnlLock = ControlBuilders.CreatePanelInsideParent(_pnlPagingContent, "lockState");
			ControlBuilders.CreateImageInsideParent(_pnlLock, "", "~/images/x.gif", 5, 5);
			_pnlLock.ToolTip = Functions.GetGlobalResource("Misc", "DataListNuggetStateButtonTooltip");
			_pnlLock.ID = "pnlLock";

			//rest of paging controls
			_pnlPagingControls = ControlBuilders.CreatePanelInsideParent(_pnlPagingContent, "invisible");
			_hypPrev = ControlBuilders.CreateHyperLinkInsideParent(_pnlPagingControls, "prevLink", "javascript:void(0);", "&laquo;");
			_lblPageNo = ControlBuilders.CreateLabelInsideParent(_pnlPagingControls);
			ControlBuilders.CreateLiteralInsideParent(_pnlPagingControls, @" / ");
			_lblTotalPages = ControlBuilders.CreateLabelInsideParent(_pnlPagingControls);
			_hypNext = ControlBuilders.CreateHyperLinkInsideParent(_pnlPagingControls, "nextLink", "javascript:void(0);", "&raquo;");
			_pnlNoData = ControlBuilders.CreatePanelInsideParent(_pnlPagingContent, "noData invisible");
			ControlBuilders.CreateLiteralInsideParent(_pnlNoData, Functions.GetGlobalResource("Misc", "NoMatches"));

			//loading
			_pnlLoading = ControlBuilders.CreatePanelInsideParent(_pnlPaging, "loading");
			ControlBuilders.CreateLiteralInsideParent(_pnlLoading, "&nbsp;");

			//filters
			DataListNuggets.Filter ctlFilter = GetFilterControlFromDataListNugget();
			if (ctlFilter != null) {
				_pnlFilters = ControlBuilders.CreatePanelInsideParent(pnlOuter, "leftNuggetFilters invisible");
				_tblFilters = ControlBuilders.CreateTableInsideParent(pnlOuter, "leftNuggetFilterRows");
				_tbl.SortColumnDirection = _ctlSourceDataListNugget._tbl.SortColumnDirection;
				_tbl.SortColumnIndex = _ctlSourceDataListNugget._tbl.SortColumnIndex;
				AddSearchButton();

				AddNewFilterItemsToStartOfList();
				//get filters from the DataListNuggets controls collection - first left side
				for (int i = 0; i < ctlFilter.tblColLeft.Controls.Count; i++) {
					if (ctlFilter.tblColLeft.Controls[i] is FilterDataItemRows.Base) AddFilterItem((FilterDataItemRows.Base)ctlFilter.tblColLeft.Controls[i]);
				}
				//...now right side
				for (int i = 0; i < ctlFilter.tblColRight.Controls.Count; i++) {
					if (ctlFilter.tblColRight.Controls[i] is FilterDataItemRows.Base) AddFilterItem((FilterDataItemRows.Base)ctlFilter.tblColRight.Controls[i]);
				}
				AddNewFilterItemsToEndOfList();
			}

			//table
			_pnlTable = ControlBuilders.CreatePanelInsideParent(pnlOuter, "leftNuggetDataListTable invisible");
			_tbl.PanelHeight = Unit.Pixel(244);
			_pnlTable.Controls.Add(_tbl);

			//remove the dummy panel
			Controls.Remove(pnlDummy);

			SetupScriptDescriptors();
		}

		protected override void OnPreRender(EventArgs e) {
			if (_blnBypassState) _blnInitiallySaveState = false;
			if (!_blnBypassState && _blnHasSavedState) RenderSavedState();
			base.OnPreRender(e);
		}

		#endregion

		#region Virtual Methods

		protected virtual void AddNewFilterItemsToStartOfList() { }
		protected virtual void AddNewFilterItemsToEndOfList() { }
		protected virtual void RenderAdditionalState() { }
		protected virtual void GetSavedState() {
			SavedState = DataListNuggetStateManager.GetDataListNuggetState(_objDataListNugget.ID, _strDataListNuggetSubType);
		}

		#endregion

		#region Methods

		protected new void AddScriptReference(string strRef) {
			bool blnDebug = false;
#if DEBUG
			blnDebug = true;
#endif
			AddScriptReference(blnDebug, "Rebound.GlobalTrader.Site", strRef);
		}

		protected void LoadDataListNugget(string strType) {
			_ctlSourceDataListNugget = (Controls.DataListNuggets.Base)this.LoadControl(String.Format("~/Controls/DataListNuggets/{0}/{0}.ascx", strType));
		}

		private void RenderSavedState() {
			if (!_blnHasSavedState) return;
			_intPageSize = _objSavedState.PageSize;
			_tbl.SortColumnDirection = (SortColumnDirection)_objSavedState.SortDirection;
			_tbl.SortColumnIndex = _objSavedState.SortIndex;
			_tbl.CurrentPage = _objSavedState.Page;
			foreach (DataListNuggetFilterState st in _objSavedState.FilterStates) {
				FilterDataItemRows.Base flt = GetFilter(st.Name);
				if (flt != null) {
					SetFilterValue(flt, st.Name, st.Value, st.Comparison);
					Functions.SetCSSVisibility(flt, st.IsShown);
					if (st.IsShown) _lstFilterShown[GetFilterIndex(st.Name)] = true;
					flt.Enable(st.IsOn);
					ShowFilterButtonState(st.Name, st.IsShown);
				}
				flt = null;
			}
			RenderAdditionalState(); //raise virtual sub so subclasses can add state
		}

		protected void SetFilterValue(FilterDataItemRows.Base flt, string strName, object objValue, Controls.DropDowns.NumericalComparison.NumericalComparisonType enmNumericalComparisonType) {
			if (flt is FilterDataItemRows.CheckBox) {
				if (objValue == null || objValue.ToString() == "") objValue = false;
				((FilterDataItemRows.CheckBox)flt).SetInitialValue(Convert.ToBoolean(objValue));
			} else if (flt is FilterDataItemRows.DropDown) {
				((FilterDataItemRows.DropDown)flt).SetInitialValue(objValue);
			} else if (flt is FilterDataItemRows.DateSelect) {
				((FilterDataItemRows.DateSelect)flt).SetInitialValue(objValue);
			} else if (flt is FilterDataItemRows.Numerical) {
				((FilterDataItemRows.Numerical)flt).SetInitialValue(objValue.ToString(), Convert.ToInt32(enmNumericalComparisonType));
			} else if (flt is FilterDataItemRows.StarRating) {
				if (objValue == null || objValue.ToString() == "") objValue = 0;
				((FilterDataItemRows.StarRating)flt).SetInitialValue(Convert.ToInt32(objValue), Convert.ToInt32(enmNumericalComparisonType));
			} else if (flt is FilterDataItemRows.TextBox) {
				((FilterDataItemRows.TextBox)flt).SetInitialValue(objValue.ToString());
			}
		}
		/// <summary>
		/// Set Filter Value - this version is usually called from sub-classes
		/// </summary>
		protected void SetFilterValue(string strName, object objValue, Controls.DropDowns.NumericalComparison.NumericalComparisonType enmNumericalComparisonType) {
			FilterDataItemRows.Base flt = GetFilter(strName);
			if (flt != null) {
				SetFilterValue(flt, strName, objValue, enmNumericalComparisonType);
				_lstFilterShown[GetFilterIndex(strName)] = true;
				Functions.SetCSSVisibility(flt, true);
				ShowFilterButtonState(strName, true);
			}
			flt = null;
		}
		protected void SetFilterValue(string strName, object objValue) {
			SetFilterValue(strName, objValue, Rebound.GlobalTrader.Site.Controls.DropDowns.NumericalComparison.NumericalComparisonType.EqualTo);
		}

		protected void ShowFilterButtonState(string strName, bool blnOn) {
			Panel pnlFilterButton = GetFilterButton(strName);
			if (pnlFilterButton != null) {
				pnlFilterButton.CssClass = (blnOn) ? "filterItem filterItemOn" : "filterItem filterItemOff";
				pnlFilterButton.Dispose();
			}
			pnlFilterButton = null;
		}

		protected void AddNewDropDownFilter(string strID, string strDropDownAssembly, string strDropDownType, string strResourceTitle, string strFilterField, object objDefaultValue) {
			Controls.FilterDataItemRows.DropDown flt = new Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.DropDown();
			flt.DropDownAssembly = strDropDownAssembly;
			flt.DropDownType = strDropDownType;
			flt.ResourceTitle = strResourceTitle;
			flt.FilterField = strFilterField;
			if (objDefaultValue != null) flt.DefaultValue = objDefaultValue.ToString();
			AddFilterItem((Controls.FilterDataItemRows.Base)flt);
		}

		private void AddSearchButton() {
			_trSearch = new TableRow();
			_trSearch.CssClass = "invisible";
			_tblFilters.Rows.Add(_trSearch);
			TableCell td = new TableCell();
			_trSearch.Cells.Add(td);
			td.CssClass = "searchButton";
			_pnlSearch = ControlBuilders.CreatePanelInsideParent(td, "searchButton");
			ControlBuilders.CreateLiteralInsideParent(_pnlSearch, Functions.GetGlobalResource("misc", "Search"));
		}

		private DataListNuggets.Filter GetFilterControlFromDataListNugget() {
			DataListNuggets.Filter ctl = null;
			if (_ctlSourceDataListNugget == null) return ctl;
			if (_ctlSourceDataListNugget._pnlFilters == null) return ctl;
			if (_ctlSourceDataListNugget._pnlFilters.Controls[0] == null) return ctl;
			for (int i = 0; i < _ctlSourceDataListNugget._pnlFilters.Controls[0].Controls.Count; i++) {
				if (_ctlSourceDataListNugget._pnlFilters.Controls[0].Controls[i] is DataListNuggets.Filter) {
					ctl = (DataListNuggets.Filter)_ctlSourceDataListNugget._pnlFilters.Controls[0].Controls[i];
					break;
				}
			}
			return ctl;
		}

		private void AddFilterItem(FilterDataItemRows.Base ctl) {
			if (_dctFilterIndexes == null) _dctFilterIndexes = new ConcurrentDictionary<string, int>();
			if (_dctFilterIDs == null) _dctFilterIDs = new ConcurrentDictionary<string, string>();
			if (_lstFilterButtonIDs == null) _lstFilterButtonIDs = new List<string>();
			if (_lstFilterIDs == null) _lstFilterIDs = new List<string>();
			if (_lstFilterShown == null) _lstFilterShown = new List<bool>();

			//create the on / off switch panel
			Panel pnl = ControlBuilders.CreatePanelInsideParent(_pnlFilters, "filterItem filterItemOff");
			pnl.ID = String.Format("pnlItem{0}", _lstFilterButtonIDs.Count);
			pnl.Attributes["bgt_leftDataList_filter"] = _lstFilterButtonIDs.Count.ToString();
			ControlBuilders.CreateLiteralInsideParent(pnl, Functions.GetGlobalResource("FormFields", ctl.ResourceTitle));
			ctl.ID = String.Format("ctlFilter{0}", _lstFilterButtonIDs.Count);

			//add new filter control based on the old one
			FilterDataItemRows.Base ctlFilter = null;

			//get correct type for filter item
			if (ctl is FilterDataItemRows.TextBox) ctlFilter = new FilterDataItemRows.TextBox();
			if (ctl is FilterDataItemRows.Numerical) ctlFilter = new FilterDataItemRows.Numerical();
			if (ctl is FilterDataItemRows.DropDown) {
				ctlFilter = new FilterDataItemRows.DropDown();
				((FilterDataItemRows.DropDown)ctlFilter).DropDownAssembly = ((FilterDataItemRows.DropDown)ctl).DropDownAssembly;
				((FilterDataItemRows.DropDown)ctlFilter).DropDownType = ((FilterDataItemRows.DropDown)ctl).DropDownType;
			}
			if (ctl is FilterDataItemRows.StarRating) ctlFilter = new FilterDataItemRows.StarRating();
			if (ctl is FilterDataItemRows.CheckBox) ctlFilter = new FilterDataItemRows.CheckBox();
			if (ctl is FilterDataItemRows.DateSelect) ctlFilter = new FilterDataItemRows.DateSelect();

			//common stuff for each filter item
			ctlFilter.ID = ctl.ID;
			ctlFilter.CssClass = "invisible";
			ctlFilter.ResourceTitle = ctl.ResourceTitle;
			ctlFilter.FilterField = ctl.FilterField;
			ctlFilter.DefaultValue = ctl.DefaultValue;
			ctlFilter.ForLeftNugget = true;

			//add to collections
			_tblFilters.Rows.Add(ctlFilter);
			_dctFilterIndexes.TryAdd(ctlFilter.FilterField, _lstFilterButtonIDs.Count);
			_dctFilterIDs.TryAdd(ctlFilter.FilterField, ctlFilter.ID);
			_lstFilterButtonIDs.Add(pnl.ClientID);
			_lstFilterIDs.Add(ctlFilter.ClientID);
			_lstFilterShown.Add(false);
		}


		protected void SetupScriptDescriptors() {
			if (_scScriptControlDescriptor == null) _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataList", ctlDesignBase.ClientID);
			_scScriptControlDescriptor.AddComponentProperty("tbl", _tbl.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlPagingContent", _pnlPagingContent.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlLoading", _pnlLoading.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlTable", _pnlTable.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlShowFilters", _pnlShowFilters.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlFilters", _pnlFilters.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlLock", _pnlLock.ClientID);
			_scScriptControlDescriptor.AddElementProperty("hypPrev", _hypPrev.ClientID);
			_scScriptControlDescriptor.AddElementProperty("lblPageNo", _lblPageNo.ClientID);
			_scScriptControlDescriptor.AddElementProperty("lblTotalPages", _lblTotalPages.ClientID);
			_scScriptControlDescriptor.AddElementProperty("hypNext", _hypNext.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlPagingControls", _pnlPagingControls.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlNoData", _pnlNoData.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlSearch", _pnlSearch.ClientID);
			_scScriptControlDescriptor.AddElementProperty("trSearch", _trSearch.ClientID);
			_scScriptControlDescriptor.AddProperty("intPageSize", _intPageSize);
			_scScriptControlDescriptor.AddProperty("aryFilterButtonIDs", _lstFilterButtonIDs);
			_scScriptControlDescriptor.AddProperty("aryFilterIDs", _lstFilterIDs);
			_scScriptControlDescriptor.AddProperty("aryFilterShown", _lstFilterShown);
			_scScriptControlDescriptor.AddProperty("intDataListNuggetID", _objDataListNugget.ID);
			_scScriptControlDescriptor.AddProperty("blnAllowSavingState", _blnAllowSavingState);
			_scScriptControlDescriptor.AddProperty("strDataListNuggetSubType", _strDataListNuggetSubType);
			_scScriptControlDescriptor.AddProperty("blnSaveState", _blnInitiallySaveState);
			_scScriptControlDescriptor.AddProperty("intPageSizeForState", (_blnHasSavedState) ? _objSavedState.PageSize : 10);
		}

		protected void SetDataListNuggetType(string strType) {
			_objDataListNugget = _objSite.GetDataListNugget(strType);
		}

		protected void SetDataListNuggetType(string strType, string strSubType) {
			SetDataListNuggetType(strType);
			_strDataListNuggetSubType = strSubType;
		}

		protected void SetDataListNuggetType(string strType, Enum enmSubType) {
			SetDataListNuggetType(strType);
			_strDataListNuggetSubType = (Convert.ToInt32(enmSubType)).ToString();
		}

		protected FilterDataItemRows.Base GetFilter(string strFilterField) {
			string strID = GetFilterID(strFilterField);
			if (string.IsNullOrEmpty(strID)) return null;
			Control ctl = Functions.FindControlRecursive(_tblFilters, strID);
			if (ctl == null) return null;
			return (FilterDataItemRows.Base)ctl;
		}

		protected Panel GetFilterButton(string strFilterField) {
			int intIndex = GetFilterIndex(strFilterField);
			if (intIndex < 0) return null;
			Control ctl = Functions.FindControlRecursive(_pnlFilters, string.Format("pnlItem{0}", intIndex));
			if (ctl == null) return null;
			return (Panel)ctl;
		}

		protected int GetFilterIndex(string strFilterField) {
			int intIndex = -1;
			if (!_dctFilterIndexes.TryGetValue(strFilterField, out intIndex)) intIndex = 0;
			return intIndex;
		}

		protected string GetFilterID(string strFilterField) {
			string strID = "";
			if (!_dctFilterIDs.TryGetValue(strFilterField, out strID)) strID = "";
			return strID;
		}

		protected string GetSavedStateValue(string strFilterField) {
			if (_objSavedState == null) return "";
			return _objSavedState.GetStateValue(strFilterField);
		}

		protected void ShowFilter(string strFilterField, bool blnShow) {
			FilterDataItemRows.Base flt = GetFilter(strFilterField);
			if (flt != null) {
				Functions.SetCSSVisibility(flt, blnShow);
				_lstFilterShown[GetFilterIndex(strFilterField)] = blnShow;
				flt.Enable(blnShow);
				ShowFilterButtonState(strFilterField, blnShow);
			}
			flt = null;
		}

		#endregion

	}
}
