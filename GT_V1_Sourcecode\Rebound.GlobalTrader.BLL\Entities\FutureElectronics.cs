﻿using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using Rebound.GlobalTrader.DAL;

namespace Rebound.GlobalTrader.BLL
{
    public partial class APIExternalLinks : BizObject
    {
        #region Properties
        protected static DAL.APIExternalLinksElement Settings
        {
            get { return Globals.Settings.APIExternalLinks; }
        }

        /// <summary>
        /// FElectronicsId
        /// </summary>
        public System.Int32 FElectronicsId { get; set; }
        /// <summary>
        /// FullPart
        /// </summary>
        public System.String FullPart { get; set; }
        /// <summary>
        /// Part
        /// </summary>
        public System.String Part { get; set; }
        /// <summary>
        /// ManufacturerNo
        /// </summary>
        public System.Int32? ManufacturerNo { get; set; }
        /// <summary>
        /// DateCode
        /// </summary>
        public System.String DateCode { get; set; }
        /// <summary>
        /// ProductNo
        /// </summary>
        public System.Int32? ProductNo { get; set; }
        /// <summary>
        /// PackageNo
        /// </summary>
        public System.Int32? PackageNo { get; set; }
        /// <summary>
        /// Quantity
        /// </summary>
        public System.Int32 Quantity { get; set; }
        /// <summary>
        /// Price
        /// </summary>
        public System.Double Price { get; set; }
        /// <summary>
        /// OriginalEntryDate
        /// </summary>
        public System.DateTime? OriginalEntryDate { get; set; }
        /// <summary>
        /// Salesman
        /// </summary>
        public System.Int32? Salesman { get; set; }
        /// <summary>
        /// SupplierNo
        /// </summary>
        public System.Int32 SupplierNo { get; set; }
        /// <summary>
        /// CurrencyNo
        /// </summary>
        public System.Int32? CurrencyNo { get; set; }
        /// <summary>
        /// ROHS
        /// </summary>
        public System.Byte? ROHS { get; set; }
        /// <summary>
        /// UpdatedBy
        /// </summary>
        public System.Int32? UpdatedBy { get; set; }
        /// <summary>
        /// DLUP
        /// </summary>
        public System.DateTime DLUP { get; set; }
        /// <summary>
        /// OfferStatusNo
        /// </summary>
        //public System.Int32? OfferStatusNo { get; set; }
        ///// <summary>
        ///// OfferStatusChangeDate
        ///// </summary>
        //public System.DateTime? OfferStatusChangeDate { get; set; }
        ///// <summary>
        ///// OfferStatusChangeLoginNo
        ///// </summary>
        //public System.Int32? OfferStatusChangeLoginNo { get; set; }
        /// <summary>
        /// SupplierName
        /// </summary>
        public System.String SupplierName { get; set; }
        /// <summary>
        /// Notes
        /// </summary>
        public System.String Notes { get; set; }
        /// <summary>
        /// ManufacturerName
        /// </summary>
        public System.String ManufacturerName { get; set; }
        /// <summary>
        /// ProductName
        /// </summary>
        public System.String ProductName { get; set; }
        /// <summary>
        /// PackageName
        /// </summary>
        public System.String PackageName { get; set; }
        /// <summary>
        /// ClientNo
        /// </summary>
        public System.Int32? ClientNo { get; set; }
        /// <summary>
        /// ManufacturerCode
        /// </summary>
        public System.String ManufacturerCode { get; set; }
        /// <summary>
        /// CurrencyCode
        /// </summary>
        public System.String CurrencyCode { get; set; }
        /// <summary>
        /// CurrencyDescription
        /// </summary>
        public System.String CurrencyDescription { get; set; }
        /// <summary>
        /// SupplierEmail
        /// </summary>
        public System.String SupplierEmail { get; set; }
        /// <summary>
        /// SalesmanName
        /// </summary>
        public System.String SalesmanName { get; set; }
        /// <summary>
        /// OfferStatusChangeEmployeeName
        /// </summary>
        public System.String OfferStatusChangeEmployeeName { get; set; }
        /// <summary>
        /// ClientId
        /// </summary>
        public System.Int32 ClientId { get; set; }
        /// <summary>
        /// ClientName
        /// </summary>
        public System.String ClientName { get; set; }
        /// <summary>
        /// ClientDataVisibleToOthers
        /// </summary>
        public System.Boolean? ClientDataVisibleToOthers { get; set; }
        //[001] code start
        /// <summary>
        /// SupplierType
        /// </summary>
        public System.String SupplierType { get; set; }
        //[001] code end
        /// <summary>
        /// SupplierEpo  hard code data
        /// </summary>
        public System.String SupplierEpo { get; set; }

        public System.String ClientCode { get; set; }

        public System.String MSL { get; set; }
        public System.String SPQ { get; set; }
        public System.String MOQ { get; set; }
        public System.String LeadTime { get; set; }
        public System.String RoHSStatus { get; set; }
        public System.String FactorySealed { get; set; }
        public System.Int32 IPOBOMNo { get; set; }
        public System.String SupplierTotalQSA { get; set; }
        public System.String SupplierMOQ { get; set; }
        public System.String SupplierLTB { get; set; }
        public System.Boolean IsSourcingHub { get; set; }
        public System.String ProductDescription { get; set; }
        public System.Int32? MSLLevelNo { get; set; }
        public System.String productNameDescrip { get; set; }
        public System.Boolean isIncludeAltPart { get; set; }
        public System.Int32? RowNum { get; set; }
        public System.Int32? TotalCount { get; set; }
        public System.String Description { get; set; }
        public System.String SupplierMessage { get; set; }
        //[001] code start
        /// <summary>
        /// Log Details CrossMatch
        /// </summary>
        public System.String LogDetails { get; set; }
        //[001] code end
        /// <summary>
        /// EpoId (from Table)
        /// </summary>
        public System.Int32 EpoId { get; set; }
        /// <summary>
        /// EpoStatusNo (from Table)
        /// usp_ipobom_source_Epo
        /// </summary>
        public System.Int32? EpoStatusNo { get; set; }
        /// <summary>
        /// EpoStatusChangeDate (from Table)
        /// usp_ipobom_source_Epo
        /// </summary>
        public System.DateTime? EpoStatusChangeDate { get; set; }
        /// <summary>
        /// EpoStatusChangeLoginNo (from Table)
        /// usp_ipobom_source_Epo
        /// </summary>
        public System.Int32? EpoStatusChangeLoginNo { get; set; }
        /// <summary>
        /// OfferStatusChangeEmployeeName (from usp_ipobom_source_Epo)
        /// </summary>
        public System.String EpoStatusChangeEmployeeName { get; set; }
        /// <summary>
        /// UpliftPrice (from usp_ipobom_source_Epo)
        /// </summary>
        public System.Double UpliftPrice { get; set; }
        ///PackageDescription
        public System.String PackageDescription { get; set; }
        public System.Int32? CustomerRequirementId { get; set; }
        //add from hear
        /// <summary>
        /// SourcingResultId
        /// </summary>
        public System.Int32 SourcingResultId { get; set; }
        /// <summary>
        /// CustomerRequirementNo
        /// </summary>
        public System.Int32 CustomerRequirementNo { get; set; }
        /// <summary>
        /// SourcingTable
        /// </summary>
        public System.String SourcingTable { get; set; }
        /// <summary>
        /// SourcingTableItemNo
        /// </summary>
        public System.Int32? SourcingTableItemNo { get; set; }
        /// <summary>
        /// TypeName
        /// </summary>
        public System.String TypeName { get; set; }
        public System.Double? SupplierPrice { get; set; }
        public System.String POHubSupplierName { get; set; }
        public System.Int32? POHubCompanyNo { get; set; }
        public System.String IsPoHub { get; set; }
        public System.Int32? POHubReleaseBy { get; set; }

        public System.String ClientSupplierName { get; set; }
        public System.Int32? ClientCompanyNo { get; set; }
        /// <summary>
        /// UPLiftPrice
        /// </summary>
        public System.Double? UPLiftPrice { get; set; }
        public System.Int32 ClientCurrencyNo { get; set; }
        public System.String ClientCurrencyCode { get; set; }
        public System.Double? ConvertedSourcingPrice { get; set; }
        public System.String MslSpqFactorySealed { get; set; }
        public double? EstimatedShippingCost { get; set; }
        public string SupplierManufacturerName { get; set; }
        public string SupplierDateCode { get; set; }
        public string SupplierPackageType { get; set; }
        public string SupplierProductType { get; set; }
        public string SupplierNotes { get; set; }
        public DateTime? DeliveryDate { get; set; }
        public System.Boolean? SourcingRelease { get; set; }
        public bool IsClosed { get; set; }
        public string ROHSStatus { get; set; }
        public string RegionName { get; set; }
        public System.Int32? RegionNo { get; set; }

        public string HubRFQName { get; set; }
        public System.Int32? HubRFQNo { get; set; }
        public bool IsSoCreated { get; set; }
        public string TermsName { get; set; }
        public bool IsApplyPOBankFee { get; set; }
        public string SourceRef { get; set; }
        public bool IsReleased { get; set; }
        public bool Recalled { get; set; }
        public string SourcingNotes { get; set; }
        public double? OriginalPrice { get; set; }
        public System.Int32? ActualCurrencyNo { get; set; }
        public System.String ActualCurrencyCode { get; set; }
        public System.Int32 SourcingReleasedCount { get; set; }
        public System.String MSLLevelText { get; set; }
        public double? ActualPrice { get; set; }
        public double? SupplierPercentage { get; set; }
        //[001] start
        /// <summary>
        /// SupplierWarranty
        /// </summary>
        public System.Int32? SupplierWarranty { get; set; }
        /// <summary>
        /// NonPreferredCompany
        /// </summary>
        public System.Boolean? NonPreferredCompany { get; set; }
        //[001] end
        //[003] start
        public System.Boolean IsTestingRecommended { get; set; }
        //[003] end
        public System.Boolean? IsImageAvailable { get; set; }
        /// <summary>
        /// PriorityId (from Table)
        /// </summary>
        public System.Int32 PriorityId { get; set; }
        /// <summary>
        /// PriorityNo 
        /// </summary>
        public System.Int32 PriorityNo { get; set; }
        public System.Int32 IHSCountryOfOriginNo { get; set; }
        public System.String IHSCountryOfOriginName { get; set; }
        public System.Int32 CountryOfOriginNo { get; set; }
        public System.String CountryOfOriginName { get; set; }
        public System.Int32? ReReleased { get; set; }
        public System.Boolean? PartWatchMatch { get; set; }
        public System.Boolean? DiffrentClientOffer { get; set; }
        /// <summary>
		/// CurrencyNo
		/// </summary>
		public System.Int32 EPOCurrencyNo { get; set; }
        public System.String VirtualCostPrice { get; set; }
        public System.DateTime? PublishDate { get; set; }
        public System.DateTime? GTDate { get; set; }
        public System.String Suppliertype { get; set; }
        public System.String ECCN { get; set; }
        public System.String UnitCostPrice { get; set; }
        public System.String Reference { get; set; }

        public System.String ApiShortName { get; set; }
        //public System.String Licensekey { get; set; }
        //public System.String HostName { get; set; }
        public System.String ApiName { get; set; }
        public System.Int32 ApiURLKeyId { get; set; }
        public System.String ApiSourceName { get; set; }
        #endregion

        #region Methods
        /// <summary>
        /// Source
        /// Calls [usp_source_Offer]
        /// </summary>
        public static List<APIExternalLinks> Source1(System.Int32? clientId, System.String partSearch, bool IsServerLocal, string SourcingName, int ApiURLKeyId)
        {
            List<APIExternalLinksDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.APIExternalLinks.Source1(clientId, partSearch, IsServerLocal, SourcingName, ApiURLKeyId);
            if (lstDetails == null)
            {
                return new List<APIExternalLinks>();
            }
            else
            {
                List<APIExternalLinks> lst = new List<APIExternalLinks>();
                foreach (APIExternalLinksDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.APIExternalLinks obj = new Rebound.GlobalTrader.BLL.APIExternalLinks();
                    obj.FElectronicsId = objDetails.FElectronicsId;
                    obj.FullPart = objDetails.FullPart;
                    obj.Part = objDetails.Part;
                    obj.ManufacturerNo = objDetails.ManufacturerNo;
                    obj.DateCode = objDetails.DateCode;
                    obj.ProductNo = objDetails.ProductNo;
                    obj.PackageNo = objDetails.PackageNo;
                    obj.Quantity = objDetails.Quantity;
                    obj.Price = objDetails.Price;
                    obj.OriginalEntryDate = objDetails.OriginalEntryDate;
                    obj.Salesman = objDetails.Salesman;
                    obj.SupplierNo = objDetails.SupplierNo;
                    obj.CurrencyNo = objDetails.CurrencyNo;
                    obj.ROHS = objDetails.ROHS;
                    obj.UpdatedBy = objDetails.UpdatedBy;
                    obj.DLUP = objDetails.DLUP;
                    //obj.OfferStatusNo = objDetails.OfferStatusNo;
                    //obj.OfferStatusChangeDate = objDetails.OfferStatusChangeDate;
                    //obj.OfferStatusChangeLoginNo = objDetails.OfferStatusChangeLoginNo;
                    obj.ManufacturerCode = objDetails.ManufacturerCode;
                    obj.ProductName = objDetails.ProductName;
                    obj.CurrencyCode = objDetails.CurrencyCode;
                    obj.CurrencyDescription = objDetails.CurrencyDescription;
                    obj.SupplierName = objDetails.SupplierName;
                    obj.ManufacturerName = objDetails.ManufacturerName;
                    obj.SupplierEmail = objDetails.SupplierEmail;
                    obj.SalesmanName = objDetails.SalesmanName;
                    obj.OfferStatusChangeEmployeeName = objDetails.OfferStatusChangeEmployeeName;
                    obj.PackageName = objDetails.PackageName;
                    obj.Notes = objDetails.Notes;
                    obj.ClientNo = objDetails.ClientNo;
                    obj.ClientId = objDetails.ClientId;
                    obj.ClientName = objDetails.ClientName;
                    obj.ClientDataVisibleToOthers = objDetails.ClientDataVisibleToOthers;
                    //[001] code start
                    obj.SupplierType = objDetails.SupplierType;
                    //[001] code end
                    obj.ClientCode = objDetails.ClientCode;
                    obj.IsSourcingHub = objDetails.IsSourcingHub;
                    obj.SupplierMessage = objDetails.SupplierMessage;
                    obj.SPQ = objDetails.SPQ;

                    obj.MOQ = objDetails.SupplierMOQ;
                    obj.SupplierPackageType = objDetails.SupplierPackageType;
                    obj.Description = objDetails.Description;
                    obj.SupplierLTB = objDetails.SupplierLTB;
                    obj.UpliftPrice = objDetails.UpliftPrice;
                    obj.VirtualCostPrice = objDetails.VirtualCostPrice;
                    obj.GTDate = objDetails.GTDate;
                    obj.ECCN = objDetails.ECCN;
                    obj.PublishDate = objDetails.PublishDate;
                    obj.Reference = objDetails.Reference;
                    obj.UnitCostPrice = objDetails.UnitCostPrice;
                    obj.ApiShortName = objDetails.ApiSourceName;
                    obj.ApiName = objDetails.ApiName;
                    obj.ApiSourceName = objDetails.ApiSourceName;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }

        public static List<ApiKeyDetails> APIExternalLinksDetails(System.String sourcingName, System.String moduleName, int clientId, bool hasServerLocal)
        {
            List<ApiKeyValueDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.APIExternalLinks.APIExternalLinksDetails(sourcingName, moduleName, clientId, hasServerLocal);
            if (lstDetails == null)
            {
                return new List<ApiKeyDetails>();
            }
            else
            {
                List<ApiKeyDetails> lst = new List<ApiKeyDetails>();
                foreach (ApiKeyValueDetails objDetails in lstDetails)
                {
                    ApiKeyDetails obj = new ApiKeyDetails();
                    obj.ApiURLKeyId = objDetails.ApiURLKeyId;
                    obj.URL = objDetails.URL;
                    obj.HostName = objDetails.HostName;
                    obj.Licensekey = objDetails.Licensekey;
                    obj.ApiShortName = objDetails.ApiShortName;
                    obj.ApiName = objDetails.ApiName;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
                //return lstDetails;
            }
            //return null;
        }

        public static int InsertMultipleAPIJson(System.String resultJson, string ApiName, int clientId, bool hasServerLocal)
        {
            int result = Rebound.GlobalTrader.DAL.SiteProvider.APIExternalLinks.InsertMultipleAPIJson(resultJson, ApiName, clientId, hasServerLocal);
            //if (result == 0)
            //    return 0;F
            //else
            //    return result;
            return result;
        }

        public static List<APIExternalLinks> ApiNotRespond(System.Int32? clientId, System.String partSearch, bool IsServerLocal, string SourcingName, string ApiName)
        {
            List<APIExternalLinksDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.APIExternalLinks.ApiNotRespond(clientId, partSearch, IsServerLocal, SourcingName, ApiName);
            //if (lstDetails == null)
            //{
            //    return new List<APIExternalLinks>();
            //}
            //else
            //{
            List<APIExternalLinks> lst = new List<APIExternalLinks>();
            //foreach (APIExternalLinksDetails objDetails in lstDetails)
            //{
            Rebound.GlobalTrader.BLL.APIExternalLinks obj = new Rebound.GlobalTrader.BLL.APIExternalLinks();
            obj.FElectronicsId = lstDetails[0].FElectronicsId;
            obj.FullPart = lstDetails[0].FullPart;
            obj.Part = lstDetails[0].Part;
            obj.ManufacturerNo = lstDetails[0].ManufacturerNo;
            obj.DateCode = lstDetails[0].DateCode;
            obj.ProductNo = lstDetails[0].ProductNo;
            obj.PackageNo = lstDetails[0].PackageNo;
            obj.Quantity = lstDetails[0].Quantity;
            obj.Price = lstDetails[0].Price;
            obj.OriginalEntryDate = lstDetails[0].OriginalEntryDate;
            obj.Salesman = lstDetails[0].Salesman;
            obj.SupplierNo = lstDetails[0].SupplierNo;
            obj.CurrencyNo = lstDetails[0].CurrencyNo;
            obj.ROHS = lstDetails[0].ROHS;
            obj.UpdatedBy = lstDetails[0].UpdatedBy;
            obj.DLUP = lstDetails[0].DLUP;
            obj.ManufacturerCode = lstDetails[0].ManufacturerCode;
            obj.ProductName = lstDetails[0].ProductName;
            obj.CurrencyCode = lstDetails[0].CurrencyCode;
            obj.CurrencyDescription = lstDetails[0].CurrencyDescription;
            obj.SupplierName = lstDetails[0].SupplierName;
            obj.ManufacturerName = lstDetails[0].ManufacturerName;
            obj.SupplierEmail = lstDetails[0].SupplierEmail;
            obj.SalesmanName = lstDetails[0].SalesmanName;
            obj.OfferStatusChangeEmployeeName = lstDetails[0].OfferStatusChangeEmployeeName;
            obj.PackageName = lstDetails[0].PackageName;
            obj.Notes = lstDetails[0].Notes;
            obj.ClientNo = lstDetails[0].ClientNo;
            obj.ClientId = lstDetails[0].ClientId;
            obj.ClientName = lstDetails[0].ClientName;
            obj.ClientDataVisibleToOthers = lstDetails[0].ClientDataVisibleToOthers;
            //[001] code start
            obj.SupplierType = lstDetails[0].SupplierType;
            //[001] code end
            obj.ClientCode = lstDetails[0].ClientCode;
            obj.IsSourcingHub = lstDetails[0].IsSourcingHub;
            obj.SupplierMessage = lstDetails[0].SupplierMessage;
            obj.SPQ = lstDetails[0].SPQ;
            obj.MOQ = lstDetails[0].SupplierMOQ;
            obj.SupplierPackageType = lstDetails[0].SupplierPackageType;
            obj.Description = lstDetails[0].Description;
            obj.SupplierLTB = lstDetails[0].SupplierLTB;
            obj.UpliftPrice = lstDetails[0].UpliftPrice;
            obj.VirtualCostPrice = lstDetails[0].VirtualCostPrice;
            obj.GTDate = lstDetails[0].GTDate;
            obj.ECCN = lstDetails[0].ECCN;
            obj.PublishDate = lstDetails[0].PublishDate;
            obj.Reference = lstDetails[0].Reference;
            obj.UnitCostPrice = lstDetails[0].UnitCostPrice;
            obj.ApiShortName = (SourcingName == "" ? "DB" : SourcingName);
            obj.ApiName = ApiName;
            lst.Add(obj);
            obj = null;
            //}
            //lstDetails = null;
            return lst;
            //}
        }

        public static List<APIExternalLinks> Source(System.String resultJson, System.Int32? clientId, System.String partSearch, bool IsServerLocal, string SourcingName, int ApiURLKeyId, System.Int32? UserId)
        {
            List<APIExternalLinksDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.APIExternalLinks.Source(resultJson, clientId, partSearch, IsServerLocal, SourcingName, ApiURLKeyId, UserId);
            if (lstDetails == null)
            {
                return new List<APIExternalLinks>();
            }
            else
            {

                List<APIExternalLinks> lst = new List<APIExternalLinks>();
                foreach (APIExternalLinksDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.APIExternalLinks obj = new Rebound.GlobalTrader.BLL.APIExternalLinks();
                    obj.FElectronicsId = objDetails.FElectronicsId;
                    obj.FullPart = objDetails.FullPart;
                    obj.Part = objDetails.Part;
                    obj.ManufacturerNo = objDetails.ManufacturerNo;
                    obj.DateCode = objDetails.DateCode;
                    obj.ProductNo = objDetails.ProductNo;
                    obj.PackageNo = objDetails.PackageNo;
                    obj.Quantity = objDetails.Quantity;
                    obj.Price = objDetails.Price;
                    obj.OriginalEntryDate = objDetails.OriginalEntryDate;
                    obj.Salesman = objDetails.Salesman;
                    obj.SupplierNo = objDetails.SupplierNo;
                    obj.CurrencyNo = objDetails.CurrencyNo;
                    obj.ROHS = objDetails.ROHS;
                    obj.UpdatedBy = objDetails.UpdatedBy;
                    obj.DLUP = objDetails.DLUP;
                    //obj.OfferStatusNo = objDetails.OfferStatusNo;
                    //obj.OfferStatusChangeDate = objDetails.OfferStatusChangeDate;
                    //obj.OfferStatusChangeLoginNo = objDetails.OfferStatusChangeLoginNo;
                    obj.ManufacturerCode = objDetails.ManufacturerCode;
                    obj.ProductName = objDetails.ProductName;
                    obj.CurrencyCode = objDetails.CurrencyCode;
                    obj.CurrencyDescription = objDetails.CurrencyDescription;
                    obj.SupplierName = objDetails.SupplierName;
                    obj.ManufacturerName = objDetails.ManufacturerName;
                    obj.SupplierEmail = objDetails.SupplierEmail;
                    obj.SalesmanName = objDetails.SalesmanName;
                    obj.OfferStatusChangeEmployeeName = objDetails.OfferStatusChangeEmployeeName;
                    obj.PackageName = objDetails.PackageName;
                    obj.Notes = objDetails.Notes;
                    obj.ClientNo = objDetails.ClientNo;
                    obj.ClientId = objDetails.ClientId;
                    obj.ClientName = objDetails.ClientName;
                    obj.ClientDataVisibleToOthers = objDetails.ClientDataVisibleToOthers;
                    //[001] code start
                    obj.SupplierType = objDetails.SupplierType;
                    //[001] code end
                    obj.ClientCode = objDetails.ClientCode;
                    obj.IsSourcingHub = objDetails.IsSourcingHub;
                    obj.SupplierMessage = objDetails.SupplierMessage;
                    obj.SPQ = objDetails.SPQ;

                    obj.MOQ = objDetails.SupplierMOQ;
                    obj.SupplierPackageType = objDetails.SupplierPackageType;
                    obj.Description = objDetails.Description;
                    obj.SupplierLTB = objDetails.SupplierLTB;
                    obj.UpliftPrice = objDetails.UpliftPrice;
                    obj.VirtualCostPrice = objDetails.VirtualCostPrice;
                    obj.GTDate = objDetails.GTDate;
                    obj.ECCN = objDetails.ECCN;
                    obj.PublishDate = objDetails.PublishDate;
                    obj.Reference = objDetails.Reference;
                    obj.UnitCostPrice = objDetails.UnitCostPrice;
                    obj.ApiShortName = (SourcingName == "" ? "DB" : SourcingName);
                    obj.ApiName = objDetails.ApiName;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }

        public static List<APIExternalLinks> SourceMultipleParts(System.String resultJson, System.Int32? clientId, System.String partSearch, bool IsServerLocal, string SourcingName, int ApiURLKeyId)
        {
            List<APIExternalLinksDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.APIExternalLinks.SourceMultipleParts(resultJson, clientId, partSearch, IsServerLocal, SourcingName, ApiURLKeyId);
            if (lstDetails == null)
            {
                return new List<APIExternalLinks>();
            }
            else
            {

                List<APIExternalLinks> lst = new List<APIExternalLinks>();
                foreach (APIExternalLinksDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.APIExternalLinks obj = new Rebound.GlobalTrader.BLL.APIExternalLinks();
                    obj.FElectronicsId = objDetails.FElectronicsId;
                    obj.FullPart = objDetails.FullPart;
                    obj.Part = objDetails.Part;
                    obj.ManufacturerNo = objDetails.ManufacturerNo;
                    obj.DateCode = objDetails.DateCode;
                    obj.ProductNo = objDetails.ProductNo;
                    obj.PackageNo = objDetails.PackageNo;
                    obj.Quantity = objDetails.Quantity;
                    obj.Price = objDetails.Price;
                    obj.OriginalEntryDate = objDetails.OriginalEntryDate;
                    obj.Salesman = objDetails.Salesman;
                    obj.SupplierNo = objDetails.SupplierNo;
                    obj.CurrencyNo = objDetails.CurrencyNo;
                    obj.ROHS = objDetails.ROHS;
                    obj.UpdatedBy = objDetails.UpdatedBy;
                    obj.DLUP = objDetails.DLUP;
                    //obj.OfferStatusNo = objDetails.OfferStatusNo;
                    //obj.OfferStatusChangeDate = objDetails.OfferStatusChangeDate;
                    //obj.OfferStatusChangeLoginNo = objDetails.OfferStatusChangeLoginNo;
                    obj.ManufacturerCode = objDetails.ManufacturerCode;
                    obj.ProductName = objDetails.ProductName;
                    obj.CurrencyCode = objDetails.CurrencyCode;
                    obj.CurrencyDescription = objDetails.CurrencyDescription;
                    obj.SupplierName = objDetails.SupplierName;
                    obj.ManufacturerName = objDetails.ManufacturerName;
                    obj.SupplierEmail = objDetails.SupplierEmail;
                    obj.SalesmanName = objDetails.SalesmanName;
                    obj.OfferStatusChangeEmployeeName = objDetails.OfferStatusChangeEmployeeName;
                    obj.PackageName = objDetails.PackageName;
                    obj.Notes = objDetails.Notes;
                    obj.ClientNo = objDetails.ClientNo;
                    obj.ClientId = objDetails.ClientId;
                    obj.ClientName = objDetails.ClientName;
                    obj.ClientDataVisibleToOthers = objDetails.ClientDataVisibleToOthers;
                    //[001] code start
                    obj.SupplierType = objDetails.SupplierType;
                    //[001] code end
                    obj.ClientCode = objDetails.ClientCode;
                    obj.IsSourcingHub = objDetails.IsSourcingHub;
                    obj.SupplierMessage = objDetails.SupplierMessage;
                    obj.SPQ = objDetails.SPQ;

                    obj.MOQ = objDetails.SupplierMOQ;
                    obj.SupplierPackageType = objDetails.SupplierPackageType;
                    obj.Description = objDetails.Description;
                    obj.SupplierLTB = objDetails.SupplierLTB;
                    obj.UpliftPrice = objDetails.UpliftPrice;
                    obj.VirtualCostPrice = objDetails.VirtualCostPrice;
                    obj.GTDate = objDetails.GTDate;
                    obj.ECCN = objDetails.ECCN;
                    obj.PublishDate = objDetails.PublishDate;
                    obj.Reference = objDetails.Reference;
                    obj.UnitCostPrice = objDetails.UnitCostPrice;
                    obj.ApiShortName = (SourcingName == "" ? "DB" : SourcingName);
                    obj.ApiName = objDetails.ApiName;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }

        public static List<LyticaAPI> InsretLyticaAPI(string APIResponseJson, int? UpdatedBy)
        {
            List<LyticaAPI> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.APIExternalLinks.InsretLyticaAPI(APIResponseJson,UpdatedBy);
            //if (lstDetails == null)
            //{
            //    return new List<LyticaAPI>();
            //}
            return lstDetails;
            //else
            //{

            //    List<LyticaAPI> lst = new List<LyticaAPI>();
            //    foreach (LyticaAPI objDetails in lstDetails)
            //    {
            //        LyticaAPI obj = new LyticaAPI();
            //        obj.LyticaAPIId = GetReaderValue_Int32(reader, "SupplierAPIID", 0);
            //        obj.Commodity = GetReaderValue_String(reader, "Commodity", "");
            //        obj.OriginalPartSearched = GetReaderValue_String(reader, "OriginalPartSearched", "");
            //        obj.Manufacturer = GetReaderValue_String(reader, "Manufacturer", "");
            //        obj.AveragePrice = GetReaderValue_NullableDouble(reader, "AveragePrice", 0);
            //        obj.TargetPrice = GetReaderValue_NullableDouble(reader, "TargetPrice", 0);
            //        obj.MarketLeading = GetReaderValue_NullableDouble(reader, "MarketLeading", 0);
            //        obj.LifeCycle = GetReaderValue_String(reader, "LifeCycle", "");
            //        obj.lifeCycleStatus = GetReaderValue_String(reader, "lifeCycleStatus", "");
            //        obj.OverAllRisk = GetReaderValue_String(reader, "OverAllRisk", "");
            //        obj.PartBreadth = GetReaderValue_String(reader, "PartBreadth", "");
            //        obj.ManufacturerBreadth = GetReaderValue_String(reader, "ManufacturerBreadth", "");
            //        obj.DueDiligence = GetReaderValue_String(reader, "DueDiligence", "");
            //        obj.PartConcentration = GetReaderValue_String(reader, "PartConcentration", "");
            //        obj.OriginalEntryDate = GetReaderValue_NullableDateTime(reader, "OriginalEntryDate", null);
            //        lst.Add(obj);
            //        obj = null;
            //    }
            //    lstDetails = null;
            //    return lst;
            //}
        }
    
        public static List<APIExternalLinks> SourceFromDBMultipleParts(System.Int32? clientId, System.String partSearch, bool IsServerLocal, string SourcingName, int ApiURLKeyId)
        {
            List<APIExternalLinksDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.APIExternalLinks.SourceFromDBMultipleParts(clientId, partSearch, IsServerLocal, SourcingName, ApiURLKeyId);
            if (lstDetails == null)
            {
                return new List<APIExternalLinks>();
            }
            else
            {
                List<APIExternalLinks> lst = new List<APIExternalLinks>();
                foreach (APIExternalLinksDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.APIExternalLinks obj = new Rebound.GlobalTrader.BLL.APIExternalLinks();
                    obj.FElectronicsId = objDetails.FElectronicsId;
                    obj.FullPart = objDetails.FullPart;
                    obj.Part = objDetails.Part;
                    obj.ManufacturerNo = objDetails.ManufacturerNo;
                    obj.DateCode = objDetails.DateCode;
                    obj.ProductNo = objDetails.ProductNo;
                    obj.PackageNo = objDetails.PackageNo;
                    obj.Quantity = objDetails.Quantity;
                    obj.Price = objDetails.Price;
                    obj.OriginalEntryDate = objDetails.OriginalEntryDate;
                    obj.Salesman = objDetails.Salesman;
                    obj.SupplierNo = objDetails.SupplierNo;
                    obj.CurrencyNo = objDetails.CurrencyNo;
                    obj.ROHS = objDetails.ROHS;
                    obj.UpdatedBy = objDetails.UpdatedBy;
                    obj.DLUP = objDetails.DLUP;
                    //obj.OfferStatusNo = objDetails.OfferStatusNo;
                    //obj.OfferStatusChangeDate = objDetails.OfferStatusChangeDate;
                    //obj.OfferStatusChangeLoginNo = objDetails.OfferStatusChangeLoginNo;
                    obj.ManufacturerCode = objDetails.ManufacturerCode;
                    obj.ProductName = objDetails.ProductName;
                    obj.CurrencyCode = objDetails.CurrencyCode;
                    obj.CurrencyDescription = objDetails.CurrencyDescription;
                    obj.SupplierName = objDetails.SupplierName;
                    obj.ManufacturerName = objDetails.ManufacturerName;
                    obj.SupplierEmail = objDetails.SupplierEmail;
                    obj.SalesmanName = objDetails.SalesmanName;
                    obj.OfferStatusChangeEmployeeName = objDetails.OfferStatusChangeEmployeeName;
                    obj.PackageName = objDetails.PackageName;
                    obj.Notes = objDetails.Notes;
                    obj.ClientNo = objDetails.ClientNo;
                    obj.ClientId = objDetails.ClientId;
                    obj.ClientName = objDetails.ClientName;
                    obj.ClientDataVisibleToOthers = objDetails.ClientDataVisibleToOthers;
                    //[001] code start
                    obj.SupplierType = objDetails.SupplierType;
                    //[001] code end
                    obj.ClientCode = objDetails.ClientCode;
                    obj.IsSourcingHub = objDetails.IsSourcingHub;
                    obj.SupplierMessage = objDetails.SupplierMessage;
                    obj.SPQ = objDetails.SPQ;

                    obj.MOQ = objDetails.SupplierMOQ;
                    obj.SupplierPackageType = objDetails.SupplierPackageType;
                    obj.Description = objDetails.Description;
                    obj.SupplierLTB = objDetails.SupplierLTB;
                    obj.UpliftPrice = objDetails.UpliftPrice;
                    obj.VirtualCostPrice = objDetails.VirtualCostPrice;
                    obj.GTDate = objDetails.GTDate;
                    obj.ECCN = objDetails.ECCN;
                    obj.PublishDate = objDetails.PublishDate;
                    obj.Reference = objDetails.Reference;
                    obj.UnitCostPrice = objDetails.UnitCostPrice;
                    obj.CurrencyCode = objDetails.CurrencyCode;
                    obj.ApiShortName = objDetails.ApiSourceName;
                    obj.ApiName = objDetails.ApiName;
                    obj.ApiSourceName = objDetails.ApiSourceName;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }

        public static List<APIExternalLinks> GetDigiKeyApi(System.Int32? clientId, System.String partSearch, bool IsServerLocal, string SourcingName, int ApiURLKeyId)
        {
            List<APIExternalLinksDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.APIExternalLinks.GetDigiKeyApi(clientId, partSearch, IsServerLocal, SourcingName, ApiURLKeyId);
            if (lstDetails == null)
            {
                return new List<APIExternalLinks>();
            }
            else
            {
                List<APIExternalLinks> lst = new List<APIExternalLinks>();
                foreach (APIExternalLinksDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.APIExternalLinks obj = new Rebound.GlobalTrader.BLL.APIExternalLinks();
                    obj.FElectronicsId = objDetails.FElectronicsId;
                    obj.FullPart = objDetails.FullPart;
                    obj.Part = objDetails.Part;
                    obj.ManufacturerNo = objDetails.ManufacturerNo;
                    obj.DateCode = objDetails.DateCode;
                    obj.ProductNo = objDetails.ProductNo;
                    obj.PackageNo = objDetails.PackageNo;
                    obj.Quantity = objDetails.Quantity;
                    obj.Price = objDetails.Price;
                    obj.OriginalEntryDate = objDetails.OriginalEntryDate;
                    obj.Salesman = objDetails.Salesman;
                    obj.SupplierNo = objDetails.SupplierNo;
                    obj.CurrencyNo = objDetails.CurrencyNo;
                    obj.ROHS = objDetails.ROHS;
                    obj.UpdatedBy = objDetails.UpdatedBy;
                    obj.DLUP = objDetails.DLUP;
                    //obj.OfferStatusNo = objDetails.OfferStatusNo;
                    //obj.OfferStatusChangeDate = objDetails.OfferStatusChangeDate;
                    //obj.OfferStatusChangeLoginNo = objDetails.OfferStatusChangeLoginNo;
                    obj.ManufacturerCode = objDetails.ManufacturerCode;
                    obj.ProductName = objDetails.ProductName;
                    obj.CurrencyCode = objDetails.CurrencyCode;
                    obj.CurrencyDescription = objDetails.CurrencyDescription;
                    obj.SupplierName = objDetails.SupplierName;
                    obj.ManufacturerName = objDetails.ManufacturerName;
                    obj.SupplierEmail = objDetails.SupplierEmail;
                    obj.SalesmanName = objDetails.SalesmanName;
                    obj.OfferStatusChangeEmployeeName = objDetails.OfferStatusChangeEmployeeName;
                    obj.PackageName = objDetails.PackageName;
                    obj.Notes = objDetails.Notes;
                    obj.ClientNo = objDetails.ClientNo;
                    obj.ClientId = objDetails.ClientId;
                    obj.ClientName = objDetails.ClientName;
                    obj.ClientDataVisibleToOthers = objDetails.ClientDataVisibleToOthers;
                    //[001] code start
                    obj.SupplierType = objDetails.SupplierType;
                    //[001] code end
                    obj.ClientCode = objDetails.ClientCode;
                    obj.IsSourcingHub = objDetails.IsSourcingHub;
                    obj.SupplierMessage = objDetails.SupplierMessage;
                    obj.SPQ = objDetails.SPQ;

                    obj.MOQ = objDetails.SupplierMOQ;
                    obj.SupplierPackageType = objDetails.SupplierPackageType;
                    obj.Description = objDetails.Description;
                    obj.SupplierLTB = objDetails.SupplierLTB;
                    obj.UpliftPrice = objDetails.UpliftPrice;
                    obj.VirtualCostPrice = objDetails.VirtualCostPrice;
                    obj.GTDate = objDetails.GTDate;
                    obj.ECCN = objDetails.ECCN;
                    obj.PublishDate = objDetails.PublishDate;
                    obj.Reference = objDetails.Reference;
                    obj.UnitCostPrice = objDetails.UnitCostPrice;
                    obj.ApiShortName = objDetails.ApiSourceName;
                    obj.ApiName = objDetails.ApiName;
                    obj.ApiSourceName = objDetails.ApiSourceName;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }

        #endregion
    }
}
