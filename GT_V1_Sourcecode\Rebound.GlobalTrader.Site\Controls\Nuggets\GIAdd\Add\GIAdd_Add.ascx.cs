using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.Forms {
	public partial class GIAdd_Add : Base {

		#region Locals

		#endregion

		#region Overrides

		/// <summary>
		/// OnInit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
			TitleText = Functions.GetGlobalResource("FormTitles", "GIAdd_Add");
			AddScriptReference("Controls.Nuggets.GIAdd.Add.GIAdd_Add.js");
		}

		/// <summary>
		/// OnPreRender
		/// </summary>
		/// <param name="e"></param>
		protected override void OnPreRender(EventArgs e) {
			SetupScriptDescriptors();
			base.OnPreRender(e);
		}

		#endregion

		/// <summary>
		/// Setup Script Descriptors
		/// </summary>
		private void SetupScriptDescriptors() {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Forms.GIAdd_Add", ctlDesignBase.ClientID);
			_scScriptControlDescriptor.AddElementProperty("ibtnSend", FindIconButton("ibtnSend").ClientID);
			if (FooterLinksHolder != null) _scScriptControlDescriptor.AddElementProperty("ibtnSend_Footer", FindFooterIconButton("ibtnSend").ClientID);
			_scScriptControlDescriptor.AddElementProperty("ibtnContinue", (FindIconButton("ibtnContinue").ClientID));
			if (FooterLinksHolder != null) _scScriptControlDescriptor.AddElementProperty("ibtnContinue_Footer", (FindFooterIconButton("ibtnContinue").ClientID));
			_scScriptControlDescriptor.AddComponentProperty("ctlSelectPO", ((ItemSearch.Base)FindContentControl("ctlSelectPO")).ctlDesignBase.ClientID);
			_scScriptControlDescriptor.AddProperty("intLoginID", SessionManager.LoginID);
			_scScriptControlDescriptor.AddProperty("intCompanyID", _objQSManager.CompanyID);
			_scScriptControlDescriptor.AddProperty("strCompanyName", _objQSManager.CompanyName);
		}

	}
}
