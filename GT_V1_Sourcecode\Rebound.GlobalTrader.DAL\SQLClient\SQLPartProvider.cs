﻿/*
 Marker     ChangedBy       ChangedDate     Remarks
 [001]      <PERSON><PERSON><PERSON>     25-Jan-2019     Sales Dashboard Changes/ Req Dashboard Headings
 */
using System;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using System.Web.Caching;
using System.Data.Common;

namespace Rebound.GlobalTrader.DAL.SqlClient {
	public class SqlPartProvider : PartProvider {
        /// <summary>
        /// AutoSearch 
		/// Calls [usp_autosearch_Part]
        /// </summary>
		public override List<PartDetails> AutoSearch(System.Int32? clientId, System.String partSearch) {
			SqlConnection cn = null;
			SqlCommand cmd = null;
			try {
				cn = new SqlConnection(this.ConnectionString);
				cmd = new SqlCommand("usp_autosearch_Part", cn);
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.CommandTimeout = 60;
				cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
				cmd.Parameters.Add("@PartSearch", SqlDbType.NVarChar).Value = partSearch;
				cn.Open();
				DbDataReader reader = ExecuteReader(cmd);
				List<PartDetails> lst = new List<PartDetails>();
				while (reader.Read()) {
					PartDetails obj = new PartDetails();
                  
                    obj.PartName = GetReaderValue_String(reader, "PartName", "");
                   
					lst.Add(obj);
					obj = null;
				}
				return lst;
			} catch (SqlException sqlex) {
				//LogException(sqlex);
				throw new Exception("Failed to get Parts", sqlex);
			} finally {
				cmd.Dispose();
				cn.Close();
				cn.Dispose();
			}
		}
        /// <summary>
        /// AutoSearch 
		/// Calls [usp_autosearch_Part_BomManager]
        /// </summary>
		public override List<PartDetails> AutoSearchBomManager(System.Int32? clientId, System.String partSearch)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_autosearch_Part_BOMManager", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@PartSearch", SqlDbType.NVarChar).Value = partSearch;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<PartDetails> lst = new List<PartDetails>();
                while (reader.Read())
                {
                    PartDetails obj = new PartDetails();

                    obj.PartName = GetReaderValue_String(reader, "PartName", "");
                    obj.ProductNo = GetReaderValue_NullableInt32(reader, "ProductNo", null);
                    obj.ProductDescription = GetReaderValue_String(reader, "ProductDescription", "");
                    obj.ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null);
                    obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Parts", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// AutoSearch 
        /// Calls [usp_autosearch_Part_New]
        /// </summary>
        public override List<PartDetails> CustReqPartSearch(System.Int32? clientId, System.String partSearch)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_autosearch_Part_New", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@PartSearch", SqlDbType.NVarChar).Value = partSearch;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<PartDetails> lst = new List<PartDetails>();
                while (reader.Read())
                {
                    PartDetails obj = new PartDetails();
                    obj.PartNameWithManufacture = GetReaderValue_String(reader, "PartName", "");
                    obj.PartName = GetReaderValue_String(reader, "Parts", "");
                    obj.ManufacturerNo = GetReaderValue_Int32(reader, "ManufacturerId", 0);
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Parts", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        /// <summary>
        /// AutoSearch 
        /// Calls [usp_autosearch_CustReqPart]
        /// </summary>
        public override List<PartDetails> CustReqPart(System.Int32? clientId, System.String partSearch, System.String ids, System.String DateCode)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            string ManufacturerNo = "";
            string ProductNumber = "";
            string PackageNumber = "";
            string[] values = ids.Split('|');// ids.Split(",");
            ManufacturerNo = values[0].Trim();
            ProductNumber = values[1].Trim();
            PackageNumber = values[2].Trim();

            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_autosearch_CustReqPart", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@PartSearch", SqlDbType.NVarChar).Value = partSearch;
                cmd.Parameters.Add("@ManufacturerNo", SqlDbType.Int).Value = Convert.ToInt32(ManufacturerNo);
                cmd.Parameters.Add("@ProductNumber", SqlDbType.Int).Value = Convert.ToInt32(ProductNumber);
                cmd.Parameters.Add("@PackageNumber", SqlDbType.Int).Value = Convert.ToInt32(PackageNumber);
                cmd.Parameters.Add("@DateCode", SqlDbType.NVarChar).Value = DateCode;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<PartDetails> lst = new List<PartDetails>();
                while (reader.Read())
                {
                    PartDetails obj = new PartDetails();
                    obj.PartNameWithManufacture = GetReaderValue_String(reader, "PartName", "");
                    obj.PartName = GetReaderValue_String(reader, "Parts", "");
                    obj.ManufacturerNo = GetReaderValue_Int32(reader, "ManufacturerId", 0);
                    obj.Productname = GetReaderValue_String(reader, "ProductName", "");
                    obj.Packagename = GetReaderValue_String(reader, "PackageName", "");
                    obj.DateCode = GetReaderValue_String(reader, "DateCode", "");
                    obj.ProductNo = GetReaderValue_Int32(reader, "ProductNo", 0);
                    obj.PackageNo = GetReaderValue_Int32(reader, "PackageNo", 0);
                    obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
                    obj.ROHSNo = GetReaderValue_Int32(reader, "ROHS", 0);

                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Parts", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// AutoSearch 
        /// Calls [usp_autosearch_Part_New]
        /// </summary>
        public override List<PartDetails> CustReqPartsGRID(System.Int32? clientId, System.String partSearch)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                partSearch = partSearch + "%";
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_PartSearch_GRID", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@PartSearch", SqlDbType.NVarChar).Value = partSearch;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<PartDetails> lst = new List<PartDetails>();
                while (reader.Read())
                {
                    PartDetails obj = new PartDetails();
                    obj.PartNameWithManufacture = GetReaderValue_String(reader, "PartName", "");
                    obj.PartName = GetReaderValue_String(reader, "Parts", "");
                    obj.ManufacturerNo = GetReaderValue_Int32(reader, "ManufacturerId", 0);
                    obj.ProductNo = GetReaderValue_Int32(reader, "ProductNo", 0);
                    obj.PackageNo = GetReaderValue_Int32(reader, "PackageNo", 0);
                    obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
                    obj.Productname = GetReaderValue_String(reader, "ProductName", "");
                    obj.Packagename = GetReaderValue_String(reader, "PackageName", "");
                    obj.DateCode = GetReaderValue_String(reader, "DateCode", "");
                    obj.DateCodeOriginal = GetReaderValue_String(reader, "DateCodeOriginal", "");
                    obj.ROHSNo = GetReaderValue_Int32(reader, "ROHS", 0);
                    obj.ProductDescription = GetReaderValue_String(reader, "ProductDescription", "");
                    obj.ProductInactive = GetReaderValue_NullableBoolean(reader, "ProductInactive", false);
                    //[001] start
                    obj.StockId = GetReaderValue_Int32(reader, "StockId", 0);
                    obj.ResultType = GetReaderValue_String(reader, "ResultType", "");
                    //[001] end
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Parts", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        //IHS Bind Data from TBIHSPART Table code start

        /// <summary>
        /// AutoSearch From IHS API Service
        /// Calls [usp_PartSearch_GRIDFromIHSAPI]
        /// </summary>
        public override List<PartDetails> CustReqPartsGRIDIHSAPI(System.Int32? clientId, System.String partSearch, System.String searchType, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                //partSearch = partSearch + "%";
                cn = new SqlConnection(this.ConnectionString);
                //cmd = new SqlCommand("usp_PartSearch_GRIDFromIHSAPI", cn);
                cmd = new SqlCommand("usp_itemsearch_GRIDBindFrom_IHSAPI", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@PartSearch", SqlDbType.NVarChar).Value = partSearch;
                cmd.Parameters.Add("@SearchType", SqlDbType.NVarChar).Value = searchType;
                cmd.Parameters.Add("@OrderBy", SqlDbType.Int).Value = orderBy;
                cmd.Parameters.Add("@SortDir", SqlDbType.Int).Value = sortDir;
                cmd.Parameters.Add("@PageIndex", SqlDbType.Int).Value = pageIndex;
                cmd.Parameters.Add("@PageSize", SqlDbType.Int).Value = pageSize;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<PartDetails> lst = new List<PartDetails>();
                while (reader.Read())
                {
                    PartDetails obj = new PartDetails();
                    obj.PartNameWithManufacture = GetReaderValue_String(reader, "PartName", "");
                    obj.PartName = GetReaderValue_String(reader, "Parts", "");
                    obj.ManufacturerNo = GetReaderValue_Int32(reader, "ManufacturerId", 0);
                    obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
                    obj.ROHSNo = GetReaderValue_Int32(reader, "ROHS", 0);
                    obj.CountryOfOrigin = GetReaderValue_String(reader, "CountryOfOrigin", "");
                    obj.CountryOfOriginNo = GetReaderValue_Int32(reader, "CountryOfOriginNo", 0);
                    obj.HTSCode = GetReaderValue_String(reader, "HTSCode", "");
                    obj.AveragePrice = GetReaderValue_Double(reader, "AveragePrice", 0);
                    obj.Packaging = GetReaderValue_String(reader, "Packaging", "");
                    obj.PackagingSize = GetReaderValue_String(reader, "PackagingSize", "");
                    obj.IHSPartsId = GetReaderValue_Int32(reader, "IHSPartsId", 0);
                    obj.ResultType = GetReaderValue_String(reader, "ResultType", "");
                    obj.ROHSName = GetReaderValue_String(reader, "ROHSName", "");
                    obj.Descriptions = GetReaderValue_String(reader, "Descriptions", "");
                    obj.PartStatus = GetReaderValue_String(reader, "PartStatus", "");
                    obj.IHSCurrencyCode = GetReaderValue_String(reader, "ColPriceCurrency", "");
                    obj.ProductDescription = GetReaderValue_String(reader, "ProductName", "");
                    obj.ProductNo = GetReaderValue_NullableInt32(reader, "ProductNo", 0);
                    obj.IHSProductDescription = GetReaderValue_String(reader, "IHSProductName", "");
                    obj.IHSDutyCode = GetReaderValue_String(reader, "IHSDutyCode", "");
                    obj.RowCnt = GetReaderValue_NullableInt32(reader, "RowCnt", null);
                   // obj.RowNum = GetReaderValue_NullableInt32(reader, "RowNum", 0);
                    obj.ManufacturerFullName = GetReaderValue_String(reader, "ManufacturerFullName", "");
                    obj.IHSProduct = GetReaderValue_String(reader, "IHSProduct", "");
                    obj.ECCNCode = GetReaderValue_String(reader, "ECCNCode", "");

                    //[001] end
                    lst.Add(obj);
                    obj = null;

  

                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Parts tbihsparts Table", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        //IHS Bind Data from TBIHSPART Table code end

        /// <summary>
        /// AutoSearch From IHS API Service
        /// Calls [usp_PartSearch_GRIDFromIHSAPI]
        /// </summary>
        public override List<PartDetails> CustReqPartsGRIDIHSAPIBOMManager(System.Int32? clientId, int BOMManagerID, int? CustomerReqID, out DataTable dtCheckParts, int curPage, int Rpp)
        {
            DataTable dt = new DataTable();
            dt.Columns.Add("Rowid");
            dt.Columns.Add("PartName");
            dt.Columns.Add("ItemFound");
            dtCheckParts = dt;
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                //partSearch = partSearch + "%";
                cn = new SqlConnection(this.ConnectionString);
                //cmd = new SqlCommand("usp_PartSearch_GRIDFromIHSAPI", cn);
                cmd = new SqlCommand("usp_itemsearch_GRIDBindFrom_IHSAPI_BOMManager", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@BOMManagerID", SqlDbType.Int).Value = BOMManagerID;
                cmd.Parameters.Add("@CustomerReqID", SqlDbType.NVarChar).Value = CustomerReqID;
                cmd.Parameters.Add("@curPage", SqlDbType.Int).Value = curPage;
                cmd.Parameters.Add("@Rpp", SqlDbType.Int).Value = Rpp;
                //cmd.Parameters.Add("@OrderBy", SqlDbType.Int).Value = orderBy;
                //cmd.Parameters.Add("@SortDir", SqlDbType.Int).Value = sortDir;
                //cmd.Parameters.Add("@PageIndex", SqlDbType.Int).Value = pageIndex;
                //cmd.Parameters.Add("@PageSize", SqlDbType.Int).Value = pageSize;
                cn.Open();
                DbDataReader reader = cmd.ExecuteReader();// ExecuteReader(cmd);
                //IDataReader dr = cmd.ExecuteReader();
                List<PartDetails> lst = new List<PartDetails>();
                while (reader.Read())
                {
                    PartDetails obj = new PartDetails();
                    obj.PartNameWithManufacture = GetReaderValue_String(reader, "PartName", "");
                    obj.PartName = GetReaderValue_String(reader, "Parts", "");
                    obj.ManufacturerNo = GetReaderValue_Int32(reader, "ManufacturerId", 0);
                    obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
                    obj.ROHSNo = GetReaderValue_Int32(reader, "ROHS", 0);
                    obj.CountryOfOrigin = GetReaderValue_String(reader, "CountryOfOrigin", "");
                    obj.CountryOfOriginNo = GetReaderValue_Int32(reader, "CountryOfOriginNo", 0);
                    obj.HTSCode = GetReaderValue_String(reader, "HTSCode", "");
                    obj.AveragePrice = GetReaderValue_Double(reader, "AveragePrice", 0);
                    obj.Packaging = GetReaderValue_String(reader, "Packaging", "");
                    obj.PackagingSize = GetReaderValue_String(reader, "PackagingSize", "");
                    obj.IHSPartsId = GetReaderValue_Int32(reader, "IHSPartsId", 0);
                    obj.ResultType = GetReaderValue_String(reader, "ResultType", "");
                    obj.ROHSName = GetReaderValue_String(reader, "ROHSName", "");
                    obj.Descriptions = GetReaderValue_String(reader, "Descriptions", "");
                    obj.PartStatus = GetReaderValue_String(reader, "PartStatus", "");
                    obj.IHSCurrencyCode = GetReaderValue_String(reader, "ColPriceCurrency", "");
                    obj.ProductDescription = GetReaderValue_String(reader, "ProductName", "");
                    obj.ProductNo = GetReaderValue_NullableInt32(reader, "ProductNo", 0);
                    obj.IHSProductDescription = GetReaderValue_String(reader, "IHSProductName", "");
                    obj.IHSDutyCode = GetReaderValue_String(reader, "IHSDutyCode", "");
                    obj.RowCnt = GetReaderValue_NullableInt32(reader, "RowCnt", null);
                    // obj.RowNum = GetReaderValue_NullableInt32(reader, "RowNum", 0);
                    obj.ManufacturerFullName = GetReaderValue_String(reader, "ManufacturerFullName", "");
                    obj.IHSProduct = GetReaderValue_String(reader, "IHSProduct", "");
                    obj.ECCNCode = GetReaderValue_String(reader, "ECCNCode", "");
                    obj.ItemFound = GetReaderValue_String(reader, "ItemFound", "");
                    obj.TotalRecords = GetReaderValue_Int32(reader, "TotalRecords", 0);
                    //[001] end
                    lst.Add(obj);
                    obj = null;



                }
                if (reader.NextResult())
                {
                    while (reader.Read())
                    {
                        DataRow dr = dtCheckParts.NewRow();
                        dr["Rowid"] = GetReaderValue_String(reader, "Rowid", "");
                        dr["PartName"] = GetReaderValue_String(reader, "PartName", "");
                        dr["ItemFound"] = GetReaderValue_String(reader, "ItemFound", "");
                        dtCheckParts.Rows.Add(dr);
                    }

                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Parts tbihsparts Table", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// IHS Come from tblihspart table
        /// Calls [usp_Search_for_IHSPartDetails]
        /// </summary>
        public override List<PartDetails> CustReqIHSPartsDetails(System.Int32? clientId, System.Int32? IHSPartsId, System.String MSLName, System.Int32? MSLNo, System.String Manufacturer, System.String IHSProdDesc,  System.String Packaging, System.String HTSCode, System.String IHSDutyCode, System.String CountryOfOrigin, System.Int32? CountryOfOriginNo, System.String PackagingSize)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_Search_for_IHSPartDetails", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@IHSPartsId", SqlDbType.Int).Value = IHSPartsId;
                cmd.Parameters.Add("@MSLName", SqlDbType.NVarChar).Value = MSLName;
                cmd.Parameters.Add("@MSLNo", SqlDbType.Int).Value = MSLNo;
                cmd.Parameters.Add("@Manufacturer", SqlDbType.NVarChar).Value = Manufacturer;
                cmd.Parameters.Add("@IHSProdDesc", SqlDbType.NVarChar).Value = IHSProdDesc;
                cmd.Parameters.Add("@Packaging", SqlDbType.NVarChar).Value = Packaging;
                cmd.Parameters.Add("@HTSCode", SqlDbType.NVarChar).Value = HTSCode;
                cmd.Parameters.Add("@IHSDutyCode", SqlDbType.NVarChar).Value = IHSDutyCode;
                cmd.Parameters.Add("@CountryOfOrigin", SqlDbType.NVarChar).Value = CountryOfOrigin;
                cmd.Parameters.Add("@CountryOfOriginNo", SqlDbType.Int).Value = CountryOfOriginNo;
                cmd.Parameters.Add("@PackagingSize", SqlDbType.NVarChar).Value = PackagingSize;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<PartDetails> lst = new List<PartDetails>();
                while (reader.Read())
                {
                    PartDetails obj = new PartDetails();
                    obj.PartNameWithManufacture = GetReaderValue_String(reader, "PartName", "");
                    obj.PartName = GetReaderValue_String(reader, "Parts", "");
                    obj.ManufacturerNo = GetReaderValue_Int32(reader, "ManufacturerId", 0);
                    obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
                    obj.ROHSNo = GetReaderValue_Int32(reader, "ROHS", 0);
                    obj.CountryOfOrigin = GetReaderValue_String(reader, "CountryOfOrigin", "");
                    obj.CountryOfOriginNo = GetReaderValue_Int32(reader, "CountryOfOriginNo", 0);
                    obj.HTSCode = GetReaderValue_String(reader, "HTSCode", "");
                    obj.AveragePrice = GetReaderValue_Double(reader, "AveragePrice", 0);
                    obj.Packaging = GetReaderValue_String(reader, "Packaging", "");
                    obj.PackagingSize = GetReaderValue_String(reader, "PackagingSize", "");
                    obj.IHSPartsId = GetReaderValue_Int32(reader, "IHSPartsId", 0);
                    obj.ResultType = GetReaderValue_String(reader, "ResultType", "");
                    obj.ROHSName = GetReaderValue_String(reader, "ROHSName", "");
                    obj.Descriptions = GetReaderValue_String(reader, "Descriptions", "");
                    obj.PartStatus = GetReaderValue_String(reader, "PartStatus", "");
                    obj.IHSCurrencyCode = GetReaderValue_String(reader, "ColPriceCurrency", "");
                    obj.ProductDescription = GetReaderValue_String(reader, "ProductName", "");
                    obj.ProductNo = GetReaderValue_NullableInt32(reader, "ProductNo", 0);
                    obj.IHSProductDescription = GetReaderValue_String(reader, "IHSProductName", "");
                    obj.IHSDutyCode = GetReaderValue_String(reader, "IHSDutyCode", "");
                    //obj.RowCnt = GetReaderValue_NullableInt32(reader, "RowCnt", null);
                    // obj.RowNum = GetReaderValue_NullableInt32(reader, "RowNum", 0);
                    obj.ManufacturerFullName = GetReaderValue_String(reader, "ManufacturerFullName", "");
                    obj.IHSProduct = GetReaderValue_String(reader, "IHSProduct", "");
                    obj.PackageId = GetReaderValue_NullableInt32(reader, "PackageId", 0);
                    obj.ECCNCode = GetReaderValue_String(reader, "ECCNCode", "");
                    obj.PackageDescription = GetReaderValue_String(reader, "PackageDescription", "");


                    //[001] end
                    lst.Add(obj);
                    obj = null;



                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Parts tbihsparts Table", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        //end


        /// <summary>
        /// Insert-Select
        /// Calls [usp_insert_IHSApiXML_Or_select]
        /// </summary>
        public override List<PartDetails> InsertIHSApiXMLOrSelect(System.Int32? clientNo, System.Int32? updatedBy, System.String strXMLData)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_insert_IHSApiXML_Or_select", cn);
                //cmd = new SqlCommand("usp_insert_IHSApiXML", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@ClientId", SqlDbType.NVarChar).Value = clientNo;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.NVarChar).Value = updatedBy;
                cmd.Parameters.Add("@IHSResults", SqlDbType.Xml).Value = strXMLData;
                cmd.Parameters.Add("@RecordCount", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                //int ret = ExecuteNonQuery(cmd);
                //return (Int32)cmd.Parameters["@RecordCount"].Value;
                DbDataReader reader = ExecuteReader(cmd);
                List<PartDetails> lst = new List<PartDetails>();
                while (reader.Read())
                {
                    PartDetails obj = new PartDetails();
                    obj.PartNameWithManufacture = GetReaderValue_String(reader, "PartName", "");
                    obj.PartName = GetReaderValue_String(reader, "Parts", "");
                    obj.ManufacturerNo = GetReaderValue_Int32(reader, "ManufacturerId", 0);
                    obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
                    obj.ROHSNo = GetReaderValue_Int32(reader, "ROHS", 0);
                    obj.CountryOfOrigin = GetReaderValue_String(reader, "CountryOfOrigin", "");
                    obj.CountryOfOriginNo = GetReaderValue_Int32(reader, "CountryOfOriginNo", 0);
                    obj.HTSCode = GetReaderValue_String(reader, "HTSCode", "");
                    obj.AveragePrice = GetReaderValue_Double(reader, "AveragePrice", 0);
                    obj.Packaging = GetReaderValue_String(reader, "Packaging", "");
                    obj.PackagingSize = GetReaderValue_String(reader, "PackagingSize", "");
                    obj.IHSPartsId = GetReaderValue_Int32(reader, "IHSPartsId", 0);
                    obj.ResultType = GetReaderValue_String(reader, "ResultType", "");
                    obj.ROHSName = GetReaderValue_String(reader, "ROHSName", "");
                    obj.Descriptions = GetReaderValue_String(reader, "Descriptions", "");
                    obj.PartStatus = GetReaderValue_String(reader, "PartStatus", "");
                    obj.IHSCurrencyCode = GetReaderValue_String(reader, "ColPriceCurrency", "");
                    obj.ProductDescription = GetReaderValue_String(reader, "ProductName", "");
                    obj.ProductNo = GetReaderValue_NullableInt32(reader, "ProductNo", 0);
                    obj.IHSProductDescription = GetReaderValue_String(reader, "IHSProductName", "");
                    obj.IHSDutyCode = GetReaderValue_String(reader, "IHSDutyCode", "");
                    obj.ManufacturerFullName = GetReaderValue_String(reader, "ManufacturerFullName", "");
                    obj.IHSProduct = GetReaderValue_String(reader, "IHSProduct", "");
                    obj.PackageId = GetReaderValue_NullableInt32(reader, "PackageId", 0);
                    obj.ECCNCode = GetReaderValue_String(reader, "ECCNCode", "");
                    obj.PackageDescription = GetReaderValue_String(reader, "PackageDescription", "");
                    lst.Add(obj);
                    obj = null;

                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to get Parts tbihsparts Table", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        //end

        public override List<PartDetails> GetPartDetail(string partNo, Int32? ClientID, System.Int32? companyNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                //partSearch = partSearch + "%";
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_LastGetCustPartDetail", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = ClientID;
                cmd.Parameters.Add("@PartNo", SqlDbType.NVarChar).Value = partNo;
                cmd.Parameters.Add("@CompanyNo", SqlDbType.Int).Value = companyNo;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<PartDetails> lst = new List<PartDetails>();
                while (reader.Read())
                {
                    PartDetails obj = new PartDetails();
                    obj.LastPricePaidByCust = GetReaderValue_Double(reader, "LastPricePaidByCust", 0);
                    obj.PaidByCustCurrencyCode = GetReaderValue_String(reader, "PaidByCustCurrencyCode", "");
                    obj.LastSoldtoCustomer = GetReaderValue_String(reader, "LastSoldtoCustomer", "");
                    obj.LastAverageReboundPriceSold = GetReaderValue_Double(reader, "LastAverageReboundPriceSold", 0);
                    obj.LastSoldOn = GetReaderValue_DateTime(reader, "LastSoldOn", DateTime.MinValue);

                    obj.LastQuantity = GetReaderValue_NullableInt32(reader, "LastQuantity", 0);
                    obj.LastSupplierType = GetReaderValue_String(reader, "LastSupplierType", "");
                    obj.LastDatecode = GetReaderValue_String(reader, "LastDatecode", "");
                    obj.LastDatePurchased = GetReaderValue_DateTime(reader, "LastDatePurchased", DateTime.MinValue);
                    obj.LastCustomerRegion = GetReaderValue_String(reader, "LastCustomerRegion", "");

                    obj.CustLastPricePaidByCust = GetReaderValue_Double(reader, "CustLastPricePaidByCust", 0);
                    obj.CustPaidByCustCurrencyCode = GetReaderValue_String(reader, "CustPaidByCustCurrencyCode", "");
                    obj.CustLastAvgReboundPriceSold = GetReaderValue_Double(reader, "CustLastAvgReboundPriceSold", 0);
                    obj.CustLastSoldtoCustomer = GetReaderValue_String(reader, "CustLastSoldtoCustomer", "");
                    obj.CustLastSoldOn = GetReaderValue_DateTime(reader, "CustLastSoldOn", DateTime.MinValue);

                    obj.CustQuantity = GetReaderValue_NullableInt32(reader, "CustQuantity", 0);
                    obj.CustSupplierType = GetReaderValue_String(reader, "CustSupplierType", "");
                    obj.CustDatecode = GetReaderValue_String(reader, "CustDatecode", "");
                    obj.CustDatePurchased = GetReaderValue_DateTime(reader, "CustDatePurchased", DateTime.MinValue);
                    obj.CustomerRegion = GetReaderValue_String(reader, "CustomerRegion", "");
                    obj.CleintBestPricePaid12 = GetReaderValue_Double(reader, "Cleint_Best_Price_Paid_12", 0);
                    obj.BestLastPricePaid12 = GetReaderValue_Double(reader, "last_Price_Paid_12", 0);
        


        //[001] end
        lst.Add(obj);
                    obj = null;



                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to  Get Part Detail Table", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        //public override DataSet GetPartDetail(string partNo, Int32? ClientID, System.Int32? companyNo)
        //{
        //    SqlConnection cn = null;
        //    SqlCommand cmd = null;
        //    try
        //    {
        //       // partNo = partNo + "%";
        //        cn = new SqlConnection(this.ConnectionString);
        //       // cmd = new SqlCommand("usp_GetCustPartAndStockInfo", cn);
        //        cmd = new SqlCommand("usp_LastGetCustPartDetail", cn);
        //        cmd.CommandType = CommandType.StoredProcedure;
        //        cmd.CommandTimeout = 60;
        //        cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = ClientID;
        //        cmd.Parameters.Add("@PartNo", SqlDbType.NVarChar).Value = partNo;
        //        cmd.Parameters.Add("@CompanyNo", SqlDbType.Int).Value = companyNo;
        //        cn.Open();
        //        DataSet ds = new DataSet();
        //        SqlDataAdapter da = new SqlDataAdapter(cmd);
        //        da.Fill(ds);
        //        return ds;
        //    }
        //    catch (SqlException sqlex)
        //    {
        //        //LogException(sqlex);
        //        throw new Exception("Failed to get Parts", sqlex);
        //    }
        //    finally
        //    {
        //        cmd.Dispose();
        //        cn.Close();
        //        cn.Dispose();
        //    }
        //}
        //ihs token count start
        public override int GetIHSTokenData(out System.String TokenNumber)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            TokenNumber = string.Empty;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_count_IHSToken", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@TokenCount", SqlDbType.Int).Direction = ParameterDirection.Output;
                cmd.Parameters.Add("@TokenNumber", SqlDbType.VarChar, 4000).Direction = ParameterDirection.Output;
                cn.Open();
                ExecuteNonQuery(cmd);
                TokenNumber = Convert.ToString(cmd.Parameters["@TokenNumber"].Value);
                return (Int32)cmd.Parameters["@TokenCount"].Value;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get TokenNumber", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }

           



        }
        //ihs token count end
		/// <summary>
		/// Create a new row
		/// Calls [usp_insert_Part]
		/// </summary>
		public override Int32 Insert(System.String fullPart, System.Int32? manufacturerNo, System.Int32? packageNo, System.Int32? productNo, System.Int32? minimumQuantity, System.Int32? reOrderQuantity, System.Int32? leadTime, System.Int32? clientNo, System.Double? resalePrice, System.String partTitle, System.Boolean? masterPart, System.Boolean? goldenPart, System.Boolean? rohsCompliant, System.Int32? updatedBy) {
			SqlConnection cn = null;
			SqlCommand cmd = null;
			try {
				cn = new SqlConnection(this.ConnectionString);
				cmd = new SqlCommand("usp_insert_Part", cn);
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.Parameters.Add("@FullPart", SqlDbType.NVarChar).Value = fullPart;
				cmd.Parameters.Add("@ManufacturerNo", SqlDbType.Int).Value = manufacturerNo;
				cmd.Parameters.Add("@PackageNo", SqlDbType.Int).Value = packageNo;
				cmd.Parameters.Add("@ProductNo", SqlDbType.Int).Value = productNo;
				cmd.Parameters.Add("@MinimumQuantity", SqlDbType.Int).Value = minimumQuantity;
				cmd.Parameters.Add("@ReOrderQuantity", SqlDbType.Int).Value = reOrderQuantity;
				cmd.Parameters.Add("@LeadTime", SqlDbType.Int).Value = leadTime;
				cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = clientNo;
				cmd.Parameters.Add("@ResalePrice", SqlDbType.Float).Value = resalePrice;
				cmd.Parameters.Add("@PartTitle", SqlDbType.NVarChar).Value = partTitle;
				cmd.Parameters.Add("@MasterPart", SqlDbType.Bit).Value = masterPart;
				cmd.Parameters.Add("@GoldenPart", SqlDbType.Bit).Value = goldenPart;
				cmd.Parameters.Add("@ROHSCompliant", SqlDbType.Bit).Value = rohsCompliant;
				cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
				cmd.Parameters.Add("@MasterPartId", SqlDbType.Int).Direction = ParameterDirection.Output;
				cn.Open();
				int ret = ExecuteNonQuery(cmd);
				return (Int32)cmd.Parameters["@MasterPartId"].Value;
			} catch (SqlException sqlex) {
				//LogException(sqlex);
				throw new Exception("Failed to insert Part", sqlex);
			} finally {
				cmd.Dispose();
				cn.Close();
				cn.Dispose();
			}
		}

        //Part ECCN Mapped Code start
        /// <summary>
        /// IHS Come from tblihspart table
        /// Calls [usp_Search_for_IHSPartECCNDetails]
        /// </summary>
        public override List<PartDetails> CustReqIHSPartEccnDetails(System.Int32? clientId, System.String ECCNCode, System.Int32 UpdatedBy,System.String PartNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_Search_for_MappedPartWithECCNCode", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@ECCNCode", SqlDbType.NVarChar).Value = ECCNCode;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = UpdatedBy;
                cmd.Parameters.Add("@PartNo", SqlDbType.NVarChar).Value = PartNo;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<PartDetails> lst = new List<PartDetails>();
                while (reader.Read())
                {
                    PartDetails obj = new PartDetails();
                    obj.PartEccnMappedId = GetReaderValue_Int32(reader, "PartEccnMappedId", 0);
                    obj.ECCNNo = GetReaderValue_Int32(reader, "ECCNNo", 0);
                    obj.ECCNCode = GetReaderValue_String(reader, "ECCNCode", "");
                    obj.ECCNStatus = GetReaderValue_Boolean(reader, "ECCNStatus", false);
                    obj.ECCNWarning = GetReaderValue_String(reader, "ECCNWarning", "");
                    lst.Add(obj);
                    obj = null;

                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Parts ECCN Mapped Table", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        //end
        //code end

        public override List<PartDetails> CustReqIHSEccnCode(System.Int32? clientId, System.String ECCNCode, System.Int32 UpdatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_Search_for_IHSECCNCodeDetails", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@ECCNCode", SqlDbType.NVarChar).Value = ECCNCode;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = UpdatedBy;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<PartDetails> lst = new List<PartDetails>();
                while (reader.Read())
                {
                    PartDetails obj = new PartDetails();
                    obj.ECCNNo = GetReaderValue_Int32(reader, "ECCNNo", 0);
                    obj.ECCNCode = GetReaderValue_String(reader, "ECCNCode", "");
                    obj.ECCNStatus = GetReaderValue_Boolean(reader, "ECCNStatus", false);
                    obj.ECCNWarning = GetReaderValue_String(reader, "ECCNWarning", "");
                    lst.Add(obj);
                    obj = null;

                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Parts ECCN Mapped Table", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override List<PartDetails> SOIHSEccnDetial(System.Int32? clientId, System.String Part, System.Int32 UpdatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_SO_for_MappedPartWithECCNCode", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@PartNo", SqlDbType.NVarChar).Value = Part;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = UpdatedBy;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<PartDetails> lst = new List<PartDetails>();
                while (reader.Read())
                {
                    PartDetails obj = new PartDetails();
                    obj.ECCNNo = GetReaderValue_Int32(reader, "ECCNNo", 0);
                    obj.ECCNCode = GetReaderValue_String(reader, "ECCNCode", "");
                    obj.ECCNWarning = GetReaderValue_String(reader, "ECCNWarning", "");
                    obj.ECCNStatus = GetReaderValue_NullableBoolean(reader, "ECCNStatus", false);
                    lst.Add(obj);
                    obj = null;

                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Parts ECCN Details on SO Line", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


    }
}