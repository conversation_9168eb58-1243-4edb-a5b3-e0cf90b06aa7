//-----------------------------------------------------------------------------------------
// RP 26.10.2009:
// - new control
//-----------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site;
using Rebound.GlobalTrader.Site.Controls;
using Rebound.GlobalTrader.Site.Enumerations;

namespace Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists {

    public partial class Services : Base {

		protected override void OnInit(EventArgs e) {
			SetLeftNuggetType("DataList_Services");
			SetDataListNuggetType("Services");
			LoadDataListNugget(_objDataListNugget.Name);
			TitleResource = "QuickBrowse";
			base.OnInit(e);
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Services", ctlDesignBase.ClientID);
			AddScriptReference("Controls.LeftNuggets.DataLists.Services.Services");
			SetupTable();
		}

		private void SetupTable() {
			_tbl.AllowSelection = false;
			_tbl.SortColumnDirection = SortColumnDirection.ASC;
			_tbl.Columns.Add(new FlexiDataColumn("Service", Unit.Empty, true));
		}
		
    }

}