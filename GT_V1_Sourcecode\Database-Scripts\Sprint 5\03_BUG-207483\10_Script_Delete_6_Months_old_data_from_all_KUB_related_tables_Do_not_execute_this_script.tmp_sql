﻿/* 
===========================================================================================
TASK      			UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[BUG-207483]		Ngai To				02-Jul-2024		FIX				Bug 207483: [PROD Bug] CPU Spike - DB deadlock in the existing KUB Assistance feature
===========================================================================================
*/

DELETE
FROM tb_KubAveragePrice_CacheDetails
WHERE DLUP < DATEADD(MONTH, - 6, GETDATE())

DELETE
FROM tb_KubGetLast10QuoteDetails_Cache
WHERE DLUP < DATEADD(MONTH, - 6, GETDATE())

DELETE
FROM tb_KubGetMainProductGroupsDetailsCache
WHERE DLUP < DATEADD(MONTH, - 6, GETDATE())

DELETE
FROM tb_KubGetTotalLineInvoicedDetailsCache
WHERE DLUP < DATEADD(MONTH, - 6, GETDATE())

DELETE
FROM tbKUB_GPCalculationDetails
WHERE DLUP < DATEADD(MONTH, - 6, GETDATE())

DELETE
FROM tbKubAssistanceDetailsCache
WHERE DLUP < DATEADD(MONTH, - 6, GETDATE())

DELETE
FROM tbKubCountryWiseSaleDetailsCache
WHERE DLUP < DATEADD(MONTH, - 6, GETDATE())

DELETE
FROM tbKubCountryWiseUnShippedSaleDetailsCache
WHERE DLUP < DATEADD(MONTH, - 6, GETDATE())

DELETE
FROM tbKUBPoDetailsCache
WHERE DLUP < DATEADD(MONTH, - 6, GETDATE())
