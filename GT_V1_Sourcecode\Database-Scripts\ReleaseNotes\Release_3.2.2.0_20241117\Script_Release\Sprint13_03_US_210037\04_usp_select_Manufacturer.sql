﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO
/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-210037]		An.TranTan			23-Oct-2024		UPDATE			Get Manufacturer advisory notes
===========================================================================================
*/
CREATE OR ALTER PROCEDURE  [dbo].[usp_select_Manufacturer]    
--      
@ManufacturerId int       
--      
AS      
--      
SELECT ManufacturerId      
,  ManufacturerName      
,  Notes       
,  ManufacturerCode      
,  Inactive      
,  UpdatedBy      
,  DLUP       
,  URL      
,  IsPDFAvailable    
,  ConflictResource 
, isnull((select count(*) from tbManufacturer where ManufacturerId=@ManufacturerId and Inactive=0 and IHSPartsNo is not null),0)as SystemManufacturer
, AdvisoryNotes
, isnull(IsDisplayAdvisory, cast(0 as bit)) as IsDisplayAdvisory
FROM  dbo.tbManufacturer      
WHERE ManufacturerId = @ManufacturerId   
  




GO


