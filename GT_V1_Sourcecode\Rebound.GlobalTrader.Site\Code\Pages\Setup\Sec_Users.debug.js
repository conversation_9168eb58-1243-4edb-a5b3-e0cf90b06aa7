///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Setup");

Rebound.GlobalTrader.Site.Pages.Setup.Sec_Users = function(el) { 
	Rebound.GlobalTrader.Site.Pages.Setup.Sec_Users.initializeBase(this, [el]);
};

Rebound.GlobalTrader.Site.Pages.Setup.Sec_Users.prototype = {

	get_ctlSecurityUsers: function() { return this._ctlSecurityUsers; }, 	set_ctlSecurityUsers: function(v) { if (this._ctlSecurityUsers !== v)  this._ctlSecurityUsers = v; }, 
	get_ctlUserProfile: function() { return this._ctlUserProfile; }, 	set_ctlUserProfile: function(v) { if (this._ctlUserProfile !== v)  this._ctlUserProfile = v; }, 
	get_ctlSecurityUserGroups: function() { return this._ctlSecurityUserGroups; }, 	set_ctlSecurityUserGroups: function(v) { if (this._ctlSecurityUserGroups !== v)  this._ctlSecurityUserGroups = v; }, 

	initialize: function() {
		Rebound.GlobalTrader.Site.Pages.Setup.Sec_Users.callBaseMethod(this, "initialize");
	},
	
	goInit: function() {
		if (this._ctlSecurityUsers) this._ctlSecurityUsers.addSelectUser(Function.createDelegate(this, this.ctlSecurityGroups_SelectUser));
		if (this._ctlUserProfile) this._ctlUserProfile.addSaveEditComplete(Function.createDelegate(this, this.ctlUserProfile_SaveEditComplete));
		if (this._ctlSecurityUserGroups) this._ctlSecurityUserGroups.addSaveEditComplete(Function.createDelegate(this, this.ctlSecurityUserGroups_SaveEditComplete));
		Rebound.GlobalTrader.Site.Pages.Setup.Sec_Users.callBaseMethod(this, "goInit");
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this._ctlSecurityUsers) this._ctlSecurityUsers.dispose();
		if (this._ctlUserProfile) this._ctlUserProfile.dispose();
		if (this._ctlSecurityUserGroups) this._ctlSecurityUserGroups.dispose();
		this._ctlSecurityUsers = null;
		this._ctlUserProfile = null;
		this._ctlSecurityUserGroups = null;
		Rebound.GlobalTrader.Site.Pages.Setup.Sec_Users.callBaseMethod(this, "dispose");
	},
	
	ctlSecurityGroups_SelectUser: function() {
		this._ctlUserProfile._intLoginID = this._ctlSecurityUsers._intLoginID;
		this._ctlUserProfile.refresh();
		this._ctlSecurityUserGroups._intLoginID = this._ctlSecurityUsers._intLoginID;
		this._ctlSecurityUserGroups.refresh();
		this._ctlSecurityUsers._tbl.resizeColumns();
		this.showNuggets(true);
	},
	
	ctlUserProfile_SaveEditComplete: function() {
		this._ctlSecurityUsers.refresh();
	},
	
	ctlSecurityUserGroups_SaveEditComplete: function() {
		this._ctlSecurityUsers.refresh();
	},
	
	showNuggets: function(bln) {
		this._ctlUserProfile.show(bln);
		this._ctlSecurityUserGroups.show(bln);
	}
	
};

Rebound.GlobalTrader.Site.Pages.Setup.Sec_Users.registerClass("Rebound.GlobalTrader.Site.Pages.Setup.Sec_Users", Rebound.GlobalTrader.Site.Pages.Content, Sys.IDisposable);
