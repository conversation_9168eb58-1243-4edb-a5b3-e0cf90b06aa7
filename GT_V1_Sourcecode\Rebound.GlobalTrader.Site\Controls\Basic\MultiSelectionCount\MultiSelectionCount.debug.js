///<reference name="MicrosoftAjax.js" />
///<reference path="~/Common/Data/Data.js" />
///<reference path="~/Common/Functions/Functions.js" />
///<reference path="~/Controls/IconButton/IconButton.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - dispose everything fully
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls");

Rebound.GlobalTrader.Site.Controls.MultiSelectionCount = function(element) { 
	Rebound.GlobalTrader.Site.Controls.MultiSelectionCount.initializeBase(this, [element]);
	this._intCount = 0;
	this._tbl = null;
};

Rebound.GlobalTrader.Site.Controls.MultiSelectionCount.prototype = {

	get_lblNumber: function() { return this._lblNumber; }, 	set_lblNumber: function(v) { if (this._lblNumber !== v)  this._lblNumber = v; }, 
	get_hypSelectAll: function() { return this._hypSelectAll; }, 	set_hypSelectAll: function(v) { if (this._hypSelectAll !== v)  this._hypSelectAll = v; }, 
	get_hypClearAll: function() { return this._hypClearAll; }, 	set_hypClearAll: function(v) { if (this._hypClearAll !== v)  this._hypClearAll = v; }, 

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.MultiSelectionCount.callBaseMethod(this, "initialize");
		this.updateCount(0);
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this._element) $clearHandlers(this._element);
		if (this._hypSelectAll) $clearHandlers(this._hypSelectAll);
		if (this._hypClearAll) $clearHandlers(this._hypClearAll);
		if (this._tbl) this._tbl.dispose();
		this._lblNumber = null;
		this._hypSelectAll = null;
		this._hypClearAll = null;
		this._tbl = null;
		Rebound.GlobalTrader.Site.Controls.MultiSelectionCount.callBaseMethod(this, "dispose");
		this.isDisposed = true;
	},
	
	updateCount: function(intCount) {
		$R_FN.setInnerHTML(this._lblNumber, intCount);
		$R_FN.showElement(this._hypSelectAll, intCount < 1);
		$R_FN.showElement(this._hypClearAll, intCount > 0);
	},
	
	registerTable: function(tbl) {
		//throw error if we've already got a table set
		if (this._tbl) eval(String.format("MultiSelectionCount_AlreadyHasTable_ID_{0}()", this._element.id));
		this._tbl = tbl;
		this._tbl.addMultipleSelectionChanged(Function.createDelegate(this, this.tbl_MultipleSelectionIndexChanged));
		$addHandler(this._hypSelectAll, "click", Function.createDelegate(this, this.selectAll));
		$addHandler(this._hypClearAll, "click", Function.createDelegate(this, this.clearAll));
	},
	
	changeTableRegistration: function(tbl) {
		this.clearTableRegistration();
		this.registerTable(tbl);
	},
	
	clearTableRegistration: function() {
		if (!this._tbl) return;
		this._tbl.removeMultipleSelectionChanged(Function.createDelegate(this, this.tbl_MultipleSelectionIndexChanged));
		this._tbl = null;
		$clearHandlers(this._hypSelectAll);
		$clearHandlers(this._hypClearAll);
	},
	
	tbl_MultipleSelectionIndexChanged: function() {
		this.updateCount(this._tbl._intCountSelected);
	},
	
	selectAll: function() {
		this._tbl.selectAllRows(true);
	},
	
	clearAll: function() {
		this._tbl.clearSelection(true);
	},
	
	show: function(bln) {
		$R_FN.showElement(this._element, bln);
	},
	
	resetCount: function() {
		this.updateCount(0);
	}

};

Rebound.GlobalTrader.Site.Controls.MultiSelectionCount.registerClass("Rebound.GlobalTrader.Site.Controls.MultiSelectionCount", Sys.UI.Control, Sys.IDisposable);