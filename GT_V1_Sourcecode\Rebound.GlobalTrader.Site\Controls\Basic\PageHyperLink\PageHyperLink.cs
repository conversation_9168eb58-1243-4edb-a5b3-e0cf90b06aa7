//---------------------------------------------------------------------------------------------------------
// RP 17.12.2009:
// - allow a querystring to be added
//---------------------------------------------------------------------------------------------------------
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace Rebound.GlobalTrader.Site.Controls {
	[ToolboxData("<{0}:PageHyperLink runat=server></{0}:PageHyperLink>")]
	public class PageHyperLink : HyperLink, INamingContainer {

		private string _strPageType;
		public string PageType {
			get { return _strPageType; }
			set { _strPageType = value; }
		}

		private string _strQueryString = "";

		/// <summary>
		/// Overrides the title using a resource from Misc.resx
		/// </summary>
		private string _strOverrideTextResource = "";
		public string OverrideTextResource {
			get { return _strOverrideTextResource; }
			set { _strOverrideTextResource = value; }
		}

		protected override void OnPreRender(EventArgs e) {
			SitePage objSitePage = Rebound.GlobalTrader.Site.Site.GetInstance().GetPage(_strPageType);
			if (string.IsNullOrEmpty(NavigateUrl)) NavigateUrl = objSitePage.Url;
			if (_strOverrideTextResource == "") {
				Text = Functions.GetGlobalResource("PageTitles", objSitePage.Name);
			} else {
				Text = Functions.GetGlobalResource("Misc", _strOverrideTextResource);
			}
			Text = Text.Replace(" ", "&nbsp;"); //put non-breaking spaces in place of normal spaces
			if (_strQueryString.Length > 0) NavigateUrl += string.Format("?{0}", _strQueryString);
			base.OnPreRender(e);
		}

		public void AddQueryStringVariable(QueryStringVariable objQSVariable, object objValue) {
			if (_strQueryString.Length > 0) _strQueryString += "&";
			_strQueryString += string.Format("{0}={1}", objQSVariable.VariableQSName, HttpContext.Current.Server.UrlEncode(objValue.ToString()));
		}
        public void AddQueryStringVariable(QueryStringVariable objQSVariable, object objValue, QueryStringVariable objQSVariable1, object objValue1)
        {
            if (_strQueryString.Length > 0) _strQueryString += "&";
            _strQueryString += string.Format("{0}={1}", objQSVariable.VariableQSName, HttpContext.Current.Server.UrlEncode(objValue.ToString()));

            if (_strQueryString.Length > 0) _strQueryString += "&";
            _strQueryString += string.Format("{0}={1}", objQSVariable1.VariableQSName, HttpContext.Current.Server.UrlEncode(objValue1.ToString()));
        }
	}
}
