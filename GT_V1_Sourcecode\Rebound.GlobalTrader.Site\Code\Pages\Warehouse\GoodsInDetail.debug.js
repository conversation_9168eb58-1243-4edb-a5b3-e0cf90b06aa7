///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
/*
Marker     Changed by      Date         Remarks
[001]      Vinay           07/05/2012   This need to upload pdf document for goodsIn section
*/
//Code Merge for GI Screen
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Warehouse");

Rebound.GlobalTrader.Site.Pages.Warehouse.GoodsInDetail = function (el) {
    Rebound.GlobalTrader.Site.Pages.Warehouse.GoodsInDetail.initializeBase(this, [el]);
};

Rebound.GlobalTrader.Site.Pages.Warehouse.GoodsInDetail.prototype = {

    get_intGIID: function () { return this._intGIID; }, set_intGIID: function (v) { if (this._intGIID !== v) this._intGIID = v; },
    get_ctlMainInfo: function () { return this._ctlMainInfo; }, set_ctlMainInfo: function (v) { if (this._ctlMainInfo !== v) this._ctlMainInfo = v; },
    get_ctlLines: function () { return this._ctlLines; }, set_ctlLines: function (v) { if (this._ctlLines !== v) this._ctlLines = v; },
    get_btnPrint: function () { return this._btnPrint; }, set_btnPrint: function (v) { if (this._btnPrint !== v) this._btnPrint = v; },
    get_lblStatus: function () { return this._lblStatus; }, set_lblStatus: function (v) { if (this._lblStatus !== v) this._lblStatus = v; },
    get_pnlStatus: function () { return this._pnlStatus; }, set_pnlStatus: function (v) { if (this._pnlStatus !== v) this._pnlStatus = v; },
    // [001] code start
    get_ctlGIDocuments: function () { return this._ctlGIDocuments; }, set_ctlGIDocuments: function (v) { if (this._ctlGIDocuments !== v) this._ctlGIDocuments = v; },
    // [001] code end 
    get_ctlGIPDFDragDrop: function () { return this._ctlGIPDFDragDrop; }, set_ctlGIPDFDragDrop: function (v) { if (this._ctlGIPDFDragDrop !== v) this._ctlGIPDFDragDrop = v; },
    get_IsGlobalLogin: function () { return this._IsGlobalLogin; }, set_IsGlobalLogin: function (v) { if (this._IsGlobalLogin !== v) this._IsGlobalLogin = v; },
    get_ctlGILineImageDragDrop: function () { return this._ctlGILineImageDragDrop; }, set_ctlGILineImageDragDrop: function (v) { if (this._ctlGILineImageDragDrop !== v) this._ctlGILineImageDragDrop = v; },
    initialize: function () {
        Rebound.GlobalTrader.Site.Pages.Warehouse.GoodsInDetail.callBaseMethod(this, "initialize");
    },

    goInit: function () {
        this._ctlLines.addShowEditForm(Function.createDelegate(this, this.ctlLines_ShowEditForm));
        if (this._btnPrint) this._btnPrint.addPrint(Function.createDelegate(this, this.printGI));
        if (this._ctlMainInfo) this._ctlMainInfo.addGetDataComplete(Function.createDelegate(this, this.ctlMainInfo_GetDataComplete));
        if (this._ctlLines) this._ctlLines.addPotentialStatusChange(Function.createDelegate(this, this.ctlLines_PotentialStatusChange));
        // [001] code start
        if (this._ctlGIDocuments) this._ctlGIDocuments.getData();
        // [001] code end
        if (this._ctlGIPDFDragDrop) this._ctlGIPDFDragDrop.getData();
        if (this._ctlGILineImageDragDrop) this._ctlGILineImageDragDrop.getData();
        if (this._btnPrint) this._btnPrint.addExtraButtonClick(Function.createDelegate(this, this.printOtherDocs));
        Rebound.GlobalTrader.Site.Pages.Warehouse.GoodsInDetail.callBaseMethod(this, "goInit");
    },

    dispose: function () {
        if (this.isDisposed) return;
        if (this._element) $clearHandlers(this._element);
        if (this._ctlMainInfo) this._ctlMainInfo.dispose();
        if (this._ctlLines) this._ctlLines.dispose();
        if (this._btnPrint) this._btnPrint.dispose();
        // [001] code start
        if (this._ctlGIDocuments) this._ctlGIDocuments.dispose();
        // [001] code end
        this._ctlMainInfo = null;
        this._ctlLines = null;
        this._btnPrint = null;
        this._lblStatus = null;
        this._pnlStatus = null;
        // [001] code start
        this._ctlGIDocuments = null;
        // [001] code end
        this._IsGlobalLogin = null;
        Rebound.GlobalTrader.Site.Pages.Warehouse.GoodsInDetail.callBaseMethod(this, "dispose");
    },

    printGI: function () {
        $R_FN.openPrintWindow($R_ENUM$PrintObject.GoodsIn, this._intGIID);
    },
    printOtherDocs: function () {
        if (this._btnPrint._strExtraButtonClickCommand == "PrintLog") $R_FN.openPrintLogWindow($R_ENUM$PrintObject.PrintLog, this._intGIID, false, "GoodsIn");
    },

    ctlMainInfo_GetDataComplete: function () {
        // var intGlobalClnt=this._IsGlobalLogin == true ? this._ctlMainInfo.getFieldValue("hidGlobalClientNo") : null;
        $R_FN.setInnerHTML(this._lblStatus, this._ctlMainInfo.getFieldValue("hidStatus"));
        this._ctlLines.updateStatus(this._ctlMainInfo.getFieldValue("hidStatusNo"));
        this._ctlLines._intPurchaseOrderNo = this._ctlMainInfo.getFieldValue("hidPoNumber");
        this._ctlLines._intGlobalClientNo = this._IsGlobalLogin == true ? this._ctlMainInfo.getFieldValue("hidGlobalClientNo") : null;
        this._ctlMainInfo._intGlobalClientNo = this._IsGlobalLogin == true ? this._ctlMainInfo.getFieldValue("hidGlobalClientNo") : null;
        this._ctlMainInfo._IsGlobalLogin = this._IsGlobalLogin;
    },

    ctlLines_PotentialStatusChange: function () {
        this._ctlMainInfo.getData();
        this._ctlMainInfo.showField("ctlSupplierInvoiceAdd", this._ctlLines._blnAnyLineInspected)
    },

    getStatusError: function (args) {
        $R_FN.showElement(this._pnlStatus, false);
    },

    ctlLines_ShowEditForm: function () {
        //pass some fields into the lines nugget from the header
        this._ctlLines._objGIHeaderFields = {
            GoodsInNumber: this._ctlMainInfo.getFieldValue("hidGoodsInNumber")
            , SupplierName: this._ctlMainInfo.getFieldValue("hidSupplierName")
            , AirWayBill: this._ctlMainInfo.getFieldValue("ctlAirWayBill")
            , Reference: this._ctlMainInfo.getFieldValue("ctlReference")
            , Currency: this._ctlMainInfo.getFieldValue("ctlCurrency")
            , CurrencyCode: this._ctlMainInfo.getFieldValue("hidCurrencyCode")
            , CurrencyID: this._ctlMainInfo.getFieldValue("hidCurrencyNo")
            , ReceivingNotes: this._ctlMainInfo.getFieldValue("ctlNotes")
            , ClientCurCode: this._ctlMainInfo.getFieldValue("hidClientCurrency")
            , SupplierNo: this._ctlMainInfo.getFieldValue("hidSupplierNo")
            , Buyer: this._ctlMainInfo.getFieldValue("ctlBuyer")
            , BuyerNo: this._ctlMainInfo.getFieldValue("hidBuyerNo")
            , PurchaseOrder: this._ctlMainInfo.getFieldValue("hidPoNumber")
            , PurchaseOrderNo: this._ctlMainInfo.getFieldValue("hidPurchaseOrderNo")
            , ReceivedDate: this._ctlMainInfo.getFieldValue("ctlReceivedDate")
            , IsPoHubLogin: this._ctlMainInfo.getFieldValue("hidIsPoHubLogin")
            , InternalPurchaseOrderId: this._ctlMainInfo.getFieldValue("hidInternalPurchaseOrderId")
            , IPOSupplier: this._ctlMainInfo.getFieldValue("hidIPOSupplier")
            , IPOSupplierName: this._ctlMainInfo.getFieldValue("hidIPOSupplierName")
        };
    }

};
Rebound.GlobalTrader.Site.Pages.Warehouse.GoodsInDetail.registerClass("Rebound.GlobalTrader.Site.Pages.Warehouse.GoodsInDetail", Rebound.GlobalTrader.Site.Pages.Content, Sys.IDisposable);
