Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");Rebound.GlobalTrader.Site.Controls.Nuggets.ClientImportBOMMainInfo=function(n){Rebound.GlobalTrader.Site.Controls.Nuggets.ClientImportBOMMainInfo.initializeBase(this,[n]);this._intBOMID=-1;this._blnHasRequirement=!1;this._blnRequestedToPoHub=!1;this._blnRelease=!1;this._isAddButtonEnable=!0;this._isPurchaseHub=!1;this._intCurrencyNo=-1;this._BomCode="";this._BomName="";this._BomCompanyName="";this._BomCompanyNo=0;this._intContact2No=-1;this._stringCurrency=null;this._inActive=!1;this._BomContactname="";this._BomContactNo=0;this._CurrentSupplier="";this._QuoteRequired="";this._blnAllHasDelDate=!1;this._blnAllHasProduct=!1;this._blnCanReleaseAll=!1;this._blnAllItemHasSourcing=!1;this.BOMStatus="";this._isClosed=!1;this._UpdatedBy=null;this._blnCanNoBidAll=!0;this._isNoBidCount=!1;this._RequestToPOHubBy=-1;this._UpdateByPH=-1;this._blnReqInValid=!1;this._ValidMessage="";this._ClientBOMStatus=-1;this._ClientCompanyId=-1};Rebound.GlobalTrader.Site.Controls.Nuggets.ClientImportBOMMainInfo.prototype={get_intBOMID:function(){return this._intBOMID},set_intBOMID:function(n){this._intBOMID!==n&&(this._intBOMID=n)},get_ibtnEdit:function(){return this._ibtnEdit},set_ibtnEdit:function(n){this._ibtnEdit!==n&&(this._ibtnEdit=n)},get_ibtnDelete:function(){return this._ibtnDelete},set_ibtnDelete:function(n){this._ibtnDelete!==n&&(this._ibtnDelete=n)},get_ibtnExportCSV:function(){return this._ibtnExportCSV},set_ibtnExportCSV:function(n){this._ibtnExportCSV!==n&&(this._ibtnExportCSV=n)},get_ibtnExportPurchaseHUB:function(){return this._ibtnExportPurchaseHUB},set_ibtnExportPurchaseHUB:function(n){this._ibtnExportPurchaseHUB!==n&&(this._ibtnExportPurchaseHUB=n)},get_ibtnNotify:function(){return this._ibtnNotify},set_ibtnNotify:function(n){this._ibtnNotify!==n&&(this._ibtnNotify=n)},get_ibtnRelease:function(){return this._ibtnRelease},set_ibtnRelease:function(n){this._ibtnRelease!==n&&(this._ibtnRelease=n)},get_blnPOHub:function(){return this._blnPOHub},set_blnPOHub:function(n){this._blnPOHub!==n&&(this._blnPOHub=n)},get_ibtnClose:function(){return this._ibtnClose},set_ibtnClose:function(n){this._ibtnClose!==n&&(this._ibtnClose=n)},get_ibtnNoBid:function(){return this._ibtnNoBid},set_ibtnNoBid:function(n){this._ibtnNoBid!==n&&(this._ibtnNoBid=n)},get_ibtnNote:function(){return this._ibtnNote},set_ibtnNote:function(n){this._ibtnNote!==n&&(this._ibtnNote=n)},get_ibtnComplete:function(){return this._ibtnComplete},set_ibtnComplete:function(n){this._ibtnComplete!==n&&(this._ibtnComplete=n)},addGotData:function(n){this.get_events().addHandler("GotData",n)},removeGotData:function(n){this.get_events().removeHandler("GotData",n)},onGotData:function(){var n=this.get_events().getHandler("GotData");n&&n(this,Sys.EventArgs.Empty)},addCallBeforeRelease:function(n){this.get_events().addHandler("CallBeforeRelease",n)},removeCallBeforeRelease:function(n){this.get_events().removeHandler("CallBeforeRelease",n)},onCallBeforeRelease:function(){var n=this.get_events().getHandler("CallBeforeRelease");n&&n(this,Sys.EventArgs.Empty)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Nuggets.ClientImportBOMMainInfo.callBaseMethod(this,"initialize");this.addRefreshEvent(Function.createDelegate(this,this.getData));this._ibtnExportCSV&&$R_IBTN.addClick(this._ibtnExportCSV,Function.createDelegate(this,this.showExportCSV));this._ibtnEdit&&($R_IBTN.addClick(this._ibtnEdit,Function.createDelegate(this,this.showEditForm)),this._frmEdit=$find(this._aryFormIDs[0]),this._frmEdit._intBOMID=this._intBOMID,this._frmEdit._BomCode=this._BomCode,this._frmEdit._BomName=this._BomName,this._frmEdit._BomCompanyName=this._BomCompanyName,this._frmEdit._BomCompanyNo=this._BomCompanyNo,this._frmEdit.addCancel(Function.createDelegate(this,this.cancelEdit)),this._frmEdit.addSaveComplete(Function.createDelegate(this,this.saveEditComplete)));this._ibtnComplete&&($R_IBTN.addClick(this._ibtnComplete,Function.createDelegate(this,this.showMarkasComplete)),this._frmMarkasComplete=$find(this._aryFormIDs[1]),this._frmMarkasComplete._intBOMID=this._intBOMID,this._frmMarkasComplete.addNotConfirmed(Function.createDelegate(this,this.cancelComplete)),this._frmMarkasComplete.addSaveComplete(Function.createDelegate(this,this.saveCompFormComplete)));this._ibtnExportPurchaseHUB&&($R_IBTN.addClick(this._ibtnExportPurchaseHUB,Function.createDelegate(this,this.showSaveAsHUBRFQComplete)),this._frmSaveAsHUBRFQ=$find(this._aryFormIDs[2]),this._frmSaveAsHUBRFQ._intBOMID=this._intBOMID,this._frmSaveAsHUBRFQ.addNotConfirmed(Function.createDelegate(this,this.cancelSaveAsHUBRFQComplete)),this._frmSaveAsHUBRFQ.addSaveComplete(Function.createDelegate(this,this.saveAsHUBRFQComplete)));this.getData()},dispose:function(){this.isDisposed||(this._ibtnEdit&&$R_IBTN.clearHandlers(this._ibtnEdit),this._ibtnNotify&&$R_IBTN.clearHandlers(this._ibtnNotify),this._ibtnDelete&&$R_IBTN.clearHandlers(this._ibtnDelete),this._ibtnClose&&$R_IBTN.clearHandlers(this._ibtnClose),this._frmEdit&&this._frmEdit.dispose(),this._frmDelete&&this._frmDelete.dispose(),this._ibtnNotify=null,this._intBOMID=null,this._ibtnEdit=null,this._ibtnDelete=null,this._frmEdit=null,this._frmDelete=null,this._ibtnExportCSV=null,this._blnHasRequirement=null,this._blnPOHub=null,this._blnRequestedToPoHub=null,this._blnRelease=null,this._intCurrencyNo=null,this._blnAllHasDelDate=null,this._blnAllHasProduct=null,this._blnCanReleaseAll=null,this._blnAllItemHasSourcing=null,this._ibtnClose=null,this._ibtnNoBid=null,this._ibtnNote=null,this._ValidMessage=null,this._SalesmanId=null,this._SalesmanName=null,this._ibtnComplete=null,this.__ibtnExportPurchaseHUB=null,Rebound.GlobalTrader.Site.Controls.Nuggets.ClientImportBOMMainInfo.callBaseMethod(this,"dispose"))},getData:function(){this._blnRequestedToPoHub=!1;this.getData_Start();var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/ClientImportBOMMainInfo");n.set_DataObject("ClientImportBOMMainInfo");n.set_DataAction("GetData");n.addParameter("id",this._intBOMID);n.addDataOK(Function.createDelegate(this,this.getDataOK));n.addError(Function.createDelegate(this,this.getDataError));n.addTimeout(Function.createDelegate(this,this.getDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getDataOK:function(n){var t=n._result;this.BOMStatus=t.BOMStatus;t.IsPoHub&&(this._isPurchaseHub=t.IsPoHub,this.disableNotifyAndExportButton(!t.IsPoHub));this._BomCode=t.Code;this._BomName=t.Name;this._BomCompanyName=t.Company;this._BomCompanyNo=t.CompanyNo;this.setFieldValue("ctlCode",$R_FN.setCleanTextValue(t.Code));this.setFieldValue("ctlName",$R_FN.setCleanTextValue(t.Name));this.setFieldValue("ctlNotes",$R_FN.setCleanTextValue(t.Notes,!0));this.setFieldValue("ctlInActive",t.InActive);this.setFieldValue("hidCompanyNo",t.CompanyNo);this.setFieldValue("hidContactNo",t.ContactNo);this.setFieldValue("ctlCompanyName",$R_FN.setCleanTextValue(t.Company));this.setFieldValue("ctlContactName",$R_FN.setCleanTextValue(t.Contact));this.setFieldValue("ctlCurrencyCode",$R_FN.setCleanTextValue(t.CurrencyName));this.setFieldValue("ctlSalespersion",$R_FN.setCleanTextValue(t.Salesman));this.setFieldValue("hidSalesmanId",$R_FN.setCleanTextValue(t.SalesmanId));this._blnRequestedToPoHub=t.blnReqToPoHub;this._blnRelease=t.blnRelease;this.setFieldValue("hidDisplayStatus",$R_FN.setCleanTextValue(t.BOMStatus));this._intCurrencyNo=t.CurrencyNo;this._stringCurrency=t.Currency_Code;this._intCurrencyNo=t.CurrencyNo;this.setDLUP(t.DLUP);this._inActive=t.InActive;this._CurrentSupplier=t.CurrentSupplier;this._QuoteRequired=t.QuoteRequired;this._blnAllItemHasSourcing=t.AllItemHasSourcing;this._UpdatedBy=t.UpdatedBy;this._isNoBidCount=t.isNoBidCount;this._isClosed=t.IsClosed;this._RequestToPOHubBy=t.RequestToPOHubBy;this._UpdateByPH=t.UpdateByPH;this._blnReqInValid=t.IsReqInValid;this._ValidMessage=t.ValidMessage;this.setFieldValue("hidSalesmanId",t.SalesmanId);this._SalesmanId=t.SalesmanId;this._SalesmanName=t.Salesman;this._isAddButtonEnable=!t.blnReqToPoHub&&!t.InActive;this._ClientBOMStatus=t.ClientBmStatus;this._ClientCompanyId=t.CompanyNo;this.setFieldValue("ctlMarkComplete",t.ClientBmStatus===4);this.setFieldValue("ctlRecordsProc",$R_FN.setCleanTextValue(t.RecordsProcessed));this.setFieldValue("ctlRecordsRemain",$R_FN.setCleanTextValue(t.RecordsRemaining));this.setFieldValue("ctlHUBRFQName",$R_FN.writeDoubleCellValue($RGT_nubButton_BOM(t.BomId,t.BomName),""));this.getDataOK_End();this.onGotData();this.enableButtons(!t.InActive)},getDataError:function(n){this.showError(!0,n.get_ErrorMessage())},enableButtons:function(n){n?(this._ibtnEdit&&$R_IBTN.enableButton(this._ibtnEdit,!(this._ClientBOMStatus===4||this._ClientBOMStatus===3)),this._ibtnComplete&&$R_IBTN.enableButton(this._ibtnComplete,!(this._ClientBOMStatus===4)),this._ibtnExportPurchaseHUB&&$R_IBTN.enableButton(this._ibtnExportPurchaseHUB,!(this._ClientBOMStatus===4||this._ClientBOMStatus===3))):(this._ibtnEdit&&$R_IBTN.enableButton(this._ibtnEdit,!1),this._ibtnComplete&&$R_IBTN.enableButton(this._ibtnComplete,!1),this._ibtnExportPurchaseHUB&&$R_IBTN.enableButton(this._ibtnExportPurchaseHUB,!1))},showEditForm:function(){this._frmEdit._intBOMID=this._intBOMID;this._frmEdit._BomCode=this._BomCode;this._frmEdit._BomName=this._BomName;this._frmEdit._BomCompanyName=this._BomCompanyName;this._frmEdit._BomCompanyNo=this._BomCompanyNo;this._frmEdit.setFieldValue("ctlCode",this.getFieldValue("ctlCode"));this._frmEdit.setFieldValue("ctlName",this.getFieldValue("ctlName"));this._frmEdit.setFieldValue("ctlNotes",this.getFieldValue("ctlNotes"));this._frmEdit.setFieldValue("ctlInActive",this.getFieldValue("ctlInActive"));this._frmEdit.setFieldValue("ctlCurrency",this._intCurrencyNo);this._frmEdit.setFieldValue("ctlCompany",this.getFieldValue("ctlCompanyName"));this._frmEdit.getFieldControl("ctlContact")._intCompanyID=this.getFieldValue("hidCompanyNo");this._frmEdit.getFieldDropDownData("ctlContact");this._frmEdit.setFieldValue("ctlContact",this.getFieldValue("hidContactNo"));this._frmEdit.setFieldValue("ctlSalespersion",this.getFieldValue("hidSalesmanId"),null,this.getFieldValue("ctlSalespersion"));this._frmEdit._blnRequestedToPoHub=this._blnRequestedToPoHub;this.showForm(this._frmEdit,!0)},hideEditForm:function(){this.showForm(this._frmEdit,!1)},cancelEdit:function(){this.hideEditForm()},saveEditComplete:function(){this.hideEditForm();this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully);this.getData()},showMarkasComplete:function(){this._frmMarkasComplete._intBOMID=this._intBOMID;this.showForm(this._frmMarkasComplete,!0)},showSaveAsHUBRFQComplete:function(){this._frmSaveAsHUBRFQ._intBOMID=this._intBOMID;this._frmSaveAsHUBRFQ._BomName=this._BomName;this.showForm(this._frmSaveAsHUBRFQ,!0)},showConfirmForm:function(){this._frmConfirm._intBOMID=this._intBOMID;this._frmConfirm._BomCode=this._BomCode;this._frmConfirm._BomName=this._BomName;this._frmConfirm._BomCompanyName=this._BomCompanyName;this._frmConfirm._BomCompanyNo=this._BomCompanyNo;this._frmConfirm._blnReqInValid=this._blnReqInValid;this._frmConfirm._ValidMessage=this._ValidMessage;this._frmConfirm._intContact2No=this._intContact2No;this.showForm(this._frmConfirm,!0)},hideConfirmForm:function(){this.showForm(this._frmConfirm,!1)},hideCompleteForm:function(){this.showForm(this._frmMarkasComplete,!1)},hideSaveAsHUBRFQForm:function(){this.showForm(this._frmSaveAsHUBRFQ,!1)},hideNoBidForm:function(){this.showForm(this._frmNoBid,!1)},hideConfirmCloseForm:function(){this.showForm(this._frmConfirmClose,!1)},hideReleaseForm:function(){this.showForm(this._frmRelease,!1)},cancelComplete:function(){this.hideCompleteForm()},cancelSaveAsHUBRFQComplete:function(){this.hideSaveAsHUBRFQForm()},showExportCSV:function(){this.getData_Start();var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/BOMItems");n.set_DataObject("BOMItems");n.set_DataAction("ExportToCSV");n.addParameter("id",this._intBOMID);n.addParameter("currency_Code",this._stringCurrency);n.addDataOK(Function.createDelegate(this,this.exportCSV_OK));n.addError(Function.createDelegate(this,this.exportCSV_Error));n.addTimeout(Function.createDelegate(this,this.exportCSV_Error));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},exportCSV_OK:function(n){var i=n._result,t;i.Filename&&(t=new Date,location.href=String.format("{0}?t={1}",i.Filename,t.getTime()),t=null);this.getDataOK_End()},exportCSV_Error:function(n){this.showError(!0,n.get_ErrorMessage())},saveCompFormComplete:function(){this.hideCompleteForm();this.getData()},saveAsHUBRFQComplete:function(){this.hideSaveAsHUBRFQForm();this.getData()},savePurchaseHUBData:function(){this.getData_Start();var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/ClientImportBOMMainInfo");n.set_DataObject("ClientImportBOMMainInfo");n.set_DataAction("savePurchaseHUBData");n.addParameter("id",this._intBOMID);n.addParameter("BomCode",this._BomCode);n.addParameter("BomName",this._BomName);n.addParameter("BomCompanyName",this._BomCompanyName);n.addParameter("BomCompanyNo",this._BomCompanyNo);n.addDataOK(Function.createDelegate(this,this.getDataOK));n.addError(Function.createDelegate(this,this.getDataError));n.addTimeout(Function.createDelegate(this,this.getDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},disableNotifyAndExportButton:function(n){this._ibtnNotify&&$R_IBTN.showButton(this._ibtnNotify,n)},enableDisableReleaseButton:function(){},getValidationOK:function(n){var t=n._result;this._blnReqInValid=t.IsReqInValid;this._ValidMessage=t.ValidMessage;this.getDataOK_End();this.onGotData();this.showConfirmForm()},getValidationError:function(n){this.showError(!0,n.get_ErrorMessage())}};Rebound.GlobalTrader.Site.Controls.Nuggets.ClientImportBOMMainInfo.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.ClientImportBOMMainInfo",Rebound.GlobalTrader.Site.Controls.Nuggets.Base);