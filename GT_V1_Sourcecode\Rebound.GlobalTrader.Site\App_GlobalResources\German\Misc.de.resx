<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="AddNew" xml:space="preserve">
    <value>Add New</value>
  </data>
  <data name="All" xml:space="preserve">
    <value>Alle</value>
  </data>
  <data name="Allocations" xml:space="preserve">
    <value>Verteilungen</value>
  </data>
  <data name="Alternate" xml:space="preserve">
    <value>Alternative</value>
  </data>
  <data name="Amended" xml:space="preserve">
    <value>Geändert</value>
  </data>
  <data name="AppTitle" xml:space="preserve">
    <value>Rebound Global:Trader</value>
  </data>
  <data name="Authorised" xml:space="preserve">
    <value>Autorisiert</value>
  </data>
  <data name="AutoSearchPart" xml:space="preserve">
    <value>Finden Sie eine Teilenummer</value>
  </data>
  <data name="BeginsWith" xml:space="preserve">
    <value>Beginn mit</value>
  </data>
  <data name="Browse" xml:space="preserve">
    <value>Grasen Sie</value>
  </data>
  <data name="BrowserError" xml:space="preserve">
    <value>Browsers-Störung</value>
  </data>
  <data name="BrowserWarning" xml:space="preserve">
    <value>Browsers-Warnung</value>
  </data>
  <data name="BuiltFor" xml:space="preserve">
    <value>Errichtet für</value>
  </data>
  <data name="BulkPrint" xml:space="preserve">
    <value>Drucken von großen Mengen</value>
  </data>
  <data name="CertificateOfConformance" xml:space="preserve">
    <value>Bescheinigung der Übereinstimmung</value>
  </data>
  <data name="ChangedField" xml:space="preserve">
    <value>Geändert {0}</value>
  </data>
  <data name="ChangedFieldToValue" xml:space="preserve">
    <value>Geändert {0} zu {1}</value>
  </data>
  <data name="ChangePassword" xml:space="preserve">
    <value>Ändern Sie Kennwort</value>
  </data>
  <data name="Checked" xml:space="preserve">
    <value>Überprüft</value>
  </data>
  <data name="Clear" xml:space="preserve">
    <value>Freier Raum</value>
  </data>
  <data name="ClearAll" xml:space="preserve">
    <value>Löschen Sie alle</value>
  </data>
  <data name="ClickToSnooze" xml:space="preserve">
    <value>Wieder innen erinnert zu werden Klicken</value>
  </data>
  <data name="Closed" xml:space="preserve">
    <value>Geschlossen</value>
  </data>
  <data name="CollapseAll" xml:space="preserve">
    <value>Stürzen alle ein</value>
  </data>
  <data name="Company" xml:space="preserve">
    <value>Firma</value>
  </data>
  <data name="CompanyShort" xml:space="preserve">
    <value>Firma</value>
  </data>
  <data name="Complete" xml:space="preserve">
    <value>Komplett</value>
  </data>
  <data name="Confirmed" xml:space="preserve">
    <value>Bestätigt</value>
  </data>
  <data name="Contacts" xml:space="preserve">
    <value>Kontakte</value>
  </data>
  <data name="ContactsForCompany" xml:space="preserve">
    <value>Kontakte für {0}</value>
  </data>
  <data name="ContactShort" xml:space="preserve">
    <value>Kontakt</value>
  </data>
  <data name="Contains" xml:space="preserve">
    <value>enthalten</value>
  </data>
  <data name="Credit" xml:space="preserve">
    <value>Kreditnote </value>
  </data>
  <data name="CreditNo" xml:space="preserve">
    <value>Gutschrift-Nr.</value>
  </data>
  <data name="CreditNote" xml:space="preserve">
    <value>Kreditnote </value>
  </data>
  <data name="CreditNotes" xml:space="preserve">
    <value>Kreditnoten</value>
  </data>
  <data name="CreditNoteShort" xml:space="preserve">
    <value>Credit</value>
  </data>
  <data name="CreditNotesShort" xml:space="preserve">
    <value>Kreditnoten</value>
  </data>
  <data name="Credits" xml:space="preserve">
    <value>Kreditnoten</value>
  </data>
  <data name="CRMA" xml:space="preserve">
    <value>Kunde RMA</value>
  </data>
  <data name="Current" xml:space="preserve">
    <value>Gegenwärtig</value>
  </data>
  <data name="CusReq" xml:space="preserve">
    <value>Kunden-Anforderung</value>
  </data>
  <data name="CustomerRequirement" xml:space="preserve">
    <value>Kunden-Anforderung</value>
  </data>
  <data name="CustomerRequirementNo" xml:space="preserve">
    <value>Kunden-Anforderung Nr</value>
  </data>
  <data name="CustomerRequirements" xml:space="preserve">
    <value>Kunden-Anforderungen</value>
  </data>
  <data name="CustomerRequirementShort" xml:space="preserve">
    <value>Kunden-Anforderung</value>
  </data>
  <data name="CustomerRequirementsShort" xml:space="preserve">
    <value>Kunden-Anforderungen</value>
  </data>
  <data name="CustomerRMA" xml:space="preserve">
    <value>Kunde RMA</value>
  </data>
  <data name="CustomerRMANo" xml:space="preserve">
    <value>Kunde RMA Nr</value>
  </data>
  <data name="CustomerRMAs" xml:space="preserve">
    <value>Kunde RMAs</value>
  </data>
  <data name="CustomerRMAShort" xml:space="preserve">
    <value>Kunde RMA</value>
  </data>
  <data name="CustomerRMAsShort" xml:space="preserve">
    <value>Kunde RMAs</value>
  </data>
  <data name="CustomerShort" xml:space="preserve">
    <value>Kreditnoten</value>
  </data>
  <data name="Days" xml:space="preserve">
    <value>Tage</value>
  </data>
  <data name="Days_1" xml:space="preserve">
    <value>1 Tag</value>
  </data>
  <data name="Days_2" xml:space="preserve">
    <value>2 Tage</value>
  </data>
  <data name="Days_3" xml:space="preserve">
    <value>3 Tage</value>
  </data>
  <data name="Days_4" xml:space="preserve">
    <value>4 Tage</value>
  </data>
  <data name="Debit" xml:space="preserve">
    <value>Belastungsanzeige</value>
  </data>
  <data name="DebitNo" xml:space="preserve">
    <value>Schuldposten-Nr.</value>
  </data>
  <data name="DebitNote" xml:space="preserve">
    <value>Belastungsanzeige</value>
  </data>
  <data name="DebitNotes" xml:space="preserve">
    <value>Belastungsanzeigen</value>
  </data>
  <data name="DebitNoteShort" xml:space="preserve">
    <value>Debit</value>
  </data>
  <data name="DebitNotesShort" xml:space="preserve">
    <value>Belastungsanzeigen</value>
  </data>
  <data name="Debits" xml:space="preserve">
    <value>Belastungsanzeigen</value>
  </data>
  <data name="Default" xml:space="preserve">
    <value>Rückstellung</value>
  </data>
  <data name="Despatched" xml:space="preserve">
    <value>Part Despatched</value>
  </data>
  <data name="Division" xml:space="preserve">
    <value>Abteilung</value>
  </data>
  <data name="DLUP" xml:space="preserve">
    <value>Letztes modernisiertes</value>
  </data>
  <data name="Ellipses" xml:space="preserve">
    <value>...</value>
  </data>
  <data name="EndsWith" xml:space="preserve">
    <value>ende mit</value>
  </data>
  <data name="EqualTo" xml:space="preserve">
    <value>=</value>
  </data>
  <data name="Error" xml:space="preserve">
    <value>Störung</value>
  </data>
  <data name="ExistingDebitNote" xml:space="preserve">
    <value>Vorhandene Belastungsanzeige</value>
  </data>
  <data name="ExistingGI" xml:space="preserve">
    <value>Vorhandene Waren in der Anmerkung</value>
  </data>
  <data name="ExistingInvoice" xml:space="preserve">
    <value>Vorhandene Rechnung</value>
  </data>
  <data name="ExpandAll" xml:space="preserve">
    <value>Erweitern Sie alle</value>
  </data>
  <data name="Expires" xml:space="preserve">
    <value>läuft ab</value>
  </data>
  <data name="Explain_Settings_Company" xml:space="preserve">
    <value>Einstellungen für {0}</value>
  </data>
  <data name="Explain_Settings_Global" xml:space="preserve">
    <value>Einstellungen für alle Firmen</value>
  </data>
  <data name="Explain_Settings_Personal" xml:space="preserve">
    <value>Preferences and settings personal to you</value>
  </data>
  <data name="Explain_Settings_Security" xml:space="preserve">
    <value>Sicherheitsbenutzer, -gruppen und -erlaubnis</value>
  </data>
  <data name="Female" xml:space="preserve">
    <value>Frau</value>
  </data>
  <data name="Filter" xml:space="preserve">
    <value>Filter</value>
  </data>
  <data name="FilterResults" xml:space="preserve">
    <value>Filter-Resultate</value>
  </data>
  <data name="For" xml:space="preserve">
    <value>Für</value>
  </data>
  <data name="FromCustomerRMA" xml:space="preserve">
    <value>Vom Kunden RMA</value>
  </data>
  <data name="FromCustomerRMALine" xml:space="preserve">
    <value>Der Linie von des Kunden-RMA</value>
  </data>
  <data name="FromInvoice" xml:space="preserve">
    <value>Von der Rechnung</value>
  </data>
  <data name="FromInvoiceLine" xml:space="preserve">
    <value>Von der Rechnungs-Linie</value>
  </data>
  <data name="FromMasterPartList" xml:space="preserve">
    <value>Von der Vorlagenteilliste</value>
  </data>
  <data name="FromPurchaseOrderLine" xml:space="preserve">
    <value>Von der Kaufauftrag-Linie</value>
  </data>
  <data name="FromPurchaseOrders" xml:space="preserve">
    <value>Von den Kaufaufträgen</value>
  </data>
  <data name="FromPurchaseRequisitions" xml:space="preserve">
    <value>Von den Kauf-Forderungen</value>
  </data>
  <data name="FromQuotes" xml:space="preserve">
    <value>Von den Preisangaben</value>
  </data>
  <data name="FromRequirements" xml:space="preserve">
    <value>Von den Anforderungen</value>
  </data>
  <data name="FromSalesOrders" xml:space="preserve">
    <value>Von den Verkaufs-Aufträgen</value>
  </data>
  <data name="FromService" xml:space="preserve">
    <value>Vom Service</value>
  </data>
  <data name="FromStock" xml:space="preserve">
    <value>Vom Vorrat</value>
  </data>
  <data name="FromSupplierRMALine" xml:space="preserve">
    <value>Der Linie von des Lieferanten-RMA</value>
  </data>
  <data name="GI" xml:space="preserve">
    <value>Waren im</value>
  </data>
  <data name="GIDocketSODatePromisedLines" xml:space="preserve">
    <value>SO zugeteilt durch {0} Linien</value>
  </data>
  <data name="GoodsIn" xml:space="preserve">
    <value>Waren im</value>
  </data>
  <data name="GoodsInNo" xml:space="preserve">
    <value>Waren im Nr.</value>
  </data>
  <data name="GoodsInShort" xml:space="preserve">
    <value>Waren im</value>
  </data>
  <data name="GP" xml:space="preserve">
    <value>GP</value>
  </data>
  <data name="GreaterThan" xml:space="preserve">
    <value>&gt;</value>
  </data>
  <data name="GreaterThanOrEqualTo" xml:space="preserve">
    <value>&gt;=</value>
  </data>
  <data name="Historical" xml:space="preserve">
    <value>Historisch</value>
  </data>
  <data name="HomepageSelectUser" xml:space="preserve">
    <value>Wählen Sie Benutzer vor </value>
  </data>
  <data name="HomepageViewForMyself" xml:space="preserve">
    <value>Ansicht für mich?</value>
  </data>
  <data name="Hours" xml:space="preserve">
    <value>Stunden</value>
  </data>
  <data name="Hours_1" xml:space="preserve">
    <value>1 Stunde</value>
  </data>
  <data name="Hours_2" xml:space="preserve">
    <value>2 Stunden</value>
  </data>
  <data name="Hours_4" xml:space="preserve">
    <value>4 Stunden</value>
  </data>
  <data name="Hours_8" xml:space="preserve">
    <value>8 Stunden</value>
  </data>
  <data name="Inactive" xml:space="preserve">
    <value>Unaktiviert</value>
  </data>
  <data name="Information" xml:space="preserve">
    <value>Information</value>
  </data>
  <data name="InTransit" xml:space="preserve">
    <value>Bei dem Transport</value>
  </data>
  <data name="Invoice" xml:space="preserve">
    <value>Rechnung</value>
  </data>
  <data name="InvoiceLineAllocationsForCRMA" xml:space="preserve">
    <value>Rechnungs-Linie Verteilungen für diesen Kunden RMA</value>
  </data>
  <data name="InvoiceLinesForCRMA" xml:space="preserve">
    <value>Rechnungs-Linien für Kunden RMA</value>
  </data>
  <data name="InvoiceLinesServices" xml:space="preserve">
    <value>Verkaufs-Auftrags-Hausanschlussleitungen</value>
  </data>
  <data name="InvoiceNo" xml:space="preserve">
    <value>Rechnungs-Nr.</value>
  </data>
  <data name="Invoices" xml:space="preserve">
    <value>Rechnungen</value>
  </data>
  <data name="InvoiceShort" xml:space="preserve">
    <value>Rechnung</value>
  </data>
  <data name="InvoicesShort" xml:space="preserve">
    <value>Rechnungen</value>
  </data>
  <data name="JumpTo" xml:space="preserve">
    <value>Springen Sie zu</value>
  </data>
  <data name="LandedCostCalculator" xml:space="preserve">
    <value>Gelandeter Kosten-Rechner</value>
  </data>
  <data name="LessThan" xml:space="preserve">
    <value>&lt;</value>
  </data>
  <data name="LessThanOrEqualTo" xml:space="preserve">
    <value>&lt;=</value>
  </data>
  <data name="LinePagingInactive" xml:space="preserve">
    <value>*** Unaktiviertes ***</value>
  </data>
  <data name="LM" xml:space="preserve">
    <value>Letzter Monat</value>
  </data>
  <data name="Loading" xml:space="preserve">
    <value>Laden</value>
  </data>
  <data name="Login" xml:space="preserve">
    <value>Login</value>
  </data>
  <data name="Lot" xml:space="preserve">
    <value>Los</value>
  </data>
  <data name="LotNo" xml:space="preserve">
    <value>Los-Nr.</value>
  </data>
  <data name="Lots" xml:space="preserve">
    <value>Los</value>
  </data>
  <data name="LotShort" xml:space="preserve">
    <value>Lose</value>
  </data>
  <data name="LotsShort" xml:space="preserve">
    <value>Lose</value>
  </data>
  <data name="LY" xml:space="preserve">
    <value>Letztes Jahr</value>
  </data>
  <data name="MacAddress" xml:space="preserve">
    <value>MAC Adresse</value>
  </data>
  <data name="Male" xml:space="preserve">
    <value>Mann</value>
  </data>
  <data name="ManufacturerShort" xml:space="preserve">
    <value>Hersteller</value>
  </data>
  <data name="Minutes_10" xml:space="preserve">
    <value>10  Minuten</value>
  </data>
  <data name="Minutes_15" xml:space="preserve">
    <value>15  Minuten</value>
  </data>
  <data name="Minutes_30" xml:space="preserve">
    <value>30 Minuten</value>
  </data>
  <data name="Minutes_5" xml:space="preserve">
    <value>5 Minuten</value>
  </data>
  <data name="MoreOpenPurchaseOrders" xml:space="preserve">
    <value>Mehr Kaufaufträge</value>
  </data>
  <data name="MoreOpenQuotes" xml:space="preserve">
    <value>Mehr Preisangaben</value>
  </data>
  <data name="MoreOpenRequirements" xml:space="preserve">
    <value>Mehr Anforderungen</value>
  </data>
  <data name="MoreOpenSalesOrders" xml:space="preserve">
    <value>Mehr Verkaufs-Aufträge</value>
  </data>
  <data name="MorePurchaseOrdersDueIn" xml:space="preserve">
    <value>Mehr Kaufaufträge passend innen</value>
  </data>
  <data name="MoreReceivedOrders" xml:space="preserve">
    <value>More Received Purchase Orders</value>
  </data>
  <data name="MoreSalesOrdersReadyToShip" xml:space="preserve">
    <value>Mehr Verkaufs-Aufträge bereit zu versenden</value>
  </data>
  <data name="MTD" xml:space="preserve">
    <value>MTD</value>
  </data>
  <data name="My" xml:space="preserve">
    <value>Meine</value>
  </data>
  <data name="NewCreditNote" xml:space="preserve">
    <value>Neue Kreditnote</value>
  </data>
  <data name="NewCustomerRequirement" xml:space="preserve">
    <value>Neue Kunden-Anforderung</value>
  </data>
  <data name="NewCustomerRMA" xml:space="preserve">
    <value>Neuer Kunde RMA</value>
  </data>
  <data name="NewDebitNote" xml:space="preserve">
    <value>Neue Belastungsanzeige</value>
  </data>
  <data name="NewGI" xml:space="preserve">
    <value>Neue Waren innen</value>
  </data>
  <data name="NewGoodsIn" xml:space="preserve">
    <value>Neue Waren innen</value>
  </data>
  <data name="NewInvoice" xml:space="preserve">
    <value>Neue Rechnung</value>
  </data>
  <data name="NewLineItem" xml:space="preserve">
    <value>Neue Linie Einzelteil</value>
  </data>
  <data name="NewPurchaseOrder" xml:space="preserve">
    <value>Neuer Kaufauftrag</value>
  </data>
  <data name="NewPurchaseRequisition" xml:space="preserve">
    <value>Neue Kauf-Forderung</value>
  </data>
  <data name="NewQuote" xml:space="preserve">
    <value>NeuePreisangabe</value>
  </data>
  <data name="NewSalesOrder" xml:space="preserve">
    <value>Neuer Verkaufs-Auftrag</value>
  </data>
  <data name="NewSupplierRMA" xml:space="preserve">
    <value>Neuer Lieferant RMA</value>
  </data>
  <data name="NewSupplierRMAShipment" xml:space="preserve">
    <value>Neuer Versand des Lieferanten-RMA</value>
  </data>
  <data name="NM" xml:space="preserve">
    <value>Nächstes Monat</value>
  </data>
  <data name="No" xml:space="preserve">
    <value>Nein</value>
  </data>
  <data name="Number" xml:space="preserve">
    <value>Nummer</value>
  </data>
  <data name="NumberShort" xml:space="preserve">
    <value>Nr.</value>
  </data>
  <data name="NumberShorter" xml:space="preserve">
    <value>#</value>
  </data>
  <data name="NY" xml:space="preserve">
    <value>Nächstes Jahr</value>
  </data>
  <data name="Of" xml:space="preserve">
    <value>von</value>
  </data>
  <data name="Open" xml:space="preserve">
    <value>Geöffnet</value>
  </data>
  <data name="Ordered" xml:space="preserve">
    <value>Bestellt</value>
  </data>
  <data name="Overdue" xml:space="preserve">
    <value>Überfällig</value>
  </data>
  <data name="PackingSlip" xml:space="preserve">
    <value>Verpackungs-Beleg</value>
  </data>
  <data name="Page" xml:space="preserve">
    <value>Seite</value>
  </data>
  <data name="Paid" xml:space="preserve">
    <value>Zahlend</value>
  </data>
  <data name="PartNo" xml:space="preserve">
    <value>Teilenummer</value>
  </data>
  <data name="Pct" xml:space="preserve">
    <value>%</value>
  </data>
  <data name="PerPage" xml:space="preserve">
    <value>Pro Seite</value>
  </data>
  <data name="Placed" xml:space="preserve">
    <value>Gesetzt</value>
  </data>
  <data name="PO" xml:space="preserve">
    <value>Kaufauftrag</value>
  </data>
  <data name="POLinesForDebitNote" xml:space="preserve">
    <value>Kaufauftrag-Linien für diese Belastungsanzeige</value>
  </data>
  <data name="POLinesForSRMA" xml:space="preserve">
    <value>Kaufauftrag-Linien für diesen Lieferanten RMA</value>
  </data>
  <data name="PrintAd" xml:space="preserve">
    <value>Verursacht mit {0}</value>
  </data>
  <data name="PriorityHigh" xml:space="preserve">
    <value>Hoch</value>
  </data>
  <data name="PriorityNormal" xml:space="preserve">
    <value>Normal</value>
  </data>
  <data name="ProFormaInvoice" xml:space="preserve">
    <value>Proformarechnung</value>
  </data>
  <data name="ProspectShort" xml:space="preserve">
    <value>Aussicht</value>
  </data>
  <data name="PurchaseOrder" xml:space="preserve">
    <value>Kaufauftrag</value>
  </data>
  <data name="PurchaseOrderNo" xml:space="preserve">
    <value>Kaufauftrag-Nr.</value>
  </data>
  <data name="PurchaseOrders" xml:space="preserve">
    <value>Kaufaufträge</value>
  </data>
  <data name="PurchaseOrderShort" xml:space="preserve">
    <value>PO</value>
  </data>
  <data name="PurchaseOrdersShort" xml:space="preserve">
    <value>POs</value>
  </data>
  <data name="PurchaseRequisition" xml:space="preserve">
    <value>Kauf-Forderung</value>
  </data>
  <data name="PurchaseRequisitions" xml:space="preserve">
    <value>Kauf-Forderungen</value>
  </data>
  <data name="PurchaseRequisitionsShort" xml:space="preserve">
    <value>Kauf-Forderungen</value>
  </data>
  <data name="Qu" xml:space="preserve">
    <value>Preisangabe</value>
  </data>
  <data name="QuantityOrderedAndShipped" xml:space="preserve">
    <value>({0} bestellt, {1} versendet)</value>
  </data>
  <data name="Quarantined" xml:space="preserve">
    <value>Unter Quarantäne gestellt</value>
  </data>
  <data name="QuickSearchWaterMark" xml:space="preserve">
    <value>&lt; schnelles Suche&gt;</value>
  </data>
  <data name="Quote" xml:space="preserve">
    <value>Preisangabe</value>
  </data>
  <data name="QuoteNo" xml:space="preserve">
    <value>Preisangabe Nr.</value>
  </data>
  <data name="Quotes" xml:space="preserve">
    <value>Preisangaben</value>
  </data>
  <data name="QuoteShort" xml:space="preserve">
    <value>Preisangabe</value>
  </data>
  <data name="QuotesShort" xml:space="preserve">
    <value>Preisangaben</value>
  </data>
  <data name="Ready" xml:space="preserve">
    <value>Bereit</value>
  </data>
  <data name="ReceiveCustomerRMA" xml:space="preserve">
    <value>Empfangen Sie Kunden RMA</value>
  </data>
  <data name="ReceiveCustomerRMAShort" xml:space="preserve">
    <value>Empfangen Sie Kunden RMA</value>
  </data>
  <data name="ReceiveCustomerRMAsShort" xml:space="preserve">
    <value>Empfangen Sie Kunden RMAs</value>
  </data>
  <data name="Received" xml:space="preserve">
    <value>Empfangen Sie Kaufauftrag</value>
  </data>
  <data name="ReceivedCRMAs" xml:space="preserve">
    <value>Empfangener Kunde RMAs</value>
  </data>
  <data name="ReceivedCustomerRMAShort" xml:space="preserve">
    <value>Empfangener Kunde RMAs</value>
  </data>
  <data name="ReceivedPurchaseOrders" xml:space="preserve">
    <value>Empfangene Kaufaufträge</value>
  </data>
  <data name="ReceivedPurchaseOrderShort" xml:space="preserve">
    <value>Empfangene POs</value>
  </data>
  <data name="ReceivePurchaseOrder" xml:space="preserve">
    <value>Empfangen Sie Kaufauftrag</value>
  </data>
  <data name="ReceivePurchaseOrderShort" xml:space="preserve">
    <value>Empfangen Sie PO</value>
  </data>
  <data name="ReceivePurchaseOrdersShort" xml:space="preserve">
    <value>Empfangen Sie POs</value>
  </data>
  <data name="ReceivingDocket" xml:space="preserve">
    <value>Empfangen des Lieferscheins</value>
  </data>
  <data name="Recent" xml:space="preserve">
    <value>Neu</value>
  </data>
  <data name="RecentPartsSourced" xml:space="preserve">
    <value>Neue Teile Ursprungs</value>
  </data>
  <data name="RequiredField" xml:space="preserve">
    <value>denotes a required field</value>
  </data>
  <data name="RequirementSourcingResults" xml:space="preserve">
    <value>Anforderungs-Auftreten-Resultate</value>
  </data>
  <data name="Reselect" xml:space="preserve">
    <value>Wählen Sie neu</value>
  </data>
  <data name="ResultsLimit" xml:space="preserve">
    <value>Resultats-Begrenzung</value>
  </data>
  <data name="ResultsWithOptionalS" xml:space="preserve">
    <value>resultate</value>
  </data>
  <data name="ROHSCompliant" xml:space="preserve">
    <value>RoHS gefällig</value>
  </data>
  <data name="ROHSExempt" xml:space="preserve">
    <value>RoHS ausgenommen</value>
  </data>
  <data name="ROHSNonCompliant" xml:space="preserve">
    <value>RoHS Non-compliant</value>
  </data>
  <data name="ROHSNotApplicable" xml:space="preserve">
    <value>RoHS nicht anwendbar</value>
  </data>
  <data name="ROHSUnknown" xml:space="preserve">
    <value>RoHS Unknown</value>
  </data>
  <data name="SalesOrder" xml:space="preserve">
    <value>Verkaufs-Auftrag</value>
  </data>
  <data name="SalesOrderNo" xml:space="preserve">
    <value>Verkaufs-Auftrag Nr</value>
  </data>
  <data name="SalesOrders" xml:space="preserve">
    <value>Verkaufs-Aufträge</value>
  </data>
  <data name="SalesOrderShort" xml:space="preserve">
    <value>SO</value>
  </data>
  <data name="SalesOrdersShort" xml:space="preserve">
    <value>SOs</value>
  </data>
  <data name="Saving" xml:space="preserve">
    <value>Einsparung</value>
  </data>
  <data name="Search" xml:space="preserve">
    <value>Suche</value>
  </data>
  <data name="Searching" xml:space="preserve">
    <value>Suchen</value>
  </data>
  <data name="Select" xml:space="preserve">
    <value>Auserwählt</value>
  </data>
  <data name="SelectAll" xml:space="preserve">
    <value>Wählen Sie alle vor</value>
  </data>
  <data name="SelectedPart" xml:space="preserve">
    <value>Z.Z. vorgewähltes Teil</value>
  </data>
  <data name="Sending" xml:space="preserve">
    <value>Senden</value>
  </data>
  <data name="Service" xml:space="preserve">
    <value>Service</value>
  </data>
  <data name="ServiceName" xml:space="preserve">
    <value>Service Name</value>
  </data>
  <data name="Services" xml:space="preserve">
    <value>Dienstleistungen</value>
  </data>
  <data name="ServiceShort" xml:space="preserve">
    <value>Service</value>
  </data>
  <data name="ServicesShort" xml:space="preserve">
    <value>Dienstleistungen</value>
  </data>
  <data name="Setup_CompanySettings" xml:space="preserve">
    <value>Firma-Einstellungen</value>
  </data>
  <data name="Setup_GlobalSettings" xml:space="preserve">
    <value>Global Settings</value>
  </data>
  <data name="Setup_Personal" xml:space="preserve">
    <value>Persönliche Einstellungen</value>
  </data>
  <data name="Setup_Security" xml:space="preserve">
    <value>Sicherheits-Einstellungen</value>
  </data>
  <data name="Ship" xml:space="preserve">
    <value>Versendet</value>
  </data>
  <data name="ShipASAP" xml:space="preserve">
    <value>Schiff so bald wie möglich </value>
  </data>
  <data name="Shipped" xml:space="preserve">
    <value>Versendet</value>
  </data>
  <data name="ShippedLines" xml:space="preserve">
    <value>Versendete Linien</value>
  </data>
  <data name="ShipSalesOrder" xml:space="preserve">
    <value>Schiffs-Verkaufs-Auftrag</value>
  </data>
  <data name="ShipSalesOrderShort" xml:space="preserve">
    <value>Schiff SO</value>
  </data>
  <data name="ShipSalesOrdersShort" xml:space="preserve">
    <value>Schiff SOs</value>
  </data>
  <data name="ShipSupplierRMA" xml:space="preserve">
    <value>Schiffs-Lieferant RMA</value>
  </data>
  <data name="ShipSupplierRMAShort" xml:space="preserve">
    <value>Schiffs-Lieferant RMA</value>
  </data>
  <data name="ShipSupplierRMAsShort" xml:space="preserve">
    <value>Schiffs-Lieferant RMAs</value>
  </data>
  <data name="ShortShipped" xml:space="preserve">
    <value>Kurzschluss versendete</value>
  </data>
  <data name="ShowingXOfYResults" xml:space="preserve">
    <value>Darstellen {0} von {1} Resultat</value>
  </data>
  <data name="SO" xml:space="preserve">
    <value>Verkaufs-Auftrag</value>
  </data>
  <data name="Sold" xml:space="preserve">
    <value>Verkauft</value>
  </data>
  <data name="SOReport" xml:space="preserve">
    <value>Verkaufs-Auftrags-Report</value>
  </data>
  <data name="SourcingShort" xml:space="preserve">
    <value>Auftreten</value>
  </data>
  <data name="SRMA" xml:space="preserve">
    <value>Lieferant RMA</value>
  </data>
  <data name="SRMAShipment" xml:space="preserve">
    <value>SupplierRMA Shipment</value>
  </data>
  <data name="StandardShipping" xml:space="preserve">
    <value>(Standardschiff innen {0})</value>
  </data>
  <data name="Status" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="StockAvailable" xml:space="preserve">
    <value>Vorhanden</value>
  </data>
  <data name="StockItem" xml:space="preserve">
    <value>Auf lagereinzelteil</value>
  </data>
  <data name="StockItems" xml:space="preserve">
    <value>Vorrat</value>
  </data>
  <data name="StockItemShort" xml:space="preserve">
    <value>Auf lagereinzelteil</value>
  </data>
  <data name="StockItemsShort" xml:space="preserve">
    <value>Vorrat</value>
  </data>
  <data name="StockLeftToAllocate" xml:space="preserve">
    <value>vorhanden zuteilen</value>
  </data>
  <data name="StockOrdered" xml:space="preserve">
    <value>bestellt</value>
  </data>
  <data name="StockShort" xml:space="preserve">
    <value>Vorrat</value>
  </data>
  <data name="SupplierRMA" xml:space="preserve">
    <value>Lieferant RMA</value>
  </data>
  <data name="SupplierRMANo" xml:space="preserve">
    <value>Lieferant RMA Nr</value>
  </data>
  <data name="SupplierRMAs" xml:space="preserve">
    <value>Lieferant RMAs</value>
  </data>
  <data name="SupplierRMAShipment" xml:space="preserve">
    <value>SupplierRMA Shipment</value>
  </data>
  <data name="SupplierRMAShort" xml:space="preserve">
    <value>Lieferant RMA</value>
  </data>
  <data name="SupplierRMAsShort" xml:space="preserve">
    <value>Lieferant RMAs</value>
  </data>
  <data name="SupplierShort" xml:space="preserve">
    <value>Lieferant </value>
  </data>
  <data name="Team" xml:space="preserve">
    <value>Mannschaft</value>
  </data>
  <data name="TM" xml:space="preserve">
    <value>Dieser Monat</value>
  </data>
  <data name="Today" xml:space="preserve">
    <value>Heute</value>
  </data>
  <data name="ToDoAlert" xml:space="preserve">
    <value>Anzeige</value>
  </data>
  <data name="Total" xml:space="preserve">
    <value>Gesamtmenge</value>
  </data>
  <data name="TY" xml:space="preserve">
    <value>Dieses Jahr</value>
  </data>
  <data name="UpdatedByDateAndUser" xml:space="preserve">
    <value>{0} durch {1}</value>
  </data>
  <data name="View" xml:space="preserve">
    <value>Ansicht</value>
  </data>
  <data name="Warning" xml:space="preserve">
    <value>Warnung</value>
  </data>
  <data name="Weeks_1" xml:space="preserve">
    <value>1 Woche</value>
  </data>
  <data name="Weeks_2" xml:space="preserve">
    <value>2 Wochen </value>
  </data>
  <data name="Welcome" xml:space="preserve">
    <value>Willkommen</value>
  </data>
  <data name="XDaysAgo" xml:space="preserve">
    <value>vor {0} Tag</value>
  </data>
  <data name="Yes" xml:space="preserve">
    <value>Ja</value>
  </data>
  <data name="Yesterday" xml:space="preserve">
    <value>Gestern</value>
  </data>
  <data name="YTD" xml:space="preserve">
    <value>YTD</value>
  </data>
</root>