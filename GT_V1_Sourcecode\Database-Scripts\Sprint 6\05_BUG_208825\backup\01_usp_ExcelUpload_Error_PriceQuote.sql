﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

IF OBJECT_ID('dbo.usp_ExcelUpload_Error_PriceQuote', 'P') IS NOT NULL
BEGIN
    DROP PROCEDURE dbo.usp_ExcelUpload_Error_PriceQuote
END
GO

/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-201934]		An.TranTan			06-May-2024		Update			Change the comparison logic between import supplier part and requirement part.
[US-205174]		An.TranTan			17-Jun-2024		Update			Re-format script + update logic:
																	- Update column number to validate because of new column CLIENT NO insert at Column2
																	- Specific client id for tbCustomerRequirement
[US-205174]		An.TranTan			05-Jul-2024		Update			Remove logic check supplier name within client, allow get from all clients
[US-205174]		Phuc Hoang			08-Jul-2024		Update			Remove logic check Duplicate
===========================================================================================
*/
CREATE PROCEDURE [dbo].[usp_ExcelUpload_Error_PriceQuote]
    @UserId INT = 0,
    @ClientId INT = 0,
    @SelectedClientId int = 0
WITH RECOMPILE
AS
BEGIN
    SELECT PriceQuoteImportId,
           (case
                when len(Column1) > 30 then
                    dbo.stripAlphahnumeric(SUBSTRING(Column1, 0, 30))
                else
                    dbo.stripAlphahnumeric(Column1)
            end
           ) as REQUIREMENTNo,
           tbcust.CustomerRequirementNumber as RequirementSNo,
           (case
                when len(Column7) > 100 then
                    dbo.stripAlphahtestingMfr(SUBSTRING(Column7, 0, 100))
                else
                    dbo.stripAlphahtestingMfr(Column7)
            end
           ) as ManufacturerName,
           (case
                when len(Column4) > 30 then
                    dbo.stripAlphahnumeric(SUBSTRING(Column4, 0, 30))
                else
                    dbo.stripAlphahnumeric(Column4)
            end
           ) as Part,
           FLOOR(dbo.stripNumeric(Column10)) as Quantity,
           (case
                when len(Column3) > 128 then
                    dbo.stripAlphahnumeric(SUBSTRING(Column3, 0, 128))
                else
                    dbo.stripAlphahnumeric(Column3)
            end
           ) as SupplierName,                              
           (case
                when len(Column8) > 30 then
                    dbo.stripAlphahnumeric2(SUBSTRING(Column8, 0, 30))
                else
                    dbo.stripAlphahnumeric2(Column8)
            end
           ) as DateCode,
           cast(dbo.stripNumeric(Column5) as FLOAT) as SupplierCost,
           (case
                when len(Column17) > 3 then
                    dbo.stripAlphahnumeric(SUBSTRING(Column17, 0, 3))
                else
                    dbo.stripAlphahnumeric(Column17)
            end
           ) as CurrencyCode,
                       
           1 as isValid,
           cast('' as varchar(max)) as ValidationMessage,
           [dbo].ufn_get_supplier_part_status(dbo.ufn_get_fullpart(Column4), tbcust.FullPart) as PartStatus,
           ROW_NUMBER() OVER (partition by Column1, Column7, Column4, Column3 order by Column1) AS RowID
    into #tbPriceQuoteImport_tempData
    from BorisGlobalTraderImports.dbo.tbPriceQuoteImport_tempData tbtemp WITH (NOLOCK)
        left join dbo.tbCustomerRequirement tbcust WITH (NOLOCK)
            on tbcust.CustomerRequirementNumber = tbtemp.Column1
			and tbcust.ClientNo = tbtemp.Column2
			and tbcust.BOMNo IS NOT NULL
    where tbtemp.ClientId = @ClientId
          and tbtemp.SelectedClientId = @SelectedClientId
          and tbtemp.CreatedBy = @UserId

    /*********** Updation Validation Query **************/

    --CurrencyCode No length 3  & mandotary--                                                            
    update TmpR
    set TmpR.ValidationMessage = case                                                             
                                     when len(TRl.Column17) < 3 then
                                         ISNULL(ValidationMessage, '') + 'Currency Code  accepts 3 characters.'
                                         + '<br/>'
                                     else
                                         TmpR.ValidationMessage
                                 end
    from #tbPriceQuoteImport_tempData TmpR
        inner Join BorisGlobalTraderImports.dbo.tbPriceQuoteImport_tempData TRl WITH (NOLOCK)
            on Trl.PriceQuoteImportId = TmpR.PriceQuoteImportId

    --Requirement No length 10  & mandotary--                                                            
    update TmpR
    set TmpR.ValidationMessage = case
                                     when isnull(TRl.Column1, '') = '' then
                                         ISNULL(ValidationMessage, '') + 'Requirement is mandotary.' + '<br/>'
                                     when isnull(TmpR.RequirementSNo, 0) = 0 then
                                         ISNULL(ValidationMessage, '')
                                         + 'Requirement does not Exist or HUBRFQ does not exist for this Requirement'
                                         + '<br/>'                                                           
                                     else
                                         TmpR.ValidationMessage
                                 end
    from #tbPriceQuoteImport_tempData TmpR
        inner Join BorisGlobalTraderImports.dbo.tbPriceQuoteImport_tempData TRl
            on Trl.PriceQuoteImportId = TmpR.PriceQuoteImportId

    --Part length 30  & mandotary--                                                            
    update TmpR
    SET TmpR.ValidationMessage = CASE
           
                                     WHEN len(TRl.Column4) > 30 THEN
                                         ISNULL(ValidationMessage, '') + 'Supplier Part No only accepts 30 characters '
                                         + '<br/>'      
                                     ELSE
                                         TmpR.ValidationMessage
                                 END
    FROM #tbPriceQuoteImport_tempData TmpR
        inner Join BorisGlobalTraderImports.dbo.tbPriceQuoteImport_tempData TRl
            on Trl.PriceQuoteImportId = TmpR.PriceQuoteImportId

    --- DateCode length 5--                                                            
    update TmpR
    set TmpR.ValidationMessage = case
                                     when len(TRl.Column8) > 5
                                          and TmpR.DateCode <> TRl.Column8 then
                                         ISNULL(ValidationMessage, '')
                                         + 'DateCode Field only accepts 5 characters and AlphaNumeric values.'
                                         + '<br/>'                                                             
                                     when len(TRl.Column8) > 5 then
                                         ISNULL(ValidationMessage, '') + 'DateCode Field only accepts 5 characters.'
                                         + '<br/>'
                                     else
                                         TmpR.ValidationMessage
                                 end
    from #tbPriceQuoteImport_tempData TmpR
        inner Join BorisGlobalTraderImports.dbo.tbPriceQuoteImport_tempData TRl
            on Trl.PriceQuoteImportId = TmpR.PriceQuoteImportId

	-- Check suppliername existed in GT client
	update TmpR
	set TmpR.ValidationMessage = ISNULL(TmpR.ValidationMessage, '') + 'Supplier name is not existed.<br/>'
	from #tbPriceQuoteImport_tempData TmpR
		inner Join BorisGlobalTraderImports.dbo.tbPriceQuoteImport_tempData TRl
            on Trl.PriceQuoteImportId = TmpR.PriceQuoteImportId
	where not exists (
		select 1 from dbo.tbCompany co with (nolock)
		jOIN dbo.tbCompanyAddress ca with (nolock)
                 ON co.CompanyId = ca.CompanyNo AND ca.DefaultBilling = 1
		where co.Inactive = 0 
			AND co.POApproved = 1
			AND ca.CeaseDate IS NULL
			AND co.CompanyName = TRl.Column3
	)

    update #tbPriceQuoteImport_tempData
    set ValidationMessage = isnull(ValidationMessage, '') + ' Requirment part does not match with supplier part.'
    where PartStatus = 'N'

    --update #tbPriceQuoteImport_tempData
    --set ValidationMessage = isnull(ValidationMessage, '')
    --                        + ' Duplicate record with same RequirementId, Part, Manufacturer and Supplier.'
    --where rowid > 1
 
    /*********** Updation Validation Query  ENDs **************/
    UPDATE #tbPriceQuoteImport_tempData
    SET isvalid = 0
    WHERE ISNULL(ValidationMessage, '') <> ''

    /*********** Select Final Query  **************/
    SELECT ROW_NUMBER() OVER (ORDER BY RL.[PriceQuoteImportId] ASC) AS SNo,
           RL.[column1]  AS 'REQUIREMENT',
           RL.[column2]  AS 'CLIENT NO',
           RL.[Column3]  AS 'SUPPLIER NAME',
           RL.[Column4]  AS 'SUPPLIER PART',
           RL.[Column5]  AS 'SUPPLIER COST',
           RL.[column6]  AS 'ROHS',
           RL.[Column7]  AS 'MANUFACTURER',
           RL.[Column8]  AS 'DateCode',
           RL.[Column6]  AS 'PACKAGE',
           RL.[Column10] AS 'OFFERED QUANTITY',
           RL.[Column11] AS 'OFFER STATUS',
           RL.[Column12] AS 'SPQ',
           RL.[Column12] AS 'FACTORY SEALED',
           RL.[Column14] AS 'QTY IN STOCK',
           RL.[Column15] AS 'MOQ',
           RL.[Column16] AS 'LAST TIME BUY',
           RL.[Column20] AS 'CURRENCY',
           RL.[Column20] AS 'BUY PRICE',
           RL.[Column20] AS 'SELL PRICE',
           RL.[Column20] AS 'SHIPPING COST',
           RL.[Column21] AS 'LEAD TIME',
           RL.[Column22] AS 'REGION',
           RL.[Column23] AS 'Delivery Date',
           RL.[Column24] AS 'NOTES',
           RL.[Column25] AS 'MSL',
           IIF(TL.ValidationMessage = '', '', '<b>' + TL.ValidationMessage + '<b/>') as 'Reason for Excel Upload failed',
           RL.OriginalFilename
    from #tbPriceQuoteImport_tempData TL
        inner join BorisGlobalTraderImports.dbo.tbPriceQuoteImport_tempData RL
            on RL.PriceQuoteImportId = TL.PriceQuoteImportId
    where isValid <> 1
END
GO


