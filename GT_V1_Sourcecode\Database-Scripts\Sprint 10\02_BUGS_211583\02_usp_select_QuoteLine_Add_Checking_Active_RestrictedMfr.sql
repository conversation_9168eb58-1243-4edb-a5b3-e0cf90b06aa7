﻿SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE OR ALTER PROCEDURE [dbo].[usp_select_QuoteLine]              
--              
@QuoteLineId int              
--              
AS              
--      
/*  
 Marker  Date   Owner   Remarks  
 [001]  06-09-2023  Ravi   RP-2230 (create new SO Line using existing quote and based on Quote, get the AS6081)  
*/  
B<PERSON><PERSON>              
declare @IsIPOCreated int,@SourcingResultNo int              
DECLARE @IsRestManufaturer bit              
  set @IsRestManufaturer =0              
  declare @ManuFacNo int              
  declare @ClientNo int          
  --for Stock detials display        
  Declare @SOLineNo int=0                    
Declare @WareHouseNo int=0           
Declare @StockNo int=0  
DECLARE @AS6081 bit; --[001]  
--for Stock detials display        
  select @ManuFacNo=ManufacturerNo,@ClientNo= b.ClientNo from tbQuoteLine a join tbQuote b on a.QuoteNo=b.QuoteId where QuoteLineId=@QuoteLineId              
  if exists(select * from tbRestrictedManufacturer where ManufacturerNo=@ManuFacNo and ClientNo=@ClientNo AND Inactive = 0)              
  begin              
     set @IsRestManufaturer =1              
  end              
              
              
select @SourcingResultNo = SourcingResultNo  
, @AS6081 = IsNull(AS6081,0) --[001]   
from tbQuoteLine where QuoteLineId = @QuoteLineId              
--for Stock detials display        
select top 1 @SOLineNo = salesorderlineid from tbsalesorderLine where SalesOrderLineId = (select top 1 SalesOrderLineId from  tbSalesOrderLine where QuoteLineNo =@QuoteLineId) and Posted=1                    
select top 1 @StockNo=sk.StockId, @WareHouseNo = sk.warehouseno from  dbo.tbAllocation al JOIN  dbo.tbStock sk ON sk.StockId = al.StockNo                     
where al.SalesOrderLineNo= @SOLineNo           
--for Stock detials display        
              
SELECT @IsIPOCreated = COUNT(1)                
 from tbInternalPurchaseOrderLine where SourcingResultNo = isnull(@SourcingResultNo,0)                 
              
SELECT *,              
case when @IsIPOCreated > 0 then 1 else 0 end as IsIPOCreated              
, dbo.ufn_get_productdutyrate(ProductNo,getdate()) as ProductDutyRate   , ps.Name as ProductSourceName              
, @IsRestManufaturer as IsRestManufaturer              
 ,cast(dbo.ufn_GetECCNMessage(q.ECCNCode,q.ClientNo)as nvarchar(900))as IHSECCNCodeDefination             
 --for Stock detials display        
 ,cast(dbo.ufn_GetStockAvailableDetail(q.Part,q.ClientNo,null)as nvarchar(900))as StockAvailableDetail                
 --for Stock detials display   
 , @AS6081 as AS6081 --[001]  
FROM  dbo.vwQuoteLine q left join tbProductSource ps on isnull(q.ProductSource,0) = ps.ProductSourceId              
              
              
WHERE QuoteLineId  = @QuoteLineId              
              
END 
