///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference name="~/Controls/IconButton/IconButton.js" />
///<reference name="~/Controls/ReboundTextBox/ReboundTextBox.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.ClientImportConfirmClose = function (element) {
    Rebound.GlobalTrader.Site.Controls.Forms.ClientImportConfirmClose.initializeBase(this, [element]);
    this._intBOMID = -1;
    //this._isAddButtonEnable = true;
};

Rebound.GlobalTrader.Site.Controls.Forms.ClientImportConfirmClose.prototype = {

    get_intBOMID: function() { return this._intBOMID; }, set_intBOMID: function(value) { if (this._intBOMID !== value) this._intBOMID = value; },
    initialize: function() {
        Rebound.GlobalTrader.Site.Controls.Forms.ClientImportConfirmClose.callBaseMethod(this, "initialize");
        this.addShown(Function.createDelegate(this, this.formShown));
    },

    dispose: function() {
        if (this.isDisposed) return;
        this._intBOMID = null;
        Rebound.GlobalTrader.Site.Controls.Forms.ClientImportConfirmClose.callBaseMethod(this, "dispose");
    },

    formShown: function() {
        if (this._blnFirstTimeShown) {
            this._ctlConfirm = this.getFieldComponent("ctlConfirmClose");
            this._ctlConfirm.addClickYesEvent(Function.createDelegate(this, this.yesClicked));
            this._ctlConfirm.addClickNoEvent(Function.createDelegate(this, this.noClicked));
        }
    },

    yesClicked: function() {
    
        this.showSaving(true);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("Controls/Nuggets/ClientImportBOMMainInfo");
        obj.set_DataObject("ClientImportBOMMainInfo");
        obj.set_DataAction("updateBOMStatusToClosed");
        obj.addParameter("id", this._intBOMID);
        obj.addDataOK(Function.createDelegate(this, this.saveConfirmComplete));
        obj.addError(Function.createDelegate(this, this.saveError));
        obj.addTimeout(Function.createDelegate(this, this.saveError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
        this.onSave();
    },

    noClicked: function() {
        this.showSaving(false);
        this.onNotConfirmed();
    },

    saveError: function(args) {
        this.showSaving(false);
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    saveConfirmComplete: function(args) {
        if (args._result.Result == true) {            
            this.showSavedOK(true);
            this.onSaveComplete();
        } else {
            this._strErrorMessage = args._errorMessage;
            this.onSaveError();
        }
    }

};

Rebound.GlobalTrader.Site.Controls.Forms.ClientImportConfirmClose.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.ClientImportConfirmClose", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
