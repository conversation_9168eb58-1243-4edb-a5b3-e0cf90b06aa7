using System;
using System.ComponentModel;
using System.Globalization;
using System.Reflection;

namespace Rebound.GlobalTrader.Site.Enumerations {
	public class ExtendableEnum {

		public int Value;
		public string Name;

		public override int GetHashCode() {
			return Value.GetHashCode();
		}

		public override bool Equals(object obj) {
			if (object.ReferenceEquals(obj, null)) return false;
			ExtendableEnum enm = obj as ExtendableEnum;
			if (!object.ReferenceEquals(enm, null)) {
				return enm.Value == Value;
			} else {
				return false;
			}
		}

		public static object Parse(Type typType, int intValue) {
			foreach (FieldInfo fieldInfo in typType.GetFields(BindingFlags.FlattenHierarchy | BindingFlags.Static | BindingFlags.Public)) {
				ExtendableEnum enumValue = fieldInfo.GetValue(null) as ExtendableEnum;
				if (enumValue != null) {
					if (enumValue.Value == intValue) return enumValue;
				}
			}
			throw new ArgumentException(string.Format("{0} is not defined in {1}", intValue, typType.Name));
		}

		public static object Parse(Type typType, string strValue) {
			return Parse(typType, strValue, false);
		}

		public static object Parse(Type typType, string strValue, bool blnIgnoreCase) {
			if (string.IsNullOrEmpty(strValue)) throw new ArgumentNullException("value was either null or empty");
			foreach (FieldInfo field in typType.GetFields(BindingFlags.Public | BindingFlags.Static | BindingFlags.FlattenHierarchy)) {
				bool blnIsMatch = false;
				if (blnIgnoreCase) {
					blnIsMatch = StringComparer.InvariantCultureIgnoreCase.Compare(field.Name, strValue) == 0;
				} else {
					blnIsMatch = StringComparer.InvariantCulture.Compare(field.Name, strValue) == 0;
				}
				if (blnIsMatch) {
					ExtendableEnum temp = field.GetValue(null) as ExtendableEnum;
					if (temp == null) throw new InvalidOperationException(string.Format("{0} not convertable to {1}", typType, typeof(ExtendableEnum)));
					object instance = Activator.CreateInstance(typType, new object[] { temp.Value, temp.Name });
					return instance;
				}
			}
			throw new ArgumentException(string.Format("{0} is not defined in {1}", strValue, typType.Name));
		}

		public static bool IsDefined(Type typType, object objValue) {
			if (object.ReferenceEquals(objValue, null)) throw new ArgumentNullException("Value");
			if (typeof(ExtendableEnum).IsAssignableFrom(typType)) {
				return IsDefined(typType, ((ExtendableEnum)objValue).Value);
			}
			return false;
		}

		public static bool IsDefined(Type typType, int intValue) {
			foreach (FieldInfo staticField in typType.GetFields(BindingFlags.Static | BindingFlags.Public | BindingFlags.FlattenHierarchy)) {
				object temp = staticField.GetValue(null);
				if (temp == null) continue;
				if (typeof(ExtendableEnum).IsAssignableFrom(typType)) {
					if (((ExtendableEnum)temp).Value == intValue) return true;
				}
			}
			return false;
		}

		public static bool IsDefined(Type typType, string strValue) {
			return IsDefined(typType, strValue, false);
		}

		public static bool IsDefined(Type typType, string strValue, bool blnIgnoreCase) {
			foreach (FieldInfo staticField in typType.GetFields(BindingFlags.Static | BindingFlags.Public | BindingFlags.FlattenHierarchy)) {
				bool blnIsMatch = false;
				if (blnIgnoreCase) {
					blnIsMatch = StringComparer.InvariantCultureIgnoreCase.Compare(staticField.Name, strValue) == 0;
				} else {
					blnIsMatch = StringComparer.InvariantCulture.Compare(staticField.Name, strValue) == 0;
				}
				if (blnIsMatch) return true;
			}
			return false;
		}

		public static explicit operator ExtendableEnum(int intValue) {
			foreach (FieldInfo staticField in typeof(ExtendableEnum).GetFields(BindingFlags.Public | BindingFlags.Static | BindingFlags.FlattenHierarchy)) {
				ExtendableEnum temp = staticField.GetValue(null) as ExtendableEnum;
				if (temp == null) continue;
				if (temp.Value == intValue) return temp;
			}
			return null;
		}

		public static explicit operator int(ExtendableEnum enm) {
			return enm.Value;
		}

		public static implicit operator ExtendableEnum(string strValue) {
			if (string.IsNullOrEmpty(strValue)) return null;
			foreach (FieldInfo field in typeof(ExtendableEnum).GetFields(BindingFlags.Public | BindingFlags.Static | BindingFlags.FlattenHierarchy)) {
				if (StringComparer.InvariantCultureIgnoreCase.Compare(field.Name, strValue) == 0) {
					ExtendableEnum temp = field.GetValue(null) as ExtendableEnum;
					if (temp == null) throw new InvalidOperationException(string.Format("{0} not convertable to {1}", strValue, typeof(ExtendableEnum)));
					ExtendableEnum instance = new ExtendableEnum(temp.Value, temp.Name);
					return instance;
				}
			}
			return null;
		}

		public static bool operator ==(ExtendableEnum a, ExtendableEnum b) {
			return a.Equals(b);
		}

		public static bool operator !=(ExtendableEnum a, ExtendableEnum b) {
			return !a.Equals(b);
		}

		protected ExtendableEnum(int intValue, string strName) {
			this.Value = intValue;
			this.Name = strName;
		}
	}
}




//
//
//
//

//[TypeConverter(typeof(FakeEnum.FakeEnumConverter))]
//public partial class FakeEnum {

//    // typical "Enum" declarations, sort of like One = 1, Two = 2, Three
//    public static readonly FakeEnum One = new FakeEnum(1, "One's Friendly Name");
//    public static readonly FakeEnum Two = new FakeEnum(2, "Two's Friendly Name");
//    public static readonly FakeEnum Three = new FakeEnum(3, "Three's Friendly Name");
//    public static readonly FakeEnum Four = new FakeEnum(4);
//    public static readonly FakeEnum Five = new FakeEnum(5);
//    public static readonly FakeEnum Six = new FakeEnum(6);


//    // implementation to provide "Enum" like functionality
//    int value;
//    string friendlyName;

//    public string FriendlyName { get { return friendlyName; } }

//    public override string ToString() {
//        return ToString("");
//    }

//    public virtual string ToString(string format) {
//        foreach(FieldInfo staticField in GetType().GetFields(BindingFlags.Static | BindingFlags.Public | BindingFlags.FlattenHierarchy)) {
//            FakeEnum temp = staticField.GetValue(null) as FakeEnum;
//            if(temp == null) continue;
//            if(temp.value == value) {
//                switch(format) {
//                    case "fn": {
//                        return temp.friendlyName;
//                    }
//                    default: {
//                        return staticField.Name;
//                    }
//                }
//            }
//        }
//        return base.ToString();
//    }

//    public override int GetHashCode() {
//        return value.GetHashCode();
//    }

//    public override bool Equals(object obj) {
//        if(object.ReferenceEquals(obj, null)) return false;
//        FakeEnum temp = obj as FakeEnum;
//        if(!object.ReferenceEquals(temp, null)) {
//            return temp.value == value;
//        } else {
//            return false;
//        }
//    }

//    public static object Parse(Type type, int value) {
//        foreach(FieldInfo fieldInfo in type.GetFields(BindingFlags.FlattenHierarchy | BindingFlags.Static | BindingFlags.Public)) {
//            FakeEnum enumValue = fieldInfo.GetValue(null) as FakeEnum;
//            if(enumValue != null) {
//                if(enumValue.value == value) {
//                    return enumValue;
//                }
//            }
//        }
//        throw new ArgumentException(string.Format("{0} is not defined in {1}", value, type.Name));
//    }

//    public static object Parse(Type type, string value) {
//        return Parse(type, value, false);
//    }

//    public static object Parse(Type type, string value, bool ignoreCase) {
//        if(string.IsNullOrEmpty(value)) throw new ArgumentNullException("value was either null or empty");
//        foreach(FieldInfo field in type.GetFields(BindingFlags.Public | BindingFlags.Static | BindingFlags.FlattenHierarchy)) {
//            bool isMatch = false;
//            if(ignoreCase) {
//                isMatch = StringComparer.InvariantCultureIgnoreCase.Compare(field.Name, value) == 0;
//            } else {
//                isMatch = StringComparer.InvariantCulture.Compare(field.Name, value) == 0;
//            }
//            if(isMatch) {
//                FakeEnum temp = field.GetValue(null) as FakeEnum;
//                if(temp == null) throw new InvalidOperationException(string.Format("{0} not convertable to {1}", type, typeof(FakeEnum)));
//                object instance = Activator.CreateInstance(type, new object[] { temp.value, temp.friendlyName });
//                return instance;
//            }
//        }
//        throw new ArgumentException(string.Format("{0} is not defined in {1}", value, type.Name));
//    }

//    public static bool IsDefined(Type type, object value) {
//        if(object.ReferenceEquals(value, null)) throw new ArgumentNullException("value");
//        if(typeof(FakeEnum).IsAssignableFrom(type)) {
//            return IsDefined(type, ((FakeEnum)value).value);
//        }
//        return false;
//    }

//    public static bool IsDefined(Type type, int value) {
//        foreach(FieldInfo staticField in type.GetFields(BindingFlags.Static | BindingFlags.Public | BindingFlags.FlattenHierarchy)) {
//            object temp = staticField.GetValue(null);
//            if(temp == null) continue;
//            if(typeof(FakeEnum).IsAssignableFrom(type)) {
//                if(((FakeEnum)temp).value == value) return true;
//            }
//        }
//        return false;
//    }

//    public static bool IsDefined(Type type, string value) {
//        return IsDefined(type, value, false);
//    }

//    public static bool IsDefined(Type type, string value, bool ignoreCase) {
//        foreach(FieldInfo staticField in type.GetFields(BindingFlags.Static | BindingFlags.Public | BindingFlags.FlattenHierarchy)) {
//            bool isMatch = false;
//            if(ignoreCase) {
//                isMatch = StringComparer.InvariantCultureIgnoreCase.Compare(staticField.Name, value) == 0;
//            } else {
//                isMatch = StringComparer.InvariantCulture.Compare(staticField.Name, value) == 0;
//            }
//            if(isMatch) return true;
//        }
//        return false;
//    }

//    public static explicit operator FakeEnum(int value) {
//        foreach(FieldInfo staticField in typeof(FakeEnum).GetFields(BindingFlags.Public | BindingFlags.Static | BindingFlags.FlattenHierarchy)) {
//            FakeEnum temp = staticField.GetValue(null) as FakeEnum;
//            if(temp == null) continue;
//            if(temp.value == value) return temp;
//        }
//        return null;
//    }

//    public static explicit operator int(FakeEnum fakeEnum) {
//        return fakeEnum.value;
//    }

//    public static implicit operator FakeEnum(string value) {
//        if(string.IsNullOrEmpty(value)) return null;
//        foreach(FieldInfo field in typeof(FakeEnum).GetFields(BindingFlags.Public | BindingFlags.Static | BindingFlags.FlattenHierarchy)) {
//            if(StringComparer.InvariantCultureIgnoreCase.Compare(field.Name, value) == 0) {
//                FakeEnum temp = field.GetValue(null) as FakeEnum;
//                if(temp == null) throw new InvalidOperationException(string.Format("{0} not convertable to {1}", value, typeof(FakeEnum)));
//                FakeEnum instance = new FakeEnum(temp.value, temp.friendlyName);
//                return instance;
//            }
//        }
//        return null;
//    }

//    public static bool operator == (FakeEnum a, FakeEnum b) { 
//        return a.Equals(b);
//    }

//    public static bool operator != (FakeEnum a, FakeEnum b) { 
//        return !a.Equals(b);
//    }

//    protected FakeEnum(int value) : this(value, null) { }

//    protected FakeEnum(int value, string friendlyName) {
//        this.value = value;
//        this.friendlyName = friendlyName;
//        if(string.IsNullOrEmpty(friendlyName)) {
//            this.friendlyName = ToString();
//        }
//    }

//    public class FakeEnumConverter : TypeConverter {
//        public override object ConvertFrom(ITypeDescriptorContext context, CultureInfo culture, object value) {
//            if(value is string) {
//                return (FakeEnum)((string)value);
//            }
//            return base.ConvertFrom(context, culture, value);
//        }
//    }
