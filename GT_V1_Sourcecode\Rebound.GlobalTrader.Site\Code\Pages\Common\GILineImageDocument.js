Type.registerNamespace("Rebound.GlobalTrader.Site.Pages");Rebound.GlobalTrader.Site.Pages.GILineImageDocument=function(n){Rebound.GlobalTrader.Site.Pages.GILineImageDocument.initializeBase(this,[n]);this._intIHSPartID=0};Rebound.GlobalTrader.Site.Pages.GILineImageDocument.prototype={get_ctlPageTitle:function(){return this._ctlPageTitle},set_ctlPageTitle:function(n){this._ctlPageTitle!==n&&(this._ctlPageTitle=n)},get_ctlMainInfo:function(){return this._ctlMainInfo},set_ctlMainInfo:function(n){this._ctlMainInfo!==n&&(this._ctlMainInfo=n)},get_ctlGILineImageDragDrop:function(){return this._ctlGILineImageDragDrop},set_ctlGILineImageDragDrop:function(n){this._ctlGILineImageDragDrop!==n&&(this._ctlGILineImageDragDrop=n)},initialize:function(){Rebound.GlobalTrader.Site.Pages.GILineImageDocument.callBaseMethod(this,"initialize")},goInit:function(){this._ctlMainInfo&&this._ctlMainInfo.addSaveEditComplete(Function.createDelegate(this,this.ctlMainInfo_EditComplete));this._ctlGILineImageDragDrop&&this._ctlGILineImageDragDrop.getData();Rebound.GlobalTrader.Site.Pages.GILineImageDocument.callBaseMethod(this,"goInit")},dispose:function(){this.isDisposed||(this._ctlPageTitle&&this._ctlPageTitle.dispose(),this._ctlMainInfo&&this._ctlMainInfo.dispose(),this._ctlGILineImageDragDrop&&this._ctlGILineImageDragDrop.dispose(),this._intIHSPartID=null,this._ctlPageTitle=null,this._ctlMainInfo=null,this._ctlManufacturerPDF=null,Rebound.GlobalTrader.Site.Pages.GILineImageDocument.callBaseMethod(this,"dispose"))},ctlMainInfo_EditComplete:function(){}};Rebound.GlobalTrader.Site.Pages.GILineImageDocument.registerClass("Rebound.GlobalTrader.Site.Pages.GILineImageDocument",Rebound.GlobalTrader.Site.Pages.Content,Sys.IDisposable);