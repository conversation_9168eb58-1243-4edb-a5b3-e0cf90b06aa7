﻿//Marker     changed by      date         Remarks
//[001]      Arpit           03/03/2023   RP-257

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Rebound.GlobalTrader.DAL;
using System.Threading.Tasks;

namespace Rebound.GlobalTrader.BLL
{
    public partial class CSLEUSearch : BizObject
    {
        #region Properties

        public int CompanyId { get; set; }
        public string Type { get; set; }

        public string CompanyName { get; set; }
        public string Remark { get; set; }
        public string Nationalities { get; set; }
        public string BirthDate { get; set; }
        public string BirthDatePlace { get; set; }
        public string Address { get; set; }
        public string Program { get; set; }
        public string IDs { get; set; }

        #endregion


        //[001]
        public static List<CSLEUSearch> GetListCSLEU(string name, string type, string address, string country, bool isFuzzyName)
        {
            try
            {
                List<CSLEUSearchDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.CSLMatchingCompany.GetListCSLEU( name,  type,  address,  country,  isFuzzyName);
                if (lstDetails == null)
                {
                    return new List<CSLEUSearch>();
                }
                else
                {
                    List<CSLEUSearch> lst = new List<CSLEUSearch>();
                    foreach (CSLEUSearchDetails objDetails in lstDetails)
                    {
                        Rebound.GlobalTrader.BLL.CSLEUSearch obj = new Rebound.GlobalTrader.BLL.CSLEUSearch();
                        obj.CompanyId = objDetails.CompanyId;
                        obj.Type = objDetails.Type;
                        obj.Remark = objDetails.Remark;
                        obj.CompanyName = objDetails.CompanyName;
                        obj.Nationalities = objDetails.Nationalities;
                        obj.BirthDate = objDetails.BirthDate;
                        obj.BirthDatePlace = objDetails.BirthDatePlace;
                        obj.Address = objDetails.Address;
                        obj.Program = objDetails.Program;
                        obj.IDs = objDetails.IDs;
                        lst.Add(obj);
                        obj = null;
                    }
                    lstDetails = null;
                    return lst;
                }
            }
            catch(Exception ex)
            {
                throw new Exception(ex.Message, ex.InnerException);
            }
        }


    }
}