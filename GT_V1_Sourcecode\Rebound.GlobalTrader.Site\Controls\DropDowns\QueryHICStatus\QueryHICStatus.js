Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.QueryHICStatus=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.QueryHICStatus.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DropDowns.QueryHICStatus.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.DropDowns.QueryHICStatus.callBaseMethod(this,"initialize");this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK))},dispose:function(){this.isDisposed||Rebound.GlobalTrader.Site.Controls.DropDowns.QueryHICStatus.callBaseMethod(this,"dispose")},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/QueryHICStatus");this._objData.set_DataObject("QueryHICStatus");this._objData.set_DataAction("GetData")},dataCallOK:function(){var n=this._objData._result,t;if(n!=null&&n.Types)for(t=0;t<n.Types.length;t++)this.addOption(n.Types[t].Name,n.Types[t].ID)}};Rebound.GlobalTrader.Site.Controls.DropDowns.QueryHICStatus.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.QueryHICStatus",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);