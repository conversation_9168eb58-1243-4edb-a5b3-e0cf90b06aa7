Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.CreditLines_Add=function(n){Rebound.GlobalTrader.Site.Controls.Forms.CreditLines_Add.initializeBase(this,[n]);this._intCreditID=0;this._intLineID=0;this._intCustomerRMALineID=null;this._intInvoiceLineID=null;this._intServiceID=null;this._strCustomerName="";this._intCurrencyNo=0;this._strCurrencyCode="";this._dtmCreditDate="";this._dtmReferenceDate="";this._strInvoiceNumber="";this._strCRMANumber="";this._aryFirstTimeSearched=[];this._dblCurrencyRateToBase=1;this._isClientInvoice=!1;this._intClientInvoiveID=null;this._InvoiceNumber=0;this._intClientInvoiceLineID=0};Rebound.GlobalTrader.Site.Controls.Forms.CreditLines_Add.prototype={get_strCustomerName:function(){return this._strCustomerName},set_strCustomerName:function(n){this._strCustomerName!==n&&(this._strCustomerName=n)},get_intCreditID:function(){return this._intCreditID},set_intCreditID:function(n){this._intCreditID!==n&&(this._intCreditID=n)},get_ibtnContinue:function(){return this._ibtnContinue},set_ibtnContinue:function(n){this._ibtnContinue!==n&&(this._ibtnContinue=n)},get_ibtnContinue_Footer:function(){return this._ibtnContinue_Footer},set_ibtnContinue_Footer:function(n){this._ibtnContinue_Footer!==n&&(this._ibtnContinue_Footer=n)},get_radSelectSource:function(){return this._radSelectSource},set_radSelectSource:function(n){this._radSelectSource!==n&&(this._radSelectSource=n)},get_trSourceFromCustomerRMA:function(){return this._trSourceFromCustomerRMA},set_trSourceFromCustomerRMA:function(n){this._trSourceFromCustomerRMA!==n&&(this._trSourceFromCustomerRMA=n)},get_ctlSourceFromCustomerRMA:function(){return this._ctlSourceFromCustomerRMA},set_ctlSourceFromCustomerRMA:function(n){this._ctlSourceFromCustomerRMA!==n&&(this._ctlSourceFromCustomerRMA=n)},get_trSourceFromInvoice:function(){return this._trSourceFromInvoice},set_trSourceFromInvoice:function(n){this._trSourceFromInvoice!==n&&(this._trSourceFromInvoice=n)},get_ctlSourceFromInvoice:function(){return this._ctlSourceFromInvoice},set_ctlSourceFromInvoice:function(n){this._ctlSourceFromInvoice!==n&&(this._ctlSourceFromInvoice=n)},get_trSourceFromService:function(){return this._trSourceFromService},set_trSourceFromService:function(n){this._trSourceFromService!==n&&(this._trSourceFromService=n)},get_ctlSourceFromService:function(){return this._ctlSourceFromService},set_ctlSourceFromService:function(n){this._ctlSourceFromService!==n&&(this._ctlSourceFromService=n)},get_lblCurrency_Price:function(){return this._lblCurrency_Price},set_lblCurrency_Price:function(n){this._lblCurrency_Price!==n&&(this._lblCurrency_Price=n)},get_lblLandedCost:function(){return this._lblLandedCost},set_lblLandedCost:function(n){this._lblLandedCost!==n&&(this._lblLandedCost=n)},get_arySources:function(){return this._arySources},set_arySources:function(n){this._arySources!==n&&(this._arySources=n)},get_tblLines:function(){return this._tblLines},set_tblLines:function(n){this._tblLines!==n&&(this._tblLines=n)},get_trSourceFromClientInvoice:function(){return this._trSourceFromClientInvoice},set_trSourceFromClientInvoice:function(n){this._trSourceFromClientInvoice!==n&&(this._trSourceFromClientInvoice=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.CreditLines_Add.callBaseMethod(this,"initialize");this._tblLines.addSelectedIndexChanged(Function.createDelegate(this,this.tbl_SelectedIndexChanged));this.addShown(Function.createDelegate(this,this.formShown))},dispose:function(){this.isDisposed||(this._ctlMultiStep&&this._ctlMultiStep.dispose(),this._ctlSourceFromInvoice&&this._ctlSourceFromInvoice.dispose(),this._ctlSourceFromCustomerRMA&&this._ctlSourceFromCustomerRMA.dispose(),this._ctlSourceFromService&&this._ctlSourceFromService.dispose(),this._tblLines&&this._tblLines.dispose(),this._ibtnContinue&&$R_IBTN.clearHandlers(this._ibtnContinue),this._ibtnContinue_Footer&&$R_IBTN.clearHandlers(this._ibtnContinue_Footer),this.getFieldControl("ctlLandedCost")&&$clearHandlers(this.getFieldControl("ctlLandedCost")),this._radSelectSource=null,this._ibtnContinue=null,this._ibtnContinue_Footer=null,this._trSourceFromCustomerRMA=null,this._ctlSourceFromCustomerRMA=null,this._trSourceFromService=null,this._ctlSourceFromService=null,this._trSourceFromInvoice=null,this._ctlSourceFromInvoice=null,this._lblCurrency_Price=null,this._lblLandedCost=null,this._aryFirstTimeSearched=null,this._intCreditID=null,this._intLineID=null,this._intCustomerRMALineID=null,this._intInvoiceLineID=null,this._intServiceID=null,this._strCustomerName=null,this._intCurrencyNo=null,this._strCurrencyCode=null,this._dtmCreditDate=null,this._dtmReferenceDate=null,this._strInvoiceNumber=null,this._strCRMANumber=null,this._dblCurrencyRateToBase=null,this._trSourceFromClientInvoice=null,this._isClientInvoice=null,this._ClInvoice=null,this._tblLines=null,this._intClientInvoiveID=null,this._intClientInvoiceLineID=null,Rebound.GlobalTrader.Site.Controls.Forms.CreditLines_Add.callBaseMethod(this,"dispose"))},formShown:function(){if(this._blnFirstTimeShown){this.addSave(Function.createDelegate(this,this.saveClicked));var n=Function.createDelegate(this,this.continueClicked);$R_IBTN.addClick(this._ibtnContinue,n);$R_IBTN.addClick(this._ibtnContinue_Footer,n);this._ctlMultiStep.addStepChanged(Function.createDelegate(this,this.stepChanged));this._ctlSourceFromCustomerRMA.addItemSelected(Function.createDelegate(this,this.selectCustomerRMALineItem));this._ctlSourceFromInvoice.addItemSelected(Function.createDelegate(this,this.selectInvoiceLineItem));this._ctlSourceFromService.addItemSelected(Function.createDelegate(this,this.selectServiceItem));$addHandler(this.getFieldControl("ctlLandedCost"),"change",Function.createDelegate(this,this.landedCostChanged));this._strPathToData="controls/Nuggets/CreditLines";this._strDataObject="CreditLines"}this.resetSteps();this.setFormFieldsToDefaults()},tbl_SelectedIndexChanged:function(){this._intInvoiceLineID=this._tblLines._varSelectedValue;this.continueClicked();this.fillData_ClientInvoiceLine();$R_IBTN.enableButton(this._ibtnSave,!0);$R_IBTN.enableButton(this._ibtnSave_Footer,!0);this.showField("ctlService",!1);this.showField("ctlCustomer",!1)},ClientInvoice:function(){Boolean.parse(this._isClientInvoice)==!0&&(this._ClInvoice="CLIENTINVOICE",this.GetClientCredit(),this.continueClicked())},landedCostChanged:function(){this.setLandedCostInCurrency($R_FN.formatCurrency(this.getFieldValue("ctlLandedCost")*this._dblCurrencyRateToBase,this._strCurrencyCode))},setLandedCostInCurrency:function(n){$R_FN.setInnerHTML(this._lblLandedCost,String.format("({0})",n))},setFieldsFromHeader:function(n,t,i,r,u,f,e,o,s,h,c){$R_FN.setInnerHTML(this._lblCurrency_Price,r);this.setFieldValue("ctlCredit",n);this.setFieldValue("ctlCustomer",t);this._strCustomerName=t;this._intCurrencyNo=i;this._strCurrencyCode=r;this._dtmCreditDate=u;this._dtmReferenceDate=o;this._strInvoiceNumber=f;this._strCRMANumber=e;this._isClientInvoice=s;this._InvoiceNumber=h;this._intClientInvoiceLineID=c},doInitialSearch:function(){switch(this._strSourceSelected){case"CRMA":this._aryFirstTimeSearched[0]||this.initialCRMASearch();this._aryFirstTimeSearched[0]=!0;break;case"INVOICE":this._aryFirstTimeSearched[1]||this.initialInvoiceSearch();this._aryFirstTimeSearched[1]=!0;break;case"SERVICE":this._aryFirstTimeSearched[2]||this.initialServiceSearch();this._aryFirstTimeSearched[2]=!0;break;case"CLIENTINVOICE":this._aryFirstTimeSearched[3]||this.initialClientInvoiceSearch();this._aryFirstTimeSearched[3]=!0}},initialCRMASearch:function(){$R_FN.showElement(this._trSourceFromClientInvoice,!1);this._ctlSourceFromCustomerRMA.setFieldValue("ctlCompany",this._strCustomerName);this._strCRMANumber&&this._ctlSourceFromCustomerRMA.setFieldValue("ctlCRMANo",this._strCRMANumber);this._ctlSourceFromCustomerRMA.getData()},initialInvoiceSearch:function(){$R_FN.showElement(this._trSourceFromClientInvoice,!1);this._ctlSourceFromInvoice.setFieldValue("ctlCompany",this._strCustomerName);this._strInvoiceNumber&&this._ctlSourceFromInvoice.setFieldValue("ctlInvoiceNo",this._strInvoiceNumber);this._ctlSourceFromInvoice.getData()},initialClientInvoiceSearch:function(){},initialServiceSearch:function(){$R_FN.showElement(this._trSourceFromClientInvoice,!1);this._ctlSourceFromService.setFieldValue("ctlName","%");this._ctlSourceFromService.getData()},continueClicked:function(){this.nextStep()},findWhichTypeSelected:function(){for(var t,n=0;n<this._arySources.length;n++)if(t=$get(String.format("{0}_{1}",this._radSelectSource.id,n)),t.checked)return this._arySources[n]},stepChanged:function(){var n,t;this._strSourceSelected=this.findWhichTypeSelected();n=this._ctlMultiStep._intCurrentStep;Boolean.parse(this._isClientInvoice)==!0&&this._strSourceSelected=="INVOICE"&&(this._strSourceSelected="CLIENTINVOICE");t=n==1||n==1&&this._strSourceSelected!="NEW";$R_IBTN.showButton(this._ibtnContinue,t);$R_IBTN.showButton(this._ibtnContinue_Footer,t);$R_IBTN.enableButton(this._ibtnSave,n==3);$R_IBTN.enableButton(this._ibtnSave_Footer,n==3);n==2&&(this._strSourceSelected=="CRMA"&&this._ctlSourceFromCustomerRMA.resizeColumns(),$R_FN.showElement(this._trSourceFromCustomerRMA,this._strSourceSelected=="CRMA"),this._strSourceSelected!="INVOICE"||Boolean.parse(this._isClientInvoice)||this._ctlSourceFromInvoice.resizeColumns(),$R_FN.showElement(this._trSourceFromInvoice,this._strSourceSelected=="INVOICE"&&!Boolean.parse(this._isClientInvoice)),this._strSourceSelected=="CLIENTINVOICE"&&Boolean.parse(this._isClientInvoice)&&(this._ClInvoice="CLIENTINVOICE",$R_FN.showElement(this._trSourceFromClientInvoice,this._strSourceSelected=="CLIENTINVOICE"&&Boolean.parse(this._isClientInvoice)),this.GetClientCredit()),this._strSourceSelected=="SERVICE"&&this._ctlSourceFromService.resizeColumns(),$R_FN.showElement(this._trSourceFromService,this._strSourceSelected=="SERVICE"),this.doInitialSearch());n==3&&(this.showField("ctlService",this._strSourceSelected=="SERVICE"),this.showField("ctlServiceDescription",this._strSourceSelected=="SERVICE"),this.showField("ctlPartNo",this._strSourceSelected!="SERVICE"),this.showField("ctlProduct",this._strSourceSelected!="SERVICE"),this.showField("ctlPackage",this._strSourceSelected!="SERVICE"),this.showField("ctlROHSStatus",this._strSourceSelected!="SERVICE"),this.showField("ctlManufacturer",this._strSourceSelected!="SERVICE"),this.showField("ctlCustomerPart",this._strSourceSelected!="SERVICE"),this.showField("ctlDateCode",this._strSourceSelected!="SERVICE"),this.fillData())},getDropDownsData:function(){this.getFieldDropDownData("ctlROHSStatus")},selectCustomerRMALineItem:function(){this._intCustomerRMALineID=this._ctlSourceFromCustomerRMA.getSelectedID();this.continueClicked()},selectInvoiceLineItem:function(){this._intInvoiceLineID=this._ctlSourceFromInvoice.getSelectedID();this.continueClicked()},selectClientInvoiceLineItem:function(){this.continueClicked()},selectServiceItem:function(){this._intServiceID=this._ctlSourceFromService.getSelectedID();this.continueClicked()},fillData:function(){switch(this._strSourceSelected){case"CRMA":this.fillData_CustomerRMALine();break;case"INVOICE":this.fillData_InvoiceLine();break;case"SERVICE":this.fillData_Service()}},fillDataError:function(n){this.showFillDataFieldsLoading(!1);this._strErrorMessage=n._errorMessage;this.onSaveError()},GetClientCredit:function(){this.setFormFieldsToDefaults();this.showFillDataFieldsLoading(!0);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("GetClientInvoice");n.addParameter("id",this._InvoiceNumber);n.addParameter("LineNo",this._intClientInvoiceLineID);n.addDataOK(Function.createDelegate(this,this.GetClientCreditOK));n.addError(Function.createDelegate(this,this.fillDataError));n.addTimeout(Function.createDelegate(this,this.fillDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},GetClientCreditOK:function(n){var r=n._result,i,t,u;if(this._tblLines.clearTable(),r.Lines)for(i=0;i<r.Lines.length;i++)t=r.Lines[i],u=[t.Num,$R_FN.writePartNo(t.Part,t.ROHS),$R_FN.setCleanTextValue(t.Date),t.Price,t.Quantity,$R_FN.setCleanTextValue(t.GoodsInNumber),$R_FN.setCleanTextValue(t.InternalPurchaseOrderNumber),$R_FN.setCleanTextValue(t.DebitNumber)],this._tblLines.addRow(u,t.ID,!1)},fillData_CustomerRMALine:function(){this.setFormFieldsToDefaults();this.showFillDataFieldsLoading(!0);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("GetCRMALineForNew");n.addParameter("id",this._intCustomerRMALineID);n.addParameter("CreditCurrencyNo",this._intCurrencyNo);n.addParameter("CreditCurrencyCode",this._strCurrencyCode);n.addParameter("CreditDate",this._dtmReferenceDate);n.addDataOK(Function.createDelegate(this,this.fillData_CustomerRMALineOK));n.addError(Function.createDelegate(this,this.fillDataError));n.addTimeout(Function.createDelegate(this,this.fillDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},fillData_CustomerRMALineOK:function(n){var t=n._result;this.setFieldValue("ctlPartNo",t.Part);this.setFieldValue("ctlDateCode",t.DC);this.setFieldValue("ctlCustomerPart",t.CustomerPart);this.setFieldValue("ctlPrice",t.Price);this.setFieldValue("ctlQuantity",t.Quantity);this.setFieldValue("ctlManufacturer",t.ManufacturerNo,null,$R_FN.setCleanTextValue(t.Manufacturer));this.setFieldValue("ctlProduct",t.ProductNo,null,$R_FN.setCleanTextValue(t.ProductDescription));this.setFieldValue("ctlPackage",t.PackageNo,null,$R_FN.setCleanTextValue(t.PackageDescription));this.setFieldValue("ctlROHSStatus",t.ROHS);this.setFieldValue("ctlLandedCost",t.LandedCost);t.LandedCostConverted&&this.setLandedCostInCurrency(t.LandedCostConverted);this.showFillDataFieldsLoading(!1);this.getDropDownsData()},fillData_InvoiceLine:function(){this.setFormFieldsToDefaults();this.showFillDataFieldsLoading(!0);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("GetInvoiceLineForNew");n.addParameter("id",this._intInvoiceLineID);n.addParameter("CreditCurrencyNo",this._intCurrencyNo);n.addParameter("CreditCurrencyCode",this._strCurrencyCode);n.addParameter("CreditDate",this._dtmReferenceDate);n.addDataOK(Function.createDelegate(this,this.fillData_InvoiceLineOK));n.addError(Function.createDelegate(this,this.fillDataError));n.addTimeout(Function.createDelegate(this,this.fillDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},fillData_InvoiceLineOK:function(n){var t=n._result;this.setFieldValue("ctlPartNo",t.Part);this.setFieldValue("ctlDateCode",t.DC);this.setFieldValue("ctlCustomerPart",t.CustomerPart);this.setFieldValue("ctlPrice",t.Price);this.setFieldValue("ctlQuantity",t.Qty);this.setFieldValue("ctlManufacturer",t.ManufacturerNo,null,t.Manufacturer);this.setFieldValue("ctlProduct",t.ProductNo,null,$R_FN.setCleanTextValue(t.ProductDescription));this.setFieldValue("ctlPackage",t.PackageNo,null,$R_FN.setCleanTextValue(t.PackageDescription));this.setFieldValue("ctlROHSStatus",t.ROHS);this.setFieldValue("ctlLandedCost",t.LandedCost);this._dblCurrencyRateToBase=t.CurrencyRate;t.LandedCostConverted&&this.setLandedCostInCurrency(t.LandedCostConverted);this.showFillDataFieldsLoading(!1);this.getDropDownsData()},fillData_Service:function(){this.setFormFieldsToDefaults();this.showFillDataFieldsLoading(!0);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("GetServiceForNew");n.addParameter("id",this._intServiceID);n.addParameter("CreditCurrencyNo",this._intCurrencyNo);n.addParameter("CreditCurrencyCode",this._strCurrencyCode);n.addParameter("CreditDate",this._dtmReferenceDate);n.addDataOK(Function.createDelegate(this,this.fillData_ServiceOK));n.addError(Function.createDelegate(this,this.fillDataError));n.addTimeout(Function.createDelegate(this,this.fillDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},fillData_ServiceOK:function(n){var t=n._result;this.setFieldValue("ctlService",t.ServiceName);this.setFieldValue("ctlServiceDescription",t.ServiceDescription);this.setFieldValue("ctlPrice",t.Price);this.setFieldValue("ctlQuantity",1);this.setFieldValue("ctlLandedCost",t.Cost);t.CostConverted&&this.setLandedCostInCurrency(t.CostConverted);this._dblCurrencyRateToBase=t.CurrencyRate;this.showFillDataFieldsLoading(!1);this.getDropDownsData()},showFillDataFieldsLoading:function(n){this.showFieldLoading("ctlPartNo",n);this.showFieldLoading("ctlDateCode",n);this.showFieldLoading("ctlPrice",n);this.showFieldLoading("ctlQuantity",n);this.showFieldLoading("ctlManufacturer",n);this.showFieldLoading("ctlProduct",n);this.showFieldLoading("ctlPackage",n);this.showFieldLoading("ctlROHSStatus",n);this.showFieldLoading("ctlCustomerPart",n);this.showFieldLoading("ctlLandedCost",n)},fillData_ClientInvoiceLine:function(){this.setFormFieldsToDefaults();this.showFillDataFieldsLoading(!0);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("GetClientInvoiceLineForNew");n.addParameter("id",this._intInvoiceLineID);n.addParameter("CreditCurrencyNo",this._intCurrencyNo);n.addParameter("CreditCurrencyCode",this._strCurrencyCode);n.addParameter("CreditDate",this._dtmReferenceDate);n.addDataOK(Function.createDelegate(this,this.fillData_ClientInvoiceLineOK));n.addError(Function.createDelegate(this,this.fillDataError));n.addTimeout(Function.createDelegate(this,this.fillDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},fillData_ClientInvoiceLineOK:function(n){var t=n._result;this.setFieldValue("ctlPartNo",t.Part);this.setFieldValue("ctlDateCode",t.DateCode);this.setFieldValue("ctlCustomerPart",t.SupplierPart);this.setFieldValue("ctlPrice",t.Price);this.setFieldValue("ctlQuantity",t.QtyReceived);this.setFieldValue("ctlManufacturer",t.ManufacturerNo,null,t.ManufacturerName);this.setFieldValue("ctlProduct",t.ProductNo,null,$R_FN.setCleanTextValue(t.ProductDescription));this.setFieldValue("ctlPackage",t.PackageNo,null,$R_FN.setCleanTextValue(t.PackageDescription));this.setFieldValue("ctlROHSStatus",t.ROHS);this.setFieldValue("ctlLandedCost",t.Landedcost);this._dblCurrencyRateToBase=t.CurrencyRate;t.LandedCostConverted&&this.setLandedCostInCurrency(t.LandedCostConverted);this.showFillDataFieldsLoading(!1);this.getDropDownsData()},saveClicked:function(){this.resetFormFields();this.validateForm()&&this.saveEdit()},validateForm:function(){var n=!0;return this._strSourceSelected=="SERVICE"?this.checkFieldEntered("ctlService")||(n=!1):this.checkFieldEntered("ctlPartNo")||(n=!1),this.checkFieldEntered("ctlQuantity")||(n=!1),this.checkFieldNumeric("ctlQuantity")||(n=!1),this.checkFieldEntered("ctlPrice")||(n=!1),this.checkFieldNumeric("ctlPrice")||(n=!1),n||this.showError(!0),n},saveEdit:function(){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("AddNew");n.addParameter("id",this._intCreditID);n.addParameter("LineIsService",this._strSourceSelected=="SERVICE");this._strSourceSelected=="SERVICE"?(n.addParameter("ServiceNo",this._intServiceID),n.addParameter("Service",this.getFieldValue("ctlService")),n.addParameter("ServiceDescription",this.getFieldValue("ctlServiceDescription")),Boolean.parse(this._isClientInvoice)&&n.addParameter("ClientInvLineNo",this._intClientInvoiceLineID)):(this._ClInvoice=="CLIENTINVOICE"&&(n.addParameter("ClientInvoice","CLIENTINVOICE"),n.addParameter("ClientInvLineNo",this._intClientInvoiceLineID)),n.addParameter("InvoiceLineNo",this._intInvoiceLineID),n.addParameter("Part",this.getFieldValue("ctlPartNo")),n.addParameter("MfrNo",this.getFieldValue("ctlManufacturer")),n.addParameter("DateCode",this.getFieldValue("ctlDateCode")),n.addParameter("PackageNo",this.getFieldValue("ctlPackage")),n.addParameter("ProductNo",this.getFieldValue("ctlProduct")),n.addParameter("CustomerPart",this.getFieldValue("ctlCustomerPart")),n.addParameter("ROHS",this.getFieldValue("ctlROHSStatus")));n.addParameter("Quantity",this.getFieldValue("ctlQuantity"));n.addParameter("LandedCost",this.getFieldValue("ctlLandedCost"));n.addParameter("Price",this.getFieldValue("ctlPrice"));n.addParameter("CustomerRMALineNo",this._intCustomerRMALineID);n.addParameter("Taxable",this.getFieldValue("ctlTaxable"));n.addParameter("LineNotes",this.getFieldValue("ctlLineNotes"));n.addDataOK(Function.createDelegate(this,this.saveEditOK));n.addError(Function.createDelegate(this,this.saveEditError));n.addTimeout(Function.createDelegate(this,this.saveEditError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},saveEditError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},saveEditOK:function(n){n._result.Result==!0?(this._intLineID=n._result.NewID,this.onSaveComplete()):this.saveEditError(n)}};Rebound.GlobalTrader.Site.Controls.Forms.CreditLines_Add.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CreditLines_Add",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);