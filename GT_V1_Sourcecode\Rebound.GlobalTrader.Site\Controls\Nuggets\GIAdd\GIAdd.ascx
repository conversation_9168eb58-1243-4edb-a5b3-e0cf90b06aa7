<%@ Control Language="C#" CodeBehind="GIAdd.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Nuggets.GIAdd" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>

<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" BoxType="Standard">
	<Links>
		<ReboundUI:IconButton ID="ibtnAdd" runat="server" IconGroup="Nugget" IconTitleResource="Add" IconCSSType="Add" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
	</Links>
	
	<Content>
		<asp:PlaceHolder ID="plhAddNotAllowed" runat="server" Visible="false">
			<div class="addNotAllowed"><%=Functions.GetGlobalResource("Messages", "AddNotAllowed_GoodsIn")%></div>
		</asp:PlaceHolder>
	</Content>

	<Forms>
		<ReboundForm:GIAdd_Add ID="ctlAdd" runat="server" />
	</Forms>
</ReboundUI_Nugget:DesignBase>
