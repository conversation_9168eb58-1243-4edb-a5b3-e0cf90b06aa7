///<reference name="MicrosoftAjax.js" />
///<reference path="~/Common/Data/Data.js" />
///<reference path="~/Common/Functions/Functions.js" />
///<reference path="~/Controls/IconButton/IconButton.js" />
//----------------------------------------------------------------------------------------------------------------
// RP 06.01.2010:
// - dispose everything fully
//
// RP 14.10.2009:
// - added Lock / Unlock for saving state
//----------------------------------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls");

Rebound.GlobalTrader.Site.Controls.PagingButtons = function(element) { 
	Rebound.GlobalTrader.Site.Controls.PagingButtons.initializeBase(this, [element]);
	this._blnEnabled = true;
	this._blnStateLocked = true;
};
	
Rebound.GlobalTrader.Site.Controls.PagingButtons.prototype = {

	get_pnlLeft: function() { return this._pnlLeft; }, 	set_pnlLeft: function(value) { if (this._pnlLeft !== value)  this._pnlLeft = value; }, 
	get_lblTotalResults: function() { return this._lblTotalResults; }, 	set_lblTotalResults: function(value) { if (this._lblTotalResults !== value)  this._lblTotalResults = value; }, 
	get_lblCurrentPage: function() { return this._lblCurrentPage; }, 	set_lblCurrentPage: function(value) { if (this._lblCurrentPage !== value)  this._lblCurrentPage = value; }, 
	get_lblTotalPages: function() { return this._lblTotalPages; }, 	set_lblTotalPages: function(value) { if (this._lblTotalPages !== value)  this._lblTotalPages = value; }, 
	get_hypShowFilter: function() { return this._hypShowFilter; }, 	set_hypShowFilter: function(value) { if (this._hypShowFilter !== value)  this._hypShowFilter = value; }, 
	get_lblPageNumbers: function() { return this._lblPageNumbers; }, 	set_lblPageNumbers: function(value) { if (this._lblPageNumbers !== value)  this._lblPageNumbers = value; }, 
	get_hypPrev: function() { return this._hypPrev; }, 	set_hypPrev: function(value) { if (this._hypPrev !== value)  this._hypPrev = value; }, 
	get_lblPrevDisabled: function() { return this._lblPrevDisabled; }, 	set_lblPrevDisabled: function(value) { if (this._lblPrevDisabled !== value)  this._lblPrevDisabled = value; }, 
	get_hypNext: function() { return this._hypNext; }, 	set_hypNext: function(value) { if (this._hypNext !== value)  this._hypNext = value; }, 
	get_lblNextDisabled: function() { return this._lblNextDisabled; }, 	set_lblNextDisabled: function(value) { if (this._lblNextDisabled !== value)  this._lblNextDisabled = value; }, 
	get_intPagesToShowEitherSideOfCurrent: function() { return this._intPagesToShowEitherSideOfCurrent; }, 	set_intPagesToShowEitherSideOfCurrent: function(value) { if (this._intPagesToShowEitherSideOfCurrent !== value)  this._intPagesToShowEitherSideOfCurrent = value; }, 
	get_intPagesToShowAtEnds: function() { return this._intPagesToShowAtEnds; }, 	set_intPagesToShowAtEnds: function(value) { if (this._intPagesToShowAtEnds !== value)  this._intPagesToShowAtEnds = value; }, 
	get_strPagesDots: function() { return this._strPagesDots; }, 	set_strPagesDots: function(value) { if (this._strPagesDots !== value)  this._strPagesDots = value; }, 
	get_intCurrentPage: function() { return this._intCurrentPage; }, 	set_intCurrentPage: function(value) { if (this._intCurrentPage !== value)  this._intCurrentPage = value; }, 
	get_intTotalResults: function() { return this._intTotalResults; }, 	set_intTotalResults: function(value) { if (this._intTotalResults !== value)  this._intTotalResults = value; }, 
	get_intTotalPages: function() { return this._intTotalPages; }, 	set_intTotalPages: function(value) { if (this._intTotalPages !== value)  this._intTotalPages = value; }, 
	get_intCurrentPageSize: function() { return this._intCurrentPageSize; }, 	set_intCurrentPageSize: function(value) { if (this._intCurrentPageSize !== value)  this._intCurrentPageSize = value; }, 
	get_blnFiltersOn: function() { return this._blnFiltersOn; }, 	set_blnFiltersOn: function(value) { if (this._blnFiltersOn !== value)  this._blnFiltersOn = value; }, 
	get_lblPageSizeLinks: function() { return this._lblPageSizeLinks; }, 	set_lblPageSizeLinks: function(value) { if (this._lblPageSizeLinks !== value)  this._lblPageSizeLinks = value; }, 
	get_pnlLock: function() { return this._pnlLock; }, 	set_pnlLock: function(value) { if (this._pnlLock !== value)  this._pnlLock = value; }, 

	addPageSizeClickEvent: function(handler) { this.get_events().addHandler("pagesizeclick", handler); },
	removePageSizeClickEvent: function(handler) { this.get_events().removeHandler("pagesizeclick", handler); },
	onPageSizeClick: function() {
		var handler = this.get_events().getHandler("pagesizeclick");
		if (handler) handler(this, Sys.EventArgs.Empty);
	},
	
	addFilterStateChangeEvent: function(handler) { this.get_events().addHandler("FilterStateChange", handler); },
	removeFilterStateChangeEvent: function(handler) { this.get_events().removeHandler("FilterStateChange", handler); },
	onFilterStateChange: function() {
		var handler = this.get_events().getHandler("FilterStateChange");
		if (handler) handler(this, Sys.EventArgs.Empty);
	},
	
	addPageChangedEvent: function(handler) { this.get_events().addHandler("PageChanged", handler); },
	removePageChangedEvent: function(handler) { this.get_events().removeHandler("PageChanged", handler); },
	onPageChanged: function() {
		var handler = this.get_events().getHandler("PageChanged");
		if (handler) handler(this, Sys.EventArgs.Empty);
	},
	
	addStateLockChanged: function(handler) { this.get_events().addHandler("StateLockChanged", handler); },
	removeStateLockChanged: function(handler) { this.get_events().removeHandler("StateLockChanged", handler); },
	onStateLockChanged: function() {
		var handler = this.get_events().getHandler("StateLockChanged");
		if (handler) handler(this, Sys.EventArgs.Empty);
	},
	
	addShowLockLoading: function(handler) { this.get_events().addHandler("ShowLockLoading", handler); },
	removeShowLockLoading: function(handler) { this.get_events().removeHandler("ShowLockLoading", handler); },
	onShowLockLoading: function() {
		var handler = this.get_events().getHandler("ShowLockLoading");
		if (handler) handler(this, Sys.EventArgs.Empty);
	},
	
	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.PagingButtons.callBaseMethod(this, "initialize");
		if (this._hypShowFilter) $addHandler(this._hypShowFilter, "click", Function.createDelegate(this, this.toggleShowFilter));
		if (this._hypPrev) $addHandler(this._hypPrev, "click", Function.createDelegate(this, this.prevPage));
		if (this._hypNext) $addHandler(this._hypNext, "click", Function.createDelegate(this, this.nextPage));
		if (this._pnlLock) $addHandler(this._pnlLock, "click", Function.createDelegate(this, this.clickLock));
		this.setupPageSizeLinks();
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this.get_element()) $clearHandlers(this.get_element());
		if (this._hypShowFilter) $clearHandlers(this._hypShowFilter);
		if (this._hypPrev) $clearHandlers(this._hypPrev);
		if (this._hypNext) $clearHandlers(this._hypNext);
		if (this._pnlLock) $clearHandlers(this._pnlLock);
		this._pnlLeft = null;
		this._lblTotalResults = null;
		this._lblCurrentPage = null;
		this._lblTotalPages = null;
		this._hypShowFilter = null;
		this._lblPageNumbers = null;
		this._hypPrev = null;
		this._lblPrevDisabled = null;
		this._hypNext = null;
		this._lblNextDisabled = null;
		this._lblPageSizeLinks = null;
		this._pnlLock = null;
		this._blnEnabled = false;
		this._blnStateLocked = false;
		Rebound.GlobalTrader.Site.Controls.PagingButtons.callBaseMethod(this, "dispose");
		this.isDisposed = true;
	},
	
	show: function(bln) {
		$R_FN.showElement(this.get_element(), bln);
	},
		
	enable: function(bln) {
		this._blnEnabled = bln;
	},

	setupPageSizeLinks: function() {
		$R_FN.setInnerHTML(this._lblPageSizeLinks, "");
		var strPageSizes = this.addPageSizeLink(5, "5");
		strPageSizes += this.addPageSizeLink(10, "10");
		strPageSizes += this.addPageSizeLink(25, "25");
		strPageSizes += this.addPageSizeLink(50, "50");
		$R_FN.setInnerHTML(this._lblPageSizeLinks, strPageSizes);
	},

	addPageSizeLink: function(intSize, strText) {
		var strOut = "";
		var strCSSClass = (intSize == this._intCurrentPageSize) ? "pagingNo_Selected" : "pagingNo";
		var strOnClick = String.format("$find('{0}').pageSizeClick({1});", this.get_element().id, intSize);
		strOut = String.format('<a href="javascript:void(0);" class="{0}" onclick="{1}">{2}</a>', strCSSClass, strOnClick, strText);
		strCSSClass = null; strOnClick = null;
		return strOut;
	},
	
	pageSizeClick: function(intSize) {
		if (!this._blnEnabled) return;
		if (intSize == this._intCurrentPageSize) return;
		this._intCurrentPageSize = intSize;
		this.setupPageSizeLinks();
		this.onPageSizeClick();
	},
	
	toggleShowFilter: function() {
		this._blnFiltersOn = !this._blnFiltersOn;
		this.setFilter(this._blnFiltersOn);
		this.onFilterStateChange();
	},
		
	setFilter: function(blnOn) {
		this._blnFiltersOn = blnOn;
		if (this._hypShowFilter) this._hypShowFilter.className = (this._blnFiltersOn) ? "showFilterOn" : "showFilter";
	},	
	
	updatePageDisplay: function() {
		$R_FN.setInnerHTML(this._lblCurrentPage, (this._intTotalPages == 0) ? 0 : this._intCurrentPage);
		$R_FN.setInnerHTML(this._lblTotalPages, this._intTotalPages);
		$R_FN.setInnerHTML(this._lblTotalResults, this._intTotalResults);
		var strPages = "";
		if (this._intTotalPages > 1) {
			//booleans for working out when to show dots
			var blnUseDots = (this._intTotalPages <= (this._intPagesToShowAtEnds + (this._intPagesToShowEitherSideOfCurrent * 2)));
			var blnJustWrittenDots = false;

			//step through all pages and write a page number link
			for (var i = 0, l = this._intTotalPages; i < l; i++) {
				var blnWriteNumber = blnUseDots || (((i + 1) > (this._intTotalPages - this._intPagesToShowAtEnds)) || (i < this._intPagesToShowAtEnds) || ((i + 1) >= (this._intCurrentPage - this._intPagesToShowEitherSideOfCurrent) && (i + 1) <= (this._intCurrentPage + this._intPagesToShowEitherSideOfCurrent)));
				if (blnWriteNumber) {
					var strCssClass = ((i + 1) == this._intCurrentPage) ? "pagingNo_Selected" : "pagingNo";
					var strOnClick = String.format("$find('{0}').changePage({1});", this.get_element().id, (i+1));
					strPages += String.format('<a href="javascript:void(0);" class="{0}" onclick="{1}">{2}</a>', strCssClass, strOnClick, (i + 1));
					blnJustWrittenDots = false;
					strCssClass = null; strOnClick = null;
				} else {
					if (!blnJustWrittenDots) strPages += "...";
					blnJustWrittenDots = true;
				}
			}
		}
		$R_FN.setInnerHTML(this._lblPageNumbers, strPages);
		
		//next and prev page links
		var blnFirst = this._intCurrentPage == 1;
		var blnLast = this._intCurrentPage == this._intTotalPages;
		$R_FN.showElement(this._hypPrev, !blnFirst);
		$R_FN.showElement(this._lblPrevDisabled, blnFirst);
		$R_FN.showElement(this._hypNext, !blnLast);
		$R_FN.showElement(this._lblNextDisabled, blnLast);
	},
	
	nextPage: function() {
		if (!this._blnEnabled) return;
		this.changePage(this._intCurrentPage + 1);
	},
	
	prevPage: function() {
		if (!this._blnEnabled) return;
		this.changePage(this._intCurrentPage - 1);
	},
	
	changePage: function(intNewPage) {
		if (!this._blnEnabled) return;
		this._intCurrentPage = this.limitPage(intNewPage);
		this.onPageChanged();
	},
	
	limitPage: function(intPage) {
		return Math.max(Math.min(intPage, this._intTotalPages), 1);
	},
	
	clickLock: function() {
		if (!this._blnEnabled) return;
		this.showLockLoading();
		this._blnStateLocked = !this._blnStateLocked;
		this.onStateLockChanged();
	},
	
	setLockState: function(blnLock) {
		if (!this._pnlLock) return;
		this._blnStateLocked = blnLock;
		Sys.UI.DomElement.removeCssClass(this._pnlLock, "lockLoading");
		if (blnLock) {
			Sys.UI.DomElement.addCssClass(this._pnlLock, "locked");
			Sys.UI.DomElement.removeCssClass(this._pnlLock, "unlocked");
		} else {
			Sys.UI.DomElement.addCssClass(this._pnlLock, "unlocked");
			Sys.UI.DomElement.removeCssClass(this._pnlLock, "locked");
		}
	},
	
	showLockLoading: function(blnDontRaiseEvent) {
		if (!this._pnlLock) return;
		Sys.UI.DomElement.addCssClass(this._pnlLock, "lockLoading");
		Sys.UI.DomElement.removeCssClass(this._pnlLock, "locked");
		Sys.UI.DomElement.removeCssClass(this._pnlLock, "unlocked");
		if (!blnDontRaiseEvent) this.onShowLockLoading();
	}

};

Rebound.GlobalTrader.Site.Controls.PagingButtons.registerClass("Rebound.GlobalTrader.Site.Controls.PagingButtons", Sys.UI.Control, Sys.IDisposable);