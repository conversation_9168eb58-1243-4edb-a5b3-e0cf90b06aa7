///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// SK 12.03.2010:
// - include customer-, supplier-code on nugget
//
// RP 06.01.2010:
// - fully dispose everything
//
// RP 23.12.2009:
// - render state on server
//
// RP 30.11.2009:
// - allow passing of initial Name search
//
// RP 28.10.2009:
// - add Company List Type to Company Detail links
// RP 16.10.2009:
// - Changes due to changes in base class
/*
Marker     changed by      date         Remarks
[001]      Vinay		   26/12/2012   Supplier code should be right align
[002]      <PERSON><PERSON><PERSON>     13-Sep-2018     [REB-12820]:Provision to add Global Security on Contact Section
*/
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DataListNuggets");

Rebound.GlobalTrader.Site.Controls.DataListNuggets.Companies = function (element) {
    Rebound.GlobalTrader.Site.Controls.DataListNuggets.Companies.initializeBase(this, [element]);
    this.getSelectedGroup = null;
};

Rebound.GlobalTrader.Site.Controls.DataListNuggets.Companies.prototype = {

    get_enmContactListType: function () { return this._enmContactListType; }, set_enmContactListType: function (value) { if (this._enmContactListType !== value) this._enmContactListType = value; },
    //[002] start
    get_IsGlobalLogin: function () { return this._IsGlobalLogin; }, set_IsGlobalLogin: function (v) { if (this._IsGlobalLogin !== v) this._IsGlobalLogin = v; },
    //[002] end
    //get_ibtnAdd: function () { return this._ibtnAdd; }, set_ibtnAdd: function (v) { if (this._ibtnAdd !== v) this._ibtnAdd = v; },
    get_IsGSA: function () { return this._IsGSA; }, set_IsGSA: function (v) { if (this._IsGSA !== v) this._IsGSA = v; },

    initialize: function () {
        this.addInitCompleteEvent(Function.createDelegate(this, this.initAfterBaseIsReady));
        this.addSetupDataCallEvent(Function.createDelegate(this, this.setupDataCall));
        this.addGetDataOKEvent(Function.createDelegate(this, this.getDataOK));
        this._strPathToData = "controls/DataListNuggets/Companies";
        this._strDataObject = "Companies";


        Rebound.GlobalTrader.Site.Controls.DataListNuggets.Companies.callBaseMethod(this, "initialize");
        //if (this._ibtnAdd) $R_IBTN.addClick(this._ibtnAdd, Function.createDelegate(this, this.showAddForm));
        this._frmAdd = $find(this._aryFormIDs[0]);
        this._frmAdd.addCancel(Function.createDelegate(this, this.hideAddForm));
        this._frmAdd.addSaveComplete(Function.createDelegate(this, this.addComplete));
        this.showGroupCodeName();
        this.getSelectedGroup = $get('ctl00_cphMain_ctlCompanies_ctlDB_ctl16_ctlFilter_ctlClientName_ddl_ddl');
    },
    getFormControlID: function (ParentId, controlID) {
        var str = "";
        str = String.format("{0}_{1}", ParentId, controlID);
        return str;
    },
    initAfterBaseIsReady: function () {
        //[002] start

        this.updateFilterVisibility();
        //[002] end
        this.addPageTabChangedEvent(Function.createDelegate(this, this.pageTabChanged));
        this.getData();
    },

    dispose: function () {
        if (this.isDisposed) return;
        this._IsGSA = null;
        this._IsGlobalLogin = null;
        this._enmContactListType = null;
        Rebound.GlobalTrader.Site.Controls.DataListNuggets.Companies.callBaseMethod(this, "dispose");
    },
    //Close: function () {
    //    $("#RequestApprovalModel").hide();
    //},
    //btnSave: function () {
    //    var dtm;
    //    var dtmToday = new Date();
    //    if ($("#ctl00_cphMain_ctlCompanies_ctlDB_ctl15_txtName").val() != "" && $("#ctl00_cphMain_ctlCompanies_ctlDB_ctl15_txtName").val() != null) {
    //        this.saveClicked();
    //    }
    //    else {
    //        alert("Name can not be empty!");
    //    }


    //},
    //saveClicked: function () {
    //    //if (!this.validateForm()) return;
    //    // this.showSaving(true);
    //    var obj = new Rebound.GlobalTrader.Site.Data();
    //    obj.set_PathToData("controls/DataListNuggets/Companies");
    //    obj.set_DataObject("Companies");
    //    obj.set_DataAction("AddNew");
    //    obj.addParameter("Name", $("#ctl00_cphMain_ctlCompanies_ctlDB_ctl15_txtName").val());
    //    obj.addParameter("Contact", $("#ctl00_cphMain_ctlCompanies_ctlDB_ctl15_ddlType_ddl").val());
    //    obj.addParameter("Type", $("#ctl00_cphMain_ctlCompanies_ctlDB_ctl15_ddlContact_ddl").val());
    //    obj.addParameter("Task", $("#ctl00_cphMain_ctlCompanies_ctlDB_ctl15_txtTask").val());
    //    obj.addParameter("TaskDate", $("#ctl00_cphMain_ctlCompanies_ctlDB_ctl15_txtTaskDate").val());
    //    obj.addParameter("ReminderDate", $("#ctl00_cphMain_ctlCompanies_ctlDB_ctl15_txtReminderDate").val());
    //    obj.addParameter("CompanyId", CompanyNo);
    //    obj.addDataOK(Function.createDelegate(this, this.saveEditComplete));
    //    obj.addError(Function.createDelegate(this, this.saveEditError));
    //    obj.addTimeout(Function.createDelegate(this, this.saveEditError));
    //    $R_DQ.addToQueue(obj);
    //    $R_DQ.processQueue();
    //    obj = null;
    //},

    //saveEditError: function (args) {
    //    this._strErrorMessage = args._errorMessage;
    //    this.onSaveError();
    //},

    //saveEditComplete: function (args) {
    //    if (args._result.NewID > 0) {
    //        this.getData();
    //        $("#RequestApprovalModel").hide();


    //    } else {
    //        this._strErrorMessage = args._errorMessage;
    //        this.onSaveError();
    //    }
    //},


    pageTabChanged: function () {
        this._table._intCurrentPage = 1;
        this._enmViewLevel = this._intCurrentTab;
        //[002] start
        this.updateFilterVisibility();
        //[002] end
        this.getData();
    },

    setupDataCall: function () {
        var strAction = "GetData_";
        switch (this._enmContactListType) {
            case $R_ENUM$CompanyListType.AllCompanies: strAction += "Companies"; break;
            case $R_ENUM$CompanyListType.Customers: strAction += "Customers"; break;
            case $R_ENUM$CompanyListType.Suppliers: strAction += "Suppliers"; break;
            case $R_ENUM$CompanyListType.Prospects: strAction += "Prospects"; break;
        }
        this._objData.set_DataAction(strAction);
        this._objData.addParameter("ViewLevel", this._enmViewLevel);
        this._objData.addParameter("CallType", this._enmContactListType);
        //[002] start
        this._objData.addParameter("IsGlobalLogin", this._IsGlobalLogin);
        //[002] end
    },
    //[001] code start
    getDataOK: function () {

        var chkdatestatus = '';
        var imagepath = "app_themes/original/images/IconButton/filters/apply.gif";
        var IsPDFAvailable = true;
        for (var i = 0, l = this._objResult.Results.length; i < l; i++) {
            var row = this._objResult.Results[i];
            var strSupCode = "";
            var strCustCode = "";
            if (row.TaskCount > 0) {
                chkdatestatus = true;
                //imagepath = "app_themes/original/images/IconButton/filters/apply.gif";
                //PDF_link_icon.png || pdficon.jpg || PDF_link_icons.png
            }

            else {
                chkdatestatus = false;
            }
            switch (this._enmContactListType) {
                case $R_ENUM$CompanyListType.AllCompanies: strCustCode = $R_FN.setCleanTextValue(row.CustomerCode); strSupCode = $R_FN.setCleanTextValue(row.SupplierCode); break;
                case $R_ENUM$CompanyListType.Customers: strCustCode = $R_FN.setCleanTextValue(row.CustomerCode); break;
                case $R_ENUM$CompanyListType.Suppliers: strSupCode = $R_FN.setCleanTextValue(row.SupplierCode); break;
            }
            var imgLink = "";
            imgLink = " <img src='../../../../images/crmicons/" + row.CRMCompleteCount + ".png'>";
            var aryData = [
                $R_FN.writeDoubleCellValueAlignRightSupplier(row.blnMakeYellow == true ?
                    '<span style="background-color:yellow;">' + $RGT_nubButton_Company(row.ID, row.Name + ' (' + row.ID + ') ', null, this._enmContactListType, row.OnStop, row.AdvisoryNotes) + '</span>'
                    : $RGT_nubButton_Company(row.ID, row.Name + ' (' + row.ID + ') ', null, this._enmContactListType, row.OnStop, row.AdvisoryNotes),
                    strCustCode, strSupCode, row.Inactive)
                , $R_FN.setCleanTextValue(imgLink)
                , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Type), $R_FN.setCleanTextValue(row.Terms))
                , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.City), $R_FN.setCleanTextValue(row.Country))
                , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Tel), $R_FN.setCleanTextValue(row.Email))
                , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.SalespersonName), row.SalesTurnOver)
                //, $R_FN.setCleanTextValue(row.LastContact)
                //,row.blnMakeYellow == false?
                //String.format("<a href=\"javascript:void(0);\" title='Add task' onclick=\"$find('{0}').showAddForm({1},'{2}');\">" + "Add Task" + "</a>", this._element.id, row.ID, row.Name)
                //    + "&nbsp;&nbsp;&nbsp" + String.format("<a href=\"javascript:void(0);\" title='View task' onclick=\"$find('{0}').redirectToDetails({1},'{2}');\">" + (row.TaskCount + " Task") + "</a>", this._element.id, row.ID, row.Name) : ''
                , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.LastContact)
                    , row.blnMakeYellow == false ?
                        String.format("<a href=\"javascript:void(0);\" title='Add task' onclick=\"$find('{0}').showAddForm({1},'{2}');\">" + "Add Task" + "</a>", this._element.id, row.ID, row.Name)
                        + "&nbsp;&nbsp;&nbsp" + String.format("<a href=\"javascript:void(0);\" title='View task' onclick=\"$find('{0}').redirectToDetails({1},'{2}');\">" + (row.TaskCount + " Task") + "</a>", this._element.id, row.ID, row.Name) : '')
                , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.InsuranceCertificateNo), $R_FN.setCleanTextValue(row.CertificateCategoryName))
                , $R_FN.setCleanTextValue(row.ClientName)


            ];
            this._table.addRow(aryData, row.ID, false, "", null, this.get_element().id, "showMoreInfo", "hideMoreInfo", [row.ID]);
            //document.getElementById(("btnAddTask" + row.ID.toString())).addEventListener("click", Function.createDelegate(this, this.showAddForm));
            aryData = null; row = null;
        }
    },
    //[001] code end
    //[002] start
    updateFilterVisibility: function () {
        this.getFilterField("ctlClientName").show(this._IsGlobalLogin || this._IsGSA);
    },

    showGroupCodeName: function () {
        if (this._enmContactListType !== $R_ENUM$CompanyListType.Customers) {
            $("#ctl00_cphMain_ctlPageTitle_ctl20_hypAddGroupCode").hide();
            if (this._enmContactListType !== $R_ENUM$CompanyListType.AllCompanies) {
                $("#ctl00_cphMain_ctlCompanies_ctlDB_ctl16_ctlFilter_ctlGroupCodeName").hide();
                $("#ctl00_cphMain_ctlCompanies_ctlDB_ctl16_ctlFilter_ctlCompSupType").hide();
                $("#ctl00_cphMain_ctlCompanies_ctlDB_ctl16_ctlFilter_ctlVATIDs").hide()
            }
            else {
                $("#ctl00_cphMain_ctlCompanies_ctlDB_ctl16_ctlFilter_ctlGroupCodeName").show();
                $("#ctl00_cphMain_ctlCompanies_ctlDB_ctl16_ctlFilter_ctlVATIDs").show();
                $("#ctl00_cphMain_ctlCompanies_ctlDB_ctl16_ctlFilter_ctlCompSupType").show();
            }
        }
        else {
            $("#ctl00_cphMain_ctlCompanies_ctlDB_ctl16_ctlFilter_ctlGroupCodeName").show();
            $("#ctl00_cphMain_ctlPageTitle_ctl20_hypAddGroupCode").show();
            $("#ctl00_cphMain_ctlCompanies_ctlDB_ctl16_ctlFilter_ctlVATIDs").hide()
        }
    },
    //[002] end
    showAddForm: function (Id, Name) {
        this._frmAdd.setFormFieldsToDefaults();
        this._frmAdd.setFieldValue("ctlDueTime", "09:00");
        this._frmAdd.setFieldValue("ctlReminderTime", "09:00");
        this._frmAdd.setFieldValue("ctlCompanyNew", Id, null, Name);
        this._frmAdd.setFieldValue("ctlCompany", '<a style="color:white;" href="Con_CompanyDetail.aspx?cm=' + Id + '">' + $R_FN.setCleanTextValue(Name) + '</a>');
        this._frmAdd._intCategoryID = 1;
        this.showForm(this._frmAdd, true);
    },

    hideAddForm: function () {
        this._frmAdd.resetFormData();
        this.showForm(this._frmAdd, false);
    },

    addComplete: function () {
        this.hideAddForm();
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
        this.getData();
    },
    redirectToDetails: function (Id, Name) {
        location.href = ('Prf_ToDo.aspx?cm=' + Id + '&cmn=' + $R_FN.setCleanTextValue(Name)), '_blank';
    },
    applyFilter: function () {
        isClientChecked = $get('ctl00_cphMain_ctlCompanies_ctlDB_ctl16_ctlFilter_ctlClientName_chkOn').checked;
        if (this.getSelectedGroup.options[this.getSelectedGroup.selectedIndex] != undefined) {
            if (this.getSelectedGroup.options[this.getSelectedGroup.selectedIndex].innerText == "All") {
                var countChecked = 0;
                const filters = new Array('ctlName', 'ctlType', 'ctlCity', 'ctllstCountry', 'ctlTel', 'ctlState', 'ctlCounty', 'ctlRelatedManufac', 'ctlGroupCodeName', 'ctlCompSupType', 'ctlInsuranceCertificateNo',
                    'ctlCertificateCategoryNo', 'ctlSalesperson', 'ctlSupplierRating', 'ctlCustomerRating', 'ctlCustomerNo', 'ctlZipcode', 'ctlRegion', 'ctlEmail', 'ctlIndustryType', 'ctlVATIDs');
                for (const filter of filters) {
                    const element = 'ctl00_cphMain_ctlCompanies_ctlDB_ctl16_ctlFilter_' + filter + '_chkOn';
                    const otherFilterChecked = $get(element).checked;
                    if (otherFilterChecked) {
                        countChecked++;
                        break;
                    }
                }
                if ((countChecked == 0) && isClientChecked) {
                    alert("Please ensure that at least one other filter is selected to be able to view data from all companies in all clients");
                    return;
                }
            }
        }
        this.onFilterData();
    }
};
Rebound.GlobalTrader.Site.Controls.DataListNuggets.Companies.registerClass("Rebound.GlobalTrader.Site.Controls.DataListNuggets.Companies", Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base, Sys.IDisposable);
