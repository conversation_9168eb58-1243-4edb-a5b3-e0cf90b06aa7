﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].[usp_ipobom_update_ReverseLogisticBulk]
--****************************************************************************************                            
--*  23.01.2024:                 
--*  Marker     Changed by           Date         Remarks               
--*  [001]      Devendra Sikarwar    23-01-2024  [RP-2603]                 
--*  EXEC  [usp_ipobom_update_ReverseLogisticBulk]  '55745,55743,55741',0,4763              
--****************************************************************************************                            
	@ReverseLogisticId NVARCHAR(300)
	,@isBulk BIT
	,@updatedBy INT
	,@RowsAffected INT = NULL OUTPUT
AS
BEGIN
	-- Declare @ActionTaken nvarchar(200)        
	-- set @ActionTaken =case when @isBulk=1 then 'Remove stocks' else 'Set quantity to zero' end        
	--exec usp_insert_ReverseLogisticBulkEditHistory @ReverseLogisticId,@updatedBy,@ActionTaken           
	UPDATE [BorisGlobalTraderImports].dbo.tbReverseLogistic
	SET Quantity = IIF(@isBulk = 0, 0, Quantity)
		,Inactive = IIF(@isBulk = 1, 1, Inactive)
		,UpdatedBy = @updatedBy
		,UpdatedOn = GETDATE()
	WHERE ReverseLogisticId IN (
			SELECT VALUE
			FROM STRING_SPLIT(@ReverseLogisticId, ',')
			)

	SELECT @RowsAffected = @@ROWCOUNT
END
GO


