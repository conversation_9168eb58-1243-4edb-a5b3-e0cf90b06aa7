SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO
IF(OBJECT_ID(N'dbo.ufn_get_local_exchange_rate', N'FN')) IS NOT NULL
    DROP FUNCTION dbo.ufn_get_local_exchange_rate;
GO
-- =============================================
-- Author:		An.TranTan
-- Create date: 14-Jun-2024
-- Description:	Get exchange rate of local currency at specific date
CREATE FUNCTION [dbo].[ufn_get_local_exchange_rate]
(	@LocalCurrencyId INT,
	@CurrencyDate DATETIME = NULL
)
RETURNS FLOAT
AS BEGIN
	IF @CurrencyDate IS NULL
		SET @CurrencyDate = CURRENT_TIMESTAMP

	SET @CurrencyDate = CAST(CAST(@CurrencyDate AS DATE) AS DATETIME) + CAST('23:59:59.997' AS DATETIME)

	DECLARE @ExchangeRate FLOAT

	SELECT	TOP 1
			@ExchangeRate = ExchangeRate
	FROM	dbo.tbExchangeRate WITH (NoLock)
	WHERE	LocalCurrencyNo = @LocalCurrencyId
	AND		ExchangeRateDate <= @CurrencyDate
	ORDER BY ExchangeRateDate DESC

	RETURN ISNULL(@ExchangeRate, 1)
	END
GO


