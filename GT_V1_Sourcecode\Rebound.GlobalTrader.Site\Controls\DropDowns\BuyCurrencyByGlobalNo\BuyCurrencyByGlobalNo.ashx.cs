//[001]      <PERSON><PERSON><PERSON>     20-Aug-2018  Provision to add Global Security in Sales Order 
using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.Site.Controls.DropDowns.Data {
	[WebService(Namespace = "http://tempuri.org/")]
	[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class BuyCurrencyByGlobalNo : Rebound.GlobalTrader.Site.Data.DropDowns.Base
    {

		protected override void GetData() {
			
             int? intGlobalCurrencyNo;
             bool? blnBuy = true;
             blnBuy = GetFormValue_Boolean("blnBuy");
            intGlobalCurrencyNo = GetFormValue_NullableInt("GlobalNo");
            //[001] start
            int? clientId =GetFormValue_NullableInt("GlobalLoginClientNo") > 0 ? GetFormValue_Int("GlobalLoginClientNo") : SessionManager.ClientID;
            //[001] end
            string strOptions = CacheManager.SerializeOptions(new object[] { clientId, intGlobalCurrencyNo ?? 0, blnBuy });
            string strCachedData = CacheManager.GetDropDownData(_objDropDown.ID, strOptions);
			if (string.IsNullOrEmpty(strCachedData)) {
				JsonObject jsn = new JsonObject();
				JsonObject jsnList = new JsonObject(true);
                List<BLL.Currency> lst = BLL.Currency.DropDownBuyForClientAndGlobal(clientId, intGlobalCurrencyNo, blnBuy);
				for (int i = 0; i < lst.Count; i++) {
					JsonObject jsnItem = new JsonObject();
					jsnItem.AddVariable("ID", lst[i].CurrencyId);
					jsnItem.AddVariable("Name", String.Format("{1} - {0}", lst[i].CurrencyDescription, lst[i].CurrencyCode));
					jsnItem.AddVariable("Code", lst[i].CurrencyCode);
					jsnList.AddVariable(jsnItem);
					jsnItem.Dispose(); jsnItem = null;
				}
				lst.Clear(); lst = null;
				jsn.AddVariable("Currencies", jsnList);
				jsnList.Dispose(); jsnList = null;
				CacheManager.StoreDropDown(_objDropDown.ID, strOptions, jsn.Result, CacheManager.CacheExpiryType.OneWorkingDay);
				OutputResult(jsn);
				jsn.Dispose(); jsn = null;
			} else {
				_context.Response.Write(strCachedData);
			}
			strCachedData = null;
		}
	}
}
