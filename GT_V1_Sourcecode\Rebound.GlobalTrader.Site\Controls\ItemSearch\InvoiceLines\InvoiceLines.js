Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.ItemSearch");Rebound.GlobalTrader.Site.Controls.ItemSearch.InvoiceLines=function(n){Rebound.GlobalTrader.Site.Controls.ItemSearch.InvoiceLines.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.ItemSearch.InvoiceLines.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.ItemSearch.InvoiceLines.callBaseMethod(this,"initialize");this.addSetupData(Function.createDelegate(this,this.doSetupData));this.addGetDataComplete(Function.createDelegate(this,this.doGetDataComplete))},dispose:function(){this.isDisposed||Rebound.GlobalTrader.Site.Controls.ItemSearch.InvoiceLines.callBaseMethod(this,"dispose")},doSetupData:function(){this._objData.set_PathToData("controls/ItemSearch/InvoiceLines");this._objData.set_DataObject("InvoiceLines");this._objData.set_DataAction("GetData");this._objData.addParameter("Part",this.getFieldValue("ctlPartNo"));this._objData.addParameter("Contact",this.getFieldValue("ctlContact"));this._objData.addParameter("CMName",this.getFieldValue("ctlCompany"));this._objData.addParameter("IncludePaid",this.getFieldValue("ctlIncludePaid"));this._objData.addParameter("Salesman",this.getFieldValue("ctlSalesman"));this._objData.addParameter("CustomerPO",this.getFieldValue("ctlCustomerPO"));this._objData.addParameter("InvoiceNoLo",this.getFieldValue_Min("ctlInvoiceNo"));this._objData.addParameter("InvoiceNoHi",this.getFieldValue_Max("ctlInvoiceNo"));this._objData.addParameter("SONoLo",this.getFieldValue_Min("ctlSalesOrderNo"));this._objData.addParameter("SONoHi",this.getFieldValue_Max("ctlSalesOrderNo"));this._objData.addParameter("DateInvoicedFrom",this.getFieldValue("ctlDateInvoicedFrom"));this._objData.addParameter("DateInvoicedTo",this.getFieldValue("ctlDateInvoicedTo"))},doGetDataComplete:function(){for(var n,i,t=0,r=this._objResult.Results.length;t<r;t++)n=this._objResult.Results[t],i=[n.No,$R_FN.setCleanTextValue(n.CMName),$R_FN.writePartNo(n.Part,n.ROHS),$R_FN.setCleanTextValue(n.Date),n.Price,n.Quantity,$R_FN.setCleanTextValue(n.CustomerPO),$R_FN.setCleanTextValue(n.SalesOrderNo),$R_FN.setCleanTextValue(n.Cost)],this._tblResults.addRow(i,n.ID,!1),i=null,n=null}};Rebound.GlobalTrader.Site.Controls.ItemSearch.InvoiceLines.registerClass("Rebound.GlobalTrader.Site.Controls.ItemSearch.InvoiceLines",Rebound.GlobalTrader.Site.Controls.ItemSearch.Base,Sys.IDisposable);