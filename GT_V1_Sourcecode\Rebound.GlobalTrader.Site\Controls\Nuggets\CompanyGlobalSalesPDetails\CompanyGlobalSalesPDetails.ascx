<%@ Control Language="C#" CodeBehind="CompanyGlobalSalesPDetails.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyGlobalSalesPDetails" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" BoxType="Standard">
	<Links>
		<ReboundUI:IconButton ID="ibtnAdd" runat="server" IconGroup="Nugget" IconTitleResource="Add" IconCSSType="Add" IconButtonMode="Hyperlink"></ReboundUI:IconButton>
		<ReboundUI:IconButton ID="ibtnEdit" runat="server" IconGroup="Nugget" IconTitleResource="Edit" IconCSSType="Edit" IconButtonMode="Hyperlink" IsInitiallyEnabled="false"></ReboundUI:IconButton>
		<ReboundUI:IconButton ID="ibtnDelete" runat="server" IconGroup="Nugget" IconTitleResource="Delete" IconCSSType="Delete" IconButtonMode="Hyperlink" IsInitiallyEnabled="false"></ReboundUI:IconButton>
	</Links>
	<Content>
		<ReboundUI:FlexiDataTable ID="tbl" runat="server" Width="100%" PanelHeight="100" />
	</Content>
	<Forms>
		<ReboundForm:CompanyGlobalSalesPDetails_AddEdit id="frmAddEdit" runat="server" />
		<ReboundForm:CompanyGlobalSalesPDetails_Delete id="frmDelete" runat="server" />
	</Forms>
</ReboundUI_Nugget:DesignBase>
