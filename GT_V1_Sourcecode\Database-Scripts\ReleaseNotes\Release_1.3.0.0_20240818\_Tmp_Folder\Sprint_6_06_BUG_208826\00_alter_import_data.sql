﻿/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[BUG-208826]	An.TranTan			23-Jul-2024		Update			Add column LineNumber to UploadedDataPriceQuote
===========================================================================================
*/
IF OBJECT_ID('dbo.usp_saveExcelBulkSave_PriceQuote', 'P') IS NOT NULL
	DROP PROCEDURE dbo.usp_saveExcelBulkSave_PriceQuote
GO
IF TYPE_ID(N'UploadedDataPriceQuote') IS NOT NULL
	DROP TYPE [dbo].[UploadedDataPriceQuote]
GO

CREATE TYPE [dbo].[UploadedDataPriceQuote] AS TABLE(
	[Column1] [nvarchar](max) NULL DEFAULT (NULL),
	[Column2] [nvarchar](max) NULL DEFAULT (NULL),
	[Column3] [nvarchar](max) NULL DEFAULT (NULL),
	[Column4] [nvarchar](max) NULL DEFAULT (NULL),
	[Column5] [nvarchar](max) NULL DEFAULT (NULL),
	[Column6] [nvarchar](max) NULL DEFAULT (NULL),
	[Column7] [nvarchar](max) NULL DEFAULT (NULL),
	[Column8] [nvarchar](max) NULL DEFAULT (NULL),
	[Column9] [nvarchar](max) NULL DEFAULT (NULL),
	[Column10] [nvarchar](max) NULL DEFAULT (NULL),
	[Column11] [nvarchar](max) NULL DEFAULT (NULL),
	[Column12] [nvarchar](max) NULL DEFAULT (NULL),
	[Column13] [nvarchar](max) NULL DEFAULT (NULL),
	[Column14] [nvarchar](max) NULL DEFAULT (NULL),
	[Column15] [nvarchar](max) NULL DEFAULT (NULL),
	[Column16] [nvarchar](max) NULL DEFAULT (NULL),
	[Column17] [nvarchar](max) NULL DEFAULT (NULL),
	[Column18] [nvarchar](max) NULL DEFAULT (NULL),
	[Column19] [nvarchar](max) NULL DEFAULT (NULL),
	[Column20] [nvarchar](max) NULL DEFAULT (NULL),
	[Column21] [nvarchar](max) NULL DEFAULT (NULL),
	[Column22] [nvarchar](max) NULL DEFAULT (NULL),
	[Column23] [nvarchar](max) NULL DEFAULT (NULL),
	[Column24] [nvarchar](max) NULL DEFAULT (NULL),
	[Column25] [nvarchar](max) NULL DEFAULT (NULL),
	[LineNumber] [int] NULL
)
GO

/*   
===========================================================================================  
TASK			UPDATED BY      DATE			ACTION		DESCRIPTION  
[BUG-208826]	An.TranTan		23-Jul-2024		Update		Add column LineNumber to table tbPriceQuoteImport_tempData
===========================================================================================  
*/ 
IF COL_LENGTH('BorisGlobalTraderimports.dbo.tbPriceQuoteImport_tempData', 'LineNumber') IS NULL
BEGIN
   ALTER TABLE BorisGlobalTraderimports.dbo.tbPriceQuoteImport_tempData ADD LineNumber INT NULL
END
GO

/*   
===========================================================================================  
TASK			UPDATED BY      DATE			ACTION		DESCRIPTION  
[US-205174]		An.TranTan		28-Jun-2024		Update		Insert column ClientNo at Column2 to tempdata table  
[BUG-208826]	An.TranTan		23-Jul-2024		Update		Insert LineNumber data from UploadedDataPriceQuote
===========================================================================================  
*/  
CREATE Procedure [dbo].[usp_saveExcelBulkSave_PriceQuote]  
    @UploadedData UploadedDataPriceQuote READONLY,  
    @originalFilename varchar(max),  
    @generatedFilename varchar(max),  
    @userId Int,  
    @clientId int,  
    @SelectedclientId int  
AS  
BEGIN  
    SET NOCOUNT ON;  
    INSERT INTO BorisGlobalTraderimports.dbo.tbPriceQuoteImport_tempData  
    (  
        [Column1],  
        [Column2],  
        [Column3],  
        [Column4],  
        [Column5],  
        [Column6],  
        [Column7],  
        [Column8],  
        [Column9],  
        [Column10],  
        [Column11],  
        [Column12],  
        [Column13],  
        [Column14],  
        [Column15],  
        [Column16],  
        [Column17],  
        [Column18],  
        [Column19],  
        [Column20],  
        [Column21],  
        [Column22],  
        [Column23],  
        [Column24],  
        [Column25],  
        [OriginalFilename],  
        [GeneratedFilename],  
        [ClientId],  
        [SelectedClientId],  
        [CreatedBy],
		[LineNumber]
    )  
    select [Column1],  
           [Column2],  
           [Column3],  
           [Column4],  
           [Column5],  
           [Column6],  
           [Column7],  
           [Column8],  
           [Column9],  
           [Column10],  
           [Column11],  
           [Column12],  
           [Column13],  
           [Column14],  
           [Column15],  
           [Column16],  
           [Column17],  
           [Column18],  
           [Column19],  
           [Column20],  
           [Column21],  
           [Column22],  
           [Column23],  
           [Column24],  
           [Column25],  
           @originalFilename,  
           @generatedFilename,  
           @clientId,  
		   @SelectedclientId,  
           @userId,
		   [LineNumber]
    from @UploadedData  
END
GO




