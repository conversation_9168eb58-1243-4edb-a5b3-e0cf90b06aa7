///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Setup");

Rebound.GlobalTrader.Site.Pages.Setup.GS_SetupList = function (el) {
    Rebound.GlobalTrader.Site.Pages.Setup.GS_SetupList.initializeBase(this, [el]);
};

Rebound.GlobalTrader.Site.Pages.Setup.GS_SetupList.prototype = {

	get_ctlLabelSetupItem: function() { return this._ctlLabelSetupItem; }, 	set_ctlLabelSetupItem: function(v) { if (this._ctlLabelSetupItem !== v)  this._ctlLabelSetupItem = v; }, 
	get_ctlLabelSetup: function () { return this._ctlLabelSetup; }, set_ctlLabelSetup: function (v) { if (this._ctlLabelSetup !== v) this._ctlLabelSetup = v; },

	initialize: function() {
	    Rebound.GlobalTrader.Site.Pages.Setup.GS_SetupList.callBaseMethod(this, "initialize");
	},
	
	goInit: function () {
	   //	this._ctlLabelSetupItem.addSelectProduct(Function.createDelegate(this, this.ctlLabelSetupItem_SelectProduct));		
	   	this._ctlLabelSetup.addSelectLabelSetup(Function.createDelegate(this, this.ctlLabelSetupItem_addLabelSetup));
		Rebound.GlobalTrader.Site.Pages.Setup.GS_SetupList.callBaseMethod(this, "goInit");
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this._ctlLabelSetupItem) this._ctlLabelSetupItem.dispose();
		if (this._ctlLabelSetup) this._ctlLabelSetup.dispose();
		this._ctlLabelSetupItem = null;
		this._ctlLabelSetup = null;
		Rebound.GlobalTrader.Site.Pages.Setup.GS_SetupList.callBaseMethod(this, "dispose");
	},
	
	ctlLabelSetupItem_SelectProduct: function () {	    
		this._ctlLabelSetupItem._tbl.resizeColumns();
	},

	ctlLabelSetupItem_addLabelSetup: function () {
	    this._ctlLabelSetupItem._intLabelSetupID = this._ctlLabelSetup._intLabelSetupID;
	    this._ctlLabelSetupItem.show(true);
	    this._ctlLabelSetup._tbl.resizeColumns();
	    this._ctlLabelSetupItem.refresh();
	}
	
};

Rebound.GlobalTrader.Site.Pages.Setup.GS_SetupList.registerClass("Rebound.GlobalTrader.Site.Pages.Setup.GS_SetupList", Rebound.GlobalTrader.Site.Pages.Content, Sys.IDisposable);
