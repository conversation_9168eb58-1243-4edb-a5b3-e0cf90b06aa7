Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.SupplierInvoiceStatus=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.SupplierInvoiceStatus.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DropDowns.SupplierInvoiceStatus.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.DropDowns.SupplierInvoiceStatus.callBaseMethod(this,"initialize");this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK))},dispose:function(){this.isDisposed||Rebound.GlobalTrader.Site.Controls.DropDowns.SupplierInvoiceStatus.callBaseMethod(this,"dispose")},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/SupplierInvoiceStatus");this._objData.set_DataObject("SupplierInvoiceStatus");this._objData.set_DataAction("GetData")},dataCallOK:function(){var t=this._objData._result,n;if(t.Items)for(n=0;n<t.Items.length;n++)this.addOption(t.Items[n].Name,t.Items[n].ID)}};Rebound.GlobalTrader.Site.Controls.DropDowns.SupplierInvoiceStatus.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.SupplierInvoiceStatus",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);