/*
Marker     Changed by      Date         Remarks
[001]      Vinay          18/05/2015     ESMS Ref:233 
*/
using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site.Controls;
using Rebound.GlobalTrader.Site;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets {
	public partial class CompanyPurchasingInfo : Base {

		#region Locals

		protected IconButton _ibtnEdit;
		protected FlexiDataTable _tblOpenPOs;
		protected Panel _pnlLoadingOpenPOs;
		protected Panel _pnlOpenPOsError;
		protected Panel _pnlOpenPOs;
		protected Panel _pnlGetOpenPOs;
		protected HyperLink _hypGetOpenPOs;
		protected FlexiDataTable _tblOverduePOs;
		protected Panel _pnlLoadingOverduePOs;
		protected Panel _pnlOverduePOsError;
		protected Panel _pnlOverduePOs;
		protected Panel _pnlGetOverduePOs;
		protected HyperLink _hypGetOverduePOs;

		#endregion

		#region Properties

		private int _intCompanyID = -1;
		public int CompanyID {
			get { return _intCompanyID; }
			set { _intCompanyID = value; }
		}

		private bool _blnCanEdit = true;
		public bool CanEdit {
			get { return _blnCanEdit; }
			set { _blnCanEdit = value; }
		}

        //[001] code start
        private bool _blnCanStopSupplier = true;
        public bool CanStopSupplier
        {
            get { return _blnCanStopSupplier; }
            set { _blnCanStopSupplier = value; }
        }
        //[001] code end


		#endregion

		#region Overrides

		/// <summary>
		/// Oninit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
			WireUpControls();
			AddScriptReference("Controls.Nuggets.CompanyPurchasingInfo.CompanyPurchasingInfo.js");
			TitleText = Functions.GetGlobalResource("Nuggets", "CompanyPurchasingInfo");
			if (_intCompanyID < 1) _intCompanyID = _objQSManager.CompanyID;
			SetupTable();
		}

		protected override void OnLoad(EventArgs e) {
			SetupScriptDescriptors();
			base.OnLoad(e);
		}

		protected override void OnPreRender(EventArgs e) {
			_ibtnEdit.Visible = _blnCanEdit;
			base.OnPreRender(e);
		}

		#endregion

		private void SetupTable() {
			_tblOpenPOs.AllowSelection = false;
			_tblOpenPOs.Columns.Add(new FlexiDataColumn("Number", WidthManager.GetWidth(WidthManager.ColumnWidth.SystemDocumentNumber)));
			_tblOpenPOs.Columns.Add(new FlexiDataColumn("Date", WidthManager.GetWidth(WidthManager.ColumnWidth.Date)));
			_tblOpenPOs.Columns.Add(new FlexiDataColumn("Value"));

			_tblOverduePOs.AllowSelection = false;
			_tblOverduePOs.Columns.Add(new FlexiDataColumn("Number", WidthManager.GetWidth(WidthManager.ColumnWidth.SystemDocumentNumber)));
			_tblOverduePOs.Columns.Add(new FlexiDataColumn("Date", WidthManager.GetWidth(WidthManager.ColumnWidth.Date)));
			_tblOverduePOs.Columns.Add(new FlexiDataColumn("Value"));
		}

		/// <summary>
		/// Sets up AJAX script descriptors
		/// </summary>
		private void SetupScriptDescriptors() {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyPurchasingInfo", ctlDesignBase.ClientID);
			if (_blnCanEdit) _scScriptControlDescriptor.AddElementProperty("ibtnEdit", _ibtnEdit.ClientID);
			_scScriptControlDescriptor.AddProperty("intCompanyID", _intCompanyID);
			_scScriptControlDescriptor.AddElementProperty("pnlLoadingOpenPOs", _pnlLoadingOpenPOs.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlOpenPOsError", _pnlOpenPOsError.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlOpenPOs", _pnlOpenPOs.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlGetOpenPOs", _pnlGetOpenPOs.ClientID);
			_scScriptControlDescriptor.AddElementProperty("hypGetOpenPOs", _hypGetOpenPOs.ClientID);
			_scScriptControlDescriptor.AddComponentProperty("tblOpenPOs", _tblOpenPOs.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlLoadingOverduePOs", _pnlLoadingOverduePOs.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlOverduePOsError", _pnlOverduePOsError.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlOverduePOs", _pnlOverduePOs.ClientID);
			_scScriptControlDescriptor.AddComponentProperty("tblOverduePOs", _tblOverduePOs.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlGetOverduePOs", _pnlGetOverduePOs.ClientID);
			_scScriptControlDescriptor.AddElementProperty("hypGetOverduePOs", _hypGetOverduePOs.ClientID);
            _scScriptControlDescriptor.AddProperty("CanStopSupplier", _blnCanStopSupplier);
		}

		/// <summary>
		/// Wire up controls from the ascx
		/// </summary>
		private void WireUpControls() {
			_ibtnEdit = FindIconButton("ibtnEdit");
			_tblOpenPOs = (FlexiDataTable)ctlDesignBase.FindContentControl("tblOpenPOs");
			_pnlLoadingOpenPOs = (Panel)ctlDesignBase.FindContentControl("pnlLoadingOpenPOs");
			_pnlOpenPOsError = (Panel)ctlDesignBase.FindContentControl("pnlOpenPOsError");
			_pnlOpenPOs = (Panel)ctlDesignBase.FindContentControl("pnlOpenPOs");
			_pnlGetOpenPOs = (Panel)ctlDesignBase.FindContentControl("pnlGetOpenPOs");
			_hypGetOpenPOs = (HyperLink)ctlDesignBase.FindContentControl("hypGetOpenPOs");
			_tblOverduePOs = (FlexiDataTable)ctlDesignBase.FindContentControl("tblOverduePOs");
			_pnlLoadingOverduePOs = (Panel)ctlDesignBase.FindContentControl("pnlLoadingOverduePOs");
			_pnlOverduePOsError = (Panel)ctlDesignBase.FindContentControl("pnlOverduePOsError");
			_pnlOverduePOs = (Panel)ctlDesignBase.FindContentControl("pnlOverduePOs");
			_pnlGetOverduePOs = (Panel)ctlDesignBase.FindContentControl("pnlGetOverduePOs");
			_hypGetOverduePOs = (HyperLink)ctlDesignBase.FindContentControl("hypGetOverduePOs");
		}

	}
}
