﻿using System.Web;
using System.Web.Services;
using System.Collections.Generic;

namespace Rebound.GlobalTrader.Site.Controls.DropDowns.Data
{
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class ToDoCategory : Rebound.GlobalTrader.Site.Data.DropDowns.Base
    {

        public override void ProcessRequest(HttpContext context)
        {
            SetDropDownType("ToDoCategory");
            base.ProcessRequest(context);
        }

        protected override void GetData()
        {
            JsonObject jsn = new JsonObject();
            JsonObject jsnList = new JsonObject(true);
            List<BLL.ToDoCategory> lst = BLL.ToDoCategory.DropDown();
            for (int i = 0; i < lst.Count; i++)
            {
                JsonObject jsnItem = new JsonObject();
                jsnItem.AddVariable("ID", lst[i].ToDoCategoryId);
                jsnItem.AddVariable("Name", lst[i].ToDoCategoryName);
                jsnList.AddVariable(jsnItem);
                jsnItem.Dispose(); jsnItem = null;
            }
            lst.Clear(); lst = null;
            jsn.AddVariable("Categories", jsnList);
            jsnList.Dispose(); jsnList = null;
            OutputResult(jsn);
            jsn.Dispose(); jsn = null;
        }
    }
}
