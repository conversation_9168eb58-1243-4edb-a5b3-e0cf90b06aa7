
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [dbo].[usp_KubStartCacheProcessForAddRequirement]  
@PartNumber  NVARCHAR(100)=NULL,  
@ClientID  INT=0  
AS   
/*  
 *Action: Created  By:<PERSON><PERSON><PERSON><PERSON>  Date:31-07-2023  Comment: Add new proc for cache the information for Add new Requirement Page.  
 */  
BEGIN  
SET NOCOUNT ON  
IF((@PartNumber IS NOT NULL) AND (@ClientID !=0))  
BEGIN  
  
--Step-1 Procedure---  
EXEC usp_KubInsertAssistanceDetailsInCache_Step1 @PartNumber,@ClientID  
  
---Sales data Table Procedure---  
EXEC usp_KubInsertCountryWiseSalesDetailsCachetb1 @PartNumber,@ClientID  
 
----Total GB calculation----
EXEC usp_KUB_GPCalculationDetails_Cache @ClientID,@PartNumber

--Load More Procedure---  
EXEC usp_KubInsertAssistanceDetailsInCache_LoadMore @PartNumber,@ClientID  
  
--PO <PERSON>a for Last 3---  
EXEC usp_KUBInsertPODetailsCache @PartNumber,@ClientID  
END  
SET NOCOUNT OFF  
END 
GO


