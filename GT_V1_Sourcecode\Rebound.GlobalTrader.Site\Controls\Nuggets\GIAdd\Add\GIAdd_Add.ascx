<%@ Control Language="C#" CodeBehind="GIAdd_Add.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.GIAdd_Add" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>

<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="true" ShowQuickHelp="false">

	<Links>
		<ReboundUI:IconButton ID="ibtnSave" runat="server" IconGroup="Nugget" IconTitleResource="Save" IconCSSType="Save" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
		<ReboundUI:IconButton ID="ibtnCancel" runat="server" IconGroup="Nugget" IconTitleResource="Cancel" IconCSSType="Cancel" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
		<ReboundUI:IconButton ID="ibtnSend" runat="server" IconGroup="Nugget" IconTitleResource="Send" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
		<ReboundUI:IconButton ID="ibtnContinue" runat="server" IconGroup="Nugget" IconTitleResource="Continue" IconCSSType="Continue" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
	</Links>

	<Explanation>
	
		<ReboundUI:MultiStep ID="ctlMultiStep" runat="server">
		
			<Items>
				<ReboundUI:MultiStepItem ID="ctlItem1" runat="server" ResourceTitle="GIAdd_SelectPO" />
				<ReboundUI:MultiStepItem ID="ctlItem2" runat="server" ResourceTitle="GIAdd_EnterDetail" />
				<ReboundUI:MultiStepItem ID="ctlItem3" runat="server" ResourceTitle="GIAdd_Notify" />
			</Items>
			
		</ReboundUI:MultiStep>
		
	</Explanation>

	<Content>
		<!-- Step 1 ------------------------------------------------------------------->
		<ReboundUI_Table:Form id="frmStep1" runat="server">
		
			<asp:TableRow id="trSelectPO" runat="server">
			
				<asp:TableCell id="tdSelectPO" runat="server">
				
					<ReboundItemSearch:PurchaseOrders id="ctlSelectPO" runat="server" />
					
					<asp:Panel id="pnlLines" runat="server" CssClass="itemSearch invisible">
						<h5><%=Functions.GetGlobalResource("Misc", "PurchaseOrders")%></h5>
						<asp:Panel id="pnlLinesError" runat="server" CssClass="itemSearchError invisible"><asp:Label id="lblLinesError" runat="server" /></asp:Panel>
						<asp:Panel id="pnlLinesLoading" runat="server" CssClass="loading invisible"><%=Functions.GetGlobalResource("Misc", "Loading")%></asp:Panel>
						<asp:Panel id="pnlLinesNoneFound" runat="server" CssClass="noneFound invisible"><%=Functions.GetGlobalResource("NotFound", "Generic")%></asp:Panel>
						<ReboundUI:FlexiDataTable ID="tblLines" runat="server" AllowMultipleSelection="true" />
					</asp:Panel>
					
				</asp:TableCell>
				
			</asp:TableRow>
			
		</ReboundUI_Table:Form>

		<!-- Step 2 ------------------------------------------------------------------->
		<ReboundUI_Table:Form id="frmStep2" runat="server">
		
            <ReboundUI_Form:FormField id="ctlPurchaseOrder" runat="server" FieldID="lblPurchaseOrder" ResourceTitle="PurchaseOrderNo" >
	            <Field><asp:Label ID="lblPurchaseOrder" runat="server" /></Field>
            </ReboundUI_Form:FormField>
            
            <ReboundUI_Form:FormField id="ctlSupplier" runat="server" FieldID="lblSupplier" ResourceTitle="Supplier">
	            <Field><asp:Label ID="lblSupplier" runat="server" /></Field>
            </ReboundUI_Form:FormField>
            
            <ReboundUI_Form:FormField id="ctlWarehouse" runat="server" FieldID="ddlWarehouse" ResourceTitle="Warehouse" IsRequiredField="true">
                <Field><ReboundDropDown:Warehouse ID="ddlWarehouse" runat="server" IncludeVirtual="true" /></Field>
            </ReboundUI_Form:FormField>
            
            <ReboundUI_Form:FormField id="ctlShipVia" runat="server" FieldID="ddlShipVia" ResourceTitle="ShipViaNo" IsRequiredField="true">
                <Field><ReboundDropDown:ShipMethod ID="ddlShipVia" runat="server" /></Field>
            </ReboundUI_Form:FormField>
            
            <ReboundUI_Form:FormField id="ctlCurrency" runat="server" FieldID="ddlCurrency" ResourceTitle="Currency" IsRequiredField="true">
                <Field><ReboundDropDown:Currency ID="ddlCurrency" runat="server" /></Field>
            </ReboundUI_Form:FormField>
            
            <ReboundUI_Form:FormField id="ctlAirWayBill" runat="server" FieldID="txtAirWayBill" ResourceTitle="AirWayBill">
	            <Field><ReboundUI:ReboundTextBox ID="txtAirWayBill" runat="server" Width="200" /></Field>
            </ReboundUI_Form:FormField>
            
            <ReboundUI_Form:FormField id="ctlReference" runat="server" FieldID="txtReference" ResourceTitle="Reference" IsRequiredField="true">
	            <Field><ReboundUI:ReboundTextBox ID="txtReference" runat="server" Width="200" /></Field>
            </ReboundUI_Form:FormField>
            
            <ReboundUI_Form:FormField id="ctlReceivingNotes" runat="server" FieldID="txtReceivingNotes" ResourceTitle="ReceivingNotes">
	            <Field><ReboundUI:ReboundTextBox ID="txtReceivingNotes" runat="server" Width="400" TextMode="MultiLine" Rows="2" /></Field>
            </ReboundUI_Form:FormField>
            
            <ReboundUI_Form:FormField id="ctlReceivedBy" runat="server" FieldID="ddlReceivedBy" ResourceTitle="ReceivedBy" IsRequiredField="true">
                <Field><ReboundDropDown:Employee ID="ddlReceivedBy" runat="server" /></Field>
            </ReboundUI_Form:FormField>
            
            <ReboundUI_Form:FormField id="ctlDateReceived" runat="server" FieldID="txtDateReceived" ResourceTitle="ReceivedDate" IsRequiredField="true">
	            <Field><ReboundUI:ReboundTextBox ID="txtDateReceived" runat="server" Width="140" /><ReboundUI:Calendar ID="calDateReceived" runat="server" RelatedTextBoxID="txtDateReceived" /></Field>
            </ReboundUI_Form:FormField>
            
        </ReboundUI_Table:Form>
		
		<!-- Step 3 ------------------------------------------------------------------->
		<ReboundUI_Table:Form id="frmStep3" runat="server">
		
			<ReboundUI_Form:FormField id="ctlSendMail" runat="server" FieldID="chkSendMail" ResourceTitle="ShouldMailBeSent">
				<Field><ReboundUI:ImageCheckBox ID="chkSendMail" runat="server" Enabled="true" Checked="true" /></Field>
			</ReboundUI_Form:FormField>
			
			<ReboundFormFieldCollection:SendMailMessage id="ctlSendMailMessage" runat="server" />
			
		</ReboundUI_Table:Form>

	</Content>
</ReboundUI_Form:DesignBase>
