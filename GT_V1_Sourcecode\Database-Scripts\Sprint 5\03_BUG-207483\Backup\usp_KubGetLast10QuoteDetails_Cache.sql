
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [dbo].[usp_KubGetLast10QuoteDetails_Cache]               
@PartNo NVARCHAR(100)=null ,              
@ClientID int=null   ,        
@CustomerReqId NVARCHAR(MAX)=null          
AS        
/*      
 * Action: Created  By: <PERSON><PERSON><PERSON><PERSON>  Date:03/08/2023  Comment: Add new for 10 quote data cache.      
 */                  
BEGIN                  
SET NOCOUNT ON;       
IF((SELECT COUNT(1) FROM tb_KubGetLast10QuoteDetails_Cache       
WHERE ClientNo=@ClientID AND Part=@PartNo AND CustomerReqId=@CustomerReqId AND DLUP>DATEAdd(DAY,-3,GETDATE()))=0)      
BEGIN         
DELETE FROM tb_KubGetLast10QuoteDetails_Cache       
WHERE ClientNo=@ClientID AND Part=@PartNo AND CustomerReqId=@CustomerReqId      
INSERT INTO tb_KubGetLast10QuoteDetails_Cache        
SELECT top(10)      
@PartNo,@ClientID,@CustomerReqId,      
Q.QuoteId QuoteID, Q.QuoteNumber QuoteNumber, format(Q.DateQuoted,'dd/MM/yyyy') as QuoteDate ,QL.Quantity Quantity,             
CASE WHEN QL.Price IS NULL THEN '0.00' ELSE CONCAT (CAST(FORMAT(CONVERT(DECIMAL(16,2),    
dbo.ufn_convert_currency_value(QL.Price,Q.CurrencyNo,c.CurrencyId,GETDATE())),'N') as varchar(100)) ,' ' , c.CurrencyCode) END  as UnitPrice,            
--CASE WHEN ts.Price IS NULL THEN '0.00' ELSE CONCAT (CAST(FORMAT(CONVERT(DECIMAL(16,2),ts.Price),'N') as varchar(20)),' ' , c.CurrencyCode) END as BuyPrice,             
CASE WHEN ts.Price IS NULL THEN '0.00' ELSE CONCAT (CAST(FORMAT(CONVERT(DECIMAL(16,2),  
dbo.ufn_convert_currency_value(ts.price,ts.CurrencyNo,c.CurrencyId,GETDATE())),'N') as varchar(20)),' ' , c.CurrencyCode) END as BuyPrice,             
--CASE WHEN (QL.Price-ts.Price)*QL.Quantity  IS NULL THEN '0.00' ELSE CONCAT (CAST(FORMAT(CONVERT(DECIMAL(16,2),(QL.Price-ts.Price)*QL.Quantity),'N') as varchar(20)) ,' ' , c.CurrencyCode)  END as Profit               
CASE WHEN (QL.Price-ts.Price)*QL.Quantity  IS NULL THEN '0.00'   
 ELSE CONCAT (  
 CAST(  
 FORMAT  
 (CONVERT(DECIMAL(16,2)  
  ,(dbo.ufn_convert_currency_value(CONVERT(DECIMAL(16,2),QL.Price),Q.CurrencyNo,c.CurrencyId,GETDATE())  
  -dbo.ufn_convert_currency_value(CONVERT(DECIMAL(16,2),ts.Price),ts.CurrencyNo,c.CurrencyId,GETDATE())))*QL.Quantity  
 ,'N')   
 as varchar(20)  
 ) ,' ' , c.CurrencyCode)  END as Profit               
,GETDATE()      
FROM  tbQuote Q  join tbQuoteLine QL on QL.QuoteNo=Q.QuoteId           
left join tbsourcingresult ts on ts.SourcingResultId=SourcingResultNo          
join tbCustomerRequirement cr on cr.CustomerRequirementId=ts.CustomerRequirementNo and Q.CompanyNo=cr.CompanyNo        
LEFT JOIN tbclient cl ON cr.clientno=cl.clientId      
left join tbCurrency c on cl.currencyNo=c.CurrencyId     
      
WHERE Q.ClientNo=@ClientID         
AND cr.ClientNo=@ClientID        
AND QL.FullPart=@PartNo        
AND Q.CompanyNo=(select top(1) CompanyNo from tbCustomerRequirement         
where CustomerRequirementId=@CustomerReqId)         
ORDER BY Q.QuoteId DESC              
END          
END 
GO


