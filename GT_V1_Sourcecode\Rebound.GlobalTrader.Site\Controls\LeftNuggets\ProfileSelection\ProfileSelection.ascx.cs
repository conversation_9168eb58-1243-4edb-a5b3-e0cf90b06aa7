using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site;

namespace Rebound.GlobalTrader.Site.Controls.LeftNuggets {
	public partial class ProfileSelection : Selection {

		protected override void OnInit(EventArgs e) {
			SetLeftNuggetType("ProfileSelection");
			base.OnInit(e);
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.LeftNuggets.Base", ctlDesignBase.ClientID);
			AddItems();
		}

		private void AddItems() {
			HtmlControl ul = new HtmlGenericControl("ul");
			ul.Controls.Add(AddItem("Profile_Edit"));
			ul.Controls.Add(AddItem("Profile_MailMessages"));
			ul.Controls.Add(AddItem("Profile_ToDo"));
			ul.Controls.Add(AddItem("Profile_MailMessageGroups"));
			_plhItems.Controls.Add(ul);
		}

	}
}