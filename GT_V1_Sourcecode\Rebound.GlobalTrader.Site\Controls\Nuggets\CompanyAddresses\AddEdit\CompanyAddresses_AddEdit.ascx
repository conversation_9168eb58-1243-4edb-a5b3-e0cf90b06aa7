<%--
Marker     Changed by      Date         Remarks
[001]      Vinay           11/06/2012   This need to Add Incoterms field in company section
[002]      Vinay           13/03/2014   EMS Ref No: 104
[003]		Ravi			30-03-2023	[RP-1224] Add VAT no,  Company No, EORI number and Telephone number on edit screen (Changes during OGEL release) 
--%>
<%@ Control Language="C#" CodeBehind="CompanyAddresses_AddEdit.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.CompanyAddresses_AddEdit" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<%@ Register TagPrefix="cc1" Namespace="Rebound.GlobalTrader.Site" Assembly="Rebound.GlobalTrader.Site" %>
<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="true" ShowQuickHelp="false">
	<Links>
		<ReboundUI:IconButton ID="ibtnSave" runat="server" IconGroup="Nugget" IconTitleResource="Save" IconCSSType="Save" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
		<ReboundUI:IconButton ID="ibtnCancel" runat="server" IconGroup="Nugget" IconTitleResource="Cancel" IconCSSType="Cancel" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
	</Links>
	<Explanation><%=Functions.GetGlobalResource("FormExplanations", "CompanyAddresses_AddEdit")%></Explanation>
	<Content>
		<ReboundUI_Table:Form id="frm" runat="server">
			<ReboundUI_Form:FormField id="ctlName" runat="server" FieldID="lblName" ResourceTitle="Company">
				<Field><asp:Label ID="lblName" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			<ReboundFormFieldCollection:Address id="ctlAddress" runat="server" AllowNonEntry="false" />
			<ReboundUI_Form:FormField id="ctlShipVia" runat="server" FieldID="ddlShipViaNo" ResourceTitle="ShipVia">
				<Field><ReboundDropDown:ShipMethod ID="ddlShipViaNo" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			<ReboundUI_Form:FormField id="ctlShipViaAccount" runat="server" FieldID="txtShipViaAccount" ResourceTitle="ShipViaAccount">
				<Field><ReboundUI:ReboundTextBox ID="txtShipViaAccount" runat="server" Width="200" /></Field>
			</ReboundUI_Form:FormField>
			<ReboundUI_Form:FormField id="ctlTax" runat="server" FieldID="ddlTax" ResourceTitle="Tax" IsRequiredField="true" >
				<Field><ReboundDropDown:Tax ID="ddlTax" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			<ReboundUI_Form:FormField id="ctlTax_Label" runat="server" FieldID="lblTax_Label" ResourceTitle="Tax" >
				<Field><asp:Label ID="lblTax_Label" runat="server" /></Field>
			</ReboundUI_Form:FormField>

			

				<ReboundUI_Form:FormField id="ctlLabelType" runat="server" FieldID="txtLabelType" ResourceTitle="LabelType" IsRequiredField="true" >
				<Field><ReboundDropDown:LabelType ID="txtLabelType" runat="server" /></Field>
			</ReboundUI_Form:FormField>

			<%--[002] code start--%>
			<ReboundUI_Form:FormField id="ctlVatNo" runat="server" FieldID="txtVatNo" ResourceTitle="VATNo">
				<Field><ReboundUI:ReboundTextBox ID="txtVatNo" runat="server" Width="200" MaxLength="100" /></Field>
			</ReboundUI_Form:FormField>
			<ReboundUI_Form:FormField id="ctlVatNo_Label" runat="server" FieldID="lblVatNo_Label" ResourceTitle="VATNo">
				<Field><asp:Label ID="lblVatNo_Label" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			<%--[002] code end--%>
			
			 <%--[001] code start--%>
			<ReboundUI_Form:FormField id="ctlIncoterm" runat="server" FieldID="ddlIncoterm" ResourceTitle="Incoterm" >
				<Field><ReboundDropDown:Incoterm ID="ddlIncoterm" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			<%--[001] code end--%>
			<ReboundUI_Form:FormField id="ctlNotes" runat="server" FieldID="txtNotes" ResourceTitle="AddressNotes">
				<Field><ReboundUI:ReboundTextBox ID="txtNotes" TextMode="multiline" Rows="2" Width="450" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			<ReboundUI_Form:FormField id="ctlShippingNotes" runat="server" FieldID="txtShippingNotes" ResourceTitle="AddressShippingNotes">
				<Field><ReboundUI:ReboundTextBox ID="txtShippingNotes" TextMode="multiline" Rows="2" Width="450" runat="server" /></Field>
			</ReboundUI_Form:FormField>

            <ReboundUI_Form:FormField id="ctlRecieveNotes" runat="server" FieldID="txtRecieveNote" ResourceTitle="RecievingNotes">
				<Field><ReboundUI:ReboundTextBox ID="txtRecieveNote" TextMode="multiline" Rows="2" Width="450" runat="server" /></Field>
			</ReboundUI_Form:FormField>

            <ReboundUI_Form:FormField id="ctlRegion" runat="server" FieldID="ddlRegion" ResourceTitle="Region" >
				<Field><ReboundDropDown:Region ID="ddlRegion" runat="server" NoValue_Value="" InitialValue=""/></Field>
			</ReboundUI_Form:FormField>

            <ReboundUI_Form:FormField id="ctlDivisionHeader" runat="server" FieldID="ddlDivisionHeader" ResourceTitle="DefaultDivisionHeader" >
				<Field><ReboundDropDown:Division ID="ddlDivisionHeader" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			
		</ReboundUI_Table:Form>
	</Content>
</ReboundUI_Form:DesignBase>
