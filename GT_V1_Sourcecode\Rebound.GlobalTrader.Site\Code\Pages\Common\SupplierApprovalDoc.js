Type.registerNamespace("Rebound.GlobalTrader.Site.Pages");Rebound.GlobalTrader.Site.Pages.SupplierApprovalDoc=function(n){Rebound.GlobalTrader.Site.Pages.SupplierApprovalDoc.initializeBase(this,[n]);this._intIHSPartID=0};Rebound.GlobalTrader.Site.Pages.SupplierApprovalDoc.prototype={get_ctlPageTitle:function(){return this._ctlPageTitle},set_ctlPageTitle:function(n){this._ctlPageTitle!==n&&(this._ctlPageTitle=n)},get_ctlMainInfo:function(){return this._ctlMainInfo},set_ctlMainInfo:function(n){this._ctlMainInfo!==n&&(this._ctlMainInfo=n)},get_ctlManufacturerPDFDragDrop:function(){return this._ctlManufacturerPDFDragDrop},set_ctlManufacturerPDFDragDrop:function(n){this._ctlManufacturerPDFDragDrop!==n&&(this._ctlManufacturerPDFDragDrop=n)},get_ctlManufacturerPDFDragDropImage:function(){return this._ctlManufacturerPDFDragDropImage},set_ctlManufacturerPDFDragDropImage:function(n){this._ctlManufacturerPDFDragDropImage!==n&&(this._ctlManufacturerPDFDragDropImage=n)},initialize:function(){Rebound.GlobalTrader.Site.Pages.SupplierApprovalDoc.callBaseMethod(this,"initialize")},goInit:function(){this._ctlMainInfo&&this._ctlMainInfo.addSaveEditComplete(Function.createDelegate(this,this.ctlMainInfo_EditComplete));this._ctlManufacturerPDFDragDrop&&this._ctlManufacturerPDFDragDrop.getData();Rebound.GlobalTrader.Site.Pages.SupplierApprovalDoc.callBaseMethod(this,"goInit")},dispose:function(){this.isDisposed||(this._ctlPageTitle&&this._ctlPageTitle.dispose(),this._ctlMainInfo&&this._ctlMainInfo.dispose(),this._ctlManufacturerEXCELDragDrop&&this._ctlManufacturerEXCELDragDrop.dispose(),this._intIHSPartID=null,this._ctlPageTitle=null,this._ctlMainInfo=null,this._ctlManufacturerPDF=null,Rebound.GlobalTrader.Site.Pages.SupplierApprovalDoc.callBaseMethod(this,"dispose"))},ctlMainInfo_EditComplete:function(){}};Rebound.GlobalTrader.Site.Pages.SupplierApprovalDoc.registerClass("Rebound.GlobalTrader.Site.Pages.SupplierApprovalDoc",Rebound.GlobalTrader.Site.Pages.Content,Sys.IDisposable);