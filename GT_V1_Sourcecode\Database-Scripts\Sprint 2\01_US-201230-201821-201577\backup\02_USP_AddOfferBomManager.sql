SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER Procedure [dbo].[USP_AddOfferBomManager]                        
(                                
@BOMManagerNo int,                          
@SourcingId int,                          
@UpdatedBy int,                          
@SourceType int,                    
@OfferSource varchar(max) null                    
)                                
WITH RECOMPILE                                
as                                
begin                                
                                
--select * from tbAutoSource                                
--select * from tbSalesXMatch where BOMManagerID=219                                
--select * from tbAPIOffers where BOMManagerID=219                                
--select * from tbEMSOffers where BOMManagerNo=219                                
                                
--select * from tbAPIOffers                                
--sp_tables '%api%'                                
                                
--select * from tbAPI                                
                                
--insert into tbAPI value                                
--(219,BAV99,BAV99,4824,95+,)                                
                                
--insert into tbAPI                                
--select  BOMManagerNo,FullPart,Part,ManufacturerNo,DateCode,ProductNo,PackageNo,Quantity,Price,OriginalEntryDate,<PERSON>man,                                
--<PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,UpdatedBy,DLUP,OfferStatusNo,OfferStatusChangeDate,OfferStatusChangeLoginNo,SupplierName,Notes,ManufacturerName,                                
--ProductName,PackageName,ClientNo,SPQ,LeadTime,ROHSStatus,FactorySealed,MSL,IPOBOMNo,ManufacturerName,DateCode,PackageNo,ProductNo,SupplierMOQ,SupplierTotalQSA,                                
--SupplierLTB,Notes,ishub,2,1,1,1,1                                
--from tbEMSOffers                                 
                                
--drop table #tempSourcingData                                
                                
   begin tran                              
begin try                              
                              
Create table #tempSourcingData(                                
BOMManagerNo int null,                                
OfferId int null,                                
FullPart nvarchar(30)  COLLATE Latin1_General_CI_AS null,                                
Part  nvarchar(30) null,                                
ManufacturerNo int null,                                
DateCode nvarchar(5)  null,                                
ProductNo int null ,                                
PackageNo int null,                                
Quantity int null,                                
Price float null,                                
OriginalEntryDate datetime null,                                
Salesman int  null,                                
SupplierNo int null,                                
CurrencyNo int  null,                                
ROHS tinyint null,                                
UpdatedBy int null,                                
DLUP datetime null,                                
OfferStatusNo int null,                                
OfferStatusChangeDate  datetime null,                                
OfferStatusChangeLoginNo int null,                                
ManufacturerCode  nvarchar(5) null,                                
ProductName nvarchar(100) null,                                
CurrencyCode nvarchar(5)  null,                                
CurrencyDescription   nvarchar(30)  null,                                
SupplierName nvarchar(128) null,                                
ManufacturerName nvarchar(50) null,                                
SupplierEmail nvarchar(128)  null,                                
SalesmanName nvarchar(128)  null,                                
OfferStatusChangeEmployeeName nvarchar(128)  null,                                
PackageName  nvarchar(60)  null,                                
Notes nvarchar(800)  null,                                
ClientNo int null,                                
ClientId int null,                   
ClientName nvarchar(128) null,                                
ClientDataVisibleToOthers bit null,                            SupplierType nvarchar(250) null,                                
ClientCode nvarchar(10) null,                                
SPQ nvarchar(10)  null,                      
LeadTime nvarchar(50)  null,                                
ROHSStatus nvarchar(50)  null,                                
FactorySealed nvarchar(50)  null,                                
MSL nvarchar(250)  null,                       
IPOBOMNo int null,                                
SupplierTotalQSA  nvarchar(20)  null,                                
SupplierLTB nvarchar(50)  null,                                
SupplierMOQ nvarchar(50)  null,                                
--ishub int null,                                
SupplierMessage nvarchar(400) null,                                
SourcingType varchar(100),                                
customerrequirementid int,                                
Risk varchar(100),                                
islock  bit,                                
bomstatus bit     
,POHubCompanyNo int  
)                                
                      
Declare @DeliveryDate datetime  
                                
 create table #Parttemp(PartNames varchar(100) COLLATE Latin1_General_CI_AS, CustomerRequirementId int, BomManagerNo int , DeliveryDate datetime)                                
                                   
 insert into #Parttemp                                 
 select cus.FullPart, CustomerRequirementId,BOMManagerNo, DatePromised from dbo.tbCustomerRequirement cus where cus.BOMManagerNo = @BOMManagerNo                          
            
select top 1 @DeliveryDate =DeliveryDate from #Parttemp  
  
if(@SourceType = 1)                          
begin                          
--for EMS offers                                
insert into #tempSourcingData                                 
select a.BOMManagerNo,EMSOfferId ,FullPart,Part,                
isnull(ManufacturerNo,(select top 1 ManufacturerID from tbManufacturer where ManufacturerName = ManufacturerName)) as ManufacturerNo,                
DateCode,ProductNo,PackageNo,Quantity,Price,OriginalEntryDate,Salesman,                                
SupplierNo,CurrencyNo,ROHS,UpdatedBy,DLUP,OfferStatusNo,OfferStatusChangeDate,OfferStatusChangeLoginNo, ManufacturerCode,                                
ProductName,CurrencyCode,CurrencyDescription,SupplierName,ManufacturerName,SupplierEmail,SalesmanName,OfferStatusChangeEmployeeName,                                
PackageName,Notes,ClientNo,ClientId,ClientName,ClientDataVisibleToOthers,SupplierType,ClientCode,SPQ,LeadTime,ROHSStatus,FactorySealed,                                
MSL,IPOBOMNo,SupplierTotalQSA, SupplierLTB,SupplierMOQ,SupplierMessage,'Offers',a.CustomerRequirementId,null,null,null ,a.POHubCompanyNo  
from tbEMSOffers a --join #Parttemp prt on a.fullpart = prt.partNames                                
where --BOMManagerNo = @BOMManagerNo                                
EMSOfferId = @SourcingId                          
--and                                 
--DLUP between DATEADD(month,-6,getdate()) and GETDATE()                                
end                                
                             
                             
--for APi offers                             
if(@SourceType = 2)                          
begin                    
 if(@OfferSource = 'Manual Offer')                    
  begin                    
   insert into #tempSourcingData                                 
   select a.BOMManagerNo,a.APIID,a.FullPart,a.Part,mf.ManufacturerId,a.DateCode,a.ProductNo,a.PackageNo,a.Quantity,a.Price,OriginalEntryDate,a.Salesman,                                
   SupplierNo,a.CurrencyNo,a.ROHS,null,a.DLUP, OfferStatusNo,OfferStatusChangeDate,OfferStatusChangeLoginNo, a.ManufacturerCode,ProductName,                                
   cr.CurrencyCode,cr.CurrencyDescription,SupplierName,mf.ManufacturerName,'SupplierEmail','SalesmanName','OfferStatusChangeEmployeeName',                                
   PackageName,'Notes',a.ClientNo,cl.ClientId,cl.ClientName,0,0,cl.ClientCode,SPQ,LeadTime,ROHSStatus,a.FactorySealed,a.MSL,IPOBOMNo,SupplierTotalQSA,                                
   SupplierLTB,SupplierMOQ,'SupplierMessage','API-Manual',                                
   b.customerrequirementid,null,null,null    ,  
   (select top 1 CompanyId from tbCompany where CompanyName = 'Future Electronics' and ClientNo = 114 and IsSupplier = 1 and POApproved = 1 ) as POHubCompanyNo                            
   from tbAPIOffers a join tbClient cl on a.ClientNo = cl.ClientId                                
   --join #Parttemp prt on a.fullpart=prt.partNames                                 
   join tbCustomerRequirement b  on  b.BOMManagerNo = @BOMManagerNo and a.FullPart=b.FullPart                                
   join tbCurrency cr on a.CurrencyNo = cr.CurrencyId                                
   join tbManufacturer mf on a.ManufacturerNo =mf.ManufacturerId                                
   where b.BOMManagerNo = @BOMManagerNo and a.APIID = @SourcingId                             
   --and a.DLUP between DATEADD(month,-6,getdate()) and GETDATE()                     
  end                    
  if(@OfferSource = 'Future Electronics')                    
  begin                    
   Declare @Package nvarchar(30), @ManufacturerName nvarchar(max), @DateCode int, @ROHS nvarchar(10)                    
                       
   (SELECT @Package=[packageType],@ManufacturerName = [manufacturerName], @DateCode = [dateCode], @ROHS = [rohs]  FROM (                    
    SELECT [name],[value],[SupplierAPINo]                                        
    FROM tbSupplierAPIPartAttributes where SupplierAPINo = @SourcingId) PartAttributes          PIVOT (min([value])                                     
       FOR [name] IN                     
       ([packageType], [manufacturerName], [dateCode], [rohs], [leadFree], [description (en)], ECCN)                                        
    ) AS PivotTable)                     
                    
   insert into #tempSourcingData                                 
   select @BOMManagerNo,tbs.SupplierAPIId                    
     ,(select FullPart from tbcustomerRequirement where part = tbs.PartNumber and BOMManagerNo = @BOMManagerNo)                    
     ,tbs.PartNumber                    
       ,(select top 1 mf.ManufacturerId from tbManufacturer mf where mf.manufacturerName = @ManufacturerName)                    
       ,@DateCode                    
       ,null --Product                    
       ,(select top 1 PackageId from tbPackage where PackageName = @Package)                    
       ,tbs.QuantityAvailable                    
       ,(select top 1 (UnitPrice) from tbSupplierAPIPricing where SupplierAPINo = tbs.SupplierAPIID order by QuantityFrom)                    
       ,tbs.DLUP,0 --Salesman                    
       ,(select top 1 CompanyId from tbCompany where CompanyName = tbs.DatasourceName and ClientNo = 114)                    
       ,(select top 1 CurrencyId from tbCurrency where CurrencyCode = tbs.CurrencyCode and ClientNo = 114)                    
       ,null--@ROHS                    
       ,null,tbs.DLUP, null --OfferStatusNo                    
       ,null --OfferStatusChangeDate                    
       ,null --OfferStatusChangeLoginNo                    
       ,null --ManufacturerCode       
       ,null --ProductName                    
       ,tbs.CurrencyCode,null --cr.CurrencyDescription                    
       ,tbs.DatasourceName                    
       ,@ManufacturerName                    
       ,'SupplierEmail','SalesmanName','OfferStatusChangeEmployeeName'                    
       ,@Package,'Notes',tbs.ClientNo,cl.ClientId,cl.ClientName,0,0,cl.ClientCode                    
       ,null --SPQ                    
       ,CONCAT(tbs.FactoryLeadTime,' ', tbs.FactoryLeadTimeUnits)                    
       ,@ROHS                    
       ,null --FactorySealed                    
       ,null --MSL                    
       ,null --IPOBOMNo                    
       ,tbs.QuantityAvailable                    
       ,null -- SupplierLTB                    
       ,tbs.QuantityMinimum                    
       ,'SupplierMessage'                    
       ,'API-FE'                    
       ,(select CustomerRequirementID from tbcustomerRequirement where part = tbs.PartNumber and BOMManagerNo = @BOMManagerNo)                    
       ,null,null,null,   
    (select top 1 CompanyId from tbCompany where CompanyName = 'Future Electronics' and ClientNo = 114 and IsSupplier = 1 and POApproved = 1 ) as POHubCompanyNo                                            
   from tbSupplierAPI tbs                    
   --LEFT JOIN (SELECT * FROM (SELECT [name],[value],[SupplierAPINo]                                        
   --   FROM tbSupplierAPIPartAttributes where SupplierAPINo = 10) PartAttributes                    
   --   PIVOT (min([value])                                     
   --     FOR [name] IN                     
   --     ([packageType], [manufacturerName], [dateCode], [rohs], [leadFree], [description (en)], ECCN)                                        
   --   ) AS PivotTable) partid on partid.SupplierAPINo = tbs.SupplierAPIID                   
   --LEFT JOIN tbCurrency cr on cr.CurrencyCode = tbs.currencyCode                    
   join tbClient cl on tbs.ClientNo = cl.ClientId                    
   where tbs.SupplierAPIId = @SourcingId                    
   --and a.DLUP between DATEADD(month,-6,getdate()) and GETDATE()                     
  end                     
end                    
                    
                              
                    
--for Xmatch               
if(@SourceType = 3)                          
begin                          
--print 'check1'                  
insert into #tempSourcingData                   
select a.BOMManagerNo,a.SalesXMatchID ,a.FullPart,a.Part,a.ManufacturerNo,a.DateCode,a.ProductNo,a.PackageNo,a.Quantity,a.UnitPrice,--a.Price,                  
OriginalEntryDate,a.Salesman,                                
SupplierNo,a.CurrencyNo,a.ROHS,null,a.DLUP, OfferStatusNo,OfferStatusChangeDate,OfferStatusChangeLoginNo, 0 ,-- mf.ManufacturerCode,                  
ProductName,                                
0,--cr.CurrencyCode,                  
'',--cr.CurrencyDescription,                  
'SupplierName' SupplierName,'',--mf.ManufacturerName,                  
'SupplierEmail','SalesmanName','OfferStatusChangeEmployeeName',                                
PackageName,'Notes',a.ClientNo,cl.ClientId,cl.ClientName,0,0,cl.ClientCode,SPQ,LeadTime,ROHSStatus,a.FactorySealed,a.MSL,IPOBOMNo,SupplierTotalQSA,                                
SupplierLTB,SupplierMOQ,'SupplierMessage','XMatch / Sourcing',                                
a.customerrequirementid,null,null,null ,a.supplierNo                                
from tbSalesXMatch a join tbClient cl on a.ClientNo = cl.ClientId                                
--join #Parttemp prt on a.fullpart=prt.partNames                                 
join tbCustomerRequirement b  on b.BOMManagerNo=@BOMManagerNo and a.FullPart=b.FullPart                                
--join tbCurrency cr on a.CurrencyNo = cr.CurrencyId             
--join tbManufacturer mf on a.ManufacturerNo =mf.ManufacturerId                                
where b.BOMManagerNo = @BOMManagerNo                                
and a.SalesXMatchID = @SourcingId                          
--and a.DLUP between DATEADD(month,-6,getdate()) and GETDATE()                   
--select * from #tempSourcingData                  
end                                          
                  
                                
----select clientno,bommanagerid,Fullpart,price from #tempSourcingData                                
--insert into #tempSourcingData (clientno,bommanagerno,Fullpart,price,SupplierMessage,SourcingType,DLUP)                                 
--select ClientNo,BOMManagerNo,FullPart,POPrice,'','XMatch / Sourcing',InvoiceDate from tbSalesXMatch where BOMManagerNo=@BOMManagerNo                                
--and InvoiceDate between DATEADD(month,-6,getdate()) and GETDATE()                                
                                
--select * from #tempSourcingData                                
                             
--drop table #tempRawData                                
select OfferId, SupplierName,                                
Fullpart,                                
ManufacturerName,                                
cast(price as decimal(30,5)) as Price,                 
cast(0.0 as decimal(30,5)) as Resale,                                
cast(0.0 as decimal(30,5)) as Profit,                                
cast(0.0 as decimal(30,5)) as Margin,                                
OriginalEntryDate,                                
supplierMOQ,                                
SPQ,                                
Quantity as ADJQty,                                
cast(0.0 as decimal(30,5)) as StockQty,                                
cast(0.0 as decimal(30,5)) as Excess,                                
cast(0.0 as decimal(30,5)) as [Value],                                
'' as DC,                                
0 as LTDays,                                
DLUP,                                 
SourcingType ,                                
ProductNo,PackageNo,ManufacturerNo, rohs, BOMManagerNo,ClientCode,ClientNo,Salesman,                                
customerrequirementid,Risk,Notes,islock,bomstatus,          SupplierNo ,CurrencyNo   ,                      
Dense_rank() over (partition by fullpart order by DLUP desc ,price desc) as RowNumber ,                                
ROW_NUMBER() OVER (partition by fullpart order by DLUP desc,price desc) AS RowID      
,POHubCompanyNo  
into #tempRawData                                
from #tempSourcingData order by Fullpart ,RowNumber                                
                      
                  
                          
--select * from #tempRawData                                
                                
--drop table #tempRawData                                
--drop table #tempLowestPrice           
--select * from  #tempRawData                                
select * into #tempLowestPrice from #tempRawData where rownumber = 1 and rowid =1                                 
                             
--select * from #tempLowestPrice                                
              
--select top 100 * from  [BorisGlobalTraderImports].dbo.tbOffer where spq is not null                                 
                                
                  
 --     select * from #tempSourcingData                     
 --select * from #tempRawData                  
                  
update #tempLowestPrice set  spq = case when isnull(spq,'')='' then 0 else case when isnumeric(spq)=1 then convert(int, convert(float,spq)) else 0 end  end     --   where isnull(spq,'')=''                             
                          
                             
update #tempLowestPrice                                 
set spq = case when isnull(spq,'')='' then 0 else spq end                                
, SupplierMOQ = case when isnull(SupplierMOQ,'')='' then 0 else convert(int, convert(float,SupplierMOQ)) end                                
                          
                                
-- ADJQTY                                
update a                                 
set ADJQty =                                
case when TRY_PARSE(a.spq as int) is not null then                                
 case when a.spq>0 then  b.Quantity else b.Quantity end                                
 else 0 end ,                       
 a.customerrequirementid = b.CustomerRequirementId                                
from #tempLowestPrice a join tbCustomerRequirement b on a.customerrequirementid= b.customerrequirementid      
--and a.FullPart =b.FullPart      
where b.BOMManagerNo = @BOMManagerNo                                
                                
                                
--profit                                
update  a                                 
set Profit =                               
--cast(case when a.price>0                                 
--   then                               
   (cast(a.Resale as decimal(30,5)) - cast(a.Price   as decimal(30,5)))* ADJQty                            
   --else 0 end as decimal(30,5))                                
from #tempLowestPrice a join tbCustomerRequirement b on a.customerrequirementid= b.customerrequirementid       
--and a.FullPart =b.FullPart      
where b.BOMManagerNo = @BOMManagerNo                                
                
                                
                                
                                
--Margin                                
update a                                 
set Margin =                              
case when a.Resale >0 then                              
cast(cast(100 as decimal(30,5))-((cast(100  as decimal(30,5))/cast(a.Resale  as decimal(30,5))* cast(a.Price  as decimal(30,5)))) as decimal(30,5))                                 
else 0 end                              
from #tempLowestPrice a join tbCustomerRequirement b on a.customerrequirementid= b.customerrequirementid       
--and a.FullPart =b.FullPart       
where b.BOMManagerNo = @BOMManagerNo                              
                                
--Excess                                
update a                                 
set Excess=                              
case when b.Quantity >0 then                              
cast((                                
cast(100 as decimal(30,5))                                
/cast(b.Quantity as decimal(30,5))                                
* ADJQty)                                 
/cast(100 as decimal(30,5)) as decimal(30,5))                                
else 0 end                              
from #tempLowestPrice a join tbCustomerRequirement b on a.customerrequirementid= b.customerrequirementid      
--and a.FullPart =b.FullPart       
where b.BOMManagerNo = @BOMManagerNo                                
                                
--select * from #tempLowestPrice                                
                              
-- Value                                
update #tempLowestPrice                                 
set [Value]= ADJQty * cast(Resale as decimal(30,5))                                
                                

update a set a.suppliername = b.companyname
from #tempLowestPrice  a join tbcompany b on a.supplierno = b.companyid
where isnull(a.suppliername,'')=''

                                
--select * from #tempLowestPrice                                 
--select * from #tempRawData where rownumber = 1 and rowid =1                                 
                           
--select a.SupplierName,a.FullPart,a.ManufacturerName,a.Price,              
--cast(case when a.price>0 then (cast(b.TargetSellPrice as decimal(12,2)) - cast(a.Resale  as decimal(12,2)))* cast(a.ADJQty as decimal(12,2))                                 
--else 0 end as decimal(12,2)) as 'Profit',                                
--cast(cast(100 as decimal(12,2))-(cast(100  as decimal(12,2))/cast(b.TargetSellPrice  as decimal(12,2))* cast(a.resale  as decimal(12,2))) as decimal(12,2)) as 'Margin',                                
--a.DLUP as 'Date', a.sourcingType,                                
--a.SupplierMOQ,                                
--TRY_PARSE(a.spq as int) as 'SPQ',                                
--case when TRY_PARSE(a.spq as int) is not null then                                
-- case when a.spq>0 then  a.spq else b.Quantity end                                
-- else 0 end                                 
--as 'ADJ Qty',                                
--cast((cast(100 as decimal(12,2))/cast(b.Quantity as decimal(12,2))* cast(case when cast(a.spq as decimal(12,2))>0.0                                 
--then cast( a.spq as decimal(12,2)) else cast(b.Quantity as decimal(12,2)) end as decimal(12,2))/cast(100 as decimal(12,2))) as decimal(12,2))                         
--as 'Excess',                                
--(case when a.spq>0 then  a.spq else b.Quantity end)* a.Resale as 'Value',                                
--a.StockQty as 'Stock Qty',                                
--a.DC as 'D/C',                                
--a.LTDays                                
--from #tempLowestPrice a join tbCustomerRequirement b on a.FullPart =b.FullPart where b.BOMManagerNo = 219                                
--select * from #tempLowestPrice                                
--if ((select count(1) from tbAutoSource a join #Parttemp prt  on a.BOMManagerNo= prt.BOMManagerNo where a.fullpart = prt.PartNames)=0)                                
                      
--select * from #tempLowestPrice                    
                   
                   
                  
insert into tbAutoSource (offerid,VendorName,VendorCategory,VendorType,ManufacturerName,FullPart,part,ProductNo,PackageNo,ManufacturerNo,cost,Resale,Profit,Margin,moq,SPQ,MSL,ROHS,                                
ADJQty,Excess,StockQty,DateCode,LT,Risk,Notes,UpdatedBy,DLUP,BOMManagerNo,CustomerRequirementId,Islock,BOMStatus,CurrencyNo,SupplierNo,DeliveryDate,POHubCompanyNo)                                
select OfferId,SupplierName,SourcingType,1, ManufacturerName,FullPart,FullPart,ProductNo,PackageNo,ManufacturerNo,Price,Resale,isnull(Profit,0),Margin,SupplierMOQ,SPQ,null,ROHS,                                
ADJQty,Excess,StockQty,DC,LTDays,Risk,Notes,null,DLUP,@BOMManagerNo,customerrequirementid,islock,bomstatus,CurrencyNo,SupplierNo,@DeliveryDate,POHubCompanyNo                              
from #tempLowestPrice --where FullPart not in (select fullpart from tbAutoSource where BOMManagerNo = @BOMManagerNo)                               
                            
                  
update cr                        
set                        
REQStatus = 3                        
from                        
tbCustomerRequirement cr                        
join #tempLowestPrice b on b.customerrequirementid = cr.CustomerRequirementId                        
where cr.BOMManagerNo = @BOMManagerNo and cr.CustomerRequirementId= b.CustomerRequirementId --and cr.FullPart = b.FullPart                        
                        
                    
declare @bommanagerstatus int                    
select @bommanagerstatus = [Status] from tbbommanager where BOMManagerId = @BOMManagerNo                    
                    
if(@bommanagerstatus < 3)                
begin                    
 update tbBomManager                    
 set [Status] = 3              
 where BOMManagerId = @BOMManagerNo              
end                    
                        
/*                            
if ((select count(1) from #Parttemp where partnames not in (select fullpart from tbautosource where bommanagerno = @BOMManagerNo))>0)                                
begin                                
insert into tbAutoSource (offerid,VendorName,VendorCategory,VendorType,ManufacturerName,FullPart,part,ProductNo,PackageNo,ManufacturerNo,cost,Resale,Profit,Margin,moq,SPQ,MSL,ROHS,                                
ADJQty,Excess,StockQty,DateCode,LT,Risk,Notes,UpdatedBy,DLUP,BOMManagerNo,CustomerRequirementId,Islock,BOMStatus,SupplierNo,CurrencyNo)           
select OfferId,SupplierName,SourcingType,1, ManufacturerName,FullPart,FullPart,ProductNo,PackageNo,ManufacturerNo,Price,Resale,isnull(Profit,0),Margin,SupplierMOQ,SPQ,null,ROHS,                                
ADJQty,Excess,StockQty,DC,LTDays,Risk,Notes,null,DLUP,@BOMManagerNo,customerrequirementid,islock,bomstatus,SupplierNo,CurrencyNo                      
from #tempLowestPrice where FullPart not in (select fullpart from tbAutoSource where BOMManagerNo = @BOMManagerNo)                               
                    
--need to put partnumber join                               
update a set a.HasHubSourcingResult=1, a.REQStatus=3  from tbcustomerrequirement a join #tempLowestPrice b on a.CustomerRequirementId =b.customerrequirementid                              
where a.BOMManagerNo = @BOMManagerNo                               
--need to update tbbommanager for offer added                              
update tbbommanager set Status =3 where BOMManagerId = @BOMManagerNo                              
--if((select count(1) from tbcustomerrequirement where BOMManagerno = @BOMManagerNo and REQStatus <4)>0)                              
--begin                              
--update tbbommanager set Status =5 where BOMManagerId = @BOMManagerNo                              
--end                              
--else                              
--begin                              
--update tbbommanager set Status =4 where BOMManagerId = @BOMManagerNo                              
--end                              
                              
                              
--select * from tbAutoSource where BOMManagerNo = @BOMManagerNo                                
end         
else                                
begin                                
print 'Exists'                                
--select * from tbAutoSource where BOMManagerNo = @BOMManagerNo                                
end                            
*/                          
drop table #tempSourcingData                                
drop table #tempRawData                                
drop table #tempLowestPrice                                
                               
select 'Success' as 'Status','Offer Added.' as 'Message'                              
--select 1/0                              
                              
                              
commit tran                              
--SELECT  @RowsAffected = @@ROWCOUNT                              
end try                              
begin catch                              
rollback tran                              
select 'Fail' as 'Status',ERROR_MESSAGE() as 'Message'            
end catch                            
                          
end 

GO


