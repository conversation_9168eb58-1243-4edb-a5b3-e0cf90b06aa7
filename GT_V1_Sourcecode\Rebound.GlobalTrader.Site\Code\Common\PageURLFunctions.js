$RGT_gotoURL_Company=function(n,t,i){var r=new Sys.StringBuilder($R_URL_Contact_CompanyDetail);return r.append(String.format("?{0}={1}",$R_QS_CompanyID,n)),t!=null&&r.append(String.format("&{0}={1}",$R_QS_Tab,t)),i!=null&&r.append(String.format("&{0}={1}",$R_QS_CompanyListType,i)),r.toString()};$RGT_gotoURL_CompanyBrowse=function(n,t){var i=new Sys.StringBuilder($R_URL_Contact_CompanyBrowse);return n==null&&(n=$R_ENUM$CompanyListType.AllCompanies),i.append(String.format("?{0}={1}",$R_QS_CompanyListType,n)),t&&i.append(String.format("&{0}={1}",$R_QS_CompanyName,escape(t))),i.toString()};$RGT_gotoURL_Contact=function(n,t){var i=new Sys.StringBuilder($R_URL_Contact_ContactDetail);return i.append(String.format("?{0}={1}",$R_QS_ContactID,n)),t!=null&&i.append(String.format("&{0}={1}",$R_QS_CompanyListType,t)),i.toString()};$RGT_gotoURL_SoQuote=function(n,t){var i=new Sys.StringBuilder($R_URL_Orders_QuoteDetail);return i.append(String.format("?{0}={1}",$R_QS_QuoteID,n)),t!=null&&i.append(String.format("&{0}={1}",$R_QS_QuoteLineID,t)),i.toString()};$RGT_gotoURL_ContactBrowse=function(n){var t=new Sys.StringBuilder($R_URL_Contact_ContactBrowse);return n&&t.append(String.format("?{0}={1}",$R_QS_ContactName,escape(n))),t.toString()};$RGT_gotoURL_Manufacturer=function(n){var t=new Sys.StringBuilder($R_URL_Contact_ManufacturerDetail);return t.append(String.format("?{0}={1}",$R_QS_ManufacturerID,n)),t.toString()};$RGT_gotoURL_ManufacturerBrowse=function(n){var t=new Sys.StringBuilder($R_URL_Contact_ManufacturerBrowse);return n&&t.append(String.format("?{0}={1}",$R_QS_ManufacturerName,escape(n))),t.toString()};$RGT_gotoURL_Invoice=function(n){var t=new Sys.StringBuilder($R_URL_Orders_InvoiceDetail);return t.append(String.format("?{0}={1}",$R_QS_InvoiceID,n)),t.toString()};$RGT_gotoURL_InvoiceAdd=function(n,t,i,r){var u=new Sys.StringBuilder("");return n&&u.append(String.format("{0}{1}={2}",u.toString().length>0?"&":"?",$R_QS_CompanyID,n)),t&&u.append(String.format("{0}{1}={2}",u.toString().length>0?"&":"?",$R_QS_CompanyName,escape(t))),i&&u.append(String.format("{0}{1}={2}",u.toString().length>0?"&":"?",$R_QS_ContactID,i)),r&&u.append(String.format("{0}{1}={2}",u.toString().length>0?"&":"?",$R_QS_ContactName,escape(r))),$R_URL_Orders_InvoiceAdd+u.toString()};$RGT_gotoURL_CustomerRequirement=function(n){var t=new Sys.StringBuilder($R_URL_Orders_CustomerRequirementDetail);return t.append(String.format("?{0}={1}",$R_QS_CustomerRequirementID,n)),t.toString()};$RGT_gotoURL_CustomerRequirementAdd=function(n,t,i){var r=new Sys.StringBuilder("");return n&&r.append(String.format("{0}{1}={2}",r.toString().length>0?"&":"?",$R_QS_CompanyID,n)),t&&r.append(String.format("{0}{1}={2}",r.toString().length>0?"&":"?",$R_QS_CompanyName,escape(t))),i&&r.append(String.format("{0}{1}={2}",r.toString().length>0?"&":"?",$R_QS_ContactID,i)),$R_URL_Orders_CustomerRequirementAdd+r.toString()};$RGT_gotoURL_Quote=function(n){var t=new Sys.StringBuilder($R_URL_Orders_QuoteDetail);return t.append(String.format("?{0}={1}",$R_QS_QuoteID,n)),t.toString()};$RGT_gotoURL_QuoteAdd=function(n,t,i,r,u){var f=new Sys.StringBuilder("");return n&&f.append(String.format("{0}{1}={2}",f.toString().length>0?"&":"?",$R_QS_CompanyID,n)),t&&f.append(String.format("{0}{1}={2}",f.toString().length>0?"&":"?",$R_QS_CompanyName,escape(t))),i&&f.append(String.format("{0}{1}={2}",f.toString().length>0?"&":"?",$R_QS_CustomerRequirementID,i)),r&&f.append(String.format("{0}{1}={2}",f.toString().length>0?"&":"?",$R_QS_LineIDs,r)),u&&f.append(String.format("{0}{1}={2}",f.toString().length>0?"&":"?",$R_QS_ContactID,u)),$R_URL_Orders_QuoteAdd+f.toString()};$RGT_gotoURL_SalesOrder=function(n){var t=new Sys.StringBuilder($R_URL_Orders_SalesOrderDetail);return t.append(String.format("?{0}={1}",$R_QS_SalesOrderID,n)),t.toString()};$RGT_gotoURL_SalesOrderAdd=function(n,t,i,r,u){var f=new Sys.StringBuilder("");return n&&f.append(String.format("{0}{1}={2}",f.toString().length>0?"&":"?",$R_QS_CompanyID,n)),t&&f.append(String.format("{0}{1}={2}",f.toString().length>0?"&":"?",$R_QS_CompanyName,escape(t))),i&&f.append(String.format("{0}{1}={2}",f.toString().length>0?"&":"?",$R_QS_ContactID,i)),r&&f.append(String.format("{0}{1}={2}",f.toString().length>0?"&":"?",$R_QS_QuoteID,r)),u&&f.append(String.format("{0}{1}={2}",f.toString().length>0?"&":"?",$R_QS_LineIDs,u)),$R_URL_Orders_SalesOrderAdd+f.toString()};$RGT_gotoURL_GoodsIn=function(n){var t=new Sys.StringBuilder($R_URL_Warehouse_GoodsInDetail);return t.append(String.format("?{0}={1}",$R_QS_GoodsInID,n)),t.toString()};$RGT_gotoURL_PurchaseOrder=function(n){var t=new Sys.StringBuilder("");return t.append(String.format("?{0}={1}",$R_QS_PurchaseOrderID,n)),$R_URL_Orders_PurchaseOrderDetail+t.toString()};$RGT_gotoURL_PurchaseOrderAdd=function(n,t,i,r,u){var f=new Sys.StringBuilder("");return n&&f.append(String.format("{0}{1}={2}",f.toString().length>0?"&":"?",$R_QS_CompanyID,n)),t&&f.append(String.format("{0}{1}={2}",f.toString().length>0?"&":"?",$R_QS_CompanyName,escape(t))),i&&f.append(String.format("{0}{1}={2}",f.toString().length>0?"&":"?",$R_QS_ContactID,i)),r&&f.append(String.format("{0}{1}={2}",f.toString().length>0?"&":"?",$R_QS_SalesOrderID,r)),u&&f.append(String.format("{0}{1}={2}",f.toString().length>0?"&":"?",$R_QS_LineIDs,u)),$R_URL_Orders_PurchaseOrderAdd+f.toString()};$RGT_gotoURL_ReceivePurchaseOrder=function(n){var t=new Sys.StringBuilder($R_URL_Warehouse_ReceivePurchaseOrderDetail);return t.append(String.format("?{0}={1}",$R_QS_PurchaseOrderID,n)),t.toString()};$RGT_gotoURL_ShipSalesOrder=function(n){var t=new Sys.StringBuilder($R_URL_Warehouse_ShipSalesOrderDetail);return t.append(String.format("?{0}={1}",$R_QS_SalesOrderID,n)),t.toString()};$RGT_gotoURL_PurchaseRequisition=function(n){var t=new Sys.StringBuilder($R_URL_Orders_PurchaseRequisitionDetail);return t.append(String.format("?{0}={1}",$R_QS_PurchaseRequisitionID,n)),t.toString()};$RGT_gotoURL_CRMA=function(n){var t=new Sys.StringBuilder($R_URL_Orders_CustomerRMADetail);return t.append(String.format("?{0}={1}",$R_QS_CRMAID,n)),t.toString()};$RGT_gotoURL_CRMAAdd=function(n,t,i,r){var u=new Sys.StringBuilder("");return n&&u.append(String.format("{0}{1}={2}",u.toString().length>0?"&":"?",$R_QS_CompanyID,n)),t&&u.append(String.format("{0}{1}={2}",u.toString().length>0?"&":"?",$R_QS_CompanyName,escape(t))),i&&u.append(String.format("{0}{1}={2}",u.toString().length>0?"&":"?",$R_QS_ContactID,i)),r&&u.append(String.format("{0}{1}={2}",u.toString().length>0?"&":"?",$R_QS_ContactName,escape(r))),$R_URL_Orders_CustomerRMAAdd+u.toString()};$RGT_gotoURL_SRMA=function(n){var t=new Sys.StringBuilder($R_URL_Orders_SupplierRMADetail);return t.append(String.format("?{0}={1}",$R_QS_SRMAID,n)),t.toString()};$RGT_gotoURL_SRMAAdd=function(n,t,i,r){var u=new Sys.StringBuilder("");return n&&u.append(String.format("{0}{1}={2}",u.toString().length>0?"&":"?",$R_QS_CompanyID,n)),t&&u.append(String.format("{0}{1}={2}",u.toString().length>0?"&":"?",$R_QS_CompanyName,escape(t))),i&&u.append(String.format("{0}{1}={2}",u.toString().length>0?"&":"?",$R_QS_ContactID,i)),r&&u.append(String.format("{0}{1}={2}",u.toString().length>0?"&":"?",$R_QS_ContactName,escape(r))),$R_URL_Orders_SupplierRMAAdd+u.toString()};$RGT_gotoURL_CreditNote=function(n){var t=new Sys.StringBuilder($R_URL_Orders_CreditNoteDetail);return t.append(String.format("?{0}={1}",$R_QS_CreditID,n)),t.toString()};$RGT_gotoURL_CreditNoteAdd=function(n,t,i,r){var u=new Sys.StringBuilder("");return n&&u.append(String.format("{0}{1}={2}",u.toString().length>0?"&":"?",$R_QS_CompanyID,n)),t&&u.append(String.format("{0}{1}={2}",u.toString().length>0?"&":"?",$R_QS_CompanyName,escape(t))),i&&u.append(String.format("{0}{1}={2}",u.toString().length>0?"&":"?",$R_QS_ContactID,i)),r&&u.append(String.format("{0}{1}={2}",u.toString().length>0?"&":"?",$R_QS_ContactName,escape(r))),$R_URL_Orders_CreditNoteAdd+u.toString()};$RGT_gotoURL_DebitNote=function(n){var t=new Sys.StringBuilder($R_URL_Orders_DebitNoteDetail);return t.append(String.format("?{0}={1}",$R_QS_DebitID,n)),t.toString()};$RGT_gotoURL_DebitNoteAdd=function(n,t,i,r){var u=new Sys.StringBuilder("");return n&&u.append(String.format("{0}{1}={2}",u.toString().length>0?"&":"?",$R_QS_CompanyID,n)),t&&u.append(String.format("{0}{1}={2}",u.toString().length>0?"&":"?",$R_QS_CompanyName,escape(t))),i&&u.append(String.format("{0}{1}={2}",u.toString().length>0?"&":"?",$R_QS_ContactID,i)),r&&u.append(String.format("{0}{1}={2}",u.toString().length>0?"&":"?",$R_QS_ContactName,escape(r))),$R_URL_Orders_DebitNoteAdd+u.toString()};$RGT_gotoURL_Stock=function(n){var t=new Sys.StringBuilder($R_URL_Warehouse_StockDetail);return t.append(String.format("?{0}={1}",$R_QS_StockID,n)),t.toString()};$RGT_gotoURL_StockBrowse=function(n){var t=new Sys.StringBuilder($R_URL_Warehouse_StockBrowse);return t.append(String.format("?{0}={1}",$R_QS_SearchPartNo,n)),t.toString()};$RGT_gotoURL_IHSBrowse=function(){var n=new Sys.StringBuilder($R_URL_Warehouse_IHSCatalogueBrowse);return n.toString()};$RGT_gotoURL_Sourcing=function(n,t){var i=new Sys.StringBuilder($R_URL_Orders_Sourcing);return n||(n=""),n.length>1&&i.append(String.format("?{0}={1}",t?$R_QS_SearchPartNo:$R_QS_PartNo,escape(n))),i.toString()};$RGT_gotoURL_MailMessages=function(n){var t=new Sys.StringBuilder($R_URL_Profile_MailMessages);return n&&t.append(String.format("?{0}={1}",$R_QS_MailMessageID,n)),t.toString()};$RGT_gotoURL_ToDo=function(n){var t=new Sys.StringBuilder($R_URL_Profile_ToDo);return t.append(String.format("?{0}={1}",$R_QS_ToDoID,n)),t.toString()};$RGT_gotoURL_SecurityUser=function(n){var t=new Sys.StringBuilder($R_URL_Setup_Security_Users);return t.append(String.format("?{0}={1}",$R_QS_LoginID,n)),t.toString()};$RGT_gotoURL_SecurityGroup=function(n){var t=new Sys.StringBuilder($R_URL_Setup_Security_Groups);return t.append(String.format("?{0}={1}",$R_QS_SecurityGroupID,n)),t.toString()};$RGT_gotoURL_ReceiveCRMA=function(n){var t=new Sys.StringBuilder($R_URL_Warehouse_ReceiveCustomerRMADetail);return t.append(String.format("?{0}={1}",$R_QS_CRMAID,n)),t.toString()};$RGT_gotoURL_ShipSRMA=function(n){var t=new Sys.StringBuilder($R_URL_Warehouse_ShipSupplierRMADetail);return t.append(String.format("?{0}={1}",$R_QS_SRMAID,n)),t.toString()};$RGT_gotoURL_Service=function(n){var t=new Sys.StringBuilder($R_URL_Warehouse_ServicesDetail);return t.append(String.format("?{0}={1}",$R_QS_ServiceID,n)),t.toString()};$RGT_gotoURL_Lot=function(n){var t=new Sys.StringBuilder($R_URL_Warehouse_LotsDetail);return t.append(String.format("?{0}={1}",$R_QS_LotID,n)),t.toString()};$RGT_gotoURL_BOM=function(n){var t=new Sys.StringBuilder($R_URL_Orders_BOMDetail);return t.append(String.format("?{0}={1}",$R_QS_BOMID,n)),t.toString()};$RGT_gotoURL_ReceivedPurchaseOrder=function(n){var t=new Sys.StringBuilder($R_URL_Accounts_ReceivedPurchaseOrderDetail);return t.append(String.format("?{0}={1}",$R_QS_PurchaseOrderID,n)),t.toString()};$RGT_gotoURL_ReceivedCRMA=function(n){var t=new Sys.StringBuilder($R_URL_Accounts_ReceivedCustomerRMADetail);return t.append(String.format("?{0}={1}",$R_QS_CRMAID,n)),t.toString()};$RGT_gotoURL_ClientBOMImport=function(n){var t=new Sys.StringBuilder($R_URL_Orders_ClientBOMDetail);return t.append(String.format("?{0}={1}",$R_QS_ClientBOMID,n)),t.toString()};$RGT_nubButton_Company=function(n,t,i,r,u,f){var e=$R_FN.setCleanTextValue(t);return n>0&&(e=$R_FN.createNubButton($RGT_gotoURL_Company(n,i,r),e)),u&&(e+=String.format('&nbsp;<span class="companyOnStop">{0}<\/span>',$R_RES.OnStop)),e+$R_FN.createAdvisoryNotesIcon(f,u?"margin-left-10":"")};$RGT_nubButton_Contact=function(n,t,i){var r=$R_FN.setCleanTextValue(t);return n>0&&(r=$R_FN.createNubButton($RGT_gotoURL_Contact(n,i),$R_FN.setCleanTextValue(t))),r};$RGT_nubButton_SoQuote=function(n,t,i){var r=$R_FN.setCleanTextValue(t);return n>0&&(r=$R_FN.createNubButton($RGT_gotoURL_SoQuote(n,i),$R_FN.setCleanTextValue(t))),r};$RGT_nubButton_Stock=function(n,t,i){var r=$R_FN.writePartNo(t,i);return n>0&&(r=$R_FN.createNubButton($RGT_gotoURL_Stock(n),r)),r};$RGT_nubButton_Manufacturer=function(n,t,i){var r=$R_FN.setCleanTextValue(t);return n>0&&(r=$R_FN.createNubButton($RGT_gotoURL_Manufacturer(n),r)),r+$R_FN.createAdvisoryNotesIcon(i)};$RGT_nubButton_ShipInstructions=function(n,t){var i=$R_FN.setCleanTextValue(t);return n>0&&(i=$R_FN.createNubButton($RGT_gotoURL_Manufacturer(n),i)),i};$RGT_nubButton_Service=function(n,t){var i=$R_FN.setCleanTextValue(t);return n>0&&(i=$R_FN.createNubButton($RGT_gotoURL_Service(n),i)),i};$RGT_nubButton_Lot=function(n,t){var i=$R_FN.setCleanTextValue(t);return n>0&&(i=$R_FN.createNubButton($RGT_gotoURL_Lot(n),i)),i};$RGT_nubButton_BOM=function(n,t){var i=$R_FN.setCleanTextValue(t);return n>0&&(i=$R_FN.createNubButton($RGT_gotoURL_BOM(n),i)),i};$RGT_nubButton_BMM=function(n,t){var i=$R_FN.setCleanTextValue(t);return n>0&&(i=$R_FN.createNubButton("ord_BOMManagerDetail.aspx?BOM="+n,i)),i};$RGT_nubButton_BMMPO=function(n,t){var i=$R_FN.setCleanTextValue(t);return n>0&&(i=$R_FN.createNubButton("ord_BOMManagerSourcing.aspx?BOM="+n,i)),i};$RGT_nubButton_ShipSRMA=function(n,t){var i="";return n>0&&(i=$R_FN.createNubButton($RGT_gotoURL_ShipSRMA(n),t)),i};$RGT_nubButton_ReceiveCRMA=function(n,t){var i="";return n>0&&(i=$R_FN.createNubButton($RGT_gotoURL_ReceiveCRMA(n),t)),i};$RGT_nubButton_ShipSalesOrder=function(n,t){var i="";return n>0&&(i=$R_FN.createNubButton($RGT_gotoURL_ShipSalesOrder(n),t)),i};$RGT_nubButton_ReceivePurchaseOrder=function(n,t){var i="";return n>0&&(i=$R_FN.createNubButton($RGT_gotoURL_ReceivePurchaseOrder(n),t)),i};$RGT_nubButton_ToDo_CusClass=function(n,t,i){var r=$R_FN.setCleanTextValue(t);return n>0&&(r=$R_FN.createNubButtonCustomClass($RGT_gotoURL_ToDo(n),r,i)),r};$RGT_nubButton_ToDo=function(n,t){var i=$R_FN.setCleanTextValue(t);return n>0&&(i=$R_FN.createNubButton($RGT_gotoURL_ToDo(n),i)),i};$RGT_nubButton_MailMessage=function(n,t){var i=$R_FN.setCleanTextValue(t);return n>0&&(i=$R_FN.createNubButton($RGT_gotoURL_MailMessages(n),i)),i};$RGT_nubButton_ReceivedCRMA=function(n,t){var i="";return n>0&&(i=$R_FN.createNubButton($RGT_gotoURL_ReceivedCRMA(n),t)),i};$RGT_nubButton_ReceivedPurchaseOrder=function(n,t){var i="";return n>0&&(i=$R_FN.createNubButton($RGT_gotoURL_ReceivedPurchaseOrder(n),t)),i};$RGT_nubButton_SystemDocument=function(n,t,i,r){var u="";if(n=Number.parseInvariant(n.toString()),n<=0)return"";switch(Number.parseInvariant(n.toString())){case $R_ENUM$SystemDocument.CreditNote:u=$R_FN.createNubButton($RGT_gotoURL_CreditNote(t,r),i);break;case $R_ENUM$SystemDocument.CustomerRequirement:u=$R_FN.createNubButton($RGT_gotoURL_CustomerRequirement(t,r),i);break;case $R_ENUM$SystemDocument.CustomerRMA:u=$R_FN.createNubButton($RGT_gotoURL_CRMA(t,r),i);break;case $R_ENUM$SystemDocument.DebitNote:u=$R_FN.createNubButton($RGT_gotoURL_DebitNote(t,r),i);break;case $R_ENUM$SystemDocument.GoodsIn:u=$R_FN.createNubButton($RGT_gotoURL_GoodsIn(t,r),i);break;case $R_ENUM$SystemDocument.Invoice:u=$R_FN.createNubButton($RGT_gotoURL_Invoice(t,r),i);break;case $R_ENUM$SystemDocument.PurchaseOrder:u=$R_FN.createNubButton($RGT_gotoURL_PurchaseOrder(t,r),i);break;case $R_ENUM$SystemDocument.Quote:u=$R_FN.createNubButton($RGT_gotoURL_Quote(t,r),i);break;case $R_ENUM$SystemDocument.SalesOrder:u=$R_FN.createNubButton($RGT_gotoURL_SalesOrder(t,r),i);break;case $R_ENUM$SystemDocument.SupplierRMA:u=$R_FN.createNubButton($RGT_gotoURL_SRMA(t,r),i);break;case $R_ENUM$SystemDocument.PurchaseRequisition:u=$R_FN.createNubButton($RGT_gotoURL_PurchaseRequisition(t,r),i);break;case $R_ENUM$SystemDocument.SupplierInvoice:u=$R_FN.createNubButton($RGT_gotoURL_SupplierInvoice(t,r),i);break;case $R_ENUM$SystemDocument.InternalPurchaseOrder:u=$R_FN.createNubButton($RGT_gotoURL_InternalPurchaseOrder(t,r),i);break;case $R_ENUM$SystemDocument.PurchaseQuote:u=$R_FN.createNubButton($RGT_gotoURL_POQuote(t,r),i);break;case $R_ENUM$SystemDocument.ClientInvoice:u=$R_FN.createNubButton($RGT_gotoURL_ClientInvoice(t,r),i)}return u};$RGT_nubButton_SalesOrder=function(n,t){var i=t;return n>0&&(i=$RGT_nubButton_SystemDocument($R_ENUM$SystemDocument.SalesOrder,n,t)),i};$RGT_nubButton_PurchaseOrder=function(n,t){var i=t;return n>0&&(i=$RGT_nubButton_SystemDocument($R_ENUM$SystemDocument.PurchaseOrder,n,t)),i};$RGT_nubButton_PurchaseOrderSRMA=function(n,t,i){var r=$R_FN.setCleanTextValue(t);return n>0&&(r=$R_FN.createNubButton($RGT_gotoURL_POSRMA(n,i),$R_FN.setCleanTextValue(t))),r};$RGT_gotoURL_POSRMA=function(n,t){var i=new Sys.StringBuilder($R_URL_Orders_PurchaseOrderDetail);return i.append(String.format("?{0}={1}",$R_QS_PurchaseOrderID,n)),t!=null&&i.append(String.format("&{0}={1}",$R_QS_PurchaseOrderLineID,t)),i.toString()};$RGT_nubButton_POSO=function(n,t,i){var r=$R_FN.setCleanTextValue(t);return n>0&&(r=$R_FN.createNubButton($RGT_gotoURL_POSO(n,i),$R_FN.setCleanTextValue(t))),r};$RGT_gotoURL_POSO=function(n,t){var i=new Sys.StringBuilder($R_URL_Orders_SalesOrderDetail);return i.append(String.format("?{0}={1}",$R_QS_SalesOrderID,n)),t!=null&&i.append(String.format("&{0}={1}",$R_QS_SalesOrderLineID,t)),i.toString()};$RGT_nubButton_Invoice=function(n,t){var i=t;return n>0&&(i=$RGT_nubButton_SystemDocument($R_ENUM$SystemDocument.Invoice,n,t)),i};$RGT_nubButton_CreditNote=function(n,t){var i=t;return n>0&&(i=$RGT_nubButton_SystemDocument($R_ENUM$SystemDocument.CreditNote,n,t)),i};$RGT_nubButton_DebitNote=function(n,t){var i=t;return n>0&&(i=$RGT_nubButton_SystemDocument($R_ENUM$SystemDocument.DebitNote,n,t)),i};$RGT_nubButton_GoodsIn=function(n,t){var i=t;return n>0&&(i=$RGT_nubButton_SystemDocument($R_ENUM$SystemDocument.GoodsIn,n,t)),i};$RGT_nubButton_Quote=function(n,t){var i=t;return n>0&&(i=$RGT_nubButton_SystemDocument($R_ENUM$SystemDocument.Quote,n,t)),i};$RGT_nubButton_CustomerRequirement=function(n,t){var i=t;return n>0&&(i=$RGT_nubButton_SystemDocument($R_ENUM$SystemDocument.CustomerRequirement,n,t)),i};$RGT_nubButton_SRMA=function(n,t){var i=t;return n>0&&(i=$RGT_nubButton_SystemDocument($R_ENUM$SystemDocument.SupplierRMA,n,t)),i};$RGT_nubButton_CRMA=function(n,t){var i=t;return n>0&&(i=$RGT_nubButton_SystemDocument($R_ENUM$SystemDocument.CustomerRMA,n,t)),i};$RGT_nubButton_PurchaseRequisition=function(n,t){var i=t;return n>0&&(i=$RGT_nubButton_SystemDocument($R_ENUM$SystemDocument.PurchaseRequisition,n,t)),i};$RGT_nubButton_SupplierInvoice=function(n,t){var i=t;return n>0&&(i=$RGT_nubButton_SystemDocument($R_ENUM$SystemDocument.SupplierInvoice,n,t)),i};$RGT_gotoURL_SupplierInvoice=function(n){var t=new Sys.StringBuilder("");return t.append(String.format("?{0}={1}",$R_QS_SupplierInvoiceID,n)),$R_URL_Warehouse_SupplierInvoiceDetail+t.toString()};$RGT_nubButton_NPR=function(n,t,i){var r=t;return n>0&&(r=$R_FN.createNubButtonNPR(n,t,i)),r};$RGT_nubButton_NPRNugget=function(n,t,i){var r=t;return n>0&&(r=$R_FN.createNubButtonNPRNugget(n,t,i)),r};$RGT_openNPRWindow=function(n,t){$R_FN.openNPRPrintWindow(n,t)};$RGT_gotoURL_NotifyNPR=function(n){var t=new Sys.StringBuilder("");return t.append(String.format("?{0}={1}",$R_QS_NPRID,n)),$R_URL_Warehouse_NPRNotify+t.toString()};$RGT_nubButton_STO=function(n,t,i){var r=t;return n>0&&(r=$R_FN.createNubButtonSTO(n,t,i)),r};$RGT_openNPRLog=function(n,t){$R_FN.openPrintNPRWindow(n,t)};$RGT_openEPRWindow=function(n,t,i){$R_FN.openEPRWindow(n,t,i)};$RGT_openCreditLimitWindow=function(n,t){$R_FN.openCreditLimitWindow(n,t)};$RGT_openCreditLimitLogWindow=function(n,t){$R_FN.openCreditLimitLogWindow(n,t)};$RGT_nubButton_EPR=function(n,t,i){var r=t;return n>0&&(r=$R_FN.createNubButtonEPR(n,t,i)),r};$RGT_nubButton_CreditLimit=function(n,t,i){var r=t;return n>0&&(r=$R_FN.createNubButtonCreditLimit(n,t,i)),r};$RGT_openEPRLog=function(n,t){$R_FN.openPrintEPRWindow(n,t)};$RGT_gotoURL_SalesOrderBrowse=function(n){var t=new Sys.StringBuilder($R_URL_Orders_SalesOrderBrowse);return t.append(String.format("?{0}={1}","bss",!0)),t.append(String.format("&{0}={1}","sp",n)),t.toString()};$RGT_gotoURL_QuoteBrowse=function(n){var t=new Sys.StringBuilder($R_URL_Orders_QuoteBrowse);return t.append(String.format("?{0}={1}","bss",!0)),t.append(String.format("&{0}={1}","sp",n)),t.toString()};$RGT_gotoURL_CusReqBrowse=function(n){var t=new Sys.StringBuilder($R_URL_Orders_CustomerRequirementBrowse);return t.append(String.format("?{0}={1}","bss",!0)),t.append(String.format("&{0}={1}","sp",n)),t.toString()};$RGT_gotoURL_POBrowse=function(n){var t=new Sys.StringBuilder($R_URL_Orders_PurchaseOrderBrowse);return t.append(String.format("?{0}={1}","bss",!0)),t.append(String.format("&{0}={1}","buy",n)),t.toString()};$RGT_openEPRWindowEmail=function(n,t){$R_FN.openEPRWindowEmail(n,t)};$RGT_gotoURL_POQuote=function(n){var t=new Sys.StringBuilder($R_URL_Orders_POQuoteDetail);return t.append(String.format("?{0}={1}",$R_QS_POQuoteID,n)),t.toString()};$RGT_nubButton_InternalPurchaseOrder=function(n,t){var i=t;return n>0&&(i=$RGT_nubButton_SystemDocument($R_ENUM$SystemDocument.InternalPurchaseOrder,n,t)),i};$RGT_gotoURL_InternalPurchaseOrder=function(n){var t=new Sys.StringBuilder("");return t.append(String.format("?{0}={1}",$R_QS_InternalPurchaseOrderID,n)),$R_URL_Orders_InternalPurchaseOrderDetail+t.toString()};$RGT_nubButton_POQuote=function(n,t){var i=t;return n>0&&(i=$RGT_nubButton_SystemDocument($R_ENUM$SystemDocument.PurchaseQuote,n,t)),i};$RGT_nubButton_CompanySourcing=function(n,t,i,r,u,f,e,o){var s=$R_FN.setCleanTextValue(t);return n>0&&(s=$R_FN.createNubButton($RGT_gotoURL_Company(n,i,null),s,"",f,e)),u&&(s+=String.format('&nbsp;<span class="companyOnStop">{0}<\/span>',$R_RES.OnStop)),o&&(s+=$R_FN.createAdvisoryNotesIcon(o)),s};$RGT_nubButton_ClientInvoice=function(n,t){var i=t;return n>0&&(i=$RGT_nubButton_SystemDocument($R_ENUM$SystemDocument.ClientInvoice,n,t)),i};$RGT_gotoURL_ClientInvoice=function(n){var t=new Sys.StringBuilder("");return t.append(String.format("?{0}={1}",$R_QS_ClientInvoiceID,n)),$R_URL_Orders_ClientInvoiceDetail+t.toString()};$RGT_nubButton_QuoteImp=function(n,t,i){var r=t;return n>0&&(r=$RGT_nubButton_SystemDocument($R_ENUM$SystemDocument.Quote,n,t)),i&&(r+=String.format('<span title="Important"  class="quoteOnStop">!<\/span>',$R_RES.Important)),r};$RGT_nubButton_Caution=function(n){return""+String.format('&nbsp;<span class="companyOnStop11">{0}<\/span>',n)};$RGT_nubButton_ClientBOMImport=function(n,t){var i=$R_FN.setCleanTextValue(t);return n>0&&(i=$R_FN.createNubButton($RGT_gotoURL_ClientBOMImport(n),i)),i};$RGT_openIHSDoc=function(n,t){$R_FN.openIHSPDFWindow(n,t)};$RGT_openBomItemIHS=function(n,t){$R_FN.openBomItemIHSWindow(n,t)};$RGT_openEndUserUndertakingDoc=function(n){$R_FN.openEndUserUndertakingPDFWindow(n)};$RGT_openCIPPDFDoc=function(n,t){$R_FN.openCIPPDFWindow(n,t)};$RGT_openShortShipmentWindow=function(n){var t=$R_FN.setCleanTextValue(n);return n>0&&(t=$R_FN.createNubButton($RGT_gotoURL_ShortShipment(n),t)),t};$RGT_gotoURL_ShortShipment=function(n){var t=new Sys.StringBuilder($R_URL_Warehouse_NotifyShortShipment);return t.append(String.format("?{0}={1}",$R_QS_ShortShipmentID,n)),t.toString()};$RGT_gotoURL_NotifyShortShipment=function(n,t){var i=new Sys.StringBuilder("");return i.append(String.format("?{0}={1}&{2}={3}",$R_QS_ShortShipmentID,n,$R_QS_ShortShipmentStage,t)),$R_URL_Warehouse_ShortShipmentNotify+i.toString()};$RGT_openSupplierApprovalDoc=function(n,t,i){$R_FN.openSupplierApprovalDocWindow(n,t,i)};$RGT_openSupplierApprovalImages=function(n,t,i){$R_FN.openSupplierApprovalImageWindow(n,t,i)};$RGT_nubButton_ShortShipment=function(n,t,i){var r=$R_FN.setCleanTextValue(t);return n>0&&(r=$R_FN.createNubButton($RGT_gotoURL_NotifyShortShipment(n,i),r)),r};$RGT_nubButton_ShortShipmentDetails=function(n,t){var i=$R_FN.setCleanTextValue(t);return n>0&&(i=$R_FN.createNubButton($RGT_gotoURL_ShortShipmentDetails(n),i)),i};$RGT_gotoURL_ShortShipmentDetails=function(n){var t=new Sys.StringBuilder("");return t.append(String.format("?{0}={1}",$R_QS_ShortShipmentID,n)),$R_URL_Warehouse_ShortShipmentDetails+t.toString()};$RGT_nubButton_QueryGILine=function(n,t,i,r){var u=$R_FN.setCleanTextValue(i);return n>0&&(u=$R_FN.createNubButton($RGT_gotoURL_QueryGILine(n,t,r),u)),u};$RGT_gotoURL_QueryGILine=function(n,t,i){var r=new Sys.StringBuilder("");return r.append(String.format("?{0}={1}&{2}={3}&{4}={5}",$R_QS_GoodsInID,n,$R_QS_LandedGiLineId,t,$R_QS_IsLandedQuery,i)),$R_URL_Warehouse_GoodsInDetail+r.toString()};$RGT_nubButton_GILineShortShipment=function(n,t,i){var r=t;return n>0&&(r=$R_FN.createNubButtonGILineShortShipment(n,t,i)),r};