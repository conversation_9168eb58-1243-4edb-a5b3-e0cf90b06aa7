using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using System.Collections.Concurrent;

namespace Rebound.GlobalTrader.Site.Controls.Forms {

	public class Base : UserControl, IScriptControl {

		#region Locals

		protected QueryStringManager _objQSManager = new QueryStringManager(HttpContext.Current.Request.QueryString);
		public DesignBase ctlDesignBase;
		protected ScriptManager _sm;
		protected ScriptReference[] _srScriptReference = new ScriptReference[5];
		private int _intNumberOfScriptReferences = 0;
		protected ScriptControlDescriptor _scScriptControlDescriptor;
		protected IconButton _ibtnSave;
		protected IconButton _ibtnCancel;
		protected IconButton _ibtnSave_Footer;
		protected IconButton _ibtnCancel_Footer;
		protected ConcurrentDictionary<string, int> _dctFieldOrdinals = new ConcurrentDictionary<string, int>();
		protected List<ConcurrentDictionary<string, string>> _lstFields = new List<ConcurrentDictionary<string, string>>();
		int _intControlOrdinal = 0;

		#endregion

		#region Properties

		public new string ClientID {
			get { return ctlDesignBase.ClientID; }
		}

		/// <summary>
		/// Container holding the links for this form
		/// </summary>
		private Panel _pnlLinksHolder;
		public Panel LinksHolder {
			get { return _pnlLinksHolder; }
			set { _pnlLinksHolder = value; }
		}

		/// <summary>
		/// Container holding the footer links for this form
		/// </summary>
		private Panel _pnlFooterLinksHolder;
		public Panel FooterLinksHolder {
			get { return _pnlFooterLinksHolder; }
			set { _pnlFooterLinksHolder = value; }
		}

		/// <summary>
		/// Index of form in the forms collection of the nugget it is inside
		/// </summary>
		private int _intIndex;
		public int Index {
			get { return _intIndex; }
			set { _intIndex = value; }
		}

		/// <summary>
		/// related nugget control containing the form
		/// </summary>
		private Nuggets.Base _ctlRelatedNugget;
		public Nuggets.Base RelatedNugget {
			get { return _ctlRelatedNugget; }
			set { _ctlRelatedNugget = value; }
		}

		/// <summary>
		/// Title text
		/// </summary>
		private string _strTitleText;
		public string TitleText {
			get { return _strTitleText; }
			set { _strTitleText = value; }
		}

		private bool _blnHideHeader = false;
		public bool HideHeader {
			get { return _blnHideHeader; }
			set { _blnHideHeader = value; }
		}

		public string SavingMessageResource { get; set; }
		public string SavedOKMessageResource { get; set; }
		public string LoadingMessageResource { get; set; }

		#endregion

		#region Overrides

		/// <summary>
		/// OnInit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			((Pages.Base)Page).AddCSSFile("Forms.css");
			ctlDesignBase = (DesignBase)Functions.FindControlRecursive(this, "ctlDB");
			if (ctlDesignBase == null) throw new Exception("A form must have a Design Base control with ID 'ctlDB' - have you checked Web.config?");
			ctlDesignBase.FormsBaseControl = this;
			ctlDesignBase.SavedOKMessageResource = SavedOKMessageResource;
			ctlDesignBase.SavingMessageResource = SavingMessageResource;
			ctlDesignBase.LoadingMessageResource = LoadingMessageResource;
			AddScriptReference("Controls.Forms._Bases.Base");
			base.OnInit(e);
		}

		/// <summary>
		/// OnPreRender
		/// </summary>
		/// <param name="e"></param>
		protected override void OnPreRender(EventArgs e) {
			ctlDesignBase.TitleText = _strTitleText;

			//fields
			ctlDesignBase.MakeChildControls();
			_dctFieldOrdinals.Clear();
			_lstFields.Clear();
			AddAllFormControls(ctlDesignBase.pnlContent);
			WireUpButtons();

			if (!this.DesignMode) {
				GetScriptManagerControl();
				_sm.RegisterScriptControl(this);
			}
			base.OnPreRender(e);
		}

		#endregion

		#region Methods

		private void GetScriptManagerControl() {
			_sm = ScriptManager.GetCurrent(Page);
			if (_sm == null) throw new HttpException("A ScriptManager control must exist on the current page.");
		}

		/// <summary>
		/// Auto wires up Common buttons
		/// </summary>
		private void WireUpButtons() {
			_ibtnSave = FindIconButton("ibtnSave");
			_ibtnSave_Footer = FindFooterIconButton("ibtnSave");
			_ibtnCancel = FindIconButton("ibtnCancel");
			_ibtnCancel_Footer = FindFooterIconButton("ibtnCancel");
		}

		/// <summary>
		/// Find IconButton control reference
		/// </summary>
		/// <param name="p"></param>
		/// <returns></returns>
		protected IconButton FindIconButton(string strID) {
			ctlDesignBase.MakeChildControls();
			if (_pnlLinksHolder == null) return null;
			return (IconButton)Functions.FindControlRecursive(_pnlLinksHolder, strID);
		}

		/// <summary>
		/// Find IconButton control reference
		/// </summary>
		/// <param name="p"></param>
		/// <returns></returns>
		protected IconButton FindFooterIconButton(string strID) {
			ctlDesignBase.MakeChildControls();
			if (_pnlFooterLinksHolder == null) return null;
			return (IconButton)Functions.FindControlRecursive(_pnlFooterLinksHolder, strID);
		}

		/// <summary>
		/// Add AJAX script reference
		/// </summary>
		/// <param name="sr"></param>
		protected void AddScriptReference(bool blnDebug, string strAssembly, string strRef) {
			ScriptReference sr = Functions.GetScriptReference(blnDebug, strAssembly, strRef, true);
			_srScriptReference[_intNumberOfScriptReferences] = sr;
			_intNumberOfScriptReferences += 1;
		}
		protected void AddScriptReference(string strAssembly, string strRef) {
			AddScriptReference(false, strAssembly, strRef);
		}
		protected void AddScriptReference(string strRef) {
			bool blnDebug = false;
#if DEBUG
			blnDebug = true;
#endif
			AddScriptReference(blnDebug, "Rebound.GlobalTrader.Site", strRef);
		}

		/// <summary>
		/// Finds a Tables.Form control
		/// </summary>
		protected Tables.Form FindFormField(string strControlName) {
			ctlDesignBase.MakeChildControls();
			return (Tables.Form)ctlDesignBase.FindContentControl(strControlName);
		}

		protected object FindContentControl(string strControlName) {
			ctlDesignBase.MakeChildControls();
			return ctlDesignBase.FindContentControl(strControlName);
		}

		/// <summary>
		/// Finds a control from within a FormField control
		/// </summary>
		protected Control FindFieldControl(string strFormFieldName, string strControlName) {
			ctlDesignBase.MakeChildControls();
			FormField fld = (FormField)ctlDesignBase.FindContentControl(strFormFieldName);
			Control ctl = null;
			if (fld != null) ctl = (Control)fld.FindFieldControl(strControlName);
			return ctl;
		}

		/// <summary>
		/// Find explanation control
		/// </summary>
		protected Control FindExplanationControl(string strControlName) {
			ctlDesignBase.MakeChildControls();
			return (Control)ctlDesignBase.FindExplanationControl(strControlName);
		}

		/// <summary>
		/// Sets a default value on a FormField
		/// </summary>
		protected void SetFieldDefault(string strFormFieldName, string strDefault) {
			FormField fld = (FormField)ctlDesignBase.FindContentControl(strFormFieldName);
			if (fld != null) fld.DefaultValue = strDefault;
		}

		/// <summary>
		/// Finds all Form Controls (iterative)
		/// </summary>
		protected void AddAllFormControls(Control ctlBase) {
			if (ctlBase == null) return;
			foreach (Control ctl in ctlBase.Controls) {
				if (ctl is FormField || ctl is LabelFormField) {
					_dctFieldOrdinals.TryAdd(ctl.ID, _intControlOrdinal);
					ConcurrentDictionary<string, string> dct = new ConcurrentDictionary<string, string>();
					dct.TryAdd("ID", ctl.ClientID);
					dct.TryAdd("Visible", "true");
					if (ctl is FormField) {
						FormField ff = (FormField)ctl;
						dct.TryAdd("Type", ((int)ff.FieldType).ToString());
						dct.TryAdd("ControlID", ff.FieldControl.ClientID);
						dct.TryAdd("Required", ff.IsRequiredField.ToString().ToLower());
						dct.TryAdd("CheckForValidEmail", ff.CheckForValidEmail.ToString().ToLower());
						dct.TryAdd("CheckForValidURL", ff.CheckForValidURL.ToString().ToLower());
						dct.TryAdd("IsNumeric", ff.IsNumeric.ToString().ToLower());
						dct.TryAdd("DefaultValue", ff.DefaultValue);
						ff.Dispose(); ff = null;
					} else if (ctl is FormFieldCollections.Base) {
						FormFieldCollections.Base ffc = (FormFieldCollections.Base)ctl;
						dct.TryAdd("Type", ((int)ffc.FieldType).ToString());
						dct.TryAdd("ControlID", ffc.LabelCell.ClientID);
						dct.TryAdd("Required", "false");
						dct.TryAdd("CheckForValidEmail", "false");
						dct.TryAdd("CheckForValidURL", "false");
						dct.TryAdd("IsNumeric", "false");
						dct.TryAdd("DefaultValue", "");
						ffc.Dispose(); ffc = null;
					} else if (ctl is LabelFormField) {
						LabelFormField lf = (LabelFormField)ctl;
						dct.TryAdd("Type", ((int)FormField.FormFieldControlType.Literal).ToString());
						dct.TryAdd("ControlID", lf.LabelCell.ClientID);
						dct.TryAdd("Required", "false");
						dct.TryAdd("CheckForValidEmail", "false");
						dct.TryAdd("CheckForValidURL", "false");
						dct.TryAdd("IsNumeric", "false");
						dct.TryAdd("DefaultValue", "");
						lf.Dispose(); lf = null;
					}
					_lstFields.Add(dct);
					dct = null;
					_intControlOrdinal += 1;
				}
				if (ctl.HasControls()) AddAllFormControls(ctl);
			}
		}

		public void RegisterScriptDescriptors() {
			GetScriptManagerControl();
			if (!this.DesignMode) _sm.RegisterScriptDescriptors(this);
		}

		/// <summary>
		/// Add a form control and all its links
		/// </summary>
		/// <param name="frm">The Form to add</param>
		/// <param name="intIndex">Unique index of the form</param>
		/// <param name="ctlNugget">Nugget we want to add it to (can be null - see Print.aspx.cs)</param>
		/// <param name="pnlLinks">If we're not adding this to a nugget we have to add the links to another panel</param>
		public static void AddForm(Forms.Base frm, int intIndex, Nuggets.Base ctlNugget, Panel pnlLinks) {
			frm.Index = intIndex;
			frm.RelatedNugget = ctlNugget;

			//buttons ('links')
			if (frm.ctlDesignBase.Links != null) {
				//to the top
				Container cnt = new Container();
				frm.ctlDesignBase.Links.InstantiateIn(cnt);
				Panel pnl = ControlBuilders.CreatePanel();
				pnl.Controls.Add(cnt);
				pnl.ID = string.Format("pnlFormLinks{0}", intIndex);
				if (ctlNugget != null) {
					ctlNugget.ctlDesignBase.pnlFormLinks.Controls.Add(pnl);
					ctlNugget.ctlDesignBase.FormLinksClientIDs.Add(pnl.ClientID);
				} else if (pnlLinks != null) {
					pnlLinks.Controls.Add(pnl);
				}
				frm.LinksHolder = pnl;
				cnt.Dispose(); cnt = null;

				//to the footer (but only for a nugget)
				if (ctlNugget != null) {
					Container cnt2 = new Container();
					frm.ctlDesignBase.Links.InstantiateIn(cnt2);
					Panel pnl2 = ControlBuilders.CreatePanel();
					pnl2.Controls.Add(cnt2);
					pnl2.ID = string.Format("pnlFormFooterLinks{0}", intIndex);
					ctlNugget.ctlDesignBase.pnlFormFooterLinks.Controls.Add(pnl2);
					ctlNugget.ctlDesignBase.FormFooterLinksClientIDs.Add(pnl2.ClientID);
					frm.FooterLinksHolder = pnl2;
					cnt2.Dispose(); cnt2 = null;
				}
			}
		}


		#endregion

		#region IScriptControl Members

		protected virtual IEnumerable<ScriptReference> GetScriptReferences() {
			return _srScriptReference;
		}

		protected virtual IEnumerable<ScriptDescriptor> GetScriptDescriptors() {
			if (_pnlLinksHolder != null) _scScriptControlDescriptor.AddElementProperty("pnlLinksHolder", _pnlLinksHolder.ClientID);
			if (FooterLinksHolder != null) _scScriptControlDescriptor.AddElementProperty("pnlFooterLinksHolder", _pnlFooterLinksHolder.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlLoading", ctlDesignBase.pnlLoading.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlSaving", ctlDesignBase.pnlSaving.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlSavedOK", ctlDesignBase.pnlSavedOK.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlHeader", ctlDesignBase.pnlHeader.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlValidateError", ctlDesignBase.pnlValidateError.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlValidateErrorText", ctlDesignBase.pnlValidateErrorText.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlContent", ctlDesignBase.pnlContent.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlContentInner", ctlDesignBase.pnlContentInner.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlExplain", ctlDesignBase.pnlExplain.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlNotes", ctlDesignBase.pnlNotes.ClientID);
			_scScriptControlDescriptor.AddProperty("intIndex", _intIndex);
			_scScriptControlDescriptor.AddProperty("blnShowQuickHelp", ctlDesignBase.ShowQuickHelp);
			_scScriptControlDescriptor.AddElementProperty("hypQuickHelp", ctlDesignBase.hypQuickHelp.ClientID);
			if (_ctlRelatedNugget != null) _scScriptControlDescriptor.AddElementProperty("ctlRelatedNugget", _ctlRelatedNugget.ctlDesignBase.ClientID);
			_scScriptControlDescriptor.AddElementProperty("h4", ctlDesignBase.h4.ClientID);
			_scScriptControlDescriptor.AddElementProperty("lblQuickHelpText", ctlDesignBase.lblQuickHelpText.ClientID);
			_scScriptControlDescriptor.AddProperty("objFieldOrdinals", _dctFieldOrdinals);
			_scScriptControlDescriptor.AddProperty("aryFields", _lstFields);
			_scScriptControlDescriptor.AddProperty("blnShowRequiredFieldsExplanation", ctlDesignBase.ShowRequiredFieldsExplanation);

			if (_ibtnSave != null) _scScriptControlDescriptor.AddElementProperty("ibtnSave", _ibtnSave.ClientID);
			if (_ibtnCancel != null) _scScriptControlDescriptor.AddElementProperty("ibtnCancel", _ibtnCancel.ClientID);
			if (_ibtnSave_Footer != null) _scScriptControlDescriptor.AddElementProperty("ibtnSave_Footer", _ibtnSave_Footer.ClientID);
			if (_ibtnCancel_Footer != null) _scScriptControlDescriptor.AddElementProperty("ibtnCancel_Footer", _ibtnCancel_Footer.ClientID);
			if (ctlDesignBase.ctlMultiStep != null) _scScriptControlDescriptor.AddComponentProperty("ctlMultiStep", ctlDesignBase.ctlMultiStep.ClientID);
			return new ScriptDescriptor[] { _scScriptControlDescriptor };
		}

		System.Collections.Generic.IEnumerable<ScriptDescriptor> IScriptControl.GetScriptDescriptors() { return GetScriptDescriptors(); }
		System.Collections.Generic.IEnumerable<ScriptReference> IScriptControl.GetScriptReferences() { return GetScriptReferences(); }

		#endregion


	}
}
