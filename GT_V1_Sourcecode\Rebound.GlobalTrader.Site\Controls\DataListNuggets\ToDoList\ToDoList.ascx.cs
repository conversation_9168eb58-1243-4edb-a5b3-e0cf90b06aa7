﻿
//Marker Changed by Date               Remarks
//[001] Bhooma          18/Feb/2022        CR:- Create New DataList Nugget for view to do list

using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site.Controls;
namespace Rebound.GlobalTrader.Site.Controls.DataListNuggets
{
    public partial class ToDoList : Base
    {

        private int _intToDoID = -1;
        public int ToDoID
        {
            get { return _intToDoID; }
            set { _intToDoID = value; }
        }

        #region Locals 
        protected IconButton _ibtnExportCSV;//[001]   
        protected IconButton _ibtnAdd;
        protected IconButton _ibtnEdit;
        protected IconButton _ibtnDelete;
        protected IconButton _ibtnMarkComplete;
        protected IconButton _ibtnMarkIncomplete;
        private FlexiDataTable _table;
        #endregion

        protected override void OnInit(EventArgs e)
        {
            SetDataListNuggetType("ToDoList");
            base.OnInit(e);
            WireUpControls();//[001]  
            TitleText = Functions.GetGlobalResource("Nuggets", "ToDoList");
            AddScriptReference("Controls.DataListNuggets.ToDoList.ToDoList.js");
            //_table = (FlexiDataTable)FindContentControl("tbl");
            SetupTable();
        }


        protected override void OnLoad(EventArgs e)
        {
            if (_intToDoID < 1) _intToDoID = _objQSManager.ToDoID;

            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.DataListNuggets.ToDoList", ctlDesignBase.ClientID);
            _scScriptControlDescriptor.AddElementProperty("ibtnExportCSV", _ibtnExportCSV.ClientID);
            _scScriptControlDescriptor.AddElementProperty("ibtnAdd", _ibtnAdd.ClientID);
            _scScriptControlDescriptor.AddElementProperty("ibtnEdit", _ibtnEdit.ClientID);
            _scScriptControlDescriptor.AddElementProperty("ibtnDelete", _ibtnDelete.ClientID);
            _scScriptControlDescriptor.AddElementProperty("ibtnMarkComplete", _ibtnMarkComplete.ClientID);
            _scScriptControlDescriptor.AddElementProperty("ibtnMarkIncomplete", _ibtnMarkIncomplete.ClientID);
            _scScriptControlDescriptor.AddProperty("intToDoID", _objQSManager.ToDoID);
            _scScriptControlDescriptor.AddProperty("intCompanyID", _objQSManager.CompanyID);
            _scScriptControlDescriptor.AddProperty("strCompanyName", _objQSManager.CompanyName);
            _scScriptControlDescriptor.AddComponentProperty("ctlSelect", FindLinksControl("ctlSelect").ClientID);
            // _scScriptControlDescriptor.AddComponentProperty("table", _table.ClientID);
            base.OnLoad(e);
        }
        protected override void GetSavedState()
        {
            base.GetSavedState();

            if (_objQSManager.QuoteNumber > 0)
            {
                SetFilterValue("QuoteNumber", _objQSManager.QuoteNumber);
            }
            if(_objQSManager.Category > 0)
            {
                SetFilterValue("TaskCategory", _objQSManager.Category);
            }
        }

        protected override void RenderAdditionalState()
        {
            base.RenderAdditionalState();
            var saleManvalue = GetSavedStateValue("Salesman");
            if (!string.IsNullOrEmpty(saleManvalue))
            {
                SetFilterValue("Salesman", Convert.ToInt32(saleManvalue));
            }
            else
            {
                SetFilterValue("Salesman", Convert.ToInt32(SessionManager.LoginID));
            }
        }

        private void SetupTable()
        {
            _tbl.AllowSelection = true;
            _tbl.AllowMultipleSelection = true;
            _tbl.SortColumnDirection = Rebound.GlobalTrader.Site.Enumerations.SortColumnDirection.DESC;
            //_tbl.Columns.Add(new FlexiDataColumn("CreatedDateFrom", WidthManager.GetWidth(WidthManager.ColumnWidth.LoginName), false));
            //_tbl.Columns.Add(new FlexiDataColumn("CreatedDateTo", WidthManager.GetWidth(WidthManager.ColumnWidth.Date), false));
            _tbl.Columns.Add(new FlexiDataColumn("TaskDateFrom", WidthManager.GetWidth(WidthManager.ColumnWidth.Date), false));
            _tbl.Columns.Add(new FlexiDataColumn("TaskDateTo", WidthManager.GetWidth(WidthManager.ColumnWidth.Date), false));
            _tbl.Columns.Add(new FlexiDataColumn("TaskReminderDate", WidthManager.GetWidth(WidthManager.ColumnWidth.LoginName), false));
            _tbl.Columns.Add(new FlexiDataColumn("TaskTitle", WidthManager.GetWidth(WidthManager.ColumnWidth.LoginName), false));
            _tbl.Columns.Add(new FlexiDataColumn("TaskType", "TaskCategory", WidthManager.GetWidth(WidthManager.ColumnWidth.LoginName), false));
            _tbl.Columns.Add(new FlexiDataColumn("TaskStatus", WidthManager.GetWidth(WidthManager.ColumnWidth.Quantity), false));
            _tbl.Columns.Add(new FlexiDataColumn("CustomerName", Unit.Empty, false));
            _tbl.Columns.Add(new FlexiDataColumn("SalesPerson", "Blank"));
            _tbl.Columns.Add(new FlexiDataColumn("QuoteNumber", WidthManager.GetWidth(WidthManager.ColumnWidth.PartNo), false));

        }
        private void WireUpControls()
        {
            _ibtnExportCSV = (IconButton)FindIconButton("ibtnExportCSV");
            _ibtnAdd = (IconButton)FindIconButton("ibtnAdd");
            _ibtnEdit = (IconButton)FindIconButton("ibtnEdit");
            _ibtnDelete = (IconButton)FindIconButton("ibtnDelete");
            _ibtnMarkComplete = (IconButton)FindIconButton("ibtnMarkComplete");
            _ibtnMarkIncomplete = (IconButton)FindIconButton("ibtnMarkIncomplete");

        }
    }
}
