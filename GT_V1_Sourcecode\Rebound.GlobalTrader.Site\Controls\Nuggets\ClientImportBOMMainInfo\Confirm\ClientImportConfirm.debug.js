///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference name="~/Controls/IconButton/IconButton.js" />
///<reference name="~/Controls/ReboundTextBox/ReboundTextBox.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
//[001]     S<PERSON>r   16-11-2016    Added combox and Get Receipient ID from Combo box.
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.ClientImportConfirm = function (element) {
    Rebound.GlobalTrader.Site.Controls.Forms.ClientImportConfirm.initializeBase(this, [element]);
    this._intBOMID = -1;
   
};

Rebound.GlobalTrader.Site.Controls.Forms.ClientImportConfirm.prototype = {

    get_intBOMID: function() { return this._intBOMID; }, set_intBOMID: function(value) { if (this._intBOMID !== value) this._intBOMID = value; },
   
    initialize: function() {
        Rebound.GlobalTrader.Site.Controls.Forms.ClientImportConfirm.callBaseMethod(this, "initialize");
        this.addShown(Function.createDelegate(this, this.formShown));
    },

    dispose: function() {
        if (this.isDisposed) return;
        this._intBOMID = null;
        this._intContact2No = null;
        Rebound.GlobalTrader.Site.Controls.Forms.ClientImportConfirm.callBaseMethod(this, "dispose");
    },

    formShown: function() {
        if (this._blnFirstTimeShown) {
            this._ctlConfirm = this.getFieldComponent("ctlConfirm");
            this._ctlConfirm.addClickYesEvent(Function.createDelegate(this, this.yesClicked));
            this._ctlConfirm.addClickNoEvent(Function.createDelegate(this, this.noClicked));
        }

     
    },

    noClicked1: function () {
        this.onNotConfirmed();
    },

    yesClicked: function () {
      // if (!this.validateForm()) return;
        this.showSaving(true);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("Controls/Nuggets/ClientImportBOMMainInfo");
        obj.set_DataObject("ClientImportBOMMainInfo");
        obj.set_DataAction("MarkAsComplete");
        obj.addParameter("id", this._intBOMID);
        obj.addDataOK(Function.createDelegate(this, this.saveConfirmComplete));
        obj.addError(Function.createDelegate(this, this.saveError));
        obj.addTimeout(Function.createDelegate(this, this.saveError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
        this.onSave();
    },
    //validateForm: function () {
    //    this.onValidate();
    //    var blnOK = true;


    //    if (!this.checkFieldEntered("ctlSalesperson")) blnOK = false;
    //    if (this.getFieldValue("ctlSalesperson") <= 0) {
    //        blnOK = false;
    //        this.showError(true, "Please select buyer");
    //    }
    //    if (!blnOK) this.showError(true);
    //    if (this._blnReqInValid == true) {
    //        blnOK = false;
    //        this.showError(true, this._ValidMessage);
    //    }

    //    return blnOK;
    //},

    noClicked: function() {
        this.showSaving(false);
        this.onNotConfirmed();
    },

    saveError: function(args) {
        this.showSaving(false);
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    saveConfirmComplete: function (args) {
        if (args._result.Result == true) {
            this.showSavedOK(true);
            this.onSaveComplete();
        } else {
            this._strErrorMessage = args._errorMessage;
            this.onSaveError();
        }
    }

};

Rebound.GlobalTrader.Site.Controls.Forms.ClientImportConfirm.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.ClientImportConfirm", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
