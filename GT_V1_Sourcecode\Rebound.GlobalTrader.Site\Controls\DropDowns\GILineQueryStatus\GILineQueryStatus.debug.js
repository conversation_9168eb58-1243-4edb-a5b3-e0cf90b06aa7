///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//
// RP 06.10.2009:
// - Changes due to changes in base class
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");

Rebound.GlobalTrader.Site.Controls.DropDowns.GILineQueryStatus = function (element) {
    Rebound.GlobalTrader.Site.Controls.DropDowns.GILineQueryStatus.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.DropDowns.GILineQueryStatus.prototype = {
    get_IsPartialGIQueryStatus: function () { return this._IsPartialGIQueryStatus; }, set_IsPartialGIQueryStatus: function (v) { if (this._IsPartialGIQueryStatus !== v) this._IsPartialGIQueryStatus = v; },

    initialize: function () {
        Rebound.GlobalTrader.Site.Controls.DropDowns.GILineQueryStatus.callBaseMethod(this, "initialize");
        this.addSetupDataCall(Function.createDelegate(this, this.setupDataCall));
        this.addGetDataOK(Function.createDelegate(this, this.dataCallOK));
    },

    dispose: function () {
        if (this.isDisposed) return;
        this._IsPartialGIQueryStatus = null;
        Rebound.GlobalTrader.Site.Controls.DropDowns.GILineQueryStatus.callBaseMethod(this, "dispose");
    },

    setupDataCall: function () {
        this._objData.set_PathToData("controls/DropDowns/GILineQueryStatus");
        this._objData.set_DataObject("GILineQueryStatus");
        this._objData.set_DataAction("GetData");
        this._objData.addParameter("IsPartialGIQueryStatus", this._IsPartialGIQueryStatus);
    },

    dataCallOK: function () {
        var result = this._objData._result;
        if (result != null) {
            if (result.Types) {
                for (var i = 0; i < result.Types.length; i++) {
                    this.addOption(result.Types[i].Name, result.Types[i].ID);
                }
            }
        }

    }

};

Rebound.GlobalTrader.Site.Controls.DropDowns.GILineQueryStatus.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.GILineQueryStatus", Rebound.GlobalTrader.Site.Controls.DropDowns.Base);
