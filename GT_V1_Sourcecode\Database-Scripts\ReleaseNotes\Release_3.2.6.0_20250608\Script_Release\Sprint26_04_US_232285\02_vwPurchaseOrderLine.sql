﻿      
/*         
===========================================================================================        
TASK           UPDATED BY     DATE            ACTION      DESCRIPTION    
[US-209108]    Phuc Hoang     15-Apr-2025     Create      PowerApps for PoR like SoR      
[US-232285]    CuongDox       18-Apr-2025     Update      PO - Add ability to modify ECCN    
===========================================================================================        
*/        
  
      
CREATE  OR ALTER VIEW [dbo].[vwPurchaseOrderLine]      
--*********************************************************************************************                                                               
--* SK 20.01.2010:                                                               
--* - add ClientNo ex vwPurchaseOrder                                                               
--*                                                               
--* SK 28.07.2009:                                                               
--* - add Notes                                                               
--*Marker     Changed by      Date          Remarks                                                               
--*[001]      Vinay           18/09/2012    Ref:## - Display Purchase Country         
--*[002]      Ravi            18-09-2023    RP-2339  AS6081 (Show AS6081 on detail screens)       
--*********************************************************************************************                                                               
AS     
    SELECT      
       pol.PurchaseOrderLineId                                                     
  , pol.PurchaseOrderNo                                                     
  , pol.FullPart                                                     
  , pol.Part                                                     
  , pol.ManufacturerNo                                                     
  , pol.DateCode                                                     
  , pol.PackageNo                                                     
  , pol.Quantity                                                     
  , pol.Price                                                     
  , pol.DeliveryDate                                                     
  , pol.ReceivingNotes                                                     
  , pol.Taxable                                                     
  , pol.ProductNo                                                     
  , pol.Posted                                                     
  , pol.ShipInCost                                                     
  , pol.SupplierPart                                                     
  , pol.Inactive                                                     
  , pol.Closed                                                     
  , pol.ROHS                                                     
  , ISNULL(pol.Notes, '') AS LineNotes                                                     
  , pol.UpdatedBy                                                     
  , pol.DLUP                                                     
  , ISNULL((SELECT      
    SUM(x.Quantity)      
   FROM dbo.tbGoodsInLine x      
   WHERE pol.PurchaseOrderLineId = x.PurchaseOrderLineNo)                                                     
  , 0) AS QuantityReceived                                                     
                                                 
  , ISNULL((SELECT      
    SUM(y.QuantityAllocated)      
   FROM dbo.tbAllocation y      
    JOIN dbo.tbStock st      
    ON pol.PurchaseOrderLineId = st.PurchaseOrderLineNo      
   -- 30 Sep 2016: Espire- Unpost issue in PO line                                                 
   --AND po.ClientNo = st.ClientNo                                                     
   WHERE y.StockNo = st.StockId)                   
  , 0) AS QuantityAllocated                                                    
                                                  
                                                   
  , ISNULL((SELECT      
    SUM(z.ShipInCost)      
   FROM dbo.tbGoodsInLine z      
   WHERE pol.PurchaseOrderLineId = z.PurchaseOrderLineNo)                                                     
  , 0) AS GIShipInCost                   
  , pr.ProductName                                               
  , pr.ProductDescription                                                     
  , pr.DutyCode AS ProductDutyCode                           
  , pk.PackageName                                                     
  , pk.PackageDescription                          
  , po.ImportCountryShippingCost                                                     
  , po.PurchaseOrderNumber                      
  , po.CurrencyCode                                         
  , po.CurrencyNo                                                     
  , po.CurrencyDescription                                                     
  , mf.ManufacturerName                                                     
  , mf.ManufacturerCode                                          
  , po.CompanyNo                                                     
  , po.CompanyName                                                     
  , po.DateOrdered                                                     
  , po.TaxRate                                                     
  , po.ClientNo                                                     
  --[001] code start                                                      
  , po.ImportCountryNo                                                     
  --[001] code end                                                             
  , pol.PromiseDate                                                    
  , pol.POSerialNo                                                     
  , 0 AS PurchaseQuoteId                                                 
  , 0 AS PurchaseQuoteNumber                                  
  , getdate() AS PurchaseRequestDate                                                  
 -- ,(Select TOP 1 BomNo from tbPurchaseRequestLine where PurchaseRequestNo=prq.PurchaseRequestId) AS BomNo                                                    
  , ipol.BOMNo AS BomNo                                                    
  --,(Select TOP 1 BOMName from tbbom where BomId=BomNo) AS BOMName                                                  
  --,'' AS BOMName                                                  
  , ipol.Price as ClientPrice                                                 
  , isnull(ipo.InternalPurchaseOrderId,0) as InternalPurchaseOrderId                                                 
  , ipo.CurrencyNo as ClientCurrencyNo                                                 
  , ipol.EstimatedShippingCost as ClientEstShipCost                                                  
  , ipo.ClientNo as IPOClientNo                                                 
  , po.BuyerName                                                 
  , po.ContactName                                                 
  , po.TypeNo                                                 
  , ipol.InternalPurchaseOrderLineId                                                 
  , sol.SalesOrderLineId AS SalesOrderLineNo                                              
  , ipo.InternalPurchaseOrderNumber                                               
  , ipo.HubCurrencyNo                                               
  , ipo.CurrencyDate                                               
  , isnull(pr.Inactive,0) as ProductInactive                                               
  , pol.ReqSerialNo                                               
  , pol.MSLLevel                                               
  , isnull(pol.SupplierWarranty,0) as SupplierWarranty                                               
  , isnull(pr.IsHazardous,0) as IsProdHazardous                                               
  , pol.PrintHazardous                                               
  , pol.ReleaseBy                                           
  , pol.OriginalDeliveryDate                                           
   , col.GlobalCountryName as   CountryOfOrigin                                         
  , pol.LifeCycleStage                                 
  , pol.HTSCode                                         
  , pol.AveragePrice                                         
  , pol.Packing                                         
  , pol.PackagingSize                                         
  , col.GlobalCountryName as IHSCountryOfOrigin                                       
  , pol.Descriptions                                   
  , pol.IHSProduct                                  
  , CASE WHEN pol.ECCNCode is null               
       THEN ('[Blank]')               
       ELSE (pol.ECCNCode)               
     END AS ECCNCode   
  , CASE WHEN ISNULL(pol.ECCNCode,'') <> ''  
  THEN (SELECT TOP 1 ECCNId FROM tbECCN WHERE ECCNCode = pol.ECCNCode)   
 ELSE  
  0  
 END as ECCNId    
  ,pol.ECCNCode as ECCNCodeEdit  
  --, isnull(sk.ClientLandedCost, sk.LandedCost) as LandedCost                                   
  --, isnull(sk.ClientLandedCost, sk.LandedCost) as ClientLandedCost                                
  , isnull(pol.RepeatOrder,0) as RepeatOrder                                
  , isnull(pr.IsOrderViaIPOonly,0) as IsOrderViaIPOonly                   
  , isnull(pr.IsRestrictedProduct ,0) as IsRestrictedProduct                    
 --,null as ECCNClientNotify                       
 --,null as ECCNSubject                       
 --,null as ECCNMessage                    
 --,null as  WarningMessag                               
 , CASE WHEN pol.ECCNCode is null               
               THEN (select top 1      
            ECCNClientNotify      
        from tbECCN eccn      
        where eccn.ECCNCode='[Blank]')               
               ELSE (select top 1      
            ECCNClientNotify      
        from tbECCN eccn      
        where eccn.ECCNCode=pol.ECCNCode)               
          END AS ECCNClientNotify               
 , CASE WHEN pol.ECCNCode is null               
            THEN (select top 1      
            ECCNSubject      
        from tbECCN eccn      
        where eccn.ECCNCode='[Blank]')               
            ELSE (select top 1      
            ECCNSubject      
        from tbECCN eccn      
        where eccn.ECCNCode=pol.ECCNCode)               
        END AS ECCNSubject                     
 , CASE WHEN pol.ECCNCode is null               
            THEN (select top 1      
            ECCNMessage      
        from tbECCN eccn      
        where eccn.ECCNCode='[Blank]')               
            ELSE (select top 1      
            ECCNMessage      
        from tbECCN eccn      
        where eccn.ECCNCode=pol.ECCNCode)               
        END AS ECCNMessage                     
 , CASE WHEN pol.ECCNCode is null               
            THEN (select top 1      
            Notes      
        from tbECCN eccn      
        where eccn.ECCNCode='[Blank]')               
            ELSE (select top 1      
            Notes      
        from tbECCN eccn      
        where eccn.ECCNCode=pol.ECCNCode)               
        END AS WarningMessage        
 , ISNULL(pol.AS6081,0) AS AS6081        
 , so.SalesOrderNumber  
 ,so.SalesOrderId  
 ,(SELECT position  
 FROM (  
  SELECT   
   SalesOrderLineId,  
   ROW_NUMBER() OVER (PARTITION BY SalesOrderNo ORDER BY SalesOrderLineId) AS position  
  FROM tbSalesOrderLine  
  WHERE SalesOrderNo = so.SalesOrderId  
 ) AS ranked  
 WHERE SalesOrderLineId = sol.SalesOrderLineId) as SOLineRank  
,(SELECT position  
 FROM (  
  SELECT   
   PurchaseOrderLineId,  
   ROW_NUMBER() OVER (PARTITION BY PurchaseOrderNo ORDER BY PurchaseOrderLineId) AS position  
  FROM tbPurchaseOrderLine  
  WHERE PurchaseOrderNo = pol.PurchaseOrderNo  
 ) AS ranked  
 WHERE PurchaseOrderLineId = pol.PurchaseOrderLineId) as POLineRank  
  
    FROM dbo.tbPurchaseOrderLine pol      
        LEFT JOIN dbo.tbProduct pr ON pol.ProductNo = pr.ProductId      
        LEFT JOIN dbo.tbInternalPurchaseOrderLine ipol ON pol.PurchaseOrderLineId = ipol.PurchaseOrderLineNo      
        LEFT JOIN tbInternalPurchaseOrder ipo on ipol.InternalPurchaseOrderNo = ipo.InternalPurchaseOrderId      
        --LEFT JOIN tbPurchaseRequestLineDetail prld ON ipol.PurchaseQuoteLineNo = prld.PurchaseRequestLineNo                                                 
        --LEFT JOIN tbPurchaseRequestLine prl ON prl.PurchaseRequestLineId = prld.PurchaseRequestLineNo                                                 
        --LEFT JOIN tbPurchaseRequest prq ON prq.PurchaseRequestId = prl.PurchaseRequestNo                                                 
        LEFT JOIN dbo.tbPackage pk ON pol.PackageNo = pk.PackageId      
        JOIN dbo.vwPurchaseOrder po ON pol.PurchaseOrderNo = po.PurchaseOrderId      
        LEFT JOIN dbo.tbManufacturer mf ON pol.ManufacturerNo = mf.ManufacturerId      
        LEFT JOIN dbo.tbGlobalCountryList col ON pol.CountryOfOriginNo = col.GlobalCountryId       
  LEFT JOIN dbo.tbPurchaseOrderLineStatus pols ON pols.PurchaseOrderLineStatusId = pol.POLineStatusNo       
  LEFT JOIN  tbStock sk ON sk.PurchaseOrderLineNo=pol.PurchaseOrderLineId  
  LEFT JOIN    tbAllocation al ON sk.StockId = al.StockNo
  LEFT JOIN tbSalesOrderLine sol ON sol.SalesOrderLineId = al.SalesOrderLineNo       
  LEFT JOIN tbSalesOrder so ON so.SalesOrderId = sol.SalesOrderNo