Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DataListNuggets");Rebound.GlobalTrader.Site.Controls.DataListNuggets.ClientInvoice=function(n){Rebound.GlobalTrader.Site.Controls.DataListNuggets.ClientInvoice.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DataListNuggets.ClientInvoice.prototype={get_blnShowCanNotBeExported:function(){return this._blnShowCanNotBeExported},set_blnShowCanNotBeExported:function(n){this._blnShowCanNotBeExported!==n&&(this._blnShowCanNotBeExported=n)},get_blnPOHub:function(){return this._blnPOHub},set_blnPOHub:function(n){this._blnPOHub!==n&&(this._blnPOHub=n)},initialize:function(){this.addSetupDataCallEvent(Function.createDelegate(this,this.setupDataCall));this.addGetDataOKEvent(Function.createDelegate(this,this.getDataOK));this.addInitCompleteEvent(Function.createDelegate(this,this.initAfterBaseIsReady));this._strPathToData="controls/DataListNuggets/ClientInvoice";this._strDataObject="ClientInvoice";this.showFilterField("ctlStatus",!this._blnShowCanNotBeExported);Rebound.GlobalTrader.Site.Controls.DataListNuggets.ClientInvoice.callBaseMethod(this,"initialize")},initAfterBaseIsReady:function(){this.addPageTabChangedEvent(Function.createDelegate(this,this.pageTabChanged));this.updateFilterVisibility();this.getData()},dispose:function(){this.isDisposed||(this._blnShowCanNotBeExported=null,this._blnPOHub=null,Rebound.GlobalTrader.Site.Controls.DataListNuggets.ClientInvoice.callBaseMethod(this,"dispose"))},pageTabChanged:function(){this._table._intCurrentPage=1;this._blnShowCanNotBeExported=this._intCurrentTab==1;this.showFilterField("ctlStatus",this._intCurrentTab==0);this.updateFilterVisibility();this.getData()},setupDataCall:function(){this._objData.addParameter("CanNotBeExported",this._blnShowCanNotBeExported)},getDataOK:function(){for(var n,i,t=0,r=this._objResult.Results.length;t<r;t++)n=this._objResult.Results[t],i=this._blnPOHub==!0?[$RGT_nubButton_ClientInvoice(n.ID,n.No),$RGT_nubButton_Company(n.CompanyNo,n.Name),$R_FN.writeDoubleCellValue($RGT_nubButton_GoodsIn(n.GI,n.GINo),""),$R_FN.writeDoubleCellValue($RGT_nubButton_PurchaseOrder(n.PONo,n.PO),$RGT_nubButton_InternalPurchaseOrder(n.IPONo,n.IPO)),$R_FN.setCleanTextValue(n.INVDate),$R_FN.writePartNo(n.Part,""),n.Value]:[$RGT_nubButton_ClientInvoice(n.ID,n.No),$RGT_nubButton_Company(n.CompanyNo,n.Name),$R_FN.writeDoubleCellValue($RGT_nubButton_GoodsIn(n.GI,n.GINo),""),$R_FN.writeDoubleCellValue(n.PO,$RGT_nubButton_InternalPurchaseOrder(n.IPONo,n.IPO)),$R_FN.setCleanTextValue(n.INVDate),$R_FN.writePartNo(n.Part,""),n.Value],this._table.addRow(i,n.ID,!1),i=null,n=null},updateFilterVisibility:function(){this.getFilterField("ctlClient").show(this._blnPOHub)}};Rebound.GlobalTrader.Site.Controls.DataListNuggets.ClientInvoice.registerClass("Rebound.GlobalTrader.Site.Controls.DataListNuggets.ClientInvoice",Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base,Sys.IDisposable);