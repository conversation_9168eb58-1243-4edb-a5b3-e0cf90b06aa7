///<reference name="MicrosoftAjax.js" />
//-----------------------------------------------------------------------------------------
// RP 06.10.2009:
// - Changes due to changes in base class
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");

Rebound.GlobalTrader.Site.Controls.DropDowns.Region = function(element) { 
	Rebound.GlobalTrader.Site.Controls.DropDowns.Region.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.DropDowns.Region.prototype = {
//get_intPOHubClientNo: function() { return this._intPOHubClientNo; }, set_intPOHubClientNo: function(v) { if (this._intPOHubClientNo !== v) this._intPOHubClientNo = v; },
	
	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.DropDowns.Region.callBaseMethod(this, "initialize");
		this.addSetupDataCall(Function.createDelegate(this, this.setupDataCall));
		this.addGetDataOK(Function.createDelegate(this, this.dataCallOK));
	},

	dispose: function() { 
		if (this.isDisposed) return;
		this._intPOHubClientNo = null;
		Rebound.GlobalTrader.Site.Controls.DropDowns.Region.callBaseMethod(this, "dispose");
	},
	
	setupDataCall: function() { 
		this._objData.set_PathToData("controls/DropDowns/Region");
		this._objData.set_DataObject("Region");
		this._objData.set_DataAction("GetData");
//		this._objData.addParameter("POHubClientNo", this._intPOHubClientNo);
	},

	dataCallOK: function() { 
		var result = this._objData._result;
		if (result.Regions) {
			for (var i = 0; i < result.Regions.length; i++) {
				this.addOption(result.Regions[i].Name, result.Regions[i].ID);
			}
		}
	}
	
};

Rebound.GlobalTrader.Site.Controls.DropDowns.Region.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.Region", Rebound.GlobalTrader.Site.Controls.DropDowns.Base);
