Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.HubImportSourcingResult=function(n){Rebound.GlobalTrader.Site.Controls.Forms.HubImportSourcingResult.initializeBase(this,[n]);this._intBOMID=0};Rebound.GlobalTrader.Site.Controls.Forms.HubImportSourcingResult.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.HubImportSourcingResult.callBaseMethod(this,"initialize")},dispose:function(){this.isDisposed||(this._intBOMID=null,Rebound.GlobalTrader.Site.Controls.Forms.HubImportSourcingResult.callBaseMethod(this,"dispose"))},importExcelData:function(n,t,i){$("#divLoader").show();var r=new Rebound.GlobalTrader.Site.Data;return r._intTimeoutMilliseconds=6e5,r.set_PathToData("controls/Nuggets/BOMItems/HubImportSourcingResult"),r.set_DataObject("HubImportSourcingResult"),r.set_DataAction("ImportExcelData"),r.addParameter("originalFilename",n),r.addParameter("generatedFilename",t),r.addParameter("ColumnHeader",i),r.addParameter("BOMNo",this._intBOMID),r.addDataOK(Function.createDelegate(this,this.importExcelDataOK)),r.addError(Function.createDelegate(this,this.importExcelDataError)),r.addTimeout(Function.createDelegate(this,this.importExcelDataError)),$R_DQ.addToQueue(r),$R_DQ.processQueue(),r=null,!0},importExcelDataOK:function(n){flogId=n._result.FileLogId;$("#divLoader").hide();$("#btnDisplayCsvData").prop("disabled",!1);$("#sourcingExcelUpload").prop("disabled",!0).css("opacity",.5);$("#fileUploadWraper").addClass("prevent-upload");$('input:checkbox[id="chkFileCCH"]').prop("disabled",!0);$("input:file").filter(function(){return this.files.length==0}).prop("disabled",!0);var t=n._result.IsLimitExceeded;t&&alert(n._result.LimitErrorMessage)},importExcelDataError:function(n){alert(n._errorMessage.split("<br/>")[0]);$("#divLoader").hide()},checkUnsaveData:function(){return isGridModified.call(this)},resetForm:function(){reset.call(this)}};Rebound.GlobalTrader.Site.Controls.Forms.HubImportSourcingResult.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.HubImportSourcingResult",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);