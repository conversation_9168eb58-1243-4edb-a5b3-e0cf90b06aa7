<%@ Control Language="C#" CodeBehind="CreditMainInfo_Export.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.CreditMainInfo_Export" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>

<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="false" AllowQuickHelp="false">

	<Explanation>
		<asp:Label ID="lblExplainExport" runat="server" CssClass="invisible"><%=Functions.GetGlobalResource("Messages", "ConfirmExportCredit")%></asp:Label>
		<asp:Label ID="lblExplainRelease" runat="server" CssClass="invisible"><%=Functions.GetGlobalResource("Messages", "ConfirmReleaseCredit")%></asp:Label>
	</Explanation>
	
	<Content>
	
		<ReboundUI_Table:Form id="frm" runat="server">
		
            <ReboundUI_Form:FormField id="ctlCreditNote" runat="server" FieldID="lblCreditNote" ResourceTitle="CreditNoteNo" >
	            <Field><asp:Label ID="lblCreditNote" runat="server" /></Field>
            </ReboundUI_Form:FormField>
            
            <ReboundUI_Form:FormField id="ctlCustomer" runat="server" FieldID="lblCustomer" ResourceTitle="Customer">
	            <Field><asp:Label ID="lblCustomer" runat="server" /></Field>
            </ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlConfirm" runat="server" FieldID="ctlConfirmation" ResourceTitle="Confirm">
				<Field><ReboundUI:Confirmation ID="ctlConfirmation" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			
		</ReboundUI_Table:Form>
		
	</Content>
	
</ReboundUI_Form:DesignBase>
