using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Collections;
using Rebound.GlobalTrader.Site.Controls.Forms;
using Rebound.GlobalTrader.Site.Controls;
using Rebound.GlobalTrader.Site;

namespace Rebound.GlobalTrader.Site.Controls.FormFieldCollections {
	[DefaultProperty("")]
	[ToolboxData("<{0}:Address runat=server></{0}:Address>")]
	public class Address : Base, INamingContainer {

		#region Locals

		private Table _tbl;
		private FormField _ctlAddressName;
		private FormField _ctlLine1;
		private FormField _ctlLine2;
		private FormField _ctlLine3;
		private FormField _ctlTown;
		private FormField _ctlCounty;
		private FormField _ctlState;
		private FormField _ctlCountry;
		private FormField _ctlPostcode;

		#endregion

		#region Properties
		private string _strTitleResource = "Address";
		public string TitleResource {
			get { return _strTitleResource; }
			set { _strTitleResource = value; }
		}

		private bool _blnRequireFields = true;
		public bool RequireFields {
			get { return _blnRequireFields; }
			set { _blnRequireFields = value; }
		}

		private bool _blnAllowNonEntry = true;
		public bool AllowNonEntry {
			get { return _blnAllowNonEntry; }
			set { _blnAllowNonEntry = value; }
		}
        private int _intMaxAddrLength = 50;
        public int MaxAddrLength
        {
            get { return _intMaxAddrLength; }
            set { _intMaxAddrLength = value; }
        }

		#endregion

		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
			AddScriptReference("Controls.FormFieldCollections.Address.Address");
			RemoveCSSClass = true;
		}

		protected override void OnLoad(EventArgs e) {
			_tbl = ControlBuilders.CreateTable();
			_tbl.Width = Unit.Percentage(100);

			//AddressName
			_ctlAddressName = new FormField();
			_ctlAddressName.ID = "ctlAddressName";
			_ctlAddressName.ResourceTitle = string.Format("{0}Name", _strTitleResource);
			_ctlAddressName.FieldID = "txtAddressName";
			if (RequireFields) _ctlAddressName.DisplayRequiredFieldMarkerOnly = true;//display required field markers (without including field in auto-validation checks)
			ReboundTextBox txtAddressName = new ReboundTextBox();
			txtAddressName.ID = "txtAddressName";
			txtAddressName.Width = 400;
			_ctlAddressName.AddFieldControl(txtAddressName);
            txtAddressName.MaxLength = _intMaxAddrLength;
            _tbl.Rows.Add(_ctlAddressName);

			//Line 1
			_ctlLine1 = new FormField();
			_ctlLine1.ID = "ctlLine1";
			_ctlLine1.ResourceTitle = _strTitleResource;
			_ctlLine1.FieldID = "txtLine1";
			if (RequireFields) _ctlLine1.DisplayRequiredFieldMarkerOnly = true;//display required field markers (without including field in auto-validation checks)
			ReboundTextBox txtLine1 = new ReboundTextBox();
			txtLine1.ID = "txtLine1";
			txtLine1.Width = 400;
            txtLine1.MaxLength = _intMaxAddrLength;
            _ctlLine1.AddFieldControl(txtLine1);
			_tbl.Rows.Add(_ctlLine1);

			//Line 2
			_ctlLine2 = new FormField();
			_ctlLine2.ID = "ctlLine2";
			_ctlLine2.FieldID = "txtLine2";
			ReboundTextBox txtLine2 = new ReboundTextBox();
			txtLine2.ID = "txtLine2";
			txtLine2.Width = 400;
            txtLine2.MaxLength = _intMaxAddrLength;
            _ctlLine2.AddFieldControl(txtLine2);
			_tbl.Rows.Add(_ctlLine2);

			//Line 3
			_ctlLine3 = new FormField();
			_ctlLine3.ID = "ctlLine3";
			_ctlLine3.FieldID = "txtLine3";
			ReboundTextBox txtLine3 = new ReboundTextBox();
			txtLine3.ID = "txtLine3";
			txtLine3.Width = 400;
            txtLine3.MaxLength = _intMaxAddrLength;
            _ctlLine3.AddFieldControl(txtLine3);
			_tbl.Rows.Add(_ctlLine3);

			//Town
			_ctlTown = new FormField();
			_ctlTown.ID = "ctlTown";
			_ctlTown.ResourceTitle = "CityTown";
			_ctlTown.FieldID = "txtTown";
			if (RequireFields) _ctlTown.DisplayRequiredFieldMarkerOnly = true;//display required field markers (without including field in auto-validation checks)
			ReboundTextBox txtTown = new ReboundTextBox();
			txtTown.ID = "txtTown";
			txtTown.Width = 200;
            txtTown.MaxLength = _intMaxAddrLength;
            _ctlTown.AddFieldControl(txtTown);
			_tbl.Rows.Add(_ctlTown);

			//County
			_ctlCounty = new FormField();
			_ctlCounty.ID = "ctlCounty";
			_ctlCounty.ResourceTitle = "County";
			_ctlCounty.FieldID = "txtCounty";
			ReboundTextBox txtCounty = new ReboundTextBox();
			txtCounty.ID = "txtCounty";
			txtCounty.Width = 200;
            txtCounty.MaxLength = _intMaxAddrLength;
            _ctlCounty.AddFieldControl(txtCounty);
			_tbl.Rows.Add(_ctlCounty);

			//State
			_ctlState = new FormField();
			_ctlState.ID = "ctlState";
			_ctlState.ResourceTitle = "State";
			_ctlState.FieldID = "txtState";
			ReboundTextBox txtState = new ReboundTextBox();
			txtState.ID = "txtState";
			txtState.Width = 50;
			txtState.UppercaseOnly = true;
			txtState.MaxLength = 3;
			_ctlState.AddFieldControl(txtState);
			_tbl.Rows.Add(_ctlState);

			//Postcode
			_ctlPostcode = new FormField();
			_ctlPostcode.ID = "ctlPostcode";
			_ctlPostcode.ResourceTitle = "Postcode";
			_ctlPostcode.FieldID = "txtPostcode";
			ReboundTextBox txtPostcode = new ReboundTextBox();
			txtPostcode.ID = "txtPostcode";
			txtPostcode.UppercaseOnly = true;
			txtPostcode.Width = 140;
			_ctlPostcode.AddFieldControl(txtPostcode);
			_tbl.Rows.Add(_ctlPostcode);

			//Country
			_ctlCountry = new FormField();
			_ctlCountry.ID = "ctlCountry";
			_ctlCountry.ResourceTitle = "Country";
			_ctlCountry.FieldID = "ddlCountry";
			if (RequireFields) _ctlCountry.DisplayRequiredFieldMarkerOnly = true;//display required field markers (without including field in auto-validation checks)
			Controls.DropDowns.Base ddlCountry = DropDownManager.GetDropDown("Rebound.GlobalTrader.Site", "Country");
			ddlCountry.ID = "ddlCountry";
			_ctlCountry.AddFieldControl(ddlCountry);
			_tbl.Rows.Add(_ctlCountry);

			AddControl(_tbl);
			SetupScriptDescriptors();
			base.OnLoad(e);
		}

		private void SetupScriptDescriptors() {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.FormFieldCollections.Address", this.ClientID);
			_scScriptControlDescriptor.AddProperty("blnRequireFields", RequireFields);
			_scScriptControlDescriptor.AddProperty("blnAllowNonEntry", AllowNonEntry);
		}

	}
}
