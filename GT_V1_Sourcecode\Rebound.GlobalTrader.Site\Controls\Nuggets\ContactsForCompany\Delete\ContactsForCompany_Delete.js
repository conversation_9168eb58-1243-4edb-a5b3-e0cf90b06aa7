Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.ContactsForCompany_Delete=function(n){Rebound.GlobalTrader.Site.Controls.Forms.ContactsForCompany_Delete.initializeBase(this,[n]);this._intContactID=-1;this._strContactName=""};Rebound.GlobalTrader.Site.Controls.Forms.ContactsForCompany_Delete.prototype={get_intContactID:function(){return this._intContactID},set_intContactID:function(n){this._intContactID!==n&&(this._intContactID=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.ContactsForCompany_Delete.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown))},dispose:function(){this.isDisposed||(this._ctlConfirm&&this._ctlConfirm.dispose(),this._intContactID=null,this._strContactName=null,this._ctlConfirm=null,Rebound.GlobalTrader.Site.Controls.Forms.ContactsForCompany_Delete.callBaseMethod(this,"dispose"))},formShown:function(){this._blnFirstTimeShown&&(this._ctlConfirm=this.getFieldComponent("ctlConfirm"),this._ctlConfirm.addClickYesEvent(Function.createDelegate(this,this.yesClicked)),this._ctlConfirm.addClickNoEvent(Function.createDelegate(this,this.noClicked)))},yesClicked:function(){this.showSaving(!0);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/ContactsForCompany");n.set_DataObject("ContactsForCompany");n.set_DataAction("Delete");n.addParameter("id",this._intContactID);n.addDataOK(Function.createDelegate(this,this.saveDeleteComplete));n.addError(Function.createDelegate(this,this.saveDeleteError));n.addTimeout(Function.createDelegate(this,this.saveDeleteError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},noClicked:function(){this.onNotConfirmed()},saveDeleteError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},saveDeleteComplete:function(n){this.showSaving(!1);n._result.Result==!0?this.onSaveComplete():(this._strErrorMessage=n._errorMessage,this.onSaveError())}};Rebound.GlobalTrader.Site.Controls.Forms.ContactsForCompany_Delete.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.ContactsForCompany_Delete",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);