﻿//-----------------------------------------------------------------------------------------
// RP 28.10.2009:
// - Remove "no value"
//-----------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;

namespace Rebound.GlobalTrader.Site.Controls.DropDowns {
	public partial class DocumentType : Base {

		protected override void OnInit(EventArgs e) {
			CanAddTo = false;
			//IncludeNoValue = false;
			//NoValue_Value = "-1";
			//InitialValue = "0";
			base.OnInit(e);
		}

		protected override void OnLoad(EventArgs e) {
            SetDropDownType("DocumentType");
            AddScriptReference("Controls.DropDowns.DocumentType.DocumentType");
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.DropDowns.DocumentType", ClientID);
			base.OnLoad(e);
		}

	}
}