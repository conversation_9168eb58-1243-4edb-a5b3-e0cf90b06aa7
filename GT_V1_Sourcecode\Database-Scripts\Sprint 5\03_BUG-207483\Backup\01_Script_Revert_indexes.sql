﻿/* 
===========================================================================================
TASK      			UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[BUG-207483]		Ngai To				02-Jul-2024		FIX				Bug 207483: [PROD Bug] CPU Spike - DB deadlock in the existing KUB Assistance feature
===========================================================================================
*/

DROP INDEX PK_tb_KubAveragePrice_CacheDetails ON dbo.tb_KubAveragePrice_CacheDetails
DROP INDEX IX_tb_KubAveragePrice_CacheDetails_Part ON dbo.tb_KubAveragePrice_CacheDetails

DROP INDEX PK_tb_KubGetLast10QuoteDetails_Cache ON dbo.tb_KubGetLast10QuoteDetails_Cache
DROP INDEX IX_tb_KubGetLast10QuoteDetails_Cache_Part ON dbo.tb_KubGetLast10QuoteDetails_Cache

DROP INDEX PK_tb_KubGetMainProductGroupsDetailsCache ON dbo.tb_KubGetMainProductGroupsDetailsCache
DROP INDEX IX_tb_KubGetMainProductGroupsDetailsCache_Part ON dbo.tb_KubGetMainProductGroupsDetailsCache

DROP INDEX PK_tb_KubGetTotalLineInvoicedDetailsCache ON dbo.tb_KubGetTotalLineInvoicedDetailsCache
DROP INDEX IX_tb_KubGetTotalLineInvoicedDetailsCache_Part ON dbo.tb_KubGetTotalLineInvoicedDetailsCache

DROP INDEX PK_tbKUB_GPCalculationDetails ON dbo.tbKUB_GPCalculationDetails
DROP INDEX IX_tbKUB_GPCalculationDetails_Part ON dbo.tbKUB_GPCalculationDetails

DROP INDEX PK_tbKubAssistanceDetailsCache ON dbo.tbKubAssistanceDetailsCache
DROP INDEX IX_tbKubAssistanceDetailsCache_PartNo ON dbo.tbKubAssistanceDetailsCache

DROP INDEX PK_tbKubCountryWiseSaleDetailsCache ON dbo.tbKubCountryWiseSaleDetailsCache
DROP INDEX IX_tbKubCountryWiseSaleDetailsCache_PartNo ON dbo.tbKubCountryWiseSaleDetailsCache

DROP INDEX PK_tbKubCountryWiseUnShippedSaleDetailsCache ON dbo.tbKubCountryWiseUnShippedSaleDetailsCache
DROP INDEX IX_tbKubCountryWiseUnShippedSaleDetailsCache_PartNo ON dbo.tbKubCountryWiseUnShippedSaleDetailsCache

DROP INDEX PK_tbKUBPoDetailsCache ON dbo.tbKUBPoDetailsCache
DROP INDEX IX_tbKUBPoDetailsCache_Part ON dbo.tbKUBPoDetailsCache