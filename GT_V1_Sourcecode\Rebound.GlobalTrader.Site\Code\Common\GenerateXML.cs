﻿using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site.Enumerations;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Text;
using System.Xml;
using Microsoft.Azure.Storage.Blob;


namespace Rebound.GlobalTrader.Site
{
    internal class GenerateXML
    {
        private XmlWriterSettings _XmlWriteSettings;
        public GenerateXML() 
        {
            _XmlWriteSettings = new XmlWriterSettings
            {
                Indent = true,
                IndentChars = "  ", // Two spaces per indent level
                NewLineChars = Environment.NewLine,
                NewLineHandling = NewLineHandling.Replace
            };
        }
        
        public string GenerateStringXmlInvoices(List<Invoice> invoices, out string fileName)
        {
            XmlDocument xmlDoc = BuildXmlInvoices(invoices, out fileName);
            return ToXmlString(xmlDoc);
        }

        /// <summary>
        /// Convert invoices to xml format and zip the file
        /// For attach document to email purpose
        /// </summary>
        /// <param name="invoices"></param>
        /// <returns>Physical path in the disk where zip file is saved</returns>
        public string GenerateZipXmlInvoices(List<Invoice> invoices, out string fileName)
        {
            XmlDocument xmlDoc = BuildXmlInvoices(invoices, out fileName);

            //string tempDirectory = FileUploadManager.GetTemporaryUploadFilePath();
            //string xmlFilePath = Path.Combine(tempDirectory, string.Format("{0}.xml", fileName));
            //string zipFilePath = Path.Combine(tempDirectory, string.Format("{0}.zip", fileName));
            ////save the formated xml file to physical disk
            //SaveFormatedXml(xmlDoc, xmlFilePath);
            ////zip the xml file
            //CreateZip(xmlFilePath, zipFilePath);

            ////delete xml file after create zip
            //if (File.Exists(xmlFilePath))
            //    File.Delete(xmlFilePath);

            //return zipFilePath;
            return SaveFileToTempDirectory(xmlDoc, fileName);
        }

        public string GenerateStringXmlCreditNotes(List<Credit> credits, out string fileName)
        {
            XmlDocument xmlDoc = BuildXmlCreditNotes(credits, out fileName);
            return ToXmlString(xmlDoc);
        }

        /// <summary>
        /// Convert credit notes to xml format and zip the file
        /// For attach document to email purpose
        /// </summary>
        /// <param name="invoices"></param>
        /// <returns>Physical path in the disk where zip file is saved</returns>
        public string GenerateZipXmlCreditNotes(List<Credit> credits, out string fileName)
        {
            XmlDocument xmlDoc = BuildXmlCreditNotes(credits, out fileName);
            return SaveFileToTempDirectory(xmlDoc, fileName);
        }
        private XmlDocument BuildXmlInvoices(List<Invoice> invoices, out string fileName)
        {
            XmlDocument xmlDoc = new XmlDocument();
            XmlElement root = xmlDoc.CreateElement("Invoices");
            foreach (var inv in invoices)
            {
                //increase print count
                Invoice invprint = Invoice.PrintInvoiceInsertSelect(inv.InvoiceId, "", inv.ClientNo, SessionManager.LoginID ?? 0, "INV");

                XmlElement invoiceElm = xmlDoc.CreateElement("Invoice");

                #region Invoice header
                //invoice number
                XmlElement invoiceNumberElm = xmlDoc.CreateElement("InvoiceNumber");
                invoiceNumberElm.InnerText = inv.InvoiceNumber.ToString();
                invoiceElm.AppendChild(invoiceNumberElm);

                //invoice date
                XmlElement invoiceDateElm = xmlDoc.CreateElement("InvoiceDate");
                invoiceDateElm.InnerText = Functions.FormatDate(inv.InvoiceDate);
                invoiceElm.AppendChild(invoiceDateElm);

                //print count
                XmlElement printCountElm = xmlDoc.CreateElement("PrintCount");
                printCountElm.InnerText = invprint.DocCount.ToString();
                invoiceElm.AppendChild(printCountElm);

                //print date
                XmlElement printDateElm = xmlDoc.CreateElement("PrintDate");
                printDateElm.InnerText = Functions.FormatDateBSTtime(DateTime.Now, true, true);
                invoiceElm.AppendChild(printDateElm);

                #endregion

                #region Bill To
                //BEGIN Bill To Company
                XmlElement billToCompanyElm = xmlDoc.CreateElement("BillTo");
                invoiceElm.AppendChild(billToCompanyElm);

                //bill to address
                XmlElement billToAddressElm = xmlDoc.CreateElement("BillToAddress");
                billToAddressElm.InnerText = AddressManager.ToLongString(inv.BillToAddress);
                billToCompanyElm.AppendChild(billToAddressElm);

                //customer code
                XmlElement custNoElm = xmlDoc.CreateElement("CustNo");
                custNoElm.InnerText = inv.CustomerCode;
                billToCompanyElm.AppendChild(custNoElm);

                //bill to VAT No
                XmlElement billToVATNoElm = xmlDoc.CreateElement("VatNo");
                billToVATNoElm.InnerText = inv.VATNo;
                billToCompanyElm.AppendChild(billToVATNoElm);

                //bill to Company Reg. No.
                XmlElement billToCompRegNoElm = xmlDoc.CreateElement("CompanyRegNo");
                billToCompRegNoElm.InnerText = inv.CompanyRegNo;
                billToCompanyElm.AppendChild(billToCompRegNoElm);

                //bill to Tel
                XmlElement billToTelElm = xmlDoc.CreateElement("Tel");
                billToTelElm.InnerText = inv.CompanyTelephone;
                billToCompanyElm.AppendChild(billToTelElm);

                //bill to fax
                XmlElement billToFaxElm = xmlDoc.CreateElement("Fax");
                billToFaxElm.InnerText = inv.CompanyFax;
                billToCompanyElm.AppendChild(billToFaxElm);

                //END Bill To Company
                #endregion

                #region Ship To
                //BEGIN Ship To Company
                XmlElement shipToCompanyElm = xmlDoc.CreateElement("ShipTo");
                invoiceElm.AppendChild(shipToCompanyElm);

                //ship to address
                XmlElement shipToAddressElm = xmlDoc.CreateElement("ShipToAddress");
                shipToAddressElm.InnerText = AddressManager.ToLongString(inv.ShipToAddress);
                shipToCompanyElm.AppendChild(shipToAddressElm);

                //ship to VAT No
                XmlElement shipToVATNoElm = xmlDoc.CreateElement("VatNo");
                shipToVATNoElm.InnerText = inv.ShipToVatNo;
                shipToCompanyElm.AppendChild(shipToVATNoElm);

                //ship to Company Reg. No.
                XmlElement shipToCompRegNoElm = xmlDoc.CreateElement("CompanyRegNo");
                shipToCompRegNoElm.InnerText = inv.CompanyRegNo;
                shipToCompanyElm.AppendChild(shipToCompRegNoElm);

                //ship to Tel.
                XmlElement shipToTelElm = xmlDoc.CreateElement("Tel");
                shipToTelElm.InnerText = inv.CompanyTelephone;
                shipToCompanyElm.AppendChild(shipToTelElm);
                //END Ship To Company
                #endregion

                #region Invoice Detail
                XmlElement invoiceDetailElm = xmlDoc.CreateElement("InvoiceDetail");
                invoiceElm.AppendChild(invoiceDetailElm);
                //boxes
                XmlElement boxesElm = xmlDoc.CreateElement("Boxes");
                boxesElm.InnerText = inv.Boxes != null ? inv.Boxes.ToString() : "";
                invoiceDetailElm.AppendChild(boxesElm);

                //total weight
                string weightUnit = Functions.GetGlobalResource("Printing", (inv.WeightInPounds) ? "Symbol_Weight_Pounds" : "Symbol_Weight_KG");
                XmlElement totalWeightElm = xmlDoc.CreateElement("TotalWt");
                if (inv.Weight > 0)
                {
                    totalWeightElm.SetAttribute("unit", weightUnit);
                    totalWeightElm.InnerText = Functions.FormatNumeric(inv.Weight, 2);
                }
                else
                {
                    totalWeightElm.InnerText = "";
                }
                invoiceDetailElm.AppendChild(totalWeightElm);

                //sales person
                XmlElement salesPersonElm = xmlDoc.CreateElement("Salesperson");
                salesPersonElm.InnerText = inv.SalesmanName;
                invoiceDetailElm.AppendChild(salesPersonElm);

                //date ordered
                XmlElement dateOrderedElm = xmlDoc.CreateElement("DateOrdered");
                dateOrderedElm.InnerText = Functions.FormatDate(inv.DateOrdered);
                invoiceDetailElm.AppendChild(dateOrderedElm);

                //date shipped
                XmlElement dateShippedElm = xmlDoc.CreateElement("DateShipped");
                dateShippedElm.InnerText = Functions.FormatDate(inv.InvoiceDate);
                invoiceDetailElm.AppendChild(dateShippedElm);

                //incoterms
                XmlElement incotermsElm = xmlDoc.CreateElement("Incoterms");
                incotermsElm.InnerText = inv.IncotermName;
                invoiceDetailElm.AppendChild(incotermsElm);

                //shipped by
                XmlElement shippedByElm = xmlDoc.CreateElement("ShippedBy");
                shippedByElm.InnerText = inv.ShippedByName;
                invoiceDetailElm.AppendChild(shippedByElm);

                //Dimensional weight
                XmlElement dimensionalWeightElm = xmlDoc.CreateElement("DimensionalWt");
                if (inv.DimensionalWeight > 0)
                {
                    dimensionalWeightElm.SetAttribute("unit", weightUnit);
                    dimensionalWeightElm.InnerText = Functions.FormatNumeric(inv.DimensionalWeight, 2);
                }
                else
                {
                    dimensionalWeightElm.InnerText = "";
                }
                invoiceDetailElm.AppendChild(dimensionalWeightElm);

                //buyer
                XmlElement buyerElm = xmlDoc.CreateElement("Buyer");
                buyerElm.InnerText = inv.ContactName;
                invoiceDetailElm.AppendChild(buyerElm);

                //ship via
                XmlElement shipViaElm = xmlDoc.CreateElement("ShipVia");
                shipViaElm.InnerText = inv.ShipViaName;
                invoiceDetailElm.AppendChild(shipViaElm);

                //account
                XmlElement accountElm = xmlDoc.CreateElement("Account");
                accountElm.InnerText = inv.Account;
                invoiceDetailElm.AppendChild(accountElm);

                // airway bill no
                XmlElement airwayBillNoElm = xmlDoc.CreateElement("AirwayBillNo");
                airwayBillNoElm.InnerText = inv.AirWayBill;
                invoiceDetailElm.AppendChild(airwayBillNoElm);

                //customer PO No
                XmlElement customerPONoElm = xmlDoc.CreateElement("CustomerPONo");
                customerPONoElm.InnerText = inv.CustomerPO;
                invoiceDetailElm.AppendChild(customerPONoElm);

                //customer PO No
                XmlElement termsElm = xmlDoc.CreateElement("Terms");
                termsElm.InnerText = inv.TermsName;
                invoiceDetailElm.AppendChild(termsElm);

                //SOA
                XmlElement soaElm = xmlDoc.CreateElement("SOA");
                soaElm.InnerText = inv.SalesOrderNumber.ToString();
                invoiceDetailElm.AppendChild(soaElm);
                #endregion

                #region Invoice Lines
                double subTotal = 0;
                double tax = 0;
                List<InvoiceLine> invoicesLines = InvoiceLine.GetListForInvoice(inv.InvoiceId);
                XmlElement invoicesLinesElm = xmlDoc.CreateElement("InvoiceLines");
                foreach (var invoiceLine in invoicesLines)
                {
                    double lineTotal = (double)invoiceLine.Price * (double)invoiceLine.QuantityShipped;
                    double lineTax = (invoiceLine.IsLineTaxable) ? (double)(lineTotal * (((double)inv.TaxRate) / 100)) : 0;
                    subTotal += lineTotal;
                    tax += lineTax;

                    XmlElement lineElm = xmlDoc.CreateElement("InvoiceLine");
                    //Quantity Ordered
                    XmlElement lineQuantityOrderedElm = xmlDoc.CreateElement("QtyOrdered");
                    lineQuantityOrderedElm.InnerText = invoiceLine.QuantityOrdered.ToString();
                    lineElm.AppendChild(lineQuantityOrderedElm);

                    //quantity shipped
                    XmlElement lineQuantityShippedElm = xmlDoc.CreateElement("QtyShipped");
                    lineQuantityShippedElm.InnerText = invoiceLine.QuantityShipped.ToString();
                    lineElm.AppendChild(lineQuantityShippedElm);

                    //part no
                    XmlElement partNoElm = xmlDoc.CreateElement("PartNo");
                    partNoElm.InnerText = invoiceLine.Part;
                    lineElm.AppendChild(partNoElm);

                    //rohs
                    XmlElement rohsElm = xmlDoc.CreateElement("RoHS");
                    rohsElm.InnerText = GetROHSForPrint(invoiceLine.ROHS);
                    lineElm.AppendChild(rohsElm);

                    //customer part
                    XmlElement customerPartElm = xmlDoc.CreateElement("CustomerPart");
                    customerPartElm.InnerText = invoiceLine.CustomerPart;
                    lineElm.AppendChild(customerPartElm);

                    //country of origin
                    XmlElement countryOfOriginElm = xmlDoc.CreateElement("CountryOfOrigin");
                    countryOfOriginElm.InnerText = invoiceLine.CountryOfManufactureName;
                    lineElm.AppendChild(countryOfOriginElm);

                    //customer PO
                    XmlElement customerPOElm = xmlDoc.CreateElement("CustomerPO");
                    customerPOElm.InnerText = invoiceLine.CustomerPO;
                    lineElm.AppendChild(customerPOElm);

                    //Sales order
                    XmlElement salesOrderElm = xmlDoc.CreateElement("SalesOrder");
                    salesOrderElm.InnerText = invoiceLine.SalesOrderNumber.ToString();
                    lineElm.AppendChild(salesOrderElm);

                    //Product
                    XmlElement productElm = xmlDoc.CreateElement("Product");
                    productElm.InnerText = invoiceLine.ProductDescription;
                    lineElm.AppendChild(productElm);

                    //Duty code
                    XmlElement dutyCodeElm = xmlDoc.CreateElement("DutyCode");
                    dutyCodeElm.InnerText = invoiceLine.ProductDutyCode;
                    lineElm.AppendChild(dutyCodeElm);

                    //manufacturer
                    XmlElement manufacturerElm = xmlDoc.CreateElement("Mfr");
                    manufacturerElm.InnerText = invoiceLine.ManufacturerCode;
                    lineElm.AppendChild(manufacturerElm);

                    //date code
                    XmlElement dateCodeElm = xmlDoc.CreateElement("DC");
                    dateCodeElm.InnerText = invoiceLine.DateCode;
                    lineElm.AppendChild(dateCodeElm);

                    //notes
                    XmlElement lineNotesElm = xmlDoc.CreateElement("Notes");
                    lineNotesElm.InnerText = invoiceLine.LineNotes;
                    lineElm.AppendChild(lineNotesElm);

                    //package
                    XmlElement packageElm = xmlDoc.CreateElement("Pack");
                    packageElm.InnerText = invoiceLine.PackageName;
                    lineElm.AppendChild(packageElm);

                    //price
                    XmlElement priceElm = xmlDoc.CreateElement("Price");
                    priceElm.SetAttribute("currency", invoiceLine.CurrencyCode);
                    priceElm.InnerText = Functions.FormatCurrency(invoiceLine.Price, null, 5, true);
                    lineElm.AppendChild(priceElm);

                    //amount
                    XmlElement amountElm = xmlDoc.CreateElement("Amount");
                    amountElm.SetAttribute("currency", invoiceLine.CurrencyCode);
                    amountElm.InnerText = Functions.FormatCurrency(lineTotal, null, 2, true);
                    lineElm.AppendChild(amountElm);

                    invoicesLinesElm.AppendChild(lineElm);
                }
                tax += ((double)inv.Freight * ((double)inv.TaxRate / 100));
                invoiceElm.AppendChild(invoicesLinesElm);
                #endregion

                #region Germany Exchange Rate
                int germanyClientNo = (int)ClientList.ClientNo.GMBH;
                string germanyCurrencyCode = ClientList.GetClientDefaultCurrency(ClientList.ClientNo.GMBH);
                if (inv.ClientNo == germanyClientNo && !inv.CurrencyCode.Equals(germanyCurrencyCode)) //germany client
                {
                    //Invoice invGermanyExchangeRate = Invoice.GetGermanyExchangeRateForCountry(inv.BillToAddressNo ?? 0, inv.InvoiceDate);
                    Invoice invGermanyExchangeRate = Invoice.GetGermanyExchangeRateForCurrency(inv.CurrencyCode, inv.InvoiceDate);
                    if (invGermanyExchangeRate != null && invGermanyExchangeRate.ExchangeRate > 0)
                    {
                        XmlElement germanExchangeRateElm = xmlDoc.CreateElement("ExchangeRate");
                        germanExchangeRateElm.InnerText = string.Format(Functions.GetGlobalResource("Printing", "ExchangeRateSummary"), invGermanyExchangeRate.LocalCurrencyCode, invGermanyExchangeRate.ExchangeRate, invGermanyExchangeRate.GlobalCurrencyCode, invGermanyExchangeRate.InvoiceDate.ToString("MMM yyyy"));
                        invoiceElm.AppendChild(germanExchangeRateElm);
                    }
                }
                #endregion

                #region local currency
                if (inv.LocalCurrencyCode.ToUpper() != inv.CurrencyCode.ToUpper() && inv.ExchangeRate.HasValue && inv.ExchangeRate.Value > 0)
                {
                    XmlElement localCurrencyElm = xmlDoc.CreateElement("LocalCurrency");
                    //Local Equivalent
                    XmlElement localEquivalentElm = xmlDoc.CreateElement("LocalEquivalent");
                    localEquivalentElm.InnerText = Functions.GetGlobalResource("Printing", "LocalEquivalent");
                    localCurrencyElm.AppendChild(localEquivalentElm);

                    //local currency sub total
                    XmlElement lcSubTotalElm = xmlDoc.CreateElement("SubTotal");
                    lcSubTotalElm.InnerText = Functions.FormatCurrency(Currency.ConvertValueToBaseCurrency(subTotal, (double)inv.ExchangeRate), null, 2, true);
                    lcSubTotalElm.SetAttribute("currency", inv.LocalCurrencyCode);
                    localCurrencyElm.AppendChild(lcSubTotalElm);

                    //local currency bank fee
                    if (inv.IsApplyBankFee.HasValue && inv.IsApplyBankFee.Value)
                    {
                        XmlElement lcBankFeeElm = xmlDoc.CreateElement("BankFee");
                        lcBankFeeElm.InnerText = Functions.FormatCurrency(Currency.ConvertValueToBaseCurrency(inv.InvoiceBankFee, (double)inv.ExchangeRate), null, 2, true);
                        lcBankFeeElm.SetAttribute("currency", inv.LocalCurrencyCode);
                        localCurrencyElm.AppendChild(lcBankFeeElm);
                    }

                    //local currency freight
                    XmlElement lcFreightElm = xmlDoc.CreateElement("Freight");
                    lcFreightElm.InnerText = Functions.FormatCurrency(Currency.ConvertValueToBaseCurrency(inv.Freight, (double)inv.ExchangeRate), null, 2, true);
                    lcFreightElm.SetAttribute("currency", inv.LocalCurrencyCode);
                    localCurrencyElm.AppendChild(lcFreightElm);

                    //lc tax name
                    XmlElement lcTaxNameElm = xmlDoc.CreateElement("TaxName");
                    lcTaxNameElm.InnerText = inv.TaxName;
                    localCurrencyElm.AppendChild(lcTaxNameElm);

                    //lc tax rate
                    XmlElement lcTaxRateElm = xmlDoc.CreateElement("TaxRate");
                    lcTaxRateElm.InnerText = Functions.FormatPercentage(inv.TaxRate, 1, false);
                    lcTaxRateElm.SetAttribute("unit", "%");
                    localCurrencyElm.AppendChild(lcTaxRateElm);

                    //lc tax
                    XmlElement lcTaxElm = xmlDoc.CreateElement("Tax");
                    lcTaxElm.InnerText = Functions.FormatCurrency(Currency.ConvertValueToBaseCurrency(tax, (double)inv.ExchangeRate), null, 2, true);
                    lcTaxElm.SetAttribute("currency", inv.LocalCurrencyCode);
                    localCurrencyElm.AppendChild(lcTaxElm);

                    //lc total
                    XmlElement lcTotalElm = xmlDoc.CreateElement("Total");
                    if (inv.IsApplyBankFee.HasValue && inv.IsApplyBankFee.Value)
                    {
                        lcTotalElm.InnerText = Functions.FormatCurrency(Currency.ConvertValueToBaseCurrency(inv.Freight + tax + subTotal + inv.InvoiceBankFee, (double)inv.ExchangeRate), null, 2, true);
                    }
                    else
                    {
                        lcTotalElm.InnerText = Functions.FormatCurrency(Currency.ConvertValueToBaseCurrency(inv.Freight + tax + subTotal, (double)inv.ExchangeRate), null, 2, true);
                    }
                    lcTotalElm.SetAttribute("currency", inv.LocalCurrencyCode);
                    localCurrencyElm.AppendChild(lcTotalElm);

                    //exchange rate summary
                    XmlElement lcExchangeRateSummaryElm = xmlDoc.CreateElement("ExchangeRateSummary");
                    lcExchangeRateSummaryElm.InnerText = string.Format(Functions.GetGlobalResource("Printing", "ExchangeRateSummary"), inv.LocalCurrencyCode, inv.ExchangeRate, inv.CurrencyCode, inv.InvoiceDate.ToString("MMM yyyy"));
                    localCurrencyElm.AppendChild(lcExchangeRateSummaryElm);

                    invoiceElm.AppendChild(localCurrencyElm);
                }
                #endregion

                #region Invoice Notes
                //print notes
                XmlElement printNotesElm = xmlDoc.CreateElement("PrintNotes");
                printNotesElm.InnerText = inv.PrintNotes;
                invoiceElm.AppendChild(printNotesElm);

                //notes
                XmlElement notesElm = xmlDoc.CreateElement("Notes");
                notesElm.InnerText = inv.Instructions;
                invoiceElm.AppendChild(notesElm);

                //currency notes
                XmlElement currencyNotesElm = xmlDoc.CreateElement("CurrencyNotes");
                currencyNotesElm.InnerText = inv.CurrencyNotes;
                invoiceElm.AppendChild(currencyNotesElm);
                #endregion

                #region Invoice Total
                XmlElement invoiceTotalElm = xmlDoc.CreateElement("InvoiceTotal");
                //sub total
                XmlElement invoiceSubTotalElm = xmlDoc.CreateElement("SubTotal");
                invoiceSubTotalElm.InnerText = Functions.FormatCurrency(subTotal, null, 2, true);
                invoiceSubTotalElm.SetAttribute("currency", inv.CurrencyCode);
                invoiceTotalElm.AppendChild(invoiceSubTotalElm);

                //bank fee
                if (inv.IsApplyBankFee.HasValue && inv.IsApplyBankFee.Value)
                {
                    XmlElement bankFeeElm = xmlDoc.CreateElement("BankFee");
                    bankFeeElm.InnerText = Functions.FormatCurrency(inv.InvoiceBankFee, null, 2, true);
                    bankFeeElm.SetAttribute("currency", inv.CurrencyCode);
                    invoiceTotalElm.AppendChild(bankFeeElm);
                }

                //freight
                XmlElement freightElm = xmlDoc.CreateElement("Freight");
                freightElm.InnerText = Functions.FormatCurrency(inv.Freight, null, 2, true);
                freightElm.SetAttribute("currency", inv.CurrencyCode);
                invoiceTotalElm.AppendChild(freightElm);

                //tax name
                XmlElement taxNameElm = xmlDoc.CreateElement("TaxName");
                taxNameElm.InnerText = inv.TaxName;
                invoiceTotalElm.AppendChild(taxNameElm);

                //tax rate
                XmlElement taxRateElm = xmlDoc.CreateElement("TaxRate");
                taxRateElm.InnerText = Functions.FormatPercentage(inv.TaxRate, 1, false);
                taxRateElm.SetAttribute("unit", "%");
                invoiceTotalElm.AppendChild(taxRateElm);

                //tax value
                XmlElement taxElm = xmlDoc.CreateElement("Tax");
                taxElm.InnerText = Functions.FormatCurrency(tax, null, 2, true);
                taxElm.SetAttribute("currency", inv.CurrencyCode);
                invoiceTotalElm.AppendChild(taxElm);

                //total
                XmlElement totalElm = xmlDoc.CreateElement("Total");
                if (inv.IsApplyBankFee.HasValue && inv.IsApplyBankFee.Value)
                    totalElm.InnerText = Functions.FormatCurrency(inv.Freight + tax + subTotal + inv.InvoiceBankFee, null, 2, true);
                else
                    totalElm.InnerText = Functions.FormatCurrency(inv.Freight + tax + subTotal, null, 2, true);
                totalElm.SetAttribute("currency", inv.CurrencyCode);
                invoiceTotalElm.AppendChild(totalElm);

                invoiceElm.AppendChild(invoiceTotalElm);
                #endregion

                #region Footer notes
                //footer notes
                XmlElement footerNotesElm = xmlDoc.CreateElement("FooterNotes");
                if (string.IsNullOrEmpty(inv.FooterText))
                {
                    footerNotesElm.InnerText = GetFooterNotes(SystemDocument.ListForPrint.Invoice);
                }
                else
                {
                    footerNotesElm.InnerText = inv.FooterText;
                }
                invoiceElm.AppendChild(footerNotesElm);
                #endregion

                string strTerms = FileUploadManager.GetTermsTxtName_Client(inv.ClientNo, "Invoice");
                string sasURL = AzureBlobSA.GetSasUrl("gtdocmgmt");
                string bothirl = AzureBlobSA.GetSasBlobUrl("gtdocmgmt", strTerms, sasURL, "terms");
                //if (File.Exists(strTerms))
                Uri blobUri = new Uri(bothirl);
                CloudBlockBlob blob = new CloudBlockBlob(blobUri);
                if (blob.Exists())
                {
                    var termText = blob.DownloadText();
                    XmlElement termsAndConditions = xmlDoc.CreateElement("TermsAndConditions");
                    termsAndConditions.InnerText = termText;
                    invoiceElm.AppendChild(termsAndConditions);
                }

                root.AppendChild(invoiceElm);
            }

            xmlDoc.AppendChild(root);

            fileName = invoices.Count == 1 ? invoices[0].InvoiceNumber.ToString() : "invoices";

            return xmlDoc;
        }

        private XmlDocument BuildXmlCreditNotes(List<Credit> credits, out string fileName)
        {
            XmlDocument xmlDoc = new XmlDocument();
            fileName = string.Empty;
            XmlElement root = xmlDoc.CreateElement("Credits");
            foreach (var cr in credits)
            {
                XmlElement creElm = xmlDoc.CreateElement("Credit");

                #region Write cr Number and cr Date
                //credit number
                XmlElement creditNumberElm = xmlDoc.CreateElement("CreditNoteNumber");
                creditNumberElm.InnerText = cr.CreditNumber.ToString();
                creElm.AppendChild(creditNumberElm);

                //credit date
                XmlElement creditDateElm = xmlDoc.CreateElement("DateRaised");
                creditDateElm.InnerText = Functions.FormatDate(cr.CreditDate);
                creElm.AppendChild(creditDateElm);

                var creditPrint = PurchaseOrder.PrintDocCountInsertSelect(cr.CreditId, "", cr.ClientNo, SessionManager.LoginID ?? 0, "CDN");
                //print count
                XmlElement printCountElm = xmlDoc.CreateElement("PrintCount");
                printCountElm.InnerText = creditPrint.DocCount.ToString();
                creElm.AppendChild(printCountElm);

                //print date
                XmlElement printDateElm = xmlDoc.CreateElement("PrintDate");
                printDateElm.InnerText = Functions.FormatDateBSTtime(DateTime.Now, true, true);
                creElm.AppendChild(printDateElm);
                #endregion

                #region Write credit addresses
                XmlElement customerElm = xmlDoc.CreateElement("Customer");
                XmlElement billAddressElm = xmlDoc.CreateElement("BillAddress");
                Address billingAddress = Company.GetDefaultBillingAddress(cr.CompanyNo);
                if (billingAddress != null)
                {
                    //address name
                    XmlElement addressNameElm = xmlDoc.CreateElement("AddressName");
                    addressNameElm.InnerText = billingAddress.AddressName;
                    billAddressElm.AppendChild(addressNameElm);

                    //line 1
                    XmlElement line1Elm = xmlDoc.CreateElement("Line1");
                    line1Elm.InnerText = billingAddress.Line1;
                    billAddressElm.AppendChild(line1Elm);

                    //line 2
                    XmlElement line2Elm = xmlDoc.CreateElement("Line2");
                    line2Elm.InnerText = billingAddress.Line2;
                    billAddressElm.AppendChild(line2Elm);

                    //line 3
                    XmlElement line3Elm = xmlDoc.CreateElement("Line3");
                    line3Elm.InnerText = billingAddress.Line3;
                    billAddressElm.AppendChild(line3Elm);

                    //city
                    XmlElement cityElm = xmlDoc.CreateElement("City");
                    cityElm.InnerText = billingAddress.City;
                    billAddressElm.AppendChild(cityElm);

                    //country
                    XmlElement countryElm = xmlDoc.CreateElement("Country");
                    countryElm.InnerText = billingAddress.County;
                    billAddressElm.AppendChild(countryElm);

                    //state
                    XmlElement stateElm = xmlDoc.CreateElement("State");
                    stateElm.InnerText = billingAddress.State;
                    billAddressElm.AppendChild(stateElm);

                    //zip
                    XmlElement zipElm = xmlDoc.CreateElement("ZIP");
                    zipElm.InnerText = billingAddress.ZIP;
                    billAddressElm.AppendChild(zipElm);

                    //country name
                    XmlElement countryNameElm = xmlDoc.CreateElement("CountryName");
                    countryNameElm.InnerText = billingAddress.CountryName;
                    billAddressElm.AppendChild(countryNameElm);
                }
                customerElm.AppendChild(billAddressElm);
                //tel
                XmlElement telElm = xmlDoc.CreateElement("Tel");
                telElm.InnerText = cr.CompanyTelephone;
                customerElm.AppendChild(telElm);

                //fax
                XmlElement faxElm = xmlDoc.CreateElement("Fax");
                faxElm.InnerText = cr.CompanyFax;
                customerElm.AppendChild(faxElm);

                //VAT NO
                XmlElement vatNoElm = xmlDoc.CreateElement("VATNo");
                vatNoElm.InnerText = cr.VATNO;
                customerElm.AppendChild(vatNoElm);

                //Cust No
                XmlElement custNoElm = xmlDoc.CreateElement("CustNo");
                custNoElm.InnerText = cr.CustomerCode;
                customerElm.AppendChild(custNoElm);

                creElm.AppendChild(customerElm);
                #endregion

                #region Write credit details
                XmlElement creditDetailsElm = xmlDoc.CreateElement("CreditDetails");
                //raised by
                XmlElement raisedByElm = xmlDoc.CreateElement("RaisedBy");
                raisedByElm.InnerText = cr.RaiserName;
                creditDetailsElm.AppendChild(raisedByElm);

                //Contact
                XmlElement contactElm = xmlDoc.CreateElement("Contact");
                contactElm.InnerText = cr.ContactName;
                creditDetailsElm.AppendChild(contactElm);

                //sales person
                XmlElement salespersonElm = xmlDoc.CreateElement("Salesperson");
                salespersonElm.InnerText = cr.SalesmanName;
                creditDetailsElm.AppendChild(salespersonElm);

                //ship via
                XmlElement shipViaElm = xmlDoc.CreateElement("ShipVia");
                shipViaElm.InnerText = cr.ShipViaName;
                creditDetailsElm.AppendChild(shipViaElm);

                //Invoice
                XmlElement invoiceElm = xmlDoc.CreateElement("Invoice");
                if (cr.isClientInvoice || cr.ClientCreditNo > 0)
                {
                    invoiceElm.InnerText = Functions.FormatNumeric(cr.ClientInvoiceNumber);
                }
                else
                {
                    invoiceElm.InnerText = Functions.FormatNumeric(cr.InvoiceNumber);
                }
                creditDetailsElm.AppendChild(invoiceElm);

                //rma
                XmlElement rmaElm = xmlDoc.CreateElement("RMA");
                rmaElm.InnerText = Functions.FormatNumeric(cr.CustomerRMANumber);
                creditDetailsElm.AppendChild(rmaElm);

                //your PO
                XmlElement poNumberElm = xmlDoc.CreateElement("YourPO");
                poNumberElm.InnerText = cr.CustomerPO;
                creditDetailsElm.AppendChild(poNumberElm);

                //Your debit note
                XmlElement debitNoteElm = xmlDoc.CreateElement("YourDebitNote");
                debitNoteElm.InnerText = cr.CustomerDebit;
                creditDetailsElm.AppendChild(debitNoteElm);

                //your return note
                XmlElement returnNoteElm = xmlDoc.CreateElement("YourReturnNote");
                returnNoteElm.InnerText = cr.CustomerRMA;
                creditDetailsElm.AppendChild(returnNoteElm);

                //Incoterms
                XmlElement incotermsElm = xmlDoc.CreateElement("Incoterms");
                incotermsElm.InnerText = cr.IncotermName;
                creditDetailsElm.AppendChild(incotermsElm);

                creElm.AppendChild(creditDetailsElm);
                #endregion

                #region Write credit lines
                //credit lines
                double dblSubTotal = 0;
                double dblTax = 0;

                List<CreditLine> lstCRL = CreditLine.GetListForCredit(cr.CreditId);
                XmlElement linesElm = xmlDoc.CreateElement("CreditLines");
                foreach (CreditLine ln in lstCRL)
                {
                    double dblLineTotal = (double)ln.Quantity * (double)ln.Price;
                    XmlElement lineElm = xmlDoc.CreateElement("CreditLine");
                    //qty credited
                    XmlElement qtyElm = xmlDoc.CreateElement("QtyCredited");
                    qtyElm.InnerText = Functions.FormatNumeric(ln.Quantity);
                    lineElm.AppendChild(qtyElm);

                    //part no
                    XmlElement partElm = xmlDoc.CreateElement("PartNo");
                    partElm.InnerText = ln.Part.Trim();
                    lineElm.AppendChild(partElm);

                    //rohs
                    XmlElement rohsElm = xmlDoc.CreateElement("RoHS");
                    rohsElm.InnerText = GetROHSForPrint(ln.ROHS);
                    lineElm.AppendChild(rohsElm);

                    //customer part
                    XmlElement customerPartElm = xmlDoc.CreateElement("CustomerPart");
                    customerPartElm.InnerText = ln.CustomerPart;
                    lineElm.AppendChild(customerPartElm);

                    //product
                    XmlElement productElm = xmlDoc.CreateElement("Product");
                    productElm.InnerText = ln.ProductDescription;
                    lineElm.AppendChild(productElm);

                    //duty code
                    XmlElement dutyCodeElm = xmlDoc.CreateElement("DutyCode");
                    dutyCodeElm.InnerText = string.Empty;//duty code is empty in print PDF function
                    lineElm.AppendChild(dutyCodeElm);


                    //line notes
                    XmlElement lineNotesElm = xmlDoc.CreateElement("Notes");
                    lineNotesElm.InnerText = ln.LineNotes;
                    lineElm.AppendChild(lineNotesElm);

                    //manufacturer
                    XmlElement mfrElm = xmlDoc.CreateElement("Mfr");
                    mfrElm.InnerText = ln.ManufacturerCode;
                    lineElm.AppendChild(mfrElm);

                    //date code
                    XmlElement dateCodeElm = xmlDoc.CreateElement("DC");
                    dateCodeElm.InnerText = ln.DateCode;
                    lineElm.AppendChild(dateCodeElm);

                    //pack
                    XmlElement packageElm = xmlDoc.CreateElement("Pack");
                    packageElm.InnerText = ln.PackageName;
                    lineElm.AppendChild(packageElm);

                    //price
                    XmlElement priceElm = xmlDoc.CreateElement("Price");
                    priceElm.InnerText = Functions.FormatCurrency(ln.Price, null, 5, true);
                    priceElm.SetAttribute("currency", cr.CurrencyCode);
                    lineElm.AppendChild(priceElm);

                    //amount
                    XmlElement amountElm = xmlDoc.CreateElement("Amount");
                    amountElm.InnerText = Functions.FormatCurrency(dblLineTotal, null, 2, true);
                    amountElm.SetAttribute("currency", cr.CurrencyCode);
                    lineElm.AppendChild(amountElm);

                    //currency
                    XmlElement curElm = xmlDoc.CreateElement("Currency");
                    curElm.InnerText = cr.CurrencyCode;
                    lineElm.AppendChild(curElm);

                    //add line to list
                    linesElm.AppendChild(lineElm);
                    dblSubTotal += dblLineTotal;
                    dblTax += (ln.Taxable) ? (double)(ln.Price * ln.Quantity * (cr.TaxRate / 100)) : 0;
                }
                creElm.AppendChild(linesElm);
                dblTax += ((double)cr.Freight * ((double)cr.TaxRate / 100));
                #endregion

                double dblBankFee = cr.CreditNoteBankFee ?? 0;
                #region local currency
                bool IsLocalCurrencyApply = false;
                string localCurrencyCode = string.Empty;
                double? localExchangeRate;
                DateTime? exchangeRateDate = null;
                if (cr.CrLocalCurrencyNo > 0)
                {
                    //localCurrencyCode = cr.CrLocalCurrencyCode;
                    localCurrencyCode = cr.GlobalCurrencyCode;
                    localExchangeRate = new GeneratePDF().GetLocalCurrencyExchangeRateCR(cr, out exchangeRateDate);
                    IsLocalCurrencyApply = true;
                }
                else
                {
                    localCurrencyCode = cr.LocalCurrencyCode;
                    localExchangeRate = cr.ExchangeRate;
                }
                if (cr.GlobalCurrencyCode.ToUpper() != localCurrencyCode.ToUpper() && localExchangeRate.HasValue && localExchangeRate.Value > 0)
                {
                    XmlElement localCurrencyElm = xmlDoc.CreateElement("LocalCurrency");
                    //Local Equivalent
                    XmlElement localEquivalentElm = xmlDoc.CreateElement("LocalEquivalent");
                    localEquivalentElm.InnerText = Functions.GetGlobalResource("Printing", "LocalEquivalent");
                    localCurrencyElm.AppendChild(localEquivalentElm);

                    //sub total
                    XmlElement lcSubTotalElm = xmlDoc.CreateElement("SubTotal");
                    if (!IsLocalCurrencyApply)
                    {
                        lcSubTotalElm.InnerText = Functions.FormatCurrency(Currency.ConvertValueToBaseCurrency(dblSubTotal, (double)localExchangeRate), null, 2, true);
                    }
                    else
                    {
                        lcSubTotalElm.InnerText = Functions.FormatCurrency(Currency.ConvertValueFromBaseCurrency(dblSubTotal, (double)localExchangeRate), null, 2, true);
                    }
                    lcSubTotalElm.SetAttribute("currency", localCurrencyCode);
                    localCurrencyElm.AppendChild(lcSubTotalElm);

                    //Freight
                    XmlElement lcFreigthElm = xmlDoc.CreateElement("Freight");
                    lcFreigthElm.InnerText = Functions.FormatCurrency(Currency.ConvertValueToBaseCurrency(cr.Freight, (double)localExchangeRate), null, 2, true);
                    lcFreigthElm.SetAttribute("currency", localCurrencyCode);
                    localCurrencyElm.AppendChild(lcFreigthElm);

                    //tax name
                    XmlElement lcTaxNameElm = xmlDoc.CreateElement("TaxName");
                    lcTaxNameElm.InnerText = cr.TaxName;
                    localCurrencyElm.AppendChild(lcTaxNameElm);

                    //tax rate
                    XmlElement lcTaxRateElm = xmlDoc.CreateElement("TaxRate");
                    lcTaxRateElm.InnerText = Functions.FormatPercentage(cr.TaxRate, 1, false);
                    lcTaxRateElm.SetAttribute("unit", "%");
                    localCurrencyElm.AppendChild(lcTaxRateElm);

                    //tax value
                    XmlElement lcTaxValueElm = xmlDoc.CreateElement("Tax");
                    lcTaxValueElm.InnerText = Functions.FormatCurrency(Currency.ConvertValueToBaseCurrency(dblTax, (double)localExchangeRate), null, 2, true);
                    lcTaxValueElm.SetAttribute("currency", localCurrencyCode);
                    localCurrencyElm.AppendChild(lcTaxValueElm);

                    //bank fee
                    if (dblBankFee > 0)
                    {
                        XmlElement lcBankFeeElm = xmlDoc.CreateElement("BankFee");
                        if (!IsLocalCurrencyApply)
                        {
                            lcBankFeeElm.InnerText = Functions.FormatCurrency(Currency.ConvertValueToBaseCurrency(dblBankFee, (double)localExchangeRate), null, 2, true);
                        }
                        else
                        {
                            lcBankFeeElm.InnerText = Functions.FormatCurrency(Currency.ConvertValueFromBaseCurrency(dblBankFee, (double)localExchangeRate), null, 2, true);
                        }
                        lcBankFeeElm.SetAttribute("currency", localCurrencyCode);
                        localCurrencyElm.AppendChild(lcBankFeeElm);
                    }

                    //total
                    XmlElement lcTotalElm = xmlDoc.CreateElement("Total");
                    if (!IsLocalCurrencyApply)
                    {
                        lcTotalElm.InnerText = Functions.FormatCurrency(Currency.ConvertValueToBaseCurrency(cr.Freight + dblTax + dblSubTotal + dblBankFee, (double)localExchangeRate), null, 2, true);
                    }
                    else
                    {
                        lcTotalElm.InnerText = Functions.FormatCurrency(Currency.ConvertValueFromBaseCurrency(cr.Freight + dblTax + dblSubTotal + dblBankFee, (double)localExchangeRate), null, 2, true);
                    }
                    lcTotalElm.SetAttribute("currency", localCurrencyCode);
                    localCurrencyElm.AppendChild(lcTotalElm);

                    //exchange rate summary
                    XmlElement lcExchangeRateElm = xmlDoc.CreateElement("ExchangeRateSummary");
                    if (IsLocalCurrencyApply)
                    {
                        lcExchangeRateElm.InnerText = string.Format(Functions.GetGlobalResource("Printing", "ExchangeRateSummary"), cr.GlobalCurrencyCode, localExchangeRate.Value, localCurrencyCode, exchangeRateDate.Value.ToString("dd/MM/yyyy", CultureInfo.InvariantCulture));
                    }
                    else
                    {
                        lcExchangeRateElm.InnerText = string.Format(Functions.GetGlobalResource("Printing", "ExchangeRateSummary"), localCurrencyCode, localExchangeRate.Value, cr.GlobalCurrencyCode, cr.CreditDate.ToString("MMM yyyy"));
                    }
                    localCurrencyElm.AppendChild(lcExchangeRateElm);
                    creElm.AppendChild(localCurrencyElm);
                }
                #endregion
                #region Total
                XmlElement totalElm = xmlDoc.CreateElement("CreditTotal");
                //sub total
                XmlElement subTotalElm = xmlDoc.CreateElement("SubTotal");
                subTotalElm.InnerText = Functions.FormatCurrency(dblSubTotal, null, 2, true);
                subTotalElm.SetAttribute("currency", cr.GlobalCurrencyCode);
                totalElm.AppendChild(subTotalElm);

                //freight
                XmlElement freightElm = xmlDoc.CreateElement("Freight");
                freightElm.InnerText = Functions.FormatCurrency(cr.Freight, null, 2, true);
                freightElm.SetAttribute("currency", cr.GlobalCurrencyCode);
                totalElm.AppendChild(freightElm);

                //tax name
                XmlElement taxNameElm = xmlDoc.CreateElement("TaxName");
                taxNameElm.InnerText = cr.TaxName;
                totalElm.AppendChild(taxNameElm);

                //tax rate
                XmlElement taxRateElm = xmlDoc.CreateElement("TaxRate");
                taxRateElm.InnerText = Functions.FormatPercentage(cr.TaxRate, 1, false);
                taxRateElm.SetAttribute("unit", "%");
                totalElm.AppendChild(taxRateElm);

                //tax value
                XmlElement taxElm = xmlDoc.CreateElement("Tax");
                taxElm.InnerText = Functions.FormatCurrency(dblTax, null, 2, true);
                taxElm.SetAttribute("currency", cr.GlobalCurrencyCode);
                totalElm.AppendChild(taxElm);

                //bank fee
                if (dblBankFee > 0)
                {
                    XmlElement bankFeeElm = xmlDoc.CreateElement("BankFee");
                    bankFeeElm.InnerText = Functions.FormatCurrency(dblBankFee, null, 2, true);
                    bankFeeElm.SetAttribute("currency", cr.GlobalCurrencyCode);
                    totalElm.AppendChild(bankFeeElm);
                }

                //total
                XmlElement totalValueElm = xmlDoc.CreateElement("Total");
                totalValueElm.InnerText = Functions.FormatCurrency(cr.Freight + dblTax + dblSubTotal + dblBankFee, null, 2, true);
                totalValueElm.SetAttribute("currency", cr.GlobalCurrencyCode);
                totalElm.AppendChild(totalValueElm);
                creElm.AppendChild(totalElm);

                //notes
                XmlElement notesElm = xmlDoc.CreateElement("Notes");
                notesElm.InnerText = cr.Notes;
                creElm.AppendChild(notesElm);

                #endregion

                #region Footer notes
                //footer notes
                XmlElement footerNotesElm = xmlDoc.CreateElement("FooterNotes");
                if (string.IsNullOrEmpty(cr.FooterTextCredit))
                {
                    footerNotesElm.InnerText = GetFooterNotes(SystemDocument.ListForPrint.CreditNote);
                }
                else
                {
                    footerNotesElm.InnerText = cr.FooterTextCredit;
                }
                creElm.AppendChild(footerNotesElm);
                #endregion
                root.AppendChild(creElm);
            }
            xmlDoc.AppendChild(root);
            fileName = credits.Count == 1 ? credits[0].CreditNumber.ToString() : "credits";

            return xmlDoc;
        }

        private string ToXmlString(XmlDocument xmlDoc)
        {
            // Use a StringBuilder to hold the formatted XML string
            StringBuilder stringBuilder = new StringBuilder();

            // Create an XmlWriter with the settings
            using (XmlWriter xmlWriter = XmlWriter.Create(stringBuilder, _XmlWriteSettings))
            {
                // Write the XML document to the XmlWriter
                xmlDoc.WriteTo(xmlWriter);
                xmlWriter.Flush();
            }

            // Return the formatted XML string
            return stringBuilder.ToString();
        }

        private string GetFooterNotes(SystemDocument.ListForPrint enmSystemDocument)
        {
            string strNotes = "";
            BLL.SystemDocumentFooter ft = BLL.SystemDocumentFooter.GetForClientAndDocument((int)SessionManager.ClientID, (int)enmSystemDocument);
            if (ft != null) strNotes = (String.IsNullOrEmpty(ft.FooterText)) ? "" : ft.FooterText;
            ft = null;
            return strNotes;
        }

        private string GetROHSForPrint(int? intROHS)
        {
            string strPartROHS;
            switch (intROHS)
            {
                case 0:
                    strPartROHS = Functions.GetGlobalResource("Misc", "ROHSUnknown");
                    break;
                case 1:
                    strPartROHS = Functions.GetGlobalResource("Misc", "ROHSCompliant");
                    break;
                case 2:
                    strPartROHS = Functions.GetGlobalResource("Misc", "ROHSNonCompliant");
                    break;
                case 3:
                    strPartROHS = Functions.GetGlobalResource("Misc", "ROHSExempt");
                    break;
                case 4:
                    strPartROHS = Functions.GetGlobalResource("Misc", "ROHSNotApplicable");
                    break;
                case 5:
                    strPartROHS = Functions.GetGlobalResource("Misc", "ROHSROHS2");
                    break;
                case 6:
                    strPartROHS = Functions.GetGlobalResource("Misc", "ROHSROHS56");
                    break;
                case 7:
                    strPartROHS = Functions.GetGlobalResource("Misc", "ROHSROHS66");
                    break;
                default:
                    strPartROHS = string.Empty;
                    break;
            }
            return strPartROHS;
        }

        private void SaveFormatedXml(XmlDocument xmlDoc, string xmlFilePath)
        {
            try
            {
                if (File.Exists(xmlFilePath))
                    File.Delete(xmlFilePath);

                // Use XmlWriter to save the formatted XML
                using (XmlWriter writer = XmlWriter.Create(xmlFilePath, _XmlWriteSettings))
                {
                    xmlDoc.Save(writer);
                }
            }
            catch (Exception ex)
            {
                Errorlog objErr = new Errorlog();
                var strError = "Exception when save xml invoice to temp upload folder: " + Convert.ToString(ex);
                objErr.LogMessage(strError);
            }
        }

        private string CreateZip(string xmlPath, string zipPath)
        {
            if (File.Exists(zipPath))
                File.Delete(zipPath);

            using (FileStream zipToOpen = new FileStream(zipPath, FileMode.Create))
            {
                using (ZipArchive archive = new ZipArchive(zipToOpen, ZipArchiveMode.Create))
                {
                    ZipArchiveEntry entry = archive.CreateEntry(Path.GetFileName(xmlPath));

                    using (Stream entryStream = entry.Open())
                    using (FileStream fileStream = new FileStream(xmlPath, FileMode.Open))
                    {
                        fileStream.CopyTo(entryStream);
                    }
                }
            }
            return zipPath;
        }

        private string SaveFileToTempDirectory(XmlDocument xmlDoc, string fileName)
        {
            string tempDirectory = FileUploadManager.GetTemporaryUploadFilePath();
            string xmlFilePath = Path.Combine(tempDirectory, string.Format("{0}.xml", fileName));
            string zipFilePath = Path.Combine(tempDirectory, string.Format("{0}.zip", fileName));
            //save the formated xml file to physical disk
            SaveFormatedXml(xmlDoc, xmlFilePath);
            //zip the xml file
            CreateZip(xmlFilePath, zipFilePath);

            //delete xml file after create zip
            if (File.Exists(xmlFilePath))
                File.Delete(xmlFilePath);

            return zipFilePath;
        }

    }
}
