﻿
GO
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/*
=========================================================================================================================================================
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-225093]     An.TranTan		 20-Feb-2025		CREATE		Soft Delete record
=========================================================================================================================================================
*/
CREATE OR ALTER   PROC [dbo].[usp_delete_HUBOfferImportLarge] (  
	@LoginID INT,
	@ImportID INT
)  
AS  
BEGIN  
	--TODO: need to delete file from blob storage too
	UPDATE BorisGlobalTraderImports.dbo.tbHUBOfferImportLargeFile
	SET [status] = 'Deleted',
		UpdatedBy = @LoginID,
		DLUP = GETDATE()
	WHERE ID = @ImportID;
END  
GO


