/* printing */
/* IMPORTANT: use only simple style names with a single selector name */
/* e.g. .className
/* Marker     Changed by      Date         Remarks
/* [001]      Vinay           23/05/2012   This need to add notes field
/* [002]      Abhinav         25/06/2013   Print Notes filed as bold
/********************************************************/
.printShell {
	font-family:Tahoma, Arial, Sans-serif;
	font-size:10pt;
}
.printShellInner {
	font-family:Tahoma, Arial, Sans-serif;
	width:720px;
}
body.printing, body.email {
	background-image: none;
	margin: 30px 10px;
	background-color: #ffffff;
}
.printFrameOuter {
	text-align: center;
}
.printFrame {
	margin: 0px auto;
	text-align: left;
}
.printLandscape .printFrame {
	width: 100%;
}
.pageBreak {
	page-break-after:always;
}

/* content stuff */
/********************************************************/
.printHeader {
	font-family:Tahoma, Arial, Sans-serif;
	padding-bottom: 2em;
	text-align: center;
}
.documentNumber {
	font-family:Tahoma, Arial, Sans-serif;
	text-align: center;
	font-weight: bold;
	font-size: 12pt;
	padding-bottom: 1em;
	text-decoration: underline;
}
.printing_blockquote {
	font-family:Tahoma, Arial, Sans-serif;
	font-size: 9pt;
	padding-left: 25px;
}
.printSummary {
	font-family:Tahoma, Arial, Sans-serif;
}
.printTopBorder {
	border-top: solid 1px #999999;
	font-size:4pt;
}
.printBottomBorder {
	border-bottom: solid 1px #999999;
	font-size:4pt;
}
.printFinalSpace {
	clear: both;
}
.printFooterNotes {
	font-family:Tahoma, Arial, Sans-serif;
	clear: both;
	font-size: 8pt;
	color: #555555;
}
.printAd {
	font-family:Tahoma, Arial, Sans-serif;
	font-size: 7pt;
	color: #bbbbbb;
}
.documentMainInfo {
	width:720px;
	font-family:Tahoma, Arial, Sans-serif;
}
.documentMainInfo_td {
	font-family:Tahoma, Arial, Sans-serif;
	font-size:9pt;
}

/* items table */
/********************************************************/
.printItems {
	border-width: 0px;
	padding: 0px;
	width: 720px;
	font-family:Tahoma, Arial, Sans-serif;
}
.printItems_th {
	margin: 0px;
	padding: 4pt 2pt;
	vertical-align: top;
	text-align: left;
	line-height: 1.1em;
	font-size: 8pt;
	border-left: solid 3px #ffffff;
	border-bottom: solid 1px #999999;
	font-weight: bold;
	background-color: #e0e0e0;
}
.printItems_td {
	margin: 0px;
	padding: 4pt 2pt;
	vertical-align: top;
	text-align: left;
	line-height: 1.1em;
	font-size: 8pt;
	border-left: solid 3px #ffffff;
	border-bottom: solid 1px #999999;
}
.printItems_first {
	border-left-width: 0px;
}
.printItems_th_doubleTop {
	font-size: 8pt;
	padding-bottom:0.5em;
}
.printItems_th_doubleBottom {
	font-size: 8pt;
	color:#999999;
	border-top:1px dotted #999999;
	padding-top:0.2em;
	padding-bottom:0.5em;
}
.printItems_td_italic {
	font-style: italic;
	font-weight: bold;
	font-size: 8pt;
	padding-right: 2pt;
}
.printItems_printNotes {
}
.printItems_printLocalCurrency {
}
.printItems_printNotes_td {
	font-family:Tahoma, Arial, Sans-serif;
	font-size: 8pt;
	padding: 8pt;
	width: 480px;
	color: #666666;
	text-align: left;
	border:solid 1px #cccccc;	
	font-weight : bold;	
}
.printItems_AlignRight {
	text-align:right;
}
.alignRight {
	text-align:right;
}

/* Sales Order Report */
/********************************************************/
.soReport table.printDataItems td {
	font-size:9pt;
}
.soReport table.printDataItems td.title {
	font-weight: bold;
	padding-right: 1em;
}
.soReport .documentMainInfo {
	width:100%;
}
.soReport .printItems {
	width:100%;
}
tr.SOReport_LineShip td, tr.SOReport_SOLine td, tr.SOReport_POStock td, tr.SOReport_POLine td, tr.SOReport_LineStock td {
	font-size:7pt;
}
tr.SOReport_POLine td, tr.SOReport_LineStock td {
	color:#777777;
}
tr.SOReport_SOLine td {
	background-color:#eeeeee;
}
tr.grossProfit td {
	border-top:solid 1px #999999;
	border-bottom:solid 1px #999999;
	padding:3px 0px;
}
tr.SOReport_LineShip td.first, tr.SOReport_POLine td.first, tr.SOReport_LineStock td.first, tr.SOReport_POStock td.first {
	background-repeat:no-repeat;
	background-position:3px 3px;
}
tr.SOReport_LineShip td.first {
	background-image:url("images/tables/shipped.gif");
}
tr.SOReport_POLine td.first, tr.SOReport_LineStock td.first, tr.SOReport_POStock td.first {
	background-image:url("images/tables/allocated.gif");
}
td.SOReport_POLineIPO {
    font-size: 7pt;
}

td.SOReport_POLineIPO {
    color: #777777;
}
td.SOReport_POLineIPO {
    background-repeat: no-repeat;
    background-position: -1px 13px;
}
td.SOReport_POLineIPO  {
    background-image: url("images/hazardous/Hazardous.png");
}

td.SOReport_POLineECCN {
	font-size: 7pt;
}

td.SOReport_POLineECCN {
	color: #777777;
}

td.SOReport_POLineECCN {
	background-repeat: no-repeat;
	background-position: -1px 13px;
}

td.SOReport_POLineECCN {
	background-image: url("images/hazardous/ihspartstatuspng.png");
}

/* SubTotal table */
/********************************************************/
.printSubTotal {
	font-family:Tahoma, Arial, Sans-serif;
	width: 200px;
	border-width: 0px;
	padding: 0px;
	font-size: 8pt;
	float: right;
}
.printLCSubTotal {
	font-family:Tahoma, Arial, Sans-serif;
	width: 300px;
	border-width: 0px;
	padding: 0px;
	font-size: 8pt;
	float: left;
}
.printItems_td_printNotes {
	width: 480px;
}
.printItems_td_printSubTotal {
	width: 200px;
}
.printItems_td_middle {
	width: 40px;
}
.printSubTotal_td {
	vertical-align: top;
	padding: 2pt 4pt 2pt 2pt;
	font-size: 8pt;
}
.printSubTotal_td_col1 {
	font-weight: bold;
	text-align: left;
	width: 150px;
}
.printSubTotal_td_col2 {
	text-align: right;
}

.printLocalEquivalent_td {
	text-align: left;
	vertical-align: top;
	padding: 2pt 4pt 2pt 2pt;
	font-size: 8pt;
}

/* Currency cells */
/********************************************************/
.printItems_Currency {
	padding-left: 0pt !important;
	padding-right: 0pt !important;
	border-left-style: none;
}

/* data items */
/********************************************************/
.printDataItems {
	border-width: 0px;
	padding: 0px;
	font-family:Tahoma, Arial, Sans-serif;
}
.printDataItems_td {
	padding:2px 0px;
	font-size:8pt;
}
.printDataItems_title {
	font-weight: bold;
	font-size: 8pt;
	padding-right: 1em;
	width:95px;
}
.printDataItems_item {
}
table.printDataItems td.subtitle3 {
	font-weight: bold;
	background-color: #e0e0e0;
	border-bottom: #999999 1px solid;
	border-top: #999999 1px solid;
	border-right: #999999 1px solid;
	border-left: #999999 1px solid;
	height: 30px;
	vertical-align: middle;
	text-align: left;
	padding-left: 10px;
	font-size: 8pt;
}
table.printDataItems td.subtitle4 {
	background-color: #e0e0e0;
	border-bottom: #999999 1px solid;
	border-top: #999999 1px solid;
	border-right: #999999 1px solid;
	border-left: #999999 1px solid;
	height: 30px;
	vertical-align: middle;
	text-align: left;
	padding-left: 10px;
	color: white;
	font-size: 8pt;
}
table.printDataItems td.subitem3 {
	font-size: 8pt;
	border-bottom: #999999 1px solid;
	border-top: #999999 1px solid;
	border-right: #999999 1px solid;
	border-left: #999999 1px solid;
	height: 30px;
	vertical-align: middle;
	padding-left: 10px;
	text-align: left;
}

/* reports */
/********************************************************/
.printReport, .printReport .header, .printReport h2, .printReport .printReportItems, .printReportItems table th, .printReportItems table td {
	font-family: Lucida Sans Typewriter, Courier New, Courier !important;
	color: #000000;
	font-size: 10px;
	padding-bottom:20px;
}
.printReport table.printItems {
	width:100%;
}
.printReport h2 {
	position: static;
	text-decoration: underline;
	margin: 0px 0px 5px 0px;
}
.printReport .header {
	margin-bottom: 15px;
}
.printReportItems table th, .printReportItems table td {
	background-color: #ffffff;
	padding: 3px 8px 3px 2px;
	text-align: inherit;
	vertical-align:top;
}
.printReportItems table th {
	background-color: #eaeaea;
	border-bottom-width: 2px;
}
.printReportItems table tr.alt td {
	background-color: #f0f0f0;
}
.printReportItems table th.alignLeft, .printReportItems table td.alignLeft {
	text-align: left;
}
.printReportItems table th.alignRight, .printReportItems table td.alignRight {
	text-align: right;
}
.printReportItems table th.alignCenter, .printReportItems table td.alignCenter {
	text-align: center;
}
.printReportItems .summaryHeader {
	font-weight: bold;
	padding-top: 10px;
}
.printReportItems td.summary {
	font-weight: bold;
}

/* email */
/********************************************************/
body.email {
	background-color: #56954e;
	margin: 0px;
}
.emailSelection {
	font-family: tahoma;
	font-size: 11px;
	position: relative;
	display: block;
	color: #ffffff;
	padding: 0px;
	width: 100%;
}
.emailSelection h4 {
	color: #ffffff;
	font-size: 11px;
	margin: 0px 5px 5px;
	text-transform: uppercase;
	border-bottom: 1px dotted #cccccc;
	padding-bottom: 2px;
}
.emailSelection .footerLinks {
	background-color: #b6f2ac;
	padding: 10px;
	margin: 10px 0px;
}

/* signature block */
/********************************************************/
.signatureBlock {
	width:600px;
	margin:50px 0px 0px 100px;
	border:1px dotted #000000;
	padding:30px 15px 15px;
}
.signatureBlock table {
	padding:0px;
	margin:0px;
}
.signatureBlock table td {
	text-align:left;
	white-space:nowrap;
}
.signatureBlock table td.sig {
	border-bottom:dashed 1px #666666;
	width:370px;
}
.signatureBlock table td.date {
	padding-left:20px;
}
.signatureBlock table td.dateSig {
	border-bottom:dashed 1px #666666;
	width:150px;
}
/* boxed data items */
/********************************************************/
table.printDataItemsBox {
	width:99%;
	border-bottom: #999999 1px solid;
}
table.printDataItemsBox td {
	font-size:8pt;
	padding:8px;
	border: #999999 1px solid;
	border-width:1px 1px 0px;
}
table.printDataItemsBox td.title {
	font-weight: bold;
	background-color: #e0e0e0;
	border-right-style:none;
	width:150px;
}
table.printDataItemsBox td.item {
	vertical-align: middle;

}
table.printDataItemsBox td.title2 {
	border-right-style:none;
	font-weight: bold;
	background-color:#ffffff;
}


.printCurrencyNotes {
	font-family:Tahoma, Arial, Sans-serif;
	clear: both;
	font-size: 8pt;
	color: #555555;
}

.traceability
{
	width:600px;
	margin: 23px 0 0 127px;
}

.HIDECOMPANYONSTOP {
    margin: 23px 0 0 127px;
    width: 600px;
}

.rebate-account{
	color: red;
	font-size: 15px;
	font-weight: bold;
	margin-left: 4px;
	padding-bottom: 5px;
}

table.invoice-radio-format td label {
	float: right;
	padding-top: 2px;
}

table.invoice-radio-format td:last-child {
	padding-left: 10px
}
.vertical-align-middle{
	vertical-align: middle !important;
}