﻿GO

IF OBJECT_ID('dbo.Usp_InsertSupplierAPIDataDigikey', 'P') IS NOT NULL
BEGIN
    DROP PROCEDURE dbo.Usp_InsertSupplierAPIDataDigikey;
END

GO
	/****** Object:  StoredProcedure [dbo].[Usp_InsertSupplierAPIDataDigikey]    Script Date: 7/26/2024 11:42:06 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO 

/*
===========================================================================================
TASK      			UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-204486]			Phuc Hoang			26-Jul-2024		Create			Sourcing - the Digikey API to be added to the supplier data feeds section
[US-210537]			Phuc Hoang			08-Aug-2024		Create			Digikey API - Manual Trigger for data refreshed in the database if it is older than 3 days
===========================================================================================
*/

CREATE PROCEDURE [dbo].[Usp_InsertSupplierAPIDataDigikey] 
	(
		@ClientId INT,
		@JSON NVARCHAR(MAX),
		@PartSearch NVARCHAR(MAX),
		@ApiURLKeyNo INT,
		@UserId INT = NULL
	) 
AS 

BEGIN 

DELETE tbDigiKeyAPIPartAttributes 
FROM tbDigiKeyAPIPartAttributes attr
JOIN tbSupplierAPI tf ON tf.SupplierAPIId = attr.SupplierAPINo
WHERE tf.PartNumber LIKE @PartSearch AND tf.ApiURLKeyNo = @ApiURLKeyNo AND attr.DLUP < DATEADD(day,-3, GETDATE()); 

DELETE tbSupplierAPIPricing 
FROM tbSupplierAPIPricing attr
JOIN tbSupplierAPI tf ON tf.SupplierAPIId = attr.SupplierAPINo
WHERE tf.PartNumber LIKE @PartSearch AND tf.ApiURLKeyNo = @ApiURLKeyNo AND attr.DLUP < DATEADD(day,-3, GETDATE()); 

DELETE FROM tbSupplierAPI WHERE PartNumber LIKE @PartSearch AND ApiURLKeyNo = @ApiURLKeyNo AND DLUP < DATEADD(day,-3, GETDATE());

SELECT PartNumber,
	ProductDescription,
	ManufacturerName,
	UnitPrice,
	WebUrl,
	DatasheetUrl,
	PhotoUrl,
	PrimaryVideoUrl,
	QuantityAvailable,
	ProductStatus,
	--Discontinued,
	--EndOfLife,
	--Ncnr,
	CategoryName,
	ManufacturerLeadWeeks,
	SeriesName,
	--ShippingInfo,
	ReachStatus,
	RohsStatus,
	MSL,
	ECCN,
	HtsusCode,
	CurrencyCode,
	DigiKeyProductNumber,
	PackageType,
	SupplierName,
	MaxQuantityForDistribution,
	MinimumOrderQuantity,
	StandardPackage,
	Mpn
	--,DigiReelFee
INTO #tmptbSupplierAPILookupPartsProcess        
FROM OPENJSON(@JSON) WITH (
		Products NVARCHAR(MAX) N'$.Products' AS JSON,
		CurrencyCode NVARCHAR(256) N'$.SearchLocaleUsed.Currency'
	)
	OUTER APPLY OPENJSON(Products) WITH (
		ProductDescription NVARCHAR(MAX) '$.Description.DetailedDescription',
		ManufacturerName NVARCHAR(MAX) '$.Manufacturer.Name',
		PartNumber NVARCHAR(MAX) '$.ManufacturerProductNumber',
		Mpn NVARCHAR(MAX) '$.ManufacturerProductNumber',
		UnitPrice DECIMAL(15, 5) '$.UnitPrice',
		WebUrl NVARCHAR(MAX) '$.ProductUrl',
		DatasheetUrl NVARCHAR(MAX) '$.DatasheetUrl',
		PhotoUrl NVARCHAR(MAX) '$.PhotoUrl',
		PrimaryVideoUrl NVARCHAR(MAX) '$.PrimaryVideoUrl',
		QuantityAvailable INT '$.QuantityAvailable',
		ProductStatus NVARCHAR(MAX) '$.ProductStatus.Status',
		--Discontinued BIT '$.Discontinued',
		--EndOfLife BIT '$.EndOfLife',
		--Ncnr BIT '$.Ncnr',
		CategoryName NVARCHAR(MAX) '$.Category.Name',
		ManufacturerLeadWeeks NVARCHAR(MAX) '$.ManufacturerLeadWeeks',
		ManufacturerPublicQuantity INT '$.ManufacturerPublicQuantity',
		SeriesName NVARCHAR(MAX) '$.Series.Name',
		--ShippingInfo NVARCHAR(MAX) '$.ShippingInfo',
		ReachStatus NVARCHAR(MAX) '$.Classifications.ReachStatus',
		RohsStatus NVARCHAR(MAX) '$.Classifications.RohsStatus',
		MSL NVARCHAR(MAX) '$.Classifications.MoistureSensitivityLevel',
		ECCN NVARCHAR(MAX) '$.Classifications.ExportControlClassNumber',
		HtsusCode NVARCHAR(MAX) '$.Classifications.HtsusCode',

		ProductVariations NVARCHAR(MAX) N'$.ProductVariations' AS JSON
	)
	OUTER APPLY OPENJSON(ProductVariations) WITH (
		DigiKeyProductNumber NVARCHAR(MAX) '$.DigiKeyProductNumber',
		PackageType NVARCHAR(MAX) '$.PackageType.Name',
		SupplierName NVARCHAR(MAX) '$.Supplier.Name',
		MaxQuantityForDistribution INT '$.MaxQuantityForDistribution',
		MinimumOrderQuantity INT '$.MinimumOrderQuantity',
		StandardPackage INT '$.StandardPackage'
		--,DigiReelFee INT '$.DigiReelFee'
	); 
----------------------------------------------------------------------------------------		
SELECT * INTO #tmptbSupplierAPILookupParts         
FROM #tmptbSupplierAPILookupPartsProcess;       

----------------------------------------------------------------------------------------		
INSERT INTO tbSupplierAPI(
		PartNumber, DatasourceName, SellerPartNumber, WebUrl, Mpn, QuantityAvailable, QuantityFactory, QuantityMinimum,
		FactoryLeadTime, FactoryLeadTimeUnits, [Site], currencyCode, ClientNo, ApiURLKeyNo, UpdatedBy
		--OrderMultQty,
		--QuantityOnOrder,
		--QuantityCommitted,
		--UnitWeight, 
		--PricingType,
	)
SELECT  PartNumber, 'DigiKey', DigiKeyProductNumber, WebUrl, Mpn, QuantityAvailable, 0, MinimumOrderQuantity, 
	    ManufacturerLeadWeeks, 'Weeks', 'NA', CurrencyCode, @ClientId, @ApiURLKeyNo, @UserId
FROM #tmptbSupplierAPILookupParts        
WHERE DigiKeyProductNumber IS NOT NULL;
----------------------------------------------------------------------------------------

SELECT 
	DigiKeyProductNumber,
	PackageType,
	ManufacturerName,
	Rohs,
	ProductName,
	ProductDescription,
	ECCN,
	MSL,
	HtsusCode,
	UnitPrice
INTO #tmptbSupplierAPIPartAttributes        
FROM OPENJSON(@JSON, N'$.Products') WITH (
		ManufacturerName NVARCHAR(MAX) '$.Manufacturer.Name',
		ProductName NVARCHAR(MAX) '$.Description.ProductDescription',
		ProductDescription NVARCHAR(MAX) '$.Description.DetailedDescription',
		ReachStatus NVARCHAR(MAX) '$.Classifications.ReachStatus',
		Rohs NVARCHAR(MAX) '$.Classifications.RohsStatus',
		MSL NVARCHAR(MAX) '$.Classifications.MoistureSensitivityLevel',
		ECCN NVARCHAR(MAX) '$.Classifications.ExportControlClassNumber',
		HtsusCode NVARCHAR(MAX) '$.Classifications.HtsusCode',
		UnitPrice DECIMAL(15, 5) '$.UnitPrice',
		
		ProductVariations NVARCHAR(MAX) N'$.ProductVariations' AS JSON
	)
	OUTER APPLY OPENJSON(ProductVariations) WITH (
		DigiKeyProductNumber NVARCHAR(MAX) '$.DigiKeyProductNumber',
		PackageType NVARCHAR(MAX) '$.PackageType.Name'
	);

----------------------------------------------------------------------------------------
INSERT INTO tbDigiKeyAPIPartAttributes (
	SupplierAPINo, DigiKeyProductNumber, PackageType, ManufacturerName, Rohs, ProductName, ProductDescription, ECCN, UnitPrice, UpdatedBy
)
SELECT tl.SupplierAPIId, DigiKeyProductNumber, PackageType, ManufacturerName, Rohs, ProductName, ProductDescription, ECCN, UnitPrice, @UserId
FROM #tmptbSupplierAPIPartAttributes ta        
	JOIN tbSupplierAPI tl ON ta.DigiKeyProductNumber = tl.SellerPartNumber
WHERE tl.SupplierAPIId NOT IN (
		SELECT SupplierAPINo
		FROM tbDigiKeyAPIPartAttributes
	)
ORDER BY tl.SupplierAPIId ASC;

----------------------------------------------------------------------------------------
--SELECT  DigiKeyProductNumber, PhotoUrl 
--INTO #tbSupplierAPIImages        
--FROM OPENJSON(@JSON, N'$.Products') WITH (
--		PhotoUrl NVARCHAR(MAX) '$.PhotoUrl',
--		ProductVariations nvarchar(MAX) N'$.ProductVariations' AS JSON
--	) 
--	OUTER APPLY OPENJSON(ProductVariations) WITH(
--		DigiKeyProductNumber NVARCHAR(MAX) '$.DigiKeyProductNumber'
--	);

------------------------------------------------------------------------------------------
--INSERT INTO tbSupplierAPIImages([Url], [SupplierAPINo], [UpdatedBy])
--SELECT ti.PhotoUrl,
--	tl.SupplierAPIId,
--	@UserId
--FROM #tbSupplierAPIImages ti        
--	join tbSupplierAPI tl ON ti.DigiKeyProductNumber = tl.SellerPartNumber
--WHERE tl.SupplierAPIId Not In (
--		SELECT DISTINCT SupplierAPINo
--		FROM tbSupplierAPIImages
--	)
--ORDER BY tl.SupplierAPIId ASC;
----------------------------------------------------------------------------------------
SELECT 
	DigiKeyProductNumber,
	BreakQuantity,
	UnitPrice,
	TotalPrice
INTO #tbSupplierAPIPricing        
FROM OPENJSON(@JSON, N'$.Products') WITH (
		ProductVariations NVARCHAR(MAX) N'$.ProductVariations' AS JSON
	)
	OUTER APPLY OPENJSON(ProductVariations) WITH (
		DigiKeyProductNumber NVARCHAR(MAX) '$.DigiKeyProductNumber',
		StandardPricing NVARCHAR(MAX) '$.StandardPricing' AS JSON
	)
	OUTER APPLY OPENJSON(StandardPricing) WITH (
		BreakQuantity INT '$.BreakQuantity',
		UnitPrice decimal(15, 5) '$.UnitPrice',
		TotalPrice decimal(15, 5) '$.TotalPrice'
	);
----------------------------------------------------------------------------------------
INSERT INTO tbSupplierAPIPricing(
	UnitPrice, QuantityTo, SupplierAPINo, UpdatedBy
	)
SELECT tp.UnitPrice, tp.BreakQuantity, tl.SupplierAPIId, @UserId
FROM #tbSupplierAPIPricing tp        
	join tbSupplierAPI tl ON tp.DigiKeyProductNumber = tl.SellerPartNumber
WHERE tl.SupplierAPIId Not In (
		SELECT SupplierAPINo
		FROM tbSupplierAPIPricing
	)
ORDER BY tl.SupplierAPIId ASC;

----------------------------------------------------------------------------------------
DROP TABLE #tmptbSupplierAPILookupPartsProcess,#tmptbSupplierAPILookupParts,#tmptbSupplierAPIPartAttributes,#tbSupplierAPIPricing;     
----------------------------------------------------------------------------------------

DECLARE @SupType VARCHAR(MAX);

SELECT @SupType = coty.Name
FROM tbCompany co
	JOIN tbCompanyType coty on co.TypeNo = coty.CompanyTypeId
WHERE co.clientno = 114
	AND co.FullName = 'DigiKeyCorporationUSD'
	AND co.IsSupplier = 1
	AND co.POApproved = 1
SELECT tf.SupplierAPIId,
	tf.QuantityAvailable QuantityInSupplier
	,tf.QuantityAvailable QuantityOnOrder
	,(
		select top 1 ManufacturerId
		from dbo.tbManufacturer
		where partid.[ManufacturerName] = ManufacturerName
	) ManufacturerNo
	,0 SupplierNo
	,null ResalePrice --st.ResalePrice                                    
	,null ROHS --st.ROHS  iif(partid.rohs='Y',1,0)                                  
	,'WarehouseName' WarehouseName --wh.WarehouseName                                    
	,'Location' Location --st.Location                                    
	,partid.PackageType PackageName --pk.PackageName              
	,'SupplierPart' SupplierPart --st.SupplierPart              
	,tf.ClientNo ClientNo --st.ClientNo                                    
	,cl.ClientName ClientName --cl.ClientName                                    
	,'ClientDataVisibleToOthers' ClientDataVisibleToOthers --cl.OwnDataVisibleToOthers AS ClientDataVisibleToOthers                                              
	,'N' SupplierLTB
	,'TR' SupplierMOQ
	,tf.QuantityAvailable QuantityAvailable
	,tf.currencyCode ClientBaseCurrencyCode --cu.CurrencyCode AS ClientBaseCurrencyCode                       
	,cl.ClientCode ClientCode --cl.ClientCode                  
	,'' ClientDataVisibleToOthers --ClientDataVisibleToOthers                  
	,'TR' SPQ
	,'N' LTB
	,tf.QuantityAvailable UpliftPrice
	,tf.DLUP
	,tf.QuantityAvailable Quantity
	,tf.PartNumber Part
	,tf.DLUP OriginalEntryDate
	,tf.DLUP GTDate
	,partid.[ProductDescription] [Description]
	,partid.ManufacturerName ManufacturerCode --mf.ManufacturerCode                      
	,partid.DateCode DateCode --st.DateCode            
	,tf.DatasourceName SupplierName
	,@SupType SupplierType
	,tf.WebUrl Reference --, tf.offers_categories_subcategory_name ProductName              
	,partid.ProductName ProductName
	,partid.PackageType PackageType --pk.PackageName              
	,partid.ECCN ECCN --, tf.offers_documents_publish_date PublishDate
	,Null PublishDate
	,tf.QuantityMinimum MOQ
	,tf.QuantityMinimum VirtualCostPrice
	,(
		SELECT TOP 1 CONCAT('$', UnitPrice)
		FROM tbSupplierAPIPricing
		WHERE SupplierAPINo = tf.SupplierAPIId
	) AS UnitCostPrice
FROM dbo.tbSupplierAPI tf
	LEFT JOIN dbo.tbDigiKeyAPIPartAttributes partid on tf.SupplierAPIId = partid.SupplierAPINo
	JOIN dbo.tbClient cl ON cl.ClientId = tf.ClientNo
WHERE (
		(tf.ClientNo = @ClientId)
		AND tf.ClientNo <> 109
		AND (tf.PartNumber LIKE @PartSearch)
	)
	AND tf.ApiURLKeyNo = @ApiURLKeyNo
ORDER BY tf.DLUP DESC

END

GO
