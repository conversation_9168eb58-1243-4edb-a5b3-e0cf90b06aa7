<%@ Control Language="C#" CodeBehind="DebitMainInfo_Export.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.DebitMainInfo_Export" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>

<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="false" AllowQuickHelp="false">

	<Explanation>
		<asp:Label ID="lblExplainExport" runat="server" CssClass="invisible"><%=Functions.GetGlobalResource("Messages", "ConfirmExportDebit")%></asp:Label>
		<asp:Label ID="lblExplainRelease" runat="server" CssClass="invisible"><%=Functions.GetGlobalResource("Messages", "ConfirmReleaseDebit")%></asp:Label>
	</Explanation>
	
	<Content>
	
		<ReboundUI_Table:Form id="frm" runat="server">
		
            <ReboundUI_Form:FormField id="ctlNotes" runat="server" FieldID="lblNotes" ResourceTitle="DebitNoteNo">
				<Field><Field><asp:Label ID="lblNotes" runat="server" /></Field></Field>
			</ReboundUI_Form:FormField>
            
            <ReboundUI_Form:FormField id="ctlSupplier" runat="server" FieldID="lblSupplier" ResourceTitle="Supplier">
				<Field><asp:Label ID="lblSupplier" runat="server" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlConfirm" runat="server" FieldID="ctlConfirmation" ResourceTitle="Confirm">
				<Field><ReboundUI:Confirmation ID="ctlConfirmation" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			
		</ReboundUI_Table:Form>
		
	</Content>
	
</ReboundUI_Form:DesignBase>
