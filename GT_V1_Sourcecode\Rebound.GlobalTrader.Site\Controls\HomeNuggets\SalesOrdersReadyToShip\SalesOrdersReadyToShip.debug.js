///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.HomeNuggets");

Rebound.GlobalTrader.Site.Controls.HomeNuggets.SalesOrdersReadyToShip = function(element) { 
	Rebound.GlobalTrader.Site.Controls.HomeNuggets.SalesOrdersReadyToShip.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.HomeNuggets.SalesOrdersReadyToShip.prototype = {

	get_pnlReady: function() { return this._pnlReady; }, 	set_pnlReady: function(value) { if (this._pnlReady !== value)  this._pnlReady = value; }, 
	get_tblReady: function() { return this._tblReady; }, 	set_tblReady: function(value) { if (this._tblReady !== value)  this._tblReady = value; }, 
	get_pnlMore: function() { return this._pnlMore; }, 	set_pnlMore: function(value) { if (this._pnlMore !== value)  this._pnlMore = value; }, 

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.HomeNuggets.SalesOrdersReadyToShip.callBaseMethod(this, "initialize");
		this.addRefreshEvent(Function.createDelegate(this, this.getData));
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this._tblReady) this._tblReady.dispose();
		this._pnlReady = null;
		this._tblReady = null;
		this._pnlMore = null;
		Rebound.GlobalTrader.Site.Controls.HomeNuggets.SalesOrdersReadyToShip.callBaseMethod(this, "dispose");
	},
	
	setupLoadingState: function() {
		$R_FN.showElement(this._pnlMore, false);
		$R_FN.showElement(this._pnlReady, false);
		Rebound.GlobalTrader.Site.Controls.HomeNuggets.SalesOrdersReadyToShip.callBaseMethod(this, "setupLoadingState");
	},
	
	showNoData: function(bln) {
		this.showContent(true);
		$R_FN.showElement(this._pnlNoData, bln);
	},
	
	getData: function() {
		this.setupLoadingState();
		var obj = new Rebound.GlobalTrader.Site.Data();
		obj.set_PathToData("controls/HomeNuggets/SalesOrdersReadyToShip");
		obj.set_DataObject("SalesOrdersReadyToShip");
		obj.set_DataAction("GetData");
		obj.addParameter("rowcount", this._intRowCount);
		obj.addDataOK(Function.createDelegate(this, this.getDataComplete));
		obj.addError(Function.createDelegate(this, this.homeNuggetDataError));
		obj.addTimeout(Function.createDelegate(this, this.homeNuggetDataError));
		$R_DQ.addToQueue(obj);
		$R_DQ.processQueue();
		obj = null;
	},
	
	getDataComplete: function(args) {
		this.showNoData(args._result.Count == 0);
		$R_FN.showElement(this._pnlMore, true);
		var result = args._result;
		//ready
		this._tblReady.clearTable();
		for (var i = 0; i < result.Ready.length; i++) {
			var row = result.Ready[i];
			var aryData = [
				$RGT_nubButton_ShipSalesOrder(row.ID, row.No),
				$RGT_nubButton_Company(row.CMNo, row.CM),
				row.Promised
				];
			this._tblReady.addRow(aryData, null);
		}
		$R_FN.showElement(this._pnlReady, result.Ready.length > 0);
		this.hideLoading();
	}
};

Rebound.GlobalTrader.Site.Controls.HomeNuggets.SalesOrdersReadyToShip.registerClass("Rebound.GlobalTrader.Site.Controls.HomeNuggets.SalesOrdersReadyToShip", Rebound.GlobalTrader.Site.Controls.HomeNuggets.Base, Sys.IDisposable);
