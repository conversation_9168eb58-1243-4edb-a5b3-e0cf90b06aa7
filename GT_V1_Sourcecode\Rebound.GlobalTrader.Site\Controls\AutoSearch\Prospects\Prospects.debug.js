///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.AutoSearch");

Rebound.GlobalTrader.Site.Controls.AutoSearch.Prospects = function(element) { 
	Rebound.GlobalTrader.Site.Controls.AutoSearch.Prospects.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.AutoSearch.Prospects.prototype = {

	initialize: function() 	{
		Rebound.GlobalTrader.Site.Controls.AutoSearch.Prospects.callBaseMethod(this, "initialize");
		this.addDataReturnedEvent(Function.createDelegate(this, this.dataReturned));
		this.setupDataObject("Prospects");
	},
	
	dispose: function() { 
		if (this.isDisposed) return;
		Rebound.GlobalTrader.Site.Controls.AutoSearch.Prospects.callBaseMethod(this, "dispose");
	},
	
	dataReturned: function() {
		if (!this._result) return;
		if (this._result.TotalRecords > 0) {
			for (var i = 0, l = this._result.Results.length; i < l; i++) {
				var res = this._result.Results[i];
				var strHTML = "";
				if (this._enmResultsActionType == $R_ENUM$AutoSearchResultsActionType.Navigate) {
					strHTML = $RGT_nubButton_Company(res.ID, res.Name);
				} else {
					strHTML = $R_FN.setCleanTextValue(res.Name);
				}
				this.addResultItem(strHTML, $R_FN.setCleanTextValue(res.Name), res.ID);
				strHTML = null; res = null;
			}
		}
	}
};

Rebound.GlobalTrader.Site.Controls.AutoSearch.Prospects.registerClass("Rebound.GlobalTrader.Site.Controls.AutoSearch.Prospects", Rebound.GlobalTrader.Site.Controls.AutoSearch.Base, Sys.IDisposable);
