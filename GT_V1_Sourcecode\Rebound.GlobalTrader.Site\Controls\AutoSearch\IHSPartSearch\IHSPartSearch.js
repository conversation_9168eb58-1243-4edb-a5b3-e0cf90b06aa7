Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.AutoSearch");Rebound.GlobalTrader.Site.Controls.AutoSearch.IHSPartSearch=function(n){Rebound.GlobalTrader.Site.Controls.AutoSearch.IHSPartSearch.initializeBase(this,[n]);this._searchType=null};Rebound.GlobalTrader.Site.Controls.AutoSearch.IHSPartSearch.prototype={get_searchType:function(){return this._searchType},set_searchType:function(n){this._searchType!==n&&(this._searchType=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.AutoSearch.IHSPartSearch.callBaseMethod(this,"initialize");this.addDataReturnedEvent(Function.createDelegate(this,this.dataReturned));this.setupDataObject("IHSPartSearch")},dispose:function(){this.isDisposed||(this._searchType=null,Rebound.GlobalTrader.Site.Controls.AutoSearch.IHSPartSearch.callBaseMethod(this,"dispose"))},dataReturned:function(){var r,t,u,n,i;if($("#lblservicemsgerror").text(""),r=this._result.ServiceStatus,r==!1)$("#lblservicemsgerror").text("Sorry, the IHS part lookup service is currently unavilable.");else{if(!this._result)return;if(this._result.TotalRecords>0)for($("#lblservicemsgerror").text(""),t=0,u=this._result.Results.length;t<u;t++)n=this._result.Results[t],i="",this._enmResultsActionType!=$R_ENUM$AutoSearchResultsActionType.Navigate&&(i=$R_FN.setCleanTextValue(n.Name)),this.addResultItem(i,$R_FN.setCleanTextValue(n.Name),n.ID,n.ExtraValue),i=null,n=null}$R_FN.setInnerHTML(this._lblResults,String.format("Quick Add: {0} result(s)",this._result.TotalRecords))}};Rebound.GlobalTrader.Site.Controls.AutoSearch.IHSPartSearch.registerClass("Rebound.GlobalTrader.Site.Controls.AutoSearch.IHSPartSearch",Rebound.GlobalTrader.Site.Controls.AutoSearch.Base,Sys.IDisposable);