﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

IF OBJECT_ID('[usp_source_AltParts]', 'P') IS NOT NULL
	DROP PROC [dbo].[usp_source_AltParts]
GO

CREATE PROCEDURE [dbo].[usp_source_AltParts]
	--********************************************************************************************                                                                
	--*Marker     Changed by    Date         Remarks                                      
	--*[001]      ABhinav Saxena           27/02/23      Create Proc for alternative parts.                                    
	--********************************************************************************************   
	/*
===========================================================================================
TASK      			UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[Bug-209448]		Ngai To				26-Jul-2024		UPDATE			Bug 209448: [PROD Bug] Supplier names do not appear on the sourcing screen
===========================================================================================
*/
	@ClientId INT,
	@PartSearch NVARCHAR(50),
	@Index INT = 1,
	@StartDate DATETIME = NULL,
	@FinishDate DATETIME = NULL,
	@OrderBy INT = 1,
	@SortDir INT = 1
	WITH RECOMPILE
AS
BEGIN
	--DECLARE VARIABLE                            
	DECLARE @Month INT
	DECLARE @FROMDATE DATETIME
	DECLARE @ENDDATE DATETIME
	DECLARE @OutPutDate DATETIME
	DECLARE @FinishDateVW DATETIME
	DECLARE @FROMDATEVW DATETIME
	DECLARE @ENDDATEVW DATETIME

	SET @Month = 6

	/*                            
        When we get index 1 then we find the maximum date from matching record                            
        and decsrease no of month for the start date.                            
     */
	DECLARE @HUBName NVARCHAR(300)

	SELECT TOP 1 @HUBName = CompanyName
	FROM tbCompany
	WHERE ClientNo = @ClientId
		AND IsPOHub = 1

	IF @Index = 1
	BEGIN
		SELECT @FinishDateVW = MAX(ISNULL(OfferStatusChangeDate, OriginalEntryDate))
		FROM tbSourcingResult tsr
		WHERE tsr.SourcingTable IN ('ALTPART')
			AND tsr.FullPart LIKE @PartSearch

		SET @FROMDATEVW = dbo.ufn_get_date_from_datetime(DATEADD(month, - @Month, @FinishDateVW))
		SET @ENDDATEVW = dbo.ufn_get_date_from_datetime(@FinishDateVW)

		SELECT @FinishDate = MAX(ISNULL(OfferStatusChangeDate, OriginalEntryDate))
		FROM [BorisGlobalTraderImports].dbo.tbAlternativePart o
		JOIN tbClient cl ON o.ClientNo = cl.ClientId
		WHERE (
				(o.ClientNo = @ClientId)
				OR (
					o.ClientNo <> @ClientId
					AND (
						CASE 
							WHEN o.ClientNo = 114
								THEN cast(1 AS BIT)
							ELSE cl.OwnDataVisibleToOthers
							END
						) = 1
					)
				)
			AND FullPart LIKE @PartSearch

		SET @FROMDATE = dbo.ufn_get_date_from_datetime(DATEADD(month, - @Month, @FinishDate))
		SET @ENDDATE = dbo.ufn_get_date_from_datetime(@FinishDate)
		SET @FROMDATE = dbo.ufn_get_date_from_datetime(@StartDate)
		SET @ENDDATE = dbo.ufn_get_date_from_datetime(@FinishDate)
		SET @FROMDATEVW = dbo.ufn_get_date_from_datetime(@StartDate)
		SET @ENDDATEVW = dbo.ufn_get_date_from_datetime(@FinishDate)
	END

	--SET THE OUTPUT DATE                            
	SET @OutPutDate = DATEADD(month, - @Month, @FinishDate)

	SELECT o.AlternativePartId AS OfferId,
		o.FullPart COLLATE DATABASE_DEFAULT AS FullPart,
		o.Part COLLATE DATABASE_DEFAULT AS Part,
		o.ManufacturerNo,
		o.DateCode COLLATE DATABASE_DEFAULT AS DateCode,
		o.ProductNo,
		o.PackageNo,
		o.Quantity,
		CASE 
			WHEN o.ClientNo = 114
				THEN 0
			ELSE o.Price
			END AS Price --o.Price                                     
		,
		o.OriginalEntryDate,
		o.Salesman,
		o.SupplierNo,
		o.CurrencyNo,
		o.ROHS,
		o.UpdatedBy,
		o.DLUP,
		o.OfferStatusNo,
		ISNULL(o.OfferStatusChangeDate, o.OriginalEntryDate) AS OfferStatusChangeDate,
		o.OfferStatusChangeLoginNo,
		m.ManufacturerCode,
		p.ProductName,
		c.CurrencyCode,
		c.CurrencyDescription,
		--CASE 
		--	WHEN o.ClientNo = 114
		--		THEN @HUBName
		--	ELSE ISNULL(s.CompanyName, o.SupplierName)
		--	END AS SupplierName,
		ISNULL(s.CompanyName, o.SupplierName) AS SupplierName,
		ISNULL(m.ManufacturerName, o.ManufacturerName) AS ManufacturerName,
		s.EMail AS SupplierEmail,
		l.EmployeeName AS SalesmanName,
		l2.EmployeeName AS OfferStatusChangeEmployeeName,
		g.PackageName,
		o.Notes COLLATE DATABASE_DEFAULT AS Notes,
		o.ClientNo,
		cl.ClientId,
		cl.ClientName,
		cl.OwnDataVisibleToOthers AS ClientDataVisibleToOthers
		--[001] code start                                    
		,
		isnull(cotype.Name, '') AS SupplierType
		--[001] code end                  
		,
		cl.ClientCode,
		dbo.ufn_GetSupplierMessage(SupplierNo) AS SupplierMessage -- soorya  
		,
		ishub = 0,
		ROW_NUMBER() OVER (
			ORDER BY CASE 
					WHEN @OrderBy = 1
						AND @SortDir = 2
						THEN Quantity
					END DESC,
				CASE 
					WHEN @OrderBy = 1
						THEN Quantity
					END,
				CASE 
					WHEN @OrderBy = 6
						AND @SortDir = 2
						THEN ISNULL(OfferStatusChangeDate, OriginalEntryDate)
					END DESC,
				CASE 
					WHEN @OrderBy = 6
						THEN ISNULL(OfferStatusChangeDate, OriginalEntryDate)
					END,
				CASE 
					WHEN @OrderBy = 7
						AND @SortDir = 2
						THEN Price
					END DESC,
				CASE 
					WHEN @OrderBy = 7
						THEN Price
					END
			) AS RowNum
	FROM [BorisGlobalTraderImports].dbo.tbAlternativePart o
	LEFT JOIN dbo.tbManufacturer m ON o.ManufacturerNo = m.ManufacturerId
	LEFT JOIN dbo.tbProduct p ON o.ProductNo = p.ProductId
	LEFT JOIN dbo.tbCurrency c ON o.CurrencyNo = c.CurrencyId
	LEFT JOIN dbo.tbCompany s ON o.SupplierNo = s.CompanyId
	LEFT JOIN dbo.tbLogin l ON o.Salesman = l.LoginId
	LEFT JOIN dbo.tbLogin l2 ON o.OfferStatusChangeLoginNo = l2.LoginId
	LEFT JOIN dbo.tbPackage g ON o.PackageNo = g.PackageId
	JOIN tbClient cl ON o.ClientNo = cl.ClientId
	LEFT JOIN dbo.tbCompanyType cotype ON s.TypeNo = cotype.CompanyTypeId
	WHERE (
			(o.ClientNo = @ClientId)
			OR (
				o.ClientNo <> @ClientId
				AND (
					CASE 
						WHEN o.ClientNo = 114
							THEN cast(1 AS BIT)
						ELSE cl.OwnDataVisibleToOthers
						END
					) = 1
				)
			)
		AND o.FullPart LIKE @PartSearch

	--AND (dbo.ufn_get_date_from_datetime(ISNULL(o.OfferStatusChangeDate, o.OriginalEntryDate)) between @FROMDATE   AND  @ENDDATE)                                      
	SELECT @OutPutDate AS OutPutDate
END
