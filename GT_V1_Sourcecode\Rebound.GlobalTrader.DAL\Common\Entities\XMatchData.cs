﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Rebound.GlobalTrader.DAL.Common.Entities
{
    public class XMatchData
    {
		#region Properties
		public System.Int32 SalesXMatchID { get; set; }
		public System.Int32 BOMManagerNo { get; set; }
		public System.String Part { get; set; }
		public System.Int32 InvoiceNumber { get; set; }
		public System.String InvoiceSls { get; set; }
		public System.String InvoiceCustomer { get; set; }
		public System.DateTime InvoiceDate { get; set; }
		public System.Int32 InvoiceQuantity { get; set; }
		public System.Decimal UnitPrice { get; set; }
		public System.Int32 POOrder { get; set; }
		public System.String POCompany { get; set; }
		public System.Int32 POQuantity { get; set; }
		public System.Decimal POPrice { get; set; }
		public System.String POCurrency { get; set; }
		public System.Int32 CustomerRequirementId { get; set; }
		public System.String SupplierMOQ { get; set; }
		public System.String SPQ { get; set; }
		public System.String DateCode { get; set; }
		public System.String LeadTime { get; set; }
		public int TotalCount { get; set; }
		public int curpage { get; set; }
		public Boolean OfferAddFlag { get; set; }

		#endregion
	}
}
