///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-------------------------------------------------------------------------------------------
// RP 17.05.2010:
// - [168]: Add new default setting for rating
//
// RP 06.01.2010:
// - fully dispose everything
//
// RP 17.11.2009:
// - add SupplierNo
//Marker     changed by      date          Remarks
//[001]      Vinay          25/03/2014     ESMS Ref:107 -  Add provision to Default Shipping from Country 
//[002]      Vinay          18/05/2015     ESMS Ref:233
//[003]      Aashu Singh    14-Sep-2018    [REB-12820]:Provision to add Global Security on Contact Section
//-------------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.CompanyPurchasingInfo_Edit = function(element) { 
	Rebound.GlobalTrader.Site.Controls.Forms.CompanyPurchasingInfo_Edit.initializeBase(this, [element]);
	this._intCompanyID = -1;
	this._blnFirstTimeApprovedClicked = true;
	this._blnApproved = false;
};

Rebound.GlobalTrader.Site.Controls.Forms.CompanyPurchasingInfo_Edit.prototype = {

    get_intCompanyID: function() { return this._intCompanyID; }, set_intCompanyID: function(value) { if (this._intCompanyID !== value) this._intCompanyID = value; },
    get_intDefaultRating: function() { return this._intDefaultRating; }, set_intDefaultRating: function(value) { if (this._intDefaultRating !== value) this._intDefaultRating = value; },
    get_intMailGroupNo: function() { return this._intMailGroupNo; }, set_intMailGroupNo: function(value) { if (this._intMailGroupNo !== value) this._intMailGroupNo = value; },

    initialize: function() {
        Rebound.GlobalTrader.Site.Controls.Forms.CompanyPurchasingInfo_Edit.callBaseMethod(this, "initialize");
        this.addShown(Function.createDelegate(this, this.formShown));
    },

    dispose: function() {
        if (this.isDisposed) return;
        if (this._ibtnSave) $R_IBTN.clearHandlers(this._ibtnSave);
        if (this._ibtnSave_Footer) $R_IBTN.clearHandlers(this._ibtnSave_Footer);
        if (this._ibtnCancel) $R_IBTN.clearHandlers(this._ibtnCancel);
        if (this._ibtnCancel_Footer) $R_IBTN.clearHandlers(this._ibtnCancel_Footer);
        if (this._chkApproved) this._chkApproved.dispose();
        this._intCompanyID = null;
        this._ibtnSave = null;
        this._ibtnSave_Footer = null;
        this._ibtnCancel = null;
        this._ibtnCancel_Footer = null;
        this._chkApproved = null;
        this._intMailGroupNo = null;
        this._blnApproved = null;
        Rebound.GlobalTrader.Site.Controls.Forms.CompanyPurchasingInfo_Edit.callBaseMethod(this, "dispose");
    },

    saveClicked: function() {
        if (!this.validateForm()) return;
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/CompanyPurchasingInfo");
        obj.set_DataObject("CompanyPurchasingInfo");
        obj.set_DataAction("SaveEdit");
        obj.addParameter("id", this._intCompanyID);
        obj.addParameter("IsApproved", this.getFieldValue("ctlApproved"));
        obj.addParameter("CurrencyNo", this.getFieldValue("ctlCurrency"));
        obj.addParameter("TermsNo", this.getFieldValue("ctlTerms"));
        //ESMS #14
        //obj.addParameter("TaxNo", this.getFieldValue("ctlTax"));
        obj.addParameter("Rating", this.getFieldValue("ctlRating"));
        //obj.addParameter("ShipViaNo", this.getFieldValue("ctlShipVia"));
        //obj.addParameter("ShippingAccountNo", this.getFieldValue("ctlShippingAccountNo"));
        obj.addParameter("ShipViaNo", null);
        obj.addParameter("ShippingAccountNo", null);
        obj.addParameter("ContactNo", this.getFieldValue("ctlContact"));
        obj.addParameter("SupplierNo", this.getFieldValue("ctlSupplierNo"));
        obj.addParameter("CountryNo", this.getFieldValue("ctlCountry"));
        //[002] code start
        obj.addParameter("OnStop", this.getFieldValue("ctlOnStop"));
        //[002] code end
        obj.addDataOK(Function.createDelegate(this, this.saveEditComplete));
        obj.addError(Function.createDelegate(this, this.saveEditError));
        obj.addTimeout(Function.createDelegate(this, this.saveEditError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
        //this.onSave();
    },

    formShown: function () {
        this.showField("ctlShipVia", false);
        this.showField("ctlShippingAccountNo", false);
        if (this._blnFirstTimeShown) {
            this.addSave(Function.createDelegate(this, this.saveClicked));
            this._chkApproved = $find(this.getField("ctlApproved").ControlID);
            this._chkApproved.addClick(Function.createDelegate(this, this.approveClicked));
        }
        //[003] start
        this.getFieldControl("ctlCurrency")._intGlobalLoginClientNo = this._globalLoginClientNo;
        this.getFieldControl("ctlTerms")._intGlobalLoginClientNo = this._globalLoginClientNo;
        this.getFieldControl("ctlShipVia")._intGlobalLoginClientNo = this._globalLoginClientNo;
        this.getFieldControl("ctlContact")._intGlobalLoginClientNo = this._globalLoginClientNo;
        this.getFieldControl("ctlCountry")._intGlobalLoginClientNo = this._globalLoginClientNo;
        //[003] end
        this.getFieldDropDownData("ctlCurrency");
        this.getFieldDropDownData("ctlTerms");
        //ESMS #14
        //this.getFieldDropDownData("ctlTax");
        this.getFieldDropDownData("ctlShipVia");
        this.getFieldDropDownData("ctlContact");
        //[001] code start
        this.getFieldDropDownData("ctlCountry");
        //[001] code end
        this._blnApproved = this._chkApproved._blnChecked;
    },

    saveEditError: function(args) {
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    saveEditComplete: function(args) {
        if (args._result.Result == true) {
            if (!this._blnApproved && this._chkApproved._blnChecked) {
                this.getMessageText();
            }
            this.onSaveComplete();

        } else {
            this._strErrorMessage = args._errorMessage;
            this.onSaveError();
        }
    },

    validateForm: function() {
        this.onValidate();
        var blnOK = this.autoValidateFields();
        return blnOK;
    },

    cancelClicked: function() {
        this.onCancel();
    },

    approveClicked: function() {
        //only set the default rating the first time
        if (this._blnFirstTimeApprovedClicked) {
            if (this._chkApproved._blnChecked) this.setFieldValue("ctlRating", this._intDefaultRating);
        }
        this._blnFirstTimeApprovedClicked = false;
    },
    getMessageText: function() {
        Rebound.GlobalTrader.Site.WebServices.GetMailMessage_CompanyPO(this._intCompanyID, Function.createDelegate(this, this.getMessageTextComplete));
    },
    getMessageTextComplete: function(strMsg) {
        this.sendMail(strMsg);
    },
    sendMail: function(strMsg) {
        Rebound.GlobalTrader.Site.WebServices.NotifyMessage("", this._intMailGroupNo, $R_RES.CompanyApproveSubject, strMsg, this._intCompanyID, Function.createDelegate(this, this.sendMailComplete));
    },
    sendMailComplete: function() {
        // this.onSaveComplete();
    }

};

Rebound.GlobalTrader.Site.Controls.Forms.CompanyPurchasingInfo_Edit.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CompanyPurchasingInfo_Edit", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
