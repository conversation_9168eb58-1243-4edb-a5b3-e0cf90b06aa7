Type.registerNamespace("Rebound.GlobalTrader.Site.Controls");Rebound.GlobalTrader.Site.Controls.DataItemRow=function(n){Rebound.GlobalTrader.Site.Controls.DataItemRow.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DataItemRow.prototype={get_tdTitle:function(){return this._tdTitle},set_tdTitle:function(n){this._tdTitle!==n&&(this._tdTitle=n)},get_lbl:function(){return this._lbl},set_lbl:function(n){this._lbl!==n&&(this._lbl=n)},get_chk:function(){return this._chk},set_chk:function(n){this._chk!==n&&(this._chk=n)},get_hid:function(){return this._hid},set_hid:function(n){this._hid!==n&&(this._hid=n)},get_hyp:function(){return this._hyp},set_hyp:function(n){this._hyp!==n&&(this._hyp=n)},get_stars:function(){return this._stars},set_stars:function(n){this._stars!==n&&(this._stars=n)},get_value:function(){return this._value},set_value:function(n){this._value!==n&&(this._value=n)},get_ctlEllipses:function(){return this._ctlEllipses},set_ctlEllipses:function(n){this._ctlEllipses!==n&&(this._ctlEllipses=n)},addEllipsesSetupData:function(n){this.get_events().addHandler("EllipsesSetupData",n)},removeEllipsesSetupData:function(n){this.get_events().removeHandler("EllipsesSetupData",n)},onEllipsesSetupData:function(){var n=this.get_events().getHandler("EllipsesSetupData");n&&n(this,Sys.EventArgs.Empty)},initialize:function(){Rebound.GlobalTrader.Site.Controls.DataItemRow.callBaseMethod(this,"initialize");this._ctlEllipses&&this._ctlEllipses.addSetupData(Function.createDelegate(this,this.onEllipsesSetupData))},dispose:function(){this.isDisposed||(this.get_element()&&$clearHandlers(this.get_element()),this._chk&&this._chk.dispose(),this._stars&&this._stars.dispose(),this._ctlEllipses&&this._ctlEllipses.dispose(),this._tdTitle=null,this._lbl=null,this._chk=null,this._hid=null,this._hyp=null,this._stars=null,this._value=null,this._ctlEllipses=null,Rebound.GlobalTrader.Site.Controls.DataItemRow.callBaseMethod(this,"dispose"),this.isDisposed=!0)},setValue:function(n,t){if(this._lbl){typeof n!="undefined"&&n||(n="");$R_FN.setInnerHTML(this._lbl,$R_FN.setCleanTextValue(n));this._value=n;return}if(this._chk){typeof n!="undefined"&&n||(n=!1);this._chk.setChecked(n);this._value=this._chk._blnChecked;return}if(this._stars){typeof n!="undefined"&&n||(n=0);this._stars.setRating(n);this._value=this._stars._intCurrentRating;return}if(this._hid){typeof n!="undefined"&&n||(n="");this._hid.value=$R_FN.setCleanTextValue(n);this._value=n;return}if(this._hyp){typeof n!="undefined"&&n||(n="javascript:void(0);");typeof t!="undefined"&&t||(t="");$R_FN.showElement(this._hyp,t.trim().length>0);$R_FN.setInnerHTML(this._hyp,$R_FN.setCleanTextValue(t));this._hyp.href=$R_FN.setCleanTextValue(n);this._value=$R_FN.setCleanTextValue(t);return}this._value=""},setEmailValue:function(n,t){t==""&&(t=n);this._hyp&&(this._hyp.target="");this.setValue($R_FN.formatEmail(n),n)},setURLValue:function(n,t,i){i&&(this._hyp&&(this._hyp.target="_blank"),n=$R_FN.formatURL(n));t==""&&(t=n);this.setValue(n,t)},showHide:function(n){$R_FN.showElement(this.get_element(),n)},getValue:function(){return this._lbl?this._lbl.innerHTML:this._chk?this._chk._blnChecked:this._stars?this._stars._intCurrentRating:this._hid?this._hid.value:this._hyp?this._hyp.text:void 0},setTitle:function(n){$R_FN.setInnerHTML(this._tdTitle,n)},hideEllipses:function(){this._ctlEllipses&&this._ctlEllipses.hide()},showEllipses:function(){this._ctlEllipses&&this._ctlEllipses.show()},resetEllipses:function(){this._ctlEllipses&&this._ctlEllipses.reset()},showEllipsesLoading:function(){this._ctlEllipses&&this._ctlEllipses.showLoading()},showEllipsesError:function(){this._ctlEllipses&&this._ctlEllipses.showError()},showEllipsesContent:function(){this._ctlEllipses&&this._ctlEllipses.showContent()}};Rebound.GlobalTrader.Site.Controls.DataItemRow.registerClass("Rebound.GlobalTrader.Site.Controls.DataItemRow",Sys.UI.Control,Sys.IDisposable);