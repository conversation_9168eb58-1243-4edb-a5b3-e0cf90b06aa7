///<reference name="MicrosoftAjax.js" />
///<reference path="~/Common/Data/Data.js" />
///<reference path="~/Common/Functions/Functions.js" />
///<reference path="~/Controls/IconButton/IconButton.js" />
//-----------------------------------------------------------------------------------------
// RP 16.03.2011:
// - change the way dropdown values are set, improving robustness
//
// RP 10.06.2010:
// - fix storeOriginalFieldValues not working correctly for DropDowns
//
// RP 11.03.2010:
// - stop save button firing if any dropdowns are still loading
//
// RP 26.01.2010:
// - fix problem with null value fields showing as having changed
//
// RP 06.01.2010:
// - dispose everything fully
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.Base = function (element) {
    Rebound.GlobalTrader.Site.Controls.Forms.Base.initializeBase(this, [element]);
    this._intIndex = -1;
    this._mode = "";
    this._strErrorMessage = "";
    this._blnShowQuickHelp = false;
    this._aryFields = [];
    this._blnShowRequiredFieldsExplanation = false;
    this._blnFirstTimeShown = true;
    this._blnIsShown = false;
    this._intPreviousScrollPosition = 0;
    this._aryChangedFieldIDs = [];
};

Rebound.GlobalTrader.Site.Controls.Forms.Base.prototype = {

    get_pnlContent: function () { return this._pnlContent; }, set_pnlContent: function (value) { if (this._pnlContent !== value) this._pnlContent = value; },
    get_pnlContentInner: function () { return this._pnlContentInner; }, set_pnlContentInner: function (value) { if (this._pnlContentInner !== value) this._pnlContentInner = value; },
    get_pnlNotes: function () { return this._pnlNotes; }, set_pnlNotes: function (value) { if (this._pnlNotes !== value) this._pnlNotes = value; },
    get_pnlLoading: function () { return this._pnlLoading; }, set_pnlLoading: function (value) { if (this._pnlLoading !== value) this._pnlLoading = value; },
    get_pnlHeader: function () { return this._pnlHeader; }, set_pnlHeader: function (value) { if (this._pnlHeader !== value) this._pnlHeader = value; },
    get_pnlSaving: function () { return this._pnlSaving; }, set_pnlSaving: function (value) { if (this._pnlSaving !== value) this._pnlSaving = value; },
    get_pnlSavedOK: function () { return this._pnlSavedOK; }, set_pnlSavedOK: function (value) { if (this._pnlSavedOK !== value) this._pnlSavedOK = value; },
    get_pnlValidateError: function () { return this._pnlValidateError; }, set_pnlValidateError: function (value) { if (this._pnlValidateError !== value) this._pnlValidateError = value; },
    get_pnlValidateErrorText: function () { return this._pnlValidateErrorText; }, set_pnlValidateErrorText: function (value) { if (this._pnlValidateErrorText !== value) this._pnlValidateErrorText = value; },
    get_pnlExplain: function () { return this._pnlExplain; }, set_pnlExplain: function (value) { if (this._pnlExplain !== value) this._pnlExplain = value; },
    get_ctlRelatedNugget: function () { return this._ctlRelatedNugget; }, set_ctlRelatedNugget: function (value) { if (this._ctlRelatedNugget !== value) this._ctlRelatedNugget = value; },
    get_blnShowQuickHelp: function () { return this._blnShowQuickHelp; }, set_blnShowQuickHelp: function (value) { if (this._blnShowQuickHelp !== value) this._blnShowQuickHelp = value; },
    get_aryFields: function () { return this._aryFields; }, set_aryFields: function (value) { if (this._aryFields !== value) this._aryFields = value; },
    get_objFieldOrdinals: function () { return this._objFieldOrdinals; }, set_objFieldOrdinals: function (value) { if (this._objFieldOrdinals !== value) this._objFieldOrdinals = value; },
    get_pnlLinksHolder: function () { return this._pnlLinksHolder; }, set_pnlLinksHolder: function (value) { if (this._pnlLinksHolder !== value) this._pnlLinksHolder = value; },
    get_pnlFooterLinksHolder: function () { return this._pnlFooterLinksHolder; }, set_pnlFooterLinksHolder: function (value) { if (this._pnlFooterLinksHolder !== value) this._pnlFooterLinksHolder = value; },
    get_intIndex: function () { return this._intIndex; }, set_intIndex: function (value) { if (this._intIndex !== value) this._intIndex = value; },
    get_h4: function () { return this._h4; }, set_h4: function (value) { if (this._h4 !== value) this._h4 = value; },
    get_hypQuickHelp: function () { return this._hypQuickHelp; }, set_hypQuickHelp: function (value) { if (this._hypQuickHelp !== value) this._hypQuickHelp = value; },
    get_lblQuickHelpText: function () { return this._lblQuickHelpText; }, set_lblQuickHelpText: function (value) { if (this._lblQuickHelpText !== value) this._lblQuickHelpText = value; },
    get_mode: function () { return this._mode; }, set_mode: function (value) { if (this._mode !== value) this._mode = value; },
    get_ibtnSave: function () { return this._ibtnSave; }, set_ibtnSave: function (value) { if (this._ibtnSave !== value) this._ibtnSave = value; },
    get_ibtnCancel: function () { return this._ibtnCancel; }, set_ibtnCancel: function (value) { if (this._ibtnCancel !== value) this._ibtnCancel = value; },
    get_ibtnSave_Footer: function () { return this._ibtnSave_Footer; }, set_ibtnSave_Footer: function (value) { if (this._ibtnSave_Footer !== value) this._ibtnSave_Footer = value; },
    get_ibtnCancel_Footer: function () { return this._ibtnCancel_Footer; }, set_ibtnCancel_Footer: function (value) { if (this._ibtnCancel_Footer !== value) this._ibtnCancel_Footer = value; },
    get_blnShowRequiredFieldsExplanation: function () { return this._blnShowRequiredFieldsExplanation; }, set_blnShowRequiredFieldsExplanation: function (value) { if (this._blnShowRequiredFieldsExplanation !== value) this._blnShowRequiredFieldsExplanation = value; },
    get_ctlMultiStep: function () { return this._ctlMultiStep; }, set_ctlMultiStep: function (v) { if (this._ctlMultiStep !== v) this._ctlMultiStep = v; },

    addValidate: function (handler) { this.get_events().addHandler("Validate", handler); },
    removeValidate: function (handler) { this.get_events().removeHandler("Validate", handler); },
    onValidate: function () {
        this.showError(false);
        this.resetFormFields();
        var handler = this.get_events().getHandler("Validate");
        if (handler) handler(this, Sys.EventArgs.Empty);
    },

    addSave: function (handler) { this.get_events().addHandler("Save", handler); },
    removeSave: function (handler) { this.get_events().removeHandler("Save", handler); },
    onSave: function () {
        this.showSaving(true);
        this.showError(false);
        var handler = this.get_events().getHandler("Save");
        if (handler) handler(this, Sys.EventArgs.Empty);
    },

    addSaveComplete: function (handler) { this.get_events().addHandler("SaveComplete", handler); },
    removeSaveComplete: function (handler) { this.get_events().removeHandler("SaveComplete", handler); },
    onSaveComplete: function () {
        this.showSaving(false);
        this.showError(false);
        this.showLoading(false);
        var handler = this.get_events().getHandler("SaveComplete");
        if (handler) handler(this, Sys.EventArgs.Empty);
    },

    addSaveError: function (handler) { this.get_events().addHandler("SaveError", handler); },
    removeSaveError: function (handler) { this.get_events().removeHandler("SaveError", handler); },
    onSaveError: function () {
        this.showSaving(false);
        this.showLoading(false);
        this.showError(false);
        this.showNuggetError(true, this._strErrorMessage);
        var handler = this.get_events().getHandler("SaveError");
        if (handler) handler(this, Sys.EventArgs.Empty);
    },

    addCancel: function (handler) { this.get_events().addHandler("Cancel", handler); },
    removeCancel: function (handler) { this.get_events().removeHandler("Cancel", handler); },
    onCancel: function () {
        var handler = this.get_events().getHandler("Cancel");
        if (handler) handler(this, Sys.EventArgs.Empty);
    },

    addModeChanged: function (handler) { this.get_events().addHandler("ModeChanged", handler); },
    removeModeChanged: function (handler) { this.get_events().removeHandler("ModeChanged", handler); },
    onModeChanged: function () {
        var handler = this.get_events().getHandler("ModeChanged");
        if (handler) handler(this, Sys.EventArgs.Empty);
    },

    addNotConfirmed: function (handler) { this.get_events().addHandler("NotConfirmed", handler); },
    removeNotConfirmed: function (handler) { this.get_events().removeHandler("NotConfirmed", handler); },
    onNotConfirmed: function () {
        var handler = this.get_events().getHandler("NotConfirmed");
        if (handler) handler(this, Sys.EventArgs.Empty);
    },

    addShown: function (handler) { this.get_events().addHandler("Shown", handler); },
    removeShown: function (handler) { this.get_events().removeHandler("Shown", handler); },
    onShown: function () {
        this.showError(false);
        this.showSaving(false);
        this.showLoading(false);
        this.showInnerContent(true);
        if (this._ctlRelatedNugget) {
            this._ctlRelatedNugget.control.showFooterContent(true);
            this._ctlRelatedNugget.control.showError(false);
        }
        var handler = this.get_events().getHandler("Shown");
        if (handler) handler(this, Sys.EventArgs.Empty);
        this._blnFirstTimeShown = false;
        this.scrollPageToForm();
    },

    initialize: function () {
        Rebound.GlobalTrader.Site.Controls.Forms.Base.callBaseMethod(this, "initialize");
        if (this._ibtnSave) this.addSaveClick(Function.createDelegate(this, this.doFormSave));
        if (this._ibtnCancel) this.addCancelClick(Function.createDelegate(this, this.onCancel));
    },

    dispose: function () {
        if (this.get_element()) $clearHandlers(this.get_element());
        if (this._ibtnSave) $R_IBTN.clearHandlers(this._ibtnSave);
        if (this._ibtnSave_Footer) $R_IBTN.clearHandlers(this._ibtnSave_Footer);
        if (this._ibtnCancel) $R_IBTN.clearHandlers(this._ibtnCancel);
        if (this._ibtnCancel_Footer) $R_IBTN.clearHandlers(this._ibtnCancel_Footer);
        this._pnlContent = null;
        this._pnlContentInner = null;
        this._pnlNotes = null;
        this._pnlLoading = null;
        this._pnlHeader = null;
        this._pnlSaving = null;
        this._pnlSavedOK = null;
        this._pnlValidateError = null;
        this._pnlValidateErrorText = null;
        this._pnlExplain = null;
        this._ctlRelatedNugget = null;
        this._aryFields = null;
        this._objFieldOrdinals = null;
        this._pnlLinksHolder = null;
        this._pnlFooterLinksHolder = null;
        this._intIndex = null;
        this._h4 = null;
        this._hypQuickHelp = null;
        this._lblQuickHelpText = null;
        this._ibtnSave = null;
        this._ibtnCancel = null;
        this._ibtnSave_Footer = null;
        this._ibtnCancel_Footer = null;
        this._blnShowRequiredFieldsExplanation = null;
        this._ctlMultiStep = null;
        Rebound.GlobalTrader.Site.Controls.Forms.Base.callBaseMethod(this, "dispose");
    },

    show: function (blnShow) {
        $R_FN.showElement(this.get_element(), blnShow);
        $R_FN.showElement(this._pnlContent, blnShow);
        $R_FN.showElement(this._pnlLinksHolder, blnShow);
        $R_FN.showElement(this._pnlFooterLinksHolder, blnShow);
        if (blnShow) {
            this.resetFormFields();
            this.showError(false);
            this._blnIsShown = true;
            this.onShown();
        } else {
            if (this._blnIsShown) {
                this._blnIsShown = false;
                this.scrollPageBackToPositionBeforeForm();
            }
        }
    },

    getField: function (strField) {
        var intPos = eval("this._objFieldOrdinals." + strField);
        if (intPos == undefined) eval(String.format("FormFieldNotFound_{0}()", strField)); //raise error
        return this._aryFields[intPos];
    },

    getFieldControl: function (strField, fld) {
        if (!fld) fld = this.getField(strField);
        switch (Number.parseInvariant(fld.Type.toString())) {
            case $R_ENUM$FormFieldControlType.TextBox: return $get(fld.ControlID); break;
            case $R_ENUM$FormFieldControlType.CheckBox: return $find(fld.ControlID); break;
            case $R_ENUM$FormFieldControlType.DropDown: return $find(fld.ControlID); break;
            case $R_ENUM$FormFieldControlType.StarRating: return $find(fld.ControlID); break;
            case $R_ENUM$FormFieldControlType.TimeSelect: return $find(fld.ControlID); break;
            case $R_ENUM$FormFieldControlType.Literal: return $get(fld.ControlID); break;
            case $R_ENUM$FormFieldControlType.FileUpload: return $find(fld.ControlID); break;
            case $R_ENUM$FormFieldControlType.Combo: return $find(fld.ControlID); break;
        }
    },

    getFieldComponent: function (strField) {
        return $find(this.getField(strField).ControlID);
    },

    getFieldElement: function (fld, strID) {
        return $get(String.format("{0}_{1}", fld.ID, strID));
    },

    showFormField: function (strField, bln) {
        var ctl = $get(this.getField(strField).ID);
        if (ctl) $R_FN.showElement(ctl, bln);
    },

    getFieldValue: function (strField, fld) {
        if (!fld) fld = this.getField(strField);
        var value;
        switch (Number.parseInvariant(fld.Type.toString())) {
            case $R_ENUM$FormFieldControlType.TextBox: value = $get(fld.ControlID).value; break;
            case $R_ENUM$FormFieldControlType.CheckBox: value = $find(fld.ControlID)._blnChecked; break;
            case $R_ENUM$FormFieldControlType.DropDown: value = $find(fld.ControlID).getValue(); break;
            case $R_ENUM$FormFieldControlType.StarRating: value = $find(fld.ControlID)._intCurrentRating; break;
            case $R_ENUM$FormFieldControlType.TimeSelect: value = $find(fld.ControlID).getValue(); break;
            case $R_ENUM$FormFieldControlType.Literal: value = $get(fld.ControlID).innerHTML; break;
            case $R_ENUM$FormFieldControlType.FileUpload: value = $find(fld.ControlID).getValue(); break;
            case $R_ENUM$FormFieldControlType.Combo: value = $find(fld.ControlID).getValue(); break;
        }
        return value;
    },

    getFieldComboText: function (strField, fld) {
        if (!fld) fld = this.getField(strField);
        return $find(fld.ControlID)._strSelectedText;
    },

    setFieldValue: function (strField, varValue, fld, varValue2) {
        if (!fld) fld = this.getField(strField);
        if (typeof (varValue) == "string") varValue = varValue.replace(/(<br \/>)|(<br\/>)|(<br>)/g, "\n");
        if (varValue == null) varValue = "";
        if (varValue == "&nbsp;") varValue = "";
        switch (Number.parseInvariant(fld.Type.toString())) {
            case $R_ENUM$FormFieldControlType.TextBox: $get(fld.ControlID).value = $R_FN.setCleanTextValue(varValue.toString()); break;
            case $R_ENUM$FormFieldControlType.CheckBox: $find(fld.ControlID).setChecked(varValue); break;
            case $R_ENUM$FormFieldControlType.DropDown: $find(fld.ControlID).setValue(varValue, varValue2); break;
            case $R_ENUM$FormFieldControlType.StarRating: $find(fld.ControlID).setRating(varValue); break;
            case $R_ENUM$FormFieldControlType.TimeSelect: $find(fld.ControlID).setValue(varValue); break;
            case $R_ENUM$FormFieldControlType.Literal: $get(fld.ControlID).innerHTML = varValue; break;
            case $R_ENUM$FormFieldControlType.Combo: $find(fld.ControlID).setValue(varValue, varValue2); break;
        }
    },

    setFieldInError: function (strField, blnInError, strMessage, fld) {
        if (!fld) fld = this.getField(strField);
        if (blnInError) {
            Sys.UI.DomElement.addCssClass($get(fld.ID), "formRowError");
        } else {
            Sys.UI.DomElement.removeCssClass($get(fld.ID), "formRowError");
        }
        if (Number.parseInvariant(fld.Type.toString()) == $R_ENUM$FormFieldControlType.FileUpload) $find(fld.ControlID).showFieldError(blnInError);
        var pnlMessages = this.getFieldElement(fld, "pnlMessages");
        if (pnlMessages) $R_FN.showElement(pnlMessages, blnInError);
        if (strMessage) $R_FN.setInnerHTML(pnlMessages, strMessage);
        pnlMessages = null;
    },

    showFieldLoading: function (strField, bln) {
        var fld = this.getField(strField);
        var pnlFieldControls = this.getFieldElement(fld, "pnlFieldControls");
        var pnlLoading = this.getFieldElement(fld, "pnlLoading");
        if (pnlLoading) $R_FN.showElement(pnlLoading, bln);
        if (pnlFieldControls) $R_FN.showElement(pnlFieldControls, !bln);
        pnlLoading = null; pnlFieldControls = null; fld = null;
    },

    resetFieldError: function (strField, fld) {
        this.setFieldInError(strField, false, "", fld);
    },

    getFieldDropDownData: function (strField) {
        var fld = this.getField(strField);
        if (!fld) return;
        if (fld.Type == $R_ENUM$FormFieldControlType.DropDown) {
            var ddl = $find(fld.ControlID);
            if (!ddl) return;
            ddl.getData();
        }
        fld = null;
    },

    getFieldDropDownText: function (strField) {
        var fld = this.getField(strField);
        var strOut = "";
        if (fld.Type == $R_ENUM$FormFieldControlType.DropDown) strOut = $find(fld.ControlID).getText();
        fld = null;
        return strOut;
    },

    getFieldDropDownExtraText: function (strField) {
        var fld = this.getField(strField);
        var strOut = "";
        if (fld.Type == $R_ENUM$FormFieldControlType.DropDown) strOut = $find(fld.ControlID).getExtraText();
        fld = null;
        return strOut;
    },

    checkFieldEntered: function (strField, fld) {
        if (!fld) fld = this.getField(strField);
        var blnEntered = true;
        switch (Number.parseInvariant(fld.Type.toString())) {
            case $R_ENUM$FormFieldControlType.TextBox: blnEntered = $R_FN.isEntered($get(fld.ControlID).value); break;
            case $R_ENUM$FormFieldControlType.DropDown: blnEntered = !$find(fld.ControlID).isSetAsNoValue(); break;
            case $R_ENUM$FormFieldControlType.FileUpload: blnEntered = $find(fld.ControlID).checkEntered(); break;
            case $R_ENUM$FormFieldControlType.Combo: blnEntered = $find(fld.ControlID).checkEntered(); break;
        }
        if (Boolean.parse(fld.Required)) {
            if (!blnEntered) {
                this.setFieldInError(strField, true, $R_RES.RequiredFieldMissingMessage, fld);
                if (Number.parseInvariant(fld.Type.toString()) == $R_ENUM$FormFieldControlType.FileUpload) $find(fld.ControlID).showFieldError(true);
            }
        }
        return blnEntered;
    },

    checkFieldNumeric: function (strField, fld) {
        if (!fld) fld = this.getField(strField);
        var blnOK = true;
        if (Boolean.parse(fld.IsNumeric)) {
            if (fld.Type == $R_ENUM$FormFieldControlType.TextBox) {
                blnOK = $R_FN.checkNumeric($get(fld.ControlID));
            }
            if (!blnOK) this.setFieldInError(null, true, $R_RES.NumericFieldError, fld);
        }
        return blnOK;
    },

    checkNumericFieldLessThan: function (strField, intMax, blnOrEqualTo) {
        var fld = this.getField(strField);
        var blnOK = true;
        if (Boolean.parse(fld.IsNumeric)) {
            if (fld.Type == $R_ENUM$FormFieldControlType.TextBox) {
                if ($R_FN.checkNumeric($get(fld.ControlID))) {
                    blnOK = Number.parseLocale(this.getFieldValue(null, fld).toString()) < ((blnOrEqualTo) ? intMax + 1 : intMax);
                } else {
                    blnOK = false;
                }
            }
            if (!blnOK) this.setFieldInError(null, true, String.format((blnOrEqualTo) ? $R_RES.NumericFieldLessThanOrEqualToError : $R_RES.NumericFieldLessThanError, intMax), fld);
        }
        return blnOK;
    },

    checkNumericFieldLessThanOrEqualTo: function (strField, intMax) {
        return this.checkNumericFieldLessThan(strField, intMax, true);
    },

    checkNumericFieldGreaterThan: function (strField, intMin, blnOrEqualTo) {
        var fld = this.getField(strField);
        var blnOK = true;
        if (Boolean.parse(fld.IsNumeric)) {
            if (fld.Type == $R_ENUM$FormFieldControlType.TextBox) {
                if ($R_FN.checkNumeric($get(fld.ControlID))) {
                    blnOK = Number.parseLocale(this.getFieldValue(null, fld).toString()) > ((blnOrEqualTo) ? intMin - 1 : intMin);
                } else {
                    blnOK = false;
                }
            }
            if (!blnOK) this.setFieldInError(null, true, String.format((blnOrEqualTo) ? $R_RES.NumericFieldGreaterThanOrEqualToError : $R_RES.NumericFieldGreaterThanError, intMin), fld);
        }
        return blnOK;
    },

    checkNumericFieldGreaterThanOrEqualTo: function (strField, intMin) {
        return this.checkNumericFieldGreaterThan(strField, intMin, true);
    },

    checkFieldValidEmail: function (strField, fld) {
        if (!fld) fld = this.getField(strField);
        if (Boolean.parse(fld.CheckForValidEmail)) {
            if (!$R_FN.isValidEmail(this.getFieldValue(null, fld))) {
                this.setFieldInError(null, true, $R_RES.EmailInvalidMessage, fld);
                return false;
            }
        }
        return true;
    },

    checkFieldValidURL: function (strField, fld) {
        if (!fld) fld = this.getField(strField);
        if (Boolean.parse(fld.CheckForValidURL)) {
            if (!$R_FN.isValidURL(this.getFieldValue(null, fld).toLowerCase())) {
                this.setFieldInError(null, true, $R_RES.URLInvalidMessage, fld);
                return false;
            }
        }
        return true;
    },

    fieldFocus: function () {
        var ctl = this.getFieldControl(strField);
        ctl.focus();
        ctl = null;
    },

    showField: function (strField, blnShow) {
        var fld = this.getField(strField);
        fld.Visible = blnShow;
        $R_FN.showElement($get(fld.ID), blnShow);
        fld = null;
    },

    disableDropdown: function (strField, blnShow) {
        var field = this.getField(strField);
        if (field.Type != $R_ENUM$FormFieldControlType.DropDown) return;

        var ctl = this.getFieldControl(null, field);
        if (!ctl || !ctl._lbx) return;

        ctl._lbx.disabled = blnShow;

        field = null;
        ctl = null;
    },

    addFieldCheckBoxClickEvent: function (strField, fn) {
        var ctl = this.getFieldControl(strField);
        if (!ctl) return;
        ctl.addClick(fn);
        ctl = null;
    },

    enableFieldCheckBox: function (strField, bln) {
        var fld = this.getField(strField);
        if (!fld) return;
        if (Number.parseInvariant(fld.Type.toString()) != $R_ENUM$FormFieldControlType.CheckBox) return;
        $find(fld.ControlID).enableButton(bln);
        fld = null;
    },

    clearField: function (strField, fld) {
        if (!fld) fld = this.getField(strField);
        switch (Number.parseInvariant(fld.Type.toString())) {
            case $R_ENUM$FormFieldControlType.TextBox: $get(fld.ControlID).value = ""; break;
            case $R_ENUM$FormFieldControlType.CheckBox: $find(fld.ControlID).setChecked(false); break;
            case $R_ENUM$FormFieldControlType.DropDown: $find(fld.ControlID).clearDropDown(); break;
            case $R_ENUM$FormFieldControlType.StarRating: $find(fld.ControlID).setRating(0); break;
            case $R_ENUM$FormFieldControlType.TimeSelect: $find(fld.ControlID).setValue("09:00"); break;
            case $R_ENUM$FormFieldControlType.Literal: $get(fld.ControlID).innerHTML = ""; break;
            case $R_ENUM$FormFieldControlType.FileUpload: $find(fld.ControlID).setValue(""); break;
            case $R_ENUM$FormFieldControlType.Combo: $find(fld.ControlID).reset(); break;
        }
    },

    setFieldToDefault: function (strField, fld) {
        if (!fld) fld = this.getField(strField);
        switch (Number.parseInvariant(fld.Type.toString())) {
            case $R_ENUM$FormFieldControlType.TextBox:
                if (fld.DefaultValue == undefined) fld.DefaultValue = "";
                break;
            case $R_ENUM$FormFieldControlType.CheckBox:
                if (fld.DefaultValue == undefined) fld.DefaultValue = false;
                break;
            case $R_ENUM$FormFieldControlType.DropDown:
                if (fld.DefaultValue == undefined) fld.DefaultValue = $find(fld.ControlID)._strNoValue_Value;
                break;
            case $R_ENUM$FormFieldControlType.StarRating:
                if (fld.DefaultValue == undefined) fld.DefaultValue = 0;
                break;
            case $R_ENUM$FormFieldControlType.TimeSelect:
                if (fld.DefaultValue == undefined) fld.DefaultValue = "09:00";
                break;
            case $R_ENUM$FormFieldControlType.FileUpload:
                $find(fld.ControlID).reset();
                return;
                break;
            case $R_ENUM$FormFieldControlType.Combo:
                $find(fld.ControlID).reset();
                return;
                break;
        }
        if (Number.parseInvariant(fld.Type.toString()) != $R_ENUM$FormFieldControlType.Literal) this.setFieldValue(null, fld.DefaultValue, fld);
    },


    autoValidateFields: function () {
        var blnOK = true;
        for (var i = 0, l = this._aryFields.length; i < l; i++) {
            var fld = this._aryFields[i];
            if (fld) {
                if (Boolean.parse(fld.Required)) { if (!this.checkFieldEntered(null, fld)) blnOK = false; }
                if (Boolean.parse(fld.CheckForValidEmail)) { if (!this.checkFieldValidEmail(null, fld)) blnOK = false; }
                if (Boolean.parse(fld.CheckForValidURL)) { if (!this.checkFieldValidURL(null, fld)) blnOK = false; }
                if (Boolean.parse(fld.IsNumeric)) { if (!this.checkFieldNumeric(null, fld)) blnOK = false; }
            }
            fld = null;
        }
        if (!blnOK) this.showError(true);
        return blnOK;
    },

    resetFormFields: function () {
        for (var i = 0, l = this._aryFields.length; i < l; i++) {
            var fld = this._aryFields[i];
            if (fld) this.resetFieldError(null, fld);
            fld = null;
        }
    },

    setFormFieldsToDefaults: function () {
        for (var i = 0, l = this._aryFields.length; i < l; i++) {
            var fld = this._aryFields[i];
            this.setFieldToDefault(null, fld);
            fld = null;
        }
    },

    storeOriginalFieldValues: function () {
        for (var i = 0, l = this._aryFields.length; i < l; i++) {
            var fld = this._aryFields[i];
            if (fld) {
                if (Number.parseInvariant(fld.Type.toString()) == $R_ENUM$FormFieldControlType.DropDown) {
                    fld.OriginalValue = $find(fld.ControlID)._initialValue;
                } else {
                    fld.OriginalValue = this.getFieldValue(null, fld);
                }
            }
            fld = null;
        }
    },

    hasFieldChanged: function (strField, fld) {
        if (!fld) fld = this.getField(strField);
        if (!fld) return false;
        if (fld.OriginalValue == undefined) return true;
        var varValue = this.getFieldValue(null, fld);
        if (fld.Type == $R_ENUM$FormFieldControlType.DropDown) {
            var ctl = this.getFieldControl(null, fld);
            if (ctl) {
                if (ctl.countOptions() == 0) return false;
            }
            if (varValue === "" || varValue == null) varValue = ctl._strNoValue_Value;
        }
        if (varValue == null) varValue = "";
        return varValue.toString() != fld.OriginalValue.toString();
    },

    getChangedFields: function (aryFieldIDs) {
        var strOut = "";
        Array.clear(this._aryChangedFieldIDs);
        //if aryFieldIDs is passed check only fields in array, else check them all
        var blnUseAllFields = true;
        if (aryFieldIDs) blnUseAllFields = false;
        for (var i = 0, l = (blnUseAllFields) ? this._aryFields.length : aryFieldIDs.length; i < l; i++) {
            var fld = (blnUseAllFields) ? this._aryFields[i] : this.getField(aryFieldIDs[i]);
            if (fld) {
                if (this.hasFieldChanged(null, fld)) {
                    if (strOut != "") strOut += "||";
                    var strField = this.getFieldTitleResource("", fld);
                    strOut += strField;
                    Array.add(this._aryChangedFieldIDs, strField);
                }
            }
            fld = null;
        }
        return strOut;
    },

    changeTitle: function (str) {
        $R_FN.setInnerHTML(this._h4, str);
    },

    changeExplanation: function (str) {
        $R_FN.setInnerHTML(this._pnlExplain, str);
    },

    changeMode: function (strMode) {
        this._mode = strMode;
        this.onModeChanged();
    },

    showError: function (bln, strText) {
        $R_FN.showElement(this._pnlValidateError, bln);
        if (bln) {
            $R_FN.showElement(this._pnlSaving, false);
            $R_FN.showElement(this._pnlSavedOK, false);
            $R_FN.showElement(this._pnlLoading, false);
            $R_FN.showElement(this._pnlContentInner, true);
            $R_FN.showElement(this._pnlLinksHolder, true);
            $R_FN.showElement(this._pnlFooterLinksHolder, true);
            if (this._ctlRelatedNugget) {
                this._ctlRelatedNugget.control.showLoading(false);
                this._ctlRelatedNugget.control.showRefresh(true);
            }
            if (!strText) strText = "";
            this._pnlValidateErrorText.innerHTML = strText;
        }
    },

    showLoading: function (bln) {
        $R_FN.showElement(this._pnlLoading, bln);
        if (this._ctlRelatedNugget) this._ctlRelatedNugget.control.showLoading(bln);
        if (bln) {
            $R_FN.showElement(this._pnlSaving, false);
            $R_FN.showElement(this._pnlSavedOK, false);
            $R_FN.showElement(this._pnlContentInner, false);
            $R_FN.showElement(this._pnlNotes, false);
        }
    },

    showSaving: function (bln) {
        $R_FN.showElement(this._pnlSaving, bln);
        if (this._ctlRelatedNugget) this._ctlRelatedNugget.control.showLoading(bln);
        if (bln) {
            if (this._ctlRelatedNugget) this._ctlRelatedNugget.control.showRefresh(true);
            $R_FN.showElement(this._pnlLinksHolder, false);
            $R_FN.showElement(this._pnlFooterLinksHolder, false);
            $R_FN.showElement(this._pnlSavedOK, false);
            $R_FN.showElement(this._pnlLoading, false);
            $R_FN.showElement(this._pnlNotes, false);
            $R_FN.showElement(this._pnlContentInner, false);
        }
    },

    showSavedOK: function (bln) {
        $R_FN.showElement(this._pnlSavedOK, bln);
        if (bln) {
            $R_FN.showElement(this._pnlSaving, false);
            $R_FN.showElement(this._pnlLoading, false);
            $R_FN.showElement(this._pnlNotes, false);
            $R_FN.showElement(this._pnlContentInner, false);
        }
    },

    showContent: function (bln) {
        $R_FN.showElement(this._pnlContent, bln);
        if (bln) {
            if (this._ctlRelatedNugget) this._ctlRelatedNugget.control.showLoading(false);
            $R_FN.showElement(this._pnlNotes, this._blnShowRequiredFieldsExplanation);
        } else {
            $R_FN.showElement(this._pnlNotes, false);
        }
    },

    showInnerContent: function (bln) {
        $R_FN.showElement(this._pnlContentInner, bln);
        if (bln) {
            $R_FN.showElement(this._pnlLinksHolder, true);
            $R_FN.showElement(this._pnlFooterLinksHolder, true);
            $R_FN.showElement(this._pnlContent, true);
            $R_FN.showElement(this._pnlLoading, false);
            $R_FN.showElement(this._pnlSaving, false);
            $R_FN.showElement(this._pnlSavedOK, false);
        }
    },

    showNuggetError: function (blnShow, strMessage) {
        if (this._ctlRelatedNugget) this._ctlRelatedNugget.control.showError(blnShow, strMessage);
    },

    addSaveClick: function (evt) {
        $R_IBTN.addClick(this._ibtnSave, evt);
        if (this._ibtnSave_Footer) $R_IBTN.addClick(this._ibtnSave_Footer, evt);
    },

    doFormSave: function () {
        //if drop downs are loading, stop the save and flicker the button
        if (this.areAnyDropDownsLoading()) {
            this.flickerSaveButtons();
            return;
        }

        this.showError(true);
        this.showSaving(true);
        this.showError(false);
        this.onSave();
    },

    flickerSaveButtons: function () {
        this.enableSaveButtons(false);
        setTimeout(Function.createDelegate(this, this.finishFlickerSaveButtons), 400);
    },

    finishFlickerSaveButtons: function () {
        this.enableSaveButtons(true);
    },

    enableSaveButtons: function (bln) {
        if (this._ibtnSave) $R_IBTN.enableButton(this._ibtnSave, bln);
        if (this._ibtnSave_Footer) $R_IBTN.enableButton(this._ibtnSave_Footer, bln);
    },

    areAnyDropDownsLoading: function () {
        //check if dropdowns are loading
        var blnDropDownsLoading = false;
        for (var i = 0, l = this._aryFields.length; i < l; i++) {
            var fld = this._aryFields[i];
            if (fld) {
                if (Number.parseInvariant(fld.Type.toString()) == $R_ENUM$FormFieldControlType.DropDown) {
                    if ($find(fld.ControlID).isLoading()) {
                        blnDropDownsLoading = true;
                        break;
                    }
                }
            }
            fld = null;
        }
        return blnDropDownsLoading;
    },

    addCancelClick: function (evt) {
        $R_IBTN.addClick(this._ibtnCancel, evt);
        if (this._ibtnCancel_Footer) $R_IBTN.addClick(this._ibtnCancel_Footer, evt);
    },

    gotoStep: function (intStep) {
        if (this._ctlMultiStep) {
            this._ctlMultiStep.gotoStep(intStep);
            this.showError(false);
        }
    },

    nextStep: function () {
        if (this._ctlMultiStep) {
            this._ctlMultiStep.nextStep();
            this.showError(false);
        }
    },

    prevStep: function () {
        if (this._ctlMultiStep) {
            this._ctlMultiStep.prevStep();
            this.showError(false);
        }
    },

    resetSteps: function () {
        this.gotoStep(1);
    },

    disableStep: function (i) {
        if (this._ctlMultiStep) this._ctlMultiStep.disableStep(i);
    },

    scrollPageToForm: function () {
        this._intPreviousScrollPosition = $R_FN.windowScrollPosition();
        if (this._ctlRelatedNugget) this._ctlRelatedNugget.control.scrollPageToForm();
    },

    scrollPageBackToPositionBeforeForm: function () {
        $R_FN.finishScrollPageToElement(this._intPreviousScrollPosition);
    },

    setSavingMessage: function (str) {
        $R_FN.setInnerHTML(this._pnlSaving, str);
    },

    setSavedOKMessage: function (str) {
        $R_FN.setInnerHTML(this._pnlSavedOK, str);
    },

    getFieldTitleResource: function (str, fld) {
        if (!fld) fld = this.getField(str);
        var strOut = "";
        if (fld) strOut = $get(fld.ID).getAttribute("titleResource");
        fld = null;
        return strOut;
    },
    showFormWithoutError: function (bln, strText) {
        $R_FN.showElement(this._pnlValidateError, false);
        if (bln) {
            $R_FN.showElement(this._pnlSaving, false);
            $R_FN.showElement(this._pnlSavedOK, false);
            $R_FN.showElement(this._pnlLoading, false);
            $R_FN.showElement(this._pnlContentInner, true);
            $R_FN.showElement(this._pnlLinksHolder, true);
            $R_FN.showElement(this._pnlFooterLinksHolder, true);
            if (this._ctlRelatedNugget) {
                this._ctlRelatedNugget.control.showLoading(false);
                this._ctlRelatedNugget.control.showRefresh(true);
            }
            //            if (!strText) strText = "";
            //            this._pnlValidateErrorText.innerHTML = strText;
        }
    },
    checkControlEntered: function (strControlID, strControlType) {
        // 
        //if (!fld) fld = this.getField(strField);
        var blnEntered = true;
        switch (strControlType) {
            case "TextBox": blnEntered = $R_FN.isEntered($get(strControlID).value); break;
            case "DropDown": blnEntered = !$find(strControlID).isSetAsNoValue(); break;
            case "FileUpload": blnEntered = $find(strControlID).checkEntered(); break;
            case "Combo": blnEntered = $find(strControlID).checkEntered(); break;
        }
        // if (Boolean.parse(fld.Required)) {
        if (!blnEntered) {
            this.setControlInError(strControlID, true, $R_RES.RequiredFieldMissingMessage);
            //if (Number.parseInvariant(fld.Type.toString()) == $R_ENUM$FormFieldControlType.FileUpload) $find(fld.ControlID).showFieldError(true);
        }
        else {
            document.getElementById(strControlID).style.border = '';
        }
        //}
        return blnEntered;
    },
    setControlInError: function (strControlID, blnInError, strMessage) {
        // 
        // if (!fld) fld = this.getField(strField);
        if (blnInError) {
            //Sys.UI.DomElement.addCssClass($find(strControlID), "formRowError");
            // document.getElementById(strControlID).focus();
            //document.getElementById(strControlID).style.borderColor = "red";
            //document.getElementById(strControlID).style.borderStyle = "solid";
            //Sys.UI.DomElement.addCssClass("lasttd", "formRowError");
            // document.getElementById(strControlID).style.borderColor = "red";
            document.getElementById(strControlID).style.border = '1px solid red';
            //lasttd
        } else {
            //document.getElementById(strControlID).focus();
            document.getElementById(strControlID).style.border = '';
        }
        //if (Number.parseInvariant(fld.Type.toString()) == $R_ENUM$FormFieldControlType.FileUpload) $find(fld.ControlID).showFieldError(blnInError);
        //var pnlMessages = this.getFieldElement(fld, "pnlMessages");
        //if (pnlMessages) $R_FN.showElement(pnlMessages, blnInError);
        //if (strMessage) $R_FN.setInnerHTML(pnlMessages, strMessage);
        //pnlMessages = null;
    },
    getControlValue: function (strControlID, strControlType) {

        var value;
        switch (strControlType) {
            case "TextBox": value = $get(strControlID).value; break;
            case "CheckBox": value = $find(strControlID)._blnChecked; break;
            case "DropDown": value = $find(strControlID).getValue(); break;
            case "StarRating": value = $find(strControlID)._intCurrentRating; break;
            case "TimeSelect": value = $find(strControlID).getValue(); break;
            case "Literal": value = $get(strControlID).innerHTML; break;
            case "FileUpload": value = $find(strControlID).getValue(); break;
            case "Combo": value = $find(strControlID).getValue(); break;
        }
        return value;
    },

    setControlValue: function (strControlType, strControlID, varValue, varValue2) {
        // if (!fld) fld = this.getField(strField);
        if (typeof (varValue) == "string") varValue = varValue.replace(/(<br \/>)|(<br\/>)|(<br>)/g, "\n");
        if (varValue == null) varValue = "";
        if (varValue == "&nbsp;") varValue = "";
        switch (strControlType) {
            case "TextBox": $get(strControlID).value = $R_FN.setCleanTextValue(varValue.toString()); break;
            case "CheckBox": $find(strControlID).setChecked(varValue); break;
            case "DropDown": $find(strControlID).setValue(varValue2); break;
            case "StarRating": $find(strControlID).setRating(varValue); break;
            case "TimeSelect": $find(strControlID).setValue(varValue); break;
            case "Literal": $get(strControlID).innerHTML = varValue; break;
            case "Combo": $find(strControlID).setValue(varValue, varValue2); break;
        }
    }


};

Rebound.GlobalTrader.Site.Controls.Forms.Base.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.Base", Sys.UI.Control, Sys.IDisposable);