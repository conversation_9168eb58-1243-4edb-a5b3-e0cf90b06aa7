using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Collections;
using System.Threading;

namespace Rebound.GlobalTrader.Site.Controls {
	[DefaultProperty("")]
	[ToolboxData("<{0}:MultiSelectionCount runat=server></{0}:MultiSelectionCount>")]
	public class MultiSelectionCount : Panel, IScriptControl, INamingContainer {

		#region Locals

		protected ScriptManager _sm;
		protected Table _tbl;
		protected Label _lblNumber;
		protected HyperLink _hypSelectAll;
		protected HyperLink _hypClearAll;

		#endregion

		#region Properties

		#endregion

		#region Constructors

		public MultiSelectionCount() { }

		#endregion

		#region Overrides

		/// <summary>
		/// OnInit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			((Pages.Base)Page).AddCSSFile("MultiSelectionCount.css");
			CssClass = "MultiSelectionCount invisible";
			base.OnInit(e);
		}

		protected override void CreateChildControls() {
			CssClass = "multiSelectionCount";

			//table
			_tbl = new Table();
			_tbl.CellPadding = 0;
			_tbl.CellSpacing = 0;
			_tbl.BorderStyle = BorderStyle.None;
			Controls.Add(_tbl);

			//Row
			TableRow tr = new TableRow();
			_tbl.Rows.Add(tr);

			//left number cell
			TableCell tdNumberLeft = new TableCell();
			tdNumberLeft.CssClass = "numberLeft";
			ControlBuilders.CreateLiteralInsideParent(tdNumberLeft, "&nbsp;");
			tr.Cells.Add(tdNumberLeft);

			//centre number cell
			TableCell tdNumberCentre = new TableCell();
			tdNumberCentre.CssClass = "numberCentre";
			_lblNumber = ControlBuilders.CreateLabelInsideParent(tdNumberCentre);
			tr.Cells.Add(tdNumberCentre);

			//right number cell
			TableCell tdNumberRight = new TableCell();
			tdNumberRight.CssClass = "numberRight";
			ControlBuilders.CreateLiteralInsideParent(tdNumberRight, "&nbsp;");
			tr.Cells.Add(tdNumberRight);

			//button centre cell
			TableCell tdButtonCentre = new TableCell();
			tdButtonCentre.CssClass = "buttonCentre";
			_hypClearAll = ControlBuilders.CreateHyperLinkInsideParent(tdButtonCentre, "invisible", "javascript:void(0);", Functions.GetGlobalResource("Misc", "ClearAll"));
			_hypSelectAll = ControlBuilders.CreateHyperLinkInsideParent(tdButtonCentre, "", "javascript:void(0);", Functions.GetGlobalResource("Misc", "SelectAll"));
			tr.Cells.Add(tdButtonCentre);

			//right button cell
			TableCell tdButtonRight = new TableCell();
			tdButtonRight.CssClass = "buttonRight";
			ControlBuilders.CreateLiteralInsideParent(tdButtonRight, "&nbsp;");
			tr.Cells.Add(tdButtonRight);

			base.CreateChildControls();
		}

		/// <summary>
		/// OnPreRender
		/// </summary>
		/// <param name="e"></param>
		protected override void OnPreRender(EventArgs e) {

			//add script reference
			if (!this.DesignMode) {
				_sm = ScriptManager.GetCurrent(Page);
				if (_sm == null) throw new HttpException("A ScriptManager control must exist on the current page.");
				_sm.RegisterScriptControl(this);
			}
			base.OnPreRender(e);
		}

		/// <summary>
		/// Render
		/// </summary>
		/// <param name="writer"></param>
		protected override void Render(HtmlTextWriter writer) {
			if (!this.DesignMode) _sm.RegisterScriptDescriptors(this);
			base.Render(writer);
		}

		#endregion

		#region IScriptControl Members

		protected virtual IEnumerable<ScriptReference> GetScriptReferences() {
			bool blnDebug = false;
#if (DEBUG)
			blnDebug = true;
#endif
			return new ScriptReference[] { Functions.GetScriptReference(blnDebug, "Rebound.GlobalTrader.Site", "Controls.Basic.MultiSelectionCount.MultiSelectionCount", true) };
		}

		protected virtual IEnumerable<ScriptDescriptor> GetScriptDescriptors() {
			ScriptControlDescriptor descriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.MultiSelectionCount", this.ClientID);
			descriptor.AddElementProperty("lblNumber", _lblNumber.ClientID);
			descriptor.AddElementProperty("hypSelectAll", _hypSelectAll.ClientID);
			descriptor.AddElementProperty("hypClearAll", _hypClearAll.ClientID);
			return new ScriptDescriptor[] { descriptor };
		}

		IEnumerable<ScriptReference> IScriptControl.GetScriptReferences() {
			return GetScriptReferences();
		}

		IEnumerable<ScriptDescriptor> IScriptControl.GetScriptDescriptors() {
			return GetScriptDescriptors();
		}

		#endregion
	}
}