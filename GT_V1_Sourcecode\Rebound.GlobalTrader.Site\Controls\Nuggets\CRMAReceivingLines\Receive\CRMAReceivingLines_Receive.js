Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.CRMAReceivingLines_Receive=function(n){Rebound.GlobalTrader.Site.Controls.Forms.CRMAReceivingLines_Receive.initializeBase(this,[n]);this._intCRMAID=0;this._intCRMALineID=0;this._intInvoiceLineID=0;this._intInvoiceLineAllocationID=0;this._intLoginID=0;this._intCustomerID=0;this._intGoodsInID=0;this._intGoodsInLineID=0;this._intManufacturerNo=-1;this._intProductNo=-1;this._intPackageNo=-1;this._intROHS=-1;this._strCustomerName="";this._intQuantityOutstanding=0;this._intGlobalClientNo=-1;this._partNo="";this._countSerialNo=-1;this._countSerialRecords=0;this._reqSerialNo=!1};Rebound.GlobalTrader.Site.Controls.Forms.CRMAReceivingLines_Receive.prototype={get_strSupplierName:function(){return this._strSupplierName},set_strSupplierName:function(n){this._strSupplierName!==n&&(this._strSupplierName=n)},get_intCRMAID:function(){return this._intCRMAID},set_intCRMAID:function(n){this._intCRMAID!==n&&(this._intCRMAID=n)},get_intCRMALineID:function(){return this._intCRMALineID},set_intCRMALineID:function(n){this._intCRMALineID!==n&&(this._intCRMALineID=n)},get_intGoodsInID:function(){return this._intGoodsInID},set_intGoodsInID:function(n){this._intGoodsInID!==n&&(this._intGoodsInID=n)},get_intLoginID:function(){return this._intLoginID},set_intLoginID:function(n){this._intLoginID!==n&&(this._intLoginID=n)},get_ibtnContinue:function(){return this._ibtnContinue},set_ibtnContinue:function(n){this._ibtnContinue!==n&&(this._ibtnContinue=n)},get_ibtnContinue_Footer:function(){return this._ibtnContinue_Footer},set_ibtnContinue_Footer:function(n){this._ibtnContinue_Footer!==n&&(this._ibtnContinue_Footer=n)},get_ibtnSend:function(){return this._ibtnSend},set_ibtnSend:function(n){this._ibtnSend!==n&&(this._ibtnSend=n)},get_ibtnSend_Footer:function(){return this._ibtnSend_Footer},set_ibtnSend_Footer:function(n){this._ibtnSend_Footer!==n&&(this._ibtnSend_Footer=n)},get_radNewOrExisting:function(){return this._radNewOrExisting},set_radNewOrExisting:function(n){this._radNewOrExisting!==n&&(this._radNewOrExisting=n)},get_trNewGI:function(){return this._trNewGI},set_trNewGI:function(n){this._trNewGI!==n&&(this._trNewGI=n)},get_trGoodsIn:function(){return this._trGoodsIn},set_trGoodsIn:function(n){this._trGoodsIn!==n&&(this._trGoodsIn=n)},get_ctlGoodsIn:function(){return this._ctlGoodsIn},set_ctlGoodsIn:function(n){this._ctlGoodsIn!==n&&(this._ctlGoodsIn=n)},get_lblCurrency:function(){return this._lblCurrency},set_lblCurrency:function(n){this._lblCurrency!==n&&(this._lblCurrency=n)},get_ctlSelectInvoiceLine:function(){return this._ctlSelectInvoiceLine},set_ctlSelectInvoiceLine:function(n){this._ctlSelectInvoiceLine!==n&&(this._ctlSelectPOLine=n)},get_lblSelectInvoiceLine:function(){return this._lblSelectInvoiceLine},set_lblSelectInvoiceLine:function(n){this._lblSelectInvoiceLine!==n&&(this._lblSelectPOLine=n)},get_strLoginFullName:function(){return this._strLoginFullName},set_strLoginFullName:function(n){this._strLoginFullName!==n&&(this._strLoginFullName=n)},get_btnAll:function(){return this._btnAll},set_btnAll:function(n){this._btnAll!==n&&(this._btnAll=n)},get_lblComplete:function(){return this._lblComplete},set_lblComplete:function(n){this._lblComplete!==n&&(this._lblComplete=n)},get_trSerialNo:function(){return this._trSerialNo},set_trSerialNo:function(n){this._trSerialNo!==n&&(this._trSerialNo=n)},get_btnAdd:function(){return this._btnAdd},set_btnAdd:function(n){this._btnAdd!==n&&(this._btnAdd=n)},get_btnRefresh:function(){return this._btnRefresh},set_btnRefresh:function(n){this._btnRefresh!==n&&(this._btnRefresh=n)},get_ctlGiSerialNumber:function(){return this._ctlGiSerialNumber},set_ctlGiSerialNumber:function(n){this._ctlGiSerialNumber!==n&&(this._ctlGiSerialNumber=n)},get_ctlMultiSelectionCount:function(){return this._ctlMultiSelectionCount},set_ctlMultiSelectionCount:function(n){this._ctlMultiSelectionCount!==n&&(this._ctlMultiSelectionCount=n)},get_tblSerialNoFinal:function(){return this._tblSerialNoFinal},set_tblSerialNoFinal:function(n){this._tblSerialNoFinal!==n&&(this._tblSerialNoFinal=n)},get_reqSerialNo:function(){return this._reqSerialNo},set_reqSerialNo:function(n){this._reqSerialNo!==n&&(this._reqSerialNo=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.CRMAReceivingLines_Receive.callBaseMethod(this,"initialize");this._ctlGiSerialNumber.addPotentialStatusChange(Function.createDelegate(this,this.ctlGiSerialNumber_PotentialStatusChange));this.addShown(Function.createDelegate(this,this.formShown))},formShown:function(){var n,t;this._blnFirstTimeShown&&(this.addSave(Function.createDelegate(this,this.saveClicked)),n=Function.createDelegate(this,this.continueClicked),$R_IBTN.addClick(this._ibtnContinue,n),$R_IBTN.addClick(this._ibtnContinue_Footer,n),t=Function.createDelegate(this,this.sendMail),$R_IBTN.addClick(this._ibtnSend,t),$R_IBTN.addClick(this._ibtnSend_Footer,t),this._ctlMail=$find(this.getField("ctlSendMailMessage").ID),this._ctlMail._ctlRelatedForm=this,this._ctlMultiStep.addStepChanged(Function.createDelegate(this,this.stepChanged)),this._ctlGoodsIn.addItemSelected(Function.createDelegate(this,this.selectGoodsInItem)),this.addFieldCheckBoxClickEvent("ctlSendMail",Function.createDelegate(this,this.chooseIfSendMail)),this._btnAdd&&$addHandler(this._btnAdd,"click",Function.createDelegate(this,this.serialSaveClicked)),this._btnAll&&$addHandler(this._btnAll,"click",Function.createDelegate(this,this.allClicked)),this._btnRefresh&&$addHandler(this._btnRefresh,"click",Function.createDelegate(this,this.refreshClicked)),this._ctlMultiSelectionCount.registerTable(this._ctlGiSerialNumber._tblResults));$R_FN.showElement(this._lblComplete,!1);this._tblSerialNoFinal.show(!1);this.showField("ctlAddUpdate",!1);this.showHideSerialNo(!1);this._countSerialRecords=0;this.setFormFieldsToDefaults();this.resetSteps();this.gotoStep(1)},dispose:function(){this.isDisposed||(this._ibtnContinue&&$R_IBTN.clearHandlers(this._ibtnContinue),this._ibtnContinue_Footer&&$R_IBTN.clearHandlers(this._ibtnContinue_Footer),this._ibtnSend&&$R_IBTN.clearHandlers(this._ibtnSend),this._ibtnSend_Footer&&$R_IBTN.clearHandlers(this._ibtnSend_Footer),this._ctlMail&&this._ctlMail.dispose(),this._ctlGoodsIn&&this._ctlGoodsIn.dispose(),this._ctlSelectInvoiceLine&&this._ctlSelectInvoiceLine.dispose(),this._ctlGiSerialNumber&&this._ctlGiSerialNumber.dispose(),this._strSupplierName=null,this._intCRMAID=null,this._intCRMALineID=null,this._intGoodsInID=null,this._intLoginID=null,this._ibtnContinue=null,this._ibtnContinue_Footer=null,this._ibtnSend=null,this._ibtnSend_Footer=null,this._radNewOrExisting=null,this._trNewGI=null,this._trGoodsIn=null,this._ctlGoodsIn=null,this._lblCurrency=null,this._ctlSelectInvoiceLine=null,this._lblSelectInvoiceLine=null,this._intInvoiceLineID=null,this._intInvoiceLineAllocationID=null,this._intCustomerID=null,this._intGoodsInLineID=null,this._intManufacturerNo=null,this._intProductNo=null,this._intPackageNo=null,this._intROHS=null,this._strCustomerName=null,this._intQuantityOutstanding=null,this._intGlobalClientNo=null,this._strLoginFullName=null,this._ctlGiSerialNumber=null,this._btnAll=null,this._lblComplete=null,this._trSerialNo=null,this._btnAdd=null,this._btnRefresh=null,this._ctlGiSerialNumber=null,this._ctlMultiSelectionCount=null,this._tblSerialNoFinal=null,this._countSerialRecords=null,this._countSerialNo=null,this._reqSerialNo=null,Rebound.GlobalTrader.Site.Controls.Forms.CRMAReceivingLines_Receive.callBaseMethod(this,"dispose"))},setFieldsFromHeader:function(n,t,i,r,u,f,e){this._strCustomerName=t;this._intCustomerID=i;this.setFieldValue("ctlCustomerRMA",n);this.setFieldValue("ctlCustomer",t);this._intWarehouseNo=r;this._intShipViaNo=u;this._intCurrencyNo=f;this._strCurrency=e},doInitialGoodsInSearch:function(){this._ctlGoodsIn._initialized||setTimeout(Function.createDelegate(this,this.doInitialGoodsInSearch),100);this._ctlGoodsIn.setFieldValue("ctlCRMANo",this.getFieldValue("ctlCustomerRMA"));this._ctlGoodsIn._intGlobalClientNo=this._intGlobalClientNo;this._ctlGoodsIn.getData()},selectGoodsInItem:function(){this._intGoodsInID=this._ctlGoodsIn.getSelectedID();this.continueClicked()},continueClicked:function(){this._strHeaderSelected=this.findWhichHeaderSelected();switch(this._ctlMultiStep._intCurrentStep){case 1:this.nextStep();break;case 2:this._strHeaderSelected=="NEW"?this.validateHeaderForm()&&this.nextStep():this.nextStep();break;case 3:this.nextStep();break;case 4:this.finishedForm()}},findWhichHeaderSelected:function(){for(var t,n=0;n<2;n++)if(t=$get(String.format("{0}_{1}",this._radNewOrExisting.id,n)),t.checked){$R_FN.showElement(this._trNewGI,n==0);$R_FN.showElement(this._trGoodsIn,n==1);switch(n){case 0:return"NEW";case 1:return"EXISTING"}}},stepChanged:function(){this._strHeaderSelected=this.findWhichHeaderSelected();var n=this._ctlMultiStep._intCurrentStep;$R_IBTN.enableButton(this._ibtnSave,n==3);$R_IBTN.enableButton(this._ibtnSave_Footer,n==3);$R_IBTN.showButton(this._ibtnSend,n==4);$R_IBTN.showButton(this._ibtnSend_Footer,n==4);$R_IBTN.showButton(this._ibtnSave,n!=4);$R_IBTN.showButton(this._ibtnSave_Footer,n!=4);$R_IBTN.showButton(this._ibtnCancel,n!=4);$R_IBTN.showButton(this._ibtnCancel_Footer,n!=4);$R_IBTN.showButton(this._ibtnContinue,n==1||n==4||this._strHeaderSelected=="NEW"&&n==2);$R_IBTN.showButton(this._ibtnContinue_Footer,n==1||n==4||this._strHeaderSelected=="NEW"&&n==2);this._ctlMultiStep.showSteps(n!=4);n==2&&($R_FN.showElement(this._trNewGI,this._strHeaderSelected=="NEW"),$R_FN.showElement(this._trGoodsIn,this._strHeaderSelected!="NEW"),this._strHeaderSelected=="NEW"?(this.setFieldValue("ctlDateReceived",$R_FN.shortDate()),this.setFieldValue("ctlReceivedBy",this._intLoginID),this.setFieldValue("ctlWarehouse",this._intWarehouseNo),this.setFieldValue("ctlShipVia",this._intShipViaNo),this.setFieldValue("ctlCurrency",this._intCurrencyNo),this.setFieldValue("ctlReceivedByLbl",this._strLoginFullName)):this.doInitialGoodsInSearch(),this.getFieldControl("ctlWarehouse")._intGlobalLoginClientNo=this._intGlobalClientNo,this.getFieldDropDownData("ctlWarehouse"),this.getFieldControl("ctlShipVia")._intGlobalLoginClientNo=this._intGlobalClientNo,this.getFieldDropDownData("ctlShipVia"),this.getFieldControl("ctlCurrency")._intGlobalLoginClientNo=this._intGlobalClientNo,this.getFieldDropDownData("ctlCurrency"),this.getFieldControl("ctlReceivedBy")._intGlobalLoginClientNo=this._intGlobalClientNo,this.getFieldDropDownData("ctlReceivedBy"),this.showField("ctlCurrency",this._intGlobalClientNo<1),this.showField("ctlLblCurrency",this._intGlobalClientNo>0),this.setFieldValue("ctlLblCurrency",this._strCurrency),this.showField("ctlReceivedBy",this._intGlobalClientNo<1),this.showField("ctlReceivedByLbl",this._intGlobalClientNo>0));n==3&&(this._reqSerialNo?$("#ctl00_cphMain_ctlReceivingLines_ctlDB_ctl14_frmReceive_ctlDB_ctlQuantity_pnlFieldControls").find("a").show():$("#ctl00_cphMain_ctlReceivingLines_ctlDB_ctl14_frmReceive_ctlDB_ctlQuantity_pnlFieldControls").find("a").hide(),this._strHeaderSelected=="NEW"?(this.setFieldValue("ctlCustomerDetail",this.getFieldValue("ctlCustomer")),this.setFieldValue("ctlAirWayBillDetail",this.getFieldValue("ctlAirWayBill")),this.setFieldValue("ctlReferenceDetail",this.getFieldValue("ctlReference")),this.setFieldValue("ctlReceivingNotesDetail",this.getFieldValue("ctlReceivingNotes"))):this.getGoodsIn(),this.showField("ctlGoodsInNumber",this._strHeaderSelected!="NEW"),this.getCRMALine(),this.getFieldDropDownData("ctlCountingMethod"));n==4&&(this._strHeaderSelected=="NEW"?(this.getMessageText(),this.setFieldValue("ctlSendMail",!1),this.showMailButtons()):this.finishedForm(),this._countSerialRecords=0)},saveHeaderThenLine:function(){this.showSaving(!0);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/GIAdd");n.set_DataObject("GIAdd");n.set_DataAction("AddNewCRMA");n.addParameter("ShipViaNo",this.getFieldValue("ctlShipVia"));n.addParameter("AirWayBill",this.getFieldValue("ctlAirWayBill"));n.addParameter("Reference",this.getFieldValue("ctlReference"));n.addParameter("CMNo",this._intCustomerID);n.addParameter("Notes",this.getFieldValue("ctlReceivingNotes"));n.addParameter("DateReceived",this.getFieldValue("ctlDateReceived"));n.addParameter("ReceivedBy",this._intGlobalClientNo>0?this._intLoginID:this.getFieldValue("ctlReceivedBy"));n.addParameter("CRMANo",this._intCRMAID);n.addParameter("WarehouseNo",this.getFieldValue("ctlWarehouse"));n.addParameter("CurrencyNo",this._intGlobalClientNo>0?this._intCurrencyNo:this.getFieldValue("ctlCurrency"));n.addParameter("Unavailable",!1);n.addParameter("GlobalClientNo",this._intGlobalClientNo);n.addDataOK(Function.createDelegate(this,this.saveHeaderThenLineOK));n.addError(Function.createDelegate(this,this.saveHeaderThenLineError));n.addTimeout(Function.createDelegate(this,this.saveHeaderThenLineError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},saveHeaderThenLineOK:function(n){n._result.NewID>0?(this._intGoodsInID=n._result.NewID,this.saveLine()):this.saveHeaderThenLineError(n)},saveHeaderThenLineError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},validateHeaderForm:function(){this.onValidate();var n=!0;return this.checkFieldEntered("ctlWarehouse")||(n=!1),this.checkFieldEntered("ctlShipVia")||(n=!1),this._intGlobalClientNo<=0&&(this.checkFieldEntered("ctlCurrency")||(n=!1)),this.checkFieldEntered("ctlReference")||(n=!1),this.checkFieldEntered("ctlDateReceived")||(n=!1),n||this.showError(!0),n},getCRMALine:function(){this.showCRMALineDataFieldsLoading(!0);$R_FN.showElement(this._pnlLines,!1);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/CRMAReceivingLines");n.set_DataObject("CRMAReceivingLines");n.set_DataAction("GetLine");n.addParameter("ID",this._intCRMALineID);n.addParameter("GICurrencyNo",this._intGlobalClientNo>0?this._intCurrencyNo:this.getFieldValue("ctlCurrency"));n.addParameter("GICurrencyCode",this.getFieldDropDownExtraText("ctlCurrency"));n.addParameter("GIDate",this.getFieldValue("ctlDateReceived"));n.addDataOK(Function.createDelegate(this,this.getCRMALineOK));n.addError(Function.createDelegate(this,this.getCRMALineError));n.addTimeout(Function.createDelegate(this,this.getCRMALineError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getCRMALineOK:function(n){var t=n._result;this.setFieldValue("ctlPartNo",$R_FN.setCleanTextValue(t.Part));this.setFieldValue("ctlDateCode",$R_FN.setCleanTextValue(t.DC));this.setFieldValue("ctlAuthorised",t.Quantity);this.setFieldValue("ctlQuantity",this._intQuantityOutstanding);this.setFieldValue("ctlPrice",t.Price);this.setFieldValue("ctlROHSStatus",$R_FN.writeROHS(t.ROHS));this.setFieldValue("ctlSupplierPart",$R_FN.setCleanTextValue(t.CustomerPart));this.setFieldValue("ctlManufacturer",$R_FN.setCleanTextValue(t.Manufacturer));this.setFieldValue("ctlProduct",$R_FN.setCleanTextValue(t.Product));this.setFieldValue("ctlPackage",$R_FN.setCleanTextValue(t.Package));this._intManufacturerNo=t.ManufacturerNo;this._intProductNo=t.ProductNo;this._intPackageNo=t.PackageNo;this._intROHS=t.ROHS;this._dblPrice=t.PriceRaw;this._intInvoiceLineID=t.InvoiceLineNo;this._partNo=$R_FN.setCleanTextValue(t.Part);this.showCRMALineDataFieldsLoading(!1)},getCRMALineError:function(n){this.showCRMALineDataFieldsLoading(!1);this._strErrorMessage=n._errorMessage;this.onSaveError()},showCRMALineDataFieldsLoading:function(n){this.showFieldLoading("ctlPartNo",n);this.showFieldLoading("ctlDateCode",n);this.showFieldLoading("ctlAuthorised",n);this.showFieldLoading("ctlQuantity",n);this.showFieldLoading("ctlDateCode",n);this.showFieldLoading("ctlPrice",n);this.showFieldLoading("ctlROHSStatus",n);this.showFieldLoading("ctlProduct",n);this.showFieldLoading("ctlPackage",n)},getGoodsIn:function(){this.showGoodsInFieldsLoading(!0);$R_FN.showElement(this._pnlLines,!1);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/GIMainInfo");n.set_DataObject("GIMainInfo");n.set_DataAction("GetData");n.addParameter("ID",this._intGoodsInID);n.addDataOK(Function.createDelegate(this,this.getGoodsInOK));n.addError(Function.createDelegate(this,this.getGoodsInError));n.addTimeout(Function.createDelegate(this,this.getGoodsInError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getGoodsInOK:function(n){var t=n._result;this.setFieldValue("ctlCustomerDetail",t.SupplierName);this.setFieldValue("ctlGoodsInNumber",t.GoodsInNumber);this.setFieldValue("ctlAirWayBillDetail",t.AirWayBill);this.setFieldValue("ctlReferenceDetail",t.Reference);this.setFieldValue("ctlReceivingNotesDetail",t.ReceivingNotes);this.showGoodsInFieldsLoading(!1)},getGoodsInError:function(n){this.showGoodsInFieldsLoading(!1);this._strErrorMessage=n._errorMessage;this.onSaveError()},showGoodsInFieldsLoading:function(n){this.showFieldLoading("ctlCustomerDetail",n);this.showFieldLoading("ctlGoodsInNumber",n);this.showFieldLoading("ctlAirWayBillDetail",n);this.showFieldLoading("ctlReferenceDetail",n);this.showFieldLoading("ctlReceivingNotesDetail",n)},saveClicked:function(){this.resetFormFields();this.validateForm()&&(this._strHeaderSelected=="NEW"?this.saveHeaderThenLine():this.saveLine())},saveLine:function(){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/CRMAReceivingLines");n.set_DataObject("CRMAReceivingLines");n.set_DataAction("ReceiveLine");n.addParameter("id",this._intGoodsInID);n.addParameter("CRMALineNo",this._intCRMALineID);n.addParameter("InvoiceLineAllocationID",this._intInvoiceLineAllocationID);n.addParameter("ManufacturerNo",this._intManufacturerNo);n.addParameter("ROHS",this._intROHS);n.addParameter("ProductNo",this._intProductNo);n.addParameter("PackageNo",this._intPackageNo);n.addParameter("Part",this.getFieldValue("ctlPartNo"));n.addParameter("DC",this.getFieldValue("ctlDateCode"));n.addParameter("Quantity",this.getFieldValue("ctlQuantity"));n.addParameter("Price",this._dblPrice);n.addParameter("ShipInCost",this.getFieldValue("ctlShipInCost"));n.addParameter("QCNotes",this.getFieldValue("ctlQualityControlNotes"));n.addParameter("Location",this.getFieldValue("ctlLocation"));n.addParameter("SupplierPart",this.getFieldValue("ctlSupplierPart"));n.addParameter("CurrencyNo",this._intCurrencyNo);n.addParameter("CountingMethodNo",this.getFieldValue("ctlCountingMethod"));n.addParameter("SerialNosRecorded",this.getFieldValue("ctlSerialNosRecorded"));n.addParameter("PartMarkings",this.getFieldValue("ctlPartMarkings"));n.addDataOK(Function.createDelegate(this,this.saveLineOK));n.addError(Function.createDelegate(this,this.saveLineError));n.addTimeout(Function.createDelegate(this,this.saveLineError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},saveLineError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},saveLineOK:function(n){n._result.Result==!0?(this._intGoodsInLineID=n._result.NewID,this.showSaving(!1),this.showInnerContent(!0),this.nextStep()):(this._strErrorMessage=n._errorMessage,this.onSaveError())},validateForm:function(){this.onValidate();var n=!0;return this.checkFieldEntered("ctlQuantity")||(n=!1),this.checkNumericFieldLessThanOrEqualTo("ctlQuantity",this.getFieldValue("ctlAuthorised"))||(n=!1),this.checkNumericFieldGreaterThan("ctlQuantity",0)||(n=!1),this.checkFieldEntered("ctlLocation")||(n=!1),this.checkFieldEntered("ctlDateCode")||(n=!1),this.checkFieldEntered("ctlShipInCost")||(n=!1),n||this.showError(!0),this._reqSerialNo&&(this._countSerialNo<1?(this.showError(!0,"Please attach serial no."),n=!1):this._countSerialNo>0&&this._countSerialNo>this.getFieldValue("ctlQuantity")&&(this.showError(!0,"No. of Serial No(s) does not match with the Qty."),n=!1)),n},showMailButtons:function(){var n=this.getFieldValue("ctlSendMail");this.showField("ctlSendMailMessage",n);$R_IBTN.showButton(this._ibtnSend,n);$R_IBTN.showButton(this._ibtnSend_Footer,n);$R_IBTN.showButton(this._ibtnContinue,!n);$R_IBTN.showButton(this._ibtnContinue_Footer,!n)},chooseIfSendMail:function(){this.showMailButtons()},getMessageText:function(){Rebound.GlobalTrader.Site.WebServices.GetMailMessage_NewGoodsIn(this._intGoodsInID,Function.createDelegate(this,this.getMessageTextComplete))},getMessageTextComplete:function(n){this._ctlMail.setValue_Body(n);this._ctlMail.setValue_Subject($R_RES.NewGoodsInAdded)},validateMailForm:function(){var n=this._ctlMail.validateFields();return n||this.showError(!0),n},sendMail:function(){this.validateMailForm()&&(Rebound.GlobalTrader.Site.WebServices.SendMessage($R_FN.arrayToSingleString(this._ctlMail._aryRecipientLoginIDs),$R_FN.arrayToSingleString(this._ctlMail._aryRecipientGroupIDs),this._ctlMail.getValue_Subject(),this._ctlMail.getValue_Body(),this._intCustomerID,Function.createDelegate(this,this.sendMailComplete)),$R_IBTN.showButton(this._ibtnSave,!1),$R_IBTN.showButton(this._ibtnSave_Footer,!1),$R_IBTN.showButton(this._ibtnSend,!1),$R_IBTN.showButton(this._ibtnSend_Footer,!1))},sendMailComplete:function(){this.finishedForm()},finishedForm:function(){this._ctlMultiStep.showExplainLabel(!1);this._ctlMultiStep.showSteps(!1);$R_IBTN.showButton(this._ibtnSave,!1);$R_IBTN.showButton(this._ibtnSave_Footer,!1);$R_IBTN.showButton(this._ibtnSend,!1);$R_IBTN.showButton(this._ibtnSend_Footer,!1);this.showSavedOK(!0);this.onSaveComplete()},OpenSerialForm:function(){this.setFieldValue("ctlQtyToShpped",this._intQuantityOutstanding);this.setFieldValue("ctlPart",this._partNo);this._ctlGiSerialNumber._intInvoiceLineNo=this._intInvoiceLineID;this._ctlGiSerialNumber.refereshCRMAGroup();this.showHideSerialNo(!0);this.getAttachedSerial();this._ctlGiSerialNumber.getData();this._ctlMultiSelectionCount.show(this._ctlGiSerialNumber._tblResults._intTotalRecords>0)},getAttachedSerial:function(){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/CRMAReceivingLines");n.set_DataObject("CRMAReceivingLines");n.set_DataAction("GetAttachedSerial");n.addParameter("CustomerRMANo",this._intCRMAID);n.addParameter("CustomerRMALineNo",this._intCRMALineID);n.addDataOK(Function.createDelegate(this,this.getAttachedSerialOK));n.addError(Function.createDelegate(this,this.getAttachedSerialError));n.addTimeout(Function.createDelegate(this,this.getAttachedSerialError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getAttachedSerialOK:function(n){var i;for(res=n._result,this._countSerialNo=res.Count,this._tblSerialNoFinal.show(res.Results.length>0),this._tblSerialNoFinal.clearTable(),i=0;i<res.Results.length;i++){var t=res.Results[i],r=[t.Group,t.SerialNo,String.format("<a href=\"javascript:void(0);\" Style='color:#006600' class='Delete'  onclick=\"$find('{0}').removeItem({1});\" class=\"quickSearchReselect\">Delete<\/a>",this._element.id,t.ID)],u={SerialNoId:t.ID,SubGroup:t.Group,SerialNo:t.SerialNo};this._tblSerialNoFinal.addRow(r,t.ID,!1,u);r=null;t=null;u=null}this._countSerialRecords=this._countSerialRecords+this._countSerialNo;this._tblSerialNoFinal.resizeColumns()},getAttachedSerialError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},searchClicked:function(){$R_FN.showElement(this._lblComplete,!1);this._ctlGiSerialNumber.getData()},ctlGiSerialNumber_PotentialStatusChange:function(){this._ctlMultiSelectionCount.clearAll();this._ctlMultiSelectionCount.show(this._ctlGiSerialNumber._objResult.Count>0);this.showField("ctlAddUpdate",this._ctlGiSerialNumber._objResult.Count>0)},serialSaveClicked:function(){$R_FN.showElement(this._lblComplete,!1);this.attachSerialNo()},attachSerialNo:function(){if(this.validateAddSerial()){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/CRMAReceivingLines");n.set_DataObject("CRMAReceivingLines");n.set_DataAction("AttachSerialNo");n.addParameter("strSerialNo",this._ctlGiSerialNumber._tblResults._aryCurrentValues);n.addParameter("CustomerRMANo",this._intCRMAID);n.addParameter("CustomerRMALineNo",this._intCRMALineID);n.addDataOK(Function.createDelegate(this,this.attachSerialComplete));n.addError(Function.createDelegate(this,this.attachSerialError));n.addTimeout(Function.createDelegate(this,this.attachSerialError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null}},attachSerialError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},attachSerialComplete:function(n){n._result.Result==!0&&(this._ctlGiSerialNumber.getData(),this.getAttachedSerial(),$R_FN.showElement(this._lblComplete,!1));this._ctlMultiSelectionCount.show(this._ctlGiSerialNumber._tblResults.countRows()>0);this.showField("ctlAddUpdate",this._ctlGiSerialNumber._tblResults.countRows()>0)},allClicked:function(){if(this._ctlGroup=this._ctlGiSerialNumber.getGroupValue(),this._ctlGroup==undefined){this._strErrorMessage="Please select BOX";this.showError(!0,this._strErrorMessage);return}this._strErrorMessage="";this.showError(!1,this._strErrorMessage);this.AttachSerialByCRMA();this._ctlGiSerialNumber.refereshCRMAGroup()},AttachSerialByCRMA:function(){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/CRMAReceivingLines");n.set_DataObject("CRMAReceivingLines");n.set_DataAction("AttachSerialByCRMA");n.addParameter("SubGroup",this._ctlGroup);n.addParameter("InvoiceLineNo",this._intInvoiceLineID);n.addParameter("CustomerRMANo",this._intCRMAID);n.addParameter("CustomerRMALineNo",this._intCRMALineID);n.addDataOK(Function.createDelegate(this,this.AttachSerialByCRMAOK));n.addError(Function.createDelegate(this,this.AttachSerialByCRMAError));n.addTimeout(Function.createDelegate(this,this.AttachSerialByCRMAError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},AttachSerialByCRMAOK:function(n){if(this._strErrorMessage="",$R_FN.showElement(this._lblComplete,!1),n._result.Result==!0){this.getAttachedSerial();this._ctlGiSerialNumber.getData();return}if(n._result.Result==!1&&n._result.ValidateMessage!=null&&n._result.NewID==-1){this._strErrorMessage=n._result.ValidateMessage;this.showError(!0,this._strErrorMessage);return}if(n._result.Result==!1&&n._result.ValidateMessage!=null&&n._result.NewID==0){$R_FN.showElement(this._lblComplete,!0);return}this._strErrorMessage=n._errorMessage;this.onSaveError()},AttachSerialByCRMAError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},refreshClicked:function(){this._tblSerialNoFinal.clearTable();this._tblSerialNoFinal.show(!1);this.showField("ctlAddUpdate",!1);this.showHideSerialNo(!1);this._strErrorMessage="";this.showError(!1,this._strErrorMessage);this.getAttachedSerial()},showHideSerialNo:function(n){$R_FN.showElement(this._trSerialNo,n);$R_FN.showElement(document.getElementById("ctl00_cphMain_ctlReceivingLines_ctlDB_ctl14_frmReceive_ctlDB_TableCell4"),n);$R_FN.showElement(document.getElementById("ctl00_cphMain_ctlReceivingLines_ctlDB_ctl14_frmReceive_ctlDB_lblSerialCount1"),n);this.showField("ctlQtyToShpped",n);this.showField("ctlPart",n)},validateAddSerial:function(){var n=!0;return this._ctlGiSerialNumber._tblResults._aryCurrentValues.length<=0&&(n=!1),n||this.showError(!0,"Please select the any records"),this._countSerialNo>parseInt(this.getFieldValue("ctlQtyToShpped"))&&(n=!1,this.showError(!0,"Cannot insert Serial Numbers beyond Quantity limit")),n&&this.showError(!1),n},removeItem:function(n){var i=confirm("Are you sure you want to delete"),t;i==!0?(t=new Rebound.GlobalTrader.Site.Data,t.set_PathToData("controls/Nuggets/CRMAReceivingLines"),t.set_DataObject("CRMAReceivingLines"),t.set_DataAction("DeleteAttachedSerial"),t.addParameter("SerialId",n),t.addParameter("CustomerRMANo",this._intCRMAID),t.addParameter("CustomerRMALineNo",this._intCRMALineID),t.addDataOK(Function.createDelegate(this,this.deleteComplete)),t.addError(Function.createDelegate(this,this.deleteError)),t.addTimeout(Function.createDelegate(this,this.deleteError)),$R_DQ.addToQueue(t),$R_DQ.processQueue(),t=null):(this._strErrorMessage=args._errorMessage,this.onSaveError())},deleteError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},deleteComplete:function(n){if(n._result.Result==!0){this.getAttachedSerial();this._ctlGiSerialNumber.getData();this._ctlGiSerialNumber.refereshCRMAGroup();return}this._strErrorMessage=n._errorMessage;this.onSaveError()},refreshSerialForm:function(){this.setFieldValue("ctlQtyToShpped",this.getFieldValue("ctlQuantity"));this.setFieldValue("ctlPart",this._partNo);this._ctlGiSerialNumber._intInvoiceLineNo=this._intInvoiceLineID;this._ctlGiSerialNumber.refereshCRMAGroup();this.showHideSerialNo(!0);this.getAttachedSerial();this._ctlGiSerialNumber.getData();this._ctlMultiSelectionCount.show(this._ctlGiSerialNumber._tblResults._intTotalRecords>0)}};Rebound.GlobalTrader.Site.Controls.Forms.CRMAReceivingLines_Receive.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CRMAReceivingLines_Receive",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);