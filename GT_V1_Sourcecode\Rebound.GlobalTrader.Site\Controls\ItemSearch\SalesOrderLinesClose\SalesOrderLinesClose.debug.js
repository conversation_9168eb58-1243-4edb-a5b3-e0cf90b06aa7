///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//----------------------------------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//----------------------------------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.ItemSearch");

Rebound.GlobalTrader.Site.Controls.ItemSearch.SalesOrderLinesClose = function(element) {
	Rebound.GlobalTrader.Site.Controls.ItemSearch.SalesOrderLinesClose.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.ItemSearch.SalesOrderLinesClose.prototype = {

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.ItemSearch.SalesOrderLinesClose.callBaseMethod(this, "initialize");
		this.addSetupData(Function.createDelegate(this, this.doSetupData));
		this.addGetDataComplete(Function.createDelegate(this, this.doGetDataComplete));
		$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmAdd_ctlDB_ctlSourceFromInvoiceSoc_ctlDB_ctlIncludeClosed").hide();
	},

	dispose: function() { 
		if (this.isDisposed) return;
		Rebound.GlobalTrader.Site.Controls.ItemSearch.SalesOrderLinesClose.callBaseMethod(this, "dispose");
	},
	
	doSetupData: function() {
		this._objData.set_PathToData("controls/ItemSearch/SalesOrderLinesClose");
		this._objData.set_DataObject("SalesOrderLinesClose");
		this._objData.set_DataAction("GetData");
		this._objData.addParameter("Part", this.getFieldValue("ctlPartNo"));
		this._objData.addParameter("CMName", this.getFieldValue("ctlCompany"));
		this._objData.addParameter("IncludeClosed", this.getFieldValue("ctlIncludeClosed"));
		this._objData.addParameter("CustomerPO", this.getFieldValue("ctlCustomerPO"));
		this._objData.addParameter("SalesOrderNoLo", this.getFieldValue_Min("ctlSalesOrderNo"));
		this._objData.addParameter("SalesOrderNoHi", this.getFieldValue_Max("ctlSalesOrderNo"));
		this._objData.addParameter("DateOrderedFrom", this.getFieldValue("ctlDateOrderedFrom"));
		this._objData.addParameter("DateOrderedTo", this.getFieldValue("ctlDateOrderedTo"));
		this._objData.addParameter("DatePromisedFrom", this.getFieldValue("ctlDatePromisedFrom"));
		this._objData.addParameter("DatePromisedTo", this.getFieldValue("ctlDatePromisedTo"));
		this._objData.addParameter("Salesman", this.getFieldValue("ctlSalesman"));
		this._objData.addParameter("OnlyFromIPO", this.getFieldValue("ctlOnlyFromIPO"));
	},
	
	doGetDataComplete: function() {
		for (var i = 0, l = this._objResult.Results.length; i < l; i++) {
			var row = this._objResult.Results[i];
			var aryData = [
				row.No,
				$R_FN.setCleanTextValue(row.CMName),
				$R_FN.writePartNo(row.Part, row.ROHS),
				$R_FN.setCleanTextValue(row.Date),
				row.Price,
				row.Quantity,
				$R_FN.setCleanTextValue(row.Salesman),
				$R_FN.setCleanTextValue(row.CustomerPO),
				row.Cost,
				
			];
			this._tblResults.addRow(aryData, row.ID, false);
			aryData = null; row = null;
		}
	}
};

Rebound.GlobalTrader.Site.Controls.ItemSearch.SalesOrderLinesClose.registerClass("Rebound.GlobalTrader.Site.Controls.ItemSearch.SalesOrderLinesClose", Rebound.GlobalTrader.Site.Controls.ItemSearch.Base, Sys.IDisposable);
