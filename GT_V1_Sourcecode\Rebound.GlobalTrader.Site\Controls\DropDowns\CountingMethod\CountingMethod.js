Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.CountingMethod=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.CountingMethod.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DropDowns.CountingMethod.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.DropDowns.CountingMethod.callBaseMethod(this,"initialize");this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK))},dispose:function(){this.isDisposed||Rebound.GlobalTrader.Site.Controls.DropDowns.CountingMethod.callBaseMethod(this,"dispose")},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/CountingMethod");this._objData.set_DataObject("CountingMethod");this._objData.set_DataAction("GetData")},dataCallOK:function(){var n=this._objData._result,t;if(n!=null&&n.CountingMethods)for(t=0;t<n.CountingMethods.length;t++)this.addOption(n.CountingMethods[t].Name,n.CountingMethods[t].ID)}};Rebound.GlobalTrader.Site.Controls.DropDowns.CountingMethod.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.CountingMethod",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);