///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//
// RP 23.10.2009:
// - new control
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists");

Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Services = function(element) { 
	Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Services.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Services.prototype = {

	initialize: function() {
		this.addGetDataOKEvent(Function.createDelegate(this, this.getDataOK));
		this._strPathToData = "controls/DataListNuggets/Services";
		this._strDataObject = "Services";
		Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Services.callBaseMethod(this, "initialize");
	},
	
	dispose: function() { 
		if (this.isDisposed) return;
		Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Services.callBaseMethod(this, "dispose");
	},

	getDataOK: function() {
		for (var i = 0, l = this._objResult.Results.length; i < l; i++) {
			var row = this._objResult.Results[i];
			var strData = String.format("<a href=\"{0}\">{1}</a>", $RGT_gotoURL_Service(row.ID), $R_FN.setCleanTextValue(row.Name));
			this._tbl.addRow([ strData ], row.ID, false);
			strData = null; row = null;
		}
	}

};
Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Services.registerClass("Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Services", Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Base, Sys.IDisposable);
