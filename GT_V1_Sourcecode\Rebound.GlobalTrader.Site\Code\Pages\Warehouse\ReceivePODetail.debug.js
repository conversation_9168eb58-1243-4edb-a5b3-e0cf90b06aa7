///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 21.01.2010:
// - pass PONumber to Lines nugget (for Receive form)
//
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Warehouse");

Rebound.GlobalTrader.Site.Pages.Warehouse.ReceivePODetail = function(el) { 
	Rebound.GlobalTrader.Site.Pages.Warehouse.ReceivePODetail.initializeBase(this, [el]);
};

Rebound.GlobalTrader.Site.Pages.Warehouse.ReceivePODetail.prototype = {

	get_intPOID: function() { return this._intPOID; }, 	set_intPOID: function(v) { if (this._intPOID !== v)  this._intPOID = v; }, 
	get_ctlLines: function() { return this._ctlLines; }, 	set_ctlLines: function(v) { if (this._ctlLines !== v)  this._ctlLines = v; }, 
	get_ctlMainInfo: function() { return this._ctlMainInfo; }, 	set_ctlMainInfo: function(v) { if (this._ctlMainInfo !== v)  this._ctlMainInfo = v; }, 
	get_pnlStatus: function() { return this._pnlStatus; }, 	set_pnlStatus: function(v) { if (this._pnlStatus !== v)  this._pnlStatus = v; }, 
	get_lblStatus: function() { return this._lblStatus; }, 	set_lblStatus: function(v) { if (this._lblStatus !== v)  this._lblStatus = v; }, 
	get_IsGlobalLogin: function () { return this._IsGlobalLogin; }, set_IsGlobalLogin: function (v) { if (this._IsGlobalLogin !== v) this._IsGlobalLogin = v; },
	initialize: function() {
		Rebound.GlobalTrader.Site.Pages.Warehouse.ReceivePODetail.callBaseMethod(this, "initialize");
	},
	
	goInit: function() {
		if (this._ctlMainInfo) this._ctlMainInfo.addGetDataComplete(Function.createDelegate(this, this.ctlMainInfo_GetDataComplete));
		if (this._ctlLines) this._ctlLines.addSaveEditComplete(Function.createDelegate(this, this.ctlLines_SaveEditComplete));
		Rebound.GlobalTrader.Site.Pages.Warehouse.ReceivePODetail.callBaseMethod(this, "goInit");
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this._ctlLines) this._ctlLines.dispose();
		if (this._ctlMainInfo) this._ctlMainInfo.dispose();
		this._intPOID = null;
		this._ctlLines = null;
		this._ctlMainInfo = null;
		this._pnlStatus = null;
		this._lblStatus = null;
		this._IsGlobalLogin = null;
		Rebound.GlobalTrader.Site.Pages.Warehouse.ReceivePODetail.callBaseMethod(this, "dispose");
	},
	
	ctlMainInfo_GetDataComplete: function() {
		this._ctlLines._strSupplierName = this._ctlMainInfo.getFieldValue("hidSupplierName");
		this._ctlLines._intSupplierRating = this._ctlMainInfo.getFieldValue("hidCompanyPORating");
		$R_FN.setInnerHTML(this._lblStatus, this._ctlMainInfo.getFieldValue("hidStatus"));
		var intStatus = Number.parseInvariant(this._ctlMainInfo.getFieldValue("hidStatusNo").toString());
		this._ctlLines._blnDisableAllButtons = (intStatus == $R_ENUM$PurchaseOrderStatus.Received || intStatus == $R_ENUM$PurchaseOrderStatus.Complete);
		this._ctlLines._intPONumber = this._ctlMainInfo._intPONumber;
		this._ctlLines._blnIPOApproved = Boolean.parse(this._ctlMainInfo.getFieldValue("hidIsIPOApprove"));
		this._ctlLines._IsRelatedToIPO = Boolean.parse(this._ctlMainInfo.getFieldValue("hidIsIPO"));
		this._ctlLines.enableEditButtons(true); //alert(this._IsGlobalLogin);
		this._ctlLines._intGlobalClientNo = this._IsGlobalLogin == true ? this._ctlMainInfo.getFieldValue("hidGlobalClientNo") : null;
		this._ctlMainInfo._IsGlobalLogin = this._IsGlobalLogin;
	},
	
	ctlLines_SaveEditComplete: function() {
		this._ctlMainInfo.getData();
	}

};
Rebound.GlobalTrader.Site.Pages.Warehouse.ReceivePODetail.registerClass("Rebound.GlobalTrader.Site.Pages.Warehouse.ReceivePODetail", Rebound.GlobalTrader.Site.Pages.Content, Sys.IDisposable);
