using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
//using Microsoft.ApplicationInsights;

namespace Rebound.GlobalTrader.Site.Controls.DropDowns.Data
{
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class GILineQueryProgressStatus : Rebound.GlobalTrader.Site.Data.DropDowns.Base
    {

        public override void ProcessRequest(HttpContext context)
        {
            SetDropDownType("GILineQueryProgressStatus");
            base.ProcessRequest(context);
        }

        protected override void GetData()
        {
            try
            {
                JsonObject jsn = new JsonObject();
                JsonObject jsnList = new JsonObject(true);
                List<BLL.GoodsInLine> lst = new List<BLL.GoodsInLine>();
                lst.Add(new BLL.GoodsInLine { ID = 1, Name = "Show All" });
                lst.Add(new BLL.GoodsInLine { ID = 2, Name = "Show Open" });
                lst.Add(new BLL.GoodsInLine { ID = 3, Name = "Show Closed" });

                for (int i = 0; i < lst.Count; i++)
                {
                    JsonObject jsnItem = new JsonObject();
                    jsnItem.AddVariable("ID", lst[i].ID);
                    jsnItem.AddVariable("Name", lst[i].Name);
                    jsnList.AddVariable(jsnItem);
                    jsnItem.Dispose(); jsnItem = null;
                }
                lst.Clear(); lst = null;
                jsn.AddVariable("Types", jsnList);
                jsnList.Dispose(); jsnList = null;
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch (Exception ex)
            {
                //var ai = new TelemetryClient(); // or re-use an existing instance
                //ai.TrackTrace("GILineQueryStatus : GetData");
                //ai.TrackException(ex);

                new Errorlog().LogMessage("Error Occured at GILineQueryStatus handler " + ex.InnerException.Message);
            }

        }
    }
}
