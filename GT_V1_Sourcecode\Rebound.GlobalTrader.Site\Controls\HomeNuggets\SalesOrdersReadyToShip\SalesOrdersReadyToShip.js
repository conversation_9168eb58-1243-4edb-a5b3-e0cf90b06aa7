Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.HomeNuggets");Rebound.GlobalTrader.Site.Controls.HomeNuggets.SalesOrdersReadyToShip=function(n){Rebound.GlobalTrader.Site.Controls.HomeNuggets.SalesOrdersReadyToShip.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.HomeNuggets.SalesOrdersReadyToShip.prototype={get_pnlReady:function(){return this._pnlReady},set_pnlReady:function(n){this._pnlReady!==n&&(this._pnlReady=n)},get_tblReady:function(){return this._tblReady},set_tblReady:function(n){this._tblReady!==n&&(this._tblReady=n)},get_pnlMore:function(){return this._pnlMore},set_pnlMore:function(n){this._pnlMore!==n&&(this._pnlMore=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.HomeNuggets.SalesOrdersReadyToShip.callBaseMethod(this,"initialize");this.addRefreshEvent(Function.createDelegate(this,this.getData))},dispose:function(){this.isDisposed||(this._tblReady&&this._tblReady.dispose(),this._pnlReady=null,this._tblReady=null,this._pnlMore=null,Rebound.GlobalTrader.Site.Controls.HomeNuggets.SalesOrdersReadyToShip.callBaseMethod(this,"dispose"))},setupLoadingState:function(){$R_FN.showElement(this._pnlMore,!1);$R_FN.showElement(this._pnlReady,!1);Rebound.GlobalTrader.Site.Controls.HomeNuggets.SalesOrdersReadyToShip.callBaseMethod(this,"setupLoadingState")},showNoData:function(n){this.showContent(!0);$R_FN.showElement(this._pnlNoData,n)},getData:function(){this.setupLoadingState();var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/HomeNuggets/SalesOrdersReadyToShip");n.set_DataObject("SalesOrdersReadyToShip");n.set_DataAction("GetData");n.addParameter("rowcount",this._intRowCount);n.addDataOK(Function.createDelegate(this,this.getDataComplete));n.addError(Function.createDelegate(this,this.homeNuggetDataError));n.addTimeout(Function.createDelegate(this,this.homeNuggetDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getDataComplete:function(n){var i,r,t,u;for(this.showNoData(n._result.Count==0),$R_FN.showElement(this._pnlMore,!0),i=n._result,this._tblReady.clearTable(),r=0;r<i.Ready.length;r++)t=i.Ready[r],u=[$RGT_nubButton_ShipSalesOrder(t.ID,t.No),$RGT_nubButton_Company(t.CMNo,t.CM),t.Promised],this._tblReady.addRow(u,null);$R_FN.showElement(this._pnlReady,i.Ready.length>0);this.hideLoading()}};Rebound.GlobalTrader.Site.Controls.HomeNuggets.SalesOrdersReadyToShip.registerClass("Rebound.GlobalTrader.Site.Controls.HomeNuggets.SalesOrdersReadyToShip",Rebound.GlobalTrader.Site.Controls.HomeNuggets.Base,Sys.IDisposable);