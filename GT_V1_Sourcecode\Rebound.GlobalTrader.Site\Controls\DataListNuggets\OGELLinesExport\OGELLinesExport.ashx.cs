/* Marker     changed by      date         Remarks  
   [001]      V<PERSON><PERSON> kumar     22/11/2011  ESMS Ref:21 - Add Country search option in SO */
/* [0002]     <PERSON><PERSON><PERSON><PERSON>  14/07/2014    ESMS Ref:172 - Issue in Part Searching in whole GT 
   [003]      <PERSON><PERSON><PERSON>     17-Aug-2018  Provision to add Global Security in Sales Order
 */

using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using System.Configuration;
using Rebound.GlobalTrader.Site.Enumerations;
using Rebound.GlobalTrader.DAL;
using Rebound.GlobalTrader.Site.Code.Common;

namespace Rebound.GlobalTrader.Site.Controls.DataListNuggets.Data {

	public class OGELLinesExport : Base {

		public override void ProcessRequest(HttpContext context)
		{
			//if (base.init(context))
			//{
			//    switch (Action)
			//    {
			//        case "ExportToCSV": ExportToCSV(); break;
			//        default: WriteErrorActionNotFound(); break;
			//    }
			//}
			base.ProcessRequest(context);
			if (Action == "ExportToCSV") ExportToCSV();
		}
		protected override void GetData() {

			//check view level
			ViewLevelList enmViewLevel = (ViewLevelList)GetFormValue_Int("ViewLevel");

			//get data
			List<OGELLines> lst = SalesOrderLine.OGEL_PackingExport(
				SessionManager.ClientID
				, (enmViewLevel == ViewLevelList.Team) ? (int?)SessionManager.LoginTeamID : null
				, (enmViewLevel == ViewLevelList.Division) ? (int?)SessionManager.LoginDivisionID : null
				, (enmViewLevel == ViewLevelList.My) ? (int?)LoginID : null
				, GetFormValue_NullableInt("SortIndex")
				, GetFormValue_NullableInt("SortDir", SortColumnDirection.ASC)
				, GetFormValue_NullableInt("PageIndex", 0)
				, GetFormValue_NullableInt("PageSize", 10)
				  //[0002] start code
				  //, GetFormValue_StringForPartSearch("Part")
				  , GetFormValue_PartForLikeSearch("Part")
				 //[0002] end code
				 //, GetFormValue_StringForNameSearch("Contact")
				 , GetFormValue_StringForNameSearchDecode("Contact")
				//[001]Code Start
				, GetFormValue_NullableInt("Country")
				 //[001]Code End
				 //, GetFormValue_StringForNameSearch("CMName")
				 , GetFormValue_StringForNameSearchDecode("CMName")
				, GetFormValue_NullableInt("Salesman")
				, GetFormValue_NullableInt("InvoiceNoLo")
				, GetFormValue_NullableInt("InvoiceNoHi")
				, GetFormValue_Boolean("RecentOnly")
				, GetFormValue_Boolean("IncludeClosed")
				, GetFormValue_NullableInt("SoNoLo")
				, GetFormValue_NullableInt("SoNoHi")
				, GetFormValue_NullableDateTime("DateOrderedFrom")
				, GetFormValue_NullableDateTime("DateOrderedTo")
				, GetFormValue_NullableDateTime("DatePromisedFrom")
				, GetFormValue_NullableDateTime("DatePromisedTo")
				//, GetFormValue_Boolean("UnauthorisedOnly")
				, null
				, GetFormValue_NullableInt("IncludeOrderSent")
				//, GetFormValue_PartForLikeSearch("ContractNo")
				, GetFormValue_StringForLikeSearch("ContractNo")
				//[003] start
				, GetFormValue_Boolean("IsGlobalLogin")
				, GetFormValue_NullableInt("Client")
				//[003] end
				, GetFormValue_NullableInt("SOCheckedStatus")
				, GetFormValue_NullableInt("SalesOrderStatus")
);

            #region need to uncomment later


            JsonObject jsn = new JsonObject();
            JsonObject jsnRowsArray = new JsonObject(true);

            //check counts
            jsn.AddVariable("TotalRecords", (lst.Count > 0) ? lst[0].RowCnt : 0);

            //format json
            for (int i = 0; i < lst.Count; i++)
            {
                if (i < lst.Count)
                {
					JsonObject jsnRow = new JsonObject();
					jsnRow.AddVariable("SoID", lst[i].SalesOrderId);
					jsnRow.AddVariable("ID", lst[i].SalesOrderNumber);
                    jsnRow.AddVariable("No", lst[i].SalesOrderLineId);
                    jsnRow.AddVariable("AirWayBill", lst[i].AirWayBill);//part
                    //jsnRow.AddVariable("Price", Functions.FormatCurrency(lst[i].Price, lst[i].CurrencyCode)) //currencycode;
                    jsnRow.AddVariable("CommodityCode", lst[i].CommodityCode); // quantity
                    jsnRow.AddVariable("ECCNCode", lst[i].ECCNCode); // quantityShipped
                    jsnRow.AddVariable("PartNumber", lst[i].PartNumber); // quantityShipped
                    jsnRow.AddVariable("OGELNumber",  lst[i].OGELNumber); // quantityShipped
                    //jsnRow.AddVariable("OGEL_MilitaryUse", lst[i].OGEL_MilitaryUse); // quantityShipped
					jsnRow.AddVariable("OGEL_MilitaryUse", lst[i].OGEL_MilitaryUse == 1 ? "Yes" : "No"); // quantityShipped
					jsnRow.AddVariable("CountryName",  lst[i].CountryName); // quantityShipped

					jsnRow.AddVariable("CM", lst[i].CompanyName);
					jsnRow.AddVariable("CMNo", lst[i].CompanyNo);
					jsnRow.AddVariable("CustPONO", lst[i].CustomerPO);
					jsnRow.AddVariable("DatePromised", Functions.FormatDate(lst[i].DatePromised));
					jsnRow.AddVariable("Status", SalesOrderManager.FormatLineAllocationShippingStatus(SalesOrderLine.GetLineCannotShipReasons(lst[i].SalesOrderLineId1, lst[i].AllocationID, lst[i].CreditStatus)));
					jsnRowsArray.AddVariable(jsnRow);
                    jsnRow.Dispose();
                    jsnRow = null;
                }
            }
            jsn.AddVariable("Results", jsnRowsArray);
            OutputResult(jsn);
            jsnRowsArray.Dispose(); jsnRowsArray = null;
            jsn.Dispose(); jsn = null;
            lst = null;
            base.GetData();
			#endregion need to uncomment later
		}
		public void ExportToCSV()
		{
			try
			{

				
				ViewLevelList enmViewLevel = (ViewLevelList)GetFormValue_Int("ViewLevel");
				string filePath = string.Empty;
				string strFilename = String.Format("report_u{0}r{1}.xlsx", LoginID, 0);
				//get data
				DataTable dtResult = SalesOrderLine.OGEL_PackingExport_Dt(
					SessionManager.ClientID
					, (enmViewLevel == ViewLevelList.Team) ? (int?)SessionManager.LoginTeamID : null
					, (enmViewLevel == ViewLevelList.Division) ? (int?)SessionManager.LoginDivisionID : null
					, (enmViewLevel == ViewLevelList.My) ? (int?)LoginID : null
					, GetFormValue_NullableInt("SortIndex")
					, GetFormValue_NullableInt("SortDir", SortColumnDirection.ASC)
					, GetFormValue_NullableInt("PageIndex", 0)
					, GetFormValue_NullableInt("PageSize", 10)
					  //[0002] start code
					  //, GetFormValue_StringForPartSearch("Part")
					  , GetFormValue_PartForLikeSearch("Part")
					 //[0002] end code
					 //, GetFormValue_StringForNameSearch("Contact")
					 , GetFormValue_StringForNameSearchDecode("Contact")
					//[001]Code Start
					, GetFormValue_NullableInt("Country")
					 //[001]Code End
					 //, GetFormValue_StringForNameSearch("CMName")
					 , GetFormValue_StringForNameSearchDecode("CMName")
					, GetFormValue_NullableInt("Salesman")
					 ,GetFormValue_NullableInt("InvoiceNoLo")
				, GetFormValue_NullableInt("InvoiceNoHi")
				, GetFormValue_Boolean("RecentOnly")
				, GetFormValue_Boolean("IncludeClosed")
				, GetFormValue_NullableInt("SoNoLo")
				, GetFormValue_NullableInt("SoNoHi")
					, GetFormValue_NullableDateTime("DateOrderedFrom")
					, GetFormValue_NullableDateTime("DateOrderedTo")
					, GetFormValue_NullableDateTime("DatePromisedFrom")
					, GetFormValue_NullableDateTime("DatePromisedTo")
					//, GetFormValue_Boolean("UnauthorisedOnly")
					, null
					, GetFormValue_NullableInt("IncludeOrderSent")
					//, GetFormValue_PartForLikeSearch("ContractNo")
					, GetFormValue_StringForLikeSearch("ContractNo")
					//[003] start
					, GetFormValue_Boolean("IsGlobalLogin")
					, GetFormValue_NullableInt("Client")
					//[003] end
					, GetFormValue_NullableInt("SOCheckedStatus")
					, GetFormValue_NullableInt("SalesOrderStatus")
	);
				filePath = (new EPPlusExportUtility()).ExportDataTableToCSVOGEL(dtResult, strFilename);
				JsonObject jsn = new JsonObject();
				jsn.AddVariable("Filename", filePath);
				OutputResult(jsn);
			}
			catch (Exception e)
			{
				WriteError(e);
			}
			finally
			{
				
			}
		}

		protected override void AddFilterStates() {
            //Prevent filter state for Tab
			//AddExplicitFilterState("ViewLevel", GetFormValue_Int("ViewLevel"));
			AddFilterState("Part");
			AddFilterState("Contact");
			AddFilterState("CMName");
			AddFilterState("Salesman");
			AddFilterState("CustPO");
			AddFilterState("RecentOnly");
			AddFilterState("IncludeClosed");
			AddFilterState("SONo");
			AddFilterState("DateOrderedFrom");
			AddFilterState("DateOrderedTo");
			AddFilterState("DatePromisedFrom");
			AddFilterState("DatePromisedTo");
			AddFilterState("UnauthorisedOnly");
            AddFilterState("IncludeOrderSent");
            AddFilterState("SOCheckedStatus");
            AddFilterState("SalesOrderStatus");
            base.AddFilterStates();
		}
	}
}