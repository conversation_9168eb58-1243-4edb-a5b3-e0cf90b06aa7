using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Collections;

namespace Rebound.GlobalTrader.Site.Controls {
	[DefaultProperty("")]
	[ToolboxData("<{0}:TabStrip runat=server></{0}:TabStrip>")]
	public class TabStrip : WebControlWithScript, IScriptControl, INamingContainer {

		#region Locals

		private Panel _pnlTabStrip;
		private Panel _pnlTabs;
		private Panel _pnlContent;
		private Panel _pnlLoading;

		#endregion

		#region Properties

		/// <summary>
		/// Tabs container
		/// </summary>
		private ITemplate _tmpTabs = null;
		[PersistenceMode(PersistenceMode.InnerProperty)]
		[TemplateContainer(typeof(Container))]
		public ITemplate TabsTemplate {
			get { return _tmpTabs; }
			set { _tmpTabs = value; }
		}

		/// <summary>
		/// Tab content container
		/// </summary>
		private ITemplate _tmpTabContent = null;
		[PersistenceMode(PersistenceMode.InnerProperty)]
		[TemplateContainer(typeof(Container))]
		public ITemplate TabsContent {
			get { return _tmpTabContent; }
			set { _tmpTabContent = value; }
		}

		/// <summary>
		/// Tabs collection
		/// </summary>
		private List<Tab> _lstTabs = new List<Tab>();
		public List<Tab> Tabs {
			get { return _lstTabs; }
			set { _lstTabs = value; }
		}

		/// <summary>
		/// Tab control IDs
		/// </summary>
		private List<string> _lstTabControlClientIDs = new List<string>();
		public List<string> TabControlClientIDs {
			get { return _lstTabControlClientIDs; }
			set { _lstTabControlClientIDs = value; }
		}

		/// <summary>
		/// index of selected tab
		/// </summary>
		private int _intSelectedTabIndex;
		public int SelectedTabIndex {
			get { return _intSelectedTabIndex; }
			set { _intSelectedTabIndex = value; }
		}

		#endregion

		#region Overrides

		protected override void OnInit(EventArgs e) {
			((Pages.Base)Page).AddCSSFile("Tabs.css");
			base.OnInit(e);
		}

		/// <summary>
		/// OnLoad
		/// </summary>
		/// <param name="e"></param>
		protected override void OnLoad(EventArgs e) {
			EnsureChildControls();
			base.OnLoad(e);
		}

		/// <summary>
		/// create controls
		/// </summary>
		protected override void CreateChildControls() {
			_pnlTabStrip = ControlBuilders.CreatePanel("nuggetTabStrip");
			_pnlTabStrip.ID = "pnlTabStrip";
			_pnlTabs = ControlBuilders.CreatePanelInsideParent(_pnlTabStrip, "nuggetTabs");
			_pnlTabs.ID = "pnlTabs";
			Controls.Add(_pnlTabStrip);
			_pnlContent = ControlBuilders.CreatePanel("nuggetTabContent");
			_pnlContent.ID = "pnlContent";
			Controls.Add(_pnlContent);
			_pnlLoading = ControlBuilders.CreatePanel("contentLoading");
			_pnlLoading.ID = "pnlLoading";
			ControlBuilders.CreateLiteralInsideParent(_pnlLoading, Functions.GetGlobalResource("misc", "Loading"));
			Functions.SetCSSVisibility(_pnlLoading, false);
			Controls.Add(_pnlLoading);

			//tab content container
			if (_tmpTabContent != null) {
				Container cbc = new Container();
				_tmpTabContent.InstantiateIn(cbc);
				_pnlContent.Controls.Add(cbc);
				cbc.Dispose();
				cbc = null;
			}

			//tabs container
			if (_tmpTabs != null) {
				Container cbc = new Container();
				_tmpTabs.InstantiateIn(cbc);
				int intTabIndex = 0;
				foreach (Control ctl in cbc.Controls) {
					if (ctl is Tab) {
						Tabs.Add((Tab)ctl);
						((Tab)ctl).ParentTabStrip = this;
						((Tab)ctl).TabIndex = intTabIndex;
                        //Espire: 25 June 20: Issue on framework 4.6, Id is not recognize in framework 4.6
                        //string id = this.ClientID + "_ctl02_" + ctl.ClientID;
                        TabControlClientIDs.Add(this.ClientID + "_ctl02_" + ctl.ClientID);
                        //TabControlClientIDs.Add(ctl.ClientID);
                        if (((Tab)ctl).IsSelected) SelectedTabIndex = Tabs.Count - 1;
						((Tab)ctl).RelatedContentPanel = (Panel)Functions.FindControlRecursive(_pnlContent, ((Tab)ctl).RelatedContentPanelID);
						if (((Tab)ctl).RelatedContentPanel != null) Functions.SetCSSVisibility(((Tab)ctl).RelatedContentPanel, (((Tab)ctl).IsSelected));
						intTabIndex += 1;
					}
				}
				_pnlTabs.Controls.Add(cbc);
				cbc.Dispose();
				cbc = null;
			}

			base.CreateChildControls();
		}

		#endregion

		/// <summary>
		/// Allows EnsureChildControls to be called from outside
		/// </summary>
		public void CreateControls() {
			EnsureChildControls();
		}

		public void ChangeSelectedTab(int intNewTabSelected) {
			_intSelectedTabIndex = intNewTabSelected;
			EnsureChildControls();
			for (int i = 0; i < Tabs.Count; i++) {
				Tabs[i].IsSelected = (i == intNewTabSelected);
				Functions.SetCSSVisibility(Tabs[i].RelatedContentPanel, true);
				if (Tabs[i].RelatedContentPanel != null) Functions.SetCSSVisibility(Tabs[i].RelatedContentPanel, (i == intNewTabSelected));
			}
		}

		#region IScriptControl Members

		protected new virtual IEnumerable<ScriptReference> GetScriptReferences() {
			bool blnDebug = false;
#if (DEBUG)
			blnDebug = true;
#endif
			return new ScriptReference[] { 
				Functions.GetScriptReference(blnDebug, "Rebound.GlobalTrader.Site", "Controls.Basic.Tabs.TabStrip", true), 
				Functions.GetScriptReference(blnDebug, "Rebound.GlobalTrader.Site", "Controls.Basic.Tabs.Tab", true) 
			};
		}

		protected new virtual IEnumerable<ScriptDescriptor> GetScriptDescriptors() {
			ScriptControlDescriptor descriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.TabStrip", this.ClientID);
			descriptor.AddProperty("aryTabIDs", _lstTabControlClientIDs);
			descriptor.AddProperty("SelectedTabIndex", _intSelectedTabIndex);
			descriptor.AddElementProperty("pnlContent", _pnlContent.ClientID);
			descriptor.AddElementProperty("pnlLoading", _pnlLoading.ClientID);
			return new ScriptDescriptor[] { descriptor };
		}

		IEnumerable<ScriptReference> IScriptControl.GetScriptReferences() {
			return GetScriptReferences();
		}

		IEnumerable<ScriptDescriptor> IScriptControl.GetScriptDescriptors() {
			return GetScriptDescriptors();
		}

		#endregion
	}
}