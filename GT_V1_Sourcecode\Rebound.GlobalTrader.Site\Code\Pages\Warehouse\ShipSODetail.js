Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Warehouse");Rebound.GlobalTrader.Site.Pages.Warehouse.ShipSODetail=function(n){Rebound.GlobalTrader.Site.Pages.Warehouse.ShipSODetail.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Pages.Warehouse.ShipSODetail.prototype={get_ctlMainInfo:function(){return this._ctlMainInfo},set_ctlMainInfo:function(n){this._ctlMainInfo!==n&&(this._ctlMainInfo=n)},get_ctlLines:function(){return this._ctlLines},set_ctlLines:function(n){this._ctlLines!==n&&(this._ctlLines=n)},get_intSOID:function(){return this._intSOID},set_intSOID:function(n){this._intSOID!==n&&(this._intSOID=n)},get_lblStatus:function(){return this._lblStatus},set_lblStatus:function(n){this._lblStatus!==n&&(this._lblStatus=n)},get_pnlStatus:function(){return this._pnlStatus},set_pnlStatus:function(n){this._pnlStatus!==n&&(this._pnlStatus=n)},get_IsGlobalLogin:function(){return this._IsGlobalLogin},set_IsGlobalLogin:function(n){this._IsGlobalLogin!==n&&(this._IsGlobalLogin=n)},initialize:function(){Rebound.GlobalTrader.Site.Pages.Warehouse.ShipSODetail.callBaseMethod(this,"initialize")},goInit:function(){this._ctlMainInfo&&this._ctlMainInfo.addGetDataComplete(Function.createDelegate(this,this.ctlMainInfo_GetDataComplete));this._ctlLines&&this._ctlLines.addSaveShipComplete(Function.createDelegate(this,this.ctlLines_SaveShipComplete));Rebound.GlobalTrader.Site.Pages.Warehouse.ShipSODetail.callBaseMethod(this,"goInit")},dispose:function(){this.isDisposed||(this._ctlLines&&this._ctlLines.dispose(),this._ctlMainInfo&&this._ctlMainInfo.dispose(),this._ctlMainInfo=null,this._ctlLines=null,this._intSOID=null,this._lblStatus=null,this._pnlStatus=null,this._IsGlobalLogin=null,Rebound.GlobalTrader.Site.Pages.Warehouse.ShipSODetail.callBaseMethod(this,"dispose"))},ctlMainInfo_GetDataComplete:function(){var n=Number.parseInvariant(this._ctlMainInfo.getFieldValue("hidStatusNo").toString());this._ctlLines._blnDisableAllButtons=n==$R_ENUM$SalesOrderStatus.Complete;this._ctlLines.enableButtons(!0);$R_FN.setInnerHTML(this._lblStatus,this._ctlMainInfo.getFieldValue("hidStatus"));this._ctlLines._intGlobalClientNo=this._IsGlobalLogin==!0?this._ctlMainInfo.getFieldValue("hidGlobalClientNo"):null;this._ctlMainInfo._IsGlobalLogin=this._IsGlobalLogin},ctlLines_SaveShipComplete:function(){this._ctlMainInfo.getData()}};Rebound.GlobalTrader.Site.Pages.Warehouse.ShipSODetail.registerClass("Rebound.GlobalTrader.Site.Pages.Warehouse.ShipSODetail",Rebound.GlobalTrader.Site.Pages.Content,Sys.IDisposable);