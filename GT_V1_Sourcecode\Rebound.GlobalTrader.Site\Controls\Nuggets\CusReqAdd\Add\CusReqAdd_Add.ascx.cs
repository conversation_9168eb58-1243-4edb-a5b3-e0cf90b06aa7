//-----------------------------------------------------------------------------------------
// RP 17.12.2009:
// - allow passing a company name to initially search for (task 357)
//-----------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site;
using Rebound.GlobalTrader.Site.Controls;
using System.Collections.Generic;

namespace Rebound.GlobalTrader.Site.Controls.Forms
{
    public partial class CustomerRequirementAdd_Add : Base
    {

        #region Locals
        protected RadioButtonList _radFactorySealedSource;
        protected RadioButtonList _radObsolete;
        protected RadioButtonList _radLastTimeBuy;
        protected RadioButtonList _radRefirbsAcceptable;
        protected RadioButtonList _radTestingRequired;
        protected RadioButtonList _radAlternativesAccepted;
        protected RadioButtonList _radRepeatBusiness;
        protected List<string> _lstSources = new List<string>();
        protected FlexiDataTable _tblPartdetails;
        protected Panel _pnlPartDetail;
        protected HyperLink _ParttypeSearch;
        
        #endregion

        #region Overrides

        /// <summary>
        /// OnInit
        /// </summary>
        /// <param name="e"></param>
        protected override void OnInit(EventArgs e)
        {
            base.OnInit(e);
            TitleText = Functions.GetGlobalResource("FormTitles", "CustomerRequirementAdd_Add");
            AddScriptReference("Controls.Nuggets.CusReqAdd.Add.CusReqAdd_Add.js");
            WireUpControls();
            SetupTable();
        }

        /// <summary>
        /// OnPreRender
        /// </summary>
        /// <param name="e"></param>
        protected override void OnPreRender(EventArgs e)
        {
            SetupSelectSourceScreen();
            SetupScriptDescriptors();
            base.OnPreRender(e);
        }

        #endregion

        private void SetupTable()
        {

            //_tblPartdetails.AllowSelection = true;
            //_tblPartdetails.Columns.Add(new FlexiDataColumn("PartNo", "PartStatus", WidthManager.GetWidth(WidthManager.ColumnWidth.PartNo)));
            //_tblPartdetails.Columns.Add(new FlexiDataColumn("Manufacturer", "MSL", WidthManager.GetWidth(WidthManager.ColumnWidth.Country)));
            //_tblPartdetails.Columns.Add(new FlexiDataColumn("CountryOfOrigin", "HTCCode", WidthManager.GetWidth(WidthManager.ColumnWidth.Country)));
            //_tblPartdetails.Columns.Add(new FlexiDataColumn("Packaging", "PackagingCode", WidthManager.GetWidth(WidthManager.ColumnWidth.Country)));
            //_tblPartdetails.Columns.Add(new FlexiDataColumn("Description", Unit.Pixel(70)));
            
            
        }
        private void SetupSelectSourceScreen()
        {
            // AddRadioButton("Yes", "YES");
            // AddRadioButton("No", "NO");
            //_radFactorySealedSource.SelectedIndex = 1;
            //_radObsolete.SelectedIndex = 1;
            //_radLastTimeBuy.SelectedIndex = 1;
            //_radTestingRequired.SelectedIndex = 1;
            //_radRefirbsAcceptable.SelectedIndex = 1;
            //_radAlternativesAccepted.SelectedIndex = 1;
            //_radRepeatBusiness.SelectedIndex = 1;


        }
        private void AddRadioButton(string strResourceTitle, string strJavascriptType)
        {
            //_radFactorySealedSource.Items.Add(new ListItem(Functions.GetGlobalResource("Misc", strResourceTitle)));
            //_radObsolete.Items.Add(new ListItem(Functions.GetGlobalResource("Misc", strResourceTitle)));
            //_radLastTimeBuy.Items.Add(new ListItem(Functions.GetGlobalResource("Misc", strResourceTitle)));
            //_radRefirbsAcceptable.Items.Add(new ListItem(Functions.GetGlobalResource("Misc", strResourceTitle)));
            //_radTestingRequired.Items.Add(new ListItem(Functions.GetGlobalResource("Misc", strResourceTitle)));
            //_radAlternativesAccepted.Items.Add(new ListItem(Functions.GetGlobalResource("Misc", strResourceTitle)));
            //_radRepeatBusiness.Items.Add(new ListItem(Functions.GetGlobalResource("Misc", strResourceTitle)));
            //_lstSources.Add(strJavascriptType);
        }
        /// <summary>
        /// Wire up controls to the ascx
        /// </summary>
        private void WireUpControls()
        {
            //_radFactorySealedSource = (RadioButtonList)FindFieldControl("ctlSelectSource", "radFactorySealedSource");
            //_radObsolete = (RadioButtonList)FindFieldControl("ctlObsolete", "radObsolete");
            //_radLastTimeBuy = (RadioButtonList)FindFieldControl("ctlLastTimeBuy", "radLastTimeBuy");
            //_radRefirbsAcceptable = (RadioButtonList)FindFieldControl("ctlRefirbsAcceptable", "radRefirbsAcceptable");
            //_radTestingRequired = (RadioButtonList)FindFieldControl("ctlTestingRequired", "radTestingRequired");
            //_radAlternativesAccepted = (RadioButtonList)FindFieldControl("ctlAlternativesAccepted", "radAlternativesAccepted");
            //_radRepeatBusiness = (RadioButtonList)FindFieldControl("ctlRepeatBusiness", "radRepeatBusiness");

            //_radFactorySealedSource = (RadioButtonList)ctlDesignBase.FindControl("radFactorySealedSource");
            //_radObsolete = (RadioButtonList)ctlDesignBase.FindControl("radObsolete");
            //_radLastTimeBuy = (RadioButtonList)ctlDesignBase.FindControl("radLastTimeBuy");
            //_radRefirbsAcceptable = (RadioButtonList)ctlDesignBase.FindControl("radRefirbsAcceptable");
            //_radTestingRequired = (RadioButtonList)ctlDesignBase.FindControl("radTestingRequired");
            //_radAlternativesAccepted = (RadioButtonList)ctlDesignBase.FindControl("radAlternativesAccepted");
            //_radRepeatBusiness = (RadioButtonList)ctlDesignBase.FindControl("radRepeatBusiness");

            //_tblPartdetails = (FlexiDataTable)ctlDesignBase.FindContentControl("tblPartdetails");
            _pnlPartDetail = (Panel)ctlDesignBase.FindContentControl("pnlPartDetail");
        }
        /// <summary>
        /// Setup Script Descriptors
        /// </summary>
        private void SetupScriptDescriptors()
        {
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Forms.CusReqAdd_Add", ctlDesignBase.ClientID);
            _scScriptControlDescriptor.AddComponentProperty("ctlSelectCompany", ((ItemSearch.Base)FindContentControl("ctlSelectCompany")).ctlDesignBase.ClientID);
            _scScriptControlDescriptor.AddElementProperty("ibtnSend", (FindIconButton("ibtnSend").ClientID));
            _scScriptControlDescriptor.AddElementProperty("ibtnSend_Footer", (FindFooterIconButton("ibtnSend").ClientID));
            _scScriptControlDescriptor.AddElementProperty("ibtnContinue", (FindIconButton("ibtnContinue").ClientID));
            if (FooterLinksHolder != null) _scScriptControlDescriptor.AddElementProperty("ibtnContinue_Footer", (FindFooterIconButton("ibtnContinue").ClientID));
            _scScriptControlDescriptor.AddProperty("intCompanyID", _objQSManager.CompanyID);
            _scScriptControlDescriptor.AddProperty("strCompanyName", _objQSManager.CompanyName);
            _scScriptControlDescriptor.AddProperty("intContactID", _objQSManager.ContactID);
            _scScriptControlDescriptor.AddProperty("strSearchCompanyName", _objQSManager.SearchCompanyName);
            //_scScriptControlDescriptor.AddElementProperty("radFactorySealedSource", _radFactorySealedSource.ClientID);

            //_scScriptControlDescriptor.AddElementProperty("radObsolete", _radObsolete.ClientID);
            //_scScriptControlDescriptor.AddElementProperty("radLastTimeBuy", _radLastTimeBuy.ClientID);
            //_scScriptControlDescriptor.AddElementProperty("radRefirbsAcceptable", _radRefirbsAcceptable.ClientID);
            //_scScriptControlDescriptor.AddElementProperty("radTestingRequired", _radTestingRequired.ClientID);
            //_scScriptControlDescriptor.AddElementProperty("radAlternativesAccepted", _radAlternativesAccepted.ClientID);
            //_scScriptControlDescriptor.AddElementProperty("radRepeatBusiness", _radRepeatBusiness.ClientID);
            //_scScriptControlDescriptor.AddProperty("arySources", _lstSources);

            //_scScriptControlDescriptor.AddComponentProperty("tblPartdetails", FindFieldControl("ctlPartDetail", "tblPartdetails").ClientID);
            //_scScriptControlDescriptor.AddComponentProperty("tblPartdetails", _tblPartdetails.ClientID);
            _scScriptControlDescriptor.AddElementProperty("pnlPartDetail", _pnlPartDetail.ClientID);
            //_scScriptControlDescriptor.AddElementProperty("btn1", FindFieldControl("ctlPartNo", "btn1").ClientID);
            //_scScriptControlDescriptor.AddElementProperty("btn2", FindFieldControl("ctlPartNo", "btn2").ClientID);
            //_scScriptControlDescriptor.AddElementProperty("lblError", FindFieldControl("ctlPartNo", "lblError").ClientID);
            _scScriptControlDescriptor.AddProperty("strLoginName", SessionManager.LoginFullName);
            if (_objQSManager.CompanyID>0)
            {
                BLL.Company cmp = BLL.Company.Get(_objQSManager.CompanyID);
                if(cmp!=null)
                _scScriptControlDescriptor.AddProperty("intCompnaySalemanNo",cmp.Salesman);
                else
                    _scScriptControlDescriptor.AddProperty("intCompnaySalemanNo", (Int32)SessionManager.LoginID);
            }
            else
                _scScriptControlDescriptor.AddProperty("intCompnaySalemanNo", (Int32)SessionManager.LoginID);

            _scScriptControlDescriptor.AddProperty("intLoginID", (Int32)SessionManager.LoginID);
            _scScriptControlDescriptor.AddComponentProperty("ctltblPartdetails", ((ItemSearch.Base)FindContentControl("ctltblPartdetails")).ctlDesignBase.ClientID);
        }

    }
}
