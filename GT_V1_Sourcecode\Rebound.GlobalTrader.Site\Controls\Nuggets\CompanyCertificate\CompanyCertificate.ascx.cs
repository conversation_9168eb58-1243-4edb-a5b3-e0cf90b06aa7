using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site.Controls;
using Rebound.GlobalTrader.Site;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets {
    public partial class CompanyCertificate : Base
    {

		#region Locals

		protected IconButton _ibtnAdd;
		protected IconButton _ibtnEdit;
		protected FlexiDataTable _tbl;
		
		#endregion

		#region Properties

		private int _intCompanyID = -1;
		public int CompanyID {
			get { return _intCompanyID; }
			set { _intCompanyID = value; }
		}

		private bool _blnCanAdd = true;
		public bool CanAdd {
			get { return _blnCanAdd; }
			set { _blnCanAdd = value; }
		}

		private bool _blnCanEdit = true;
		public bool CanEdit {
			get { return _blnCanEdit; }
			set { _blnCanEdit = value; }
		}

		
		#endregion

		#region Overrides

		/// <summary>
		/// Oninit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
			WireUpControls();
            AddScriptReference("Controls.Nuggets.CompanyCertificate.CompanyCertificate.js");
            TitleText = Functions.GetGlobalResource("Nuggets", "CompanyCertificate");
			if (_objQSManager.CompanyID > 0) _intCompanyID = _objQSManager.CompanyID;
			SetupTables();
		}

		protected override void OnLoad(EventArgs e) {
			base.OnLoad(e);
			
		}

		/// <summary>
		/// OnPreRender
		/// </summary>
		/// <param name="e"></param>
		protected override void OnPreRender(EventArgs e) {
			_ibtnAdd.Visible = _blnCanAdd;
			_ibtnEdit.Visible = _blnCanEdit;
			SetupScriptDescriptors();
			base.OnPreRender(e);
		}

		#endregion

		/// <summary>
		/// Sets up AJAX script descriptors
		/// </summary>
		private void SetupScriptDescriptors() {
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyCertificate", ctlDesignBase.ClientID);
			if (_blnCanAdd) _scScriptControlDescriptor.AddElementProperty("ibtnAdd", _ibtnAdd.ClientID);
			if (_blnCanEdit) _scScriptControlDescriptor.AddElementProperty("ibtnEdit", _ibtnEdit.ClientID);
		
			_scScriptControlDescriptor.AddProperty("intCompanyID", _intCompanyID);
			_scScriptControlDescriptor.AddComponentProperty("tbl", _tbl.ClientID);
			
		}

		private void SetupTables() {
            _tbl.Columns.Add(new FlexiDataColumn("CeriticateName", Unit.Empty));
            _tbl.Columns.Add(new FlexiDataColumn("CategoryName", WidthManager.GetWidth(WidthManager.ColumnWidth.PartNo)));
            _tbl.Columns.Add(new FlexiDataColumn("CeriticateNumber", WidthManager.GetWidth(WidthManager.ColumnWidth.PartNo)));
            _tbl.Columns.Add(new FlexiDataColumn("StartDate", WidthManager.GetWidth(WidthManager.ColumnWidth.Date)));
            _tbl.Columns.Add(new FlexiDataColumn("ExpiryDate",  WidthManager.GetWidth(WidthManager.ColumnWidth.Date)));
			_tbl.Columns.Add(new FlexiDataColumn("PDFIHSDOCtitle", WidthManager.GetWidth(WidthManager.ColumnWidth.Quantity)));


		}

		/// <summary>
		/// Wire up controls from the ascx
		/// </summary>
		private void WireUpControls() {
			_ibtnAdd = FindIconButton("ibtnAdd");
			_ibtnEdit = FindIconButton("ibtnEdit");
			_tbl = (FlexiDataTable)ctlDesignBase.FindContentControl("tbl");
		}
	}
}
