///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference name="~/Controls/IconButton/IconButton.js" />
///<reference name="~/Controls/ReboundTextBox/ReboundTextBox.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Contact");

Rebound.GlobalTrader.Site.Pages.Contact.ContactDetail = function(el) { 
	Rebound.GlobalTrader.Site.Pages.Contact.ContactDetail.initializeBase(this, [el]);
};

Rebound.GlobalTrader.Site.Pages.Contact.ContactDetail.prototype = {

	get_ctlPageTitle: function() { return this._ctlPageTitle; }, 	set_ctlPageTitle: function(v) { if (this._ctlPageTitle !== v)  this._ctlPageTitle = v; }, 
	get_ctlLeft_RecentlyViewed: function() { return this._ctlLeft_RecentlyViewed; }, 	set_ctlLeft_RecentlyViewed: function(v) { if (this._ctlLeft_RecentlyViewed !== v)  this._ctlLeft_RecentlyViewed = v; }, 
	get_ctlContactList: function() { return this._ctlContactList; }, 	set_ctlContactList: function(v) { if (this._ctlContactList !== v)  this._ctlContactList = v; }, 
	get_ctlContactInfoMain: function() { return this._ctlContactInfoMain; }, 	set_ctlContactInfoMain: function(v) { if (this._ctlContactInfoMain !== v)  this._ctlContactInfoMain = v; }, 
	get_ctlContactExtendedInfo: function() { return this._ctlContactExtendedInfo; }, 	set_ctlContactExtendedInfo: function(v) { if (this._ctlContactExtendedInfo !== v)  this._ctlContactExtendedInfo = v; }, 
	get_ctlContactLog: function() { return this._ctlContactLog; }, 	set_ctlContactLog: function(v) { if (this._ctlContactLog !== v)  this._ctlContactLog = v; }, 
	get_intContactID: function() { return this._intContactID; }, 	set_intContactID: function(v) { if (this._intContactID !== v)  this._intContactID = v; }, 
	get_btnTransactions: function() { return this._btnTransactions; }, 	set_btnTransactions: function(v) { if (this._btnTransactions !== v)  this._btnTransactions = v; }, 

	initialize: function() {
		Rebound.GlobalTrader.Site.Pages.Contact.ContactDetail.callBaseMethod(this, "initialize");
	},
	
	goInit: function() {
		if (this._ctlContactList) this._ctlContactList.addSelectContact(Function.createDelegate(this, this.ctlContactList_SelectContact));
		if (this._ctlContactInfoMain) this._ctlContactInfoMain.addSaveEditComplete(Function.createDelegate(this, this.ctlContactInfoMain_SaveEditComplete));
		Rebound.GlobalTrader.Site.Pages.Contact.ContactDetail.callBaseMethod(this, "goInit");
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this._ctlPageTitle) this._ctlPageTitle.dispose();
		if (this._ctlLeft_RecentlyViewed) this._ctlLeft_RecentlyViewed.dispose();
		if (this._ctlContactList) this._ctlContactList.dispose();
		if (this._ctlContactInfoMain) this._ctlContactInfoMain.dispose();
		if (this._ctlContactExtendedInfo) this._ctlContactExtendedInfo.dispose();
		if (this._ctlContactLog) this._ctlContactLog.dispose();
		if (this._btnTransactions) this._btnTransactions.dispose();
		this._ctlPageTitle = null;
		this._ctlLeft_RecentlyViewed = null;
		this._ctlContactList = null;
		this._ctlContactInfoMain = null;
		this._ctlContactExtendedInfo = null;
		this._ctlContactLog = null;
		this._btnTransactions = null;
		Rebound.GlobalTrader.Site.Pages.Contact.ContactDetail.callBaseMethod(this, "dispose");
	},
	
	ctlContactList_SelectContact: function() { 
		this._intContactID = this._ctlContactList._intContactID;
		var strTitle = String.format($R_RES.ContactsForCompany, this._ctlContactList._strCompanyName);
		if (this._intContactID > 0) {
			strTitle = String.format("{0} ({1})", this._ctlContactList._strContactName, this._ctlContactList._strCompanyName);
			this._ctlContactInfoMain._intContactID = this._intContactID;
			this._ctlContactInfoMain.refresh();
			//this._ctlContactExtendedInfo._intContactID = this._intContactID;
			//this._ctlContactExtendedInfo.refresh();
			this._ctlContactLog._intContactID = this._intContactID;
			this._ctlContactLog.refresh();
			this._ctlLeft_RecentlyViewed.addItem($RGT_gotoURL_Contact(this._intContactID), strTitle);
		}
		this._ctlPageTitle.updateTitle(strTitle);
		this._ctlContactInfoMain.show(this._intContactID > 0);
		//this._ctlContactExtendedInfo.show(this._intContactID > 0);
		this._ctlContactLog.show(this._intContactID > 0);
		if (this._btnTransactions) this._btnTransactions.changeContact(this._intContactID, this._ctlContactList._strContactName);
	},
	
	ctlContactInfoMain_SaveEditComplete: function() {
		this._ctlContactList.refresh();
	}
	
};

Rebound.GlobalTrader.Site.Pages.Contact.ContactDetail.registerClass("Rebound.GlobalTrader.Site.Pages.Contact.ContactDetail", Rebound.GlobalTrader.Site.Pages.Content, Sys.IDisposable);
