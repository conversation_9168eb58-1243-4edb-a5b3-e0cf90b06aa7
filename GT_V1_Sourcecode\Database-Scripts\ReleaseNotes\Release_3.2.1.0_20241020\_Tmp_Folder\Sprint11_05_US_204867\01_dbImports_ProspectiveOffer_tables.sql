﻿/*   
===========================================================================================  
TASK			UPDATED BY      DATE			ACTION		DESCRIPTION  
[US-204867]		An.TranTan		23-Sep-2024		Create		Create new tables for import prospective offers
===========================================================================================  
*/
--store temp column header from import file
IF OBJECT_ID(N'BorisGlobalTraderImports.dbo.tbProspectiveOffer_tempHeading', N'U') IS NULL
BEGIN
	CREATE TABLE BorisGlobalTraderImports.dbo.tbProspectiveOffer_tempHeading(
		ProsOfferHeadingId INT IDENTITY(1,1) NOT NULL PRIMARY KEY,
		Column1 NVARCHAR(200) NULL,
		Column2 NVARCHAR(200) NULL,
		Column3 NVARCHAR(200) NULL,
		Column4 NVARCHAR(200) NULL,
		Column5 NVARCHAR(200) NULL,
		Column6 NVARCHAR(200) NULL,
		Column7 NVARCHAR(200) NULL,
		Column8 NVARCHAR(200) NULL,
		Column9 NVARCHAR(200) NULL,
		Column10 NVARCHAR(200) NULL,
		Column11 NVARCHAR(200) NULL,
		[CreatedBy] INT NOT NULL,
		[CreatedDate] DATETIME NOT NULL DEFAULT GETDATE()
	)
END
GO
--store temp column data from import file
IF OBJECT_ID(N'BorisGlobalTraderImports.dbo.tbProspectiveOffer_tempData', N'U') IS NULL
BEGIN
	CREATE TABLE BorisGlobalTraderImports.dbo.tbProspectiveOffer_tempData(
		ProspectiveOfferId INT IDENTITY(1,1) NOT NULL PRIMARY KEY,
		Column1 NVARCHAR(MAX) NULL,
		Column2 NVARCHAR(MAX) NULL,
		Column3 NVARCHAR(MAX) NULL,
		Column4 NVARCHAR(MAX) NULL,
		Column5 NVARCHAR(MAX) NULL,
		Column6 NVARCHAR(MAX) NULL,
		Column7 NVARCHAR(MAX) NULL,
		Column8 NVARCHAR(MAX) NULL,
		Column9 NVARCHAR(MAX) NULL,
		Column10 NVARCHAR(MAX) NULL,
		Column11 NVARCHAR(MAX) NULL,
		LineNumber INT NULL,
		[OriginalFilename] [nvarchar](100) NULL,
		[GeneratedFilename] [nvarchar](100) NULL,
		[CreatedBy] INT NOT NULL,
		[CreatedDate] DATETIME NOT NULL DEFAULT GETDATE()
	)
END
GO
--store column mapping option for specific supplier and import user
IF OBJECT_ID(N'BorisGlobalTraderImports.dbo.tbProspectiveOffer_ColumnMapping', N'U') IS NULL
BEGIN
	CREATE TABLE BorisGlobalTraderImports.dbo.tbProspectiveOffer_ColumnMapping(
		SupplierNo INT NOT NULL,
		Manufacturer NVARCHAR(10) NULL,
		Part NVARCHAR(10) NULL,
		Quantity NVARCHAR(10) NULL,
		Price NVARCHAR(10) NULL,
		[Description] NVARCHAR(10) NULL,
		AlterPart NVARCHAR(10) NULL,
		DateCode NVARCHAR(10) NULL,
		Product NVARCHAR(10) NULL,
		Package NVARCHAR(10) NULL,
		ROHS NVARCHAR(10) NULL,
		SupplierPart NVARCHAR(10) NULL,
		FixedCurrencyNo INT,
		[CreatedBy] INT NOT NULL,
		[CreatedDate] DATETIME NOT NULL DEFAULT GETDATE(),
		[UpdatedBy] INT  NULL,
		[UpdatedDate] DATETIME NULL
	)
END
GO
--store data to be imported
IF OBJECT_ID(N'BorisGlobalTraderImports.dbo.tbProspectiveOffer_ToBeImported', N'U') IS NULL
BEGIN
	--DROP TABLE BorisGlobalTraderImports.dbo.tbProspectiveOffer_ToBeImported
	CREATE TABLE BorisGlobalTraderImports.dbo.tbProspectiveOffer_ToBeImported(
		ProspectiveOfferId INT IDENTITY(1,1) NOT NULL PRIMARY KEY,
		SupplierNo INT NOT NULL,
		ManufacturerNo INT NULL,
		Part NVARCHAR(30) NULL,
		AlternativePart NVARCHAR(30) NULL,
		Quantity INT NOT NULL,
		Price FLOAT NOT NULL,
		[Description] NVARCHAR(500) NULL,
		DateCode NVARCHAR(5) NULL,
		ProductNo INT NULL,
		PackageNo INT NULL,
		ROHS INT NULL,
		CurrencyNo INT NULL,
		SupplierPart NVARCHAR(30) NULL,
		OriginalFilename NVARCHAR(100) NULL,
		CreatedBy INT NOT NULL,
		DLUP DATETIME NOT NULL DEFAULT GETDATE()
	)
END
GO

IF COL_LENGTH('dbo.tbProspectiveOfferLines', 'ROHS') IS NULL
BEGIN
   ALTER TABLE dbo.tbProspectiveOfferLines ADD ROHS INT NULL
END
GO

--insert new type to tbUtilityType
IF NOT EXISTS(SELECT * FROM tbUtilityType WHERE UtilityTypeName = 'Prospective Offers Import Tool')
BEGIN
	INSERT INTO dbo.tbUtilityType(UtilityTypeName, Inactive, DLUP) VALUES('Prospective Offers Import Tool', 0, GETDATE())
END


