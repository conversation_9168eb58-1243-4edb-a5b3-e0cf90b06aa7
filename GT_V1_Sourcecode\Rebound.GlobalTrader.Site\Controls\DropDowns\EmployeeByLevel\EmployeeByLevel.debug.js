///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//
// RP 06.10.2009:
// - Changes due to changes in base class
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");

Rebound.GlobalTrader.Site.Controls.DropDowns.EmployeeByLevel = function(element) { 
	Rebound.GlobalTrader.Site.Controls.DropDowns.EmployeeByLevel.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.DropDowns.EmployeeByLevel.prototype = {

	get_blnLimitToCurrentUsersDivision: function() { return this._blnLimitToCurrentUsersDivision; }, set_blnLimitToCurrentUsersDivision: function(v) { if (this._blnLimitToCurrentUsersDivision !== v) this._blnLimitToCurrentUsersDivision = v; },
	get_blnLimitToCurrentUsersTeam: function() { return this._blnLimitToCurrentUsersTeam; }, set_blnLimitToCurrentUsersTeam: function(v) { if (this._blnLimitToCurrentUsersTeam !== v) this._blnLimitToCurrentUsersTeam = v; },
	get_blnExcludeCurrentUser: function() { return this._blnExcludeCurrentUser; }, set_blnExcludeCurrentUser: function(v) { if (this._blnExcludeCurrentUser !== v) this._blnExcludeCurrentUser = v; },

	initialize: function() {
		this.addSetupDataCall(Function.createDelegate(this, this.setupDataCall));
		this.addGetDataOK(Function.createDelegate(this, this.dataCallOK));
		Rebound.GlobalTrader.Site.Controls.DropDowns.EmployeeByLevel.callBaseMethod(this, "initialize");
	},
	
	dispose: function() { 
		if (this.isDisposed) return;
		this._blnLimitToCurrentUsersDivision = null;
		this._blnLimitToCurrentUsersTeam = null;
		this._blnExcludeCurrentUser = null;
		Rebound.GlobalTrader.Site.Controls.DropDowns.EmployeeByLevel.callBaseMethod(this, "dispose");
	},
	
	setupDataCall: function() { 
		this._objData.set_PathToData("controls/DropDowns/EmployeeByLevel");
		this._objData.set_DataObject("EmployeeByLevel");
		this._objData.set_DataAction("GetData");
		this._objData.addParameter("LimitToCurrentUsersTeam", this._blnLimitToCurrentUsersTeam);
		this._objData.addParameter("LimitToCurrentUsersDivision", this._blnLimitToCurrentUsersDivision);
		this._objData.addParameter("ExcludeCurrentUser", this._blnExcludeCurrentUser);
	},
	
	dataCallOK: function() { 
		var result = this._objData._result;
		if (result.Employees) {
			for (var i = 0; i < result.Employees.length; i++) {
				this.addOption(result.Employees[i].Name, result.Employees[i].ID);
			}
		}
	}
	
};

Rebound.GlobalTrader.Site.Controls.DropDowns.EmployeeByLevel.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.EmployeeByLevel", Rebound.GlobalTrader.Site.Controls.DropDowns.Base);
