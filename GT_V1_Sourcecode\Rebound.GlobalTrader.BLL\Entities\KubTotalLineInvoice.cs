﻿using Rebound.GlobalTrader.DAL;
using Rebound.GlobalTrader.DAL.SQLClient;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Rebound.GlobalTrader.BLL
{
    public partial class KubTotalLineInvoice : BizObject
    {
        #region Properties
        public System.String TotalNoOfLinesInvoicedIn12Months { get; set; }
        #endregion

        #region Methods
        public static KubTotalLineInvoice GetKubTotalLineInvoiceDetails(System.String PartNo, System.Int32 ClientID, System.Int32? CustomerReqId)
        {
            KubTotalLineInvoice obj = new KubTotalLineInvoice();
            KubTotalLineInvoiceDetails objDetails = new KubTotalLineInvoiceDetails();
            SQLKubProvider objSQLKubProvider = new SQLKubProvider();
            try
            {
                objDetails = objSQLKubProvider.FetchKubTotalLineInvoiceDetails(PartNo, ClientID, CustomerReqId);
                if (objDetails == null)
                {
                    return null;
                }
                else
                {
                    obj.TotalNoOfLinesInvoicedIn12Months = objDetails.TotalNoOfLinesInvoicedIn12Months;
                }
                return obj;
            }
            catch (Exception ex)
            {
                throw ex;
            }
            finally
            {
                obj = null;
            }
        }
        #endregion

    }
}
