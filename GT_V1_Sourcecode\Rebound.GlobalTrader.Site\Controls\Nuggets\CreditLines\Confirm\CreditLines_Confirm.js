Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.CreditLines_Confirm=function(n){Rebound.GlobalTrader.Site.Controls.Forms.CreditLines_Confirm.initializeBase(this,[n]);this._intRequirementLineID=-1;this._intBOMID=-1;this._CreditLineIds=""};Rebound.GlobalTrader.Site.Controls.Forms.CreditLines_Confirm.prototype={get_BomCode:function(){return this._BomCode},set_BomCode:function(n){this._BomCode!==n&&(this._BomCode=n)},get_BomName:function(){return this._BomName},set_BomName:function(n){this._BomName!==n&&(this._BomName=n)},get_BomCompanyName:function(){return this._BomCompanyName},set_BomCompanyName:function(n){this._BomCompanyName!==n&&(this._BomCompanyName=n)},get_BomCompanyNo:function(){return this._BomCompanyNo},set_BomCompanyNo:function(n){this._BomCompanyNo!==n&&(this._BomCompanyNo=n)},get_SalesManNo:function(){return this._SalesManNo},set_SalesManNo:function(n){this._SalesManNo!==n&&(this._SalesManNo=n)},get_SalesManName:function(){return this._SalesManName},set_SalesManName:function(n){this._SalesManName!==n&&(this._SalesManName=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.CreditLines_Confirm.callBaseMethod(this,"initialize");this._strDataPath="controls/Nuggets/CreditLines";this._strDataObject="CreditLines";this.addShown(Function.createDelegate(this,this.formShown))},dispose:function(){this.isDisposed||(this._frmConfirm&&this._frmConfirm.dispose(),this._frmConfirm=null,this._intRequirementLineID=null,Rebound.GlobalTrader.Site.Controls.Forms.CreditLines_Confirm.callBaseMethod(this,"dispose"))},formShown:function(){this._blnFirstTimeShown&&(this._frmConfirm=this.getFieldComponent("frmConfirm"),this._frmConfirm.addClickYesEvent(Function.createDelegate(this,this.yesClicked)),this._frmConfirm.addClickNoEvent(Function.createDelegate(this,this.noClicked)))},yesClicked:function(){this.showSaving(!0);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strDataPath);n.set_DataObject(this._strDataObject);n.set_DataAction("SendCreditNoteToPOHUB");n.addParameter("CreditLineID",this._CreditLineIds);n.addDataOK(Function.createDelegate(this,this.saveComplete));n.addError(Function.createDelegate(this,this.saveError));n.addTimeout(Function.createDelegate(this,this.saveError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},noClicked:function(){this.showSaving(!1);this.onNotConfirmed()},saveError:function(n){this.showSaving(!1);this._strErrorMessage=n._errorMessage;this.onSaveError()},saveComplete:function(n){this.showSaving(!1);n._result.Result?this.onSaveComplete():(this._strErrorMessage=n._errorMessage,this.onSaveError())}};Rebound.GlobalTrader.Site.Controls.Forms.CreditLines_Confirm.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CreditLines_Confirm",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);