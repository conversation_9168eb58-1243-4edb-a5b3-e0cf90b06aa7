<%@ Control Language="C#" CodeBehind="SRMALines.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.ItemSearch.SRMALines" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_ItemSearch:DesignBase id="ctlDB" runat="server" InitialSortDirection="DESC">
	<FieldsLeft>
		<ReboundUI_FilterDataItemRow:Numerical id="ctlSRMANo" runat="server" ResourceTitle="SRMANo" />
		<ReboundUI_FilterDataItemRow:TextBox id="ctlPart" runat="server" ResourceTitle="PartNo" />
		<ReboundUI_FilterDataItemRow:TextBox id="ctlCompany" runat="server" ResourceTitle="Company" />
		<ReboundUI_FilterDataItemRow:TextBox id="ctlContact" runat="server" ResourceTitle="Contact" />
		<ReboundUI_FilterDataItemRow:DropDown id="ctlBuyer" runat="server" ResourceTitle="Buyer" DropDownType="Employee" DropDownAssembly="Rebound.GlobalTrader.Site" />
	</FieldsLeft>
	<FieldsRight>
		<ReboundUI_FilterDataItemRow:Numerical id="ctlPurchaseOrderNo" runat="server" ResourceTitle="PurchaseOrderNo" />
		<ReboundUI_FilterDataItemRow:TextBox id="ctlSRMANotes" runat="server" ResourceTitle="Notes" InitialSearchType="Contains" />
		<ReboundUI_FilterDataItemRow:DateSelect id="ctlSRMADateFrom" runat="server" ResourceTitle="SupplierRMADateFrom" />
		<ReboundUI_FilterDataItemRow:DateSelect id="ctlSRMADateTo" runat="server" ResourceTitle="SupplierRMADateTo" />
	</FieldsRight>	
</ReboundUI_ItemSearch:DesignBase>
