Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.CRMAReceiveLines_Confirm=function(n){Rebound.GlobalTrader.Site.Controls.Forms.CRMAReceiveLines_Confirm.initializeBase(this,[n]);this._intLineID=-1;this._intSelectedLineID=0;this._intSelectedGoodsLineID=0;this._ctlConfirm=null;this._intAllocationID=-1;this._intInvoiceLineID=-1};Rebound.GlobalTrader.Site.Controls.Forms.CRMAReceiveLines_Confirm.prototype={get_intGIID:function(){return this._intGIID},set_intGIID:function(n){this._intGIID!==n&&(this._intGIID=n)},get_intLineID:function(){return this._intLineID},set_intLineID:function(n){this._intLineID!==n&&(this._intLineID=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.CRMAReceiveLines_Confirm.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown))},dispose:function(){this.isDisposed||(this._ctlConfirm&&this._ctlConfirm.dispose(),this._intSelectedGoodsLineID=null,this._intSelectedLineID=null,this._ctlConfirm=null,this._intAllocationID=null,Rebound.GlobalTrader.Site.Controls.Forms.CRMAReceiveLines_Confirm.callBaseMethod(this,"dispose"))},formShown:function(){this._blnFirstTimeShown&&(this._ctlConfirm=this.getFieldComponent("ctlConfirm"),this._ctlConfirm.addClickYesEvent(Function.createDelegate(this,this.yesClicked)),this._ctlConfirm.addClickNoEvent(Function.createDelegate(this,this.noClicked)))},yesClicked:function(){this.deleteSerialBySOLine();this.onSaveComplete()},deleteSerialBySOLine:function(){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/CRMAReceivingLines");n.set_DataObject("CRMAReceivingLines");n.set_DataAction("DeattachCRMASerial");n.addParameter("InvoiceLineNo",this._intInvoiceLineID);n.addDataOK(Function.createDelegate(this,this.deleteSerialBySOLineOK));n.addError(Function.createDelegate(this,this.deleteSerialBySOLineError));n.addTimeout(Function.createDelegate(this,this.deleteSerialBySOLineError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},deleteSerialBySOLineOK:function(n){this.showSaving(!1);n._result.Result==!0&&(this.showSavedOK(!0),this.onSaveComplete())},deleteSerialBySOLineError:function(n){this.showSaving(!1);this._strErrorMessage=n._errorMessage;this.onSaveError()},noClicked:function(){this.onNotConfirmed()}};Rebound.GlobalTrader.Site.Controls.Forms.CRMAReceiveLines_Confirm.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CRMAReceiveLines_Confirm",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);