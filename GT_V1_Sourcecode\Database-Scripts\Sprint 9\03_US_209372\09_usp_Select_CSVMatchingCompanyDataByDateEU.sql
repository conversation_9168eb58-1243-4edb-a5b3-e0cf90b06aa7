﻿/*===========================================================================================  
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[BUG-209372]    cuongdx			27-AUG-2024			CREATE		Get CSV Matching company EU
===========================================================================================  
*/ 
CREATE OR ALTER procedure usp_Select_CSVMatchingCompanyDataByDateEU (@InputDate varchar(100) = null
                                                         --@ToDate varchar(100) = null           
                                                         )
AS
BEGIN

    select csl.companyid,
           csl.CustomerCode,
           csl.ClientNo,
           csl.ClientName,
           csl.CompanyName,
           csl.Notes as [General_customer_info],
           csl.ImportantNotes as Accounts_notes,
           csl.GT_Company_Address,
           csl.CSL_Name,
           csl.CSL_Address,
           CSL_ALT_Name
    from tbCSLGTComparisionEU csl
        LEFT JOIN tbCompany co
            on co.CompanyId = csl.companyid
        LEFT Join tbClient cl
            on cl.ClientId = csl.ClientNo
    where
        --cast(Insertedon as date) >= cast(IsNUll(@InputDate,getdate()) as date)           
        --and cast(Insertedon as date) < cast(IsNUll(@ToDate,getdate()) as date)           
        cast(Insertedon as date) = cast(IsNUll(@InputDate, getdate()) as date)
        AND co.Inactive != 1
        AND cl.Inactive != 1
END