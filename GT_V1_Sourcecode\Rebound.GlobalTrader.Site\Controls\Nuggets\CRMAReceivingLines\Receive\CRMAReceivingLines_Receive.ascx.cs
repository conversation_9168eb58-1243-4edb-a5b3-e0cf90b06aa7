using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.Forms {
	public partial class CRMAReceivingLines_Receive : Base {

		#region Locals

		protected RadioButtonList _radNewOrExisting;
		protected IconButton _ibtnContinue;
		protected IconButton _ibtnContinue_Footer;
		protected IconButton _ibtnSend;
		protected IconButton _ibtnSend_Footer;
		protected TableRow _trNewGI;
		protected TableRow _trGoodsIn;
		protected ItemSearch.GoodsIn _ctlGoodsIn;
        protected TableRow _trSerialNo;
        protected FlexiDataTable _tblSerialNoFinal;
        protected MultiSelectionCount _ctlMultiSelectionCount;

		#endregion

		#region Properties

		#endregion

		#region Overrides

		/// <summary>
		/// OnInit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
			TitleText = Functions.GetGlobalResource("FormTitles", "CRMAReceivingLines_Receive");
			AddScriptReference("Controls.Nuggets.CRMAReceivingLines.Receive.CRMAReceivingLines_Receive.js");
			WireUpControls();
            SetupTable();
			SetupSelectSourceScreen();
		}

		/// <summary>
		/// OnPreRender
		/// </summary>
		/// <param name="e"></param>
		protected override void OnPreRender(EventArgs e) {
			WireUpButtons();
			SetupScriptDescriptors();
			//imgCalculate.ImageUrl = Functions.GetThemeImage("nuggets/lcc.gif", Page.Theme);
			base.OnPreRender(e);
		}

		#endregion

		/// <summary>
		/// Setup controls for Select Source screen
		/// </summary>
		private void SetupSelectSourceScreen() {
			_radNewOrExisting.Items.Add(new ListItem(Functions.GetGlobalResource("Misc", "NewGI")));
			_radNewOrExisting.Items.Add(new ListItem(Functions.GetGlobalResource("Misc", "ExistingGI")));
			_radNewOrExisting.SelectedIndex = 0;
		}

		private void WireUpButtons() {
			_ibtnContinue = (IconButton)FindIconButton("ibtnContinue");
			_ibtnContinue_Footer = (IconButton)FindFooterIconButton("ibtnContinue");
			_ibtnSend = (IconButton)FindIconButton("ibtnSend");
			_ibtnSend_Footer = (IconButton)FindFooterIconButton("ibtnSend");
            
		}

		/// <summary>
		/// Wire up controls to the ascx
		/// </summary>
		private void WireUpControls() {
			_radNewOrExisting = (RadioButtonList)FindFieldControl("ctlSelectNewOrExisting", "radNewOrExisting");
			_trNewGI = (TableRow)FindContentControl("trNewGI");
			_trGoodsIn = (TableRow)FindContentControl("trGoodsIn");
			_ctlGoodsIn = (ItemSearch.GoodsIn)FindContentControl("ctlGoodsIn");
            _ctlMultiSelectionCount = (MultiSelectionCount)ctlDesignBase.FindContentControl("ctlMultiSelectionCount");
            _tblSerialNoFinal = (FlexiDataTable)ctlDesignBase.FindContentControl("tblSerialNoFinal");
            _trSerialNo = (TableRow)FindContentControl("trSerialNo");
		}

		/// <summary>
		/// Setup Script Descriptors
		/// </summary>
		private void SetupScriptDescriptors() {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Forms.CRMAReceivingLines_Receive", ctlDesignBase.ClientID);
			_scScriptControlDescriptor.AddElementProperty("radNewOrExisting", _radNewOrExisting.ClientID);
			_scScriptControlDescriptor.AddProperty("intLoginID", SessionManager.LoginID);
			_scScriptControlDescriptor.AddElementProperty("ibtnContinue", _ibtnContinue.ClientID);
			if (FooterLinksHolder != null) _scScriptControlDescriptor.AddElementProperty("ibtnContinue_Footer", _ibtnContinue_Footer.ClientID);
			_scScriptControlDescriptor.AddElementProperty("ibtnSend", _ibtnSend.ClientID);
			if (FooterLinksHolder != null) _scScriptControlDescriptor.AddElementProperty("ibtnSend_Footer", _ibtnSend_Footer.ClientID);
			_scScriptControlDescriptor.AddElementProperty("trNewGI", _trNewGI.ClientID);
			_scScriptControlDescriptor.AddElementProperty("trGoodsIn", _trGoodsIn.ClientID);
			_scScriptControlDescriptor.AddComponentProperty("ctlGoodsIn", _ctlGoodsIn.ctlDesignBase.ClientID);
            _scScriptControlDescriptor.AddProperty("strLoginFullName", SessionManager.LoginFullName);

            _scScriptControlDescriptor.AddElementProperty("lblComplete", FindFieldControl("ctlComplete", "lblComplete").ClientID);
            _scScriptControlDescriptor.AddElementProperty("trSerialNo", _trSerialNo.ClientID);
            _scScriptControlDescriptor.AddElementProperty("btnAll", ((IconButton)FindContentControl("btnAll")).ClientID);
            _scScriptControlDescriptor.AddComponentProperty("ctlGiSerialNumber", ((ItemSearch.Base)FindContentControl("ctlGiSerialNumber")).ctlDesignBase.ClientID);
            _scScriptControlDescriptor.AddComponentProperty("ctlMultiSelectionCount", _ctlMultiSelectionCount.ClientID);
            _scScriptControlDescriptor.AddElementProperty("btnAdd", FindFieldControl("ctlAddUpdate", "btnAdd").ClientID);
            _scScriptControlDescriptor.AddElementProperty("btnRefresh", FindFieldControl("ctlAddUpdate", "btnRefresh").ClientID);
            _scScriptControlDescriptor.AddComponentProperty("tblSerialNoFinal", _tblSerialNoFinal.ClientID);
		}
        private void SetupTable()
        {

            _tblSerialNoFinal.AllowMultipleSelection = true;
            _tblSerialNoFinal.Columns.Add(new FlexiDataColumn("SubGroup", WidthManager.GetWidth(WidthManager.ColumnWidth.DateAndTime)));
            _tblSerialNoFinal.Columns.Add(new FlexiDataColumn("SerialNo", WidthManager.GetWidth(WidthManager.ColumnWidth.Warehouse)));
            _tblSerialNoFinal.Columns.Add(new FlexiDataColumn("Action", Unit.Empty, false));

        }
	}
}