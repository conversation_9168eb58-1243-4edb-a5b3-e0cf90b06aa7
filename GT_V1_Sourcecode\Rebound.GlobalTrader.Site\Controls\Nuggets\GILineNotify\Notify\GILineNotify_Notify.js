Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.GILineNotify_Notify=function(n){Rebound.GlobalTrader.Site.Controls.Forms.GILineNotify_Notify.initializeBase(this,[n]);this._intGIID=0;this._intLineID=-1;this._intCurrencyID=-1;this._strCurrencyCode="";this._dblPOLineShipInCost=0;this._intPOQuantityOrdered=0;this._blnCanEditShipInCost=!1;this._blnCanEditPurchasePrice=!1;this._blnRelatedToIPO=!1;this._intIPOClientNo=-1;this._poBankFee=0;this._intGlobalClientNo=-1;this._intSerialNoCount=0;this._blnSerNoRecorded=!1;this._blnProductHaza=!1;this._blnGISplited=!1;this._intManufacturerId=-1;this._intPackagingType=-1;this._intROHSValue=-1;this._ctlMSL="";this._intGILineId=-1;this._intApprovalType=-1;this._intLoginType=0;this._intGINumber=-1;this._intUpdatedBy=-1};Rebound.GlobalTrader.Site.Controls.Forms.GILineNotify_Notify.prototype={get_ibtnSend:function(){return this._ibtnSend},set_ibtnSend:function(n){this._ibtnSend!==n&&(this._ibtnSend=n)},get_ibtnSend_Footer:function(){return this._ibtnSend_Footer},set_ibtnSend_Footer:function(n){this._ibtnSend_Footer!==n&&(this._ibtnSend_Footer=n)},get_tblPackagingBreakdown:function(){return this._tblPackagingBreakdown},set_tblPackagingBreakdown:function(n){this._tblPackagingBreakdown!==n&&(this._tblPackagingBreakdown=n)},get_tblDateCode:function(){return this._tblDateCode},set_tblDateCode:function(n){this._tblDateCode!==n&&(this._tblDateCode=n)},get_intGILineId:function(){return this._intGILineId},set_intGILineId:function(n){this._intGILineId!==n&&(this._intGILineId=n)},get_IsPOHub:function(){return this._IsPOHub},set_IsPOHub:function(n){this._IsPOHub!==n&&(this._IsPOHub=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.GILineNotify_Notify.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown));this.addCancel(Function.createDelegate(this,this.cancelClicked))},formShown:function(){this.getGIQuery();this._blnFirstTimeShown&&($R_IBTN.addClick(this._ibtnSend,Function.createDelegate(this,this.sendMail)),$R_IBTN.addClick(this._ibtnSend_Footer,Function.createDelegate(this,this.sendMail)));$find(this.getFormControlID(this._element.id,"ddlQueryApprovedSales")).getData();$find(this.getFormControlID(this._element.id,"ddlQueryApprovedPurchase")).getData();$find(this.getFormControlID(this._element.id,"ddlQueryApprovedQuality")).getData();$addHandler($get("ctl00_cphMain_ctlGILineNotify_ctlDB_ctl14_ctlNotify_ctlDB_ibtnGeneratePDF_hyp"),"click",this.createPDF);$addHandler($get("ctl00_cphMain_ctlGILineNotify_ctlDB_ctl14_ctlNotify_ctlDB_ibnQuarantineProduct_hyp"),"click",this.ReportNPR);document.getElementById("ctl00_cphMain_ctlGILineNotify_ctlDB_ctl14_ctlNotify_ctlDB_ibtnGeneratePDF_hyp").style.color="white";document.getElementById("ctl00_cphMain_ctlGILineNotify_ctlDB_ctl14_ctlNotify_ctlDB_ibnQuarantineProduct_hyp").style.color="white";$addHandler($get("ctl00_cphMain_ctlGILineNotify_ctlDB_ctl14_ctlNotify_ctlDB_ibnAddImage_hyp"),"click",this.uploadImage);document.getElementById("ctl00_cphMain_ctlGILineNotify_ctlDB_ctl14_ctlNotify_ctlDB_ibnAddImage_hyp").style.color="white";$find(this.getFormControlID(this._element.id,"ddlUpdateType")).getData();$("#ctl00_cphMain_ctlGILineNotify_ctlDB_ctl14_ctlNotify_ctlDB_ddlUpdateType_ddl").prop("disabled",!0);$("#ctl00_cphMain_ctlGILineNotify_ctlDB_ctl14_ctlNotify_ctlDB_ddlUpdateType_ctl02").css("display","none");$find(this.getFormControlID(this._element.id,"ddlROHSStatus")).getData();$("#ctl00_cphMain_ctlGILineNotify_ctlDB_ctl14_ctlNotify_ctlDB_ddlROHSStatus_ddl").prop("disabled",!0);$("#ctl00_cphMain_ctlGILineNotify_ctlDB_ctl14_ctlNotify_ctlDB_ddlROHSStatus_ctl02").css("display","none")},dispose:function(){this.isDisposed||(this._ibtnSend&&$R_IBTN.clearHandlers(this._ibtnSend),this._ibtnSend_Footer&&$R_IBTN.clearHandlers(this._ibtnSend_Footer),this._ibtnSend=null,this._ibtnSend_Footer=null,this._tblPackagingBreakdown&&this._tblPackagingBreakdown.dispose(),this._tblPackagingBreakdown=null,this._tblDateCode&&this._tblDateCode.dispose(),this._tblDateCode=null,Rebound.GlobalTrader.Site.Controls.Forms.GILineNotify_Notify.callBaseMethod(this,"dispose"))},sendMail:function(){if(this.validateForm()){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/GILineNotify");n.set_DataObject("GILineNotify");n.set_DataAction("NotifyQuery");n.addParameter("id",this._intGILineId);n.addParameter("QueryApprovedStatusSales",$find(this.getFormControlID(this._element.id,"ddlQueryApprovedSales")).getValue());n.addParameter("QueryApprovedStatusPurchase",$find(this.getFormControlID(this._element.id,"ddlQueryApprovedPurchase")).getValue());n.addParameter("QueryApprovedStatusQuality",$find(this.getFormControlID(this._element.id,"ddlQueryApprovedQuality")).getValue());n.addParameter("IsPDFReportRequired",this.getControlValue(this.getFormControlID(this._element.id,"chkPDFReportRequired"),"CheckBox"));n.addParameter("IsQuarantineProduct",this.getControlValue(this.getFormControlID(this._element.id,"chkQuarantineProduct"),"CheckBox"));n.addParameter("QueryReply",$get(this.getFormControlID(this._element.id,"GIQueryReply")).value);n.addParameter("LoginType",this._intLoginType);n.addParameter("GINumber",this._intGINumber);n.addParameter("UpdatedBy",this._intUpdatedBy);n.addDataOK(Function.createDelegate(this,this.sendMailComplete));n.addError(Function.createDelegate(this,this.sendMailError));n.addTimeout(Function.createDelegate(this,this.sendMailError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null}},validateForm:function(){var n=!0;return n||this.showError(!0),n},sendMailComplete:function(){this.showLoading(!1);this.showSavedOK(!0);location.href=$RGT_gotoURL_GoodsIn(this._intGIID);this.enableButton(!0)},sendMailError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},cancelClicked:function(){this._intGIID<=0?$R_FN.navigateBack():location.href=$RGT_gotoURL_GoodsIn(this._intGIID)},enableButton:function(n){$R_IBTN.enableButton(this._ibtnSend,n);$R_IBTN.enableButton(this._ibtnSend_Footer,n)},setCurrency:function(n,t){this._intCurrencyID=n;this._strCurrencyCode=t;$R_FN.setInnerHTML(this._lblCurrency_Price,t);$R_FN.setInnerHTML(this._lblCurrency_PriceLabel,t);$R_FN.setInnerHTML(this._lblCurrency_Price_IPO,t);$R_FN.setInnerHTML(this._lblCurrency_PriceLabel_IPO,t)},setCurrencyIPO:function(n){this._strCurrencyCode=n;$R_FN.setInnerHTML(this._lblCurrency_Price_IPO,n);$R_FN.setInnerHTML(this._lblCurrency_PriceLabel_IPO,n)},showHideQuantity:function(n){n==!0?($("#"+this.getFormControlID(this._element.id,"txtQuantity")).prop("disabled",!0),$find(this.getFormControlID(this._element.id,"chkFullQuantityReceived")).enableButton(!1)):this.getControlValue(this.getFormControlID(this._element.id,"chkFullQuantityReceived"),"CheckBox")!=!0&&($("#"+this.getFormControlID(this._element.id,"txtQuantity")).prop("disabled",!1),$find(this.getFormControlID(this._element.id,"chkFullQuantityReceived")).enableButton(!0))},getFormControlID:function(n,t){return String.format("{0}_{1}",n,t)},populatePackagingBreakdownList:function(n){var t,i,r;if(this._tblPackagingBreakdown.clearTable(),n!=undefined&&n.length>0)for(t=0;t<n.length;t++)i=n[t],r=[$R_FN.setCleanTextValue(i.PackBreakdownType),'<label id="NumberOfPacks'+(t+1)+'" style="width: 85px;">'+i.NumberOfPacks+"<\/label>",'<label id="PackSize'+(t+1)+'" style="width: 85px;">'+i.PackSize+"<\/label>",'<label id="TotalPackSize'+(t+1)+'" style="width: 85px;">'+i.TotalPackSize+"<\/label>"],this._tblPackagingBreakdown.addRowRowColor(r,i.ID),i=null,r=null},populateDateCodeList:function(n){var i,t,r;if(this._tblDateCode.clearTable(),n!=undefined&&n.length>0)for(i=0;i<n.length;i++)t=n[i],r=[$R_FN.setCleanTextValue(t.DateCodes),$R_FN.setCleanTextValue(t.Quantity)],this._tblDateCode.addRowRowColor(r,t.ID),t=null,r=null},getGIQuery:function(){$R_FN.showElement(this._pnlLines,!1);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/GILines");n.set_DataObject("GILines");n.set_DataAction("GetGILineData");n.addParameter("ID",this._intGILineId);n.addDataOK(Function.createDelegate(this,this.getGIQueryOK));n.addError(Function.createDelegate(this,this.getGIQueryError));n.addTimeout(Function.createDelegate(this,this.getGIQueryError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},processFormLoadData:function(){this._IsPOHub?($("#"+this.getFormControlID(this._element.id,"lblShipInCost")).css("display","none"),$("#"+this.getFormControlID(this._element.id,"lblShipInCost")).css("display","none")):($("#"+this.getFormControlID(this._element.id,"lblShipInCost")).css("display",(this._blnCanEditShipInCost&&!this._blnGISplited)==!0?"block":"none"),$("#"+this.getFormControlID(this._element.id,"lblShipInCost")).css("display",!(this._blnCanEditShipInCost&&!this._blnGISplited)==!0?"block":"none"));this._blnGISplited==!1?this._blnRelatedToIPO?($("#"+this.getFormControlID(this._element.id,"txtPrice")).css("display",(this._blnCanEditPurchasePrice&&this._IsPOHub)==!0?"block":"none"),$("#"+this.getFormControlID(this._element.id,"lblCurrency_Price")).css("display",(this._blnCanEditPurchasePrice&&this._IsPOHub)==!0?"block":"none"),$("#"+this.getFormControlID(this._element.id,"lblPrice")).css("display",!this._blnCanEditPurchasePrice==!0?"block":"none"),$("#"+this.getFormControlID(this._element.id,"lblCurrency_PriceLabel")).css("display",!this._blnCanEditPurchasePrice==!0?"block":"none")):($("#"+this.getFormControlID(this._element.id,"txtPrice")).css("display",this._blnCanEditPurchasePrice==!0?"block":"none"),$("#"+this.getFormControlID(this._element.id,"lblCurrency_Price")).css("display",this._blnCanEditPurchasePrice==!0?"block":"none"),$("#"+this.getFormControlID(this._element.id,"lblPrice")).css("display",!this._blnCanEditPurchasePrice==!0?"block":"none"),$("#"+this.getFormControlID(this._element.id,"lblCurrency_PriceLabel")).css("display",!this._blnCanEditPurchasePrice==!0?"block":"none")):this._blnRelatedToIPO?($("#"+this.getFormControlID(this._element.id,"txtPrice")).css("display",(this._blnCanEditPurchasePrice&&this._IsPOHub&&!this._blnGISplited)==!0?"block":"none"),$("#"+this.getFormControlID(this._element.id,"lblCurrency_Price")).css("display",(this._blnCanEditPurchasePrice&&this._IsPOHub&&!this._blnGISplited)==!0?"block":"none"),$("#"+this.getFormControlID(this._element.id,"lblPrice")).css("display",!(this._blnCanEditPurchasePrice&&this._IsPOHub&&!this._blnGISplited)==!0?"block":"none"),$("#"+this.getFormControlID(this._element.id,"lblCurrency_PriceLabel")).css("display",!(this._blnCanEditPurchasePrice&&this._IsPOHub&&!this._blnGISplited)==!0?"block":"none")):($("#"+this.getFormControlID(this._element.id,"txtPrice")).css("display",(this._blnCanEditPurchasePrice&&!this._blnGISplited)==!0?"block":"none"),$("#"+this.getFormControlID(this._element.id,"lblCurrency_Price")).css("display",(this._blnCanEditPurchasePrice&&!this._blnGISplited)==!0?"block":"none"),$("#"+this.getFormControlID(this._element.id,"lblPrice")).css("display",!(this._blnCanEditPurchasePrice&&!this._blnGISplited)==!0?"block":"none"),$("#"+this.getFormControlID(this._element.id,"lblCurrency_PriceLabel")).css("display",!(this._blnCanEditPurchasePrice&&!this._blnGISplited)==!0?"block":"none"));this._blnGISplited==!1?($("#"+this.getFormControlID(this._element.id,"txtPrice_IPO")).css("display",(this._blnCanEditPurchasePrice&&this._blnRelatedToIPO&&!this._IsPOHub)==!0?"block":"none"),$("#"+this.getFormControlID(this._element.id,"lblCurrency_Price_IPO")).css("display",(this._blnCanEditPurchasePrice&&this._blnRelatedToIPO&&!this._IsPOHub)==!0?"block":"none"),$("#"+this.getFormControlID(this._element.id,"lblPrice_IPO")).css("display",!this._blnCanEditPurchasePrice==!0?"block":"none"),$("#"+this.getFormControlID(this._element.id,"lblCurrency_PriceLabel_IPO")).css("display",!this._blnCanEditPurchasePrice==!0?"block":"none")):($("#"+this.getFormControlID(this._element.id,"txtPrice_IPO")).css("display",(this._blnCanEditPurchasePrice&&this._blnRelatedToIPO&&!this._IsPOHub&&!this._blnGISplited)==!0?"block":"none"),$("#"+this.getFormControlID(this._element.id,"lblCurrency_Price_IPO")).css("display",(this._blnCanEditPurchasePrice&&this._blnRelatedToIPO&&!this._IsPOHub&&!this._blnGISplited)==!0?"block":"none"),$("#"+this.getFormControlID(this._element.id,"lblPrice_IPO")).css("display",!this._blnCanEditPurchasePrice==!0?"block":"none"),$("#"+this.getFormControlID(this._element.id,"lblCurrency_PriceLabel_IPO")).css("display",!this._blnCanEditPurchasePrice==!0?"block":"none"));$find(this.getFormControlID(this._element.id,"chkReqSerailNo")).enableButton(!(this._intSerialNoCount>0));$find(this.getFormControlID(this._element.id,"chkSerialNosRecorded")).enableButton(!(this._intSerialNoCount>0));this._blnGISplited==!1?this.showHideQuantity(this._blnSerNoRecorded):this.showHideQuantity(this._blnGISplited);$find(this.getFormControlID(this._element.id,"chkFullQuantityReceived")).enableButton(!1);$find(this.getFormControlID(this._element.id,"chkPartNumberCorrect")).enableButton(!1);$find(this.getFormControlID(this._element.id,"chkManufacturerCorrect")).enableButton(!1);$find(this.getFormControlID(this._element.id,"chkDateCodeCorrect")).enableButton(!1);$find(this.getFormControlID(this._element.id,"chkDateCodeRequired")).enableButton(!1);$find(this.getFormControlID(this._element.id,"chkPackageCorrect")).enableButton(!1);$find(this.getFormControlID(this._element.id,"chkMSLCorrect")).enableButton(!1);$find(this.getFormControlID(this._element.id,"chkHICCorrect")).enableButton(!1);$find(this.getFormControlID(this._element.id,"chkRohsStatusCorrect")).enableButton(!1);$find(this.getFormControlID(this._element.id,"chkReqSerailNo")).enableButton(!1);$find(this.getFormControlID(this._element.id,"chkLotCodeReq")).enableButton(!1);$find(this.getFormControlID(this._element.id,"chkEnhancedInpectionRequired")).enableButton(!1);$find(this.getFormControlID(this._element.id,"chkbakingYes")).enableButton(!1);$find(this.getFormControlID(this._element.id,"chkbakingNo")).enableButton(!1);$find(this.getFormControlID(this._element.id,"chkbakingNA")).enableButton(!1);$find(this.getFormControlID(this._element.id,"chkInspectionConducted")).enableButton(!1);$find(this.getFormControlID(this._element.id,"chkUnavailable")).enableButton(!1);$find(this.getFormControlID(this._element.id,"chkSerialNosRecorded")).enableButton(!1);$find(this.getFormControlID(this._element.id,"chkPrintHazWar")).enableButton(!1);$find(this.getFormControlID(this._element.id,"chkSales")).enableButton(!1);$find(this.getFormControlID(this._element.id,"chkPurchasing")).enableButton(!1);$find(this.getFormControlID(this._element.id,"chkQualityApproval")).enableButton(!1)},setFieldsFromGIQuery:function(n){n&&(this.setControlValue("Literal",this.getFormControlID(this._element.id,"lblGoodsIn"),n.GoodsInNumber,""),this.setControlValue("Literal",this.getFormControlID(this._element.id,"lblSupplier"),n.CompanyName,""),this.setControlValue("Literal",this.getFormControlID(this._element.id,"lblSupplierType"),n.CompanyType,""),this.setControlValue("Literal",this.getFormControlID(this._element.id,"lblAirWayBill"),n.AirWayBill,""),this.setControlValue("Literal",this.getFormControlID(this._element.id,"lblReference"),n.Reference,""),this.setControlValue("Literal",this.getFormControlID(this._element.id,"lblLocation"),n.Location,""),this.setControlValue("Literal",this.getFormControlID(this._element.id,"lblLot"),$R_FN.setCleanTextValue(n.LotName),""),this.setControlValue("TextBox",this.getFormControlID(this._element.id,"txtQualityControlNotes"),n.QCNotes,""),this.setControlValue("Literal",this.getFormControlID(this._element.id,"lblQuantityOrdered"),n.QuantityOrdered,""),this.setControlValue("TextBox",this.getFormControlID(this._element.id,"txtQuantity"),n.Quantity,""),this.setControlValue("Literal",this.getFormControlID(this._element.id,"lblPartNo"),n.Part,""),this.setControlValue("Literal",this.getFormControlID(this._element.id,"lblManufacturer"),n.Manufacturer,""),this.setControlValue("Literal",this.getFormControlID(this._element.id,"lblDateCode"),n.DateCode,""),this.setControlValue("Literal",this.getFormControlID(this._element.id,"lblPackage"),n.Package,""),this.setControlValue("Literal",this.getFormControlID(this._element.id,"lblCorrectPackage"),n.CorrectPackageName,""),this.setControlValue("Literal",this.getFormControlID(this._element.id,"lblMSL"),n.MSLLevel,""),this.setControlValue("Literal",this.getFormControlID(this._element.id,"lblCountryOfManufacture"),n.CountryOfManufactureName),this.setControlValue("Literal",this.getFormControlID(this._element.id,"lblCountingMethod"),n.CountingMethod),this.setControlValue("CheckBox",this.getFormControlID(this._element.id,"chkReqSerailNo"),n.ReqSeriaNo,""),this.setControlValue("CheckBox",this.getFormControlID(this._element.id,"chkLotCodeReq"),n.IsLotCodesReq,""),this.setControlValue("CheckBox",this.getFormControlID(this._element.id,"chkEnhancedInpectionRequired"),n.EnhancedInspectionReq,""),this.setControlValue("TextBox",this.getFormControlID(this._element.id,"txtGeneralInspectionNotes"),n.GeneralInspectionNotes,""),n.BakingLevelAdded==!0?this.setControlValue("CheckBox",this.getFormControlID(this._element.id,"chkbakingYes"),n.BakingLevelAdded,""):n.BakingLevelAdded=="false"?this.setControlValue("CheckBox",this.getFormControlID(this._element.id,"chkbakingNo"),!0,""):this.setControlValue("CheckBox",this.getFormControlID(this._element.id,"chkbakingNA"),!0,""),this.setControlValue("CheckBox",this.getFormControlID(this._element.id,"chkInspectionConducted"),n.IsInspectionConducted,""),this.setControlValue("Literal",this.getFormControlID(this._element.id,"lblReceivingNotes"),n.ReceivingNotes,""),this.setControlValue("TextBox",this.getFormControlID(this._element.id,"txtSupplierPart"),n.SupplierPart,""),this.setControlValue("Literal",this.getFormControlID(this._element.id,"lblProducts"),n.Product),this.setControlValue("TextBox",this.getFormControlID(this._element.id,"txtPrice"),n.Price,""),this.setControlValue("Literal",this.getFormControlID(this._element.id,"lblPrice"),n.Price,""),this.setControlValue("TextBox",this.getFormControlID(this._element.id,"txtPrice_IPO"),n.ClientPriceVal,""),this.setControlValue("Literal",this.getFormControlID(this._element.id,"lblPrice_IPO"),n.ClientPriceVal,""),this.setControlValue("TextBox",this.getFormControlID(this._element.id,"txtShipInCost"),n.ShipInCostActVal,""),this.setControlValue("Literal",this.getFormControlID(this._element.id,"lblShipInCost"),n.ShipInCostActVal,""),this.setControlValue("CheckBox",this.getFormControlID(this._element.id,"chkUnavailable"),n.Unavailable,""),this.setControlValue("CheckBox",this.getFormControlID(this._element.id,"chkSerialNosRecorded"),n.SerialNosRecorded,""),this.setControlValue("TextBox",this.getFormControlID(this._element.id,"txtPartMarkings"),n.PartMarkings,""),this.setControlValue("CheckBox",this.getFormControlID(this._element.id,"chkPrintHazWar"),n.IsPrintHaz,""),this.setControlValue("TextBox",this.getFormControlID(this._element.id,"txtLineNotes"),n.LineNotes,""),this.setControlValue("Literal",this.getFormControlID(this._element.id,"lblCorrectRohsStatus"),n.ROHSValue,""),this.setControlValue("TextBox",this.getFormControlID(this._element.id,"txtCorrectPartNo"),n.CorrectPartNo,""),this.setControlValue("TextBox",this.getFormControlID(this._element.id,"txtCorrectDateCode"),n.CorrectDateCode,""),this.setControlValue("TextBox",this.getFormControlID(this._element.id,"txtCorrectDateCode"),n.CorrectDateCode,""),this.setControlValue("Literal",this.getFormControlID(this._element.id,"lblPackage"),n.CorrectPackageName),this.setControlValue("Literal",this.getFormControlID(this._element.id,"lblCorrectMSL"),n.CorrectMSLLevel,""),this.setControlValue("TextBox",this.getFormControlID(this._element.id,"txtCorrectHIC"),n.CorrectHICStatus,""),this.setControlValue("DropDown",this.getFormControlID(this._element.id,"ddlROHSStatus"),"",n.CorrectROHSStatus),this.setControlValue("CheckBox",this.getFormControlID(this._element.id,"chkFullQuantityReceived"),n.IsFullQtyRecieved==!0?!0:!1,""),this.setControlValue("CheckBox",this.getFormControlID(this._element.id,"chkPartNumberCorrect"),n.IsPartNoCorrect==!0?!0:!1,""),this.setControlValue("CheckBox",this.getFormControlID(this._element.id,"chkManufacturerCorrect"),n.IsManufacturerCorrect==!0?!0:!1,""),this.setControlValue("CheckBox",this.getFormControlID(this._element.id,"chkDateCodeCorrect"),n.IsDateCodeCorrect==!0?!0:!1,""),this.setControlValue("CheckBox",this.getFormControlID(this._element.id,"chkPackageCorrect"),n.IsPackageTypeCorrect==!0?!0:!1,""),this.setControlValue("CheckBox",this.getFormControlID(this._element.id,"chkMSLCorrect"),n.IsMSLLevelCorrect==!0?!0:!1,""),this.setControlValue("CheckBox",this.getFormControlID(this._element.id,"chkHICCorrect"),n.IsHICStatusCorrect==!0?!0:!1,""),this.setControlValue("CheckBox",this.getFormControlID(this._element.id,"chkRohsStatusCorrect"),n.IsROHSStatusCorrect==!0?!0:!1,""),this.setControlValue("CheckBox",this.getFormControlID(this._element.id,"chkDateCodeRequired"),n.IsDateCodeRequired==!0?!0:!1,""),this.setControlValue("CheckBox",this.getFormControlID(this._element.id,"chkSales"),n.IsSalesNotify,""),this.setControlValue("CheckBox",this.getFormControlID(this._element.id,"chkPurchasing"),n.IsPurchaseNotify,""),this.setControlValue("CheckBox",this.getFormControlID(this._element.id,"chkQualityApproval"),n.IsQualityNotify,""),this.setControlValue("TextBox",this.getFormControlID(this._element.id,"txtGIAllQueries"),n.GIQuery.replaceAll(/((<br \/>)|(<br>)|(<br\/>))/gi,"\r\n"),""),this.setControlValue("CheckBox",this.getFormControlID(this._element.id,"chkPDFReportRequired"),n.IsPDFReportRequired==!0?!0:!1,""),this.setControlValue("CheckBox",this.getFormControlID(this._element.id,"chkQuarantineProduct"),n.IsQuarantineProduct==!0?!0:!1,""),$find(this.getFormControlID(this._element.id,"ddlQueryApprovedSales")).setValue(n.SalesApprovalStatus),$find(this.getFormControlID(this._element.id,"ddlQueryApprovedPurchase")).setValue(n.PurchaseApprovalStatus),$find(this.getFormControlID(this._element.id,"ddlQueryApprovedQuality")).setValue(n.QualityApprovalStatus),n.IsDateCodeRequired==!0?($("#ctl00_cphMain_ctlGILineNotify_ctlDB_ctl14_ctlNotify_ctlDB_tblDateCode").css("display","block"),$("#lblDateCodeReceived").css("display","block")):($("#ctl00_cphMain_ctlGILineNotify_ctlDB_ctl14_ctlNotify_ctlDB_tblDateCode").css("display","none"),$("#lblDateCodeReceived").css("display","none")),this.setControlValue("TextBox",this.getFormControlID(this._element.id,"txtPackageBreakdownInfo"),n.PKGBreakdownMismatch,""),this.enableApprovalDropDownByGroupName(n.LoginGroupMember,n),this.setCurrency(n.CurrencyID,n.CurrencyCode),this.setCurrencyIPO(n.ClientCurCode),this._blnCanEditShipInCost=this._blnCanEditShipInCost,this._blnCanEditPurchasePrice=this._blnCanEditPurchasePrice,this._blnRelatedToIPO=n.RelatedIPO,this._intIPOClientNo=n.IPOClientNo,this._poBankFee=n.POBankFee,this._intGlobalClientNo=-1,this._intSerialNoCount=n.SerialNoCount,this._blnSerNoRecorded=n.SerialNoCount===n.Quantity,this._blnProductHaza=n.IsProdHaz,this._StringDLUP=n.StringDLUP,this._blnGISplited=n.GISplitted,this._intManufacturerId=n.ManufacturerNo,this._intPackagingType=n.PackageNo,this._ctlMSL=n.MSL,this._intROHSValue=n.ROHS,this._intGIID=n.GoodsInId,this._intGINumber=n.GoodsInNumber,this._intUpdatedBy=n.UpdatedBy,this.populatePackagingBreakdownList(n.PackagingBreakdown),this.populateDateCodeList(n.DateCodeRequired),this.processFormLoadData())},getGIQueryOK:function(n){var t=n._result;this.setFieldsFromGIQuery(t)},getGIQueryError:function(n){this._strErrorMessage=n._errorMessage;this.showError(!0,this._strErrorMessage)},enableApprovalDropDownByGroupName:function(n,t){var e,f;if(n!=undefined&&n.length>0){var i=!1,r=!1,u=!1;for(e=0;e<n.length;e++)f=n[e],f.Name=="Sales"&&($("#ctl00_cphMain_ctlGILineNotify_ctlDB_ctl14_ctlNotify_ctlDB_trQueryApprovedSales").css("display",""),this.setControlValue("TextBox",this.getFormControlID(this._element.id,"GIQueryReply"),t.SalesQueryReply,""),i=!0),f.Name=="Purchase"&&($("#ctl00_cphMain_ctlGILineNotify_ctlDB_ctl14_ctlNotify_ctlDB_trQueryApprovedPurchase").css("display",""),this.setControlValue("TextBox",this.getFormControlID(this._element.id,"GIQueryReply"),t.PurchaseQueryReply,""),r=!0),f.Name=="Quality"&&($("#ctl00_cphMain_ctlGILineNotify_ctlDB_ctl14_ctlNotify_ctlDB_trQueryApprovedQuality").css("display",""),this.setControlValue("TextBox",this.getFormControlID(this._element.id,"GIQueryReply"),t.QualityQueryReply,""),u=!0),f=null;i==!0&&(this._intLoginType=1);r==!0&&(this._intLoginType=2);u==!0&&(this._intLoginType=3);i==!0&&r==!0&&(this._intLoginType=4);i==!0&&r==!0&&u==!0&&(this._intLoginType=5);r==!0&&u==!0&&(this._intLoginType=6);u==!0&&i==!0&&(this._intLoginType=7)}},createPDF:function(){var n=window.location.href,t=new URL(n),i=t.searchParams.get("gilId");$R_FN.openPrintWindow($R_ENUM$PrintObject.PrintGIScreenPDF,i);$("#ctl00_cphMain_ctlGILineNotify_ctlDB_ctl14_ctlNotify_ctlDB_chkPDFReportRequired").attr("class","imageCheckBoxDisabled")},ReportNPR:function(){var n=window.location.href,t=new URL(n),i=t.searchParams.get("gilId");$R_FN.openNPRPrintWindow(i)},uploadImage:function(){var n=window.location.href,t=new URL(n),i=t.searchParams.get("gilId");$R_FN.openGIImageWindow(i)}};Rebound.GlobalTrader.Site.Controls.Forms.GILineNotify_Notify.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.GILineNotify_Notify",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);