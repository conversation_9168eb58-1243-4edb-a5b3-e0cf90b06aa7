﻿using Rebound.GlobalTrader.DAL.SQLClient;
using Rebound.GlobalTrader.DAL;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Rebound.GlobalTrader.BLL
{

    public partial class KubCountryWiseSale : BizObject
    {
        #region Properties
        public System.String Countries { get; set; }
        public System.String NoOfSales { get; set; }
        public System.String ReSale { get; set; }
        public System.String UnShippedReSale { get; set; }
        public System.String UnShippedNoOfSales { get; set; }
        public System.String InvoiceId { get; set; }
        public System.String InvoiceNumber { get; set; }
        public System.String Quantity { get; set; }
        public System.String ShippingCost { get; set; }
        public System.String Freight { get; set; }
        public System.String LandedCost { get; set; }
        public System.String InvoiceValue { get; set; }
        public System.String TOTAL { get; set; }

        #endregion

        #region Methods
        /// <summary>
        /// getKub Details
        /// Calls [sp_Kub]
        /// </summary>
        public static List<KubCountryWiseSale> GetKubCountryWiseSaleDetails(System.String PartNo, System.Int32 ClientNo)
        {
            List<KubCountryWiseSale> lts = new List<KubCountryWiseSale>();
            List<KubCountryWiseSaleDetails> lstkubDetails = new List<KubCountryWiseSaleDetails>();
            SQLKubProvider objSQLKubProvider = new SQLKubProvider();
            try
            {
                lstkubDetails = objSQLKubProvider.ListKubCountryWiseSaleDetails(PartNo, ClientNo);
                if (lstkubDetails == null)
                {
                    return new List<KubCountryWiseSale>();
                }
                else
                {
                    foreach (KubCountryWiseSaleDetails objDetails in lstkubDetails)
                    {
                        KubCountryWiseSale obj = new KubCountryWiseSale();
                        obj.Countries = objDetails.Countries;
                        obj.NoOfSales = objDetails.NoOfSales;
                        obj.ReSale = objDetails.ReSale;
                        obj.UnShippedNoOfSales = objDetails.UnShippedNoOfSales;
                        obj.UnShippedReSale = objDetails.UnShippedReSale;

                        lts.Add(obj);
                    }
                }
                return lts;
            }
            catch (Exception ex)
            {
                throw ex;
            }
            finally
            {
                lts = null;
                lstkubDetails = null;
            }
        }

        public static List<KubCountryWiseSale> GetGPCalculationDetails(System.String PartNo, System.Int32 ClientNo)
        {
            List<KubCountryWiseSale> lts = new List<KubCountryWiseSale>();
            List<KubCountryWiseSaleDetails> lstkubDetails = new List<KubCountryWiseSaleDetails>();
            SQLKubProvider objSQLKubProvider = new SQLKubProvider();
            try
            {
                lstkubDetails = objSQLKubProvider.GetGPCalculationDetails(PartNo, ClientNo);
                if (lstkubDetails == null)
                {
                    return new List<KubCountryWiseSale>();
                }
                else
                {
                    foreach (KubCountryWiseSaleDetails objDetails in lstkubDetails)
                    {
                        KubCountryWiseSale obj = new KubCountryWiseSale();
                        obj.InvoiceId = objDetails.InvoiceId;
                        obj.InvoiceNumber = objDetails.InvoiceNumber;
                        obj.Quantity = objDetails.Quantity;
                        obj.ShippingCost = objDetails.ShippingCost;
                        obj.Freight = objDetails.Freight;
                        obj.LandedCost = objDetails.LandedCost;
                        obj.InvoiceValue = objDetails.InvoiceValue;
                        obj.TOTAL = objDetails.TOTAL;
                        lts.Add(obj);
                    }
                }
                return lts;
            }
            catch (Exception ex)
            {
                throw ex;
            }
            finally
            {
                lts = null;
                lstkubDetails = null;
            }
        }

        public static List<KubCountryWiseSale> GetGPLastSaleCalculationDetails(System.String PartNo, System.Int32 ClientNo)
        {
            List<KubCountryWiseSale> lts = new List<KubCountryWiseSale>();
            List<KubCountryWiseSaleDetails> lstkubDetails = new List<KubCountryWiseSaleDetails>();
            SQLKubProvider objSQLKubProvider = new SQLKubProvider();
            try
            {
                lstkubDetails = objSQLKubProvider.GetGPLastSaleCalculationDetails(PartNo, ClientNo);
                if (lstkubDetails == null)
                {
                    return new List<KubCountryWiseSale>();
                }
                else
                {
                    foreach (KubCountryWiseSaleDetails objDetails in lstkubDetails)
                    {
                        KubCountryWiseSale obj = new KubCountryWiseSale();
                        obj.InvoiceId = objDetails.InvoiceId;
                        obj.InvoiceNumber = objDetails.InvoiceNumber;
                        obj.Quantity = objDetails.Quantity;
                        obj.ShippingCost = objDetails.ShippingCost;
                        obj.Freight = objDetails.Freight;
                        obj.LandedCost = objDetails.LandedCost;
                        obj.InvoiceValue = objDetails.InvoiceValue;
                        obj.TOTAL = objDetails.TOTAL;
                        lts.Add(obj);
                    }
                }
                return lts;
            }
            catch (Exception ex)
            {
                throw ex;
            }
            finally
            {
                lts = null;
                lstkubDetails = null;
            }
        }
        #endregion
    }
}
