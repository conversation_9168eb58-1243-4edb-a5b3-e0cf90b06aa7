<%@ Control Language="C#" CodeBehind="ClientInvoiceMainInfo_Notify.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.ClientInvoiceMainInfo_Notify" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>

<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="false" AllowQuickHelp="false">

	<Links>
		<ReboundUI:IconButton ID="ibtnSend" runat="server" IconGroup="Nugget" IconTitleResource="Send" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
		<ReboundUI:IconButton ID="ibtnCancel" runat="server" IconGroup="Nugget" IconTitleResource="Cancel" IconCSSType="Cancel" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
	</Links>

	<Explanation><%=Functions.GetGlobalResource("FormExplanations", "ClientInvoiceMainInfo_Notify")%></Explanation>

	<Content>
	
		<ReboundUI_Table:Form id="frm" runat="server">
			<ReboundFormFieldCollection:SendMailMessage id="ctlSendMailMessage" runat="server" />
		</ReboundUI_Table:Form>
		
	</Content>
		
</ReboundUI_Form:DesignBase>
