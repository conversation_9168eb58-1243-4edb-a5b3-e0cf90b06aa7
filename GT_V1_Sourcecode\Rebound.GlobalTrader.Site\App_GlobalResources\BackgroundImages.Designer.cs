//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option or rebuild the Visual Studio project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.VisualStudio.Web.Application.StronglyTypedResourceProxyBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class BackgroundImages {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal BackgroundImages() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Resources.BackgroundImages", global::System.Reflection.Assembly.Load("App_GlobalResources"));
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Brick.
        /// </summary>
        internal static string brick {
            get {
                return ResourceManager.GetString("brick", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fern.
        /// </summary>
        internal static string fern {
            get {
                return ResourceManager.GetString("fern", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Flowers.
        /// </summary>
        internal static string flowers {
            get {
                return ResourceManager.GetString("flowers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fruit.
        /// </summary>
        internal static string fruit {
            get {
                return ResourceManager.GetString("fruit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Map.
        /// </summary>
        internal static string map {
            get {
                return ResourceManager.GetString("map", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Metal.
        /// </summary>
        internal static string metal {
            get {
                return ResourceManager.GetString("metal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Office.
        /// </summary>
        internal static string office {
            get {
                return ResourceManager.GetString("office", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Palm.
        /// </summary>
        internal static string palm {
            get {
                return ResourceManager.GetString("palm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Plants.
        /// </summary>
        internal static string plants {
            get {
                return ResourceManager.GetString("plants", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Silk.
        /// </summary>
        internal static string silk {
            get {
                return ResourceManager.GetString("silk", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sky.
        /// </summary>
        internal static string sky {
            get {
                return ResourceManager.GetString("sky", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Snowflake.
        /// </summary>
        internal static string snowflake {
            get {
                return ResourceManager.GetString("snowflake", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Spark.
        /// </summary>
        internal static string spark {
            get {
                return ResourceManager.GetString("spark", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sun.
        /// </summary>
        internal static string sun {
            get {
                return ResourceManager.GetString("sun", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sunflowers.
        /// </summary>
        internal static string sunflowers {
            get {
                return ResourceManager.GetString("sunflowers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Wood.
        /// </summary>
        internal static string wood {
            get {
                return ResourceManager.GetString("wood", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Christmas Lights.
        /// </summary>
        internal static string xmaslights {
            get {
                return ResourceManager.GetString("xmaslights", resourceCulture);
            }
        }
    }
}
