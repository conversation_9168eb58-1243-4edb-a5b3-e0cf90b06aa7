Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.CompanyInsuranceCertificate_Add=function(n){Rebound.GlobalTrader.Site.Controls.Forms.CompanyInsuranceCertificate_Add.initializeBase(this,[n]);this._intCompanyID=0};Rebound.GlobalTrader.Site.Controls.Forms.CompanyInsuranceCertificate_Add.prototype={get_intCompanyID:function(){return this._intCompanyID},set_intCompanyID:function(n){this._intCompanyID!==n&&(this._intCompanyID=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.CompanyInsuranceCertificate_Add.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown))},dispose:function(){this.isDisposed||(this._intCompanyID=null,Rebound.GlobalTrader.Site.Controls.Forms.CompanyInsuranceCertificate_Add.callBaseMethod(this,"dispose"))},formShown:function(){this._blnFirstTimeShown&&(this.addSave(Function.createDelegate(this,this.saveClicked)),$find(this.getField("ctlCategory").ControlID).addChanged(Function.createDelegate(this,this.getCertificateByCategory)),this._strPathToData="controls/Nuggets/CompanyInsuranceCertificate",this._strDataObject="CompanyInsuranceCertificate");this.getFieldDropDownData("ctlCategory");this.getCertificateByCategory();this.setFormFieldsToDefaults()},getCertificateByCategory:function(){this.showCertificateFieldsLoading(!0);this.getFieldComponent("ctlCertificate")._intCategoryID=this.getFieldValue("ctlCategory");this.getFieldDropDownData("ctlCertificate");this.showCertificateFieldsLoading(!1)},showCertificateFieldsLoading:function(n){this.showFieldLoading("ctlCertificate",n)},saveClicked:function(){this.resetFormFields();this.validateForm()&&this.addNew()},validateForm:function(){var n=!0;return n=this.autoValidateFields(),n||this.showError(!0),n},addNew:function(){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("AddNew");n.addParameter("id",this._intCompanyID);n.addParameter("Category",this.getFieldValue("ctlCategory"));n.addParameter("Certificate",this.getFieldValue("ctlCertificate"));n.addParameter("Number",this.getFieldValue("ctlCertificateNumbre"));n.addParameter("StartDate",this.getFieldValue("ctlStartDate"));n.addParameter("ExpiryDate",this.getFieldValue("ctlExpiryDate"));n.addParameter("InActive",this.getFieldValue("ctlInActive"));n.addParameter("Desc",this.getFieldValue("ctlDescription"));n.addDataOK(Function.createDelegate(this,this.saveEditOK));n.addError(Function.createDelegate(this,this.saveEditError));n.addTimeout(Function.createDelegate(this,this.saveEditError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},saveEditError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},saveEditOK:function(n){n._result.Result==!0?(this._intLineID=n._result.NewID,this.onSaveComplete()):this.saveEditError(n)}};Rebound.GlobalTrader.Site.Controls.Forms.CompanyInsuranceCertificate_Add.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CompanyInsuranceCertificate_Add",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);