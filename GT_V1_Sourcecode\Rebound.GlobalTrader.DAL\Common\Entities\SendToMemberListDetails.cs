﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Rebound.GlobalTrader.DAL
{
    public class SendToMemberListDetails
    {
        #region Constructors

        public SendToMemberListDetails() { }

        #endregion

        #region Properties
        /// <summary>
        /// LoginNo (from Table)
        /// </summary>
        
        public System.String LoginNo { get; set; }
        public System.String SecurityGroupNo { get; set; }
        #endregion


    }
}
