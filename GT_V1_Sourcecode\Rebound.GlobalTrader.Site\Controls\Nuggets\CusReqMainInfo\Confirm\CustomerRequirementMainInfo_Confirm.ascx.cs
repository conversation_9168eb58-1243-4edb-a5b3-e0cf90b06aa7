using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.Forms {
    public partial class CustomerRequirementMainInfo_Confirm : Base
    {
		#region Locals
        //protected IconButton _ibtnBack;

		#endregion

		#region Properties

        private int _intCustomerRequirementID;
        public int CustomerRequirementID
        {
            get
            {
                return _intCustomerRequirementID;
            }
            set
            {
                _intCustomerRequirementID = value;
            }
        }

		#endregion

		#region Overrides

		/// <summary>
		/// OnInit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
			//TitleText = Functions.GetGlobalResource("FormTitles", "Confirm");
            AddScriptReference("Controls.Nuggets.CusReqMainInfo.Confirm.CustomerRequirementMainInfo_Confirm");            
		}

		/// <summary>
		/// OnPreRender
		/// </summary>
		/// <param name="e"></param>
		protected override void OnPreRender(EventArgs e) {
            //WireUpButtons();
			SetupScriptDescriptors();
			base.OnPreRender(e);
		}

		#endregion

		/// <summary>
		/// Setup Script Descriptors
		/// </summary>
		private void SetupScriptDescriptors() {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Forms.Confirm", ctlDesignBase.ClientID);
            //_scScriptControlDescriptor.AddElementProperty("ibtnBack", FindIconButton("ibtnBack").ClientID);
            _scScriptControlDescriptor.AddProperty("intCustomerRequirementID", _intCustomerRequirementID);
		}
        private void WireUpButtons()
        {
            //_ibtnBack = (IconButton)FindIconButton("ibtnBack");
        }
	}
}