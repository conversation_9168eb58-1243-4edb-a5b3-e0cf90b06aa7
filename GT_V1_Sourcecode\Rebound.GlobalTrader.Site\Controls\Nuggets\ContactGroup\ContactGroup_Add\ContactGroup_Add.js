Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.ContactGroup_Add=function(n){Rebound.GlobalTrader.Site.Controls.Forms.ContactGroup_Add.initializeBase(this,[n]);this._strContactType="";this._arrPOLineIds=[]};Rebound.GlobalTrader.Site.Controls.Forms.ContactGroup_Add.prototype={get_ibtnSearch:function(){return this._ibtnSearch},set_ibtnSearch:function(n){this._ibtnSearch!==n&&(this._ibtnSearch=n)},get_ibtnReset:function(){return this._ibtnReset},set_ibtnReset:function(n){this._ibtnReset!==n&&(this._ibtnReset=n)},get_strContactType:function(){return this._strContactType},set_strContactType:function(n){this._strContactType!==n&&(this._strContactType=n)},get_tbl:function(){return this._tbl},set_tbl:function(n){this._tbl!==n&&(this._tbl=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.ContactGroup_Add.callBaseMethod(this,"initialize");this.addCancel(Function.createDelegate(this,this.cancelClicked))},cancelClicked:function(){window.location.href="Con_ManufacturerBrowse.aspx"},dispose:function(){this.isDisposed||(this._tbl&&this._tbl.dispose(),this._tbl=null,this._strContactType=null,Rebound.GlobalTrader.Site.Controls.Forms.ContactGroup_Add.callBaseMethod(this,"dispose"))},getData:function(){alert("inside click")},getDataOK:function(n){var o=n._result,r=n._result,i,u;if(r.Items)for(i=0;i<r.Items.length;i++){var t=r.Items[i],f=[this.writeCheckbox(t.LineID,i,tbl),$R_FN.setCleanTextValue(t.Name),$R_FN.setCleanTextValue(t.Code)],s={Inactive:t.Inactive},e=t.Inactive?"ceased":"";this._tbl.addRow(f,t.LineID,null,null,e);this.registerCheckBox(t.LineID,i,!1,!0,tbl);u=this.getCheckBox(i,tbl);u._element.setAttribute("onClick",String.format('$find("{0}").getCheckedCellValue({1},{2});',this._element.id,i,t.LineID));t=null}this._tbl.resizeColumns();this.showContent(!0);this.showContentLoading(!1)},writeCheckbox:function(n,t,i){var r=this.getControlID("chk",t,i),u=this.getControlID("chkImg",t,i);return String.format('<div class="imageCheckBoxDisabled" id="{0}" ><img id="{1}" class="{2}" src="images/x.gif" style="border-width: 0px;" /> <\/div>',r,u,"off")},getControlID:function(n,t,i){return String.format("{0}_{1}{2}",i._element.id,n,t)},getCheckBox:function(n,t){return $find(this.getControlID("chk",n,t))},getCheckedCellValue:function(n,t){var i=this._tbl,r=this.getCheckBox(n,i),u=r._blnChecked,f=i._tbl.rows[n];f&&(u==!0?(this.clearMessages(),Array.add(this._arrPOLineIds,t)):Array.remove(this._arrPOLineIds,t))},getDataError:function(n){this.showError(!0,n.get_ErrorMessage())},saveEdit:function(){},cancelEdit:function(){this.hideEditForm();this.showContent(!0)},showEditForm:function(){},hideEditForm:function(){this.showForm(this._frmEdit,!1)},saveEditComplete:function(){},saveEditError:function(){this.showError(!0,this._frmEdit._strErrorMessage)}};Rebound.GlobalTrader.Site.Controls.Forms.ContactGroup_Add.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.ContactGroup_Add",Rebound.GlobalTrader.Site.Controls.Forms.Base);