﻿/*Backup schema of [BorisGlobalTraderImports].[dbo].[tbPriceQuoteImport_tempData]*/
ALTER TABLE [BorisGlobalTraderImports].[dbo].[tbPriceQuoteImport_tempData] DROP CONSTRAINT [DF_tbPriceQuoteImport_tempData_ImportDate]
GO

ALTER TABLE [BorisGlobalTraderImports].[dbo].[tbPriceQuoteImport_tempData] DROP CONSTRAINT [DF_tbPriceQuoteImport_tempData_ROHS]
GO

ALTER TABLE [BorisGlobalTraderImports].[dbo].[tbPriceQuoteImport_tempData] DROP CONSTRAINT [DF_tbPriceQuoteImport_tempData__SupplierCost]
GO

ALTER TABLE [BorisGlobalTraderImports].[dbo].[tbPriceQuoteImport_tempData] DROP CONSTRAINT [DF_tbPriceQuoteImport_tempData_Quantity]
GO

/****** Object:  Table [dbo].[tbPriceQuoteImport_tempData]    Script Date: 6/28/2024 10:01:34 AM ******/
IF  EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[BorisGlobalTraderImports].[dbo].[tbPriceQuoteImport_tempData]') AND type in (N'U'))
DROP TABLE [BorisGlobalTraderImports].[dbo].[tbPriceQuoteImport_tempData]
GO

/****** Object:  Table [dbo].[tbPriceQuoteImport_tempData]    Script Date: 6/28/2024 10:01:34 AM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [BorisGlobalTraderImports].[dbo].[tbPriceQuoteImport_tempData](
	[PriceQuoteImportId] [int] IDENTITY(1,1) NOT NULL,
	[Column1] [nvarchar](max) NULL,
	[Column2] [nvarchar](max) NULL,
	[Column3] [nvarchar](max) NULL,
	[Column4] [nvarchar](max) NULL,
	[Column5] [nvarchar](max) NULL,
	[Column6] [nvarchar](max) NULL,
	[Column7] [nvarchar](max) NULL,
	[Column8] [nvarchar](max) NULL,
	[Column9] [nvarchar](max) NULL,
	[Column10] [nvarchar](max) NULL,
	[Column11] [nvarchar](max) NULL,
	[Column12] [nvarchar](max) NULL,
	[Column13] [nvarchar](100) NULL,
	[Column14] [nvarchar](100) NULL,
	[Column15] [nvarchar](max) NULL,
	[Column16] [nvarchar](200) NULL,
	[Column17] [nvarchar](200) NULL,
	[Column18] [nvarchar](max) NULL,
	[Column19] [nvarchar](max) NULL,
	[OriginalFilename] [nvarchar](100) NULL,
	[GeneratedFilename] [nvarchar](100) NULL,
	[ClientId] [int] NULL,
	[SelectedClientId] [int] NULL,
	[CreatedBy] [int] NULL,
	[ImportDate] [datetime] NULL,
	[Column20] [varchar](max) NULL,
	[Column21] [varchar](max) NULL,
	[Column22] [varchar](max) NULL,
	[Column23] [varchar](max) NULL,
	[Column24] [varchar](100) NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

ALTER TABLE [BorisGlobalTraderImports].[dbo].[tbPriceQuoteImport_tempData] ADD  CONSTRAINT [DF_tbPriceQuoteImport_tempData_Quantity]  DEFAULT ('0') FOR [Column4]
GO

ALTER TABLE [BorisGlobalTraderImports].[dbo].[tbPriceQuoteImport_tempData] ADD  CONSTRAINT [DF_tbPriceQuoteImport_tempData__SupplierCost]  DEFAULT ('0') FOR [Column9]
GO

ALTER TABLE [BorisGlobalTraderImports].[dbo].[tbPriceQuoteImport_tempData] ADD  CONSTRAINT [DF_tbPriceQuoteImport_tempData_ROHS]  DEFAULT ((0)) FOR [Column16]
GO

ALTER TABLE [BorisGlobalTraderImports].[dbo].[tbPriceQuoteImport_tempData] ADD  CONSTRAINT [DF_tbPriceQuoteImport_tempData_ImportDate]  DEFAULT (getdate()) FOR [ImportDate]
GO


