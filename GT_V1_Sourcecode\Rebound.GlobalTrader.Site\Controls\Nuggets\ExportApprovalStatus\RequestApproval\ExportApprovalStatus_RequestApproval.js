Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.ExportApprovalStatus_RequestApproval=function(n){Rebound.GlobalTrader.Site.Controls.Forms.ExportApprovalStatus_RequestApproval.initializeBase(this,[n]);this._strSONumber="";this._strRequestSubject="";this._strRequestBody=""};Rebound.GlobalTrader.Site.Controls.Forms.ExportApprovalStatus_RequestApproval.prototype={get_strSONumber:function(){return this._strSONumber},set_strSONumber:function(n){this._strSONumber!==n&&(this._strSONumber=n)},get_intCompanyID:function(){return this._intCompanyID},set_intCompanyID:function(n){this._intCompanyID!==n&&(this._intCompanyID=n)},get_intSalesOrderLineID:function(){return this._intSalesOrderLineID},set_intSalesOrderLineID:function(n){this._intSalesOrderLineID!==n&&(this._intSalesOrderLineID=n)},get_ibtnSend:function(){return this._ibtnSend},set_ibtnSend:function(n){this._ibtnSend!==n&&(this._ibtnSend=n)},get_ibtnSend_Footer:function(){return this._ibtnSend_Footer},set_ibtnSend_Footer:function(n){this._ibtnSend_Footer!==n&&(this._ibtnSend_Footer=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.ExportApprovalStatus_RequestApproval.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown));$R_IBTN.addClick(this._ibtnSend,Function.createDelegate(this,this.sendApprovalRequest));$R_IBTN.addClick(this._ibtnSend_Footer,Function.createDelegate(this,this.sendApprovalRequest))},formShown:function(){this.getExportApprovalData();this._blnFirstTimeShown&&(this._ctlRequestApproval=$find(this.getField("ctlRequestApproval").ID),this._ctlRequestApproval._ctlRelatedForm=this,this._ctlRequestApproval.SetApproverType("Export Approval"),this._ctlRequestApproval.SetDocID(this._intSalesOrderLineID),this._ctlRequestApproval.addNewLoginRecipient(1,"Abhinav Saxena"),$("#ctl00_cphMain_ctlExportApprovalStatus_ctlDB_ctl14_frmSendApproval_ctlDB_ctlRequestApproval_ctlTo").hide(),this.ShowSONotifyer(),this.addFieldCheckBoxClickEvent("ctlSendMail",Function.createDelegate(this,this.ShowSONotifyer)))},dispose:function(){this.isDisposed||(this._ibtnSend&&$R_IBTN.clearHandlers(this._ibtnSend),this._ibtnSend_Footer&&$R_IBTN.clearHandlers(this._ibtnSend_Footer),this._ctlRequestApproval&&this._ctlRequestApproval.dispose(),this._ctlRequestApproval=null,this._strSONumber=null,this._intCompanyID=null,this._intSalesOrderLineID=null,this._ibtnSend=null,this._ibtnSend_Footer=null,Rebound.GlobalTrader.Site.Controls.Forms.ExportApprovalStatus_RequestApproval.callBaseMethod(this,"dispose"))},getMessageText:function(){this.getMessageTextComplete()},getMessageTextComplete:function(){this._ctlRequestApproval.setValue_Body(this._strRequestBody);this._ctlRequestApproval.setValue_Subject(String.format(this._strRequestSubject))},getExportApprovalData:function(){this.showLoading(!0);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/ExportApprovalStatus");n.set_DataObject("ExportApprovalStatus");n.set_DataAction("GetExportApprovalData");n.addParameter("id",this._intSalesOrderLineID);n.addDataOK(Function.createDelegate(this,this.getExportApprovalDataOK));n.addError(Function.createDelegate(this,this.getExportApprovalDataError));n.addTimeout(Function.createDelegate(this,this.getExportApprovalDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getExportApprovalDataError:function(){this.showInnerContent(!0);this.showLoading(!1)},getExportApprovalDataOK:function(n){var t=n._result;this._strRequestSubject=t.SubJect;this._strRequestBody="Hi\nBelow are the information for your review";this._strRequestBody+="\n\nSales Person: "+t.SalesmanName;this._strRequestBody+="\nSales Order: "+t.SalesOrderNo;this._strRequestBody+="\nSO Line Number: "+t.SOSerialNo;this._strRequestBody+="\nCustomer: "+t.CustomerName;this._strRequestBody+="\nPart Number: "+t.Part;this._strRequestBody+="\nShip From Warehouse (Country): "+t.ShipFromWarehouse;this._strRequestBody+="\nShip From Country (Supplier): "+t.ShipFromCountry;this._strRequestBody+="\nCountry of Origin: "+t.CountryOfOrigin;this._strRequestBody+="\nShip to Customer Name: "+t.ShipToCustomerName;this._strRequestBody+="\nShip to Customer Country: "+t.ShipToCustomerCountry;this._strRequestBody+="\nCommodity Code: "+t.CommodityCode;this._strRequestBody+="\nECCN: "+(t.ECCN===""?"Blank":t.ECCN);this._strRequestBody+="\nDestination Country: "+t.DestinationCountry;this._strRequestBody+="\nMilitary Use?: "+t.MilitaryUseName;this._strRequestBody+="\nEnd User: "+t.EndUser;this._strRequestBody+="\nPart Application: "+t.PartApplication;this._strRequestBody+="\nIf Export Control is required, has the supplier verified they have the appropriate license: "+(t.ExportControl===0?"No":"Yes");this._strRequestBody+="\nAerospace Use: "+(t.AerospaceUse===0?"No":"Yes");this._strRequestBody+="\nWill the parts be tested and where (Please also advise Company Test House name): "+t.PartTested;this.getMessageText();this.showInnerContent(!0);this.showLoading(!1)},sendApprovalRequest:function(){if(this.validateForm()){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/ExportApprovalStatus");n.set_DataObject("ExportApprovalStatus");n.set_DataAction("SendExportApprovalRequest");n.addParameter("id",this._intSalesOrderLineID);n.addParameter("Subject",this._ctlRequestApproval.getValue_Subject());n.addParameter("Message",this._ctlRequestApproval.getValue_Body());n.addParameter("ApproverIds",$R_FN.arrayToSingleString(this._ctlRequestApproval._aryRecipientLoginIDs));n.addParameter("IsNotifySales",this.getFieldValue("ctlSendMail"));n.addDataOK(Function.createDelegate(this,this.sendApprovalRequestComplete));n.addError(Function.createDelegate(this,this.sendApprovalRequesError));n.addTimeout(Function.createDelegate(this,this.sendApprovalRequesError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null;this.onSave()}},validateForm:function(){var n=this._ctlRequestApproval.validateFields();return n||this.showError(!0),n},sendApprovalRequesError:function(n){this.showSaving(!1);this._strErrorMessage=n._errorMessage;this.onSaveError()},sendApprovalRequestComplete:function(){this.showSavedOK(!0);this.onSaveComplete();setTimeout(function(){$("#ctl00_cphMain_ctlDragDropForSOR_ctlDB_imgRefresh").trigger("click")},1e3)},ShowSONotifyer:function(){var t=this.getFieldValue("ctlSendMail"),n;t?(n=new Rebound.GlobalTrader.Site.Data,n.set_PathToData("controls/Nuggets/ExportApprovalStatus"),n.set_DataObject("ExportApprovalStatus"),n.set_DataAction("ShowSONotifyerForExportApproval"),n.addParameter("id",this._intSalesOrderLineID),n.addDataOK(Function.createDelegate(this,this.ShowSONotifyerOk)),n.addError(Function.createDelegate(this,this.sendApprovalRequesError)),n.addTimeout(Function.createDelegate(this,this.sendApprovalRequesError)),$R_DQ.addToQueue(n),$R_DQ.processQueue(),n=null):$("#lblsoNotifyer").html("")},ShowSONotifyerOk:function(n){var t=n._result;$("#lblsoNotifyer").html(t.SoNotifyersName);this.enableFieldCheckBox("ctlSendMail",t.IsAllowCheckSoNotify)},ShowSONotifyerError:function(){}};Rebound.GlobalTrader.Site.Controls.Forms.ExportApprovalStatus_RequestApproval.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.ExportApprovalStatus_RequestApproval",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);