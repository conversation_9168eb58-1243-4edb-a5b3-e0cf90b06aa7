
/****** Object:  StoredProcedure [dbo].[usp_selectAll_CustomerRequirement_for_BOMManager]    Script Date: 4/22/2024 11:07:35 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [dbo].[usp_selectAll_CustomerRequirement_for_BOMManager]                                                 
@BOMManagerNo int ,                                                                                      
@ClientID int = 0,                                                  
@IsPoHub bit  = 0,                                            
@Part  VARCHAR(100) = null                                  
, @curPage int =1                                  
, @Rpp int =10                          
,@ReqType int = 0                
AS                                                            
BEGIN                                                          
                
                
if(@ReqType = 1 )                
begin                 
                
select  d.*,e.GeneratedFilename,         '' as POHubReleaseName,      
'1' PackageName, '1' ProductName,'1' CurrencyCode,'1' SalesmanName,'1' ManufacturerCode,'1' CompanyName,'1' ,'1' BOMManagerHeader,'1' BOMManagerFullName,                
'1' SourcingResult,'1' RequestToPOHubBy,'1' BOMManagerFullName,'1' ConvertedTargetValue,'1' BOMManagerCurrencyCode,'1' ClientName,                
'1' MSL,'1' BOMManagerCode,'1' AllSorcingHasDelDate,'1' AllSorcingHasProduct,'1' SourcingResult,'1' BOMManagerStatus,'1' Line                
,'1' LineValue,'1' ManufacturerName,'1' DateCode,'1' Price,'1' HasHubSourcingResult,'1' IsPrimarySourcing,'1' IsPrimarySourcing,'1' QuoteGenerated,                
'1' AutoSourcingStatus,'1' ReqStatusText,'1' TotalRecords,'1' OfferCount                
,'1' UpdateByPH,'1' SupportTeamMemberName,'1' QuoteId,a.QuoteNumber QuoteNo  ,'0' as productid, '' as ProductDescription               
from tbQuote a join tbQuoteLine b on a.QuoteId = b.QuoteNo                
join tbSourcingResult c on b.SourcingResultNo = c.SourcingResultId                 
join tbCustomerRequirement d on c.CustomerRequirementNo = d.CustomerRequirementId                
join tbBOMManager e on d.BOMManagerNo = e.BOMManagerId                
where a.QuoteId = @BOMManagerNo                 
                
return                 
end                
                
                
                                                          
DECLARE @AllSorcingHasDelDate INT                                                                  
DECLARE @AllSorcingHasProduct INT                                                                        
 Declare @SourcingResult int                                                                    
 Declare @SourcingResultId int                                                                 
                                                                   
SELECT @AllSorcingHasDelDate = count(*) FROM tbCustomerRequirement cr                                                                         
JOIN tbBOMManager bm ON bm.BOMManagerId = cr.BOMManagerNo                                                                         
JOIN tbSourcingResult sr on cr.CustomerRequirementId = sr.CustomerRequirementNo                                                                        
WHERE bm.BOMManagerId = @BOMManagerNo AND sr.SourcingTable in ('PQ','OFPH','EXPH') and  sr.DeliveryDate IS NULL                                                                  
                                                                    
SELECT @AllSorcingHasProduct = count(*) FROM tbCustomerRequirement cr                                                                         
JOIN tbBOMManager bm ON bm.BOMManagerId = cr.BOMManagerNo                                                                         
JOIN tbSourcingResult sr on cr.CustomerRequirementId = sr.CustomerRequirementNo                                                                        
WHERE bm.BOMManagerId = @BOMManagerNo AND sr.SourcingTable in ('PQ','OFPH','EXPH') and  sr.ProductNo IS NULL                                                                 
            
        declare @TotalRecords int, @skip int                                   
                                    
  select @TotalRecords = count(CustomerRequirementId) FROM tbCustomerRequirement cr                                
        WHERE cr.BOMManagerNo = @BOMManagerNo  and cr.FullPart = case when isnull(@Part,'')='' then cr.fullpart else @Part end                           
                                      
   set @skip = (@Rpp * (@curPage - 1))                                      
                                
   if (@skip >= @TotalRecords and @TotalRecords > 0)                                      
   begin                                      
    set @curPage = CAST(@TotalRecords/@Rpp as int)                                      
    set @skip = CAST((@Rpp * (@curPage - 1)) as int)                                       
   end                                      
                                   
                                                              
SELECT                                  
 cr.CustomerRequirementId                                                                    
 ,cr.CustomerRequirementNumber                                                                    
 ,cr.ClientNo                                                                    
 ,REPLACE(cr.FullPart, '"', '') AS FullPart                                                                    
 ,REPLACE(cr.Part, '"', '') AS Part                                                                    
 ,cr.ManufacturerNo                                                                    
 ,cr.DateCode                                                                    
 ,cr.PackageNo                                                                    
 ,cr.Quantity                                                                    
 ,cr.Price                                                                    
 ,cr.CurrencyNo                                                                    
 ,cr.Salesman                                                                    
 ,cr.DatePromised                                                                    
 ,cr.Instructions                                      
 ,cr.Notes                                      
 ,cr.CompanyNo                                                                    
 ,cr.Alternate                                                                    
 ,cr.CustomerPart                           
 ,cr.Closed                                                                    
 ,cr.ROHS                         
 ,cr.UpdatedBy                                        
 ,cr.DLUP                                                                    
 ,cr.FactorySealed                                                
 ,cr.MSL                                     
 ,cr.PartialQuantityAcceptable                                                           
 ,cr.Obsolete                                                          
 ,cr.LastTimeBuy                                                           
 ,cr.RefirbsAcceptable                                                           
 ,cr.TestingRequired                                                           
 ,cr.TargetSellPrice                                                           
 ,cr.CompetitorBestOffer                                                          
 ,cr.CustomerDecisionDate                                             
 ,cr.RFQClosingDate                                                           
 ,cr.QuoteValidityRequired                                                           
 ,cr.ReqType                                                           
 ,cr.OrderToPlace                                                           
 ,cr.ReqForTraceability                                                                     
 ,lg.EmployeeName AS SalesmanName                                                                    
 ,co.CompanyName                                                             
 ,cu.CurrencyCode                                                                    
 ,pr.ProductName                                                                    
 ,mf.ManufacturerCode                        
,mf.ManufacturerName                                              
 ,Sequence as Line             ,(cast(cr.Price as decimal(12,2)) * cr.Quantity) as LineValue                  
 ,pk.PackageName                                                                   
 ,ct.IsTraceability                                                           
 ,bom.BOMManagerName AS BOMManagerHeader                                            
 ,cr.BOMManagerNo                   
 ,cr.BOMName              
 ,cr.POHubReleaseBy                                                                    
 ,bom.RequestToPOHubBy                   
 ,bom.BOMManagerCode                                                                    
 ,bom.BOMManagerName AS BOMManagerFullName                                                                    
 ,bom.CurrencyNo AS BOMManagerCurrencyNo                                                             
 ,bom.DLUP AS BOMManagerDate                                                            
 ,bom.UpdateByPH                                                                       
 ,CASE bom.[Status]                                                                           
     WHEN  1 THEN 'NEW'                                                                          
     WHEN  2 THEN 'OPEN'                                                                          
  WHEN  3 THEN 'RPQ'                                                                          
     WHEN  4 THEN 'PARTIAL RELEASED'                                                                          
     WHEN  5 THEN 'RELEASED'                                                                 
  WHEN  6 THEN 'CLOSED'                                                                      
   END                                                                          
   as BOMManagerStatus                                                                   
  , c.ClientName                                                    
 --,  (SELECT TOP 1 SourcingResultId FROM tbSourcingResult sr WHERE sr.CustomerRequirementNo=cr.CustomerRequirementId ) AS SourcingResultId                                                                          
--, (SELECT TOP 1 POHubCompanyNo FROM tbSourcingResult WHERE CustomerRequirementNo=cr.CustomerRequirementId and POHubCompanyNo is not null ) AS POHubCompany                                                                             
, cob.CurrencyCode as BOMManagerCurrencyCode                                                                              
, dbo.ufn_convert_currency_value(cr.Price, cr.CurrencyNo, bom.CurrencyNo, bom.DLUP) AS ConvertedTargetValue                                                          
, @AllSorcingHasDelDate AS AllSorcingHasDelDate                                                     
, @AllSorcingHasProduct AS AllSorcingHasProduct                                                            
, 0 as  SourcingResult                         
, cr.HasClientSourcingResult                                                          
, cr.HasHubSourcingResult                                                                
 --,@SourcingResultId AS SourcingResult                                                     
 ,cr.IsNoBid                                                           
 ,cr.ExpediteDate                                                        
 , case when cr.Alternate= 1 and  cr.AlternateStatus is null then cast(1 as tinyint) else  cr.AlternateStatus end as  AlternateStatus                                                        
 -- 1-Alternate,2-Possible Alternate,3-Firm Alternate                                                       
 ,cr.SupportTeamMemberNo                                                    
 , stm.EmployeeName AS SupportTeamMemberName                                                   
 ,(                                                  
  --- query added by arpit 15.03.2022                                                  
  --- to get the row if any discripancy is found                          
  Select top 1 PriceIssueBuyAndSell from (                                                  
   Select case when A.ActualPrice >= A.Price then 1 else 0 end PriceIssueBuyAndSell from (                                                  
   SELECT                                       
    CASE WHEN sr.PartWatchMatch= 1 AND sr.POHubCompanyNo IS NOT NULL AND cr.ClientNo != 114 AND @IsPoHub =0 THEN 0 ELSE sr.Price END Price                                                                                
    , sr.ActualPrice                                                                                   
   FROM    dbo.tbSourcingResult sr                                                                                        
   WHERE   sr.CustomerRequirementNo = cr.CustomerRequirementId                                                                                                    
   AND (sr.PartWatchMatch=1 OR ((@IsPoHub = 0 AND (sr.IsReleased=1 OR sr.POHubCompanyNo IS NULL))  OR (@IsPoHub =1 AND NOT sr.POHubCompanyNo IS NULL)))                                                   
   AND ISNULL(sr.PartWatchMatch,0)=0                                                   
   ) A                                                  
 ) B where B.PriceIssueBuyAndSell = 1                                                  
 )  PriceIssueBuyAndSell  ,                                        
 case when (select top 1 tbsrc.IsPrimarySourcing from tbSourcingResult tbsrc where cr.CustomerRequirementId = tbsrc.CustomerRequirementNo and tbsrc.IsPrimarySourcing = 1      
 and cr.reqstatus >3 and isnull(tbsrc.IsSoftDelete,0)=0) =1                           
      
 then convert(bit,1)                                        
 else convert(bit,0) end as IsPrimarySourcing,                                        
  case when (select top 1  tbsrc.Closed from tbSourcingResult tbsrc where cr.CustomerRequirementId = tbsrc.CustomerRequirementNo and tbsrc.IsPrimarySourcing = 1  ) =1                 
  then convert(bit,1)                                        
 else convert(bit,0) end as QuoteGenerated,                                        
 (select top 1 tbqtln.quoteno from tbSourcingResult tbsrcrslt --on tbsrcrslt.CustomerRequirementNo = cr.CustomerRequirementId                                        
 join tbQuoteLine tbqtln on tbqtln.SourcingResultNo = tbsrcrslt.SourcingResultId                                         
 where tbsrcrslt.CustomerRequirementNo = cr.CustomerRequirementId ) as QuoteId,                                        
 (select top 1 tbqt.QuoteNumber from tbSourcingResult tbsrcrslt --on tbsrcrslt.CustomerRequirementNo = cr.CustomerRequirementId                                        
 join tbQuoteLine tbqtln on tbqtln.SourcingResultNo = tbsrcrslt.SourcingResultId                                         
 join tbQuote tbqt on tbqtln.QuoteNo = tbqt.QuoteId                                        
 where tbsrcrslt.CustomerRequirementNo = cr.CustomerRequirementId ) as QuoteNo,                                    
 cr.REQStatus,                                    
 case          
 --when ((select top 1  IsSoftDelete from tbSourcingResult tbasrc          
 -- join tbautosource tbauto on cr.bommanagerno=tbauto.bommanagerno and cr.CustomerRequirementId = tbauto.CustomerRequirementId         
 -- where tbasrc.CustomerRequirementNo = cr.CustomerRequirementId and isnobid=1 and REQStatus<2 and tbauto.isdeleted <>1)=1) then 'Recalled NoBid'          
 when ((select top 1  IsSoftDelete from tbSourcingResult tbasrc where tbasrc.CustomerRequirementNo = cr.CustomerRequirementId and REQStatus=3 )=1) then 'Recalled'          
 when (tbreqstts.[Name]) <> 'New' then tbreqstts.[Name]             
 when cr.isnobid = 1 then 'NoBid'          
          
 else ('Awaiting Offer') end as ReqStatusText ,                                    
 case when (select count(1) from tbautosource tbasrc where tbasrc.BOMManagerNo = bom.BOMManagerId and  tbasrc.customerrequirementid = cr.customerrequirementid                 
   and isnull(tbasrc.isdeleted,0)=0 )>0 then convert(bit,1)                                    
 else convert(bit,0) end as AutoSourcingStatus,                                  
 @TotalRecords as TotalRecords,                  
 CASE            
  WHEN (cr.ReqStatus = 3) THEN (select count(1) from tbAutoSource ar             
         where ar.CustomerRequirementId = cr.CustomerRequirementID and isnull(ar.isdeleted,0)=0 and ar.BomManagerNo = @BOMManagerNo)            
  WHEN (cr.ReqStatus > 3) THEN (Select Count(1) from tbSourcingResult sr join tbautosource tbasrc on sr.AutoSourceID=tbasrc.SourceId                
     where tbasrc.CustomerRequirementid = cr.CustomerRequirementId and isnull(tbasrc.isdeleted,0)=0 and isnull(sr.IsSoftDelete,0)=0)            
  ELSE '0'            
 END as OfferCount            
  ,bom.GeneratedFilename        
  ,(select EmployeeName from tblogin where loginid = cr.POHubReleaseBy) as POHubReleaseName    
  ,pr.Productid  
  ,pr.ProductName +' ->'+ pr.ProductDescription + ' ->'+ Pr.DutyCode +' ('+cast(ISNULL(dbo.ufn_get_productdutyrate(pr.ProductId,getdate()),0)as nvarchar(18))+')' AS ProductDescription   
 FROM dbo.tbCustomerRequirement cr           
JOIN dbo.tbCompany co   ON cr.CompanyNo = co.CompanyId                                                                    
LEFT JOIN dbo.tbCurrency cu  ON cr.CurrencyNo = cu.CurrencyId                                                                    
LEFT JOIN dbo.tbLogin lg    ON cr.Salesman = lg.LoginId                                                    
--LEFT JOIN dbo.tbContact cn  ON cr.ContactNo = cn.ContactId                                                                    
LEFT JOIN dbo.tbProduct pr  ON cr.ProductNo = pr.ProductId                                                                    
LEFT JOIN dbo.tbPackage pk  ON cr.PackageNo = pk.PackageId                                                                
LEFT JOIN dbo.tbManufacturer mf  ON cr.ManufacturerNo = mf.ManufacturerId                                                                    
--LEFT JOIN dbo.tbUsage us   ON cr.UsageNo = us.UsageId                                                        
--LEFT JOIN dbo.tbReason re   ON cr.ReasonNo = re.ReasonId                                                                    
--LEFT JOIN dbo.tbDivision dv ON lg.DivisionNo = dv.DivisionId                                                  
LEFT JOIN tbCompanyType ct  ON ct.CompanyTypeId = co.TypeNo                                                                    
LEFT JOIN tbBOMManager bom  ON bom.BOMManagerId = cr.BOMManagerNo                                                                
LEFT JOIN tbCurrency cob on bom.CurrencyNo = cob.CurrencyId                                                              
LEFT JOIN tbClient c    ON cr.ClientNo = c.ClientId                                                           
LEFT JOIN dbo.tbLogin stm ON cr.SupportTeamMemberNo = stm.LoginId                    
LEFT JOIN tbreqstatus tbreqstts on cr.reqstatus = tbreqstts.REQStatusId                    
where cr.BOMManagerNo = @BOMManagerNo                                              
and cr.FullPart = case when isnull(@Part,'')='' then cr.fullpart else @Part end                            
order by cr.sequence ASC                                  
--OFFSET @skip ROWS                                      
--FETCH NEXT @Rpp ROWS ONLY                                  
                                  
END 

GO

