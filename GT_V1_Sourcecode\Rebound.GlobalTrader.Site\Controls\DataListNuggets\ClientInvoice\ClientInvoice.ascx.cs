//----------------------------------------------------------------------------------------------------
//Marker     Changed by      Date               Remarks
//[005]      Prakash           11/04/2014         Add Client Invoice
//----------------------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.DataListNuggets {
    public partial class ClientInvoice : Base
    {

        #region Properties

        private bool _blnShowCanNotBeExported = false;
        public bool ShowCanNotBeExported {
            get { return _blnShowCanNotBeExported; }
            set { _blnShowCanNotBeExported = value; }
        }


        #endregion

        #region Overrides

        /// <summary>
        /// OnInit
        /// </summary>
        /// <param name="e"></param>
        protected override void OnInit(EventArgs e) {
            SetDataListNuggetType("ClientInvoice");
            base.OnInit(e);
            TitleText = Functions.GetGlobalResource("Nuggets", "ClientInvoices");
            AddScriptReference("Controls.DataListNuggets.ClientInvoice.ClientInvoice");
         	SetupTable();
        }

		protected override void OnLoad(EventArgs e) {
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.DataListNuggets.ClientInvoice", ctlDesignBase.ClientID);
            _scScriptControlDescriptor.AddProperty("blnPOHub", Convert.ToBoolean(SessionManager.IsPOHub));
			base.OnLoad(e);
		}
        protected override void RenderAdditionalState()
        {
            int intTab = 0;
            var strCallType = this.GetSavedStateValue("CallType").ToUpper();
            if (string.IsNullOrEmpty(strCallType)) strCallType = "ALL";
            if (strCallType == "ALL") intTab = 0;
            if (strCallType == "CANNOTBEEXPORTED") intTab = 1;
            this._blnShowCanNotBeExported = (intTab == 1);
            ((Pages.Content)Page).CurrentTab = intTab;                       
            this.OnAskPageToChangeTab();
            base.RenderAdditionalState();
        }

		protected override void OnPreRender(EventArgs e) {
			base.OnPreRender(e);
            _scScriptControlDescriptor.AddProperty("blnShowCanNotBeExported", _blnShowCanNotBeExported);
		}

        #endregion

        private void SetupTable() {
            _tbl.AllowSelection = false;
            _tbl.SortColumnDirection = Rebound.GlobalTrader.Site.Enumerations.SortColumnDirection.DESC;
            _tbl.Columns.Add(new FlexiDataColumn("ClientInvoice", WidthManager.GetWidth(WidthManager.ColumnWidth.SystemDocumentNumber), true));
            _tbl.Columns.Add(new FlexiDataColumn("Client", Unit.Empty, true));
            _tbl.Columns.Add(new FlexiDataColumn("GoodsIn", Unit.Empty, true));
            _tbl.Columns.Add(new FlexiDataColumn("PurchaseOrder", "InternalPurchaseOrder", WidthManager.GetWidth(WidthManager.ColumnWidth.SystemDocumentNumber), true));
            //_tbl.Columns.Add(new FlexiDataColumn("PurchaseOrder", WidthManager.GetWidth(WidthManager.ColumnWidth.SystemDocumentNumber), true));
           // _tbl.Columns.Add(new FlexiDataColumn("URNNumber", WidthManager.GetWidth(WidthManager.ColumnWidth.SystemDocumentNumber), true));
            _tbl.Columns.Add(new FlexiDataColumn("InvoiceDate", WidthManager.GetWidth(WidthManager.ColumnWidth.DateAndTime), true));
            _tbl.Columns.Add(new FlexiDataColumn("PartNo", WidthManager.GetWidth(WidthManager.ColumnWidth.PartNo), true));
            _tbl.Columns.Add(new FlexiDataColumn("Value", WidthManager.GetWidth(WidthManager.ColumnWidth.CurrencyValue), true));
        }
    }
}