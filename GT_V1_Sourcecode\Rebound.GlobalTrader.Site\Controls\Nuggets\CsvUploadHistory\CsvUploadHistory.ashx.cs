//-------------------------------------------------------------------------------------------
// RP 12.01.2010:
// - convert values for YTD / Last year as they are returned in base currency
//
// RP 17.11.2009:
// - use specific query for sales info to cut down data going through pipes
// - get the YTD / last year values in one hit
//Marker     Changed by      Date         Remarks
//[001]      Vinay           11/06/2012   This need to Add Incoterms field in company section
//-------------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets.Data {
	[WebService(Namespace = "http://tempuri.org/")]
	[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class CsvUploadHistory : Rebound.GlobalTrader.Site.Data.Base
    {

		public override void ProcessRequest(HttpContext context) {
			if (base.init(context)) {
				switch (Action) {
                    case "GetLog": GetUploadLog(); break;
					default: WriteErrorActionNotFound(); break;
				}
			}
		}

		
	
		/// <summary>
		/// Get the CSV Import Log
		/// </summary>
        private void GetUploadLog()
        {
          
			JsonObject jsn = new JsonObject();
			JsonObject jsnItems = new JsonObject(true);
            List<PurchaseQuoteLine> lst;
            lst = PurchaseQuoteLine.GetUploadLog(Convert.ToInt32(SessionManager.LoginID));
           
			for (int i = 0; i < lst.Count; i++) {
				JsonObject jsnItem = new JsonObject();
                jsnItem.AddVariable("ID", lst[i].PurchaseQuoteLineId);			
                jsnItem.AddVariable("Message", lst[i].Message);
				jsnItem.AddVariable("Date", Functions.FormatDate(lst[i].DatePOQuoted));
				jsnItems.AddVariable(jsnItem);
				jsnItem.Dispose(); jsnItem = null;
			}
			lst = null;
			jsn.AddVariable("CreditHist", jsnItems);
			OutputResult(jsn);
			jsn.Dispose(); jsn = null;
		}

	
	}
}
