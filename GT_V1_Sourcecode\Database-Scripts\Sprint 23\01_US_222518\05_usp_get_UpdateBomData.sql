﻿CREATE OR ALTER PROCEDURE usp_get_UpdateBomData
	@BOMID int
AS
BEGIN
	DECLARE @ClientId int	
			,@BomName nvarchar(1000) = null
			,@DefaultCurrencyId int = null
			,@CompanyId int = null
			,@CompanyName nvarchar(1000) = null
			,@SalespersonId int = null
			,@SalespersonName nvarchar(256) = null
			,@SelectedContactId int

	SELECT @ClientId = b.ClientNo
			,@BomName = b.Bom<PERSON>
			,@DefaultCurrencyId = b.CurrencyNo
			,@CompanyId = b.CompanyNo
			,@CompanyName = c.CompanyName
			,@SalespersonId = b.Salesman2
			,@SalespersonName = l.EmployeeName
			,@SelectedContactId = b.ContactNo
	FROM tbBom b WITH(NOLOCK)
	LEFT JOIN tbCompany c WITH(NOLOCK) on c.CompanyId = b.CompanyNo
	LEFT JOIN tbLogin l WITH(NOLOCK) on l.LoginId = b.<PERSON>man2
	WHERE b.BomId = @BOMID;

	;with cteContact AS(
		SELECT ContactId
			,ContactName
		FROM dbo.tbContact WITH(NOLOCK)
		WHERE CompanyNo = @CompanyId AND Inactive <> 1)
	SELECT 
		@ClientId AS ClientId
		,@BomName AS BomName
		,@DefaultCurrencyId AS DefaultCurrencyId
		,@CompanyId AS CompanyId
		,@CompanyName AS CompanyName
		,@SalespersonId AS SalespersonId
		,@SalespersonName Salesperson
		,@SelectedContactId AS SelectedContactId
		,ContactId
		,ContactName
	FROM cteContact
END
GO

/*
	exec usp_get_UpdateBomData @BomId = 632637
*/