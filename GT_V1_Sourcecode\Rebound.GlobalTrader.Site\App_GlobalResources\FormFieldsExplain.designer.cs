//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option or rebuild the Visual Studio project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.VisualStudio.Web.Application.StronglyTypedResourceProxyBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class FormFieldsExplain {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal FormFieldsExplain() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Resources.FormFieldsExplain", global::System.Reflection.Assembly.Load("App_GlobalResources"));
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Choose whether the PO will be approved on creation or will need to be manually approved.
        /// </summary>
        internal static string AppSettingsEdit_AutoApprovePO {
            get {
                return ResourceManager.GetString("AppSettingsEdit_AutoApprovePO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Choose whether the SO will be approved on creation or will need to be manually approved.
        /// </summary>
        internal static string AppSettingsEdit_AutoApproveSO {
            get {
                return ResourceManager.GetString("AppSettingsEdit_AutoApproveSO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier invoice will be automatically exported, if it is checked.
        /// </summary>
        internal static string AppSettingsEdit_AutoExportSI {
            get {
                return ResourceManager.GetString("AppSettingsEdit_AutoExportSI", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select a default rating for when you approve a vendor.
        /// </summary>
        internal static string AppSettingsEdit_DefaultPORating {
            get {
                return ResourceManager.GetString("AppSettingsEdit_DefaultPORating", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select a default rating for when you approve a customer.
        /// </summary>
        internal static string AppSettingsEdit_DefaultSORating {
            get {
                return ResourceManager.GetString("AppSettingsEdit_DefaultSORating", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Date Promised period between current month and Date Promise month, if it is checked.
        /// </summary>
        internal static string AppSettingsEdit_EditPromiseDate {
            get {
                return ResourceManager.GetString("AppSettingsEdit_EditPromiseDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This is the Enhanced Inspection changes configuration..
        /// </summary>
        internal static string AppSettingsEdit_EICharges {
            get {
                return ResourceManager.GetString("AppSettingsEdit_EICharges", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You can enable and disable Power app workflow notifications..
        /// </summary>
        internal static string AppSettingsEdit_EnablePowerApp {
            get {
                return ResourceManager.GetString("AppSettingsEdit_EnablePowerApp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to How many top salespeople should be shown on the homepage?.
        /// </summary>
        internal static string AppSettingsEdit_HomepageTopSalespeople {
            get {
                return ResourceManager.GetString("AppSettingsEdit_HomepageTopSalespeople", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invoice and Packing Slip will be print\email, if it is checked.
        /// </summary>
        internal static string AppSettingsEdit_IncludeInvoiceEmbedImage {
            get {
                return ResourceManager.GetString("AppSettingsEdit_IncludeInvoiceEmbedImage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Choose whether to include shipping in the calculations for &apos;All Statistics&apos; on the homepage.
        /// </summary>
        internal static string AppSettingsEdit_IncludeShippingOnHomepageStats {
            get {
                return ResourceManager.GetString("AppSettingsEdit_IncludeShippingOnHomepageStats", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Activate auto invoice export.
        /// </summary>
        internal static string AppSettingsEdit_InvAutoExport {
            get {
                return ResourceManager.GetString("AppSettingsEdit_InvAutoExport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IPO Purchasing email group.
        /// </summary>
        internal static string AppSettingsEdit_IPOPurchasing {
            get {
                return ResourceManager.GetString("AppSettingsEdit_IPOPurchasing", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Limit for the number of PDF document that can be added to existing pages.
        /// </summary>
        internal static string AppSettingsEdit_MaxPDFDocument {
            get {
                return ResourceManager.GetString("AppSettingsEdit_MaxPDFDocument", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Limit for the number of images that can be added to a Stock bin on the Stock detail page.
        /// </summary>
        internal static string AppSettingsEdit_MaxStockImages {
            get {
                return ResourceManager.GetString("AppSettingsEdit_MaxStockImages", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Choose whether to allow other client companies to view Excess, Offers  and History data .
        /// </summary>
        internal static string AppSettingsEdit_OwnDataVisibleToOthers {
            get {
                return ResourceManager.GetString("AppSettingsEdit_OwnDataVisibleToOthers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to To allow email sending on UAT.
        /// </summary>
        internal static string AppSettingsEdit_SendEmail {
            get {
                return ResourceManager.GetString("AppSettingsEdit_SendEmail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Virtual warehouses are hidden from the Orders screens, only showing in Warehouse functions.
        /// </summary>
        internal static string Setup_Warehouse_Virtual {
            get {
                return ResourceManager.GetString("Setup_Warehouse_Virtual", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Choose whether all lines have their quantities set back to the amount shipped (or zero if none has been shipped).
        /// </summary>
        internal static string SOClose_ResetQuantities {
            get {
                return ResourceManager.GetString("SOClose_ResetQuantities", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Choose whether any photos should be copied over to the split stock.
        /// </summary>
        internal static string StockSplit_ShouldPhotosBeCopied {
            get {
                return ResourceManager.GetString("StockSplit_ShouldPhotosBeCopied", resourceCulture);
            }
        }
    }
}
