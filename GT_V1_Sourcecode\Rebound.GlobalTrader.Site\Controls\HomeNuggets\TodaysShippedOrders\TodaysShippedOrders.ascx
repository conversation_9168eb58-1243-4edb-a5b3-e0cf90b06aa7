<%@ Control Language="C#" CodeBehind="TodaysShippedOrders.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.HomeNuggets.TodaysShippedOrders" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Nugget:DesignBase id="ctlDB" runat="server">
	<content>
		<div class="homepageNugget">
		    <asp:Panel ID="pnlShipped" runat="server">
			    <ReboundUI:SimpleDataTable ID="tblShipped" runat="server" AllowSelection="false" />
		    </asp:Panel>
		</div>
	</content>
</ReboundUI_Nugget:DesignBase>
