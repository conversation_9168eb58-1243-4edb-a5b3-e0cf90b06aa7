using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site.Controls;
using System.Collections.Generic;
using Rebound.GlobalTrader.Site.Controls.Forms;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets
{
    public partial class ClientBOMItems : Base
    {

        #region Locals
        protected IconButton _ibtnAdd;
        protected IconButton _ibtnDelete;
        protected IconButton _ibtnUnRelease;
        protected IconButton _ibtnRelease;
        protected IconButton _ibtnAddLineItem;
        //End
        private TabStrip _ctlTabs;
        protected Tab _ctlTabStock;
        protected FlexiDataTable _tblStock;
        protected Panel _pnlLineDetail;
        protected Panel _pnlLoadingLineDetail;
        protected Panel _pnlLineDetailError;
        //  protected Controls.Forms.BOMItems_Add _frmAdd;

        protected IconButton _ibtnNoBid;
        protected IconButton _ibtnRecallNoBid;
        protected IconButton _ibtnNote;
        protected IconButton _ibtnExportPurchaseHUB;
        #endregion

        #region Properties

        private int _intBOMID = -1;
        public int BOMID
        {
            get { return _intBOMID; }
            set { _intBOMID = value; }
        }
        private bool _blnCanRelease = true;
        public bool CanRelease
        {
            get { return _blnCanRelease; }
            set { _blnCanRelease = value; }
        }

        private bool _blnAdd = true;
        public bool CanAdd
        {
            get { return _blnAdd; }
            set { _blnAdd = value; }
        }

        private bool _blnDelete = true;
        public bool CanDelete
        {
            get { return _blnDelete; }
            set { _blnDelete = value; }
        }
        private bool _blnUnRelease = true;
        public bool CanUnRelease
        {
            get { return _blnUnRelease; }
            set { _blnUnRelease = value; }
        }

        private bool _blnCanNobid = true;
        public bool CanNoBid
        {
            get { return _blnCanNobid; }
            set { _blnCanNobid = value; }
        }
        private bool _blnCanRecallNobid = true;
        public bool CanRecallNoBid
        {
            get { return _blnCanRecallNobid; }
            set { _blnCanRecallNobid = value; }
        }
        private bool _blnCanNote = true;
        public bool CanNote
        {
            get { return _blnCanNote; }
            set { _blnCanNote = value; }
        }
        #endregion

        #region Overrides
        /// <summary>
        /// Oninit
        /// </summary>
        /// <param name="e"></param>
        protected override void OnInit(EventArgs e)
        {
            base.OnInit(e);
            WireUpControls();
            SetupTables();
            AddScriptReference("Controls.Nuggets.ClientBOMItems.ClientBOMItems.js");
            TitleText = Functions.GetGlobalResource("Nuggets", "ClientBOMItems");
            if (_objQSManager.BOMID > 0) _intBOMID = _objQSManager.BOMID;
        }

        protected override void OnPreRender(EventArgs e)
        {
            //_ibtnRelease.Visible = _blnCanRelease;
            //_ibtnRelease.Visible = Convert.ToBoolean(SessionManager.IsPOHub);
            _ibtnAdd.Visible = !Convert.ToBoolean(SessionManager.IsPOHub);
            //_ibtnNoBid.Visible = Convert.ToBoolean(SessionManager.IsPOHub);
            //_ibtnRecallNoBid.Visible = Convert.ToBoolean(SessionManager.IsPOHub);
            base.OnPreRender(e);
        }

        protected override void OnLoad(EventArgs e)
        {
            SetupScriptDescriptors();
            base.OnLoad(e);
        }


        #endregion

        private void SetupTables()
        {


            _tblStock.PanelHeight = Unit.Pixel(160);
            _tblStock.AllowSelection = true;
            //_tblStock.Columns.Add(new FlexiDataColumn("Select", Unit.Pixel(35), false, HorizontalAlign.Center));

            _tblStock.Columns.Add(new FlexiDataColumn("PartNo", "CustomerPart", WidthManager.GetWidth(WidthManager.ColumnWidth.PartNo)));
            _tblStock.Columns.Add(new FlexiDataColumn("Requirement", "Quantity", WidthManager.GetWidth(WidthManager.ColumnWidth.SystemDocumentNumber)));
            _tblStock.Columns.Add(new FlexiDataColumn("ManufacturerAbbreviation", "DateCode", WidthManager.GetWidth(WidthManager.ColumnWidth.BOMItemsManufacturerCode)));
            _tblStock.Columns.Add(new FlexiDataColumn("Product", "Package", WidthManager.GetWidth(WidthManager.ColumnWidth.BOMItemsPackage)));
            _tblStock.Columns.Add(new FlexiDataColumn("Customer", "DateRequired", WidthManager.GetWidth(WidthManager.ColumnWidth.BOMItemsCompanyName)));
            _tblStock.Columns.Add(new FlexiDataColumn("TargetPrice", "Salesman", WidthManager.GetWidth(WidthManager.ColumnWidth.BOMItemsSalesman)));
            //_tblStock.Columns.Add(new FlexiDataColumn("MSL", "FactorySealed", WidthManager.GetWidth(WidthManager.ColumnWidth.BOMItemsFactorySealed)));
            _tblStock.Columns.Add(new FlexiDataColumn("Notes", Unit.Pixel(90)));
            _tblStock.Columns.Add(new FlexiDataColumn("MSL", "FactorySealed", Unit.Pixel(42)));
           // _tblStock.Columns.Add(new FlexiDataColumn("Quantity"));

            //if (SessionManager.IsPOHub == true)
            //    //_tblStock.Columns.Add(new FlexiDataColumn("PurchaseRequestNo", "Notes", WidthManager.GetWidth(WidthManager.ColumnWidth.BOMItemsFactorySealed)));
            //    _tblStock.Columns.Add(new FlexiDataColumn("PurchaseRequestNo", "Notes", WidthManager.GetWidth(WidthManager.ColumnWidth.BOMItemsFactorySealed)));
            //else
            //    _tblStock.Columns.Add(new FlexiDataColumn("Notes", WidthManager.GetWidth(WidthManager.ColumnWidth.BOMItemsNotes)));


        }

        /// <summary>
        /// Sets up AJAX script descriptors
        /// </summary>
        private void SetupScriptDescriptors()
        {
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Nuggets.ClientBOMItems", ctlDesignBase.ClientID);
            _scScriptControlDescriptor.AddProperty("intBOMID", _intBOMID);
            _scScriptControlDescriptor.AddComponentProperty("ctlTabStrip", _ctlTabs.ClientID);
            _scScriptControlDescriptor.AddComponentProperty("tblStock", _tblStock.ClientID);
            _scScriptControlDescriptor.AddComponentProperty("ctlTabStock", _ctlTabStock.ClientID);
            if (_blnAdd) _scScriptControlDescriptor.AddElementProperty("ibtnAdd", _ibtnAdd.ClientID);
            _scScriptControlDescriptor.AddProperty("blnPOHub", Convert.ToBoolean(SessionManager.IsPOHub));
            _scScriptControlDescriptor.AddElementProperty("pnlLineDetail", _pnlLineDetail.ClientID);
            _scScriptControlDescriptor.AddElementProperty("pnlLoadingLineDetail", _pnlLoadingLineDetail.ClientID);
            _scScriptControlDescriptor.AddElementProperty("pnlLineDetailError", _pnlLineDetailError.ClientID);
            _scScriptControlDescriptor.AddElementProperty("ibtnAddLineItem", _ibtnAddLineItem.ClientID);
            _scScriptControlDescriptor.AddElementProperty("ibtnExportPurchaseHUB", _ibtnExportPurchaseHUB.ClientID);

        }

        /// <summary>
        /// Wire up controls from the ascx
        /// </summary>
        private void WireUpControls()
        {
            _ctlTabs = (TabStrip)ctlDesignBase.FindContentControl("ctlTabs");
            _ctlTabs.CreateControls();
            _ctlTabStock = (Tab)ctlDesignBase.FindContentControl("ctlTabStock");
            _tblStock = (FlexiDataTable)_ctlTabStock.FindContentControl("tblStock");
            _ibtnAdd = FindIconButton("ibtnAdd");
            //_ibtnDelete = FindIconButton("ibtnDelete");
            //_ibtnUnRelease = FindIconButton("ibtnUnRelease");
            //_ibtnRelease = (IconButton)FindIconButton("ibtnRelease");
            _pnlLineDetail = (Panel)Functions.FindControlRecursive(this, "pnlLineDetail");
            _pnlLoadingLineDetail = (Panel)Functions.FindControlRecursive(this, "pnlLoadingLineDetail");
            _pnlLineDetailError = (Panel)Functions.FindControlRecursive(this, "pnlLineDetailError");
            //_ibtnNoBid = FindIconButton("ibtnNoBid");
            //_ibtnRecallNoBid = FindIconButton("ibtnRecallNoBid");
            //_ibtnNote = FindIconButton("ibtnNote");
            _ibtnAddLineItem = FindIconButton("ibtnAddLineItem");
            _ibtnExportPurchaseHUB = FindIconButton("ibtnExportPurchaseHUB");

        }

    }
}
