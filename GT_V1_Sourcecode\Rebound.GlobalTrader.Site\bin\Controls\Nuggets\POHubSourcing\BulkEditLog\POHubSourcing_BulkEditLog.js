Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.POHubSourcing_BulkEditLog=function(n){Rebound.GlobalTrader.Site.Controls.Forms.POHubSourcing_BulkEditLog.initializeBase(this,[n]);this._sourcingType="";this._partNo=""};Rebound.GlobalTrader.Site.Controls.Forms.POHubSourcing_BulkEditLog.prototype={get_ctlBulkEditLog:function(){return this._ctlBulkEditLog},set_ctlBulkEditLog:function(n){this._ctlBulkEditLog!==n&&(this._ctlBulkEditLog=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.POHubSourcing_BulkEditLog.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown))},formShown:function(){this._ctlBulkEditLog._sourcingType=this._sourcingType;this._ctlBulkEditLog.setFieldValue("ctlPartNo",this._partNo);this._ctlBulkEditLog.searchClicked()},clearFilterInput:function(){this._ctlBulkEditLog.clearFilterInput()},dispose:function(){this.isDisposed||(this._ctlBulkEditLog&&this._ctlBulkEditLog.dispose(),this._ctlBulkEditLog=null,this._sourcingType=null,this._partNo=null,Rebound.GlobalTrader.Site.Controls.Forms.POHubSourcing_BulkEditLog.callBaseMethod(this,"dispose"))}};Rebound.GlobalTrader.Site.Controls.Forms.POHubSourcing_BulkEditLog.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.POHubSourcing_BulkEditLog",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);