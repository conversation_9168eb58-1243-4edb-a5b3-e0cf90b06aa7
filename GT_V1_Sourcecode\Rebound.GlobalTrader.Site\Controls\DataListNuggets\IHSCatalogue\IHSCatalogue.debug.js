///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//
// RP 23.12.2009:
// - render state on server
//
// RP 15.12.2009:
// - allow ordering by PO Delivery Date (task 171)
//
// RP 16.10.2009:
// - Changes due to changes in base class
//-----------------------------------------------------------------------------------------
/* <PERSON><PERSON>     changed by      date         Remarks
/* [0001]     Anand Gupta     14/07/2020   IHSCatalogue */
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DataListNuggets");

Rebound.GlobalTrader.Site.Controls.DataListNuggets.IHSCatalogue = function (element) {
    Rebound.GlobalTrader.Site.Controls.DataListNuggets.IHSCatalogue.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.DataListNuggets.IHSCatalogue.prototype = {
    get_ibtnExportCSV: function () { return this._ibtnExportCSV; }, set_ibtnExportCSV: function (v) { if (this._ibtnExportCSV !== v) this._ibtnExportCSV = v; },//[001]
    get_blnShowUninspectedOnly: function() { return this._blnShowUninspectedOnly; }, set_blnShowUninspectedOnly: function(v) { if (this._blnShowUninspectedOnly !== v) this._blnShowUninspectedOnly = v; },
    get_blnPOHub: function () { return this._blnPOHub; }, set_blnPOHub: function (v) { if (this._blnPOHub !== v) this._blnPOHub = v; },
    get_AllowAvgPrice: function () { return this._AllowAvgPrice; }, set_AllowAvgPrice: function (v) { if (this._AllowAvgPrice !== v) this._AllowAvgPrice = v; },
    get_redirectFromMFR: function () { return this._redirectFromMFR; }, set_redirectFromMFR: function (v) { if (this._redirectFromMFR !== v) this._redirectFromMFR = v; },

    initialize: function () {
        $("#ctl00_cphMain_ctlPageTitle_tab1").hide();
        $("#ctl00_cphMain_ctlPageTitle_tab2").hide();
        
        this.addSetupDataCallEvent(Function.createDelegate(this, this.setupDataCall));
        this.addGetDataOKEvent(Function.createDelegate(this, this.getDataOK));
        //this.updateFilterVisibility();
        this.addInitCompleteEvent(Function.createDelegate(this, this.initAfterBaseIsReady));
        this._strPathToData = "controls/DataListNuggets/IHSCatalogue";
        this._strDataObject = "IHSCatalogue";
        Rebound.GlobalTrader.Site.Controls.DataListNuggets.IHSCatalogue.callBaseMethod(this, "initialize");
        if (this._ibtnExportCSV) $R_IBTN.addClick(this._ibtnExportCSV, Function.createDelegate(this, this.exportCSV));//[001]

        
    },
   
    initAfterBaseIsReady: function () {

        this.addPageTabChangedEvent(Function.createDelegate(this, this.pageTabChanged));
       // [0001] Code Start
       // this.getData();
       // this.showContentLoading(false);
        // [0001] Code End
        if (this._redirectFromMFR) {
            this.setFilterFieldValue("ctlManufacturer", this._redirectFromMFR);
            this.applyFilter();

        }
        this.getData();
    },

    dispose: function() {
        if (this.isDisposed) return;
        this._blnShowUninspectedOnly = null;
        this._blnPOHub = null;
        this._AllowAvgPrice = null;
        if (this._ibtnExportCSV) $R_IBTN.clearHandlers(this._ibtnExportCSV);//[001]
        this._ibtnExportCSV = null;//[001]
        Rebound.GlobalTrader.Site.Controls.DataListNuggets.IHSCatalogue.callBaseMethod(this, "dispose");

    },

    pageTabChanged: function() {
        this._table._intCurrentPage = 1;
        this._blnShowUninspectedOnly = (this._intCurrentTab == 1);
        this.updateFilterVisibility();
        // [0001] Code Start
        this.getData();
        this.showContentLoading(false);
       
        // [0001] Code End
    },

    setupDataCall: function() {
        //this._objData.addParameter("UninspectedOnly", this._blnShowUninspectedOnly);
        //this._objData.addParameter("AllowAvgPrice", this._AllowAvgPrice);
    },

    getDataOK: function () {
        var chkdatestatus = '';
        for (var i = 0, l = this._objResult.Results.length; i < l; i++) {
            chkdatestatus = '';
            imagepath = '';
            var row = this._objResult.Results[i];
            var aryData;
            if (row.IsPDFAvailable == true) { 
            chkdatestatus = true;
                imagepath = "app_themes/original/images/IconButton/pdficon.jpg";
                //PDF_link_icon.png || pdficon.jpg || PDF_link_icons.png
            }
                
            else if(row.IsPDFAvailable == false)
            {
            chkdatestatus = false;
            }
            if (this._blnPOHub && this._AllowAvgPrice) {
                aryData = [
                     $R_FN.writeDoubleCellValue($R_FN.writePartNo(row.Part), $R_FN.setCleanTextValue(row.LifeCycleStage))
                    , $R_FN.writeDoubleCellValue($RGT_nubButton_Manufacturer(row.MfrNo, row.Mfr), $R_FN.setCleanTextValue(row.MSLName))
                    , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.CountryOfOrigin), $R_FN.setCleanTextValue(row.HTSCode))
                    , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Packaging), $R_FN.setCleanTextValue(row.PackagingSize))
                    , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.AveragePrice), $R_FN.setCleanTextValue(row.Date))
                    , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Descriptions), $R_FN.setCleanTextValue(row.ECCNCode))
                    // , $R_FN.setCleanTextValue(String.format("&nbsp;&nbsp;<a href=\"javascript:void(0);\" onclick=\"$RGT_openIHSDoc({0})\" ><img border='0' alt=PDF' src=" + chkdatestatus +" width='60' height='60'></a>", row.IHSPartsId))
                    , $R_FN.setCleanTextValue((chkdatestatus == true) ? String.format("&nbsp;&nbsp;<center><a href=\"javascript:void(0);\" onclick=\"$RGT_openIHSDoc({0},{1})\" title=\"Click to View and add docs\"><img border='0'  src=" + imagepath + " width='30' height='26'></center></a>", row.IHSPartsId, 0) : String.format("&nbsp;&nbsp;<a href=\"javascript:void(0);\" onclick=\"$RGT_openIHSDoc({0},{1})\" style='text-decoration:none;' title=\"Click to add docs\"><center><b>+</b></center></a>", row.IHSPartsId,0))
                    , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.APIImportedData), "")
                ];
            }
            else {

                aryData = [
                    $R_FN.writeDoubleCellValue($R_FN.writePartNo(row.Part), $R_FN.setCleanTextValue(row.LifeCycleStage))
                    , $R_FN.writeDoubleCellValue($RGT_nubButton_Manufacturer(row.MfrNo, row.Mfr), $R_FN.setCleanTextValue(row.MSLName))
                    , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.CountryOfOrigin), $R_FN.setCleanTextValue(row.HTSCode))
                    , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Packaging), $R_FN.setCleanTextValue(row.PackagingSize))
                    ,  $R_FN.setCleanTextValue(row.Date)
                    , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Descriptions), $R_FN.setCleanTextValue(row.ECCNCode))
                    , $R_FN.setCleanTextValue((chkdatestatus == true) ? String.format("&nbsp;&nbsp;<center><a href=\"javascript:void(0);\" onclick=\"$RGT_openIHSDoc({0},{1})\"   title=\"Click to View and add docs\"><img border='0'  src=" + imagepath + " width='30' height='26'></center></a>", row.IHSPartsId, 0) : String.format("&nbsp;&nbsp;<a href=\"javascript:void(0);\" onclick=\"$RGT_openIHSDoc({0},{1})\" style='text-decoration:none;'title=\"Click to add docs\"><center><b>+</b></center></a>", row.IHSPartsId,0))
                    , $R_FN.setCleanTextValue($R_FN.setCleanTextValue(row.APIImportedData))
                ];
                
                
                    
            }
                this._table.addRow(aryData, row.IHSPartsId, false);
            aryData = null; row = null;
        }
    },
    //updateFilterVisibility: function() {
    //    //this.getFilterField("ctlClientName").show(this._blnPOHub || this._AllowAvgPrice);
        
    //},

    exportCSV: function () {
    this.getData_Start();
    var obj = new Rebound.GlobalTrader.Site.Data();
    obj.set_PathToData("controls/DataListNuggets/IHSCatalogue");
    obj.set_DataObject("IHSCatalogue");
    obj.addParameter("ViewLevel", this._enmViewLevel);
    obj.set_DataAction("ExportToCSV");
    obj._intTimeoutMilliseconds = 500 * 1000;
    obj.addParameter("SortIndex", this._sortIndex);
    obj.addParameter("SortDir", this._sortDir);
    obj.addParameter("PageIndex", this._pageIndex);
    obj.addParameter("PageSize", this._pageSize);
    obj.addParameter("Part", this.getFilterFieldValue("ctlPart"));
    obj.addParameter("Manufacturer", this.getFilterFieldValue("ctlManufacturer"));
    obj.addParameter("countryOforigin", this.getFilterFieldValue("ctlcountryOforigin"));
    obj.addParameter("MSL", this.getFilterFieldValue("ctlMSL"));
    obj.addParameter("HtcCode", this.getFilterFieldValue("ctlHtcCode"));
    obj.addParameter("Description", this.getFilterFieldValue("ctlDescription"));
    obj.addParameter("RecentOnly", this.getFilterFieldValue("ctlRecentOnly"));
    obj.addDataOK(Function.createDelegate(this, this.exportCSV_OK));
    obj.addError(Function.createDelegate(this, this.exportCSV_Error));
    obj.addTimeout(Function.createDelegate(this, this.exportCSV_Error));
    $R_DQ.addToQueue(obj);
    $R_DQ.processQueue();
    obj = null;
        
},

exportCSV_OK: function (args) {
    var res = args._result;
    if (res.Filename) {
        //add the date to the file to force the latest version to be returned (not from cache)
        var dt = new Date();
        location.href = String.format("{0}?t={1}", res.Filename, dt.getTime());
        dt = null;
    }
    this.getDataOK_End();
},

exportCSV_Error: function (args) {
    this.showError(true, args.get_ErrorMessage());
}

};
Rebound.GlobalTrader.Site.Controls.DataListNuggets.IHSCatalogue.registerClass("Rebound.GlobalTrader.Site.Controls.DataListNuggets.IHSCatalogue", Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base, Sys.IDisposable);
