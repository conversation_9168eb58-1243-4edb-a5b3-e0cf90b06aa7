Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DataListNuggets");Rebound.GlobalTrader.Site.Controls.DataListNuggets.PurchaseOrdersReceive=function(n){Rebound.GlobalTrader.Site.Controls.DataListNuggets.PurchaseOrdersReceive.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DataListNuggets.PurchaseOrdersReceive.prototype={get_blnShowAllOrders:function(){return this._blnShowAllOrders},set_blnShowAllOrders:function(n){this._blnShowAllOrders!==n&&(this._blnShowAllOrders=n)},get_IsGlobalLogin:function(){return this._IsGlobalLogin},set_IsGlobalLogin:function(n){this._IsGlobalLogin!==n&&(this._IsGlobalLogin=n)},initialize:function(){this.addInitCompleteEvent(Function.createDelegate(this,this.initAfterBaseIsReady));this.addSetupDataCallEvent(Function.createDelegate(this,this.setupDataCall));this.addGetDataOKEvent(Function.createDelegate(this,this.getDataOK));this._strPathToData="controls/DataListNuggets/PurchaseOrdersReceive";this._strDataObject="PurchaseOrdersReceive";Rebound.GlobalTrader.Site.Controls.DataListNuggets.PurchaseOrdersReceive.callBaseMethod(this,"initialize")},initAfterBaseIsReady:function(){this.addPageTabChangedEvent(Function.createDelegate(this,this.pageTabChanged));this.updateFilterVisibility();this.getData()},dispose:function(){this.isDisposed||(this._blnShowAllOrders=null,this._IsGlobalLogin=null,Rebound.GlobalTrader.Site.Controls.DataListNuggets.PurchaseOrdersReceive.callBaseMethod(this,"dispose"))},pageTabChanged:function(){this._table._intCurrentPage=1;this._blnShowAllOrders=this._intCurrentTab==1;this.updateFilterVisibility();this.getData()},setupDataCall:function(){this._objData.addParameter("IsGlobalLogin",this._IsGlobalLogin);var n="GetData";this._blnShowAllOrders&&(n+="_All");this._objData.set_DataAction(n)},getDataOK:function(){for(var n,i,t=0,r=this._objResult.Results.length;t<r;t++)n=this._objResult.Results[t],i=null,i=this._IsGlobalLogin==!0?[$R_FN.writeDoubleCellValue($RGT_nubButton_ReceivePurchaseOrder(n.ID,n.No),$RGT_nubButton_InternalPurchaseOrder(n.IPOID,n.IPONo)),$R_FN.writeDoubleCellValue($R_FN.writePartNo(n.Part,n.ROHS),$RGT_nubButton_Manufacturer(n.MfrNo,n.Mfr)),$R_FN.writeDoubleCellValue(n.Quantity,n.Oustanding),$R_FN.writeDoubleCellValue($RGT_nubButton_Company(n.CMNo,n.CM),$RGT_nubButton_Contact(n.ContactNo,n.Contact)),$R_FN.setCleanTextValue(n.Delivery),$R_FN.setCleanTextValue(n.Ship),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(n.ClientName)),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(n.ReceivePOStatus))]:[$R_FN.writeDoubleCellValue($RGT_nubButton_ReceivePurchaseOrder(n.ID,n.No),$RGT_nubButton_InternalPurchaseOrder(n.IPOID,n.IPONo)),$R_FN.writeDoubleCellValue($R_FN.writePartNo(n.Part,n.ROHS),$RGT_nubButton_Manufacturer(n.MfrNo,n.Mfr)),$R_FN.writeDoubleCellValue(n.Quantity,n.Oustanding),$R_FN.writeDoubleCellValue($RGT_nubButton_Company(n.CMNo,n.CM),$RGT_nubButton_Contact(n.ContactNo,n.Contact)),$R_FN.setCleanTextValue(n.Delivery),$R_FN.setCleanTextValue(n.Ship),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(n.ReceivePOStatus))],this._table.addRow(i,n.ID,!1),i=null,n=null},updateFilterVisibility:function(){this.getFilterField("ctlRecentOnly").show(this._blnShowAllOrders);this.getFilterField("ctlIncludeClosed").show(this._blnShowAllOrders);this.getFilterField("ctlClientName").show(this._IsGlobalLogin)}};Rebound.GlobalTrader.Site.Controls.DataListNuggets.PurchaseOrdersReceive.registerClass("Rebound.GlobalTrader.Site.Controls.DataListNuggets.PurchaseOrdersReceive",Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base,Sys.IDisposable);