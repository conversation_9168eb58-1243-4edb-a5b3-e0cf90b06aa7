﻿//Marker     Changed by         Date         Remarks
//[001]      A<PERSON><PERSON><PERSON>     10/09/2021    added new class for the purchase method.
using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

namespace Rebound.GlobalTrader.DAL
{
    public class PurchaseMethodDetails
    {
        #region Constructors

        public PurchaseMethodDetails() { }

        #endregion

        #region Properties

        /// <summary>
        /// ROHSStatusId (from Table)
        /// </summary>
        public System.Int32 PurchaseMethodId { get; set; }
        /// <summary>
        /// Name (from Table)
        /// </summary>
        public System.String Name { get; set; }

        #endregion
    }
}
