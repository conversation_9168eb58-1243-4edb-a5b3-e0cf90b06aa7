using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using System.Text;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site.Controls;
using Rebound.GlobalTrader.Site;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets
{
    public partial class CsvUploadHistory : Base
    {

        #region Locals


        protected FlexiDataTable _tblUploadHistory;

        #endregion

        #region Properties

        private int _intBOMID = -1;
        public int BOMID
        {
            get { return _intBOMID; }
            set { _intBOMID = value; }
        }
        private int _intPOQuoteID = -1;
        public int POQuoteID
        {
            get { return _intPOQuoteID; }
            set { _intPOQuoteID = value; }
        }

        #endregion

        #region Overrides

        /// <summary>
        /// Oninit
        /// </summary>
        /// <param name="e"></param>
        protected override void OnInit(EventArgs e)
        {
            base.OnInit(e);
            WireUpControls();
            AddScriptReference("Controls.Nuggets.CsvUploadHistory.CsvUploadHistory.js");
            TitleText = Functions.GetGlobalResource("Nuggets", "CsvUploadHistory");
          
            SetupTable();
        }

        protected override void OnLoad(EventArgs e)
        {
            SetupScriptDescriptors();
            base.OnLoad(e);
        }

        protected override void OnPreRender(EventArgs e)
        {

            base.OnPreRender(e);
        }

        #endregion

        private void SetupTable()
        {

            _tblUploadHistory.AllowSelection = false;
            _tblUploadHistory.Columns.Add(new FlexiDataColumn("LogMessageSupplier"));
            _tblUploadHistory.Columns.Add(new FlexiDataColumn("Date", WidthManager.GetWidth(WidthManager.ColumnWidth.Date)));
          
        }

        /// <summary>
        /// Sets up AJAX script descriptors
        /// </summary>
        private void SetupScriptDescriptors()
        {
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Nuggets.CsvUploadHistory", ctlDesignBase.ClientID);


            _scScriptControlDescriptor.AddComponentProperty("tblUploadHistory", _tblUploadHistory.ClientID);
        }

        /// <summary>
        /// Wire up controls from the ascx
        /// </summary>
        private void WireUpControls()
        {

            _tblUploadHistory = (FlexiDataTable)ctlDesignBase.FindContentControl("tblUploadHistory");
        }

    }
}
