Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.ShortShipmentStatus=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.ShortShipmentStatus.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DropDowns.ShortShipmentStatus.prototype={get_IsPartialShortShipmentStatus:function(){return this._IsPartialShortShipmentStatus},set_IsPartialShortShipmentStatus:function(n){this._IsPartialShortShipmentStatus!==n&&(this._IsPartialShortShipmentStatus=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.DropDowns.ShortShipmentStatus.callBaseMethod(this,"initialize");this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK))},dispose:function(){this.isDisposed||(this._IsPartialShortShipmentStatus=null,this._IsPartialShortShipmentStatus24=null,this._IsPartialShortShipmentStatus25=null,Rebound.GlobalTrader.Site.Controls.DropDowns.ShortShipmentStatus.callBaseMethod(this,"dispose"))},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/ShortShipmentStatus");this._objData.set_DataObject("ShortShipmentStatus");this._objData.set_DataAction("GetData");this._objData.addParameter("IsPartialShortShipmentStatus",this._IsPartialShortShipmentStatus);this._objData.addParameter("_IsPartialShortShipmentStatus24",this._IsPartialShortShipmentStatus24);this._objData.addParameter("_IsPartialShortShipmentStatus25",this._IsPartialShortShipmentStatus25)},dataCallOK:function(){var t=this._objData._result,n;if(t.Types)for(n=0;n<t.Types.length;n++)this.addOption(t.Types[n].Name,t.Types[n].ID)}};Rebound.GlobalTrader.Site.Controls.DropDowns.ShortShipmentStatus.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.ShortShipmentStatus",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);