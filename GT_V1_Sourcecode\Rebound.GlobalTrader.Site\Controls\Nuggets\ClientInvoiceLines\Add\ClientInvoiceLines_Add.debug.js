///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
//Marker     Changed by      Date               Remarks
//[001]      Vinay           06/06/2013         CR:- Client Invoice
//-----------------------------------------------------------------------------------------

Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.ClientInvoiceLines_Add = function(element) {
    Rebound.GlobalTrader.Site.Controls.Forms.ClientInvoiceLines_Add.initializeBase(this, [element]);
    this._intClientInvoiceID = 0;
    this._intCompanyID = 0;
    this._aryGoodsInLineIds = [];
    this._lnsSeperator = "/";
    this._floatLineTotal = 0;
    this._strSecondRef = "";
    this._strNarrative = "";
    this._aryAddedGoodsInLineIds = [];
    this._intInvoiceClientNo = -1;
};

Rebound.GlobalTrader.Site.Controls.Forms.ClientInvoiceLines_Add.prototype = {

    get_ctlSelectSIGILines: function() { return this._ctlSelectSIGILines; }, set_ctlSelectSIGILines: function(v) { if (this._ctlSelectSIGILines !== v) this._ctlSelectSIGILines = v; },
    get_dtFromDate: function() { return this._dtFromDate; }, set_dtFromDate: function(v) { if (this._dtFromDate !== v) this._dtFromDate = v; },
    get_dtToDate: function() { return this._dtToDate; }, set_dtToDate: function(v) { if (this._dtToDate !== v) this._dtToDate = v; },
    get_SelectedGI: function() { return this._SelectedGI; }, set_SelectedGI: function(value) { if (this._SelectedGI !== value) this._SelectedGI = value; },

    initialize: function() {
        Rebound.GlobalTrader.Site.Controls.Forms.ClientInvoiceLines_Add.callBaseMethod(this, "initialize");
        this.addSave(Function.createDelegate(this, this.saveLine));
        this.addShown(Function.createDelegate(this, this.formShown));
        this._ctlSelectSIGILines.addPotentialStatusChange(Function.createDelegate(this, this.ctlSelectSIGILines_PotentialStatusChange));
    },

    dispose: function() {
        if (this.isDisposed) return;
        this._intClientInvoiceID = null;
        if (this._ctlSelectSIGILines) this._ctlSelectSIGILines.dispose();
        this._ctlSelectSIGILines = null;
        this._intCompanyID = null;
        this._aryGoodsInLineIds = null;
        this._lnsSeperator = null;
        this._floatLineTotal = null;
        this._strSecondRef = null;
        this._strNarrative = null;
        this._aryAddedGoodsInLineIds = null;
        this._intInvoiceClientNo = null;
        Rebound.GlobalTrader.Site.Controls.Forms.ClientInvoiceLines_Add.callBaseMethod(this, "dispose");
    },

    formShown: function() {
        if (this._blnFirstTimeShown) {

        }
      //  alert(this._intInvoiceClientNo);
        Array.clear(this._aryGoodsInLineIds);
        this._ctlSelectSIGILines._aryAddedGoodsInLineIds = this._aryAddedGoodsInLineIds;
        this._ctlSelectSIGILines._aryAddedGoodsInLineIds._intCount = 0;
        this._ctlSelectSIGILines._isClientInvoice = true;
        this._ctlSelectSIGILines._intInvoiceClientNo = this._intInvoiceClientNo;
       
        this.getClientInvoice();

    },


    saveLine: function () {
      
       // return;
        if (!this.validateForm()) return;
        var obj = new Rebound.GlobalTrader.Site.Data();
        this.showSaving(true);
        obj.set_PathToData("controls/Nuggets/ClientInvoiceAdd");
        obj.set_DataObject("ClientInvoiceAdd");
        obj.set_DataAction("SaveLine");
        obj.addParameter("id", this._intClientInvoiceID);
        obj.addParameter("GoodsInLineIDs", $R_FN.arrayToSingleString(this._aryGoodsInLineIds));
        obj.addDataOK(Function.createDelegate(this, this.saveLineOK));
        obj.addError(Function.createDelegate(this, this.saveLineError));
        obj.addTimeout(Function.createDelegate(this, this.saveLineError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    saveLineError: function(args) {
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    saveLineOK: function (args) {
        //alert(args._result.Result);
        if (args._result.Result) {
           // alert("ji");
            this.updateHeader();
        } else {
            this._strErrorMessage = args._errorMessage;
            this.onSaveError();
        }
    },
    updateHeader: function () {
       // alert("Hi");
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/ClientInvoiceAdd");
        obj.set_DataObject("ClientInvoiceAdd");
        obj.set_DataAction("UpdateHeader");
        obj.addParameter("id", this._intClientInvoiceID);
        obj.addParameter("SecondRef", this.getFieldValue("ctlSecondRef"));
        obj.addParameter("Narrative", this.getFieldValue("ctlNarrative"));
        obj.addParameter("CanBeExported", this.getFieldValue("ctlCanExported"));
        obj.addDataOK(Function.createDelegate(this, this.updateHeaderComplete));
        obj.addError(Function.createDelegate(this, this.updateHeaderError));
        obj.addTimeout(Function.createDelegate(this, this.updateHeaderError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    updateHeaderError: function(args) {
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    updateHeaderComplete: function(args) {
        this.showSaving(false);
        if (args._result.Result) {
            this.onSaveComplete();
        } else {
            this._strErrorMessage = args._errorMessage;
            this.onSaveError();
        }
    },

    validateForm: function() {
        this.onValidate();
        var blnOK = this.autoValidateFields();
        if (!blnOK) this.showError(true);
        if ((this.getFieldValue("ctlSecondRef").length > 16)) {
            this.showError(true, "SecondRef should be less than 16 characters");
            blnOK = false;
        }
        if ((this.getFieldValue("ctlNarrative").length > 41)) {
            this.showError(true, "Narrative should be less than 41 characters");
            blnOK = false;
        }
        if (!(this._aryGoodsInLineIds.length > 0)) {
            this.showError(true, "Please select atleast one goodsIn line");
            blnOK = false;
        }
        return blnOK;
    },
    ctlSelectSIGILines_PotentialStatusChange: function() {
        this.setFieldValue("ctlSecondRef", ((this._strSecondRef.length <= 0) ? "" : (this._strSecondRef + ((this._ctlSelectSIGILines._aryPONumber.length > 0) ? this._lnsSeperator : ""))) + $R_FN.arrayToSingleString(this._ctlSelectSIGILines._aryPONumber, this._lnsSeperator));
        this.setFieldValue("ctlNarrative", ((this._strNarrative.length <= 0) ? "" : (this._strNarrative + ((this._ctlSelectSIGILines._aryGINumber.length > 0) ? this._lnsSeperator : ""))) + $R_FN.arrayToSingleString(this._ctlSelectSIGILines._aryGINumber, this._lnsSeperator));
        //this.setFieldValue("ctlSelectedGI", ((parseFloat(this._ctlSelectSIGILines._floatTotalSelectedValue) + parseFloat(this._floatLineTotal)) <= 0) ? "" : (parseFloat(this._ctlSelectSIGILines._floatTotalSelectedValue) + parseFloat(this._floatLineTotal)));
        document.getElementById(this._SelectedGI).value = $R_FN.formatCurrency((parseFloat(this._ctlSelectSIGILines._floatTotalSelectedValue) + parseFloat(this._floatLineTotal)), null, 2, false);
        this._aryGoodsInLineIds = this._ctlSelectSIGILines._aryGoodsInLineIds;
    },
    getClientInvoice: function() {
        this.showLoading(true);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/ClientInvoiceMainInfo");
        obj.set_DataObject("ClientInvoiceMainInfo");
        obj.set_DataAction("GetData");
        obj.addParameter("ID", this._intClientInvoiceID);
        obj.addDataOK(Function.createDelegate(this, this.getClientInvoiceOK));
        obj.addError(Function.createDelegate(this, this.getClientInvoiceError));
        obj.addTimeout(Function.createDelegate(this, this.getClientInvoiceError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getClientInvoiceOK: function(args) {
        var res = args._result;
        this._intCompanyID = res.CompanyNo;
        this.showField("ctlSupplierCode", false);
        this.setFieldValue("ctlSupplier", $R_FN.setCleanTextValue(res.SupplierName));
        this.setFieldValue("ctlSupplierCode", $R_FN.setCleanTextValue(res.SupplierCode));
        this.setFieldValue("ctlClientInvoice", res.ClientInvoiceNumber);
        this.setFieldValue("ctlInvoiceDate", res.ClientInvoiceDate);
        this.setFieldValue("ctlCurrency", res.CurrencyCode);
        this.setFieldValue("ctlInvoiceAmount", res.InvoiceAmount);
        this.setFieldValue("ctlGoodsValue", res.GoodsValue);
        this.setFieldValue("ctlTax", res.Tax);
        this.setFieldValue("ctlDeliveryCharge", res.DeliveryCharge);
        this.setFieldValue("ctlBankFee", res.BankFee);
        this.setFieldValue("ctlCreditCardFee", res.CreditCardFee);
        this.setFieldValue("ctlNotes",$R_FN.setCleanTextValue(res.Notes));
        this.setFieldValue("ctlCanExported", res.CanbeExported);
        this.setFieldValue("ctlSecondRef", $R_FN.setCleanTextValue(res.SecondRef));
        this.setFieldValue("ctlNarrative", $R_FN.setCleanTextValue(res.Narrative));
        document.getElementById(this._SelectedGI).value = this._floatLineTotal;
        this._strSecondRef = res.SecondRef;
        this._strNarrative = res.Narrative;
        this.showLoading(false);
        this.showInnerContent(true);

        this._ctlSelectSIGILines._CompanyNo = this._intCompanyID;
        // this._ctlSelectSIGILines._FromCurrencyCode = res.CurrencyCode;
        // this._ctlSelectSIGILines._FromCurrencyId = res.CurrencyNo;
        this._ctlSelectSIGILines.setFieldValue("ctlGIDateFrom", this._dtFromDate);
        this._ctlSelectSIGILines.setFieldValue("ctlGIDateTo", this._dtToDate);
        this._ctlSelectSIGILines.setFieldValue("ctlIncludeInvoiced", true);
       
        this._ctlSelectSIGILines.searchClicked();
    },

    getClientInvoiceError: function(args) {
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    }

};

Rebound.GlobalTrader.Site.Controls.Forms.ClientInvoiceLines_Add.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.ClientInvoiceLines_Add", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
