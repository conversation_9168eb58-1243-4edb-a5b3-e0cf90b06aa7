Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.SiteLanguage=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.SiteLanguage.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DropDowns.SiteLanguage.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.DropDowns.SiteLanguage.callBaseMethod(this,"initialize");this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK))},dispose:function(){this.isDisposed||Rebound.GlobalTrader.Site.Controls.DropDowns.SiteLanguage.callBaseMethod(this,"dispose")},setupDataCall:function(){this._objData.set_PathToData(this._strDataPathModification+"controls/DropDowns/SiteLanguage");this._objData.set_DataObject("SiteLanguage");this._objData.set_DataAction("GetData")},dataCallOK:function(){var t=this._objData._result,n;if(t.Items)for(n=0;n<t.Items.length;n++)this.addOption(t.Items[n].Name,t.Items[n].ID)}};Rebound.GlobalTrader.Site.Controls.DropDowns.SiteLanguage.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.SiteLanguage",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);