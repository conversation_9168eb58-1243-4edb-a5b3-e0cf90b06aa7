﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="ActiveVendors" xml:space="preserve">
    <value>Lists all Vendors active between the specified dates</value>
  </data>
  <data name="ApprovedCustomersOnStop" xml:space="preserve">
    <value>Lists all customers currently on stop.</value>
  </data>
  <data name="AutoEnteredSuppliers_Unedited" xml:space="preserve">
    <value>Lists all Suppliers by date range that have been added to the system as new companies when Information has been imported from stock disks and emails.</value>
  </data>
  <data name="ClosedRequirementsReasons" xml:space="preserve">
    <value>Lists the number of requirements closed for each reason.</value>
  </data>
  <data name="CommunicationLogActivityforaUser" xml:space="preserve">
    <value>Lists all communication log activities for the selected user and date range.</value>
  </data>
  <data name="CompaniesApprovedToPurchaseFrom" xml:space="preserve">
    <value>Lists all suppliers that have been approved to buy from and their linked vendor names.</value>
  </data>
  <data name="CompaniesNotContacted" xml:space="preserve">
    <value>Lists all companies not contacted since the selected cutoff date.</value>
  </data>
  <data name="ContactEmailList" xml:space="preserve">
    <value>Lists all contact email addresses by country.</value>
  </data>
  <data name="ContactsNotContacted" xml:space="preserve">
    <value>Lists all company contacts not contacted since the selected cutoff date.</value>
  </data>
  <data name="CreditNotes" xml:space="preserve">
    <value>All credit notes for items returned within a specified date range</value>
  </data>
  <data name="CreditNotesforaCustomer" xml:space="preserve">
    <value>All credit notes for a customer for items returned within a specified date range</value>
  </data>
  <data name="CreditNotesforaSalesperson" xml:space="preserve">
    <value>All credit notes for a salesperson for items returned within a specified date range</value>
  </data>
  <data name="CustomerListforSalesperson" xml:space="preserve">
    <value>Lists customers for a salesperson</value>
  </data>
  <data name="CustomerOnTimeDeliveryReport" xml:space="preserve">
    <value>Lists delivery statistics by cutsomer within the specified date range.  Orders are considered on time if they are shipped on or before the promissed date.</value>
  </data>
  <data name="CustomerStatement" xml:space="preserve">
    <value>Lists all outstanding invoices for the selected customer.</value>
  </data>
  <data name="DailyCustomerRequirementsbySalesperson_Detailed" xml:space="preserve">
    <value>Lists the customer requirements entered each day by salesperson</value>
  </data>
  <data name="DailyCustomerRequirementsbySalesperson_Summary" xml:space="preserve">
    <value>Lists the number of customer requirements entered daily for each customer by salesperson</value>
  </data>
  <data name="DailyCustomerRequirementsbySalesperson_Totals" xml:space="preserve">
    <value>Lists the number of customer requirements entered daily by salesperson</value>
  </data>
  <data name="DailyImports" xml:space="preserve">
    <value>Lists details by date range of stock offers and reqs from suppliers.</value>
  </data>
  <data name="DailyImportsBySource" xml:space="preserve">
    <value>Lists details by date range of stock offers and reqs from the supplier broken down by the source of the information.</value>
  </data>
  <data name="DaysSinceLastInvoicebyContact" xml:space="preserve">
    <value>Lists the number of days since a contact has been invoiced.</value>
  </data>
  <data name="DaysSinceLastInvoicebyCustomer" xml:space="preserve">
    <value>Lists the number of days since a customer has been invoiced.</value>
  </data>
  <data name="GoodsReceived" xml:space="preserve">
    <value>A summary of stock received by date range</value>
  </data>
  <data name="GoodsReceivedNotInvoiced" xml:space="preserve">
    <value>A summary of stock received but not invoiced by date range</value>
  </data>
  <data name="GoodsReceivedShipmentDetails" xml:space="preserve">
    <value>A summary of stock received with shipment details by date range.</value>
  </data>
  <data name="GrossProfitBreakdown" xml:space="preserve">
    <value>Breaks down shipped orders within a specified date range by gross profit ranges.</value>
  </data>
  <data name="IntrastatReportforEECArrivals_CustomerRMAs" xml:space="preserve">
    <value>List all commodities received from EEC countries, excluding those shipped within the country of receipt.</value>
  </data>
  <data name="IntrastatReportforEECArrivals_Purchases" xml:space="preserve">
    <value>List all commodities received from EEC countries, excluding those shipped within the country of receipt.</value>
  </data>
  <data name="IntrastatReportforEECDispatches_Sales" xml:space="preserve">
    <value>List all commodities shipped to EEC countries, excluding those shipped within the country of dispatch.</value>
  </data>
  <data name="IntrastatReportforEECDispatches_SupplierRMAs" xml:space="preserve">
    <value>List all commodities shipped to EEC countries, excluding those shipped within the country of dispatch.</value>
  </data>
  <data name="InventoryLocationReport" xml:space="preserve">
    <value>All inventory items ordered by location</value>
  </data>
  <data name="InventoryLocationReportforLot" xml:space="preserve">
    <value>All inventory items for a selected lot ordered by location.</value>
  </data>
  <data name="InvoicesSortedbyInvoiceNumber" xml:space="preserve">
    <value>Lists all invoice line items within a specified date range ordered by invoice number, with gross profit calculations.</value>
  </data>
  <data name="InvoicesSortedbyInvoiceNumberforaCustomer" xml:space="preserve">
    <value>Lists all invoice line items for the selected customer within a specified date range ordered by invoice number, with gross profit calculations.</value>
  </data>
  <data name="InvoicesSortedbyInvoiceNumberforaSalesperson" xml:space="preserve">
    <value>Lists all invoice line items for the selected salesperson within a specified date range ordered by invoice number, with gross profit calculations.</value>
  </data>
  <data name="LoginStatistics" xml:space="preserve">
    <value>List number of times users have logged in between specified date ranges.</value>
  </data>
  <data name="LoginStatisticsbyName" xml:space="preserve">
    <value>List number of times specific users have logged in between specified date ranges.</value>
  </data>
  <data name="NumberofAccountsbySalesperson" xml:space="preserve">
    <value>Lists the number of accounts linked to a sales person.</value>
  </data>
  <data name="NumberofOffersbyVendor" xml:space="preserve">
    <value>Lists the number of offers for each vendor.</value>
  </data>
  <data name="NumberofOffersHistorybyVendor" xml:space="preserve">
    <value>Lists the number of historic offers for each vendor.</value>
  </data>
  <data name="NumberofRequirementsbyVendor" xml:space="preserve">
    <value>Lists the number of requirements for each vendor.</value>
  </data>
  <data name="OpenCustomerRMAs" xml:space="preserve">
    <value>Lists all open customer RMAs with costing information, where available.</value>
  </data>
  <data name="OpenCustomerRMAsforaCustomer" xml:space="preserve">
    <value>Lists all open customer RMAs for the selected customer with costing information, where available.</value>
  </data>
  <data name="OpenCustomerRMAsforaCustomerwithReasons" xml:space="preserve">
    <value>Lists all open customer RMAs with reasons for return and for the selected customer with costing information, where available.</value>
  </data>
  <data name="OpenCustomerRMAsforaSaleperson" xml:space="preserve">
    <value>Lists all open customer RMAs for the selected salesperson with costing information, where available.</value>
  </data>
  <data name="OpenCustomerRMAsforaSalepersonwithReasons" xml:space="preserve">
    <value>Lists all open customer RMAs with reasons for return and for the selected salesperson with costing information, where available.</value>
  </data>
  <data name="OpenCustomerRMAswithReasons" xml:space="preserve">
    <value>Lists all open customer RMAs with reasons for return and costing information, where available.</value>
  </data>
  <data name="OpenRequirementsbyCustomer" xml:space="preserve">
    <value>Lists total number of open reqs against each customer</value>
  </data>
  <data name="OpenRequirementsReportBySalesperson" xml:space="preserve">
    <value>Shows all open requirements for the selected salesperson</value>
  </data>
  <data name="OpenSalesOrders" xml:space="preserve">
    <value>Lists all open sales orders with allocation information and gross profit calculations.</value>
  </data>
  <data name="OpenSalesOrdersforSalesperson" xml:space="preserve">
    <value>Lists all open sales orders for the specified salesperson with allocation information and gross profit calculations.</value>
  </data>
  <data name="OpenSupplierRMAs" xml:space="preserve">
    <value>Lists all open supplier RMAs.</value>
  </data>
  <data name="OpenSupplierRMAsforaBuyer" xml:space="preserve">
    <value>Lists all open supplier RMAs for the selected buyer.</value>
  </data>
  <data name="OpenSupplierRMAsforaBuyerwithReasons" xml:space="preserve">
    <value>Lists all open supplier RMAs with reasons for return for the selected buyer.</value>
  </data>
  <data name="OpenSupplierRMAsforaSupplier" xml:space="preserve">
    <value>Lists all open supplier RMAs for the selected supplier.</value>
  </data>
  <data name="OpenSupplierRMAsforaSupplierwithReasons" xml:space="preserve">
    <value>Lists all open supplier RMAs with reasons for return for the selected supplier.</value>
  </data>
  <data name="OpenSupplierRMAswithReasons" xml:space="preserve">
    <value>Lists all open supplier RMAs with reasons for return.</value>
  </data>
  <data name="OrdersToBeShipped" xml:space="preserve">
    <value>All outstanding orders to be shipped up until the selected date</value>
  </data>
  <data name="OrdersToBeShippedBySalesperson" xml:space="preserve">
    <value>All outstanding orders to be shipped up until the selected date by salesperson</value>
  </data>
  <data name="OustandingCustomerInvoices" xml:space="preserve">
    <value>Lists all outstanding invoices.</value>
  </data>
  <data name="PickSheetSalesOrdersBasic" xml:space="preserve">
    <value>Lists all sales orders due and ready to ship.</value>
  </data>
  <data name="PickSheetSalesOrdersDetailed" xml:space="preserve">
    <value>Lists all sales orders due and ready to ship with additional details (lot number, resale values, PO #, etc).</value>
  </data>
  <data name="PickSheetSupplierRMAs" xml:space="preserve">
    <value>Lists all Supplier RMAs due and ready to ship</value>
  </data>
  <data name="PurchaseOrdersDueIn" xml:space="preserve">
    <value>All outstanding posted purchase orders due in within the selected date range.</value>
  </data>
  <data name="PurchaseOrdersDueInforBuyer" xml:space="preserve">
    <value>All outstanding posted purchase orders due in for a buyer within the selected date range</value>
  </data>
  <data name="PurchaseOrdersDueInforSalesperson" xml:space="preserve">
    <value>All outstanding posted purchase orders due in for a salesperson within the selected date range.</value>
  </data>
  <data name="PurchaseRequisitions" xml:space="preserve">
    <value>List all posted sales order items that are not fully allocated.</value>
  </data>
  <data name="PurchaseRequisitionsforaCustomer" xml:space="preserve">
    <value>List all posted sales order items for a customer that are not fully allocated.</value>
  </data>
  <data name="PurchaseRequisitionsforaSalesPerson" xml:space="preserve">
    <value>List all posted sales order items for a sales person that are not fully allocated.</value>
  </data>
  <data name="ReceivedCustomerRMAs" xml:space="preserve">
    <value>Lists all received customer RMAs with costing information, where available.</value>
  </data>
  <data name="ReceivedCustomerRMAsforaCustomer" xml:space="preserve">
    <value>Lists all received customer RMAs for the selected customer with costing information, where available.</value>
  </data>
  <data name="ReceivedCustomerRMAsforaCustomerwithReasons" xml:space="preserve">
    <value>Lists all received customer RMAs with reasons for return for the selected customer with costing information, where available.</value>
  </data>
  <data name="ReceivedCustomerRMAsforaSaleperson" xml:space="preserve">
    <value>Lists all received customer RMAs for the selected salesperson with costing information, where available.</value>
  </data>
  <data name="ReceivedCustomerRMAsforaSalepersonwithReasons" xml:space="preserve">
    <value>Lists all received customer RMAs with reasons for return for the selected salesperson with costing information, where available.</value>
  </data>
  <data name="ReceivedCustomerRMAswithReasons" xml:space="preserve">
    <value>Lists all received customer RMAs with reasons for return and costing information, where available.</value>
  </data>
  <data name="ReceivedGoodsValuationbyCountry" xml:space="preserve">
    <value>Lists the value of stock received by country within the specified date range.</value>
  </data>
  <data name="ShippedGoodsValuationbyCountry" xml:space="preserve">
    <value>Lists the value of stock shipped by country within the specified date range.</value>
  </data>
  <data name="ShippedOrdersSortedbyInvoiceNumber" xml:space="preserve">
    <value>Lists all shipped orders within a specified date range ordered by invoice number broken down by lot (when applicable).</value>
  </data>
  <data name="ShippedOrdersSortedbyInvoiceNumberforaCustomer" xml:space="preserve">
    <value>Lists all shipped orders for the selected customer within a specified date range ordered by invoice number broken down by lot (when applicable).</value>
  </data>
  <data name="ShippedOrdersSortedbyInvoiceNumberforaSalesperson" xml:space="preserve">
    <value>Lists all shipped orders for the selected salesperson (total invoice value not split by salesperson) within a specified date range ordered by invoice number broken down by lot (when applicable).</value>
  </data>
  <data name="ShippedSalesforLot" xml:space="preserve">
    <value>Shows details of shipped line items for a lot within a specified date range.</value>
  </data>
  <data name="ShippedSupplierRMAs" xml:space="preserve">
    <value>Lists all shipped supplier RMAs.</value>
  </data>
  <data name="ShippedSupplierRMAsforaBuyer" xml:space="preserve">
    <value>Lists all shipped supplier RMAs for the selected buyer.</value>
  </data>
  <data name="ShippedSupplierRMAsforaBuyerwithReasons" xml:space="preserve">
    <value>Lists all shipped supplier RMAs with reasons for return for the selected buyer.</value>
  </data>
  <data name="ShippedSupplierRMAsforaSupplier" xml:space="preserve">
    <value>Lists all shipped supplier RMAs for the selected supplier.</value>
  </data>
  <data name="ShippedSupplierRMAsforaSupplierwithReasons" xml:space="preserve">
    <value>Lists all shipped supplier RMAs with reasons for return for the selected supplier.</value>
  </data>
  <data name="ShippedSupplierRMAswithReasons" xml:space="preserve">
    <value>Lists all shipped supplier RMAs with reasons for return.</value>
  </data>
  <data name="StockCount" xml:space="preserve">
    <value>Lists all stock for a warehouse by location with extra columns for manually entering physical counts and notes.</value>
  </data>
  <data name="StockList" xml:space="preserve">
    <value>Lists all stock with the ability to filter out items on order and/or allocated items and/or item from lots which are on hold.</value>
  </data>
  <data name="StockValuation" xml:space="preserve">
    <value>All stock items ordered by total value</value>
  </data>
  <data name="SummaryBookedOrdersbyCustomer" xml:space="preserve">
    <value>Show total costs, values, gross profit, margin for sales orders by customer booked within a specified date range.</value>
  </data>
  <data name="SummaryBookedOrdersbyDivision" xml:space="preserve">
    <value>Show total costs, values, gross profit, margin for sales orders by division booked within a specified date range.</value>
  </data>
  <data name="SummaryBookedOrdersbySalesperson" xml:space="preserve">
    <value>Show total costs, values, gross profit, margin for sales orders by salesperson booked within a specified date range.</value>
  </data>
  <data name="SummaryOpenOrdersbyCustomer" xml:space="preserve">
    <value>Show total costs, values, gross profit, margin for open orders by customer for a specified date range.</value>
  </data>
  <data name="SummaryOpenOrdersbyDivision" xml:space="preserve">
    <value>Show total costs, values, gross profit, margin for open orders by division for a specified date range.</value>
  </data>
  <data name="SummaryOpenOrdersbySalesperson" xml:space="preserve">
    <value>Show total costs, values, gross profit, margin for open orders by salesperson for a specified date range.</value>
  </data>
  <data name="SummaryShippedSalesbyCustomer" xml:space="preserve">
    <value>Show total costs, values, gross profit, margin for shipped orders by customer for a specified date range.</value>
  </data>
  <data name="SummaryShippedSalesbyDivision" xml:space="preserve">
    <value>Show total costs, values, gross profit, margin for shipped orders by division for a specified date range.</value>
  </data>
  <data name="SummaryShippedSalesbySalesperson" xml:space="preserve">
    <value>Show total costs (split by salesperson), values, gross profit, margin for shipped orders by salesperson for a specified date range.</value>
  </data>
  <data name="SupplierOnTimeDeliveryReport" xml:space="preserve">
    <value>Lists delivery statistics by supplier within the specified date range.  Orders are considered on time if they are received on or before the delivery date on the purchase order.</value>
  </data>
  <data name="UserList" xml:space="preserve">
    <value>Lists details of all users.</value>
  </data>
  <data name="OpenQuotes" xml:space="preserve">
    <value>List all open quotes</value>
  </data>
  <data name="OpenPurchaseOrders" xml:space="preserve">
    <value>List all open purchase orders</value>
  </data>
  <data name="OpenPurchaseOrdersbyCompanyType" xml:space="preserve">
    <value>List all open purchase orders for a specified company type</value>
  </data>
  <data name="OpenPurchaseOrdersbySupplier" xml:space="preserve">
    <value>List all open purchase orders for a supplier</value>
  </data>
  <data name="InvalidCompanyPurchasingInfo" xml:space="preserve">
    <value>Lists all Companies with invalid or missing Purchasing information</value>
  </data>
  <data name="InvalidCompanySalesInfo" xml:space="preserve">
    <value>Lists all Companies with invalid or missing Sales information</value>
  </data>
  <data name="RandomStockCheck" xml:space="preserve">
    <value>Gives a random list of stock for a warehouse.</value>
  </data>
  <data name="BulkEmailInvoiceStatus" xml:space="preserve">
    <value>Lists all bulk email invoice status within a specified date range.</value>
  </data>
  <data name="DailyReportLog" xml:space="preserve">
    <value>Daily Report Log Detials</value>
  </data>
</root>