<%--
Marker     Changed by      Date         Remarks
[001]      Umendra         21/01/2019   Adding View Tree Button
--%>
<%@ Control Language="C#" CodeBehind="BOMMainInfo.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Nuggets.BOMMainInfo" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" BoxType="Standard">
    <Links>
        <style>
             .disable-click {
            pointer-events: none;
        }
             #ctl00_cphMain_ctlMainInfo_ctlDB_ctl13_ctlCompany_tdItem .advisory-notes{
                 margin-left: -10px !important;
             }
        </style>
        <ReboundUI:IconButton ID="ibtnEdit" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget"
            IconTitleResource="Edit" IconCSSType="Edit" />
      <ReboundUI:IconButton ID="ibtnDelete" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget"
            IconTitleResource="Delete" IconCSSType="Delete" style="display:none"/>&nbsp;&nbsp;&nbsp
            <ReboundUI:IconButton ID="ibtnExportCSV" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="Export" /> 
            <ReboundUI:IconButton ID="ibtnExportPurchaseHUB" runat="server" IconButtonMode="hyperlink"  IconGroup="Nugget" ToolTip="Send to Purchase Hub"  IconTitleResource="ExportToPurchaseHUB"  /> 
            <ReboundUI:IconButton ID="ibtnNotify" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="SendToSupplier" />
            <ReboundUI:IconButton ID="ibtnRelease" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="ReleaseAll" IsInitiallyEnabled="false" />
            <ReboundUI:IconButton ID="ibtnClose" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="Close" />
         <ReboundUI:IconButton ID="ibtnNoBid" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="NoBidAll" IsInitiallyEnabled="false" />
        <ReboundUI:IconButton ID="ibtnNote" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="AddNewHUBFRQNote" IconCSSType="Add"  />
        <%--[001] code Start--%>
        <ReboundUI:IconButton ID="ibtnViewTree" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="ViewTree" IconCSSType="Add" />
    	<%--[001] code End--%>
            <ReboundUI:IconButton ID="ibtnCrossMatch" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget"  Style="display:none;"  IconTitleResource="CrossMatch" IconCSSType="Add" />
         </Links>
    
   
       <%-- <asp:Panel ID="Panel1" runat="server" CssClass="invisible">
           <strong>
            <%=Functions.GetGlobalResource("Misc", "SupplierAdvice")%>
            </strong>
            </asp:Panel>--%>
    <Content>
        <div id="ctl00_cphMain_ctlMainInfo_ctlDB_BuyAndPriceMergine" class="nuggetMessages">
            <div class="nuggetMessage nuggetMessageError">Some of the Sourcing Results having price issue kindly check and verify.</div></div>
        <table class="threeCols">
            <tr>
                <td class="col1">
                    <table cellpadding="0" cellspacing="0" border="0" class="dataItems">
                        <ReboundUI:DataItemRow ID="ctlCode" runat="server" ResourceTitle="Code" />
                        <ReboundUI:DataItemRow ID="ctlName" runat="server" ResourceTitle="Name" />                        
                       
                        
                        <%--<ReboundUI:DataItemRow ID="ctlCurrency" runat="server" ResourceTitle="Currency" />--%>
                        <ReboundUI:DataItemRow id="hidCurrency" runat="server" FieldType="Hidden" />
                        <ReboundUI:DataItemRow ID="ctlQuoteRequired" runat="server" ResourceTitle="QuoteRequired" />  
                        <ReboundUI:DataItemRow ID="ctlRequestedby" runat="server" ResourceTitle="Requestedby" /> 


                         <%--<ReboundUI:DataItemRow ID="ctlContact" runat="server" ResourceTitle="Contact" /> --%>                       
                        <ReboundUI:DataItemRow id="hidCompanyNo" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="hidContactNo" runat="server" FieldType="Hidden" /> 
						<ReboundUI:DataItemRow id="hidDisplayStatus" runat="server" FieldType="Hidden"/>
                        <ReboundUI:DataItemRow id="hidContact2No" runat="server" FieldType="Hidden" /> 
						 <ReboundUI:DataItemRow ID="ctlContact2" runat="server" ResourceTitle="Contact2" /> 
						  
                    </table>
                </td>
                <td class="col2">
                    <table cellpadding="0" cellspacing="0" border="0" class="dataItems">   
                         <ReboundUI:DataItemRow ID="ctlCompany" runat="server" ResourceTitle="Company" />
                         <ReboundUI:DataItemRow ID="ctlCurrency" runat="server" ResourceTitle="Currency" /> 
                         <ReboundUI:DataItemRow ID="ctlContact" runat="server" ResourceTitle="Contact" /> 
                         <ReboundUI:DataItemRow ID="ctlCurrentSupplier" runat="server" ResourceTitle="CurrentSupplier" /> 
                        	<%--[001] code start--%>
						<ReboundUI:DataItemRow id="ctlAS9120" runat="server" FieldType="CheckBox" ResourceTitle="AS9120" />
                        <ReboundUI:DataItemRow id="ctlIsFromPrOffer" runat="server" FieldType="CheckBox" ResourceTitle="IsFromProspectiveOffer" />
                        <ReboundUI:DataItemRow id="ctlPrOUploadedBy" runat="server" ResourceTitle="PrOUploadedBy" />
						<%--[001] code end--%>  
                        <ReboundUI:DataItemRow id="hidReqSalesperson" runat="server" FieldType="Hidden" />
                        <ReboundUI:DataItemRow id="hidSupportTeamMemberNo" runat="server" FieldType="Hidden" />
                       
                        						                
                    </table>
                </td>  
                 <td class="col2">
                    <table cellpadding="0" cellspacing="0" border="0" class="dataItems">  
                         <ReboundUI:DataItemRow ID="ctlInActive" runat="server" FieldType="CheckBox" ResourceTitle="IsInactive" />
                        <ReboundUI:DataItemRow ID="ctlReleasedby" runat="server" ResourceTitle="Releasedby" /> 
                         <ReboundUI:DataItemRow ID="ctlAssignTo" runat="server" ResourceTitle="AssignTo" />  
                         <ReboundUI:DataItemRow ID="ctlNotes" runat="server" ResourceTitle="Notes" />
                        <ReboundUI:DataItemRow ID="ctlAS6081" runat="server" ResourceTitle="AS6081MainInfoLabel" />
                        <ReboundUI:DataItemRow id="ctlPurchasingNotes" runat="server" ResourceTitle="CompanyPurchasingNotes" />


                       </table>
                </td>                 
            </tr>
        </table>
    </Content>
    <Forms>
        <ReboundForm:BOMMainInfo_Edit ID="ctlEdit" runat="server" />
        <ReboundForm:BOMMainInfo_Delete ID="ctlDelete" runat="server" />
        <ReboundForm:BOMMainInfo_Notify ID="ctlNotify" runat="server" />
         <ReboundForm:Confirm ID="ctlConfirm" runat="server" />
     <ReboundForm:BOMMainInfo_Release ID="ctlRelease" runat="server" />
     <ReboundForm:ConfirmClose ID="ctlConfirmClose" runat="server" />
         <ReboundForm:BOMMainInfo_NoBid ID="ctlNoBid" runat="server" />
       <ReboundForm:BOMMainInfo_AddExpedite ID="ctlAddExpedite" runat="server" />
        
    </Forms>

    
</ReboundUI_Nugget:DesignBase>
