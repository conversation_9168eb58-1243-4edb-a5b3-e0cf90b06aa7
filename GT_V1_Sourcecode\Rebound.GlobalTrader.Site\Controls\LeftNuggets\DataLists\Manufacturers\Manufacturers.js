Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists");Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Manufacturers=function(n){Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Manufacturers.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Manufacturers.prototype={initialize:function(){this.addGetDataOKEvent(Function.createDelegate(this,this.getDataOK));this._strPathToData="controls/DataListNuggets/Manufacturers";this._strDataObject="Manufacturers";Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Manufacturers.callBaseMethod(this,"initialize")},dispose:function(){this.isDisposed||Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Manufacturers.callBaseMethod(this,"dispose")},getDataOK:function(){for(var n,t=0,i=this._objResult.Results.length;t<i;t++)n=this._objResult.Results[t],this._tbl.addRow([String.format('<a href="{0}">{1} - {2}',$RGT_gotoURL_Manufacturer(n.ID),$R_FN.setCleanTextValue(n.Code),$R_FN.setCleanTextValue(n.Name))]),n=null}};Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Manufacturers.registerClass("Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Manufacturers",Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Base,Sys.IDisposable);