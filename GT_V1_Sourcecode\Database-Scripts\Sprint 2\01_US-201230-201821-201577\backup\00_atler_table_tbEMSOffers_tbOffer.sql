/*Alter table tbEMSOffers*/
IF COL_LENGTH('dbo.tbEMSOffers', 'SupplierWarranty') IS NOT NULL
BEGIN
   ALTER TABLE tbEMSOffers DROP COLUMN SupplierWarranty
END

IF COL_LENGTH('dbo.tbEMSOffers', 'CountryOfOriginNo') IS NOT NULL
BEGIN
   ALTER TABLE tbEMSOffers DROP COLUMN CountryOfOriginNo
END

IF COL_LENGTH('dbo.tbEMSOffers', 'SellPrice') IS NOT NULL
BEGIN
   ALTER TABLE tbEMSOffers DROP COLUMN SellPrice
END

IF COL_LENGTH('dbo.tbEMSOffers', 'SellPriceLessReason') IS NOT NULL
BEGIN
   ALTER TABLE tbEMSOffers DROP COLUMN SellPriceLessReason
END

IF COL_LENGTH('dbo.tbEMSOffers', 'ShippingCost') IS NOT NULL
BEGIN
   ALTER TABLE tbEMSOffers DROP COLUMN ShippingCost
END

IF COL_LENGTH('dbo.tbEMSOffers', 'RegionNo') IS NOT NULL
BEGIN
   ALTER TABLE tbEMSOffers DROP COLUMN RegionNo
END

IF COL_LENGTH('dbo.tbEMSOffers', 'DeliveryDate') IS NOT NULL
BEGIN
   ALTER TABLE tbEMSOffers DROP COLUMN DeliveryDate
END

IF COL_LENGTH('dbo.tbEMSOffers', 'TestingRecommended') IS NOT NULL
BEGIN
   ALTER TABLE tbEMSOffers DROP COLUMN TestingRecommended
END

/*Alter table BorisGlobalTraderImports.dbo.tbOffer*/
IF COL_LENGTH('BorisGlobalTraderImports.dbo.tbOffer', 'SupplierWarranty') IS NOT NULL
BEGIN
   ALTER TABLE BorisGlobalTraderImports.dbo.tbOffer DROP COLUMN SupplierWarranty 
END

IF COL_LENGTH('BorisGlobalTraderImports.dbo.tbOffer', 'CountryOfOriginNo') IS NOT NULL
BEGIN
   ALTER TABLE BorisGlobalTraderImports.dbo.tbOffer DROP COLUMN CountryOfOriginNo 
END

IF COL_LENGTH('BorisGlobalTraderImports.dbo.tbOffer', 'SellPrice') IS NOT NULL
BEGIN
   ALTER TABLE BorisGlobalTraderImports.dbo.tbOffer DROP COLUMN SellPrice 
END

IF COL_LENGTH('BorisGlobalTraderImports.dbo.tbOffer', 'SellPriceLessReason') IS NOT NULL
BEGIN
   ALTER TABLE BorisGlobalTraderImports.dbo.tbOffer DROP COLUMN SellPriceLessReason 
END

IF COL_LENGTH('BorisGlobalTraderImports.dbo.tbOffer', 'ShippingCost') IS NOT NULL
BEGIN
   ALTER TABLE BorisGlobalTraderImports.dbo.tbOffer DROP COLUMN ShippingCost 
END

IF COL_LENGTH('BorisGlobalTraderImports.dbo.tbOffer', 'RegionNo') IS NOT NULL
BEGIN
   ALTER TABLE BorisGlobalTraderImports.dbo.tbOffer DROP COLUMN RegionNo 
END

IF COL_LENGTH('BorisGlobalTraderImports.dbo.tbOffer', 'DeliveryDate') IS NOT NULL
BEGIN
   ALTER TABLE BorisGlobalTraderImports.dbo.tbOffer DROP COLUMN DeliveryDate 
END

IF COL_LENGTH('BorisGlobalTraderImports.dbo.tbOffer', 'TestingRecommended') IS NOT NULL
BEGIN
   ALTER TABLE BorisGlobalTraderImports.dbo.tbOffer DROP COLUMN TestingRecommended 
END

IF COL_LENGTH('BorisGlobalTraderImports.dbo.tbOffer', 'AlternateStatus') IS NOT NULL
BEGIN
   ALTER TABLE BorisGlobalTraderImports.dbo.tbOffer DROP COLUMN AlternateStatus 
END