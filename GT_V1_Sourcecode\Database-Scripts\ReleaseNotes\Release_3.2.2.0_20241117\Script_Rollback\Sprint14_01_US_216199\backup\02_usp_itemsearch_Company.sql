﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO
CREATE OR ALTER PROCEDURE [dbo].[usp_itemsearch_Company]
	--********************************************************************************                    
	--* RP 11.11.2009:                    
	--* - remove flabby view, use function to get DaysSinceLastContact                    
	--*                    
	--* RP 02.11.2009:                    
	--* - add WITH RECOMPILE (task 339)                    
	--*                    
	--* SK 01.11.2009:                    
	--* - allow for new column - FullName - used for searching                    
	--*                    
	--* RP 21.05.2009:                    
	--* - sorted out PO and SO approved parameters                     
	--*  (they would be more correctly named RestrictToOnly[PO/SO]Approved...)                    
	--*                    
	--* RP 19.05.2009:                    
	--* - rewrite to CTE, default parameters, pre-calc paging, return only required fields                   
	--* Marker     Changed by      Date          Remarks                          
	--*[CR:SI]      Vinay          18-07-2013    CR:- Add Supplier code in company item search grid                   
	--********************************************************************************                    
	@ClientId INT
	,@OrderBy INT = 1
	,@SortDir INT = 1
	,@PageIndex INT = 0
	,@PageSize INT = 10
	,@NameSearch NVARCHAR(50) = NULL
	,@POApproved BIT = 0
	,@SOApproved BIT = 0
	,@IncludeOnStop BIT = 0
	,@ExcludeSupplierOnStop BIT = 0
	,@PONoLo INT = NULL
	,@PONoHi INT = NULL
	WITH RECOMPILE
AS
BEGIN
	DECLARE @StartPage INT
		,@EndPage INT

	SET @StartPage = (@PageIndex * @PageSize + 1)
	SET @EndPage = ((@PageIndex + 1) * @PageSize)

	IF OBJECT_ID('tempdb..#TEMPCOMSEARCH') IS NOT NULL
		DROP TABLE #TEMPCOMSEARCH

	IF OBJECT_ID('tempdb..#TEMPCOMSEARCH2') IS NOT NULL
		DROP TABLE #TEMPCOMSEARCH2

	--Search for PO Number      
	IF (
			(NOT @PONoHi IS NULL)
			OR (NOT @PONoHi IS NULL)
			)
	BEGIN
		IF OBJECT_ID('tempdb..#tempComp') IS NOT NULL
			DROP TABLE #tempComp

		SELECT DISTINCT CompanyNo
		INTO #tempComp
		FROM tbPurchaseOrder po
		WHERE po.ClientNo = @ClientId
			AND (
				(@PONoLo IS NULL)
				OR (
					NOT @PONoLo IS NULL
					AND PurchaseOrderNumber >= @PONoLo
					)
				)
			AND (
				(@PONoHi IS NULL)
				OR (
					NOT @PONoHi IS NULL
					AND PurchaseOrderNumber <= @PONoHi
					)
				)

		SELECT CompanyId
			,CompanyName
			,b.[Name] AS CompanyType
			,City
			,d.CountryName AS Country
			,a.Telephone
			,Salesman
			,j.EmployeeName AS SalesmanName
			,dbo.ufn_days_since_last_contact_for_company(CompanyId) AS DaysSinceContact
			--[CR:SI] code start                
			,a.SupplierCode
			--[CR:SI] code end               
			,b.IsTraceability
			,ROW_NUMBER() OVER (
				ORDER BY --                    
					CASE 
						WHEN @OrderBy = 1
							AND @SortDir = 2
							THEN CompanyName
						END DESC
					,CASE 
						WHEN @OrderBy = 1
							THEN CompanyName
						END
					,CASE 
						WHEN @OrderBy = 2
							AND @SortDir = 2
							THEN b.[Name]
						END DESC
					,CASE 
						WHEN @OrderBy = 2
							THEN b.[Name]
						END
					,CASE 
						WHEN @OrderBy = 3
							AND @SortDir = 2
							THEN City
						END DESC
					,CASE 
						WHEN @OrderBy = 3
							THEN City
						END
					,CASE 
						WHEN @OrderBy = 4
							AND @SortDir = 2
							THEN CountryName
						END DESC
					,CASE 
						WHEN @OrderBy = 4
							THEN CountryName
						END
					,CASE 
						WHEN @OrderBy = 6
							AND @SortDir = 2
							THEN EmployeeName
						END DESC
					,CASE 
						WHEN @OrderBy = 6
							THEN EmployeeName
						END
					,CASE 
						WHEN @OrderBy = 7
							AND @SortDir = 2
							THEN dbo.ufn_days_since_last_contact_for_company(CompanyId)
						END DESC
					,CASE 
						WHEN @OrderBy = 7
							THEN dbo.ufn_days_since_last_contact_for_company(CompanyId)
						END
				) AS RowNum
		INTO #TEMPCOMSEARCH
		FROM dbo.tbCompany a
		LEFT JOIN dbo.tbCompanyType b ON a.TypeNo = b.CompanyTypeId
		LEFT JOIN dbo.tbCompanyAddress i ON a.CompanyId = i.CompanyNo
			AND i.DefaultBilling = 1
			AND i.CeaseDate IS NULL
		LEFT JOIN dbo.tbAddress c ON c.AddressId = i.AddressNo
		LEFT JOIN dbo.tbCountry d ON c.CountryNo = d.CountryId
			AND a.ClientNo = d.ClientNo
		LEFT JOIN dbo.tbLogin j ON a.Salesman = j.LoginId
		--Added Espire: 31st July 2018, Search supplier on basis of PO number         
		JOIN #tempComp T ON a.CompanyId = T.CompanyNo
		WHERE a.Inactive = 0
			AND isnull(a.IsPOHub, 0) = 0
			AND a.ClientNo = @ClientId
			AND (
				@NameSearch IS NULL
				OR (
					NOT @NameSearch IS NULL
					AND FullName LIKE @NameSearch
					)
				)
			--Espire: 29 Oct 2021: User can create PO in the case the above field is checked for Supplier.     
			--But PO not be approved until the Supplier is approved from the companys Details tab.     
			--AND (@POApproved = 0                    
			--     OR (@POApproved = 1                    
			--         AND POApproved = 1))     
			AND (
				@POApproved = 0
				OR (
					@POApproved = 1
					AND IsSupplier = 1
					)
				)
			AND (
				@SOApproved = 0
				OR (
					@SOApproved = 1
					AND SOApproved = 1
					)
				)
			-- Below code commented as per client request by Bhooma    
			--AND ((@IncludeOnStop = 1)                
			--                         OR (@IncludeOnStop = 0                    
			--                                AND isnull(OnStop,0) = 0))               
			AND (
				(@ExcludeSupplierOnStop = 0)
				OR (
					@ExcludeSupplierOnStop = 1
					AND ISNULL(a.SupplierOnStop, 0) = 0
					)
				)

		SELECT DISTINCT *
			,(
				SELECT DISTINCT count(*)
				FROM #TEMPCOMSEARCH
				) AS RowCnt
		FROM #TEMPCOMSEARCH
		WHERE RowNum BETWEEN @StartPage
				AND @EndPage
	END
	ELSE
	BEGIN
		SELECT CompanyId
			,CompanyName
			,b.[Name] AS CompanyType
			,City
			,d.CountryName AS Country
			,a.Telephone
			,Salesman
			,j.EmployeeName AS SalesmanName
			,dbo.ufn_days_since_last_contact_for_company(CompanyId) AS DaysSinceContact
			--[CR:SI] code start                
			,a.SupplierCode
			--[CR:SI] code end               
			,b.IsTraceability
			,ROW_NUMBER() OVER (
				ORDER BY --                    
					CASE 
						WHEN @OrderBy = 1
							AND @SortDir = 2
							THEN CompanyName
						END DESC
					,CASE 
						WHEN @OrderBy = 1
							THEN CompanyName
						END
					,CASE 
						WHEN @OrderBy = 2
							AND @SortDir = 2
							THEN b.[Name]
						END DESC
					,CASE 
						WHEN @OrderBy = 2
							THEN b.[Name]
						END
					,CASE 
						WHEN @OrderBy = 3
							AND @SortDir = 2
							THEN City
						END DESC
					,CASE 
						WHEN @OrderBy = 3
							THEN City
						END
					,CASE 
						WHEN @OrderBy = 4
							AND @SortDir = 2
							THEN CountryName
						END DESC
					,CASE 
						WHEN @OrderBy = 4
							THEN CountryName
						END
					,CASE 
						WHEN @OrderBy = 6
							AND @SortDir = 2
							THEN EmployeeName
						END DESC
					,CASE 
						WHEN @OrderBy = 6
							THEN EmployeeName
						END
					,CASE 
						WHEN @OrderBy = 7
							AND @SortDir = 2
							THEN dbo.ufn_days_since_last_contact_for_company(CompanyId)
						END DESC
					,CASE 
						WHEN @OrderBy = 7
							THEN dbo.ufn_days_since_last_contact_for_company(CompanyId)
						END
				) AS RowNum
		INTO #TEMPCOMSEARCH2
		FROM dbo.tbCompany a
		LEFT JOIN dbo.tbCompanyType b ON a.TypeNo = b.CompanyTypeId
		LEFT JOIN dbo.tbCompanyAddress i ON a.CompanyId = i.CompanyNo
			AND i.DefaultBilling = 1
			AND i.CeaseDate IS NULL
		LEFT JOIN dbo.tbAddress c ON c.AddressId = i.AddressNo
		LEFT JOIN dbo.tbCountry d ON c.CountryNo = d.CountryId
			AND a.ClientNo = d.ClientNo
		LEFT JOIN dbo.tbLogin j ON a.Salesman = j.LoginId
		WHERE a.Inactive = 0
			AND isnull(a.IsPOHub, 0) = 0
			AND a.ClientNo = @ClientId
			AND (
				@NameSearch IS NULL
				OR (
					NOT @NameSearch IS NULL
					AND FullName LIKE @NameSearch
					)
				)
			--Espire: 29 Oct 2021: User can create PO in the case the above field is checked for Supplier.     
			--But PO not be approved until the Supplier is approved from the companys Details tab.                   
			--AND (@POApproved = 0                    
			--     OR (@POApproved = 1                    
			--         AND POApproved = 1))      
			AND (
				@POApproved = 0
				OR (
					@POApproved = 1
					AND IsSupplier = 1
					)
				)
			AND (
				@SOApproved = 0
				OR (
					@SOApproved = 1
					AND SOApproved = 1
					)
				)
			-- Below code commented as per client request by Bhooma       
			--AND ((@IncludeOnStop = 1)                    
			--     OR (@IncludeOnStop = 0                    
			--         AND isnull(OnStop,0) = 0))               
			AND (
				(@ExcludeSupplierOnStop = 0)
				OR (
					@ExcludeSupplierOnStop = 1
					AND ISNULL(a.SupplierOnStop, 0) = 0
					)
				)

		SELECT *
			,(
				SELECT count(*)
				FROM #TEMPCOMSEARCH2
				) AS RowCnt
		FROM #TEMPCOMSEARCH2
		WHERE RowNum BETWEEN @StartPage
				AND @EndPage
	END
END
