///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-------------------------------------------------------------------------------------------
// RP 14.03.2011:
// - add specific queries for YTD and Last Year values to prevent timeouts
//
// RP 06.01.2010:
// - fully dispose everything
//
// RP 17.11.2009:
// - get the YTD / last year values in one hit
// - add SupplierNo
// - don't get data straight away because the Company Detail page now switches tabs
//   with javascript
//Marker     Changed by      Date          Remarks
//[001]      Vinay          25/03/2014     ESMS Ref:107 -  Add provision to Default Shipping from Country 
//[002]      Aashu Singh    14-Sep-2018    [REB-12820]:Provision to add Global Security on Contact Section
//-------------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");

Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyPurchasingInfo = function(element) { 
	Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyPurchasingInfo.initializeBase(this, [element]);
    this._intCompanyID = -1;
    this._inactive = false;
};

Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyPurchasingInfo.prototype = {

    get_intCompanyID: function() { return this._intCompanyID; }, set_intCompanyID: function(value) { if (this._intCompanyID !== value) this._intCompanyID = value; },
    get_ibtnEdit: function() { return this._ibtnEdit; }, set_ibtnEdit: function(value) { if (this._ibtnEdit !== value) this._ibtnEdit = value; },
    get_tblOpenPOs: function() { return this._tblOpenPOs; }, set_tblOpenPOs: function(value) { if (this._tblOpenPOs !== value) this._tblOpenPOs = value; },
    get_pnlOpenPOs: function() { return this._pnlOpenPOs; }, set_pnlOpenPOs: function(value) { if (this._pnlOpenPOs !== value) this._pnlOpenPOs = value; },
    get_pnlGetOpenPOs: function() { return this._pnlGetOpenPOs; }, set_pnlGetOpenPOs: function(value) { if (this._pnlGetOpenPOs !== value) this._pnlGetOpenPOs = value; },
    get_hypGetOpenPOs: function() { return this._hypGetOpenPOs; }, set_hypGetOpenPOs: function(value) { if (this._hypGetOpenPOs !== value) this._hypGetOpenPOs = value; },
    get_pnlLoadingOpenPOs: function() { return this._pnlLoadingOpenPOs; }, set_pnlLoadingOpenPOs: function(value) { if (this._pnlLoadingOpenPOs !== value) this._pnlLoadingOpenPOs = value; },
    get_pnlOpenPOsError: function() { return this._pnlOpenPOsError; }, set_pnlOpenPOsError: function(value) { if (this._pnlOpenPOsError !== value) this._pnlOpenPOsError = value; },
    get_tblOverduePOs: function() { return this._tblOverduePOs; }, set_tblOverduePOs: function(value) { if (this._tblOverduePOs !== value) this._tblOverduePOs = value; },
    get_pnlOverduePOs: function() { return this._pnlOverduePOs; }, set_pnlOverduePOs: function(value) { if (this._pnlOverduePOs !== value) this._pnlOverduePOs = value; },
    get_pnlLoadingOverduePOs: function() { return this._pnlLoadingOverduePOs; }, set_pnlLoadingOverduePOs: function(value) { if (this._pnlLoadingOverduePOs !== value) this._pnlLoadingOverduePOs = value; },
    get_pnlOverduePOsError: function() { return this._pnlOverduePOsError; }, set_pnlOverduePOsError: function(value) { if (this._pnlOverduePOsError !== value) this._pnlOverduePOsError = value; },
    get_pnlGetOverduePOs: function() { return this._pnlGetOverduePOs; }, set_pnlGetOverduePOs: function(value) { if (this._pnlGetOverduePOs !== value) this._pnlGetOverduePOs = value; },
    get_hypGetOverduePOs: function() { return this._hypGetOverduePOs; }, set_hypGetOverduePOs: function(value) { if (this._hypGetOverduePOs !== value) this._hypGetOverduePOs = value; },
    get_CanStopSupplier: function() { return this._CanStopSupplier; }, set_CanStopSupplier: function(value) { if (this._CanStopSupplier !== value) this._CanStopSupplier = value; },

    initialize: function() {
        Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyPurchasingInfo.callBaseMethod(this, "initialize");
        this._strPathToData = "controls/Nuggets/CompanyPurchasingInfo";
        this._strDataObject = "CompanyPurchasingInfo";
        this.addRefreshEvent(Function.createDelegate(this, this.getData));
        $addHandler(this._hypGetOpenPOs, "click", Function.createDelegate(this, this.getOpenPOs));
        $addHandler(this._hypGetOverduePOs, "click", Function.createDelegate(this, this.getOverduePOs));

        //ellipses clicks
        this._fldYearToDate = this.getEllipsesControl("ctlYearToDate");
        this._fldYearToDate.addSetupData(Function.createDelegate(this, this.getYearToDate));
        this._fldLastYear = this.getEllipsesControl("ctlLastYear");
        this._fldLastYear.addSetupData(Function.createDelegate(this, this.getLastYear));

        //edit form
        if (this._ibtnEdit) {
            $R_IBTN.addClick(this._ibtnEdit, Function.createDelegate(this, this.showEditForm));
            this._frmEdit = $find(this._aryFormIDs[0]);
            this._frmEdit.addCancel(Function.createDelegate(this, this.cancelEdit));
            this._frmEdit.addSaveComplete(Function.createDelegate(this, this.saveEditComplete));
            this._frmEdit.addSaveError(Function.createDelegate(this, this.saveEditError));
        }
    },

    dispose: function() {
        if (this.isDisposed) return;
        if (this._hypGetOpenPOs) $clearHandlers(this._hypGetOpenPOs);
        if (this._hypGetOverduePOs) $clearHandlers(this._hypGetOverduePOs);
        if (this._ibtnEdit) $R_IBTN.clearHandlers(this._ibtnEdit);
        if (this._frmEdit) this._frmEdit.dispose();
        if (this._tblOpenPOs) this._tblOpenPOs.dispose();
        if (this._tblOverduePOs) this._tblOverduePOs.dispose();
        this._intCompanyID = null;
        this._ibtnEdit = null;
        this._frmEdit = null;
        this._tblOpenPOs = null;
        this._pnlOpenPOs = null;
        this._pnlGetOpenPOs = null;
        this._hypGetOpenPOs = null;
        this._pnlLoadingOpenPOs = null;
        this._pnlOpenPOsError = null;
        this._tblOverduePOs = null;
        this._pnlOverduePOs = null;
        this._pnlLoadingOverduePOs = null;
        this._pnlOverduePOsError = null;
        this._pnlGetOverduePOs = null;
        this._hypGetOverduePOs = null;
        this._CanStopSupplier = null;
        this._blnEditHubSupplier = null;
        this._inactive = null;
        Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyPurchasingInfo.callBaseMethod(this, "dispose");
    },

    getData: function() {
        this.getData_Start();
        this.showOpenPOsGetData(true);
        this.showOpenPOsError(false);
        this.showOverduePOsGetData(true);
        this.showOverduePOsError(false);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData(this._strPathToData);
        obj.set_DataObject(this._strDataObject);
        obj.set_DataAction("GetData");
        obj.addParameter("id", this._intCompanyID);
        obj.addDataOK(Function.createDelegate(this, this.getDataOK));
        obj.addError(Function.createDelegate(this, this.getDataError));
        obj.addTimeout(Function.createDelegate(this, this.getDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        this.getOpenPOs();
        this.getOverduePOs();
        obj = null;
    },

    getDataOK: function(args) {
        var res = args._result;
        this.setFieldValue("ctlIsApproved", $R_FN.getApprovedByAndDate(res.IsApproved, res.ApprovedByAndDate));
        this.setFieldValue("hidIsApproved", res.IsApproved);
        this.setFieldValue("ctlCurrency", res.Currency);
        this.setFieldValue("hidCurrencyNo", res.CurrencyNo);
        this.setFieldValue("ctlTerms", res.Terms);
        this.setFieldValue("hidTermsNo", res.TermsNo);
        //ESMS #14
        //this.setFieldValue("ctlTax", res.Tax);
        //this.setFieldValue("hidTaxNo", res.TaxNo);
        this.setFieldValue("ctlRating", res.Rating);
        this.setFieldValue("ctlShipVia", res.ShipVia);
        this.setFieldValue("hidShipViaNo", res.ShipViaNo);
        this.setFieldValue("ctlShippingAccountNo", res.ShippingAccountNo);
        this.setFieldValue("hidContactNo", res.ContactNo);
        this.setFieldValue("ctlContact", $RGT_nubButton_Contact(res.ContactNo, res.ContactName));
        this.setFieldValue("ctlYearToDate", res.YearToDate);
        this.setFieldValue("ctlLastYear", res.LastYear);
        this.setFieldValue("ctlSupplierNo", res.SupplierCode);
        //[001] code start
        this.setFieldValue("hidPOShipCountryNo", res.DefaultPOCountryNo);
        this.setFieldValue("ctlPOShipCountry", res.DefaultPOCountry);
        //[001] code end
        this.setFieldValue("ctlOnStop", res.OnStop);
        this.setFieldValue("hidSupOnStop", res.OnStop);
        this._inactive = res.Inactive;
        if (this._ibtnEdit) $R_IBTN.enableButton(this._ibtnEdit, !this._inactive);

        this.setDLUP(res.DLUP);
        this.getDataOK_End();
    },

    getDataError: function(args) {
        this.showError(true, args.get_ErrorMessage());
    },

    getOpenPOs: function() {
        this.showLoadingOpenPOs(true);
        this.showOpenPOsError(false);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData(this._strPathToData);
        obj.set_DataObject(this._strDataObject);
        obj.set_DataAction("GetOpenPOs");
        obj.addParameter("id", this._intCompanyID);
        obj.addDataOK(Function.createDelegate(this, this.getOpenPOsOK));
        obj.addError(Function.createDelegate(this, this.getOpenPOsError));
        obj.addTimeout(Function.createDelegate(this, this.getOpenPOsError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getOpenPOsOK: function(args) {
        res = args._result;
        this.showLoadingOpenPOs(false);
        this.showOpenPOsError(false);
        this._tblOpenPOs.clearTable();
        this.processPOList(this._tblOpenPOs);
        this._tblOpenPOs.resizeColumns();
    },

    getOpenPOsError: function(args) {
        this.showLoadingOpenPOs(false);
        this.showOpenPOsError(true, args.get_ErrorMessage());
    },

    showLoadingOpenPOs: function(blnShow) {
        $R_FN.showElement(this._pnlLoadingOpenPOs, blnShow);
        $R_FN.showElement(this._pnlOpenPOs, !blnShow);
        this.showLoading(blnShow);
        if (blnShow) $R_FN.showElement(this._pnlGetOpenPOs, false);
    },

    showOpenPOsError: function(blnShow, strMessage) {
        $R_FN.showElement(this._pnlOpenPOsError, blnShow);
        if (blnShow) {
            this.showLoading(false);
            $R_FN.showElement(this._pnlOpenPOs, false);
            $R_FN.showElement(this._pnlGetOpenPOs, false);
            $R_FN.setInnerHTML(this._pnlOpenPOsError, strMessage);
        }
    },

    showOpenPOsGetData: function(blnShow) {
        $R_FN.showElement(this._pnlGetOpenPOs, blnShow);
        if (blnShow) {
            this.showLoading(false);
            $R_FN.showElement(this._pnlLoadingOpenPOs, false);
            $R_FN.showElement(this._pnlOpenPOs, false);
            $R_FN.setInnerHTML(this._pnlOpenPOsError, false);
        }
    },

    processPOList: function(tbl) {
        if (res.Items) {
            for (var i = 0; i < res.Items.length; i++) {
                var row = res.Items[i];
                var aryData = [
					$RGT_nubButton_PurchaseOrder(row.ID, row.No),
					row.Date,
					row.Amount
				];
                tbl.addRow(aryData, row.ID, false);
                row = null; aryData = null;
            }
        }
    },

    getOverduePOs: function() {
        this.showLoadingOverduePOs(true);
        this.showOverduePOsError(false);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData(this._strPathToData);
        obj.set_DataObject(this._strDataObject);
        obj.set_DataAction("GetOverduePOs");
        obj.addParameter("id", this._intCompanyID);
        obj.addDataOK(Function.createDelegate(this, this.getOverduePOsOK));
        obj.addError(Function.createDelegate(this, this.getOverduePOsError));
        obj.addTimeout(Function.createDelegate(this, this.getOverduePOsError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getOverduePOsOK: function(args) {
        res = args._result;
        this.showLoadingOverduePOs(false);
        this.showOverduePOsError(false);
        this._tblOverduePOs.clearTable();
        this.processPOList(this._tblOverduePOs);
        this._tblOverduePOs.resizeColumns();
    },

    getOverduePOsError: function(args) {
        this.showLoadingOverduePOs(false);
        this.showOverduePOsError(true, args.get_ErrorMessage());
    },

    showLoadingOverduePOs: function(blnShow) {
        $R_FN.showElement(this._pnlLoadingOverduePOs, blnShow);
        $R_FN.showElement(this._pnlOverduePOs, !blnShow);
        this.showLoading(blnShow);
        if (blnShow) $R_FN.showElement(this._pnlGetOverduePOs, false);
    },

    showOverduePOsError: function(blnShow, strMessage) {
        $R_FN.showElement(this._pnlOverduePOsError, blnShow);
        if (blnShow) {
            this.showLoading(false);
            $R_FN.showElement(this._pnlOverduePOs, false);
            $R_FN.showElement(this._pnlGetOverduePOs, false);
            $R_FN.setInnerHTML(this._pnlOverduePOsError, strMessage);
        }
    },

    showOverduePOsGetData: function(blnShow) {
        $R_FN.showElement(this._pnlGetOverduePOs, blnShow);
        if (blnShow) {
            this.showLoading(false);
            $R_FN.showElement(this._pnlLoadingOverduePOs, false);
            $R_FN.showElement(this._pnlOverduePOs, false);
            $R_FN.setInnerHTML(this._pnlOverduePOsError, false);
        }
    },

    showEditForm: function() {
        var intCMID = this._intCompanyID;
        this._frmEdit.getFieldComponent("ctlContact")._intCompanyID = intCMID;
        this._frmEdit.setFieldValue("ctlApproved", Boolean.parse(this.getFieldValue("hidIsApproved")));
        this._frmEdit.setFieldValue("ctlCurrency", this.getFieldValue("hidCurrencyNo"));
        this._frmEdit.setFieldValue("ctlTerms", this.getFieldValue("hidTermsNo"));
        //ESMS #14
        //this._frmEdit.setFieldValue("ctlTax", this.getFieldValue("hidTaxNo"));
        this._frmEdit.setFieldValue("ctlRating", this.getFieldValue("ctlRating"));
        this._frmEdit.setFieldValue("ctlShipVia", this.getFieldValue("hidShipViaNo"));
        this._frmEdit.setFieldValue("ctlShippingAccountNo", this.getFieldValue("ctlShippingAccountNo"));
        this._frmEdit.setFieldValue("ctlContact", this.getFieldValue("hidContactNo"));
        this._frmEdit.setFieldValue("ctlSupplierNo", this.getFieldValue("ctlSupplierNo"));
        //[001] code start
        this._frmEdit.setFieldValue("ctlCountry", this.getFieldValue("hidPOShipCountryNo"));
        this._frmEdit.showField("ctlOnStop", this._CanStopSupplier);
        this._frmEdit.setFieldValue("ctlOnStop", this.getFieldValue("ctlOnStop"));
        //[001] code end
        //[002] start
        this._frmEdit._globalLoginClientNo = this._globalLoginClientNo;
        //[002] end
        this.showForm(this._frmEdit, true);
    },

    cancelEdit: function() {
        this.showContent(true);
        this.showForm(this._frmEdit, false);
    },

    saveEditComplete: function() {
        this.showForm(this._frmEdit, false);
        this.getData();
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
        this.onSaveEditComplete();
    },

    saveEditError: function() {
        this.showForm(this._frmEdit, false);
        this.showError(true, this._frmEdit._strErrorMessage);
    },

    getYearToDate: function() {
        var obj = this._fldYearToDate._objData;
        obj.set_PathToData(this._strPathToData);
        obj.set_DataObject(this._strDataObject);
        obj.set_DataAction("GetYearToDate");
        obj.addParameter("id", this._intCompanyID);
    },

    getLastYear: function() {
        var obj = this._fldLastYear._objData;
        obj.set_PathToData(this._strPathToData);
        obj.set_DataObject(this._strDataObject);
        obj.set_DataAction("GetLastYear");
        obj.addParameter("id", this._intCompanyID);
    },
    enableButtons: function (bln) {
        //$R_IBTN.enableButton(this._ibtnEdit, bln);
        if (this._ibtnEdit) {
            $R_IBTN.enableButton(this._ibtnEdit, bln && !this._inactive);
        }
    }
   

};

Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyPurchasingInfo.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyPurchasingInfo", Rebound.GlobalTrader.Site.Controls.Nuggets.Base);
