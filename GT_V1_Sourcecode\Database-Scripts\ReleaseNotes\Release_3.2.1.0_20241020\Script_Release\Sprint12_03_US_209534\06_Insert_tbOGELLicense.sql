﻿SET NOCOUNT ON

SET IDENTITY_INSERT [tbOGELLicense] ON

/*===========================================================================================  
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-209534]     NgaiTo		 	17-Sep-2024			UPDATE		209534: OGEL approval dropdown to be moved out of code to the setup screen
===========================================================================================  
*/

MERGE INTO [tbOGELLicense] AS Target
USING (VALUES
  (1,'GBOGE2020/00615',NULL,0,'2024-10-08T11:04:50.300',1)
 ,(2,'GBOGE2024/00532 - OGEL MIL GMST',NULL,0,'2024-10-08T11:05:06.583',1)
 ,(3,'GBOGE2024/00756 - OGEL PCB Comp MIL',NULL,0,'2024-10-08T11:05:21.357',1)
) AS Source ([OgelId],[OgelNumber],[Description],[Inactive],[DLUP],[UpdatedBy])
ON (Target.[OgelId] = Source.[OgelId])
WHEN MATCHED AND (
	NULLIF(Source.[OgelNumber], Target.[OgelNumber]) IS NOT NULL OR NULLIF(Target.[OgelNumber], Source.[OgelNumber]) IS NOT NULL OR 
	NULLIF(Source.[Description], Target.[Description]) IS NOT NULL OR NULLIF(Target.[Description], Source.[Description]) IS NOT NULL OR 
	NULLIF(Source.[Inactive], Target.[Inactive]) IS NOT NULL OR NULLIF(Target.[Inactive], Source.[Inactive]) IS NOT NULL OR 
	NULLIF(Source.[DLUP], Target.[DLUP]) IS NOT NULL OR NULLIF(Target.[DLUP], Source.[DLUP]) IS NOT NULL OR 
	NULLIF(Source.[UpdatedBy], Target.[UpdatedBy]) IS NOT NULL OR NULLIF(Target.[UpdatedBy], Source.[UpdatedBy]) IS NOT NULL) THEN
 UPDATE SET
 [OgelNumber] = Source.[OgelNumber], 
[Description] = Source.[Description], 
[Inactive] = Source.[Inactive], 
[DLUP] = Source.[DLUP], 
[UpdatedBy] = Source.[UpdatedBy]
WHEN NOT MATCHED BY TARGET THEN
 INSERT([OgelId],[OgelNumber],[Description],[Inactive],[DLUP],[UpdatedBy])
 VALUES(Source.[OgelId],Source.[OgelNumber],Source.[Description],Source.[Inactive],Source.[DLUP],Source.[UpdatedBy])
;

GO
DECLARE @mergeError int
 , @mergeCount int
SELECT @mergeError = @@ERROR, @mergeCount = @@ROWCOUNT
IF @mergeError != 0
 BEGIN
 PRINT 'ERROR OCCURRED IN MERGE FOR [tbOGELLicense]. Rows affected: ' + CAST(@mergeCount AS VARCHAR(100)); -- SQL should always return zero rows affected
 END
ELSE
 BEGIN
 PRINT '[tbOGELLicense] rows affected by MERGE: ' + CAST(@mergeCount AS VARCHAR(100));
 END
GO

SET IDENTITY_INSERT [tbOGELLicense] OFF
GO
SET NOCOUNT OFF
GO