using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.Forms {
	public partial class GILines_Edit : Base {

        #region Locals

        //protected Image imgCalculate;
       // protected IconButton _ibtnSend;
        //protected IconButton _ibtnSend_Footer;
        protected FlexiDataTable _tblPackagingBreakdown;
        protected FlexiDataTable _tblDateCode;
        protected IconButton _ibtnSendQuery;
        protected IconButton _ibtnSaveRelease;
        
       
        #endregion

        #region Properties

        private int _intGIID = -1;
		public int GIID {
			get { return _intGIID; }
			set { _intGIID = value; }
		}

		#endregion

		#region Overrides

		/// <summary>
		/// OnInit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
			TitleText = Functions.GetGlobalResource("FormTitles", "GILines_Edit");
			AddScriptReference("Controls.Nuggets.GILines.Edit.GILines_Edit.js");
			if (_objQSManager.GoodsInID > 0) _intGIID = _objQSManager.GoodsInID;
            //WireUpControls();
        }

		/// <summary>
		/// OnPreRender
		/// </summary>
		/// <param name="e"></param>
		protected override void OnPreRender(EventArgs e) {
            WireUpButtons();
            SetupScriptDescriptors();
            //imgCalculate.ImageUrl = Functions.GetThemeImage("nuggets/lcc.gif", Page.Theme);
            PackagingBreakdown();
            DateCode();
            base.OnPreRender(e);
		}

		#endregion

		/// <summary>
		/// Wire up controls to the ascx
		/// </summary>
		private void WireUpControls() {
			//imgCalculate = (Image)FindFieldControl("ctlCost", "imgCalculate");
		}
        private void WireUpButtons()
        {
            //_ibtnSend = (IconButton)FindIconButton("ibtnSend");
            //_ibtnSend_Footer = (IconButton)FindFooterIconButton("ibtnSend");
            //_tblPackagingBreakdown = (FlexiDataTable)ctlDesignBase.FindContentControl("tblPackagingBreakdown");
            //_tblDateCode = (FlexiDataTable)ctlDesignBase.FindContentControl("tblDateCode");
            _ibtnSendQuery = FindIconButton("ibtnSendQuery");
            _ibtnSaveRelease = FindIconButton("ibtnSaveRelease");
            
        }
        /// <summary>
        /// sets up the contact table headings
        /// </summary>
        private void PackagingBreakdown()
        {
            //_tblPackagingBreakdown.Columns.Add(new FlexiDataColumn("PackagingBreakdownType", WidthManager.GetWidth(WidthManager.ColumnWidth.BOMItemsQuantity)));
            //_tblPackagingBreakdown.Columns.Add(new FlexiDataColumn("NumberOfPacks", WidthManager.GetWidth(WidthManager.ColumnWidth.CurrencyValue)));
            //_tblPackagingBreakdown.Columns.Add(new FlexiDataColumn("BreakdownPackSize", WidthManager.GetWidth(WidthManager.ColumnWidth.CurrencyValue)));
            //_tblPackagingBreakdown.Columns.Add(new FlexiDataColumn("TotalNoPackSize"));
        }
        private void DateCode()
        {
            //_tblDateCode.Columns.Add(new FlexiDataColumn("DateCodes", WidthManager.GetWidth(WidthManager.ColumnWidth.CurrencyValue)));
            //_tblDateCode.Columns.Add(new FlexiDataColumn("Quantity"));
        }
        /// <summary>
        /// Setup Script Descriptors
        /// </summary>
        private void SetupScriptDescriptors() {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Forms.GILines_Edit", ctlDesignBase.ClientID);
			_scScriptControlDescriptor.AddProperty("intGIID", _intGIID);
			//_scScriptControlDescriptor.AddElementProperty("lblCurrency_Price", FindFieldControl("txtPrice", "lblCurrency_Price").ClientID);
   //         _scScriptControlDescriptor.AddElementProperty("lblCurrency_PriceLabel", FindFieldControl("ctlPriceLabel", "lblCurrency_PriceLabel").ClientID);

            //_scScriptControlDescriptor.AddElementProperty("lblCurrency_Price_IPO", FindFieldControl("ctlPrice_IPO", "lblCurrency_Price_IPO").ClientID);
            //_scScriptControlDescriptor.AddElementProperty("lblCurrency_PriceLabel_IPO", FindFieldControl("ctlPriceLabel_IPO", "lblCurrency_PriceLabel_IPO").ClientID);
            _scScriptControlDescriptor.AddProperty("IsPOHub", SessionManager.IsPOHub.Value);
            //_scScriptControlDescriptor.AddElementProperty("hypCalculate", FindFieldControl("ctlCost", "hypCalculate").ClientID);
            //_scScriptControlDescriptor.AddElementProperty("ibtnSend", _ibtnSend.ClientID);
            //if (FooterLinksHolder != null) _scScriptControlDescriptor.AddElementProperty("ibtnSend_Footer", _ibtnSend_Footer.ClientID);
            //_scScriptControlDescriptor.AddElementProperty("ibtnContinue", (FindIconButton("ibtnContinue").ClientID));
            //if (FooterLinksHolder != null) _scScriptControlDescriptor.AddElementProperty("ibtnContinue_Footer", (FindFooterIconButton("ibtnContinue").ClientID));

            //_scScriptControlDescriptor.AddComponentProperty("tblPackagingBreakdown", _tblPackagingBreakdown.ClientID);
            //_scScriptControlDescriptor.AddComponentProperty("tblDateCode", _tblDateCode.ClientID);
            _scScriptControlDescriptor.AddElementProperty("ibtnSendQuery", _ibtnSendQuery.ClientID);
            _scScriptControlDescriptor.AddElementProperty("ibtnSaveRelease", _ibtnSaveRelease.ClientID);
            
        }

	}
}