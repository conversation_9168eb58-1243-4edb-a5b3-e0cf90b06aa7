Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DataListNuggets");Rebound.GlobalTrader.Site.Controls.DataListNuggets.Quotes=function(n){Rebound.GlobalTrader.Site.Controls.DataListNuggets.Quotes.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DataListNuggets.Quotes.prototype={get_intSalesPersonID:function(){return this._intSalesPersonID},set_intSalesPersonID:function(n){this._intSalesPersonID!==n&&(this._intSalesPersonID=n)},get_IsGSA:function(){return this._IsGSA},set_IsGSA:function(n){this._IsGSA!==n&&(this._IsGSA=n)},get_ibtnViewTask:function(){return this._ibtnViewTask},set_ibtnViewTask:function(n){this._ibtnViewTask!==n&&(this._ibtnViewTask=n)},initialize:function(){this.addSetupDataCallEvent(Function.createDelegate(this,this.setupDataCall));this.addGetDataOKEvent(Function.createDelegate(this,this.getDataOK));this.addInitCompleteEvent(Function.createDelegate(this,this.initAfterBaseIsReady));this._strPathToData="controls/DataListNuggets/Quotes";this._strDataObject="Quotes";this._ibtnViewTask&&$R_IBTN.addClick(this._ibtnViewTask,Function.createDelegate(this,this.viewTask));this._frmAdd=$find(this._aryFormIDs[0]);this._frmAdd.addCancel(Function.createDelegate(this,this.hideAddForm));this._frmAdd.addSaveComplete(Function.createDelegate(this,this.addComplete));Rebound.GlobalTrader.Site.Controls.DataListNuggets.Quotes.callBaseMethod(this,"initialize")},initAfterBaseIsReady:function(){this.addPageTabChangedEvent(Function.createDelegate(this,this.pageTabChanged));this.applySalesPersonFilter();this.updateFilterVisibility();this.getData()},dispose:function(){this.isDisposed||(this._intSalesPersonID=null,this._IsGSA=null,Rebound.GlobalTrader.Site.Controls.DataListNuggets.Quotes.callBaseMethod(this,"dispose"))},pageTabChanged:function(){this._table._intCurrentPage=1;this._enmViewLevel=this._intCurrentTab;this.updateFilterVisibility();this.getData()},setupDataCall:function(){this._objData.addParameter("ViewLevel",this._enmViewLevel)},getDataOK:function(){for(var n,t="",i=0,u=this._objResult.Results.length;i<u;i++){t="";n=this._objResult.Results[i];t=n.DateOfferStatus=="Green"?"green":n.DateOfferStatus=="Amber"?"#FFBF00":n.DateOfferStatus=="Red"?"Red":n.DateOfferStatus=="White"?"White":"White";var f=String.format("<a href=\"javascript:void(0);\" title='Add task' onclick=\"$find('{0}').showAddForm({1},'{2}','{3}','{4}');\">Add Task<\/a>",this._element.id,n.ID,n.No,$R_FN.setCleanTextValue(n.CM),n.QStatus)+"&nbsp;&nbsp;&nbsp;",e=String.format("<a href=\"javascript:void(0);\" title='View task' onclick=\"$find('{0}').redirectToDetails({1},'{2}');\" style=\"color: {3};\">"+(n.TaskCount+" Task")+"<\/a>",this._element.id,n.ID,n.No,n.HasUnFinishedTask?"red":""),r=[$RGT_nubButton_Quote(n.ID,n.No),$R_FN.writePartNo(n.Part,n.ROHS),n.Price,n.Quantity,$R_FN.writeDoubleCellValue(n.blnMakeYellow==!0?'<span style="background-color:yellow;">'+$RGT_nubButton_Company(n.CMNo,n.CM)+"<\/span>":$RGT_nubButton_Company(n.CMNo,n.CM),$RGT_nubButton_Contact(n.ContactNo,n.Contact)),$R_FN.setCleanTextValue(n.Date),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(n.QuoteOfferedDate),t=="White"?"<span style='background-color:"+t+"!important;float: right;margin-top: -17px;height: 20px;width: 20px;visibility: hidden;'><\/span>":"<span style='background-color:"+t+"!important;float: right;margin-top: -17px;height: 20px;width: 20px;'><\/span>"),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(n.Salesman),$R_FN.setCleanTextValue(n.QStatus)),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(n.TotalValue),$R_FN.setCleanTextValue(n.TotalBase)),$R_FN.setCleanTextValue(n.OfferProfit),f+e];this._table.addRow(r,n.ID,!1);r=null;n=null}},updateFilterVisibility:function(){this.getFilterField("ctlSalesmanName").show(this._enmViewLevel!=0);this.getFilterField("ctlClientName").show(this._IsGSA)},applySalesPersonFilter:function(){this._intSalesPersonID&&this._intSalesPersonID>0&&this.getFilterField("ctlSalesmanName").setValue(this._intSalesPersonID)},viewTask:function(){location.href="Prf_ToDo.aspx?Category=2";"_blank"},showAddForm:function(n,t,i,r){this._frmAdd.setFormFieldsToDefaults();this._frmAdd.setFieldValue("ctlDueTime","09:00");this._frmAdd.setFieldValue("ctlReminderTime","09:00");this._frmAdd.setFieldValue("ctlQuotes",n,null,t);this._frmAdd._intCategoryID=2;this._frmAdd._customerName=i;this._frmAdd._quoteStatus=r;this.showForm(this._frmAdd,!0)},hideAddForm:function(){this._frmAdd.resetFormData();this.showForm(this._frmAdd,!1)},addComplete:function(){this.hideAddForm();this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully);this.getData()},redirectToDetails:function(n,t){location.href="Prf_ToDo.aspx?qn="+t+"&Category=2";"_blank"}};Rebound.GlobalTrader.Site.Controls.DataListNuggets.Quotes.registerClass("Rebound.GlobalTrader.Site.Controls.DataListNuggets.Quotes",Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base,Sys.IDisposable);