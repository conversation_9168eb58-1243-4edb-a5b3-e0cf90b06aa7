/*
Marker      Date        Changed By       Remarks
[001]      <PERSON><PERSON>   21-Jan-2019   Add View Tree Button.
 */
using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site.Controls;
using Rebound.GlobalTrader.Site;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets {
	public partial class DebitMainInfo : Base {

		#region Locals

		protected IconButton _ibtnEdit;
        protected IconButton _ibtnExport;
        protected IconButton _ibtnRelease;
        protected IconButton _ibtnViewTree;//[001]
		#endregion

		#region Properties

		private int _intDebitID = -1;
		public int DebitID {
			get { return _intDebitID; }
			set { _intDebitID = value; }
		}

		private bool _blnCanEdit = true;
		public bool CanEdit {
			get { return _blnCanEdit; }
			set { _blnCanEdit = value; }
		}

        //private bool _blnCanExport = true;
        //public bool CanExport
        //{
        //    get { return _blnCanExport; }
        //    set { _blnCanExport = value; }
        //}

        private bool _blnCanExportRelease = true;
        public bool CanExportRelease
        {
            get { return _blnCanExportRelease; }
            set { _blnCanExportRelease = value; }
        }
		private bool _IsGSAEditPermission = true;
		public bool IsGSAEditPermission
		{
			get { return _IsGSAEditPermission; }
			set { _IsGSAEditPermission = value; }
		}
		private bool _IsDiffrentClient = false;
		public bool IsDiffrentClient
		{
			get { return _IsDiffrentClient; }
			set { _IsDiffrentClient = value; }
		}
		private bool _IsGSA = false;
		public bool IsGSA
		{
			get { return _IsGSA; }
			set { _IsGSA = value; }
		}
		#endregion

		#region Overrides

		/// <summary>
		/// Oninit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
            WireUpControls();
			AddScriptReference("Controls.Nuggets.DebitMainInfo.DebitMainInfo.js");
			TitleText = Functions.GetGlobalResource("Nuggets", "DebitMainInfo");
			if (_objQSManager.DebitID > 0) _intDebitID = _objQSManager.DebitID;
		}

		protected override void OnPreRender(EventArgs e) {
			_ibtnEdit.Visible = _blnCanEdit;
            _ibtnExport.Visible = _blnCanExportRelease;
            _ibtnRelease.Visible = _blnCanExportRelease;

            if (SessionManager.IsGlobalUser != true)
            {
				if (_IsDiffrentClient == true)
				{
					if (SessionManager.IsGSA == true)
					{
						if (_IsGSAEditPermission == true)
						{
							_ibtnEdit.Visible = true;
							_ibtnExport.Visible = true;
							_ibtnRelease.Visible = true;
						}
						else
						{
							_ibtnEdit.Visible = false;
							_ibtnExport.Visible = false;
							_ibtnRelease.Visible = false;
						}
					}
				}
			}

			SetupScriptDescriptors();
			base.OnPreRender(e);
		}

		#endregion

		/// <summary>
		/// Sets up AJAX script descriptors
		/// </summary>
		private void SetupScriptDescriptors() {
			if (SessionManager.IsGlobalUser != true)
            {
				_IsGSA = SessionManager.IsGSA.Value;
			}
            else
            {
				_IsGSA = false;
			}
				
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Nuggets.DebitMainInfo", ctlDesignBase.ClientID);
			if (_blnCanEdit) _scScriptControlDescriptor.AddElementProperty("ibtnEdit", _ibtnEdit.ClientID);
			_scScriptControlDescriptor.AddProperty("intDebitID", _intDebitID);
            _scScriptControlDescriptor.AddProperty("blnGlobalLogin", SessionManager.IsGlobalUser.Value);
            if (_blnCanExportRelease) _scScriptControlDescriptor.AddElementProperty("ibtnExport", _ibtnExport.ClientID);
            if (_blnCanExportRelease) _scScriptControlDescriptor.AddElementProperty("ibtnRelease", _ibtnRelease.ClientID);
            _scScriptControlDescriptor.AddElementProperty("ibtnViewTree", _ibtnViewTree.ClientID);//[001]
			
			_scScriptControlDescriptor.AddProperty("IsDiffrentClient", _IsDiffrentClient);
			_scScriptControlDescriptor.AddProperty("IsGSAEditPermission", _IsGSAEditPermission);
			_scScriptControlDescriptor.AddProperty("IsGSA", _IsGSA);
		}

		/// <summary>
		/// Wire up controls from the ascx
		/// </summary>
		private void WireUpControls() {
			_ibtnEdit = FindIconButton("ibtnEdit");
            _ibtnExport = FindIconButton("ibtnExport");
            _ibtnRelease = FindIconButton("ibtnRelease");
            _ibtnViewTree = FindIconButton("ibtnViewTree");//[001]
		}

	}
}
