Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DataListNuggets");Rebound.GlobalTrader.Site.Controls.DataListNuggets.CustomerRequirementsBOMImport=function(n){Rebound.GlobalTrader.Site.Controls.DataListNuggets.CustomerRequirementsBOMImport.initializeBase(this,[n]);this._blnGet=!0;this._intCompanyNo=-1;this._intContactNo=-1};Rebound.GlobalTrader.Site.Controls.DataListNuggets.CustomerRequirementsBOMImport.prototype={get_intCompanyID:function(){return this._intCompanyID},set_intCompanyID:function(n){this._intCompanyID!==n&&(this._intCompanyID=n)},get_strCompanyName:function(){return this._strCompanyName},set_strCompanyName:function(n){this._strCompanyName!==n&&(this._strCompanyName=n)},get_intContactID:function(){return this._intContactID},set_intContactID:function(n){this._intContactID!==n&&(this._intContactID=n)},initialize:function(){this.addSetupDataCallEvent(Function.createDelegate(this,this.setupDataCall));this.addGetDataOKEvent(Function.createDelegate(this,this.getDataOK));this.addInitCompleteEvent(Function.createDelegate(this,this.initAfterBaseIsReady));this.addSortDataEvent(Function.createDelegate(this,this.getSorting));this._strPathToData="controls/DataListNuggets/CustomerRequirementsBOMImport";this._strDataObject="CustomerRequirementsBOMImport";this.enableBulkButtons(!1);this._table.addMultipleSelectionChanged(Function.createDelegate(this,this.selectionMade));this._strCK="Req";this._strCKExp=1;this._lnsSeperator="|";this._ibtnPrint&&$R_IBTN.addClick(this._ibtnPrint,Function.createDelegate(this,this.printCustReq));this._table._intSortColumnIndex=4;this._table._enmSortDirection=2;Rebound.GlobalTrader.Site.Controls.DataListNuggets.CustomerRequirementsBOMImport.callBaseMethod(this,"initialize")},initAfterBaseIsReady:function(){this.getData()},dispose:function(){this.isDisposed||(this._intCompanyID=null,this._strCompanyName=null,this._intContactID=null,this._blnGet=null,this._intCompanyNo=null,this._intContactNo=null,this._ibtnPrint&&(this._ibtnPrint=null),Rebound.GlobalTrader.Site.Controls.DataListNuggets.CustomerRequirementsBOMImport.callBaseMethod(this,"dispose"))},enableBulkButtons:function(n){this._ibtnPrint&&$R_IBTN.enableButton(this._ibtnPrint,n)},setupDataCall:function(){this._objData.addParameter("CmpID",this._intCompanyNo);this._objData.addParameter("ConID",this._intContactNo);this._objData.addParameter("IsGet",this._blnGet);this._blnGet=!1;this._objData.addParameter("PageLimit",this._txtLimitResults.value)},printCustReq:function(){this._cmbCustomer&&(this._intCompanyID=this._cmbCustomer.getValue());var n=$R_FN.arrayToSingleString(this._table._aryCurrentValues,this._lnsSeperator);$R_FN.setCookie(this._strCK,n,this._strCKExp);n="";$R_FN.openPrintWindowCustReqWithMultiples($R_ENUM$PrintObject.CustomerRequirement,this._intCompanyID);n=null},selectionMade:function(){this.enableBulkButtons(this._table._arySelectedIndexes.length>0)},getDataOK:function(){for(var n,i,t=0,r=this._objResult.Results.length;t<r;t++)n=this._objResult.Results[t],i=[$R_FN.writeDoubleCellValue($RGT_nubButton_ClientBOMImport(n.ID,n.ClientBOMNo),n.ClientBOMName),$R_FN.writeDoubleCellValue(n.Company,n.ContactName),$R_FN.writeDoubleCellValue($RGT_nubButton_BOM(n.BomId,n.BOMName),""),$R_FN.setCleanTextValue(n.Salesman),$R_FN.setCleanTextValue(n.ImportDate),$R_FN.writeDoubleCellValue(n.RecordsProcessed,n.RecordsRemaining)],this._table.addRow(i,n.ID,!1),i=null,n=null},enableApplyFilter:function(n){this._ibtnApply&&$R_IBTN.enableButton(this._ibtnApply,n)},applyFilter:function(){(this._blnGet=!0,this._blnGettingData)||this.onFilterData()},selectedCustomer:function(){this._table.clearTable();this._table.resizeColumns();this._table.clearSelection(!0);this.selectionMade()},resetFilter:function(){for(var t,n=0,i=this._aryFilterFieldIDs.length;n<i;n++)t=$find(this._aryFilterFieldIDs[n]),t.reset(),t.resetToDefault(),t=null;this._blnAllowSelection&&(this._txtLimitResults.value=50,this._intResultsLimit=50);this._cmbCustomer.setValue("","")},reselectData:function(){this._table.clearTable();this._table.resizeColumns();this._table.clearSelection(!0);this._blnGet=!1;this.getData()},getSorting:function(){this._blnGet=!0}};Rebound.GlobalTrader.Site.Controls.DataListNuggets.CustomerRequirementsBOMImport.registerClass("Rebound.GlobalTrader.Site.Controls.DataListNuggets.CustomerRequirementsBOMImport",Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base,Sys.IDisposable);