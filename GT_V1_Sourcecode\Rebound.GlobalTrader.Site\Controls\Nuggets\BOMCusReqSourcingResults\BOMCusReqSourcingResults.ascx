<%@ Control Language="C#" CodeBehind="BOMCusReqSourcingResults.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Nuggets.BOMCustomerRequirementSourcingResults" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<script src="js/AS6081.js" type="text/javascript"></script>

<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" TitleText="Main Information"
    BoxType="Standard">
    <Links>
        <ReboundUI:MultiSelectionCount ID="ctlMultiSelectionCount" runat="server" />
        <ReboundUI:IconButton ID="ibtnAdd" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget"
            IconTitleResource="Add" IconCSSType="Add" />
        <ReboundUI:IconButton ID="ibtnEdit" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget"
            IconTitleResource="Edit" IconCSSType="Edit" IsInitiallyEnabled="false" />
        <ReboundUI:IconButton ID="ibtnQuote" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget"
            IconTitleResource="Quote" IsInitiallyEnabled="false" />

        <ReboundUI:IconButton ID="ibtnDelete" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget"
            IconTitleResource="Delete" IconCSSType="Delete" IsInitiallyEnabled="false" />
        <ReboundUI:IconButton ID="ibtnConfirm" runat="server" IconButtonMode="hyperlink"
            IconGroup="Nugget" IconTitleResource="Release" IsInitiallyEnabled="true" />

        <ReboundUI:IconButton ID="ibtnApproval" runat="server" IconButtonMode="hyperlink"
            IconGroup="Nugget" IconTitleResource="Approval" IsInitiallyEnabled="true" />

        <ReboundUI:IconButton ID="ibtnDeletePartwatch" runat="server" IconButtonMode="hyperlink"
            IconGroup="Nugget" IconTitleResource="DeletePartWatchMatch" IconCSSType="Delete" IsInitiallyEnabled="false" />
    </Links>
    <Content>
        <ReboundUI:FlexiDataTable ID="tblQuote" runat="server" AllowMultipleSelection="true" PanelHeight="180" />

        <asp:Panel ID="pnlLoadingLineDetail" runat="server" CssClass="subInfoLoading invisible">
            <%=Functions.GetGlobalResource("Misc", "Loading")%>
        </asp:Panel>
        <asp:Panel ID="pnlLineDetailError" runat="server" CssClass="error invisible" />
        <asp:Panel ID="pnlLineDetail" runat="server" CssClass="invisible">
            <asp:Panel ID="Panel1" runat="server" CssClass="invisible">
                <strong>
                    <%=Functions.GetGlobalResource("Misc", "SupplierAdvice")%>
                </strong>
            </asp:Panel>

            <table class="threeCols">
                <tr>
                    <td class="col1">
                        <table cellpadding="0" cellspacing="0" border="0" class="dataItems">
                            <ReboundUI:DataItemRow ID="ctlManufacturerName" runat="server" ResourceTitle="ManufacturerName" />
                            <ReboundUI:DataItemRow ID="ctlDateCode" runat="server" ResourceTitle="DateCode" />
                            <ReboundUI:DataItemRow ID="ctlPackageType" runat="server" ResourceTitle="PackageType" />
                            <ReboundUI:DataItemRow ID="ctlProductType" runat="server" ResourceTitle="ProductType" />
                            <ReboundUI:DataItemRow ID="ctlSupplierWarranty" runat="server" ResourceTitle="SupplierWarranty" />
                            <ReboundUI:DataItemRow ID="ctlTestingRecommended" runat="server" ResourceTitle="TestingRecommended" CssClass="YellowBackground" />
                            <ReboundUI:DataItemRow ID="ctlPriority" runat="server" ResourceTitle="Priority" />
                            <ReboundUI:DataItemRow ID="ctlCountryOfOrigin" runat="server" ResourceTitle="CountryOfOrigin" />
                        </table>
                    </td>
                    <td class="col2">
                        <table cellpadding="0" cellspacing="0" border="0" class="dataItems">
                            <ReboundUI:DataItemRow ID="ctlMOQ" runat="server" ResourceTitle="MOQ" />
                            <ReboundUI:DataItemRow ID="ctlTotalQSA" runat="server" ResourceTitle="TotalQSA" />
                            <ReboundUI:DataItemRow ID="ctlLTB" runat="server" ResourceTitle="LTB" />
                            <ReboundUI:DataItemRow ID="ctlNotes" runat="server" ResourceTitle="Notes" />
                            <ReboundUI:DataItemRow ID="ctlIsImage" runat="server" ResourceTitle="IsSorcingImage" />
                        </table>
                    </td>
                    <td class="col3">
                        <table cellpadding="0" cellspacing="0" border="0" class="dataItems">
                            <ReboundUI:DataItemRow ID="ctlSPQ" runat="server" ResourceTitle="SPQ" />
                            <ReboundUI:DataItemRow ID="ctlLeadTime" runat="server" ResourceTitle="LeadTime" />
                            <%--<ReboundUI:DataItemRow ID="ctlROHSStatus" runat="server" ResourceTitle="ROHSStatus" />--%>
                            <ReboundUI:DataItemRow ID="ctlROHSStatus" runat="server" ResourceTitle="ROHS" />
                            <ReboundUI:DataItemRow ID="ctlFactorySealed" runat="server" ResourceTitle="FactorySealed" />
                            <ReboundUI:DataItemRow ID="ctlMSL" runat="server" ResourceTitle="MSL" />
                            <ReboundUI:DataItemRow ID="ctlTypeOfSupplierName" runat="server" ResourceTitle="AS6081TOS" />
                            <ReboundUI:DataItemRow ID="ctlReasonForSupplierName" runat="server" ResourceTitle="AS6081RCS" />
                            <ReboundUI:DataItemRow ID="ctlRiskOfSupplierName" runat="server" ResourceTitle="AS6081ROS" />
                        </table>
                    </td>
                </tr>
            </table>
        </asp:Panel>
    </Content>
    <Forms>
        <ReboundForm:BOMCustomerRequirementSourcingResults_Add ID="ctlAdd" runat="server" />
        <ReboundForm:BOMCustomerRequirementSourcingResults_Edit ID="ctlEdit" runat="server" />
        <ReboundForm:BOMCustomerRequirementSourcingResults_Delete ID="ctlDelete" runat="server" />
        <ReboundForm:BOMCustomerRequirementSourcingResults_Confirm ID="ctlConfirm" runat="server" />
        <ReboundForm:CustomerRequirementSourcingResults_Add ID="ctlAddClient" runat="server" />
        <ReboundForm:CustomerRequirementSourcingResults_Edit ID="ctlEditClient" runat="server" />
        <ReboundForm:BOMCustomerRequirementSourcingResults_Approval ID="BOMCustomerRequirementSourcingResults_Approval" runat="server" />
        <ReboundForm:BOMCusReqSourcingResults_DeletePartWatch ID="ctlDeletePartwatch" runat="server" />
		<ReboundForm:QuoteMainInfo_MarkAsToDo ID="ctlAddTask" runat="server" />
    </Forms>
</ReboundUI_Nugget:DesignBase>
<style>
    #ctl00_cphMain_ctlSourcingResults_ctlDB_ctl13_tblQuote_th_0,
    #ctl00_cphMain_ctlSourcingResults_ctlDB_ctl13_tblQuote_tbl tbody tr td:first-child{
        width: 13% !important;
    }
   
    #ctl00_cphMain_ctlSourcingResults_ctlDB_ctl13_tblQuote_th_1,
    #ctl00_cphMain_ctlSourcingResults_ctlDB_ctl13_tblQuote_th_2,
    #ctl00_cphMain_ctlSourcingResults_ctlDB_ctl13_tblQuote_tbl tbody tr td:nth-child(2),
    #ctl00_cphMain_ctlSourcingResults_ctlDB_ctl13_tblQuote_tbl tbody tr td:nth-child(3) {
        width: 10% !important;
    }
    
    #ctl00_cphMain_ctlSourcingResults_ctlDB_ctl13_tblQuote_th_3,
    #ctl00_cphMain_ctlSourcingResults_ctlDB_ctl13_tblQuote_tbl tbody tr td:nth-child(4) {
        width: 13% !important;
    }
   
    #ctl00_cphMain_ctlSourcingResults_ctlDB_ctl13_tblQuote_th_4,
    #ctl00_cphMain_ctlSourcingResults_ctlDB_ctl13_tblQuote_tbl tbody tr td:nth-child(5) {
        width: 8% !important;
    }

    #ctl00_cphMain_ctlSourcingResults_ctlDB_ctl13_tblQuote_th_5,
    #ctl00_cphMain_ctlSourcingResults_ctlDB_ctl13_tblQuote_tbl tbody tr td:nth-child(6),
    #ctl00_cphMain_ctlSourcingResults_ctlDB_ctl13_tblQuote_th_6,
    #ctl00_cphMain_ctlSourcingResults_ctlDB_ctl13_tblQuote_tbl tbody tr td:nth-child(7) {
        width: 7% !important;
    }
   
    #ctl00_cphMain_ctlSourcingResults_ctlDB_ctl13_tblQuote_th_7,
    #ctl00_cphMain_ctlSourcingResults_ctlDB_ctl13_tblQuote_tbl tbody tr td:nth-child(8){
        width: 6% !important;
    }

    #ctl00_cphMain_ctlSourcingResults_ctlDB_ctl13_tblQuote_th_8, 
    #ctl00_cphMain_ctlSourcingResults_ctlDB_ctl13_tblQuote_tbl tbody tr td:nth-child(9){
        width: 7% !important;
    }
    #ctl00_cphMain_ctlSourcingResults_ctlDB_ctl13_tblQuote_th_9,
    #ctl00_cphMain_ctlSourcingResults_ctlDB_ctl13_tblQuote_tbl tbody tr td:nth-child(10),
    #ctl00_cphMain_ctlSourcingResults_ctlDB_ctl13_tblQuote_th_11,
    #ctl00_cphMain_ctlSourcingResults_ctlDB_ctl13_tblQuote_tbl tbody tr td:last-child {
        width: 7% !important;
    }
    #ctl00_cphMain_ctlSourcingResults_ctlDB_ctl13_tblQuote_th_10,
    #ctl00_cphMain_ctlSourcingResults_ctlDB_ctl13_tblQuote_tbl tbody tr td:nth-child(11) {
        width: 5% !important;
    }
</style>