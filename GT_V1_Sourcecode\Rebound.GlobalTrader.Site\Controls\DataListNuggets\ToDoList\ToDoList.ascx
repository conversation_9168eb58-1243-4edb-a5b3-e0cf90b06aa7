﻿<%--
Marker     Changed by      Date               Remarks
[001]      Bhooma          18/Feb/2022        CR:- Create New DataList Nugget for view to do list
--%>
<%@ Control Language="C#"  CodeBehind="ToDoList.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.DataListNuggets.ToDoList" %>

<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" BoxType="List">
    <%--[001] code start--%>
    <Links>
        <ReboundUI:MultiSelectionCount id="ctlSelect" runat="server" />
        <ReboundUI:IconButton ID="ibtnExportCSV" runat="server" Style="margin-left: 8px;" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="ExportToExcel" />
        <ReboundUI:IconButton ID="ibtnAdd" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="Add" IconCSSType="Add"  IsInitiallyEnabled="true" />
		<ReboundUI:IconButton ID="ibtnEdit" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="Edit" IconCSSType="Edit" IsInitiallyEnabled="false" />
        <ReboundUI:IconButton ID="ibtnDelete" runat="server" IconButtonMode="Hyperlink" Href="javascript:void(0);" IconGroup="Nugget" IconTitleResource="Delete" IconCSSType="Delete" IsInitiallyEnabled="false" />
		<ReboundUI:IconButton ID="ibtnMarkComplete" runat="server" IconButtonMode="Hyperlink" Href="javascript:void(0);" IconGroup="Nugget" IconTitleResource="MarkComplete" IsInitiallyEnabled="false" />
		<ReboundUI:IconButton ID="ibtnMarkIncomplete" runat="server" IconButtonMode="Hyperlink" Href="javascript:void(0);" IconGroup="Nugget" IconTitleResource="MarkIncomplete" IsInitiallyEnabled="false" />


    </Links>
    <%--[001] code end--%>
    <Filters>
        <ReboundUI_DataListNugget:Filter ID="ctlFilter" runat="server">
            <FieldsLeft>
                <%--<ReboundUI_FilterDataItemRow:DateSelect ID="ctlCreatedDateFrom" runat="server" ResourceTitle="CreatedDateFrom" FilterField="CreatedDateFrom" />--%>
                <%--<ReboundUI_FilterDataItemRow:DateSelect ID="ctlCreatedDateTo" runat="server" ResourceTitle="CreatedDateTo" FilterField="CreatedDateTo" />--%>
                <ReboundUI_FilterDataItemRow:DateSelect ID="ctlTaskDateFrom" runat="server" ResourceTitle="TaskDateFrom" FilterField="TaskDateFrom" />
                <ReboundUI_FilterDataItemRow:DateSelect ID="ctlTaskDateTo" runat="server" ResourceTitle="TaskDateTo" FilterField="TaskDateTo" />
                <ReboundUI_FilterDataItemRow:DateSelect ID="ctlTaskReminderDate" runat="server" ResourceTitle="TaskReminderDate" FilterField="TaskReminderDate" />
                <ReboundUI_FilterDataItemRow:DropDown id="ctlTaskType" runat="server" DropDownType="ToDoListType" ResourceTitle="TaskType" FilterField="TaskType" DropDownAssembly="Rebound.GlobalTrader.Site" />
                <ReboundUI_FilterDataItemRow:CheckBox ID="ctlReviewOnly" runat="server" ResourceTitle="ReviewOnly" FilterField="ReviewOnly" />
            </FieldsLeft>
            <FieldsRight>
                <%--<ReboundUI_FilterDataItemRow:TextBox ID="ctlTaskType" runat="server" ResourceTitle="TaskType" FilterField="TaskType" />--%>
                <ReboundUI_FilterDataItemRow:TextBox id="ctlTaskStatus" runat="server" ResourceTitle="TaskStatus" FilterField="TaskStatus" />
                <ReboundUI_FilterDataItemRow:DropDown id="ctlTaskCategory" runat="server" DropDownType="ToDoCategory" ResourceTitle="TaskCategory" FilterField="TaskCategory" DropDownAssembly="Rebound.GlobalTrader.Site" />
                <ReboundUI_FilterDataItemRow:TextBox id="ctlCustomerName" runat="server" ResourceTitle="CustomerName" FilterField="CustomerName" />
                <%--<ReboundUI_FilterDataItemRow:TextBox id="ctlSalesPerson" runat="server" ResourceTitle="SalesPerson" FilterField="Salesman" />--%>
                <ReboundUI_FilterDataItemRow:DropDown id="ctlSalesperson" runat="server" LimitToCurrentUsersTeam="false" LimitToCurrentUsersDivision="false" ExcludeCurrentUser="false" DropDownType="EmployeeAllClient" ResourceTitle="Salesperson" IncludeNoValue="true" FilterField="Salesman" DropDownAssembly="Rebound.GlobalTrader.Site" />
				<ReboundUI_FilterDataItemRow:TextBox id="ctlQuoteNumber" runat="server" ResourceTitle="QuoteNumber" FilterField="QuoteNumber" class="invisible"/>
            </FieldsRight>
        </ReboundUI_DataListNugget:Filter>
    </Filters>
    <Forms>
		<%--<ReboundForm:ToDoList_Confirm ID="ctlConfirm" runat="server" />
		<ReboundForm:ToDoList_Edit ID="ctlEdit" runat="server" />
        <ReboundForm:MailMessages_MarkAsToDo id="ctlAdd" runat="server" />--%>
        <ReboundForm:ToDo_Edit id="ctlEdit" runat="server" />
		<ReboundForm:ToDo_Confirm id="ctlConfirm" runat="server" />
		<ReboundForm:MailMessages_MarkAsToDo id="ctlAdd" runat="server" />
	</Forms>
</ReboundUI_Nugget:DesignBase>
