Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.ShipSOReadyStatus=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.ShipSOReadyStatus.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DropDowns.ShipSOReadyStatus.prototype={get_blnAllTab:function(){return this._blnAllTab},set__blnAllTab:function(n){this._blnAllTab!==n&&(this._blnAllTab=n)},get_blnLimitToCurrentUsersDivision:function(){return this._blnLimitToCurrentUsersDivision},set_blnLimitToCurrentUsersDivision:function(n){this._blnLimitToCurrentUsersDivision!==n&&(this._blnLimitToCurrentUsersDivision=n)},get_blnLimitToCurrentUsersTeam:function(){return this._blnLimitToCurrentUsersTeam},set_blnLimitToCurrentUsersTeam:function(n){this._blnLimitToCurrentUsersTeam!==n&&(this._blnLimitToCurrentUsersTeam=n)},get_blnExcludeCurrentUser:function(){return this._blnExcludeCurrentUser},set_blnExcludeCurrentUser:function(n){this._blnExcludeCurrentUser!==n&&(this._blnExcludeCurrentUser=n)},get_intGlobalLoginClientNo:function(){return this._intGlobalLoginClientNo},set_intGlobalLoginClientNo:function(n){this._intGlobalLoginClientNo!==n&&(this._intGlobalLoginClientNo=n)},initialize:function(){this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK));Rebound.GlobalTrader.Site.Controls.DropDowns.ShipSOReadyStatus.callBaseMethod(this,"initialize")},dispose:function(){this.isDisposed||(this._blnLimitToCurrentUsersDivision=null,this._blnLimitToCurrentUsersTeam=null,this._blnExcludeCurrentUser=null,this._intGlobalLoginClientNo=null,this._blnAllTab=null,Rebound.GlobalTrader.Site.Controls.DropDowns.Employee.callBaseMethod(this,"dispose"))},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/ShipSOReadyStatus");this._objData.set_DataObject("ShipSOReadyStatus");this._objData.set_DataAction("GetData");this._objData.addParameter("AllTab",this._blnAllTab);this._objData.addParameter("LimitToCurrentUsersTeam",this._blnLimitToCurrentUsersTeam);this._objData.addParameter("LimitToCurrentUsersDivision",this._blnLimitToCurrentUsersDivision);this._objData.addParameter("ExcludeCurrentUser",this._blnExcludeCurrentUser);this._objData.addParameter("GlobalLoginClientNo",this._intGlobalLoginClientNo)},dataCallOK:function(){var t=this._objData._result,n;if(t.ShipSOReadyStatus)for(n=0;n<t.ShipSOReadyStatus.length;n++)this.addOption(t.ShipSOReadyStatus[n].Name,t.ShipSOReadyStatus[n].ID)}};Rebound.GlobalTrader.Site.Controls.DropDowns.ShipSOReadyStatus.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.ShipSOReadyStatus",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);