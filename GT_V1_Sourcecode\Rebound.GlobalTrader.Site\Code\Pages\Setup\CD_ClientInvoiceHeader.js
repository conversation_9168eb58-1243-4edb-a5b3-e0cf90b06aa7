Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Setup");Rebound.GlobalTrader.Site.Pages.Setup.CD_ClientInvoiceHeader=function(n){Rebound.GlobalTrader.Site.Pages.Setup.CD_ClientInvoiceHeader.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Pages.Setup.CD_ClientInvoiceHeader.prototype={get_ctlClientInvoiceHeader:function(){return this._ctlClientInvoiceHeader},set_ctlClientInvoiceHeader:function(n){this._ctlClientInvoiceHeader!==n&&(this._ctlClientInvoiceHeader=n)},get_ctlClientInvoiceHeaderImage:function(){return this._ctlClientInvoiceHeaderImage},set_ctlClientInvoiceHeaderImage:function(n){this._ctlClientInvoiceHeaderImage!==n&&(this._ctlClientInvoiceHeaderImage=n)},initialize:function(){Rebound.GlobalTrader.Site.Pages.Setup.CD_ClientInvoiceHeader.callBaseMethod(this,"initialize")},goInit:function(){this._ctlClientInvoiceHeader.addSelectClientInvoiceHeader(Function.createDelegate(this,this.ctlClientInvoiceHeader_SelectClientInvoiceHeader));Rebound.GlobalTrader.Site.Pages.Setup.CD_ClientInvoiceHeader.callBaseMethod(this,"goInit")},dispose:function(){this.isDisposed||(this._ctlClientInvoiceHeader&&this._ctlClientInvoiceHeader.dispose(),this._ctlClientInvoiceHeaderImage&&this._ctlClientInvoiceHeaderImage.dispose(),this._ctlClientInvoiceHeader=null,this._ctlClientInvoiceHeaderImage=null,Rebound.GlobalTrader.Site.Pages.Setup.CD_ClientInvoiceHeader.callBaseMethod(this,"dispose"))},ctlClientInvoiceHeader_SelectClientInvoiceHeader:function(){this._ctlClientInvoiceHeaderImage._intClientInvoiceHeaderID=this._ctlClientInvoiceHeader._intClientInvoiceHeaderID;this._ctlClientInvoiceHeaderImage.refresh();this._ctlClientInvoiceHeader._tbl.resizeColumns();this.showNuggets(!0)},showNuggets:function(n){this._ctlClientInvoiceHeaderImage.show(n)}};Rebound.GlobalTrader.Site.Pages.Setup.CD_ClientInvoiceHeader.registerClass("Rebound.GlobalTrader.Site.Pages.Setup.CD_ClientInvoiceHeader",Rebound.GlobalTrader.Site.Pages.Content,Sys.IDisposable);