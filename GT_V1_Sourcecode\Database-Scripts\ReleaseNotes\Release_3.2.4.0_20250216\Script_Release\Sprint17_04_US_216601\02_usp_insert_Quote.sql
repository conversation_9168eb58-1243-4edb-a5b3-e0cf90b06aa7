﻿
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/* 
==========================================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-216601]	    Phuc Hoang			12-Dec-2024		Update			216601: Quote - New status matrix
==========================================================================================================
*/

CREATE OR ALTER PROCEDURE [dbo].[usp_insert_Quote]                               
--******************************************************************************************                              
--* SK 15.02.2010:                              
--* - add IncotermNo                              
--*Marker     changed by      date          Remarks                            
--*[001]      Vinay           21/01/2014   CR:- Add AS9120 Requirement in GT application              
--*[002]   Ravi     01-09-2023 RP-2230 (AS6081 Shipping/ Warehouse)          
--******************************************************************************************                              
    @ClientNo int                              
  , @Notes nvarchar(max) = NULL                              
  , @Instructions nvarchar(max) = NULL                              
  , @CompanyNo int                              
  , @ContactNo int                              
  , @DateQuoted datetime                              
  , @CurrencyNo int                              
  , @Salesman int                              
  , @TermsNo int = NULL                              
  , @DivisionNo int                              
  , @Freight float = NULL                              
  , @Closed bit                              
  , @IncotermNo int = NULL                              
  , @UpdatedBy int = NULL                             
  --[001] code start                            
  , @AS9120 bit = NULL                             
  --[001] code end                            
  , @IsImportant bit = 0                           
  , @SupportTeamMemberNo int =null                      
  , @DivisionHeaderNo int  =null                      
  , @CustomerRequirementNo int= null                       
  , @DocHeaderImageName varchar(200)=null         
  , @AS6081 bit = null        
  , @QuoteId int OUTPUT                              
AS                               
    BEGIN                              
                              
        DECLARE @QuoteNumber int                            
        EXEC usp_select_Quote_NextNumber @ClientNo, @UpdatedBy, @QuoteNumber OUTPUT                              
    IF (@CustomerRequirementNo is not null)                    
 begin                      
 UPDATE tbCustomerRequirement set REQStatus = 5 where CustomerRequirementId = @CustomerRequirementNo           
 --[002] start  (if quote is created using requirement in that case, get AS6081 value from tbCustomerRequirement table )        
 --Select @AS6081 = isnull(AS6081,0) from tbCustomerRequirement where CustomerRequirementId = @CustomerREquirementNo          
 --[002] end          
 end                    
                  
--- Footer form history table                  
 Declare @tmpSystemDocumentFooterHistoryID int                  
 select @tmpSystemDocumentFooterHistoryID=SystemDocumentFooterHistoryId from tbSystemDocumentFooterHistory                  
 where SystemDocumentNo=4 and ClientNo=@ClientNo                  
 and FooterActive=1                  
-------------------------                 
                
   declare @SysDocAS9120HistoryNo int,                
   @SysDocHazardousHistoryNo int,                
   @SysDocCOOHistoryNo int                
                
   -- For AS9120                
   select top 1 @SysDocAS9120HistoryNo=SystemDocumentFooterHistoryId from tbSystemDocumentFooterHistory                 
            where SystemDocumentNo=21 and ClientNo=@ClientNo order by DLUP desc                 
                
   --For PrintHazardous                
   select top 1 @SysDocHazardousHistoryNo=SystemDocumentFooterHistoryId from tbSystemDocumentFooterHistory                 
            where SystemDocumentNo=26 and ClientNo=@ClientNo order by DLUP desc                 
                
   --For CountryOfOrigin                
   select top 1 @SysDocCOOHistoryNo=SystemDocumentFooterHistoryId from tbSystemDocumentFooterHistory                 
            where SystemDocumentNo=27 and ClientNo=@ClientNo order by DLUP desc                 
                
                   
--- Image Header Name set as Null for workaround : 28 Jul 2021                
set @DocHeaderImageName=null                
                    
        INSERT  INTO dbo.tbQuote (                              
  ClientNo                              
      , QuoteNumber                              
      , Notes                              
      , Instructions                 
   , CompanyNo                              
      , ContactNo                              
      , DateQuoted                              
      , CurrencyNo                              
      , Salesman                              
      , TermsNo           
      , DivisionNo                              
      , Freight                              
      , Closed                              
      , IncotermNo                               
      , UpdatedBy                             
      --[001] code start                            
      , AS9120                            
      , IsImportant                          
      ,SupportTeamMemberNo                       
      ,DivisionHeaderNo                
      --[001] code end                            
      , QuoteStatus                   
    --  , FooterText                  
       , HeaderImageName                           
        ,SystemDocumentFooterHistoryNo                 
   ,SysDocAS9120HistoryNo                
   ,SysDocHazardousHistoryNo                
   ,SysDocCOOHistoryNo           
   , AS6081 --[002]          
   )                              
        VALUES  (                              
                 @ClientNo                              
       , @QuoteNumber                              
               , @Notes                              
               , @Instructions                              
               , @CompanyNo                              
               , @ContactNo                              
               , @DateQuoted                              
               , @CurrencyNo                              
               , @Salesman                              
               , @TermsNo                              
               , @DivisionNo                              
               , @Freight                              
               , @Closed                              
               , @IncotermNo                               
               , @UpdatedBy                              
               , @AS9120                            
      , @IsImportant                           
   ,@SupportTeamMemberNo                      
   ,@DivisionHeaderNo                            
      , 5 -- Default "New" 
 --  , @FooterText                  
   , @DocHeaderImageName                        
  ,@tmpSystemDocumentFooterHistoryID                  
  ,@SysDocAS9120HistoryNo                
  ,@SysDocHazardousHistoryNo                
  ,@SysDocCOOHistoryNo            
  , @AS6081 --[002]          
   )                           
     SET @QuoteId = scope_identity()                         
    END; 
GO


