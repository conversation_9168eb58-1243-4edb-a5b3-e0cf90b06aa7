CREATE PROCEDURE KPI_GetCustomerRevenueTargetByID
    @SalesTargetNo int,
    @YearNo INT
as
Begin

    ;with CTE_CustomerSummary
     as (select 1 as OrderBy,
                0 as RowId,
                0 as SalesmanId,
                'Sales Target' as SalesmanName,
                0 as CompanyId,
                'Sales Target' as CustomerName,
                'TotalTarget' as SectionType,
                (
                    select isnull(JanTarget, 0)
                ) JanTarget,
                (
                    select isnull(FebTarget, 0)
                ) FebTarget,
                (
                    select isnull(MarchTarget, 0)
                ) MarchTarget,
                (
                    select isnull(AprTarget, 0)
                ) AprTarget,
                (
                    select isnull(MayTarget, 0)
                ) MayTarget,
                (
                    select isnull(JuneTarget, 0)
                ) JuneTarget,
                (
                    select isnull(JulyTarget, 0)
                ) JulyTarget,
                (
                    select isnull(AugTarget, 0)
                ) AugTarget,
                (
                    select isnull(SepTarget, 0)
                ) SepTarget,
                (
                    select isnull(OctTarget, 0)
                ) OctTarget,
                (
                    select isnull(NovTarget, 0)
                ) NovTarget,
                (
                    select isnull(DecTarget, 0)
                ) DecTarget,
                (
                    select isnull(sum(resale), 0)
                    from tbKPICustomersRevenueSummary
                    where salesman = @SalesTargetNo
                          and invMonth = 1
                          and yearNo = @YearNo
                ) as JanRevenue,
                (
                    select isnull(sum(resale), 0)
                    from tbKPICustomersRevenueSummary
                    where salesman = @SalesTargetNo
                          and invMonth = 2
                          and yearNo = @YearNo
                ) as FebRevenue,
                (
                    select isnull(sum(resale), 0)
                    from tbKPICustomersRevenueSummary
                    where salesman = @SalesTargetNo
                          and invMonth = 3
                          and yearNo = @YearNo
                ) as MarchRevenue,
                (
                    select isnull(sum(resale), 0)
                    from tbKPICustomersRevenueSummary
                    where salesman = @SalesTargetNo
                          and invMonth = 4
                          and yearNo = @YearNo
                ) as AprRevenue,
                (
                    select isnull(sum(resale), 0)
                    from tbKPICustomersRevenueSummary
                    where salesman = @SalesTargetNo
                          and invMonth = 5
                          and yearNo = @YearNo
                ) as MayRevenue,
                (
                    select isnull(sum(resale), 0)
                    from tbKPICustomersRevenueSummary
                    where salesman = @SalesTargetNo
                          and invMonth = 6
                          and yearNo = @YearNo
                ) as JuneRevenue,
                (
                    select isnull(sum(resale), 0)
                    from tbKPICustomersRevenueSummary
                    where salesman = @SalesTargetNo
                          and invMonth = 7
                          and yearNo = @YearNo
                ) as JulyRevenue,
                (
                    select isnull(sum(resale), 0)
                    from tbKPICustomersRevenueSummary
                    where salesman = @SalesTargetNo
                          and invMonth = 8
                          and yearNo = @YearNo
                ) as AugRevenue,
                (
                    select isnull(sum(resale), 0)
                    from tbKPICustomersRevenueSummary
                    where salesman = @SalesTargetNo
                          and invMonth = 9
                          and yearNo = @YearNo
                ) as SepRevenue,
                (
                    select isnull(sum(resale), 0)
                    from tbKPICustomersRevenueSummary
                    where salesman = @SalesTargetNo
                          and invMonth = 10
                          and yearNo = @YearNo
                ) as OctRevenue,
                (
                    select isnull(sum(resale), 0)
                    from tbKPICustomersRevenueSummary
                    where salesman = @SalesTargetNo
                          and invMonth = 11
                          and yearNo = @YearNo
                ) as NovRevenue,
                (
                    select isnull(sum(resale), 0)
                    from tbKPICustomersRevenueSummary
                    where salesman = @SalesTargetNo
                          and invMonth = 12
                          and yearNo = @YearNo
                ) as DecRevenue,
                cast(0 as float) as TotalTarget,
                0 as TotalRevenue
         from tbSalesTargetFinal
         where SalesManNo = @SalesTargetNo
               and yearno = @YearNo
         union
         select 2 as OrderBy,
                0 as RowId,
                0 as SalesmanId,
                'Allocated Customer Target' as SalesmanName,
                0 as CompanyId,
                'Allocated Customer Target' as CustomerName,
                'AllocatedTarget' as SectionType,
                0 as JanTarget,
                0 as FebTarget,
                0 as MarchTarget,
                0 as AprTarget,
                0 as MayTarget,
                0 as JuneTarget,
                0 as JulyTarget,
                0 as AugTarget,
                0 as SepTarget,
                0 as OctTarget,
                0 as NovTarget,
                0 as DecTarget,
                0 as JanRevenue,
                0 as FebRevenue,
                0 as MarchRevenue,
                0 as AprRevenue,
                0 as MayRevenue,
                0 as JuneRevenue,
                0 as JulyRevenue,
                0 as AugRevenue,
                0 as SepRevenue,
                0 as OctRevenue,
                0 as NovRevenue,
                0 as DecRevenue,
                cast(0 as float) as TotalTarget,
                0 as TotalRevenue
         union
         select 3 as OrderBy,
                0 as RowId,
                0 as SalesmanId,
                'Sales Remaining Target' as SalesmanName,
                0 as CompanyId,
                'Sales Remaining Target' as CustomerName,
                'RemainingTarget' as SectionType,
                0 as JanTarget,
                0 as FebTarget,
                0 as MarchTarget,
                0 as AprTarget,
                0 as MayTarget,
                0 as JuneTarget,
                0 as JulyTarget,
                0 as AugTarget,
                0 as SepTarget,
                0 as OctTarget,
                0 as NovTarget,
                0 as DecTarget,
                0 as JanRevenue,
                0 as FebRevenue,
                0 as MarchRevenue,
                0 as AprRevenue,
                0 as MayRevenue,
                0 as JuneRevenue,
                0 as JulyRevenue,
                0 as AugRevenue,
                0 as SepRevenue,
                0 as OctRevenue,
                0 as NovRevenue,
                0 as DecRevenue,
                cast(0 as float) as TotalTarget,
                0 as TotalRevenue
         union
         --Find all customers  Details            
         select 4 as OrderBy,
                ct.CustomerTargetId as RowId,
                lg.loginid as SalesmanId,
                case
                    when ISNULL(lg.EmployeeName, '') = '' then
                        null
                    else
                        lg.EmployeeName
                END as SalesmanName,
                cm.CompanyId,
                cm.Companyname as CustomerName,
                -------------------------------                              
                'customer' as SectionType,
                isnull(round(ct.JanTarget, 2), 0) as JanTarget,
                isnull(ct.FebTarget, 0) as FebTarget,
                isnull(ct.MarchTarget, 0) as MarchTarget,
                isnull(ct.AprTarget, 0) as AprTarget,
                isnull(ct.MayTarget, 0) as MayTarget,
                isnull(ct.JuneTarget, 0) as JuneTarget,
                isnull(ct.JulyTarget, 0) as JulyTarget,
                isnull(ct.AugTarget, 0) as AugTarget,
                isnull(ct.SepTarget, 0) as SepTarget,
                isnull(ct.OctTarget, 0) as OctTarget,
                isnull(ct.NovTarget, 0) as NovTarget,
                isnull(ct.DecTarget, 0) as DecTarget,
                (
                    select isnull(sum(resale), 0)
                    from tbKPICustomersRevenueSummary
                    where CompanyId = ct.CompanyNo
                          and invMonth = 1
                          and yearNo = @YearNo
                ) as JanRevenue,
                (
                    select isnull(sum(resale), 0)
                    from tbKPICustomersRevenueSummary
                    where CompanyId = ct.CompanyNo
                          and invMonth = 2
                          and yearNo = @YearNo
                ) as FebRevenue,
                (
                    select isnull(sum(resale), 0)
                    from tbKPICustomersRevenueSummary
                    where CompanyId = ct.CompanyNo
                          and invMonth = 3
                          and yearNo = @YearNo
                ) as MarchRevenue,
                (
                    select isnull(sum(resale), 0)
                    from tbKPICustomersRevenueSummary
                    where CompanyId = ct.CompanyNo
                          and invMonth = 4
                          and yearNo = @YearNo
                ) as AprRevenue,
                (
                    select isnull(sum(resale), 0)
                    from tbKPICustomersRevenueSummary
                    where CompanyId = ct.CompanyNo
                          and invMonth = 5
                          and yearNo = @YearNo
                ) as MayRevenue,
                (
                    select isnull(sum(resale), 0)
                    from tbKPICustomersRevenueSummary
                    where CompanyId = ct.CompanyNo
                          and invMonth = 6
                          and yearNo = @YearNo
                ) as JuneRevenue,
                (
                    select isnull(sum(resale), 0)
                    from tbKPICustomersRevenueSummary
                    where CompanyId = ct.CompanyNo
                          and invMonth = 7
                          and yearNo = @YearNo
                ) as JulyRevenue,
                (
                    select isnull(sum(resale), 0)
                    from tbKPICustomersRevenueSummary
                    where CompanyId = ct.CompanyNo
                          and invMonth = 8
                          and yearNo = @YearNo
                ) as AugRevenue,
                (
                    select isnull(sum(resale), 0)
                    from tbKPICustomersRevenueSummary
                    where CompanyId = ct.CompanyNo
                          and invMonth = 9
                          and yearNo = @YearNo
                ) as SepRevenue,
                (
                    select isnull(sum(resale), 0)
                    from tbKPICustomersRevenueSummary
                    where CompanyId = ct.CompanyNo
                          and invMonth = 10
                          and yearNo = @YearNo
                ) as OctRevenue,
                (
                    select isnull(sum(resale), 0)
                    from tbKPICustomersRevenueSummary
                    where CompanyId = ct.CompanyNo
                          and invMonth = 11
                          and yearNo = @YearNo
                ) as NovRevenue,
                (
                    select isnull(sum(resale), 0)
                    from tbKPICustomersRevenueSummary
                    where CompanyId = ct.CompanyNo
                          and invMonth = 12
                          and yearNo = @YearNo
                ) as DecRevenue,
                cast(0 as float) as TotalTarget,
                0 as TotalRevenue
         -----------------------------------------------------------------------------------                            
         from tbCustomerTargetFinal ct
             --LEFT JOIN  tbSalesTargetFinal  STF    on ct.SalesTargetNo = stf.SalesTargetId      
             inner JOIN tblogin lg
                 on lg.LoginId = CT.SalesManNo
             inner JOIN tbCompany cm
                 on cm.CompanyId = ct.CompanyNo
         where cm.salesman = @SalesTargetNo
               and ct.salesmanno = @SalesTargetNo
               and ct.yearno = @YearNo
        )
    select *
    from CTE_CustomerSummary
    ORDER BY orderby,
             CASE
                 WHEN RowId = 0 THEN
                     0
                 ELSE
                     1
             END,
             CustomerName
End





