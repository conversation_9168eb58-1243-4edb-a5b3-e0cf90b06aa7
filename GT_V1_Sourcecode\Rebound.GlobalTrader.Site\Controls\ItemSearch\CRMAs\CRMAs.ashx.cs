using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site.Enumerations;

namespace Rebound.GlobalTrader.Site.Controls.ItemSearch.Data {
	[WebService(Namespace = "http://tempuri.org/")]
	[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
	public class CRMAs : Rebound.GlobalTrader.Site.Data.ItemSearch.Base {
		
		protected override void GetData() {
			List<CustomerRma> lst = null;
			try {
				lst = CustomerRma.ItemSearch(
					SessionManager.ClientID
					, GetFormValue_NullableInt("Order", 0)
					, GetFormValue_NullableInt("SortDir", SortColumnDirection.ASC)
					, GetFormValue_NullableInt("PageIndex", 0)
					, GetFormValue_NullableInt("PageSize", 10)
					, GetFormValue_String("Contact")
                    //, GetFormValue_StringForNameSearch("CMName")
                    , GetFormValue_StringForNameSearchDecode("CMName")
					, GetFormValue_NullableInt("Salesman")
					, GetFormValue_String("Notes")
					, GetFormValue_NullableInt("InvoiceNoLo")
					, GetFormValue_NullableInt("InvoiceNoHi")
					, GetFormValue_NullableInt("CRMANoLo")
					, GetFormValue_NullableInt("CRMANoHi")
					, GetFormValue_NullableDateTime("CRMADateFrom", null)
					, GetFormValue_NullableDateTime("CRMADateTo", null)
					);
				JsonObject jsn = new JsonObject();
				JsonObject jsnItems = new JsonObject(true);
				for (int i = 0; i < lst.Count; i++) {
					JsonObject jsnItem = new JsonObject();
					jsnItem.AddVariable("ID", lst[i].CustomerRMAId);
					jsnItem.AddVariable("No", lst[i].CustomerRMANumber);
					jsnItem.AddVariable("CMNo", lst[i].CompanyNo);
					jsnItem.AddVariable("CMName", lst[i].CompanyName);
					jsnItem.AddVariable("Date", Functions.FormatDate(lst[i].CustomerRMADate));
					jsnItem.AddVariable("Salesman", lst[i].SalesmanName);
					jsnItem.AddVariable("Authoriser", lst[i].AuthoriserName);
					jsnItem.AddVariable("InvoiceNo", lst[i].InvoiceNumber);
					jsnItems.AddVariable(jsnItem);
					jsnItem.Dispose();
					jsnItem = null;
				}
				jsn.AddVariable("Count", (lst.Count > 0) ? lst[0].RowCnt : 0);
				jsn.AddVariable("Results", jsnItems);
				OutputResult(jsn);
				jsnItems.Dispose();
				jsnItems = null;
				jsn.Dispose();
				jsn = null;
			} catch (Exception ex) {
				WriteError(ex);
			} finally {
				lst = null;
			}
			base.GetData();
		}
	}
}
