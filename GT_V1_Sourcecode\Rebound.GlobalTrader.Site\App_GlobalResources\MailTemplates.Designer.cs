//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option or rebuild the Visual Studio project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.VisualStudio.Web.Application.StronglyTypedResourceProxyBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class MailTemplates {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal MailTemplates() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Resources.MailTemplates", global::System.Reflection.Assembly.Load("App_GlobalResources"));
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please find attached client invoice {0}
        ///
        ///Regards
        ///{1}.
        /// </summary>
        internal static string ClientInvoicePDF {
            get {
                return ResourceManager.GetString("ClientInvoicePDF", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please find attached {0}
        ///
        ///Regards
        ///{1}.
        /// </summary>
        internal static string COCBodyMessage {
            get {
                return ResourceManager.GetString("COCBodyMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please find attached Commercial Invoice {0}
        ///
        ///Regards
        ///{1}.
        /// </summary>
        internal static string CommercialInvoice {
            get {
                return ResourceManager.GetString("CommercialInvoice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Company has been approved:
        ///
        ///Customer Name:  [#CUSTOMERNAME#]
        ///Salesperson Name:  [#SALESPERSONNAME#]
        ///Currency :  [#CURRENCY#]
        ///Customer No :  [#CUSTOMERNO#]
        ///Terms :  [#TERM#]
        ///Approved By :  [#APPROVEDBY#]
        ///Client :  [#CLIENTNAME#]
        ///Supplier No: [#SUPPLIERNO#].
        /// </summary>
        internal static string CompanyApproved {
            get {
                return ResourceManager.GetString("CompanyApproved", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hello,
        ///
        ///Your Credit Limit Application [#CAF#] has been [#APPLICATIONSTATUS#] against  &lt;a href=&quot;[#HYPERLINK#]&quot;&gt; [#COMPANYNAME#]&lt;/a&gt;
        ///
        ///Kind Regards
        ///Global Trader.
        /// </summary>
        internal static string CreditLimitApplicationNotification {
            get {
                return ResourceManager.GetString("CreditLimitApplicationNotification", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please find attached credit note {0}
        ///
        ///Regards
        ///{1}.
        /// </summary>
        internal static string CreditNote {
            get {
                return ResourceManager.GetString("CreditNote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hello [#SALESMAN#],
        ///
        ///Kindly note the below mentioned Credit Note has been issued:
        ///
        ///Customer: [#CUSTOMER#]
        ///Contact: [#CONTACT#]
        ///Invoice Number: [#INV_NUMBER#]
        ///Customer PO: [#CUSTOMER_PO#]
        ///
        ///Please review this &lt;a href=&quot;[#HYPERLINK#]&quot;&gt;Credit Note [#CREDIT_NUMBER#]&lt;/a&gt;
        ///
        ///Kind Regards
        ///Global Trader.
        /// </summary>
        internal static string CreditNoteIssueNotification {
            get {
                return ResourceManager.GetString("CreditNoteIssueNotification", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New credit notes created..
        /// </summary>
        internal static string CreditNotesToPoHub {
            get {
                return ResourceManager.GetString("CreditNotesToPoHub", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New CRMA created..
        /// </summary>
        internal static string CustomerRMAToPoHub {
            get {
                return ResourceManager.GetString("CustomerRMAToPoHub", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IPO No :  [#InternalPurchaseOrderNumber#]   Line :   [#PoLine#]   Part :   [#part#]    Qty : [#Quantity#]                                                                                             
        ///Deallocated from the following Sales order(s) By  :  [#LoginFullName#]  
        ///.
        /// </summary>
        internal static string DeallocationNotification {
            get {
                return ResourceManager.GetString("DeallocationNotification", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SO : {0}  Line :  {1}  Qty : {2}.
        /// </summary>
        internal static string DeallocationNotificationLoopBody {
            get {
                return ResourceManager.GetString("DeallocationNotificationLoopBody", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IPO No : [#InternalPurchaseOrderNumber#]  Line :  [#PoLine#]  Deallocation Notification from HUB.
        /// </summary>
        internal static string DeallocationNotificationSubject {
            get {
                return ResourceManager.GetString("DeallocationNotificationSubject", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please find attached debit note {0}
        ///
        ///Regards
        ///{1}.
        /// </summary>
        internal static string DebitNote {
            get {
                return ResourceManager.GetString("DebitNote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New debit notes created..
        /// </summary>
        internal static string DebitNotesToPoHub {
            get {
                return ResourceManager.GetString("DebitNotesToPoHub", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please find attached Certificate Of Conformance {0}
        ///
        ///Regards
        ///{1}.
        /// </summary>
        internal static string EmailCertificateOfConformance {
            get {
                return ResourceManager.GetString("EmailCertificateOfConformance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please find attached details of Credit Note {0}
        ///
        ///Regards
        ///{1}.
        /// </summary>
        internal static string EmailCreditNote {
            get {
                return ResourceManager.GetString("EmailCreditNote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please find attached details of Customer RMA {0}
        ///
        ///Regards
        ///{1}.
        /// </summary>
        internal static string EmailCustomerRMA {
            get {
                return ResourceManager.GetString("EmailCustomerRMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please find attached details of Debit Note {0}
        ///
        ///Regards
        ///{1}.
        /// </summary>
        internal static string EmailDebitNote {
            get {
                return ResourceManager.GetString("EmailDebitNote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please find attached Invoice {0}
        ///
        ///Regards
        ///{1}.
        /// </summary>
        internal static string EmailInvoice {
            get {
                return ResourceManager.GetString("EmailInvoice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please find attached Packing Slip {0}
        ///
        ///Regards
        ///{1}.
        /// </summary>
        internal static string EmailPackingSlip {
            get {
                return ResourceManager.GetString("EmailPackingSlip", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please find attached Pro Forma Invoice {0}
        ///
        ///Regards
        ///{1}.
        /// </summary>
        internal static string EmailProFormaInvoice {
            get {
                return ResourceManager.GetString("EmailProFormaInvoice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please find attached details of Purchase Order {0}
        ///
        ///Regards
        ///{1}.
        /// </summary>
        internal static string EmailPurchaseOrder {
            get {
                return ResourceManager.GetString("EmailPurchaseOrder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quote Number {0}
        ///
        ///Regards
        ///{1}.
        /// </summary>
        internal static string EmailQuote {
            get {
                return ResourceManager.GetString("EmailQuote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please find attached details of Sales Order {0}
        ///
        ///Regards
        ///{1}.
        /// </summary>
        internal static string EmailSalesOrder {
            get {
                return ResourceManager.GetString("EmailSalesOrder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please find attached details of Supplier RMA {0}
        ///
        ///Regards
        ///{1}.
        /// </summary>
        internal static string EmailSupplierRMA {
            get {
                return ResourceManager.GetString("EmailSupplierRMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hello,&lt;/br&gt;
        ///&lt;b&gt;SO-Line Number:&lt;/b&gt; {0}&lt;/br&gt;
        ///&lt;b&gt;Part No:&lt;/b&gt; {1}&lt;/br&gt;
        ///&lt;b&gt;Message:&lt;/b&gt; The above sales order has been deallocated and deauthorised due to containing a line of quarantined stock, please review and reallocate/ reauthorise the SO..
        /// </summary>
        internal static string GIQuarantine {
            get {
                return ResourceManager.GetString("GIQuarantine", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hello,
        ///
        ///You have a GI query ({0}) to review - {3} click here for more information.
        ///
        ///{1}
        ///
        ///Kind Regards
        ///{2}.
        /// </summary>
        internal static string GIQuerySales {
            get {
                return ResourceManager.GetString("GIQuerySales", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hello,
        ///
        ///Below are the status of the Goods In Line ({0}) request:
        ///
        ///Status: {1}
        ///Action By: {2}
        ///Additional Comments: {3}
        ///
        ///Kind Regards
        ///{4}.
        /// </summary>
        internal static string GIQueryScreenReply {
            get {
                return ResourceManager.GetString("GIQueryScreenReply", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please find attached debit note {0}
        ///
        ///Regards
        ///{1}.
        /// </summary>
        internal static string HubDebit {
            get {
                return ResourceManager.GetString("HubDebit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please find attached invoice {0}
        ///
        ///Regards
        ///{1}.
        /// </summary>
        internal static string InvoicePDF {
            get {
                return ResourceManager.GetString("InvoicePDF", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please find invoice {0} attached with CofC
        ///
        ///Regards
        ///{1}.
        /// </summary>
        internal static string InvoiceWithCofc {
            get {
                return ResourceManager.GetString("InvoiceWithCofc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to I have added a &lt;a href=&quot;[#HYPERLINK#]&quot;&gt;new Credit Note&lt;/a&gt;:
        ///
        ///Number: [#CREDIT_NUMBER#]
        ///Customer: [#CUSTOMER#]
        ///Contact: [#CONTACT#]
        ///Invoice Number: [#INV_NUMBER#]
        ///Customer PO: [#CUSTOMER_PO#]
        ///Salesperson: [#SALESMAN#].
        /// </summary>
        internal static string NewCredit {
            get {
                return ResourceManager.GetString("NewCredit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to I have added a &lt;a href=&quot;[#HYPERLINK#]&quot;&gt;new Customer Requirement&lt;/a&gt;:
        ///
        ///Number: [#CUSREQ_NUMBER#]
        ///Customer: [#CUSTOMER#]
        ///Quantity: [#QUANTITY#]
        ///Part No: [#PART#]
        ///Cust Part No: [#CUSTOMERPART#]
        ///Manufacturer:	[#MANUFACTURER#]
        ///DateCode: [#DATECODE#]
        ///Product: [#PRODUCT#]
        ///Package: [#PACKAGE#]
        ///Target Price: [#PRICE#]
        ///Date Required: [#DATEREQUIRED#].
        /// </summary>
        internal static string NewCustomerRequirement {
            get {
                return ResourceManager.GetString("NewCustomerRequirement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &lt;b&gt;REQ Number :&lt;/b&gt; &lt;a href=&quot;[#HYPERLINK#]&quot;&gt; [#CUSREQ_NUMBER#] &lt;/a&gt;&lt;/br&gt;
        ///&lt;b&gt;Part No :&lt;/b&gt; [#PART#]&lt;/br&gt;
        ///&lt;b&gt;ECCN Code :&lt;/b&gt; [#ECCNCODE#]&lt;/br&gt;
        ///&lt;b&gt;Warning Message :&lt;/b&gt; [#WARNINGMESSAGE#]&lt;/br&gt;
        ///&lt;b&gt;ECCN Message :&lt;/b&gt; [#ECCNMESSAGE#]&lt;/br&gt;.
        /// </summary>
        internal static string NewCustomerRequirementwithECCNNotify {
            get {
                return ResourceManager.GetString("NewCustomerRequirementwithECCNNotify", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to I have added a &lt;a href=&quot;[#HYPERLINK#]&quot;&gt;new Customer RMA&lt;/a&gt;:
        ///
        ///Number: [#CRMA_NUMBER#]
        ///Customer: [#CUSTOMER#]
        ///Contact: [#CONTACT#]
        ///Invoice Number: [#INVOICE_NUMBER#]
        ///Authorised By: [#AUTHORISED_BY#].
        /// </summary>
        internal static string NewCustomerRMA {
            get {
                return ResourceManager.GetString("NewCustomerRMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to I have added a &lt;a href=&quot;[#HYPERLINK#]&quot;&gt;new Debit Note&lt;/a&gt;:
        ///
        ///Number: [#DEBIT_NUMBER#]
        ///Supplier: [#SUPPLIER#]
        ///Contact: [#CONTACT#]
        ///Purchase Order: [#PO_NUMBER#]
        ///Supplier RMA: [#SRMA_NUMBER#]
        ///Supplier Invoice: [#SUPPLIER_INV#]
        ///Buyer: [#BUYER#].
        /// </summary>
        internal static string NewDebit {
            get {
                return ResourceManager.GetString("NewDebit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to I have added a &lt;a href=&quot;[#HYPERLINK#]&quot;&gt;new Goods In Note&lt;/a&gt;:
        ///
        ///Number: [#GI_NUMBER#]
        ///Supplier: [#SUPPLIER#]
        ///PO Number: [#PO_NUMBER#].
        /// </summary>
        internal static string NewGoodsIn {
            get {
                return ResourceManager.GetString("NewGoodsIn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to I have added a &lt;a href=&quot;[#HYPERLINK#]&quot;&gt;new Invoice&lt;/a&gt;:
        ///
        ///Number: [#INVOICE_NUMBER#]
        ///Customer: [#CUSTOMER#]
        ///Contact: [#CONTACT#]
        ///Sales Order: [#SO_NUMBER#]
        ///Customer PO: [#CUSTOMER_PO#]
        ///Salesperson: [#SALESMAN#].
        /// </summary>
        internal static string NewInvoice {
            get {
                return ResourceManager.GetString("NewInvoice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to I have added a &lt;a href=&quot;[#HYPERLINK#]&quot;&gt;new Purchase Order&lt;/a&gt;:
        ///
        ///Number: [#PO_NUMBER#]
        ///Supplier: [#SUPPLIER#]
        ///Contact: [#CONTACT#]
        ///Buyer: [#BUYER#].
        /// </summary>
        internal static string NewPurchaseOrder {
            get {
                return ResourceManager.GetString("NewPurchaseOrder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to I have added a &lt;a href=&quot;[#HYPERLINK#]&quot;&gt;new Quote&lt;/a&gt;:
        ///
        ///Number: [#QUOTE_NUMBER#]
        ///Customer: [#CUSTOMER#]
        ///Contact: [#CONTACT#]
        ///Currency: [#CURRENCY#]
        ///Date Quoted: [#DATE#].
        /// </summary>
        internal static string NewQuote {
            get {
                return ResourceManager.GetString("NewQuote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to I have added a &lt;a href=&quot;[#HYPERLINK#]&quot;&gt;new Sales Order&lt;/a&gt;:
        ///
        ///Number: [#SO_NUMBER#]
        ///Customer: [#CUSTOMER#]
        ///Contact: [#CONTACT#]
        ///Salesperson: [#SALESMAN#].
        /// </summary>
        internal static string NewSalesOrder {
            get {
                return ResourceManager.GetString("NewSalesOrder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to I have added a &lt;a href=&quot;[#HYPERLINK#]&quot;&gt;new Supplier Invoice&lt;/a&gt;:
        ///
        ///Number: [#SI_NUMBER#]
        ///Supplier: [#SUPPLIER#]
        ///.
        /// </summary>
        internal static string NewSupplierInvoice {
            get {
                return ResourceManager.GetString("NewSupplierInvoice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to I have added a &lt;a href=&quot;[#HYPERLINK#]&quot;&gt;new Supplier RMA&lt;/a&gt;:
        ///
        ///Number: [#SRMA_NUMBER#]
        ///Supplier: [#SUPPLIER#]
        ///Contact: [#CONTACT#]
        ///PO Number: [#PO_NUMBER#]
        ///Authorised By: [#AUTHORISED_BY#].
        /// </summary>
        internal static string NewSupplierRMA {
            get {
                return ResourceManager.GetString("NewSupplierRMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please review this &lt;a href=&quot;[#HYPERLINK#]&quot;&gt;BOM Manager&lt;/a&gt;:
        ///
        ///BOM Manager: [#IPO_BOM_NUMBER#].
        /// </summary>
        internal static string NotifyCloseBomManager {
            get {
                return ResourceManager.GetString("NotifyCloseBomManager", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please review this &lt;a href=&quot;[#HYPERLINK#]&quot;&gt;Internal Purchase Order&lt;/a&gt;:
        ///
        ///IPO Number:[#IPO_NUMBER#]
        ///PO Number: [#PO_NUMBER#]
        ///Buyer: [#BUYER#].
        /// </summary>
        internal static string NotifyCreateIPO {
            get {
                return ResourceManager.GetString("NotifyCreateIPO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchase order created &amp; pending for verification .
        /// </summary>
        internal static string NotifyCreateIPOSubject {
            get {
                return ResourceManager.GetString("NotifyCreateIPOSubject", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please review this &lt;a href=&quot;[#HYPERLINK#]&quot;&gt;Purchase Order&lt;/a&gt;:
        ///
        ///IPO Number:[#IPO_NUMBER#]
        ///PO Number: [#PO_NUMBER#]
        ///Buyer: [#BUYER#].
        /// </summary>
        internal static string NotifyCreatePOMail {
            get {
                return ResourceManager.GetString("NotifyCreatePOMail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &lt;b&gt;PO Number :&lt;/b&gt; &lt;a href=&quot;[#HYPERLINK#]&quot;&gt; [#PO_NUMBER#] &lt;/a&gt;&lt;/br&gt;
        ///&lt;b&gt;Part No :&lt;/b&gt; [#PART#]&lt;/br&gt;
        ///&lt;b&gt;ECCN Code :&lt;/b&gt; [#ECCNCODE#]&lt;/br&gt;
        ///&lt;b&gt;Warning Message :&lt;/b&gt; [#WARNINGMESSAGE#]&lt;/br&gt;
        ///&lt;b&gt;ECCN Message :&lt;/b&gt; [#ECCNMESSAGE#]&lt;/br&gt;.
        /// </summary>
        internal static string NotifyECCNPO {
            get {
                return ResourceManager.GetString("NotifyECCNPO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &lt;b&gt;SO Number :&lt;/b&gt; &lt;a href=&quot;[#HYPERLINK#]&quot;&gt; [#SO_NUMBER#] &lt;/a&gt;&lt;/br&gt;
        ///&lt;b&gt;Part No :&lt;/b&gt; [#PART#]&lt;/br&gt;
        ///&lt;b&gt;ECCN Code :&lt;/b&gt; [#ECCNCODE#]&lt;/br&gt;
        ///&lt;b&gt;Warning Message :&lt;/b&gt; [#WARNINGMESSAGE#]&lt;/br&gt;
        ///&lt;b&gt;ECCN Message :&lt;/b&gt; [#ECCNMESSAGE#]&lt;/br&gt;.
        /// </summary>
        internal static string NotifyECCNSalesOrder {
            get {
                return ResourceManager.GetString("NotifyECCNSalesOrder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Number: [#EPR_NO#]
        ///Supplier: [#SUPPLIER#]
        ///Client: [#CLIENT#].
        /// </summary>
        internal static string NotifyEPR {
            get {
                return ResourceManager.GetString("NotifyEPR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please review this &lt;a href=&quot;[#HYPERLINK#]&quot;&gt;Goods In Note&lt;/a&gt;:
        ///
        ///Number: [#GI_NUMBER#]
        ///Supplier: [#SUPPLIER#]
        ///Received By: [#RECEIVED_BY#]
        ///PO Number: [#PO_NUMBER#].
        /// </summary>
        internal static string NotifyGoodsIn {
            get {
                return ResourceManager.GetString("NotifyGoodsIn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PO Number: [#PO_NO#]
        ///NPR Number: [#NPR_NO#]
        ///Supplier: [#SUPPLIER#]
        ///Client: [#CLIENT#]
        ///Non-conformance detail (Reason for rejection): [#NC_DETAIL#].
        /// </summary>
        internal static string NotifyNPR {
            get {
                return ResourceManager.GetString("NotifyNPR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please review this &lt;a href=&quot;[#HYPERLINK#]&quot;&gt;Purchase Order&lt;/a&gt;:
        ///
        ///PO Number: [#PO_NUMBER#].
        /// </summary>
        internal static string NotifyPOApproved {
            get {
                return ResourceManager.GetString("NotifyPOApproved", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please review this &lt;a href=&quot;[#HYPERLINK#]&quot;&gt;Internal Purchase Order&lt;/a&gt;:
        ///
        ///IPO Number:[#IPO_NUMBER#]
        ///PO Number: [#PO_NUMBER#]
        ///Buyer: [#BUYER#].
        /// </summary>
        internal static string NotifyPOApprovedNew {
            get {
                return ResourceManager.GetString("NotifyPOApprovedNew", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchase order Approved.
        /// </summary>
        internal static string NotifyPOApprovedSubject {
            get {
                return ResourceManager.GetString("NotifyPOApprovedSubject", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchase order Un-Approved.
        /// </summary>
        internal static string NotifyPOUnApprovedSubject {
            get {
                return ResourceManager.GetString("NotifyPOUnApprovedSubject", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please review this &lt;a href=&quot;[#HYPERLINK#]&quot;&gt;Purchase Order&lt;/a&gt;:
        ///
        ///Number: [#PO_NUMBER#]
        ///Supplier: [#SUPPLIER#]
        ///Contact: [#CONTACT#]
        ///Buyer: [#BUYER#].
        /// </summary>
        internal static string NotifyPurchaseOrder {
            get {
                return ResourceManager.GetString("NotifyPurchaseOrder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please review this &lt;a href=&quot;[#HYPERLINK#]&quot;&gt;HUBRFQ&lt;/a&gt;:
        ///
        ///HUBRFQ: [#IPO_BOM_NUMBER#].
        /// </summary>
        internal static string NotifyPurchaseRequestBom {
            get {
                return ResourceManager.GetString("NotifyPurchaseRequestBom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please review this BOM Manager:
        ///
        ///BOM Manager: &lt;a href=&quot;[#HYPERLINK#]&quot;&gt; [#IPO_BOM_NUMBER#]&lt;/a&gt;.
        /// </summary>
        internal static string NotifyPurchaseRequestBomManager {
            get {
                return ResourceManager.GetString("NotifyPurchaseRequestBomManager", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please review this &lt;a href=&quot;[#HYPERLINK#]&quot;&gt;HUBRFQ&lt;/a&gt;:
        ///
        ///HUBRFQ: [#IPO_BOM_NUMBER#]
        ///.
        /// </summary>
        internal static string NotifyReleaseBom {
            get {
                return ResourceManager.GetString("NotifyReleaseBom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please review this BOMManager:
        ///
        ///BOMManager: &lt;a href=&quot;[#HYPERLINK#]&quot;&gt; [#IPO_BOM_NUMBER#] &lt;/a&gt;.
        /// </summary>
        internal static string NotifyReleaseBomManager {
            get {
                return ResourceManager.GetString("NotifyReleaseBomManager", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dear Reverse Logistics Team Member,&lt;/br&gt;A HUBRFQ has been raised for a part that we have recently made an offer for.&lt;/br&gt;Details of the HUBRFQ are here&lt;/br&gt;Please review this &lt;a href=&quot;[#HYPERLINK#]&quot;&gt;[#IPO_BOM_NUMBER#]&lt;/a&gt;&lt;/br&gt;Part No : [#PART#]&lt;/br&gt;Company Name : [#COMPANYNAME#]&lt;/br&gt;Please visit and action&lt;/br&gt;Kind Regards&lt;/br&gt;Global Trader&lt;/br&gt;[#PARTLINESRL#].
        /// </summary>
        internal static string NotifyReverseLogistics {
            get {
                return ResourceManager.GetString("NotifyReverseLogistics", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please review this &lt;a href=&quot;[#HYPERLINK#]&quot;&gt;Sales Order&lt;/a&gt;:
        ///
        ///Number: [#SO_NUMBER#]
        ///Customer: [#CUSTOMER#]
        ///Contact: [#CONTACT#]
        ///Salesperson: [#SALESMAN#].
        /// </summary>
        internal static string NotifySalesOrder {
            get {
                return ResourceManager.GetString("NotifySalesOrder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please review this &lt;a href=&quot;[#HYPERLINK#]&quot;&gt;Supplier Invoice&lt;/a&gt;:
        ///
        ///Number: [#SI_NUMBER#]
        ///Supplier: [#SUPPLIER#].
        /// </summary>
        internal static string NotifySupplierInvoice {
            get {
                return ResourceManager.GetString("NotifySupplierInvoice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &lt;a href=&quot;[#HYPERLINK#]&quot;&gt;NPR&lt;/a&gt;.
        /// </summary>
        internal static string NPREmail {
            get {
                return ResourceManager.GetString("NPREmail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please find attached Packing Slip {0}
        ///
        ///Regards
        ///{1}.
        /// </summary>
        internal static string PackingSlip {
            get {
                return ResourceManager.GetString("PackingSlip", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please find packing slip {0} attached with invoice and CofC
        ///
        ///Regards
        ///{1}.
        /// </summary>
        internal static string PackingSlipWithCofc {
            get {
                return ResourceManager.GetString("PackingSlipWithCofc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hi ,&lt;br/&gt;
        ///The promise date has been changed for the sales order number {0}&lt;br/&gt;
        ///Promise date : {1} &lt;br/&gt;
        ///Reason: {2}	&lt;/br&gt;
        ///Sales Order : {0} &lt;/br&gt;
        ///Thanks &amp; Regards,&lt;/br&gt;
        ///{3} &lt;/br&gt;.
        /// </summary>
        internal static string PromiseReasonEmail {
            get {
                return ResourceManager.GetString("PromiseReasonEmail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  &lt;table width=&quot;100%&quot; border=&quot;1&quot; cellspacing=&quot;1&quot; cellpadding=&quot;1&quot; bordercolor=&quot;#000000&quot;&gt;&lt;tr style=&quot;background-color:sandybrown&quot;&gt;&lt;th style=&quot;text-align: center; vertical-align: top&quot; class=&quot;Textclass&quot;&gt;HUBRFQ No &lt;br /&gt;&lt;br/&gt;Date&lt;/th&gt;        &lt;th style=&quot;text-align: center; vertical-align: top&quot; class=&quot;Textclass&quot;&gt;Customer&lt;/th&gt;        &lt;th style=&quot;text-align: center; vertical-align: top &quot; class=&quot;Textclass&quot;&gt;Part No &lt;br /&gt;&lt;br/&gt;CustomerPart No&lt;/th&gt;        &lt;th style=&quot;text-align: center; vertical-align: top&quot; class=&quot;Textclass&quot;&gt; [rest of string was truncated]&quot;;.
        /// </summary>
        internal static string ProspectiveOfferEmail {
            get {
                return ResourceManager.GetString("ProspectiveOfferEmail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hello [#SALEMAN#],.
        /// </summary>
        internal static string ProspectiveOfferHeaderMail {
            get {
                return ResourceManager.GetString("ProspectiveOfferHeaderMail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please find attached purchase order {0}
        ///
        ///Regards
        ///{1}.
        /// </summary>
        internal static string PurchaseOrder {
            get {
                return ResourceManager.GetString("PurchaseOrder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to I have received Purchase Order [#PO_NUMBER#] on &lt;a href=&quot;[#HYPERLINK#]&quot;&gt;Goods In Note [#GI_NUMBER#]&lt;/a&gt;:
        ///
        ///Number: [#GI_NUMBER#]
        ///Supplier: [#SUPPLIER#]
        ///PO Number: [#PO_NUMBER#].
        /// </summary>
        internal static string ReceivedPurchaseOrder {
            get {
                return ResourceManager.GetString("ReceivedPurchaseOrder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  &lt;table width=&quot;100%&quot; border=&quot;1&quot; cellspacing=&quot;1&quot; cellpadding=&quot;1&quot; bordercolor=&quot;#000000&quot;&gt;&lt;tr style=&quot;background-color:sandybrown&quot;&gt;&lt;th style=&quot;text-align: center; vertical-align: top&quot; class=&quot;Textclass&quot;&gt;Company Name&lt;/th&gt;&lt;th style=&quot;text-align: center; vertical-align: top&quot; class=&quot;Textclass&quot;&gt;Part No&lt;/th&gt;&lt;th style=&quot;text-align: center; vertical-align: top&quot; class=&quot;Textclass&quot;&gt;Mrf&lt;/th&gt;&lt;th style=&quot;text-align: center; vertical-align: top&quot; class=&quot;Textclass&quot;&gt;Product&lt;/th&gt;&lt;th style=&quot;text-align: center; vertical-align: top&quot; class= [rest of string was truncated]&quot;;.
        /// </summary>
        internal static string ReverseLogisticStockBody1 {
            get {
                return ResourceManager.GetString("ReverseLogisticStockBody1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  &lt;table width=&quot;100%&quot; border=&quot;1&quot; cellspacing=&quot;1&quot; cellpadding=&quot;1&quot; bordercolor=&quot;#000000&quot;&gt;&lt;tr style=&quot;background-color:sandybrown&quot;&gt;&lt;th style=&quot;text-align: center; vertical-align: top&quot; class=&quot;Textclass&quot;&gt;HUBRFQ No&lt;/th&gt;&lt;th style=&quot;text-align: center; vertical-align: top&quot; class=&quot;Textclass&quot;&gt;Company Name&lt;/th&gt;&lt;th style=&quot;text-align: center; vertical-align: top&quot; class=&quot;Textclass&quot;&gt;Mrf&lt;/th&gt;&lt;th style=&quot;text-align: center; vertical-align: top&quot; class=&quot;Textclass&quot;&gt;Product&lt;/th&gt;&lt;th style=&quot;text-align: center; vertical-align: top&quot; clas [rest of string was truncated]&quot;;.
        /// </summary>
        internal static string ReverseLogisticStockBody2 {
            get {
                return ResourceManager.GetString("ReverseLogisticStockBody2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please confirm price and availability for the following, thank you.
        /// </summary>
        internal static string RFQ {
            get {
                return ResourceManager.GetString("RFQ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please find attached sales order {0}
        ///
        ///Regards
        ///{1}.
        /// </summary>
        internal static string SalesOrder {
            get {
                return ResourceManager.GetString("SalesOrder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please review this &lt;a href=&quot;[#HYPERLINK#]&quot;&gt;Sales Order&lt;/a&gt;:
        ///
        ///Number: [#SO_NUMBER#]
        ///Customer: [#CUSTOMER#]
        ///Contact: [#CONTACT#]
        ///Salesperson: [#SALESMAN#].
        /// </summary>
        internal static string SalesOrderRequestApproval {
            get {
                return ResourceManager.GetString("SalesOrderRequestApproval", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sales order has been checked: {0}
        ///Customer : {2}
        ///Comments :{3}
        ///Regards
        ///{1}.
        /// </summary>
        internal static string SOAuthorized {
            get {
                return ResourceManager.GetString("SOAuthorized", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please find attached pro forma invoice {0}
        ///
        ///Regards
        ///{1}.
        /// </summary>
        internal static string SOProForma {
            get {
                return ResourceManager.GetString("SOProForma", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New SRMA created..
        /// </summary>
        internal static string SupplierRMAToPoHub {
            get {
                return ResourceManager.GetString("SupplierRMAToPoHub", resourceCulture);
            }
        }
    }
}
