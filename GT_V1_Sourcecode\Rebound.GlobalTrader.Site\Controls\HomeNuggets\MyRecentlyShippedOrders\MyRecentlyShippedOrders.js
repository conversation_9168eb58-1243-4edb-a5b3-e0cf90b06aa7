Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.HomeNuggets");Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyRecentlyShippedOrders=function(n){Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyRecentlyShippedOrders.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyRecentlyShippedOrders.prototype={get_tblShipped:function(){return this._tblShipped},set_tblShipped:function(n){this._tblShipped!==n&&(this._tblShipped=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyRecentlyShippedOrders.callBaseMethod(this,"initialize");this.addRefreshEvent(Function.createDelegate(this,this.getData))},dispose:function(){this.isDisposed||(this._tblShipped&&this._tblShipped.dispose(),this._tblShipped=null,Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyRecentlyShippedOrders.callBaseMethod(this,"dispose"))},setupLoadingState:function(){this._tblShipped.show(!1);Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyRecentlyShippedOrders.callBaseMethod(this,"setupLoadingState")},getData:function(){this.setupLoadingState();var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/HomeNuggets/MyRecentlyShippedOrders");n.set_DataObject("MyRecentlyShippedOrders");n.set_DataAction("GetData");n.addParameter("rowcount",this._intRowCount);n.addDataOK(Function.createDelegate(this,this.getDataComplete));n.addError(Function.createDelegate(this,this.homeNuggetDataError));n.addTimeout(Function.createDelegate(this,this.homeNuggetDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getDataComplete:function(n){var i,r,t,u;for(this.showNoneFoundOrContent(n._result.Count),i=n._result,this._tblShipped.clearTable(),this._tblShipped.show(i.Shipped.length>0),r=0;r<i.Shipped.length;r++)t=i.Shipped[r],u=[$RGT_nubButton_SalesOrder(t.ID,t.No),$RGT_nubButton_Company(t.CMNo,t.CM),$R_FN.writeDoubleCellValue(t.Due,t.DateShipped)],this._tblShipped.addRow(u,null);this.hideLoading()}};Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyRecentlyShippedOrders.registerClass("Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyRecentlyShippedOrders",Rebound.GlobalTrader.Site.Controls.HomeNuggets.Base,Sys.IDisposable);