///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//
// RP 06.10.2009:
// - Changes due to changes in base class
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");

Rebound.GlobalTrader.Site.Controls.DropDowns.Product = function(element) { 
	Rebound.GlobalTrader.Site.Controls.DropDowns.Product.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.DropDowns.Product.prototype = {
    get_intPOHubClientNo: function() { return this._intPOHubClientNo; }, set_intPOHubClientNo: function(v) { if (this._intPOHubClientNo !== v) this._intPOHubClientNo = v; },
    get_intGlobalLoginClientNo: function () { return this._intGlobalLoginClientNo; }, set_intGlobalLoginClientNo: function (v) { if (this._intGlobalLoginClientNo !== v) this._intGlobalLoginClientNo = v; },
    initialize: function() {
        Rebound.GlobalTrader.Site.Controls.DropDowns.Product.callBaseMethod(this, "initialize");
        this.addSetupDataCall(Function.createDelegate(this, this.setupDataCall));
        this.addGetDataOK(Function.createDelegate(this, this.dataCallOK));
    },

    dispose: function() {
        if (this.isDisposed) return;
        this._intPOHubClientNo = null;
        this._intGlobalLoginClientNo = null;
        Rebound.GlobalTrader.Site.Controls.DropDowns.Product.callBaseMethod(this, "dispose");
    },

    setupDataCall: function() {
        this._objData.set_PathToData("controls/DropDowns/Product");
        this._objData.set_DataObject("Product");
        this._objData.set_DataAction("GetData");
        this._objData.addParameter("POHubClientNo", this._intPOHubClientNo);
        this._objData.addParameter("GlobalLoginClientNo", this._intGlobalLoginClientNo);
    },

    dataCallOK: function() {
        var result = this._objData._result;
        if (result.Products) {
            for (var i = 0; i < result.Products.length; i++) {
                this.addOption(result.Products[i].Name, result.Products[i].ID);
            }
        }
    }

};

Rebound.GlobalTrader.Site.Controls.DropDowns.Product.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.Product", Rebound.GlobalTrader.Site.Controls.DropDowns.Base);
