///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//
// RP 23.12.2009:
// - render state on server
//
// RP 16.10.2009:
// - Changes due to changes in base class
//-----------------------------------------------------------------------------------------
//Marker     Changed by        Date         Remarks
//[001]      <PERSON><PERSON>     28/08/2018    Get Radio button selected value for showing grid data and export csv.
//[002]      Devendra Sikarwar 10/01/2024    RP-2727 (Show and Hide MailGroup DropDown and Change Label Assigned User)
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DataListNuggets");

Rebound.GlobalTrader.Site.Controls.DataListNuggets.BOMManagerLines = function (element) {
    Rebound.GlobalTrader.Site.Controls.DataListNuggets.BOMManagerLines.initializeBase(this, [element]);
    this._frmConfirm = null;
    this._intSalesPersonId = null;
};

Rebound.GlobalTrader.Site.Controls.DataListNuggets.BOMManagerLines.prototype = {
    get_blnPOHub: function () { return this._blnPOHub; }, set_blnPOHub: function (value) { if (this._blnPOHub !== value) this._blnPOHub = value; },
    get_ibtnExportCSV: function () { return this._ibtnExportCSV; }, set_ibtnExportCSV: function (v) { if (this._ibtnExportCSV !== v) this._ibtnExportCSV = v; },//[001]  
    //get_hypAssignToMe: function() { return this._hypAssignToMe; }, set_hypAssignToMe: function(value) { if (this._hypAssignToMe !== value) this._hypAssignToMe = value; },
    //  get_pnlCustReq: function () { return this._pnlCustReq; }, set_pnlCustReq: function (v) { if (this._pnlCustReq !== v) this._pnlCustReq = v; },
    // get_tblCustReq: function () { return this._tblCustReq; }, set_tblCustReq: function (v) { if (this._tblCustReq !== v) this._tblCustReq = v; },
    get_sortIndex: function () { return this._sortIndex; }, set_sortIndex: function (v) { if (this._sortIndex !== v) this._sortIndex = v; },
    get_sortDir: function () { return this._sortDir; }, set_sortDir: function (v) { if (this._sortDir !== v) this._sortDir = v; },
    get_pageIndex: function () { return this._pageIndex; }, set_pageIndex: function (v) { if (this._pageIndex !== v) this._pageIndex = v; },
    get_pageSize: function () { return this._pageSize; }, set_pageSize: function (v) { if (this._pageSize !== v) this._pageSize = v; },
    get_code: function () { return this._code; }, set_code: function (v) { if (this._code !== v) this._code = v; },
    get_name: function () { return this._name; }, set_name: function (v) { if (this._name !== v) this._name = v; },
    get_intSalesPersonId: function () { return this._intSalesPersonId; }, set_intSalesPersonId: function (v) { if (this._intSalesPersonId !== v) this._intSalesPersonId = v; },

    initialize: function () {
        //debugger;
        //this._ibtnPrint = $get(this._aryButtonIDs[0]);
        //this._ibtnPrint = $get(this._aryButtonIDs[0]);
        this.addSetupDataCallEvent(Function.createDelegate(this, this.setupDataCall));
        this.addGetDataOKEvent(Function.createDelegate(this, this.getDataOK));
        this.addInitCompleteEvent(Function.createDelegate(this, this.initAfterBaseIsReady));
        this._strPathToData = "controls/DataListNuggets/BOMManagerLines";
        this._strDataObject = "BOMManagerLines";
        //$find(this.getFilterField("ctlClient").ControlID).addChanged(Function.createDelegate(this, this.getDivision));
        Rebound.GlobalTrader.Site.Controls.DataListNuggets.BOMManagerLines.callBaseMethod(this, "initialize");
        if (this._ibtnExportCSV) $R_IBTN.addClick(this._ibtnExportCSV, Function.createDelegate(this, this.exportCSV));//[001]
        //$find(this.getFilterField("ctlClient").get_id())._element.setAttribute("onchange", String.format("$find(\"{0}\").onClientChange()", this._element.id));
    },

    initAfterBaseIsReady: function () {
        this.addPageTabChangedEvent(Function.createDelegate(this, this.pageTabChanged));
        //alert($find((this.getFilterField("ctlClient")).ControlID));
        //[002] START CODE
        var clientId = $('#ctl00_ddlClientByMaster_ddl').val();
        if (clientId!= '114') {
            $('#ctl00_cphMain_ctlBOMManagerResults_ctlDB_ctl16_ctlFilter_ctlSalesperson td :eq(0)').html('').html('Assigned User');
            $('#ctl00_cphMain_ctlBOMManagerResults_ctlDB_ctl16_ctlFilter_ctlBomMailGroups').hide();
        }
            //[002] END CODE
        this.updateFilterVisibility();
        this.getData();
    },

    dispose: function () {
        if (this.isDisposed) return;
        this._blnPOHub = null;
        if (this._ibtnExportCSV) $R_IBTN.clearHandlers(this._ibtnExportCSV);//[001]
        this._ibtnExportCSV = null;//[001]
        //  this._pnlCustReq = null;
        // this._tblCustReq = null;
        Rebound.GlobalTrader.Site.Controls.DataListNuggets.BOMManagerLines.callBaseMethod(this, "dispose");
    },
    pageTabChanged: function () {
        this._table._intCurrentPage = 1;
        this._enmViewLevel = this._intCurrentTab;
        this.updateFilterVisibility();
        this.getData();
    },

    setupDataCall: function () {
        //alert(this.getRadioButtonValue());
        this._objData.addParameter("SelectedRadio", this.getRadioButtonValue());//[001] 
        this._objData.addParameter("ViewLevel", this._enmViewLevel);

    },
    //start [001]
    getRadioButtonValue: function () {
        var result = "";
        var list = document.getElementById("ctl00_cphMain_ctlBOMManagerResults_ctlDB_ctl16_ctlFilter_ctl11_ctl02_radHeaderDetail"); //Client ID of the radiolist
        var inputs = list.getElementsByTagName("input");
        var selected;
        for (var i = 0; i < inputs.length; i++) {
            if (inputs[i].checked) {
                selected = inputs[i];
                result = selected.value;
                break;
            }
        }
        return result;
    },
    //end [001]
    getDataOK: function () {
        //debugger;

        var isSearchFromRequirements = this._objResult.isSearchFromRequirements;
        this._sortIndex = this._objResult.SortIndex;
        this._sortDir = this._objResult.SortDir;
        this._pageIndex = this._objResult.PageIndex;
        this._pageSize = this._objResult.PageSize;
        this._code = this._objResult.Code;
        this._name = this._objResult.Name;
        this._intSalesPersonId = this._objResult.SalesPerson;
        //  alert(isSearchFromRequirements);
        if (!isSearchFromRequirements) {
            for (var i = 0, l = this._objResult.Results.length; i < l; i++) {
                var row = this._objResult.Results[i];

                var aryData = [
                    //$RGT_nubButton_BOM(row.ID, row.Name)
                    //, $R_FN.setCleanTextValue(row.AssignedUser)
                    //, $R_FN.setCleanTextValue(row.Contact)
                    //, $R_FN.setCleanTextValue(row.Company)
                    //, row.BOMStatus
                    //, row.Date
                    //, row.TotalValue


                    $R_FN.writeDoubleCellValue(row.isPoHUB == true ? $RGT_nubButton_BMMPO(row.ID, row.Code) : $RGT_nubButton_BMM(row.ID, row.Code))
                    , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Name))
                    , $R_FN.writeDoubleCellValue("", "")
                    , $R_FN.writeDoubleCellValue($RGT_nubButton_Company(row.CompanyNo, row.Company), $RGT_nubButton_Contact(row.ContactNo, row.Contact))
                    , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Salesman))
                    , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.ReceivedDate), $R_FN.setCleanTextValue(row.PromiseDate))
                    , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.BOMStatus))
                    , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.TotalValue))
                    ////, $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Code), $R_FN.setCleanTextValue(row.Salesman))//$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Code), "")
                    //, $R_FN.writeTriCellValue($R_FN.setCleanTextValue(row.Code), $R_FN.setCleanTextValue(row.Salesman), $R_FN.setCleanTextValue(""))
                    //, $R_FN.writeDoubleCellValue($RGT_nubButton_Company(row.CompanyNo, row.Company), $RGT_nubButton_Contact(row.ContactNo, row.Contact))
                    ////, $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Company),"")
                    //, $R_FN.writeDoubleCellValue(row.BOMStatus,$R_FN.setCleanTextValue(row.DivName))
                    //,  $R_FN.writeDoubleCellValue(row.Date,"")
                    , row.TotalValue


                ];

                this._table.addRow(aryData, row.ID, false);
                //this._table.addRow(aryData, row.ID, false, null, strCSS);
                aryData = null; row = null;
            }
        }
        if (isSearchFromRequirements) {
            for (var i = 0, l = this._objResult.Results.length; i < l; i++) {
                var row = this._objResult.Results[i];
                var aryData = [
                    // $RGT_nubButton_CustomerRequirement(row.ID, row.No)
                    // , $R_FN.writeDoubleCellValue($R_FN.writePartNo(row.Part, row.ROHS), $RGT_nubButton_Manufacturer(row.MfrNo, row.Mfr))
                    // , row.Quantity
                    // , $R_FN.writeDoubleCellValue($RGT_nubButton_Company(row.CMNo, row.CM), $RGT_nubButton_Contact(row.ContactNo, row.Contact))
                    // , $R_FN.setCleanTextValue(row.Salesman)
                    // , $R_FN.writeDoubleCellValue(row.Received, row.Promised)
                    ////, $RGT_nubButton_BOM(row.BOMNo, row.BOMCode)
                    // , $RGT_nubButton_BOM(row.BOMNo, row.BOMName)

                    ////$R_FN.writeDoubleCellValue($RGT_nubButton_BOM(row.BOMNo, row.BOMName), row.isPoHUB ==true? row.No : $RGT_nubButton_CustomerRequirement(row.ID, row.No))
                    ////, $R_FN.writeDoubleCellValue("", row.Quantity)
                    //// , $R_FN.writeDoubleCellValue($R_FN.writePartNo(row.Part, row.ROHS), $RGT_nubButton_Manufacturer(row.MfrNo, row.Mfr))
                    //// //, $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.BOMCode), $R_FN.setCleanTextValue(row.Salesman))
                    //// , $R_FN.writeTriCellValue($R_FN.setCleanTextValue(row.BOMCode), $R_FN.setCleanTextValue(row.Salesman), $R_FN.setCleanTextValue($R_FN.showYellowText(row.SupportTeamMember)))
                    //// , $R_FN.writeDoubleCellValue($RGT_nubButton_Company(row.CMNo, row.CM), $RGT_nubButton_Contact(row.ContactNo, row.Contact))
                    //// , $R_FN.writeDoubleCellValue(row.BOMStatus, $R_FN.setCleanTextValue(row.DivName))
                    //// , $R_FN.writeDoubleCellValue(row.Received, row.Promised)
                    //////, $RGT_nubButton_BOM(row.BOMNo, row.BOMCode)
                    //// , row.TotalValue


                    $R_FN.writeDoubleCellValue(row.isPoHUB == true ? $RGT_nubButton_BMMPO(row.ID, row.BOMCode) : $RGT_nubButton_BMM(row.ID, row.BOMCode))
                    , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.BOMName))
                    , $R_FN.writeDoubleCellValue($R_FN.writePartNo(row.Part, row.ROHS), $RGT_nubButton_Manufacturer(row.MfrNo, row.Mfr))
                    , $R_FN.writeDoubleCellValue($RGT_nubButton_Company(row.CMNo, row.CM), $RGT_nubButton_Contact(row.ContactNo, row.Contact))
                   // , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.AssignedUser), "")
                    , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Salesman))
                    , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Received), $R_FN.setCleanTextValue(row.Promised))
                    , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.BOMStatus))
                    //, $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Part))
                    , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.TotalValue))
                    ////, $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Code), $R_FN.setCleanTextValue(row.Salesman))//$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Code), "")
                    //, $R_FN.writeTriCellValue($R_FN.setCleanTextValue(row.Code), $R_FN.setCleanTextValue(row.Salesman), $R_FN.setCleanTextValue(""))
                    //, $R_FN.writeDoubleCellValue($RGT_nubButton_Company(row.CompanyNo, row.Company), $RGT_nubButton_Contact(row.ContactNo, row.Contact))
                    ////, $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Company),"")
                    //, $R_FN.writeDoubleCellValue(row.BOMStatus,$R_FN.setCleanTextValue(row.DivName))
                    //,  $R_FN.writeDoubleCellValue(row.Date,"")
                    , row.TotalValue
                ];

                // var strCSS = (row.SupportTeamMember) ? "ceased" : "ceased";
                this._table.addRow(aryData, row.ID, false);
                //this._table.addRow(aryData, row.ID, false, null, strCSS);
                aryData = null; row = null;
            }
        }
    },
    updateFilterVisibility: function() {
        //debugger;
        this.getFilterField("ctlDivision")._ddl._intSelClientNo = 9999;
        this.getFilterField("ctlDivision")._ddl.getData();

        this.getFilterField("ctlClient").show(this._blnPOHub);
        this.getFilterField("ctlDivision").show(this._blnPOHub);
        //this._ibtnAssignToMe.show(this._blnPOHub);

    },

    //getDivision: function () {
    //    //alert("tets");
    //    if (this.getFilterField("ctlClient").getValue() == null || this.getFilterField("ctlClient").getValue() == -1)
    //        this.getFilterField("ctlDivision")._ddl._intSelClientNo = 9999;
    //    else
    //        this.getFilterField("ctlDivision")._ddl._intSelClientNo = this.getFilterField("ctlClient").getValue();
    //    this.getFilterField("ctlDivision")._ddl.getData();
    //   // alert(this.getFilterField("ctlDivision")._ddl._intSelClientNo);
    //  //  alert("ctlClient=" + this.getFilterField("ctlClient").getValue());
    //   // this.getFilterField("ctlContact")._ddl._intCompanyID = this._intCompanyID;
    //    //if (this._blnForContact) this.getFilterField("ctlContact").setValue(this._intCompanyID);
    //},
    //start [001]
    exportCSV: function () {
        this.getData_Start();
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/DataListNuggets/BOMManagerLines");
        obj.set_DataObject("BOMManagerLines");
        obj.addParameter("ViewLevel", this._enmViewLevel);
        obj.set_DataAction("ExportToCSV");
        obj._intTimeoutMilliseconds = 500 * 1000;
        obj.addParameter("SortIndex", this._sortIndex);
        obj.addParameter("SortDir", this._sortDir);
        obj.addParameter("PageIndex", this._pageIndex);
        obj.addParameter("PageSize", this._pageSize);
        obj.addParameter("Code", this._code);
        obj.addParameter("Name", this._name);
        //obj.addParameter("SalesPerson", this._intSalesPersonId);
        //obj.addParameter("SelectedRadio", this.getRadioButtonValue());

        obj.addParameter("BomStatus", this.getFilterFieldValue("ctlStatus"));
        //obj.addParameter("PoHubBuyer", this.getFilterFieldValue("ctlClient"));
        obj.addParameter("Manufacturer", this.getFilterFieldValue("ctlManufacturer"));
        //obj.addParameter("Part", this.getFilterFieldValue("ctlPartNumber"));
        obj.addParameter("PoHubBuyer", this.getFilterFieldValue("ctlSalesperson"));
        obj.addParameter("StartDate", this.getFilterFieldValue("ctlStartDate"));
        obj.addParameter("EndDate", this.getFilterFieldValue("ctlEndDate"));


        obj.addDataOK(Function.createDelegate(this, this.exportCSV_OK));
        obj.addError(Function.createDelegate(this, this.exportCSV_Error));
        obj.addTimeout(Function.createDelegate(this, this.exportCSV_Error));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;

    },

    exportCSV_OK: function (args) {
        var res = args._result;
        if (res.Filename) {
            //add the date to the file to force the latest version to be returned (not from cache)
            var dt = new Date();
            location.href = String.format("{0}?t={1}", res.Filename, dt.getTime());
            dt = null;
        }
        this.getDataOK_End();
    },

    exportCSV_Error: function (args) {
        this.showError(true, args.get_ErrorMessage());
    },
    //end [001]
    onClientChange: function () {
        this.getDivision();
        this.getSalesPersonByClient();
    }
    //getSalesPersonByClient: function () {

    //    var intClientNo = 999;
    //    if (this.getFilterField("ctlClient").getValue() != null)
    //        intClientNo = this.getFilterField("ctlClient").getValue();

    //    this.getFilterField("ctlClientSalesperson")._ddl._intGlobalLoginClientNo = intClientNo;
    //    //this.getFilterField("ctlClientSalesperson")._ddl.addParameter("GlobalLoginClientNo", this._intGlobalLoginClientNo);
    //    this.getFilterField("ctlClientSalesperson")._ddl.getData();

    //}

};
Rebound.GlobalTrader.Site.Controls.DataListNuggets.BOMManagerLines.registerClass("Rebound.GlobalTrader.Site.Controls.DataListNuggets.BOMManagerLines", Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base, Sys.IDisposable);
