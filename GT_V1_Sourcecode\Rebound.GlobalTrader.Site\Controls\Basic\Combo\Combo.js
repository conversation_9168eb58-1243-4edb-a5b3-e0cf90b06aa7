Type.registerNamespace("Rebound.GlobalTrader.Site.Controls");Rebound.GlobalTrader.Site.Controls.Combo=function(n){Rebound.GlobalTrader.Site.Controls.Combo.initializeBase(this,[n]);this._intSelectedID=null;this._strSelectedText="";this._strSelectedExtraText=""};Rebound.GlobalTrader.Site.Controls.Combo.prototype={get_txt:function(){return this._txt},set_txt:function(n){this._txt!==n&&(this._txt=n)},get_aut:function(){return this._aut},set_aut:function(n){this._aut!==n&&(this._aut=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Combo.callBaseMethod(this,"initialize");this._aut.addSelectionMadeEvent(Function.createDelegate(this,this.selectionMade))},dispose:function(){this.isDisposed||(this.get_element()&&$clearHandlers(this.get_element()),this._aut&&this._aut.dispose(),this._txt=null,this._aut=null,this._intSelectedID=null,this._strSelectedText=null,this._strSelectedExtraText=null,Rebound.GlobalTrader.Site.Controls.Combo.callBaseMethod(this,"dispose"),this.isDisposed=!0)},reset:function(){this._intSelectedID=null;this._strSelectedText="";this._aut&&this._aut.reselect()},selectionMade:function(){this._intSelectedID=this._aut._varSelectedID;this._strSelectedText=this._aut._varSelectedValue;this._strSelectedExtraText=this._aut._varComboExtraText},checkEntered:function(){return this.selectionMade(),this._intSelectedID>0},setValue:function(n,t){n!=undefined&&t!=undefined&&(n||(n=""),t||(t=""),n==""||t==""?this._aut.reselect():this._aut.doItemClick(t,n))},getValue:function(){return this.selectionMade(),this._intSelectedID}};Rebound.GlobalTrader.Site.Controls.Combo.registerClass("Rebound.GlobalTrader.Site.Controls.Combo",Sys.UI.Control,Sys.IDisposable);