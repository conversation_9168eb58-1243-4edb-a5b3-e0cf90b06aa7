﻿/* 
===========================================================================================
TASK      		UPDATED BY     	DATE         	ACTION 			DESCRIPTION
[US-201576]		Phuc Hoang		22-May-2024		CREATE			[RP-2621] BOM Manager sourcing KUB 
[US-201581]		An.TranTan		30-May-2024		UPDATE			KUB Assistant in BOM Manager Details (Client Side): 
																- Filter data base on client/BOM Manager customer
																- Correct Lowest/Highest Invoiced Price
																- Correct Latest Quoted Price link
																- Add Date selected part last sold to the selected customer (client side)
[US-201581]		An.TranTan		02-Jun-2024		UPDATE			Add @IsHubRFQ flag to distinct data for BOM manager and HUBRFQ
[BUG-205015]	An.TranTan		04-Jun-2024		UPDATE			Set ClientID = 0 if client is POHub
[US-201581]	    An.TranTan		06-Jun-2024		UPDATE			Correct Latest Quoted Price shared by HUB
[US-201576]	    Phuc Hoang		06-Jun-2024		UPDATE			Add more Base Currency for Customer Quotes section
[US-201581]	    An.TranTan		11-Jun-2024		UPDATE			Update for feedback from James:
																- Count NumberOfRequirement base on ReceivedDate
																- Filter LastSoldPrice by BOM customer
[US-201581]	    An.TranTan		11-Jun-2024		UPDATE			GET IHS and Lytica data from table instead of API
[US-201581]	    An.TranTan		13-Jun-2024		UPDATE			Update logic base on feedback:
																- Order invoice by shipped date in case multiple invoice have same price
																- Using BOMManagerNo column instead of BOM to filter number of customer requirement
[US-205444]	    An.TranTan		15-Jun-2024		UPDATE			- Only get Lytica data for HUB side
																- Add title for quote price
[US-207699]	    An.TranTan		05-Jul-2024		UPDATE			KUB for HUBRFQ client side: count the win/loss ratio base on Quotes instead of QuoteLines
[US-207699]	    Phuc Hoang		08-Jul-2024		UPDATE			KUB for HUBRFQ client side: format for Quote Price
[US-207699]	    Phuc Hoang		09-Jul-2024		UPDATE			KUB for HUBRFQ client side: check when SourcingResult is NULL
===========================================================================================
*/

CREATE OR ALTER PROCEDURE [dbo].[usp_select_KubAssistanceForBOMManager]  
	@PartNumber  NVARCHAR(100) = NULL,              
	@ClientID  INT = 0,
	@BOMManagerID INT = 0,
	@IsHubRFQ BIT = 0,
	@ManufacturerId INT = NULL,
	@ManufacturerName NVARCHAR(500) = NULL
AS

BEGIN

SET NOCOUNT ON                                      
  
DECLARE @Last12Months DATETIME = dateadd(month,datediff(month,0,getdate())-12,0);                                  
DECLARE @LastestHubNumberDate DATE = NULL;
DECLARE @LastestHubRFQName NVARCHAR(500) = NULL;
DECLARE @LastestHubRFQId NVARCHAR(50) = NULL;
DECLARE @NumberOfRequirement INT = 0; -- Number of Customer Requirement for partNo in the last 12 months
DECLARE @LastQuotedPrice NVARCHAR(MAX) = NULL                       
DECLARE @LastHubprice NVARCHAR(MAX) = NULL              
DECLARE @LastSoldPrice NVARCHAR(MAX) = NULL;
DECLARE @LastHighestSoldPrice NVARCHAR(MAX) = NULL;
DECLARE @LastLowestSoldPrice NVARCHAR(MAX) = NULL;
DECLARE @NumberOfInvoice INT = 0; -- Number of PartNumber Invoiced for the in 12 months
DECLARE @NumberOfQuote INT = 0;
DECLARE @NumberQuoteToSalesOrder INT = 0;
DECLARE @LastUpdatedDate NVARCHAR(100) = NULL;
DECLARE @BOMManagerCustomerNo INT = NULL;
--FOR IHS data
DECLARE @IHSAveragePrice FLOAT = NULL, 
		@IHSPartStatus NVARCHAR(60) = NULL,
		@IHSResult NVARCHAR(MAX) = NULL;
--FOR Lytica
DECLARE @LyticaAveragePrice FLOAT = NULL, 
		@LyticaMarketLeading FLOAT = NULL,
		@LyticaTargetPrice FLOAT = NULL,
		@LyticaStatus VARCHAR(200) = NULL,
		@LyticaResult NVARCHAR(MAX) = NULL;


--Get all data if client is POHub
SET @ClientID = CASE WHEN @ClientID = 114 THEN 0 
					ELSE ISNULL(@ClientID, 0) 
				END;

---Get customer of HUBRFQ/BOM Manager---
IF(@IsHubRFQ = 1)
BEGIN
	SELECT @BOMManagerCustomerNo = CompanyNo FROM dbo.tbBOM WITH (NOLOCK) WHERE BOMId = @BOMManagerID;
END
ELSE BEGIN
	SELECT @BOMManagerCustomerNo = CompanyNo FROM dbo.tbBOMManager WITH (NOLOCK) WHERE BOMManagerId = @BOMManagerID;
END
---------END-----------

---Number of Customer Requirement for Part Number in the last 12 months---
SELECT @NumberOfRequirement=COUNT(*) FROM tbCustomerRequirement WITH (NOLOCK)
WHERE FullPart = @PartNumber
	AND (@ClientID = 0 OR ClientNo = @ClientID)
	AND ISNULL(BOMManagerNo, 0) = 0
	AND ReceivedDate >= @Last12Months;
---------END-----------
SELECT       
 TOP 1      
 @LastUpdatedDate = FORMAT(DLUP, 'D', 'en-gb' ) + ' '  + FORMAT( GETDATE(),'hh:mm tt', 'en-gb' ) 
FROM tbKubAssistanceDetailsCache WITH (NOLOCK)    
WHERE PartNo = @PartNumber

---Last HUBRFQ/Customer Requirement for Part Number---
SELECT TOP 1 
	@LastestHubRFQName = bom.BOMName,
	@LastestHubNumberDate = bom.DLUP,
	@LastestHubRFQId = bom.BOMId
FROM tbBOM bom WITH (NOLOCK)
JOIN tbCustomerRequirement cr WITH (NOLOCK) on cr.BOMNo = bom.BOMId
WHERE cr.FullPart = @PartNumber
	AND (@ClientID = 0 OR bom.ClientNo = @ClientID)
ORDER BY bom.DLUP DESC;
---------END-----------

---Latest Quoted Price---                            
SELECT TOP 1 @LastQuotedPrice=                             
	CASE
		WHEN qol.Price IS NOT NULL
		THEN 
		'<span title="Quote''s Unit Price">' + CAST(CONVERT(DECIMAL(16, 5), qol.Price) AS NVARCHAR(100)) + ' ' + cu.CurrencyCode + '</span>' 
		+ '<span title="Original HUB Offer" class="actualCurrency">[HUB: ' + 
		
		CASE WHEN sr.Price IS NOT NULL
			THEN 
				CAST(CONVERT(DECIMAL(16,5), sr.Price / dbo.ufn_get_exchange_rate(ISNULL(sr.ClientCurrencyNo, sr.CurrencyNo), GETDATE())) AS NVARCHAR(100))
				+' '+ (SELECT TOP 1 cr.CurrencyCode FROM tbClient cl JOIN tbCurrency cr ON cr.CurrencyId = cl.CurrencyNo WHERE ClientId = qt.ClientNo)
			ELSE 'N/A'
		END
				
		+']</span>'+
		+ '&nbsp;&nbsp;(' + CAST(FORMAT(qt.DateQuoted, 'dd-MM-yyyy') AS NVARCHAR(40)) 
		+ ')<a class="documentachor" href="../../Ord_QuoteDetail.aspx?qt=' + CAST(qol.QuoteNo AS NVARCHAR(20)) 
		+ '" target="_blank">(' + CAST(qt.QuoteNumber AS NVARCHAR(25)) + ')</a>'
		ELSE 'N/A'
	END
                        
FROM tbQuoteLine qol WITH (NOLOCK)
LEFT JOIN tbQuote qt WITH (NOLOCK) ON qol.QuoteNo = qt.QuoteId
LEFT JOIN dbo.tbSourcingResult sr WITH (NOLOCK) ON sr.SourcingResultId = qol.SourcingResultNo 
JOIN dbo.tbCurrency cu WITH (NOLOCK) ON qt.CurrencyNo = cu.CurrencyId
WHERE qol.FullPart = @PartNumber                         
	AND (@ClientID = 0 OR qt.ClientNo = @ClientID)                           
ORDER BY qol.DLUP DESC  
---------END-----------

---Latest Quoted Price shared by HUB---
SELECT TOP 1 @LastHubprice=                          
	CASE
		WHEN ISNULL(sr.Price, 0) > 0 
		THEN  
			'<span title="Quote''s Unit Price">' + CAST(CONVERT(DECIMAL(16, 5), sr.Price) AS NVARCHAR(100)) + ' ' + cr.CurrencyCode + '</span>' 
			+ '<span title="Original HUB Offer" class="actualCurrency">[HUB: '+ CAST(CONVERT(DECIMAL(16,5),
			sr.Price / dbo.ufn_get_exchange_rate(ISNULL(sr.ClientCurrencyNo, sr.CurrencyNo), GETDATE())
			) AS NVARCHAR(100))
			+' '+ (SELECT TOP 1 cr.CurrencyCode FROM tbClient cl
					JOIN tbCurrency cr ON cr.CurrencyId = cl.CurrencyNo
					WHERE ClientId = cust.ClientNo) 
			+']</span>'+
			+ '&nbsp;&nbsp;(' + CAST(FORMAT(sr.DLUP, 'dd-MM-yyyy') AS NVARCHAR(40)) + ')<a class="documentachor" href="../../Ord_BOMDetail.aspx?BOM=' 
			+ CAST(bom.BOMId AS NVARCHAR(20)) + '" target="_blank">(' + CAST(bom.BOMName AS NVARCHAR(25)) + ')</a>'
		ELSE '0'
	END
FROM tbSourcingresult sr WITH (NOLOCK)         
	JOIN tbCustomerRequirement cust WITH (NOLOCK) ON sr.CustomerRequirementNo=cust.CustomerRequirementId 
	JOIN tbBOM bom WITH (NOLOCK) ON bom.BOMId = cust.BOMNo 
	JOIN tbCurrency cr WITH (NOLOCK) ON cr.CurrencyId = sr.CurrencyNo                        
WHERE sr.FullPart = @PartNumber AND sr.SourcingTable IN ('EPPH','EXPH','OFPH','RLPH') 
AND (@ClientID = 0 OR bom.ClientNo = @ClientID) 
ORDER BY sr.DLUP  DESC    
---------END-----------

---Price last invoiced to this Customer for the selected part in the last 12 months (Rolling)---                            
SELECT TOP 1  @LastSoldPrice=                          
	CASE
		WHEN ISNULL(inl.Price, 0) > 0 
		THEN 
		'<span>' + CAST(CONVERT(DECIMAL(16, 5), inl.Price) AS NVARCHAR(100)) + ' ' + cr.CurrencyCode + '</span>' + '&nbsp;&nbsp;' 
		+ CAST(inl.QuantityShipped AS NVARCHAR(25)) + ' QTY  ' + '&nbsp;&nbsp;(' + CAST(FORMAT(inl.ShippedDate, 'dd-MM-yyyy') AS NVARCHAR(40)) 
		+ ')<a class="documentachor" href="../../Ord_InvoiceDetail.aspx?inv=' + CAST(inl.InvoiceNo AS NVARCHAR(20)) + '" target="_blank">(' + CAST(inl.InvoiceNumber AS NVARCHAR(25)) + ')</a>'
		ELSE '0'
	END                        
FROM vwInvoiceLine inl                                                       
	JOIN tbCurrency cr WITH (NOLOCK) ON inl.CurrencyNo = cr.CurrencyId                          
WHERE inl.FullPart = @PartNumber 
	AND inl.ShippedDate >= @Last12Months
	AND (@ClientID = 0 OR inl.ClientNo = @ClientID)
	AND inl.CompanyNo = @BOMManagerCustomerNo
ORDER BY inl.ShippedDate DESC 
---------END-----------

---Lowest Invoiced Price in 12 months ---            
SELECT TOP 1 @LastLowestSoldPrice =                          
	CASE
		WHEN ISNULL(inl.Price, 0) > 0 THEN 
		'<span>' + CAST(CONVERT(DECIMAL(16, 5), inl.Price) AS NVARCHAR(100)) + ' ' + cr.CurrencyCode + '</span>' 
		+ '&nbsp;&nbsp;' + CAST(inl.QuantityShipped AS NVARCHAR(25)) + ' QTY  ' + '&nbsp;&nbsp;(' + CAST(FORMAT(inl.ShippedDate, 'dd-MM-yyyy') AS NVARCHAR(40)) 
		+ ')<a class="documentachor" href="../../Ord_InvoiceDetail.aspx?inv=' + CAST(inl.InvoiceNo AS NVARCHAR(20)) + '" target="_blank">(' + CAST(inl.InvoiceNumber AS NVARCHAR(25)) + ')</a>'
		ELSE '0'
	END                       
FROM vwInvoiceLine inl                                                     
	JOIN tbCurrency cr WITH (NOLOCK) ON inl.CurrencyNo = cr.CurrencyId                          
WHERE inl.FullPart = @PartNumber 
	AND (@ClientID = 0 OR inl.ClientNo = @ClientID) 
	AND inl.ShippedDate >= @Last12Months
ORDER BY inl.Price ASC, inl.ShippedDate DESC 
---------END-----------

---Highest Invoiced Price in 12 months ---                            
SELECT TOP 1 @LastHighestSoldPrice =                          
	CASE
		WHEN ISNULL(inl.Price, 0) > 0 THEN 
		'<span>' + CAST(CONVERT(DECIMAL(16, 5), inl.Price) AS NVARCHAR(100)) + ' ' + cr.CurrencyCode + '</span>' + '&nbsp;&nbsp;' 
		+ CAST(inl.QuantityShipped AS NVARCHAR(25)) + ' QTY  ' + '&nbsp;&nbsp;(' + CAST(FORMAT(inl.ShippedDate, 'dd-MM-yyyy') AS NVARCHAR(40)) 
		+ ')<a class="documentachor" href="../../Ord_InvoiceDetail.aspx?inv=' + CAST(inl.InvoiceNo AS NVARCHAR(20)) + '" target="_blank">(' + CAST(inl.InvoiceNumber AS NVARCHAR(25)) + ')</a>'
		ELSE '0'
	END                        
FROM vwInvoiceLine inl                                                        
	JOIN tbCurrency cr WITH (NOLOCK) ON inl.CurrencyNo = cr.CurrencyId                          
WHERE inl.FullPart = @PartNumber                         
	AND (@ClientID = 0 OR inl.ClientNo = @ClientID) 
	AND inl.ShippedDate >= @Last12Months
ORDER BY inl.Price DESC, inl.ShippedDate DESC 
---------END-----------

------------Date selected part last sold to the selected customer (for client side only)------------
DECLARE @LastDatePartSoldToBomCustomer NVARCHAR(40) = NULL;
SELECT TOP 1 @LastDatePartSoldToBomCustomer = CAST(FORMAT(inl.ShippedDate, 'dd-MM-yyyy') AS NVARCHAR(40))
FROM vwInvoiceLine inl
JOIN tbInvoice i WITH (NOLOCK) on i.InvoiceId = inl.InvoiceNo
WHERE inl.FullPart = @PartNumber
	AND inl.ClientNo = @ClientID
	AND i.CompanyNo = @BOMManagerCustomerNo
ORDER BY inl.ShippedDate DESC;
---------END-----------

---Number of PartNumber Invoiced for the in 12 months --- 
SELECT @NumberOfInvoice = SUM(QuantityShipped)
FROM vwInvoiceLine
WHERE FullPart = @PartNumber 
	AND ShippedDate >= @Last12Months
	AND (@ClientID = 0 OR ClientNo = @ClientID);
---------END-----------

---Win loss ratio of PartNumber for selected customer in last 12 months --- 
DECLARE @tempQuoteForCustomer TABLE(
	QuoteId INT,
	SalesOrderLineId INT
)
INSERT INTO @tempQuoteForCustomer
(
	QuoteId,
	SalesOrderLineId
)SELECT ql.QuoteNo,
	sol.SalesOrderLineId
FROM vwQuoteLine ql
LEFT JOIN dbo.tbSalesOrderLine sol WITH (NOLOCK) on  sol.QuoteLineNo = ql.QuoteLineId
WHERE ql.FullPart = @PartNumber
	AND (@ClientID = 0 OR ql.ClientNo = @ClientID)
	AND ql.DateQuoted >= @Last12Months 
	AND ql.CompanyNo = @BOMManagerCustomerNo;

---Number Quote generate to SO of PartNumber for selected customer in last 12 months --- 
;WITH cteQuoteWon AS(
	SELECT QuoteId
	FROM @tempQuoteForCustomer
	WHERE SalesOrderLineId IS NOT NULL
	GROUP BY QuoteId
)SELECT @NumberQuoteToSalesOrder = COUNT(*) FROM cteQuoteWon

---Number Quote of PartNumber for selected customer in last 12 months --- 
;WITH ctesAllQuote AS (
	SELECT QuoteId FROM @tempQuoteForCustomer GROUP BY QuoteId
)SELECT @NumberOfQuote = COUNT(*) FROM ctesAllQuote
---------END-----------

--Get IHS data for part + manufacturer
SELECT TOP 1 @IHSAveragePrice = ISNULL(AveragePrice, dbo.ufn_extract_IHS_AvgPrice(Descriptions)),
			 @IHSPartStatus = PartStatus
FROM tbIHSparts WITH (NOLOCK)
WHERE FullPart = @PartNumber
	AND (ManufacturerNo = @ManufacturerId OR ManufacturerName = @ManufacturerName)
	AND ISNULL(Inactive, 0) = 0
ORDER BY DLUP DESC

SET @IHSResult = 'AV Price: ' + CASE WHEN @IHSAveragePrice IS NULL THEN '0.00'
									ELSE CAST(@IHSAveragePrice AS NVARCHAR(25))
								END
				+ '&nbsp;&nbsp; P/Status: ' + ISNULL(@IHSPartStatus, 'N/A');
---------END-----------

--Get Lytica data for part + manufacturer (only in HUB side)
IF(@ClientID = 0)
BEGIN
	SELECT TOP 1 @LyticaAveragePrice = AveragePrice,
				 @LyticaMarketLeading = MarketLeading,
				 @LyticaTargetPrice = TargetPrice,
				 @LyticaStatus = lifeCycleStatus
	FROM tbLyticaAPI WITH (NOLOCK)
	WHERE dbo.ufn_get_fullpart(OriginalPartSearched) = @PartNumber
		AND Manufacturer = @ManufacturerName
		AND ISNULL(Inactive, 0) = 0 
	ORDER BY DLUP DESC
	
	SET @LyticaResult = 'AV Price: ' + CASE WHEN @LyticaAveragePrice IS NULL THEN '0.00'
											ELSE CAST(@LyticaAveragePrice AS NVARCHAR(25))
										END
						+ '&nbsp;&nbsp; M/Leading: ' + CASE WHEN @LyticaMarketLeading IS NULL THEN '0.00'
															ELSE CAST(@LyticaMarketLeading AS NVARCHAR(25))
													   END
						+ '&nbsp;&nbsp; Target: ' + CASE WHEN @LyticaTargetPrice IS NULL THEN '0.00'
														ELSE CAST(@LyticaTargetPrice AS NVARCHAR(25))
													END
						+ '&nbsp;&nbsp; P/Status: ' + ISNULL(@LyticaStatus, 'N/A')
END
---------END-----------

---------Final Query result-----------
SELECT @NumberOfRequirement AS NumberOfRequirement, 
@LastQuotedPrice AS LastQuotedPrice,
@LastHubprice AS LastHubprice,
@LastSoldPrice AS LastSoldPrice,
@LastHighestSoldPrice AS LastHighestSoldPrice,
@LastLowestSoldPrice AS LastLowestSoldPrice,
@NumberOfInvoice AS NumberOfInvoice,
@LastestHubRFQName AS LastestHubRFQName,
@LastestHubNumberDate AS LastestHubNumberDate,
@LastestHubRFQId AS LastestHubRFQId,
@NumberOfQuote AS NumberOfQuote,
@NumberQuoteToSalesOrder AS NumberQuoteToSalesOrder,
@LastUpdatedDate AS LastUpdatedDate,
@LastDatePartSoldToBomCustomer AS LastDatePartSoldToBomCustomer,
@IHSResult AS IHSResult,
@LyticaResult AS LyticaResult;

END        
        
GO


