﻿/* Marker     changed by      date         Remarks  
   [001]      A<PERSON><PERSON><PERSON>  25-Aug-2021   Add for Supplier Approval.
 */
using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using Rebound.GlobalTrader.DAL;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.BLL
{
    public partial class PowerApp : BizObject
    {
        #region Properties
        public System.Int32? SOId { get; set; }
        public System.Boolean? IsNotifySO { get; set; }
        public System.Int32? LoginId { get; set; }
        public System.String LoginName { get; set; }
        public System.DateTime? LastVisited { get; set; }
        public System.String ReportName { get; set; }

        public System.Int32? SalesOrderNo { get; set; }
        public System.Int32? SalesOrderNumber { get; set; }
        public System.Int32? Salesman { get; set; }
        public System.String SalesmanName { get; set; }
        public System.Int32? SOSerialNo { get; set; }
        public System.Int32? ExportApprovalStatusId { get; set; }
        public System.String ExportApprovalStatus { get; set; }
        public System.Int32? ExportApprovalId { get; set; }
        public System.Boolean? Result { get; set; }
        #endregion

        #region Methods

        /// <summary>
        /// Line Manager Approvals.
        /// Calls [usp_PowerApp_update_SO_Authorise]
        /// </summary>
        public static List<PowerApp> SOCheckedUnchecked(System.Int32? SOId, System.String AuthorisedByEmail, System.Boolean? Authorise,System.String ApproverNote, System.Int32? RequestId, System.String TokenValue)
        {
            List<PowerAppDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.PowerApp.SOCheckedUnchecked(SOId, AuthorisedByEmail, Authorise, ApproverNote, RequestId, TokenValue);
            if (lstDetails == null)
            {
                return new List<PowerApp>();
            }
            else
            {
                List<PowerApp> lst = new List<PowerApp>();
                foreach (PowerAppDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.PowerApp obj = new Rebound.GlobalTrader.BLL.PowerApp();
                    obj.SOId = objDetails.SOId;
                    obj.IsNotifySO = objDetails.IsNotifySO;
                    obj.LoginId = objDetails.LoginId;
                    obj.LoginName = objDetails.LoginName;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }

        /// <summary>
        /// calls [usp_select_PowerBIActivity]
        /// </summary>
        /// <param name="loginId"></param>
        /// <param name="clientId"></param>
        /// <returns></returns>
        public static List<PowerApp> GetPowerBIActivity(System.Int32? loginId, System.Int32? clientId)
        {
            List<PowerAppDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.PowerApp.GetPowerBIActivity(loginId, clientId);
            if (lstDetails == null)
            {
                return new List<PowerApp>();
            }
            else
            {
                List<PowerApp> lst = new List<PowerApp>();
                foreach (PowerAppDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.PowerApp obj = new Rebound.GlobalTrader.BLL.PowerApp();
                    obj.LoginName = objDetails.LoginName;
                    obj.LastVisited = objDetails.LastVisited;
                    obj.ReportName = objDetails.ReportName;
                    
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        public static string LastVisitedBI(int? LoginId,int? clientId)
        {
            string visiteddate = "15/03/2023 17:08";
            visiteddate = Rebound.GlobalTrader.DAL.SiteProvider.PowerApp.LastVisitedBI(LoginId, clientId);
            return visiteddate;
        }

        /// <summary>
        /// Export Approvals.
        /// Calls [usp_PowerAppExportApproveRejectById]
        /// </summary>
        public static List<PowerApp> ApproveRejectExportApproval(System.Int32? ExportApprovalId, System.String AuthorisedByEmail, System.String ApproverNote, System.Int32? RequestId, System.String TokenValue,System.Int32? ApprovalOption, System.Int32? OgelNumber)
        {
            List<PowerAppDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.PowerApp.ApproveRejectExportApproval(ExportApprovalId, AuthorisedByEmail, ApproverNote, RequestId, TokenValue, ApprovalOption, OgelNumber);
            if (lstDetails == null)
            {
                return new List<PowerApp>();
            }
            else
            {
                List<PowerApp> lst = new List<PowerApp>();
                foreach (PowerAppDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.PowerApp obj = new Rebound.GlobalTrader.BLL.PowerApp();
                    obj.SalesOrderNo = objDetails.SalesOrderNo;
                    obj.SalesOrderNumber = objDetails.SalesOrderNumber;
                    obj.Salesman = objDetails.Salesman;
                    obj.SalesmanName = objDetails.SalesmanName;
                    obj.SOSerialNo = objDetails.SOSerialNo;
                    obj.ExportApprovalStatusId = objDetails.ExportApprovalStatusId;
                    obj.ExportApprovalStatus = objDetails.ExportApprovalStatus;
                    obj.ExportApprovalId = objDetails.ExportApprovalId;
                    obj.Result = objDetails.Result;
                    obj.IsNotifySO = objDetails.IsNotifySO;
                    obj.LoginId = objDetails.LoginId;
                    obj.LoginName = objDetails.LoginName;


                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }

        /// <summary>
        /// Notify changes of RL Stock.
        /// Calls [usp_PowerApp_notify_RL_Stock]
        /// </summary>
        public static string GetFlowUrlByFlowName(System.String flowName)
        {
            PowerAppDetails details = Rebound.GlobalTrader.DAL.SiteProvider.PowerApp.GetFlowUrlByFlowName(flowName);
            if (details == null)
            {
                return "";
            }
            else
            {
                return details.FlowUrl;
            }
        }

        #endregion
    }
}
