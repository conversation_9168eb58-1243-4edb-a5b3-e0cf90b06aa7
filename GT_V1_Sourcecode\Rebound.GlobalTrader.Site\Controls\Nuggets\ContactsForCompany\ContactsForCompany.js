Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");Rebound.GlobalTrader.Site.Controls.Nuggets.ContactsForCompany=function(n){Rebound.GlobalTrader.Site.Controls.Nuggets.ContactsForCompany.initializeBase(this,[n]);this._intCompanyID=-1;this._intContactID=-1;this._intNumberOfContacts=0};Rebound.GlobalTrader.Site.Controls.Nuggets.ContactsForCompany.prototype={get_intCompanyID:function(){return this._intCompanyID},set_intCompanyID:function(n){this._intCompanyID!==n&&(this._intCompanyID=n)},get_intContactID:function(){return this._intContactID},set_intContactID:function(n){this._intContactID!==n&&(this._intContactID=n)},get_ibtnDelete:function(){return this._ibtnDelete},set_ibtnDelete:function(n){this._ibtnDelete!==n&&(this._ibtnDelete=n)},get_ibtnAdd:function(){return this._ibtnAdd},set_ibtnAdd:function(n){this._ibtnAdd!==n&&(this._ibtnAdd=n)},get_ibtnMakeDefaultSO:function(){return this._ibtnMakeDefaultSO},set_ibtnMakeDefaultSO:function(n){this._ibtnMakeDefaultSO!==n&&(this._ibtnMakeDefaultSO=n)},get_ibtnMakeDefaultPO:function(){return this._ibtnMakeDefaultPO},set_ibtnMakeDefaultPO:function(n){this._ibtnMakeDefaultPO!==n&&(this._ibtnMakeDefaultPO=n)},get_tbl:function(){return this._tbl},set_tbl:function(n){this._tbl!==n&&(this._tbl=n)},get_strCompanyName:function(){return this._strCompanyName},set_strCompanyName:function(n){this._strCompanyName!==n&&(this._strCompanyName=n)},get_strContactName:function(){return this._strContactName},set_strContactName:function(n){this._strContactName!==n&&(this._strContactName=n)},get_ibtnDefaultPOLedger:function(){return this._ibtnDefaultPOLedger},set_ibtnDefaultPOLedger:function(n){this._ibtnDefaultPOLedger!==n&&(this._ibtnDefaultPOLedger=n)},get_ibtnDefaultSOLedger:function(){return this._ibtnDefaultSOLedger},set_ibtnDefaultSOLedger:function(n){this._ibtnDefaultSOLedger!==n&&(this._ibtnDefaultSOLedger=n)},addSelectContact:function(n){this.get_events().addHandler("SelectContact",n)},removeSelectContact:function(n){this.get_events().removeHandler("SelectContact",n)},onSelectContact:function(){var n=this.get_events().getHandler("SelectContact");n&&n(this,Sys.EventArgs.Empty)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Nuggets.ContactsForCompany.callBaseMethod(this,"initialize");this.addRefreshEvent(Function.createDelegate(this,this.getData));this.addRolledOut(Function.createDelegate(this,this.resizeTableColumns));this._strPathToData="controls/Nuggets/ContactsForCompany";this._strDataObject="ContactsForCompany";this._tbl.addSelectedIndexChanged(Function.createDelegate(this,this.tbl_SelectedIndexChanged));this._ibtnAdd&&($R_IBTN.addClick(this._ibtnAdd,Function.createDelegate(this,this.showAddForm)),this._frmAdd=$find(this._aryFormIDs[0]),this._frmAdd.addCancel(Function.createDelegate(this,this.cancelAdd)),this._frmAdd.addSaveComplete(Function.createDelegate(this,this.saveAddComplete)),this._frmAdd.addSaveError(Function.createDelegate(this,this.saveAddError)));this._ibtnDelete&&($R_IBTN.addClick(this._ibtnDelete,Function.createDelegate(this,this.showDeleteForm)),this._frmDelete=$find(this._aryFormIDs[1]),this._frmDelete.addNotConfirmed(Function.createDelegate(this,this.hideDeleteForm)),this._frmDelete.addSaveComplete(Function.createDelegate(this,this.saveDeleteOK)),this._frmDelete.addSaveError(Function.createDelegate(this,this.saveDeleteError)));(this._ibtnMakeDefaultSO||this._ibtnMakeDefaultPO||this._ibtnDefaultPOLedger||this._ibtnDefaultSOLedger)&&(this._ibtnMakeDefaultSO&&$R_IBTN.addClick(this._ibtnMakeDefaultSO,Function.createDelegate(this,this.showMakeDefaultSOForm)),this._ibtnMakeDefaultPO&&$R_IBTN.addClick(this._ibtnMakeDefaultPO,Function.createDelegate(this,this.showMakeDefaultPOForm)),this._ibtnDefaultPOLedger&&$R_IBTN.addClick(this._ibtnDefaultPOLedger,Function.createDelegate(this,this.showMakeDefaultPOLedgerForm)),this._ibtnDefaultSOLedger&&$R_IBTN.addClick(this._ibtnDefaultSOLedger,Function.createDelegate(this,this.showMakeDefaultSOLedgerForm)),this._frmMakeDefault=$find(this._aryFormIDs[2]),this._frmMakeDefault.addNotConfirmed(Function.createDelegate(this,this.hideMakeDefaultForm)),this._frmMakeDefault.addSaveComplete(Function.createDelegate(this,this.saveMakeDefaultOK)),this._frmMakeDefault.addSaveError(Function.createDelegate(this,this.saveMakeDefaultError)));this._blnIsNoDataFound||this.getData()},dispose:function(){this.isDisposed||(this._ibtnMakeDefaultSO&&$R_IBTN.clearHandlers(this._ibtnMakeDefaultSO),this._ibtnMakeDefaultPO&&$R_IBTN.clearHandlers(this._ibtnMakeDefaultPO),this._ibtnDefaultPOLedger&&$R_IBTN.clearHandlers(this._ibtnDefaultPOLedger),this._ibtnDefaultSOLedger&&$R_IBTN.clearHandlers(this._ibtnDefaultSOLedger),this._ibtnAdd&&$R_IBTN.clearHandlers(this._ibtnAdd),this._ibtnDelete&&$R_IBTN.clearHandlers(this._ibtnDelete),this._tbl&&this._tbl.dispose(),this._frmAdd&&this._frmAdd.dispose(),this._frmDelete&&this._frmDelete.dispose(),this._frmMakeDefault&&this._frmMakeDefault.dispose(),this._intCompanyID=null,this._intContactID=null,this._intNumberOfContacts=null,this._ibtnDelete=null,this._ibtnAdd=null,this._ibtnMakeDefaultSO=null,this._ibtnMakeDefaultPO=null,this._tbl=null,this._strCompanyName=null,this._strContactName=null,this._frmAdd=null,this._frmDelete=null,this._frmMakeDefault=null,this._ibtnDefaultPOLedger=null,this._ibtnDefaultSOLedger=null,Rebound.GlobalTrader.Site.Controls.Nuggets.ContactsForCompany.callBaseMethod(this,"dispose"))},getData:function(){this.enableButtons(!1);this.getData_Start();var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("GetContactList");n.addParameter("id",this._intCompanyID);n.addDataOK(Function.createDelegate(this,this.getDataOK));n.addError(Function.createDelegate(this,this.getDataError));n.addTimeout(Function.createDelegate(this,this.getDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getDataOK:function(n){var r=n._result,u,i,t,f,e;if(this._tbl.clearTable(),r)for(u="",this._intNumberOfContacts=0,i=0;i<r.Contacts.length;i++)t=r.Contacts[i],f=[$R_FN.setCleanTextValue(t.Name),$R_FN.setCleanTextValue(t.Tel),t.Email?$R_FN.createNubButton("mailto:"+$R_FN.setCleanTextValue(t.Email),$R_FN.setCleanTextValue(t.Email)):"",t.DefaultSO?$R_RES.Yes:"-",t.DefaultPO?$R_RES.Yes:"-",t.FinanceContact?$R_RES.Yes:"-",t.DefaultPOLedger?$R_RES.Yes:"-",t.DefaultSOLedger?$R_RES.Yes:"-"],u=t.Inactive==!0?"RowColorInactive":"",e={Inactive:t.Inactive,Name:$R_FN.setCleanTextValue(t.Name),DefaultSO:t.DefaultSO,DefaultPO:t.DefaultPO,DefaultPOLedger:t.DefaultPOLedger,DefaultSOLedger:t.DefaultSOLedger},this._tbl.addRowRowColor(f,t.ID,t.ID==this._intContactID,e,t.DefaultSO||t.DefaultPO||t.DefaultPOLedger||t.DefaultSOLedger?"defaultContact":"",null,t.Inactive,u),t=null,f=null,this._intNumberOfContacts+=1;this.resizeTableColumns();this.getDataOK_End();this.enableButtons(!0)},getDataError:function(n){this.showError(!0,n.get_ErrorMessage())},tbl_SelectedIndexChanged:function(){this._intContactID=this._tbl._varSelectedValue;this._strContactName=this._tbl.getSelectedExtraData().Name;this.enableButtons(!0);this.onSelectContact()},resizeTableColumns:function(){this._tbl.resizeColumns()},enableButtons:function(n){n?(this._ibtnMakeDefaultPO&&$R_IBTN.enableButton(this._ibtnMakeDefaultPO,this._intContactID>0&&!this._tbl.getSelectedExtraData().DefaultPO),this._ibtnMakeDefaultSO&&$R_IBTN.enableButton(this._ibtnMakeDefaultSO,this._intContactID>0&&!this._tbl.getSelectedExtraData().DefaultSO),this._ibtnDefaultPOLedger&&$R_IBTN.enableButton(this._ibtnDefaultPOLedger,this._intContactID>0&&!this._tbl.getSelectedExtraData().DefaultPOLedger),this._ibtnDefaultSOLedger&&$R_IBTN.enableButton(this._ibtnDefaultSOLedger,this._intContactID>0&&!this._tbl.getSelectedExtraData().DefaultSOLedger),this._ibtnDelete&&$R_IBTN.enableButton(this._ibtnDelete,this._intNumberOfContacts>1&&!this._tbl.getSelectedExtraData().Inactive)):(this._ibtnDelete&&$R_IBTN.enableButton(this._ibtnDelete,!1),this._ibtnMakeDefaultPO&&$R_IBTN.enableButton(this._ibtnMakeDefaultPO,!1),this._ibtnMakeDefaultSO&&$R_IBTN.enableButton(this._ibtnMakeDefaultSO,!1),this._ibtnDefaultPOLedger&&$R_IBTN.enableButton(this._ibtnDefaultPOLedger,!1),this._ibtnDefaultSOLedger&&$R_IBTN.enableButton(this._ibtnDefaultSOLedger,!1))},showDeleteForm:function(){this._frmDelete._intContactID=this._intContactID;this._frmDelete.setFieldValue("ctlContactName",this._tbl.getSelectedCellValue(0));this.showForm(this._frmDelete,!0)},hideDeleteForm:function(){this.showForm(this._frmDelete,!1);this._tbl.resizeColumns()},saveDeleteOK:function(){this.hideDeleteForm();this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully);this._intContactID=-1;this.onSelectContact();this.getData()},saveDeleteError:function(){this.showError(!0,this._frmDelete._strErrorMessage)},showAddForm:function(){this._frmAdd._intCompanyID=this._intCompanyID;this.showForm(this._frmAdd,!0)},hideAddForm:function(){this.showForm(this._frmAdd,!1);this._tbl.resizeColumns()},cancelAdd:function(){this.hideAddForm()},saveAddComplete:function(){this.hideAddForm();this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully);this._intContactID=this._frmAdd._intNewID;this.getData()},saveAddError:function(){this.showError(!0,this._frmAdd._strErrorMessage)},showMakeDefaultSOForm:function(){this._frmMakeDefault.changeMode("SO");this.showMakeDefaultForm()},showMakeDefaultPOForm:function(){this._frmMakeDefault.changeMode("PO");this.showMakeDefaultForm()},showMakeDefaultPOLedgerForm:function(){this._frmMakeDefault.changeMode("POLedger");this.showMakeDefaultForm()},showMakeDefaultSOLedgerForm:function(){this._frmMakeDefault.changeMode("SOLedger");this.showMakeDefaultForm()},showMakeDefaultForm:function(){this._frmMakeDefault._intContactID=this._intContactID;this._frmMakeDefault._intCompanyID=this._intCompanyID;this._frmMakeDefault.setFieldValue("ctlContactName",this._tbl.getSelectedCellValue(0));this.showForm(this._frmMakeDefault,!0)},hideMakeDefaultForm:function(){this.showForm(this._frmMakeDefault,!1);this._tbl.resizeColumns()},saveMakeDefaultOK:function(){this.hideMakeDefaultForm();this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully);this.onSelectContact();this.getData()},saveMakeDefaultError:function(){this.showError(!0,this._frmMakeDefault._strErrorMessage)}};Rebound.GlobalTrader.Site.Controls.Nuggets.ContactsForCompany.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.ContactsForCompany",Rebound.GlobalTrader.Site.Controls.Nuggets.Base);