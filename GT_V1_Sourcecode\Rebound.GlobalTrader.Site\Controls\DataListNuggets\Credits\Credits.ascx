<%@ Control Language="C#" CodeBehind="Credits.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.DataListNuggets.Credits" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" BoxType="List">
	<Filters>
		<ReboundUI_DataListNugget:Filter ID="ctlFilter" runat="server">
			<FieldsLeft>
				<ReboundUI_FilterDataItemRow:Numerical id="ctlCreditNo" runat="server" ResourceTitle="CreditNo"  FilterField="CreditNo" />
				<ReboundUI_FilterDataItemRow:TextBox id="ctlPart" runat="server" ResourceTitle="PartNo" FilterField="Part" />
				<ReboundUI_FilterDataItemRow:TextBox id="ctlCompanyName" runat="server" ResourceTitle="CompanyName" FilterField="CMName" />
				<ReboundUI_FilterDataItemRow:CheckBox id="ctlPohubOnly" runat="server" ResourceTitle="PohubOnly" FilterField="PohubOnly" DefaultValue="true"  />
				<ReboundUI_FilterDataItemRow:Numerical id="ctlInvNo" runat="server" ResourceTitle="InvoiceNo" FilterField="InvNo" />
				<ReboundUI_FilterDataItemRow:Numerical id="ctlCRMANo" runat="server" ResourceTitle="CRMANo" FilterField="CRMANo" />
				<ReboundUI_FilterDataItemRow:TextBox id="ctlCustomerPO" runat="server" ResourceTitle="CustomerPurchaseOrderNo" FilterField="CustPO" />
			</FieldsLeft>
			<FieldsRight>
				<ReboundUI_FilterDataItemRow:TextBox id="ctlContactName" runat="server" ResourceTitle="ContactName" FilterField="Contact" />
				<ReboundUI_FilterDataItemRow:DropDown id="ctlSalesman" runat="server" ResourceTitle="Salesman" DropDownType="Employee" DropDownAssembly="Rebound.GlobalTrader.Site" FilterField="Salesman" />
				<ReboundUI_FilterDataItemRow:TextBox id="ctlCreditNotes" runat="server" ResourceTitle="Notes" InitialSearchType="Contains" FilterField="CreditNotes" />
				<ReboundUI_FilterDataItemRow:DateSelect id="ctlCreditDateFrom" runat="server" ResourceTitle="CreditDateFrom" FilterField="CreditDateFrom" />
				<ReboundUI_FilterDataItemRow:DateSelect id="ctlCreditDateTo" runat="server" ResourceTitle="CreditDateTo" FilterField="CreditDateTo" />
			<ReboundUI_FilterDataItemRow:Numerical id="ctlClientInvNo" runat="server" ResourceTitle="ClientInvoiceNo" FilterField="ClientInvNo" />
            <ReboundUI_FilterDataItemRow:DropDown ID="ctlClientName" runat="server" ResourceTitle="ClientName" DropDownType="Client" DropDownAssembly="Rebound.GlobalTrader.Site" FilterField="Client" />
			</FieldsRight>
		</ReboundUI_DataListNugget:Filter>
	</Filters>
</ReboundUI_Nugget:DesignBase>
