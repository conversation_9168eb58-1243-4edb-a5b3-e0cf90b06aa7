﻿GO
/*   
===========================================================================================  
TASK			UPDATED BY			DATE			ACTION		DESCRIPTION  
[US-209115]		Trung Pham			23-Oct-2024		CREATE      Insert new power automate link
[US-209115]		Trung Pham			23-Oct-2024		UPDATE      Update new link
===========================================================================================  
*/
IF EXISTS (SELECT 1 FROM tbPowerApp_urls WHERE FlowName = 'Reverse_Logistic_Notification')
BEGIN
	DELETE FROM tbPowerApp_urls WHERE FlowName = 'Reverse_Logistic_Notification'
END

GO
INSERT INTO [dbo].[tbPowerApp_urls] ([FlowName],[FlowUrl])
VALUES('Reverse_Logistic_Notification'
       ,'https://gt-uat2-webapp-001.azurewebsites.net')