<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="CommunicationLogType_Edit" xml:space="preserve">
    <value>Edit CommunicationLog Type details</value>
  </data>
  <data name="CompanyAddresses_Add" xml:space="preserve">
    <value>Add New Address</value>
  </data>
  <data name="CompanyAddresses_Cease" xml:space="preserve">
    <value>Cease Address</value>
  </data>
  <data name="CompanyAddresses_DefaultBill" xml:space="preserve">
    <value>Make Default Billing Address</value>
  </data>
  <data name="CompanyAddresses_Edit" xml:space="preserve">
    <value>Edit Address</value>
  </data>
  <data name="CommunicationLog_Add" xml:space="preserve">
    <value>Add Contact Log Item</value>
  </data>
  <data name="CompanyContactLog_Delete" xml:space="preserve">
    <value>Delete Contact Log Item</value>
  </data>
  <data name="CommunicationLog_Edit" xml:space="preserve">
    <value>Edit Contact Log Item</value>
  </data>
  <data name="CompanyMainInfo_Edit" xml:space="preserve">
    <value>Edit Main Company Information</value>
  </data>
  <data name="CompanyManufacturers_Add" xml:space="preserve">
    <value>Add Manufacturer Supplied</value>
  </data>
  <data name="CompanyManufacturers_Delete" xml:space="preserve">
    <value>Delete Manufacturer Supplied</value>
  </data>
  <data name="CompanyManufacturers_Edit" xml:space="preserve">
    <value>Edit Manufacturer Supplied</value>
  </data>
  <data name="CompanyPurchasingInfo_Edit" xml:space="preserve">
    <value>Edit Purchasing Information</value>
  </data>
  <data name="CompanySalesInfo_Edit" xml:space="preserve">
    <value>Edit Sales Information</value>
  </data>
  <data name="CompanyType_Add" xml:space="preserve">
    <value>Add New Company Type</value>
  </data>
  <data name="CompanyType_Edit" xml:space="preserve">
    <value>Edit Company Type details</value>
  </data>
  <data name="ContactExtendedInfo_Edit" xml:space="preserve">
    <value>Edit Extended Contact Information</value>
  </data>
  <data name="ContactMainInfo_Edit" xml:space="preserve">
    <value>Edit Main Contact Information</value>
  </data>
  <data name="ContactsForCompany_Add" xml:space="preserve">
    <value>Add New Contact</value>
  </data>
  <data name="ContactsForCompany_Delete" xml:space="preserve">
    <value>Delete Contact</value>
  </data>
  <data name="Country_Add" xml:space="preserve">
    <value>Add New Country</value>
  </data>
  <data name="Country_Edit" xml:space="preserve">
    <value>Edit Country details</value>
  </data>
  <data name="Currency_Add" xml:space="preserve">
    <value>Add New Currency</value>
  </data>
  <data name="Currency_Edit" xml:space="preserve">
    <value>Edit Currency Details</value>
  </data>
  <data name="CustomerRequirementAdd_Add" xml:space="preserve">
    <value>Add New Requirement</value>
  </data>
  <data name="Division_Add" xml:space="preserve">
    <value>Add New Division</value>
  </data>
  <data name="Division_Edit" xml:space="preserve">
    <value>Edit Division Details</value>
  </data>
  <data name="ManufacturerMainInfo_Edit" xml:space="preserve">
    <value>Edit Main Manufacturer Information</value>
  </data>
  <data name="GlobalCountryList_Edit" xml:space="preserve">
    <value>Edit Master Country</value>
  </data>
  <data name="GlobalCurrencyList_Edit" xml:space="preserve">
    <value>Edit Master Currency</value>
  </data>
  <data name="IndustryType_Add" xml:space="preserve">
    <value>Add New Industry Type</value>
  </data>
  <data name="IndustryType_Edit" xml:space="preserve">
    <value>Edit Industry Type Details</value>
  </data>
  <data name="Package_Add" xml:space="preserve">
    <value>Add New Package</value>
  </data>
  <data name="Package_Edit" xml:space="preserve">
    <value>Edit Package details</value>
  </data>
  <data name="POAdd_Add" xml:space="preserve">
    <value>Add New Purchase Order</value>
  </data>
  <data name="Product_Add" xml:space="preserve">
    <value>Add New Product</value>
  </data>
  <data name="Product_Edit" xml:space="preserve">
    <value>Edit Product details</value>
  </data>
  <data name="QuoteAdd_Add" xml:space="preserve">
    <value>Add New Quote</value>
  </data>
  <data name="ShipVia_Add" xml:space="preserve">
    <value>Add New Shipping Method</value>
  </data>
  <data name="ShipVia_Edit" xml:space="preserve">
    <value>Edit Shipping Method details</value>
  </data>
  <data name="SOAdd_Add" xml:space="preserve">
    <value>Add New Sales Order</value>
  </data>
  <data name="SOLines_Add" xml:space="preserve">
    <value>Add Sales Order Line</value>
  </data>
  <data name="SOLines_Allocate" xml:space="preserve">
    <value>Allocate Sales Order Line</value>
  </data>
  <data name="SOLines_Deallocate" xml:space="preserve">
    <value>Deallocate Sales Order Line</value>
  </data>
  <data name="SOLines_Delete" xml:space="preserve">
    <value>Delete Sales Order Line</value>
  </data>
  <data name="SOLines_Post" xml:space="preserve">
    <value>Post Line</value>
  </data>
  <data name="SOLines_Unpost" xml:space="preserve">
    <value>Unpost Line</value>
  </data>
  <data name="SOMainInfo_Edit" xml:space="preserve">
    <value>Edit Main Sales Order Information</value>
  </data>
  <data name="Surcing_AddToReqo" xml:space="preserve">
    <value>Add to Requirement</value>
  </data>
  <data name="Sourcing_EditOffer" xml:space="preserve">
    <value>Edit Offer</value>
  </data>
  <data name="Sourcing_RFQ" xml:space="preserve">
    <value>Request for Quote</value>
  </data>
  <data name="StockLogReason_Add" xml:space="preserve">
    <value>Add New Stock Log Reason</value>
  </data>
  <data name="StockLogReason_Edit" xml:space="preserve">
    <value>Edit Stock Log Reason Details</value>
  </data>
  <data name="Tax_Add" xml:space="preserve">
    <value>Add New Tax</value>
  </data>
  <data name="Tax_Edit" xml:space="preserve">
    <value>Edit Tax Details</value>
  </data>
  <data name="Team_Add" xml:space="preserve">
    <value>Add New Team</value>
  </data>
  <data name="Team_Edit" xml:space="preserve">
    <value>Edit Team Details</value>
  </data>
  <data name="Terms_Add" xml:space="preserve">
    <value>Add New Terms</value>
  </data>
  <data name="Terms_Edit" xml:space="preserve">
    <value>Edit Terms Details</value>
  </data>
  <data name="User_Add" xml:space="preserve">
    <value>Add New User</value>
  </data>
  <data name="User_Edit" xml:space="preserve">
    <value>Edit User details</value>
  </data>
  <data name="Warehouse_Add" xml:space="preserve">
    <value>Add New Warehouse</value>
  </data>
  <data name="Warehouse_Edit" xml:space="preserve">
    <value>Edit Warehouse Details</value>
  </data>
  <data name="ManufacturerSuppliers_Add" xml:space="preserve">
    <value>Add Supplier who Distributes</value>
  </data>
  <data name="ManufacturerSuppliers_Delete" xml:space="preserve">
    <value>Delete Supplier who Distributes</value>
  </data>
  <data name="ManufacturerSuppliers_Edit" xml:space="preserve">
    <value>Edit Supplier who Distributes</value>
  </data>
  <data name="Reason_Add" xml:space="preserve">
    <value>Add New Reason</value>
  </data>
  <data name="Reason_Edit" xml:space="preserve">
    <value>Edit Reason details</value>
  </data>
  <data name="MailMessages_DeleteFolder" xml:space="preserve">
    <value>Delete Folder</value>
  </data>
  <data name="MailMessages_DeleteMessage" xml:space="preserve">
    <value>Delete Message</value>
  </data>
  <data name="MailMessages_EditFolder" xml:space="preserve">
    <value>Edit Folder</value>
  </data>
  <data name="MailMessages_MoveMessage" xml:space="preserve">
    <value>Move Message</value>
  </data>
  <data name="MailMessages_NewFolder" xml:space="preserve">
    <value>New Folder</value>
  </data>
  <data name="MailMessages_NewMessage" xml:space="preserve">
    <value>New Message</value>
  </data>
  <data name="ManufacturerCompanies_Add" xml:space="preserve">
    <value>Add Related Company</value>
  </data>
  <data name="ManufacturerCompanies_Delete" xml:space="preserve">
    <value>Remove Related Company</value>
  </data>
  <data name="QuoteMainInfo_Edit" xml:space="preserve">
    <value>Edit Main Quote Information</value>
  </data>
  <data name="CompanyAdd_Add" xml:space="preserve">
    <value>Add New Company</value>
  </data>
  <data name="GroupCodeCompanyAdd_Add" xml:space="preserve">
    <value>Customer Group Code</value>
  </data>
  <data name="ManufacturerAdd_Add" xml:space="preserve">
    <value>Add New Manufacturer</value>
  </data>
  <data name="QuoteLines_Add" xml:space="preserve">
    <value>Add New Quote Line</value>
  </data>
  <data name="QuoteLines_Close" xml:space="preserve">
    <value>Close Quote Line</value>
  </data>
  <data name="MailMessages_MarkAsToDo" xml:space="preserve">
    <value>Create To Do Item</value>
  </data>
  <data name="ToDo_Delete" xml:space="preserve">
    <value>Delete To Do Item(s)</value>
  </data>
  <data name="ToDo_Edit" xml:space="preserve">
    <value>Edit To Do Item</value>
  </data>
  <data name="ToDo_MarkComplete" xml:space="preserve">
    <value>Mark Item(s) Complete</value>
  </data>
  <data name="ToDo_MarkIncomplete" xml:space="preserve">
    <value>Mark Item(s) Incomplete</value>
  </data>
  <data name="CommunicationLogType_Add" xml:space="preserve">
    <value>Add New CommunicationLog Type</value>
  </data>
  <data name="UserPreferences_Edit" xml:space="preserve">
    <value>Edit Preferences</value>
  </data>
  <data name="UserProfile_Edit" xml:space="preserve">
    <value>Edit Profile</value>
  </data>
  <data name="UserProfile_ChangePassword" xml:space="preserve">
    <value>Change Password</value>
  </data>
  <data name="CusReqSourcingResults_Add" xml:space="preserve">
    <value>Add New Sourcing Result</value>
  </data>
  <data name="CusReqSourcingResults_Edit" xml:space="preserve">
    <value>Edit Sourcing Result</value>
  </data>
  <data name="CustomerRequirementMainInfo_Edit" xml:space="preserve">
    <value>Edit Main Customer Requirement Information</value>
  </data>
  <data name="CustomerRequirementMainInfo_AddAlternate" xml:space="preserve">
    <value>Add Alternate Part</value>
  </data>
  <data name="QuoteLines_Edit" xml:space="preserve">
    <value>Edit Line</value>
  </data>
  <data name="CRMALines_Add" xml:space="preserve">
    <value>Add Customer RMA Line</value>
  </data>
  <data name="CRMALines_Delete" xml:space="preserve">
    <value>Delete Customer RMA Line</value>
  </data>
  <data name="CRMALines_Edit" xml:space="preserve">
    <value>Edit Customer RMA Line</value>
  </data>
  <data name="SRMALines_Add" xml:space="preserve">
    <value>Add Supplier RMA Line</value>
  </data>
  <data name="SRMALines_Delete" xml:space="preserve">
    <value>Delete Supplier RMA Line</value>
  </data>
  <data name="SRMALines_Edit" xml:space="preserve">
    <value>Edit Supplier RMA Line</value>
  </data>
  <data name="SRMAMainInfo_Edit" xml:space="preserve">
    <value>Edit Main Supplier RMA Information</value>
  </data>
  <data name="SRMAAdd_Add" xml:space="preserve">
    <value>Add New Supplier RMA</value>
  </data>
  <data name="CRMAAdd_Add" xml:space="preserve">
    <value>Add New Customer RMA</value>
  </data>
  <data name="Feedback_Add" xml:space="preserve">
    <value>Send Feedback</value>
  </data>
  <data name="SecurityGroupMembers_EditMembers" xml:space="preserve">
    <value>Edit Security Group Members</value>
  </data>
  <data name="SecurityGroupPermissionsGeneral_Edit" xml:space="preserve">
    <value>Edit Permissions</value>
  </data>
  <data name="SecurityGroupPermissionsReports_Edit" xml:space="preserve">
    <value>Edit Report Permissions</value>
  </data>
  <data name="Sourcing_EditAltPart" xml:space="preserve">
    <value>Edit Alternative Part</value>
  </data>
  <data name="SecurityGroups_Add" xml:space="preserve">
    <value>Add New Security Group</value>
  </data>
  <data name="SecurityGroups_Delete" xml:space="preserve">
    <value>Delete Security Group</value>
  </data>
  <data name="SecurityGroups_Edit" xml:space="preserve">
    <value>Edit Security Group</value>
  </data>
  <data name="CRMAReceivingLines_Receive" xml:space="preserve">
    <value>Receive Customer RMA Line</value>
  </data>
  <data name="POReceivingLines_Receive" xml:space="preserve">
    <value>Receive Purchase Order Line</value>
  </data>
  <data name="InvoiceLines_Add" xml:space="preserve">
    <value>Add New Line</value>
  </data>
  <data name="InvoiceLines_Edit" xml:space="preserve">
    <value>Edit Invoice Line</value>
  </data>
  <data name="InvoiceLines_EditAllocation" xml:space="preserve">
    <value>Edit Invoice Line Allocation</value>
  </data>
  <data name="InvoiceMainInfo_Edit" xml:space="preserve">
    <value>Edit Main Invoice Information</value>
  </data>
  <data name="SecurityUserGroups_EditMembers" xml:space="preserve">
    <value>Edit Group Members</value>
  </data>
  <data name="SecurityUsers_Add" xml:space="preserve">
    <value>Add New User</value>
  </data>
  <data name="SecurityUsers_Disable" xml:space="preserve">
    <value>Disable User</value>
  </data>
  <data name="SecurityUsers_Edit" xml:space="preserve">
    <value>Edit User</value>
  </data>
  <data name="SecurityUsers_Enable" xml:space="preserve">
    <value>Enable User</value>
  </data>
  <data name="SourcingLinks_Add" xml:space="preserve">
    <value>Add New Sourcing Link</value>
  </data>
  <data name="SourcingLinks_Delete" xml:space="preserve">
    <value>Delete Sourcing Link</value>
  </data>
  <data name="SourcingLinks_Edit" xml:space="preserve">
    <value>Edit Sourcing Link</value>
  </data>
  <data name="UserProfile_ResetPassword" xml:space="preserve">
    <value>Reset Password</value>
  </data>
  <data name="GlobalCurrencyList_Add" xml:space="preserve">
    <value>Add New Master Currency</value>
  </data>
  <data name="POMainInfo_Edit" xml:space="preserve">
    <value>Edit Main Purchase Order Information</value>
  </data>
  <data name="POLines_Add" xml:space="preserve">
    <value>Add Purchase Order Line</value>
  </data>
  <data name="POLines_Allocate" xml:space="preserve">
    <value>Allocate Purchase Order Line</value>
  </data>
  <data name="POLines_Deallocate" xml:space="preserve">
    <value>Deallocate Purchase Order Line</value>
  </data>
  <data name="POLines_Delete" xml:space="preserve">
    <value>Delete Purchase Order Line</value>
  </data>
  <data name="POLines_Post" xml:space="preserve">
    <value>Post Line</value>
  </data>
  <data name="POLines_Unpost" xml:space="preserve">
    <value>Unpost Line</value>
  </data>
  <data name="POLines_Edit" xml:space="preserve">
    <value>Edit Purchase Order Line</value>
  </data>
  <data name="SOLines_Edit" xml:space="preserve">
    <value>Edit Sales Order Line</value>
  </data>
  <data name="GIAdd_Add" xml:space="preserve">
    <value>Add new Goods In Note</value>
  </data>
  <data name="InvoiceAdd_Add" xml:space="preserve">
    <value>Add New Invoice</value>
  </data>
  <data name="ContactsForCompany_MakeDefaultPO" xml:space="preserve">
    <value>Make Default for POs</value>
  </data>
  <data name="ContactsForCompany_MakeDefaultSO" xml:space="preserve">
    <value>Make Default for SOs</value>
  </data>
  <data name="SOShippingLines_Ship" xml:space="preserve">
    <value>Ship Sales Order Line</value>
  </data>
  <data name="Currency_EditRates" xml:space="preserve">
    <value>Edit Current Currency Rates</value>
  </data>
  <data name="StockAllocations_Deallocate" xml:space="preserve">
    <value>Deallocate Stock</value>
  </data>
  <data name="StockMainInfo_Edit" xml:space="preserve">
    <value>Edit Main Information</value>
  </data>
  <data name="StockMainInfo_Quarantine" xml:space="preserve">
    <value>Quarantine Stock</value>
  </data>
  <data name="StockMainInfo_Split" xml:space="preserve">
    <value>Split Stock</value>
  </data>
  <data name="StockMainInfo_MakeAvailable" xml:space="preserve">
    <value>Make Stock Available</value>
  </data>
  <data name="CRMAMainInfo_Edit" xml:space="preserve">
    <value>Edit Main Information</value>
  </data>
  <data name="Tax_EditRates" xml:space="preserve">
    <value>Edit Tax Rates</value>
  </data>
  <data name="LotMainInfo_Edit" xml:space="preserve">
    <value>Edit Main Information</value>
  </data>
  <data name="CreditLines_Add" xml:space="preserve">
    <value>Add Credit Line</value>
  </data>
  <data name="CreditLines_Delete" xml:space="preserve">
    <value>Delete Credit Line</value>
  </data>
  <data name="CreditLines_Edit" xml:space="preserve">
    <value>Edit Credit Line</value>
  </data>
  <data name="CreditMainInfo_Edit" xml:space="preserve">
    <value>Edit Main Information</value>
  </data>
  <data name="LotAdd_Add" xml:space="preserve">
    <value>Add New Lot</value>
  </data>
  <data name="CreditAdd_Add" xml:space="preserve">
    <value>Add New Credit Note</value>
  </data>
  <data name="ServiceAdd_Add" xml:space="preserve">
    <value>Add New Service</value>
  </data>
  <data name="StockAdd_Add" xml:space="preserve">
    <value>Add New Stock Item</value>
  </data>
  <data name="DebitAdd_Add" xml:space="preserve">
    <value>Add New Debit Note</value>
  </data>
  <data name="DebitLines_Add" xml:space="preserve">
    <value>Add Debit Line</value>
  </data>
  <data name="DebitLines_Delete" xml:space="preserve">
    <value>Delete Debit Line</value>
  </data>
  <data name="DebitLines_Edit" xml:space="preserve">
    <value>Edit Debit Line</value>
  </data>
  <data name="DebitMainInfo_Edit" xml:space="preserve">
    <value>Edit Main Information</value>
  </data>
  <data name="MailMessageGroupMembers_EditMembers" xml:space="preserve">
    <value>Edit Mail Group Members</value>
  </data>
  <data name="MailMessageGroups_Add" xml:space="preserve">
    <value>Add New Mail Group</value>
  </data>
  <data name="MailMessageGroups_Delete" xml:space="preserve">
    <value>Delete Mail Group</value>
  </data>
  <data name="MailMessageGroups_Edit" xml:space="preserve">
    <value>Edit Mail Group</value>
  </data>
  <data name="Sourcing_AddToReq_SelectCusReq" xml:space="preserve">
    <value>Add to Customer Requirement</value>
  </data>
  <data name="GILines_Delete" xml:space="preserve">
    <value>Delete Goods In Line</value>
  </data>
  <data name="GILines_Edit" xml:space="preserve">
    <value>Edit Goods In Line</value>
  </data>
  <data name="GIMainInfo_Edit" xml:space="preserve">
    <value>Edit Main Goods In Information</value>
  </data>
  <data name="SRMAShippingLines_Ship" xml:space="preserve">
    <value>Ship Supplier RMA Line</value>
  </data>
  <data name="SOMainInfo_Authorise" xml:space="preserve">
    <value>Authorise Sales Order</value>
  </data>
  <data name="SOMainInfo_Deauthorise" xml:space="preserve">
    <value>De-authorise Sales Order</value>
  </data>
  <data name="CompanyAddresses_DefaultShip" xml:space="preserve">
    <value>Make Default Shipping Address</value>
  </data>
  <data name="StockImages_Add" xml:space="preserve">
    <value>Add a new Image</value>
  </data>
  <data name="LotItems_Delete_Service" xml:space="preserve">
    <value>Delete Unallocated Services</value>
  </data>
  <data name="LotItems_Delete_Stock" xml:space="preserve">
    <value>Delete Unallocated Stock</value>
  </data>
  <data name="LotItems_Transfer_Service" xml:space="preserve">
    <value>Transfer Services</value>
  </data>
  <data name="LotItems_Transfer_Stock" xml:space="preserve">
    <value>Transfer Stock</value>
  </data>
  <data name="LotMainInfo_Delete" xml:space="preserve">
    <value>Delete Lot</value>
  </data>
  <data name="ServiceMainInfo_Edit" xml:space="preserve">
    <value>Edit Main Information</value>
  </data>
  <data name="StockImages_Delete" xml:space="preserve">
    <value>Delete Image</value>
  </data>
  <data name="DocHeaderImage_Add" xml:space="preserve">
    <value>Add Document Header Image</value>
  </data>
  <data name="DocHeaderImage_Delete" xml:space="preserve">
    <value>Delete Document Header Image</value>
  </data>
  <data name="DocFooters_Edit" xml:space="preserve">
    <value>Edit Document Footer</value>
  </data>
  <data name="GILines_Inspect" xml:space="preserve">
    <value>Inspect Goods In Line</value>
  </data>
  <data name="SRMALines_Allocate" xml:space="preserve">
    <value>Allocate SRMA Line</value>
  </data>
  <data name="SRMALines_Deallocate" xml:space="preserve">
    <value>Deallocate SRMA Line</value>
  </data>
  <data name="ServiceMainInfo_Delete" xml:space="preserve">
    <value>Delete Service</value>
  </data>
  <data name="EmailDocument" xml:space="preserve">
    <value>Send Email</value>
  </data>
  <data name="Sequencer_Edit" xml:space="preserve">
    <value>Edit Sequence Numbers</value>
  </data>
  <data name="SOAuthorisation_Authorise" xml:space="preserve">
    <value>Authorise Sales Order</value>
  </data>
  <data name="SOAuthorisation_Deauthorise" xml:space="preserve">
    <value>De-authorise Sales Order</value>
  </data>
  <data name="SOMainInfo_Notify" xml:space="preserve">
    <value>Sales Order Notification </value>
  </data>
  <data name="DocHeaderImage_Edit" xml:space="preserve">
    <value>Edit Document Header Image</value>
  </data>
  <data name="GIMainInfo_Notify" xml:space="preserve">
    <value>Goods In Notification </value>
  </data>
  <data name="POLines_PostAll" xml:space="preserve">
    <value>Post All Lines</value>
  </data>
  <data name="POLines_UnpostAll" xml:space="preserve">
    <value>Unpost All Lines</value>
  </data>
  <data name="InvoiceLines_Delete" xml:space="preserve">
    <value>Delete Invoice Line</value>
  </data>
  <data name="CurrencyRateHistory_Delete" xml:space="preserve">
    <value>Delete Currency Rate</value>
  </data>
  <data name="CurrencyRateHistory_Edit" xml:space="preserve">
    <value>Edit Currency Rate</value>
  </data>
  <data name="CusReqMainInfo_Close" xml:space="preserve">
    <value>Close Customer Requirement</value>
  </data>
  <data name="SOLines_PostAll" xml:space="preserve">
    <value>Post All Lines</value>
  </data>
  <data name="SOLines_UnpostAll" xml:space="preserve">
    <value>Unpost All Lines</value>
  </data>
  <data name="CountingMethod_Add" xml:space="preserve">
    <value>Add New Counting Method</value>
  </data>
  <data name="CountingMethod_Edit" xml:space="preserve">
    <value>Edit Counting Method details</value>
  </data>
  <data name="POMainInfo_Close" xml:space="preserve">
    <value>Close Purchase Order</value>
  </data>
  <data name="SOMainInfo_Close" xml:space="preserve">
    <value>Close Sales Order</value>
  </data>
  <data name="POLines_Close" xml:space="preserve">
    <value>Close Purchase Order Line</value>
  </data>
  <data name="SOLines_Close" xml:space="preserve">
    <value>Close Sales Order Line</value>
  </data>
  <data name="CRMALines_Deallocate" xml:space="preserve">
    <value>Deallocate CRMA Line</value>
  </data>
  <data name="ServiceAllocations_Deallocate" xml:space="preserve">
    <value>Deallocate Service</value>
  </data>
  <data name="AppSettings_Edit" xml:space="preserve">
    <value>Edit Application Settings</value>
  </data>
  <data name="Incoterm_Add" xml:space="preserve">
    <value>Add New Incoterm</value>
  </data>
  <data name="Incoterm_Edit" xml:space="preserve">
    <value>Edit Incoterm Details</value>
  </data>
  <data name="SecurityGroups_Clone" xml:space="preserve">
    <value>Clone Security Group</value>
  </data>
  <data name="SecurityUsers_Transfer" xml:space="preserve">
    <value>Transfer User Sales Accounts</value>
  </data>
  <data name="GlobalCountryList_Add" xml:space="preserve">
    <value>Add Master Country</value>
  </data>
  <data name="POMainInfo_Approve" xml:space="preserve">
    <value>Approve Purchase Order</value>
  </data>
  <data name="POMainInfo_Disapprove" xml:space="preserve">
    <value>Un-approve Purchase Order</value>
  </data>
  <data name="InvoiceMainInfo_Export" xml:space="preserve">
    <value>Export Invoice Details</value>
  </data>
  <data name="InvoiceMainInfo_Release" xml:space="preserve">
    <value>Release Exported Invoice Details</value>
  </data>
  <data name="POMainInfo_Notify" xml:space="preserve">
    <value>Purchase Order Notification</value>
  </data>
  <data name="Warehouse_ClearDefault" xml:space="preserve">
    <value>Clear Default Warehouse</value>
  </data>
  <data name="Warehouse_MakeDefault" xml:space="preserve">
    <value>Set Default Warehouse</value>
  </data>
  <data name="TaxRateHistory_Delete" xml:space="preserve">
    <value>Delete Future Tax Rate</value>
  </data>
  <data name="CRMALines_Close" xml:space="preserve">
    <value>Close Customer RMA Line</value>
  </data>
  <data name="CRMALine_Close" xml:space="preserve">
    <value>CLOSE CUSTOMER RMA LINE</value>
  </data>
  <data name="GILines_PrintLabel" xml:space="preserve">
    <value>Print Goods In Label</value>
  </data>
  <data name="PDF_Add" xml:space="preserve">
    <value>PDF</value>
  </data>
  <data name="PDF_Delete" xml:space="preserve">
    <value>Delete PDF</value>
  </data>
  <data name="EmailComposer_Edit" xml:space="preserve">
    <value>Invoice Bulk Email Composer</value>
  </data>
  <data name="Invoice_Email" xml:space="preserve">
    <value>Invoice Bulk Email</value>
  </data>
  <data name="Incoterm_Disable" xml:space="preserve">
    <value>Disable Incoterm</value>
  </data>
  <data name="Incoterm_Enable" xml:space="preserve">
    <value>Enable Incoterm</value>
  </data>
  <data name="ContactsForCompany_MakeDefaultPOLedger" xml:space="preserve">
    <value>Make Default Purchase Orders Ledger</value>
  </data>
  <data name="ContactsForCompany_MakeDefaultSOLedger" xml:space="preserve">
    <value>Make Default Sales Orders Ledger</value>
  </data>
  <data name="InvoiceLines_EditBankFee" xml:space="preserve">
    <value>Edit Bank Charge Fee</value>
  </data>
  <data name="Sourcing_AddStockInfo" xml:space="preserve">
    <value>Add Sourcing Info</value>
  </data>
  <data name="Sourcing_EditStockInfo" xml:space="preserve">
    <value>Edit Sourcing Info</value>
  </data>
  <data name="Sourcing_AddOffer" xml:space="preserve">
    <value>Add Offer</value>
  </data>
  <data name="Sourcing_AddTrusted" xml:space="preserve">
    <value>Add Trusted</value>
  </data>
  <data name="TabSecurity_Edit" xml:space="preserve">
    <value>Permissions for Tab Security</value>
  </data>
  <data name="LocalCurrency_Add" xml:space="preserve">
    <value>Add New Local Currency</value>
  </data>
  <data name="LocalCurrency_Edit" xml:space="preserve">
    <value>Edit Local Currency</value>
  </data>
  <data name="GILines_NPRPrinted" xml:space="preserve">
    <value>Edit NPR Status</value>
  </data>
  <data name="SupplierInvoiceAdd_Add" xml:space="preserve">
    <value>Add New Supplier Invoice</value>
  </data>
  <data name="SupplierInvoiceMainInfo_Edit" xml:space="preserve">
    <value>Edit Main Supplier Invoice Information</value>
  </data>
  <data name="SupplierInvoiceLines_Add" xml:space="preserve">
    <value>Supplier Invoice Line</value>
  </data>
  <data name="SupplierInvoiceLines_Delete" xml:space="preserve">
    <value>Delete Supplier Invoice Line</value>
  </data>
  <data name="SupplierInvoiceMainInfo_Notify" xml:space="preserve">
    <value>Supplier Invoice Notification</value>
  </data>
  <data name="NPRNotify_Notify" xml:space="preserve">
    <value>NPR Notify</value>
  </data>
  <data name="Printer_Add" xml:space="preserve">
    <value>Add New Printer</value>
  </data>
  <data name="Printer_Edit" xml:space="preserve">
    <value>Edit Printer Details</value>
  </data>
  <data name="StockMainInfo_StockProvision" xml:space="preserve">
    <value>Stock Provision</value>
  </data>
  <data name="GILines_PhysicalInspect" xml:space="preserve">
    <value>Inspect Goods In Line</value>
  </data>
  <data name="CertificateCategory_Add" xml:space="preserve">
    <value>Add Certificate Category</value>
  </data>
  <data name="CertificateCategory_Edit" xml:space="preserve">
    <value>Edit Certificate Category</value>
  </data>
  <data name="Certificate_Add" xml:space="preserve">
    <value>Add New Certificate</value>
  </data>
  <data name="Certificate_Edit" xml:space="preserve">
    <value>Edit Certificate</value>
  </data>
  <data name="CompanyCertificate_Add" xml:space="preserve">
    <value>Add Company Certificate</value>
  </data>
  <data name="CompanyCertificate_Edit" xml:space="preserve">
    <value>Edit Company Certificate</value>
  </data>
  <data name="EightDCode_Add" xml:space="preserve">
    <value>Root Cause Category Add</value>
  </data>
  <data name="EightDCode_Edit" xml:space="preserve">
    <value>Root Cause Category Edit</value>
  </data>
  <data name="EightDSubCategory_Add" xml:space="preserve">
    <value>Add Root Cause sub category</value>
  </data>
  <data name="EigthDSubCategory_Edit" xml:space="preserve">
    <value>Edit Root Cause sub category</value>
  </data>
  <data name="LotItems_StockProvision" xml:space="preserve">
    <value>Stock Provision</value>
  </data>
  <data name="LotItems_LotStockProvision" xml:space="preserve">
    <value>STOCK LOT PROVISION</value>
  </data>
  <data name="LotItems_Save" xml:space="preserve">
    <value>Save Stock Provision</value>
  </data>
  <data name="LabelFullPath_Add" xml:space="preserve">
    <value>Add New Nice Label Path</value>
  </data>
  <data name="LabelFullPath_Edit" xml:space="preserve">
    <value>Edit Nice Label Path</value>
  </data>
  <data name="QuoteLines_EditOffer" xml:space="preserve">
    <value>Edit Original Offer</value>
  </data>
  <data name="InvoiceMainInfo_EditShippingInfo" xml:space="preserve">
    <value>Edit Invoice Shipping Information</value>
  </data>
  <data name="EPRNotify_Notify" xml:space="preserve">
    <value>EPR Notify</value>
  </data>
  <data name="EXCEL_Add" xml:space="preserve">
    <value>Excel</value>
  </data>
  <data name="EXCEL_Delete" xml:space="preserve">
    <value>Delete Excel</value>
  </data>
  <data name="POExpNotes_Add" xml:space="preserve">
    <value>Add Expedite Note</value>
  </data>
  <data name="BOMAdd_Add" xml:space="preserve">
    <value>Add New HUBRFQ</value>
  </data>
  <data name="BOMMainInfo_Delete" xml:space="preserve">
    <value>Delete HUBRFQ</value>
  </data>
  <data name="BOMMainInfo_Edit" xml:space="preserve">
    <value>Edit Main Information</value>
  </data>
  <data name="InternalPOAdd_Add" xml:space="preserve">
    <value>Add New Internal Purchase Order</value>
  </data>
  <data name="SOMainInfo_Warehouse" xml:space="preserve">
    <value>Warehouse</value>
  </data>
  <data name="SOLines_Warehouse" xml:space="preserve">
    <value>Warehouse</value>
  </data>
  <data name="BOMMainInfo_Notify" xml:space="preserve">
    <value>Notification</value>
  </data>
  <data name="POQuoteMainInfo_Notify" xml:space="preserve">
    <value>Notification</value>
  </data>
  <data name="BOMItems_Save" xml:space="preserve">
    <value>Release Customer Requirement</value>
  </data>
  <data name="POQuoteLines_Add" xml:space="preserve">
    <value>Add New Price Request Line</value>
  </data>
  <data name="POQuoteAdd_Add" xml:space="preserve">
    <value>Add New Price Request</value>
  </data>
  <data name="POQuoteMainInfo_Edit" xml:space="preserve">
    <value>Edit Main Price Request Information</value>
  </data>
  <data name="BOMITEMS_ADD" xml:space="preserve">
    <value>Add New HUBRFQ Item</value>
  </data>
  <data name="POQuoteLines_Close" xml:space="preserve">
    <value>Delete Price Request Line</value>
  </data>
  <data name="CSV_Add" xml:space="preserve">
    <value>Import CSV File</value>
  </data>
  <data name="Sourcing_AddToReq" xml:space="preserve">
    <value>Add to Requirement</value>
  </data>
  <data name="BOMItems_Delete" xml:space="preserve">
    <value>DELETE CUSTOMER REQUIREMENT</value>
  </data>
  <data name="ClientInvoiceMainInfo_Edit" xml:space="preserve">
    <value>Edit Main Client Invoice Information</value>
  </data>
  <data name="ClientInvoiceLines_Add" xml:space="preserve">
    <value>Client Invoice Line</value>
  </data>
  <data name="InvoiceSetting_Add" xml:space="preserve">
    <value>Schedule invoice</value>
  </data>
  <data name="InvoiceSetting_Edit" xml:space="preserve">
    <value>Schedule invoice</value>
  </data>
  <data name="BOMItems_UnRelease" xml:space="preserve">
    <value>Revoke HUBRFQ Item</value>
  </data>
  <data name="PurchaseRequestLineDetail_Add" xml:space="preserve">
    <value>Add Price Request line detail</value>
  </data>
  <data name="PurchaseRequestLineDetail_Edit" xml:space="preserve">
    <value>Edit Price Request Line Detail</value>
  </data>
  <data name="BOM_AssignToMe" xml:space="preserve">
    <value>Assign to me HUBRFQ Item</value>
  </data>
  <data name="BOMCusReqSourcingResults_Delete" xml:space="preserve">
    <value>Delete Sourcing Result</value>
  </data>
  <data name="CreditLines_Confirm" xml:space="preserve">
    <value>Add Credit Note For POHUB</value>
  </data>
  <data name="INTERNALPOLINES_EDIT" xml:space="preserve">
    <value>Edit Internal Purchase Order Line</value>
  </data>
  <data name="CusReqSourcingResults_EditHub" xml:space="preserve">
    <value>Edit Quote to Client</value>
  </data>
  <data name="SOLINES_CREATECLONE" xml:space="preserve">
    <value>Create Clone</value>
  </data>
  <data name="InternalPOMainInfo_Edit" xml:space="preserve">
    <value>Edit Internal Purchase Order</value>
  </data>
  <data name="ClientInvoiceLines_Delete" xml:space="preserve">
    <value>Delete Client Invoice Line</value>
  </data>
  <data name="ClientInvoiceAdd_Add" xml:space="preserve">
    <value>Add New Client Invoice</value>
  </data>
  <data name="HUBRFQNoBid" xml:space="preserve">
    <value>No-Bid HUBRFQ</value>
  </data>
  <data name="HUBRFQRecallNoBid" xml:space="preserve">
    <value>Recall No-Bid HUBRFQ</value>
  </data>
  <data name="GILines_PrintNiceLabel" xml:space="preserve">
    <value>Goods In Label</value>
  </data>
  <data name="GILines_PrintNiceLabelCRX" xml:space="preserve">
    <value>Goods In Label CRX B2B</value>
  </data>
  <data name="GILines_PrintRejectedLabel" xml:space="preserve">
    <value>Goods In Label Rejected</value>
  </data>
  <data name="GILines_PrintStockLabel" xml:space="preserve">
    <value>Goods In Label Stock</value>
  </data>
  <data name="StockMainInfo_PrintNiceLabel" xml:space="preserve">
    <value>Stock Label</value>
  </data>
  <data name="StockMainInfo_PrintNiceLabelCRX" xml:space="preserve">
    <value>Stock Label CRX B2B</value>
  </data>
  <data name="StockMainInfo_PrintRejectedLabel" xml:space="preserve">
    <value>Stock Label Rejected</value>
  </data>
  <data name="StockMainInfo_PrintStockLabel" xml:space="preserve">
    <value>Stock Label</value>
  </data>
  <data name="HUBRFQExpNotes_Add" xml:space="preserve">
    <value>Add HUBRFQ Communication Note</value>
  </data>
  <data name="GlobalProduct_Add" xml:space="preserve">
    <value>ADD NEW GLOBAL PRODUCT</value>
  </data>
  <data name="GlobalProduct_Edit" xml:space="preserve">
    <value>EDIT GLOBAL PRODUCT DETAILS</value>
  </data>
  <data name="GlobalSecGroupPermissionsGeneral_Edit" xml:space="preserve">
    <value>Edit Permissions</value>
  </data>
  <data name="GlobalSecurityGroupMembers_EditMembers" xml:space="preserve">
    <value>Edit Security Group Members</value>
  </data>
  <data name="GlobalProductDutyRateHistory_Edit" xml:space="preserve">
    <value>Edit Client Product details</value>
  </data>
  <data name="CreditMainInfo_Export" xml:space="preserve">
    <value>Export Credit Note Details</value>
  </data>
  <data name="CreditMainInfo_Release" xml:space="preserve">
    <value>Release Exported Credit Note Details</value>
  </data>
  <data name="STOCKMAININFO_PRINTEXCESSSTOCKLABEL" xml:space="preserve">
    <value>Print Excess Label</value>
  </data>
  <data name="GlobalProductName_Add" xml:space="preserve">
    <value>ADD NEW GLOBAL PRODUCT NAME</value>
  </data>
  <data name="GlobalProductName_Edit" xml:space="preserve">
    <value>EDIT GLOBAL PRODUCT NAME DETAILS </value>
  </data>
  <data name="GTUPDATE_EDIT" xml:space="preserve">
    <value>Edit GT Application Update Information</value>
  </data>
  <data name="CRMAInternalLog" xml:space="preserve">
    <value>Add Internal Log</value>
  </data>
  <data name="SRMAInternalLog" xml:space="preserve">
    <value>Add Internal Log</value>
  </data>
  <data name="INTERNALPOEXPNOTES_ADD" xml:space="preserve">
    <value>Add Expedite Note</value>
  </data>
  <data name="POLine_Release" xml:space="preserve">
    <value>Release Purchase Order Line</value>
  </data>
  <data name="POLine_UnRelease" xml:space="preserve">
    <value>UnRelease Purchase Order Line</value>
  </data>
  <data name="SOMainInfo_PayByCreditCard" xml:space="preserve">
    <value>Pay By Credit Card</value>
  </data>
  <data name="CustReqAllInfo" xml:space="preserve">
    <value>Customer Requirement All Information</value>
  </data>
  <data name="LabelSetupItem_Add" xml:space="preserve">
    <value>Add New Master Status List</value>
  </data>
  <data name="LabelSetupItem_Edit" xml:space="preserve">
    <value>Edit Master Status List</value>
  </data>
  <data name="ClientBOMAdd_Add" xml:space="preserve">
    <value>Add New BOM</value>
  </data>
  <data name="ClientBOMITEMS_ADD" xml:space="preserve">
    <value>Add New BOM Item</value>
  </data>
  <data name="Sourcing_StockImportTool" xml:space="preserve">
    <value>Offers Import Tool</value>
  </data>
  <data name="GILINES_SPLITGI" xml:space="preserve">
    <value>Split Goods In Line</value>
  </data>
  <data name="INVOICEMAININFO_HOLD" xml:space="preserve">
    <value>Hold Exported Invoice Details</value>
  </data>
  <data name="INVOICEMAININFO_UNHOLD" xml:space="preserve">
    <value>Un Hold Exported Invoice Details</value>
  </data>
  <data name="CLIENT_EDIT" xml:space="preserve">
    <value>EDIT CLIENT DETAILS</value>
  </data>
  <data name="SOURCING_STOCKIMPORTEPOTOOL" xml:space="preserve">
    <value>Strategic Offers Import Tool</value>
  </data>
  <data name="SOURCING_EPOTOOL" xml:space="preserve">
    <value>Strategic Offers Import Tool</value>
  </data>
  <data name="SOURCING_EDITEPO" xml:space="preserve">
    <value>EDIT Strategic Offers</value>
  </data>
  <data name="Sourcing_EditReverseLogistic" xml:space="preserve">
    <value>EDIT Reverse Logistic</value>
  </data>
  <data name="CUSREQMAININFO_DELETE" xml:space="preserve">
    <value>Delete Alternate Part</value>
  </data>
  <data name="SOURCING_BOMIMPORTSOURCINGRESULT" xml:space="preserve">
    <value>Bom Import Sourcing Result</value>
  </data>
  <data name="MASTERLOGIN_CONFIRM" xml:space="preserve">
    <value>Master Login</value>
  </data>
  <data name="editinvoiceinfoafterrelease" xml:space="preserve">
    <value>EDIT INVOICE INFORMATION AFTER RELEASE</value>
  </data>
  <data name="UTILITYOFFER_IMPORT" xml:space="preserve">
    <value>Offers Import Tool</value>
  </data>
  <data name="UtilityBOM_Import" xml:space="preserve">
    <value>Utility BOM Import</value>
  </data>
  <data name="UTILITYXMATCH_IMPORT" xml:space="preserve">
    <value>X-Match Import</value>
  </data>
  <data name="CloneHUB" xml:space="preserve">
    <value>Clone requirement and send to HUB</value>
  </data>
  <data name="CloneHUBRFQ" xml:space="preserve">
    <value>Clone requirement and add HUBRFQ</value>
  </data>
  <data name="SA_LineManagerApprove" xml:space="preserve">
    <value>Line Manager Approve</value>
  </data>
  <data name="SA_LineManagerDecline" xml:space="preserve">
    <value>Line Manager Decline</value>
  </data>
  <data name="SA_LineManagerIndpndt" xml:space="preserve">
    <value>Approve Independent Testing Recommended</value>
  </data>
  <data name="SA_QualityApprove" xml:space="preserve">
    <value>Quality Approve</value>
  </data>
  <data name="SA_QualityDecline" xml:space="preserve">
    <value>Quality Decline</value>
  </data>
  <data name="Supplier_Approval_Edit" xml:space="preserve">
    <value>Edit Supplier Approval Details</value>
  </data>
  <data name="GILINES_ADDSHORTSHIPMENT" xml:space="preserve">
    <value>SHORT SHIPMENT REQUEST - WAREHOUSE</value>
  </data>
  <data name="SHORTSHIPMENTNOTIFY_NOTIFY" xml:space="preserve">
    <value>SHORT SHIPMENT REQUEST - WAREHOUSE</value>
  </data>
  <data name="CUSREQSOURCINGRESULTS_DELETE" xml:space="preserve">
    <value>DELETE PARTWATCH MATCH</value>
  </data>
  <data name="GILINE_NOTIFY" xml:space="preserve">
    <value>Goods In Line Notify</value>
  </data>
  <data name="GILINES_IMAGEUPLOAD" xml:space="preserve">
    <value>GI Line Images Upload</value>
  </data>
  <data name="GILINES_QUARANTINE" xml:space="preserve">
    <value>Quarantine Product</value>
  </data>
  <data name="GILINES_UPLOADPDF" xml:space="preserve">
    <value>Upload PDF</value>
  </data>
  <data name="DebitMainInfo_Export" xml:space="preserve">
    <value>Export Debit Note Details</value>
  </data>
  <data name="DebitMainInfo_Release" xml:space="preserve">
    <value>Release Exported Debit Note Details</value>
  </data>
  <data name="GILINES_EXPORT" xml:space="preserve">
    <value>Export to PDF/Word</value>
  </data>
  <data name="RestrictedManufacture_Add" xml:space="preserve">
    <value>Restricted Manufacturer</value>
  </data>
  <data name="RestrictedManufacture_Edit" xml:space="preserve">
    <value>Restricted Manufacturer</value>
  </data>
  <data name="WarningMessage_Edit" xml:space="preserve">
    <value>Edit Warning Message</value>
  </data>
  <data name="WarningMessage_Add" xml:space="preserve">
    <value>ADD WARNING MESSAGE</value>
  </data>
  <data name="SSMAININFO_CANCEL" xml:space="preserve">
    <value>Cancel Short Shipment</value>
  </data>
  <data name="SSMAININFO_CLOSE" xml:space="preserve">
    <value>Close Short Shipment</value>
  </data>
  <data name="SSMAININFO_CONFIRM" xml:space="preserve">
    <value>UPDATE SHORT SHIPMENT</value>
  </data>
  <data name="ECCN_ADD" xml:space="preserve">
    <value>ADD ECCN Code</value>
  </data>
  <data name="ECCN_EDIT" xml:space="preserve">
    <value>EDIT ECCN Code</value>
  </data>
  <data name="SA_QualityEscalate" xml:space="preserve">
    <value>Quality Escalate</value>
  </data>
  <data name="UTILITYSTOCK_IMPORT" xml:space="preserve">
    <value>Utility Stock Import</value>
  </data>
  <data name="COMPANYPROSPECTS_EDIT" xml:space="preserve">
    <value />
  </data>
  <data name="TODOLISTTYPE_ADD" xml:space="preserve">
    <value>ADD To Do List Type</value>
  </data>
  <data name="TODOLISTTYPE_EDIT" xml:space="preserve">
    <value>EDIT To Do List Type</value>
  </data>
  <data name="SAIMAGEDRAGDROP_ADD" xml:space="preserve">
    <value>Images</value>
  </data>
  <data name="GT" xml:space="preserve">
    <value>GT</value>
  </data>
  <data name="REQUESTAPPROVAL" xml:space="preserve">
    <value>SOR Approval</value>
  </data>
  <data name="UTILITYLOG_IMPORT" xml:space="preserve">
    <value>UTILITY LOG</value>
  </data>
  <data name="GlobalProductMainCategory_Map" xml:space="preserve">
    <value>MAP GLOBAL PRODUCT TO CATEGORY</value>
  </data>
  <data name="GLOBALPRODUCTMAINCATEGORY_ADD" xml:space="preserve">
    <value>ADD NEW GLOBAL PRODUCT CATEGORY NAME</value>
  </data>
  <data name="GLOBALPRODUCTMAINCATEGORY_EDIT" xml:space="preserve">
    <value>Edit Category Name</value>
  </data>
  <data name="ENHANCEDINSPECTIONTEST_EDIT" xml:space="preserve">
    <value>Edit Enhanced Inspection Test</value>
  </data>
  <data name="ENHANCEDINSPECTIONTEST_ADD" xml:space="preserve">
    <value>Add new Test</value>
  </data>
  <data name="EnhancedInspectionTest_Delete" xml:space="preserve">
    <value>Are you sure you would like to delete this enhance inspection test?</value>
  </data>
  <data name="SalesBOM_Import" xml:space="preserve">
    <value>Sales BOM Import</value>
  </data>
  <data name="QUOTELINES_DELETE" xml:space="preserve">
    <value>Delete Quote Line</value>
  </data>
  <data name="UTILITYBOMMANAGER_IMPORT" xml:space="preserve">
    <value>BOM Manager Import</value>
  </data>
  <data name="BOMManager" xml:space="preserve">
    <value>BOM Manager</value>
  </data>
  <data name="BOMItems_SaveApplyPartwatch" xml:space="preserve">
    <value>Apply Part Watch for HUBIPO</value>
  </data>
  <data name="BOMItems_SaveRemoveApplyPartwatch" xml:space="preserve">
    <value>Remove Applyed Part Watch for HUBIPO</value>
  </data>
  <data name="BOMCusReqSourcingResults_DeletePartWatch" xml:space="preserve">
    <value>Delete Partwatch Result</value>
  </data>
  <data name="OGELApprovetitle" xml:space="preserve">
    <value>Authorise Export</value>
  </data>
  <data name="OGELLines" xml:space="preserve">
    <value>OGEL Lines</value>
  </data>
  <data name="ExportApprovalRequestSend" xml:space="preserve">
    <value>Send Export Approval</value>
  </data>
  <data name="Utility_ReverseLogisticsImport" xml:space="preserve">
    <value>Reverse Logistics Import Tool</value>
  </data>
  <data name="UTILITYREVERSELOGISTICS_IMPORT" xml:space="preserve">
    <value>Reverse Logistics Import Tool</value>
  </data>
  <data name="Utility_RLImport" xml:space="preserve">
    <value>Reverse Logistics Import Tool</value>
  </data>
  <data name="ExportApproval_Edit" xml:space="preserve">
    <value>Edit Export Approval Details</value>
  </data>
  <data name="ExportApproval_EditAll" xml:space="preserve">
    <value>Edit All Export Approval Details</value>
  </data>
  <data name="ECCN_EDITMAP" xml:space="preserve">
    <value>EDIT ECCN Warning</value>
  </data>
  <data name="GILines_CloseInspection" xml:space="preserve">
    <value>Complete Inspection</value>
  </data>
  <data name="GILines_StartInspection" xml:space="preserve">
    <value>Start Inspection</value>
  </data>
  <data name="SOURCING_SEARCHOFFER" xml:space="preserve">
    <value>Check Supplier / Manufacturer Data</value>
  </data>
  <data name="CompanyApiCustomer_Add" xml:space="preserve">
    <value>Customer API Add</value>
  </data>
  <data name="CompanyApiCustomer_Edit" xml:space="preserve">
    <value>Customer API Edit</value>
  </data>
  <data name="ECCN_EDITCLONE" xml:space="preserve">
    <value>Clone ECCN CODE</value>
  </data>
  <data name="AS6081_EDIT" xml:space="preserve">
    <value>Edit Type Of Suppliers Details</value>
  </data>
  <data name="AS6081_RCS_EDIT" xml:space="preserve">
    <value>Edit Reason For Chosen Supplier Details</value>
  </data>
  <data name="AS6081_ROS_EDIT" xml:space="preserve">
    <value>Edit Risk Of Supplier Details</value>
  </data>
  <data name="AS6081_ADD" xml:space="preserve">
    <value>Add New Type Of Suppliers</value>
  </data>
  <data name="AS6081_Delete" xml:space="preserve">
    <value>Delete Type Of Suppliers</value>
  </data>
  <data name="AS6081_RCS_ADD" xml:space="preserve">
    <value>Add New Reason For Chosen Supplier</value>
  </data>
  <data name="AS6081_RCS_Delete" xml:space="preserve">
    <value>Delete Reason For Chosen Supplier</value>
  </data>
  <data name="AS6081_ROS_ADD" xml:space="preserve">
    <value>Add New Risk Of Supplier</value>
  </data>
  <data name="AS6081_ROS_Delete" xml:space="preserve">
    <value>Delete Risk Of Supplier</value>
  </data>
  <data name="PDFDocumentFileSize_Add" xml:space="preserve">
    <value>Add New Document File Size</value>
  </data>
  <data name="PDFDocumentFileSize_Edit" xml:space="preserve">
    <value>Edit Document File Size Details</value>
  </data>
  <data name="CompanyGlobalSalesPDetails_Delete" xml:space="preserve">
    <value>Delete Global Sales Person</value>
  </data>
  <data name="CompanyGlobalSalesPDetails_Add" xml:space="preserve">
    <value>Add Global Sales Person Details</value>
  </data>
  <data name="CompanyGlobalSalesPDetails_Edit" xml:space="preserve">
    <value>Edit Global Sales Person Details</value>
  </data>
  <data name="ENTERTAINMENTTYPE_ADD" xml:space="preserve">
    <value>ENTERTAINMENT TYPE ADD</value>
  </data>
  <data name="ENTERTAINMENTTYPE_EDIT" xml:space="preserve">
    <value>ENTERTAINMENT TYPE EDIT</value>
  </data>
  <data name="SOURCING_EDITSTOCK" xml:space="preserve">
    <value>Edit Stock</value>
  </data>
  <data name="GSA_EDITMEMBERS" xml:space="preserve">
    <value>MANAGE GLOBAL SALES ACCESS MEMBERS</value>
  </data>
  <data name="ClientInvoiceHeader_Add" xml:space="preserve">
    <value>Add New Client Invoice Header</value>
  </data>
  <data name="ClientInvoiceHeader_Edit" xml:space="preserve">
    <value>Edit Client Invoice Header Details</value>
  </data>
  <data name="ClientInvoiceHeaderImage_Add" xml:space="preserve">
    <value>Add Client Invoice Header Image</value>
  </data>
  <data name="ClientInvoiceHeaderImage_Delete" xml:space="preserve">
    <value>Delete Client Invoice Header Image</value>
  </data>
  <data name="ClientInvoiceHeaderImage_Edit" xml:space="preserve">
    <value>Edit Client Invoice Header Image</value>
  </data>
  <data name="COMPANYINSURANCECERTIFICATE_ADD" xml:space="preserve">
    <value>Add Company Insurance Certificate</value>
  </data>
  <data name="COMPANYINSURANCECERTIFICATE_EDIT" xml:space="preserve">
    <value>Edit Company Insurance Certificate</value>
  </data>
  <data name="SOAuthorisation_ReadyToShip" xml:space="preserve">
    <value>Ready To Ship</value>
  </data>
  <data name="Sourcing_EditReverseLogisticsBulk" xml:space="preserve">
    <value>ARE YOU SURE YOU WOULD LIKE TO UPDATE IN BULK?</value>
    <comment>RP-2603</comment>
  </data>
  <data name="PoHubSourcing_EditReverseLogisticsBulk" xml:space="preserve">
    <value>ARE YOU SURE YOU WOULD LIKE TO UPDATE IN BULK?</value>
    <comment>RP-2603</comment>
  </data>
  <data name="CompanyFinanceInfo_Link" xml:space="preserve">
    <value>Link Accounts</value>
  </data>
  <data name="Country_ManageHeader" xml:space="preserve">
    <value>Manage Header</value>
  </data>
  <data name="COUNTRY_DELETEHEADER" xml:space="preserve">
    <value>Remove Header</value>
  </data>
  <data name="Sourcing_BulkEditReverseLogisticHistory" xml:space="preserve">
    <value>Show Bulk Edited Reverse Logistic History</value>
  </data>
  <data name="PPVBOM_ADD" xml:space="preserve">
    <value>ADD NEW PPV/BOM</value>
  </data>
  <data name="PPVBOM_EDIT" xml:space="preserve">
    <value>EDIT PPV/BOM DETAILS</value>
  </data>
  <data name="BOMPVV_EDIT" xml:space="preserve">
    <value>PPV/ BOM Qualification ADD/EDIT</value>
  </data>
  <data name="BOMPVV_DELETE" xml:space="preserve">
    <value>PPV/ BOM Qualification Delete</value>
  </data>
  <data name="Credit_Email" xml:space="preserve">
    <value>Credit Bulk Email</value>
  </data>
  <data name="Debit_Email" xml:space="preserve">
    <value>Debit Bulk Email</value>
  </data>
  <data name="UtilityProspectiveOffer_Import" xml:space="preserve">
    <value>Prospective Offer Import Tool</value>
  </data>
  <data name="UtilityHUBOffer_ImportLarge" xml:space="preserve">
    <value>Bulk Offer Scheduled Import</value>
  </data>
  <data name="OGELLicenses_Add" xml:space="preserve">
    <value>Add New OGEL License</value>
  </data>
  <data name="OGELLicenses_Edit" xml:space="preserve">
    <value>Edit OGEL License details</value>
  </data>
  <data name="StarRating_Add" xml:space="preserve">
    <value>ADD NEW STAR RATING</value>
  </data>
  <data name="ManufacturerSuppliers_View" xml:space="preserve">
    <value>VIEW SUPPLIER WHO DISTRIBUTES</value>
  </data>
  <data name="CompanyManufacturers_View" xml:space="preserve">
    <value>View Manufacturer Supplied</value>
  </data>
  <data name="GlobalTax_Edit" xml:space="preserve">
    <value>Edit Master Tax Details</value>
  </data>
  <data name="GlobalTax_EditRates" xml:space="preserve">
    <value>Edit Master Tax Rates</value>
  </data>
  <data name="IHSAdd_Add" xml:space="preserve">
    <value>Add New Part Information</value>
  </data>
</root>