﻿using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

namespace Rebound.GlobalTrader.DAL
{
	
	public class RevenueTargetDetails
	{
		
		#region Constructors
		
		public RevenueTargetDetails() { }

		#endregion

		#region Properties

		/// <summary>
		/// SectionId means division team sales or customer id
		/// </summary>
		public System.Int32 SectionId { get; set; }
		/// <summary>
		/// SectionName means name of section like Division Team Sales or Customer
		/// </summary>
		public string SectionName { get; set; }
		/// <summary>
		/// Division Team Sales Customer
		/// </summary>
		public string SectionType { get; set; }
		public System.Double? JanTarget { get; set; }
		public System.Double? FebTarget { get; set; }
		public System.Double? MarchTarget { get; set; }
		public System.Double? AprTarget { get; set; }
		public System.Double? MayTarget { get; set; }
		public System.Double? JuneTarget { get; set; }
		public System.Double? JulyTarget { get; set; }
		public System.Double? AugTarget { get; set; }
		public System.Double? SepTarget { get; set; }
		public System.Double? OctTarget { get; set; }
		public System.Double? NovTarget { get; set; }
		public System.Double? DecTarget { get; set; }
		public System.Double? JanRevenue { get; set; }
		public System.Double? FebRevenue { get; set; }
		public System.Double? MarchRevenue { get; set; }
		public System.Double? AprRevenue { get; set; }
		public System.Double? MayRevenue { get; set; }
		public System.Double? JuneRevenue { get; set; }
		public System.Double? JulyRevenue { get; set; }
		public System.Double? AugRevenue { get; set; }
		public System.Double? SepRevenue { get; set; }
		public System.Double? OctRevenue { get; set; }
		public System.Double? NovRevenue { get; set; }
		public System.Double? DecRevenue { get; set; }

		public System.Double? JanGrossProfit { get; set; }
		public System.Double? FebGrossProfit { get; set; }
		public System.Double? MarchGrossProfit { get; set; }
		public System.Double? AprGrossProfit { get; set; }
		public System.Double? MayGrossProfit { get; set; }
		public System.Double? JuneGrossProfit { get; set; }
		public System.Double? JulyGrossProfit { get; set; }
		public System.Double? AugGrossProfit { get; set; }
		public System.Double? SepGrossProfit { get; set; }
		public System.Double? OctGrossProfit { get; set; }
		public System.Double? NovGrossProfit { get; set; }
		public System.Double? DecGrossProfit { get; set; }

		public System.Double? AllocatedPer { get; set; }
		public System.Double? RevenuePer { get; set; }
		public System.Double? TotalTarget { get; set; }
		public System.Double? TotalRevenue { get; set; }
		public System.Double? TotalGrossProfit { get; set; }

		public System.Int32 RowId { get; set; }
        public System.Int32 RecordCount { get; set; }

        #endregion

    }
}