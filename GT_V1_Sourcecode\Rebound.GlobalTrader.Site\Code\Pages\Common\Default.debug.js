///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//
// RP 26.11.2009:
// - switch tabs with javascript rather than a full page load
//-----------------------------------------------------------------------------------------
/*
Marker     Changed by      Date         Remarks
[001]      S<PERSON>r   07/05/2012   BOM Details on Dashboard
[002]      <PERSON><PERSON><PERSON>     05/07/2018   Add customer order value nugget on broker and sales tab
[003]      Aashu Singh     26-Sep-2018  REB-13083: Change request PO - delivery status
*/
Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Default");

Rebound.GlobalTrader.Site.Pages.Default = function (el) {
    Rebound.GlobalTrader.Site.Pages.Default.initializeBase(this, [el]);
    this._intDifferentUserID = 0;
    var _this = this;
};

Rebound.GlobalTrader.Site.Pages.Default.prototype = {

    get_ctlPageTitle: function () { return this._ctlPageTitle; }, set_ctlPageTitle: function (v) { if (this._ctlPageTitle !== v) this._ctlPageTitle = v; },
    get_ctlTopLinks: function () { return this._ctlTopLinks; }, set_ctlTopLinks: function (v) { if (this._ctlTopLinks !== v) this._ctlTopLinks = v; },
    get_chkMyself: function () { return this._chkMyself; }, set_chkMyself: function (v) { if (this._chkMyself !== v) this._chkMyself = v; },
    get_chkIncludeCredit: function () { return this._chkIncludeCredit; }, set_chkIncludeCredit: function (v) { if (this._chkIncludeCredit !== v) this._chkIncludeCredit = v; },

    get_pnlChooseUser: function () { return this._pnlChooseUser; }, set_pnlChooseUser: function (v) { if (this._pnlChooseUser !== v) this._pnlChooseUser = v; },
    get_ddlEmployee: function () { return this._ddlEmployee; }, set_ddlEmployee: function (v) { if (this._ddlEmployee !== v) this._ddlEmployee = v; },
    get_intCurrentTab: function () { return this._intCurrentTab; }, set_intCurrentTab: function (v) { if (this._intCurrentTab !== v) this._intCurrentTab = v; },
    get_pnlToday: function () { return this._pnlToday; }, set_pnlToday: function (v) { if (this._pnlToday !== v) this._pnlToday = v; },
    get_pnlAccounts: function () { return this._pnlAccounts; }, set_pnlAccounts: function (v) { if (this._pnlAccounts !== v) this._pnlAccounts = v; },
    get_pnlBroker: function () { return this._pnlBroker; }, set_pnlBroker: function (v) { if (this._pnlBroker !== v) this._pnlBroker = v; },
    get_pnlPurchasing: function () { return this._pnlPurchasing; }, set_pnlPurchasing: function (v) { if (this._pnlPurchasing !== v) this._pnlPurchasing = v; },
    get_pnlSales: function () { return this._pnlSales; }, set_pnlSales: function (v) { if (this._pnlSales !== v) this._pnlSales = v; },
    get_pnlWarehouse: function () { return this._pnlWarehouse; }, set_pnlWarehouse: function (v) { if (this._pnlWarehouse !== v) this._pnlWarehouse = v; },
    get_PnlBI: function () { return this._PnlBI; }, set_PnlBI: function (v) { if (this._PnlBI !== v) this._PnlBI = v; },
    get_ctlToday_TopSalespersons: function () { return this._ctlToday_TopSalespersons; }, set_ctlToday_TopSalespersons: function (v) { if (this._ctlToday_TopSalespersons !== v) this._ctlToday_TopSalespersons = v; },
    get_ctlToday_MyRecentActivity: function () { return this._ctlToday_MyRecentActivity; }, set_ctlToday_MyRecentActivity: function (v) { if (this._ctlToday_MyRecentActivity !== v) this._ctlToday_MyRecentActivity = v; },
    get_ctlToday_PartsOrdered: function () { return this._ctlToday_PartsOrdered; }, set_ctlToday_PartsOrdered: function (v) { if (this._ctlToday_PartsOrdered !== v) this._ctlToday_PartsOrdered = v; },
    get_ctlToday_MyToDoList: function () { return this._ctlToday_MyToDoList; }, set_ctlToday_MyToDoList: function (v) { if (this._ctlToday_MyToDoList !== v) this._ctlToday_MyToDoList = v; },
    get_ctlToday_MyMessages: function () { return this._ctlToday_MyMessages; }, set_ctlToday_MyMessages: function (v) { if (this._ctlToday_MyMessages !== v) this._ctlToday_MyMessages = v; },
    get_ctlAccounts_AllStatistics: function () { return this._ctlAccounts_AllStatistics; }, set_ctlAccounts_AllStatistics: function (v) { if (this._ctlAccounts_AllStatistics !== v) this._ctlAccounts_AllStatistics = v; },
    get_ctlAccounts_MyToDoList: function () { return this._ctlAccounts_MyToDoList; }, set_ctlAccounts_MyToDoList: function (v) { if (this._ctlAccounts_MyToDoList !== v) this._ctlAccounts_MyToDoList = v; },
    get_ctlAccounts_ApprovedCustomersOnStop: function () { return this._ctlAccounts_ApprovedCustomersOnStop; }, set_ctlAccounts_ApprovedCustomersOnStop: function (v) { if (this._ctlAccounts_ApprovedCustomersOnStop !== v) this._ctlAccounts_ApprovedCustomersOnStop = v; },
    get_ctlAccounts_TodaysShippedOrders: function () { return this._ctlAccounts_TodaysShippedOrders; }, set_ctlAccounts_TodaysShippedOrders: function (v) { if (this._ctlAccounts_TodaysShippedOrders !== v) this._ctlAccounts_TodaysShippedOrders = v; },
    get_ctlAccounts_ReceivedOrders: function () { return this._ctlAccounts_ReceivedOrders; }, set_ctlAccounts_ReceivedOrders: function (v) { if (this._ctlAccounts_ReceivedOrders !== v) this._ctlAccounts_ReceivedOrders = v; },
    get_ctlBroker_MyStatistics: function () { return this._ctlBroker_MyStatistics; }, set_ctlBroker_MyStatistics: function (v) { if (this._ctlBroker_MyStatistics !== v) this._ctlBroker_MyStatistics = v; },
    get_ctlBroker_MyApprovedCustomersOnStop: function () { return this._ctlBroker_MyApprovedCustomersOnStop; }, set_ctlBroker_MyApprovedCustomersOnStop: function (v) { if (this._ctlBroker_MyApprovedCustomersOnStop !== v) this._ctlBroker_MyApprovedCustomersOnStop = v; },
    get_ctlBroker_ReceivedOrders: function () { return this._ctlBroker_ReceivedOrders; }, set_ctlBroker_ReceivedOrders: function (v) { if (this._ctlBroker_ReceivedOrders !== v) this._ctlBroker_ReceivedOrders = v; },
    get_ctlBroker_MyRecentlyShippedOrders: function () { return this._ctlBroker_MyRecentlyShippedOrders; }, set_ctlBroker_MyRecentlyShippedOrders: function (v) { if (this._ctlBroker_MyRecentlyShippedOrders !== v) this._ctlBroker_MyRecentlyShippedOrders = v; },
    get_ctlBroker_MyOpenSalesOrders: function () { return this._ctlBroker_MyOpenSalesOrders; }, set_ctlBroker_MyOpenSalesOrders: function (v) { if (this._ctlBroker_MyOpenSalesOrders !== v) this._ctlBroker_MyOpenSalesOrders = v; },
    get_ctlBroker_MyOpenPurchaseOrders: function () { return this._ctlBroker_MyOpenPurchaseOrders; }, set_ctlBroker_MyOpenPurchaseOrders: function (v) { if (this._ctlBroker_MyOpenPurchaseOrders !== v) this._ctlBroker_MyOpenPurchaseOrders = v; },
    get_ctlBroker_MyOpenQuotes: function () { return this._ctlBroker_MyOpenQuotes; }, set_ctlBroker_MyOpenQuotes: function (v) { if (this._ctlBroker_MyOpenQuotes !== v) this._ctlBroker_MyOpenQuotes = v; },
    get_ctlBroker_MyOpenRequirements: function () { return this._ctlBroker_MyOpenRequirements; }, set_ctlBroker_MyOpenRequirements: function (v) { if (this._ctlBroker_MyOpenRequirements !== v) this._ctlBroker_MyOpenRequirements = v; },
    get_ctlBroker_MyToDoList: function () { return this._ctlBroker_MyToDoList; }, set_ctlBroker_MyToDoList: function (v) { if (this._ctlBroker_MyToDoList !== v) this._ctlBroker_MyToDoList = v; },
    get_ctlPurchasing_ReceivedOrders: function () { return this._ctlPurchasing_ReceivedOrders; }, set_ctlPurchasing_ReceivedOrders: function (v) { if (this._ctlPurchasing_ReceivedOrders !== v) this._ctlPurchasing_ReceivedOrders = v; },
    get_ctlPurchasing_MyToDoList: function () { return this._ctlPurchasing_MyToDoList; }, set_ctlPurchasing_MyToDoList: function (v) { if (this._ctlPurchasing_MyToDoList !== v) this._ctlPurchasing_MyToDoList = v; },
    get_ctlPurchasing_MyOpenPurchaseOrders: function () { return this._ctlPurchasing_MyOpenPurchaseOrders; }, set_ctlPurchasing_MyOpenPurchaseOrders: function (v) { if (this._ctlPurchasing_MyOpenPurchaseOrders !== v) this._ctlPurchasing_MyOpenPurchaseOrders = v; },
    get_ctlSales_MyStatistics: function () { return this._ctlSales_MyStatistics; }, set_ctlSales_MyStatistics: function (v) { if (this._ctlSales_MyStatistics !== v) this._ctlSales_MyStatistics = v; },
    get_ctlSales_MyToDoList: function () { return this._ctlSales_MyToDoList; }, set_ctlSales_MyToDoList: function (v) { if (this._ctlSales_MyToDoList !== v) this._ctlSales_MyToDoList = v; },
    get_ctlSales_MyApprovedCustomersOnStop: function () { return this._ctlSales_MyApprovedCustomersOnStop; }, set_ctlSales_MyApprovedCustomersOnStop: function (v) { if (this._ctlSales_MyApprovedCustomersOnStop !== v) this._ctlSales_MyApprovedCustomersOnStop = v; },
    get_ctlSales_MyRecentlyShippedOrders: function () { return this._ctlSales_MyRecentlyShippedOrders; }, set_ctlSales_MyRecentlyShippedOrders: function (v) { if (this._ctlSales_MyRecentlyShippedOrders !== v) this._ctlSales_MyRecentlyShippedOrders = v; },
    get_ctlSales_MyOpenSalesOrders: function () { return this._ctlSales_MyOpenSalesOrders; }, set_ctlSales_MyOpenSalesOrders: function (v) { if (this._ctlSales_MyOpenSalesOrders !== v) this._ctlSales_MyOpenSalesOrders = v; },
    get_ctlSales_MyOpenQuotes: function () { return this._ctlSales_MyOpenQuotes; }, set_ctlSales_MyOpenQuotes: function (v) { if (this._ctlSales_MyOpenQuotes !== v) this._ctlSales_MyOpenQuotes = v; },
    get_ctlSales_MyOpenRequirements: function () { return this._ctlSales_MyOpenRequirements; }, set_ctlSales_MyOpenRequirements: function (v) { if (this._ctlSales_MyOpenRequirements !== v) this._ctlSales_MyOpenRequirements = v; },
    get_ctlWarehouse_SalesOrdersReadyToShip: function () { return this._ctlWarehouse_SalesOrdersReadyToShip; }, set_ctlWarehouse_SalesOrdersReadyToShip: function (v) { if (this._ctlWarehouse_SalesOrdersReadyToShip !== v) this._ctlWarehouse_SalesOrdersReadyToShip = v; },
    get_ctlWarehouse_TodaysShippedOrders: function () { return this._ctlWarehouse_TodaysShippedOrders; }, set_ctlWarehouse_TodaysShippedOrders: function (v) { if (this._ctlWarehouse_TodaysShippedOrders !== v) this._ctlWarehouse_TodaysShippedOrders = v; },
    get_ctlWarehouse_MyToDoList: function () { return this._ctlWarehouse_MyToDoList; }, set_ctlWarehouse_MyToDoList: function (v) { if (this._ctlWarehouse_MyToDoList !== v) this._ctlWarehouse_MyToDoList = v; },
    get_ctlWarehouse_AllPurchaseOrdersDueIn: function () { return this._ctlWarehouse_AllPurchaseOrdersDueIn; }, set_ctlWarehouse_AllPurchaseOrdersDueIn: function (v) { if (this._ctlWarehouse_AllPurchaseOrdersDueIn !== v) this._ctlWarehouse_AllPurchaseOrdersDueIn = v; },
    get_ctlWarehouse_MyApprovedCustomersOnStop: function () { return this._ctlWarehouse_MyApprovedCustomersOnStop; }, set_ctlWarehouse_MyApprovedCustomersOnStop: function (v) { if (this._ctlWarehouse_MyApprovedCustomersOnStop !== v) this._ctlWarehouse_MyApprovedCustomersOnStop = v; },
    // [001] code start here
    get_ctlToday_Bom: function () { return this._ctlToday_Bom; }, set_ctlToday_Bom: function (v) { if (this._ctlToday_Bom !== v) this._ctlToday_Bom = v; },
    get_ctlToday_BomManager: function () { return this._ctlToday_BomManager; }, set_ctlToday_BomManager: function (v) { if (this._ctlToday_BomManager !== v) this._ctlToday_BomManager = v; },
    // [001] code end here
    // [002] code start here
    get_ctlToday_TodayOpenPurchaseOrders: function () { return this._ctlToday_TodayOpenPurchaseOrders; }, set_ctlToday_TodayOpenPurchaseOrders: function (v) { if (this._ctlToday_TodayOpenPurchaseOrders !== v) this._ctlToday_TodayOpenPurchaseOrders = v; },
    // [002] code end here
    // [003] code start here
    get_ctlToday_UnProcessSalesOrders: function () { return this._ctlToday_UnProcessSalesOrders; }, set_ctlToday_UnProcessSalesOrders: function (v) { if (this._ctlToday_UnProcessSalesOrders !== v) this._ctlToday_UnProcessSalesOrders = v; },
    // [003] code end here

    get_ctlToday_UncheckedIPO: function () { return this._ctlToday_UncheckedIPO; }, set_ctlToday_UncheckedIPO: function (v) { if (this._ctlToday_UncheckedIPO !== v) this._ctlToday_UncheckedIPO = v; },
    get_ctlToday_OpenSupplierPOApproval: function () { return this._ctlToday_OpenSupplierPOApproval; }, set_ctlToday_OpenSupplierPOApproval: function (v) { if (this._ctlToday_OpenSupplierPOApproval !== v) this._ctlToday_OpenSupplierPOApproval = v; },
    //[002] start
    get_ctlBroker_CustomerOrderValue: function () { return this._ctlBroker_CustomerOrderValue; }, set_ctlBroker_CustomerOrderValue: function (v) { if (this._ctlBroker_CustomerOrderValue !== v) this._ctlBroker_CustomerOrderValue = v; },
    get_ctlSales_CustomerOrderValue: function () { return this._ctlSales_CustomerOrderValue; }, set_ctlSales_CustomerOrderValue: function (v) { if (this._ctlSales_CustomerOrderValue !== v) this._ctlSales_CustomerOrderValue = v; },
    //[002] end
    //[003] start
    get_ctlPurchasing_PODeliveryStatus: function () { return this._ctlPurchasing_PODeliveryStatus; }, set_ctlPurchasing_PODeliveryStatus: function (v) { if (this._ctlPurchasing_PODeliveryStatus !== v) this._ctlPurchasing_PODeliveryStatus = v; },
    //[003] end
    //get_ddlClientByMaster: function () { return this._ddlClientByMaster; }, set_ddlClientByMaster: function (v) { if (this._ddlClientByMaster !== v) this._ddlClientByMaster = v; }, 
    //get_intMasterLoginNo: function () { return this._intMasterLoginNo; }, set_intMasterLoginNo: function (v) { if (this._intMasterLoginNo !== v) this._intMasterLoginNo = v; },
    //get_intDefaultClientNo: function () { return this._intDefaultClientNo; }, set_intDefaultClientNo: function (v) { if (this._intDefaultClientNo !== v) this._intDefaultClientNo = v; },
    //[004] start
    get_ctlToday_MyGIQueries: function () { return this._ctlToday_MyGIQueries; }, set_ctlToday_MyGIQueries: function (v) { if (this._ctlToday_MyGIQueries !== v) this._ctlToday_MyGIQueries = v; },
    get_ctlToday_MyQualityGIQueries: function () { return this._ctlToday_MyQualityGIQueries; }, set_ctlToday_MyQualityGIQueries: function (v) { if (this._ctlToday_MyQualityGIQueries !== v) this._ctlToday_MyQualityGIQueries = v; },
    get_ctlToday_PowerBIActivity: function () { return this._ctlToday_PowerBIActivity; }, set_ctlToday_PowerBIActivity: function (v) { if (this._ctlToday_PowerBIActivity !== v) this._ctlToday_PowerBIActivity = v; },
    get_ctlToday_PowerBiSalesDashboard: function () { return this._ctlToday_PowerBiSalesDashboard; }, set_ctlToday_PowerBiSalesDashboard: function (v) { if (this._ctlToday_PowerBiSalesDashboard !== v) this._ctlToday_PowerBiSalesDashboard = v; },
    //[004] end
    initialize: function () {
        Rebound.GlobalTrader.Site.Pages.Default.callBaseMethod(this, "initialize");
    },

    goInit: function () {
        if (this._ctlTopLinks) {
            this._chkMyself.addClick(Function.createDelegate(this, this.selectMyselfOrOther));
            this._chkIncludeCredit.addClick(Function.createDelegate(this, this.selectIncludeCredit))
            this._ddlEmployee.addChanged(Function.createDelegate(this, this.selectLogin));
            //this._ddlClientByMaster.addChanged(Function.createDelegate(this, this.LoginWithOtherClient));

            //if (this._ddlClientByMaster) {
            //    this._ddlClientByMaster._intMasterLoginNo = this._intMasterLoginNo;
            //    this._ddlClientByMaster.getData();
            //    this._ddlClientByMaster.setValue(this._intDefaultClientNo);
            //}
        }
        this.changeTab(this._intCurrentTab);

        // For pop menu
        //callModel();
        //end

    },

    dispose: function () {
        if (this.isDisposed) return;
        if (this._ctlTopLinks) this._ctlTopLinks.dispose();
        if (this._ctlPageTitle) this._ctlPageTitle.dispose();
        if (this._chkMyself) this._chkMyself.dispose();
        if (this._chkIncludeCredit) this._chkIncludeCredit.dispose();

        if (this._ctlToday_TopSalespersons) this._ctlToday_TopSalespersons.dispose();
        if (this._ctlToday_MyRecentActivity) this._ctlToday_MyRecentActivity.dispose();
        if (this._ctlToday_PartsOrdered) this._ctlToday_PartsOrdered.dispose();
        if (this._ctlToday_MyToDoList) this._ctlToday_MyToDoList.dispose();
        if (this._ctlToday_MyMessages) this._ctlToday_MyMessages.dispose();
        if (this._ctlAccounts_AllStatistics) this._ctlAccounts_AllStatistics.dispose();
        if (this._ctlAccounts_MyToDoList) this._ctlAccounts_MyToDoList.dispose();
        if (this._ctlAccounts_ApprovedCustomersOnStop) this._ctlAccounts_ApprovedCustomersOnStop.dispose();
        if (this._ctlAccounts_TodaysShippedOrders) this._ctlAccounts_TodaysShippedOrders.dispose();
        if (this._ctlAccounts_ReceivedOrders) this._ctlAccounts_ReceivedOrders.dispose();
        if (this._ctlBroker_MyStatistics) this._ctlBroker_MyStatistics.dispose();
        if (this._ctlBroker_MyApprovedCustomersOnStop) this._ctlBroker_MyApprovedCustomersOnStop.dispose();
        if (this._ctlBroker_ReceivedOrders) this._ctlBroker_ReceivedOrders.dispose();
        if (this._ctlBroker_MyRecentlyShippedOrders) this._ctlBroker_MyRecentlyShippedOrders.dispose();
        if (this._ctlBroker_MyOpenSalesOrders) this._ctlBroker_MyOpenSalesOrders.dispose();
        if (this._ctlBroker_MyOpenPurchaseOrders) this._ctlBroker_MyOpenPurchaseOrders.dispose();
        if (this._ctlBroker_MyOpenQuotes) this._ctlBroker_MyOpenQuotes.dispose();
        if (this._ctlBroker_MyToDoList) this._ctlBroker_MyToDoList.dispose();
        if (this._ctlBroker_MyOpenRequirements) this._ctlBroker_MyOpenRequirements.dispose();
        if (this._ctlPurchasing_ReceivedOrders) this._ctlPurchasing_ReceivedOrders.dispose();
        if (this._ctlPurchasing_MyToDoList) this._ctlPurchasing_MyToDoList.dispose();
        if (this._ctlPurchasing_MyOpenPurchaseOrders) this._ctlPurchasing_MyOpenPurchaseOrders.dispose();
        if (this._ctlSales_MyStatistics) this._ctlSales_MyStatistics.dispose();
        if (this._ctlSales_MyToDoList) this._ctlSales_MyToDoList.dispose();
        if (this._ctlSales_MyApprovedCustomersOnStop) this._ctlSales_MyApprovedCustomersOnStop.dispose();
        if (this._ctlSales_MyRecentlyShippedOrders) this._ctlSales_MyRecentlyShippedOrders.dispose();
        if (this._ctlSales_MyOpenSalesOrders) this._ctlSales_MyOpenSalesOrders.dispose();
        if (this._ctlSales_MyOpenQuotes) this._ctlSales_MyOpenQuotes.dispose();
        if (this._ctlSales_MyOpenRequirements) this._ctlSales_MyOpenRequirements.dispose();
        if (this._ctlWarehouse_SalesOrdersReadyToShip) this._ctlWarehouse_SalesOrdersReadyToShip.dispose();
        if (this._ctlWarehouse_TodaysShippedOrders) this._ctlWarehouse_TodaysShippedOrders.dispose();
        if (this._ctlWarehouse_MyToDoList) this._ctlWarehouse_MyToDoList.dispose();
        if (this._ctlWarehouse_AllPurchaseOrdersDueIn) this._ctlWarehouse_AllPurchaseOrdersDueIn.dispose();
        if (this._ctlWarehouse_MyApprovedCustomersOnStop) this._ctlWarehouse_MyApprovedCustomersOnStop.dispose();
        //[001] code start here
        if (this._ctlToday_Bom) this._ctlToday_Bom.dispose();
        if (this._ctlToday_BomManager) this._ctlToday_BomManager.dispose();
        //[001] code end here
        //[002] code start here
        if (this._ctlToday_TodayOpenPurchaseOrders) this._ctlToday_TodayOpenPurchaseOrders.dispose();
        //[002] code end here
        //[003] code start here
        if (this._ctlToday_UnProcessSalesOrders) this._ctlToday_UnProcessSalesOrders.dispose();
        //[003] code end here
        if (this._ctlToday_UncheckedIPO) this._ctlToday_UncheckedIPO.dispose();
        if (this._ctlToday_OpenSupplierPOApproval) this._ctlToday_OpenSupplierPOApproval.dispose();
        this._ctlTopLinks = null;
        this._ctlPageTitle = null;
        this._chkMyself = null;
        this._chkIncludeCredit = null;
        this._pnlChooseUser = null;
        this._ddlEmployee = null;
        this._pnlToday = null;
        this._pnlAccounts = null;
        this._pnlBroker = null;
        this._pnlPurchasing = null;
        this._pnlSales = null;
        this._pnlWarehouse = null;
        this._ctlToday_TopSalespersons = null;
        this._ctlToday_MyRecentActivity = null;
        this._ctlToday_PartsOrdered = null;
        this._ctlToday_MyToDoList = null;
        this._ctlToday_MyMessages = null;
        this._ctlAccounts_AllStatistics = null;
        this._ctlAccounts_MyToDoList = null;
        this._ctlAccounts_ApprovedCustomersOnStop = null;
        this._ctlAccounts_TodaysShippedOrders = null;
        this._ctlAccounts_ReceivedOrders = null;
        this._ctlBroker_MyStatistics = null;
        this._ctlBroker_MyApprovedCustomersOnStop = null;
        this._ctlBroker_ReceivedOrders = null;
        this._ctlBroker_MyRecentlyShippedOrders = null;
        this._ctlBroker_MyOpenSalesOrders = null;
        this._ctlBroker_MyOpenPurchaseOrders = null;
        this._ctlBroker_MyOpenQuotes = null;
        this._ctlBroker_MyToDoList = null;
        this.ctlToday_PowerBIActivity = null;
        this._ctlBroker_MyOpenRequirements = null;
        this._ctlPurchasing_ReceivedOrders = null;
        this._ctlPurchasing_MyToDoList = null;
        this._ctlPurchasing_MyOpenPurchaseOrders = null;
        this._ctlSales_MyStatistics = null;
        this._ctlSales_MyToDoList = null;
        this._ctlSales_MyApprovedCustomersOnStop = null;
        this._ctlSales_MyRecentlyShippedOrders = null;
        this._ctlSales_MyOpenSalesOrders = null;
        this._ctlSales_MyOpenQuotes = null;
        this._ctlSales_MyOpenRequirements = null;
        this._ctlWarehouse_SalesOrdersReadyToShip = null;
        this._ctlWarehouse_TodaysShippedOrders = null;
        this._ctlWarehouse_MyToDoList = null;
        this._ctlWarehouse_AllPurchaseOrdersDueIn = null;
        this._ctlWarehouse_MyApprovedCustomersOnStop = null;
        //[001] code start here
        this._ctlToday_Bom = null;
        this._ctlToday_BomManager = null;
        //[001] code end here
        //[002] code start here
        this._ctlToday_TodayOpenPurchaseOrders = null;
        //[002] code end here
        //[003] code start here
        this._ctlToday_UnProcessSalesOrders = null;
        //[003] code end here
        //[002] start
        if (this._ctlBroker_CustomerOrderValue) this._ctlBroker_CustomerOrderValue.dispose();
        this._ctlBroker_CustomerOrderValue = null;
        if (this._ctlSales_CustomerOrderValue) this._ctlSales_CustomerOrderValue.dispose();
        this._ctlSales_CustomerOrderValue = null;
        //[002] end
        //[003] start
        if (this._ctlPurchasing_PODeliveryStatus) this._ctlPurchasing_PODeliveryStatus.dispose();
        this._ctlPurchasing_PODeliveryStatus = null;
        //[003] end
        this._ctlToday_UncheckedIPO = null;
        this._ctlToday_OpenSupplierPOApproval = null;
        this._ddlClientByMaster = null;
        if (this._ctlToday_MyGIQueries) this._ctlToday_MyGIQueries.dispose();
        this._ctlToday_MyGIQueries = null;
        if (this._ctlToday_MyQualityGIQueries) this._ctlToday_MyQualityGIQueries.dispose();
        this._ctlToday_MyQualityGIQueries = null;
        if (this._ctlToday_PowerBIActivity) this._ctlToday_PowerBIActivity.dispose();
        this._ctlToday_PowerBIActivity = null;

        if (this.get_ctlToday_PowerBiSalesDashboard) this._ctlToday_PowerBiSalesDashboard.dispose();
        this._ctlToday_PowerBiSalesDashboard = null;
        Rebound.GlobalTrader.Site.Pages.Default.callBaseMethod(this, "dispose");
    },

    changeTab: function (intTab) {
        this._intCurrentTab = intTab;
        this._ctlPageTitle.selectTab(intTab);
        if (this._ctlTopLinks) this._ctlTopLinks.show(intTab != $R_ENUM$HomeTabList.Accounts);
        $R_FN.showElement(this._pnlToday, intTab == $R_ENUM$HomeTabList.Today);
        $R_FN.showElement(this._pnlAccounts, intTab == $R_ENUM$HomeTabList.Accounts);
        $R_FN.showElement(this._pnlBroker, intTab == $R_ENUM$HomeTabList.Broker);
        $R_FN.showElement(this._pnlPurchasing, intTab == $R_ENUM$HomeTabList.Purchasing);
        $R_FN.showElement(this._pnlSales, intTab == $R_ENUM$HomeTabList.Sales);
        $R_FN.showElement(this._pnlWarehouse, intTab == $R_ENUM$HomeTabList.Warehouse);
        $R_FN.showElement(this._PnlBI, intTab == $R_ENUM$HomeTabList.PowerBIDash);

        switch (this._intCurrentTab) {
            case $R_ENUM$HomeTabList.Today:
                if (this._ctlToday_MyRecentActivity) this._ctlToday_MyRecentActivity.getData();
                if (this._ctlToday_TopSalespersons) this._ctlToday_TopSalespersons.getData();
                //if (this._ctlToday_MyRecentActivity) this._ctlToday_MyRecentActivity.getData(); 
                if (this._ctlToday_PartsOrdered) this._ctlToday_PartsOrdered.getData();
                if (this._ctlToday_MyToDoList) this._ctlToday_MyToDoList.getData();
                if (this._ctlToday_MyMessages) this._ctlToday_MyMessages.getData();
                //[001] code start here
                if (this._ctlToday_Bom) this._ctlToday_Bom.getData();
                if (this._ctlToday_BomManager) this._ctlToday_BomManager.getData();
                //[001] code end here
                //[002] code start here
                if (this._ctlToday_TodayOpenPurchaseOrders) this._ctlToday_TodayOpenPurchaseOrders.getData();
                //[002] code end here
                //[003] code start here
                if (this._ctlToday_UnProcessSalesOrders) this._ctlToday_UnProcessSalesOrders.getData();
                //[003] code end here
                if (this._ctlToday_UncheckedIPO) this._ctlToday_UncheckedIPO.getData();
                if (this._ctlToday_OpenSupplierPOApproval) this._ctlToday_OpenSupplierPOApproval.getData();
                if (this._ctlToday_MyGIQueries) this._ctlToday_MyGIQueries.getData();
                if (this._ctlToday_PowerBIActivity) this._ctlToday_PowerBIActivity.getData();
                if (this._ctlToday_MyQualityGIQueries) this._ctlToday_MyQualityGIQueries.getData();
                //if (this._ctlToday_PowerBiSalesDashboard) this._ctlToday_PowerBiSalesDashboard.setDefaultTextinTable();
                break;
            case $R_ENUM$HomeTabList.Accounts:
                if (this._ctlAccounts_AllStatistics) this._ctlAccounts_AllStatistics.getData();
                if (this._ctlAccounts_MyToDoList) this._ctlAccounts_MyToDoList.getData();
                if (this._ctlAccounts_ApprovedCustomersOnStop) this._ctlAccounts_ApprovedCustomersOnStop.getData();
                if (this._ctlAccounts_TodaysShippedOrders) this._ctlAccounts_TodaysShippedOrders.getData();
                if (this._ctlAccounts_ReceivedOrders) this._ctlAccounts_ReceivedOrders.getData();
                break;
            case $R_ENUM$HomeTabList.Broker:
                if (this._ctlBroker_MyStatistics) this._ctlBroker_MyStatistics.getData();
                if (this._ctlBroker_MyToDoList) this._ctlBroker_MyToDoList.getData();
                if (this._ctlBroker_MyApprovedCustomersOnStop) this._ctlBroker_MyApprovedCustomersOnStop.getData();
                if (this._ctlBroker_ReceivedOrders) this._ctlBroker_ReceivedOrders.getData();
                if (this._ctlBroker_MyRecentlyShippedOrders) this._ctlBroker_MyRecentlyShippedOrders.getData();
                if (this._ctlBroker_MyOpenSalesOrders) this._ctlBroker_MyOpenSalesOrders.getData();
                if (this._ctlBroker_MyOpenPurchaseOrders) this._ctlBroker_MyOpenPurchaseOrders.getData();
                if (this._ctlBroker_MyOpenQuotes) this._ctlBroker_MyOpenQuotes.getData();
                if (this._ctlBroker_MyOpenRequirements) this._ctlBroker_MyOpenRequirements.getData();
                //[002] start
                //alert(this._ctlBroker_CustomerOrderValue);
                if (this._ctlBroker_CustomerOrderValue) this._ctlBroker_CustomerOrderValue.getData();
                //if (this._ctlSales_CustomerOrderValue) this._ctlSales_CustomerOrderValue.getData();
                //[002] end
                break;
            case $R_ENUM$HomeTabList.Purchasing:
                if (this._ctlPurchasing_ReceivedOrders) this._ctlPurchasing_ReceivedOrders.getData();
                if (this._ctlPurchasing_MyToDoList) this._ctlPurchasing_MyToDoList.getData();
                if (this._ctlPurchasing_MyOpenPurchaseOrders) this._ctlPurchasing_MyOpenPurchaseOrders.getData();
                //[003] start
                if (this._ctlPurchasing_PODeliveryStatus) this._ctlPurchasing_PODeliveryStatus.getData();
                //[003] end
                break;
            case $R_ENUM$HomeTabList.Sales:
                if (this._ctlSales_MyStatistics) this._ctlSales_MyStatistics.getData();
                if (this._ctlSales_MyToDoList) this._ctlSales_MyToDoList.getData();
                if (this._ctlSales_MyApprovedCustomersOnStop) this._ctlSales_MyApprovedCustomersOnStop.getData();
                if (this._ctlSales_MyRecentlyShippedOrders) this._ctlSales_MyRecentlyShippedOrders.getData();
                if (this._ctlSales_MyOpenSalesOrders) this._ctlSales_MyOpenSalesOrders.getData();
                if (this._ctlSales_MyOpenQuotes) this._ctlSales_MyOpenQuotes.getData();
                if (this._ctlSales_MyOpenRequirements) this._ctlSales_MyOpenRequirements.getData();
                //[002] start
                if (this._ctlSales_CustomerOrderValue) this._ctlSales_CustomerOrderValue.getData();
                //[002] end
                break;
            case $R_ENUM$HomeTabList.Warehouse:
                if (this._ctlWarehouse_SalesOrdersReadyToShip) this._ctlWarehouse_SalesOrdersReadyToShip.getData();
                if (this._ctlWarehouse_TodaysShippedOrders) this._ctlWarehouse_TodaysShippedOrders.getData();
                if (this._ctlWarehouse_MyToDoList) this._ctlWarehouse_MyToDoList.getData();
                if (this._ctlWarehouse_AllPurchaseOrdersDueIn) this._ctlWarehouse_AllPurchaseOrdersDueIn.getData();
                if (this._ctlWarehouse_MyApprovedCustomersOnStop) this._ctlWarehouse_MyApprovedCustomersOnStop.getData();

                break;
            case $R_ENUM$HomeTabList.PowerBIDash:
                getToken().call(this);
                break;
        }
    },
    selectMyselfOrOther: function () {

        // document.getElementById("modal8a").click();
        if (!this._ctlTopLinks) return;
        $R_FN.showElement(this._pnlChooseUser, !this._chkMyself._blnChecked);
        if (this._chkMyself._blnChecked) {
            if (this._intDifferentUserID > 0) {
                if (this._ctlToday_MyRecentActivity) this._ctlToday_MyRecentActivity.revertUserToCurrentLogin();
                if (this._ctlToday_PowerBIActivity) this._ctlToday_PowerBIActivity.revertUserToCurrentLogin();
                //if (this._ctlToday_PowerBiSalesDashboard) this._ctlToday_PowerBiSalesDashboard.setDefaultTextinTable();
                //[001] start here
                //	if (this._ctlToday_PartsOrdered) this._ctlToday_PartsOrdered.revertUserToCurrentLogin();
                //[001] End Here
                if (this._ctlBroker_MyStatistics) this._ctlBroker_MyStatistics.revertUserToCurrentLogin();
                if (this._ctlBroker_MyApprovedCustomersOnStop) this._ctlBroker_MyApprovedCustomersOnStop.revertUserToCurrentLogin();
                if (this._ctlBroker_MyOpenSalesOrders) this._ctlBroker_MyOpenSalesOrders.revertUserToCurrentLogin();
                if (this._ctlBroker_MyOpenPurchaseOrders) this._ctlBroker_MyOpenPurchaseOrders.revertUserToCurrentLogin();
                if (this._ctlBroker_MyRecentlyShippedOrders) this._ctlBroker_MyRecentlyShippedOrders.revertUserToCurrentLogin();
                if (this._ctlBroker_MyOpenQuotes) this._ctlBroker_MyOpenQuotes.revertUserToCurrentLogin();
                if (this._ctlBroker_MyOpenRequirements) this._ctlBroker_MyOpenRequirements.revertUserToCurrentLogin();
                if (this._ctlSales_MyStatistics) this._ctlSales_MyStatistics.revertUserToCurrentLogin();
                if (this._ctlSales_MyApprovedCustomersOnStop) this._ctlSales_MyApprovedCustomersOnStop.revertUserToCurrentLogin();
                if (this._ctlSales_MyOpenSalesOrders) this._ctlSales_MyOpenSalesOrders.revertUserToCurrentLogin();
                if (this._ctlSales_MyRecentlyShippedOrders) this._ctlSales_MyRecentlyShippedOrders.revertUserToCurrentLogin();
                if (this._ctlSales_MyOpenQuotes) this._ctlSales_MyOpenQuotes.revertUserToCurrentLogin();
                if (this._ctlSales_MyOpenRequirements) this._ctlSales_MyOpenRequirements.revertUserToCurrentLogin();
                if (this._ctlWarehouse_MyApprovedCustomersOnStop) this._ctlWarehouse_MyApprovedCustomersOnStop.revertUserToCurrentLogin();
                if (this._ctlPurchasing_MyOpenPurchaseOrders) this._ctlPurchasing_MyOpenPurchaseOrders.revertUserToCurrentLogin();
                //[002] start
                if (this._ctlBroker_CustomerOrderValue) this._ctlBroker_CustomerOrderValue.revertUserToCurrentLogin();
                if (this._ctlSales_CustomerOrderValue) this._ctlSales_CustomerOrderValue.revertUserToCurrentLogin();
                //[002] end
                switch (this._intCurrentTab) {
                    case $R_ENUM$HomeTabList.Today:
                        if (this._ctlToday_MyRecentActivity) this._ctlToday_MyRecentActivity.getData();
                        if (this._ctlToday_PowerBIActivity) this._ctlToday_PowerBIActivity.getData();
                        //if (this._ctlToday_PowerBiSalesDashboard) this._ctlToday_PowerBiSalesDashboard.setDefaultTextinTable();
                        //[001] start here 
                        //if (this._ctlToday_PartsOrdered) this._ctlToday_PartsOrdered.getData();
                        //[001] End Here
                        break;
                    case $R_ENUM$HomeTabList.Broker:
                        if (this._ctlBroker_MyStatistics) this._ctlBroker_MyStatistics.getData();
                        if (this._ctlBroker_MyApprovedCustomersOnStop) this._ctlBroker_MyApprovedCustomersOnStop.getData();
                        if (this._ctlBroker_MyOpenSalesOrders) this._ctlBroker_MyOpenSalesOrders.getData();
                        if (this._ctlBroker_MyRecentlyShippedOrders) this._ctlBroker_MyRecentlyShippedOrders.getData();
                        if (this._ctlBroker_MyOpenPurchaseOrders) this._ctlBroker_MyOpenPurchaseOrders.getData();
                        if (this._ctlBroker_MyOpenQuotes) this._ctlBroker_MyOpenQuotes.getData();
                        if (this._ctlBroker_MyOpenRequirements) this._ctlBroker_MyOpenRequirements.getData();
                        //[002] start
                        if (this._ctlBroker_CustomerOrderValue) this._ctlBroker_CustomerOrderValue.getData();
                        //[002] end
                        break;
                    case $R_ENUM$HomeTabList.Sales:
                        if (this._ctlSales_MyStatistics) this._ctlSales_MyStatistics.getData();
                        if (this._ctlSales_MyApprovedCustomersOnStop) this._ctlSales_MyApprovedCustomersOnStop.getData();
                        if (this._ctlSales_MyOpenSalesOrders) this._ctlSales_MyOpenSalesOrders.getData();
                        if (this._ctlSales_MyRecentlyShippedOrders) this._ctlSales_MyRecentlyShippedOrders.getData();
                        if (this._ctlSales_MyOpenRequirements) this._ctlSales_MyOpenRequirements.getData();
                        if (this._ctlSales_MyOpenQuotes) this._ctlSales_MyOpenQuotes.getData();
                        //[002] start
                        if (this._ctlSales_CustomerOrderValue) this._ctlSales_CustomerOrderValue.getData();
                        //[002] end
                        break;
                    case $R_ENUM$HomeTabList.Warehouse:
                        if (this._ctlWarehouse_MyApprovedCustomersOnStop) this._ctlWarehouse_MyApprovedCustomersOnStop.getData();
                        break;
                    case $R_ENUM$HomeTabList.Purchasing:
                        if (this._ctlPurchasing_MyOpenPurchaseOrders) this._ctlPurchasing_MyOpenPurchaseOrders.getData();
                        break;
                }
            }
            this._intDifferentUserID = 0;
        } else {
            if (!this._ddlEmployee.isSetAsNoValue()) this.selectLogin();
        }
    },
    LoginWithOtherClient: function () {
        //  alert("Hi");
        //this.stopCheckForMessages();
        //clearTimeout(this._intLoginTimeoutID);
        location.href = String.format("{0}?ret={1}&CID={2}", "LoginWithOtherClient.aspx", escape(location.href), this._ddlClientByMaster.getValue());
    },

    selectLogin: function () {
        if (this._ddlEmployee.isSetAsNoValue()) return;
        if (this._ddlEmployee._blnInDataCall) return;
        var strName = this._ddlEmployee.getText();
        var intID = this._ddlEmployee.getValue();
        var includedCredit = this._chkIncludeCredit._blnChecked
        this._intDifferentUserID = intID;
        if (this._ctlToday_MyRecentActivity) this._ctlToday_MyRecentActivity.changeUser(intID, strName);
        if (this._ctlToday_PowerBIActivity) this._ctlToday_PowerBIActivity.changeUser(intID, strName);
        //[001] Start Here
        //if (this._ctlToday_PartsOrdered) this._ctlToday_PartsOrdered.changeUser(intID, strName);
        //[001] End Here
        if (this._ctlBroker_MyStatistics) this._ctlBroker_MyStatistics.changeUser(intID, strName, includedCredit);
        if (this._ctlBroker_MyApprovedCustomersOnStop) this._ctlBroker_MyApprovedCustomersOnStop.changeUser(intID, strName);
        if (this._ctlBroker_MyOpenSalesOrders) this._ctlBroker_MyOpenSalesOrders.changeUser(intID, strName);
        if (this._ctlBroker_MyOpenPurchaseOrders) this._ctlBroker_MyOpenPurchaseOrders.changeUser(intID, strName);
        if (this._ctlBroker_MyRecentlyShippedOrders) this._ctlBroker_MyRecentlyShippedOrders.changeUser(intID, strName);
        if (this._ctlBroker_MyOpenQuotes) this._ctlBroker_MyOpenQuotes.changeUser(intID, strName);
        if (this._ctlBroker_MyOpenRequirements) this._ctlBroker_MyOpenRequirements.changeUser(intID, strName);
        if (this._ctlSales_MyStatistics) this._ctlSales_MyStatistics.changeUser(intID, strName);
        if (this._ctlSales_MyApprovedCustomersOnStop) this._ctlSales_MyApprovedCustomersOnStop.changeUser(intID, strName);
        if (this._ctlSales_MyOpenSalesOrders) this._ctlSales_MyOpenSalesOrders.changeUser(intID, strName);
        if (this._ctlSales_MyRecentlyShippedOrders) this._ctlSales_MyRecentlyShippedOrders.changeUser(intID, strName);
        if (this._ctlSales_MyOpenQuotes) this._ctlSales_MyOpenQuotes.changeUser(intID, strName);
        if (this._ctlSales_MyOpenRequirements) this._ctlSales_MyOpenRequirements.changeUser(intID, strName);
        if (this._ctlWarehouse_MyApprovedCustomersOnStop) this._ctlWarehouse_MyApprovedCustomersOnStop.changeUser(intID, strName);
        if (this._ctlPurchasing_MyOpenPurchaseOrders) this._ctlPurchasing_MyOpenPurchaseOrders.changeUser(intID, strName);
        //[002] start
        if (this._ctlBroker_CustomerOrderValue) this._ctlBroker_CustomerOrderValue.changeUser(intID, strName);
        if (this._ctlSales_CustomerOrderValue) this._ctlSales_CustomerOrderValue.changeUser(intID, strName);
        //[002] end
        switch (this._intCurrentTab) {
            case $R_ENUM$HomeTabList.Today:
                if (this._ctlToday_MyRecentActivity) this._ctlToday_MyRecentActivity.getData();
                if (this._ctlToday_PowerBIActivity) this._ctlToday_PowerBIActivity.getData();
                //if (this._ctlToday_PowerBiSalesDashboard) this._ctlToday_PowerBiSalesDashboard.setDefaultTextinTable();
                break;
            case $R_ENUM$HomeTabList.Broker:
                if (this._ctlBroker_MyStatistics) this._ctlBroker_MyStatistics.getData();
                if (this._ctlBroker_MyApprovedCustomersOnStop) this._ctlBroker_MyApprovedCustomersOnStop.getData();
                if (this._ctlBroker_MyOpenSalesOrders) this._ctlBroker_MyOpenSalesOrders.getData();
                if (this._ctlBroker_MyRecentlyShippedOrders) this._ctlBroker_MyRecentlyShippedOrders.getData();
                if (this._ctlBroker_MyOpenPurchaseOrders) this._ctlBroker_MyOpenPurchaseOrders.getData();
                if (this._ctlBroker_MyOpenQuotes) this._ctlBroker_MyOpenQuotes.getData();
                if (this._ctlBroker_MyOpenRequirements) this._ctlBroker_MyOpenRequirements.getData();
                //[002] start
                if (this._ctlBroker_CustomerOrderValue) this._ctlBroker_CustomerOrderValue.getData();
                //[002] end
                break;
            case $R_ENUM$HomeTabList.Sales:
                if (this._ctlSales_MyStatistics) this._ctlSales_MyStatistics.getData();
                if (this._ctlSales_MyApprovedCustomersOnStop) this._ctlSales_MyApprovedCustomersOnStop.getData();
                if (this._ctlSales_MyOpenSalesOrders) this._ctlSales_MyOpenSalesOrders.getData();
                if (this._ctlSales_MyRecentlyShippedOrders) this._ctlSales_MyRecentlyShippedOrders.getData();
                if (this._ctlSales_MyOpenRequirements) this._ctlSales_MyOpenRequirements.getData();
                if (this._ctlSales_MyOpenQuotes) this._ctlSales_MyOpenQuotes.getData();
                //[002] start
                if (this._ctlSales_CustomerOrderValue) this._ctlSales_CustomerOrderValue.getData();
                //[002] end
                break;
            case $R_ENUM$HomeTabList.Warehouse:
                if (this._ctlWarehouse_MyApprovedCustomersOnStop) this._ctlWarehouse_MyApprovedCustomersOnStop.getData();
                if (this._ctlPurchasing_MyOpenPurchaseOrders) this._ctlPurchasing_MyOpenPurchaseOrders.getData();
                break;
        }
    },
    selectIncludeCredit: function () {
        if (!this._chkMyself._blnChecked) {
            if (this._ddlEmployee.isSetAsNoValue()) return;
            if (this._ddlEmployee._blnInDataCall) return;
            var strName = this._ddlEmployee.getText();
            var intID = this._ddlEmployee.getValue();
            var includedCredit = this._chkIncludeCredit._blnChecked

            this._intDifferentUserID = intID;
            if (this._ctlBroker_MyStatistics) this._ctlBroker_MyStatistics.changeUser(intID, strName, includedCredit);
            switch (this._intCurrentTab) {
                case $R_ENUM$HomeTabList.Broker:
                    if (this._ctlBroker_MyStatistics) this._ctlBroker_MyStatistics.getData();
                    break;
            }
        }
        else {
            var includedCredit = this._chkIncludeCredit._blnChecked
            if (this._intDifferentUserID > 0) {
                if (this._ctlSales_MyStatistics) this._ctlSales_MyStatistics.revertUserToCurrentLogin();
                switch (this._intCurrentTab) {
                    case $R_ENUM$HomeTabList.Broker:
                        if (this._ctlBroker_MyStatistics) this._ctlBroker_MyStatistics.getData();
                        break;
                }
                this._intDifferentUserID = 0;
            }
            else {
                switch (this._intCurrentTab) {
                    case $R_ENUM$HomeTabList.Broker:
                        if (this._ctlBroker_MyStatistics) this._ctlBroker_MyStatistics.getData();
                        break;
                }
            }
        }

    }

};

Rebound.GlobalTrader.Site.Pages.Default.registerClass("Rebound.GlobalTrader.Site.Pages.Default", Rebound.GlobalTrader.Site.Pages.Content, Sys.IDisposable);
