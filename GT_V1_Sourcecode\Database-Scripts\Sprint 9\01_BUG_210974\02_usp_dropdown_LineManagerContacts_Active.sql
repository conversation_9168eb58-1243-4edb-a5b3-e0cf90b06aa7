﻿SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
ALTER PROCEDURE [dbo].[usp_dropdown_LineManagerContacts]      
@BuyerId   INT=0  ,    
@ClientNo  INT=0  ,  
@UpdateManager BIT=0    
--================================================================================================================        
--Marker             Created By              Date         Comment         
--[001]              Abhinav Saxena          28-01-2022   Add new proc for finding Line Manager Contact.        
/*
TASK      			UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[Bug-210974]		Trung Pham			19-Aug-2024		UPDATE			Just get active member for MailGroup
*/
--================================================================================================================        
AS        
BEGIN   
IF(@UpdateManager=0)  
BEGIN       
IF((SELECT COUNT(dv.Manager) FROM tbLogin lg LEFT OUTER JOIN tbDivision dv ON lg.DivisionNo=dv.DivisionId WHERE lg.LoginId=@BuyerId)>0)      
BEGIN      
--SELECT       
--    dv.Manager AS LineManagerContactId,      
--    lm.EmployeeName AS LineManagerContactName      
--FROM tbLogin lg       
--LEFT OUTER JOIN tbDivision dv ON lg.DivisionNo=dv.DivisionId      
--LEFT OUTER JOIN tbLogin lm ON dv.Manager=lm.LoginId      
--WHERE lg.LoginId=@BuyerId    
IF((SELECT COUNT(1) FROM tbMailGroup WHERE [Name]='Line Manager Approval' AND ClientNo=@ClientNo)>0)     
BEGIN  
SELECT   
LoginNo AS LineManagerContactId,  
lg.EmployeeName AS LineManagerContactName   
FROM tbMailGroupMember mg   
LEFT OUTER JOIN tbLogin lg ON mg.LoginNo=lg.LoginId  
WHERE mg.MailGroupNo=  
(SELECT MailGroupId FROM tbMailGroup WHERE [Name]='Line Manager Approval' AND ClientNo=@ClientNo)    
AND lg.Inactive = 0
END 
 
END       
ELSE      
BEGIN   
IF((SELECT COUNT(1) FROM tbMailGroup WHERE [Name]='Line Manager Approval' AND ClientNo=@ClientNo)>0)     
BEGIN  
SELECT   
LoginNo AS LineManagerContactId,  
lg.EmployeeName AS LineManagerContactName   
FROM tbMailGroupMember mg   
LEFT OUTER JOIN tbLogin lg ON mg.LoginNo=lg.LoginId  
WHERE mg.MailGroupNo=  
(SELECT MailGroupId FROM tbMailGroup WHERE [Name]='Line Manager Approval' AND ClientNo=@ClientNo)    
AND lg.Inactive = 0
END  
END     
END  
ELSE  
BEGIN  
IF((SELECT COUNT(1) FROM tbMailGroup WHERE [Name]='Line Manager Approval' AND ClientNo=@ClientNo)>0)  
BEGIN  
SELECT   
LoginNo AS LineManagerContactId,  
lg.EmployeeName AS LineManagerContactName   
FROM tbMailGroupMember mg   
LEFT OUTER JOIN tbLogin lg ON mg.LoginNo=lg.LoginId  
WHERE mg.MailGroupNo=  
(SELECT MailGroupId FROM tbMailGroup WHERE [Name]='Line Manager Approval' AND ClientNo=@ClientNo)  
AND lg.Inactive = 0
END  
END   
END

