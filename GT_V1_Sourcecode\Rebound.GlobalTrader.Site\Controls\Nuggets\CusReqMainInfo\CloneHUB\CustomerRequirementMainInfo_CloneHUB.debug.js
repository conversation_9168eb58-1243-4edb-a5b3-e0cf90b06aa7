///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference name="~/Controls/IconButton/IconButton.js" />
///<reference name="~/Controls/ReboundTextBox/ReboundTextBox.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
//Marker     changed by      date         Remarks
//[001]     Shashi Keshar   18-10-2016    Issue Resolved of send to Purchase request message notification. Problem was Not allready exist BOM name.
//[002]     Shashi Keshar   16-11-2016    Added combox and Get Receipient ID from Combo box.

Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.CloneHUB = function (element) {
    Rebound.GlobalTrader.Site.Controls.Forms.CloneHUB.initializeBase(this, [element]);
    this._intCustomerRequirementID = -1;
    this._intCurrencyID = -1;
    this._intCompanyID = -1;
    this._blnReqValidated = true;
    this._ctlCompany = "";
    this._strCustomerRequirementNumber = "";
};

Rebound.GlobalTrader.Site.Controls.Forms.CloneHUB.prototype = {
    get_intCustomerRequirementID: function () { return this._intCustomerRequirementID; }, set_intCustomerRequirementID: function (value) { if (this._intCustomerRequirementID !== value) this._intCustomerRequirementID = value; },

    initialize: function () {
        Rebound.GlobalTrader.Site.Controls.Forms.CloneHUB.callBaseMethod(this, "initialize");
        this.addShown(Function.createDelegate(this, this.formShown));

    },

    dispose: function () {
        if (this.isDisposed) return;
        this._intCustomerRequirementID = null;
        this._intCurrencyID = -null;
        this._intCompanyID = null;
        this._ctlCompany = "";
        this._strCustomerRequirementNumber = "";
        Rebound.GlobalTrader.Site.Controls.Forms.CloneHUB.callBaseMethod(this, "dispose");
    },



    formShown: function () {
        if (this._blnFirstTimeShown) {
            this._ctlConfirm = this.getFieldComponent("ctlConfirm");
            this._ctlConfirm.addClickYesEvent(Function.createDelegate(this, this.yesClicked));
            this._ctlConfirm.addClickNoEvent(Function.createDelegate(this, this.noClicked));
            //[002] Start Code
            //this._ctlMail = $find(this.getField("ctlSendMailMessage").ID);
            //this._ctlMail._ctlRelatedForm = this;
            //[002]  End Code
        }
        $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_lblCustomer").text(this._ctlCompany);
        $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUB_ctlDB_lblCustReqNo").text(this._strCustomerRequirementNumber);
        //this.getFieldDropDownData("ctlSalesperson");
        //this.getFieldDropDownData("ctlSalesman")



        //if (this._blnReqValidated == false) {
        //    this.showError(true, "Some mandatory data is missing from this requirement. Please go back and fill in the missing data.");
        //    this.showField("ctlSalesperson", false);
        //    this.showField("ctlSendMailMessage", false);
        //    this.showField("ctlQuoteRequired", false);
        //    this.showField("ctlConfirm", false);
        //}
        //else {

        //    this.showField("ctlSalesperson", true);
        //    this.showField("ctlSendMailMessage", true);
        //    this.showField("ctlQuoteRequired", true);
        //    this.showField("ctlConfirm", true);
        //}

        $addHandler(document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl25_ibtnBack_hyp"), "click", Function.createDelegate(this, this.noClicked1));
        $addHandler(document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl26_ibtnBack_hyp"), "click", Function.createDelegate(this, this.noClicked1));
    },

    noClicked1: function () {
        this.onNotConfirmed();
    },

    yesClicked: function () {
        if (!this.validateForm()) return;
        this.showSaving(true);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("Controls/Nuggets/CusReqMainInfo");
        obj.set_DataObject("CusReqMainInfo");
        obj.set_DataAction("CloneRequirementDataHUB");
        obj.addParameter("id", this._intCustomerRequirementID);
        //obj.addParameter("CurrencyID", this._intCurrencyID);
        //obj.addParameter("CompanyID", this._intCompanyID);
        //obj.addParameter("AssignUserNo", this.getFieldValue("ctlSalesperson"));
        //obj.addParameter("DateRequired", this.getFieldValue("ctlQuoteRequired"));
        //[002] Start Code
        //obj.addParameter("aryRecipientLoginIDs", $R_FN.arrayToSingleString(this._ctlMail._aryRecipientLoginIDs));
        //[002] End Code
        //obj.addParameter("Contact2No", this.getFieldValue("ctlSalesman"));
        obj.addDataOK(Function.createDelegate(this, this.saveCloneHUBComplete));
        obj.addError(Function.createDelegate(this, this.saveError));
        obj.addTimeout(Function.createDelegate(this, this.saveError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
        this.onSave();
    },

    noClicked: function () {
        this.showSaving(false);
        this.onNotConfirmed();
    },
    validateForm: function () {
        this.onValidate();
        var blnOK = true;
        //if (!this.checkFieldEntered("ctlSalesperson")) blnOK = false;
        // alert(this.getFieldValue("ctlSalesperson"));
        //if (this.getFieldValue("ctlSalesperson") == null || Number.parseInt(this.getFieldValue("ctlSalesperson")) <= 0) {
        //    blnOK = false;
        //    this.showError(true, "Please select buyer");
        //}
        //if (!this.checkFieldEntered("ctlQuoteRequired")) blnOK = false;
        //if (!blnOK) this.showError(true);
        //if (this._blnReqValidated == false) {
        //    blnOK = false;
        //    this.showError(true, "Some mandatory data is missing from this requirement. Please go back and fill in the missing data.");
        //}

        return blnOK;
    },

    saveError: function (args) {
        this.showSaving(false);
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    saveCloneHUBComplete: function (args) {
        var res = args._result;
        // alert(args._result.Result);
        if (args._result.Result > 0) {
            //this.showSavedOK(true);
            //this.onSaveComplete();
            //window.location.href = ("Ord_CusReqDetail.aspx?req=" + args._result.Result)
            this.showSavedOK(true);
            location.href = $RGT_gotoURL_CustomerRequirement(args._result.Result);
            //[001] Start Here
        } else {
            this.showError(true, args._result.ValidationMessage);
        }
        //[001] End Here
    }

};

Rebound.GlobalTrader.Site.Controls.Forms.CloneHUB.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CloneHUB", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
