///<reference name="MicrosoftAjax.js" />
///<reference path="~/Common/Data/Data.js" />
///<reference path="~/Common/Functions/Functions.js" />
///<reference path="~/Controls/IconButton/IconButton.js" />
//-----------------------------------------------------------------------------------------
// RP 27.01.2010:
// - ensure calendar pops-up below the text box for all browsers
//
// RP 08.01.2010:
// - allow no events to be raised on show/hide calendar (to prevent recursion)
//
// RP 04.01.2010:
// - put in pause before showing month / year select
/*
Marker   Changed By     ChangedDate     Remarks
[001]    Aashu <PERSON>    04-Oct-2018     [REB-12867]: Calendar display with week in it
*/
//-----------------------------------------------------------------------------------------
Rebound.GlobalTrader.Site.Controls.CalendarManager = function (element) {
    Rebound.GlobalTrader.Site.Controls.CalendarManager.initializeBase(this, [element]);
    this._aryCalendarIDs = [];
};

Rebound.GlobalTrader.Site.Controls.CalendarManager.prototype = {

    dispose: function () {
        if (this.isDisposed) return;
        if (this._aryCalendarIDs) {
            for (var i = 0, l = this._aryCalendarIDs.length; i < l; i++) {
                var cal = $find(this._aryCalendarIDs[i]);
                if (cal) cal.dispose();
                cal = null;
            }
        }
        this._aryCalendarIDs = null;
        this.isDisposed = true;
    },

    registerCalendar: function (ctl) {
        Array.add(this._aryCalendarIDs, ctl._element.id);
    },

    closeOtherCalendars: function (ctl) {
        for (var i = 0, l = this._aryCalendarIDs.length; i < l; i++) {
            if (this._aryCalendarIDs[i] != ctl._element.id) $find(this._aryCalendarIDs[i]).explicitHideCalendar();
        }
    }

};

Rebound.GlobalTrader.Site.Controls.CalendarManager.registerClass("Rebound.GlobalTrader.Site.Controls.CalendarManager");

$R_CAL_MANAGER = new Rebound.GlobalTrader.Site.Controls.CalendarManager();

//----------------------------------------------------------------------------------------

Type.registerNamespace("Rebound.GlobalTrader.Site.Controls");

Rebound.GlobalTrader.Site.Controls.Calendar = function (element) {
    Rebound.GlobalTrader.Site.Controls.Calendar.initializeBase(this, [element]);
    this._dtmSelectedDate = new Date();
    this._blnFormatAsBirthday = false;
    this._intMonthSelectTimeout = -1;
    this._intYearSelectTimeout = -1;
    this._intYearMonthTimeout = { Show: 250, Hide: 50 };
    //[001] start
    this._blnShowWeekNumber = true;
    //[001] end
};

Rebound.GlobalTrader.Site.Controls.Calendar.prototype = {

    get_txt: function () { return this._txt; }, set_txt: function (v) { if (this._txt !== v) this._txt = v; },
    get_pnlCalendar: function () { return this._pnlCalendar; }, set_pnlCalendar: function (v) { if (this._pnlCalendar !== v) this._pnlCalendar = v; },
    get_imgCal: function () { return this._imgCal; }, set_imgCal: function (v) { if (this._imgCal !== v) this._imgCal = v; },
    get_tbl: function () { return this._tbl; }, set_tbl: function (v) { if (this._tbl !== v) this._tbl = v; },
    get_hypOff: function () { return this._hypOff; }, set_hypOff: function (v) { if (this._hypOff !== v) this._hypOff = v; },
    get_blnVisible: function () { return this._blnVisible; }, set_blnVisible: function (v) { if (this._blnVisible !== v) this._blnVisible = v; },
    get_blnFormatAsBirthday: function () { return this._blnFormatAsBirthday; }, set_blnFormatAsBirthday: function (v) { if (this._blnFormatAsBirthday !== v) this._blnFormatAsBirthday = v; },

    addShowCalendarEvent: function (handler) { this.get_events().addHandler("ShowCalendar", handler); },
    removeShowCalendarEvent: function (handler) { this.get_events().removeHandler("ShowCalendar", handler); },
    onShowCalendar: function () {
        var handler = this.get_events().getHandler("ShowCalendar");
        if (handler) handler(this, Sys.EventArgs.Empty);
    },

    addCloseCalendarEvent: function (handler) { this.get_events().addHandler("CloseCalendar", handler); },
    removeCloseCalendarEvent: function (handler) { this.get_events().removeHandler("CloseCalendar", handler); },
    onCloseCalendar: function () {
        var handler = this.get_events().getHandler("CloseCalendar");
        if (handler) handler(this, Sys.EventArgs.Empty);
    },

    addDateSelectEvent: function (handler) { this.get_events().addHandler("DateSelect", handler); },
    removeDateSelectEvent: function (handler) { this.get_events().removeHandler("DateSelect", handler); },
    onDateSelect: function () {
        var handler = this.get_events().getHandler("DateSelect");
        if (handler) handler(this, Sys.EventArgs.Empty);
    },

    initialize: function () {
        Rebound.GlobalTrader.Site.Controls.Calendar.callBaseMethod(this, "initialize");
        $addHandler(this._imgCal, "click", Function.createDelegate(this, this.imgCal_Click));
        $addHandler(this._txt, "click", Function.createDelegate(this, this.txt_Click));
        $addHandler(this._txt, "focus", Function.createDelegate(this, this.txt_Click));
        $addHandler(this._hypOff, "click", Function.createDelegate(this, this.hypOff_Click));
        $R_CAL_MANAGER.registerCalendar(this);
    },

    dispose: function () {
        if (this.isDisposed) return;
        if (this.get_element()) $clearHandlers(this.get_element());
        if (this._imgCal) $clearHandlers(this._imgCal);
        if (this._txt) $clearHandlers(this._txt);
        if (this._hypOff) $clearHandlers(this._hypOff);
        this._txt = null;
        this._pnlCalendar = null;
        this._imgCal = null;
        this._tbl = null;
        this._hypOff = null;
        this._dtmSelectedDate = null;
        this._blnFormatAsBirthday = null;
        this._blnVisible = null;
        this._intMonthSelectTimeout = null;
        this._intYearSelectTimeout = null;
        this._intYearMonthTimeout = null;
        Rebound.GlobalTrader.Site.Controls.Calendar.callBaseMethod(this, "dispose");
        this.isDisposed = true;
    },

    imgCal_Click: function () {
        this.toggleShowCalendar();
    },

    txt_Click: function () {
        if (!this._blnVisible) this.showCalendar(true);
    },

    hypOff_Click: function () {
        this.showCalendar(false);
    },

    setInitialMonthAndYear: function () {
        if (this._txt.value.length > 0) this._dtmSelectedDate = Date.parseLocale(this._txt.value);
        if (!this._dtmSelectedDate) this._dtmSelectedDate = new Date();
        this._intCurrentYear = this._dtmSelectedDate.getFullYear();
        this._intCurrentMonth = this._dtmSelectedDate.getMonth() + 1;
    },

    toggleShowCalendar: function () {
        this._blnVisible = !this._blnVisible;
        this.showCalendar(this._blnVisible);
    },

    showCalendar: function (blnShow, blnDontRaiseEvents) {
        this._blnVisible = blnShow;
        $R_FN.showElement(this._pnlCalendar, blnShow);
        if (blnShow) {
            this.setInitialMonthAndYear();
            this.populateCalendar();
            if (!blnDontRaiseEvents) this.onShowCalendar();
            $R_CAL_MANAGER.closeOtherCalendars(this);

            //make sure the calendar comes up underneath the textbox on all browsers
            //IE8 is fine with the default settings, IE7 (deprecated for use with GT) is not
            //Only Safari (and Chrome which identifies itself as Safari) need to move the calendar left;
            if (Sys.Browser.agent == Sys.Browser.Safari) this._pnlCalendar.style.left = String.format("-{0}px", Sys.UI.DomElement.getBounds(this._txt).width);
        } else {
            if (!blnDontRaiseEvents) this.onCloseCalendar();
        }
    },

    explicitHideCalendar: function () {
        this._blnVisible = false;
        $R_FN.showElement(this._pnlCalendar, false);
        this.onCloseCalendar();
    },

    changeMonth: function (intChange) {
        this._intCurrentMonth = this._intCurrentMonth + intChange;
        if (this._intCurrentMonth > 12) { this._intCurrentMonth = 1; this._intCurrentYear += 1; }
        if (this._intCurrentMonth < 1) { this._intCurrentMonth = 12; this._intCurrentYear -= 1; }
        this.selectMonth(this._intCurrentMonth);
    },

    selectMonth: function (intMonth) {
        this._intCurrentMonth = intMonth;
        this.populateCalendar();
    },

    changeYear: function (intChange) {
        this.selectYear(this._intCurrentYear + intChange);
    },

    selectYear: function (intYear) {
        this._intCurrentYear = intYear;
        this.populateCalendar();
    },

    selectDate: function (strDate) {
        this._dtmSelectedDate = new Date(strDate);
        this._txt.value = this._dtmSelectedDate.localeFormat((this._blnFormatAsBirthday) ? "m" : "d");
        this.showCalendar(false);
        this.onDateSelect();
    },

    selectToday: function () {
        this.selectDate(new Date());
    },

    selectNone: function () {
        this._txt.value = "";
        this.showCalendar(false);
        this.onDateSelect();
    },

    populateCalendar: function () {
        var dtmStartOfMonth = new Date();
        dtmStartOfMonth.setFullYear(this._intCurrentYear, this._intCurrentMonth - 1, 1);
        var dtmEndOfMonth = new Date(dtmStartOfMonth);
        dtmEndOfMonth.setDate(27);
        while (dtmEndOfMonth.getMonth() == dtmStartOfMonth.getMonth()) { dtmEndOfMonth.setDate(dtmEndOfMonth.getDate() + 1); }
        dtmEndOfMonth.setDate(dtmEndOfMonth.getDate() - 1);
        var dtmWork = new Date(dtmStartOfMonth);
        var td = [];
        var anc;
        var obj = this;
        var strID = this.get_element().id;

        //clear table
        for (var i = this._tbl.rows.length - 1; i >= 0; i--) {
            this._tbl.deleteRow(i);
        }

        //header row
        
        Array.clear(td);
        td[0] = document.createElement("td"); td[0].align = "center";
        //[001] start --Updated logic for colspan
        td[0].colSpan = this._blnShowWeekNumber ? 8 : 7;
        //[001] end
        td[0].className = "calendarNextPrevStyle";
        var divPrev = document.createElement("div");
        divPrev.className = "calendarPrev";
        anc = document.createElement("a"); anc.href = "javascript:void(0);";
        anc.setAttribute("onclick", String.format("$find('{0}').changeYear(-1);", strID));
        anc.innerHTML = "&lt;&lt;";
        divPrev.appendChild(anc);
        anc2 = document.createElement("a");
        anc2.href = "javascript:void(0);";
        anc2.setAttribute("onclick", String.format("$find('{0}').changeMonth(-1);", strID));
        anc2.innerHTML = "&lt;";
        divPrev.appendChild(anc2);
        td[0].appendChild(divPrev);
        var divNext = document.createElement("div");
        divNext.className = "calendarNext";
        anc2 = document.createElement("a");
        anc2.href = "javascript:void(0);";
        anc2.setAttribute("onclick", String.format("$find('{0}').changeYear(1);", strID));
        anc2.innerHTML = "&gt;&gt;";
        divNext.appendChild(anc2);
        anc = document.createElement("a");
        anc.href = "javascript:void(0);";
        anc.setAttribute("onclick", String.format("$find('{0}').changeMonth(1);", strID));
        anc.innerHTML = "&gt;";
        divNext.appendChild(anc);
        td[0].appendChild(divNext);
        td[0].innerHTML += String.format("{0}&nbsp;{1}", this.formatMonthString(this._intCurrentMonth), this.formatYearString(this._intCurrentYear));
        this.addRowToTable(td, "calendarTitle");

        //add days of week
        Array.clear(td);
        var dtmTemp = new Date(dtmStartOfMonth);
        while (dtmTemp.getDay() != 0) { dtmTemp.setDate(dtmTemp.getDate() + 1); }
        tr = document.createElement("tr");
        for (i = 0; i < 7; i++) {
            var strClass = ((i == 0) || (i == 6)) ? "calendarWeekendHeader" : "calendarHeader";
            var strText = dtmTemp.localeFormat("ddd");
            td[i] = document.createElement("td");
            td[i].align = "center";
            td[i].className = strClass;
            td[i].innerHTML = strText;
            dtmTemp.setDate(dtmTemp.getDate() + 1);
        }
        //[001] start
        //Add week header
        if (this._blnShowWeekNumber) {
            td[7] = document.createElement("td");
            td[7].align = "center";
            td[7].className = "calendarHeader";
            td[7].innerHTML = "Week";
        }
        //[001] end
        dtmTemp = null;
        this.addRowToTable(td);

        //add days
        for (i = 0; i < 6; i++) {
            if (dtmWork.valueOf() <= dtmEndOfMonth.valueOf()) {
                Array.clear(td);

                //first time offset to the correct day position
                var k = 0;
                if (i == 0) { for (k, l = dtmStartOfMonth.getDay() ; k < l; k++) { td[k] = document.createElement("td"); td[k].align = "center"; td[k].innerHTML = "&nbsp;"; } }

                for (var j = k; j < 7; j++) {
                    if (dtmWork.valueOf() <= dtmEndOfMonth.valueOf()) {
                        strClass = ((dtmWork.getDay() == 0) || (dtmWork.getDay() == 6)) ? "calendarWeekendDay" : "calendarDay";
                        if (dtmWork.getDate() == this._dtmSelectedDate.getDate() && dtmWork.getMonth() == this._dtmSelectedDate.getMonth() && dtmWork.getYear() == this._dtmSelectedDate.getYear()) strClass = "calendarSelectedDay";
                        td[j] = document.createElement("td");
                        td[j].align = "center";
                        td[j].className = strClass;
                        td[j].innerHTML = String.format("<a href=\"javascript:void(0);\" onclick=\"$find('{0}').selectDate({1});\">{2}</a>", this.get_element().id, dtmWork.valueOf(), dtmWork.getDate());
                    } else {
                        td[j] = document.createElement("td"); td[j].align = "center"; td[j].innerHTML = "&nbsp;";
                    }
                    
                    //[001] start
                    //Add week number
                    if (this._blnShowWeekNumber) {
                        td[7] = document.createElement("td");
                        td[7].align = "center";
                        td[7].className = "calendarHeader";
                        td[7].innerHTML = this.getWeek(dtmWork);
                    }
                    //[001] end
                    dtmWork.setDate(dtmWork.getDate() + 1);
                }
                this.addRowToTable(td);
            }
        }

        //footer
        Array.clear(td);
        td[0] = document.createElement("td");
        td[0].align = "center";
        td[0].colSpan = 7;
        td[0].className = "calendarNextPrevStyle";
        anc = document.createElement("a");
        anc.href = "javascript:void(0);";
        anc.onclick = new Function("evtSelectToday", String.format("$find('{0}').selectToday();", strID));
        anc.innerHTML = $R_RES.Today; td[0].appendChild(anc);
        anc2 = document.createElement("a");
        anc2.href = "javascript:void(0);";
        anc2.onclick = new Function("evtSelectNone", String.format("$find('{0}').selectNone();", strID));
        anc2.innerHTML = $R_RES.None; td[0].appendChild(anc2);
        this.addRowToTable(td);
        td = null;
        anc = null;
        anc2 = null;
    },

    addRowToTable: function (aryTD, strClass) {
        if (!strClass) strClass = "";
        var tr = this._tbl.insertRow(-1);
        tr.className = strClass;
        for (var i = 0, l = aryTD.length; i < l; i++) { tr.appendChild(aryTD[i]); }
        tr = null;
    },

    formatMonthString: function (intMonth) {
        var str = "";
        var strID = this._element.id;
        str = String.format("<div class=\"monthSelect\" onmouseover=\"$find('{0}').showMonthSelect(true);\" onmouseout=\"$find('{0}').showMonthSelect(false);\">{1}<div id=\"{0}_monthSelect\" class=\"monthSelectDrop invisible\">", strID, Sys.CultureInfo.CurrentCulture.dateTimeFormat.AbbreviatedMonthNames[intMonth - 1]);
        for (var i = 0; i < 6; i++) {
            str += "<div>";
            str += String.format("<div class=\"item\" onclick=\"\$find('{1}').selectMonth({2})\">{0}</div>", Sys.CultureInfo.CurrentCulture.dateTimeFormat.AbbreviatedMonthNames[i], strID, i + 1);
            str += String.format("<div class=\"item\" onclick=\"\$find('{1}').selectMonth({2})\">{0}</div>", Sys.CultureInfo.CurrentCulture.dateTimeFormat.AbbreviatedMonthNames[i + 6], strID, i + 6 + 1);
            str += "</div>";
        }
        str += "</div></div>";
        return str;
    },

    formatYearString: function (intYear) {
        var str = "";
        var strID = this._element.id;
        str = String.format("<div class=\"yearSelect\" onmouseover=\"$find('{0}').showYearSelect(true);\" onmouseout=\"$find('{0}').showYearSelect(false);\">{1}<div id=\"{0}_yearSelect\" class=\"yearSelectDrop invisible\">", strID, intYear);
        intYear -= 5;
        for (var i = 0; i < 7; i++) {
            intYear += 1;
            str += String.format("<div onclick=\"\$find('{1}').selectYear({0})\">{0}</div>", intYear, strID);
        }
        str += "</div></div>";
        return str;
    },

    showMonthSelect: function (bln) {
        var el = $get(String.format("{0}_monthSelect", this._element.id));
        this.clearShowMonthTimeout();
        if (bln) {
            if (!$R_FN.isElementVisible(el)) this._intMonthSelectTimeout = setTimeout(Function.createDelegate(this, this.finishShowMonthSelect), this._intYearMonthTimeout.Show);
        } else {
            if ($R_FN.isElementVisible(el)) this._intMonthSelectTimeout = setTimeout(Function.createDelegate(this, this.finishHideMonthSelect), this._intYearMonthTimeout.Hide);
        }
        el = null;
    },

    clearShowMonthTimeout: function () {
        if (this._intMonthSelectTimeout != -1) clearTimeout(this._intMonthSelectTimeout);
    },

    finishShowMonthSelect: function () {
        this.clearShowMonthTimeout();
        $R_FN.showElement($get(String.format("{0}_monthSelect", this._element.id)), true);
    },

    finishHideMonthSelect: function () {
        this.clearShowMonthTimeout();
        $R_FN.showElement($get(String.format("{0}_monthSelect", this._element.id)), false);
    },

    showYearSelect: function (bln) {
        var el = $get(String.format("{0}_yearSelect", this._element.id));
        this.clearShowYearTimeout();
        if (bln) {
            if (!$R_FN.isElementVisible(el)) this._intYearSelectTimeout = setTimeout(Function.createDelegate(this, this.finishShowYearSelect), this._intYearMonthTimeout.Show);
        } else {
            if ($R_FN.isElementVisible(el)) this._intYearSelectTimeout = setTimeout(Function.createDelegate(this, this.finishHideYearSelect), this._intYearMonthTimeout.Hide);
        }
        el = null;
    },

    clearShowYearTimeout: function () {
        if (this._intYearSelectTimeout != -1) clearTimeout(this._intYearSelectTimeout);
    },

    finishShowYearSelect: function () {
        this.clearShowYearTimeout();
        $R_FN.showElement($get(String.format("{0}_yearSelect", this._element.id)), true);
    },

    finishHideYearSelect: function () {
        this.clearShowYearTimeout();
        $R_FN.showElement($get(String.format("{0}_yearSelect", this._element.id)), false);
    },
    //[001] start
    getWeek: function (dt) {
        var calc = function (o) {
            if (o.dtmin.getDay() != 1) {
                if (o.dtmin.getDay() <= 4 && o.dtmin.getDay() != 0) o.w += 1;
                o.dtmin.setDate((o.dtmin.getDay() == 0) ? 2 : 1 + (7 - o.dtmin.getDay()) + 1);
            }
            o.w += Math.ceil((((o.dtmax.getTime() - o.dtmin.getTime()) / (24 * 60 * 60 * 1000)) + 1) / 7);
        }, getNbDaysInAMonth = function (year, month) {
            var nbdays = 31;
            for (var i = 0; i <= 3; i++) {
                nbdays = nbdays - i;
                if ((dtInst = new Date(year, month - 1, nbdays)) && dtInst.getDate() == nbdays && (dtInst.getMonth() + 1) == month && dtInst.getFullYear() == year)
                    break;
            }
            return nbdays;
        };
        if (dt.getMonth() + 1 == 1 && dt.getDate() >= 1 && dt.getDate() <= 3 && (dt.getDay() >= 5 || dt.getDay() == 0)) {
            var pyData = { "dtmin": new Date(dt.getFullYear() - 1, 0, 1, 0, 0, 0, 0), "dtmax": new Date(dt.getFullYear() - 1, 11, getNbDaysInAMonth(dt.getFullYear() - 1, 12), 0, 0, 0, 0), "w": 0 };
            calc(pyData);
            return pyData.w;
        } else {
            var ayData = { "dtmin": new Date(dt.getFullYear(), 0, 1, 0, 0, 0, 0), "dtmax": new Date(dt.getFullYear(), dt.getMonth(), dt.getDate(), 0, 0, 0, 0), "w": 0 },
                nd12m = getNbDaysInAMonth(dt.getFullYear(), 12);
            if (dt.getMonth() == 12 && dt.getDay() != 0 && dt.getDay() <= 3 && nd12m - dt.getDate() <= 3 - dt.getDay()) ayData.w = 1; else calc(ayData);
            return ayData.w;
        }
    }
    //[001] end
};

Rebound.GlobalTrader.Site.Controls.Calendar.registerClass("Rebound.GlobalTrader.Site.Controls.Calendar", Sys.UI.Control, Sys.IDisposable);