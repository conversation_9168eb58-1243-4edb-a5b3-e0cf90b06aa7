Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.ExportApprovalStatus_EditAll=function(n){Rebound.GlobalTrader.Site.Controls.Forms.ExportApprovalStatus_EditAll.initializeBase(this,[n]);this._intPurchaseOrderID=0;this._intLineID=-1;this._blnPartReceivedEdit=!1;this._blnRestrictedEdit=!1;this._intLineReceived=0;this._intIPOClientNo=-1;this._blnClientPO=!1;this._intGlobalClientNo=-1;this._blnProductHaza=!1;this._blnCanEditQty=!0;this._blnCanEditPrice=!0;this._TypeNo="";this._IsSendToLineManager=!1;this._IsSendToQuality=!1;this._aryCurrentValues=""};Rebound.GlobalTrader.Site.Controls.Forms.ExportApprovalStatus_EditAll.prototype={get_intPurchaseOrderID:function(){return this._intPurchaseOrderID},set_intPurchaseOrderID:function(n){this._intPurchaseOrderID!==n&&(this._intPurchaseOrderID=n)},get_strTitleMessage:function(){return this._strTitleMessage},set_strTitleMessage:function(n){this._strTitleMessage!==n&&(this._strTitleMessage=n)},get_lblCurrency:function(){return this._lblCurrency},set_lblCurrency:function(n){this._lblCurrency!==n&&(this._lblCurrency=n)},get_lblTotalShipInCost:function(){return this._lblTotalShipInCost},set_lblTotalShipInCost:function(n){this._lblTotalShipInCost!==n&&(this._lblTotalShipInCost=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.ExportApprovalStatus_EditAll.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown))},dispose:function(){this.isDisposed||(this._intPurchaseOrderID=null,this._strTitleMessage=null,this._ctlMail&&this._ctlMail.dispose(),this._intLineID=null,this._IsSendToLineManager=null,this._IsSendToQuality=null,this._blnPartReceivedEdit=null,this._blnRestrictedEdit=null,this._intLineReceived=null,this._lblCurrency=null,this._lblTotalShipInCost=null,this._intIPOClientNo=null,this._blnClientPO=null,this._aryCurrentValues="",Rebound.GlobalTrader.Site.Controls.Forms.ExportApprovalStatus_EditAll.callBaseMethod(this,"dispose"))},formShown:function(){this._blnFirstTimeShown&&this.addSave(Function.createDelegate(this,this.saveClicked));this.getFieldDropDownData("ctlDestinationCountry");this.getFieldDropDownData("ctlMilitaryuse")},saveClicked:function(){if(this.validateForm()){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/ExportApprovalStatus");n.set_DataObject("ExportApprovalStatus");n.set_DataAction("SaveAllExportApprovalDetails");n.addParameter("ExportApprovalIds",this._aryCurrentValues);n.addParameter("DestinationCountryNo",this.getFieldValue("ctlDestinationCountry"));n.addParameter("MilitaryuseNo",this.getFieldValue("ctlMilitaryuse"));n.addParameter("EndUserText",this.getFieldValue("ctlEndUser"));n.addDataOK(Function.createDelegate(this,this.saveEditOK));n.addError(Function.createDelegate(this,this.saveEditError));n.addTimeout(Function.createDelegate(this,this.saveEditError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null}},saveEditError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},saveEditOK:function(n){n._result.Result==!0?(this.onSaveComplete(),this.setFieldValue("ctlDestinationCountry",""),this.setFieldValue("ctlMilitaryuse",""),this.setFieldValue("ctlEndUser",""),$("#AllocationErrorMsg").hide(),$("#dvtxt").html("")):(this._strErrorMessage=n._errorMessage,this.onSaveError())},validateForm:function(){this.onValidate();var n=!0;return this.checkFieldEntered("ctlDestinationCountry")||(n=!1),this.checkFieldEntered("ctlMilitaryuse")||(n=!1),this.checkFieldEntered("ctlEndUser")||(n=!1),n||this.showError(!0),n}};Rebound.GlobalTrader.Site.Controls.Forms.ExportApprovalStatus_EditAll.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.ExportApprovalStatus_EditAll",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);