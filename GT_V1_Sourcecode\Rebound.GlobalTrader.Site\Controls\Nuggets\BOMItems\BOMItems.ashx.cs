/* Marker     changed by      date         Remarks
 [001]      Vinay           01/08/2012     Delete UnAllocated Stock Bug
 */
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site.Code.Common;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Web;
using System.Web.Services;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets.Data
{
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class BOMItems : Rebound.GlobalTrader.Site.Data.Base
    {

        public override void ProcessRequest(HttpContext context)
        {
            if (base.init(context))
            {
                if (context.Request.QueryString["action"] != null)
                    Action = context.Request.QueryString["action"];

                switch (Action)
                {
                    case "GetData": GetData(); break;
                    case "ExportToCSV": ExportToCSV(); break;
                    case "ExportToExcel": ExportToExcel(); break;
                    case "ReleaseRequirement": ReleaseRequirement(); break;
                    case "Update": Update(); break;
                    case "DeleteBomItem": DeleteBomItem(); break;
                    case "UnReleaseBomItem": UnReleaseBomItem(); break;
                    case "GetItem": GetItem(); break;
                    case "NoBidRequirement": NoBidRequirement(); break;
                    case "RecallNoBidRequirement": RecallNoBidRequirement(); break;
                    case "SaveExpedite": SaveExpedite(); break;
                    case "PartWatchHUBIPOMatch": PartWatchHUBIPOMatch(); break;
                    case "RemovePartWatch": RemovePartWatch(); break;

                    case "GetKubConfigData": GetKubConfigData(); break;
                    case "GetListKubTop10QuoteForBOM": GetListKubTop10QuoteForBOM(context); break;
                    case "GetListKubTop20CusReqForBOM": GetListKubTop20CusReqForBOM(context); break;
                    case "GetListKubTop3BuyPrice": GetListKubTop3BuyPrice(context); break;
                    case "GetStockDetailsForBOMPart": GetStockDetailsForBOMPart(context); break;

                    case "GetLyticaManufacturer": GetLyticaManufacturer(); break;

                    default: WriteErrorActionNotFound(); break;
                }
            }
        }

        /// <summary>
        /// Add new quoteLine
        /// </summary>
        public void Update()
        {
            try
            {
                string ids = GetFormValue_String("ReqIds");
                string Bomid = GetFormValue_String("BomId");
                System.Boolean intUpdateStatus = CustomerRequirement.Update(
                      Convert.ToInt32(Bomid)
                      , LoginID
                      , SessionManager.ClientID
                      , ids
                      , (int)BOMStatus.List.Open
                      );
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Result", Convert.ToInt32(intUpdateStatus) > 0);
                OutputResult(jsn);
                jsn.Dispose();
                jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

        /// <summary>
        /// Gets data for a stock item
        /// </summary>
        public void GetData()
        {
            try
            {
                List<CustomerRequirement> lst = CustomerRequirement.GetBOMListForCustomerRequirement(ID, SessionManager.ClientID, Convert.ToBoolean(SessionManager.IsPOHub));
                JsonObject jsn = new JsonObject();
                JsonObject jsnItems = new JsonObject(true);
                jsn.AddVariable("Count", lst.Count);
                bool blnAllHasDeliveryDate = false;
                bool blnAllHasProduct = false;
                bool IsAssignedToMe = false;
                if (lst.Count > 0)
                {
                    blnAllHasDeliveryDate = lst[0].AllSorcingHasDelDate == 0;
                    blnAllHasProduct = lst[0].AllSorcingHasProduct == 0;
                }
                foreach (CustomerRequirement cReq in lst)
                {

                    JsonObject jsnItem = new JsonObject();
                    jsnItem.AddVariable("ID", cReq.CustomerRequirementId);
                    jsnItem.AddVariable("CustReqNo", cReq.CustomerRequirementNumber);
                    jsnItem.AddVariable("PartNo", cReq.Part);
                    //jsnItem.AddVariable("Part", (cReq.Alternate) ? String.Format("{0} ({1})", cReq.Part, Functions.GetGlobalResource("Misc", "Alternate")) : cReq.Part);
                    jsnItem.AddVariable("Part", Functions.GetPartWithAlternate(cReq.Part, cReq.AlternateStatus, cReq.Alternate));
                    jsnItem.AddVariable("Closed", cReq.Closed);
                    jsnItem.AddVariable("ClosedReason", cReq.ClosedReason);
                    jsnItem.AddVariable("MfrNo", cReq.ManufacturerNo);
                    jsnItem.AddVariable("Mfr", cReq.ManufacturerCode);
                    jsnItem.AddVariable("MfrAdvisoryNotes", Functions.ReplaceLineBreaks(cReq.MfrAdvisoryNotes));
                    jsnItem.AddVariable("Product", cReq.ProductName);
                    jsnItem.AddVariable("DC", cReq.DateCode);
                    jsnItem.AddVariable("Package", cReq.PackageName);
                    jsnItem.AddVariable("Price", Functions.FormatCurrency(cReq.Price, cReq.CurrencyCode));
                    jsnItem.AddVariable("TPriceInBom", Functions.FormatCurrency(cReq.ConvertedTargetValue, cReq.BOMCurrencyCode));
                    if (cReq.CurrencyNo != SessionManager.ClientCurrencyID) jsnItem.AddVariable("PriceInBase", Functions.FormatCurrency(BLL.Currency.ConvertValueToBaseCurrency(cReq.Price, (int)cReq.CurrencyNo, DateTime.Now), SessionManager.ClientCurrencyCode));
                    jsnItem.AddVariable("Quantity", cReq.Quantity);
                    jsnItem.AddVariable("Alt", cReq.Alternate);
                    jsnItem.AddVariable("ROHS", cReq.ROHS);
                    jsnItem.AddVariable("Date", Functions.FormatDate(cReq.DatePromised));
                    jsnItem.AddVariable("CustomerPart", cReq.CustomerPart);
                    if (SessionManager.IsPOHub == true)
                    {
                        jsnItem.AddVariable("Company", cReq.ClientName);
                    }
                    else
                    {
                        jsnItem.AddVariable("Company", cReq.CompanyName);
                        jsnItem.AddVariable("CompanyAdvisoryNotes", Functions.ReplaceLineBreaks(cReq.CompanyAdvisoryNotes));
                    }
                    jsnItem.AddVariable("CompanyNo", cReq.CompanyNo);
                    jsnItem.AddVariable("SalesmanName", cReq.SalesmanName);
                    jsnItem.AddVariable("Salesman", cReq.Salesman);
                    jsnItem.AddVariable("BOMCode", cReq.BOMCode);
                    jsnItem.AddVariable("BOMFullName", cReq.BOMFullName);
                    jsnItem.AddVariable("IsAs6081Required", cReq.IsAs6081Required);
                    jsnItem.AddVariable("AssignedTo", cReq.AssignedTo);

                    string[] Ids = cReq.AssigneeId.Split(',');
                    for (int i = 0; i < Ids.Length; i++)
                    {
                        if (Convert.ToInt32(Ids[i]) == SessionManager.LoginID)
                        {
                            IsAssignedToMe = true;
                        }
                        else
                        {
                            IsAssignedToMe = false;
                        }
                        if (IsAssignedToMe == true)
                        {
                            break;
                        }
                    }
                    jsnItem.AddVariable("IsAssignedToMe", IsAssignedToMe);
                    jsnItem.AddVariable("Instructions", Functions.ReplaceLineBreaks(cReq.Instructions));
                    if (!cReq.Alternate)
                    {
                        string strReason = (cReq.ReasonNo > 0) ? string.Format(" ({0})", cReq.ClosedReason) : "";
                        jsn.AddVariable("DisplayStatus", String.Format("{0}{1}", Functions.GetGlobalResource("Status", cReq.DisplayStatus), strReason));
                    }
                    jsnItem.AddVariable("BOMNo", cReq.BOMNo);
                    jsnItem.AddVariable("Released", cReq.POHubReleaseBy > 0);
                    jsnItem.AddVariable("CMNo", cReq.CompanyNo);

                    if (SessionManager.IsPOHub.Value)
                        jsnItem.AddVariable("HasSourcingResult", cReq.HasHubSourcingResult.Value);
                    else
                        jsnItem.AddVariable("HasSourcingResult", cReq.HasClientSourcingResult.Value);

                    jsnItem.AddVariable("IsRequestToPurchaseQuote", (cReq.RequestToPOHubBy ?? 0) > 0);
                    jsnItem.AddVariable("PurchaseQuoteNumber", cReq.PurchaseQuoteNumber);
                    jsnItem.AddVariable("PurchaseQuoteId", cReq.PurchaseQuoteId);
                    jsnItem.AddVariable("IsPurchaseRequestCreated", ((cReq.PurchaseQuoteId ?? 0) > 0));
                    jsnItem.AddVariable("FactorySealed", cReq.FactorySealed == false ? "NO" : "YES");
                    jsnItem.AddVariable("MSL", cReq.MSL);
                    jsnItem.AddVariable("SourcingResult", cReq.SourcingResult > 0 ? false : true);
                    jsnItem.AddVariable("BOMStatus", cReq.BOMStatus);
                    jsnItem.AddVariable("IPOClientNo", cReq.ClientNo);
                    jsnItem.AddVariable("IsNoBid", cReq.IsNoBid);
                    jsnItem.AddVariable("IsExpeditDate", cReq.ExpeditDate.HasValue != false ? "Yes" : "");
                    jsnItem.AddVariable("UpdateByPH", cReq.UpdateByPH);
                    jsnItem.AddVariable("RequestToPOHubBy", cReq.RequestToPOHubBy);
                    jsn.AddVariable("SupportTeamMemberNo", cReq.SupportTeamMemberNo);
                    jsn.AddVariable("SupportTeamMemberName", cReq.SupportTeamMemberName);
                    jsnItem.AddVariable("PartWatchHUBIPO", cReq.PartWatchHUBIPO);
                    jsnItem.AddVariable("PriceIssueBuyAndSell", cReq.PriceIssueBuyAndSell);
                    //Code end
                    jsnItems.AddVariable(jsnItem);
                    jsnItem.Dispose();

                    jsnItem = null;
                }
                jsn.AddVariable("Items", jsnItems);
                jsn.AddVariable("AllHasDelDate", blnAllHasDeliveryDate);
                jsn.AddVariable("AllHasProduct", blnAllHasProduct);
                var hasImportFile = BOM.HasImportFile(ID);
                jsn.AddVariable("HasImportFile", hasImportFile);
                OutputResult(jsn);
                jsn.Dispose();
                jsn = null;
            }
            catch (Exception ex)
            {
                WriteError(ex);
            }
        }
        /// <summary>
        /// Writes report to a CSV file and returns the filename 
        /// </summary>
        /// <returns></returns>
        public void ExportToCSV()
        {
            JsonObject jsn = new JsonObject();
            try
            {
                ////List<List<object>> lstData = CustomerRequirement.GetBOMListForCRList(ID, SessionManager.ClientID);
                JsonObject jsnItems = new JsonObject(true);
                string CurrencyCode = GetFormValue_String("currency_Code");
                //return saved filename to the page
                string strFilename = FileUploadManager.ExportToCSV((int)Rebound.GlobalTrader.BLL.Report.List.RequirementWithBOM, ID, CurrencyCode, "E");
                jsn.AddVariable("Filename", String.Format("{0}/{1}", FileUploadManager.GetUploadTempCSVFilePath_Relative(false), strFilename));
                OutputResult(jsn);
            }
            catch (Exception e)
            {
                WriteError(e);
            }
            finally
            {
                jsn.Dispose();
                jsn = null;
            }
        }

        public void ExportToExcel()
        {
            JsonObject jsn = new JsonObject();
            try
            {
                string filePath = string.Empty;
                string strFilename = String.Format("HUBRFQ_SourcingResultTemplate.xlsx");
                DataTable dtResult = BLL.BOM.DataListNugget_ExportToExcel(SessionManager.ClientID, ID);
                filePath = (new EPPlusExportUtility()).ExportDataTableToExcelHUBRFQ(dtResult, strFilename);
                jsn.AddVariable("Filename", filePath);
                OutputResult(jsn);
            }
            catch (Exception e)
            {
                WriteError(e);
            }
            finally
            {
                jsn.Dispose();
                jsn = null;
            }
        }

        /// <summary>
        /// NoBid customer requirement for client user
        /// </summary>
        public void RecallNoBidRequirement()
        {
            try
            {
                int? Bomid = GetFormValue_Int("BomId");
                string BOMCode = GetFormValue_String("BOMCode");
                string BOMName = GetFormValue_String("BOMName");
                string BomCompanyName = GetFormValue_String("BomCompanyName");
                int? BomCompanyNo = GetFormValue_Int("BomCompanyNo");
                string SalesManName = GetFormValue_String("SalesManName");
                int? SalesManNo = GetFormValue_Int("SalesManNo");
                int? CustReqNo = GetFormValue_Int("CustReqNo");
                bool blnResult = CustomerRequirement.RecallNoBidRequirement(
                    ID,
                    LoginID
                );
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Result", blnResult);
                OutputResult(jsn);
                jsn.Dispose();
                jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }
        /// <summary>RecallNoBidRequirement
        /// NoBid customer requirement for client user
        /// </summary>
        public void NoBidRequirement()
        {
            try
            {
                int? Bomid = GetFormValue_Int("BomId");
                string BOMCode = GetFormValue_String("BOMCode");
                string BOMName = GetFormValue_String("BOMName");
                string BomCompanyName = GetFormValue_String("BomCompanyName");
                int? BomCompanyNo = GetFormValue_Int("BomCompanyNo");
                string SalesManName = GetFormValue_String("SalesManName");
                int? SalesManNo = GetFormValue_Int("SalesManNo");
                int? CustReqNo = GetFormValue_Int("CustReqNo");
                string Notes = GetFormValue_String("Notes");
                bool blnResult = CustomerRequirement.NoBidRequirement(
                    ID,
                    LoginID,
                   Convert.ToInt32(Bomid),
                   Notes
                );

                if (blnResult)
                {
                    WebServices servic = new WebServices();
                    //BLL.CustomerRequirement bomr = BLL.CustomerRequirement.Get(ID);
                    servic.NotifyNoBidBom((SalesManNo ?? 0).ToString(), "", Functions.GetGlobalResource("Messages", "BOMNoBid"), (Bomid ?? 0), BOMCode, BOMName, BomCompanyNo, BomCompanyName, false, ID);
                }
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Result", blnResult);
                OutputResult(jsn);
                jsn.Dispose();
                jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }
        /// <summary>
        /// Release customer requirement for client user
        /// </summary>
        public void ReleaseRequirement()
        {
            try
            {
                int? Bomid = GetFormValue_Int("BomId");
                string BOMCode = GetFormValue_String("BOMCode");
                string BOMName = GetFormValue_String("BOMName");
                string BomCompanyName = GetFormValue_String("BomCompanyName");
                int? BomCompanyNo = GetFormValue_Int("BomCompanyNo");
                //string SalesManName = GetFormValue_String("SalesManName");
                int? SalesManNo = GetFormValue_Int("SalesManNo");
                int? CustReqNo = GetFormValue_Int("CustReqNo");
                string ReqSalesPerson = GetFormValue_String("Reqsalesman");
                string SupportTeamMemberNo = GetFormValue_String("SupportTeamMemberNo");
                bool blnResult = CustomerRequirement.ReleaseRequirement(
                    ID,
                    LoginID,
                   Convert.ToInt32(Bomid)
                );

                if (blnResult)
                {
                    WebServices servic = new WebServices();
                    //BLL.CustomerRequirement bomr = BLL.CustomerRequirement.Get(ID);

                    if (!string.IsNullOrEmpty(SupportTeamMemberNo))
                    {
                        servic.NotifyReleaseBom((SalesManNo ?? 0).ToString() + "||" + ReqSalesPerson + "||" + SupportTeamMemberNo, "", Functions.GetGlobalResource("Messages", "BOMReleased"), (Bomid ?? 0), BOMCode, BOMName, BomCompanyNo, BomCompanyName, false, ID);
                    }
                    else
                    {
                        servic.NotifyReleaseBom((SalesManNo ?? 0).ToString() + "||" + ReqSalesPerson, "", Functions.GetGlobalResource("Messages", "BOMReleased"), (Bomid ?? 0), BOMCode, BOMName, BomCompanyNo, BomCompanyName, false, ID);
                    }

                }
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Result", blnResult);
                OutputResult(jsn);
                jsn.Dispose();
                jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }
        /// <summary>
        /// Created by surendra
        /// Created date March 31, 2016
        /// Delete item from bom 
        /// </summary>
        public void DeleteBomItem()
        {
            int? BomID = GetFormValue_Int("BomID");
            int? RequirementID = GetFormValue_Int("RequirementID");
            bool IsDeleted = CustomerRequirement.DeleteBomItem(
                BomID,
                RequirementID,
                LoginID,
                SessionManager.ClientID
                );
            JsonObject jsn = new JsonObject();
            jsn.AddVariable("Result", IsDeleted);
            OutputResult(jsn);
            jsn.Dispose();
            jsn = null;
        }
        /// <summary>
        /// Created by surendra
        /// Created date April 18, 2016
        /// UnRelease item from bom 
        /// </summary>
        private void UnReleaseBomItem()
        {
            int? BomID = GetFormValue_Int("BomID");
            int? RequirementID = GetFormValue_Int("RequirementID");
            bool IsSuccess = CustomerRequirement.UnReleaseBomItem(BomID, RequirementID);
            JsonObject jsn = new JsonObject();
            jsn.AddVariable("Result", IsSuccess);
            OutputResult(jsn);
            jsn.Dispose();
        }

        /// <summary>
        /// Get Shipping method cost and charge
        /// </summary>
        private void GetLyticaManufacturer()
        {
            try
            {
                string mfrName = GetFormValue_String("RsManufacturerName");
                int cusReqNo = GetFormValue_Int("CustomerRequirementID");
                var lyticaData = CustomerRequirement.GetLyticaDataOnHUBRFQByPartMfr(cusReqNo, mfrName);

                JsonObject jsn = new JsonObject();
                if (lyticaData != null)
                {
                    jsn.AddVariable("AveragePrice", Functions.FormatCurrency(lyticaData.AveragePrice, lyticaData.AveragePrice == null || lyticaData.AveragePrice == 0 ? 2 : 5));
                    jsn.AddVariable("TargetPrice", Functions.FormatCurrency(lyticaData.TargetPrice, lyticaData.TargetPrice == null || lyticaData.TargetPrice == 0 ? 2 : 5));
                    jsn.AddVariable("MarketLeading", Functions.FormatCurrency(lyticaData.MarketLeading, lyticaData.MarketLeading == null || lyticaData.MarketLeading == 0 ? 2 : 5));
                    jsn.AddVariable("LifeCycleStatus", string.IsNullOrEmpty(lyticaData.lifeCycleStatus) ? "N/A" : lyticaData.lifeCycleStatus);

                }
                else
                {
                    jsn.AddVariable("AveragePrice", "");
                    jsn.AddVariable("TargetPrice", "");
                    jsn.AddVariable("MarketLeading", "");
                    jsn.AddVariable("LifeCycleStatus", "N/A");
                }

                OutputResult(jsn);
                jsn.Dispose();
                jsn = null;
                lyticaData = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

        private void GetItem()
        {
            CustomerRequirement cReq = CustomerRequirement.GetReqBOM(ID, SessionManager.ClientID);

            //var lyticaData = CustomerRequirement.GetLyticaDataByPartMfr(cReq.Part, cReq.ManufacturerCode, cReq.ManufacturerName);
            //if (lyticaData != null)
            //{
            //    cReq.AveragePrice = lyticaData.AveragePrice;
            //    cReq.TargetPrice = lyticaData.TargetPrice;
            //    cReq.MarketLeading = lyticaData.MarketLeading;
            //}
            //else
            //{
            //    cReq.AveragePrice = 0;
            //    cReq.TargetPrice = 0;
            //    cReq.MarketLeading = 0;
            //}

            var partNo = Functions.GetFullPart(cReq.Part);
            BLL.AllowedEnabledForHUB enableObj = new AllowedEnabledForHUB();
            BLL.KubAssistance kubObj = new KubAssistance();
            enableObj = BLL.AllowedEnabledForHUB.IsAllowedEnabledForHUB(partNo, Convert.ToInt32(SessionManager.ClientID), ID, cReq.ManufacturerNo ?? 0, cReq.ManufacturerName, true);
            cReq.IsAllowedEnableKub = enableObj.IsAllowedEnable == true;

            if (cReq.IsAllowedEnableKub == true)
            {
                kubObj = BLL.KubAssistance.LoadKubAssistanceForBOMManager(partNo, Convert.ToInt32(SessionManager.ClientID), cReq.BOMNo, cReq.ManufacturerNo ?? 0, cReq.ManufacturerName, true);
            }

            OutputResult(GetItem(cReq, kubObj));
            cReq = null;
        }
        /// <summary>
        /// get specific customerRequirement by key
        /// </summary>
        public JsonObject GetItem(CustomerRequirement cReq, KubAssistance kubObj)
        {
            JsonObject jsn = null;
            if (cReq != null)
            {
                jsn = new JsonObject();
                jsn.AddVariable("CustomerRequirementNumber", cReq.CustomerRequirementNumber);
                jsn.AddVariable("CustomerNo", cReq.CompanyNo);
                jsn.AddVariable("CustomerName", cReq.CompanyName);
                jsn.AddVariable("CustomerOnStop", cReq.CompanyOnStop);
                jsn.AddVariable("Contact", cReq.ContactName);
                jsn.AddVariable("ContactNo", cReq.ContactNo);
                jsn.AddVariable("Salesman", cReq.SalesmanName);
                jsn.AddVariable("SalesmanNo", cReq.Salesman);
                jsn.AddVariable("FullPart", cReq.FullPart);
                jsn.AddVariable("Part", cReq.Part);
                jsn.AddVariable("CustomerPart", cReq.CustomerPart);
                jsn.AddVariable("Manufacturer", cReq.ManufacturerName);
                jsn.AddVariable("ManufacturerNo", cReq.ManufacturerNo);
                string mfrNotes = Manufacturer.GetAdvisoryNotes(cReq.ManufacturerNo ?? 0, (int)SessionManager.ClientID);
                jsn.AddVariable("MfrAdvisoryNotes", Functions.ReplaceLineBreaks(mfrNotes));
                jsn.AddVariable("DateCode", cReq.DateCode);
                jsn.AddVariable("Package", cReq.PackageDescription);
                jsn.AddVariable("PackageName", cReq.PackageName);
                jsn.AddVariable("PackageNo", cReq.PackageNo);
                jsn.AddVariable("Quantity", cReq.Quantity);
                string strPrice = Functions.FormatCurrency(cReq.Price, cReq.CurrencyCode);

                if (SessionManager.IsPOHub.Value)
                {
                    double? reqInBasePrice = BLL.Currency.ConvertValueToBaseCurrency(cReq.Price, (int)cReq.CurrencyNo, DateTime.Now);
                    if (cReq.ReqGlobalCurrencyNo != cReq.ClientGlobalCurrencyNo)
                    {
                        strPrice += "\r\n" + String.Format(" ({0})", Functions.FormatCurrency(BLL.Currency.ConvertValueFromBaseCurrency(reqInBasePrice, (int)cReq.ClientCurrencyNo, DateTime.Now), cReq.ClientCurrencyCode));
                    }
                }
                else
                {
                    if (cReq.CurrencyNo != SessionManager.ClientCurrencyID) strPrice += "\r\n" + String.Format(" ({0})", Functions.FormatCurrency(BLL.Currency.ConvertValueToBaseCurrency(cReq.Price, (int)cReq.CurrencyNo, DateTime.Now), SessionManager.ClientCurrencyCode));
                }

                jsn.AddVariable("Price", strPrice);
                jsn.AddVariable("PriceRaw", Functions.FormatCurrency(cReq.Price));
                jsn.AddVariable("Currency", Functions.FormatCurrencyDescription(cReq.CurrencyDescription, cReq.CurrencyCode));
                jsn.AddVariable("CurrencyCode", cReq.CurrencyCode);
                jsn.AddVariable("CurrencyNo", cReq.CurrencyNo);
                jsn.AddVariable("ReceivedDate", Functions.FormatDate(cReq.ReceivedDate));
                jsn.AddVariable("DatePromised", Functions.FormatDate(cReq.DatePromised));
                jsn.AddVariable("Product", cReq.ProductDescription);
                jsn.AddVariable("ProductName", cReq.ProductName);
                jsn.AddVariable("ProductNo", cReq.ProductNo);
                jsn.AddVariable("Usage", cReq.UsageName);
                jsn.AddVariable("UsageNo", cReq.UsageNo);
                jsn.AddVariable("Shortage", cReq.Shortage);
                jsn.AddVariable("Alternate", cReq.Alternate);
                jsn.AddVariable("OriginalCReqNo", cReq.OriginalCustomerRequirementNo);
                jsn.AddVariable("ReasonNo", cReq.ReasonNo);
                jsn.AddVariable("ROHS", cReq.ROHS);
                jsn.AddVariable("Notes", Functions.ReplaceLineBreaks(cReq.Notes));
                jsn.AddVariable("Instructions", Functions.ReplaceLineBreaks(cReq.Instructions));
                jsn.AddVariable("Closed", cReq.Closed);
                jsn.AddVariable("ClosedReason", cReq.ClosedReason);
                jsn.AddVariable("DLUP", Functions.FormatDLUP(cReq.DLUP, cReq.UpdatedBy));
                string strReason = (cReq.ReasonNo > 0) ? string.Format(" ({0})", cReq.ClosedReason) : "";
                jsn.AddVariable("DisplayStatus", string.Format("{0} {1}", Functions.GetGlobalResource("Status", cReq.DisplayStatus), strReason));
                jsn.AddVariable("PartWatch", cReq.PartWatch);
                jsn.AddVariable("BOM", cReq.BOM);
                jsn.AddVariable("BOMName", cReq.BOMName);
                jsn.AddVariable("DivisionNo", cReq.DivisionNo);
                jsn.AddVariable("Division", cReq.DivisionName);
                jsn.AddVariable("Traceability", cReq.Traceability);
                jsn.AddVariable("BOMHeader", cReq.BOMHeader);
                jsn.AddVariable("BOMId", cReq.BOMNo);
                jsn.AddVariable("RequestToPOHubBy", cReq.RequestToPOHubBy);
                jsn.AddVariable("MSL", cReq.MSL);
                jsn.AddVariable("FactorySealed", cReq.FactorySealed);

                jsn.AddVariable("PQA", cReq.PQA);
                jsn.AddVariable("Obsolete", cReq.Obsolete);

                jsn.AddVariable("LastTimeBuy", cReq.LastTimeBuy);
                jsn.AddVariable("RefirbsAcceptable", cReq.RefirbsAcceptable);
                jsn.AddVariable("TestingRequired", cReq.TestingRequired);

                jsn.AddVariable("hidTargetSellPrice", Functions.FormatCurrency(cReq.TargetSellPrice));
                jsn.AddVariable("hidCompetitorBestOffer", Functions.FormatCurrency(cReq.CompetitorBestOffer));

                jsn.AddVariable("TargetSellPrice", Functions.FormatCurrency(cReq.TargetSellPrice, cReq.CurrencyCode));
                jsn.AddVariable("CompetitorBestOffer", Functions.FormatCurrency(cReq.CompetitorBestOffer, cReq.CurrencyCode));
                jsn.AddVariable("CustomerDecisionDate", Functions.FormatDate(cReq.CustomerDecisionDate));

                jsn.AddVariable("RFQClosingDate", Functions.FormatDate(cReq.RFQClosingDate));
                jsn.AddVariable("QuoteValidityRequired", cReq.QuoteValidityRequired);
                jsn.AddVariable("Type", cReq.Type);

                jsn.AddVariable("OrderToPlace", cReq.OrderToPlace);
                jsn.AddVariable("RequirementforTraceability", cReq.RequirementforTraceability);
                jsn.AddVariable("QuoteValidityText", cReq.QuoteValidityText);

                jsn.AddVariable("ReqTypeText", cReq.ReqTypeText);
                jsn.AddVariable("ReqForTraceabilityText", cReq.ReqForTraceabilityText);
                jsn.AddVariable("SourcingResult", cReq.SourcingResult > 0 ? false : true);
                jsn.AddVariable("EAU", cReq.EAU);
                jsn.AddVariable("IsNoBid", cReq.IsNoBid);
                jsn.AddVariable("IsNoBidStatus", cReq.IsNoBid == true ? Functions.GetGlobalResource("FormFields", "IsNoBid") : "");
                jsn.AddVariable("NoBidNotes", Functions.ReplaceLineBreaks(cReq.NoBidNotes));
                jsn.AddVariable("AlternativesAccepted", cReq.AlternativesAccepted);
                jsn.AddVariable("RepeatBusiness", cReq.RepeatBusiness);
                jsn.AddVariable("DutyCodeAndRate", string.Format("{0} ({1})", cReq.DutyCode, Functions.FormatNumeric(cReq.DutyRate, 5)));
                jsn.AddVariable("MSLLevelNo", cReq.MSLLevelNo);
                jsn.AddVariable("SupportTeamMemberNo", cReq.SupportTeamMemberNo);

                //ihs code start
                jsn.AddVariable("CountryOfOrigin", cReq.CountryOfOrigin);
                jsn.AddVariable("CountryOfOriginNo", cReq.CountryOfOriginNo);
                jsn.AddVariable("LifeCycleStage", cReq.LifeCycleStage);
                jsn.AddVariable("HTSCode", cReq.HTSCode);

                jsn.AddVariable("LyticaManufacturerRef", cReq.LyticaManufacturerRef);

                if (cReq.LyticaAveragePrice + cReq.LyticaTargetPrice + cReq.LyticaMarketLeading > 0)
                {
                    jsn.AddVariable("AveragePrice", Functions.FormatCurrency(cReq.LyticaAveragePrice, Convert.ToString(cReq.IHSCurrencyCode), cReq.LyticaAveragePrice == 0 ? 2 : 5));
                    jsn.AddVariable("TargetPrice", Functions.FormatCurrency(cReq.LyticaTargetPrice, Convert.ToString(cReq.IHSCurrencyCode), cReq.LyticaTargetPrice == 0 ? 2 : 5));
                    jsn.AddVariable("MarketLeading", Functions.FormatCurrency(cReq.LyticaMarketLeading, Convert.ToString(cReq.IHSCurrencyCode), cReq.LyticaMarketLeading == 0 ? 2 : 5));
                }
                else
                {
                    jsn.AddVariable("AveragePrice", "");
                    jsn.AddVariable("TargetPrice", "");
                    jsn.AddVariable("MarketLeading", "");
                }


                jsn.AddVariable("Packaging", cReq.Packaging);
                jsn.AddVariable("PackagingSize", cReq.PackagingSize);
                jsn.AddVariable("Descriptions", Functions.ReplaceLineBreaks(cReq.Descriptions));
                if (string.IsNullOrEmpty(cReq.Descriptions))
                    jsn.AddVariable("DescShort", Functions.ReplaceLineBreaks(cReq.Descriptions));
                else if (cReq.Descriptions.Length <= 10)
                    jsn.AddVariable("DescShort", Functions.ReplaceLineBreaks(cReq.Descriptions));
                else
                    jsn.AddVariable("DescShort", Functions.ReplaceLineBreaks(cReq.Descriptions.Substring(0, 10)));
                jsn.AddVariable("IHSProduct", cReq.IHSProduct);
                jsn.AddVariable("IHSProductNo", cReq.IHSProductNo);
                jsn.AddVariable("IHSProductName", cReq.IHSProductName);
                jsn.AddVariable("IHSHTSCode", cReq.IHSHTSCode);
                jsn.AddVariable("IHSDutyCode", cReq.IHSDutyCode);
                //jsn.AddVariable("PurchaseRequestId", cReq.PurchaseRequestId);
                //jsn.AddVariable("PurchaseRequestNumber", cReq.PurchaseRequestNumber);
                jsn.AddVariable("PurchaseRequestId", this.GetJsonObject(cReq.PurchaseRequestId, "PurchaseRequestId"));
                jsn.AddVariable("PurchaseRequestNumber", this.GetJsonObject(cReq.PurchaseRequestNumber, "PurchaseRequestNumber"));
                jsn.AddVariable("IsPOHub", Convert.ToBoolean(SessionManager.IsPOHub));
                jsn.AddVariable("IsAs6081Required", cReq.IsAs6081Required);
                jsn.AddVariable("CustomerRefNo", cReq.CustomerRefNo);
                string IHSStatusDefination = string.Empty;
                if (!string.IsNullOrEmpty(cReq.LifeCycleStage))
                {
                    IHSStatusDefination = cReq.LifeCycleStage;
                    switch (IHSStatusDefination)
                    {
                        case "Active":
                            IHSStatusDefination = GetHazardousNotes(SystemDocument.FooterDocument.Active, (int)cReq.ClientNo);
                            break;
                        case "Active-Unconfirmed":
                            IHSStatusDefination = GetHazardousNotes(SystemDocument.FooterDocument.ActiveUnconfirmed, (int)cReq.ClientNo);
                            break;
                        case "Contact Mfr":
                            IHSStatusDefination = GetHazardousNotes(SystemDocument.FooterDocument.ContactMfr, (int)cReq.ClientNo);
                            break;
                        case "EOL":
                            IHSStatusDefination = GetHazardousNotes(SystemDocument.FooterDocument.EOL, (int)cReq.ClientNo);
                            break; ;
                        case "NRFND":
                            IHSStatusDefination = GetHazardousNotes(SystemDocument.FooterDocument.NRFND, (int)cReq.ClientNo);
                            break;
                        case "Discontinued":
                            IHSStatusDefination = GetHazardousNotes(SystemDocument.FooterDocument.Discontinued, (int)cReq.ClientNo);
                            break;
                        case "Discontinued-Unconfirmed":
                            IHSStatusDefination = GetHazardousNotes(SystemDocument.FooterDocument.DiscontinuedUnconfirmed, (int)cReq.ClientNo);
                            break;
                        case "Transferred":
                            IHSStatusDefination = GetHazardousNotes(SystemDocument.FooterDocument.Transferred, (int)cReq.ClientNo);
                            break;
                            // default:
                            //ende of case st

                            // break;
                    }
                }
                jsn.AddVariable("IHSStatusDefination", Functions.ReplaceLineBreaks(IHSStatusDefination));
                jsn.AddVariable("ECCNCode", cReq.ECCNCode);
                if (!string.IsNullOrEmpty(cReq.IHSECCNCodeDefination))
                {
                    //[009] code start
                    string IHSECCNSCodeDefination = cReq.IHSECCNCodeDefination;
                    jsn.AddVariable("IHSECCNSCodeDefination", Functions.ReplaceLineBreaks(IHSECCNSCodeDefination));
                }
                //[009] code start
                jsn.AddVariable("IsPDFAvailable", cReq.IsPDFAvailable);
                jsn.AddVariable("IHSPartsId", cReq.IHSPartsId);
                jsn.AddVariable("StockAvailableDetail", cReq.StockAvailableDetail);

                if (cReq.IsAllowedEnableKub == true)
                {
                    //203794: [RP-2621] KUB Assistant in HUBRFQ (HUB Side)
                    jsn.AddVariable("IsAllowedEnableKub", cReq.IsAllowedEnableKub);
                    jsn.AddVariable("NumberOfRequirement", kubObj.NumberOfRequirement);
                    jsn.AddVariable("LastQuotedPrice", kubObj.LastQuotedPrice);
                    jsn.AddVariable("LastHubprice", kubObj.LastHubprice);
                    jsn.AddVariable("NumberOfInvoice", kubObj.NumberOfInvoice);
                    jsn.AddVariable("LastestHubRFQName", kubObj.LastestHubRFQName);
                    jsn.AddVariable("LastestHubNumberDate", Functions.FormatUSDate(kubObj.LastestHubNumberDate));
                    jsn.AddVariable("LastestHubRFQId", kubObj.LastestHubRFQId);
                    jsn.AddVariable("LastSoldPrice", kubObj.LastSoldPrice);
                    jsn.AddVariable("LastHighestSoldPrice", kubObj.LastHighestSoldPrice);
                    jsn.AddVariable("LastLowestSoldPrice", kubObj.LastLowestSoldPrice);
                    jsn.AddVariable("NumberOfQuote", kubObj.NumberOfQuote);
                    jsn.AddVariable("NumberQuoteToSalesOrder", kubObj.NumberQuoteToSalesOrder);
                    jsn.AddVariable("LastUpdatedDate", kubObj.LastUpdatedDate);
                    jsn.AddVariable("IHSResultForPartNo", kubObj.IHSResultForPartNo);
                    jsn.AddVariable("LyticaResultForPartNo", kubObj.LyticaResultForPartNo);
                }
            }
            return jsn;
        }
        private string GetHazardousNotes(SystemDocument.FooterDocument enmSystemDocument, int ClientNo)
        {
            string strNotes = "";
            BLL.SystemDocumentFooter ft = BLL.SystemDocumentFooter.GetForClientAndDocument((int)ClientNo, (int)enmSystemDocument);
            if (ft != null) strNotes = (String.IsNullOrEmpty(ft.FooterText)) ? "" : ft.FooterText;
            ft = null;
            return strNotes;
        }

        /// <summary>
        /// GetJsonObject
        /// </summary>
        /// <param name="strValues"></param>
        /// <returns></returns>
        private JsonObject GetJsonObject(string strValues, string strName)
        {
            JsonObject jsnItems = new JsonObject(true);
            Array strArray = Functions.JavascriptStringToArray(strValues, new string[] { "," });
            for (int i = 0; i < strArray.Length; i++)
            {
                JsonObject jsnItem = new JsonObject();
                jsnItem.AddVariable(strName, strArray.GetValue(i));
                jsnItems.AddVariable(jsnItem);
            }

            return jsnItems;
        }

        public void SaveExpedite()
        {
            try
            {
                StringBuilder message = new StringBuilder();
                WebServices servic = new WebServices();
                string addrCC = string.Empty;
                string Subject = Functions.GetGlobalResource("Printing", "CommunicationNoteSubject");
                string ReqIds = GetFormValue_String("ReqIds");
                int HUBRFQId = ID;
                string HUBRFQName = GetFormValue_String("HUBRFQName");
                string Notes = GetFormValue_String("AddNotes");
                string CCUserId = GetFormValue_String("CCUserId");
                string SendToGroup = GetFormValue_String("SendToGroup");

                int RequestToPOHubBy = SessionManager.IsPOHub == true ? GetFormValue_Int("RequestToPOHubBy") : GetFormValue_Int("UpdateByPH");
                int CompanyNo = GetFormValue_Int("HUBRFQCompanyNo");
                System.Int32 Contact2No = GetFormValue_Int("Contact2No");
                servic.GetNameOfCCLoginIDHUBRFQ(CCUserId, out addrCC);

                int intResult = CustomerRequirement.InsertExpedite(
                     HUBRFQId,
                     Notes,
                     LoginID,
                     ReqIds,
                     RequestToPOHubBy,
                     addrCC,
                     SendToGroup
                );

                if (intResult > 0)
                {
                    string FullPart = "";
                    string CustomerRequirementNumber = "";
                    string ReqSalesman = "";
                    string SendToMembersList = "";
                    string SendToArrayList = "";

                    List<CustomerRequirement> lst = CustomerRequirement.GetHUBRFQReqNos(ReqIds, SessionManager.ClientID);
                    foreach (CustomerRequirement objDetails in lst)
                    {
                        FullPart = FullPart + "," + objDetails.FullPart;
                        CustomerRequirementNumber = CustomerRequirementNumber + "," + Convert.ToString(objDetails.CustomerRequirementNumber);
                        if (SessionManager.IsPOHub == true)
                            ReqSalesman = ReqSalesman + "||" + Convert.ToString(objDetails.Salesman);
                    }

                    if (SendToGroup == "Hub Only")
                    {
                        List<SendToMemberList> list = new List<SendToMemberList>();
                        List<SendToMemberList> sglist = SendToMemberList.GetSecurityGroupList();

                        foreach (SendToMemberList objsg in sglist)
                        {
                            list = SendToMemberList.GetMemberList(Convert.ToInt32(objsg.SecurityGroupNo));
                        }

                        foreach (SendToMemberList objDetails in list)
                        {
                            SendToMembersList = SendToMembersList + "||" + Convert.ToString(objDetails.LoginNo);
                        }

                        //if (Contact2No != 0)
                        if (SendToGroup == "Hub Only")
                        {
                            SendToArrayList = SendToMembersList;
                            SendToArrayList = /*Convert.ToString(RequestToPOHubBy) + "||" + Convert.ToString(Contact2No)*/  SendToMembersList;
                            //SendToArrayList = Convert.ToString(RequestToPOHubBy) + "||" + Convert.ToString(Contact2No) + SendToMembersList; changed due to CC communication note 
                            //String contact = Convert.ToString(Contact2No);
                        }
                        else
                        {
                            SendToArrayList = Convert.ToString(RequestToPOHubBy) + SendToMembersList;
                        }

                    }
                    else
                    {
                        if (Contact2No != 0)
                        {
                            if (ReqSalesman == "")
                            {
                                SendToArrayList = Convert.ToString(RequestToPOHubBy) + "||" + Convert.ToString(Contact2No);
                            }
                            else
                            {
                                SendToArrayList = Convert.ToString(RequestToPOHubBy) + "||" + Convert.ToString(Contact2No) + ReqSalesman;
                            }
                        }
                        else
                        {
                            if (ReqSalesman == "")
                            {
                                SendToArrayList = Convert.ToString(RequestToPOHubBy);
                            }
                            else
                            {
                                SendToArrayList = Convert.ToString(RequestToPOHubBy) + ReqSalesman;
                            }
                        }
                    }
                    string poref = string.Format("Reference HUBRFQ   : <a onclick=\"{0}\" target=\"_blank\" href=\"{1}\">{2}</a>", "javascript:void(0);", string.Format(Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["redirecturi"])) + "Ord_BOMDetail.aspx?BOM=" + HUBRFQId, HUBRFQName);
                    message.Append("Message By  : " + SessionManager.LoginFullName + "<br />");
                    message.Append("Date & Time  : " + Functions.FormatDate(Functions.GetUKLocalTime()) + " " + Functions.FormatTime(Functions.GetUKLocalTime()) + "<br />");
                    message.Append("Part  : " + FullPart.Remove(0, 1) + "<br />");
                    message.Append("Req Nos  : " + CustomerRequirementNumber.Remove(0, 1) + "<br />");
                    message.Append(poref + "<br /><br />");
                    message.Append("Communication Note  : " + Notes + "<br />");
                    message.Append("<br /><br />Regards,<br />" + SessionManager.LoginFullName + "<br />");
                    string SendCCArrayList = CCUserId;
                    servic.NotifyMessageExpediteNoteHUBRFQ(SendToArrayList, Subject, Convert.ToString(message), false, SendCCArrayList, SendToGroup);
                    message = null;
                    servic = null;
                    JsonObject jsn = new JsonObject();
                    jsn.AddVariable("Result", intResult);
                    OutputResult(jsn);
                    jsn.Dispose();
                    jsn = null;
                }
                else
                {
                    WriteErrorSQLActionFailed("Insert");
                }
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

        public void PartWatchHUBIPOMatch()
        {
            try
            {
                int intResult = 0;
                int updatedBy = SessionManager.LoginID ?? 0;
                string UpdateByName = SessionManager.LoginFullName;
                int RequirementID = 0;
                string ReqIds = GetFormValue_String("ReqIds");
                #region PartWatch HUBIPO Match offer logic
                string aryIDs = GetFormValue_String("ReqIds");
                string[] arryval = aryIDs.Split(',');
                int j = arryval.Length;
                int i = 0;
                for (i = 0; i < j; i++)
                {
                    DataTable dtOffer = CustomerRequirement.GetPartMachHUBIPOInfo(
                                        SessionManager.ClientID
                                        , updatedBy
                                        , Convert.ToInt32(arryval[i]));

                    RequirementID = Convert.ToInt32(arryval[i]);
                    if (Convert.ToInt32(dtOffer.Rows[0]["CUstomerReqNo"]) > 0)
                    {
                        WebServices servic = new WebServices();
                        string strToLoginsArray = string.Empty;
                        //strToLoginsArray = LoginID.ToString();
                        strToLoginsArray = updatedBy.ToString();
                        servic.NotifyHUBIPIPartWatchMatchMessage(strToLoginsArray, "", Functions.GetGlobalResource("Messages", "PartWatchMatchMsg"), 0, UpdateByName, RequirementID.ToString(), Convert.ToInt32(dtOffer.Rows[0]["CUstomerReqNo"]).ToString(), Convert.ToInt32(GetFormValue_String("Id")));
                        servic = null;
                    }

                }
                intResult = 1;
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Result", intResult);
                OutputResult(jsn);
                jsn.Dispose();
                jsn = null;

                #endregion
            }
            catch (Exception e)
            {
                WriteError(e);
            }

        }

        //RemovePartWatch
        public void RemovePartWatch()
        {
            try
            {
                int intResult = 0;
                string ReqIds = GetFormValue_String("ReqIds");
                #region PartWatch HUBIPO Match offer logic
                intResult = BLL.SourcingResult.DeletePartWatchMatchHUBIPO(GetFormValue_String("ReqIds"), LoginID);
                //intResult = 1;
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Result", intResult);
                OutputResult(jsn);
                jsn.Dispose();
                jsn = null;
                #endregion
            }
            catch (Exception e)
            {
                WriteError(e);
            }

        }

        public void GetKubConfigData()
        {
            bool enable = false;
            var settings = BLL.Setting.GetListValues(null);
            var kubSetting = settings.SingleOrDefault(x => x.SettingItemID == 17);
            if (kubSetting != null)
            {
                enable = kubSetting.SettingValue.ToLower().Equals("true");
            }

            string json = Newtonsoft.Json.JsonConvert.SerializeObject(new { Enable = enable });
            _context.Response.Write(json);
        }

        public void GetListKubTop20CusReqForBOM(HttpContext context)
        {
            try
            {
                string fullPartNo = string.IsNullOrEmpty(context.Request.QueryString["fullPartNo"])
                    ? string.Empty : RemovePunctuationRetainingPercentSigns(context.Request.QueryString["fullPartNo"]);
                List<KubTop20CusReqForBOM> lst = BLL.KubTop20CusReqForBOM.GetListKubTop20CusReqForBOM(fullPartNo, Convert.ToInt32(SessionManager.ClientID));

                string json = Newtonsoft.Json.JsonConvert.SerializeObject(lst);
                _context.Response.Write(json);
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

        public void GetListKubTop10QuoteForBOM(HttpContext context)
        {
            try
            {
                string fullPartNo = string.IsNullOrEmpty(context.Request.QueryString["fullPartNo"])
                    ? string.Empty : RemovePunctuationRetainingPercentSigns(context.Request.QueryString["fullPartNo"]);

                List<KubTop10QuoteForBOM> lst = BLL.KubTop10QuoteForBOM.GetListKubTop10QuoteForBOM(fullPartNo, Convert.ToInt32(SessionManager.ClientID));

                string json = Newtonsoft.Json.JsonConvert.SerializeObject(lst);
                _context.Response.Write(json);
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

        public void GetListKubTop3BuyPrice(HttpContext context)
        {
            try
            {
                string fullPartNo = string.IsNullOrEmpty(context.Request.QueryString["fullPartNo"])
                    ? string.Empty : RemovePunctuationRetainingPercentSigns(context.Request.QueryString["fullPartNo"]);

                List<KubTop3BuyPrice> lst = BLL.KubTop3BuyPrice.GetKubTop3BuyPriceDetailsForHUB(fullPartNo, Convert.ToInt32(SessionManager.ClientID));

                string json = Newtonsoft.Json.JsonConvert.SerializeObject(lst);
                _context.Response.Write(json);
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

        public void GetStockDetailsForBOMPart(HttpContext context)
        {
            try
            {
                string fullPartNo = string.IsNullOrEmpty(context.Request.QueryString["fullPartNo"])
                    ? string.Empty : RemovePunctuationRetainingPercentSigns(context.Request.QueryString["fullPartNo"]);
                int BOMId = Convert.ToInt32(context.Request.QueryString["BOMId"]);

                var stockDetails = BLL.KubStockDetailsForBOM.GetStockDetailsForBOMPart(fullPartNo, Convert.ToInt32(SessionManager.ClientID), BOMId, true);

                string json = Newtonsoft.Json.JsonConvert.SerializeObject(stockDetails);
                _context.Response.Write(json);
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

        private static string RemovePunctuationRetainingPercentSigns(string strIn)
        {
            strIn = strIn.Trim();
            string strOut = RemovePunctuation(strIn);
            if (strIn.StartsWith("%")) strOut = string.Format("%{0}", strOut);
            if (strIn.EndsWith("%")) strOut = string.Format("{0}%", strOut);
            return strOut;
        }
        private static string RemovePunctuation(string strIn)
        {
            return Regex.Replace(strIn.Trim(), @"\W*", "");
        }
    }
}