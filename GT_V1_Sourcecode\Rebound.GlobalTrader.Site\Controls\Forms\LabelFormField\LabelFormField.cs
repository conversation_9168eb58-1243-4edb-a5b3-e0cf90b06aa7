using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Collections;

namespace Rebound.GlobalTrader.Site.Controls.Forms {
	[DefaultProperty("")]
	[ToolboxData("<{0}:LabelFormField runat=server></{0}:LabelFormField>")]
	public class LabelFormField : TableRow, INamingContainer, IScriptControl {

		#region Locals

		protected ScriptManager _sm;
		protected ScriptReference[] _srScriptReference = new ScriptReference[5];
		protected int _intNumberOfScriptReferences = 0;
		protected ScriptControlDescriptor _scScriptControlDescriptor;
		protected bool _blnConfigurationIsDebug = false;

		#endregion

		#region Properties

		private bool _blnRemoveCSSClass = false;
		public bool RemoveCSSClass {
			get { return _blnRemoveCSSClass; }
			set { _blnRemoveCSSClass = value; }
		}

		protected TableCell _tdLabel;
		public TableCell LabelCell {
			get { return _tdLabel; }
		}


		#endregion

		#region Overrides

		/// <summary>
		/// OnInit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			EnsureChildControls();
#if (DEBUG)
			_blnConfigurationIsDebug = true;
#endif
			AddScriptReference("Controls.Forms.LabelFormField.LabelFormField.js");
			base.OnInit(e);
		}

		/// <summary>
		/// create controls
		/// </summary>
		protected override void CreateChildControls() {
			_tdLabel = new TableCell();
			_tdLabel.ID = "tdLabel";
			_tdLabel.ColumnSpan = 2;
			_tdLabel.CssClass = "label";
			Cells.Add(_tdLabel);
			base.CreateChildControls();
		}

		/// <summary>
		/// OnPreRender
		/// </summary>
		/// <param name="e"></param>
		protected override void OnPreRender(EventArgs e) {
			if (_blnRemoveCSSClass) _tdLabel.CssClass = "";
			if (!this.DesignMode) {
				_sm = ScriptManager.GetCurrent(Page);
				if (_sm == null) throw new HttpException("A ScriptManager control must exist on the current page.");
				_sm.RegisterScriptControl(this);
			}
			base.OnPreRender(e);
		}

		/// <summary>
		/// Render
		/// </summary>
		/// <param name="writer"></param>
		protected override void Render(HtmlTextWriter writer) {
			if (!this.DesignMode) _sm.RegisterScriptDescriptors(this);
			base.Render(writer);
		}

		#endregion

		/// <summary>
		/// Adds a control to this tablecell
		/// </summary>
		/// <param name="ctl"></param>
		public void AddControl(Control ctl) {
			EnsureChildControls();
			_tdLabel.Controls.Add(ctl);
		}

		public void SetText(string strText) {
			EnsureChildControls();
			_tdLabel.Text = strText;
		}

		public void SetCssClass(string strCss) {
			EnsureChildControls();
			_tdLabel.CssClass = strCss;
		}

		protected void AddScriptReference(bool blnDebug, string strAssembly, string strRef) {
			ScriptReference sr = Functions.GetScriptReference(blnDebug, strAssembly, strRef, true);
			_srScriptReference[_intNumberOfScriptReferences] = sr;
			_intNumberOfScriptReferences += 1;
		}
		protected void AddScriptReference(string strAssembly, string strRef) {
			AddScriptReference(_blnConfigurationIsDebug, strAssembly, strRef);
		}
		protected void AddScriptReference(string strRef) {
			AddScriptReference(_blnConfigurationIsDebug, "Rebound.GlobalTrader.Site", strRef);
		}

		#region IScriptControl Members

		protected virtual IEnumerable<ScriptReference> GetScriptReferences() {
			return _srScriptReference;
		}

		protected virtual IEnumerable<ScriptDescriptor> GetScriptDescriptors() {
			if (_scScriptControlDescriptor == null) _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Forms.LabelFormField", this.ClientID);
			_scScriptControlDescriptor.AddElementProperty("td", _tdLabel.ClientID);
			return new ScriptDescriptor[] { _scScriptControlDescriptor };
		}

		IEnumerable<ScriptReference> IScriptControl.GetScriptReferences() { return GetScriptReferences(); }
		IEnumerable<ScriptDescriptor> IScriptControl.GetScriptDescriptors() { return GetScriptDescriptors(); }

		#endregion

	}
}
