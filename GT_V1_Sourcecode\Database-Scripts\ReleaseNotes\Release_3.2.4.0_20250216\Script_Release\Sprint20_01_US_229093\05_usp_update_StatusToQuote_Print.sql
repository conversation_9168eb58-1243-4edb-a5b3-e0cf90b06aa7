﻿
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/* 
==========================================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-216601]	    Phuc Hoang			12-Dec-2024		Create			216601: Quote - New status matrix
[US-229093]		Ngai To				21-Jan-2024		Update			229093: Quote - Add traffic light colouring coding based on date offered
==========================================================================================================
*/

CREATE OR ALTER PROCEDURE [dbo].[usp_update_StatusToQuote_Print] (
    @QuoteId INT
  , @QuoteStatusNo INT
  , @UpdatedBy INT
  , @QuoteStatusName NVARCHAR(128) = NULL OUTPUT
)
AS 
    DECLARE @StatusNo INT = NULL;
	SELECT @StatusNo=QuoteStatus FROM [dbo].[tbQuote] WHERE QuoteId = @QuoteId;
    
	IF (@StatusNo = 4 OR @StatusNo = 5 OR @StatusNo = 6)
	BEGIN
		UPDATE  [dbo].[tbQuote] 
		SET     QuoteStatus = @QuoteStatusNo --Offered
		       ,QuoteOfferedDate = CURRENT_TIMESTAMP
		WHERE   QuoteId = @QuoteId;
	END
	
    SELECT @QuoteStatusName = qs.[Name] 
	FROM [dbo].[tbQuote] q
	INNER JOIN [dbo].[tbQuoteStatus] qs ON q.QuoteStatus = qs.QuoteStatusId
	WHERE q.QuoteId = @QuoteId;
GO


