﻿
GO
/*
===========================================================================================
TASK      			UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-211138]			Phuc Hoang			14-Aug-2024		UPDATE          [PROD Bug] Error when adding an Offer without Currency in HUBRFQ
===========================================================================================
*/

CREATE OR ALTER PROCEDURE [dbo].[usp_insert_SourcingResult_From_PurchaseRequest]                  
--***************************************************************************************************                      
--* RP 16.02.2010:                      
--* - add @UpdatedBy                      
--***************************************************************************************************                      
    @CustomerRequirementNo INT                      
  , @POQuoteLineDetailNo INT                      
  , @UpdatedBy INT                      
  , @SourcingResultId INT OUTPUT    
  , @LinkCurrencyMsg varchar(150)='' OUTPUT                      
AS                       
    BEGIN                      
             SET @LinkCurrencyMsg = ''                     
     --check if we have a row already                      
        IF (SELECT  COUNT(*)                      
            FROM    tbSourcingResult                      
            WHERE   CustomerRequirementNo = @CustomerRequirementNo                      
                    AND SourcingTable = 'PQ'                      
                    AND SourcingTableItemNo = @POQuoteLineDetailNo                      
           ) = 0                       
            BEGIN                     
					DECLARE @ClientCompanyNo INT                
					DECLARE @CompanyNo INT                
					DECLARE @ClientNo INT                
					DECLARE @ClientCurrencyNo INT
					DECLARE @HubCurrencyNo INT         
					DECLARE @ManufacturerNo int       
					DECLARE @ProductNo int        
					DECLARE @PackageNo int  
				
					DECLARE @ClientLinkCurrencyNo INT   
					DECLARE @LinkMultiCurrencyNo int  
					DECLARE @OfferCurrencyNo INT 
				
					DECLARE @BuyExchangeRate float      
					DECLARE @HubExchangeRate float     
					DECLARE @HubCurrencyName varchar(20)
					DECLARE @ClientCode varchar(20)     
         
                  SELECT @ClientNo = ClientNo ,@ManufacturerNo=IsNULL(ManufacturerNo,0),@ProductNo=IsNULL(ProductNo,0),@PackageNo=IsNULL(PackageNo,0)  FROM tbCustomerRequirement where CustomerRequirementId = @CustomerRequirementNo                 
                  SELECT TOP 1 @ClientCompanyNo = CompanyId FROM tbCompany WHERE ClientNo = @ClientNo AND IsPOHub =1                

				 --SELECT top 1 @HubCurrencyNo = CurrencyNo FROM tbClient where isnull(IsPOHub,0) = 1
				     SELECT @OfferCurrencyNo = CurrencyNo FROM   dbo.tbPurchaseRequestLineDetail  WHERE PurchaseRequestLineDetailId = @POQuoteLineDetailNo
				SELECT @ClientLinkCurrencyNo = l.SupplierCurrencyNo,@LinkMultiCurrencyNo = l.LinkMultiCurrencyId,@ClientCurrencyNo = l.SupplierCurrencyNo
				 from tbCurrency c left join tbLinkMultiCurrency l   on l.GlobalCurrencyNo = c.GlobalCurrencyNo 
				    WHERE l.ClientNo = @ClientNo and c.ClientNo = 114 AND c.CurrencyId = @OfferCurrencyNo
                                
      		SELECT  @HubCurrencyNo = dbo.ufn_get_HUB_DefaultCurrencyNo(@OfferCurrencyNo,@ClientNo,114)
            --Get the buy exchange rate .66
			SELECT @BuyExchangeRate = dbo.ufn_get_exchange_rate(ISNULL(@OfferCurrencyNo, 0), GETDATE())  
			--Get Hub Exchange rate           
			SELECT @HubExchangeRate = dbo.ufn_get_exchange_rate(ISNULL(@HubCurrencyNo, 0), GETDATE()) 

		  IF @LinkMultiCurrencyNo IS NULL
		   BEGIN
		     SELECT @ClientCode = ClientCode  FROM tbclient where clientid=@ClientNo
			 select @HubCurrencyName = CurrencyCode   from tbCurrency where CurrencyId = @OfferCurrencyNo
			 SET @LinkCurrencyMsg = 'Cannot use '+ISNULL(@HubCurrencyName, '')+' currency for the '+ISNULL(@ClientCode, '')+' client. Kindly contact administrator.';
			 IF @OfferCurrencyNo IS NULL
			 BEGIN
				SET @LinkCurrencyMsg = 'Unable to add Offer(s). Please update the Currency for the Offer(s) and add to the Requirement.'; 
			 END
			 SET @SourcingResultId = 0
			 RETURN
		   END
	  
	               
                INSERT  INTO [dbo].[tbSourcingResult] (                      
								 [CustomerRequirementNo]                      
								, [SourcingTable]                      
								, [SourcingTableItemNo]                      
								, [FullPart]                      
								, [Part]                      
								, [ManufacturerNo]                      
								, [DateCode]                      
								, [ProductNo]                      
								, [PackageNo]                      
								, [Quantity]                      
								, [Price]                      
								, [CurrencyNo]                      
								, [OriginalEntryDate]         
								, [Salesman]                      
								, [OfferStatusNo]                      
								, [OfferStatusChangeDate]                      
								, [OfferStatusChangeLoginNo]               
								, [SupplierNo]                      
								, [UpdatedBy]            
								, [DLUP]                      
								, [TypeName]                      
								, [Notes]                      
								, [ROHS]              
								, POHubCompanyNo                  
								, SupplierPrice          
								, ClientCompanyNo             
								, EstimatedShippingCost       
								, ClientCurrencyNo      
								, SupplierManufacturerName        
								, SupplierDateCode        
								, SupplierPackageType        
								, SupplierProductType        
								, SupplierMOQ        
								, SupplierTotalQSA        
								, SupplierLTB        
								, SupplierNotes         
								, SPQ  
								, LeadTime   
								, ROHSStatus  
								, FactorySealed  
								, MSL  
								, Buyer
								, ActualPrice
								, ActualCurrencyNo
								, ExchangeRate
							    , LinkMultiCurrencyNo
                         )                      
                                  
            SELECT 
			@CustomerRequirementNo                     
			, 'PQ'                      
			, @POQuoteLineDetailNo                      
			, prl.FullPart                      
			, prl.Part                      
			, isnull(@ManufacturerNo, 0)    
			, cr.DateCode                      
			, isnull(@ProductNo, 0)                      
			, isnull(@PackageNo, 0)  
			, 0
			--, ISNULL(cr.Quantity, 0)                      
			--, @Price                      
			 , (
		       (isnull(prld.Price,0) / @BuyExchangeRate) 
			      +  ((isnull(prld.Price,0) / @BuyExchangeRate) * ISNULL(company.UPLiftPrice,0))/100 
		       ) * @HubExchangeRate
			--, ISNULL(prld.CurrencyNo, 0)                      
			--, @HubCurrencyNo
			, dbo.ufn_get_HUB_DefaultCurrencyNo(prld.CurrencyNo,@ClientNo,114)
			, GETDATE()                     
			, ISNULL(cr.Salesman, 0)                      
			, null                      
			, GETDATE()                      
			, @UpdatedBy                      
			, cr.CompanyNo                      
			, @UpdatedBy                      
			, GETDATE()                      
			, ''                      
			, ''                      
			, cr.ROHS                    
			, prld.CompanyNo                  
			--, (isnull(prld.Price,0) / dbo.ufn_get_exchange_rate(ISNULL(prld.CurrencyNo, 0), GETDATE()))                 
		    , (
	              (isnull(prld.Price,0) / @BuyExchangeRate)
		       ) 
			, @ClientCompanyNo          
			, (isnull(country.ShippingCost,0))* @HubExchangeRate
			, @ClientCurrencyNo                                           
			, prld.ManufacturerName      
			, prld.DateCode      
			, prld.PackageType      
			, prld.ProductType      
			, prld.MOQ      
			--, prld.TotalQuantityAvailableInStock    
			, case when ISNUMERIC(prld.TotalQuantityAvailableInStock) = 1 then prld.TotalQuantityAvailableInStock else 0 end    
			, prld.LTB      
			, prld.Notes     
			, prld.SPQ  
			, prld.LeadTime  
			, prld.ROHSStatus  
			, prld.FactorySealed  
			, prld.MSL
			, @UpdatedBy  
			, ISNULL(prld.Price, 0)  
			, ISNULL(prld.CurrencyNo, 0)  
			, dbo.ufn_get_exchange_rate(ISNULL(prld.CurrencyNo, 0), GETDATE())  
			, @LinkMultiCurrencyNo          
		FROM   dbo.tbPurchaseRequestLineDetail prld                  
		JOIN tbPurchaseRequestLine prl ON prld.PurchaseRequestLineNo = prl.PurchaseRequestLineId                                       
		JOIN dbo.tbPurchaseRequest pr ON prl.PurchaseRequestNo = pr.PurchaseRequestId            
		LEFT JOIN dbo.tbCustomerRequirement cr ON prl.CustomerRequirementNo = cr.CustomerRequirementId            
		--LEFT JOIN dbo.tbCountry c ON prld.CompanyNo = c.ClientNo                 
		LEFT JOIN dbo.tbCompany company ON prld.CompanyNo = company.CompanyId         
		LEFT JOIN dbo.tbCountry country ON country.CountryId = company.DefaultPOShipCountryNo  AND company.ClientNo=country.ClientNo                                           
		WHERE  prld.PurchaseRequestLineDetailId = @POQuoteLineDetailNo     
                                                                           
          SET @SourcingResultId = SCOPE_IDENTITY()    
				 
             UPDATE tbCustomerRequirement set HasHubSourcingResult = 1 where CustomerRequirementId = @CustomerRequirementNo                     
                
            END                      
        ELSE                       
            BEGIN                      
                SET @SourcingResultId = 1 ; --spoof an OK result                 
            END                      
                      
                      
    END 


GO


