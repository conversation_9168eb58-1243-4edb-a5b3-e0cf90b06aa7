﻿
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].[usp_update_InvoiceLine]
--********************************************************************************
--* SK 29.10.2009:
--* - allow for new column - FullCustomerPart - used for searching
--*  
--* SK 29/07/2009:
--* - allow for Notes
--*
--* SK 09.07.2009:
--* - update CustomerPart
--********************************************************************************
    @InvoiceLineId int
  , @SalesOrderLineNo int = NULL
  , @ShippedBy int = NULL
  , @ShippedDate datetime = NULL
  , @CustomerPart nvarchar(30) = Null
  , @Notes nvarchar(128) = Null 
  , @UpdatedBy int = NULL
  , @PrintHazardous bit = NULL
  , @RowsAffected int = NULL OUTPUT
AS 
    UPDATE  dbo.tbInvoiceLine
    SET     SalesOrderLineNo = @SalesOrderLineNo
          , ShippedBy = @ShippedBy
          , ShippedDate = @ShippedDate
		  ,	CustomerPart = @CustomerPart
		  , Notes = @Notes
          , UpdatedBy = @UpdatedBy
          , DLUP = CURRENT_TIMESTAMP
		  , FullCustomerPart = dbo.ufn_get_fullpart(@CustomerPart)
		  , PrintHazardous = @PrintHazardous
    WHERE   InvoiceLineId = @InvoiceLineId

    SELECT  @RowsAffected = @@ROWCOUNT


GO


