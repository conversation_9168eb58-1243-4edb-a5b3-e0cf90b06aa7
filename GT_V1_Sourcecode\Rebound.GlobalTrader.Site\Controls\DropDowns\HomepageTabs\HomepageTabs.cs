﻿//-----------------------------------------------------------------------------------------
// RP 28.10.2009:
// - Set "no value" to -1 so it doesn't clash with the first tab
//-----------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;

namespace Rebound.GlobalTrader.Site.Controls.DropDowns {
	public partial class HomepageTabs : Base {

		protected override void OnInit(EventArgs e) {
			NoValue_Value = "-1";
			base.OnInit(e);
		}

		protected override void OnLoad(EventArgs e) {
            SetDropDownType("HomepageTabs");
            AddScriptReference("Controls.DropDowns.HomepageTabs.HomepageTabs");
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.DropDowns.HomepageTabs", ClientID);
			base.OnLoad(e);
		}

	}
}