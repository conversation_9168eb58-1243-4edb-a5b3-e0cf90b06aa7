Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.HomeNuggets");Rebound.GlobalTrader.Site.Controls.HomeNuggets.TableActivity=function(n){Rebound.GlobalTrader.Site.Controls.HomeNuggets.TableActivity.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.HomeNuggets.TableActivity.prototype={get_pnlCR:function(){return this._pnlCR},set_pnlCR:function(n){this._pnlCR!==n&&(this._pnlCR=n)},get_pnlGI:function(){return this._pnlGI},set_pnlGI:function(n){this._pnlGI!==n&&(this._pnlGI=n)},get_pnlQU:function(){return this._pnlQU},set_pnlQU:function(n){this._pnlQU!==n&&(this._pnlQU=n)},get_pnlPO:function(){return this._pnlPO},set_pnlPO:function(n){this._pnlPO!==n&&(this._pnlPO=n)},get_pnlSO:function(){return this._pnlSO},set_pnlSO:function(n){this._pnlSO!==n&&(this._pnlSO=n)},get_pnlCRMA:function(){return this._pnlCRMA},set_pnlCRMA:function(n){this._pnlCRMA!==n&&(this._pnlCRMA=n)},get_pnlSRMA:function(){return this._pnlSRMA},set_pnlSRMA:function(n){this._pnlSRMA!==n&&(this._pnlSRMA=n)},get_pnlCredit:function(){return this._pnlCredit},set_pnlCredit:function(n){this._pnlCredit!==n&&(this._pnlCredit=n)},get_pnlDebit:function(){return this._pnlDebit},set_pnlDebit:function(n){this._pnlDebit!==n&&(this._pnlDebit=n)},get_pnlInv:function(){return this._pnlInv},set_pnlInv:function(n){this._pnlInv!==n&&(this._pnlInv=n)},get_tblCR:function(){return this._tblCR},set_tblCR:function(n){this._tblCR!==n&&(this._tblCR=n)},get_tblGI:function(){return this._tblGI},set_tblGI:function(n){this._tblGI!==n&&(this._tblGI=n)},get_tblQU:function(){return this._tblQU},set_tblQU:function(n){this._tblQU!==n&&(this._tblQU=n)},get_tblPO:function(){return this._tblPO},set_tblPO:function(n){this._tblPO!==n&&(this._tblPO=n)},get_tblSO:function(){return this._tblSO},set_tblSO:function(n){this._tblSO!==n&&(this._tblSO=n)},get_tblCRMA:function(){return this._tblCRMA},set_tblCRMA:function(n){this._tblCRMA!==n&&(this._tblCRMA=n)},get_tblSRMA:function(){return this._tblSRMA},set_tblSRMA:function(n){this._tblSRMA!==n&&(this._tblSRMA=n)},get_tblCredit:function(){return this._tblCredit},set_tblCredit:function(n){this._tblCredit!==n&&(this._tblCredit=n)},get_tblDebit:function(){return this._tblDebit},set_tblDebit:function(n){this._tblDebit!==n&&(this._tblDebit=n)},get_tblInv:function(){return this._tblInv},set_tblInv:function(n){this._tblInv!==n&&(this._tblInv=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.HomeNuggets.TableActivity.callBaseMethod(this,"initialize");this.addRefreshEvent(Function.createDelegate(this,this.getData))},dispose:function(){this.isDisposed||(this._tblCR&&this._tblCR.dispose(),this._tblGI&&this._tblGI.dispose(),this._tblQU&&this._tblQU.dispose(),this._tblPO&&this._tblPO.dispose(),this._tblSO&&this._tblSO.dispose(),this._tblCRMA&&this._tblCRMA.dispose(),this._tblSRMA&&this._tblSRMA.dispose(),this._tblCredit&&this._tblCredit.dispose(),this._tblDebit&&this._tblDebit.dispose(),this._tblInv&&this._tblInv.dispose(),this._pnlCR=null,this._pnlGI=null,this._pnlQU=null,this._pnlPO=null,this._pnlSO=null,this._pnlCRMA=null,this._pnlSRMA=null,this._pnlCredit=null,this._pnlDebit=null,this._pnlInv=null,this._tblCR=null,this._tblGI=null,this._tblQU=null,this._tblPO=null,this._tblSO=null,this._tblCRMA=null,this._tblSRMA=null,this._tblCredit=null,this._tblDebit=null,this._tblInv=null,Rebound.GlobalTrader.Site.Controls.HomeNuggets.TableActivity.callBaseMethod(this,"dispose"))},getData:function(){this.setupLoadingState();var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/HomeNuggets/TableActivity");n.set_DataObject("TableActivity");n.set_DataAction("GetData");n.addParameter("rowcount",this._intRowCount);n.addDataOK(Function.createDelegate(this,this.getDataComplete));n.addError(Function.createDelegate(this,this.homeNuggetDataError));n.addTimeout(Function.createDelegate(this,this.homeNuggetDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getDataComplete:function(n){var i,u,t,r;for(this.showHomeNuggetContent(!0),i=n._result,this._tblCR.clearTable(),this._tblCR.show(i.CRActivity.length>0),r=0;r<i.CRActivity.length;r++)t=i.CRActivity[r],u=[$RGT_nubButton_CustomerRequirement(t.ID,t.No),$RGT_nubButton_Company(t.CMNo,t.CM),t.Date],this._tblCR.addRow(u,null);for($R_FN.showElement(this._pnlCR,i.CRActivity.length>0),this._tblGI.clearTable(),this._tblGI.show(i.GIActivity.length>0),r=0;r<i.GIActivity.length;r++)t=i.GIActivity[r],u=[$RGT_nubButton_GoodsIn(t.ID,t.No),$RGT_nubButton_Company(t.CMNo,t.CM),t.Date],this._tblGI.addRow(u,null);for($R_FN.showElement(this._pnlGI,i.GIActivity.length>0),this._tblQU.clearTable(),this._tblQU.show(i.QUActivity.length>0),r=0;r<i.QUActivity.length;r++)t=i.QUActivity[r],u=[$RGT_nubButton_Quote(t.ID,t.No),$RGT_nubButton_Company(t.CMNo,t.CM),t.Date],this._tblQU.addRow(u,null);for($R_FN.showElement(this._pnlQU,i.QUActivity.length>0),this._tblPO.clearTable(),this._tblPO.show(i.POActivity.length>0),r=0;r<i.POActivity.length;r++)t=i.POActivity[r],u=[$RGT_nubButton_PurchaseOrder(t.ID,t.No),$RGT_nubButton_Company(t.CMNo,t.CM),t.Date],this._tblPO.addRow(u,null);for($R_FN.showElement(this._pnlPO,i.POActivity.length>0),this._tblSO.clearTable(),this._tblSO.show(i.SOActivity.length>0),r=0;r<i.SOActivity.length;r++)t=i.SOActivity[r],u=[$RGT_nubButton_SalesOrder(t.ID,t.No),$RGT_nubButton_Company(t.CMNo,t.CM),t.Date],this._tblSO.addRow(u,null);for($R_FN.showElement(this._pnlSO,i.SOActivity.length>0),this._tblCRMA.clearTable(),this._tblCRMA.show(i.CRMAActivity.length>0),r=0;r<i.CRMAActivity.length;r++)t=i.CRMAActivity[r],u=[$RGT_nubButton_CRMA(t.ID,t.No),$RGT_nubButton_Company(t.CMNo,t.CM),t.Date],this._tblCRMA.addRow(u,null);for($R_FN.showElement(this._pnlCRMA,i.CRMAActivity.length>0),this._tblSRMA.clearTable(),this._tblSRMA.show(i.SRMAActivity.length>0),r=0;r<i.SRMAActivity.length;r++)t=i.SRMAActivity[r],u=[$RGT_nubButton_SRMA(t.ID,t.No),$RGT_nubButton_Company(t.CMNo,t.CM),t.Date],this._tblSRMA.addRow(u,null);for($R_FN.showElement(this._pnlSRMA,i.SRMAActivity.length>0),this._tblCredit.clearTable(),this._tblCredit.show(i.CreditActivity.length>0),r=0;r<i.CreditActivity.length;r++)t=i.CreditActivity[r],u=[$RGT_nubButton_CreditNote(t.ID,t.No),$RGT_nubButton_Company(t.CMNo,t.CM),t.Date],this._tblCredit.addRow(u,null);for($R_FN.showElement(this._pnlCredit,i.CreditActivity.length>0),this._tblDebit.clearTable(),this._tblDebit.show(i.DebitActivity.length>0),r=0;r<i.DebitActivity.length;r++)t=i.DebitActivity[r],u=[$RGT_nubButton_DebitNote(t.ID,t.No),$RGT_nubButton_Company(t.CMNo,t.CM),t.Date],this._tblDebit.addRow(u,null);for($R_FN.showElement(this._pnlDebit,i.DebitActivity.length>0),this._tblInv.clearTable(),this._tblInv.show(i.InvoiceActivity.length>0),r=0;r<i.InvoiceActivity.length;r++)t=i.InvoiceActivity[r],u=[$RGT_nubButton_Invoice(t.ID,t.No),$RGT_nubButton_Company(t.CMNo,t.CM),t.Date],this._tblInv.addRow(u,null);$R_FN.showElement(this._pnlInv,i.InvoiceActivity.length>0);this.showNoData(i.Count==0);this.hideLoading()}};Rebound.GlobalTrader.Site.Controls.HomeNuggets.TableActivity.registerClass("Rebound.GlobalTrader.Site.Controls.HomeNuggets.TableActivity",Rebound.GlobalTrader.Site.Controls.HomeNuggets.Base,Sys.IDisposable);