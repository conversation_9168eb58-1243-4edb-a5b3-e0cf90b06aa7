///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference name="~/Controls/IconButton/IconButton.js" />
///<reference name="~/Controls/ReboundTextBox/ReboundTextBox.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// SK 13.04.2010:
// - display Print/Email buttons based on PO aproval/disapproval 
//
// RP 06.01.2010:
// - fully dispose everything
/*
Marker     Changed by      Date         Remarks
[001]      Vinay           07/05/2012   This need to upload pdf document for Purchage Order section
[002]       <PERSON><PERSON>an         25/06/2015  Pdf Drag and Drop
*/
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Orders");

Rebound.GlobalTrader.Site.Pages.Orders.InternalPODetail = function(el) {
    
    Rebound.GlobalTrader.Site.Pages.Orders.InternalPODetail.initializeBase(this, [el]);
    this._blnIsApproved = false;
    
};

Rebound.GlobalTrader.Site.Pages.Orders.InternalPODetail.prototype = {

    get_intPOID: function() { return this._intPOID; }, set_intPOID: function(v) { if (this._intPOID !== v) this._intPOID = v; },
    get_ctlMainInfo: function() { return this._ctlMainInfo; }, set_ctlMainInfo: function(v) { if (this._ctlMainInfo !== v) this._ctlMainInfo = v; },
    get_ctlLines: function() { return this._ctlLines; }, set_ctlLines: function(v) { if (this._ctlLines !== v) this._ctlLines = v; },
    get_btnPrint: function() { return this._btnPrint; }, set_btnPrint: function(v) { if (this._btnPrint !== v) this._btnPrint = v; },
    get_pnlStatus: function() { return this._pnlStatus; }, set_pnlStatus: function(v) { if (this._pnlStatus !== v) this._pnlStatus = v; },
    get_lblStatus: function() { return this._lblStatus; }, set_lblStatus: function(v) { if (this._lblStatus !== v) this._lblStatus = v; },
    // [001] code start
    get_ctlPODocuments: function() { return this._ctlPODocuments; }, set_ctlPODocuments: function(v) { if (this._ctlPODocuments !== v) this._ctlPODocuments = v; },
    // [001] code end
    // [002] code start
    get_ctlPOPDFDragDrop: function() { return this._ctlPOPDFDragDrop }, set_ctlPOPDFDragDrop: function(a) { if (this._ctlPOPDFDragDrop !== a) { this._ctlPOPDFDragDrop = a } },
    // [002] code end

    initialize: function() {
        
        Rebound.GlobalTrader.Site.Pages.Orders.InternalPODetail.callBaseMethod(this, "initialize");

    },

    goInit: function() {
        if (this._btnPrint) this._btnPrint.addPrint(Function.createDelegate(this, this.printPO));
        if (this._btnPrint) this._btnPrint.addEmail(Function.createDelegate(this, this.emailPO));
        if (this._btnPrint) this._btnPrint.addExtraButtonClick(Function.createDelegate(this, this.printOtherDocs));
        if (this._ctlMainInfo) this._ctlMainInfo.addGetDataComplete(Function.createDelegate(this, this.ctlMainInfo_GetDataComplete));
        if (this._ctlMainInfo) this._ctlMainInfo.addSaveEditComplete(Function.createDelegate(this, this.ctlMainInfo_SaveEditComplete));
        if (this._ctlMainInfo) this._ctlMainInfo.addPotentialStatusChange(Function.createDelegate(this, this.ctlMainInfo_PotentialStatusChange));
        if (this._ctlLines) this._ctlLines.addPotentialStatusChange(Function.createDelegate(this, this.ctlLines_PotentialStatusChange));
        if (this._ctlMainInfo) this.setLineFieldsFromHeader();
        // [001] code start
        if (this._ctlPODocuments) this._ctlPODocuments.getData();
        // [001] code end
        // [002] code start
        if (this._ctlPOPDFDragDrop) { this._ctlPOPDFDragDrop.getData() }
        // [002] code end
        Rebound.GlobalTrader.Site.Pages.Orders.InternalPODetail.callBaseMethod(this, "goInit");
    },

    dispose: function() {
        if (this.isDisposed) return;
        if (this._btnPrint) this._btnPrint.dispose();
        if (this._ctlMainInfo) this._ctlMainInfo.dispose();
        if (this._ctlLines) this._ctlLines.dispose();
        this._btnPrint = null;
        this._ctlMainInfo = null;
        this._ctlLines = null;
        this._pnlStatus = null;
        this._lblStatus = null;
        this._intPOID = null;
        // [001] code start
        this._ctlPODocuments = null;
        // [001] code end
        Rebound.GlobalTrader.Site.Pages.Orders.InternalPODetail.callBaseMethod(this, "dispose");
    },

    printPO: function() {
        $R_FN.openPrintWindow($R_ENUM$PrintObject.InternalPurchaseOrder, this._intPOID);
    },

    emailPO: function() {
        $R_FN.openPrintWindow($R_ENUM$PrintObject.InternalPurchaseOrder, this._intPOID, true);
    },
    printOtherDocs: function() {
        if (this._btnPrint._strExtraButtonClickCommand == "EmailPOHTML") $R_FN.openPrintWindow($R_ENUM$PrintObject.PurchaseOrderEmail, this._intPOID, true);
        if (this._btnPrint._strExtraButtonClickCommand == "PrintLog") $R_FN.openPrintLogWindow($R_ENUM$PrintObject.PrintLog, this._intPOID, false, "InternalPurchaseOrder");
    },

    ctlMainInfo_SaveEditComplete: function() {
        this._ctlLines.getTabData();
    },

    ctlMainInfo_GetDataComplete: function() {
        this.setLineFieldsFromHeader();
        $R_FN.setInnerHTML(this._lblStatus, this._ctlMainInfo.getFieldValue("hidStatus"));
        this._ctlLines.updateStatus(this._ctlMainInfo.getFieldValue("hidStatusNo"));
        if (this._btnPrint) $R_FN.showElement(this._btnPrint._element, this._ctlMainInfo._blnIsApproved);
    },

    setLineFieldsFromHeader: function() {
        var strSupplierName = this._ctlMainInfo.getFieldValue("hidSupplierName");
        var strPONumber = this._ctlMainInfo.getFieldValue("hidPONumber");
        var strTotalShipInCostText = this._ctlMainInfo.getFieldValue("hidTotalShipInCostText");
        var strCurrencyCode = this._ctlMainInfo.getFieldValue("hidCurrencyCode");
        var intCurrencyNo = this._ctlMainInfo.getFieldValue("hidCurrencyNo");
        var strPODate = this._ctlMainInfo.getFieldValue("ctlDateOrdered");
        if (this._ctlLines._frmAdd) this._ctlLines._frmAdd.setFieldsFromHeader(strPONumber, strSupplierName, intCurrencyNo, strCurrencyCode, strTotalShipInCostText, strPODate);
        if (this._ctlLines._frmEdit) this._ctlLines._frmEdit.setFieldsFromHeader(strPONumber, strSupplierName, strCurrencyCode, strTotalShipInCostText);
        if (this._ctlLines._frmPost) this._ctlLines._frmPost.setFieldsFromHeader(strPONumber, strSupplierName);
        if (this._ctlLines._frmDelete) this._ctlLines._frmDelete.setFieldsFromHeader(strPONumber, strSupplierName);
        if (this._ctlLines._frmDeallocate) this._ctlLines._frmDeallocate.setFieldsFromHeader(strPONumber, strSupplierName);
        if (this._ctlLines._frmClose) this._ctlLines._frmClose.setFieldsFromHeader(strPONumber, strSupplierName);
    },

    ctlMainInfo_PotentialStatusChange: function() {
        this._ctlMainInfo.getData();
        this._ctlLines.getTabData();
        this._ctlLines.onRefreshAllocations();
    },

    ctlLines_PotentialStatusChange: function() {
        this._ctlMainInfo.getData();
        this._ctlLines.getTabData();
    },

    getStatus: function() {
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/InternalPOMainInfo");
        obj.set_DataObject("InternalPOMainInfo");
        obj.set_DataAction("GetStatus");
        obj.addParameter("id", this._intPOID);
        obj.addDataOK(Function.createDelegate(this, this.getStatusOK));
        obj.addError(Function.createDelegate(this, this.getStatusError));
        obj.addTimeout(Function.createDelegate(this, this.getStatusError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getStatusOK: function(args) {
        var strStatus = $R_FN.setCleanTextValue(args._result.Status);
        this._ctlLines.updateStatus(args._result.StatusNo);
        this._ctlMainInfo.setFieldValue("hidStatus", strStatus);
        $R_FN.setInnerHTML(this._lblStatus, strStatus);
        $R_FN.showElement(this._pnlStatus, true);
        strStatus = null;
    },

    getStatusError: function(args) {
        $R_FN.showElement(this._pnlStatus, false);
    }

};
Rebound.GlobalTrader.Site.Pages.Orders.InternalPODetail.registerClass("Rebound.GlobalTrader.Site.Pages.Orders.InternalPODetail", Rebound.GlobalTrader.Site.Pages.Content, Sys.IDisposable);
