//------------------------------------------------------------------------------------------
// RP 14.01.2010:
// - Replace Price with LandedCost on Line Allocations
//Marker     changed by      date         Remarks
//[001]      Vinay           31/08/2012   Add sales order link in purchase order allocation lines
//[002]      Vinay           01/04/2015   Ticket Number : 223
//[003]      S<PERSON>shar   16/11/2016   Added New Column in Closed section
//[004]      A<PERSON><PERSON>     18/07/2018   REB-12614 :Sales order Confirmation requirements
//[005]      A<PERSON><PERSON>     18/07/2018   Add Confirm All button on sales order line
//[006]      A<PERSON><PERSON>     11-Feb-2019  [REB-13395] Log reasons for changes to Date Promised. 
//[006]      <PERSON>     20/09/2022    RP-30 --REVIEW - How can we add an audit log to the ECCN field on each line entry
//------------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site.Controls;
using Rebound.GlobalTrader.Site;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets {
	public partial class EndUserUndertakingForm : Base {

        #region Locals
        protected FlexiDataTable _tblAll;
        protected IconButton _ibtnEUUForm;//[001]   
        //protected IconButton _ibtnEUUFormSA992C;//[001]   
        #endregion

		#region Properties

		private int _intSalesOrderID = -1;
		public int SalesOrderID {
			get { return _intSalesOrderID; }
			set { _intSalesOrderID = value; }
		}

        private int _intSalesOrderLineID = -1;
        public int SalesOrderLineID
        {
            get { return _intSalesOrderLineID; }
            set { _intSalesOrderLineID = value; }
        }

        private Boolean _IsIPOHUB = false;
        public Boolean IsIPOHUB
        {
            get { return _IsIPOHUB; }
            set { _IsIPOHUB = value; }
        }

        #endregion

        #region Overrides

        /// <summary>
        /// Oninit
        /// </summary>
        /// <param name="e"></param>
        protected override void OnInit(EventArgs e) {
			base.OnInit(e);
			WireUpControls();
			AddScriptReference("Controls.Nuggets.EndUserUndertakingForm.EndUserUndertakingForm.js");
			TitleText = Functions.GetGlobalResource("Nuggets", "EndUserUndertakingForm");
			if (_objQSManager.SalesOrderID > 0) _intSalesOrderID = _objQSManager.SalesOrderID;
            //[001] code start
            if (_objQSManager.SalesOrderLineID > 0) _intSalesOrderLineID = _objQSManager.SalesOrderLineID;
            //[001] code end
			SetupTables();
		}

		/// <summary>
		/// OnPreRender
		/// </summary>
		/// <param name="e"></param>
		protected override void OnPreRender(EventArgs e) {
			//buttons
            _IsIPOHUB = Convert.ToBoolean(System.Configuration.ConfigurationManager.AppSettings["IsIPOHUB"]);
            SetupScriptDescriptors();
			base.OnPreRender(e);
		}

		/// <summary>
		/// OnLoad
		/// </summary>
		/// <param name="e"></param>
		protected override void OnLoad(EventArgs e) {
			base.OnLoad(e);
        }

		#endregion

        /// <summary>
        /// Sets up AJAX script descriptors
        /// </summary>
        private void SetupScriptDescriptors()
        {
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Nuggets.EndUserUndertakingForm", ctlDesignBase.ClientID);
            _scScriptControlDescriptor.AddProperty("intSalesOrderID", _intSalesOrderID);
            _scScriptControlDescriptor.AddComponentProperty("tblAll", _tblAll.ClientID);
            _scScriptControlDescriptor.AddProperty("ibtnEUUForm", _ibtnEUUForm.ClientID);
            //_scScriptControlDescriptor.AddProperty("ibtnEUUFormSA992C", _ibtnEUUFormSA992C.ClientID);
        }

		private void SetupTables() {

            //_tblAll.Columns.Add(new FlexiDataColumn("", Unit.Pixel(15), false, HorizontalAlign.Center));
            _tblAll.Columns.Add(new FlexiDataColumn("LineNo", WidthManager.GetWidth(WidthManager.ColumnWidth.LineNo)));
			_tblAll.Columns.Add(new FlexiDataColumn("PartNo", "CustomerPart", WidthManager.GetWidth(WidthManager.ColumnWidth.PartNo)));
			_tblAll.Columns.Add(new FlexiDataColumn("EUUFormRequired", WidthManager.GetWidth(WidthManager.ColumnWidth.ManufacturerCode)));
			_tblAll.Columns.Add(new FlexiDataColumn("DateUploaded", WidthManager.GetWidth(WidthManager.ColumnWidth.Quantity)));
			_tblAll.Columns.Add(new FlexiDataColumn("UploadedBy", WidthManager.GetWidth(WidthManager.ColumnWidth.Quantity)));
			_tblAll.Columns.Add(new FlexiDataColumn("Uploaded", WidthManager.GetWidth(WidthManager.ColumnWidth.Quantity)));

        }

        /// <summary>
        /// Wire up controls from the ascx
        /// </summary>
        private void WireUpControls()
        {
            _tblAll = (FlexiDataTable)ctlDesignBase.FindContentControl("tblAll");
            _ibtnEUUForm = (IconButton)FindIconButton("ibtnEUUForm");
           // _ibtnEUUFormSA992C = (IconButton)FindIconButton("ibtnEUUFormSA992C");
        }
    }
}
