using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.ItemSearch {
	public partial class SRMALines : Base {

		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
			SetItemSearchType("SRMALines");
			AddScriptReference("Controls.ItemSearch.SRMALines.SRMALines.js");
		}

		protected override void OnLoad(EventArgs e) {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.ItemSearch.SRMALines", ctlDesignBase.ClientID);
			base.OnLoad(e);
		}

		protected override void OnPreRender(EventArgs e) {
			ctlDesignBase.MakeChildControls();
			ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("Number", WidthManager.GetWidth(WidthManager.ColumnWidth.SystemDocumentNumber), true));
			ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("Company", Unit.Empty, true));
			ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("PartNo", WidthManager.GetWidth(WidthManager.ColumnWidth.PartNo), true));
			ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("SupplierRMADate", WidthManager.GetWidth(WidthManager.ColumnWidth.Date), true));
			ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("Quantity", WidthManager.GetWidth(WidthManager.ColumnWidth.Quantity), true));
			ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("Buyer", WidthManager.GetWidth(WidthManager.ColumnWidth.LoginName), true));
			ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("PurchaseOrder", WidthManager.GetWidth(WidthManager.ColumnWidth.SystemDocumentNumber), true));
			base.OnPreRender(e);
		}
	}
}