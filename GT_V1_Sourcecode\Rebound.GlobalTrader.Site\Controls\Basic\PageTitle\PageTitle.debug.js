///<reference name="MicrosoftAjax.js" />
///<reference path="~/Common/Data/Data.js" />
///<reference path="~/Common/Functions/Functions.js" />
///<reference path="~/Controls/IconButton/IconButton.js" />
//----------------------------------------------------------------------------------------------------------------
// RP 08.10.2010:
// - allow show / hide of tab
//
// RP 06.01.2010:
// - dispose everything fully
//----------------------------------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls");

Rebound.GlobalTrader.Site.Controls.PageTitle = function(element) { 
	Rebound.GlobalTrader.Site.Controls.PageTitle.initializeBase(this, [element]);
	this._aryTabIDs = [];
	this._intCurrentTab = 0;
};

Rebound.GlobalTrader.Site.Controls.PageTitle.prototype = {

    get_ctlH3: function() { return this._ctlH3; }, set_ctlH3: function(value) { if (this._ctlH3 !== value) this._ctlH3 = value; },
    get_aryTabIDs: function() { return this._aryTabIDs; }, set_aryTabIDs: function(value) { if (this._aryTabIDs !== value) this._aryTabIDs = value; },
    get_intCurrentTab: function() { return this._intCurrentTab; }, set_intCurrentTab: function(value) { if (this._intCurrentTab !== value) this._intCurrentTab = value; },

    initialize: function() {
        Rebound.GlobalTrader.Site.Controls.PageTitle.callBaseMethod(this, "initialize");
    },

    dispose: function() {
        if (this.isDisposed) return;
        if (this._element) $clearHandlers(this._element);
        this._aryTabIDs = null;
        this._ctlH3 = null;
        this._intCurrentTab = null;
        Rebound.GlobalTrader.Site.Controls.PageTitle.callBaseMethod(this, "dispose");
        this.isDisposed = true;
    },

    updateTitle: function(strTitle) {
        if (!strTitle || !this._ctlH3) return;
        $R_FN.setInnerHTML(this._ctlH3, strTitle);
        document.title = String.format("{0} - {1}", $R_RES.AppTitle, strTitle);
    },

    updateTitleColor: function(strClass, blnEARI) {
        if (!strClass || !this._ctlH3) return;
        if (blnEARI)
            Sys.UI.DomElement.addCssClass(this._ctlH3, strClass);
        else
            Sys.UI.DomElement.removeCssClass(this._ctlH3, strClass);
    },

    selectTab: function(intTab) {
        if (intTab < 0 || intTab >= this._aryTabIDs.length) return;
        for (var i = 0, l = this._aryTabIDs.length; i < l; i++) {
            var tab = $get(String.format("{0}_tab", this._aryTabIDs[i]));
            tab.className = (i == intTab) ? "tabSelected" : "tab";
            tab = null;
        }
        this._intCurrentTab = intTab;
    },

    getTitle: function() {
        return this._ctlH3.innerHTML;
    },

    showTab: function(intTab, bln) {
        var tab = $get(String.format("{0}_tab", this._aryTabIDs[intTab]));
        if (tab) $R_FN.showElement(tab, bln);
        tab = null;
    }

};

Rebound.GlobalTrader.Site.Controls.PageTitle.registerClass("Rebound.GlobalTrader.Site.Controls.PageTitle", Sys.UI.Control, Sys.IDisposable);