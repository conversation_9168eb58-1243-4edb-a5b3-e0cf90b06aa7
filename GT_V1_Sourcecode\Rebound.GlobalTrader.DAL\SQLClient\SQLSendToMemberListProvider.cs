﻿using System;
using System.Collections.Generic;
using System.Data.Common;
using System.Data.SqlClient;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.UI.WebControls.WebParts;

namespace Rebound.GlobalTrader.DAL.SQLClient
{
    //Anuj
    public class SQLSendToMemberListProvider : SendToMemberListProvider
    {
        public override List<SendToMemberListDetails> GetMembersLoginNoList(int SecurityGroupNo)
        {
            List<SendToMemberListDetails> list = new List<SendToMemberListDetails>();
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_selectAll_HUBRFQMembersBasedOnSecurityGroup_for_SendingEmail", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.AddWithValue("@SecurityGroupNo", SecurityGroupNo);
                cn.Open();
                DbDataReader dr = ExecuteReader(cmd);
                if (dr.HasRows)
                {
                    while (dr.Read())
                    {
                        SendToMemberListDetails obj = new SendToMemberListDetails();
                        obj.LoginNo = GetReaderValue_String(dr, "LoginNo", "");
                        list.Add(obj);
                        obj = null;
                    }
                }
                else
                {
                    list = null;
                }
                return list;
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to GetMembersLoginNoList", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public  List<SendToMemberListDetails> GetMembersLoginNoListForpo(int SecurityGroupNo)
        {
            List<SendToMemberListDetails> list = new List<SendToMemberListDetails>();
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("GetEMailForGroupMembers", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.AddWithValue("@SecurityGroupNo", SecurityGroupNo);
                cn.Open();
                DbDataReader dr = ExecuteReader(cmd);
                if (dr.HasRows)
                {
                    while (dr.Read())
                    {
                        SendToMemberListDetails obj = new SendToMemberListDetails();
                        obj.LoginNo = GetReaderValue_String(dr, "LoginNo", "");
                        list.Add(obj);
                        obj = null;
                    }
                }
                else
                {
                    list = null;
                }
                return list;
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to GetMembersLoginNoList", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public List<SendToMemberListDetails> GetMembersLoginNoListForOGELECCN(int SecurityGroupNo)
        {
            List<SendToMemberListDetails> list = new List<SendToMemberListDetails>();
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("GetEMailForGroupMembersOGELECCN", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.AddWithValue("@SecurityGroupNo", SecurityGroupNo);
                cn.Open();
                DbDataReader dr = ExecuteReader(cmd);
                if (dr.HasRows)
                {
                    while (dr.Read())
                    {
                        SendToMemberListDetails obj = new SendToMemberListDetails();
                        obj.LoginNo = GetReaderValue_String(dr, "LoginNo", "");
                        list.Add(obj);
                        obj = null;
                    }
                }
                else
                {
                    list = null;
                }
                return list;
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to GetMembersLoginNoList", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override List<SendToMemberListDetails> GetRequiredSecurityGroupList()
        {
            List<SendToMemberListDetails> list = new List<SendToMemberListDetails>();
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_selectAll_filter_SecurityGroup", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cn.Open();
                DbDataReader dr = ExecuteReader(cmd);
                if (dr.HasRows)
                {
                    while (dr.Read())
                    {
                        SendToMemberListDetails obj = new SendToMemberListDetails();
                        obj.SecurityGroupNo = GetReaderValue_String(dr, "SecurityGroupId", "");
                        list.Add(obj);
                        obj = null;
                    }
                }
                else
                {
                    list = null;
                }
                return list;
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to GetMembersLoginNoList", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

    }
}
