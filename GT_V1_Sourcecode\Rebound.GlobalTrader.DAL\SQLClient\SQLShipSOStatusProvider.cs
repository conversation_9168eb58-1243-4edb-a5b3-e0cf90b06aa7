﻿//Marker     Changed by       Date         Remarks
//[001]      A<PERSON><PERSON><PERSON>   05/10/2021   Add new dropdown for Ship SO Status
using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using System.Web.Caching;
using System.Data.Common;

namespace Rebound.GlobalTrader.DAL.SqlClient
{
    public class SqlShipSOStatusProvider : ShipSOStatusProvider
    {

        /// <summary>
        /// DropDownForClient 
        /// Calls [usp_dropdown_ShipSo_Status]
        /// </summary>
        public override List<ShipSOStatusDetails> DropDownForClient()
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_dropdown_ShipSo_Status", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<ShipSOStatusDetails> lst = new List<ShipSOStatusDetails>();
                while (reader.Read())
                {
                    ShipSOStatusDetails obj = new ShipSOStatusDetails();
                    obj.StatusId = GetReaderValue_Int32(reader, "StatusId", 0);
                    obj.StatusName = GetReaderValue_String(reader, "StatusName", "");
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException)
            {
                //LogException(sqlex);
                //throw new Exception("Failed to get Logins", sqlex);
                return null;
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// DropDownForReadyStatus 
        /// Calls [usp_dropdown_ShipSoReady_Status]
        /// </summary>
        public override List<ShipSOStatusDetails> DropDownForReadyStatus()
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_dropdown_ShipSoReady_Status", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<ShipSOStatusDetails> lst = new List<ShipSOStatusDetails>();
                while (reader.Read())
                {
                    ShipSOStatusDetails obj = new ShipSOStatusDetails();
                    obj.StatusId = GetReaderValue_Int32(reader, "StatusId", 0);
                    obj.StatusName = GetReaderValue_String(reader, "StatusName", "");
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException)
            {
                //LogException(sqlex);
                //throw new Exception("Failed to get Logins", sqlex);
                return null;
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

    }
}
