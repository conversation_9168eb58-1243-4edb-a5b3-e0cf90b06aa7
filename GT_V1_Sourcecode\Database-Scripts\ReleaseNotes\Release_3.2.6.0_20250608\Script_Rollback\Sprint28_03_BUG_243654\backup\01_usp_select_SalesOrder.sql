﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/*     
===========================================================================================    
TASK        UPDATED BY       DATE          ACTION    DESCRIPTION    
[001]   	Anand Gupta   	20/01/2020  	Update   Add two column GlobalCurrencyName,LocalCurrencyid    
[RP-1238]  	Ravi B<PERSON>han  	09-10-2023  	Update   If EI selected and not booked, checked + Request approval button should be greyed out    
[RP-236]  	Ab<PERSON>av <PERSON>xena  10-01-2024  	Update   For RP-236.    
[RP-2623]  	Abhinav <PERSON>xena  18-01-2024  	Update   For RP-2623.    
[US-201037] An.TranTan   	22-04-2024  	Update   Add flag to decide disable OGEL field.    
[US-239019] CuongDox      	14-Apr-2025  	CREATE   Quote Task Reminder Enhancement on Quote/ HUBRFQ  
[US-232193]	An.TranTan		15-04-2025		Update	Check if SO currency <> Customer currency
[US-225812] Trung Pham		20-Apr-2025		UPDATE	Get country and related warning message 
===========================================================================================    
*/
CREATE OR ALTER PROCEDURE [dbo].[usp_select_SalesOrder] @SalesOrderId INT,
	@LoginId INT = 0,
	@Clientno INT = 0
AS
BEGIN
	DECLARE @IsPaidByCreditCard BIT = 0;
	--[001]                              
	DECLARE @SOLineNo INT = 0;
	DECLARE @WareHouseNo INT = 0;

	SELECT TOP 1 @SOLineNo = salesorderlineid
	FROM tbsalesorderLine
	WHERE SalesOrderNo = @SalesOrderId
		AND Posted = 1

	SELECT TOP 1 @WareHouseNo = sk.warehouseno
	FROM dbo.tbAllocation al
	JOIN tbStock sk ON sk.StockId = al.StockNo
	WHERE al.SalesOrderLineNo = @SOLineNo

	DECLARE @LocalCurrencyNo INT = 0;

	SELECT @LocalCurrencyNo = LocalCurrencyNo
	FROM tbwarehouse
	WHERE WarehouseId = @WareHouseNo

	---Start RP-2623---          
	DECLARE @IsRedFlaggedSO BIT = 0;
	DECLARE @LineSerialNo NVARCHAR(500) = '';

	CREATE TABLE #RedFlagCheckTbl (
		ID INT IDENTITY(1, 1),
		DatePromised DATETIME,
		Quantity INT,
		QuantityInStock INT,
		SOSerialNo INT,
		SalesOrderNo INT
		)

	INSERT INTO #RedFlagCheckTbl
	SELECT sol.DatePromised,
		sol.Quantity,
		ISNULL((
				SELECT sum(isnull(QuantityInStock, 0))
				FROM tbStock stk
				JOIN dbo.tbAllocation al ON al.StockNo = stk.StockId
				WHERE sol.SalesOrderLineId = al.SalesOrderLineNo
				), 0) AS QuantityInStock,
		sol.SOSerialNo,
		sol.SalesOrderNo
	FROM tbSalesOrderLine sol
	WHERE sol.SalesOrderNo = @SalesOrderId
		AND ISNULL(sol.Closed, 0) = 0

	SELECT @IsRedFlaggedSO = CAST(CASE 
				WHEN COUNT(1) > 0
					THEN 1
				ELSE 0
				END AS BIT)
	FROM #RedFlagCheckTbl
	WHERE --Quantity!=QuantityInStock AND       
		(DATEDIFF(DAY, GETDATE(), DatePromised)) <= 0

	SELECT @LineSerialNo = STUFF((
				SELECT ', ' + CAST(SOSerialNo AS NVARCHAR(100))
				FROM #RedFlagCheckTbl b
				WHERE b.SalesOrderNo = a.SalesOrderNo
					--AND b.Quantity!=b.QuantityInStock       
					AND (DATEDIFF(DAY, GETDATE(), b.DatePromised)) <= 0
				ORDER BY SOSerialNo ASC
				FOR XML PATH('')
				), 1, 1, '')
	FROM #RedFlagCheckTbl a
	WHERE a.SalesOrderNo = @SalesOrderId
	GROUP BY SalesOrderNo

	----END-----          
	--[001]                      
	IF EXISTS (SELECT 1
			FROM tbSOPaymentInfo
			WHERE SalesOrderNo = @SalesOrderId
				AND INACTIVE = 0
			)
		SET @IsPaidByCreditCard = CAST(1 AS BIT)

	SELECT so.*,
		soval.LineSubTotal,
		soval.TotalTax,
		soval.TotalValue,
		dbo.ufn_check_CurrencyInSameFaimly(so.ClientNo, so.CurrencyNo) AS IsCurrencyInSameFaimly,
		@IsPaidByCreditCard AS IsPaidByCreditCard,
		ISNULL(w.WarehouseName, '') AS PreferredWarehouseName,
		iSNULL(w.WarehouseId, 0) AS PreferredWarehouseNo,
		g.GlobalCurrencyName,
		iSNULL(@LocalCurrencyNo, 0) AS LocalCurrencyid,
		CAST([dbo].[ufn_OGELGetSOExportApproved](@SalesOrderId) AS BIT) AS IsExportApprove,
		[dbo].[ufn_SOAUthDisabledReason](@SalesOrderId) AS SOAuthDisabledReason,
		c.PurchasingNotes,
		so.CompanyOnStop,
		dbo.ufn_GetSecurityPermissions(@LoginId, @Clientno, 30005032) AS IsAllowCheckViewPermission,
		ISNULL(so.IsAllowReadyTOShip, 0) AS AllowReadyToShipTicked,
		ISNULL(@IsRedFlaggedSO, 0) AS RedFlagged,
		ISNULL(@LineSerialNo, '') AS LineSerialNo
		--, @EiSelectedButNotBooked as EiSelectedButNotBooked    
		,
		dbo.ufn_check_OGEL_Disabled(@SalesOrderId) AS IsOGELDisabled --[US-201037]  ,  
		,
		dbo.ufn_check_SODifferCurrency(@SalesOrderId) AS IsDifferCurrencyFromCustomer --[US-232193]
		,
		(
			SELECT count(*)
			FROM tbToDo td WITH (NOLOCK)
			WHERE td.SalesOrderNo = so.SalesOrderId
			) AS TaskCount,
		CASE 
			WHEN EXISTS (
					SELECT TOP 1 1
					FROM tbToDo td WITH (NOLOCK)
					WHERE td.SalesOrderNo = so.SalesOrderId
						AND td.IsComplete = 0
					)
				THEN 1
			ELSE 0
			END AS HasUnFinishedTask,
		ct.CountryName,
		swm.WarningText,
		CAST(CASE 
				WHEN EXISTS (
						SELECT 1
						FROM tbSystemWarningMessage
						WHERE SystemWarningMessageId = swm.SystemWarningMessageId
						)
					THEN 1
				ELSE 0
				END AS BIT) AS IsHasCountryMessage
	FROM vwSalesOrder so
	JOIN dbo.ufn_get_salesOrder_values(@SalesOrderId) soval ON so.SalesOrderId = soval.SalesOrderId
	LEFT JOIN dbo.tbCompany c ON c.CompanyId = so.CompanyNo
	LEFT JOIN dbo.tbWarehouse w ON w.WarehouseId = c.WarehouseNo
	LEFT JOIN dbo.tbAddress ad ON so.ShipToAddressNo = ad.AddressId
	LEFT JOIN dbo.tbCompanyAddress ca ON c.CompanyId = ca.CompanyNo
		AND ca.DefaultBilling = 1
	LEFT JOIN dbo.tbAddress bad ON ca.AddressNo = bad.AddressId
	LEFT JOIN dbo.tbCountry ct ON bad.CountryNo = ct.CountryId
		AND ISNULL(ct.InActive, 0) = 0
	LEFT JOIN dbo.tbSystemWarningMessage swm ON ct.CountryId = swm.ApplyTo
		AND swm.ClientNo = so.ClientNo
		AND ISNULL(swm.InActive, 0) = 0
	--[001] code start                                        
	LEFT JOIN tbGlobalCurrencyList g ON g.GlobalCurrencyId = so.GlobalCurrencyNo
	--[001] code end                                
	WHERE so.SalesOrderId = @SalesOrderId
END
GO


