﻿using Microsoft.Azure.Storage;
using Microsoft.Azure.Storage.Auth;
using Microsoft.Azure.Storage.Blob;
using Newtonsoft.Json;
using Rebound.GlobalTrader.BLL;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.IO;
using System.Linq;
using System.Net;
using System.Reflection;
using System.Text;
using System.Text.RegularExpressions;
using System.Web;
using System.Web.Script.Serialization;
using System.Web.Services;
using static Rebound.GlobalTrader.Site.Controls.Nuggets.Data.UtilityProspectiveOfferImport;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets.Data
{
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class HubImportSourcingResult : GlobalTrader.Site.Data.Base
    {
        public override void ProcessRequest(HttpContext context)
        {
            if (base.init(context))
            {

                if (string.IsNullOrEmpty(Action))
                {
                    Action = context.Request.QueryString["action"];
                }
                switch (Action)
                {
                    case "ImportExcelData": ImportExcelData(context); break;
                    case "ImportBOMSourcingResults": ImportBOMSourcingResults(context); break;
                    case "GetRawDataForCorrection": GetRawDataForCorrection(context); break;
                    case "GetMfrAutoComplete": GetMfrAutoComplete(context); break;
                    case "GetSupplierAutoComplete": GetSupplierAutoComplete(context); break;
                    case "UpdateIncorrectData": UpdateIncorrectData(context); break;
                    case "BulkSaveInlineData": BulkSaveInlineData(context); break;
                    default: WriteErrorActionNotFound(); break;
                }
            }
        }

        public void ImportExcelData(HttpContext context)
        {
            try
            {
                bool IsLimitExceeded = false;
                string LimitErrorMessage = "";
                string originalFilename = GetFormValue_String("originalFilename");
                string generatedFilename = GetFormValue_String("generatedFilename");
                string chkcolumnheader = GetFormValue_String("ColumnHeader");
                int hubrfqId = GetFormValue_Int("BOMNo");

                string filepathtempfolder = FileUploadManager.GetTemporaryUploadFilePath() + generatedFilename;
                string filepath = ConfigurationManager.AppSettings["ExcelDocumentPhysicalURLForAzureBlob"].ToString() + @"BOM/" + generatedFilename;
                string accountname = ConfigurationManager.AppSettings.Get("StorageName");
                string accesskey = ConfigurationManager.AppSettings.Get("StorageKey1");
                StorageCredentials creden = new StorageCredentials(accountname, accesskey);
                CloudStorageAccount acc = new CloudStorageAccount(creden, useHttps: true);
                CloudBlobClient client = acc.CreateCloudBlobClient();
                CloudBlobContainer cont = client.GetContainerReference("gtdocmgmt");
                if (cont.Exists())
                {
                    CloudBlobDirectory directory = cont.GetDirectoryReference("BOM");
                    CloudBlockBlob cblob = directory.GetBlockBlobReference(GetFormValue_String("generatedFilename"));
                    if (cblob.Exists())
                    {
                        string fileExtension = Path.GetExtension(filepath);
                        DataTable dt = fileExtension == ".csv" ? ConvertCSVToDataTable(filepathtempfolder, chkcolumnheader, generatedFilename)
                            : ConvertExcelToDataTable(filepathtempfolder, chkcolumnheader, generatedFilename);

                        int filelogid = 0;
                        if (dt.Rows.Count <= Convert.ToInt32(ConfigurationManager.AppSettings["MaxUploadRowCount"]))
                        {
                            SaveImportData(dt, originalFilename, generatedFilename, hubrfqId);
                            IsLimitExceeded = false;
                            LimitErrorMessage = "";
                        }
                        else
                        {
                            cblob.DeleteIfExists();
                            IsLimitExceeded = true;
                            LimitErrorMessage = string.Format(ConfigurationManager.AppSettings["RowCountWarningMessage"].ToString(), ConfigurationManager.AppSettings["MaxUploadRowCount"].ToString());
                        }

                        dt.Dispose();

                        JsonObject jsn = new JsonObject();
                        jsn.AddVariable("FileLogId", filelogid);
                        jsn.AddVariable("IsLimitExceeded", IsLimitExceeded);
                        jsn.AddVariable("LimitErrorMessage", LimitErrorMessage);
                        jsn.AddVariable("OriginalFileName", originalFilename);
                        OutputResult(jsn);
                        jsn.Dispose(); jsn = null;
                    }
                    else
                    {
                        throw new Exception("File not uploaded successfully.", new Exception("FileNotFound"));
                    }
                }

            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Error at method ImportExcelData : " + ex.Message);
                WriteError(ex);
            }
        }

        private void ImportBOMSourcingResults(HttpContext context)
        {
            try
            {
                var serializer = new JavaScriptSerializer
                {
                    MaxJsonLength = Int32.MaxValue
                };
                int importCount = Stock.ImportBOMSourcingResults(SessionManager.LoginID ?? 0, out string outputMessage);
                ImportReturn obj = new ImportReturn()
                {
                    IsError = importCount <= 0,
                    Message = outputMessage
                };
                context.Response.Write(serializer.Serialize(obj));
            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Inside ImportBOMSourcingResults : " + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                WriteError(ex);
            }
        }

        private void SaveImportData(DataTable dt, string originalFilename, string generatedFilename, int hubrfqId)
        {
            try
            {
                var loginId = SessionManager.LoginID ?? 0;
                //delete previous temp data
                Stock.DeleteHubSourcingTempData(loginId);

                //add new columns
                List<DataColumn> additionalColumns = new List<DataColumn>()
                {
                    new DataColumn("BOMNo", typeof(Int32)) { DefaultValue = hubrfqId },
                    new DataColumn("CreatedBy", typeof(Int32)) { DefaultValue = loginId },
                    new DataColumn("OriginalFilename", typeof(string)) { DefaultValue = originalFilename },
                    new DataColumn("GeneratedFilename", typeof(string)) { DefaultValue = generatedFilename }
                };
                dt.Columns.AddRange(additionalColumns.ToArray());
                Stock.SaveBOMImportSourcingData(dt, originalFilename, generatedFilename, hubrfqId, loginId);
            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Error in SaveImportData: " + ex.InnerException.Message);
                WriteError(ex);
            }
        }

        private void GetRawDataForCorrection(HttpContext context)
        {
            try
            {
                int pageNumber = GetFormValue_Int("pq_curpage");
                int pageSize = GetFormValue_Int("pq_rpp");
                bool showMismatchOnly = GetFormValue_Boolean("ShowMismatchOnly");
                HubSourcingResultImportTemp rawData = BLL.Stock.GetHubSourcingTempData(SessionManager.LoginID ?? 0, pageNumber, pageSize, showMismatchOnly);
                rawData.FirstErrorColIndx = GetFirstErrorColumnIndex(rawData.RawDataList);

                // Serialize object to JObject
                string jsonString = JsonConvert.SerializeObject(rawData);
                context.Response.ContentType = "application/json";
                context.Response.Write(jsonString);
            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Error at method GetRawDataForCorrection : " + ex.Message);
                WriteError(ex);
            }
        }

        private void GetMfrAutoComplete(HttpContext context)
        {
            try
            {
                JsonObject jsnRows = new JsonObject(true);
                string term = context.Request.QueryString["term"];
                if (term == "<")
                {
                    OutputResult(jsnRows);
                    jsnRows.Dispose(); jsnRows = null;
                    return;
                }
                term = Functions.RemovePunctuationRetainingPercentSigns(HttpUtility.UrlDecode(term));
                term = term.Replace(" ", "");
                term += '%';
                List<Manufacturer> lstMfr = Manufacturer.AutoSearch(term, false);

                for (int i = 0; i < lstMfr.Count; i++)
                {
                    if (i < lstMfr.Count)
                    {
                        JsonObject jsnRow = new JsonObject();
                        jsnRow.AddVariableNew("value", lstMfr[i].ManufacturerId);
                        jsnRow.AddVariableNew("label", lstMfr[i].ManufacturerName);
                        jsnRows.AddVariableNew(jsnRow);
                        jsnRow.Dispose();
                        jsnRow = null;
                    }
                }
                OutputResult(jsnRows);
                jsnRows.Dispose(); jsnRows = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }
        private void GetSupplierAutoComplete(HttpContext context)
        {
            try
            {
                JsonObject jsnRows = new JsonObject(true);
                string term = context.Request.QueryString["term"];

                if (term == "<")
                {
                    OutputResult(jsnRows);
                    jsnRows.Dispose(); jsnRows = null;
                    return;
                }
                term = Functions.RemovePunctuationRetainingPercentSigns(HttpUtility.UrlDecode(term));
                term = term.Replace(" ", "");
                term += '%';
                List<Company> lst = Company.AutoSearchForSuppliers(SessionManager.ClientID, term);

                for (int i = 0; i < lst.Count; i++)
                {
                    if (i < lst.Count)
                    {
                        JsonObject jsnRow = new JsonObject();
                        jsnRow.AddVariableNew("value", lst[i].CompanyId);
                        jsnRow.AddVariableNew("label", lst[i].CompanyName);
                        jsnRows.AddVariableNew(jsnRow);
                        jsnRow.Dispose();
                        jsnRow = null;
                    }
                }
                OutputResult(jsnRows);
                jsnRows.Dispose(); jsnRows = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

        private void UpdateIncorrectData(HttpContext context)
        {
            try
            {
                string incorrectValue = Functions.BeautifyPartNumber(context.Request.QueryString["incorrectValue"]);
                string newValue = Functions.BeautifyPartNumber(context.Request.QueryString["newValue"]);
                string type = context.Request.QueryString["Type"];
                var total = BLL.Stock.CorrectHubSourcingTempData(incorrectValue, newValue, type, SessionManager.LoginID ?? 0);
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("\"succeed\"", total > 0);
                jsn.AddVariable("\"message\"", string.Format("Replace successful: {0} record(s) updated.", total.ToString()));
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Error at method UpdateIncorrectData : " + ex.Message);
                WriteError(ex);
            }
        }

        private void BulkSaveInlineData(HttpContext context)
        {
            try
            {
                using (var reader = new System.IO.StreamReader(context.Request.InputStream))
                {
                    var requestBody = reader.ReadToEnd();
                    var total = BLL.Stock.BulkSaveHubSourcingTempData(requestBody, SessionManager.LoginID ?? 0);
                    JsonObject jsn = new JsonObject();

                    jsn.AddVariable("\"succeed\"", total > 0);
                    jsn.AddVariable("\"message\"", string.Format("Update successful: {0} record(s) updated.", total.ToString()));
                    OutputResult(jsn);
                    jsn.Dispose(); jsn = null;
                }
            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Error at method BulkSaveTemp : " + ex.Message);
                WriteError(ex);
            }
        }

        #region convert functions

        private DataTable ConvertCSVToDataTable(string filepath, string chkhead, string FileName)
        {
            DataTable dtCsv = new DataTable();
            dtCsv.Clear();
            Regex CSVParser = new Regex(",(?=(?:[^\"]*\"[^\"]*\")*(?![^\"]*\"))");
            try

            {
                string Fulltext;
                WebClient web = new WebClient();
                System.IO.Stream stream = web.OpenRead(filepath);
                using (StreamReader sr = new StreamReader(stream, Encoding.Default))
                {
                    while (!sr.EndOfStream)
                    {
                        Fulltext = sr.ReadToEnd().ToString(); //read full file text  
                        Fulltext = Regex.Replace(Fulltext, "\"[^\"]*(?:\"\"[^\"]*)*\"", m => m.Value.Replace("\n", ""));
                        string[] rows = Fulltext.Split('\n'); //split full file text into rows  
                        if (chkhead == "YES")
                        {
                            for (int i = 0; i < rows.Length - 1; i++)
                            {
                                string[] rowValues = CSVParser.Split(rows[i]); //split each row with comma to get individual values  
                                {
                                    if (i == 0)
                                    {
                                        for (int j = 0; j < rowValues.Length; j++)
                                        {
                                            dtCsv.Columns.Add(Functions.CleanDatabaseFilter(Functions.ReplaceLineBreaks(Functions.CleanJunkCharInCSV(Functions.FormatStringForDatabase((rowValues[j].ToString()))))));
                                        }
                                    }
                                    else
                                    {
                                        DataRow dr = dtCsv.NewRow();
                                        for (int k = 0; k < rowValues.Length; k++)
                                        {
                                            dr[k] = Functions.CleanJunkCharInCSV(Functions.CleanDatabaseForStockTool(Functions.CleanCarriageReturnTabAndNewLineCharacter(rowValues[k].ToString())));
                                        }
                                        dtCsv.Rows.Add(dr); //add other rows 

                                    }
                                }
                            }
                        }
                        else
                        {
                            for (int i = 0; i < rows.Length - 1; i++)
                            {
                                string[] rowValues = CSVParser.Split(rows[i]); //split each row with comma to get individual values  
                                {
                                    if (i == 0)
                                    {
                                        int counter = 1;

                                        for (int j = 0; j < rowValues.Length; j++)
                                        {
                                            dtCsv.Columns.Add("F" + counter); //Add header if not have header like F1
                                            counter++;

                                        }
                                        DataRow dr = dtCsv.NewRow();
                                        for (int k = 0; k < rowValues.Length; k++)
                                        {
                                            dr[k] = Functions.CleanJunkCharInCSV(Functions.CleanDatabaseForStockTool((rowValues[k].ToString())));
                                        }
                                        dtCsv.Rows.Add(dr); //add other rows 

                                    }
                                    else
                                    {

                                        DataRow dr = dtCsv.NewRow();
                                        for (int k = 0; k < rowValues.Length; k++)
                                        {

                                            dr[k] = Functions.CleanJunkCharInCSV(Functions.CleanDatabaseForStockTool((rowValues[k].ToString())));
                                        }
                                        dtCsv.Rows.Add(dr); //add other rows 

                                    }
                                }
                            }
                        }
                    }
                }

                stream.Dispose();
                web.Dispose();
                web = null;
                CSVParser = null;
                string Deletetempfolderfile = FileUploadManager.GetTemporaryUploadFilePath() + FileName;
                System.IO.File.Delete(Deletetempfolderfile);
            }
            catch (Exception ex)
            {
                string Deletetempfolderfile = FileUploadManager.GetTemporaryUploadFilePath() + FileName;
                System.IO.File.Delete(Deletetempfolderfile);
                throw new Exception("Error Occured while processing the csv file. Kindly review the data in the csv file.", new Exception("CSVDataError"));
            }
            return dtCsv;
        }

        private DataTable ConvertExcelToDataTable(string FilePath, string chkhead, string FileName)
        {
            DataTable dt = new DataTable();
            FileInfo fi = new FileInfo(FilePath);
            try
            {
                if (fi.Exists)
                {
                    List<string> sheets = ExcelAdapter.GetSheet(FilePath);
                    if (sheets.Count > 0)
                    {
                        int rowStartIndex = 2;
                        dt = ExcelAdapter.ReadExcel(FilePath, sheets[0], rowStartIndex);
                    }
                }
                string Deletetempfolderfile = FileUploadManager.GetTemporaryUploadFilePath() + FileName;
                System.IO.File.Delete(Deletetempfolderfile);

            }
            catch (Exception ex)
            {
                string Deletetempfolderfile = FileUploadManager.GetTemporaryUploadFilePath() + FileName;
                System.IO.File.Delete(Deletetempfolderfile);
                throw new Exception("Error Occured while processing the excel file.Kindly review the data in the excel file.", new Exception("ExcelDataError"));
            }
            return dt;
        }

        /// <summary>
        /// Get index of first error column in all row data for scroll in UI
        /// </summary>
        /// <param name="rawDataList"></param>
        /// <returns></returns>
        private int GetFirstErrorColumnIndex(List<SourcingResultImportTemp> rawDataList)
        {
            var errorColIndexs = new HashSet<int>();
            foreach (var row in rawDataList)
            {
                //get all error messages of import row
                var rowMessages = row.GetType()
                                    .GetProperties(BindingFlags.Public | BindingFlags.Instance)
                                    .Where(p => p.Name.EndsWith("Message")) //filter row's props ends with "Message", ex: RequirementMessage
                                    .Select(p => p.GetValue(row))
                                    .ToArray();
                //find the first column has error aka Columm Message <> empty
                int firstErrorColIndex = Array.FindIndex(rowMessages, m => !string.IsNullOrEmpty(m?.ToString()));

                errorColIndexs.Add(firstErrorColIndex);

                //break the loop if found the first column of any row has error
                if (firstErrorColIndex == 0) break;
            }
            return errorColIndexs.Any() ? errorColIndexs.Min() : 0;
        }

        #endregion
    }
}