///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//
// RP 06.10.2009:
// - Changes due to changes in base class
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");

Rebound.GlobalTrader.Site.Controls.DropDowns.BomManagerStatus = function(element) {
    Rebound.GlobalTrader.Site.Controls.DropDowns.BomManagerStatus.initializeBase(this, [element]);
this._intCompanyID = null;
};

Rebound.GlobalTrader.Site.Controls.DropDowns.BomManagerStatus.prototype = {
get_intCompanyID: function() { return this._intCompanyID; }, set_intCompanyID: function(value) { if (this._intCompanyID !== value) this._intCompanyID = value; }, 

    initialize: function() {
        Rebound.GlobalTrader.Site.Controls.DropDowns.BomManagerStatus.callBaseMethod(this, "initialize");
        this.addSetupDataCall(Function.createDelegate(this, this.setupDataCall));
        this.addGetDataOK(Function.createDelegate(this, this.dataCallOK));
    },

    dispose: function() {
        if (this.isDisposed) return;
        Rebound.GlobalTrader.Site.Controls.DropDowns.BomManagerStatus.callBaseMethod(this, "dispose");
    },

    setupDataCall: function() {
        this._objData.set_PathToData("controls/DropDowns/BomManagerStatus");
        this._objData.set_DataObject("BomManagerStatus");
        this._objData.set_DataAction("GetData");
        this._objData.addParameter("id", this._intCompanyID);
    },

    dataCallOK: function() {
        //debugger;
        var result = this._objData._result;
        if (result.BomManagerStatus) {
            for (var i = 0; i < result.BomManagerStatus.length; i++) {
                this.addOption(result.BomManagerStatus[i].Name, result.BomManagerStatus[i].ID);
            }
        }
    }

};

Rebound.GlobalTrader.Site.Controls.DropDowns.BomManagerStatus.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.BomManagerStatus", Rebound.GlobalTrader.Site.Controls.DropDowns.Base);
