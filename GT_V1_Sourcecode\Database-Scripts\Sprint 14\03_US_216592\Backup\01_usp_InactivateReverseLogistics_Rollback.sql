
GO

/****** Object:  StoredProcedure [dbo].[usp_InactivateReverseLogistics]    Script Date: 11/4/2024 2:41:59 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].[usp_InactivateReverseLogistics]                                                                                                                
 @UserId INT=0 ,                                                        
  @ClientId INT =0,                                                        
  @SelectedImportId int=0  
  As
  BEGIN 

  if exists(select 1 from  tbSourcingResult a join BorisGlobalTraderImports..tbReverseLogistic b on a.SourcingTableItemNo = b.ReverseLogisticId
  where a.SourcingTable = 'RLPH' and b.ImportId=@SelectedImportId
  )
  begin 
  select 'Items attached in sourcing.' as 'Message', convert(bit,0) as 'Status' 
  end
  else
  begin 

  Update  BorisGlobalTraderImports..tbReverseLogistic set Inactive=1 , UpdatedBy =@UserId, InactiveDate=GETDATE() where  ImportId = @SelectedImportId  --and UpdatedBy = @UserId
  Update  BorisGlobalTraderimports.dbo.tbReverseLogistic_ImportHistory  set InactiveDate=GETDATE() where  ImportId = @SelectedImportId  --and UpdatedBy = @UserId

   --select * from BorisGlobalTraderImports..tbReverseLogistic where  ImportId = @SelectedImportId 

  select 'Items Inactivated.' as 'Message', convert(bit,1) as 'Status' 
  end

  

  END

GO


