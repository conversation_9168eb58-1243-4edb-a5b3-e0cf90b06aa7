
/****** Object:  StoredProcedure [dbo].[usp_GPDetail_MTD_GP_for_Login]    Script Date: 5/22/2024 6:18:55 PM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

ALTER PROC [dbo].[usp_GPDetail_MTD_GP_for_Login]
    --********************************************************************************************
    --* SK 29.07.2010:
    --* - Services are allocated by definition without existing on tbAllocation
    --*   BackOrderQuantity - despite being on tbBackOrder - will therefore be zero
    --* 
    --* SK 26.04.2010:
    --* - redo process for open orders to reflect reporting process elsewhere
    --* - 
    --* SK 22.04.2010:
    --* - distinct count of orders to remove lines
    --* - 
    --* SK 12.04.2010:
    --* - do not add shipping or freight into cost and resale for shipped sales as that is 
    --*   performed in the report
    --* - 
    --* SK 06.04.2010:
    --* - correct retrieval of duty for landed cost calculation 
    --* - 
    --* SK 04.11.2009:
    --* - use Price from tbInvoiceLine rather than tbSalesOrderLine 
    --*   which allows us to get the price of service items correctly
    --* 
    --* SK 22.08.2009:
    --* - allow for second salesman on open orders 
    --* - 
    --* SK 15.07.2009:
    --* - open orders do not allow for salesman2 at this time - and do not include shipping  
    --* - 
    --* SK 14.07.2009:
    --* - recalculate LandedCost as at today's date  
    --* - 
    --* SK 25.06.2009:
    --* - allow for currency date on SO  
    --* - 
    --* SK 17.06.2009:
    --* - 
    --* - include shipping in landed cost 
    --* - ensure calculations for open(and shipped) as per reports
    --* SK 22.05.2024:
      --* - 
      --* - include credit 
      --* - add new ship cost that includes credit
      --* - cuongdox
    --********************************************************************************************
    @LoginId int
AS
DECLARE @FromDate datetime
DECLARE @ToDate datetime

SET @FromDate = dateadd(mm, datediff(mm, 0, getdate()), 0)
SET @ToDate = GetDate()

CREATE TABLE #tmpGP
(
    SalesOrderNumber int,
    OpenShippingCost float,
    OpenFreightCharge float,
    OpenLandedCost float,
    OpenSalesValue float,
    InvoiceNumber int,
    ShippedShippingCost float,
    ShippedFreightCharge float,
    ShippedLandedCost float,
    ShippedSalesValue float,
    SalesmanPercent float
)

CREATE TABLE #tmpOpenGP
(
    SalesOrderNumber int,
    OpenShippingCost float,
    OpenFreightCharge float,
    LandedCost float,
    BuyPrice float,
    Allocation int,
    PONumber int,
    GILineNo int,
    LandedCostExAllocation float,
    OpenSalesValue float,
    SalesmanPercent float,
    InStock int, -- GA 03/12/2012
    Pct int
)

CREATE TABLE #Credits
(
      CreditNo int,
      Salesman int,
      CurrencyRate float,
      SalesmanPct float
)
CREATE TABLE #CreditSummary
(
      Salesman int,
      Cost float,
      Resale float
)

INSERT INTO #tmpOpenGP
SELECT SalesOrderNumber,
       ShippingCost,
       Freight,
       LandedCost,
       BuyPriceInBase,
       QuantityAllocated,
       PurchaseOrderNumber,
       GoodsInLineNo,
       LandedCost,
       GoodsSales,
       SalesmanPercent,
       QuantityInStock,
       1
FROM dbo.tmpOpenOrders o
WHERE dbo.ufn_get_date_from_datetime(DatePromised)
      BETWEEN @FromDate AND @ToDate
      AND Salesman = @LoginId


-- allow for salesman2
INSERT INTO #tmpOpenGP
SELECT SalesOrderNumber,
       ShippingCost,
       Freight,
       LandedCost,
       BuyPriceInBase,
       QuantityAllocated,
       PurchaseOrderNumber,
       GoodsInLineNo,
       LandedCost,
       GoodsSales,
       Salesman2Percent,
       QuantityInStock,
       2
FROM dbo.tmpOpenOrders o
WHERE dbo.ufn_get_date_from_datetime(DatePromised)
      BETWEEN @FromDate AND @ToDate
      AND Salesman2 = @LoginId

----if manual stock (i.e. PO does not exist) take landed cost from Allocation 
--UPDATE	#tmpOpenGP
--SET		LandedCost	= IsNull(LandedCostExAllocation,0)
--WHERE	InStock > 0								-- GA 03/12/2012

INSERT INTO #tmpGP
SELECT tmp.SalesOrderNumber,
       tmp.OpenShippingCost,
       tmp.OpenFreightCharge,
       SUM(ISNULL(IsNull(tmp.LandedCost, tmp.BuyPrice) * tmp.Allocation, 0)),
       SUM(ISNULL(tmp.OpenSalesValue, 0)),
       0,
       0,
       0,
       0,
       0,
       tmp.SalesmanPercent
FROM #tmpOpenGP tmp
Group BY SalesOrderNumber,
         OpenShippingCost,
         OpenFreightCharge,
         Pct,
         SalesmanPercent


INSERT INTO #tmpGP
SELECT 0,
       0,
       0,
       0,
       0,
       InvoiceNumber,
       ShippingCost,
       Freight,
       sum(GoodsCost),
       sum(GoodsSales),
       SalesmanPercent
FROM dbo.tmpShippedOrders a
WHERE a.InvoiceDate
      BETWEEN @FromDate AND @ToDate
      AND a.Salesman = @LoginId
GROUP BY a.InvoiceNumber,
         a.ShippingCost,
         a.Freight,
         a.CurrencyNo,
         a.InvoiceDate,
         a.SalesmanPercent

-- allow for salesman2
INSERT INTO #tmpGP
SELECT 0,
       0,
       0,
       0,
       0,
       InvoiceNumber,
       ShippingCost,
       Freight,
       sum(GoodsCost),
       sum(GoodsSales),
       Salesman2Percent
FROM dbo.tmpShippedOrders a
WHERE a.InvoiceDate
      BETWEEN @FromDate AND @ToDate
      AND a.Salesman2 = @LoginId
GROUP BY a.InvoiceNumber,
         a.ShippingCost,
         a.Freight,
         a.CurrencyNo,
         a.InvoiceDate,
         a.Salesman2Percent

DELETE FROM dbo.tbGPDetail
WHERE LoginNo = @LoginId
      AND Period = 'MD'

--get all credits  
INSERT INTO #Credits
SELECT CreditId,
      Salesman,
      dbo.ufn_get_exchange_rate(CurrencyNo, InvoiceDate),
      (100 - Salesman2Percent)
FROM dbo.vwCredit
      LEFT JOIN tbLogin lg
      on lg.LoginId = Salesman
WHERE
 (
      (@LoginId IS NULL)
      OR (
                             NOT @LoginId IS NULL
      AND (Salesman = @LoginId)
                         )
                  )
      AND
      dbo.ufn_get_date_from_datetime(CreditDate)
      BETWEEN @FromDate AND @ToDate

--and again for salesman2  
INSERT INTO #Credits
SELECT CreditId,
      Salesman2,
      dbo.ufn_get_exchange_rate(CurrencyNo, InvoiceDate),
      Salesman2Percent
FROM dbo.vwCredit c
      LEFT JOIN tbLogin lg
      on lg.LoginId = c.Salesman2
WHERE 
      (
      (@LoginId IS NULL)
      OR (
                  NOT @LoginId IS NULL
      AND (Salesman2 = @LoginId)
            )
      )
      AND dbo.ufn_get_date_from_datetime(CreditDate)
      BETWEEN @FromDate AND @ToDate
      AND Salesman2 IS NOT NULL

INSERT INTO #CreditSummary
SELECT v.Salesman,
      SUM(v.Cost),
      SUM(v.Resale)
FROM
      (
            SELECT c.Salesman,
            isnull((((cr.CreditCost + cr.ShippingCost) / 100) * c.SalesmanPct), 0) AS Cost,
            isnull((((cr.CreditValue + cr.Freight) / 100) * c.SalesmanPct) / c.CurrencyRate, 0) AS Resale
      FROM #Credits c
            JOIN dbo.vwCredit cr
            ON cr.CreditId = c.CreditNo
        ) AS v
GROUP BY v.Salesman

INSERT INTO dbo.tbGPDetail
SELECT null,
       null,
       @LoginId,
       null,
       'MD',
       IsNull(sum((OpenShippingCost / 100) * SalesmanPercent), 0),
       IsNull(sum((OpenFreightCharge / 100) * SalesmanPercent), 0),
       IsNull(sum((OpenLandedCost / 100) * SalesmanPercent), 0),
       IsNull(sum((OpenSalesValue / 100) * SalesmanPercent), 0),
       (
           SELECT count(DISTINCT SalesOrderNumber)
           FROM #tmpGP
           WHERE SalesOrderNumber > 0
       ),
       IsNull(sum((ShippedShippingCost / 100) * SalesmanPercent), 0),
       IsNull(sum((ShippedFreightCharge / 100) * SalesmanPercent), 0),
       IsNull(sum((ShippedLandedCost / 100) * SalesmanPercent), 0),
       IsNull(sum((ShippedSalesValue / 100) * SalesmanPercent), 0),
       (
           SELECT count(DISTINCT InvoiceNumber) FROM #tmpGP WHERE InvoiceNumber > 0
       ),
       -- calculate for credit
      (
            SELECT TOP 1 cs.Resale FROM #CreditSummary cs
            WHERE cs.Salesman = @LoginId
      ),
      (
            SELECT TOP 1 cs.Cost FROM #CreditSummary cs
            WHERE cs.Salesman = @LoginId
      ) 		
FROM #tmpGP

DROP TABLE #tmpGP
DROP TABLE #tmpopenGP
DROP TABLE #Credits
DROP TABLE #CreditSummary