///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//Marker     changed by      date         Remarks

//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.ClientBOMAdd_Add = function (element) {
    Rebound.GlobalTrader.Site.Controls.Forms.ClientBOMAdd_Add.initializeBase(this, [element]);
    this._intNewID = 0;
    this._intCompanyID = 0;
    this._strCompanyName = "";
    this._intGlobalCurrencyNo = -1; this._intPOCurrencyNo = -1;//by umendra
    this._intSalesPersionID = 0;
    this._strSalesPersionName = "";
};

Rebound.GlobalTrader.Site.Controls.Forms.ClientBOMAdd_Add.prototype = {

    get_intCompanyID: function () { return this._intCompanyID; }, set_intCompanyID: function (v) { if (this._intCompanyID !== v) this._intCompanyID = v; },
    get_strCompanyName: function () { return this._strCompanyName; }, set_strCompanyName: function (v) { if (this._strCompanyName !== v) this._strCompanyName = v; },
    get_intSalesPersionID: function () { return this._intSalesPersionID; }, set_intSalesPersionID: function (v) { if (this._intSalesPersionID !== v) this._intSalesPersionID = v; },
    get_strSalesPersionName: function () { return this._strSalesPersionName; }, set_strSalesPersionName: function (v) { if (this._strSalesPersionName !== v) this._strSalesPersionName = v; },

    initialize: function () {
        Rebound.GlobalTrader.Site.Controls.Forms.ClientBOMAdd_Add.callBaseMethod(this, "initialize");
        this.addCancel(Function.createDelegate(this, this.cancelClicked));
        this.addSave(Function.createDelegate(this, this.saveClicked));
        this.addShown(Function.createDelegate(this, this.formShown));

    },

    dispose: function () {
        if (this.isDisposed) return;
        this._intNewID = null;
        this._intCompanyID = null;
        this._strCompanyName = null;
        this._intSalesPersionID = null;
        this._strSalesPersionName = null;
        Rebound.GlobalTrader.Site.Controls.Forms.ClientBOMAdd_Add.callBaseMethod(this, "dispose");
    },

    cancelClicked: function () {
        $R_FN.navigateBack();
    },

    formShown: function () {
        this._intGlobalCurrencyNo = -1;
        // $find(this.getField("ctlSalesman").ControlID).addChanged(Function.createDelegate(this, this.getSalesman));
        if (this._intCompanyID > 0) {  //Worked for selected Company id with code.

            this.setFieldValue("ctlCompany", this._intCompanyID, null, $R_FN.setCleanTextValue(this._strCompanyName));
            this.setFieldValue("ctlSalespersion", this._intSalesPersionID, null, $R_FN.setCleanTextValue(this._strSalesPersionName));
            this.getContact();
        }
        else {  //Normal BOM Add          
            if ($find(this.getField("ctlCompany").ControlID)) $find(this.getField("ctlCompany").ControlID)._aut.addSelectionMadeEvent(Function.createDelegate(this, this.getContact));
        }
        
        this.getFieldDropDownData("ctlCurrency");
        this.getFieldDropDownData("ctlSalesman");
    },
    getContact: function () {
        this.getBOMData();
        this.getFieldControl("ctlContact")._intCompanyID = this.getFieldValue("ctlCompany");
        this.getFieldDropDownData("ctlContact");
        
    },
    getBOMData: function () {
        //this.getData_Start();
        var obj = new Rebound.GlobalTrader.Site.Data();
        //obj.set_PathToData("controls/Nuggets/CompanySalesInfo");
        //obj.set_DataObject("CompanySalesInfo");
        //obj.set_DataAction("GetData");
        this._strPath = "controls/Nuggets/CompanySalesInfo";
        this._strData = "CompanySalesInfo";
        obj.set_PathToData(this._strPath);
        obj.set_DataObject(this._strData);
        obj.set_DataAction("GetDefaultSalesInfo");
        obj.addParameter("id", this.getFieldValue("ctlCompany"));
        obj.addParameter("id", this.getFieldValue("ctlSalespersion"));
        obj.addDataOK(Function.createDelegate(this, this.getBOMDataOK));
        obj.addError(Function.createDelegate(this, this.getBOMDataError));
        obj.addTimeout(Function.createDelegate(this, this.getBOMDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getBOMDataOK: function (args) {
        var res = args._result;
        //  alert(res.IsTraceability);
        // alert(res.CurrencyNo);
        this._intGlobalCurrencyNo = res.GlobalCurrencyNo;
        this._intPOCurrencyNo = res.CurrencyNo;
        this.bindCurrency();
       // this.setFieldValue("ctlCurrency", res.GlobalCurrencyNo);
        //this.setFieldValue("ctlContact", res.ContactNo);
        //this.setFieldValue("ctlAS9120", res.IsTraceability);
        //this.getDataOK_End();
    },
    bindCurrency: function () {
        //start code by umendra for binding currencies
        this.getFieldControl("ctlCurrency")._intGlobalCurrencyNo = this._intGlobalCurrencyNo;
        this.getFieldControl("ctlCurrency")._blnIsBuy = false;
        this.getFieldDropDownData("ctlCurrency");
        this.setFieldValue("ctlCurrency", this._intPOCurrencyNo);
        //end code by umendra
    },

    getBOMDataError: function (args) {
        this.showError(true, args.get_ErrorMessage());
    },

    saveClicked: function () {
        if (!this.validateForm()) return;
        this.showSaving(true);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/ClientBOMAdd");
        obj.set_DataObject("ClientBOMAdd");
        obj.set_DataAction("AddNew");
        obj.addParameter("Name", this.getFieldValue("ctlName"));
        obj.addParameter("Company", this.getFieldValue("ctlCompany"));
        obj.addParameter("Contact", this.getFieldValue("ctlContact"));
        obj.addParameter("InActive", this.getFieldValue("ctlInActive"));
        obj.addParameter("Notes", this.getFieldValue("ctlNotes"));
        obj.addParameter("CurrencyNo", this.getFieldValue("ctlCurrency"));
        //obj.addParameter("Salesman", this.getFieldValue("ctlSalesman"));
        obj.addParameter("SalesPersion", this.getFieldValue("ctlSalespersion"));
        obj.addDataOK(Function.createDelegate(this, this.saveEditComplete));
        obj.addError(Function.createDelegate(this, this.saveEditError));
        obj.addTimeout(Function.createDelegate(this, this.saveEditError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    saveEditError: function (args) {
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    saveEditComplete: function (args) {

        if (args._result.Result) {
            if (args._result.NewID > 0) {
                this._intNewID = args._result.NewID;
                this.showSavedOK(true);
                this.onSaveComplete();
            }
        } else {
            this.showError(true, args._result.ValidationMessage);
        }
    },

    validateForm: function () {
        this.onValidate();
        var blnOK = this.autoValidateFields();
        return blnOK;
    }

    //getSalesman: function () {
    //    this.showSalesmanFieldsLoading(true);
    //    if (!this.checkFieldEntered("ctlSalesman")) {
    //        this.getSalesmanError();
    //        return;
    //    }
    //    var obj = new Rebound.GlobalTrader.Site.Data();
    //    obj.set_PathToData("controls/SetupNuggets/SecurityUsers");
    //    obj.set_DataObject("SecurityUsers");
    //    obj.set_DataAction("GetItem");
    //    obj.addParameter("ID", this.getFieldValue("ctlSalesman"));
    //    obj.addDataOK(Function.createDelegate(this, this.getSalesmanOK));
    //    obj.addError(Function.createDelegate(this, this.getSalesmanError));
    //    obj.addTimeout(Function.createDelegate(this, this.getSalesmanError));
    //    $R_DQ.addToQueue(obj);
    //    $R_DQ.processQueue();
    //    obj = null;
    //},

    //getSalesmanOK: function (args) {
    //    var res = args._result;
    //    this.setFieldValue("ctlDivision", res.DivisionNo);
    //    this.getFieldDropDownData("ctlDivision");
    //    this.setFieldValue("ctlDivision_Label", $R_FN.setCleanTextValue(res.Division));
    //    this.showSalesmanFieldsLoading(false);
    //},

    //getSalesmanError: function (args) {
    //    this.showSalesmanFieldsLoading(false);
    //    this._strErrorMessage = args._errorMessage;
    //    this.onSaveError();
    //}
};

Rebound.GlobalTrader.Site.Controls.Forms.ClientBOMAdd_Add.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.ClientBOMAdd_Add", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
