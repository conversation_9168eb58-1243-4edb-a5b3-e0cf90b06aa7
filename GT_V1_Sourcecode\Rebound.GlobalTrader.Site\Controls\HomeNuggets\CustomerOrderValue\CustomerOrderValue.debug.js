///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
//Marker  Date          Changed By     Remarks
//[001]   03/07/2018    A<PERSON><PERSON>    Add customer order value nugget on broker and sales tab
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.HomeNuggets");

Rebound.GlobalTrader.Site.Controls.HomeNuggets.CustomerOrderValue = function (element) {
    Rebound.GlobalTrader.Site.Controls.HomeNuggets.CustomerOrderValue.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.HomeNuggets.CustomerOrderValue.prototype = {

    get_tblCustomerOrder: function () { return this._tblCustomerOrder; }, set_tblCustomerOrder: function (value) { if (this._tblCustomerOrder !== value) this._tblCustomerOrder = value; },

	initialize: function() {
	    Rebound.GlobalTrader.Site.Controls.HomeNuggets.CustomerOrderValue.callBaseMethod(this, "initialize");
		this.addRefreshEvent(Function.createDelegate(this, this.getData));
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this._tblCustomerOrder) this._tblCustomerOrder.dispose();
		this._tblCustomerOrder = null;
		Rebound.GlobalTrader.Site.Controls.HomeNuggets.CustomerOrderValue.callBaseMethod(this, "dispose");
	},
	
	setupLoadingState: function() {
	    this._tblCustomerOrder.show(false);
		Rebound.GlobalTrader.Site.Controls.HomeNuggets.CustomerOrderValue.callBaseMethod(this, "setupLoadingState");
	},
	
	getData: function () {
	    this.setupLoadingState();
		var obj = new Rebound.GlobalTrader.Site.Data();
		obj.set_PathToData('controls/HomeNuggets/CustomerOrderValue');
		obj.set_DataObject('CustomerOrderValue');
		obj.set_DataAction('GetData');
		obj.addDataOK(Function.createDelegate(this, this.getDataComplete));
		obj.addError(Function.createDelegate(this, this.homeNuggetDataError));
		obj.addTimeout(Function.createDelegate(this, this.homeNuggetDataError));
		$R_DQ.addToQueue(obj);
		$R_DQ.processQueue();
		obj = null;
	},
	
	getDataComplete: function (args) {
	    
	    this.showNoneFoundOrContent(args._result.Count);
		var result = args._result;
		this._tblCustomerOrder.clearTable();
		this._tblCustomerOrder.show(result.CustomerOrder.length > 0);
		for (var i = 0; i < result.CustomerOrder.length; i++) {
		    var row = result.CustomerOrder[i];
			var aryData = [
				    $RGT_nubButton_Company(row.CompanyNo, row.CompanyName),
                    row.TotalValue,
                    row.AvailCredit
				];
			this._tblCustomerOrder.addRow(aryData, null);
			this._tblCustomerOrder.RowColor(i + 1, row.RowCSS);
        }
		this.hideLoading();
	}
};

Rebound.GlobalTrader.Site.Controls.HomeNuggets.CustomerOrderValue.registerClass("Rebound.GlobalTrader.Site.Controls.HomeNuggets.CustomerOrderValue", Rebound.GlobalTrader.Site.Controls.HomeNuggets.Base, Sys.IDisposable);
