///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//
// RP 23.12.2009:
// - render state on server
//
// RP 16.10.2009:
// - If no name (shouldn't be the case generally, we enforce a new contact having a name)
//   display "???" so there is something to click
//
// RP 16.10.2009:
// - Changes due to changes in base class
/*
 Marker     ChangedBy       Date            Remarks
 [001]      <PERSON><PERSON><PERSON>     13-Sep-2018     [REB-12820]:Provision to add Global Security on Contact Section
 */
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DataListNuggets");

Rebound.GlobalTrader.Site.Controls.DataListNuggets.Contacts = function(element) { 
	Rebound.GlobalTrader.Site.Controls.DataListNuggets.Contacts.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.DataListNuggets.Contacts.prototype = {

    get_enmContactListType: function () { return this._enmContactListType; }, set_enmContactListType: function (value) { if (this._enmContactListType !== value) this._enmContactListType = value; },
    //[001] start
    get_IsGlobalLogin: function () { return this._IsGlobalLogin; }, set_IsGlobalLogin: function (v) { if (this._IsGlobalLogin !== v) this._IsGlobalLogin = v; },
    //[001] end

    initialize: function() {
        this.addInitCompleteEvent(Function.createDelegate(this, this.initAfterBaseIsReady));
        this.addSetupDataCallEvent(Function.createDelegate(this, this.setupDataCall));
        this.addGetDataOKEvent(Function.createDelegate(this, this.getDataOK));
        this._strPathToData = "controls/DataListNuggets/Contacts";
        this._strDataObject = "Contacts";
        Rebound.GlobalTrader.Site.Controls.DataListNuggets.Contacts.callBaseMethod(this, "initialize");
    },

    initAfterBaseIsReady: function () {
        //[001] start
        this.updateFilterVisibility();
        //[001] end
        this.addPageTabChangedEvent(Function.createDelegate(this, this.pageTabChanged));
        this.getData();
    },

    dispose: function() {
        if (this.isDisposed) return;
        this._enmContactListType = null;
        //[001] start
        this._IsGlobalLogin = null;
        //[001] end
        Rebound.GlobalTrader.Site.Controls.DataListNuggets.Contacts.callBaseMethod(this, "dispose");
    },

    pageTabChanged: function() {
        this._table._intCurrentPage = 1;
        this._enmViewLevel = this._intCurrentTab;
        //[001] start
        this.updateFilterVisibility();
        //[001] end
        this.getData();
    },

    setupDataCall: function() {
        this._objData.addParameter("ViewLevel", this._enmViewLevel);
        //[001] start
        this._objData.addParameter("IsGlobalLogin", this._IsGlobalLogin);
        //[001] end
    },

    getDataOK: function() {
        for (var i = 0, l = this._objResult.Results.length; i < l; i++) {
            var row = this._objResult.Results[i];
            var aryData = [
				$RGT_nubButton_Contact(row.ID, (row.Name) ? row.Name : "???")
			, $R_FN.setCleanTextValue(row.Title)
			, $RGT_nubButton_Company(row.CoID, row.Co)
			//, $R_FN.setCleanTextValue(row.Tel)
            , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Tel), $R_FN.setCleanTextValue(row.Email))
			, $R_FN.setCleanTextValue(row.SMan)
            , $R_FN.setCleanTextValue(row.ClientName)
			];
            this._table.addRow(aryData, row.ID);
            aryData = null; row = null;
        }
    },
    //[001] start
    updateFilterVisibility: function () {
             this.getFilterField("ctlClientName").show(this._IsGlobalLogin);
    }
    //[001] end
};
Rebound.GlobalTrader.Site.Controls.DataListNuggets.Contacts.registerClass("Rebound.GlobalTrader.Site.Controls.DataListNuggets.Contacts", Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base, Sys.IDisposable);
