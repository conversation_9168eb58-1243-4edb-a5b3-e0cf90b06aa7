Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.BOMPVV_Edit=function(n){Rebound.GlobalTrader.Site.Controls.Forms.BOMPVV_Edit.initializeBase(this,[n]);this._intBOMID=-1;this._blnRequestedToPoHub=!1;this._IsView=!1;this._IsFromBOMAdd=!1;this._strGeneratedID=""};Rebound.GlobalTrader.Site.Controls.Forms.BOMPVV_Edit.prototype={get_intBOMID:function(){return this._intBOMID},set_intBOMID:function(n){this._intBOMID!==n&&(this._intBOMID=n)},get_BomCode:function(){return this._BomCode},set_BomCode:function(n){this._BomCode!==n&&(this._BomCode=n)},get_BomName:function(){return this._BomName},set_BomName:function(n){this._BomName!==n&&(this._BomName=n)},get_BomCompanyName:function(){return this._BomCompanyName},set_BomCompanyName:function(n){this._BomCompanyName!==n&&(this._BomCompanyName=n)},get_BomCompanyNo:function(){return this._BomCompanyNo},set_BomCompanyNo:function(n){this._BomCompanyNo!==n&&(this._BomCompanyNo=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.BOMPVV_Edit.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown))},formShown:function(){this._blnFirstTimeShown&&(this.addSave(Function.createDelegate(this,this.saveClicked)),this.GetListData());this.GetListData()},dispose:function(){this.isDisposed||(this._intBOMID=null,this._IsView=null,this._IsFromBOMAdd=null,this._strGeneratedID=null,Rebound.GlobalTrader.Site.Controls.Forms.BOMPVV_Edit.callBaseMethod(this,"dispose"))},GetListData:function(){this._blnRequestedToPoHub=!1;var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/BOMPVV");n.set_DataObject("BOMPVV");this._IsFromBOMAdd?(n.set_DataAction("GetListDataTemp"),n.addParameter("idGenerated",this._strGeneratedID)):(n.set_DataAction("GetListData"),n.addParameter("id",this._intBOMID));n.addDataOK(Function.createDelegate(this,this.getDataOK));n.addError(Function.createDelegate(this,this.getDataError));n.addTimeout(Function.createDelegate(this,this.getDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getDataOK:function(n){var u="",t,r;if($("#ctl00_cphMain_ctlBOMPVV_ctlDB_ctl17_ibtnSave_hyp").show(),$("#ctl00_cphMain_ctlBOMPVV_ctlDB_ctl18_ibtnSave_hyp").show(),$("#ctl00_cphMain_ctlAdd_ctlDB_ctl20_ibtnSave").show(),$("#ctl00_cphMain_ctlAdd_ctlDB_ctl19_ibtnSave").show(),this._IsView==!0&&(u="readonly",$("#ctl00_cphMain_ctlBOMPVV_ctlDB_ctl17_ibtnSave_hyp").hide(),$("#ctl00_cphMain_ctlBOMPVV_ctlDB_ctl18_ibtnSave_hyp").hide(),$("#ctl00_cphMain_ctlAdd_ctlDB_ctl20_ibtnSave").hide(),$("#ctl00_cphMain_ctlAdd_ctlDB_ctl19_ibtnSave").hide()),t=n._result,t!=null){for(r="<table id='PPVData' class='LinkedItems'>",r+="<tr>",i=0;i<t.Results.length;i++)r+="<td>",r+='<tr><td style="width:55%;">'+$R_FN.setCleanTextValue(t.Results[i].PVVQuestionName)+'<\/td><td class="item"> <input type="hidden" id="custId" name="custId" value="'+t.Results[i].PVVQuestionId+'"><textarea id="'+t.Results[i].PVVQuestionId+'" class="PVVAnswer" rows="4" cols="80" maxlength="500" placeholder="500 Character max" '+u+' value="'+t.Results[i].PVVAnswerName+'">'+$R_FN.setCleanTextValue(t.Results[i].PVVAnswerName)+"<\/textarea><\/td><\/tr>",r+="<\/td>";r+="<\/tr>";r+="<\/table>";$("#PVVBOMDiv").html(r)}},getDataError:function(n){this.showError(!0,n.get_ErrorMessage())},saveClicked:function(){var t="",i="",n;$(".PVVAnswer").each(function(){t+=this.id+"|"+$("#"+this.id).val()+",|,"});i=t.substr(0,t.lastIndexOf(",|,")-0);n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/BOMPVV");n.set_DataObject("BOMPVV");n.set_DataAction("SaveEdit");this._IsFromBOMAdd&&(n.set_DataAction("SaveEditTemp"),n.addParameter("idGenerated",this._strGeneratedID));n.addParameter("BOMNo",this._intBOMID);n.addParameter("PVVAnswers",i);n.addDataOK(Function.createDelegate(this,this.saveEditComplete));n.addError(Function.createDelegate(this,this.saveEditError));n.addTimeout(Function.createDelegate(this,this.saveEditError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},saveEditError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},saveEditComplete:function(n){n._result.Result==!0?(this.showSavedOK(!0),this.onSaveComplete()):(this._strErrorMessage=n._errorMessage,this.onSaveError())},validateForm:function(){this.onValidate();return this.autoValidateFields()},showFieldsLoading:function(){}};Rebound.GlobalTrader.Site.Controls.Forms.BOMPVV_Edit.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.BOMPVV_Edit",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);