Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Orders");Rebound.GlobalTrader.Site.Pages.Orders.PODetail=function(n){Rebound.GlobalTrader.Site.Pages.Orders.PODetail.initializeBase(this,[n]);this._blnIsApproved=!1};Rebound.GlobalTrader.Site.Pages.Orders.PODetail.prototype={get_intPOID:function(){return this._intPOID},set_intPOID:function(n){this._intPOID!==n&&(this._intPOID=n)},get_ctlMainInfo:function(){return this._ctlMainInfo},set_ctlMainInfo:function(n){this._ctlMainInfo!==n&&(this._ctlMainInfo=n)},get_ctlLines:function(){return this._ctlLines},set_ctlLines:function(n){this._ctlLines!==n&&(this._ctlLines=n)},get_btnPrint:function(){return this._btnPrint},set_btnPrint:function(n){this._btnPrint!==n&&(this._btnPrint=n)},get_pnlStatus:function(){return this._pnlStatus},set_pnlStatus:function(n){this._pnlStatus!==n&&(this._pnlStatus=n)},get_lblStatus:function(){return this._lblStatus},set_lblStatus:function(n){this._lblStatus!==n&&(this._lblStatus=n)},get_ctlPODocuments:function(){return this._ctlPODocuments},set_ctlPODocuments:function(n){this._ctlPODocuments!==n&&(this._ctlPODocuments=n)},get_ctlPOPDFDragDrop:function(){return this._ctlPOPDFDragDrop},set_ctlPOPDFDragDrop:function(n){this._ctlPOPDFDragDrop!==n&&(this._ctlPOPDFDragDrop=n)},get_lblCompanyStatus:function(){return this._lblCompanyStatus},set_lblCompanyStatus:function(n){this._lblCompanyStatus!==n&&(this._lblCompanyStatus=n)},get_hypCM:function(){return this._hypCM},set_hypCM:function(n){this._hypCM!==n&&(this._hypCM=n)},get_lblSupplierUpdated:function(){return this._lblSupplierUpdated},set_lblSupplierUpdated:function(n){this._lblSupplierUpdated!==n&&(this._lblSupplierUpdated=n)},get_IsGlobalLogin:function(){return this._IsGlobalLogin},set_IsGlobalLogin:function(n){this._IsGlobalLogin!==n&&(this._IsGlobalLogin=n)},get_ctlDragDropForPOR:function(){return this._ctlDragDropForPOR},set_ctlDragDropForPOR:function(n){this._ctlDragDropForPOR!==n&&(this._ctlDragDropForPOR=n)},initialize:function(){Rebound.GlobalTrader.Site.Pages.Orders.PODetail.callBaseMethod(this,"initialize")},goInit:function(){this._btnPrint&&this._btnPrint.addPrint(Function.createDelegate(this,this.printPO));this._btnPrint&&this._btnPrint.addEmail(Function.createDelegate(this,this.emailPO));this._btnPrint&&this._btnPrint.addExtraButtonClick(Function.createDelegate(this,this.printOtherDocs));this._ctlMainInfo&&this._ctlMainInfo.addGetDataComplete(Function.createDelegate(this,this.ctlMainInfo_GetDataComplete));this._ctlMainInfo&&this._ctlMainInfo.addSaveEditComplete(Function.createDelegate(this,this.ctlMainInfo_SaveEditComplete));this._ctlMainInfo&&this._ctlMainInfo.addPotentialStatusChange(Function.createDelegate(this,this.ctlMainInfo_PotentialStatusChange));this._ctlLines&&this._ctlLines.addPotentialStatusChange(Function.createDelegate(this,this.ctlLines_PotentialStatusChange));this._ctlMainInfo&&this.setLineFieldsFromHeader();this._ctlLines&&this._ctlLines.addGetDataComplete(Function.createDelegate(this,this.ctlLines_GetDataComplete));this._ctlPODocuments&&this._ctlPODocuments.getData();this._ctlPOPDFDragDrop&&this._ctlPOPDFDragDrop.getData();this._ctlDragDropForPOR&&this._ctlDragDropForPOR.getData();Rebound.GlobalTrader.Site.Pages.Orders.PODetail.callBaseMethod(this,"goInit")},dispose:function(){this.isDisposed||(this._btnPrint&&this._btnPrint.dispose(),this._ctlMainInfo&&this._ctlMainInfo.dispose(),this._ctlLines&&this._ctlLines.dispose(),this._btnPrint=null,this._ctlMainInfo=null,this._ctlLines=null,this._pnlStatus=null,this._lblStatus=null,this._intPOID=null,this._ctlPODocuments=null,this._lblCompanyStatus=null,this._hypCM=null,this._lblSupplierUpdated=null,this._IsGlobalLogin=null,this._ctlDragDropForPOR=null,Rebound.GlobalTrader.Site.Pages.Orders.PODetail.callBaseMethod(this,"dispose"))},printPO:function(){$R_FN.openPrintWindow($R_ENUM$PrintObject.PurchaseOrder,this._intPOID)},emailPO:function(){$R_FN.openPrintWindow($R_ENUM$PrintObject.PurchaseOrder,this._intPOID,!0)},printOtherDocs:function(){this._btnPrint._strExtraButtonClickCommand=="EmailPOHTML"&&$R_FN.openPrintWindow($R_ENUM$PrintObject.PurchaseOrderEmail,this._intPOID,!0);this._btnPrint._strExtraButtonClickCommand=="POR"&&$R_FN.openPrintWindow($R_ENUM$PrintObject.PurchaseOrderReport,this._intPOID);this._btnPrint._strExtraButtonClickCommand=="PrintLog"&&$R_FN.openPrintLogWindow($R_ENUM$PrintObject.PrintLog,this._intPOID,!1,"PurchaseOrder");this._btnPrint._strExtraButtonClickCommand=="ExportToExcel"&&this.exportClicked()},ctlMainInfo_SaveEditComplete:function(){this._ctlLines.getTabData();this._ctlLines.enableDisableAddButton(this._ctlMainInfo._isIPO);this._ctlLines._ipoClientNo=this._ctlMainInfo._ipoClientNo},ctlLines_GetDataComplete:function(){this._ctlMainInfo._IsPOLineReceived=this._ctlLines._IsPOLineReceived},ctlMainInfo_GetDataComplete:function(){var n,t,i;for(this.setLineFieldsFromHeader(),this._ctlLines._intGlobalClientNo=this._IsGlobalLogin==!0?this._ctlMainInfo.getFieldValue("hidGlobalClientNo"):null,this._ctlMainInfo._IsGlobalLogin=this._IsGlobalLogin,$R_FN.setInnerHTML(this._lblStatus,this._ctlMainInfo.getFieldValue("hidStatus")),this._ctlLines.updateStatus(this._ctlMainInfo.getFieldValue("hidStatusNo")),this._ctlLines._ipoClientNo=this._ctlMainInfo._ipoClientNo,this._btnPrint&&$R_FN.showElement(this._btnPrint._element,this._ctlMainInfo._blnIsApproved),this._ctlLines.enableDisableAddButton(this._ctlMainInfo._isIPO),this._ctlLines._PONumber=this._ctlMainInfo._PONumber,n="",t=0;t<this._ctlMainInfo._POLineEPRIds.length;t++)i=this._ctlMainInfo._POLineEPRIds[t],n+=$RGT_nubButton_EPR(this._ctlMainInfo._intPurchaseOrderID,i.POLineEPRIds,this._ctlMainInfo._PONumber);$R_FN.setInnerHTML(this._ctlLines._pnlEPR,"");$R_FN.setInnerHTML(this._ctlLines._pnlEPR,n);$R_FN.showElement(this._ctlLines._pnlEPR,n.length>0);this.CompanyStatus()},CompanyStatus:function(){$R_FN.showElement(this._lblCompanyStatus,!1);$R_FN.showElement(this._lblSupplierUpdated,this._ctlMainInfo._lblCompanyStatus);$R_FN.setInnerHTML(this._lblSupplierUpdated,"Supplier Changed")},setLineFieldsFromHeader:function(){var n=this._ctlMainInfo.getFieldValue("hidSupplierName"),u=this._ctlMainInfo.getFieldValue("hidSupplierNotes"),t=this._ctlMainInfo.getFieldValue("hidPONumber"),i=this._ctlMainInfo.getFieldValue("hidTotalShipInCostText"),r=this._ctlMainInfo.getFieldValue("hidCurrencyCode"),f=this._ctlMainInfo.getFieldValue("hidCurrencyNo"),e=this._ctlMainInfo.getFieldValue("ctlDateOrdered"),o=this._ctlMainInfo._MailGroupId;this._ctlLines._frmAdd&&this._ctlLines._frmAdd.setFieldsFromHeader(t,n,f,r,i,e,u);this._ctlLines._frmEdit&&this._ctlLines._frmEdit.setFieldsFromHeader(t,n,r,i);this._ctlLines._frmPost&&this._ctlLines._frmPost.setFieldsFromHeader(t,n);this._ctlLines._frmDelete&&this._ctlLines._frmDelete.setFieldsFromHeader(t,n);this._ctlLines._frmDeallocate&&this._ctlLines._frmDeallocate.setFieldsFromHeader(t,n,o);this._ctlLines._frmClose&&this._ctlLines._frmClose.setFieldsFromHeader(t,n)},ctlMainInfo_PotentialStatusChange:function(){this._ctlMainInfo.getData();this._ctlLines.getTabData();this._ctlLines.onRefreshAllocations()},ctlLines_PotentialStatusChange:function(){this._ctlMainInfo.getData();this._ctlLines.getTabData()},getStatus:function(){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/POMainInfo");n.set_DataObject("POMainInfo");n.set_DataAction("GetStatus");n.addParameter("id",this._intPOID);n.addDataOK(Function.createDelegate(this,this.getStatusOK));n.addError(Function.createDelegate(this,this.getStatusError));n.addTimeout(Function.createDelegate(this,this.getStatusError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getStatusOK:function(n){var t=$R_FN.setCleanTextValue(n._result.Status);this._ctlLines.updateStatus(n._result.StatusNo);this._ctlMainInfo.setFieldValue("hidStatus",t);$R_FN.setInnerHTML(this._lblStatus,t);$R_FN.showElement(this._pnlStatus,!0);t=null},getStatusError:function(){$R_FN.showElement(this._pnlStatus,!1)},exportClicked:function(){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/POMainInfo");n.set_DataObject("POMainInfo");n.set_DataAction("ExportPurchaseOrderReport");n.addParameter("id",this._intPOID);n._intTimeoutMilliseconds=9e4;n.addDataOK(Function.createDelegate(this,this.exportComplete));n.addError(Function.createDelegate(this,this.exportError));n.addTimeout(Function.createDelegate(this,this.exportError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},exportError:function(n){this.showError(!0,n.get_ErrorMessage())},exportComplete:function(n){if(n._result.Result==1){var t=new Date,i=!1;i=this.UrlExists(window.location.origin+"/"+n._result.FileURL);i==!0&&(location.href=String.format("{0}?t={1}",n._result.FileURL,t.getTime()));t=null}},UrlExists:function(n){var t=new XMLHttpRequest;t.open("HEAD",n,!1);try{t.send()}catch(i){}return t.status!=404}};Rebound.GlobalTrader.Site.Pages.Orders.PODetail.registerClass("Rebound.GlobalTrader.Site.Pages.Orders.PODetail",Rebound.GlobalTrader.Site.Pages.Content,Sys.IDisposable);