///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.AutoSearch");

Rebound.GlobalTrader.Site.Controls.AutoSearch.Lot = function(element) { 
	Rebound.GlobalTrader.Site.Controls.AutoSearch.Lot.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.AutoSearch.Lot.prototype = {

    initialize: function() {
        Rebound.GlobalTrader.Site.Controls.AutoSearch.Lot.callBaseMethod(this, "initialize");
        this.addDataReturnedEvent(Function.createDelegate(this, this.dataReturned));
        this.setupDataObject("Lot");
    },

    dispose: function() {
        if (this.isDisposed) return;
        Rebound.GlobalTrader.Site.Controls.AutoSearch.Lot.callBaseMethod(this, "dispose");
    },

    dataReturned: function() {
        //if (!this._result) return;
        //if (this._result.TotalRecords > 0) {
        //    for (var i = 0, l = this._result.Results.length; i < l; i++) {
        //        var res = this._result.Results[i];
        //        var strHTML = "";
        //        strHTML = $R_FN.setCleanTextValue(res.Name);
        //        this.addResultItem(strHTML, $R_FN.setCleanTextValue(res.Name), res.ID, res.Code);
        //        strHTML = null; res = null;
        //    }
        //}
        if (!this._result) return;
        if (this._result.TotalRecords > 0) {
            for (var i = 0, l = this._result.Results.length; i < l; i++) {
                var res = this._result.Results[i];
                var strHTML = "";
                if (this._enmResultsActionType == $R_ENUM$AutoSearchResultsActionType.Navigate) {
                    strHTML = $RGT_nubButton_Package(res.ID, res.Name);
                } else {
                    strHTML = $R_FN.setCleanTextValue(res.Name);
                }
                this.addResultItem(strHTML, $R_FN.setCleanTextValue(res.Name), res.ID, res.Code);
                strHTML = null;
                res = null;
            }
        }
    }
};

Rebound.GlobalTrader.Site.Controls.AutoSearch.Lot.registerClass("Rebound.GlobalTrader.Site.Controls.AutoSearch.Lot", Rebound.GlobalTrader.Site.Controls.AutoSearch.Base, Sys.IDisposable);
