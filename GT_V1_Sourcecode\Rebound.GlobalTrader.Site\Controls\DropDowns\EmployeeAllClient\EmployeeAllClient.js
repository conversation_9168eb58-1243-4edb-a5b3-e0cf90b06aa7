Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.EmployeeAllClient=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.EmployeeAllClient.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DropDowns.EmployeeAllClient.prototype={get_blnLimitToCurrentUsersDivision:function(){return this._blnLimitToCurrentUsersDivision},set_blnLimitToCurrentUsersDivision:function(n){this._blnLimitToCurrentUsersDivision!==n&&(this._blnLimitToCurrentUsersDivision=n)},get_blnLimitToCurrentUsersTeam:function(){return this._blnLimitToCurrentUsersTeam},set_blnLimitToCurrentUsersTeam:function(n){this._blnLimitToCurrentUsersTeam!==n&&(this._blnLimitToCurrentUsersTeam=n)},get_blnExcludeCurrentUser:function(){return this._blnExcludeCurrentUser},set_blnExcludeCurrentUser:function(n){this._blnExcludeCurrentUser!==n&&(this._blnExcludeCurrentUser=n)},get_intGlobalLoginClientNo:function(){return this._intGlobalLoginClientNo},set_intGlobalLoginClientNo:function(n){this._intGlobalLoginClientNo!==n&&(this._intGlobalLoginClientNo=n)},initialize:function(){this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK));Rebound.GlobalTrader.Site.Controls.DropDowns.EmployeeAllClient.callBaseMethod(this,"initialize")},dispose:function(){this.isDisposed||(this._blnLimitToCurrentUsersDivision=null,this._blnLimitToCurrentUsersTeam=null,this._blnExcludeCurrentUser=null,this._intGlobalLoginClientNo=null,Rebound.GlobalTrader.Site.Controls.DropDowns.EmployeeAllClient.callBaseMethod(this,"dispose"))},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/EmployeeAllClient");this._objData.set_DataObject("EmployeeAllClient");this._objData.set_DataAction("GetData");this._objData.addParameter("LimitToCurrentUsersTeam",this._blnLimitToCurrentUsersTeam);this._objData.addParameter("LimitToCurrentUsersDivision",this._blnLimitToCurrentUsersDivision);this._objData.addParameter("ExcludeCurrentUser",this._blnExcludeCurrentUser);this._objData.addParameter("GlobalLoginClientNo",this._intGlobalLoginClientNo)},dataCallOK:function(){var n=this._objData._result,t;if(n!=null&&n.Employees)for(t=0;t<n.Employees.length;t++)this.addOption(n.Employees[t].Name,n.Employees[t].ID)}};Rebound.GlobalTrader.Site.Controls.DropDowns.EmployeeAllClient.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.EmployeeAllClient",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);