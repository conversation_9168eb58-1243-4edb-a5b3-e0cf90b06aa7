Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.ClientInvoiceLines_Add=function(n){Rebound.GlobalTrader.Site.Controls.Forms.ClientInvoiceLines_Add.initializeBase(this,[n]);this._intClientInvoiceID=0;this._intCompanyID=0;this._aryGoodsInLineIds=[];this._lnsSeperator="/";this._floatLineTotal=0;this._strSecondRef="";this._strNarrative="";this._aryAddedGoodsInLineIds=[];this._intInvoiceClientNo=-1};Rebound.GlobalTrader.Site.Controls.Forms.ClientInvoiceLines_Add.prototype={get_ctlSelectSIGILines:function(){return this._ctlSelectSIGILines},set_ctlSelectSIGILines:function(n){this._ctlSelectSIGILines!==n&&(this._ctlSelectSIGILines=n)},get_dtFromDate:function(){return this._dtFromDate},set_dtFromDate:function(n){this._dtFromDate!==n&&(this._dtFromDate=n)},get_dtToDate:function(){return this._dtToDate},set_dtToDate:function(n){this._dtToDate!==n&&(this._dtToDate=n)},get_SelectedGI:function(){return this._SelectedGI},set_SelectedGI:function(n){this._SelectedGI!==n&&(this._SelectedGI=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.ClientInvoiceLines_Add.callBaseMethod(this,"initialize");this.addSave(Function.createDelegate(this,this.saveLine));this.addShown(Function.createDelegate(this,this.formShown));this._ctlSelectSIGILines.addPotentialStatusChange(Function.createDelegate(this,this.ctlSelectSIGILines_PotentialStatusChange))},dispose:function(){this.isDisposed||(this._intClientInvoiceID=null,this._ctlSelectSIGILines&&this._ctlSelectSIGILines.dispose(),this._ctlSelectSIGILines=null,this._intCompanyID=null,this._aryGoodsInLineIds=null,this._lnsSeperator=null,this._floatLineTotal=null,this._strSecondRef=null,this._strNarrative=null,this._aryAddedGoodsInLineIds=null,this._intInvoiceClientNo=null,Rebound.GlobalTrader.Site.Controls.Forms.ClientInvoiceLines_Add.callBaseMethod(this,"dispose"))},formShown:function(){this._blnFirstTimeShown;Array.clear(this._aryGoodsInLineIds);this._ctlSelectSIGILines._aryAddedGoodsInLineIds=this._aryAddedGoodsInLineIds;this._ctlSelectSIGILines._aryAddedGoodsInLineIds._intCount=0;this._ctlSelectSIGILines._isClientInvoice=!0;this._ctlSelectSIGILines._intInvoiceClientNo=this._intInvoiceClientNo;this.getClientInvoice()},saveLine:function(){if(this.validateForm()){var n=new Rebound.GlobalTrader.Site.Data;this.showSaving(!0);n.set_PathToData("controls/Nuggets/ClientInvoiceAdd");n.set_DataObject("ClientInvoiceAdd");n.set_DataAction("SaveLine");n.addParameter("id",this._intClientInvoiceID);n.addParameter("GoodsInLineIDs",$R_FN.arrayToSingleString(this._aryGoodsInLineIds));n.addDataOK(Function.createDelegate(this,this.saveLineOK));n.addError(Function.createDelegate(this,this.saveLineError));n.addTimeout(Function.createDelegate(this,this.saveLineError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null}},saveLineError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},saveLineOK:function(n){n._result.Result?this.updateHeader():(this._strErrorMessage=n._errorMessage,this.onSaveError())},updateHeader:function(){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/ClientInvoiceAdd");n.set_DataObject("ClientInvoiceAdd");n.set_DataAction("UpdateHeader");n.addParameter("id",this._intClientInvoiceID);n.addParameter("SecondRef",this.getFieldValue("ctlSecondRef"));n.addParameter("Narrative",this.getFieldValue("ctlNarrative"));n.addParameter("CanBeExported",this.getFieldValue("ctlCanExported"));n.addDataOK(Function.createDelegate(this,this.updateHeaderComplete));n.addError(Function.createDelegate(this,this.updateHeaderError));n.addTimeout(Function.createDelegate(this,this.updateHeaderError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},updateHeaderError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},updateHeaderComplete:function(n){this.showSaving(!1);n._result.Result?this.onSaveComplete():(this._strErrorMessage=n._errorMessage,this.onSaveError())},validateForm:function(){this.onValidate();var n=this.autoValidateFields();return n||this.showError(!0),this.getFieldValue("ctlSecondRef").length>16&&(this.showError(!0,"SecondRef should be less than 16 characters"),n=!1),this.getFieldValue("ctlNarrative").length>41&&(this.showError(!0,"Narrative should be less than 41 characters"),n=!1),this._aryGoodsInLineIds.length>0||(this.showError(!0,"Please select atleast one goodsIn line"),n=!1),n},ctlSelectSIGILines_PotentialStatusChange:function(){this.setFieldValue("ctlSecondRef",(this._strSecondRef.length<=0?"":this._strSecondRef+(this._ctlSelectSIGILines._aryPONumber.length>0?this._lnsSeperator:""))+$R_FN.arrayToSingleString(this._ctlSelectSIGILines._aryPONumber,this._lnsSeperator));this.setFieldValue("ctlNarrative",(this._strNarrative.length<=0?"":this._strNarrative+(this._ctlSelectSIGILines._aryGINumber.length>0?this._lnsSeperator:""))+$R_FN.arrayToSingleString(this._ctlSelectSIGILines._aryGINumber,this._lnsSeperator));document.getElementById(this._SelectedGI).value=$R_FN.formatCurrency(parseFloat(this._ctlSelectSIGILines._floatTotalSelectedValue)+parseFloat(this._floatLineTotal),null,2,!1);this._aryGoodsInLineIds=this._ctlSelectSIGILines._aryGoodsInLineIds},getClientInvoice:function(){this.showLoading(!0);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/ClientInvoiceMainInfo");n.set_DataObject("ClientInvoiceMainInfo");n.set_DataAction("GetData");n.addParameter("ID",this._intClientInvoiceID);n.addDataOK(Function.createDelegate(this,this.getClientInvoiceOK));n.addError(Function.createDelegate(this,this.getClientInvoiceError));n.addTimeout(Function.createDelegate(this,this.getClientInvoiceError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getClientInvoiceOK:function(n){var t=n._result;this._intCompanyID=t.CompanyNo;this.showField("ctlSupplierCode",!1);this.setFieldValue("ctlSupplier",$R_FN.setCleanTextValue(t.SupplierName));this.setFieldValue("ctlSupplierCode",$R_FN.setCleanTextValue(t.SupplierCode));this.setFieldValue("ctlClientInvoice",t.ClientInvoiceNumber);this.setFieldValue("ctlInvoiceDate",t.ClientInvoiceDate);this.setFieldValue("ctlCurrency",t.CurrencyCode);this.setFieldValue("ctlInvoiceAmount",t.InvoiceAmount);this.setFieldValue("ctlGoodsValue",t.GoodsValue);this.setFieldValue("ctlTax",t.Tax);this.setFieldValue("ctlDeliveryCharge",t.DeliveryCharge);this.setFieldValue("ctlBankFee",t.BankFee);this.setFieldValue("ctlCreditCardFee",t.CreditCardFee);this.setFieldValue("ctlNotes",$R_FN.setCleanTextValue(t.Notes));this.setFieldValue("ctlCanExported",t.CanbeExported);this.setFieldValue("ctlSecondRef",$R_FN.setCleanTextValue(t.SecondRef));this.setFieldValue("ctlNarrative",$R_FN.setCleanTextValue(t.Narrative));document.getElementById(this._SelectedGI).value=this._floatLineTotal;this._strSecondRef=t.SecondRef;this._strNarrative=t.Narrative;this.showLoading(!1);this.showInnerContent(!0);this._ctlSelectSIGILines._CompanyNo=this._intCompanyID;this._ctlSelectSIGILines.setFieldValue("ctlGIDateFrom",this._dtFromDate);this._ctlSelectSIGILines.setFieldValue("ctlGIDateTo",this._dtToDate);this._ctlSelectSIGILines.setFieldValue("ctlIncludeInvoiced",!0);this._ctlSelectSIGILines.searchClicked()},getClientInvoiceError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()}};Rebound.GlobalTrader.Site.Controls.Forms.ClientInvoiceLines_Add.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.ClientInvoiceLines_Add",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);