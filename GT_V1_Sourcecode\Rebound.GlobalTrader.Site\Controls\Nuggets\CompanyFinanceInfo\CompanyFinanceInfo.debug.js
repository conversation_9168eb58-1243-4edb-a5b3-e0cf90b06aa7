///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-------------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//
// RP 17.11.2009:
// - get the YTD / last year values in one hit
// - don't get data straight away because the Company Detail page now switches tabs
//   with javascript



//-------------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");

Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyFinanceInfo = function(element) {
    Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyFinanceInfo.initializeBase(this, [element]);
	this._intCompanyID = -1;
	this._warehouseNo = -1;
};

Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyFinanceInfo.prototype = {

    get_intCompanyID: function() { return this._intCompanyID; }, set_intCompanyID: function(value) { if (this._intCompanyID !== value) this._intCompanyID = value; },
    get_ibtnLink: function() { return this._ibtnLink; }, set_ibtnLink: function(value) { if (this._ibtnLink !== value) this._ibtnLink = value; },
// [002] End Here
   get_Status: function() { return this._Status; }, set_Status: function(value) { if (this._Status !== value) this._Status = value; },

    initialize: function() {
        Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyFinanceInfo.callBaseMethod(this, "initialize");

        //data
        this._strPathToData = "controls/Nuggets/CompanyFinanceInfo";
        this._strDataObject = "CompanyFinanceInfo";

        //events
        this.addRefreshEvent(Function.createDelegate(this, this.getData));
        //ellipses clicks


        //form
        if (this._ibtnLink) {
            $R_IBTN.addClick(this._ibtnLink, Function.createDelegate(this, this.showEditForm));
            this._frmEdit = $find(this._aryFormIDs[0]);
            this._frmEdit.addCancel(Function.createDelegate(this, this.cancelEdit));
            this._frmEdit.addSaveComplete(Function.createDelegate(this, this.saveEditComplete));
            this._frmEdit.addSaveError(Function.createDelegate(this, this.saveEditError));
        }
        var that = this;
        $('#RefreshCompanyFinanceInfoCurrencyDropdown').click(function () {
            that.getCurrencyDropdownData();
        });
        this.getCurrencyDropdownData();
        this.getData();
        this.getCompanyInactive();
    },

    dispose: function() {
        if (this.isDisposed) return;

        this._intCompanyID = null;
        this._ibtnLink = null;
        Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyFinanceInfo.callBaseMethod(this, "dispose");
    },

    getData: function() {
        this.getData_Start();
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData(this._strPathToData);
        obj.set_DataObject(this._strDataObject);
        obj.set_DataAction("GetData");
        obj.addParameter("id", this._intCompanyID);
        obj.addDataOK(Function.createDelegate(this, this.getDataOK));
        obj.addError(Function.createDelegate(this, this.getDataError));
        obj.addTimeout(Function.createDelegate(this, this.getDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getDataOK: function (args) {
        var res = args._result;
        if (res != null && res.SelectedCompanies.length > 0) {
            res = res.SelectedCompanies;
            console.log(res);
            $('#CompanyFinanceInfoCurrencyDropdown option').each(function () {
                if (this.value == res[0].CurrencyNo) {
                    $("#CompanyFinanceInfoCurrencyDropdown").val(res[0].CurrencyNo);
                }
            });
            var content = "<table class='LinkedItems'>";
            content += '<tr>';
            for (i = 0; i < res.length; i++) {
                content += '<td class="LinkedItemTD">';
                content += '<table class="dataItems">';
                content += '<tr><td class="desc">Company Name</td><td class="item"><a href="/Con_CompanyDetail.aspx?cm=' + res[i].CompanyID + '">' + $R_FN.setCleanTextValue(res[i].CompanyName) + '</a></td></tr>';
                content += '<tr><td class="desc">Sales Person</td>' + '<td class="item">' + res[i].Salesman + '</td></tr>';

                content += '<tr><td class="desc">Approved By</td><td class="item"><div class="imageCheckBoxDisabled"><img style="border-width: 0px;width:12px;height:12px;border:none !important;" class="';
                if (res[i].IsApproved) {
                    content += 'on" src="../../../App_Themes/Original/images/ImageCheckBox/on_dis.gif"';
                }
                else {
                    content += 'off" src="../../../App_Themes/Original/images/ImageCheckBox/off_dis.gif"';
                }
                content += '>&nbsp;&nbsp;<span style="font-size:9px;vertical-align:top;color:#808080;">' + res[i].ApprovedByAndDate + '</span></div>' + '</td></tr>';

                content += '<tr><td class="desc">Stop Status</td>' + '<td class="item">' + res[i].StopStatus + '</td></tr>';
                content += '<tr><td class="desc">Currency</td>' + '<td class="item">' + res[i].Currency + '</td></tr>';
                content += '<tr><td class="desc">Customer No</td>' + '<td class="item">' + res[i].CustomerNo + '</td></tr>';
                content += '<tr><td class="desc">Terms</td>' + '<td class="item">' + res[i].Terms + '</td></tr>';

                content += '<tr><td class="desc">Rating</td><td class="item"><span class="starsOuter starsOuterReadOnly">';
                for (var j = 1; j < 6; j++) {
                    if (j <= res[i].Rating) {
                        content += '<img class="StarSaved" style="height:12px;width:12px;border-width:0px;margin:1px;" src="../../../App_Themes/Original/images/StarRating/saved.png">';
                    }
                    else {
                        content += '<img class="StarEmpty" style="height:12px;width:12px;border-width:0px;margin:1px;" src="../../../App_Themes/Original/images/StarRating/empty.png">';
                    }
                }
                content += '</span></td></tr>';

                content += '<tr><td class="desc">On Stop?</td><td class="item"><div class="imageCheckBoxDisabled"><img style="border-width: 0px;width:12px;height:12px;border:none !important;" class="';
                if (res[i].OnStop) {
                    content += 'on" src="../../../App_Themes/Original/images/ImageCheckBox/on_dis.gif"';
                }
                else {
                    content += 'off" src="../../../App_Themes/Original/images/ImageCheckBox/off_dis.gif"';
                }
                content += '></div>' + '</td></tr>';

                content += '<tr><td class="desc">Waive Shipping & Shipping Surcharge?</td><td class="item"><div class="imageCheckBoxDisabled"><img style="border-width: 0px;width:12px;height:12px;border:none !important;" class="';
                if (res[i].IsShippingWaived) {
                    content += 'on" src="../../../App_Themes/Original/images/ImageCheckBox/on_dis.gif"';
                }
                else {
                    content += 'off" src="../../../App_Themes/Original/images/ImageCheckBox/off_dis.gif"'
                }
                content += '></div>' + '</td></tr>';

                content += '<tr><td class="desc">Default Contact</td><td class="item">';
                if (res[i].ContactNo != undefined) {
                    content += '<a href = "Con_ContactDetail.aspx?con=' + res[i].ContactNo + '" class="nubButton nubButtonAlignLeft" >' + res[i].ContactName + '</a></td ></tr > ';
                }
                else {
                    content += '</td ></tr >';
                }
                content += '<tr><td class="desc">Notes to Invoice</td>' + '<td class="item">' + $R_FN.setCleanTextValue(res[i].NotesToInvoice) + '</td></tr>';
                content += '<td colspan="2"><div class="line"></div></td>';
                content += '<tr><td class="desc">Preferred Warehouse</td>' + '<td class="item">' + res[i].WarehouseName + '</td></tr>';
                content += '<tr><td class="desc">Max Exposure Credit Limit</td>' + '<td class="item">' + res[i].CreditLimit + '</td></tr>';
                content += '<tr><td class="desc">Credit Limit</td>' + '<td class="item">' + res[i].ActualCreditLimit + '</td></tr>';
                content += '<tr><td class="desc">Year To Date</td>' + '<td class="item">' + res[i].ThisYearValue + '</td></tr>';
                content += '<tr><td class="desc">Last Year</td>' + '<td class="item">' + res[i].LastYearValue + '</td></tr>';
                content += '<tr><td class="desc">Balance</td>' + '<td class="item">' + res[i].Balance + '</td></tr>';
                content += '<tr><td class="desc">Not Overdue</td>' + '<td class="item">' + res[i].Current + '</td></tr>';
                content += '<tr><td class="desc">01- 29 Days</td>' + '<td class="item">' + res[i].Days1 + '</td></tr>';
                content += '<tr><td class="desc">30 - 59 days</td>' + '<td class="item">' + res[i].Days30 + '</td></tr>';
                content += '<tr><td class="desc">60 - 89 days</td>' + '<td class="item">' + res[i].Days60 + '</td></tr>';
                content += '<tr><td class="desc">90 - 119 days</td>' + '<td class="item">' + res[i].Days90 + '</td></tr>';
                content += '<tr><td class="desc">120+ days</td>' + '<td class="item">' + res[i].Days120 + '</td></tr>';
                content += '<tr><td class="desc">Balance with all Posted Lines</td>' + '<td class="item">' + res[i].BalanceWithOpenSalesOrders + '</td></tr>';
                content += '<tr><td class="desc">Invoice Not Exported</td>' + '<td class="item">' + res[i].InvoiceNotExport + '</td></tr>';
                content += '<tr><td class="desc">Year to date [spend | profit (%)]:</td>' + '<td class="item">' + res[i].YearToDate + '</td></tr>';
                content += '<tr><td class="desc">Last year [spend | profit (%)]:</td>' + '<td class="item">' + res[i].LastYear + '</td></tr>';
                content += '<td colspan="2"><div class="line"></div></td>';
                content += '<tr><td class="desc">Insurance File No</td>' + '<td class="item">' + res[i].InsuranceFileNo + '</td></tr>';
                content += '<tr><td class="desc">Insured Amount</td>' + '<td class="item">' + res[i].InsuredAmount + '</td></tr>';
                content += '</table>';
                content += '</td>';
            }
            content += '</tr>';
            content += "</table>";
            console.log(content);
            $('#LinkedCompanyInfo').html(content);
        }
        this.getDataOK_End();
        var str = $("#CompanyFinanceInfoCurrencyDropdown option:selected").text();
        this.getLinkedAccountsCombinedInfo($("#CompanyFinanceInfoCurrencyDropdown").val(), str.substr(0, str.indexOf('-') - 1));
    },

    getDataError: function(args) {
        this.showError(true, args.get_ErrorMessage());
    },
    getCompanyInactive: function () {
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData(this._strPathToData);
        obj.set_DataObject(this._strDataObject);
        obj.set_DataAction("GetCompanyDetailInactive");
        obj.addParameter("id", this._intCompanyID);
        obj.addDataOK(Function.createDelegate(this, this.getCompanyInactiveOK));
        obj.addError(Function.createDelegate(this, this.getCompanyInactiveError));
        obj.addTimeout(Function.createDelegate(this, this.getCompanyInactiveError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getCompanyInactiveOK: function (args) {
        var result = args._result;
        if (this._ibtnLink) $R_IBTN.enableButton(this._ibtnLink, !result.Inactive);
        var financeDrop = document.getElementById("CompanyFinanceInfoCurrencyDropdown");
        var refreshDrop = document.getElementById("RefreshCompanyFinanceInfoCurrencyDropdown");
        if (result.Inactive) {
            financeDrop.style.pointerEvents = 'none';
            financeDrop.style.opacity = 0.8;
            refreshDrop.style.display = 'none';
        } else {
            financeDrop.style.pointerEvents = 'auto';
            financeDrop.style.opacity = 1;
            refreshDrop.style.display = '';
        }
    },

    getCompanyInactiveError: function (args) {
        this.showError(true, args.get_ErrorMessage());
    },
    showEditForm: function () {
        //[003] start
        this._frmEdit._globalLoginClientNo = this._globalLoginClientNo;
        //[003] end
        //[001] Code Start
        $R_FN.showElement(this._ibtnLink, false);
        //[001] Code End

        this.showForm(this._frmEdit, true); 
        //[001] Code Start
        $R_FN.showElement(this._ibtnLink, true);

        
        
    },

    cancelEdit: function() {
        this.showContent(true);
        this.showForm(this._frmEdit, false);
    },

    saveEditComplete: function() {
        this.showForm(this._frmEdit, false);
        this.getData();
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
        this.onSaveEditComplete();
    },

    saveEditError: function() {
        this.showForm(this._frmEdit, false);
        this.showError(true, this._frmEdit._strErrorMessage);
    },
    getCurrencyDropdownData: function () {
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData(this._strPathToData);
        obj.set_DataObject(this._strDataObject);
        obj.set_DataAction("CurrencyDropdownData");
        obj.addDataOK(Function.createDelegate(this, this.getCurrencyDropdownDataOK));
        obj.addError(Function.createDelegate(this, this.getCurrencyDropdownDataError));
        obj.addTimeout(Function.createDelegate(this, this.getCurrencyDropdownDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },
    getCurrencyDropdownDataOK: function (args) {
        var res = args._result.Currencies;
        var defaultOption = '<option value="0">&lt; Select &gt;</option>';
        var optionStart = "<option value='";
        var optionEnd = "</option>";
        var listItems = "";
        //var listItems = defaultOption;
        var SelectedValue = $("#CompanyFinanceInfoCurrencyDropdown").val();
        $("#CompanyFinanceInfoCurrencyDropdown").html("");
        for (var i = 0; i < res.length; i++) {
            listItems += optionStart + res[i].ID + "'>" + res[i].Name + optionEnd;
        }
        $("#CompanyFinanceInfoCurrencyDropdown").html(listItems);
        $("#CompanyFinanceInfoCurrencyDropdown").val(SelectedValue);
        var that = this;
        $("#CompanyFinanceInfoCurrencyDropdown").change(function () {
            var str = $("#CompanyFinanceInfoCurrencyDropdown option:selected").text();
            that.getLinkedAccountsCombinedInfo($("#CompanyFinanceInfoCurrencyDropdown").val(), str.substr(0, str.indexOf('-') - 1));
        });
    },

    getCurrencyDropdownDataError: function (args) {
        this.showError(true, args.get_ErrorMessage());
    },
    getLinkedAccountsCombinedInfo: function (selectedCurrencyNo,CurrencyCode) {
        this.getData_Start();
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData(this._strPathToData);
        obj.set_DataObject(this._strDataObject);
        obj.set_DataAction("GetLinkedAccountsCombinedInfo");
        obj.addParameter("SelectedCurrencyNo", selectedCurrencyNo);
        obj.addParameter("id", this._intCompanyID);
        obj.addParameter("CurrencyCode", CurrencyCode);
        obj.addDataOK(Function.createDelegate(this, this.getLinkedAccountsCombinedInfoOK));
        obj.addError(Function.createDelegate(this, this.getLinkedAccountsCombinedInfoError));
        obj.addTimeout(Function.createDelegate(this, this.getLinkedAccountsCombinedInfoError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },
    getLinkedAccountsCombinedInfoOK: function (args) {
        var res = args._result;
        var creditlimitcontent = "<table class='LinkedItemsCreditLimit dataItems' style='width:40%;'>";
        creditlimitcontent += "<tr>";
        creditlimitcontent += "<td class='desc'>Exchange Rate</td>" + "<td class='item'>" + res.FromConversionRate + " = " + res.ToConversionRate + "</td>";
        creditlimitcontent += "</tr>";
        creditlimitcontent += "<tr>";
        creditlimitcontent += "<td class='desc'>Total Credit Limit</td>" + "<td class='item'>" + res.ActualCreditLimit + "</td>";
        creditlimitcontent += "</tr>";
        creditlimitcontent += "<tr>";
        creditlimitcontent += "<td class='desc'>Total Max Exposure Credit Limit</td>" + "<td class='item'>" + res.CreditLimit + "</td>";
        creditlimitcontent += "</tr>";
        creditlimitcontent += "<tr>";
        creditlimitcontent += "<td class='desc'>Total Insured Amount</td>" + "<td class='item'>" + res.InsuredAmount + "</td>";
        creditlimitcontent += "</tr>";
        creditlimitcontent += "</table>";

        $('#LinkedCompanyCreditInfo').html(creditlimitcontent);
        //$('#ExchangeRatetd').html(res.FromConversionRate + " = " + res.ToConversionRate);

        this.getDataOK_End();
    },

    getLinkedAccountsCombinedInfoError: function (args) {
        this.showError(true, args.get_ErrorMessage());
    }
    //getYearToDate: function () {
    //    var obj = this._fldYearToDate._objData;
    //    obj.set_PathToData(this._strPathToData);
    //    obj.set_DataObject(this._strDataObject);
    //    obj.set_DataAction("GetYearToDateNew");
    //    obj.addParameter("id", this._intCompanyID);
    //},

    //getLastYear: function () {
    //    var obj = this._fldLastYear._objData;
    //    obj.set_PathToData(this._strPathToData);
    //    obj.set_DataObject(this._strDataObject);
    //    obj.set_DataAction("GetLastYearNew");
    //    obj.addParameter("id", this._intCompanyID);
    //}

};

Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyFinanceInfo.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyFinanceInfo", Rebound.GlobalTrader.Site.Controls.Nuggets.Base);
