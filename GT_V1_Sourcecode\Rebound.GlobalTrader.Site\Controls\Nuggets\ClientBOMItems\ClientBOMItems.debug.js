

Rebound.GlobalTrader.Site.Controls.Nuggets.ClientBOMItems = function (element) {
    Rebound.GlobalTrader.Site.Controls.Nuggets.ClientBOMItems.initializeBase(this, [element]);
    this._intBOMID = -1;
    this._intCountStock = 0;
    this._isEnable = false;
    this._intCountTab = 0;
    this._intCustomerRequirementID = -1;
    this._blnRequirementClosed = false;
    this._intCompanyID = -1;
    this._hasSourcingResult = false;
    this._blnRequirementReleased = false;
    this._allExistInSourcingResult = false;
    this._intSelectedLineNo = 0;
    this._BomCode = "";
    this._BomName = "";
    this._BomCompanyName = "";
    this._BomCompanyNo = 0;
    this._SalesManNo = 0;
    this._SalesManName = 0;
    this._lineLength = 0;
    this._isRequestToPurchaseQuote = false;
    this._blnAllHasDelDate = false;
    this._blnAllItemHasDelDate = false;
    this._blnAllHasProduct = false;
    this._blnAllItemHasProduct = false;
    this._blnCanRelease = false;
    this._blnCanRecal = true;
    var bomStatus = "";
    this._isClosed = false;
    //var isClosed=false;
    this._CustReqNo = -1;
    this._blnCanNoBid = true;
    this._isNoBid = false;
    this._ReqIds = [];
    this._ReqNos = [];
    this._BOMNo = -1;
    this._RequestToPOHubBy = -1;
    this._UpdateByPH = -1;
    this._intContact2No = -1;
    this._intClientStatus = -1;
    this._intClientCompanyId = -1;
    this._IsSelectedRequirementRow = false;
};

Rebound.GlobalTrader.Site.Controls.Nuggets.ClientBOMItems.prototype = {
    get_intBOMID: function () { return this._intBOMID; }, set_intBOMID: function (value) { if (this._intBOMID !== value) this._intBOMID = value; },
    get_tblStock: function () { return this._tblStock; }, set_tblStock: function (value) { if (this._tblStock !== value) this._tblStock = value; },
    get_ctlTabStrip: function () { return this._ctlTabStrip; }, set_ctlTabStrip: function (v) { if (this._ctlTabStrip !== v) this._ctlTabStrip = v; },
    get_ctlTabStock: function () { return this._ctlTabStock; }, set_ctlTabStock: function (v) { if (this._ctlTabStock !== v) this._ctlTabStock = v; },
    get_IsEnable: function () { return this._isEnable; }, set_isEnable: function (v) { this._isEnable = v; },
    get_ibtnRelease: function () { return this._ibtnRelease; }, set_ibtnRelease: function (value) { if (this._ibtnRelease !== value) this._ibtnRelease = value; },
    get_blnPOHub: function () { return this._blnPOHub; }, set_blnPOHub: function (value) { if (this._blnPOHub !== value) this._blnPOHub = value; },
    get_ibtnAdd: function () { return this._ibtnAdd; }, set_ibtnAdd: function (v) { if (this._ibtnAdd !== v) this._ibtnAdd = v; },
    get_ibtnAddLineItem: function () { return this._ibtnAddLineItem; }, set_ibtnAddLineItem: function (v) { if (this._ibtnAddLineItem !== v) this._ibtnAddLineItem = v; },
    get_ibtnDelete: function () { return this._ibtnDelete; }, set_ibtnDelete: function (value) { if (this._ibtnDelete !== value) this._ibtnDelete = value; },
    get_ibtnUnRelease: function () { return this._ibtnUnRelease; }, set_ibtnUnRelease: function (value) { if (this._ibtnUnRelease !== value) this._ibtnUnRelease = value; },
    get_pnlLineDetail: function () { return this._pnlLineDetail; }, set_pnlLineDetail: function (v) { if (this._pnlLineDetail !== v) this._pnlLineDetail = v; },
    get_pnlLoadingLineDetail: function () { return this._pnlLoadingLineDetail; }, set_pnlLoadingLineDetail: function (v) { if (this._pnlLoadingLineDetail !== v) this._pnlLoadingLineDetail = v; },
    get_pnlLineDetailError: function () { return this._pnlLineDetailError; }, set_pnlLineDetailError: function (v) { if (this._pnlLineDetailError !== v) this._pnlLineDetailError = v; },
    get_ibtnNoBid: function () { return this._ibtnNoBid; }, set_ibtnNoBid: function (value) { if (this._ibtnNoBid !== value) this._ibtnNoBid = value; },
    get_ibtnRecallNoBid: function () { return this._ibtnRecallNoBid; }, set_ibtnRecallNoBid: function (value) { if (this._ibtnRecallNoBid !== value) this._ibtnRecallNoBid = value; },
    get_ibtnNote: function () { return this._ibtnNote; }, set_ibtnNote: function (value) { if (this._ibtnNote !== value) this._ibtnNote = value; },

    get_ibtnExportPurchaseHUB: function () { return this._ibtnExportPurchaseHUB; }, set_ibtnExportPurchaseHUB: function (v) { if (this._ibtnExportPurchaseHUB !== v) this._ibtnExportPurchaseHUB = v; },//[005] 

    addStartGetData: function (handler) { this.get_events().addHandler("StartGetData", handler); },
    removeStartGetData: function (handler) { this.get_events().removeHandler("StartGetData", handler); },

    onStartGetData: function () {
        var handler = this.get_events().getHandler("StartGetData");
        if (handler) handler(this, Sys.EventArgs.Empty);
    },

    addGotDataOK: function (handler) { this.get_events().addHandler("GotDataOK", handler); },

    removeGotDataOK: function (handler) { this.get_events().removeHandler("GotDataOK", handler); },
    onGotDataOK: function () {
        var handler = this.get_events().getHandler("GotDataOK");
        if (handler) handler(this, Sys.EventArgs.Empty);
    },

    addPartSelected: function (handler) {
        this.get_events().addHandler("PartSelected", handler);
    },
    removePartSelected: function (handler) { this.get_events().removeHandler("PartSelected", handler); },
    onPartSelected: function () {

        var handler = this.get_events().getHandler("PartSelected");
        if (handler) handler(this, Sys.EventArgs.Empty);
    },

    addCallBeforeRelease: function (handler) { this.get_events().addHandler("CallBeforeRelease", handler); },
    removeCallBeforeRelease: function (handler) { this.get_events().removeHandler("CallBeforeRelease", handler); },
    onCallBeforeRelease: function () {
        var handler = this.get_events().getHandler("CallBeforeRelease");
        if (handler) handler(this, Sys.EventArgs.Empty);
    },
    addRefereshAfterRelease: function (handler) { this.get_events().addHandler("RefereshAfterRelease", handler); },
    removeRefereshAfterRelease: function (handler) { this.get_events().removeHandler("RefereshAfterRelease", handler); },
    onRefereshAfterRelease: function () {
        var handler = this.get_events().getHandler("RefereshAfterRelease");
        if (handler) handler(this, Sys.EventArgs.Empty);
    },

    initialize: function () {
        Rebound.GlobalTrader.Site.Controls.Nuggets.ClientBOMItems.callBaseMethod(this, "initialize");
        this.addRefreshEvent(Function.createDelegate(this, this.getData));
        this._tblStock.addSelectedIndexChanged(Function.createDelegate(this, this.tbl_SelectedIndexChanged));

        if (this._ibtnRelease) {
            $R_IBTN.addClick(this._ibtnRelease, Function.createDelegate(this, this.showReleaseForm));
            this._frmConfirm = $find(this._aryFormIDs[0]);
            this._frmConfirm._BomCode = this._BomCode;
            this._frmConfirm._BomName = this._BomName;
            this._frmConfirm._BomCompanyName = this._BomCompanyName;
            this._frmConfirm._BomCompanyNo = this._BomCompanyNo;
            this._frmConfirm._SalesManNo = this._SalesManNo;
            this._frmConfirm._SalesManName = this._SalesManName;
            this._frmConfirm._CustReqNo = this._CustReqNo;
            this._frmConfirm.addCancel(Function.createDelegate(this, this.hideConfirmForm));
            this._frmConfirm.addSaveComplete(Function.createDelegate(this, this.saveCeaseComplete));
            this._frmConfirm.addNotConfirmed(Function.createDelegate(this, this.hideConfirmForm));

        }
        if (this._ibtnAdd) {
            $R_IBTN.addClick(this._ibtnAdd, Function.createDelegate(this, this.showAddForm));
            this._frmAdd = $find(this._aryFormIDs[0]);
            this._frmAdd.addCancel(Function.createDelegate(this, this.hideAddForm));
            this._frmAdd.addSaveComplete(Function.createDelegate(this, this.saveAddComplete));
            this._frmAdd.addNotConfirmed(Function.createDelegate(this, this.hideAddForm));
            this._frmAdd._intBOMID = this._intBOMID;
        }
        if (this._ibtnAddLineItem) {
            $R_IBTN.addClick(this._ibtnAddLineItem, Function.createDelegate(this, this.showAddLineItemForm));
            this._frmAddLineItem = $find(this._aryFormIDs[1]);
            this._frmAddLineItem.addCancel(Function.createDelegate(this, this.hideAddLineItemForm));
            this._frmAddLineItem._intBOMID = this._intBOMID;
        }
        if (this._ibtnExportPurchaseHUB) {
            $R_IBTN.addClick(this._ibtnExportPurchaseHUB, Function.createDelegate(this, this.showAddToHUBRFQForm));
            this._frmAddToHUBRFQ = $find(this._aryFormIDs[2]);
            this._frmAddToHUBRFQ.addCancel(Function.createDelegate(this, this.hideAddToHUBRFQForm));
            this._frmAddToHUBRFQ.addSaveComplete(Function.createDelegate(this, this.saveAddToHUBRFQComplete));
            this._frmAddToHUBRFQ.addNotConfirmed(Function.createDelegate(this, this.hideAddToHUBRFQForm));
            this._frmAddToHUBRFQ._intClientCompanyId = this._intClientCompanyId;
        }
        //if (this._ibtnDelete) {
        //    $R_IBTN.addClick(this._ibtnDelete, Function.createDelegate(this, this.showDeleteForm));
        //    this._frmDelete = $find(this._aryFormIDs[2]);
        //    this._frmDelete.addCancel(Function.createDelegate(this, this.hideDeleteForm));
        //    this._frmDelete._intBOMID = this._intBOMID;
        //    this._frmDelete._intCustomerRequirementID = this._intCustomerRequirementID;
        //    this._frmDelete.addSaveComplete(Function.createDelegate(this, this.DeleteComplete));
        //    this._frmDelete.addNotConfirmed(Function.createDelegate(this, this.hideDeleteForm));
        //}
        //if (this._ibtnUnRelease) {
        //    $R_IBTN.addClick(this._ibtnUnRelease, Function.createDelegate(this, this.showUnReleaseForm));
        //    this._frmUnRelease = $find(this._aryFormIDs[3]);
        //    this._frmUnRelease.addCancel(Function.createDelegate(this, this.hideUnReleaseForm));
        //    this._frmUnRelease._intBOMID = this._intBOMID;
        //    this._frmUnRelease._intCustomerRequirementID = this._intCustomerRequirementID;
        //    this._frmUnRelease.addSaveComplete(Function.createDelegate(this, this.UnReleaseComplete));
        //    this._frmUnRelease.addNotConfirmed(Function.createDelegate(this, this.hideUnReleaseForm));
        //}
        
        this.getData();
    },

    dispose: function () {
        if (this.isDisposed) return;
        if (this._frmTransfer) this._frmTransfer.dispose();
        if (this._ctlTabStrip) this._ctlTabStrip.dispose();
        if (this._ctlTabStock) this._ctlTabStock.dispose();
        if (this._tblStock) this._tblStock.dispose();
        if (this._frmConfirm) this._frmConfirm.dispose();
        this._intBOMID = null;
        this._ctlTabStrip = null;
        this._tblStock = null;
        this._ibtnExportCSV = null;
        this._pnlSummary = null;
        this._ibtnRelease = null;
        this._intCustomerRequirementID = null;
        this._blnRequirementClosed = null;
        this._blnRequirementReleased = null;
        this._blnPOHub = null;
        this._ibtnAdd = null;
        this._ibtnAddLineItem = null;
        this._intCompanyID = null;
        this._intSelectedLineNo = null;
        this._blnAllHasDelDate = null;
        this._blnAllItemHasDelDate = null;
        this._blnAllHasProduct = null;
        this._blnAllItemHasProduct = null;
        this._blnCanRelease = null;
        this._isClosed = null;
        this._ibtnNoBid = null;
        this._isNoBid = null;
        this._ibtnRecallNoBid = null;
        this._intClientStatus = null;
        this._intClientCompanyId = null;
        this._ibtnExportPurchaseHUB = null;
        //this._ibtnNote = null;    
        Rebound.GlobalTrader.Site.Controls.Nuggets.ClientBOMItems.callBaseMethod(this, "dispose");
    },


    getData: function () {
        //this.getData_Start();
        this.showLoading(true);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/ClientBOMItems");
        obj.set_DataObject("ClientBOMItems");
        obj.set_DataAction("GetData");
        obj.addParameter("id", this._intBOMID);
        obj.addDataOK(Function.createDelegate(this, this.getDataOK));
        obj.addError(Function.createDelegate(this, this.getDataError));
        obj.addTimeout(Function.createDelegate(this, this.getDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getDataOK: function (args) {
        this.showLoading(false);
        var res = args._result;
        var aryData, row;
        this._tblStock.clearTable();
        
        this._intCustomerRequirementID = -1;
        this._blnRequirementReleased = false;
        this._intCompanyID = -1;
        this.enableButtons(true);
        this._ctlTabStock.addCountToTitle(0);
        

        //alert(strCSS)
        //end
        //start code by umendra
        if (res.Items.length == 0) {
            $R_IBTN.enableButton(this._ibtnExportPurchaseHUB, false);
        }
        else {
            $R_IBTN.enableButton(this._ibtnExportPurchaseHUB, true);
        }
        //end code by umendra
        if (res.Items) {

            for (var i = 0; i < res.Items.length; i++) {
                var row = res.Items[i];
                //start code by umendra
                if (row.BOMNo != null) {
                    $R_IBTN.enableButton(this._ibtnExportPurchaseHUB, false);
                }
                else {

                    if (this._IsSelectedRequirementRow == true) {
                        $R_IBTN.enableButton(this._ibtnExportPurchaseHUB, true);
                    }
                    else {
                        $R_IBTN.enableButton(this._ibtnExportPurchaseHUB, false);
                    }
                }
                this._lineLength = res.Items.length; 
                this._BomCode = row.BOMCode;
                this._BomName = row.BOMFullName;
                this._BomCompanyName = row.Company;
                this._BomCompanyNo = row.CompanyNo;
                this._SalesManNo = row.Salesman;
                this._SalesManName = row.SalesmanName;
                this._RequestToPOHubBy = row.RequestToPOHubBy;
                this._UpdateByPH = row.UpdateByPH;
                var IsCheckBoxEnabled = row.IsRequestToPurchaseQuote && !this._isClosed;

                var strCSS = "cusReqMainPart";
                if (!row.Alt) strCSS = "cusReqMainPart";
                if (row.Released) strCSS = "readyToShip";
                if (row.Released == false && row.HasSourcingResult == true) strCSS = "allocated";
                if (row.Released) strCSS = "readyToShip";
                if (this._allExistInSourcingResult == false) {
                    this._allExistInSourcingResult = row.HasSourcingResult;
                }
                this._isRequestToPurchaseQuote = row.IsRequestToPurchaseQuote;

                aryData = [
                     $R_FN.writeDoubleCellValue($R_FN.writePartNo(row.Part, row.ROHS), $R_FN.setCleanTextValue(row.CustomerPart))
             , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue($R_FN.setCleanTextValue($RGT_nubButton_CustomerRequirement(row.ID, row.CustReqNo))),  row.Quantity)
            , $R_FN.writeDoubleCellValue($RGT_nubButton_Manufacturer(row.MfrNo, row.Mfr), $R_FN.setCleanTextValue(row.DC))
            , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Product), $R_FN.setCleanTextValue(row.Package))
            , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Company), $R_FN.setCleanTextValue(row.Date))
            , $R_FN.writeDoubleCellValue(row.TPriceInBom, row.SalesmanName)
            , $R_FN.setCleanTextValue(row.Instructions)
            , $R_FN.writeDoubleCellValue(row.MSL, row.FactorySealed)
                ];
                var objExtraData = {
                    Part: $R_FN.setCleanTextValue(row.PartNo)
              , Closed: row.Closed
              , Released: row.Released
              , CMNo: row.CMNo
              , HasSourcingResult: row.HasSourcingResult
              , SourcingResult: row.SourcingResult
              , IPOClientNo: row.IPOClientNo
              , CustomerRequirementNumber: row.CustReqNo
              , IsNoBid: row.IsNoBid
              , BOMNo: row.BOMNo
                };
                this._tblStock.addRow(aryData, row.ID, false, objExtraData);

                bomStatus = row.BOMStatus;
                chk = null;
                row = null; aryData = null; objExtraData = null;
                
                this._ctlTabStock.addCountToTitle(i + 1);
            }
        }






        //if (res.Items) {
        //    this._intCountStock = res.Count;
        //    this._blnAllItemHasDelDate = res.AllHasDelDate;
        //    this._blnAllItemHasProduct = res.AllHasProduct;

        //}

        //this.disableItemAddButton(!this._blnRequestedToPoHub && !this._inActive && !this._isClosed);
        //this.getDataOK_End();
        //Array.clear(this._ReqIds);
        this._tblStock.resizeColumns();
        this.showContent(true);
        this.showContentLoading(false);
       
    },

   

    getDataError: function (args) {
        this.showError(true, args.get_ErrorMessage());
    },

    enableButtons: function (bln) {
        if (bln) {
            if (this._ibtnAdd) $R_IBTN.enableButton(this._ibtnAdd, !(this._intClientStatus === 4 || this._intClientStatus === 3));
            if (this._ibtnAddLineItem) $R_IBTN.enableButton(this._ibtnAddLineItem, !(this._intClientStatus === 4 || this._intClientStatus === 3));
        }
        else {
            if (this._ibtnAdd) $R_IBTN.enableButton(this._ibtnAdd, false);
            if (this._ibtnAddLineItem) $R_IBTN.enableButton(this._ibtnAddLineItem, false);
        }

    },

    saveError: function (args) {
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    showExportCSV: function () {
        this.getData_Start();
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/ClientBOMItems");
        obj.set_DataObject("ClientBOMItems");
        obj.set_DataAction("ExportToCSV");
        obj.addParameter("id", this._intBOMID);
        obj.addDataOK(Function.createDelegate(this, this.exportCSV_OK));
        obj.addError(Function.createDelegate(this, this.exportCSV_Error));
        obj.addTimeout(Function.createDelegate(this, this.exportCSV_Error));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
        this.onSave();
    },
    exportCSV_OK: function (args) {
        var res = args._result;
        if (res.Filename) {
            //add the date to the file to force the latest version to be returned (not from cache)
            var dt = new Date();
            location.href = String.format("{0}?t={1}", res.Filename, dt.getTime());
            dt = null;
        }
        this.getDataOK_End();
    },

    exportCSV_Error: function (args) {
        this.showError(true, args.get_ErrorMessage());
    },
    showAddForm: function () {
        this.showForm(this._frmAdd, true);
    },
    hideAddForm: function () {
        this.showForm(this._frmAdd, false);
    },
    showAddLineItemForm: function () {
        //clearGrid();

        GetBOMInfo.call(this);;
        GetAllColumn.call(this);
        GetMappedColumn.call(this);
        deRegisterClick.call(this);
        $("#btnLockMapping").click(function () { LockMapping(); event.preventDefault(); });
        $("#btnProcess").click(function () {
            Process();
            event.preventDefault();
        });
        $("#btnCancel").click(function () { closeForm(); });
        $("#btnSubmit").click(function () { SaveUpdateRecord(); });
        $("#btnReset").click(function () { resetBomData(); });
        $(".popupCloseButton").click(function () {
            closeForm();
        });
        this.showForm(this._frmAddLineItem, true);
        bindGridData.call(this);
        //hide left menu
        this.showLeftMenu();
    },
    showLeftMenu: function () {
        $link = $('#ctl00_pnlLeftButton a:first');
        $link[0].click();

    },
    hideLeftMenu: function () {
        $link = $('#ctl00_pnlLeftButton a:first');
        $link[0].click();
    },
    hideAddLineItemForm: function () {
        deleteTempMapping.call(this);
        refreshHeader.call(this);
        refreshUploadedFilesLog.call(this);
        this.showForm(this._frmAddLineItem, false);
        this.getData();
        this.hideLeftMenu();
    },
    saveAddComplete: function () {
        //this._decPercentage = 0;
        this.hideAddForm();
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
        this.getData();
    },
    showReleaseForm: function () {
        this.onCallBeforeRelease();
        if (this._blnCanRelease) {
            this._frmConfirm._intRequirementLineID = this._intCustomerRequirementID;
            this._frmConfirm._intBOMID = this._intBOMID;
            this._frmConfirm._BomCode = this._BomCode;
            this._frmConfirm._BomName = this._BomName;
            this._frmConfirm._BomCompanyName = this._BomCompanyName;
            this._frmConfirm._BomCompanyNo = this._BomCompanyNo;
            this._frmConfirm._SalesManNo = this._SalesManNo;
            this._frmConfirm._SalesManName = this._SalesManName;
            this._frmConfirm._CustReqNo = this._CustReqNo;
            this.showForm(this._frmConfirm, true);
        }
    },



    hideConfirmForm: function () {
        this.showForm(this._frmConfirm, false);
    },

    saveCeaseComplete: function () {
        this.hideConfirmForm();
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
        this.onRefereshAfterRelease();
    },

    enableBOMProvision: function (bln) {
        if (this._ibtnStockProvision) $R_IBTN.enableButton(this._ibtnStockProvision, bln && this._blnProvisionLoaded);
    },
    tbl_SelectedIndexChanged: function () {
        this._IsSelectedRequirementRow = true;
        $R_IBTN.enableButton(this._ibtnExportPurchaseHUB, true);
        this.enableDisableItemDeleteButton(this._isRequestToPurchaseQuote);
        this._intCustomerRequirementID = this._tblStock._varSelectedValue;
        this._intSelectedLineNo = this._tblStock._varSelectedValue;
        this._blnRequirementClosed = this._tblStock.getSelectedExtraData().Closed;
        this._blnRequirementReleased = this._tblStock.getSelectedExtraData().Released;
        this._intCompanyID = this._tblStock.getSelectedExtraData().CMNo;
        this._hasSourcingResult = this._tblStock.getSelectedExtraData().HasSourcingResult;
        this._CustReqNo = this._tblStock.getSelectedExtraData().CustomerRequirementNumber;
        this._isNoBid = this._tblStock.getSelectedExtraData().IsNoBid;
        this.enableButtons(true);
        this.onPartSelected();
        this.onGotDataOK();
        this.getLineData();
    },
    getSelectedPartNo: function () {
        return this._tblStock.getSelectedExtraData().Part;
    },
    getIPOClientNo: function () {
        return this._tblStock.getSelectedExtraData().IPOClientNo;
    },
    enableItemReleaseButton: function (hasSourcingResult) {
        if (this._ibtnRelease) $R_IBTN.enableButton(this._ibtnRelease, this._intCustomerRequirementID > 0 && !this._blnRequirementReleased && this._blnPOHub && hasSourcingResult && !this._isClosed);
    },
    disableItemAddButton: function (isRequestedToPQ) {
        // if (this._ibtnAdd) $R_IBTN.enableButton(this._ibtnAdd, isRequestedToPQ);
    }
    ,
    enableDisableItemDeleteButton: function (sendToPurchaseRequest) {
        if (this._ibtnDelete) $R_IBTN.enableButton(this._ibtnDelete, this._lineLength > 0 && !sendToPurchaseRequest && !this._isClosed);

    },
    getLineData: function () {

        this._blnLineLoaded = false;
        var obj = new Rebound.GlobalTrader.Site.Data();
        this.showLoading(true);
        $R_FN.showElement(this._pnlLineDetail, false);
        $R_FN.showElement(this._pnlLoadingLineDetail, true);
        $R_FN.showElement(this._pnlLineDetailError, false);
        obj.set_PathToData("controls/Nuggets/ClientBOMItems");
        obj.set_DataObject("ClientBOMItems");
        obj.set_DataAction("GetItem");
        obj.addParameter("id", this._intCustomerRequirementID);
        obj.addDataOK(Function.createDelegate(this, this.getLineDataOK));
        obj.addError(Function.createDelegate(this, this.getLineDataError));
        obj.addTimeout(Function.createDelegate(this, this.getLineDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getLineDataOK: function (args) {
        var res = args._result;
        //alert(res.BOMId);
        this.setFieldValue("hidCompanyID", res.CustomerNo);
        this.setFieldValue("hidCompanyName", $R_FN.setCleanTextValue(res.CustomerName));
        this.setFieldValue("hidContactID", res.ContactNo);
        this.setFieldValue("hidContactName", $R_FN.setCleanTextValue(res.Contact));
        this.setFieldValue("ctlQuantity", res.Quantity);
        this.setFieldValue("ctlPartNo", res.Part);
        this.setFieldValue("ctlCustomerPart", $R_FN.setCleanTextValue(res.CustomerPart));
        this.setFieldValue("ctlManufacturer", $RGT_nubButton_Manufacturer(res.ManufacturerNo, res.Manufacturer));
        this.setFieldValue("hidManufacturer", $R_FN.setCleanTextValue(res.Manufacturer));
        this.setFieldValue("hidManufacturerNo", res.ManufacturerNo);
        this.setFieldValue("ctlDateCode", res.DateCode);
        this.setFieldValue("ctlProduct", $R_FN.setCleanTextValue(res.Product));
        this.setFieldValue("hidProductID", res.ProductNo);
        this.setFieldValue("ctlPackage", $R_FN.setCleanTextValue(res.Package));
        this.setFieldValue("hidPackageID", res.PackageNo);
        this.setFieldValue("ctlTargetPrice", res.Price);
        this.setFieldValue("hidPrice", res.PriceRaw);
        this.setFieldValue("ctlCurrency", $R_FN.setCleanTextValue(res.Currency));
        this.setFieldValue("hidCurrencyID", res.CurrencyNo);
        this.setFieldValue("ctlDateRequired", res.DatePromised);
        this.setFieldValue("ctlUsage", $R_FN.setCleanTextValue(res.Usage));
        this.setFieldValue("hidUsageID", $R_FN.setCleanTextValue(res.UsageNo));
        this.setFieldValue("ctlNotes", $R_FN.setCleanTextValue(res.Notes));
        this.setFieldValue("ctlInstructions", $R_FN.setCleanTextValue(res.Instructions));
        this.setFieldValue("ctlROHS", $R_FN.writeROHS(res.ROHS));
        this.setFieldValue("hidROHS", res.ROHS);
        this.setFieldValue("ctlClosedReason", res.ClosedReason);
        this.setFieldValue("hidDisplayStatus", $R_FN.setCleanTextValue(res.DisplayStatus));
        this.setFieldValue("ctlPartWatch", res.PartWatch);
        this.setFieldValue("ctlBOM", res.BOM);
        this.setFieldValue("ctlBOMName", res.BOMName);
        this.setFieldValue("hidBOMID", res.BOMId);
        this.setFieldValue("hidBOMHeaderDisplayStatus", res.RequestToPOHubBy == null ? true : false);
        this.setFieldValue("ctlMSL", res.MSL);
        this.setFieldValue("ctlFactorySealed", res.FactorySealed);
        this.setFieldValue("ctlPQA", res.PQA);
        this.setFieldValue("ctlObsolete", res.Obsolete);
        this.setFieldValue("ctlLastTimeBuy", res.LastTimeBuy);
        this.setFieldValue("ctlRefirbsAcceptable", res.RefirbsAcceptable);
        this.setFieldValue("ctlTestingRequired", res.TestingRequired);
        this.setFieldValue("ctlTargetSellPrice", res.TargetSellPrice);
        this.setFieldValue("ctlCompetitorBestoffer", res.CompetitorBestOffer);
        this.setFieldValue("ctlCustomerDecisionDate", res.CustomerDecisionDate);
        this.setFieldValue("ctlRFQClosingDate", res.RFQClosingDate);
        this.setFieldValue("ctlQuoteValidityRequiredHid", res.QuoteValidityRequired);
        this.setFieldValue("ctlQuoteValidityRequired", res.QuoteValidityText);
        this.setFieldValue("ctlTypeHid", res.Type);
        this.setFieldValue("ctlType", res.ReqTypeText);
        this.setFieldValue("ctlOrderToPlace", res.OrderToPlace);
        this.setFieldValue("ctlRequirementforTraceability", res.ReqForTraceabilityText);
        this.setFieldValue("ctlRequirementforTraceabilityHid", res.RequirementforTraceability);
        this.setFieldValue("ctlTargetSellPriceHidden", res.hidTargetSellPrice);
        this.setFieldValue("ctlCompetitorBestofferHidden", res.hidCompetitorBestOffer);
        this.setFieldValue("ctlEAU", res.EAU);
        this.setFieldValue("ctlIsNoBid", $R_FN.showLargeFonts(res.IsNoBidStatus));
        this.showField("ctlIsNoBid", res.IsNoBid);
        this.setFieldValue("ctlIsNoBidNotes", res.NoBidNotes);
        this.showField("ctlIsNoBidNotes", res.IsNoBid);
        ////[001] Start Here
        this.setFieldValue("ctlClosed", res.Closed == false ? $R_FN.showLargeFontsWithColor('No') : $R_FN.showLargeFonts('Yes'));
        //[001] End Here
        this.setFieldValue("ctlAlternativesAccepted", res.AlternativesAccepted);
        this.setFieldValue("ctlRepeatBusiness", res.RepeatBusiness);
        this.setFieldValue("ctlPrdDutyCodeRate", res.DutyCodeAndRate);
        this._isNoBid = res.IsNoBid;
        this._blnCanRecal = res.SourcingResult;
        this.setFieldValue("hidMSL", res.MSLLevelNo);
        this.setFieldValue("ctlBOMHeader", $RGT_nubButton_BOM(res.BOMId, $R_FN.setCleanTextValue(res.BOMHeader)));
        if (res.BOMId > 0)
            $R_IBTN.enableButton(this._ibtnExportPurchaseHUB, false);
        this.enableButtons(true);
        $R_FN.showElement(this._pnlLineDetail, true);
        $R_FN.showElement(this._pnlLoadingLineDetail, false);
        this.showLoading(false);
        //added on 17 May 2018
        this.onGotDataOK();
    },

    getLineDataError: function (args) {
        this.showLoading(false);
        $R_FN.showElement(this._pnlLoadingLineDetail, false);
        $R_FN.showElement(this._pnlLineDetailError, true);
        $R_FN.setInnerHTML(this._pnlLineDetailError, args.get_ErrorMessage());
        Array.clear(this._ReqIds);
    },

    writeCheckbox: function (varID, intIndex, tbl) {
        var strChkID = this.getControlID("chk", intIndex, tbl);
        var strChkImageID = this.getControlID("chkImg", intIndex, tbl);
        var str = String.format("<div class=\"imageCheckBoxDisabled\" id=\"{0}\" ><img id=\"{1}\" class=\"{2}\" src=\"images/x.gif\" style=\"border-width: 0px;\" /> </div>", strChkID, strChkImageID, "off");
        return str;
    },
    getControlID: function (str, i, tbl) {
        return String.format("{0}_{1}{2}", tbl._element.id, str, i);
    },
    getCheckBox: function (intCheckBox, tbl) {
        return $find(this.getControlID("chk", intCheckBox, tbl));
    },
    registerCheckBox: function (varID, intIndex, blnChecked, blnEnabled, tbl) {

        var strChkID = this.getControlID("chk", intIndex, tbl);

        var strChkImageID = this.getControlID("chkImg", intIndex, tbl);

        var chk = this.getCheckBox(intIndex, tbl);

        if (chk) {
            chk.dispose();
            chk = null;
        }

        eval($R_FN.getCreateStatement("Rebound.GlobalTrader.Site.Controls.ImageCheckBox", [["blnChecked", blnChecked], ["blnEnabled", blnEnabled], ["img", String.format("$get(\"{0}\")", strChkImageID)]], strChkID));

    },

    getCheckedCellValue: function (intIndex, ID) {
        var tbl = this._tblStock;
        var chk = this.getCheckBox(intIndex, tbl);
        var IsChecked = chk._blnChecked;
        var tr = tbl._tbl.rows[intIndex];
        if (!tr) return;
        if (IsChecked) {
            $R_IBTN.enableButton(this._ibtnNote, true);
            Array.add(this._ReqIds, ID);
        }
        else {
            $R_IBTN.enableButton(this._ibtnNote, false);
            Array.remove(this._ReqIds, ID)
        }
        if (this._ReqIds.length == 0) {
            $R_IBTN.enableButton(this._ibtnNote, false);
        }
        else {
            $R_IBTN.enableButton(this._ibtnNote, true);
        }
    },
    enableItemUnReleaseButton: function (hasSourcingResult) {
        // if (this._ibtnUnRelease) $R_IBTN.enableButton(this._ibtnUnRelease, this._intCustomerRequirementID > 0 && this._blnRequirementReleased && this._blnPOHub && hasSourcingResult);
    },
    showAddToHUBRFQForm: function () {
        this._frmAddToHUBRFQ._intClientCompanyId = this._intClientCompanyId;
        this._frmAddToHUBRFQ._CustReqNo = this._CustReqNo;
        this.showForm(this._frmAddToHUBRFQ, true);

    },
    hideAddToHUBRFQForm: function () {
        this.showForm(this._frmAddToHUBRFQ, false);
    },
    saveAddToHUBRFQComplete: function () {
        //this._decPercentage = 0;
        this.hideAddToHUBRFQForm();
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
        this.getData();
    }
};
Rebound.GlobalTrader.Site.Controls.Nuggets.ClientBOMItems.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.ClientBOMItems", Rebound.GlobalTrader.Site.Controls.Nuggets.Base);
