﻿
GO

/****** Object:  StoredProcedure [dbo].[usp_update_Quote<PERSON>ine_Close]    Script Date: 12/11/2024 8:59:55 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].[usp_update_QuoteLine_Close] 
--*-----------------------------------------------------------------------------------------------
--* RP 30.11.2009:
--* - copy NotQuoted flag into QuoteLine from Reason
--*-----------------------------------------------------------------------------------------------
    @Quote<PERSON>ineId int
  , @ReasonNo int
  , @UpdatedBy int
  , @Reasons NVARCHAR(200)=null -- Added as per client requirement 14-3-2018
  , @RowsAffected int = NULL OUTPUT
AS 
    DECLARE @SourcingResultNo INT 
    SELECT @SourcingResultNo=SourcingResultNo FROM dbo.tbQuoteLine WHERE   QuoteLineId = @QuoteLineId
    UPDATE  dbo.tbQuoteLine
    SET     ReasonNo = @ReasonNo
          , Closed = 1
          , UpdatedBy = @UpdatedBy
          , NotQuoted = (SELECT NotQuoted FROM tbReason WHERE ReasonId = @ReasonNo)
          , DLUP = CURRENT_TIMESTAMP
		  , Reasons = @Reasons  -- Added as per client requirement 14-3-2018
    WHERE   QuoteLineId = @QuoteLineId

    SELECT  @RowsAffected = @@rowcount
    
    --now check to see if quote should be closed
    DECLARE @QuoteNo int
    SELECT  @QuoteNo = QuoteNo
    FROM    dbo.tbQuoteLine
    WHERE   QuoteLineId = @QuoteLineId
    
    EXEC usp_update_Quote_CheckClosed @QuoteNo
	UPDATE tbSourcingResult set Closed = null where SourcingResultId = @SourcingResultNo

GO


