///<reference name="MicrosoftAjax.js" />
///<reference path="~/Common/Data/Data.js" />
///<reference path="~/Common/Functions/Functions.js" />
///<reference path="~/Controls/IconButton/IconButton.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - add full disposing event
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.FilterDataItemRows");

Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.Numerical = function(element) { 
	Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.Numerical.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.Numerical.prototype = {

	get_txt: function() { return this._txt; }, set_txt: function(value) { if (this._txt !== value)  this._txt = value; }, 
	get_ddl: function() { return this._ddl; }, set_ddl: function(value) { if (this._ddl !== value)  this._ddl = value; }, 

	addEnterPressed: function(fn) {
		$R_TXTBOX.addEnterPressedEvent(this._txt, fn);
	},

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.Numerical.callBaseMethod(this, "initialize");
		$addHandler(this._txt, "focus", Function.createDelegate(this, this.onFocus));
		$addHandler(this._txt, "blur", Function.createDelegate(this, this.onBlur));
		$addHandler(this._txt, "keyup", Function.createDelegate(this, this.onChange));
		$addHandler(this._ddl, "focus", Function.createDelegate(this, this.onFocus));
		$addHandler(this._ddl, "blur", Function.createDelegate(this, this.onBlur));
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this.get_element()) $clearHandlers(this.get_element());
		if (this._txt) $clearHandlers(this._txt);
		if (this._ddl) $clearHandlers(this._ddl);
		this._txt = null;
		this._ddl = null;
		Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.Numerical.callBaseMethod(this, "dispose");
	},
	
	onFocus: function() {
		this.enableField(true);
	},
	
	onBlur: function() {
		this.enableField(this.isEntered());
	},
	
	onChange: function() {
		this.enableField(this.isEntered());
	},
	
	getValue: function() {
		return this._txt.value;
	},
	
	getMinValue: function() {
		var obj = $R_FN.parseComparisonToMinMax(this._ddl.value, this._txt.value);
		return obj.Min;
	},
	
	getMaxValue: function() {
		var obj = $R_FN.parseComparisonToMinMax(this._ddl.value, this._txt.value);
		return obj.Max;
	},
	
	setValue: function(v) {
		if (typeof(v) == "undefined" || v == null) v = "";
		this._txt.value = v;
		this.enableField(this._txt.value.trim().length > 0);
	},

	reset: function() {
		this._txt.value = "";
		this._ddl.value = $R_ENUM$NumericalComparisonType.EqualTo;
		this.enableField(false);
	},
	
	setComparisonType: function(enmComparisonType) {
		this._ddl.value = enmComparisonType;
	},
	
	isEntered: function() {
		return $R_FN.isEntered(this._txt.value);
	}
	
};

Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.Numerical.registerClass("Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.Numerical", Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.Base, Sys.IDisposable);
