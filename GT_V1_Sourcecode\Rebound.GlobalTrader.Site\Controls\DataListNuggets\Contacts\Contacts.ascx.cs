/*
 Marker     ChangedBy       Date            Remarks
 [001]      <PERSON><PERSON><PERSON>     13-Sep-2018     [REB-12820]:Provision to add Global Security on Contact Section
 */
using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.DataListNuggets
{
    public partial class Contacts : Base
    {

        #region Properties
        //[001] start
        private bool _IsGlobalLogin = false;
        public bool IsGlobalLogin
        {
            get { return _IsGlobalLogin; }
            set { _IsGlobalLogin = value; }
        }
        //[001] end
        #endregion

        #region Overrides

        /// <summary>
        /// OnInit
        /// </summary>
        /// <param name="e"></param>
        protected override void OnInit(EventArgs e)
        {
            //[001] start
            IsGlobalLogin = SessionManager.IsGlobalUser.Value;
            //[001] end
            SetDataListNuggetType("Contacts");
            base.OnInit(e);
            AddScriptReference("Controls.DataListNuggets.Contacts.Contacts.js");
            TitleText = Functions.GetGlobalResource("CompanyListType", DataListNuggets.CompanyListType.Contacts);
            SetupTable();
        }

        protected override void OnLoad(EventArgs e)
        {
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.DataListNuggets.Contacts", ctlDesignBase.ClientID);
            //[001] start
            _scScriptControlDescriptor.AddProperty("IsGlobalLogin", _IsGlobalLogin);
            //[001] end
            base.OnLoad(e);
        }

        protected override void GetSavedState()
        {
            base.GetSavedState();

            //don't render state if we are searching for a Contact
            if (!String.IsNullOrEmpty(_objQSManager.ContactName))
            {
                ResetAllState();
                string[] arySplitContactName = _objQSManager.ContactName.Split(' ');
                ((Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.TextBox)this.FindFilterControl("ctlFirstName")).SetInitialValue(arySplitContactName[0]);
                if (arySplitContactName.Length > 1)
                {
                    ((Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.TextBox)this.FindFilterControl("ctlLastName")).SetInitialValue(_objQSManager.ContactName.Replace(String.Format("{0} ", arySplitContactName[0]), ""));
                }
                _enmViewLevel = ViewLevelList.Company;
            }
        }

        protected override void RenderAdditionalState()
        {
            string strViewLevel = this.GetSavedStateValue("ViewLevel");
            if (!string.IsNullOrEmpty(strViewLevel)) _enmViewLevel = (ViewLevelList)Convert.ToInt32(strViewLevel);
            base.RenderAdditionalState();
        }

        protected override void OnPreRender(EventArgs e)
        {
            base.OnPreRender(e);
            ((Pages.Content)Page).CurrentTab = Convert.ToInt32(_enmViewLevel);
            this.OnAskPageToChangeTab();
        }

        #endregion

        private void SetupTable()
        {
            _tbl.AllowSelection = false;
            _tbl.Columns.Add(new FlexiDataColumn("ContactName", WidthManager.GetWidth(WidthManager.ColumnWidth.ContactName), true));
            _tbl.Columns.Add(new FlexiDataColumn("Title", Unit.Empty, true));
            _tbl.Columns.Add(new FlexiDataColumn("Company", Unit.Empty, true));
            _tbl.Columns.Add(new FlexiDataColumn("Tel","Email", WidthManager.GetWidth(WidthManager.ColumnWidth.TelNo), false));
            _tbl.Columns.Add(new FlexiDataColumn("Salesperson", WidthManager.GetWidth(WidthManager.ColumnWidth.LoginName), true));
            //[001] start
            if (_IsGlobalLogin == true)
            {
                _tbl.Columns.Add(new FlexiDataColumn("Client", WidthManager.GetWidth(WidthManager.ColumnWidth.CompanyName), true));
            }
            //[001] end
        }

    }
}