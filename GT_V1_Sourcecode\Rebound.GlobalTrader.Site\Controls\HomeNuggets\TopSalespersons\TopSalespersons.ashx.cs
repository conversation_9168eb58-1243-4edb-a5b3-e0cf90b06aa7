using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.Site.Controls.HomeNuggets.Data {
	[WebService(Namespace = "http://tempuri.org/")]
	[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
	public class TopSalespersons : Rebound.GlobalTrader.Site.Data.Base {

		public override void ProcessRequest(HttpContext context) {
			if (base.init(context)) {
				switch (Action) {
					case "GetData": GetData(); break;
					default: WriteErrorActionNotFound(); break;
				}
			}
		}

		/// <summary>
		/// Gets the main data
		/// </summary>
		private void GetData() {
            try
            {
                List<Login> lstTopSalespersons = Login.GetListTopSalespersons(SessionManager.ClientID, RowCount);
                if (lstTopSalespersons == null)
                {
                    WriteErrorDataNotFound();
                }
                else
                {
                    //received
                    JsonObject jsn = new JsonObject();
                    JsonObject jsnItems = new JsonObject(true);
                    for (int i = 0; i < lstTopSalespersons.Count; i++)
                    {
                        JsonObject jsnItem = new JsonObject();
                        jsnItem.AddVariable("ID", lstTopSalespersons[i].LoginId);
                        jsnItem.AddVariable("Name", lstTopSalespersons[i].EmployeeName);
                        jsnItem.AddVariable("GrossProfit", Functions.FormatCurrency(lstTopSalespersons[i].GrossProfit, SessionManager.ClientCurrencyCode, 2));
                        jsnItem.AddVariable("Margin", Functions.FormatPercentage(lstTopSalespersons[i].Margin, 2, true));
                        jsnItems.AddVariable(jsnItem);
                        jsnItem.Dispose();
                        jsnItem = null;
                    }
                    jsn.AddVariable("TopSalespersons", jsnItems);
                    jsn.AddVariable("Count", lstTopSalespersons.Count);
                    jsnItems.Dispose();
                    jsnItems = null;

                    OutputResult(jsn);
                    jsn.Dispose();
                    jsn = null;
                }
                lstTopSalespersons = null;
            }
            catch (Exception ex)
            {
                Errorlog obj = new Errorlog();
                obj.LogMessage(Convert.ToString("Exception- " + ex.Message + " " + ex.StackTrace));
            }
        }

	}
}
