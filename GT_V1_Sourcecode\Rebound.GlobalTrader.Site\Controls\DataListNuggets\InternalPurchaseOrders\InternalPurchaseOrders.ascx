﻿<%@ Control Language="C#" AutoEventWireup="true" CodeBehind="InternalPurchaseOrders.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.DataListNuggets.InternalPurchaseOrders" %>

<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" BoxType="List">
	<Filters>
		<ReboundUI_DataListNugget:Filter ID="ctlFilter" runat="server">
			<FieldsLeft>
				<ReboundUI_FilterDataItemRow:Numerical id="ctlPONo" runat="server" ResourceTitle="InternalPurchaseOrderNo" FilterField="PONo" />
				<ReboundUI_FilterDataItemRow:TextBox id="ctlPart" runat="server" ResourceTitle="PartNo" FilterField="Part" />
		        <ReboundUI_FilterDataItemRow:CheckBox id="ctlRecentOnly" runat="server" ResourceTitle="RecentOnly" FilterField="RecentOnly" DefaultValue="true" />
		        <ReboundUI_FilterDataItemRow:CheckBox id="ctlIncludeClosed" runat="server" ResourceTitle="IncludeClosed" FilterField="IncludeClosed" />
<%--				<ReboundUI_FilterDataItemRow:TextBox id="ctlCompanyName" runat="server" ResourceTitle="CompanyName" FilterField="CMName" />
				<ReboundUI_FilterDataItemRow:TextBox id="ctlContactName" runat="server" ResourceTitle="ContactName" FilterField="Contact" />--%>
				<ReboundUI_FilterDataItemRow:DropDown id="ctlBuyerName" runat="server" ResourceTitle="BuyerName" DropDownType="Employee" DropDownAssembly="Rebound.GlobalTrader.Site" FilterField="BuyerName" />
				<%--[001]Code Start--%>
				<ReboundUI_FilterDataItemRow:DropDown id="ctlCountry" runat="server" ResourceTitle="Country" DropDownType="Country" DropDownAssembly="Rebound.GlobalTrader.Site" FilterField="Country" />				
				<%--[001]Code End--%>
			</FieldsLeft>
			<FieldsRight>
				<ReboundUI_FilterDataItemRow:DateSelect id="ctlDateOrderedFrom" runat="server" ResourceTitle="DateOrderedFrom" FilterField="DateOrderedFrom" />
				<ReboundUI_FilterDataItemRow:DateSelect id="ctlDateOrderedTo" runat="server" ResourceTitle="DateOrderedTo" FilterField="DateOrderedTo" />
				<ReboundUI_FilterDataItemRow:DateSelect id="ctlDeliveryDateFrom" runat="server" ResourceTitle="DeliveryDateFrom" FilterField="DeliveryDateFrom" />
				<ReboundUI_FilterDataItemRow:DateSelect id="ctlDeliveryDateTo" runat="server" ResourceTitle="DeliveryDateTo" FilterField="DeliveryDateTo" />
				<ReboundUI_FilterDataItemRow:DateSelect id="ctlExpediteDateFrom" runat="server" ResourceTitle="ExpediteDateFrom" FilterField="ExpediteDateFrom" />
				<ReboundUI_FilterDataItemRow:DateSelect id="ctlExpediteDateTo" runat="server" ResourceTitle="ExpediteDateTo" FilterField="ExpediteDateTo" />
                <ReboundUI_FilterDataItemRow:DropDown id="ctlClientName" runat="server" ResourceTitle="ClientName" DropDownType="Client" DropDownAssembly="Rebound.GlobalTrader.Site" FilterField="Client"  />				
			</FieldsRight>
		</ReboundUI_DataListNugget:Filter>
	</Filters>
</ReboundUI_Nugget:DesignBase>
