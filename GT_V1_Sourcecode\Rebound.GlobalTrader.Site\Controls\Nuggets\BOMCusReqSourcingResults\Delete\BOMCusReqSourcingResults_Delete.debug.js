///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.BOMCusReqSourcingResults_Delete = function(element) {
    Rebound.GlobalTrader.Site.Controls.Forms.BOMCusReqSourcingResults_Delete.initializeBase(this, [element]);
    this._intSourcingResultID = -1;
    this._blnIsPOHub = false;
};

Rebound.GlobalTrader.Site.Controls.Forms.BOMCusReqSourcingResults_Delete.prototype = {

    initialize: function() {
        Rebound.GlobalTrader.Site.Controls.Forms.BOMCusReqSourcingResults_Delete.callBaseMethod(this, "initialize");
        this.addShown(Function.createDelegate(this, this.formShown));
    },

    dispose: function() {
        if (this.isDisposed) return;
        this._intSourcingResultID=null;
        this._ctlDelete = null;
        Rebound.GlobalTrader.Site.Controls.Forms.BOMCusReqSourcingResults_Delete.callBaseMethod(this, "dispose");
    },

    formShown: function() {
        if (this._blnFirstTimeShown) {
            this._ctlDelete = this.getFieldComponent("ctlDelete");
            this._ctlDelete.addClickYesEvent(Function.createDelegate(this, this.yesClicked));
            this._ctlDelete.addClickNoEvent(Function.createDelegate(this, this.noClicked));
        }
    },

    yesClicked: function() {
        this.showSaving(true);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/BOMCusReqSourcingResults");
        obj.set_DataObject("BOMCusReqSourcingResults");
        obj.set_DataAction("DeleteItem");
        obj.addParameter("id", this._intSourcingResultID);
        obj.addParameter("IsPOHub", this._blnIsPOHub);
        //
        obj.addParameter("ListOfIds", this._aryCurrentValues);
        
        obj.addDataOK(Function.createDelegate(this, this.saveComplete));
        obj.addError(Function.createDelegate(this, this.saveError));
        obj.addTimeout(Function.createDelegate(this, this.saveError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },


    noClicked: function() {
        this.showSaving(false);
        this.onNotConfirmed();
    },

    saveError: function(args) {
        this.showSaving(false);
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    saveComplete: function(args) {
        this.showSaving(false);
        if (args._result.Result) {
            this.onSaveComplete();
        } else {
            this._strErrorMessage = args._errorMessage;
            this.onSaveError();
        }
    }

};

Rebound.GlobalTrader.Site.Controls.Forms.BOMCusReqSourcingResults_Delete.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.BOMCusReqSourcingResults_Delete", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
