///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference name="~/Controls/IconButton/IconButton.js" />
///<reference name="~/Controls/ReboundTextBox/ReboundTextBox.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
/*

*/
Type.registerNamespace("Rebound.GlobalTrader.Site.Pages");

Rebound.GlobalTrader.Site.Pages.GILineImageDocument = function(el) { 
    Rebound.GlobalTrader.Site.Pages.GILineImageDocument.initializeBase(this, [el]);
    this._intIHSPartID = 0;
};

Rebound.GlobalTrader.Site.Pages.GILineImageDocument.prototype = {

    get_ctlPageTitle: function() { return this._ctlPageTitle; }, set_ctlPageTitle: function(v) { if (this._ctlPageTitle !== v) this._ctlPageTitle = v; },
    get_ctlMainInfo: function() { return this._ctlMainInfo; }, set_ctlMainInfo: function(v) { if (this._ctlMainInfo !== v) this._ctlMainInfo = v; },
    get_ctlGILineImageDragDrop: function() { return this._ctlGILineImageDragDrop; }, set_ctlGILineImageDragDrop: function(v) { if (this._ctlGILineImageDragDrop !== v) this._ctlGILineImageDragDrop = v; },
    initialize: function() {
        Rebound.GlobalTrader.Site.Pages.GILineImageDocument.callBaseMethod(this, "initialize");
    },

    goInit: function() {
        if (this._ctlMainInfo) this._ctlMainInfo.addSaveEditComplete(Function.createDelegate(this, this.ctlMainInfo_EditComplete));
        if (this._ctlGILineImageDragDrop) this._ctlGILineImageDragDrop.getData();
        Rebound.GlobalTrader.Site.Pages.GILineImageDocument.callBaseMethod(this, "goInit");
    },

    dispose: function() {
        if (this.isDisposed) return;
        if (this._ctlPageTitle) this._ctlPageTitle.dispose();
        if (this._ctlMainInfo) this._ctlMainInfo.dispose();
        if (this._ctlGILineImageDragDrop) this._ctlGILineImageDragDrop.dispose();
        this._intIHSPartID = null;
        this._ctlPageTitle = null;
        this._ctlMainInfo = null;
        this._ctlManufacturerPDF = null;
        Rebound.GlobalTrader.Site.Pages.GILineImageDocument.callBaseMethod(this, "dispose");
    },

    ctlMainInfo_EditComplete: function() {
        //this._ctlPageTitle.updateTitle(this._ctlMainInfo._frmEdit.getFieldValue("ctlName"));
        //$R_FN.setInnerHTML(this._lblAbbreviation, this._ctlMainInfo._frmEdit.getFieldValue("ctlAbbreviation"));

    }

};

Rebound.GlobalTrader.Site.Pages.GILineImageDocument.registerClass("Rebound.GlobalTrader.Site.Pages.GILineImageDocument", Rebound.GlobalTrader.Site.Pages.Content, Sys.IDisposable);
