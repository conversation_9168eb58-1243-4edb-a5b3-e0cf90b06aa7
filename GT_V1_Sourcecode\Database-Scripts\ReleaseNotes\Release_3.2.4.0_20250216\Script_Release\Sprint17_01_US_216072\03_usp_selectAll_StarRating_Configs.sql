﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/*   
===========================================================================================  
TASK			UPDATED BY		DATE			ACTION		DESCRIPTION  
[US-216072]		Trung Pham		11-Dec-2024		CREATE		Get list of star rating configurations
[US-216072]		An.TranTan		03-Jan-2025		UPDATE		Get created by
===========================================================================================  
*/ 
CREATE OR ALTER PROCEDURE [dbo].[usp_selectAll_StarRating_Configs]
AS
BEGIN
	SELECT rc.NumOfPO
		, rc.CountedStar
		, rc.CreatedDate
		, CONCAT(l.FirstName, ' ', l.LastName) AS CreatedBy
	FROM tbStarRatingConfig rc WITH(NOLOCK)
	JOIN tbLogin l WITH(NOLOCK) ON l.LoginId = rc.CreatedBy
	ORDER BY rc.CreatedDate DESC
END
GO

