///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 09.09.2010:
// - hide Salesperson filter on "My" tab
//
// RP 06.01.2010:
// - fully dispose everything
//
// RP 23.12.2009:
// - render state on server
//
// RP 16.10.2009:
// - Changes due to changes in base class
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DataListNuggets");

Rebound.GlobalTrader.Site.Controls.DataListNuggets.Credits = function(element) { 
	Rebound.GlobalTrader.Site.Controls.DataListNuggets.Credits.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.DataListNuggets.Credits.prototype = {
    get_blnPOHub: function () { return this._blnPOHub; }, set_blnPOHub: function (v) { if (this._blnPOHub !== v) this._blnPOHub = v; },
    get_blnHubCredit: function () { return this._blnHubCredit; }, set_blnHubCredit: function (v) { if (this._blnHubCredit !== v) this._blnHubCredit = v; },
    get_IsGSA: function () { return this._IsGSA; }, set_IsGSA: function (v) { if (this._IsGSA !== v) this._IsGSA = v; },

    initialize: function() {
        Rebound.GlobalTrader.Site.Controls.DataListNuggets.Credits.callBaseMethod(this, "initialize");
        this.addSetupDataCallEvent(Function.createDelegate(this, this.setupDataCall));
        this.addGetDataOKEvent(Function.createDelegate(this, this.getDataOK));
        this.addInitCompleteEvent(Function.createDelegate(this, this.initAfterBaseIsReady));
        this._strPathToData = "controls/DataListNuggets/Credits";
        this._strDataObject = "Credits";
        this.getData();
    },

    initAfterBaseIsReady: function() {
        this.addPageTabChangedEvent(Function.createDelegate(this, this.pageTabChanged));
        this.updateFilterVisibility();
        this.hubCreditFilterVisibility();
        this.getData();
    },

    dispose: function () {
        if (this.isDisposed) return;
        this._blnHubCredit = null;
        this._blnPOHub = null;
        this._IsGSA = null;
        Rebound.GlobalTrader.Site.Controls.DataListNuggets.Credits.callBaseMethod(this, "dispose");
    },

    pageTabChanged: function() {
        this._table._intCurrentPage = 1;
        this._enmViewLevel = this._intCurrentTab;
        this.updateFilterVisibility();
        this.hubCreditFilterVisibility();
        this.getData();
    },

    setupDataCall: function() {
        this._objData.addParameter("ViewLevel", this._enmViewLevel);
        this._objData.addParameter("blnHubCredit", this._blnHubCredit);
    },

    getDataOK: function() {
        for (var i = 0, l = this._objResult.Results.length; i < l; i++) {
            var row = this._objResult.Results[i];
            var aryData = [
				$RGT_nubButton_CreditNote(row.ID, row.No)
				, $R_FN.writeDoubleCellValue($R_FN.writePartNo(row.Part, row.ROHS), $RGT_nubButton_Manufacturer(row.MfrNo, row.Mfr))
                , $R_FN.writeDoubleCellValue(row.blnMakeYellow == true ? '<span style="background-color:yellow;">' + $RGT_nubButton_Company(row.CMNo, row.CM) + '</span>':$RGT_nubButton_Company(row.CMNo, row.CM), $RGT_nubButton_Contact(row.ContactNo, row.Contact))
				, $R_FN.setCleanTextValue(row.Date)
				, $R_FN.writeDoubleCellValue($RGT_nubButton_Invoice(row.InvNo, row.Invoice), $RGT_nubButton_ClientInvoice(row.ClientInvoiceNo, row.ClientInvoiceNumber))
                , $R_FN.setCleanTextValue(row.CustPO)
                ,  $R_FN.setCleanTextValue(row.Total)

			];
            this._table.addRow(aryData, row.ID, false);
            aryData = null; row = null;
        }
    },

    updateFilterVisibility: function() {
        this.getFilterField("ctlSalesman").show(this._enmViewLevel != 0);
         this.getFilterField("ctlPohubOnly").show(this._blnPOHub);
         this.getFilterField("ctlPohubOnly").enableField(this._blnPOHub);
        this.getFilterField("ctlClientInvNo").show(false);
        this.getFilterField("ctlClientInvNo").enableField(false);
        this.getFilterField("ctlClientName").show(this._IsGSA);
    },
    hubCreditFilterVisibility: function () {
        this.getFilterField("ctlSalesman").show(this._enmViewLevel != 0 && !this._blnHubCredit);
        this.getFilterField("ctlCreditNotes").show(!this._blnHubCredit);
        this.getFilterField("ctlCompanyName").show(!this._blnHubCredit);
        this.getFilterField("ctlContactName").show(!this._blnHubCredit);
        this.getFilterField("ctlCRMANo").show(!this._blnHubCredit);
        this.getFilterField("ctlClientInvNo").show(true);
      
    }

};
Rebound.GlobalTrader.Site.Controls.DataListNuggets.Credits.registerClass("Rebound.GlobalTrader.Site.Controls.DataListNuggets.Credits", Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base, Sys.IDisposable);
