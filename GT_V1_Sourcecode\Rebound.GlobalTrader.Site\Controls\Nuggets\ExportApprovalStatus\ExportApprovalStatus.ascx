﻿<%-- 
Marker  ChangedBy        Date           Remarks
[001]   <PERSON><PERSON><PERSON><PERSON>   19-Aug-2021    Add new nugget for supplier approval.
--%>
<%@ Control Language="C#" CodeBehind="ExportApprovalStatus.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Nuggets.ExportApprovalStatus" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" TitleText="Approvals" BoxType="Standard">

    <Links>
        <ReboundUI:MultiSelectionCount id="ctlMultiSelectionCount" runat="server" />
        <ReboundUI:IconButton ID="ibtnEditApproval" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="EditExportApproval" IconCSSType="Edit" IsInitiallyEnabled="false" />
        <ReboundUI:IconButton ID="ibtnApproval" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="Approval" IconCSSType="Checked" IsInitiallyEnabled="false" />
        <ReboundUI:IconButton ID="ibtnRequestApproval" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="SendPowerSOR" IconCSSType="Post" IsInitiallyEnabled="false" />
        &nbsp&nbsp&nbsp<div id="dvRequestApprovalDisabledReason" style="padding-right: 13px; margin-left: -10px; " class="tooltip" title=""></div>
        <ReboundUI:IconButton ID="ibtnEditAllApproval" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="EditAllExportApproval" IconCSSType="Edit" IsInitiallyEnabled="false" />
        
        <%--[001] end --%>
    </Links>

    <Content>
        <div style="margin-bottom: 8px;" id="dvERAIMessage">
            <span class="stockQuarantinedInner" id="spnSupplierERIAMsg"><%=Functions.GetGlobalResource("Messages", "SupplierERAIMessage")%></span>
            <span class="stockQuarantinedInner" id="spnPartERIAMsg"><%=Functions.GetGlobalResource("Messages", "PartERAIMessage")%></span>
        </div>
        <div id="AllocationErrorMsg" class="nuggetMessages">
            <div class="nuggetMessage nuggetMessageError" id="dvtxt"></div></div>
        <ReboundUI:TabStrip ID="ctlTabs" runat="server">
                    
            <TabsTemplate>
                <ReboundUI:Tab ID="ctlTabAll" runat="server" RelatedContentPanelID="pnlTabAll" IsSelected="true" TitleText="All" />
                <ReboundUI:Tab ID="ctlTabApproved" runat="server" TitleText="Approved" RelatedContentPanelID="pnlTabApproved" />
                <ReboundUI:Tab ID="ctlTabAwaiting" TitleText="Awaiting" runat="server" RelatedContentPanelID="pnlTabAwaiting" />
            </TabsTemplate>

            <TabsContent>
                <asp:Panel ID="pnlTabAll" runat="server">
                    <ReboundUI:FlexiDataTable ID="tblAll" runat="server" AllowSelection="true" PanelHeight="160" AllowMultipleSelection="true" />
                </asp:Panel>
                <asp:Panel ID="pnlTabApproved" runat="server">
                    <ReboundUI:FlexiDataTable ID="tblApproved" runat="server" AllowSelection="true" PanelHeight="160" />
                </asp:Panel>
                <asp:Panel ID="pnlTabAwaiting" runat="server">
                    <ReboundUI:FlexiDataTable ID="tblAwaiting" runat="server" AllowSelection="true" PanelHeight="160" />
                </asp:Panel>
            </TabsContent>

        </ReboundUI:TabStrip>

        <asp:Panel ID="pnlLoadingLineDetail" runat="server" CssClass="subInfoLoading invisible"><%=Functions.GetGlobalResource("Misc", "Loading")%></asp:Panel>
        <asp:Panel ID="pnlLineDetailError" runat="server" CssClass="error invisible" />
        <asp:Panel ID="pnlLineDetail" runat="server" CssClass="invisible">
            <h4 class="extraTopMargin">
                <asp:HyperLink ID="hypPrev" runat="server" NavigateUrl="javascript:void(0);" CssClass="linePagingNextPrev linePagingPrev">&laquo;</asp:HyperLink><asp:Label ID="lblLineNumber" runat="server" /><asp:HyperLink ID="hypNext" runat="server" NavigateUrl="javascript:void(0);" CssClass="linePagingNextPrev linePagingNext">&raquo;</asp:HyperLink><asp:Label ID="lblLineInactive" runat="server" CssClass="linePagingInactive" /></h4>
            <table class="twoCols">
                <tr>
                    <td class="col1">
                        <table cellpadding="0" cellspacing="0" border="0" class="dataItems">
                            <ReboundUI:DataItemRow ID="ctlPartNo" runat="server" ResourceTitle="PartNo" />
                            <ReboundUI:DataItemRow ID="hidPartNo" runat="server" FieldType="Hidden" />
                            <ReboundUI:DataItemRow ID="ctlROHS" runat="server" ResourceTitle="ROHS" />
                            <ReboundUI:DataItemRow ID="hidROHS" runat="server" FieldType="Hidden" />
                            <ReboundUI:DataItemRow ID="ctlSupplierPart" runat="server" ResourceTitle="SupplierPart" />
                            <ReboundUI:DataItemRow ID="ctlManufacturer" runat="server" ResourceTitle="Manufacturer" />
                            <ReboundUI:DataItemRow ID="hidManufacturerNo" runat="server" FieldType="Hidden" />
                            <ReboundUI:DataItemRow ID="hidManufacturer" runat="server" FieldType="Hidden" />
                            <ReboundUI:DataItemRow ID="ctlDateCode" runat="server" ResourceTitle="DateCode" />
                            <%--<ReboundUI:DataItemRow id="ctlProduct" runat="server" ResourceTitle="Product" />--%>
                            <ReboundUI:DataItemRow ID="ctlProduct" runat="server" FieldType="Hidden" />
                            <ReboundUI:DataItemRow ID="ctlProductDis" runat="server" ResourceTitle="Product" />
                            <ReboundUI:DataItemRow ID="ctlPrdDutyCodeRate" runat="server" ResourceTitle="DutyCodeRate" />
                            <ReboundUI:DataItemRow ID="hidProductNo" runat="server" FieldType="Hidden" />
                            <%--	<ReboundUI:DataItemRow id="ctlProductDutyCode" runat="server" ResourceTitle="DutyCode" />--%>
                            <ReboundUI:DataItemRow ID="ctlPackage" runat="server" ResourceTitle="Package" />
                            <ReboundUI:DataItemRow ID="hidPackageNo" runat="server" FieldType="Hidden" />
                            <ReboundUI:DataItemRow ID="ctlDeliveryDate" runat="server" ResourceTitle="DeliveryDate" />
                            <ReboundUI:DataItemRow ID="ctlPromiseDate" runat="server" ResourceTitle="PromiseDate" />

                           <%-- <ReboundUI:DataItemRow ID="ctlPurchaseQuote" runat="server" ResourceTitle="PurchaseQuote" />
                            <ReboundUI:DataItemRow ID="ctlPurchaseQuoteDate" runat="server" ResourceTitle="PurchaseQuoteDate" />--%>
                            <ReboundUI:DataItemRow ID="ctlSupplierWarranty" runat="server" ResourceTitle="SupplierWarranty" />
                            <ReboundUI:DataItemRow ID="ctlEPR" runat="server" ResourceTitle="EPR" />

                            <%--IHS code Start--%>
                           <ReboundUI:DataItemRow id="ctlCountryOfOrigin" runat="server" ResourceTitle="CountryOfOrigin" />
                            <ReboundUI:DataItemRow id="ctlLifeCycleStage" runat="server" ResourceTitle="LifeCycleStage" />
                            <ReboundUI:DataItemRow id="ctlIHSProduct" runat="server" ResourceTitle="IHSProductName" />
                            <ReboundUI:DataItemRow id="ctlHTSCode" runat="server" ResourceTitle="HTSCode" />
                            <ReboundUI:DataItemRow id="ctlECCNCode" runat="server" ResourceTitle="ECCNCode" />
                            <ReboundUI:DataItemRow id="ctlPackagingSize" runat="server" ResourceTitle="PackagingSize" />
                            <ReboundUI:DataItemRow id="ctlDescriptions" runat="server" ResourceTitle="Descriptions" />
                            <ReboundUI:DataItemRow id="ctlAveragePrice" runat="server" ResourceTitle="AveragePrice" />
                            <%--IHS code Start--%>
                        </table>
                    </td>
                    <td class="col2">
                        <table cellpadding="0" cellspacing="0" border="0" class="dataItems">
                            <ReboundUI:DataItemRow ID="ctlRepeatOrdr" runat="server" FieldType="CheckBox" ResourceTitle="RepeatOrder" />
                            <ReboundUI:DataItemRow ID="ctlQuantityOrdered" runat="server" ResourceTitle="QuantityOrdered" />
                            <ReboundUI:DataItemRow ID="ctlQuantityAllocated" runat="server" ResourceTitle="QuantityAllocated" />
                            <ReboundUI:DataItemRow ID="ctlQuantityReceived" runat="server" ResourceTitle="QuantityReceived" />
                            <ReboundUI:DataItemRow ID="ctlQuantityOutstanding" runat="server" ResourceTitle="QuantityOutstanding" />
                            <ReboundUI:DataItemRow ID="hidIsClosed" runat="server" FieldType="Hidden" />
                            <ReboundUI:DataItemRow ID="hidCurrencyCode" runat="server" FieldType="Hidden" />
                            <ReboundUI:DataItemRow ID="ctlPrice" runat="server" ResourceTitle="Price" />
                            <ReboundUI:DataItemRow ID="hidPrice" runat="server" FieldType="Hidden" />
                            <ReboundUI:DataItemRow ID="ctlTaxable" runat="server" FieldType="CheckBox" ResourceTitle="Taxable" />
                            <ReboundUI:DataItemRow ID="ctlShipInCost" runat="server" ResourceTitle="ShipInCost" />
                            <ReboundUI:DataItemRow ID="hidShipInCost" runat="server" FieldType="Hidden" />
                            <ReboundUI:DataItemRow ID="ctlReceivingNotes" runat="server" ResourceTitle="POReceivingNotes" />
                            <ReboundUI:DataItemRow ID="ctlLineNotes" runat="server" ResourceTitle="POLineNotes" />
                            <ReboundUI:DataItemRow ID="ctlIPOBom" runat="server" ResourceTitle="IPOBom" />
                            <ReboundUI:DataItemRow ID="hidIPOClientNo" runat="server" FieldType="Hidden" />
                            <ReboundUI:DataItemRow ID="ctlReqSeriaNo" runat="server" FieldType="CheckBox" ResourceTitle="ReqSerailNo" />
                            <ReboundUI:DataItemRow ID="ctlMSL" runat="server" ResourceTitle="MSL" />
                            <ReboundUI:DataItemRow ID="hidPrintHaza" runat="server" FieldType="Hidden" />
                            <ReboundUI:DataItemRow ID="hidProductHazar" runat="server" FieldType="Hidden" />
                        </table>
                    </td>
                </tr>
            </table>

            <ReboundUI:FieldSet ID="fldAllocations" runat="server" FieldSetType="POAllocations">
                <Title><%=Functions.GetGlobalResource("Misc", "Allocations")%></Title>
                <Buttons>
                    <ReboundUI:IconButton ID="ibtnDeallocate" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="Deallocate" IsInitiallyEnabled="false" />
                </Buttons>
                <Content>
                    <ReboundUI:FlexiDataTable ID="tblAllocations" runat="server" AllowMultipleSelection="true" />
                </Content>
            </ReboundUI:FieldSet>

            <ReboundUI:FieldSet ID="fldReceived" runat="server" FieldSetType="POReceived">
                <Title><%=Functions.GetGlobalResource("Misc", "Received")%></Title>
                <Content>
                    <ReboundUI:FlexiDataTable ID="tblReceived" runat="server" />
                </Content>
            </ReboundUI:FieldSet>

        </asp:Panel>

    </Content>

    <Forms>
        <reboundform:ExportApprovalStatus_RequestApproval id="frmSendApproval" runat="server" />
        <reboundform:ExportApprovalStatus_Approvals id="frmApprovals" runat="server" />
        <reboundform:ExportApprovalStatus_edit id="frmEdit" runat="server" />
        <reboundform:ExportApprovalStatus_EditAll id="frmEditAll" runat="server" />

        <reboundform:polines_add id="frmAdd" runat="server" />
        <reboundform:polines_delete id="frmDelete" runat="server" />
        <reboundform:polines_close id="frmClose" runat="server" />
        <reboundform:polines_deallocate id="frmDeallocate" runat="server" />
        <reboundform:pomaininfo_addexpedite id="ctlExpedite" runat="server" />
        <ReboundForm:POLineRelease ID="ctlPOLineRelease" runat="server" />
        <ReboundForm:POLineUnRelease ID="ctlPOLineUnRelease" runat="server" />

    </Forms>

</ReboundUI_Nugget:DesignBase>
