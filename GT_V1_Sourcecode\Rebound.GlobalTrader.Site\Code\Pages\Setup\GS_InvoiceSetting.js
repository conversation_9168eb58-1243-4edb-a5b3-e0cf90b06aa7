Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Setup");Rebound.GlobalTrader.Site.Pages.Setup.GS_InvoiceSetting=function(n){Rebound.GlobalTrader.Site.Pages.Setup.GS_InvoiceSetting.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Pages.Setup.GS_InvoiceSetting.prototype={get_ctlInvoiceSetting:function(){return this._ctlInvoiceSetting},set_ctlInvoiceSetting:function(n){this._ctlInvoiceSetting!==n&&(this._ctlInvoiceSetting=n)},get_ctlEightDSubCategory:function(){return this._ctlEightDSubCategory},set_ctlEightDSubCategory:function(n){this._ctlEightDSubCategory!==n&&(this._ctlEightDSubCategory=n)},initialize:function(){Rebound.GlobalTrader.Site.Pages.Setup.GS_InvoiceSetting.callBaseMethod(this,"initialize")},goInit:function(){Rebound.GlobalTrader.Site.Pages.Setup.GS_InvoiceSetting.callBaseMethod(this,"goInit")},dispose:function(){this.isDisposed||(this._ctlInvoiceSetting&&this._ctlInvoiceSetting.dispose(),this._ctlEightDSubCategory&&this._ctlEightDSubCategory.dispose(),this._ctlInvoiceSetting=null,this._ctlEightDSubCategory=null,Rebound.GlobalTrader.Site.Pages.Setup.GS_InvoiceSetting.callBaseMethod(this,"dispose"))},ctlInvoiceSetting_SelectCategory:function(){this._ctlEightDSubCategory._intcategoryID=this._ctlInvoiceSetting._intCategoryID;this._ctlEightDSubCategory._tbl.resizeColumns();this._ctlEightDSubCategory.show(!0);this._ctlEightDSubCategory.refresh()}};Rebound.GlobalTrader.Site.Pages.Setup.GS_InvoiceSetting.registerClass("Rebound.GlobalTrader.Site.Pages.Setup.GS_InvoiceSetting",Rebound.GlobalTrader.Site.Pages.Content,Sys.IDisposable);