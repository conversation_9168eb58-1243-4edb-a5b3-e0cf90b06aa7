﻿
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

    
/*=======================================================================================================================================================      
TASK            UPDATED BY       DATE             ACTION        DESCRIPTION      
[US-209544]     CuongDox         17-Sep-2024      CREATED       check exist file
[US-223401]     Phuc Hoang		 20-Feb-2025      CREATE		IPO - Bulk Offer Import tool - Apply fuzzy matching and rationalize data before importing
==========================================================================================================================================================      
*/    
    
CREATE OR ALTER  PROC [dbo].[usp_check_exist_HUBOfferImportLargeFile] (    
 @original_file_name VARCHAR(255),  
 @RecordCount INT out  
)    
AS    
    
BEGIN    
  SELECT @RecordCount = COUNT(*) 
  FROM BorisGlobalTraderImports.dbo.tbHUBOfferImportLargeFile  
  WHERE OriginalFileName = @original_file_name AND [status] != 'Deleted';     
END    
GO


