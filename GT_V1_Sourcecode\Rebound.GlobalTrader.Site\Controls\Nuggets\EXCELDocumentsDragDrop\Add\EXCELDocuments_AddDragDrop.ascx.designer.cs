﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:2.0.50727.5485
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Rebound.GlobalTrader.Site.Controls.Forms {
    
    
    public partial class EXCELDocuments_AddDragDrop {
        
        /// <summary>
        /// ctlDB control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::Rebound.GlobalTrader.Site.Controls.Forms.DesignBase ctlDB;
        
        /// <summary>
        /// ibtnSave control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::Rebound.GlobalTrader.Site.Controls.IconButton ibtnSave;
        
        /// <summary>
        /// ibtnCancel control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::Rebound.GlobalTrader.Site.Controls.IconButton ibtnCancel;
        
        /// <summary>
        /// frm control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::Rebound.GlobalTrader.Site.Controls.Tables.Form frm;
        
        /// <summary>
        /// ctlFile control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::Rebound.GlobalTrader.Site.Controls.Forms.FormField ctlFile;
        
        /// <summary>
        /// ctlCaption control.
        /// </summary>
        /// <remarks>
        /// Auto-generated field.
        /// To modify move field declaration from designer file to code-behind file.
        /// </remarks>
        protected global::Rebound.GlobalTrader.Site.Controls.Forms.FormField ctlCaption;
    }
}
