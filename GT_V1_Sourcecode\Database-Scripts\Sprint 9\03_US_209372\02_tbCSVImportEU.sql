﻿/*===========================================================================================  
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[BUG-209372]    cuongdx			21-AUG-2024			Create		compare company from EU Sanctioned list
===========================================================================================  
*/  
DROP TABLE IF EXISTS tbCSVImportEU;

CREATE TABLE tbCSVImportEU (
    fileGenerationDate NVARCHAR(MAX),
	EntityLogicalId NVARCHAR(MAX),
	EntityEUReferenceNumber NVARCHAR(MAX),
	EntityUnitedNationId NVARCHAR(MAX),
	EntityDesignationDate NVARCHAR(MAX),
	EntityDesignationDetails NVARCHAR(MAX),
	EntityRemark NVARCHAR(MAX),
	EntitySubjectType NVARCHAR(MAX),
	EntitySubjectTypeClassificationCode NVARCHAR(MAX),
	EntityRegulationType NVARCHAR(MAX),
	EntityRegulationOrganisationType NVARCHAR(MAX),
	EntityRegulationPublicationDate NVARCHAR(MAX),
	EntityRegulationEntryIntoForceDate NVARCHAR(MAX),
	EntityRegulationNumberTitle NVARCHAR(MAX),
	EntityRegulationProgramme NVARCHAR(MAX),
	EntityRegulationPublicationUrl NVARCHAR(MAX),
	NameAliasLastName NVARCHAR(MAX),
	NameAliasFirstName NVARCHAR(MAX),
	NameAliasMiddleName NVARCHAR(MAX),
	NameAliasWholeName NVARCHAR(MAX),
	NameAliasNameLanguage NVARCHAR(MAX),
	NameAliasGender NVARCHAR(MAX),
	NameAliasTitle NVARCHAR(MAX),
	NameAliasFunction NVARCHAR(MAX),
	NameAliasLogicalId NVARCHAR(MAX),
	NameAliasRegulationLanguage NVARCHAR(MAX),
	NameAliasRemark NVARCHAR(MAX),
	NameAliasRegulationType NVARCHAR(MAX),
	NameAliasRegulationOrganisationType NVARCHAR(MAX),
	NameAliasRegulationPublicationDate NVARCHAR(MAX),
	NameAliasRegulationEntryIntoForceDate NVARCHAR(MAX),
	NameAliasRegulationNumberTitle NVARCHAR(MAX),
	NameAliasRegulationProgramme NVARCHAR(MAX),
	NameAliasRegulationPublicationUrl NVARCHAR(MAX),
	AddressCity NVARCHAR(MAX),
	AddressStreet NVARCHAR(MAX),
	AddressPoBox NVARCHAR(MAX),
	AddressZipCode NVARCHAR(MAX),
	AddressRegion NVARCHAR(MAX),
	AddressPlace NVARCHAR(MAX),
	AddressAsAtListingTime NVARCHAR(MAX),
	AddressContactInfo NVARCHAR(MAX),
	AddressCountryIso2Code NVARCHAR(MAX),
	AddressCountryDescription NVARCHAR(MAX),
	AddressLogicalId NVARCHAR(MAX),
	AddressRegulationLanguage NVARCHAR(MAX),
	AddressRemark NVARCHAR(MAX),
	AddressRegulationType NVARCHAR(MAX),
	AddressRegulationOrganisationType NVARCHAR(MAX),
	AddressRegulationPublicationDate NVARCHAR(MAX),
	AddressRegulationEntryIntoForceDate NVARCHAR(MAX),
	AddressRegulationNumberTitle NVARCHAR(MAX),
	AddressRegulationProgramme NVARCHAR(MAX),
	AddressRegulationPublicationUrl NVARCHAR(MAX),
	BirthDateBirthDate NVARCHAR(MAX),
	BirthDateDay NVARCHAR(MAX),
	BirthDateMonth NVARCHAR(MAX),
	BirthDateYear NVARCHAR(MAX),
	BirthDateYearRangeFrom NVARCHAR(MAX),
	BirthDateYearRangeTo NVARCHAR(MAX),
	BirthDateCirca NVARCHAR(MAX),
	BirthDateCalendarType NVARCHAR(MAX),
	BirthDateZipCode NVARCHAR(MAX),
	BirthDateRegion NVARCHAR(MAX),
	BirthDatePlace NVARCHAR(MAX),
	BirthDateCity NVARCHAR(MAX),
	BirthDateCountryIso2Code NVARCHAR(MAX),
	BirthDateCountryDescription NVARCHAR(MAX),
	BirthDateLogicalId NVARCHAR(MAX),
	BirthDateRegulationLanguage NVARCHAR(MAX),
	BirthDateRemark NVARCHAR(MAX),
	BirthDateRegulationType NVARCHAR(MAX),
	BirthDateRegulationOrganisationType NVARCHAR(MAX),
	BirthDateRegulationPublicationDate NVARCHAR(MAX),
	BirthDateRegulationEntryIntoForceDate NVARCHAR(MAX),
	BirthDateRegulationNumberTitle NVARCHAR(MAX),
	BirthDateRegulationProgramme NVARCHAR(MAX),
	BirthDateRegulationPublicationUrl NVARCHAR(MAX),
	IdentificationNumber NVARCHAR(MAX),
	IdentificationDiplomatic NVARCHAR(MAX),
	IdentificationKnownExpired NVARCHAR(MAX),
	IdentificationKnownFalse NVARCHAR(MAX),
	IdentificationReportedLost NVARCHAR(MAX),
	IdentificationRevokedByIssuer NVARCHAR(MAX),
	IdentificationIssuedBy NVARCHAR(MAX),
	IdentificationIssuedDate NVARCHAR(MAX),
	IdentificationValidFrom NVARCHAR(MAX),
	IdentificationValidTo NVARCHAR(MAX),
	IdentificationLatinNumber NVARCHAR(MAX),
	IdentificationNameOnDocument NVARCHAR(MAX),
	IdentificationTypeCode NVARCHAR(MAX),
	IdentificationTypeDescription NVARCHAR(MAX),
	IdentificationRegion NVARCHAR(MAX),
	IdentificationCountryIso2Code NVARCHAR(MAX),
	IdentificationCountryDescription NVARCHAR(MAX),
	IdentificationLogicalId NVARCHAR(MAX),
	IdentificationRegulationLanguage NVARCHAR(MAX),
	IdentificationRemark NVARCHAR(MAX),
	IdentificationRegulationType NVARCHAR(MAX),
	IdentificationRegulationOrganisationType NVARCHAR(MAX),
	IdentificationRegulationPublicationDate NVARCHAR(MAX),
	IdentificationRegulationEntryIntoForceDate NVARCHAR(MAX),
	IdentificationRegulationNumberTitle NVARCHAR(MAX),
	IdentificationRegulationProgramme NVARCHAR(MAX),
	IdentificationRegulationPublicationUrl NVARCHAR(MAX),
	CitizenshipRegion NVARCHAR(MAX),
	CitizenshipCountryIso2Code NVARCHAR(MAX),
	CitizenshipCountryDescription NVARCHAR(MAX),
	CitizenshipLogicalId NVARCHAR(MAX),
	CitizenshipRegulationLanguage NVARCHAR(MAX),
	CitizenshipRemark NVARCHAR(MAX),
	CitizenshipRegulationType NVARCHAR(MAX),
	CitizenshipRegulationOrganisationType NVARCHAR(MAX),
	CitizenshipRegulationPublicationDate NVARCHAR(MAX),
	CitizenshipRegulationEntryIntoForceDate NVARCHAR(MAX),
	CitizenshipRegulationNumberTitle NVARCHAR(MAX),
	CitizenshipRegulationProgramme NVARCHAR(MAX),
	CitizenshipRegulationPublicationUrl NVARCHAR(MAX),
	CreatedOn	datetime	
);

ALTER TABLE [dbo].tbCSVImportEU ADD  DEFAULT (getdate()) FOR [CreatedOn]
GO

--118 colm