using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using System.Text;
using Rebound.GlobalTrader.Site;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets {
	public partial class ControlStrip : Base {

		#region Controls

		#endregion

		#region Properties

		private ITemplate _tmpContent = null;
		/// <summary>
		/// Content container
		/// </summary>
		[PersistenceMode(PersistenceMode.InnerProperty)]
		[TemplateContainer(typeof(Container))]
		public ITemplate Content {
			get { return _tmpContent; }
			set { _tmpContent = value; }
		}

		#endregion

		#region Overrides

		/// <summary>
		/// Oninit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			HasInitialData = true;
			base.OnInit(e);
			//pass content through to Design Base control
			if (_tmpContent != null) Functions.AddControlsFromTemplate(ctlDesignBase.pnlContent, _tmpContent);
			AddScriptReference("Controls.Nuggets.ControlStrip.ControlStrip");
		}

		protected override void OnLoad(EventArgs e) {
			SetupScriptDescriptors();
			TitleText = "";
			base.OnLoad(e);
		}

		#endregion


		/// <summary>
		/// Sets up AJAX script descriptors
		/// </summary>
		private void SetupScriptDescriptors() {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Nuggets.ControlStrip", ctlDesignBase.ClientID);
		}

	}
}
