//Marker     Changed by      Date               Remarks
//[001]      Vinay           13/06/2013         CR:- Supplier Invoice
using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets.Data {
	[WebService(Namespace = "http://tempuri.org/")]
	[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class ClientInvoiceAdd : Rebound.GlobalTrader.Site.Data.Base
    {
		public override void ProcessRequest(HttpContext context) {
			if (base.init(context)) {
				switch (Action) {
					case "AddNew": AddNew(); break;
                    case "SaveLine": SaveLine(); break;
                    case "UpdateHeader": UpdateHeader(); break;
					default: WriteErrorActionNotFound(); break;
				}
			}
		}

     
		/// <summary>
		/// Add new purchaseOrder
		/// </summary>
		public void AddNew() {
			try {
                int intNewClientInvoiceID = ClientInvoice.Insert(
                    
				    SessionManager.ClientID,
                    GetFormValue_Int("ClientID"),
                    GetFormValue_NullableDouble("Amount", null),
                    GetFormValue_NullableDouble("GoodsValue", null),
                    GetFormValue_NullableDouble("Tax", null),
                    GetFormValue_NullableInt("TaxNo", null),
                    GetFormValue_String("TaxCode"),
                    GetFormValue_NullableDouble("DeliveryCharge", null),
                    GetFormValue_NullableDouble("BankFee", null),
                    GetFormValue_NullableDouble("CreditCardFee", null),
                    GetFormValue_String("Notes"),
                    GetFormValue_String("SecondRef"),
                    GetFormValue_String("Narrative"),
                   // GetFormValue_NullableBoolean("CanExported", false),
                   true ,
                    LoginID,
                     GetFormValue_NullableInt("CurrencyNo", 0),
                     GetFormValue_String("CurrencyCode")
				);

                if (intNewClientInvoiceID > 0)
                {
					JsonObject jsn = new JsonObject();
                    jsn.AddVariable("NewID", intNewClientInvoiceID);
					OutputResult(jsn);
					jsn.Dispose();
					jsn = null;
				} else {
					WriteErrorSQLActionFailed("Insert");
				}
			} catch (Exception e) {
				WriteError(e);
			}
		}
        public void SaveLine()
        {
            bool blnOK = true;
            try
            {

                JsonObject jsn = new JsonObject();
                Array aryGoodsInLineIDs = Functions.JavascriptStringToArray(GetFormValue_String("GoodsInLineIDs"));
                for (int i = 0; i < aryGoodsInLineIDs.Length; i++)
                {
                    int intNewClientInvoiceLineID = ClientInvoiceLine.InsertClientInvoiceLine(
                        Convert.ToInt32(aryGoodsInLineIDs.GetValue(i))
                        , ID
                        , LoginID
                       // ,SessionManager.IsPOHub
                    );
                    if (intNewClientInvoiceLineID < 1)
                    {
                        blnOK = false;
                        continue;
                    }
                }
                jsn.AddVariable("Result", blnOK);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

        /// <summary>
        /// Update header
        /// </summary>
        public void UpdateHeader()
        {
            try
            {
                bool blnResult = ClientInvoice.UpdateHeader(
                    ID,
                    GetFormValue_String("SecondRef"),
                    GetFormValue_String("Narrative"),
                    GetFormValue_Boolean("CanBeExported"),
                    LoginID
                );
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Result", blnResult);
                OutputResult(jsn);
                jsn.Dispose();
                jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

	}
}
