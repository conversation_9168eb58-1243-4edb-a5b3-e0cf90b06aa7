using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Collections;
using System.Threading;

namespace Rebound.GlobalTrader.Site.Controls {
	[DefaultProperty("")]
	[ToolboxData("<{0}:LanguageSwitchLink runat=server></{0}:LanguageSwitchLink>")]
	public class LanguageSwitchLink : Panel, INamingContainer {

		#region Locals

		protected ScriptManager _sm;
		protected HyperLink _hyp;
		protected Image _img;

		#endregion

		#region Properties

		private string _strCulture = "";
		public string Culture {
			get { return _strCulture; }
			set { _strCulture = value; }
		}

		private string _strHoverText = "";
		public string HoverText {
			get { return _strHoverText; }
			set { _strHoverText = value; }
		}

		private bool _blnIsCurrent = false;
		public bool IsCurrent {
			get { return _blnIsCurrent; }
			set { _blnIsCurrent = value; }
		}

		private string _strTitle;
		public string Title {
			get { return _strTitle; }
			set { _strTitle = value; }
		}

		#endregion

		#region Constructors

		public LanguageSwitchLink() { }
		public LanguageSwitchLink(Enumerations.GlobalLanguage.List enmLanguage) {
			_strHoverText = Functions.GetGlobalResource("Languages", enmLanguage);
			_strTitle = Functions.GetGlobalResource("LanguagesInNativeLanguage", enmLanguage); 
			_strCulture = Enumerations.GlobalLanguage.GetLanguageCode(enmLanguage);
			if (SessionManager.Culture.Length > 0) _blnIsCurrent = (_strCulture.ToUpper() == SessionManager.Culture.ToUpper());
		}

		#endregion

		#region Overrides

		protected override void CreateChildControls() {
			_hyp = ControlBuilders.CreateHyperLink("", "javascript:void(0)");
			_hyp.CssClass = String.Format("languageItem language_{0}", _strCulture);
			_hyp.ID = "hyp";
			_hyp.ToolTip = _strHoverText;
			_hyp.Controls.Add(ControlBuilders.CreateLiteral(_strTitle));
			Controls.Add(_hyp);
			base.CreateChildControls();
		}

		/// <summary>
		/// OnPreRender
		/// </summary>
		/// <param name="e"></param>
		protected override void OnPreRender(EventArgs e) {
			Attributes.Add("bgt_culture", _strCulture);
			_hyp.Attributes.Add("bgt_culture", _strCulture);
			CssClass = (_blnIsCurrent) ? "langSelected" : "lang";
			base.OnPreRender(e);
		}

		#endregion
	}
}