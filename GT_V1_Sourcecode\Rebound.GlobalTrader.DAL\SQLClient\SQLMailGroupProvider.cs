﻿using System;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using System.Web.Caching;
using System.Data.Common;

namespace Rebound.GlobalTrader.DAL.SqlClient
{
    public class SqlMailGroupProvider : MailGroupProvider
    {
        /// <summary>
        /// Delete MailGroup
        /// Calls [usp_delete_MailGroup]
        /// </summary>
        public override bool Delete(System.Int32? mailGroupId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_delete_MailGroup", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@MailGroupId", SqlDbType.Int).Value = mailGroupId;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to delete MailGroup", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// Create a new row
        /// Calls [usp_insert_MailGroup]
        /// </summary>
        public override Int32 Insert(System.Int32? loginNo, System.Int32? clientNo, System.String name, System.Int32? updatedBy, System.String GroupDiscription)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_insert_MailGroup", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@LoginNo", SqlDbType.Int).Value = loginNo;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = clientNo;
                cmd.Parameters.Add("@Name", SqlDbType.NVarChar).Value = name;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@GroupDiscription", SqlDbType.NVarChar).Value = GroupDiscription;
                cmd.Parameters.Add("@MailGroupId", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (Int32)cmd.Parameters["@MailGroupId"].Value;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert MailGroup", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// Get 
        /// Calls [usp_select_MailGroup]
        /// </summary>
        public override MailGroupDetails Get(System.Int32? mailGroupNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_select_MailGroup", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@MailGroupNo", SqlDbType.Int).Value = mailGroupNo;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd, CommandBehavior.SingleRow);
                if (reader.Read())
                {
                    //return GetMailGroupFromReader(reader);
                    MailGroupDetails obj = new MailGroupDetails();
                    obj.MailGroupId = GetReaderValue_Int32(reader, "MailGroupId", 0);
                    obj.Name = GetReaderValue_String(reader, "Name", "");
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.DLUP = GetReaderValue_NullableDateTime(reader, "DLUP", null);
                    obj.ClientNo = GetReaderValue_NullableInt32(reader, "ClientNo", null);
                    obj.LoginNo = GetReaderValue_NullableInt32(reader, "LoginNo", null);
                    obj.NumberOfMembers = GetReaderValue_NullableInt32(reader, "NumberOfMembers", null);
                    return obj;
                }
                else
                {
                    return null;
                }
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get MailGroup", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// GetListForClient 
        /// Calls [usp_selectAll_MailGroup_for_Client]
        /// </summary>
        public override List<MailGroupDetails> GetListForClient(System.Int32? clientNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_selectAll_MailGroup_for_Client", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = clientNo;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<MailGroupDetails> lst = new List<MailGroupDetails>();
                while (reader.Read())
                {
                    MailGroupDetails obj = new MailGroupDetails();
                    obj.MailGroupId = GetReaderValue_Int32(reader, "MailGroupId", 0);
                    obj.Name = GetReaderValue_String(reader, "Name", "");
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.DLUP = GetReaderValue_NullableDateTime(reader, "DLUP", null);
                    obj.ClientNo = GetReaderValue_NullableInt32(reader, "ClientNo", null);
                    obj.LoginNo = GetReaderValue_NullableInt32(reader, "LoginNo", null);
                    obj.NumberOfMembers = GetReaderValue_NullableInt32(reader, "NumberOfMembers", null);
                    obj.GroupDiscription = GetReaderValue_String(reader, "GroupDiscription", "");
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get MailGroups", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// GetListForLogin 
        /// Calls [usp_selectAll_MailGroup_for_Login]
        /// </summary>
        public override List<MailGroupDetails> GetListForLogin(System.Int32? loginNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_selectAll_MailGroup_for_Login", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@LoginNo", SqlDbType.Int).Value = loginNo;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<MailGroupDetails> lst = new List<MailGroupDetails>();
                while (reader.Read())
                {
                    MailGroupDetails obj = new MailGroupDetails();
                    obj.MailGroupId = GetReaderValue_Int32(reader, "MailGroupId", 0);
                    obj.Name = GetReaderValue_String(reader, "Name", "");
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.DLUP = GetReaderValue_NullableDateTime(reader, "DLUP", null);
                    obj.ClientNo = GetReaderValue_NullableInt32(reader, "ClientNo", null);
                    obj.LoginNo = GetReaderValue_NullableInt32(reader, "LoginNo", null);
                    obj.NumberOfMembers = GetReaderValue_NullableInt32(reader, "NumberOfMembers", null);
                    obj.GroupDiscription = GetReaderValue_String(reader, "GroupDiscription", "");
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get MailGroups", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// Update MailGroup
        /// Calls [usp_update_MailGroup]
        /// </summary>
        public override bool Update(System.Int32? mailGroupId, System.Int32? loginNo, System.Int32? clientNo, System.String name, System.Int32? updatedBy, System.String GroupDiscription)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_MailGroup", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@MailGroupId", SqlDbType.Int).Value = mailGroupId;
                cmd.Parameters.Add("@LoginNo", SqlDbType.Int).Value = loginNo;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = clientNo;
                cmd.Parameters.Add("@Name", SqlDbType.NVarChar).Value = name;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@GroupDiscription", SqlDbType.NVarChar).Value = GroupDiscription;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update MailGroup", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Update MailGroup
        /// Calls [usp_update_MailGroup]
        /// </summary>
        public override Int32? GetQualityMailGroupNo(System.Int32? clientNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            Int32? QualityMailGroupNo = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_get_QualityMailGroupNo", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = clientNo;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd, CommandBehavior.SingleRow);
                while (reader.Read())
                {
                    QualityMailGroupNo = GetReaderValue_NullableInt32(reader, "MailGroupId", null);
                }
                return QualityMailGroupNo;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Quality MailGroups", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// Update MailGroup  [ New Customer Approval Notification ]
        /// Calls [usp_update_MailGroup]
        /// </summary>
        public override Int32? GetNewCustomerApprovalNotificationMailGroupNo(System.Int32? clientNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            Int32? NewCustomerApprovalMailGroupNo = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_get_NewCustomerApprovalMailGroupNo", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = clientNo;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd, CommandBehavior.SingleRow);
                while (reader.Read())
                {
                    NewCustomerApprovalMailGroupNo = GetReaderValue_NullableInt32(reader, "MailGroupId", null);
                }
                return NewCustomerApprovalMailGroupNo;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get New Customer Approval MailGroups", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        /// <summary>
        /// GetListForClient 
        /// Calls [usp_GetListForMailGroupId]
        /// </summary>
        public override List<MailGroupDetails> GetListForMailGroupId(System.Int32? clientNo, System.String Type, System.Int32 GoodsInLineId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_GetListForMailGroupId", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = clientNo;
                cmd.Parameters.Add("@GoodsInLineNo", SqlDbType.Int).Value = GoodsInLineId;
                cmd.Parameters.Add("@Type", SqlDbType.VarChar).Value = Type;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<MailGroupDetails> lst = new List<MailGroupDetails>();
                while (reader.Read())
                {
                    MailGroupDetails obj = new MailGroupDetails();
                    obj.MailGroupId = GetReaderValue_Int32(reader, "MailGroupId", 0);
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get GetListForMailGroupId", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        /// <summary>
        /// GetMailGroupMember
        /// Calls [usp_selectAll_MailGroupMember_for_Login]
        /// </summary>
        /// <param name="loginId"></param>
        /// <returns></returns>
        public override List<MailGroupDetails> GetMailGroupMember(System.Int32? loginId, System.Int32? GoodsInLineId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_selectAll_MailGroupMember_for_Login", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@LoginId", SqlDbType.Int).Value = loginId;
                cmd.Parameters.Add("@GoodsInLineId", SqlDbType.Int).Value = GoodsInLineId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<MailGroupDetails> lst = new List<MailGroupDetails>();
                while (reader.Read())
                {
                    MailGroupDetails obj = new MailGroupDetails();
                    obj.MailGroupId = GetReaderValue_Int32(reader, "MailGroupId", 0);
                    obj.Name = GetReaderValue_String(reader, "Name", "");
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get GetMailGroupMember", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// GetListForClient 
        /// Calls [usp_GetQualityGroupId]
        /// </summary>
        public override List<MailGroupDetails> GetListForQualityGroupId(System.Int32? clientNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_GetQualityGroupId", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = clientNo;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<MailGroupDetails> lst = new List<MailGroupDetails>();
                while (reader.Read())
                {
                    MailGroupDetails obj = new MailGroupDetails();
                    obj.MailGroupId = GetReaderValue_Int32(reader, "MailGroupId", 0);
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get GetListForQltyOrFinanceGroupId", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Update MailGroup  [ New Customer Approval Notification ]
        /// Calls [usp_update_MailGroup]
        /// </summary>
        public override Int32? GetECCNRestrictedUsenotifcationMailGroupNo(System.Int32? clientNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            Int32? ECCNRestrictedUsenotifcationMailGroupNo = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_get_ECCNRestrictedUseNotifyMailGroupNo", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = clientNo;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd, CommandBehavior.SingleRow);
                while (reader.Read())
                {
                    ECCNRestrictedUsenotifcationMailGroupNo = GetReaderValue_NullableInt32(reader, "MailGroupId", null);
                }
                return ECCNRestrictedUsenotifcationMailGroupNo;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get New Customer Approval MailGroups", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Update MailGroup  [ New Customer Approval Notification ]
        /// Calls [usp_update_MailGroup]
        /// </summary>
        public override Int32? GetReverseLogisticsMailGroupNo(System.Int32? clientNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            Int32? ReverseLogisticsMailGroupNo = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_Get_ReverseLogistics_MailGroup", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = clientNo;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd, CommandBehavior.SingleRow);
                while (reader.Read())
                {
                    ReverseLogisticsMailGroupNo = GetReaderValue_NullableInt32(reader, "MailGroupId", null);
                }
                return ReverseLogisticsMailGroupNo;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Reverse Logistics MailGroups", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Update MailGroup  [ GetstrategicoffersMailGroupNo Notification ]
        /// Calls [usp_Get_strategicoffers_MailGroup]
        /// </summary>
        public override Int32? GetstrategicoffersMailGroupNo(System.Int32? clientNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            Int32? ReverseLogisticsMailGroupNo = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_Get_strategicoffers_MailGroup", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = clientNo;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd, CommandBehavior.SingleRow);
                while (reader.Read())
                {
                    ReverseLogisticsMailGroupNo = GetReaderValue_NullableInt32(reader, "MailGroupId", null);
                }
                return ReverseLogisticsMailGroupNo;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get strategicoffers MailGroups", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
    }
}