///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------

Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.UtilityHUBOffer_ImportLarge = function (element) {
    Rebound.GlobalTrader.Site.Controls.Forms.UtilityHUBOffer_ImportLarge.initializeBase(this, [element]);
    this._importedFileId = 0;
};

Rebound.GlobalTrader.Site.Controls.Forms.UtilityHUBOffer_ImportLarge.prototype = {

    // get_intNPRID: function() { return this._intNPRID; }, set_intNPRID: function(v) { if (this._intNPRID !== v) this._intNPRID = v; },
    get_ibtnSend: function () { return this._ibtnSend; }, set_ibtnSend: function (value) { if (this._ibtnSend !== value) this._ibtnSend = value; },
    get_ibtnSend_Footer: function () { return this._ibtnSend_Footer; }, set_ibtnSend_Footer: function (v) { if (this._ibtnSend_Footer !== v) this._ibtnSend_Footer = v; },
    // get_strMessageText: function() { return this._strMessageText; }, set_strMessageText: function(v) { if (this._strMessageText !== v) this._strMessageText = v; },
    // get_strNPRNo: function() { return this._strNPRNo; }, set_strNPRNo: function(v) { if (this._strNPRNo !== v) this._strNPRNo = v; },
    //  get_intGoodsIn: function() { return this._intGoodsIn; }, set_intGoodsIn: function(v) { if (this._intGoodsIn !== v) this._intGoodsIn = v; },
    //  get_strBuyerName: function() { return this._strBuyerName; }, set_strBuyerName: function(v) { if (this._strBuyerName !== v) this._strBuyerName = v; },
    // get_intBuyerId: function() { return this._intBuyerId; }, set_intBuyerId: function(v) { if (this._intBuyerId !== v) this._intBuyerId = v; },
    // get_intGoodsInLineId: function () { return this._intGoodsInLineId; }, set_intGoodsInLineId: function (v) { if (this._intGoodsInLineId !== v) this._intGoodsInLineId = v; },
    // get_intClientNo: function () { return this._intClientNo; }, set_intClientNo: function (v) { if (this._intClientNo !== v) this._intClientNo = v; },
    get_intMasterLoginNo: function () { return this._intMasterLoginNo; }, set_intMasterLoginNo: function (v) { if (this._intMasterLoginNo !== v) this._intMasterLoginNo = v; },


    initialize: function () {
        Rebound.GlobalTrader.Site.Controls.Forms.UtilityHUBOffer_ImportLarge.callBaseMethod(this, "initialize");
        this.addShown(Function.createDelegate(this, this.formShown));
        this.addCancel(Function.createDelegate(this, this.cancelClicked));
    },
    getFormControlID: function (ParentId, controlID) {
        var str = "";
        str = String.format("{0}_{1}", ParentId, controlID);
        return str;
    },
    formShown: function () {

        //this._ctlMail._autLoginOrGroup._intGlobalLoginClientNo = this._intClientNo;
    },
    getContact: function () {
        getCompanyAndOtherMasterData.call(this);

    },
    dispose: function () {
        if (this.isDisposed) return;
        if (this._ibtnSend) $R_IBTN.clearHandlers(this._ibtnSend);
        if (this._ibtnSend_Footer) $R_IBTN.clearHandlers(this._ibtnSend_Footer);
        if (this._ctlMail) this._ctlMail.dispose();
        this._ctlMail = null;
        this._ibtnSend = null;
        this._ibtnSend_Footer = null;
        this._intMasterLoginNo = null;
        this._importedFileId = nul;

        Rebound.GlobalTrader.Site.Controls.Forms.UtilityHUBOffer_ImportLarge.callBaseMethod(this, "dispose");
    },

    getMessageText: function () {
        Rebound.GlobalTrader.Site.WebServices.GetMailMessage_NotifyNPR(this._intNPRID, Function.createDelegate(this, this.getMessageTextComplete));
    },

    getMessageTextComplete: function (strMsg) {
        this._ctlMail.setValue_Body(strMsg);
        this._ctlMail.setValue_Subject(String.format($R_RES.NotifyNPR, this._strNPRNo));
        this._ctlMail.addNewLoginRecipient(this._intBuyerId, this._strBuyerName);
    },
    //[001] code start
    sendMail: function () {
        if (!this.validateForm()) return;
        this.showLoading(true);
        this.enableButton(false);
        Rebound.GlobalTrader.Site.WebServices.NotifyNPRMessage($R_FN.arrayToSingleString(this._ctlMail._aryRecipientLoginIDs), $R_FN.arrayToSingleString(this._ctlMail._aryRecipientGroupIDs), this._ctlMail.getValue_Subject(), this._ctlMail.getValue_Body(), "", this._intNPRID, this._intGoodsInLineId, $R_FN.arrayToSingleString(this._ctlMail._aryRecipientLoginNames, "/"), $R_FN.arrayToSingleString(this._ctlMail._aryRecipientGroupNames, "/"), Function.createDelegate(this, this.sendMailComplete));
    },
    //[001] code end
    validateForm: function () {
        var blnOK = this._ctlMail.validateFields();
        if (!blnOK) this.showError(true);
        return blnOK;
    },

    sendMailComplete: function () {
        this.showLoading(false);
        this.showSavedOK(true);
        location.href = $RGT_gotoURL_GoodsIn(this._intGoodsIn);
        this.enableButton(true);
    },
    cancelClicked: function () {
        // $R_FN.navigateBack();
        window.location.href = 'Default.aspx';
        //this.showLeftMenu();
    },
    enableButton: function (bln) {
        $R_IBTN.enableButton(this._ibtnSend, bln);
        $R_IBTN.enableButton(this._ibtnSend_Footer, bln);
    },
    importExcelData: function (originalFilename, generatedFilename) {
        $('#divLoader').show();

        var obj = new Rebound.GlobalTrader.Site.Data();
        obj._intTimeoutMilliseconds = 200000;
        obj.set_PathToData("controls/Nuggets/UtilityHUBOfferImportLarge");
        obj.set_DataObject("UtilityHUBOfferImportLarge");
        obj.set_DataAction("ImportData");
        obj.addParameter("originalFilename", originalFilename);
        obj.addParameter("generatedFilename", generatedFilename);
        obj.addDataOK(Function.createDelegate(this, this.importExcelDataOK));
        obj.addError(Function.createDelegate(this, this.importExcelDataError));
        obj.addTimeout(Function.createDelegate(this, this.importExcelDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();

        obj = null;
    },
    importExcelDataOK: function (args) {
        // alert("test1");
        //flogId = args._result.FileLogId;
        //bindBomGridData.call(this);
        message = args._result.message
        $('#divLoader').hide();
        //$('input:checkbox[id="chkFileCCH"]').prop('disabled', true);
        //$('input:file').filter(function () {
        //    return this.files.length == 0
        //}).prop('disabled', true);
        //$("#excelipload").prop('disabled', true).css('opacity', 0.5);
        alert(message);
        //after load ok then load data to grid
        fileId = args._result.FileLogId;
        this._importedFileId = fileId;
        PopulateGridForValidation(fileId)
    },
    importExcelDataError: function (args) {
        alert(args._errorMessage.split('<br/>')[0]);
        $('#divLoader').hide();
    }
};

Rebound.GlobalTrader.Site.Controls.Forms.UtilityHUBOffer_ImportLarge.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.UtilityHUBOffer_ImportLarge", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
