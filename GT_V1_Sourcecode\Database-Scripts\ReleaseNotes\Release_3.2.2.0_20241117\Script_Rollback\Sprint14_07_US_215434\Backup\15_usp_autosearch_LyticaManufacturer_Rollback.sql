﻿
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/*     
========================================================================================================================    
TASK          UPDATED BY      DATE          ACTION    DESCRIPTION    
[US-215434]	  Phuc Hoang	  06-Nov-2024	Update	  Lytica Price should apply fuzzy logic for inserting & displaying
========================================================================================================================   
*/  

CREATE OR ALTER PROCEDURE [dbo].[usp_autosearch_LyticaManufacturer]              
(     
	@NameSearch nvarchar(50),
	@PartNo NVARCHAR(256) = NULL,
	@ShowInactive bit   
)
AS         
BEGIN     
	SELECT DISTINCT TOP 10   
		mfr.ManufacturerId        
		, mfr.ManufacturerName        
		, rtrim(ltrim(mfr.ManufacturerCode)) + ' - ' + rtrim(ltrim(mfr.FullName)) AS FullName      
	FROM  dbo.tbManufacturer mfr
	LEFT JOIN tbLyticaAPI lytica ON (
			lytica.Manufacturer = mfr.ManufacturerName 
			OR lytica.Manufacturer LIKE mfr.ManufacturerName + '%' 
			OR mfr.ManufacturerName LIKE lytica.Manufacturer + '%'
			--OR lytica.Manufacturer LIKE [dbo].[ufn_GetFirstWord](mfr.ManufacturerName) + '%'
		)

	WHERE lytica.OriginalPartSearched = @PartNo --AND (mfr.FullName LIKE @NameSearch OR mfr.ManufacturerCode LIKE @NameSearch)       
	ORDER BY ManufacturerName      

END   
GO

