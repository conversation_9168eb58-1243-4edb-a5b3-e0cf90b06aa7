<%@ Control Language="C#" CodeBehind="Bom.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.HomeNuggets.Bom" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server">
    <Content>
        <div class="homepageNugget">
        
              <asp:Panel ID="pnlReady" runat="server">
             <h5><%=Functions.GetGlobalResource("misc", "Released")%></h5>
                <ReboundUI:SimpleDataTable ID="tblReady" runat="server" AllowSelection="false" />
            </asp:Panel>
            
             <asp:Panel ID="pnlPartial" runat="server">
             <h5><%=Functions.GetGlobalResource("misc", "Partial")%></h5>
                <ReboundUI:SimpleDataTable ID="tblPartial" runat="server" AllowSelection="false" />
            </asp:Panel>
            
            <asp:Panel ID="pnlRFQ" runat="server">
             <h5><%=Functions.GetGlobalResource("misc", "RPQ")%></h5>
                <ReboundUI:SimpleDataTable ID="tblRFQ" runat="server" AllowSelection="false" />
            </asp:Panel>
            <asp:Panel ID="pnlNew" runat="server">
             <h5><%=Functions.GetGlobalResource("misc", "New")%></h5>
                <ReboundUI:SimpleDataTable ID="tblNew" runat="server" AllowSelection="false" />
            </asp:Panel>
            
            <asp:Panel ID="pnlClosed" runat="server">
             <h5><%=Functions.GetGlobalResource("misc", "Closed")%></h5>
                <ReboundUI:SimpleDataTable ID="tblClosed" runat="server" AllowSelection="false" />
            </asp:Panel>   
            
             <asp:Panel ID="pnlNoBid" runat="server">
             <h5><%=Functions.GetGlobalResource("misc", "NoBid")%></h5>
                <ReboundUI:SimpleDataTable ID="tblNoBid" runat="server" AllowSelection="false" />
            </asp:Panel>  
             
            <asp:Panel ID="pnlMore" runat="server" CssClass="homeNuggetMoreLink">
                <ReboundUI:PageHyperLink ID="lnkMore" runat="server" PageType="Orders_BOMBrowse"
                    OverrideTextResource="HomeNuggetBom" CssClass="nubButton nubButtonAlignLeft" />
            </asp:Panel>
        </div>
    </Content>
</ReboundUI_Nugget:DesignBase>
