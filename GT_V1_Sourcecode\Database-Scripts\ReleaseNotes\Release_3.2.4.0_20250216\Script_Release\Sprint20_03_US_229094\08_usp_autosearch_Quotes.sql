﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO
/*
--============================================================================================================================
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-229094]     An.TranTan		 21-Jan-2025		Create		Get quotes for auto search in Add Task form, allow Quote with status = Offered, Partially Offered
[US-232041]     An.TranTan		 04-Feb-2025		UPDATE		Allow add task for quotes with status = New
[US-232041]     An.TranTan		 05-Feb-2025		UPDATE		Remove restrict filter quote by status
==============================================================================================================================  
*/
CREATE OR ALTER PROCEDURE [dbo].[usp_autosearch_Quotes]   
    @ClientID int  
  , @QuoteNumberSearch nvarchar(50)  
AS   
SELECT  
	q.QuoteId, q.QuoteNumber 
FROM    dbo.tbQuote q WITH(NOLOCK)
LEFT JOIN dbo.tbQuoteStatus qs WITH(NOLOCK) on qs.QuoteStatusId = q.QuoteStatus
WHERE   q.ClientNo = @ClientID  
AND  ISNULL(q.Closed,0) = 0
AND  QuoteNumber LIKE @QuoteNumberSearch
--AND (qs.Name = 'Offered' OR qs.Name = 'Partially Offered' OR qs.Name = 'New')
ORDER BY QuoteNumber  

GO


