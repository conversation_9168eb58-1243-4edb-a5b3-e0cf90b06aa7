using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.Site.Controls.AutoSearch.Data
{

    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class SalesPersion : Rebound.GlobalTrader.Site.Controls.AutoSearch.Data.Base
    {

        protected override void GetData()
        {
            int? intGlobalLoginClientNo = 0;
            intGlobalLoginClientNo = GetFormValue_NullableInt("GlobalLoginClientNo");
            int intTeamNo = 0;
            int intDivisionNo = 0;
            int intExcludeLoginNo = 0;
            if (GetFormValue_Boolean("LimitToCurrentUsersTeam")) intTeamNo = (int)SessionManager.LoginTeamID;
            if (GetFormValue_Boolean("LimitToCurrentUsersDivision")) intDivisionNo = (int)SessionManager.LoginDivisionID;
            if (GetFormValue_Boolean("ExcludeCurrentUser")) intExcludeLoginNo = (int)SessionManager.LoginID;
            //[0001]
            //if (intGlobalLoginClientNo <= 0)
            //    intGlobalLoginClientNo = SessionManager.ClientID;
            if (!intGlobalLoginClientNo.HasValue && intGlobalLoginClientNo == null)
                intGlobalLoginClientNo = SessionManager.ClientID;
            List<BLL.Login> lst = null;
            try
            {
                lst = Login.AutoSearchComboboxforSalesPersion(intGlobalLoginClientNo, intTeamNo, intDivisionNo, intExcludeLoginNo, GetFormValue_StringForNameSearchNew("search"));
                OutputSalesPersionList(lst);
            }
            catch (Exception e)
            {
                WriteError(e);
            }
            finally
            {
                lst = null;
            }
            base.GetData();
        }

    }
}