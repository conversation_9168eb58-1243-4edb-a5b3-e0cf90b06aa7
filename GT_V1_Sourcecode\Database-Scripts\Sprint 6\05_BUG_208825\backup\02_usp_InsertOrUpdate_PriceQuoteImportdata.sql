﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

IF OBJECT_ID('dbo.usp_InsertOrUpdate_PriceQuoteImportdata', 'P') IS NOT NULL
BEGIN
    DROP PROCEDURE dbo.usp_InsertOrUpdate_PriceQuoteImportdata
END
GO

/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-205174]		An.TranTan			28-Jun-2024		Update			Insert selected client id to tbPriceQuoteToBeImported using column CLIENT NO from import file
===========================================================================================
*/
CREATE PROCEDURE [dbo].[usp_InsertOrUpdate_PriceQuoteImportdata]
    @UserId INT,
    @ClientId INT,
    @SelectedClientId int,
    @list_Label_name nvarchar(max),
    @list_column_name nvarchar(max),
    @insertDataList nvarchar(max),
    @fileColName nvarchar(100),
    @SupplierId int,
    @CurrencyName nvarchar(255) = NULL,
    @recordType int,
    @RecordCount INT OUTPUT,
    @ErrorMessage VARCHAR(2000) Output,
    @PriceQuoteNo int OUTPUT
AS
BEGIN
    SET NOCOUNT ON;
    SET @ErrorMessage = NULL
    SET @RecordCount = 0
    SET @PriceQuoteNo = 0
    BEGIN TRY
        BEGIN TRANSACTION PriceQuoteImportTransaction

        -------------------Update--tbTempStockDa--Command----------Start-----------------------------------------------------------                                

        DECLARE @Label_name VARCHAR(MAX),
                @column_name VARCHAR(MAX)
        declare @isErrorFound bit = 0
        DECLARE @msg NVARCHAR(max),
                @counterVar int = 1

        truncate table tbTmpUtilityTable

        DECLARE cursor_SavePriceQuoteImportStockData CURSOR FOR WITH T1
                                                                AS (
                                                                   select val as Label_name,
                                                                          ROW_NUMBER() OVER (ORDER BY (SELECT 1)) AS ID
                                                                   FROM [BorisGlobalTrader].dbo.[SplitString](
                                                                                                                 @list_Label_name,
                                                                                                                 ','
                                                                                                             )
                                                                   ),
                                                                     T2
                                                                AS (select val as Column_name,
                                                                           ROW_NUMBER() OVER (ORDER BY (SELECT 1)) AS ID
                                                                    FROM [BorisGlobalTrader].dbo.[SplitString](
                                                                                                                  @list_column_name,
                                                                                                                  ','
                                                                                                              )
                                                                   )
        SELECT T1.Label_name,
               T2.Column_name
        FROM T1
            FULL JOIN T2
                ON (T1.ID = T2.ID)
        OPEN cursor_SavePriceQuoteImportStockData;

        FETCH NEXT FROM cursor_SavePriceQuoteImportStockData
        INTO @Label_name,
             @column_name

        WHILE @@FETCH_STATUS = 0
        BEGIN

            insert into tbTmpUtilityTable
            (
                Value1,
                Value2,
                rwStatus,
                lineNumber,
                insertedby,
                clientid,
                utilityname
            )
            select @Label_name,
                   @column_name,
                   0,
                   @counterVar,
                   @UserId,
                   @ClientId,
                   'PriceQuoteImport'

            declare @strScript nvarchar(max)
            set @strScript = ''
            --RequirementNo--1                                                               
            IF @Label_name = 'RequirementNo'
            BEGIN
                set @strScript
                    = 'update BorisGlobalTraderimports.dbo.[tbPriceQuoteImport_tempData] set ' + @column_name + '='
                      + '' + ' dbo.stripNumeric(' + @column_name + ')' + '' + ' WHERE  ClientId= '
                      + CONVERT(NVARCHAR(10), @ClientId) + ' and SelectedClientId= '
                      + CONVERT(NVARCHAR(10), @SelectedClientId) + '  and CreatedBy= ' + CONVERT(NVARCHAR(10), @UserId)
                      + ''
                SET @isErrorFound = 1;
                exec (@strScript)
            -- PRINT 'RequirementNo  ' + @Label_name + ' Column name ' + @column_name                                                                   
            -- print '1'                                                                              
            END


            --ManufacturerName--1                                                                                          
            IF @Label_name = 'ManufacturerName'
            BEGIN
                set @strScript
                    = 'update BorisGlobalTraderimports.dbo.[tbPriceQuoteImport_tempData] set ' + @column_name + '='
                      + '' + '(case    when len( ' + @column_name + ')>100 then dbo.stripAlphahtestingMfr(SUBSTRING('
                      + @column_name
                      + ',0, 100))                                              
      else dbo.stripAlphahtestingMfr( ' + @column_name + ' )  end)' + '' + ' WHERE  ClientId= '
                      + CONVERT(NVARCHAR(10), @ClientId) + ' and SelectedClientId= '
                      + CONVERT(NVARCHAR(10), @SelectedClientId) + '  and CreatedBy= ' + CONVERT(NVARCHAR(10), @UserId)
                      + ''
                SET @isErrorFound = 1;
                exec (@strScript)

            -- PRINT 'ManufacturerName  ' + @Label_name + ' Column name ' + @column_name                                                                   
            -- print '1'                                                                                                                         
            END
            --Part--2                                                                                                                          
            IF @Label_name = 'Part'
            BEGIN
                set @strScript
                    = 'update BorisGlobalTraderimports.dbo.[tbPriceQuoteImport_tempData] set ' + @column_name + '='
                      + '' + '(case    when len( ' + @column_name + ')>30 then dbo.stripAlphahnumeric(SUBSTRING('
                      + @column_name
                      + ',0, 30))                                                                  
   else dbo.stripAlphahnumeric( ' + @column_name + ' )  end)' + '' + ' WHERE  ClientId= '
                      + CONVERT(NVARCHAR(10), @ClientId) + ' and SelectedClientId= '
                      + CONVERT(NVARCHAR(10), @SelectedClientId) + '  and CreatedBy= ' + CONVERT(NVARCHAR(10), @UserId)
                      + ''
                SET @isErrorFound = 1;
                exec (@strScript)
            -- PRINT 'Part  ' + @Label_name + ' Column name ' + @column_name                                   
            --print '2'                       
            END
            --Quantity--3                                                                                                                          
            IF @Label_name = 'Quantity'
            BEGIN


                set @strScript
                    = 'update BorisGlobalTraderimports.dbo.[tbPriceQuoteImport_tempData] set ' + @column_name + '='
                      + 'FLOOR(' + @column_name
                      + ')                                                    
              '                  + ' WHERE  ClientId= ' + CONVERT(NVARCHAR(10), @ClientId) + ' and SelectedClientId= '
                      + CONVERT(NVARCHAR(10), @SelectedClientId) + '  and CreatedBy= ' + CONVERT(NVARCHAR(10), @UserId)
                      + ''
                SET @isErrorFound = 1;
                exec (@strScript)
                PRINT 'Quantity  ' + @Label_name + ' Column name ' + @column_name
            --print '3'                                                                                                 
            END

            --Price--4                                                                                                                   
            IF @Label_name = 'Price'
            BEGIN

                set @strScript
                    = 'update BorisGlobalTraderimports.dbo.[tbPriceQuoteImport_tempData] set ' + @column_name + '='
                      + ' dbo.stripNumeric(' + @column_name
                      + ')                                                                                                  


    '                            + ' WHERE  ClientId= ' + CONVERT(NVARCHAR(10), @ClientId) + ' and SelectedClientId= '
                      + CONVERT(NVARCHAR(10), @SelectedClientId) + '  and CreatedBy= ' + CONVERT(NVARCHAR(10), @UserId)
                      + ''
                SET @isErrorFound = 1;
                exec (@strScript)
            -- PRINT 'Price  ' + @Label_name + ' Column name ' + @column_name      
            --print '4'                                                                             
            END
            --Description--5                                                                 
            IF @Label_name = 'Description'
            BEGIN
                set @strScript
                    = 'update BorisGlobalTraderimports.dbo.[tbPriceQuoteImport_tempData] set ' + @column_name + '='
                      + '' + '(case    when len( ' + @column_name + ')>500 then dbo.stripAlphahnumeric2(SUBSTRING('
                      + @column_name
                      + ',0, 500))                                                                    
 else dbo.stripAlphahnumeric2( ' + @column_name + ' )  end)' + '' + ' WHERE  ClientId= '
                      + CONVERT(NVARCHAR(10), @ClientId) + ' and SelectedClientId= '
                      + CONVERT(NVARCHAR(10), @SelectedClientId) + '  and CreatedBy= ' + CONVERT(NVARCHAR(10), @UserId)
                      + ''
                SET @isErrorFound = 1;
                exec (@strScript)
            --print '20'                                                                                                                         
            END
            --AlternatePart--6                                                               
            IF @Label_name = 'AlternatePart'
            BEGIN
                set @strScript
                    = 'update BorisGlobalTraderimports.dbo.[tbPriceQuoteImport_tempData] set ' + @column_name + '='
                      + '' + '(case    when len( ' + @column_name + ')>30 then dbo.stripAlphahnumeric2(SUBSTRING('
                      + @column_name
                      + ',0, 30))                                                                                          
             else dbo.stripAlphahnumeric2( ' + @column_name + ' )  end)' + '' + ' WHERE  ClientId= '
                      + CONVERT(NVARCHAR(10), @ClientId) + ' and SelectedClientId= '
                      + CONVERT(NVARCHAR(10), @SelectedClientId) + '  and CreatedBy= ' + CONVERT(NVARCHAR(10), @UserId)
                      + ''
                SET @isErrorFound = 1;
                exec (@strScript)
            --print '5'                                                                                                                    
            END
            --DateCode--7                                                                                                                          
            IF @Label_name = 'DateCode'
            BEGIN
                --print @Label_name                                                                                                           
                set @strScript
                    = 'update BorisGlobalTraderimports.dbo.[tbPriceQuoteImport_tempData] set ' + @column_name + '='
                      + '' + '(case when len( ' + @column_name + ') > 5 then dbo.stripAlphahnumeric(SUBSTRING('
                      + @column_name
                      + ',0, 5))                                                                                             
              else dbo.stripAlphahnumeric( ' + @column_name + ' )  end)' + '' + ' WHERE  ClientId= '
                      + CONVERT(NVARCHAR(10), @ClientId) + ' and SelectedClientId= '
                      + CONVERT(NVARCHAR(10), @SelectedClientId) + '  and CreatedBy= ' + CONVERT(NVARCHAR(10), @UserId)
                      + ''

                SET @isErrorFound = 1;
                --print @strScript                                                                                         
                exec (@strScript)
            --print '6'                                                                                                              
            end --ProductName--8                                                                                                                          
            IF @Label_name = 'ProductName'
            BEGIN
                set @strScript
                    = 'update BorisGlobalTraderimports.dbo.[tbPriceQuoteImport_tempData] set ' + @column_name + '='
                      + '' + '(case    when len( ' + @column_name + ')>128 then dbo.stripAlphahnumeric(SUBSTRING('
                      + @column_name
                      + ',0, 128))                                                        
   else dbo.stripAlphahnumeric( ' + @column_name + ' )  end)' + '' + ' WHERE  ClientId= '
                      + CONVERT(NVARCHAR(10), @ClientId) + ' and SelectedClientId= '
                      + CONVERT(NVARCHAR(10), @SelectedClientId) + '  and CreatedBy= ' + CONVERT(NVARCHAR(10), @UserId)
                      + ''
                SET @isErrorFound = 1;
                exec (@strScript)
            END
            --PackageName--9                                                                                                             
            IF @Label_name = 'PackageName'
            BEGIN
                set @strScript
                    = 'update BorisGlobalTraderimports.dbo.[tbPriceQuoteImport_tempData] set ' + @column_name + '='
                      + '' + '(case    when len( ' + @column_name + ')>60 then dbo.stripAlphahnumeric(SUBSTRING('
                      + @column_name
                      + ',0, 60))                                                                                                               
               else dbo.stripAlphahnumeric( ' + @column_name + ' ) end)' + '' + ' WHERE  ClientId= '
                      + CONVERT(NVARCHAR(10), @ClientId) + ' and SelectedClientId= '
                      + CONVERT(NVARCHAR(10), @SelectedClientId) + '  and CreatedBy= ' + CONVERT(NVARCHAR(10), @UserId)
                      + ''
                SET @isErrorFound = 1;
                exec (@strScript)
            END
            --ROHS--10                       
            IF @Label_name = 'ROHS'
            BEGIN
                set @strScript
                    = 'update BorisGlobalTraderimports.dbo.[tbPriceQuoteImport_tempData] set ' + @column_name + '='
                      + '(case  when (len(' + @column_name + ')>50) then dbo.stripAlphahnumeric2(SUBSTRING('
                      + @column_name
                      + ',0, 50))                                                              
    when LOWER('                 + @column_name + ')=''compliant'' then ''1'' when LOWER(' + @column_name
                      + ')=''compliance'' then ''1'' when LOWER(' + @column_name
                      + ')=''comp'' then ''1''                                                                                              
 when LOWER('                    + @column_name
                      + ')=''yes'' then ''1''                                                
    when LOWER('                 + @column_name + ')=''rohs compliant'' then ''1'' when LOWER(' + @column_name
                      + ')=''rohscompliant'' then ''1'' when LOWER(' + @column_name
                      + ')=''rohs'' then ''1''                                                   
                          
    when LOWER('                 + @column_name + ')=''noncompliant'' then ''2'' when LOWER(' + @column_name
                      + ')=''uncompliant'' then ''2'' when LOWER(' + @column_name
                      + ')=''false'' then ''2''                                                                                         
    when LOWER('                 + @column_name + ')=''no'' then ''2'' when LOWER(' + @column_name
                      + ')=''rohs non-compliant'' then ''2''                                                                                                                 
    when LOWER('                 + @column_name + ')=''rohs noncompliant'' then ''2'' when LOWER(' + @column_name
                      + ')=''rohsnoncompliant'' then ''2''                                           
    when LOWER('                 + @column_name + ')=''non-compliant'' then ''2'' when LOWER(' + @column_name
                      + ')=''exempt'' then ''3'' when LOWER(' + @column_name
                      + ')=''rohs exempt'' then ''3''                                                                            
    when LOWER('                 + @column_name + ')=''rohsexempt'' then ''3'' when LOWER(' + @column_name
                      + ')=''na'' then ''4''                                                                                                                
    when LOWER('                 + @column_name + ')=''n/a'' then ''4'' when LOWER(' + @column_name
                      + ')=''rohs not applicable'' then ''4''                                                                                                        
    when LOWER('                 + @column_name
                      + ')=''rohs notapplicable'' then ''4''                                      
    when LOWER('                 + @column_name
                      + ')=''rohsnotapplicable'' then ''4''                                                                                                      
    when LOWER('                 + @column_name + ')=''rohs2'' then ''5'' when LOWER(' + @column_name
                      + ')=''rohs 2'' then ''5''  when LOWER(' + @column_name
                      + ')=''rohs 5/6'' then ''6''                                                                                                 
    when LOWER('                 + @column_name + ')=''rohs5/6'' then ''6'' when LOWER(' + @column_name
                      + ')=''rohs56'' then ''6''                                                     
    when LOWER('                 + @column_name + ')=''rohs 56'' then ''6'' when LOWER(' + @column_name
                      + ')=''rohs 6/6'' then ''7''                                                                                                    
    when LOWER('                 + @column_name + ')=''rohs6/6'' then ''7'' when LOWER(' + @column_name
                      + ')=''rohs66'' then ''7''                                                                          
   when LOWER('                  + @column_name + ')=''rohs 66'' then ''7''  else ''0'' end) ' + ' WHERE  ClientId= '
                      + CONVERT(NVARCHAR(10), @ClientId) + ' and SelectedClientId= '
                      + CONVERT(NVARCHAR(10), @SelectedClientId) + '  and CreatedBy= ' + CONVERT(NVARCHAR(10), @UserId)
                      + ''
                --print  @strScript                                                             
                SET @isErrorFound = 1;
                exec (@strScript)
            --print '8'                                          
            END
            --SupplierPart--11                                                                                     
            IF @Label_name = 'SupplierPart'
            BEGIN
                set @strScript
                    = 'update BorisGlobalTraderimports.dbo.[tbPriceQuoteImport_tempData] set ' + @column_name + '='
                      + '' + '(case    when len( ' + @column_name + ')>100 then dbo.stripAlphahnumeric2(SUBSTRING('
                      + @column_name
                      + ',0, 40))                                                                                      
            else dbo.stripAlphahnumeric2( ' + @column_name + ' )  end)' + '' + ' WHERE  ClientId= '
                      + CONVERT(NVARCHAR(10), @ClientId) + ' and SelectedClientId= '
                      + CONVERT(NVARCHAR(10), @SelectedClientId) + '  and CreatedBy= ' + CONVERT(NVARCHAR(10), @UserId)
                      + ''
                SET @isErrorFound = 1;
                exec (@strScript)
            --print '9'                                                                                                   
            END
            IF @Label_name = 'SPQ'
            BEGIN
                set @strScript
                    = 'update BorisGlobalTraderimports.dbo.tbPriceQuoteImport_tempdata set ' + @column_name + '=' + ''
                      + '(case    when len( ' + @column_name + ')>10 then dbo.stripAlphahnumeric2(SUBSTRING('
                      + @column_name + ',0, 10))              
            else dbo.stripAlphahnumeric2( ' + @column_name + ' )  end)' + '' + ' WHERE  ClientId= '
                      + CONVERT(NVARCHAR(10), @ClientId) + ' and SelectedClientId= '
                      + CONVERT(NVARCHAR(10), @SelectedClientId) + '  and CreatedBy= ' + CONVERT(NVARCHAR(10), @UserId)
                      + ''
                SET @isErrorFound = 1;
                exec (@strScript)
            --print '9'                                                                                                                       
            END

            IF @Label_name = 'MOQ'
            BEGIN
                set @strScript
                    = 'update BorisGlobalTraderimports.dbo.tbPriceQuoteImport_tempdata set ' + @column_name + '=' + ''
                      + '(case    when len( ' + @column_name + ')>100 then dbo.stripAlphahnumeric2(SUBSTRING('
                      + @column_name
                      + ',0, 10))                                                                                     
            else dbo.stripAlphahnumeric2( ' + @column_name + ' )  end)' + '' + ' WHERE  ClientId= '
                      + CONVERT(NVARCHAR(10), @ClientId) + ' and SelectedClientId= '
                      + CONVERT(NVARCHAR(10), @SelectedClientId) + '  and CreatedBy= ' + CONVERT(NVARCHAR(10), @UserId)
                      + ''
                SET @isErrorFound = 1;
                exec (@strScript)
            --print '9'                                                                                                                       
            END

            IF @Label_name = 'QtyInStock'
            BEGIN
                set @strScript
                    = 'update BorisGlobalTraderimports.dbo.tbPriceQuoteImport_tempdata set ' + @column_name + '=' + ''
                      + '(case    when len( ' + @column_name
                      + ')>20                                         
   then dbo.stripAlphahnumeric2(SUBSTRING(' + @column_name
                      + ',0, 20))                                 
            else dbo.stripAlphahnumeric2( ' + @column_name + ' )  end)' + '' + ' WHERE  ClientId= '
                      + CONVERT(NVARCHAR(10), @ClientId) + ' and SelectedClientId= '
                      + CONVERT(NVARCHAR(10), @SelectedClientId) + '  and CreatedBy= ' + CONVERT(NVARCHAR(10), @UserId)
                      + ''
                SET @isErrorFound = 1;
                exec (@strScript)
            -- print (@strScript)   ;                                        
            --print '9'       
            END

            IF @Label_name = 'OfferStatus'
            BEGIN

                set @strScript
                    = 'update BorisGlobalTraderimports.dbo.tbPriceQuoteImport_tempdata set ' + @column_name + '=' + ''
                      + '(case    when len( ' + @column_name + ')>30 then dbo.stripAlphahnumeric2(SUBSTRING('
                      + @column_name
                      + ',0, 30))                                                                                      
            else dbo.stripAlphahnumeric2( ' + @column_name + ' )  end)' + '' + ' WHERE  ClientId= '
                      + CONVERT(NVARCHAR(10), @ClientId) + ' and SelectedClientId= '
                      + CONVERT(NVARCHAR(10), @SelectedClientId) + '  and CreatedBy= ' + CONVERT(NVARCHAR(10), @UserId)
                      + ''
                SET @isErrorFound = 1;
                exec (@strScript)
            --print '9'                                                                                                                       
            END

            IF @Label_name = 'FactorySealed'
            BEGIN
                set @strScript
                    = 'update BorisGlobalTraderimports.dbo.tbPriceQuoteImport_tempdata set ' + @column_name + '=' + ''
                      + '(case    when len( ' + @column_name + ')>50 then dbo.stripAlphahnumeric2(SUBSTRING('
                      + @column_name
                      + ',0, 50))                                                                                      
            else dbo.stripAlphahnumeric2( ' + @column_name + ' )  end)' + '' + ' WHERE  ClientId= '
                      + CONVERT(NVARCHAR(10), @ClientId) + ' and SelectedClientId= '
                      + CONVERT(NVARCHAR(10), @SelectedClientId) + '  and CreatedBy= ' + CONVERT(NVARCHAR(10), @UserId)
                      + ''
                SET @isErrorFound = 1;
                exec (@strScript)
            --print '9'                                
            END

            IF @Label_name = 'LeadTime'
            BEGIN
                set @strScript
                    = 'update BorisGlobalTraderimports.dbo.tbPriceQuoteImport_tempdata set ' + @column_name + '=' + ''
                      + '(case    when len( ' + @column_name + ')>50 then dbo.stripAlphahnumeric2(SUBSTRING('
                      + @column_name
                      + ',0, 50))                                                                         
            else dbo.stripAlphahnumeric2( ' + @column_name + ' )  end)' + '' + ' WHERE  ClientId= '
                      + CONVERT(NVARCHAR(10), @ClientId) + ' and SelectedClientId= '
                      + CONVERT(NVARCHAR(10), @SelectedClientId) + '  and CreatedBy= ' + CONVERT(NVARCHAR(10), @UserId)
                      + ''
                SET @isErrorFound = 1;
                exec (@strScript)
            --print '9'                                  
            END
            IF @Label_name = 'Region'
            BEGIN
                set @strScript
                    = 'update BorisGlobalTraderimports.dbo.tbPriceQuoteImport_tempdata set ' + @column_name + '=' + ''
                      + '(case    when len( ' + @column_name + ')>200 then dbo.stripAlphahnumeric2(SUBSTRING('
                      + @column_name
                      + ',0, 200))                                                          
  else dbo.stripAlphahnumeric2( ' + @column_name + ' )  end)' + '' + ' WHERE  ClientId= '
                      + CONVERT(NVARCHAR(10), @ClientId) + ' and SelectedClientId= '
                      + CONVERT(NVARCHAR(10), @SelectedClientId) + '  and CreatedBy= ' + CONVERT(NVARCHAR(10), @UserId)
                      + ''
                SET @isErrorFound = 1;
                exec (@strScript)
            --print '9'                                                                                                     
            END


            IF @Label_name = 'CurrencyCode'
            BEGIN
                set @strScript
                    = 'update BorisGlobalTraderimports.dbo.tbPriceQuoteImport_tempdata set ' + @column_name + '=' + ''
                      + '(case    when len( ' + @column_name + ')>3 then dbo.stripAlphahnumeric2(SUBSTRING('
                      + @column_name
                      + ',0, 3))                                                                                      
            else dbo.stripAlphahnumeric2( ' + @column_name + ' )  end)' + '' + ' WHERE  ClientId= '
                      + CONVERT(NVARCHAR(10), @ClientId) + ' and SelectedClientId= '
                      + CONVERT(NVARCHAR(10), @SelectedClientId) + '  and CreatedBy= ' + CONVERT(NVARCHAR(10), @UserId)
                      + ''
                SET @isErrorFound = 1;
                exec (@strScript)
            -- PRINT 'CurrencyCode  ' + @Label_name + ' Column name ' + @column_name               
            --print '9'                           
            END
            IF @Label_name = 'Suppliername'
            BEGIN
                set @strScript
                    = 'update BorisGlobalTraderimports.dbo.tbPriceQuoteImport_tempdata set ' + @column_name + '=' + ''
                      + '(case    when len( ' + @column_name + ')>128 then SUBSTRING(' + @column_name
                      + ',0, 128)                             
            else  '              + @column_name + '   end)' + '' + ' WHERE  ClientId= '
                      + CONVERT(NVARCHAR(10), @ClientId) + ' and SelectedClientId= '
                      + CONVERT(NVARCHAR(10), @SelectedClientId) + '  and CreatedBy= ' + CONVERT(NVARCHAR(10), @UserId)
                      + ''
                SET @isErrorFound = 1;
                exec (@strScript)
            --print '9'                         
            END
            --SupplierCost--9                                                
            IF @Label_name = 'SupplierCost'
            BEGIN
                set @strScript
                    = 'update BorisGlobalTraderimports.dbo.[tbPriceQuoteImport_tempData] set ' + @column_name + '='
                      + '' + 'dbo.stripNumeric(' + @column_name + ')' + '' + ' WHERE  ClientId= '
                      + CONVERT(NVARCHAR(10), @ClientId) + ' and SelectedClientId= '
                      + CONVERT(NVARCHAR(10), @SelectedClientId) + '  and CreatedBy= ' + CONVERT(NVARCHAR(10), @UserId)
                      + ''
                SET @isErrorFound = 1;
                exec (@strScript)
            -- PRINT 'SupplierCost  ' + @Label_name + ' Column name ' + @column_name                                                                   
            -- print '1'                                                                                                                         
            END

            --Quantity--                                                                           
            IF @Label_name = 'BuyPrice'
            BEGIN

                SET @strScript
                    = 'update BorisGlobalTraderimports.dbo.[tbPriceQuoteImport_tempData] set ' + @column_name + '='
                      + '' + 'dbo.stripNumeric(' + @column_name + ')' + '' + ' WHERE  ClientId= '
                      + CONVERT(NVARCHAR(10), @ClientId) + ' and SelectedClientId= '
                      + CONVERT(NVARCHAR(10), @SelectedClientId) + '  and CreatedBy= ' + CONVERT(NVARCHAR(10), @UserId)
                      + ''
                SET @isErrorFound = 1;
                exec (@strScript)
            -- PRINT 'BuyPrice  ' + @Label_name + ' Column name ' + @column_name                                                                           
            END

            IF @Label_name = 'SellPrice'
            BEGIN
                SET @strScript
                    = 'update BorisGlobalTraderimports.dbo.[tbPriceQuoteImport_tempData] set ' + @column_name + '='
                      + '' + 'dbo.stripNumeric(' + @column_name + ')' + '' + ' WHERE  ClientId= '
                      + CONVERT(NVARCHAR(10), @ClientId) + ' and SelectedClientId= '
                      + CONVERT(NVARCHAR(10), @SelectedClientId) + '  and CreatedBy= ' + CONVERT(NVARCHAR(10), @UserId)
                      + ''
                SET @isErrorFound = 1;
                exec (@strScript)
            -- PRINT 'SellPrice  ' + @Label_name + ' Column name ' + @column_name                                                                                 
            END

            IF @Label_name = 'ShippingCost'
            BEGIN
                SET @strScript
                    = 'update BorisGlobalTraderimports.dbo.[tbPriceQuoteImport_tempData] set ' + @column_name + '='
                      + '' + 'dbo.stripNumeric(' + @column_name + ')' + '' + ' WHERE  ClientId= '
                      + CONVERT(NVARCHAR(10), @ClientId) + ' and SelectedClientId= '
                      + CONVERT(NVARCHAR(10), @SelectedClientId) + '  and CreatedBy= ' + CONVERT(NVARCHAR(10), @UserId)
                      + ''
                SET @isErrorFound = 1;
                exec (@strScript)
            -- PRINT 'ShippingCost  ' + @Label_name + ' Column name ' + @column_name                                               
            END
            IF @Label_name = 'LastTimeBuy'
            BEGIN
                SET @strScript
                    = 'update BorisGlobalTraderimports.dbo.tbPriceQuoteImport_tempdata set ' + @column_name + '=' + ''
                      + '(case    when len( ' + @column_name + ')>10 then SUBSTRING(' + @column_name
                      + ',0, 10)                                                                                      
            else  '              + @column_name + '   end)' + '' + ' WHERE  ClientId= '
                      + CONVERT(NVARCHAR(10), @ClientId) + ' and SelectedClientId= '
                      + CONVERT(NVARCHAR(10), @SelectedClientId) + '  and CreatedBy= ' + CONVERT(NVARCHAR(10), @UserId)
                      + ''
                SET @isErrorFound = 1;
                exec (@strScript)
            END
            IF @Label_name = 'DeliveryDate'
            BEGIN
                SET @strScript
                    = 'update BorisGlobalTraderimports.dbo.tbPriceQuoteImport_tempdata set ' + @column_name + '=' + ''
                      + '(case    when len( ' + @column_name + ')>10 then SUBSTRING(' + @column_name
                      + ',1, 10)                                                                                      
            else  '              + @column_name + '   end)' + '' + ' WHERE  ClientId= '
                      + CONVERT(NVARCHAR(10), @ClientId) + ' and SelectedClientId= '
                      + CONVERT(NVARCHAR(10), @SelectedClientId) + '  and CreatedBy= ' + CONVERT(NVARCHAR(10), @UserId)
                      + ''
                SET @isErrorFound = 1;
                exec (@strScript)
            END
            IF @Label_name = 'MSL'
            BEGIN
                set @strScript
                    = 'update BorisGlobalTraderimports.dbo.tbPriceQuoteImport_tempdata set ' + @column_name + '=' + ''
                      + '(case    when len( ' + @column_name + ')>10 then dbo.stripAlphahnumeric2(SUBSTRING('
                      + @column_name
                      + ',1, 10))                                                                                      
            else dbo.stripAlphahnumeric2( ' + @column_name + ' )  end)' + '' + ' WHERE  ClientId= '
                      + CONVERT(NVARCHAR(10), @ClientId) + ' and SelectedClientId= '
                      + CONVERT(NVARCHAR(10), @SelectedClientId) + '  and CreatedBy= ' + CONVERT(NVARCHAR(10), @UserId)
                      + ''
                SET @isErrorFound = 1;
                exec (@strScript)
            --print '9'                                                                                                                       
            END
            -- print @strScript;                                                    


            update tbTmpUtilityTable
            set rwStatus = 1
            where Value1 = @Label_name
                  and Value2 = @column_name
                  and clientid = @ClientId
                  and insertedby = @UserId
                  and utilityname = 'PriceQuoteImport'

            --- increment @counterVar to +1 to set the line number to match with the row data of excel uploaded                                                                        
            SET @counterVar = @counterVar + 1;

            FETCH NEXT FROM cursor_SavePriceQuoteImportStockData
            INTO @Label_name,
                 @column_name
        END;

        CLOSE cursor_SavePriceQuoteImportStockData;
        DEALLOCATE cursor_SavePriceQuoteImportStockData;

        SET @isErrorFound = 0;

        -------------------Update--tbTempStockDa--Command----------end-----------------------------------------------------------                                                                                                                           
        --print '10'                                                   
        DECLARE @DynamicQuery nvarchar(max)

        SET @DynamicQuery
            = 'insert  into   BorisGlobalTraderimports.dbo.[tbPriceQuoteToBeImported] (OriginalFilename,GeneratedFilename,ClientId,SelectedClientId,CreatedBy,'
              + @list_Label_name + ' ' + ') '
              + 'select OriginalFilename,GeneratedFilename,ClientId,Column2,CreatedBy,' + @insertDataList
              + ' from BorisGlobalTraderimports.dbo.[tbPriceQuoteImport_tempData] ' + 'WHERE  ClientId= '
              + CONVERT(NVARCHAR(10), @ClientId) + ' and SelectedClientId= ' + CONVERT(NVARCHAR(10), @SelectedClientId)
              + '  and CreatedBy= ' + CONVERT(NVARCHAR(10), @UserId)
                                                                                              
        print @DynamicQuery;
        Exec (@DynamicQuery)
        SET @RecordCount = @@ROWCOUNT                                                                      

        DECLARE @PrId INT;
        EXEC [dbo].[usp_Import_PriceQuoteImport] @UserId, @PrId OUT
        SET @PriceQuoteNo = @PrId;

        COMMIT TRANSACTION PriceQuoteImportTransaction
    END try
    BEGIN catch

        declare @curStatus smallint
        SET @curStatus = Cursor_Status('global', 'cursor_SavePriceQuoteImportStockData'); --set it to LOCAL above, if using global above change here too                                                                
        IF @curStatus >= 0
        BEGIN
            CLOSE cursor_SavePriceQuoteImportStockData;
            DEALLOCATE cursor_SavePriceQuoteImportStockData;
        END
        ELSE IF @curStatus = -1 --may have been closed already so just deallocate                                                                
        BEGIN
            DEALLOCATE cursor_SavePriceQuoteImportStockData;
        END;

        if (@isErrorFound = 1)
        BEGIN
            --print 'inside error'                                                  
            DECLARE @LabelText1 varchar(100);
            DECLARE @ColumnText1 varchar(100);
            --select * from tbTmpUtilityTable                                                                                  
            Select @LabelText1 = Value1,
                   @ColumnText1 = Value2
            from tbTmpUtilityTable
            where RwID =
            (
                Select min(RwID)
                from tbTmpUtilityTable
                where rwStatus = 0
                      and insertedby = @UserId
                      and utilityname = 'PriceQuoteImport'
            );

            --SET @msg = Replace(FORMATMESSAGE('Error while processing: %s for column %s mapped to %s', Error_message(),@LabelText1, @ColumnText1),'.',' ');                                                               
            SET @msg = Replace(FORMATMESSAGE('Please check column %s for data format issues.', @LabelText1), '.', ',');
        END
        ELSE
        BEGIN
            SET @msg = Replace(FORMATMESSAGE(' Some issue in data processing %s', Error_message()), '.', ' ');
        END

        ROLLBACK TRANSACTION PriceQuoteImportTransaction
        SET @RecordCount = 0
        SET @PriceQuoteNo = 0
        SELECT @ErrorMessage = @msg

    END catch
END

GO



