using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.Site.Controls.HomeNuggets.Data {
	[WebService(Namespace = "http://tempuri.org/")]
	[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
	public class MyRecentlyShippedOrders : Rebound.GlobalTrader.Site.Data.Base {

		public override void ProcessRequest(HttpContext context) {
			if (base.init(context)) {
				switch (Action) {
					case "GetData": GetData(); break;
					default: WriteErrorActionNotFound(); break;
				}
			}
		}

		/// <summary>
		/// Gets the main data
		/// </summary>
		private void GetData() {
            try
            {
                int intLoginID = LoginID;
                if (GetFormValue_Int("OtherLoginID") > 0) intLoginID = GetFormValue_Int("OtherLoginID");
                List<SalesOrder> lstShipped = SalesOrder.GetListShippedRecentlyForLogin(intLoginID, RowCount);
                if (lstShipped == null)
                {
                    WriteErrorDataNotFound();
                }
                else
                {
                    //shipped
                    JsonObject jsn = new JsonObject();
                    JsonObject jsnItems = new JsonObject(true);
                    for (int i = 0; i < lstShipped.Count; i++)
                    {
                        JsonObject jsnItem = new JsonObject();
                        jsnItem.AddVariable("ID", lstShipped[i].SalesOrderNo);
                        jsnItem.AddVariable("No", lstShipped[i].SalesOrderNumber);
                        jsnItem.AddVariable("Due", Functions.FormatDate(lstShipped[i].DatePromised));
                        jsnItem.AddVariable("CM", lstShipped[i].CompanyName);
                        jsnItem.AddVariable("CMNo", lstShipped[i].CompanyNo);
                        jsnItem.AddVariable("DateShipped", Functions.FormatDate(lstShipped[i].InvoiceDate));
                        jsnItems.AddVariable(jsnItem);
                        jsnItem.Dispose();
                        jsnItem = null;
                    }
                    jsn.AddVariable("Shipped", jsnItems);
                    jsn.AddVariable("Count", lstShipped.Count);
                    jsnItems.Dispose();
                    jsnItems = null;

                    OutputResult(jsn);
                    jsn.Dispose();
                    jsn = null;
                }
                lstShipped = null;
            }
            catch (Exception ex)
            {
                Errorlog obj = new Errorlog();
                obj.LogMessage(Convert.ToString("Exception- " + ex.Message + " " + ex.StackTrace));
            }
        }


	}
}
