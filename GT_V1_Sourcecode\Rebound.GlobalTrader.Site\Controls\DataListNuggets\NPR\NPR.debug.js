﻿/*
Marker     Changed by      Date          Remarks
[001]      <PERSON><PERSON><PERSON>          13/09/2014   NPR Search
*/
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DataListNuggets");
Rebound.GlobalTrader.Site.Controls.DataListNuggets.NPR = function(element) {
    Rebound.GlobalTrader.Site.Controls.DataListNuggets.NPR.initializeBase(this, [element]);
};
Rebound.GlobalTrader.Site.Controls.DataListNuggets.NPR.prototype = {
    initialize: function() {
        this.addGetDataOKEvent(Function.createDelegate(this, this.getDataOK));
        this.addInitCompleteEvent(Function.createDelegate(this, this.initAfterBaseIsReady));
        this._strPathToData = "controls/DataListNuggets/NPR";
        this._strDataObject = "NPR";
        Rebound.GlobalTrader.Site.Controls.DataListNuggets.NPR.callBaseMethod(this, "initialize");
    },

    initAfterBaseIsReady: function() {

        this.getData();
    },

    dispose: function() {
        if (this.isDisposed) return;       
        Rebound.GlobalTrader.Site.Controls.DataListNuggets.NPR.callBaseMethod(this, "dispose");
    },
    
    getDataOK: function() {
		for (var i = 0, l = this._objResult.Results.length; i < l; i++) {
			var row = this._objResult.Results[i];
			var aryData = [
			
			//$RGT_nubButton_NPRNugget(row.ID, row.NprNo)

				  $R_FN.writeDoubleCellValue($RGT_nubButton_NPRNugget(row.ID, row.NprNo, row.GILIneNo), $RGT_nubButton_PurchaseOrder(row.POID, row.PONo))
				, $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Part), $R_FN.setCleanTextValue(row.Location))

				, $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Quantity), $R_FN.setCleanTextValue(row.UPrice))
				, $RGT_nubButton_Company(row.CMNo, row.CM)
				, $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.AuthBy), $R_FN.setCleanTextValue(row.CompBy))
				, $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.NprDate),$R_FN.setCleanTextValue(row.Actn))
			];
			this._table.addRow(aryData, row.ID, false);
			aryData = null; row = null;
		}
	}



};




Rebound.GlobalTrader.Site.Controls.DataListNuggets.NPR.registerClass("Rebound.GlobalTrader.Site.Controls.DataListNuggets.NPR", Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base, Sys.IDisposable);
