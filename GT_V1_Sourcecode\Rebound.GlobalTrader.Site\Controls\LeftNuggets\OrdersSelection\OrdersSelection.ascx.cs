using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site;

namespace Rebound.GlobalTrader.Site.Controls.LeftNuggets {
	public partial class OrdersSelection : Selection {

		protected override void OnInit(EventArgs e) {
			SetLeftNuggetType("OrdersSelection");
			base.OnInit(e);
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.LeftNuggets.Base", ctlDesignBase.ClientID);
			AddItems();
		}

		private void AddItems() {
			HtmlControl ul = new HtmlGenericControl("ul");
			ul.Controls.Add(AddItem("Orders_Sourcing"));
			ul.Controls.Add(AddItem("Orders_CustomerRequirementBrowse"));
			ul.Controls.Add(AddItem("Orders_QuoteBrowse"));
			ul.Controls.Add(AddItem("Orders_SalesOrderBrowse"));
			ul.Controls.Add(AddItem("Orders_InvoiceBrowse"));
			ul.Controls.Add(AddItem("Orders_PurchaseOrderBrowse"));
			ul.Controls.Add(AddItem("Orders_PurchaseRequisitionBrowse"));
			ul.Controls.Add(AddItem("Orders_CustomerRMABrowse"));
			ul.Controls.Add(AddItem("Orders_SupplierRMABrowse"));
			ul.Controls.Add(AddItem("Orders_CreditNoteBrowse"));
			ul.Controls.Add(AddItem("Orders_DebitNoteBrowse"));
            ul.Controls.Add(AddItem("Orders_InternalPurchaseOrderBrowse"));
            ul.Controls.Add(AddItem("Orders_BOMBrowse"));
			_plhItems.Controls.Add(ul);
		}

	}
}
