Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.ToDoListType=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.ToDoListType.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DropDowns.ToDoListType.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.DropDowns.ToDoListType.callBaseMethod(this,"initialize");this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK))},dispose:function(){this.isDisposed||Rebound.GlobalTrader.Site.Controls.DropDowns.ToDoListType.callBaseMethod(this,"dispose")},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/ToDoListType");this._objData.set_DataObject("ToDoListType");this._objData.set_DataAction("GetData")},dataCallOK:function(){var t=this._objData._result,n;if(t.Types)for(n=0;n<t.Types.length;n++)this.addOption(t.Types[n].Name,t.Types[n].ID)}};Rebound.GlobalTrader.Site.Controls.DropDowns.ToDoListType.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.ToDoListType",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);