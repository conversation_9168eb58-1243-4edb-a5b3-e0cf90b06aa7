﻿/*===========================================================================================  
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-220887]     An.TranTan		 18-Nov-2024		CREATE		Set default value for new permission
===========================================================================================  
*/
DECLARE @SecurityFunctionNo INT = NULL;

SELECT TOP 1 @SecurityFunctionNo = SecurityFunctionId 
FROM tbSecurityFunction 
WHERE FunctionName = 'Orders_Sourcing_BulkEdit_StrategicStock';

IF @SecurityFunctionNo IS NOT NULL
BEGIN
	--Clear all existing permissions
	DELETE tbSecurityGroupSecurityFunctionPermission
	WHERE SecurityFunctionNo = @SecurityFunctionNo
	
	;WITH cte AS(
		SELECT SecurityGroupId,
			@SecurityFunctionNo as SecurityFunctionId,
			CASE WHEN Administrator = 1 AND ClientNo = 114 THEN CAST(1 AS BIT) 
				ELSE CAST(0 AS BIT) 
			END AS IsAllowed
		FROM tbSecurityGroup
	)
	INSERT INTO tbSecurityGroupSecurityFunctionPermission 
	(  
		SecurityGroupNo  
	    ,SecurityFunctionNo  
	    ,IsAllowed  
	    ,DLUP
	) 
	SELECT 
		SecurityGroupId,
		SecurityFunctionId,	
		IsAllowed, --default: allow for admin in DMCC only
		GETDATE()
	FROM cte 
END
