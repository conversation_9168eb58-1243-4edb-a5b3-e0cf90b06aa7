﻿/*===========================================================================================  
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-221442]     An.TranTan		 22-Nov-2024		CREATE		Add new permissions allow generating Invoice in XML format
===========================================================================================  
*/
DECLARE @NewSecurityFunction TABLE
(
	SecurityFunctionId INT
	,FunctionName NVARCHAR(300)
	,Description NVARCHAR(600)
	,SitePageNo INT
	,SiteSectionNo INT
	,ReportNo INT
	,UpdatedBy INT 
	,DLUP DATETIME 
	,InitiallyProhibitedForNewLogins BIT
	,DisplaySortOrder INT
)
INSERT INTO @NewSecurityFunction
(
	SecurityFunctionId,
    FunctionName,
    Description,
    SitePageNo,
    SiteSectionNo,
    ReportNo,
    UpdatedBy,
    DLUP,
    InitiallyProhibitedForNewLogins,
    DisplaySortOrder
)VALUES
(
	20010031
	,'Orders_Invoice_Generate_XML'
	,'Allow generating Invoice in XML format'
	,2000502		--SitePageNo
	,2				--SiteSectionNo
	,NULL			--ReportNo
	,1				--UpdatedBy
	,GETDATE()		--DLUP
	,1				--InitiallyProhibitedForNewLogins
	,(SELECT (ISNULL(MAX(DisplaySortOrder),0) + 1) FROM tbSecurityFunction WHERE SiteSectionNo = 2 AND SitePageNo = 2000502)
)
,(
	20010032
	,'Orders_CreditNote_Generate_XML'
	,'Allow generating Credit Note in XML format'
	,2001002		--SitePageNo
	,2				--SiteSectionNo
	,NULL			--ReportNo
	,1				--UpdatedBy
	,GETDATE()		--DLUP
	,1				--InitiallyProhibitedForNewLogins
	,(SELECT (ISNULL(MAX(DisplaySortOrder),0) + 1) FROM tbSecurityFunction WHERE SiteSectionNo = 2 AND SitePageNo = 2001002)
)

INSERT INTO tbSecurityFunction (
	SecurityFunctionId,
    FunctionName,
    Description,
    SitePageNo,
    SiteSectionNo,
    ReportNo,
    UpdatedBy,
    DLUP,
    InitiallyProhibitedForNewLogins,
    DisplaySortOrder
)
SELECT 
	nsf.SecurityFunctionId,
    nsf.FunctionName,
    nsf.Description,
    nsf.SitePageNo,
    nsf.SiteSectionNo,
    nsf.ReportNo,
    nsf.UpdatedBy,
    nsf.DLUP,
    nsf.InitiallyProhibitedForNewLogins,
    nsf.DisplaySortOrder
FROM @NewSecurityFunction nsf
LEFT JOIN tbSecurityFunction sf ON sf.SecurityFunctionId = nsf.SecurityFunctionId
WHERE sf.SecurityFunctionId IS NULL	--insert if not exist ID

--set default value for new permissions
DELETE tbSecurityGroupSecurityFunctionPermission
WHERE SecurityFunctionNo IN (SELECT SecurityFunctionId FROM @NewSecurityFunction);

;WITH cte AS(
	SELECT	sg.SecurityGroupId,
			sf.SecurityFunctionId,
			CASE WHEN sg.Administrator = 1 THEN CAST(1 AS BIT) ELSE CAST(0 AS BIT) END AS IsAllowed
	FROM tbSecurityGroup sg, @NewSecurityFunction sf
)
INSERT INTO tbSecurityGroupSecurityFunctionPermission 
(  
	SecurityGroupNo  
    ,SecurityFunctionNo  
    ,IsAllowed  
    ,DLUP
) 
SELECT 
	SecurityGroupId,
	SecurityFunctionId,	
	IsAllowed,
	GETDATE()
FROM cte 
