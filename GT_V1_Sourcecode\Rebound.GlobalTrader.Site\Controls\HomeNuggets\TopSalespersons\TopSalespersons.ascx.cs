//----------------------------------------------------------------------------------------
// RP 21.12.2009:
//- use SimpleDataTable
//----------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.HomeNuggets {
	public partial class TopSalespersons : Base {

		protected SimpleDataTable _tblTopSalespersons;

		/// <summary>
		/// On init
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			this.HomePageNuggetType = "TopSalespersons";
			base.OnInit(e);
			WireUpControls();
			SetupTables();
			AddScriptReference("Controls.HomeNuggets.TopSalespersons.TopSalespersons.js");
			RowCount = SettingsManager.GetSetting_Int(Rebound.GlobalTrader.BLL.SettingItem.List.HomepageTopSalespeople);
		}

		protected override void OnLoad(EventArgs e) {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.HomeNuggets.TopSalespersons", this.ctlDesignBase.ClientID);
			_scScriptControlDescriptor.AddComponentProperty("tblTopSalespersons", _tblTopSalespersons.ClientID);
			base.OnLoad(e);
		}

		private void SetupTables() {
			_tblTopSalespersons.Columns.Add(new SimpleDataColumn("Salesman"));
			_tblTopSalespersons.Columns.Add(new SimpleDataColumn("GrossProfit", Unit.Pixel(75), HorizontalAlign.Right));
			_tblTopSalespersons.Columns.Add(new SimpleDataColumn("Margin", Unit.Pixel(75), HorizontalAlign.Right));
		}

		private void WireUpControls() {
			_tblTopSalespersons = (SimpleDataTable)FindContentControl("tblTopSalespersons");
		}
	}
}