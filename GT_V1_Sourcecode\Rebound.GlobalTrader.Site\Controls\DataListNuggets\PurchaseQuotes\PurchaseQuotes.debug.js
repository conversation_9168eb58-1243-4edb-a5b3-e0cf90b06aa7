
///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------

Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DataListNuggets");

Rebound.GlobalTrader.Site.Controls.DataListNuggets.PurchaseQuotes = function(element) { 
	Rebound.GlobalTrader.Site.Controls.DataListNuggets.PurchaseQuotes.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.DataListNuggets.PurchaseQuotes.prototype = {
    get_intSalesPersonID: function() { return this._intSalesPersonID; }, set_intSalesPersonID: function(value) { if (this._intSalesPersonID !== value) this._intSalesPersonID = value; },

    initialize: function() {
        this.addSetupDataCallEvent(Function.createDelegate(this, this.setupDataCall));
        this.addGetDataOKEvent(Function.createDelegate(this, this.getDataOK));
        this.addInitCompleteEvent(Function.createDelegate(this, this.initAfterBaseIsReady));
        this._strPathToData = "controls/DataListNuggets/PurchaseQuotes";
        this._strDataObject = "PurchaseQuotes";
        Rebound.GlobalTrader.Site.Controls.DataListNuggets.PurchaseQuotes.callBaseMethod(this, "initialize");
    },

    initAfterBaseIsReady: function() {
        this.addPageTabChangedEvent(Function.createDelegate(this, this.pageTabChanged));
        this.applySalesPersonFilter();
      //  this.updateFilterVisibility();
        this.getData();
    },

    dispose: function() {
        if (this.isDisposed) return;
        this._intSalesPersonID = null;
        Rebound.GlobalTrader.Site.Controls.DataListNuggets.PurchaseQuotes.callBaseMethod(this, "dispose");
    },

    pageTabChanged: function() {
        this._table._intCurrentPage = 1;
        this._enmViewLevel = this._intCurrentTab;
        //this.updateFilterVisibility();
        this.getData();
    },

    setupDataCall: function() {
        this._objData.addParameter("ViewLevel", this._enmViewLevel);
    },

    getDataOK: function() {
        for (var i = 0, l = this._objResult.Results.length; i < l; i++) {
            var row = this._objResult.Results[i];
            var aryData = [
				 $RGT_nubButton_POQuote(row.POQNo, row.POQNu)
				, $R_FN.setCleanTextValue(row.Part)
				, $RGT_nubButton_BOM(row.BOMNo, row.BOM)
				, row.Quantity
				, $R_FN.setCleanTextValue(row.CM) 
				, $R_FN.setCleanTextValue(row.Emp) 
				, row.Date
			];
            this._table.addRow(aryData, row.ID, false);
            aryData = null; row = null;
        }
    },
    
//    ,
//    updateFilterVisibility: function() {
//        this.getFilterField("ctlSalesmanName").show(this._enmViewLevel != 0);
//    },
    applySalesPersonFilter: function() {
    if ((this._intSalesPersonID) && this._intSalesPersonID > 0)
        this.getFilterField("ctlSalesmanName").setValue(this._intSalesPersonID);
    }

};
Rebound.GlobalTrader.Site.Controls.DataListNuggets.PurchaseQuotes.registerClass("Rebound.GlobalTrader.Site.Controls.DataListNuggets.PurchaseQuotes", Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base, Sys.IDisposable);
