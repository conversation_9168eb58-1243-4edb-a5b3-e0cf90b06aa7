﻿/*
 * Marker       Changed by      Date         Remarks
 * [001]        A<PERSON><PERSON><PERSON>  13-12-2021   Add login id for open my supplier Approval dashboard.
 */
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Services;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.DAL;

namespace Rebound.GlobalTrader.Site.Controls.HomeNuggets.Data
{
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class OpenSupplierPOApproval : Rebound.GlobalTrader.Site.Data.Base
    {
        public override void ProcessRequest(HttpContext context)
        {
            if (base.init(context))
            {
                switch (Action)
                {
                    case "GetData": GetData(); break;
                    default: WriteErrorActionNotFound(); break;
                }
            }
        }

        /// <summary>
        /// Gets the main data
        /// </summary>
        private void GetData()
        {
            try
            {
                int intLoginID = LoginID;
                if (GetFormValue_Int("OtherLoginID") > 0) intLoginID = GetFormValue_Int("OtherLoginID");

                List<InternalPurchaseOrderLine> lstPO = InternalPurchaseOrderLine.GetOpenSupplierPOApprovalStatus(SessionManager.ClientID, null, SessionManager.LoginID);
                if (lstPO == null)
                {
                    WriteErrorDataNotFound();
                }
                else
                {
                    JsonObject jsn = new JsonObject();
                    JsonObject jsnItems = new JsonObject(true);
                    for (int i = 0; i < lstPO.Count; i++)
                    {
                        JsonObject jsnItem = new JsonObject();
                        jsnItem.AddVariable("ID", lstPO[i].PurchaseOrderId);
                        jsnItem.AddVariable("No", lstPO[i].PurchaseOrderNo);
                        jsnItem.AddVariable("Date", Functions.FormatDate(lstPO[i].CreateDate));
                        jsnItem.AddVariable("LineManagerApproveStatus", lstPO[i].LineManagerApproveStatus);
                        jsnItem.AddVariable("QualityStatus", lstPO[i].QualityStatus);
                        jsnItem.AddVariable("DateOrdered", Functions.FormatDate(lstPO[i].DateOrdered));
                        jsnItems.AddVariable(jsnItem);
                        jsnItem.Dispose();
                        jsnItem = null;
                    }
                    jsn.AddVariable("POApprovalStatus", jsnItems);
                    OutputResult(jsn);
                    jsnItems.Dispose();
                    jsnItems = null;
                }
                lstPO = null;
            }
            catch (Exception ex)
            {
                Errorlog obj = new Errorlog();
                obj.LogMessage(Convert.ToString("Exception- " + ex.Message + " " + ex.StackTrace));
            }
        }

    }

}