<%@ Control Language="C#" CodeBehind="CreditBulk.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.DataListNuggets.CreditBulk" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" BoxType="List">
	<Filters>
		<ReboundUI_DataListNugget:Filter ID="ctlFilter" runat="server">
			<FieldsLeft>
				<ReboundUI_FilterDataItemRow:Numerical id="ctlCreditNo" runat="server" ResourceTitle="CreditNo"  FilterField="CreditNo" />
				<ReboundUI_FilterDataItemRow:TextBox id="ctlPart" runat="server" ResourceTitle="PartNo" FilterField="Part" />
				<ReboundUI_FilterDataItemRow:TextBox id="ctlCompanyName" runat="server" ResourceTitle="CompanyName" FilterField="CMName" />
				<ReboundUI_FilterDataItemRow:CheckBox id="ctlPohubOnly" runat="server" ResourceTitle="PohubOnly" FilterField="PohubOnly" DefaultValue="true"  />
				<ReboundUI_FilterDataItemRow:Numerical id="ctlInvNo" runat="server" ResourceTitle="InvoiceNo" FilterField="InvNo" />
				<ReboundUI_FilterDataItemRow:Numerical id="ctlCRMANo" runat="server" ResourceTitle="CRMANo" FilterField="CRMANo" />
				<ReboundUI_FilterDataItemRow:TextBox id="ctlCustomerPO" runat="server" ResourceTitle="CustomerPurchaseOrderNo" FilterField="CustPO" />
			</FieldsLeft>
			<FieldsRight>
				<ReboundUI_FilterDataItemRow:TextBox id="ctlContactName" runat="server" ResourceTitle="ContactName" FilterField="Contact" />
				<ReboundUI_FilterDataItemRow:DropDown id="ctlSalesman" runat="server" ResourceTitle="Salesman" DropDownType="Employee" DropDownAssembly="Rebound.GlobalTrader.Site" FilterField="Salesman" />
				<ReboundUI_FilterDataItemRow:TextBox id="ctlCreditNotes" runat="server" ResourceTitle="Notes" InitialSearchType="Contains" FilterField="CreditNotes" />
				<ReboundUI_FilterDataItemRow:DateSelect id="ctlCreditDateFrom" runat="server" ResourceTitle="CreditDateFrom" FilterField="CreditDateFrom" />
				<ReboundUI_FilterDataItemRow:DateSelect id="ctlCreditDateTo" runat="server" ResourceTitle="CreditDateTo" FilterField="CreditDateTo" />
			<ReboundUI_FilterDataItemRow:Numerical id="ctlClientInvNo" runat="server" ResourceTitle="ClientInvoiceNo" FilterField="ClientInvNo" />
            <ReboundUI_FilterDataItemRow:DropDown ID="ctlClientName" runat="server" ResourceTitle="ClientName" DropDownType="Client" DropDownAssembly="Rebound.GlobalTrader.Site" FilterField="Client" />
			</FieldsRight>
		</ReboundUI_DataListNugget:Filter>
		<div id="overlay" style="display: none"></div>
	</Filters>
	<Forms>
		<ReboundForm:CreditBulk_Confirm ID="ctlConfirm" runat="server" />
	</Forms>
</ReboundUI_Nugget:DesignBase>
<Content>
    <script src="../../../js/jquery-3.4.1.min.js"></script>
    <script src="../../../js/jquery-ui.js"></script>
    <style type="text/css">
        .custom-format-dialog {
            position: absolute;
            top: 40%;
            left: 40%;
            border-radius: 5px;
            background-image: url(../../../App_Themes/Original/images/Nuggets/standard/bg_top.gif);
            background-repeat: repeat-x;
            z-index: 2;
        }

        .ui-dialog-titlebar {
            font-weight: normal;
            padding: 5px;
            font-family: Lucida Sans Unicode, Arial !important;
            font-size: 12px;
            color: black;
        }

        .ui-dialog-titlebar-close {
            display: none;
        }

        .custom-modal {
            background-color: #56954E;
            color: white;
            padding-left: 5px;
            width: auto;
            height: 110px;
        }

        .modal-content-title {
            border-bottom: dotted 1px #cccccc;
            padding: 5px 0px;
            margin-bottom: 5px;
        }

        .modal-footer {
            background-image: url(../../../App_Themes/Original/images/Nuggets/standard/bg_top.gif);
            background-repeat: repeat-x;
            border-bottom-left-radius: 5px;
            border-bottom-right-radius: 5px;
            padding: 5px;
        }

        #overlay {
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0,0,0,0.5);
            z-index: 1;
            cursor: pointer;
        }

        .radio-container {
            display: flex;
            align-items: center;
        }

            .radio-container input[type=radio] {
                margin-bottom: 1px;
            }
    </style>
    <script type="text/javascript">
        $(function () {
            // Initialize the modal dialog
            $("#formatModal").dialog({
                autoOpen: false, // Start closed
                modal: true,
                resizable: false,
                draggable: false,
                width: 400,
                dialogClass: 'custom-format-dialog'
            });
        });
    </script>
    <div id="formatModal" class="modal" title="Print Credit" style="display: none">
        <div class="modal-content custom-modal">
            <div>
                <h4 id="formatActionTitle" class="modal-content-title">PRINT CREDIT</h4>
                <div style="padding-bottom: 5px">Select the generate format of credit notes</div>
                <div class="radio-container">
                    <div id="optionPDF" style="padding-right: 15px">
                        <input type="radio" name="rdFormat" id="rdPDF" value="PDF" />
                        <label for="rdPDF">PDF Format</label>
                    </div>
                    <div id="optionXML" style="display: none;">
                        <input type="radio" name="rdFormat" id="rdXml" value="XML" />
                        <label for="rdXml">XML Format</label>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <span>
                <a id="btnFormatAction" class="iconButton iconButton_Nugget iconButton_Nugget_Save iconButton_alignLeft" href="javascript:void(0);">Print</a>
            </span>
            <span>
                <a id="btnFormatCancel" class="iconButton iconButton_Nugget iconButton_Nugget_Cancel iconButton_alignLeft" href="javascript:void(0);">Cancel</a>
            </span>
        </div>
    </div>
</Content>
