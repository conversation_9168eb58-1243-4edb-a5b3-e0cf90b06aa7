/* Marker     changed by      date         Remarks
/* [0001]      <PERSON>   14/07/2020  IHS Work*/

using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site;
using Rebound.GlobalTrader.Site.Enumerations;
using System.Text;
using System.IO;
using System.Xml;
using System.Text.RegularExpressions;
using System.Data.Common;
using System.Reflection;
using Rebound.GlobalTrader.Site.Code.Common;

namespace Rebound.GlobalTrader.Site.Controls.DataListNuggets.Data
{

    public class IHSCatalogue : Base
    {
        public override void ProcessRequest(HttpContext context)
        {
            //if (base.init(context))
            //{
            //    switch (Action)
            //    {
            //        case "GetData": GetData(); break;
            //        case "ExportToCSV": ExportToCSV(); break;
            //        default: WriteErrorActionNotFound(); break;
            //    }
            //}
            base.ProcessRequest(context);
            if (Action == "ExportToCSV") ExportToCSV();
        }
        private string _strCallType;
        protected override void GetData()
        {

            Boolean IsGlobalLogin = GetFormValue_Boolean("IsGlobalLogin");
            List<GoodsInLine> lst = GoodsInLine.DataListNuggetIHS(
                    SessionManager.ClientID
                  , GetFormValue_NullableInt("SortIndex")
                  , GetFormValue_NullableInt("SortDir", SortColumnDirection.ASC)
                  , GetFormValue_NullableInt("PageIndex", 0)
                  , GetFormValue_NullableInt("PageSize", 10)
                   //, GetFormValue_PartForLikeSearch("Part")
                   , GetFormValue_StringForLikeSearch("Part")
                  , GetFormValue_StringForLikeSearch("Manufacturer")
                  , GetFormValue_NullableInt("countryOforigin")
                  , GetFormValue_StringForSearch("MSL")
                  , GetFormValue_StringForSearch("HtcCode")
                  , GetFormValue_StringForSearch("Description")
                  , GetFormValue_Boolean("RecentOnly")
                  , SessionManager.IsPOHub == true ? 1 : 0

              );
            JsonObject jsn = new JsonObject();
            jsn.AddVariable("TotalRecords", (lst.Count > 0) ? lst[0].RowCnt : 0);
            JsonObject jsnRowsArray = new JsonObject(true);
            for (int i = 0; i < lst.Count; i++)
            {
                if (i < lst.Count)
                {
                    JsonObject jsnRow = new JsonObject();
                    jsnRow.AddVariable("IHSPartsId", lst[i].IHSPartsId);
                    jsnRow.AddVariable("Part", lst[i].Part);
                    jsnRow.AddVariable("Mfr", lst[i].ManufacturerCode);
                    jsnRow.AddVariable("MfrNo", lst[i].ManufacturerNo);
                    jsnRow.AddVariable("Descriptions", Functions.ReplaceLineBreaks(lst[i].Descriptions));
                    jsnRow.AddVariable("CountryOfOrigin", lst[i].CountryOfOrigin);
                    jsnRow.AddVariable("CountryOfOriginNo", lst[i].CountryOfOriginNo);
                    jsnRow.AddVariable("LifeCycleStage", lst[i].LifeCycleStage);
                    jsnRow.AddVariable("MSL", lst[i].MSL);
                    jsnRow.AddVariable("MSLNo", lst[i].MSLNo);
                    jsnRow.AddVariable("HTSCode", lst[i].HTSCode);
                    jsnRow.AddVariable("AveragePrice", Functions.FormatCurrency(lst[i].AveragePrice, lst[i].IHSCurrencyCode), 2);
                    jsnRow.AddVariable("Packaging", lst[i].Packaging);
                    jsnRow.AddVariable("PackagingSize", lst[i].PackagingSize);
                    jsnRow.AddVariable("MSLName", lst[i].MSLName);
                    jsnRow.AddVariable("RowCnt", lst[i].RowCnt);
                    jsnRow.AddVariable("Date", Functions.FormatDate(lst[i].OriginalEntryDate));
                    jsnRow.AddVariable("IsPoHub", SessionManager.IsPOHub);
                    jsnRow.AddVariable("ECCNCode", Functions.ReplaceLineBreaks(lst[i].ECCNCode));

                    jsnRow.AddVariable("IsPDFAvailable", lst[i].IsPDFAvailable);
                    if (lst[i].IsPDFAvailable != true)
                        jsnRow.AddVariable("IconPath", "");
                    jsnRow.AddVariable("IconPath", "app_themes/original/images/IconButton/pdficon.jpg");
                    jsnRow.AddVariable("APIImportedData", lst[i].APIImportedData !=null ? (lst[i].APIImportedData.Value ? "Yes" : "No") : "Yes");

                    jsnRowsArray.AddVariable(jsnRow);
                    jsnRow.Dispose();
                    jsnRow = null;
                }
            }
            jsn.AddVariable("Results", jsnRowsArray);
            OutputResult(jsn);
            jsnRowsArray.Dispose();
            jsnRowsArray = null;
            jsn.Dispose();
            jsn = null;
            lst = null;
            base.GetData();
        }
        public void ExportToCSV()
        {
            JsonObject jsn = new JsonObject();
            try
            {

                Boolean? isPoHUB = SessionManager.IsPOHub;
                ViewLevelList enmViewLevel = (ViewLevelList)GetFormValue_Int("ViewLevel");
                string filePath = string.Empty;
                string strFilename = String.Format("report_u{0}r{1}.xlsx", LoginID, 0);

                //get data
                DataTable dtResult = CustomerRequirement.DataListNuggetIHS_Export(
                         //isPoHUB == true ? null : SessionManager.ClientID
                         //, GetFormValue_NullableInt("SortIndex", 0)
                         //, GetFormValue_NullableInt("SortDir", SortColumnDirection.ASC)
                         // //, GetFormValue_PartForLikeSearch("Part")
                         // , GetFormValue_StringForLikeSearch("Part")
                         //, GetFormValue_String("Manufacturer")
                         //, GetFormValue_String("countryOforigin")
                         //, GetFormValue_String("MSL")
                         //, GetFormValue_String("HtcCode")
                         // , Convert.ToBoolean(SessionManager.IsPOHub)
                         SessionManager.ClientID
                     , GetFormValue_NullableInt("SortIndex")
                     , GetFormValue_NullableInt("SortDir", SortColumnDirection.ASC)
                     , GetFormValue_NullableInt("PageIndex", 0)
                     , GetFormValue_NullableInt("PageSize", 10)
                      //, GetFormValue_PartForLikeSearch("Part")
                      , GetFormValue_StringForLikeSearch("Part")
                     , GetFormValue_StringForLikeSearch("Manufacturer")
                     , GetFormValue_NullableInt("countryOforigin")
                     , GetFormValue_StringForSearch("MSL")
                     , GetFormValue_StringForSearch("HtcCode")
                     , GetFormValue_StringForSearch("Description")
                     , GetFormValue_Boolean("RecentOnly")
                     , SessionManager.IsPOHub == true ? 1 : 0

                    );
                filePath = (new EPPlusExportUtility()).ExportDataTableToCSVIHS(dtResult, strFilename);

                jsn.AddVariable("Filename", filePath);
                OutputResult(jsn);

            }
            catch (Exception e)
            {
                WriteError(e);
            }
            finally
            {
                jsn.Dispose();
                jsn = null;
            }
        }
        protected override void AddFilterStates()
        {
            AddExplicitFilterState("CallType", _strCallType);
            AddFilterState("Part");
            AddFilterState("Manufacturer");
            AddFilterState("MSL");
            AddFilterState("HtcCode");
            AddFilterState("Description");
            AddFilterState("RecentOnly");
            base.AddFilterStates();
        }
        /// <summary>
        /// Writes report to a CSV file and returns the filename 
        /// </summary>
        /// <returns></returns>
        //public void ExportToCSV()
        //{
        //    JsonObject jsn = new JsonObject();
        //    try
        //    {

        //        Boolean? isPoHUB = SessionManager.IsPOHub;
        //        ViewLevelList enmViewLevel = (ViewLevelList)GetFormValue_Int("ViewLevel");
        //        string filePath = string.Empty;
        //        string strFilename = String.Format("report_u{0}r{1}.xlsx", LoginID, 0);

        //        //get data
        //        DataTable dtResult = CustomerRequirement.DataListNuggetIHS_Export(
        //                isPoHUB == true ? null : SessionManager.ClientID
        //                , GetFormValue_NullableInt("SortIndex", 0)
        //                , GetFormValue_NullableInt("SortDir", SortColumnDirection.ASC)
        //                 //, GetFormValue_PartForLikeSearch("Part")
        //                 , GetFormValue_StringForLikeSearch("Part")
        //                , GetFormValue_String("Manufacturer")
        //                , GetFormValue_String("countryOforigin")
        //                , GetFormValue_String("MSL")
        //                , GetFormValue_String("HtcCode")
        //                 , Convert.ToBoolean(SessionManager.IsPOHub)

        //            );
        //        filePath = (new EPPlusExportUtility()).ExportDataTableToCSVIHS(dtResult, strFilename);

        //        jsn.AddVariable("Filename", filePath);
        //        OutputResult(jsn);

        //    }
        //    catch (Exception e)
        //    {
        //        WriteError(e);
        //    }
        //    finally
        //    {
        //        jsn.Dispose();
        //        jsn = null;
        //    }
        //}





        private string FormatTextForCSV(string strIn)
        {
            if (!string.IsNullOrEmpty(strIn) && strIn.Contains(","))
                strIn = strIn.Replace(",", "");
            return strIn;
        }

        private string BlankCSVLine()
        {
            return string.Format(@"{0}", System.Environment.NewLine);
        }

    }
}
