using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;

namespace Rebound.GlobalTrader.Site.Controls.DropDowns.Data {
	[WebService(Namespace = "http://tempuri.org/")]
	[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
	public class CompanyGrouping : Rebound.GlobalTrader.Site.Data.DropDowns.Base {

		public override void ProcessRequest(HttpContext context) {
			SetDropDownType("CompanyGrouping");
			base.ProcessRequest(context);
		}

		protected override void GetData() {
			JsonObject jsn = new JsonObject();
			JsonObject jsnList = new JsonObject(true);
            jsnList.AddVariable(GetCompanyGroupingJsonItem("cust"));
            jsnList.AddVariable(GetCompanyGroupingJsonItem("supp"));
            jsnList.AddVariable(GetCompanyGroupingJsonItem("pros"));
			jsn.AddVariable("Types", jsnList);
			jsnList.Dispose(); jsnList = null;
			OutputResult(jsn);
			jsn.Dispose(); jsn = null;
		}

		private JsonObject GetCompanyGroupingJsonItem(string strValue) {
			JsonObject jsnItem = new JsonObject();
			jsnItem.AddVariable("ID", strValue);
            jsnItem.AddVariable("Name", Functions.GetGlobalResource("CompanyGrouping", strValue));
			return jsnItem;
		}
	}
}
