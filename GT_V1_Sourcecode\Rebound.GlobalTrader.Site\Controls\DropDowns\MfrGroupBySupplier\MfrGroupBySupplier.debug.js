///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// - Changes due to changes in base class
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");

Rebound.GlobalTrader.Site.Controls.DropDowns.MfrGroupBySupplier = function(element) {
	Rebound.GlobalTrader.Site.Controls.DropDowns.MfrGroupBySupplier.initializeBase(this, [element]);
	this._intCompanyID = null;
};

Rebound.GlobalTrader.Site.Controls.DropDowns.MfrGroupBySupplier.prototype = {

	get_intCompanyID: function() { return this._intCompanyID; }, set_intCompanyID: function(value) { if (this._intCompanyID !== value)  this._intCompanyID = value; }, 
	
	initialize: function() 	{
		Rebound.GlobalTrader.Site.Controls.DropDowns.MfrGroupBySupplier.callBaseMethod(this, "initialize");
		this.addSetupDataCall(Function.createDelegate(this, this.setupDataCall));
		this.addGetDataOK(Function.createDelegate(this, this.dataCallOK));
	},
	
	dispose: function() { 
		if (this.isDisposed) return;
		this._intCompanyID = null;
		Rebound.GlobalTrader.Site.Controls.DropDowns.MfrGroupBySupplier.callBaseMethod(this, "dispose");
	},
	
	setupDataCall: function() { 
		this._objData.set_PathToData("controls/DropDowns/MfrGroupBySupplier");
		this._objData.set_DataObject("MfrGroupBySupplier");
		this._objData.set_DataAction("GetData");
		this._objData.addParameter("SupplierId", this._intCompanyID);
	},
	
	dataCallOK: function() { 
		var result = this._objData._result;
		if (result.Items) {
			for (var i = 0; i < result.Items.length; i++) {
				this.addOption(result.Items[i].Name, result.Items[i].ID);
			}
		}
	}
	
};

Rebound.GlobalTrader.Site.Controls.DropDowns.MfrGroupBySupplier.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.MfrGroupBySupplier", Rebound.GlobalTrader.Site.Controls.DropDowns.Base);
