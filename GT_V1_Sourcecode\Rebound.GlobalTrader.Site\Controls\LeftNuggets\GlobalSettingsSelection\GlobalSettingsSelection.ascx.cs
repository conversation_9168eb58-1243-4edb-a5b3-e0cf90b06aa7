using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site;

namespace Rebound.GlobalTrader.Site.Controls.LeftNuggets {
	public partial class GlobalSettingsSelection : Selection {

		protected override void OnInit(EventArgs e) {
			SetLeftNuggetType("GlobalSettingsSelection");
			base.OnInit(e);
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.LeftNuggets.Base", ctlDesignBase.ClientID);
			AddItems();
		}

		private void AddItems() {
			HtmlControl ul = new HtmlGenericControl("ul");
			ul.Controls.Add(AddHeading(Functions.GetGlobalResource("Misc", "Setup_GlobalSettings")));
			if (SecurityManager.CheckSectionPermission(Rebound.GlobalTrader.BLL.SecurityFunction.List.Setup_GlobalSettings_CommunicationLogTypes)) ul.Controls.Add(AddItem(BLL.SitePage.List.Setup_GlobalSettings_CommunicationLogType));
			if (SecurityManager.CheckSectionPermission(Rebound.GlobalTrader.BLL.SecurityFunction.List.Setup_GlobalSettings_CompanyTypes)) ul.Controls.Add(AddItem(BLL.SitePage.List.Setup_GlobalSettings_CompanyType));
			if (SecurityManager.CheckSectionPermission(Rebound.GlobalTrader.BLL.SecurityFunction.List.Setup_GlobalSettings_CountingMethods)) ul.Controls.Add(AddItem(BLL.SitePage.List.Setup_GlobalSettings_CountingMethod));
			if (SecurityManager.CheckSectionPermission(Rebound.GlobalTrader.BLL.SecurityFunction.List.Setup_GlobalSettings_Incoterms)) ul.Controls.Add(AddItem(BLL.SitePage.List.Setup_GlobalSettings_Incoterm));
			if (SecurityManager.CheckSectionPermission(Rebound.GlobalTrader.BLL.SecurityFunction.List.Setup_GlobalSettings_IndustryTypes)) ul.Controls.Add(AddItem(BLL.SitePage.List.Setup_GlobalSettings_IndustryType));
			if (SecurityManager.CheckSectionPermission(Rebound.GlobalTrader.BLL.SecurityFunction.List.Setup_GlobalSettings_EntertainmentType)) ul.Controls.Add(AddItem(BLL.SitePage.List.Setup_GlobalSettings_EntertainmentType));
			if (SecurityManager.CheckSectionPermission(Rebound.GlobalTrader.BLL.SecurityFunction.List.Setup_GlobalSettings_AppSettings)) ul.Controls.Add(AddItem(BLL.SitePage.List.Setup_GlobalSettings_ApplicationSettings, Functions.GetGlobalResource("Misc", "MasterApplicationSettings")));
			if (SecurityManager.CheckSectionPermission(Rebound.GlobalTrader.BLL.SecurityFunction.List.Setup_GlobalSettings_MasterCountryList)) ul.Controls.Add(AddItem(BLL.SitePage.List.Setup_GlobalSettings_MasterCountryList));
			if (SecurityManager.CheckSectionPermission(Rebound.GlobalTrader.BLL.SecurityFunction.List.Setup_GlobalSettings_MasterCurrencyList)) ul.Controls.Add(AddItem(BLL.SitePage.List.Setup_GlobalSettings_MasterCurrencyList));
			if (SecurityManager.CheckSectionPermission(Rebound.GlobalTrader.BLL.SecurityFunction.List.Setup_GlobalSettings_Packages)) ul.Controls.Add(AddItem(BLL.SitePage.List.Setup_GlobalSettings_Package));
			if (SecurityManager.CheckSectionPermission(Rebound.GlobalTrader.BLL.SecurityFunction.List.Setup_GlobalSettings_Reasons)) ul.Controls.Add(AddItem(BLL.SitePage.List.Setup_GlobalSettings_Reason));
            if (SecurityManager.CheckSectionPermission(Rebound.GlobalTrader.BLL.SecurityFunction.List.Setup_GlobalSettings_EightDCode)) ul.Controls.Add(AddItem(BLL.SitePage.List.Setup_GlobalSettings_EightDCode));
			if (SecurityManager.CheckSectionPermission(Rebound.GlobalTrader.BLL.SecurityFunction.List.Setup_GlobalSettings_PDFDocumentFileSize)) ul.Controls.Add(AddItem(BLL.SitePage.List.Setup_GlobalSettings_PDFDocumentFileSize));
			_plhItems.Controls.Add(ul);
		}

	}
}