﻿
GO


SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/*
===========================================================================================================================================================  
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-202901]     Phuc Hoang		 11-Sep-2024		CREATE		German Invoice Part 1-Implement monthly Germany exchange rates published by tax authorities
[US-235850]     Phuc Hoang		 05-Mar-2025		UPDATE		[PROD Bug] Missing Exchange Rate in month 10,11,12
===========================================================================================================================================================  
*/

CREATE OR ALTER PROC [dbo].[usp_Get_GermanyExchangeRate] (
	@CurrencyDate DATETIME = NULL
)
AS

BEGIN
	SELECT * FROM [dbo].[tbGermanyExchangeRate]   
	WHERE ExchangeRateId IN (
		SELECT MAX(ExchangeRateId) 
		FROM [dbo].[tbGermanyExchangeRate] 
		WHERE YEAR(@CurrencyDate) = YEAR(DLUP) 
		GROUP BY CountryName
	)
END

GO


