<%@ Control Language="C#" CodeBehind="CompanyProspects.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyProspects" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" BoxType="Standard">
    <Links>
        <ReboundUI:IconButton ID="ibtnAdd" runat="server" IconGroup="Nugget" IconTitleResource="AddTask" IconCSSType="Add" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
        <ReboundUI:IconButton ID="ibtnViewTask" runat="server" IconGroup="Nugget" IconTitleResource="ViewTask" IconCSSType="Add" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
        <ReboundUI:IconButton ID="ibtnEdit" runat="server" IconGroup="Nugget" IconTitleResource="Edit" IconCSSType="Edit" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
    </Links>
    <Content>
        <div id="dvCompletePecent" style="margin-left:300px"></div>
        <table class="twoCols" cellpadding="0" cellspacing="0" border="0">
            <tr>
                <td class="col1">
                    <table cellpadding="0" cellspacing="0" border="0" class="dataItems">
                        <ReboundUI:DataItemRow ID="ctlProspectType" runat="server" ResourceTitle="ProspectType" />
                        <ReboundUI:DataItemRow ID="ctlMFRBoardLevel" runat="server"  ResourceTitle="MFRBoardLevel" />
                        <ReboundUI:DataItemRow ID="ctlFinalAssembly" runat="server" ResourceTitle="FinalAssembly" />
                        <ReboundUI:DataItemRow ID="ctlEndCustomer" runat="server" ResourceTitle="EndCustomer" />
                        <%--<ReboundUI:DataItemRow ID="ctlIndustry" runat="server" FieldType="CheckBox"   ResourceTitle="Industry" />--%>

                        <ReboundUI:DataItemRow id="ctlIndustryType" runat="server" ResourceTitle="IndustryType" />
                        <ReboundUI:DataItemRow id="hidIndustryTypeIDs" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="hidAllIndustryTypeNames" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="hidAllIndustryTypeIDs" runat="server" FieldType="Hidden" />

                      <%--  <ReboundUI:DataItemRow id="ctlEntertainmentType" runat="server" ResourceTitle="EntertainmentType" />
                        <ReboundUI:DataItemRow id="hidEntertainmentTypeIDs" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="hidAllEntertainmentTypeNames" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="hidAllEntertainmentTypeIDs" runat="server" FieldType="Hidden" />--%>
                    </table>
                </td>
                <td class="col2">
                    <table cellpadding="0" cellspacing="0" border="0" class="dataItems">
                        <ReboundUI:DataItemRow ID="ctlCreditInfo" runat="server" ResourceTitle="CreditInfo" />
                        <ReboundUI:DataItemRow ID="ctlElectronicSpend" runat="server" ResourceTitle="ElectronicSpend" />
                        <ReboundUI:DataItemRow ID="ctlFrequencyOfPurchase" runat="server" ResourceTitle="FrequencyOfPurchase" />
                        <ReboundUI:DataItemRow ID="ctlCommodities" runat="server" ResourceTitle="Commodities" />
                        <ReboundUI:DataItemRow ID="ctlTurnover" runat="server" ResourceTitle="Turnover" />
                        <ReboundUI:DataItemRow ID="hidProspectTypeId" runat="server" FieldType="Hidden" />
                        <ReboundUI:DataItemRow ID="hidIsIndustry" runat="server" FieldType="Hidden" />
                        <ReboundUI:DataItemRow ID="hidElectronicSpendId" runat="server" FieldType="Hidden" />
                        <ReboundUI:DataItemRow ID="hidFrequencyOfPurchaseId" runat="server" FieldType="Hidden" />
                        <ReboundUI:DataItemRow ID="hidCommoditiesId" runat="server" FieldType="Hidden" />
                        <ReboundUI:DataItemRow ID="hidTurnoverId" runat="server" FieldType="Hidden" />
                        <ReboundUI:DataItemRow ID="hidLimitedEstimate" runat="server" FieldType="Hidden" />
                        <ReboundUI:DataItemRow ID="hidHealthRating" runat="server" FieldType="Hidden" />

                        <ReboundUI:DataItemRow ID="hidMFRBoardLevel" runat="server" FieldType="Hidden" />
                        <ReboundUI:DataItemRow ID="hidFinalAssembly" runat="server" FieldType="Hidden" />
                       <%-- RP-2120--%>
                        <%-- <ReboundUI:DataItemRow id="ctlIndustryAreaType" runat="server" ResourceTitle="IndustryAreaType" />
                        <ReboundUI:DataItemRow id="hidIndustryAreaTypeIDs" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="hidAllIndustryAreaTypeNames" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="hidAllIndustryAreaTypeIDs" runat="server" FieldType="Hidden" />--%>
                    </table>
                </td>
            </tr>
        </table>
    </Content>

    <Forms>
        <ReboundForm:CompanyProspects_Edit ID="ctlCompanyProspects_Edit" runat="server" />
        <ReboundForm:MailMessages_MarkAsToDo id="ctlAdd" runat="server" />
    </Forms>

</ReboundUI_Nugget:DesignBase>
<style>
#dvCompletePecent{margin: 0px!important;
    text-align: center;
    border-bottom: 1px #ddd solid;
    margin-bottom: 10px;}
.twoCols{margin-top:10px;}
</style>
<script src="js/CompanyProspect.js"></script>
