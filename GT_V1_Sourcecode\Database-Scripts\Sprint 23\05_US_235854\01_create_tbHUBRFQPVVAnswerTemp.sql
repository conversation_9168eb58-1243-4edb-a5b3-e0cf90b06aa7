﻿/*
============================================================================================================================ 
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-235854]     CuongDox		 12-Mar-2025		CREATE		IPO- Simplified HUBRFQ Creation - Addition of PPV/ Bom Qualification at creation stage (Client Side)
============================================================================================================================  
*/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'tbHUBRFQPVVAnswerTemp')
BEGIN
	CREATE TABLE [dbo].[tbHUBRFQPVVAnswerTemp](
		[PVVAnswerId] [int] IDENTITY(1,1) NOT NULL,
		[PVVQuestionNo] [int] NOT NULL,
		[BomNoGenerated] [NVARCHAR](100) NULL,
		BomNo INT  NULL,
		HUBRFQNo INT  NULL,
		[PVVAnswerName] [nvarchar](max) NULL,
		[DLUP] [datetime] NOT NULL,
		[Inactive] [bit] NULL,
		[ClientNo] [int] NULL,
		[UpdatedBy] [int] NULL,
	PRIMARY KEY CLUSTERED 
	(
		[PVVAnswerId] ASC
	)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
	) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]


	ALTER TABLE [dbo].[tbHUBRFQPVVAnswerTemp] ADD  DEFAULT (getdate()) FOR [DLUP]

	ALTER TABLE [dbo].[tbHUBRFQPVVAnswerTemp] ADD  DEFAULT ((0)) FOR [Inactive]



END