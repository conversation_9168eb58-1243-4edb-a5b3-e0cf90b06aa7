//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option or rebuild the Visual Studio project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.VisualStudio.Web.Application.StronglyTypedResourceProxyBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class NotFound {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal NotFound() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Resources.NotFound", global::System.Reflection.Assembly.Load("App_GlobalResources"));
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to There are no Purchase Orders due in.
        /// </summary>
        internal static string AllPurchaseOrdersDueIn {
            get {
                return ResourceManager.GetString("AllPurchaseOrdersDueIn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to There are currently no approved customers on stop.
        /// </summary>
        internal static string ApprovedCustomersOnStop {
            get {
                return ResourceManager.GetString("ApprovedCustomersOnStop", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, that BOM cannot be found or you do not have the permissions to see it..
        /// </summary>
        internal static string BOM {
            get {
                return ResourceManager.GetString("BOM", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This HUBRFQ has no CSV documents.
        /// </summary>
        internal static string BOMCSV {
            get {
                return ResourceManager.GetString("BOMCSV", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Certificate has no pdf document.
        /// </summary>
        internal static string CIPDocumnetPDF {
            get {
                return ResourceManager.GetString("CIPDocumnetPDF", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, that Client Invoice cannot be found or you do not have the permissions to see it..
        /// </summary>
        internal static string ClientInvoiceDetail {
            get {
                return ResourceManager.GetString("ClientInvoiceDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to There is currently no Client Invoice Header Image.
        /// </summary>
        internal static string ClientInvoiceHeaderImage {
            get {
                return ResourceManager.GetString("ClientInvoiceHeaderImage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No communication log items.
        /// </summary>
        internal static string CommunicationLog {
            get {
                return ResourceManager.GetString("CommunicationLog", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, that Company cannot be found or you do not have the permissions to see it..
        /// </summary>
        internal static string Company {
            get {
                return ResourceManager.GetString("Company", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No global sales person assigned.
        /// </summary>
        internal static string CompanyGlobalSalesNoData {
            get {
                return ResourceManager.GetString("CompanyGlobalSalesNoData", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No manufacturers supplied.
        /// </summary>
        internal static string CompanyManufacturers {
            get {
                return ResourceManager.GetString("CompanyManufacturers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This Company has no PDF document.
        /// </summary>
        internal static string CompanyPDF {
            get {
                return ResourceManager.GetString("CompanyPDF", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, that Contact cannot be found or you do not have the permissions to see it..
        /// </summary>
        internal static string Contact {
            get {
                return ResourceManager.GetString("Contact", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, that Credit Note cannot be found or you do not have the permissions to see it..
        /// </summary>
        internal static string CreditNote {
            get {
                return ResourceManager.GetString("CreditNote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No invoice lines are available for adding to this Customer RMA. Use the &quot;Edit&quot; facility to change any line information..
        /// </summary>
        internal static string CRMANotAvailable {
            get {
                return ResourceManager.GetString("CRMANotAvailable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This CRMA currently has no lines awaiting receipt.
        /// </summary>
        internal static string CRMAReceivingLines {
            get {
                return ResourceManager.GetString("CRMAReceivingLines", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This Customer Requirement has no Sourcing Results.
        /// </summary>
        internal static string CusReqSourcingResults {
            get {
                return ResourceManager.GetString("CusReqSourcingResults", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, that Customer Requirement cannot be found.
        /// </summary>
        internal static string CustomerRequirement {
            get {
                return ResourceManager.GetString("CustomerRequirement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, that Customer RMA cannot be found or you do not have the permissions to see it..
        /// </summary>
        internal static string CustomerRMA {
            get {
                return ResourceManager.GetString("CustomerRMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This CRMA has no PDF document.
        /// </summary>
        internal static string CustomerRMAPDF {
            get {
                return ResourceManager.GetString("CustomerRMAPDF", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, that Debit Note cannot be found or you do not have the permissions to see it..
        /// </summary>
        internal static string DebitNote {
            get {
                return ResourceManager.GetString("DebitNote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This Division has no document header image.
        /// </summary>
        internal static string DivisionDocHeaderImage {
            get {
                return ResourceManager.GetString("DivisionDocHeaderImage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This Division has no members.
        /// </summary>
        internal static string DivisionMembers {
            get {
                return ResourceManager.GetString("DivisionMembers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to There is currently no Document Header Image.
        /// </summary>
        internal static string DocHeaderImage {
            get {
                return ResourceManager.GetString("DocHeaderImage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, that document cannot be found or you do not have the permissions to see it..
        /// </summary>
        internal static string Document {
            get {
                return ResourceManager.GetString("Document", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, that email status cannot be found or you do not have the permissions to see it..
        /// </summary>
        internal static string EmailStatus {
            get {
                return ResourceManager.GetString("EmailStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter the Company Name.
        /// </summary>
        internal static string EnterCompanyName {
            get {
                return ResourceManager.GetString("EnterCompanyName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, that EPR Log cannot be found or you do not have the permissions to see it..
        /// </summary>
        internal static string EPRlog {
            get {
                return ResourceManager.GetString("EPRlog", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This Sales Order Line does not have any End User Undertaking document.
        /// </summary>
        internal static string EUUDocumnetPDF {
            get {
                return ResourceManager.GetString("EUUDocumnetPDF", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to There were some problems with your form&lt;br /&gt;Please check the form and try again..
        /// </summary>
        internal static string FormProblems {
            get {
                return ResourceManager.GetString("FormProblems", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, no data was found.
        /// </summary>
        internal static string Generic {
            get {
                return ResourceManager.GetString("Generic", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, No data was found.
        /// </summary>
        internal static string GILineDocumnetImage {
            get {
                return ResourceManager.GetString("GILineDocumnetImage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, that Goods In Note cannot be found or you do not have the permissions to see it..
        /// </summary>
        internal static string GoodsIn {
            get {
                return ResourceManager.GetString("GoodsIn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This GoodsIn has no PDF document.
        /// </summary>
        internal static string GoodsInPDF {
            get {
                return ResourceManager.GetString("GoodsInPDF", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No global sales access users selected.
        /// </summary>
        internal static string GSA {
            get {
                return ResourceManager.GetString("GSA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GT.
        /// </summary>
        internal static string GT {
            get {
                return ResourceManager.GetString("GT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This ihs has no pdf document.
        /// </summary>
        internal static string IHSDocumnetPDF {
            get {
                return ResourceManager.GetString("IHSDocumnetPDF", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, that Invoice cannot be found or you do not have the permissions to see it..
        /// </summary>
        internal static string Invoice {
            get {
                return ResourceManager.GetString("Invoice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No sales order service lines are available for adding to this Invoice..
        /// </summary>
        internal static string InvoiceNotAvailable {
            get {
                return ResourceManager.GetString("InvoiceNotAvailable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This invoice has no pdf document.
        /// </summary>
        internal static string InvoicePDF {
            get {
                return ResourceManager.GetString("InvoicePDF", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This invoice has no POD pdf document.
        /// </summary>
        internal static string InvoicePODPDF {
            get {
                return ResourceManager.GetString("InvoicePODPDF", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, that Lot cannot be found or you do not have the permissions to see it..
        /// </summary>
        internal static string Lot {
            get {
                return ResourceManager.GetString("Lot", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, that message does not exist or you do not have permissions to see it.
        /// </summary>
        internal static string MailMessage {
            get {
                return ResourceManager.GetString("MailMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Mail Group has no members.
        /// </summary>
        internal static string MailMessageGroupMembers {
            get {
                return ResourceManager.GetString("MailMessageGroupMembers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, that Manufacturer cannot be found or you do not have the permissions to see it..
        /// </summary>
        internal static string Manufacturer {
            get {
                return ResourceManager.GetString("Manufacturer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This Manufacturer has no related companies.
        /// </summary>
        internal static string ManufacturerCompanies {
            get {
                return ResourceManager.GetString("ManufacturerCompanies", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This Manufacturer has no suppliers.
        /// </summary>
        internal static string ManufacturerSuppliers {
            get {
                return ResourceManager.GetString("ManufacturerSuppliers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This manufacturer has no Excel document.
        /// </summary>
        internal static string ManufatureEXCEL {
            get {
                return ResourceManager.GetString("ManufatureEXCEL", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This manufacturer has no pdf document.
        /// </summary>
        internal static string ManufaturePDF {
            get {
                return ResourceManager.GetString("ManufaturePDF", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to I currently have no approved customers on stop.
        /// </summary>
        internal static string MyApprovedCustomersOnStop {
            get {
                return ResourceManager.GetString("MyApprovedCustomersOnStop", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} currently has no approved customers on stop.
        /// </summary>
        internal static string MyApprovedCustomersOnStop_ForUser {
            get {
                return ResourceManager.GetString("MyApprovedCustomersOnStop_ForUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, No data was found.
        /// </summary>
        internal static string MyGIQueries {
            get {
                return ResourceManager.GetString("MyGIQueries", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to I currently have no Messages.
        /// </summary>
        internal static string MyMessages {
            get {
                return ResourceManager.GetString("MyMessages", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to I currently have no open Purchase Orders.
        /// </summary>
        internal static string MyOpenPurchaseOrders {
            get {
                return ResourceManager.GetString("MyOpenPurchaseOrders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} currently has no open Purchase Orders.
        /// </summary>
        internal static string MyOpenPurchaseOrders_ForUser {
            get {
                return ResourceManager.GetString("MyOpenPurchaseOrders_ForUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to I currently have no open Quotes.
        /// </summary>
        internal static string MyOpenQuotes {
            get {
                return ResourceManager.GetString("MyOpenQuotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} currently has no open Quotes.
        /// </summary>
        internal static string MyOpenQuotes_ForUser {
            get {
                return ResourceManager.GetString("MyOpenQuotes_ForUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to I currently have no open Requirements.
        /// </summary>
        internal static string MyOpenRequirements {
            get {
                return ResourceManager.GetString("MyOpenRequirements", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} currently has no open Requirements.
        /// </summary>
        internal static string MyOpenRequirements_ForUser {
            get {
                return ResourceManager.GetString("MyOpenRequirements_ForUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to I currently have no open Sales Orders.
        /// </summary>
        internal static string MyOpenSalesOrders {
            get {
                return ResourceManager.GetString("MyOpenSalesOrders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} currently has no open Sales Orders.
        /// </summary>
        internal static string MyOpenSalesOrders_ForUser {
            get {
                return ResourceManager.GetString("MyOpenSalesOrders_ForUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No GI Quality Approval pending at your end.
        /// </summary>
        internal static string MyQualityGIQueries {
            get {
                return ResourceManager.GetString("MyQualityGIQueries", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to I have no recent activity.
        /// </summary>
        internal static string MyRecentActivity {
            get {
                return ResourceManager.GetString("MyRecentActivity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} has no recent activity.
        /// </summary>
        internal static string MyRecentActivity_ForUser {
            get {
                return ResourceManager.GetString("MyRecentActivity_ForUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to I currently have no Recently Shipped Orders.
        /// </summary>
        internal static string MyRecentlyShippedOrders {
            get {
                return ResourceManager.GetString("MyRecentlyShippedOrders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} currently has no Recently Shipped Orders.
        /// </summary>
        internal static string MyRecentlyShippedOrders_ForUser {
            get {
                return ResourceManager.GetString("MyRecentlyShippedOrders_ForUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to I currently have no Scheduled Tasks.
        /// </summary>
        internal static string MyScheduledTasks {
            get {
                return ResourceManager.GetString("MyScheduledTasks", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0}  currently has no Scheduled Tasks.
        /// </summary>
        internal static string MyScheduledTasks_ForUser {
            get {
                return ResourceManager.GetString("MyScheduledTasks_ForUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to I currently have no items on my To Do list.
        /// </summary>
        internal static string MyToDoList {
            get {
                return ResourceManager.GetString("MyToDoList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No attachment found..
        /// </summary>
        internal static string NoAttachement {
            get {
                return ResourceManager.GetString("NoAttachement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, that NPR cannot be found or you do not have the permissions to see it..
        /// </summary>
        internal static string NPR {
            get {
                return ResourceManager.GetString("NPR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, that NPR Log cannot be found or you do not have the permissions to see it..
        /// </summary>
        internal static string NPRlog {
            get {
                return ResourceManager.GetString("NPRlog", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, No data was found.
        /// </summary>
        internal static string OpenSupplierPOApproval {
            get {
                return ResourceManager.GetString("OpenSupplierPOApproval", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, No data was found.
        /// </summary>
        internal static string OrdersDueOut {
            get {
                return ResourceManager.GetString("OrdersDueOut", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} has no Orders Due Out.
        /// </summary>
        internal static string OrdersDueOut_ForUser {
            get {
                return ResourceManager.GetString("OrdersDueOut_ForUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, that page cannot be found or you do not have the permissions to see it..
        /// </summary>
        internal static string Page {
            get {
                return ResourceManager.GetString("Page", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to There have been no parts ordered today.
        /// </summary>
        internal static string PartsBeingOrderedToday {
            get {
                return ResourceManager.GetString("PartsBeingOrderedToday", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} There have been no parts ordered today.
        /// </summary>
        internal static string PartsBeingOrderedToday_ForUser {
            get {
                return ResourceManager.GetString("PartsBeingOrderedToday_ForUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, no parts could be found to match your search.
        /// </summary>
        internal static string PartSearch {
            get {
                return ResourceManager.GetString("PartSearch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to There are currently no item to show..
        /// </summary>
        internal static string PODeliveryStatus {
            get {
                return ResourceManager.GetString("PODeliveryStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, no data was found.
        /// </summary>
        internal static string PONotAvailable {
            get {
                return ResourceManager.GetString("PONotAvailable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, that Purchase Quote cannot be found or you do not have the permissions to see it..
        /// </summary>
        internal static string POQuote {
            get {
                return ResourceManager.GetString("POQuote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This Purchase Order has no lines awaiting receipt.
        /// </summary>
        internal static string POReceivingLines {
            get {
                return ResourceManager.GetString("POReceivingLines", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, No activity was found.
        /// </summary>
        internal static string PowerBIActivity {
            get {
                return ResourceManager.GetString("PowerBIActivity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, No activity was found.
        /// </summary>
        internal static string PowerBIActivity_ForUser {
            get {
                return ResourceManager.GetString("PowerBIActivity_ForUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This Purchase Order has no POR.
        /// </summary>
        internal static string PO_PORPDFNew {
            get {
                return ResourceManager.GetString("PO_PORPDFNew", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, No data was found.
        /// </summary>
        internal static string PrintEmaillog {
            get {
                return ResourceManager.GetString("PrintEmaillog", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, that Purchase Order cannot be found or you do not have the permissions to see it..
        /// </summary>
        internal static string PurchaseOrder {
            get {
                return ResourceManager.GetString("PurchaseOrder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This Purchase Order has no PDF document.
        /// </summary>
        internal static string PurchaseOrderPDF {
            get {
                return ResourceManager.GetString("PurchaseOrderPDF", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This Price Request has no CSV document.
        /// </summary>
        internal static string PurchaseQuoteCSV {
            get {
                return ResourceManager.GetString("PurchaseQuoteCSV", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, that Purchase Requisition cannot be found or you do not have the permissions to see it..
        /// </summary>
        internal static string PurchaseRequisition {
            get {
                return ResourceManager.GetString("PurchaseRequisition", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, that Quote cannot be found or you do not have the permissions to see it..
        /// </summary>
        internal static string Quote {
            get {
                return ResourceManager.GetString("Quote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, No data was found.
        /// </summary>
        internal static string QuoteToClientImages {
            get {
                return ResourceManager.GetString("QuoteToClientImages", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to There are currently no Received Orders.
        /// </summary>
        internal static string ReceivedOrders {
            get {
                return ResourceManager.GetString("ReceivedOrders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, that Report cannot be found or you do not have the permissions to see it..
        /// </summary>
        internal static string Report {
            get {
                return ResourceManager.GetString("Report", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This report has no data for the parameters specified.
        /// </summary>
        internal static string ReportData {
            get {
                return ResourceManager.GetString("ReportData", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This Report has no parameters.
        /// </summary>
        internal static string ReportParameters {
            get {
                return ResourceManager.GetString("ReportParameters", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to There are currently no installed reports.
        /// </summary>
        internal static string Reports {
            get {
                return ResourceManager.GetString("Reports", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, that Sales Order cannot be found or you do not have the permissions to see it..
        /// </summary>
        internal static string SalesOrder {
            get {
                return ResourceManager.GetString("SalesOrder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This Sales Order has no PDF document.
        /// </summary>
        internal static string SalesOrderPDF {
            get {
                return ResourceManager.GetString("SalesOrderPDF", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to There are no Sales Orders ready to ship.
        /// </summary>
        internal static string SalesOrdersReadyToShip {
            get {
                return ResourceManager.GetString("SalesOrdersReadyToShip", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This Security Group has no members.
        /// </summary>
        internal static string SecurityGroupMembers {
            get {
                return ResourceManager.GetString("SecurityGroupMembers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to There are currently no permissions that can be set.
        /// </summary>
        internal static string SecurityGroupPermissionsGeneral {
            get {
                return ResourceManager.GetString("SecurityGroupPermissionsGeneral", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to There are currently no reports having permissions that can be set.
        /// </summary>
        internal static string SecurityGroupPermissionsReports {
            get {
                return ResourceManager.GetString("SecurityGroupPermissionsReports", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This User is not a member of any Security Groups - this will give them full access to the system.
        /// </summary>
        internal static string SecurityUserGroups {
            get {
                return ResourceManager.GetString("SecurityUserGroups", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, that Service cannot be found or you do not have the permissions to see it..
        /// </summary>
        internal static string Service {
            get {
                return ResourceManager.GetString("Service", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This Sales Order has no shipped lines.
        /// </summary>
        internal static string ShipSOMainInfo_ShippedLines {
            get {
                return ResourceManager.GetString("ShipSOMainInfo_ShippedLines", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This SO has no Excel or  doc file.
        /// </summary>
        internal static string SOEXCELDOC {
            get {
                return ResourceManager.GetString("SOEXCELDOC", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, No data was found.
        /// </summary>
        internal static string SOImages {
            get {
                return ResourceManager.GetString("SOImages", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This Sales Order Line has no allocations.
        /// </summary>
        internal static string SOLineAllocations {
            get {
                return ResourceManager.GetString("SOLineAllocations", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This Sales Order Line has no shipped lines.
        /// </summary>
        internal static string SOLinesShipped {
            get {
                return ResourceManager.GetString("SOLinesShipped", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This Sales Order has no lines awaiting shipment.
        /// </summary>
        internal static string SOShippingLines {
            get {
                return ResourceManager.GetString("SOShippingLines", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This Sales Order has no payment file attached..
        /// </summary>
        internal static string SOShowPaymentFiles {
            get {
                return ResourceManager.GetString("SOShowPaymentFiles", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No Sourcing Links.
        /// </summary>
        internal static string SourcingLinks {
            get {
                return ResourceManager.GetString("SourcingLinks", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This Sales Order has no Customer PO.
        /// </summary>
        internal static string SO_SORPDF {
            get {
                return ResourceManager.GetString("SO_SORPDF", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This Sales Order has no SOR.
        /// </summary>
        internal static string SO_SORPDFNew {
            get {
                return ResourceManager.GetString("SO_SORPDFNew", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No purchase order lines are available for adding to this Supplier RMA. Use the &quot;Edit&quot; facility to change any line information..
        /// </summary>
        internal static string SRMANotAvailable {
            get {
                return ResourceManager.GetString("SRMANotAvailable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This SRMA currently has no lines awaiting shipment.
        /// </summary>
        internal static string SRMAShippingLines {
            get {
                return ResourceManager.GetString("SRMAShippingLines", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, that Stock Item cannot be found or you do not have the permissions to see it..
        /// </summary>
        internal static string Stock {
            get {
                return ResourceManager.GetString("Stock", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This Stock has no allocations.
        /// </summary>
        internal static string StockAllocations {
            get {
                return ResourceManager.GetString("StockAllocations", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, that image was not found.
        /// </summary>
        internal static string StockImage {
            get {
                return ResourceManager.GetString("StockImage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This Stock has no images.
        /// </summary>
        internal static string StockImages {
            get {
                return ResourceManager.GetString("StockImages", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This Stock has no PDF document.
        /// </summary>
        internal static string StockPDF {
            get {
                return ResourceManager.GetString("StockPDF", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, that Supplier Invoice cannot be found or you do not have the permissions to see it..
        /// </summary>
        internal static string SupplierInvoiceDetail {
            get {
                return ResourceManager.GetString("SupplierInvoiceDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This Supplier Invoice has no PDF document.
        /// </summary>
        internal static string SupplierInvoicePDF {
            get {
                return ResourceManager.GetString("SupplierInvoicePDF", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, that Supplier RMA cannot be found or you do not have the permissions to see it..
        /// </summary>
        internal static string SupplierRMA {
            get {
                return ResourceManager.GetString("SupplierRMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This SRMA has no PDF document.
        /// </summary>
        internal static string SupplierRMAPDF {
            get {
                return ResourceManager.GetString("SupplierRMAPDF", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This Team has no members.
        /// </summary>
        internal static string TeamMembers {
            get {
                return ResourceManager.GetString("TeamMembers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Today Open Purchase Orders.
        /// </summary>
        internal static string TodayOpenPurchaseOrders {
            get {
                return ResourceManager.GetString("TodayOpenPurchaseOrders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to There are no Shipped Orders today.
        /// </summary>
        internal static string TodaysShippedOrders {
            get {
                return ResourceManager.GetString("TodaysShippedOrders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The Top Salespeople cannot be established .
        /// </summary>
        internal static string TopSalespersons {
            get {
                return ResourceManager.GetString("TopSalespersons", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to There have been no IPO created today.
        /// </summary>
        internal static string UncheckedIPO {
            get {
                return ResourceManager.GetString("UncheckedIPO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Un Process Sales Orders.
        /// </summary>
        internal static string UnProcessSalesOrders {
            get {
                return ResourceManager.GetString("UnProcessSalesOrders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, No data was found.
        /// </summary>
        internal static string UploadExcelDragDrop {
            get {
                return ResourceManager.GetString("UploadExcelDragDrop", resourceCulture);
            }
        }
    }
}
