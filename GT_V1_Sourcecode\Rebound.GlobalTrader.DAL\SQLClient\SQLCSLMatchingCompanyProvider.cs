﻿//Marker     changed by      date         Remarks
//[001]      Arpit           03/03/2023   RP-257

using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Data.Common;

namespace Rebound.GlobalTrader.DAL.SqlClient
{
    public class SQLCSLMatchingCompanyProvider : CSLMatchingCompanyProvider
    {
        //[001]
        public override List<CSLMatchingCompanyDetails> GetCSVImportedDate(string type)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                var sp = "";
                switch (type)
                {
                    case "CSL":
                        sp = "usp_CSL_CSVImportedDate";
                        break;
                    case "EU":
                        sp = "usp_CSL_CSVImportedDateEU";
                        break;
                    case "All":
                        sp = "usp_CSL_CSVImportedDateAll";
                        break;
                }
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand(sp, cn)
                {
                    CommandType = CommandType.StoredProcedure,
                    CommandTimeout = 60    // in seconds
                };
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<CSLMatchingCompanyDetails> lst = new List<CSLMatchingCompanyDetails>();
                while (reader.Read())
                {
                    CSLMatchingCompanyDetails obj = new CSLMatchingCompanyDetails
                    {
                        CSL_DateInserted = GetReaderValue_String(reader, "insertedon", "")
                    };
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to get CSV inserted date", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        //[001]
        public override List<CSLMatchingCompanyDetails> GetListCSLMatchingCompany(string dateInput, string type)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                var sp = "";
                switch (type)
                {
                    case "CSL":
                        sp = "usp_Select_CSVMatchingCompanyDataByDate";
                        break;
                    case "EU":
                        sp = "usp_Select_CSVMatchingCompanyDataByDateEU";
                        break;
                    case "All":
                        sp = "usp_Select_CSVMatchingCompanyDataByDateAll";
                        break;
                }
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand(sp, cn)
                {
                    CommandType = CommandType.StoredProcedure,
                    CommandTimeout = 60
                };
                cmd.Parameters.Add("@InputDate", SqlDbType.VarChar).Value = dateInput;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<CSLMatchingCompanyDetails> lst = new List<CSLMatchingCompanyDetails>();
                while (reader.Read())
                {
                    CSLMatchingCompanyDetails obj = new CSLMatchingCompanyDetails();
                    obj.CompanyId = GetReaderValue_Int32(reader, "companyid", 0);
                    obj.CustomerCode = GetReaderValue_String(reader, "CustomerCode", "");
                    obj.ClientNo = GetReaderValue_Int32(reader, "ClientNo", 0);
                    obj.ClientName = GetReaderValue_String(reader, "ClientName", "");
                    obj.CompanyName = GetReaderValue_String(reader, "CompanyName", "");
                    obj.GT_Company_Address = GetReaderValue_String(reader, "GT_Company_Address", "");
                    obj.CSL_Name = GetReaderValue_String(reader, "CSL_Name", "");
                    obj.CSL_Address = GetReaderValue_String(reader, "CSL_Address", "");
                    obj.CSL_ALT_Name = GetReaderValue_String(reader, "CSL_ALT_Name", "");
                    obj.Notes = GetReaderValue_String(reader, "General_customer_info", "");
                    obj.ImportantNotes = GetReaderValue_String(reader, "Accounts_notes", "");
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get CSL matching company", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override List<CSLEUSearchDetails> GetListCSLEU(string name, string type, string address, string country, bool isFuzzyName)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                var sp = "";
                switch (isFuzzyName)
                {
                    case true:
                        sp = "usp_Select_CSVSantionedEUList_Fuzzy";
                        break;
                    case false:
                        sp = "usp_Select_CSVSantionedEUList";
                        break;
                }
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand(sp, cn)
                {
                    CommandType = CommandType.StoredProcedure,
                    CommandTimeout = 60
                };
                cmd.Parameters.Add("@Name", SqlDbType.VarChar).Value = name;
                cmd.Parameters.Add("@Type", SqlDbType.VarChar).Value = type;
                cmd.Parameters.Add("@Address", SqlDbType.VarChar).Value = address;
                cmd.Parameters.Add("@Country", SqlDbType.VarChar).Value = country;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<CSLEUSearchDetails> lst = new List<CSLEUSearchDetails>();
                while (reader.Read())
                {
                    CSLEUSearchDetails obj = new CSLEUSearchDetails();
                    obj.Program = GetReaderValue_String(reader, "EntityRegulationProgramme", "");
                    obj.IDs = GetReaderValue_String(reader, "IdentificationNumber", "");
                    obj.Remark = GetReaderValue_String(reader, "EntityRemark", "");
                    obj.Type = GetReaderValue_String(reader, "EntitySubjectTypeClassificationCode", "");
                    obj.CompanyName = GetReaderValue_String(reader, "NameAliasWholeName", "");
                    obj.Address = GetReaderValue_String(reader, "Address", "");
                    obj.BirthDate = GetReaderValue_String(reader, "BirthDateBirthDate", "");
                    obj.BirthDatePlace = GetReaderValue_String(reader, "BirthDatePlace", "");
                    obj.Nationalities = GetReaderValue_String(reader, "BirthDateCountryDescription", "");
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get CSL matching company", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
    }
}
