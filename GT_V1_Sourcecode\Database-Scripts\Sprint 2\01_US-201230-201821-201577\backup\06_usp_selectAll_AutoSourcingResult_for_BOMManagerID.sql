SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].[usp_selectAll_AutoSourcingResult_for_BOMManagerID]      
                                                                                                                                
@BOMManagerID int,                                              
@CustomerReqID int,                                              
@IsPOHUB bit = 0                    
, @curPage int = 1                    
, @Rpp int = 5                    
AS                                                                                                                      
                                                                                                                    
DECLARE @BOMCurrencyNo INT                                                                                                                    
DECLARE @BOMCurrencyCode NVARCHAR(5)                                                                                                                    
                                                                  
DECLARE @ClientNo int                                                                  
SET @ClientNo=(SELECT DISTINCT(Clientno) from tbCustomerRequirement where CustomerRequirementId=@CustomerReqID and BOMManagerNo = @BOMManagerID)                                                                  
                                                                                                                                                                      
 declare @TotalRecords int, @skip int                    
                    
      select @TotalRecords = count(sr.SourcingResultId) FROM tbSourcingResult sr                    
        left join tbBOMManager bm on bm.BOMManagerId = @BOMManagerID  
  join dbo.tbautosource tbas on sr.autosourceid = tbas.sourceid           
  WHERE    sr.CustomerRequirementNo = @CustomerReqID          
     and isnull(tbas.isdeleted,0)=0                  
     and isnull(sr.IsSoftDelete,0)= 0                
     AND(bm.[Status] >= 4)  
                        
 set @skip = (@Rpp * (@curPage - 1))                    
                    
 if (@skip >= @TotalRecords and @TotalRecords > 0)                    
 begin                    
  set @curPage = CAST(@TotalRecords/@Rpp as int)                    
  set @skip = CAST((@Rpp * (@curPage - 1)) as int)                     
 end                    
                     
SELECT                     
   sr.CustomerRequirementNo as CustomerRequirementId                                      
  ,sr.SourcingResultId                                                                                                                                                 
  , sr.FullPart                                                                                                                                
  , sr.Part                                                                                                                                
  , sr.ManufacturerNo                                                                                                                                
  , sr.DateCode                                                                                                                                
  , sr.ProductNo                                                                                                                                
  , sr.PackageNo                                                                                                                                
  , cr.Quantity                                                                                                                                                        
  , sr.ActualPrice          
  , sr.ActualCurrencyNo          
  , (select CurrencyCode from tbCurrency where CurrencyId = sr.ActualCurrencyNo) as ActualCurrencyCode          
  , sr.Salesman                                                                                                                
  , sr.UpdatedBy                                                                                                
  , sr.DLUP                                                          
  , sr.ROHS                                                                                            
  , sr.Notes                                                           
  , mf.ManufacturerName                         
  , mf.ManufacturerCode                              
  --, (select CurrencyCode from tbCurrency where CurrencyId = bm.CurrencyNo) as CurrencyCode                                                                                 
  , 'Rebound Electronics International Services DMCC' AS SupplierName                                                                                                                      
  , pr.ProductName                                                                                                                                
  , pr.ProductDescription                                                                                                                     
  , pk.PackageName                                                                                                                                
  , pk.PackageDescription                                                                                                                                     
  , sr.Price          
  , sr.CurrencyNo          
  , (select CurrencyCode from tbCurrency where CurrencyId = sr.CurrencyNo)as CurrencyCode          
  , cr.POHubReleaseBy                                                                                                          
  , sr.ClientCompanyNo                                                                                                                                                                       
  , bm.ClientCurrencyNo                                                                                                                       
  , (select CurrencyCode from tbCurrency where CurrencyId = bm.ClientCurrencyNo) as ClientCurrencyCode                                                                                                                                                                   
  , mf.ManufacturerName                                                                                                        
  , sr.DateCode                                                                                                               
  , sr.SupplierMOQ                                                                                                                                                                                    
  , sr.SPQ                                                                                                                                                            
  ,sr.MSL                                                                                                                                                                               
  ,cr.ClientNo                                                                       
  --, cub.CurrencyCode as BuyCurrencyCode                                                             
  , sr.CurrencyNo                                                                                         
  ,(SELECT COUNT(1) FROM tbAutoSource where ISNULL(BOMStatus,0)=1 and BOMManagerNo = @BOMManagerID and isnull(IsDeleted,0)=-0 ) as SourcingReleasedCount                                                                                                       
  
    
      
        
                           
  ,cr.CountryOfOriginNo as IHSCountryOfOriginNo                                                              
  ,cro.GlobalCountryName  as IHSCountryOfOriginName                                                                                                              
  ,(select rh.Description from tbROHSStatus rh where rh.ROHSStatusId=sr.ROHS) as ROHSDescription                                                  
  --,sr.PartWatchMatchHUBIPO                                               
  ,cr.DatePromised as DeliveryDate                
  ,(select EmployeeName from tbLogin where LoginId = cr.POHubReleaseBy) as SalesmanName                                          
  ,cr.DatePOHubRelease as OfferDate                                  
  ,cr.REQStatus                                
  ,ql.QuoteNo as QuoteID                                
  ,qt.QuoteNumber               
  --,(Select top 1 up.UpliftPercentage from tbUpliftPercentage_BOMManager up where up.CustomerRequirementNo = sr.CustomerRequirementNo and isnull(up.IsDeleted,0)=0) as UpliftPercentage                    
  ,sr.IsPrimarySourcing                    
  ,@TotalRecords as TotalRecords              
  ,rg.RegionName              
  ,tct.[Name] as SupplierType          
  --,  tbas.isdeleted,sr.IsSoftDelete        
  --,tbas.sourceid        
 FROM    dbo.tbSourcingResult sr                     
 join dbo.tbautosource tbas on sr.autosourceid = tbas.sourceid                  
 LEFT                                                                     
 JOIN dbo.tbManufacturer mf                                                                      
 ON sr.ManufacturerNo   = mf.ManufacturerID                                                                                                               
 LEFT                                                                                                                     
 JOIN dbo.tbCurrency cu                                                                                                                                 
 ON sr.CurrencyNo    = cu.CurrencyID                                                                                                                                                                                                                          
  
    
       
 LEFT                                                                             
 JOIN dbo.tbProduct pr                                                                       
 ON sr.ProductNo    = pr.ProductId                                                                                                                                
 LEFT JOIN dbo.tbPackage pk ON sr.PackageNo    = pk.PackageId                                                                                                                                                                                                                           
 
 LEFT                                                        
 JOIN dbo.tbCompany coc                                                                               
 ON sr.SupplierNo = coc.CompanyId              
left join tbcompanytype tct on tct.CompanyTypeId = coc.TypeNo              
left join tbCompanyAddress ca on ca.CompanyNo = coc.CompanyId and ca.DefaultBilling = 1             
left join tbAddress ad on ad.AddressId = ca.AddressNo              
left join tbRegion rg on rg.RegionId = ad.RegionNo              
 LEFT JOIN  dbo.tbCustomerRequirement cr on cr.CustomerRequirementId = sr.CustomerRequirementNo                                                                                                                           
 --LEFT JOIN tbCurrency cur on isnull(sr.ClientCurrencyNo,0) = cur.CurrencyId                                                                                                                                  
 --LEFT JOIN tbCurrency cub on isnull(sr.ClientCurrencyNo,0) = cub.CurrencyId                                                                                                                                             
 left join  tbGlobalCountryList cro on cr.CountryOfOriginNo=cro.GlobalCountryId                                              
 left join tbBOMManager bm on bm.BOMManagerId = @BOMManagerID                                
 left join tbQuoteLine ql on ql.SourcingResultNo = sr.SourcingResultId                                
 left join tbQuote qt on qt.QuoteId = ql.QuoteNo                              
 WHERE   sr.CustomerRequirementNo = @CustomerReqID          
 and isnull(tbas.isdeleted,0)=0                  
 and isnull(sr.IsSoftDelete,0)= 0                
 AND(bm.[Status] >= 4)                     
ORDER BY cr.[sequence]         
  OFFSET @skip ROWS                                      
  FETCH NEXT @Rpp ROWS ONLY       
GO


