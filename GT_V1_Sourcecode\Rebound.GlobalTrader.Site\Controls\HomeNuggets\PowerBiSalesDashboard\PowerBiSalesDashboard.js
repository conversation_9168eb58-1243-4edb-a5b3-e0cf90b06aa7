Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.HomeNuggets");Rebound.GlobalTrader.Site.Controls.HomeNuggets.PowerBiSalesDashboard=function(n){Rebound.GlobalTrader.Site.Controls.HomeNuggets.PowerBiSalesDashboard.initializeBase(this,[n]);this._tblSalesDashboard=null;this._powerBiUrl=null};Rebound.GlobalTrader.Site.Controls.HomeNuggets.PowerBiSalesDashboard.prototype={get_powerBiUrl:function(){return this._powerBiUrl},set_powerBiUrl:function(n){this._powerBiUrl!==n&&(this._powerBiUrl=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.HomeNuggets.PowerBiSalesDashboard.callBaseMethod(this,"initialize");this.hideLoading();$("#ctl00_cphMain_ctlToday_PowerBiSalesDashboard_ctlDB_pnlContent").removeClass("invisible");$("#showSalesDashboard").click(function(){var n=window.location.pathname.replace("Default.aspx","");AbsoluteURL=window.location.origin;handlerUrl=window.location.origin+"/controls/HomeNuggets/PowerBiSalesDashboard/PowerBiSalesDashboard.ashx";$.ajax({processData:!0,contentType:"application/json",type:"POST",url:handlerUrl+"?Action=ReturnURL",dataType:"text",async:!1,success:function(n){var t=n.replaceAll("'",'"'),i=JSON.stringify(t),r=JSON.parse(i),u=JSON.parse(r);powerBiURL=u.url;AddToken(powerBiURL,3)},error:function(n,t,i){alert(n.status);alert(i)}})});this.getData();this.addRefreshEvent(Function.createDelegate(this,this.getData))},dispose:function(){this.isDisposed||Rebound.GlobalTrader.Site.Controls.HomeNuggets.PowerBiSalesDashboard.callBaseMethod(this,"dispose")},setDefaultTextinTable:function(){},showNoData:function(){},getData:function(){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/HomeNuggets/PowerBiSalesDashboard");n.set_DataObject("PowerBiSalesDashboard");n.set_DataAction("GetData");n.addDataOK(Function.createDelegate(this,this.getDataComplete));n.addError(Function.createDelegate(this,this.homeNuggetDataError));n.addTimeout(Function.createDelegate(this,this.homeNuggetDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getDataComplete:function(n){var t=n._result;t.PowerBiPermission==!0?$("#ctl00_cphMain_ctlToday_PowerBiSalesDashboard_ctlDB_pnlContent").removeClass("invisible"):$("#ctl00_cphMain_ctlToday_PowerBiSalesDashboard_ctlDB_pnlContent").addClass("invisible")},homeNuggetDataError:function(){}};Rebound.GlobalTrader.Site.Controls.HomeNuggets.PowerBiSalesDashboard.registerClass("Rebound.GlobalTrader.Site.Controls.HomeNuggets.PowerBiSalesDashboard",Rebound.GlobalTrader.Site.Controls.HomeNuggets.Base,Sys.IDisposable);