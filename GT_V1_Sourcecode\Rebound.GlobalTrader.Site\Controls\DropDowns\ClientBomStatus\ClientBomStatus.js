Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.ClientBomStatus=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.ClientBomStatus.initializeBase(this,[n]);this._intCompanyID=null};Rebound.GlobalTrader.Site.Controls.DropDowns.ClientBomStatus.prototype={get_intCompanyID:function(){return this._intCompanyID},set_intCompanyID:function(n){this._intCompanyID!==n&&(this._intCompanyID=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.DropDowns.ClientBomStatus.callBaseMethod(this,"initialize");this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK))},dispose:function(){this.isDisposed||Rebound.GlobalTrader.Site.Controls.DropDowns.ClientBomStatus.callBaseMethod(this,"dispose")},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/ClientBomStatus");this._objData.set_DataObject("ClientBomStatus");this._objData.set_DataAction("GetData");this._objData.addParameter("id",this._intCompanyID)},dataCallOK:function(){var t=this._objData._result,n;if(t.ClientBomStatus)for(n=0;n<t.ClientBomStatus.length;n++)this.addOption(t.ClientBomStatus[n].BOMStatusName,t.ClientBomStatus[n].BOMStatusId)}};Rebound.GlobalTrader.Site.Controls.DropDowns.ClientBomStatus.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.ClientBomStatus",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);