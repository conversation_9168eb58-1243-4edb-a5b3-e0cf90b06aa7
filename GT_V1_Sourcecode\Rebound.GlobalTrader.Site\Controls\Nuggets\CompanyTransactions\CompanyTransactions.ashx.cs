using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets.Data {
	[WebService(Namespace = "http://tempuri.org/")]
	[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
	public class CompanyTransactions : Rebound.GlobalTrader.Site.Data.Base {

		public override void ProcessRequest(HttpContext context) {
			if (base.init(context)) {
				switch (Action) {
					case "GetCounts": GetCounts(); break;
					case "GetCustomerRequirementData": GetCustomerRequirementData(); break;
					case "GetCustomerBOMsData": GetCustomerBOMsData(); break;
					case "GetQuotesData": GetQuotesData(); break;
					case "GetSalesOrderData": GetSalesOrderData(); break;
					case "GetPurchaseOrderData": GetPurchaseOrderData(); break;
					case "GetSupplierRMAData": GetSupplierRMAData(); break;
					case "GetCustomerRMAData": GetCustomerRMAData(); break;
					case "GetInvoiceData": GetInvoiceData(); break;
					case "GetCreditNoteDate": GetCreditNoteData(); break;
					case "GetDebitNoteData": GetDebitNoteData(); break;
					default: WriteErrorActionNotFound(); break;
				}
			}
		}

		private void GetCounts() {
			JsonObject jsn = new JsonObject();
			jsn.AddVariable("Requirements", CustomerRequirement.CountForCompany(ID, GetFormValue_Boolean("IncludeClosed")));
			jsn.AddVariable("BOMs", BOMManagerContract.CountForCompany(ID, GetFormValue_Boolean("IncludeClosed")));
			jsn.AddVariable("Quotes", Quote.CountForCompany(ID, GetFormValue_Boolean("IncludeClosed")));
			jsn.AddVariable("SalesOrders", SalesOrder.CountForCompany(ID, GetFormValue_Boolean("IncludeClosed")));
			jsn.AddVariable("Invoices", Invoice.CountForCompany(ID, GetFormValue_Boolean("IncludeClosed")));
			jsn.AddVariable("PurchaseOrders", PurchaseOrder.CountForCompany(ID, GetFormValue_Boolean("IncludeClosed")));
			jsn.AddVariable("SupplierRMAs", SupplierRma.CountForCompany(ID, GetFormValue_Boolean("IncludeClosed")));
			jsn.AddVariable("CustomerRMAs", CustomerRma.CountForCompany(ID, GetFormValue_Boolean("IncludeClosed")));
			jsn.AddVariable("CreditNotes", Credit.CountForCompany(ID));
			jsn.AddVariable("DebitNotes", Debit.CountForCompany(ID));
			OutputResult(jsn);
			jsn.Dispose(); jsn = null;
		}

		/// <summary>
		/// Gets the customerRequirement data
		/// </summary>
		private void GetCustomerRequirementData() {
			List<CustomerRequirement> lst = CustomerRequirement.GetListForCompany(ID, GetFormValue_Boolean("IncludeClosed"));
			JsonObject jsn = new JsonObject();
			JsonObject jsnItems = new JsonObject(true);
			foreach (CustomerRequirement cr in lst) {
				JsonObject jsnItem = new JsonObject();
				jsnItem.AddVariable("ID", cr.CustomerRequirementId);
				jsnItem.AddVariable("No", cr.CustomerRequirementNumber);
				jsnItem.AddVariable("ReceivedDate", Functions.FormatDate(cr.ReceivedDate));
				jsnItem.AddVariable("Part",Functions.ReplaceLineBreaks(cr.Part));
				jsnItem.AddVariable("Quantity", cr.Quantity);
				jsnItem.AddVariable("Price", Functions.FormatCurrency(cr.Price, cr.CurrencyCode,5,true));
				jsnItems.AddVariable(jsnItem);
				jsnItem.Dispose(); jsnItem = null;
			}
			jsn.AddVariable("Items", jsnItems);
			jsnItems.Dispose(); jsnItems = null;
			OutputResult(jsn);
			jsn.Dispose(); jsn = null;
		}

		/// <summary>
		/// Gets the customerRequirement data
		/// </summary>
		/// </summary>
		private void GetCustomerBOMsData()
		{
			List<BOMManagerContract> lst = BOMManagerContract.GetListForCompany(SessionManager.ClientID , ID, GetFormValue_Boolean("IncludeClosed"));
			JsonObject jsn = new JsonObject();
			JsonObject jsnItems = new JsonObject(true);
			foreach (BOMManagerContract bmc in lst)
			{
				JsonObject jsnItem = new JsonObject();
				jsnItem.AddVariable("ID", bmc.BOMManagerId);
				jsnItem.AddVariable("BOMCode", bmc.BOMManagerCode);
				jsnItem.AddVariable("BOMName", bmc.BOMManagerName);
				jsnItem.AddVariable("ReceivedDate", Functions.FormatDate(bmc.ReceivedDate));
				jsnItem.AddVariable("Salesperson", bmc.SalesmanName);
				jsnItem.AddVariable("Status", bmc.BOMManagerStatus);
				if (SessionManager.IsPOHub == true)
				{
					jsnItem.AddVariable("TotalValue", Functions.FormatCurrency(BLL.Currency.ConvertValueToBaseCurrency(bmc.TotalBomManagerLinePrice, (int)SessionManager.ClientCurrencyID, (bmc.DateRequestToPOHub.HasValue) ? bmc.DateRequestToPOHub.Value : bmc.DLUP), SessionManager.ClientCurrencyCode, 2));
				}
				else
				{
					jsnItem.AddVariable("TotalValue", Functions.FormatCurrency(BLL.Currency.ConvertValueToBaseCurrency(bmc.TotalBomManagerLinePrice, (int)bmc.POCurrencyNo, (bmc.DateRequestToPOHub.HasValue) ? bmc.DateRequestToPOHub.Value : bmc.DLUP), SessionManager.ClientCurrencyCode, 2));
				}
				
				jsnItems.AddVariable(jsnItem);
				jsnItem.Dispose(); 
				jsnItem = null;
			}
			jsn.AddVariable("Items", jsnItems);
			jsnItems.Dispose(); jsnItems = null;
			OutputResult(jsn);
			jsn.Dispose(); jsn = null;
		}

		/// <summary>
		/// Gets the quotation data
		/// </summary>
		private void GetQuotesData() {
			List<Quote> lst = Quote.GetListForCompany(ID, GetFormValue_Boolean("IncludeClosed"));
			JsonObject jsn = new JsonObject();
			JsonObject jsnItems = new JsonObject(true);
			foreach (Quote qu in lst) {
				JsonObject jsnItem = new JsonObject();
				jsnItem.AddVariable("ID", qu.QuoteId);
				jsnItem.AddVariable("No", qu.QuoteNumber);
				jsnItem.AddVariable("Date", Functions.FormatDate(qu.DateQuoted));
				jsnItem.AddVariable("Value", Functions.FormatCurrency(qu.QuoteValue, qu.CurrencyCode, 2,true));
				jsnItem.AddVariable("Salesman", qu.SalesmanName);
				jsnItems.AddVariable(jsnItem);
				jsnItem.Dispose(); jsnItem = null;
			}
			jsn.AddVariable("Items", jsnItems);
			OutputResult(jsn);
			jsnItems.Dispose(); jsnItems = null;
			jsn.Dispose(); jsn = null;
		}

		/// <summary>
		/// Gets the salesOrder data
		/// </summary>
		private void GetSalesOrderData() {
			List<SalesOrder> lst = SalesOrder.GetListForCompany(ID, GetFormValue_Boolean("IncludeClosed"));
			JsonObject jsn = new JsonObject();
			JsonObject jsnItems = new JsonObject(true);
			foreach (SalesOrder so in lst) {
				JsonObject jsnItem = new JsonObject();
				jsnItem.AddVariable("ID", so.SalesOrderId);
				jsnItem.AddVariable("No", so.SalesOrderNumber);
				jsnItem.AddVariable("Date", Functions.FormatDate(so.DateOrdered));
				SalesOrder SOTotal = SalesOrder.GetSummaryValues(so.SalesOrderId);
				jsnItem.AddVariable("Value", Functions.FormatCurrency(SOTotal.TotalValue, so.CurrencyCode, 2));
				SOTotal = null;
				jsnItem.AddVariable("Salesman", so.SalesmanName);
                jsnItem.AddVariable("CustomerPO", so.CustomerPO);
                jsnItems.AddVariable(jsnItem);
				jsnItem.Dispose(); jsnItem = null;
			}
			jsn.AddVariable("Items", jsnItems);
			OutputResult(jsn);
			jsnItems.Dispose(); jsnItems = null;
			jsn.Dispose(); jsn = null;
		}

		/// <summary>
		/// Gets the purchaseOrder data
		/// </summary>
		private void GetPurchaseOrderData() {
			List<PurchaseOrder> lst = PurchaseOrder.GetListForCompany(ID, GetFormValue_Boolean("IncludeClosed"));
			JsonObject jsn = new JsonObject();
			JsonObject jsnItems = new JsonObject(true);
			foreach (PurchaseOrder po in lst) {
				JsonObject jsnItem = new JsonObject();
				jsnItem.AddVariable("ID", po.PurchaseOrderId);
				jsnItem.AddVariable("No", po.PurchaseOrderNumber);
				jsnItem.AddVariable("Date", Functions.FormatDate(po.DateOrdered));
				jsnItem.AddVariable("Value", Functions.FormatCurrency(po.PurchaseOrderValue, po.CurrencyCode, 2,true));
				jsnItem.AddVariable("Buyer", po.BuyerName);
				jsnItems.AddVariable(jsnItem);
				jsnItem.Dispose(); jsnItem = null;
			}
			jsn.AddVariable("Items", jsnItems);
			OutputResult(jsn);
			jsnItems.Dispose(); jsnItems = null;
			jsn.Dispose(); jsn = null;
		}

		/// <summary>
		/// Gets the supplierRMA data
		/// </summary>
		private void GetSupplierRMAData() {
			List<SupplierRma> lst = SupplierRma.GetListForCompany(ID, GetFormValue_Boolean("IncludeClosed"));
			JsonObject jsn = new JsonObject();
			JsonObject jsnItems = new JsonObject(true);
			foreach (SupplierRma srma in lst) {
				JsonObject jsnItem = new JsonObject();
				jsnItem.AddVariable("ID", srma.SupplierRMAId);
				jsnItem.AddVariable("No", srma.SupplierRMANumber);
				jsnItem.AddVariable("Date", Functions.FormatDate(srma.SupplierRMADate));
				jsnItem.AddVariable("PONo", srma.PurchaseOrderNo);
				jsnItem.AddVariable("PONumber", srma.PurchaseOrderNumber);
				jsnItem.AddVariable("Authoriser", srma.AuthoriserName);
				jsnItem.AddVariable("Contact", srma.ContactName);
				jsnItem.AddVariable("ContactNo", srma.ContactNo);
				jsnItems.AddVariable(jsnItem);
				jsnItem.Dispose(); jsnItem = null;
			}
			jsn.AddVariable("Items", jsnItems);
			OutputResult(jsn);
			jsnItems.Dispose(); jsnItems = null;
			jsn.Dispose(); jsn = null;
		}

		/// <summary>
		/// Gets the customerRMA data
		/// </summary>
		private void GetCustomerRMAData() {
			List<CustomerRma> lst = CustomerRma.GetListForCompany(ID, GetFormValue_Boolean("IncludeClosed"));
			JsonObject jsn = new JsonObject();
			JsonObject jsnItems = new JsonObject(true);
			foreach (CustomerRma crma in lst) {
				JsonObject jsnItem = new JsonObject();
				jsnItem.AddVariable("ID", crma.CustomerRMAId);
				jsnItem.AddVariable("No", crma.CustomerRMANumber);
				jsnItem.AddVariable("Date", Functions.FormatDate(crma.CustomerRMADate));
				jsnItem.AddVariable("InvoiceNo", crma.InvoiceNo);
				jsnItem.AddVariable("InvoiceNumber", crma.InvoiceNumber);
				jsnItem.AddVariable("Authoriser", crma.AuthoriserName);
				jsnItem.AddVariable("Contact", crma.ContactName);
				jsnItem.AddVariable("ContactNo", crma.ContactNo);
				jsnItems.AddVariable(jsnItem);
				jsnItem.Dispose(); jsnItem = null;
			}
			jsn.AddVariable("Items", jsnItems);
			OutputResult(jsn);
			jsnItems.Dispose(); jsnItems = null;
			jsn.Dispose(); jsn = null;
		}

		private void GetDebitNoteData() {
			List<Debit> lst = Debit.GetListForCompany(ID);
			JsonObject jsn = new JsonObject();
			JsonObject jsnItems = new JsonObject(true);
			foreach (Debit db in lst) {
				JsonObject jsnItem = new JsonObject();
				jsnItem.AddVariable("ID", db.DebitId);
				jsnItem.AddVariable("No", db.DebitNumber);
				jsnItem.AddVariable("Date", Functions.FormatDate(db.DebitDate));
				jsnItem.AddVariable("Value", Functions.FormatCurrency(db.DebitValue, db.CurrencyCode, 2,true));
				jsnItem.AddVariable("Raiser", db.RaiserName);
				jsnItem.AddVariable("ContactNo", db.ContactNo);
				jsnItem.AddVariable("Contact", db.ContactName);
				jsnItem.AddVariable("PONo", db.PurchaseOrderNo);
				jsnItem.AddVariable("PO", db.PurchaseOrderNumber);
				jsnItems.AddVariable(jsnItem);
				jsnItem.Dispose(); jsnItem = null;
			}
			jsn.AddVariable("Items", jsnItems);
			OutputResult(jsn);
			jsnItems.Dispose(); jsnItems = null;
			jsn.Dispose(); jsn = null;
		}

		private void GetInvoiceData() {
			List<Invoice> lst = Invoice.GetListForCompany(ID, GetFormValue_Boolean("IncludeClosed"));
			JsonObject jsn = new JsonObject();
			JsonObject jsnItems = new JsonObject(true);
			foreach (Invoice iv in lst) {
				JsonObject jsnItem = new JsonObject();
				jsnItem.AddVariable("ID", iv.InvoiceId);
				jsnItem.AddVariable("No", iv.InvoiceNumber);
				jsnItem.AddVariable("Date", Functions.FormatDate(iv.InvoiceDate));
				jsnItem.AddVariable("Value", Functions.FormatCurrency(iv.InvoiceValue, iv.CurrencyCode, 2,true));
				jsnItem.AddVariable("SONo", iv.SalesOrderNo);
				jsnItem.AddVariable("SO", iv.SalesOrderNumber);
                jsnItem.AddVariable("CustomerPO", iv.CustomerPO);
                jsnItems.AddVariable(jsnItem);
				jsnItem.Dispose(); jsnItem = null;
			}
			jsn.AddVariable("Items", jsnItems);
			OutputResult(jsn);
			jsnItems.Dispose(); jsnItems = null;
			jsn.Dispose(); jsn = null;
		}

		private void GetCreditNoteData() {
			List<Credit> lst = Credit.GetListForCompany(ID);
			JsonObject jsn = new JsonObject();
			JsonObject jsnItems = new JsonObject(true);
			foreach (Credit cr in lst) {
				JsonObject jsnItem = new JsonObject();
				jsnItem.AddVariable("ID", cr.CreditId);
				jsnItem.AddVariable("No", cr.CreditNumber);
				jsnItem.AddVariable("Date", Functions.FormatDate(cr.CreditDate));
				jsnItem.AddVariable("Value", Functions.FormatCurrency(cr.CreditValue, cr.CurrencyCode, 2,true));
				jsnItem.AddVariable("Raiser", cr.RaiserName);
				jsnItem.AddVariable("ContactNo", cr.ContactNo);
				jsnItem.AddVariable("Contact", cr.ContactName);
				jsnItem.AddVariable("SONo", cr.SalesOrderNo);
				jsnItem.AddVariable("SO", cr.SalesOrderNumber);
				jsnItems.AddVariable(jsnItem);
				jsnItem.Dispose(); jsnItem = null;
			}
			jsn.AddVariable("Items", jsnItems);
			OutputResult(jsn);
			jsnItems.Dispose(); jsnItems = null;
			jsn.Dispose(); jsn = null;
		}

	}
}