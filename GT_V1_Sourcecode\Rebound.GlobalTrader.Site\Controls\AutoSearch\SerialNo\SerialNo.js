Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.AutoSearch");Rebound.GlobalTrader.Site.Controls.AutoSearch.SerialNo=function(n){Rebound.GlobalTrader.Site.Controls.AutoSearch.SerialNo.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.AutoSearch.SerialNo.prototype={get_ShowInactive:function(){return this._ShowInactive},set_ShowInactive:function(n){this._ShowInactive!==n&&(this._ShowInactive=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.AutoSearch.SerialNo.callBaseMethod(this,"initialize");this.addDataReturnedEvent(Function.createDelegate(this,this.dataReturned));this.setupDataObject("SerialNo")},dispose:function(){this.isDisposed||(this._ShowInactive=null,Rebound.GlobalTrader.Site.Controls.AutoSearch.SerialNo.callBaseMethod(this,"dispose"))},dataReturned:function(){var t,r,n,i;if(this._result.Results&&this._result.Results.length>0)for(t=0,r=this._result.Results.length;t<r;t++)n=this._result.Results[t],i="",i=this._enmResultsActionType==$R_ENUM$AutoSearchResultsActionType.Navigate?$RGT_nubButton_Manufacturer(n.ID,n.SerialNo):$R_FN.setCleanTextValue(n.SerialNo),this.addResultItem(i,$R_FN.setCleanTextValue(n.SerialNo),n.ID),i=null,n=null}};Rebound.GlobalTrader.Site.Controls.AutoSearch.SerialNo.registerClass("Rebound.GlobalTrader.Site.Controls.AutoSearch.SerialNo",Rebound.GlobalTrader.Site.Controls.AutoSearch.Base,Sys.IDisposable);