﻿
GO
/*
===========================================================================================
TASK      			UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-211138]			Phuc Hoang			14-Aug-2024		UPDATE          [PROD Bug] Error when adding an Offer without Currency in HUBRFQ
===========================================================================================
*/
CREATE OR ALTER PROCEDURE [dbo].[usp_Import_Offer_ByUser]  
--*************************************************************************************************              
--* SG 25.07.2019              
--* - Use temporary Product table because updated tool is removing spaces etc.               
--*              
--* SG 28.07.2017              
--* - Pre-pocess Manufacturer matching - gives an improved chance of matching              
--*   plus slight performance improvement              
--* - Use temporary table for Package matching to map additional commonly used values              
--* - Added Inactive checks to Package matching              
--*              
--* SK 23.03.2011:              
--* - due to multiple currencies the currencyCode is now actually currencyDescription - match accordngly              
--*              
--* SK 20.01.2010:              
--* - add ClientNo to columns inserted              
--*              
--* SK 13.11.2009:              
--* - change delete processing - this removes the need for the delete doubles step overnight              
--* - include GO-based version of Manufacturer, Package and Product              
--*************************************************************************************************              
              
@UpdatedBy int              
AS              
BEGIN              
              
SET ANSI_WARNINGS OFF              
SET NOCOUNT ON              
              
CREATE TABLE #tmpManufacturer (              
 ManufacturerName NVARCHAR(128),              
 ManufacturerId  INT NULL)              
              
CREATE TABLE #tmpPackage              
( PackageId  INT              
, PackageName  NVARCHAR(20)              
, Inactive  INT)              
              
CREATE TABLE #tmpProduct              
( ProductId   INT              
, ClientNo   INT              
, ProductDescription NVARCHAR(128))              
              
--IF OBJECT_ID('tempdb..#tmpOffersToBeImported') IS NOT NULL DROP TABLE #tmpOffersToBeImported              
--IF OBJECT_ID('tempdb..#tmpManufacturer') IS NOT NULL DROP TABLE #tmpManufacturer              
--IF OBJECT_ID('tempdb..#tmpPackage') IS NOT NULL DROP TABLE #tmpPackage              
              
--Espire : 10 Oct 17: Store all data into temp table              
SELECT *              
INTO #tmpOffersToBeImported              
FROM BorisGlobalTraderImports.dbo.tbOffersToBeImported where CreatedBy=@UpdatedBy              
              
--Espire : 10 Oct 17 : Commented              
--SELECT DISTINCT SupplierNo, Part, DateCode, ManufacturerName              
--INTO #tmpOfferImport              
--FROM BorisGlobalTraderImports.dbo.tbOffersToBeImported              
              
IF EXISTS (SELECT 1 FROM #tmpOffersToBeImported)              
BEGIN              
              
 BEGIN TRANSACTION              
              
 DECLARE @Msg NVARCHAR(100)              
              
 INSERT INTO #tmpPackage              
 SELECT PackageId              
 ,  PackageName              
 ,  Inactive              
 FROM tbPackage              
 WHERE Inactive = 0              
              
 --INSERT INTO #tmpPackage SELECT PackageId, 'CUTTAPE'      , Inactive FROM tbPackage WHERE PackageName = 'TAPEM' AND Inactive = 0              
 --INSERT INTO #tmpPackage SELECT PackageId, 'PARTIAL-REEL' , Inactive FROM tbPackage WHERE PackageName = 'PREEL' AND Inactive = 0              
 --INSERT INTO #tmpPackage SELECT PackageId, 'PARTIAL-REELS', Inactive FROM tbPackage WHERE PackageName = 'PREEL' AND Inactive = 0              
 --INSERT INTO #tmpPackage SELECT PackageId, 'REEL'         , Inactive FROM tbPackage WHERE PackageName = 'T&R'   AND Inactive = 0              
 --INSERT INTO #tmpPackage SELECT PackageId, 'REELS'        , Inactive FROM tbPackage WHERE PackageName = 'T&R'   AND Inactive = 0              
 --INSERT INTO #tmpPackage SELECT PackageId, 'TUBE'         , Inactive FROM tbPackage WHERE PackageName = 'TUBED' AND Inactive = 0     
            
 CREATE CLUSTERED INDEX tmpPackage_Name ON #tmpPackage (PackageName, Inactive, PackageId)              
              
 INSERT INTO #tmpManufacturer (ManufacturerName)              
 SELECT DISTINCT ManufacturerName              
 FROM #tmpOffersToBeImported              
 ORDER BY ManufacturerName              
              
 CREATE CLUSTERED INDEX tmpManufacturer_Name ON #tmpManufacturer (ManufacturerName, ManufacturerId)              
 CREATE NONCLUSTERED INDEX tmpManufacturer_Id ON #tmpManufacturer (ManufacturerName, ManufacturerId)              
              
 -- Try to match on Manufacturer Name              
 --UPDATE #tmpManufacturer              
 --SET  ManufacturerId = match.ManufacturerId              
 --FROM #tmpManufacturer              
 --CROSS APPLY (              
 --    SELECT MIN(m.ManufacturerId) AS ManufacturerId              
 --    FROM tbManufacturer AS m              
 --    WHERE m.ManufacturerName = #tmpManufacturer.ManufacturerName              
 --    AND  m.Inactive = 0) AS match              
              
 -- Try to match on Manufacturer FullName              
 UPDATE #tmpManufacturer              
 SET  ManufacturerId = match.ManufacturerId              
 FROM #tmpManufacturer              
 CROSS APPLY (              
     SELECT MIN(m.ManufacturerId) AS ManufacturerId       FROM tbManufacturer AS m              
     --WHERE m.FullName = #tmpManufacturer.ManufacturerName              
  WHERE m.ManufacturerName = #tmpManufacturer.ManufacturerName              
     AND  m.Inactive = 0) AS match              
 WHERE #tmpManufacturer.ManufacturerId IS NULL              
              
 -- Try to match on Manufacturer Code              
 UPDATE #tmpManufacturer              
 SET  ManufacturerId = match.ManufacturerId              
 FROM #tmpManufacturer              
 CROSS APPLY (              
     SELECT MIN(m.ManufacturerId) AS ManufacturerId              
     FROM tbManufacturer AS m              
     WHERE m.ManufacturerCode = #tmpManufacturer.ManufacturerName              
     AND  m.Inactive = 0) AS match              
 WHERE #tmpManufacturer.ManufacturerId IS NULL              
              
 INSERT INTO #tmpProduct (ProductId, ClientNo, ProductDescription)              
 SELECT ProductId,              
   ClientNo,              
   ProductDescription          
   --dbo.ufn_get_fullname(ProductDescription)              
 FROM tbProduct              
 WHERE Inactive = 0              
              
 CREATE NONCLUSTERED INDEX tmpProduct_Id ON #tmpProduct (ProductDescription, ClientNo, ProductId)              
              
----Table varibale --              
--DECLARE @MyTableVar table(              
--OfferId int,                
--RefId int ,              
--clientno int,              
--OfferType nvarchar(10),              
--LocalData bit                
--  );              
              
-----------------------              
              
 INSERT INTO BorisGlobalTraderImports.dbo.tbOffer              
 (  SupplierNo              
 ,  Part              
 ,  FullPart              
 ,  DateCode              
 ,  Quantity              
 ,  Price              
 ,  OriginalEntryDate              
 ,  ManufacturerNo              
 ,  CurrencyNo              
 ,  ProductNo              
 ,  PackageNo              
 ,  Notes              
 ,  ManufacturerName              
 ,  ProductName              
 ,  PackageName              
 ,  ClientNo              
 ,  ActionType              
 ,  OfferStatusNo                 
 ,  SupplierTotalQSA               
 ,  SupplierLTB               
 ,  SupplierMOQ               
 ,  LeadTime              
 ,  ROHSStatus              
 ,  FactorySealed              
 ,  MSLLevelNo              
 ,  SPQ              
 ,  ROHS 
 ,       RefId      
 ,DLUP        
,updatedby       
 ,Salesman             
               
 )              
  --OUTPUT               
  -- inserted.OfferId,              
  -- inserted.RefId,              
  -- inserted.ClientNo,                 
  -- 'Offer'              
  -- ,1             
                     
  --INTO @MyTableVar                
               
 SELECT offer.SupplierNo              
 ,  offer.Part              
 ,  dbo.ufn_get_fullpart(offer.Part)              
 ,  offer.DateCode              
 ,  isnull(offer.Quantity,0) as Quantity              
 ,  isnull(offer.Price,0) as Price              
 ,  offer.ImportDate              
 ,  tmpMan.ManufacturerId              
 ,  ISNULL(dbo.tbCurrency.CurrencyId, cmp.SOCurrencyNo) as CurrencyNo              
 ,  tmpProd.ProductId              
 ,  tmpPack.PackageId              
 ,  RTRIM(LTRIM(ISNULL(offer.Description, '')              
     + ISNULL((CASE WHEN tmpMan.ManufacturerId   IS NULL AND ISNULL(offer.ManufacturerName, '') <> '' THEN ' | Mfr: '  + RTRIM(offer.ManufacturerName) END), '')              
     + ISNULL((CASE WHEN tmpProd.ProductId  IS NULL AND ISNULL(offer.ProductName,      '') <> '' THEN ' | Prod: ' + RTRIM(offer.ProductName)      END), '')              
     + ISNULL((CASE WHEN tmpPack.PackageId       IS NULL AND ISNULL(offer.PackageName,      '') <> '' THEN ' | Pack: ' + RTRIM(offer.PackageName)      END), '')))              
 ,  offer.ManufacturerName              
 ,  offer.ProductName              
 ,  offer.PackageName              
 ,  offer.ClientNo              
 ,  offer.ActionType              
 ,  offer.OfferStatusNo                 
 ,  offer.SupplierTotalQSA               
 ,  offer.SupplierLTB               
 ,  offer.SupplierMOQ               
 ,  offer.LeadTime              
 ,  offer.ROHSStatus              
 ,  offer.FactorySealed              
 ,  offer.MSLLevelNo              
 ,  offer.SPQ              
 ,  offer.ROHS              
 ,       offer.RefId              
 , getdate()          
 , offer.createdby      
 , offer.createdby            
 --,  offer.Description              
 -- Espire : 10 Oct 17              
 --FROM  BorisGlobalTraderImports.dbo.tbOffersToBeImported AS offer              
 FROM  #tmpOffersToBeImported AS offer           
 LEFT JOIN #tmpManufacturer AS tmpMan ON tmpMan.ManufacturerName = offer.ManufacturerName  --collate SQL_Latin1_General_CP1_CI_AS              
 LEFT JOIN dbo.tbGlobalCurrencyList ON dbo.tbGlobalCurrencyList.GlobalCurrencyName = offer.CurrencyCode              
 LEFT JOIN dbo.tbCurrency    ON dbo.tbCurrency.CurrencyDescription = offer.CurrencyCode              
            AND dbo.tbCurrency.ClientNo = offer.ClientNo              
            AND dbo.tbCurrency.Buy = 1              
            AND dbo.tbCurrency.Inactive = 0              
 LEFT JOIN #tmpProduct AS tmpProd  ON tmpProd.ProductDescription = offer.ProductName              
            AND tmpProd.ClientNo = offer.ClientNo              
 LEFT JOIN #tmpPackage AS tmpPack  ON tmpPack.PackageName = offer.PackageName              
            AND tmpPack.Inactive = 0   
  LEFT JOIN tbCompany cmp ON cmp.CompanyId = offer.SupplierNo              
            
			
 -------------------------Import History------------------------------------     
   declare  @RowCount int = 0           
   declare  @ClientId int = 0           
   declare  @OriginalFilename nvarchar(200)= null           
   select  @RowCount=count(*),@OriginalFilename=imp.OfferName,@ClientId=imp.ClientNo from  BorisGlobalTraderimports.dbo.tbOffersToBeImported imp    
   INNER JOIN #tmpOffersToBeImported  AS tmp                
   ON  tmp.id = imp.id where imp.CreatedBy=@UpdatedBy  group by imp.OfferName,imp.ClientNo        
   insert into BorisGlobalTraderImports.dbo.tbUtilityLog (FileName,UtilityType,Clientid,LoginNo,DLUP,iRowCount)     
   values (@OriginalFilename,4,@ClientId,@UpdatedBy,getdate(),@RowCount)          
     --------------------------Import History End--------------------------------  			          
          
              
 DELETE BorisGlobalTraderImports.dbo.tbOffersToBeImported              
  FROM BorisGlobalTraderImports.dbo.tbOffersToBeImported AS imp              
  INNER JOIN #tmpOffersToBeImported  AS tmp              
  ON  tmp.id = imp.id where imp.CreatedBy=@UpdatedBy              
              
 IF @@ERROR = 0              
 BEGIN              
  COMMIT TRANSACTION              
  SET @Msg = 'Import Success'                        
 END              
 ELSE              
 BEGIN              
  ROLLBACK TRANSACTION              
  SET @Msg = 'Import Failed'              
 END              
              /*              
              
           -- Read your query results into an XML variable              
    DECLARE @xml AS XML = ( select offerid ,RefId ,'OFFER' as Offer,0 as Transfered,clientno from @MyTableVar where ClientNo = 114 FOR XML PATH)              
              
    -- Cast the XML variable into a VARCHAR              
    DECLARE @xmlChar AS VARCHAR(max) = CAST(@xml AS VARCHAR(max))              
    declare @Filename nvarchar(50)              
    select @Filename = replace(replace(convert(varchar(50),getdate(),121)+'.xml',' ',''),':','')              
    -- Escape the < and > characters         
    SET @xmlChar = REPLACE(REPLACE(@xmlChar, '>', '^>'), '<', '^<')              
              
    -- Create command GO to echo to file              
    DECLARE @command VARCHAR(8000) = 'echo ' + @xmlChar + ' > c:\\test\\'+@Filename              
              
    -- Execute the command              
    EXEC xp_cmdshell @command              
    */              
              
     --INSERT INTO  BorisGlobalTraderImports.dbo.tbUpdateOfferRef(OfferId,RefId,OfferType,Transfered,clientno)              
     --select offerid ,RefId ,'OFFER',0,clientno from @MyTableVar              
     --where ClientNo = 114               
              
 -- Log to import activity table              
 INSERT INTO BorisGlobalTraderImports.dbo.tbImportActivity (SupplierName, ImportDate, ImportName, RowsAffected, Target)              
 SELECT LEFT(CompanyName, 50),              
   current_timestamp,              
   'tbOffer',              
   0,              
   @Msg              
 FROM dbo.tbCompany              
 WHERE CompanyId IN (SELECT SupplierNo FROM #tmpOffersToBeImported)              
 END              
              
DROP TABLE #tmpManufacturer              
DROP TABLE #tmpPackage              
DROP TABLE #tmpProduct              
--DROP TABLE #tmpOfferImport              
DROP TABLE #tmpOffersToBeImported              
END              

GO


