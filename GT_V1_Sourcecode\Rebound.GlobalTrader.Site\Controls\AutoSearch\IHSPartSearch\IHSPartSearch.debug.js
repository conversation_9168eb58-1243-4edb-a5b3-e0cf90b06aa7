///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.AutoSearch");

Rebound.GlobalTrader.Site.Controls.AutoSearch.IHSPartSearch = function(element) { 
	Rebound.GlobalTrader.Site.Controls.AutoSearch.IHSPartSearch.initializeBase(this, [element]);
	this._searchType = null;
};

Rebound.GlobalTrader.Site.Controls.AutoSearch.IHSPartSearch.prototype = {

	get_searchType: function() { return this._searchType; }, set_searchType: function(value) { if (this._searchType !== value) this._searchType = value; }, 
	
	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.AutoSearch.IHSPartSearch.callBaseMethod(this, "initialize");
		this.addDataReturnedEvent(Function.createDelegate(this, this.dataReturned));
		this.setupDataObject("IHSPartSearch");
	},
	
	dispose: function() { 
		if (this.isDisposed) return;
		this._searchType = null;
		Rebound.GlobalTrader.Site.Controls.AutoSearch.IHSPartSearch.callBaseMethod(this, "dispose");
	},
    //lblservicemsgerror
	dataReturned: function () {
	    $("#lblservicemsgerror").text("");
	    var ihsmsg = this._result.ServiceStatus;
	    if (ihsmsg == false) {
            $("#lblservicemsgerror").text("Sorry, the IHS part lookup service is currently unavilable.");
	    }
	    else {
	        if (!this._result) return;
	        if (this._result.TotalRecords > 0) {
	            $("#lblservicemsgerror").text("");
	            for (var i = 0, l = this._result.Results.length; i < l; i++) {
	                var res = this._result.Results[i];
	                var strHTML = "";
	                if (this._enmResultsActionType != $R_ENUM$AutoSearchResultsActionType.Navigate) {
	                    strHTML = $R_FN.setCleanTextValue(res.Name);
	                }
	                this.addResultItem(strHTML, $R_FN.setCleanTextValue(res.Name), res.ID, res.ExtraValue);
	                strHTML = null; res = null;
	            }
	        }
        }
        $R_FN.setInnerHTML(this._lblResults, String.format("Quick Add: {0} result(s)", this._result.TotalRecords));
	}
};

Rebound.GlobalTrader.Site.Controls.AutoSearch.IHSPartSearch.registerClass("Rebound.GlobalTrader.Site.Controls.AutoSearch.IHSPartSearch", Rebound.GlobalTrader.Site.Controls.AutoSearch.Base, Sys.IDisposable);
