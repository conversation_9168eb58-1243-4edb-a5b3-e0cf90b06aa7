Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.SupplierContact=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.SupplierContact.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DropDowns.SupplierContact.prototype={get_intCompanyID:function(){return this._intCompanyID},set_intCompanyID:function(n){this._intCompanyID!==n&&(this._intCompanyID=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.DropDowns.SupplierContact.callBaseMethod(this,"initialize");this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK))},dispose:function(){this.isDisposed||(Rebound.GlobalTrader.Site.Controls.DropDowns.SupplierContact.callBaseMethod(this,"dispose"),this._intCompanyID=null)},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/SupplierContact");this._objData.set_DataObject("SupplierContact");this._objData.set_DataAction("GetData");this._objData.addParameter("id",this._intCompanyID)},dataCallOK:function(){var t=this._objData._result,n;if(t.Types)for(n=0;n<t.Types.length;n++)this.addOption(t.Types[n].Name,t.Types[n].ID)}};Rebound.GlobalTrader.Site.Controls.DropDowns.SupplierContact.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.SupplierContact",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);