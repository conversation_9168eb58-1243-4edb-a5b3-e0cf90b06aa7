﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/*   
===========================================================================================  
TASK        UPDATED BY       DATE           ACTION    DESCRIPTION  
[US-210037]  An.TranTan      23-Oct-2024	Create    Get multiple Manufacturer advisory notes  
===========================================================================================  
*/  
CREATE OR ALTER PROCEDURE  [dbo].[usp_get_multiple_AdvisoryNotes]      
  @IDs NVARCHAR(MAX)
AS  
BEGIN
	SET NOCOUNT ON;

	;with cte as(
		select distinct cast(value as int) as MfrId
		FROM STRING_SPLIT(@IDs, ',')
	)select
		m.ManufacturerId,
		CASE WHEN ISNULL(IsDisplayAdvisory, 0) = 1 THEN AdvisoryNotes ELSE '' END AS AdvisoryNotes
	from cte
	join tbManufacturer m WITH(NOLOCK) on m.ManufacturerId = cte.MfrId

	SET NOCOUNT OFF;
END
GO