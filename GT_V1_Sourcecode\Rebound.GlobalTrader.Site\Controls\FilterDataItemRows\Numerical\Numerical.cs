/*
Marker     changed by      date         Remarks
[001]      Vinay           10/07/2013   ESMS Ref:23 - GT having some .14 ASP.Net warnings
*/
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Collections;
using Rebound.GlobalTrader.Site.Controls.Nuggets;
using Rebound.GlobalTrader.Site.Controls.DropDowns;

namespace Rebound.GlobalTrader.Site.Controls.FilterDataItemRows {
    [DefaultProperty("")]
    [ToolboxData("<{0}:Numerical runat=server></{0}:Numerical>")]
    public class Numerical : Base {

        #region Locals

        protected ReboundTextBox _txt;
        protected DropDowns.NumericalComparison _ddl = new Rebound.GlobalTrader.Site.Controls.DropDowns.NumericalComparison();

        #endregion

        #region Properties

        //private Unit _untTextBoxWidth = Unit.Pixel(65);
        //Change the textbox width 65 px to 70 px as per requirement : 7 Sept 2016
        private Unit _untTextBoxWidth = Unit.Pixel(80);
        public Unit TextBoxWidth {
            get { return _untTextBoxWidth; }
            set { _untTextBoxWidth = value; }
        }
        private int _intTextboxLength = 11;
        public int TextBoxMaxLength
        {
            get { return _intTextboxLength; }
            set { _intTextboxLength = value; }
        }

        private bool _blnCanShowDecimal = false;
        public bool CanShowDecimal {
            get { return _blnCanShowDecimal; }
            set { _blnCanShowDecimal = value; }
        }
        #endregion

        #region Overrides

        /// <summary>
        /// OnInit
        /// </summary>
        /// <param name="e"></param>
        protected override void OnInit(EventArgs e) {
            FieldType = Type.Numerical;
            base.OnInit(e);
            AddScriptReference(Functions.GetScriptReference(_blnConfigurationIsDebug, "Rebound.GlobalTrader.Site", "Controls.FilterDataItemRows.Numerical.Numerical", true));
        }

        /// <summary>
        /// OnLoad
        /// </summary>
        /// <param name="e"></param>
        protected override void OnLoad(EventArgs e) {
            EnsureChildControls();
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.Numerical", ClientID);
            base.OnLoad(e);
        }

        protected override void OnPreRender(EventArgs e) {
            _scScriptControlDescriptor.AddElementProperty("txt", _txt.ClientID);
            _scScriptControlDescriptor.AddElementProperty("ddl", _ddl.ddl.ClientID);
            base.OnPreRender(e);
        }

        /// <summary>
        /// create controls
        /// </summary>
        protected override void CreateChildControls() {
            base.CreateChildControls();

            //dropdown
            UserControl usc = new UserControl();
            _ddl.IncludeNoValue = false;
            _ddl.ID = "ddlComparison";
            _ddl.CanRefresh = false;
            _lblField.Controls.Add(_ddl);
            usc.Dispose(); usc = null;

            //textbox
            _txt = new ReboundTextBox();
            _txt.ID = "txt";
            _txt.Width = _untTextBoxWidth;
            if (!_blnCanShowDecimal)
                _txt.TextBoxMode = ReboundTextBox.TextBoxModeList.Numeric;
            else
                _txt.TextBoxMode = ReboundTextBox.TextBoxModeList.Currency;
            //[001] code start 
            //_txt.MaxLength = 9; // Commented due to increase the textbox length from PO hub 
            _txt.MaxLength = _intTextboxLength;
            //[001] code end
            _lblField.Controls.Add(_txt);
        }

        public override void SetDefaultValue() {
            if (DefaultValue == null) DefaultValue = "";
            SetInitialValue(DefaultValue);
            base.SetDefaultValue();
        }

        public override void Reset() {
			EnsureChildControls();
			_ddl.InitialValue = Convert.ToInt32(NumericalComparison.NumericalComparisonType.EqualTo).ToString();
            _txt.Text = "";
            Enable(false);
            base.Reset();
        }

        #endregion

        #region Methods

        public void SetInitialValue(string strValue, int intNumericalComparison) {
			strValue = HttpUtility.UrlDecode(strValue.Trim());
			EnsureChildControls();
            _ddl.InitialValue = intNumericalComparison.ToString();
            _txt.Text = strValue;
			if (!string.IsNullOrEmpty(_txt.Text)) Enable(true);
        }
        public void SetInitialValue(string strValue) {
            SetInitialValue(strValue, Convert.ToInt32(NumericalComparison.NumericalComparisonType.EqualTo));
        }

        #endregion

    }

}
