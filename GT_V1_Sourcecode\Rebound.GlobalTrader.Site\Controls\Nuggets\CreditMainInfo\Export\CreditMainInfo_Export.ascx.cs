using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.Forms 
{
    public partial class CreditMainInfo_Export : Base
    {

		#region Locals

        private string _strTitle_Export;
        private string _strTitle_Release;
        private Label _lblExplainExport;
        private Label _lblExplainRelease;

		#endregion

		#region Properties

        private int _intCreditID = -1;
        public int CreditID
        {
            get { return _intCreditID; }
            set { _intCreditID = value; }
        }

		#endregion

		#region Overrides

		/// <summary>
		/// OnInit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
            _strTitle_Export = Functions.GetGlobalResource("FormTitles", "CreditMainInfo_Export");
            _strTitle_Release = Functions.GetGlobalResource("FormTitles", "CreditMainInfo_Release");
            AddScriptReference("Controls.Nuggets.CreditMainInfo.Export.CreditMainInfo_Export.js");
            if (_objQSManager.CreditID > 0) _intCreditID = _objQSManager.CreditID;
            WireUpControls();
        }

		/// <summary>
		/// OnPreRender
		/// </summary>
		/// <param name="e"></param>
		protected override void OnPreRender(EventArgs e) {
			SetupScriptDescriptors();
			base.OnPreRender(e);
		}

		#endregion

		/// <summary>
		/// Wire up controls to the ascx
		/// </summary>
		private void WireUpControls() {
            _lblExplainExport = (Label)ctlDesignBase.FindExplanationControl("lblExplainExport");
            _lblExplainRelease = (Label)ctlDesignBase.FindExplanationControl("lblExplainRelease");
        }

		/// <summary>
		/// Setup Script Descriptors
		/// </summary>
		private void SetupScriptDescriptors() {
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Forms.CreditMainInfo_Export", ctlDesignBase.ClientID);
            _scScriptControlDescriptor.AddProperty("intCreditID", _intCreditID);
            _scScriptControlDescriptor.AddProperty("strTitle_Export", _strTitle_Export);
            _scScriptControlDescriptor.AddProperty("strTitle_Release", _strTitle_Release);
            _scScriptControlDescriptor.AddElementProperty("lblExplainExport", _lblExplainExport.ClientID);
            _scScriptControlDescriptor.AddElementProperty("lblExplainRelease", _lblExplainRelease.ClientID);
        }

	}
}