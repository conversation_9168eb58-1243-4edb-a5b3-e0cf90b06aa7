/*
Marker     Changed by      Date          Remarks
[001]      Vinay           18/09/2012    Ref:## - Display Purchase Country
[002]      Soorya          03/03/2023   RP-1048 Remove AI code
*/
using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
//using Microsoft.ApplicationInsights; // [002] 

namespace Rebound.GlobalTrader.Site.Controls.Nuggets.Data {
	[WebService(Namespace = "http://tempuri.org/")]
	[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
	public class GIAdd : Rebound.GlobalTrader.Site.Data.Base {
		public override void ProcessRequest(HttpContext context) {
			if (base.init(context)) {
				switch (Action) {
					case "AddNew": AddNew(); break;
					case "AddNewCRMA": AddNewCRMA(); break;
					default: WriteErrorActionNotFound(); break;
				}
			}
		}

		/// <summary>
		/// Add new goodsIn for PO
		/// </summary>
		public void AddNew() {
			try {
                int? intGlobalClientNo;
                intGlobalClientNo = GetFormValue_NullableInt("GlobalClientNo");
				int intResult = GoodsIn.Insert(
                    (intGlobalClientNo.HasValue && intGlobalClientNo.Value > 0) ? intGlobalClientNo.Value : SessionManager.ClientID,
					GetFormValue_NullableInt("ShipViaNo"),
					GetFormValue_String("AirWayBill"),
					GetFormValue_String("Reference"),
					GetFormValue_NullableInt("CMNo"),
					GetFormValue_String("Notes"),
					GetFormValue_NullableDateTime("DateReceived", DateTime.Now),
					GetFormValue_NullableInt("ReceivedBy"),
					GetFormValue_NullableInt("PONo"),
					null,
					GetFormValue_NullableInt("WarehouseNo"),
					GetFormValue_NullableInt("CurrencyNo"),
                    LoginID,
                    //[001] code start
                    GetFormValue_NullableInt("ImportCountryNo")
					//[001] code end
				);
				if (intResult > 0) {
					JsonObject jsn = new JsonObject();
					jsn.AddVariable("NewID", intResult);
					OutputResult(jsn);
					jsn.Dispose();
					jsn = null;
				} else {
					WriteErrorSQLActionFailed("Insert");
				}
			} catch (Exception e) {
				//[002]
				//var ai = new TelemetryClient();
				//ai.TrackTrace("Exception Header: GI ADD Action");
				//ai.TrackException(e);
				new Errorlog().LogMessage("Error at AddNew() in GIAdd.ashx.cs : " + e.Message);

				WriteError(e);


			}
		}

		/// <summary>
		/// Add new goodsIn for CRMA
		/// </summary>
		public void AddNewCRMA() {
			try {
                int? intGlobalClientNo;
                intGlobalClientNo = GetFormValue_NullableInt("GlobalClientNo");
				int intResult = GoodsIn.Insert(
                    (intGlobalClientNo.HasValue && intGlobalClientNo.Value > 0) ? intGlobalClientNo.Value : SessionManager.ClientID,
					GetFormValue_NullableInt("ShipViaNo"),
					GetFormValue_String("AirWayBill"),
					GetFormValue_String("Reference"),
					GetFormValue_NullableInt("CMNo"),
					GetFormValue_String("Notes"),
					GetFormValue_NullableDateTime("DateReceived", DateTime.Now),
					GetFormValue_NullableInt("ReceivedBy"),
					null,
					GetFormValue_NullableInt("CRMANo"),
					GetFormValue_NullableInt("WarehouseNo"),
					GetFormValue_NullableInt("CurrencyNo"),
					LoginID,
                    //[001] code start
                    null
                    //[001] code end
				);
				if (intResult > 0) {
					JsonObject jsn = new JsonObject();
					jsn.AddVariable("NewID", intResult);
					OutputResult(jsn);
					jsn.Dispose();
					jsn = null;
				} else {
					WriteErrorSQLActionFailed("Insert");
				}
			} catch (Exception e) {
				WriteError(e);
			}
		}
	}
}