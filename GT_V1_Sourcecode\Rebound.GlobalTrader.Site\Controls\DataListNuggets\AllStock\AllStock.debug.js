///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//
// RP 23.12.2009:
// - render state on server
//
// RP 30.11.2009:
// - allow passing of initial PartNo search
//
// RP 16.10.2009:
// - Changes due to changes in base class
/* Marker     changed by      date         Remarks
[001]      Vikas kumar     17/11/2011  ESMS Ref:23 - PO <PERSON>. and <PERSON><PERSON>. should also be displayed */
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DataListNuggets");

Rebound.GlobalTrader.Site.Controls.DataListNuggets.AllStock = function(element) { 
	Rebound.GlobalTrader.Site.Controls.DataListNuggets.AllStock.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.DataListNuggets.AllStock.prototype = {
    get_blnPOHub: function () { return this._blnPOHub; }, set_blnPOHub: function (v) { if (this._blnPOHub !== v) this._blnPOHub = v; },
    get_IsGlobalLogin: function () { return this._IsGlobalLogin; }, set_IsGlobalLogin: function (v) { if (this._IsGlobalLogin !== v) this._IsGlobalLogin = v; },
    initialize: function() {
        this.addSetupDataCallEvent(Function.createDelegate(this, this.setupDataCall));
        this.addGetDataOKEvent(Function.createDelegate(this, this.getDataOK));
        this.updateFilterVisibility();
        this.addInitCompleteEvent(Function.createDelegate(this, this.initAfterBaseIsReady));
        this._strPathToData = "controls/DataListNuggets/AllStock";
        this._strDataObject = "AllStock";
        Rebound.GlobalTrader.Site.Controls.DataListNuggets.AllStock.callBaseMethod(this, "initialize");
    },

    initAfterBaseIsReady: function() {
        this.addPageTabChangedEvent(Function.createDelegate(this, this.pageTabChanged));
        this.getData();
    },

    dispose: function() {
        if (this.isDisposed) return;
        this._blnPOHub = null;
        this._IsGlobalLogin=null;
        Rebound.GlobalTrader.Site.Controls.DataListNuggets.AllStock.callBaseMethod(this, "dispose");
    },

    pageTabChanged: function() {
        this._table._intCurrentPage = 1;
        this.updateFilterVisibility();
        this.getData();
    },

    setupDataCall: function() {
        var strAction = "GetData_";
        switch (this._intCurrentTab) {
            case 0: strAction += "All"; break;
            case 1: strAction += "Available"; break;
            case 2: strAction += "Quarantined"; break;
        }
        this._objData.set_DataAction(strAction);
        this._objData.addParameter("IsGlobalLogin", this._IsGlobalLogin);
    },

    getDataOK: function(args) {
        for (var i = 0, l = this._objResult.Results.length; i < l; i++) {
            var row = this._objResult.Results[i];
            var aryData;
            if (row.IsPoHub == true || this._IsGlobalLogin == true) {
                 aryData = [
				$R_FN.writeDoubleCellValue($RGT_nubButton_Stock(row.ID, row.Part, row.ROHS), $R_FN.setCleanTextValue(row.SupplierPart))
				, $R_FN.writeDoubleCellValue(row.InStock, row.OnOrder)
				, $R_FN.writeDoubleCellValue(row.Allocated, row.Available)
				, $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Warehouse), row.Location)
                //[001]Code Start
				, $R_FN.writeDoubleCellValue($RGT_nubButton_Lot(row.LotNo, row.Lot), $RGT_nubButton_PurchaseOrder(row.PurchaseOrderNo, row.PurchaseOrder))
				, $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Status), $RGT_nubButton_CRMA(row.CRMANo, row.CRMA))
                //[001]Code End
				, row.ClientName
			];
            }
            else {
                 aryData = [
				$R_FN.writeDoubleCellValue($RGT_nubButton_Stock(row.ID, row.Part, row.ROHS), $R_FN.setCleanTextValue(row.SupplierPart))
				, $R_FN.writeDoubleCellValue(row.InStock, row.OnOrder)
				, $R_FN.writeDoubleCellValue(row.Allocated, row.Available)
				, $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Warehouse), row.Location)
                //[001]Code Start
				, $R_FN.writeDoubleCellValue($RGT_nubButton_Lot(row.LotNo, row.Lot), $RGT_nubButton_PurchaseOrder(row.PurchaseOrderNo, row.PurchaseOrder))
				, $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Status), $RGT_nubButton_CRMA(row.CRMANo, row.CRMA))
                //[001]Code End
                , row.ClientName
                
			];
            }
            this._table.addRow(aryData, row.ID);
            aryData = null; row = null;
        }
    },
    updateFilterVisibility: function() {
        this.getFilterField("ctlClientName").show(this._blnPOHub || this._IsGlobalLogin);
    }

};
Rebound.GlobalTrader.Site.Controls.DataListNuggets.AllStock.registerClass("Rebound.GlobalTrader.Site.Controls.DataListNuggets.AllStock", Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base, Sys.IDisposable);
