Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");Rebound.GlobalTrader.Site.Controls.Tab=function(n){Rebound.GlobalTrader.Site.Controls.Tab.initializeBase(this,[n]);this._tabIndex=0;this._titleText="";this._isSelected=!1};Rebound.GlobalTrader.Site.Controls.Tab.prototype={get_TabIndex:function(){return this._tabIndex},set_TabIndex:function(n){this._tabIndex!==n&&(this._tabIndex=n)},get_TitleText:function(){return this._titleText},set_TitleText:function(n){this._titleText!==n&&(this._titleText=n)},get_pnlTab:function(){return this._pnlTab},set_pnlTab:function(n){this._pnlTab!==n&&(this._pnlTab=n)},get_IsSelected:function(){return this._isSelected},set_IsSelected:function(n){this._isSelected!==n&&(this._isSelected=n)},get_ParentTabStrip:function(){return this._parentTabStrip},set_ParentTabStrip:function(n){this._parentTabStrip!==n&&(this._parentTabStrip=n)},get_pnlRelatedContent:function(){return this._pnlRelatedContent},set_pnlRelatedContent:function(n){this._pnlRelatedContent!==n&&(this._pnlRelatedContent=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Tab.callBaseMethod(this,"initialize");$addHandler(this.get_element(),"click",Function.createDelegate(this,this.onClick))},dispose:function(){this.isDisposed||(this.get_element()&&$clearHandlers(this.get_element()),this._pnlTab=null,this._ParentTabStrip=null,this._pnlRelatedContent=null,Rebound.GlobalTrader.Site.Controls.Tab.callBaseMethod(this,"dispose"),this.isDisposed=!0)},onClick:function(){this._parentTabStrip.selectTab(this._tabIndex)},selectTab:function(n){n?(Sys.UI.DomElement.addCssClass(this._pnlTab,"tabItemSelected"),Sys.UI.DomElement.removeCssClass(this._pnlTab,"tabItem")):(Sys.UI.DomElement.addCssClass(this._pnlTab,"tabItem"),Sys.UI.DomElement.removeCssClass(this._pnlTab,"tabItemSelected"));this._isSelected=n},updateTitle:function(n){$R_FN.setInnerHTML(this._pnlTab,n)},addToTitle:function(n){n=String.format("{0} {1}",this._titleText,n);this.updateTitle(n)},addCountToTitle:function(n){strNewText=String.format("{0} ({1})",this._titleText,n);this.updateTitle(strNewText)},resetTitle:function(){this.updateTitle(this._titleText)}};Rebound.GlobalTrader.Site.Controls.Tab.registerClass("Rebound.GlobalTrader.Site.Controls.Tab",Sys.UI.Control,Sys.IDisposable);