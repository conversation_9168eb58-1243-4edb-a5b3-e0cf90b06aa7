Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists");Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Base=function(n){Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Base.initializeBase(this,[n]);this._blnIsExpanded=!0;this._aryFilterButtonIDs=[];this._aryFilterIDs=[];this._aryFilterShown=[];this._blnFiltersShown=!1;this._intTimeoutID_Filters=-1;this._aryFilterFieldsToInit=[];this._intCountFilterFieldsToInit=0;this._intCountDropDownsToCheckForData=0;this._objStateData={};this._blnInitialisedControls=!1;this._enmInitialSortDirection=1;this._intInitialSortColumnIndex=0;this._blnFirstSearch=!0};Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Base.prototype={get_tbl:function(){return this._tbl},set_tbl:function(n){this._tbl!==n&&(this._tbl=n)},get_pnlTable:function(){return this._pnlTable},set_pnlTable:function(n){this._pnlTable!==n&&(this._pnlTable=n)},get_pnlLoading:function(){return this._pnlLoading},set_pnlLoading:function(n){this._pnlLoading!==n&&(this._pnlLoading=n)},get_pnlPagingContent:function(){return this._pnlPagingContent},set_pnlPagingContent:function(n){this._pnlPagingContent!==n&&(this._pnlPagingContent=n)},get_hypPrev:function(){return this._hypPrev},set_hypPrev:function(n){this._hypPrev!==n&&(this._hypPrev=n)},get_lblPageNo:function(){return this._lblPageNo},set_lblPageNo:function(n){this._lblPageNo!==n&&(this._lblPageNo=n)},get_lblTotalPages:function(){return this._lblTotalPages},set_lblTotalPages:function(n){this._lblTotalPages!==n&&(this._lblTotalPages=n)},get_hypNext:function(){return this._hypNext},set_hypNext:function(n){this._hypNext!==n&&(this._hypNext=n)},get_intPageSize:function(){return this._intPageSize},set_intPageSize:function(n){this._intPageSize!==n&&(this._intPageSize=n)},get_aryFilterButtonIDs:function(){return this._aryFilterButtonIDs},set_aryFilterButtonIDs:function(n){this._aryFilterButtonIDs!==n&&(this._aryFilterButtonIDs=n)},get_aryFilterIDs:function(){return this._aryFilterIDs},set_aryFilterIDs:function(n){this._aryFilterIDs!==n&&(this._aryFilterIDs=n)},get_aryFilterShown:function(){return this._aryFilterShown},set_aryFilterShown:function(n){this._aryFilterShown!==n&&(this._aryFilterShown=n)},get_pnlShowFilters:function(){return this._pnlShowFilters},set_pnlShowFilters:function(n){this._pnlShowFilters!==n&&(this._pnlShowFilters=n)},get_pnlFilters:function(){return this._pnlFilters},set_pnlFilters:function(n){this._pnlFilters!==n&&(this._pnlFilters=n)},get_pnlLock:function(){return this._pnlLock},set_pnlLock:function(n){this._pnlLock!==n&&(this._pnlLock=n)},get_pnlPagingControls:function(){return this._pnlPagingControls},set_pnlPagingControls:function(n){this._pnlPagingControls!==n&&(this._pnlPagingControls=n)},get_pnlNoData:function(){return this._pnlNoData},set_pnlNoData:function(n){this._pnlNoData!==n&&(this._pnlNoData=n)},get_pnlSearch:function(){return this._pnlSearch},set_pnlSearch:function(n){this._pnlSearch!==n&&(this._pnlSearch=n)},get_trSearch:function(){return this._trSearch},set_trSearch:function(n){this._trSearch!==n&&(this._trSearch=n)},get_intDataListNuggetID:function(){return this._intDataListNuggetID},set_intDataListNuggetID:function(n){this._intDataListNuggetID!==n&&(this._intDataListNuggetID=n)},get_blnAllowSavingState:function(){return this._blnAllowSavingState},set_blnAllowSavingState:function(n){this._blnAllowSavingState!==n&&(this._blnAllowSavingState=n)},get_strDataListNuggetSubType:function(){return this._strDataListNuggetSubType},set_strDataListNuggetSubType:function(n){this._strDataListNuggetSubType!==n&&(this._strDataListNuggetSubType=n)},get_blnSaveState:function(){return this._blnSaveState},set_blnSaveState:function(n){this._blnSaveState!==n&&(this._blnSaveState=n)},get_intPageSizeForState:function(){return this._intPageSizeForState},set_intPageSizeForState:function(n){this._intPageSizeForState!==n&&(this._intPageSizeForState=n)},get_enmViewLevel:function(){return this._enmViewLevel},set_enmViewLevel:function(n){this._enmViewLevel!==n&&(this._enmViewLevel=n)},addSetupDataCallEvent:function(n){this.get_events().addHandler("SetupDataCall",n)},removeSetupDataCallEvent:function(n){this.get_events().removeHandler("SetupDataCall",n)},onSetupDataCall:function(){var n=this.get_events().getHandler("SetupDataCall");n&&n(this,Sys.EventArgs.Empty)},addGetDataOKEvent:function(n){this.get_events().addHandler("GetDataOK",n)},removeGetDataOKEvent:function(n){this.get_events().removeHandler("GetDataOK",n)},onGetDataOK:function(){var n=this.get_events().getHandler("GetDataOK");n&&n(this,Sys.EventArgs.Empty)},addDataCallErrorEvent:function(n){this.get_events().addHandler("DataCallError",n)},removeDataCallErrorEvent:function(n){this.get_events().removeHandler("DataCallError",n)},onDataCallError:function(){var n=this.get_events().getHandler("DataCallError");n&&n(this,Sys.EventArgs.Empty)},addInitCompleteEvent:function(n){this.get_events().addHandler("InitComplete",n)},removeInitCompleteEvent:function(n){this.get_events().removeHandler("InitComplete",n)},onInitComplete:function(){if(!this._blnInitialisedControls){this._blnInitialisedControls=!0;var n=this.get_events().getHandler("InitComplete");n&&n(this,Sys.EventArgs.Empty)}},initialize:function(){var n,r,t,i;for(Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Base.callBaseMethod(this,"initialize"),this._tbl.addSortDataEvent(Function.createDelegate(this,this.sortData)),this._tbl._intCurrentPageSize=this._intPageSize,this._tbl._intCurrentPage=1,this._enmInitialSortDirection=this._tbl._enmSortDirection,this._intInitialSortColumnIndex=this._tbl._intSortColumnIndex,$R_FN.showElement(this._pnlLock,!1),$addHandler(this._hypPrev,"click",Function.createDelegate(this,this.prevPage)),$addHandler(this._hypNext,"click",Function.createDelegate(this,this.nextPage)),$addHandler(this._pnlSearch,"click",Function.createDelegate(this,this.clickSearch)),$addHandler(this._pnlLock,"click",Function.createDelegate(this,this.clickLock)),$addHandler(this._pnlShowFilters,"mouseover",Function.createDelegate(this,this.showFilters)),$addHandler(this._pnlShowFilters,"mouseout",Function.createDelegate(this,this.hideFilters)),$addHandler(this._pnlFilters,"mouseover",Function.createDelegate(this,this.showFilters)),$addHandler(this._pnlFilters,"mouseout",Function.createDelegate(this,this.hideFilters)),$addHandler(this._pnlPagingContent,"mouseover",Function.createDelegate(this,this.cancelFilterHideTimeout)),$addHandler(this._pnlPagingContent,"mouseout",Function.createDelegate(this,this.hideFilters)),this.setFilterFieldEnterPressedEvents(),$R_FN.showElement(this._pnlTable,!1),this._aryFilterFieldsToInit=[],n=0,r=this._aryFilterIDs.length;n<r;n++)t=$find(this._aryFilterIDs[n]),(Object.getTypeName(t)=="Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.DropDown"||Object.getTypeName(t)=="Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.StarRating")&&Array.add(this._aryFilterFieldsToInit,t),t=null,i=$get(this._aryFilterButtonIDs[n]),i&&$addHandler(i,"click",Function.createDelegate(this,this.showFilterItem)),i=null;if(this._intCountFilterFieldsToInit=this._aryFilterFieldsToInit.length,this._intCountFilterFieldsToInit==0)this.controlsInitialized();else for(n=0;n<this._intCountFilterFieldsToInit;n++)this.ensureFilterFieldControlInitialized(this._aryFilterFieldsToInit[n])},ensureFilterFieldControlInitialized:function(n){if(n.get_isInitialized())this._intCountFilterFieldsToInit-=1,this._intCountFilterFieldsToInit==0&&(this._aryFilterFieldsToInit=null,this.controlsInitialized());else{var t=this._element.id,i=function(){$find(t).ensureFilterFieldControlInitialized(n)};setTimeout(i,5)}},controlsInitialized:function(){this.getDropDownsData(!0)},dispose:function(){var n,i,t;if(!this.isDisposed){for(this._element&&$clearHandlers(this._element),this._hypPrev&&$clearHandlers(this._hypPrev),this._hypNext&&$clearHandlers(this._hypNext),this._pnlSearch&&$clearHandlers(this._pnlSearch),this._pnlShowFilters&&$clearHandlers(this._pnlShowFilters),this._pnlFilters&&$clearHandlers(this._pnlFilters),this._pnlLock&&$clearHandlers(this._pnlLock),this._pnlPagingContent&&$clearHandlers(this._pnlPagingContent),n=0,i=this._aryFilterButtonIDs.length;n<i;n++)t=$get(this._aryFilterButtonIDs[n]),t&&$clearHandlers(t),t=null;this._tbl&&this._tbl.dispose();this._objData&&this._objData.dispose();this._tbl=null;this._pnlTable=null;this._pnlLoading=null;this._pnlPagingContent=null;this._hypPrev=null;this._lblPageNo=null;this._lblTotalPages=null;this._hypNext=null;this._aryFilterButtonIDs=null;this._aryFilterIDs=null;this._aryFilterShown=null;this._pnlShowFilters=null;this._pnlFilters=null;this._pnlLock=null;this._pnlSearch=null;this._trSearch=null;this._pnlNoData=null;this._pnlPagingControls=null;this._objData=null;this._aryFilterFieldsToInit=null;this._intPageSizeForState=null;this._enmViewLevel=null;Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Base.callBaseMethod(this,"dispose")}},clearState:function(){Rebound.GlobalTrader.Site.WebServices.ClearDataListNuggetState(this._intDataListNuggetID,this._strDataListNuggetSubType);Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Base.callBaseMethod(this,"clearState")},getFilterFieldByFilterName:function(n){for(var i=null,t=0,r=this._aryFilterIDs.length;t<r;t++)if($find(this._aryFilterIDs[t])._strFilterField==n){i=$find(this._aryFilterIDs[t]);break}return i},getFilterValue:function(n){var i=null,t=this.getFilterFieldByFilterName(n);return t&&(i=t.getValue()),t=null,i},setFilterValue:function(n,t){var i=this.getFilterFieldByFilterName(n);i&&(i.setValue(t),this.doShowFilterItemByName(n,!0));i=null},getStateVariableByName:function(n){var t,r,i;if(!this._objStateData)return{};for(t=0,r=this._objStateData.Filters.length;t<r;t++){if(i=this._objStateData.Filters[t],n.toUpperCase()==i.Name.toUpperCase())return i;i=null}return{}},getStateValue:function(n){var i=null,t=this.getStateVariableByName(n);return t&&(i=t.Value),t=null,i},resetState:function(){var t,i,n;for(this._tbl._intCurrentPage=1,this._tbl._intCurrentPageSize=this._intPageSize,this._tbl._enmSortDirection=this._enmInitialSortDirection,this._tbl._intSortColumnIndex=this._intInitialSortColumnIndex,t=0,i=this._aryFilterIDs.length;t<i;t++)n=$find(this._aryFilterIDs[t]),n.reset(),n.resetToDefault(),n._strDefaultValue!=""&&typeof n._strDefaultValue!="undefined"&&n._strDefaultValue!=null&&this.doShowFilterItem(t,!0),n=null},getDropDownsData:function(n){var t,i,r;if(n)for(i=0,r=this._aryFilterIDs.length;i<r;i++)t=$find(this._aryFilterIDs[i]),Object.getTypeName(t)=="Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.DropDown"&&(this._intCountDropDownsToCheckForData+=1),t=null;for(i=0,r=this._aryFilterIDs.length;i<r;i++)t=$find(this._aryFilterIDs[i]),Object.getTypeName(t)=="Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.DropDown"&&(n&&t._ddl.addGotDataComplete(Function.createDelegate(this,this.gotDropDownDataComplete)),t.getDropDownData()),t=null;n&&this._intCountDropDownsToCheckForData==0&&this.completionOfInit()},gotDropDownDataComplete:function(){this._intCountDropDownsToCheckForData-=1;this._intCountDropDownsToCheckForData==0&&this.completionOfInit()},completionOfInit:function(){this.displayLockState();this.showLoading(!1);$R_FN.showElement(this._pnlTable,!1);this.onInitComplete()},getData:function(){this._blnGettingData||(this._blnFirstSearch=!1,this.showLoading(!0),this._ibtnCancel&&$R_IBTN.showButton(this._ibtnCancel,!0),this._objData=new Rebound.GlobalTrader.Site.Data,this._objData.set_DataAction("GetData"),this._objData.set_PathToData(this._strPathToData),this._objData.set_DataObject(this._strDataObject),this.onSetupDataCall(),this._objData.addParameter("DLNID",this._intDataListNuggetID),this._objData.addParameter("DLNSubType",this._strDataListNuggetSubType),this._objData.addParameter("SortIndex",this._tbl._intSortColumnIndex+1),this._objData.addParameter("SortDir",this._tbl._enmSortDirection),this._objData.addParameter("PageIndex",this._tbl._intCurrentPage-1),this._objData.addParameter("PageSize",this._tbl._intCurrentPageSize),this._objData.addParameter("PageSizeForState",this._intPageSizeForState),this._objData.addParameter("SaveState",this._blnSaveState),this.addFilterParameters(this._objData),this._objData.addDataOK(Function.createDelegate(this,this.dataList_getDataOK)),this._objData.addError(Function.createDelegate(this,this.dataList_getDataError)),this._objData.addTimeout(Function.createDelegate(this,this.dataList_getDataError)),$R_DQ.addToQueue(this._objData),$R_DQ.processQueue(),this._blnGettingData=!0,this._objData=null)},dataList_getDataOK:function(n){this._blnGettingData=!1;this._objResult=n._result;this._tbl._intTotalRecords=this._objResult.TotalRecords;this._tbl.clearTable();this.onGetDataOK();this._tbl.calculatePages();this.updatePagingDisplay();this.showLoading(!1);this.showNoData(this._objResult.TotalRecords==0);this._tbl.resizeColumns()},dataList_getDataError:function(){this._blnGettingData=!1;this.showLoading(!1);this.onDataCallError()},updatePagingDisplay:function(){$R_FN.setInnerHTML(this._lblPageNo,this._tbl._intCurrentPage);$R_FN.setInnerHTML(this._lblTotalPages,this._tbl._intTotalPages)},prevPage:function(){this._tbl._intCurrentPage<=1||(this._tbl.prevPage(),this.updatePagingDisplay(),this.getData())},nextPage:function(){this._tbl._intCurrentPage>=this._tbl._intTotalPages||(this._tbl.nextPage(),this.updatePagingDisplay(),this.getData())},sortData:function(){this.getData()},showLoading:function(n){$R_FN.showElement(this._pnlLoading,n);$R_FN.showElement(this._pnlPagingContent,!n);$R_FN.showElement(this._pnlTable,!n);$R_FN.showElement(this._trSearch,!n);$R_FN.showElement(this._pnlShowFilters,!n);$R_FN.showElement(this._pnlLock,!n);n&&$R_FN.showElement(this._pnlNoData,!1)},showNoData:function(n){$R_FN.showElement(this._pnlNoData,n);$R_FN.showElement(this._pnlPagingControls,!n);$R_FN.showElement(this._pnlTable,!n);n&&(this._tbl._intCurrentPage=1)},showFilterItem:function(n){var i=n.target,t;i&&(t=Number.parseInvariant(i.getAttribute("bgt_leftDataList_filter")),this.doShowFilterItem(t,!this._aryFilterShown[t]))},doShowFilterItemByName:function(n,t){var i=0;for(i=0,l=this._aryFilterIDs.length;i<l;i++)if($find(this._aryFilterIDs[i])._strFilterField==n)break;this.doShowFilterItem(i,t)},doShowFilterItem:function(n,t){var i,r;this._aryFilterShown[n]=t;i=$get(this._aryFilterButtonIDs[n]);Sys.UI.DomElement.removeCssClass(i,"filterItemOn");Sys.UI.DomElement.removeCssClass(i,"filterItemOff");Sys.UI.DomElement.addCssClass(i,this._aryFilterShown[n]?"filterItemOn":"filterItemOff");i=null;r=$get(this._aryFilterIDs[n]);r&&$R_FN.showElement(r,this._aryFilterShown[n]);r=null},completelyHideFilterItem:function(n,t){var i=0;for(i=0,l=this._aryFilterIDs.length;i<l;i++)if($find(this._aryFilterIDs[i])._strFilterField==n)break;$R_FN.showElement($get(this._aryFilterButtonIDs[i]),!t);$R_FN.showElement($get(this._aryFilterIDs[i]),!t)},showFilters:function(){(this.cancelFilterHideTimeout(),this._blnFiltersShown)||($R_FN.showElement(this._pnlFilters,!0),this._blnFiltersShown=!0)},cancelFilterHideTimeout:function(){clearTimeout(this._intTimeoutID_Filters)},hideFilters:function(){this._blnFiltersShown&&(this.cancelFilterHideTimeout(),this._intTimeoutID_Filters=setTimeout(Function.createDelegate(this,this.finishHideFilters),100))},finishHideFilters:function(){this.cancelFilterHideTimeout();$R_FN.showElement(this._pnlFilters,!1);this._blnFiltersShown=!1},setFilterFieldEnterPressedEvents:function(){for(var t,n=0,i=this._aryFilterIDs.length;n<i;n++)t=$find(this._aryFilterIDs[n]),(Object.getTypeName(t)=="Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.TextBox"||Object.getTypeName(t)=="Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.Numerical")&&t.addEnterPressed(Function.createDelegate(this,this.getData))},addFilterParameters:function(n){for(var t,i=0,r=this._aryFilterIDs.length;i<r;i++)this._aryFilterIDs[i]&&(t=$find(this._aryFilterIDs[i]),t&&(t._blnOn&&this._aryFilterShown[i]&&(Object.getTypeName(t)=="Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.Numerical"||Object.getTypeName(t)=="Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.StarRating"?(n.addParameter(String.format("{0}Lo",t._strFilterField),t.getMinValue()),n.addParameter(String.format("{0}Hi",t._strFilterField),t.getMaxValue()),n.addParameter(String.format("{0}_Comparison",t._strFilterField),t._ddl.value)):Object.getTypeName(t)=="Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.DropDown"?t._ddl.isSetAsNoValue()||t._ddl._blnFirstTimeIn?t.enableField(!1):n.addParameter(t._strFilterField,t.getValue()):n.addParameter(t._strFilterField,t.getValue())),n.addParameter(String.format("{0}_IsShown",t._strFilterField),this._aryFilterShown[i]),n.addParameter(String.format("{0}_IsOn",t._strFilterField),t._blnOn),n.addParameter(String.format("{0}_Type",t._strFilterField),t._enmFieldType)),t=null)},clickLock:function(){this._blnGettingData||(this.showLockLoading(),this.updateLockState(!this._blnSaveState))},updateLockState:function(n){var t=this._blnSaveState!=n;this._blnSaveState=n;t&&(this._blnSaveState?this.saveState():this.clearState());this.displayLockState()},displayLockState:function(){Sys.UI.DomElement.removeCssClass(this._pnlLock,"lockLoading");this._blnSaveState?(Sys.UI.DomElement.addCssClass(this._pnlLock,"locked"),Sys.UI.DomElement.removeCssClass(this._pnlLock,"unlocked")):(Sys.UI.DomElement.addCssClass(this._pnlLock,"unlocked"),Sys.UI.DomElement.removeCssClass(this._pnlLock,"locked"))},showLockLoading:function(){Sys.UI.DomElement.addCssClass(this._pnlLock,"lockLoading");Sys.UI.DomElement.removeCssClass(this._pnlLock,"locked");Sys.UI.DomElement.removeCssClass(this._pnlLock,"unlocked")},cancelDataCall:function(){this._blnGettingData=!1;this._objData&&this._objData.cancel()},cancelClicked:function(){this.cancelDataCall();this._ibtnCancel&&$R_IBTN.showButton(this._ibtnCancel,!1);this.showResultsPanels(!0);this.clearMessages();this.addMessage($R_RES.SearchCancelled,$R_ENUM$MessageTypeList.Warning)},resetFilter:function(){for(var t,n=0,i=this._aryFilterIDs.length;n<i;n++)t=$find(this._aryFilterIDs[n]),t.reset(),t.resetToDefault(),t=null},clearState:function(){},saveState:function(){this._blnSaveState&&(this._objData=new Rebound.GlobalTrader.Site.Data,this.addFilterParameters(this._objData),this.onSetupDataCall(),this._objData.set_PathToData(this._strPathToData),this._objData.set_DataObject(this._strDataObject),this._objData.set_DataAction("SaveState"),this._objData.addParameter("DLNID",this._intDataListNuggetID),this._objData.addParameter("DLNSubType",this._strDataListNuggetSubType),this._objData.addParameter("SortIndex",this._tbl._intSortColumnIndex+1),this._objData.addParameter("SortDir",this._tbl._enmSortDirection),this._objData.addParameter("PageIndex",this._tbl._intCurrentPage-1),this._objData.addParameter("PageSize",this._intPageSizeForState),this._objData.addParameter("SaveState",!0),this._objData.addDataOK(Function.createDelegate(this,this.saveStateOK)),this._objData.addError(Function.createDelegate(this,this.saveStateOK)),this._objData.addTimeout(Function.createDelegate(this,this.saveStateOK)),$R_DQ.addToQueue(this._objData),$R_DQ.processQueue())},saveStateOK:function(){},clickSearch:function(){this._blnFirstSearch||(this._tbl._intCurrentPage=1);this.getData()}};Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Base.registerClass("Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Base",Rebound.GlobalTrader.Site.Controls.LeftNuggets.Base,Sys.IDisposable);