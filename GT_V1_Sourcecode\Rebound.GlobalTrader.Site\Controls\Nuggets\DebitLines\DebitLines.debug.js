///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 30.03.2010:
// - ensure line is fully loaded before we allow edits (to stop the wrong data being
//   edited)
//
// RP 06.01.2010:
// - fully dispose everything
//
// RP 14.10.2009:
// - retrofixes from v3.0.34
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");

Rebound.GlobalTrader.Site.Controls.Nuggets.DebitLines = function(element) { 
	Rebound.GlobalTrader.Site.Controls.Nuggets.DebitLines.initializeBase(this, [element]);
	this._intDebitID = -1;
	this._intLineID = -1;
	this._intLineCount = 0;
	this._intLineDataCalls = 0;
	this._blnLineIsService = false;
	this._blnLineLoaded = false;
	this._isEditEnable = false;
	this._intGlobalClientNo = -1;
	this._blnGlobalUser = false;
};

Rebound.GlobalTrader.Site.Controls.Nuggets.DebitLines.prototype = {

	get_intDebitID: function() { return this._intDebitID; }, set_intDebitID: function(v) { if (this._intDebitID !== v) this._intDebitID = v; },
	get_intLineID: function() { return this._intLineID; }, set_intLineID: function(v) { if (this._intLineID !== v) this._intLineID = v; },
	get_ibtnAdd: function() { return this._ibtnAdd; }, set_ibtnAdd: function(v) { if (this._ibtnAdd !== v)  this._ibtnAdd = v; }, 
	get_ibtnEdit: function() { return this._ibtnEdit; }, set_ibtnEdit: function(v) { if (this._ibtnEdit !== v)  this._ibtnEdit = v; }, 
	get_ibtnDelete: function() { return this._ibtnDelete; }, set_ibtnDelete: function(v) { if (this._ibtnDelete !== v)  this._ibtnDelete = v; }, 
	get_tbl: function() { return this._tbl; }, set_tbl: function(v) { if (this._tbl !== v)  this._tbl = v; }, 
	get_hypPrev: function() { return this._hypPrev; }, set_hypPrev: function(v) { if (this._hypPrev !== v)  this._hypPrev = v; }, 
	get_hypNext: function() { return this._hypNext; }, set_hypNext: function(v) { if (this._hypNext !== v)  this._hypNext = v; }, 
	get_lblLineNumber: function() { return this._lblLineNumber; }, set_lblLineNumber: function(v) { if (this._lblLineNumber !== v)  this._lblLineNumber = v; }, 
	get_pnlLineDetail: function() { return this._pnlLineDetail; }, set_pnlLineDetail: function(v) { if (this._pnlLineDetail !== v)  this._pnlLineDetail = v; }, 
	get_pnlLoadingLineDetail: function() { return this._pnlLoadingLineDetail; }, set_pnlLoadingLineDetail: function(v) { if (this._pnlLoadingLineDetail !== v)  this._pnlLoadingLineDetail = v; }, 
	get_pnlLineDetailError: function() { return this._pnlLineDetailError; }, set_pnlLineDetailError: function(v) { if (this._pnlLineDetailError !== v)  this._pnlLineDetailError = v; }, 
	get_lblSubTotal: function() { return this._lblSubTotal; }, set_lblSubTotal: function(v) { if (this._lblSubTotal !== v) this._lblSubTotal = v; }, 
	get_lblTax: function() { return this._lblTax; }, set_lblTax: function(v) { if (this._lblTax !== v)  this._lblTax = v; }, 
	get_lblFreight: function() { return this._lblFreight; }, set_lblFreight: function(v) { if (this._lblFreight !== v)  this._lblFreight = v; }, 
	get_lblTotal: function() { return this._lblTotal; }, set_lblTotal: function(v) { if (this._lblTotal !== v)  this._lblTotal = v; }, 

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.Nuggets.DebitLines.callBaseMethod(this, "initialize");	
		
		//data
		this._strDataPath = "controls/Nuggets/DebitLines";
		this._strDataObject = "DebitLines";
		
		//nugget events
		this.addRefreshEvent(Function.createDelegate(this, this.getData));
		
		//other controls
		this._tbl.addSelectedIndexChanged(Function.createDelegate(this, this.tbl_SelectedIndexChanged));
		$addHandler(this._hypPrev, "click", Function.createDelegate(this, this.prevLine));
		$addHandler(this._hypNext, "click", Function.createDelegate(this, this.nextLine));

		//add form
		if (this._ibtnAdd) {
			$R_IBTN.addClick(this._ibtnAdd, Function.createDelegate(this, this.showAddForm));
			this._frmAdd = $find(this._aryFormIDs[0]);
			this._frmAdd.addCancel(Function.createDelegate(this, this.hideAddForm));
			this._frmAdd.addSaveComplete(Function.createDelegate(this, this.saveAddComplete));
		}
	
		//edit form
		if (this._ibtnEdit) {
			$R_IBTN.addClick(this._ibtnEdit, Function.createDelegate(this, this.showEditForm));
			this._frmEdit = $find(this._aryFormIDs[1]);
			this._frmEdit.addCancel(Function.createDelegate(this, this.hideEditForm));
			this._frmEdit.addSaveComplete(Function.createDelegate(this, this.saveEditComplete));
	    }

		//delete form
		if (this._ibtnDelete) {
			$R_IBTN.addClick(this._ibtnDelete, Function.createDelegate(this, this.showDeleteForm));
			this._frmDelete = $find(this._aryFormIDs[2]);
			this._frmDelete.addCancel(Function.createDelegate(this, this.hideDeleteForm));
			this._frmDelete.addSaveComplete(Function.createDelegate(this, this.deleteComplete));
			this._frmDelete.addNotConfirmed(Function.createDelegate(this, this.hideDeleteForm));
	    }
		
		this.getData();
		$R_FN.showElement(this._pnlLineDetail, false);
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this._hypPrev) $clearHandlers(this._hypPrev);
		if (this._hypNext) $clearHandlers(this._hypNext);
		if (this._ibtnAdd) $R_IBTN.clearHandlers(this._ibtnAdd);
		if (this._ibtnEdit) $R_IBTN.clearHandlers(this._ibtnEdit);
		if (this._ibtnDelete) $R_IBTN.clearHandlers(this._ibtnDelete);
		if (this._frmAdd) this._frmAdd.dispose();
		if (this._frmEdit) this._frmEdit.dispose();
		if (this._frmDelete) this._frmDelete.dispose();
		if (this._tbl) this._tbl.dispose();
		this._frmAdd = null;
		this._frmEdit = null;
		this._frmDelete = null;
		this._intDebitID = null;
		this._intLineID = null;
		this._ibtnAdd = null;
		this._ibtnEdit = null;
		this._ibtnDelete = null;
		this._tbl = null;
		this._hypPrev = null;
		this._hypNext = null;
		this._lblLineNumber = null;
		this._pnlLineDetail = null;
		this._pnlLoadingLineDetail = null;
		this._pnlLineDetailError = null;
		this._lblSubTotal = null;
		this._lblTax = null;
		this._lblFreight = null;
		this._lblTotal = null;
		this._intGlobalClientNo = null;
		this._blnGlobalUser = null;
		Rebound.GlobalTrader.Site.Controls.Nuggets.DebitLines.callBaseMethod(this, "dispose");
	},
	
	getData: function() {
		this.enableEditButtons(false);
		$R_FN.showElement(this._pnlLineDetail, false);
		this.showLoading(true);
		var obj = new Rebound.GlobalTrader.Site.Data();
		obj.set_PathToData(this._strDataPath);
		obj.set_DataObject(this._strDataObject);
		obj.set_DataAction("GetLines");
		obj.addParameter("id", this._intDebitID);
		obj.addDataOK(Function.createDelegate(this, this.getDataOK));
		obj.addError(Function.createDelegate(this, this.getDataError));
		obj.addTimeout(Function.createDelegate(this, this.getDataError));
		$R_DQ.addToQueue(obj);
		$R_DQ.processQueue();
		obj = null;
	},

	enableEditButtons: function(bln) {
		if (bln) {
			//if (this._ibtnEdit) $R_IBTN.enableButton(this._ibtnEdit, this._blnLineLoaded && !this._isEditEnable);
			if (this._ibtnEdit) $R_IBTN.enableButton(this._ibtnEdit, this._blnLineLoaded);
			if (this._ibtnDelete) $R_IBTN.enableButton(this._ibtnDelete, this._blnLineLoaded && !this._isEditEnable);
		} else {
			if (this._ibtnEdit) $R_IBTN.enableButton(this._ibtnEdit, false);
			if (this._ibtnDelete) $R_IBTN.enableButton(this._ibtnDelete, false);
		}
	},

	getDataOK: function(args) { 
		this.showLoading(false);
		this._tbl.clearTable();
		var result = args._result;
		if (result.Lines) {
			for (var i = 0; i < result.Lines.length; i++) {
				var row = result.Lines[i];
				var aryData = [
					$R_FN.writeDoubleCellValue($RGT_nubButton_Stock(row.StockNo, row.Part, row.ROHS), $R_FN.setCleanTextValue(row.SupplierPart))
					, $R_FN.writeDoubleCellValue($RGT_nubButton_Manufacturer(row.ManufacturerNo, row.Manufacturer, row.MfrAdvisoryNotes), $R_FN.setCleanTextValue(row.DC))
					, $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Product), $R_FN.setCleanTextValue(row.Package))
					, row.Quantity
					, row.Price
					, $R_FN.writeDoubleCellValue(row.Total, row.Tax)
				];
				  var objExtraData = { IsParentDebitLineNo: row.IsParentDebitLineNo };
             
				this._tbl.addRow(aryData, row.ID, row.ID == this._intLineID,objExtraData);
				row = null;
			}
		}
		this._intLineCount = this._tbl.countRows();
		$R_FN.setInnerHTML(this._lblSubTotal, result.SubTotal);
		$R_FN.setInnerHTML(this._lblTax, result.Tax);
		$R_FN.setInnerHTML(this._lblFreight, result.Freight);
		$R_FN.setInnerHTML(this._lblTotal, result.Total);
		this.showContent(true);
		this.showContentLoading(false);
		this._tbl.resizeColumns();
	},

	getDataError: function(args) {
		this.showError(true, args.get_ErrorMessage());
	},
		
	tbl_SelectedIndexChanged: function() {
        //$("#divDbn").show();
        //$("#hypShowHideDbn").val("-");
	 this._isEditEnable = this._tbl.getSelectedExtraData().IsParentDebitLineNo;
	 	//alert(this._isEditEnable);
		this.enableEditButtons(true);
		this._intLineID = this._tbl._varSelectedValue;
		this.getLineData();
	
		//$R_FN.scrollPageToElement(this._element);
	},

	prevLine: function() {
		var intNewIndex = this._tbl._intSelectedIndex - 1;
		if (intNewIndex < 0) return;
		this._tbl.selectRow(intNewIndex, true);
	},
	
	nextLine: function() {
		var intNewIndex = this._tbl._intSelectedIndex + 1;
		if (intNewIndex >= this._intLineCount) return;
		this._tbl.selectRow(intNewIndex, true);
	},
	
	getLineData: function() {
		this._intLineDataCalls += 1;
		this._blnLineLoaded = false;
		this.enableEditButtons(false);
		this.showLoading(true);
		$R_FN.showElement(this._pnlLoadingLineDetail, true);
		$R_FN.showElement(this._pnlLineDetailError, false);
		$R_FN.showElement(this._pnlLineDetail, false);
		var obj = new Rebound.GlobalTrader.Site.Data();
		obj.set_PathToData(this._strDataPath);
		obj.set_DataObject(this._strDataObject);
		obj.set_DataAction("GetData");
		obj.addParameter("id", this._intLineID);
		obj.addDataOK(Function.createDelegate(this, this.getLineDataOK));
		obj.addError(Function.createDelegate(this, this.getLineDataError));
		obj.addTimeout(Function.createDelegate(this, this.getLineDataError));
		$R_DQ.addToQueue(obj);
		$R_DQ.processQueue();
		obj = null;
	},

	getLineDataError: function(args) {
		this._intLineDataCalls -= 1;
		if (this._intLineDataCalls < 1) this.showLoading(false);
		$R_FN.showElement(this._pnlLoadingLineDetail, false);
		$R_FN.showElement(this._pnlLineDetail, false);
		$R_FN.showElement(this._pnlLineDetailError, true);
		$R_FN.setInnerHTML(this._pnlLineDetailError, args.get_ErrorMessage());
	},

	getLineDataOK: function(args) {
		this._intLineDataCalls -= 1;
		if (this._intLineDataCalls < 1) this.showLoading(false);
		$R_FN.showElement(this._pnlLineDetailError, false);
		var result = args._result;
		this.setFieldValue("ctlQuantity", result.Quantity);
		this.setFieldValue("ctlPrice", $R_FN.setCleanTextValue(result.Price));
		this.setFieldValue("hidPriceRaw", result.PriceRaw);
		this.setFieldValue("ctlPartNo", $RGT_nubButton_Stock(result.StockNo, result.Part, result.ROHS));
		this.setFieldValue("hidPartNo", result.Part);
		this.setFieldValue("ctlManufacturer", $RGT_nubButton_Manufacturer(result.ManufacturerNo, result.Manufacturer, result.MfrAdvisoryNotes));
		this.setFieldValue("hidManufacturer", $R_FN.setCleanTextValue(result.Manufacturer));
		this.setFieldValue("hidManufacturerNo", result.ManufacturerNo);
		this.setFieldValue("ctlSupplierPart", $R_FN.setCleanTextValue(result.SupplierPart));
	    //this.setFieldValue("ctlProduct", $R_FN.setCleanTextValue(result.Product));
		this.setFieldValue("ctlProduct", $R_FN.setCleanTextValue(result.Product));
		//this.setFieldValue("ctlProductDis", $R_FN.showHazardous(result.Product, result.IsProdHaz));
        this.setFieldValue("ctlProductDis", $R_FN.showHazardousNew(result.Product, result.IsProdHaz, $R_FN.setCleanTextValue(result.ProductMessage)));
		this.setFieldValue("hidProductNo", result.ProductNo);
		this.setFieldValue("ctlPackage", $R_FN.setCleanTextValue(result.Package));
		this.setFieldValue("hidPackageNo", result.PackageNo);
		this.setFieldValue("ctlROHS", $R_FN.writeROHS(result.ROHS));
		this.setFieldValue("hidROHS", result.ROHS);
		this.setFieldValue("ctlDateCode", $R_FN.setCleanTextValue(result.DC));
		this.setFieldValue("ctlTaxable", $R_FN.setCleanTextValue(result.Taxable));
		this._blnLineIsService = result.IsService;
		this.setFieldValue("ctlService", $RGT_nubButton_Service(result.ServiceNo, result.Part));
		this.setFieldValue("hidService", $R_FN.setCleanTextValue(result.Part));
		this.setFieldValue("ctlServiceDescription", $R_FN.setCleanTextValue(result.SupplierPart));
		this.setFieldValue("ctlLineNotes", $R_FN.setCleanTextValue(result.LineNotes));
		this.showField("ctlPartNo", !this._blnLineIsService);
		this.showField("ctlManufacturer", !this._blnLineIsService);
		this.showField("ctlSupplierPart", !this._blnLineIsService);
		this.showField("ctlProduct", !this._blnLineIsService);
		this.showField("ctlPackage", !this._blnLineIsService);
		this.showField("ctlROHS", !this._blnLineIsService);
		this.showField("ctlDateCode", !this._blnLineIsService);
		this.showField("ctlService", this._blnLineIsService);
		this.showField("ctlServiceDescription", this._blnLineIsService);

		this.setFieldValue("hidProductHazar", result.IsProdHaz);
		this.setFieldValue("hidPrintHaza", result.IsPrintHaz);
		$R_FN.showElement(this._pnlLineDetail, true);
		$R_FN.showElement(this._pnlLoadingLineDetail, false);
		$R_FN.setInnerHTML(this._lblLineNumber, String.format($R_RES.LineXOfY, this._tbl._intSelectedIndex + 1, this._intLineCount));
		this._blnLineLoaded = true;
		this.enableEditButtons(true);
	},
	
	showAddForm: function() {
	    this._frmAdd._intDebitID = this._intDebitID;
	    this._frmAdd._intGlobalClientNo = this._intGlobalClientNo;
	    this._frmAdd._blnGlobalUser = this._blnGlobalUser;
		this.showForm(this._frmAdd, true);
	},
	
	hideAddForm: function() {
		this.showForm(this._frmAdd, false);
		this._tbl.resizeColumns();
	},
	
	saveAddComplete: function() {
		this.hideAddForm();
		this._intLineID = this._frmAdd._intLineID;
		this.getData();
		this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
	},
	
	showEditForm: function() {
		this._frmEdit._intLineID = this._intLineID;
		this._frmEdit._blnLineIsService = this._blnLineIsService;
		this._frmEdit.setFieldValue("ctlPartNo", $R_FN.writePartNo(this.getFieldValue("hidPartNo"), this.getFieldValue("hidROHS")));
		this._frmEdit.setFieldValue("ctlManufacturer", this.getFieldValue("hidManufacturer"));
		this._frmEdit.setFieldValue("ctlDateCode", this.getFieldValue("ctlDateCode"));
		this._frmEdit.setFieldValue("ctlPackage", this.getFieldValue("ctlPackage"));
		this._frmEdit.setFieldValue("ctlProduct", this.getFieldValue("ctlProduct"));
		this._frmEdit.setFieldValue("ctlQuantity", this.getFieldValue("ctlQuantity"));
		this._frmEdit.setFieldValue("ctlPrice", this.getFieldValue("hidPriceRaw"));
		this._frmEdit.setFieldValue("ctlService", this.getFieldValue("hidService"));
		this._frmEdit.setFieldValue("ctlServiceDescription", this.getFieldValue("ctlServiceDescription"));
		this._frmEdit.setFieldValue("ctlLineNotes", this.getFieldValue("ctlLineNotes"));
		this._frmEdit.setFieldValue("ctlPrintHazWar", Boolean.parse(this.getFieldValue("hidPrintHaza")));
		this._frmEdit._blnProductHaza = Boolean.parse(this.getFieldValue("hidProductHazar"));
		this.showForm(this._frmEdit, true);
	},
	
	hideEditForm: function() {
		this.showForm(this._frmEdit, false);
		this._tbl.resizeColumns();
	},
	
	saveEditComplete: function() {
		this.hideEditForm();
		this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
		this.getData();
	},
	
	showDeleteForm: function() {
		this._frmDelete._intLineID = this._intLineID;
		this._frmDelete._blnLineIsService = this._blnLineIsService;
		this._frmDelete.setFieldValue("ctlPartNo", $R_FN.writePartNo(this.getFieldValue("hidPartNo"), this.getFieldValue("hidROHS")));
		this._frmDelete.setFieldValue("ctlManufacturer", this.getFieldValue("hidManufacturer"));
		this._frmDelete.setFieldValue("ctlDateCode", this.getFieldValue("ctlDateCode"));
		this._frmDelete.setFieldValue("ctlPackage", this.getFieldValue("ctlPackage"));
		this._frmDelete.setFieldValue("ctlProduct", this.getFieldValue("ctlProduct"));
		this._frmDelete.setFieldValue("ctlService", this.getFieldValue("hidService"));
		this._frmDelete.setFieldValue("ctlServiceDescription", this.getFieldValue("ctlServiceDescription"));
		this.showForm(this._frmDelete, true);
	},
	
	hideDeleteForm: function() {
		this.showForm(this._frmDelete, false);
		this._tbl.resizeColumns();
	},
	
	deleteComplete: function() {
		this.hideDeleteForm();
		this.getData();		
	}
		
};

Rebound.GlobalTrader.Site.Controls.Nuggets.DebitLines.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.DebitLines", Rebound.GlobalTrader.Site.Controls.Nuggets.Base);
