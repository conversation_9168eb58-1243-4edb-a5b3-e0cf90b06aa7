CREATE OR ALTER PROCEDURE [dbo].[usp_update_PurchaseOrderLine]                                              
--******************************************************************************************                          
--*  08.06.2023:  By <PERSON><PERSON> for RP-1772                                              
--*  [003]     IPO allocated po stock line negative stock calculation                        
--*                          
--* SK 29.10.2009:                                              
--* - allow for new column - FullSupplierPart - used for searching                                              
--*                                                
--* SK 29/07/2009:                                              
--* - allow for Notes                                        
--* [002]      Abhinav <PERSON> 29/07/2021  Added new field for repeat order.                                        
--******************************************************************************************                                              
    @PurchaseOrderLineId int                                              
  , @Part nvarchar(30)                                              
  , @ManufacturerNo int = NULL                                              
  , @DateCode nvarchar(5) = NULL                                              
  , @PackageNo int = NULL                                              
  , @Quantity int                                              
  , @Price float                                              
  , @DeliveryDate datetime                                              
  , @ReceivingNotes nvarchar(max) = NULL                                              
  , @Taxable bit                                              
  , @ProductNo int = NULL                                              
  , @ShipInCost float = NULL                                              
  , @SupplierPart nvarchar(30) = NULL                                              
  , @Inactive bit                                              
  , @ROHS tinyint  = NULL                                            
  , @Notes nvarchar(2000) = Null                                               
  , @PromiseDate datetime                                              
  , @UpdatedBy int = NULL                                              
  , @ReqSerialNo  bit = 0                                        
  , @MSLLevel nvarchar(100) = null                                           
  , @PrintHazardous bit = null                                      
  , @StatusId int = null                               
  --[002] code start                                   
  , @RepeatOrder bit=0              
  , @Eprflag bit =0            
  --[002]  code end                                  
  , @RowsAffected int = NULL OUTPUT                                              
   ,@TempLogFlag   bit =0 output                                     
AS               
if @Eprflag =1            
begin            
declare              
   @PrevPurchaseOrderLineId int                                              
  , @PrevPart nvarchar(30)                                              
  , @PrevManufacturerNo int = NULL                                              
  , @PrevDateCode nvarchar(5) = NULL                                              
  , @PrevPackageNo int = NULL                                              
  , @PrevQuantity int                                              
  , @PrevPrice float                                              
  , @PrevDeliveryDate datetime                                              
  , @PrevReceivingNotes nvarchar(max) = NULL                                              
  , @PrevTaxable bit                                              
  , @PrevProductNo int = NULL                                              
  , @PrevShipInCost float = NULL                                              
  , @PrevSupplierPart nvarchar(30) = NULL                                              
  , @PrevInactive bit                                              
  , @PrevROHS tinyint  = NULL                 
  , @PrevNotes nvarchar(2000) = Null                                               
  , @PrevPromiseDate datetime                                              
  , @PrevUpdatedBy int = NULL                                              
  , @PrevReqSerialNo  bit = 0                                        
  , @PrevMSLLevel nvarchar(100) = null            
  , @PrevPrintHazardous bit = null                                      
  , @PrevStatusId int = null                               
  --[002] code start                                   
  , @TempFlag bit=0                      
  ,@PrevRepeatOrder bit=0        
  , @taxableval int = NULL       
   , @serialnoval int = NULL      
 , @repeatorderval int = NULL     
  ,  @kPrevPurchaseOrderLineId int                                              
  , @kPrevPart nvarchar(30)                                              
  , @kPrevManufacturerNo int = NULL                                              
  , @kPrevDateCode nvarchar(5) = NULL                                              
  , @kPrevPackageNo int = NULL                                              
  , @kPrevQuantity int                                              
  , @kPrevPrice float                                              
  , @kPrevDeliveryDate datetime                                              
  , @kPrevReceivingNotes nvarchar(max) = NULL                                              
  , @kPrevTaxable bit                                              
  , @kPrevProductNo int = NULL                                              
  , @kPrevShipInCost float = NULL                                              
  , @kPrevSupplierPart nvarchar(30) = NULL                                              
  , @kPrevInactive bit                                              
  , @kPrevROHS tinyint  = NULL                 
  , @kPrevNotes nvarchar(2000) = Null                                               
  , @kPrevPromiseDate datetime                                              
  , @kPrevUpdatedBy int = NULL                                              
  , @kPrevReqSerialNo  bit = 0                                        
  , @kPrevMSLLevel nvarchar(100) = null            
  , @kPrevPrintHazardous bit = null                                      
  , @kPrevStatusId int = null                               
  --[002] code start                                   
  , @kTempFlag bit=0                      
  ,@kPrevRepeatOrder bit=0        
  , @ktaxableval int = NULL       
   , @kserialnoval int = NULL      
 , @krepeatorderval int = NULL     
   
SELECT               
       @kPrevPart = Part,              
      @kPrevManufacturerNo = ManufacturerNo,              
       @kPrevDateCode = DateCode,              
       @kPrevPackageNo = PackageNo,              
       @kPrevQuantity = Quantity,              
       @kPrevPrice = Price,              
       @kPrevDeliveryDate = DeliveryDate,              
       @kPrevReceivingNotes = ReceivingNotes,              
       @kPrevTaxable = Taxable,              
       @kPrevProductNo = ProductNo,              
       @kPrevShipInCost = ShipInCost,              
       @kPrevSupplierPart = SupplierPart,              
       @kPrevInactive = Inactive,              
       @kPrevROHS = ROHS,              
       @kPrevNotes = Notes,              
       @kPrevPromiseDate = PromiseDate,              
       @kPrevUpdatedBy = UpdatedBy,              
       @kPrevReqSerialNo = ReqSerialNo,              
       @kPrevMSLLevel = MSLLevel,              
       @kPrevPrintHazardous = PrintHazardous,              
       @kPrevStatusId = POLineStatusNo,              
       @kPrevRepeatOrder = RepeatOrder              
FROM tbPurchaseOrderLine where PurchaseOrderLineId=@PurchaseOrderLineId ;                  
SELECT               
       @PrevPart = Part,              
      @PrevManufacturerNo = ManufacturerNo,              
       @PrevDateCode = DateCode,              
       @PrevPackageNo = PackageNo,              
       @PrevQuantity = Quantity,              
       @PrevPrice = Price,              
       @PrevDeliveryDate = DeliveryDate,              
       @PrevReceivingNotes = ReceivingNotes,              
       @PrevTaxable = Taxable,              
       @PrevProductNo = ProductNo,              
       @PrevShipInCost = ShipInCost,              
       @PrevSupplierPart = SupplierPart,              
       @PrevInactive = Inactive,              
       @PrevROHS = ROHS,              
       @PrevNotes = Notes,              
       @PrevPromiseDate = PromiseDate,              
       @PrevUpdatedBy = UpdatedBy,              
       @PrevReqSerialNo = ReqSerialNo,              
       @PrevMSLLevel = MSLLevel,              
       @PrevPrintHazardous = PrintHazardous,              
       @PrevStatusId = POLineStatusNo,              
       @PrevRepeatOrder = RepeatOrder              
FROM tbPurchaseOrderLine where PurchaseOrderLineId=@PurchaseOrderLineId ;      
    
              
              
--print              
--@PrevPart              
IF ISNULL(@PrevPart,'') = ISNULL(@Part,'') BEGIN SET @PrevPart = NULL END ELSE BEGIN SET @PrevPart = @Part SET @TempFlag = 1 END;      
IF ISNULL(@PrevManufacturerNo,'') = ISNULL(@ManufacturerNo,'') BEGIN SET @PrevManufacturerNo = NULL END ELSE BEGIN SET @PrevManufacturerNo = @ManufacturerNo SET @TempFlag = 1 END;      
IF ISNULL(@PrevDateCode,'') = ISNULL(@DateCode,'') BEGIN SET @PrevDateCode = NULL END ELSE BEGIN SET @PrevDateCode = @DateCode SET @TempFlag = 1 END;      
IF ISNULL(@PrevPackageNo,'') = ISNULL(@PackageNo,'') BEGIN SET @PrevPackageNo = NULL END ELSE BEGIN SET @PrevPackageNo = @PackageNo SET @TempFlag = 1 END;      
IF ISNULL(@PrevQuantity,'') = ISNULL(@Quantity,'') BEGIN SET @PrevQuantity = NULL END ELSE BEGIN SET @PrevQuantity = @Quantity SET @TempFlag = 1 END;      
IF ISNULL(@PrevPrice,'') = ISNULL(@Price,'') BEGIN SET @PrevPrice = NULL END ELSE BEGIN SET @PrevPrice = @Price SET @TempFlag = 1 END;      
IF ISNULL(@PrevDeliveryDate,'') = ISNULL(@DeliveryDate,'') BEGIN SET @PrevDeliveryDate = NULL END ELSE BEGIN SET @PrevDeliveryDate = @DeliveryDate SET @TempFlag = 1 END;      
IF ISNULL(@PrevReceivingNotes,'') = ISNULL(@ReceivingNotes,'') BEGIN SET @PrevReceivingNotes = NULL END ELSE BEGIN SET @PrevReceivingNotes = @ReceivingNotes SET @TempFlag = 1 END;      
IF ISNULL(@PrevTaxable,'') = ISNULL(@Taxable,'') BEGIN SET @taxableval = 2 END ELSE BEGIN SET @taxableval = @Taxable SET @TempFlag = 1 END;      
IF ISNULL(@PrevNotes,'') = ISNULL(@Notes,'') BEGIN SET @PrevNotes = NULL END ELSE BEGIN SET @PrevNotes = @Notes SET @TempFlag = 1 END;      
IF ISNULL(@PrevProductNo,'') = ISNULL(@ProductNo,'') BEGIN SET @PrevProductNo = NULL END ELSE BEGIN SET @PrevProductNo = @ProductNo SET @TempFlag = 1 END;      
IF ISNULL(@PrevShipInCost,'') = ISNULL(@ShipInCost,'') BEGIN SET @PrevShipInCost = NULL END ELSE BEGIN SET @PrevShipInCost = @ShipInCost SET @TempFlag = 1 END;      
IF ISNULL(@PrevSupplierPart,'') = ISNULL(@SupplierPart,'') BEGIN SET @PrevSupplierPart = NULL END ELSE BEGIN SET @PrevSupplierPart = @SupplierPart SET @TempFlag = 1 END;      
IF ISNULL(@PrevInactive,'') = ISNULL(@Inactive,'') BEGIN SET @PrevInactive = NULL END ELSE BEGIN SET @PrevInactive = @Inactive SET @TempFlag = 1 END;      
IF ISNULL(@PrevROHS,'') = ISNULL(@ROHS,'') BEGIN SET @PrevROHS = NULL END ELSE BEGIN SET @PrevROHS = @ROHS SET @TempFlag = 1 END;      
IF ISNULL(@PrevPromiseDate,'') = ISNULL(@PromiseDate,'') BEGIN SET @PrevPromiseDate = NULL END ELSE BEGIN SET @PrevPromiseDate = @PromiseDate SET @TempFlag = 1 END;      
IF ISNULL(@PrevReqSerialNo,'') = ISNULL(@ReqSerialNo,'') BEGIN SET @serialnoval = 2 END ELSE BEGIN SET @serialnoval = @ReqSerialNo SET @TempFlag = 1 END;      
IF ISNULL(@PrevMSLLevel,'') = ISNULL(@MSLLevel,'') BEGIN SET @PrevMSLLevel = NULL END ELSE BEGIN SET @PrevMSLLevel = @MSLLevel SET @TempFlag = 1 END;      
IF ISNULL(@PrevPrintHazardous,'') = ISNULL(@PrintHazardous,'') BEGIN SET @PrevPrintHazardous = NULL END ELSE BEGIN SET @PrevPrintHazardous = @PrintHazardous SET @TempFlag = 1 END;      
IF ISNULL(@PrevStatusId,'') = ISNULL(@StatusId,'') BEGIN SET @PrevStatusId = NULL END ELSE BEGIN SET @PrevStatusId = @StatusId SET @TempFlag = 1 END;      
IF ISNULL(@PrevRepeatOrder,'') = ISNULL(@RepeatOrder,'') BEGIN SET @repeatorderval = 2 END ELSE BEGIN SET @repeatorderval = @RepeatOrder SET @TempFlag = 1 END;      
            
 Set @TempLogFlag= @TempFlag              
              
  if @TempLogFlag=1               
 begin              
 INSERT INTO prevPurchaseOrderLineLogDetails (              
    PurchaseOrderLineId,              
    Part,              
    ManufacturerNo,              
    DateCode,              
    PackageNo,              
    Quantity,              
    Price,              
    DeliveryDate,              
    ReceivingNotes,              
    Taxable,              
    ProductNo,              
    ShipInCost,              
    SupplierPart,              
    Inactive,              
    ROHS,              
    Notes,              
    PromiseDate,              
    UpdatedBy,              
    ReqSerialNo,              
    MSLLevel,              
    PrintHazardous,              
    StatusId,              
    RepeatOrder ,             
    taxableval  ,      
 serialnreqval,      
 repeatorderval      
)              
VALUES (              
      @PurchaseOrderLineId,              
      @kPrevPart,              
      @kPrevManufacturerNo,              
      @kPrevDateCode,              
      @kPrevPackageNo,              
      @kPrevQuantity,              
      @kPrevPrice,              
      @kPrevDeliveryDate,              
      @kPrevReceivingNotes,              
      @kPrevTaxable,              
      @kPrevProductNo,              
      @kPrevShipInCost,              
      @kPrevSupplierPart,              
      @kPrevInactive,              
      @kPrevROHS,              
      @kPrevNotes,              
      @kPrevPromiseDate,              
      @kPrevUpdatedBy,              
      @kPrevReqSerialNo,              
      @kPrevMSLLevel,              
      @kPrevPrintHazardous,              
      @kPrevStatusId,              
      @kPrevRepeatOrder,      
   @ktaxableval,      
   @kserialnoval,      
   @krepeatorderval      
);  
 INSERT INTO PurchaseOrderLineLogDetails (              
    PurchaseOrderLineId,              
    Part,              
    ManufacturerNo,              
    DateCode,              
    PackageNo,              
    Quantity,              
    Price,              
    DeliveryDate,              
    ReceivingNotes,              
    Taxable,              
    ProductNo,              
    ShipInCost,              
    SupplierPart,              
    Inactive,              
    ROHS,              
    Notes,              
    PromiseDate,              
    UpdatedBy,              
    ReqSerialNo,              
    MSLLevel,              
    PrintHazardous,              
    StatusId,              
    RepeatOrder ,             
    taxableval  ,      
 serialnreqval,      
 repeatorderval      
)              
VALUES (              
      @PurchaseOrderLineId,              
      @PrevPart,              
      @PrevManufacturerNo,              
      @PrevDateCode,              
      @PrevPackageNo,              
      @PrevQuantity,              
      @PrevPrice,              
      @PrevDeliveryDate,              
      @PrevReceivingNotes,              
      @PrevTaxable,              
      @PrevProductNo,              
      @PrevShipInCost,              
      @PrevSupplierPart,              
      @PrevInactive,              
      @PrevROHS,              
      @PrevNotes,              
      @PrevPromiseDate,              
      @PrevUpdatedBy,              
      @PrevReqSerialNo,              
      @PrevMSLLevel,              
      @PrevPrintHazardous,              
      @PrevStatusId,              
      @PrevRepeatOrder,      
   @taxableval,      
   @serialnoval,      
   @repeatorderval      
);             
 end;             
 end            
 else          
 begin          
 Set @TempLogFlag=0          
 end          
              
 --Unrelease the poline if price and qty gets changed                                      
 DECLARE   @OldQuantity int , @OldPrice float ,                    
 @AllocationId int=Null, @PurchaseOrderId INT=Null ,@SoNo int=Null,                     
 @PartName varchar(100)='',@LineNo varchar(200)=Null, @ExpediteNotes   nvarchar(MAX) = Null, @IsMailSent bit = NULL ,@IPO INT=Null                    
 ,@body varchar(max)='', @SalesmanName varchar(100)='',@salesmanId int=Null,@SalesmanEmail varchar(100)='' ;                                     
 SELECT TOP 1 @OldQuantity=Quantity,@OldPrice=Price FROM dbo.tbPurchaseOrderLine WHERE   PurchaseOrderLineId = @PurchaseOrderLineId AND INACTIVE =0 ;                        
                        
 IF(@Quantity != @OldQuantity OR @Price!=@OldPrice)                                      
 BEGIN                                      
  UPDATE  dbo.tbPurchaseOrderLine            
  SET     ReleaseBy = null,                                              
    ReleaseDate = null                                              
  WHERE   PurchaseOrderLineId = @PurchaseOrderLineId                                        
 END                          
  --[003] code start                         
 IF(@Quantity != @OldQuantity)                            
 BEGIN              
SELECT top 1 @AllocationId=al.AllocationId,                        
             @SoNo=   so.SalesOrderNumber,                        
            @LineNo= cast(tpl.PurchaseOrderLineId as varchar(200)),   --tpl.POSerialNo                     
             @PartName= tpl.Part,                        
             @PurchaseOrderId=tpl.PurchaseOrderNo,                        
          --@IPO = ipo.InternalPurchaseOrderNo                      
            @IPO = tp.InternalPurchaseOrderNumber,                    
   @SalesmanName=l.EmployeeName,                    
   @SalesmanEmail=l.EMail,                    
   @salesmanId=so.Salesman                   
      FROM  tbPurchaseOrderLine tpl                         
 inner join tbInternalPurchaseOrderLine ipo on tpl.PurchaseOrderLineId=ipo.PurchaseOrderLineNo                         
 left join tbAllocation al on ipo.SalesOrderLineNo=al.SalesOrderLineNo                        
 inner join tbInternalPurchaseOrder tp on tpl.PurchaseOrderNo=tp.PurchaseOrderNo                        
 inner join tbsalesorderline sol on ipo.SalesOrderLineNo=sol.SalesOrderLineId                        
 inner join tbSalesOrder so on sol.SalesOrderNo=so.SalesOrderId                        
 inner join tbLogin l on so.Salesman=l.LoginId                    
 where ipo.PurchaseOrderLineNo=@PurchaseOrderLineId;                        
                    
  set @ExpediteNotes ='PO Quantity ordered for Sales Order No : '+ cast(@SoNo as varchar(100))                         
  +' and  Line # : '+ cast(@LineNo as varchar(100))+' Part '+@PartName+' has been reduced by IPO Team. <br/> Kindly review and rellocate from IPO NO : '                        
  +cast(@IPO as varchar(100))+''                             
set @body ='Dear  ' +@SalesmanName+', <br/><br/>                    
             PO Quantity ordered for Sales Order No : '+ cast(@SoNo as varchar(100))                         
  +' and  Line # : '+ cast(@LineNo as varchar(100))+' Part '+@PartName+' has been reduced by IPO Team. <br/> Kindly review and rellocate from IPO NO : '                        
  +cast(@IPO as varchar(100))+' <br/><br/>                    
        Thanks'         
                          
  /*  Proc For DeAllocate Line  */                      
  IF(ISNULL(@AllocationId,0)>0)                    
  BEGIN                    
     EXEC [dbo].[usp_delete_Allocation]  @AllocationId,@UpdatedBy;                      
  END                    
                     
       /*  Proc For Expediate Note  */                                            
 EXEC [dbo].[usp_insert_ExpediteNote] @PurchaseOrderId,@ExpediteNotes,@UpdatedBy,@LineNo,1                     
                     
    /*  Mail Sending Work  */                     
insert into tbMailMessage                      
(FromLoginNo,ToLoginNo,Subject,Body,DateSent,CompanyNo,UpdatedBy,DLUP,RecipientHasBeenNotified,HasBeenRead)                      
 values                      
 (0,@salesmanId,'You have a new Expedite Note',@body,getdate(),0,@UpdatedBy,Getdate(),1,0)                      
                    
 insert into tbEmailer (FromEmail,ToEmail,Subject,Body,DateSent,UpdatedBy,DLUP, process)                      
  values                      
  (null,@SalesmanEmail,'You have a new Expedite Note',@body,null,@UpdatedBy,GETDATE(),0)                      
                    
 END                        
   --[003] code END                         
                        
    ---------------Update PurchaseOrderLine Table ------------------                                          
    UPDATE  dbo.tbPurchaseOrderLine                                              
    SET     FullPart = dbo.ufn_get_fullpart(@Part)                                              
          , Part = @Part                                              
          , ManufacturerNo = @ManufacturerNo                                              
          , DateCode = @DateCode                                              
          , PackageNo = @PackageNo                                              
          , Quantity = @Quantity                                              
         , Price = @Price                                              
          , DeliveryDate = @DeliveryDate                                              
          , ReceivingNotes = @ReceivingNotes            
          , Taxable = @Taxable                                              
          , ProductNo = @ProductNo                                              
          , ShipInCost = @ShipInCost                                              
          , SupplierPart = @SupplierPart                                              
          , Inactive = @Inactive                                              
          , ROHS = @ROHS                                              
          , Notes = @Notes                                             
          , PromiseDate = @PromiseDate                                           
          , UpdatedBy = @UpdatedBy                                              
          , DLUP = CURRENT_TIMESTAMP                                              
          , FullSupplierPart = dbo.ufn_get_fullpart(@SupplierPart)                                         
    ,ReqSerialNo =@ReqSerialNo                                             
    , MSLLevel = @MSLLevel                                        
    , PrintHazardous = @PrintHazardous                                        
    , POLineStatusNo =  @StatusId                               
 --[002] code start                                  
 , RepeatOrder=@RepeatOrder             
 ,isPOLogFlag =@TempLogFlag        
--[002] code end                                
    WHERE   PurchaseOrderLineId = @PurchaseOrderLineId                                              
                                              
    ---------------Update InternalPurchaseOrderLine Table ------------------                                          
IF EXISTS(SELECT COUNT(InternalPurchaseOrderLineId) FROM tbInternalPurchaseOrderLine WHERE PurchaseOrderLineNo=@PurchaseOrderLineId)                                        
 BEGIN                                        
  UPDATE  dbo.tbInternalPurchaseOrderLine                                  
  SET      
     UpdatedBy = @UpdatedBy  ,                                        
     ProductNo = @ProductNo  ,                                     
 POLineStatusNo =  @StatusId                                      
                                                       
  WHERE PurchaseOrderLineNo = @PurchaseOrderLineId                                        
 END                                        
                                               
    SELECT  @RowsAffected = @@ROWCOUNT   
  