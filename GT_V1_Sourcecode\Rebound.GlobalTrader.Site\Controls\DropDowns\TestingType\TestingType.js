Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.TestingType=function(a){Rebound.GlobalTrader.Site.Controls.DropDowns.TestingType.initializeBase(this,[a])};Rebound.GlobalTrader.Site.Controls.DropDowns.TestingType.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.DropDowns.TestingType.callBaseMethod(this,"initialize");this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK))},dispose:function(){if(this.isDisposed)return;Rebound.GlobalTrader.Site.Controls.DropDowns.TestingType.callBaseMethod(this,"dispose")},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/TestingType");this._objData.set_DataObject("TestingType");this._objData.set_DataAction("GetData")},dataCallOK:function(){var a=this._objData._result;if(a!=null)if(a.Types)for(var b=0;b<a.Types.length;b++)this.addOption(a.Types[b].Name,a.Types[b].ID)}};Rebound.GlobalTrader.Site.Controls.DropDowns.TestingType.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.TestingType",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);