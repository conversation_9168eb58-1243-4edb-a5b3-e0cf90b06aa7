//--------------------------------------------------------------------------------------------------------
// RP 11.12.2009:
// - cache data
//--------------------------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;

namespace Rebound.GlobalTrader.Site.Controls.DropDowns.Data {
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class Country : Rebound.GlobalTrader.Site.Data.DropDowns.Base {

        public override void ProcessRequest(HttpContext context) {
            SetDropDownType("Country");
            base.ProcessRequest(context);
        }

        protected override void GetData()
        {
            int? intPOHubClientNo;
            intPOHubClientNo = GetFormValue_NullableInt("POHubClientNo");
            int? intGlobalLoginClientNo;
            intGlobalLoginClientNo = GetFormValue_NullableInt("GlobalLoginClientNo");
            string ddlCurrencyId = Convert.ToString(Rebound.GlobalTrader.Site.Site.GetInstance().GetDropDown("Country").ID);
            Functions.ClearAllCacheByDropDown(ddlCurrencyId);
            string strCacheOptions = CacheManager.SerializeOptions(new object[] { intGlobalLoginClientNo.HasValue? intGlobalLoginClientNo.Value: SessionManager.ClientID, (intPOHubClientNo.HasValue) ? intPOHubClientNo.Value : 0 });
            string strCachedData = CacheManager.GetDropDownData(_objDropDown.ID, strCacheOptions);
            if (string.IsNullOrEmpty(strCachedData))
            {
                JsonObject jsn = new JsonObject();
                JsonObject jsnCountries = new JsonObject(true);
                foreach (BLL.Country country in BLL.Country.DropDownForClient(intGlobalLoginClientNo.HasValue ? intGlobalLoginClientNo.Value : ((intPOHubClientNo.HasValue && intPOHubClientNo.Value > 0) ? intPOHubClientNo : SessionManager.ClientID)))
                {
                    JsonObject jsnCountry = new JsonObject();
                    jsnCountry.AddVariable("ID", country.CountryId);
                    jsnCountry.AddVariable("Name", country.CountryName);
                    jsnCountries.AddVariable(jsnCountry);
                    jsnCountry.Dispose(); jsnCountry = null;
                }
                jsn.AddVariable("Countries", jsnCountries);
                jsnCountries.Dispose();
                jsnCountries = null;
                CacheManager.StoreDropDown(_objDropDown.ID, strCacheOptions, jsn.Result, CacheManager.CacheExpiryType.OneWorkingDay);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            else
            {
                _context.Response.Write(strCachedData);
            }
            strCachedData = null;
        }
    }
}
