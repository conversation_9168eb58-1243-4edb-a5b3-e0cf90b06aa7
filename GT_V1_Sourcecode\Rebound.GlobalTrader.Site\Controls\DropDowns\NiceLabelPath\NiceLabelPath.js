Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.NiceLabelPath=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.NiceLabelPath.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DropDowns.NiceLabelPath.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.DropDowns.NiceLabelPath.callBaseMethod(this,"initialize");this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK))},dispose:function(){this.isDisposed||Rebound.GlobalTrader.Site.Controls.DropDowns.NiceLabelPath.callBaseMethod(this,"dispose")},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/NiceLabelPath");this._objData.set_DataObject("NiceLabelPath");this._objData.set_DataAction("GetData")},dataCallOK:function(){var t=this._objData._result,n;if(t.NiceLabels)for(n=0;n<t.NiceLabels.length;n++)this.addOption($R_FN.setCleanTextValueForBackSlash(t.NiceLabels[n].Name),t.NiceLabels[n].ID)}};Rebound.GlobalTrader.Site.Controls.DropDowns.NiceLabelPath.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.NiceLabelPath",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);