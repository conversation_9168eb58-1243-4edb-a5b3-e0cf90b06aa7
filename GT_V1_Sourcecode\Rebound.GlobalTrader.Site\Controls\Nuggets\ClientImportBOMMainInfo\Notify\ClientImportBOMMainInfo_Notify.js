Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.ClientImportBOMMainInfo_Notify=function(n){Rebound.GlobalTrader.Site.Controls.Forms.ClientImportBOMMainInfo_Notify.initializeBase(this,[n]);this._strPONumber=""};Rebound.GlobalTrader.Site.Controls.Forms.ClientImportBOMMainInfo_Notify.prototype={get_intBOMID:function(){return this._intBOMID},set_intBOMID:function(n){this._intBOMID!==n&&(this._intBOMID=n)},get_intCompanyID:function(){return this._intCompanyID},set_intCompanyID:function(n){this._intCompanyID!==n&&(this._intCompanyID=n)},get_ibtnSend:function(){return this._ibtnSend},set_ibtnSend:function(n){this._ibtnSend!==n&&(this._ibtnSend=n)},get_ibtnSend_Footer:function(){return this._ibtnSend_Footer},set_ibtnSend_Footer:function(n){this._ibtnSend_Footer!==n&&(this._ibtnSend_Footer=n)},get_stringCurrency:function(){return this._stringCurrency},set_stringCurrency:function(n){this._stringCurrencyD!==n&&(this._stringCurrency=n)},get_BomCode:function(){return this._BomCode},set_BomCode:function(n){this._BomCode!==n&&(this._BomCode=n)},get_BomName:function(){return this._BomName},set_BomName:function(n){this._BomName!==n&&(this._BomName=n)},get_BomCompanyName:function(){return this._BomCompanyName},set_BomCompanyName:function(n){this._BomCompanyName!==n&&(this._BomCompanyName=n)},get_BomCompanyNo:function(){return this._BomCompanyNo},set_BomCompanyNo:function(n){this._BomCompanyNo!==n&&(this._BomCompanyNo=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.ClientImportBOMMainInfo_Notify.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown))},formShown:function(){this._blnFirstTimeShown&&($R_IBTN.addClick(this._ibtnSend,Function.createDelegate(this,this.sendMail)),$R_IBTN.addClick(this._ibtnSend_Footer,Function.createDelegate(this,this.sendMail)),this._ctlMail=$find(this.getField("ctlSendMailMessage").ID),this._ctlMail._ctlRelatedForm=this,this.getMessage())},dispose:function(){this.isDisposed||(this._ibtnSend&&$R_IBTN.clearHandlers(this._ibtnSend),this._ibtnSend_Footer&&$R_IBTN.clearHandlers(this._ibtnSend_Footer),this._ctlMail&&this._ctlMail.dispose(),this._ctlMail=null,this._strPONumber=null,this._intCompanyID=null,this._intPurchaseOrderID=null,this._ibtnSend=null,this._ibtnSend_Footer=null,Rebound.GlobalTrader.Site.Controls.Forms.ClientImportBOMMainInfo_Notify.callBaseMethod(this,"dispose"))},getMessage:function(){this._ctlMail.setValue_Body("Kindly find the attached HUBRFQ regarding our requirements.<br/>Please update the price column of the sheet & send us back for further processing.");this._ctlMail.setValue_Subject("HUBRFQ "+this._BomCode+" Notification")},sendMail:function(){this.validateForm()&&(this.showSaving(!0),Rebound.GlobalTrader.Site.WebServices.NotifyMessageSupplier($R_FN.arrayToSingleString(this._ctlMail._aryRecipientEmail),this._ctlMail.getValue_Subject(),this._ctlMail.getValue_Body(),114,this._intBOMID,this._intCompanyID,$R_FN.arrayToSingleString(this._ctlMail._aryCompanyIDs),this._stringCurrency,$R_FN.arrayToSingleString(this._ctlMail._aryRecipientLoginNames),Function.createDelegate(this,this.sendMailComplete)))},validateForm:function(){this._ctlMail.setKeyValueArray();var n=this._ctlMail.validateFields();return n||this.showError(!0),n},sendMailComplete:function(){this.showSaving(!1);this.showSavedOK(!0);this.onSaveComplete()}};Rebound.GlobalTrader.Site.Controls.Forms.ClientImportBOMMainInfo_Notify.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.ClientImportBOMMainInfo_Notify",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);