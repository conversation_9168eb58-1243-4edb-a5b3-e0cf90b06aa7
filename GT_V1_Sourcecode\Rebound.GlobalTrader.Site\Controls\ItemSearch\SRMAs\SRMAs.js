Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.ItemSearch");Rebound.GlobalTrader.Site.Controls.ItemSearch.SRMAs=function(n){Rebound.GlobalTrader.Site.Controls.ItemSearch.SRMAs.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.ItemSearch.SRMAs.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.ItemSearch.SRMAs.callBaseMethod(this,"initialize");this.addSetupData(Function.createDelegate(this,this.doSetupData));this.addGetDataComplete(Function.createDelegate(this,this.doGetDataComplete))},dispose:function(){this.isDisposed||Rebound.GlobalTrader.Site.Controls.ItemSearch.SRMAs.callBaseMethod(this,"dispose")},doSetupData:function(){this._objData.set_PathToData("controls/ItemSearch/SRMAs");this._objData.set_DataObject("SRMAs");this._objData.set_DataAction("GetData");this._objData.addParameter("SRMANoLo",this.getFieldValue_Min("ctlSRMANo"));this._objData.addParameter("SRMANoHi",this.getFieldValue_Max("ctlSRMANo"));this._objData.addParameter("Contact",this.getFieldValue("ctlContact"));this._objData.addParameter("CMName",this.getFieldValue("ctlCompany"));this._objData.addParameter("Buyer",this.getFieldValue("ctlBuyer"));this._objData.addParameter("Notes",this.getFieldValue("ctlSRMANotes"));this._objData.addParameter("PONoLo",this.getFieldValue_Min("ctlPurchaseOrderNo"));this._objData.addParameter("PONoHi",this.getFieldValue_Max("ctlPurchaseOrderNo"));this._objData.addParameter("SRMADateFrom",this.getFieldValue("ctlSRMADateFrom"));this._objData.addParameter("SRMADateTo",this.getFieldValue("ctlSRMADateTo"))},doGetDataComplete:function(){for(var n,i,t=0,r=this._objResult.Results.length;t<r;t++)n=this._objResult.Results[t],i=[n.No,$R_FN.setCleanTextValue(n.CMName),$R_FN.setCleanTextValue(n.Date),$R_FN.setCleanTextValue(n.Buyer),$R_FN.setCleanTextValue(n.PurchaseOrderNo)],this._tblResults.addRow(i,n.ID,!1),i=null,n=null}};Rebound.GlobalTrader.Site.Controls.ItemSearch.SRMAs.registerClass("Rebound.GlobalTrader.Site.Controls.ItemSearch.SRMAs",Rebound.GlobalTrader.Site.Controls.ItemSearch.Base,Sys.IDisposable);