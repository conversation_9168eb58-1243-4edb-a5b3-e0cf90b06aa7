/****** Object:  cuongdx create funtion to get first n words from string [dbo].[GetFirstNWords]    Script Date: 5/30/2024 3:29:05 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

IF OBJECT_ID('dbo.GetFirstNWords', 'FN') IS NOT NULL
    DROP FUNCTION dbo.GetFirstNWords;
GO

-- Create the function
CREATE FUNCTION [dbo].[GetFirstNWords] (@InputString NVARCHAR(MAX), @N INT)
RETURNS NVARCHAR(MAX)
AS
BEGIN
    DECLARE @WordCount INT = 0;
    DECLARE @CurrentPosition INT = 0;
    DECLARE @NextSpacePosition INT;
    DECLARE @Result NVARCHAR(MAX) = '';
    DECLARE @Length INT = LEN(@InputString);
    
    -- Loop until we have collected N words or we reach the end of the string
    WHILE @WordCount < @N AND @CurrentPosition < @Length
    BEGIN
        -- Find the next space position
        SET @NextSpacePosition = CHARINDEX(' ', @InputString, @CurrentPosition + 1);
        
        -- If no more spaces are found, use the rest of the string
        IF @NextSpacePosition = 0
        BEGIN
            SET @NextSpacePosition = @Length + 1;
        END
        
        -- Append the current word to the result
        SET @Result = @Result + ' ' + SUBSTRING(@InputString, @CurrentPosition + 1, @NextSpacePosition - @CurrentPosition - 1);
        
        -- Move to the next position
        SET @CurrentPosition = @NextSpacePosition;
        SET @WordCount = @WordCount + 1;
    END
    
    -- Trim leading space from the result
    RETURN LTRIM(@Result);
END;
GO


