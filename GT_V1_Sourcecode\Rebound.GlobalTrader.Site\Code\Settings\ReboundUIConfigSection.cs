using System;
using System.Collections.Generic;
using System.Text;
using Rebound.GlobalTrader.Site.Settings;
using System.Configuration;

namespace Rebound.GlobalTrader.Site.Settings {
	/// <summary>
	/// This class is actually what loads the custom settings.
	/// </summary>
	public class ReboundUIConfigSection : System.Configuration.ConfigurationSection {

		private static string strConfigurationSectionConstant = "ReboundUIConfiguration";

		public static ReboundUIConfigSection GetConfig() {
			return (ReboundUIConfigSection)System.Configuration.ConfigurationManager.GetSection(ReboundUIConfigSection.strConfigurationSectionConstant) ?? new ReboundUIConfigSection();
		}

		[ConfigurationProperty("applicationID", IsRequired = true)]
		public int ApplicationID {
			get { return Convert.ToInt32(this["applicationID"]); }
		}

		[ConfigurationProperty("applicationName", IsRequired = true)]
		public string ApplicationName {
			get { return this["applicationName"] as string; }
		}

		[ConfigurationProperty("applicationNameShort", IsRequired = true)]
		public string ApplicationNameShort {
			get { return this["applicationNameShort"] as string; }
		}

		[ConfigurationProperty("templatePath", IsRequired = false)]
		public string TemplatePath {
			get { return this["templatePath"] as string; }
		}

		[ConfigurationProperty("sitePages")]
		public SitePageCollection SitePages {
			get { return (SitePageCollection)this["sitePages"] ?? new SitePageCollection(); }
		}

		[ConfigurationProperty("siteSections")]
		public SiteSectionCollection SiteSections {
			get { return (SiteSectionCollection)this["siteSections"] ?? new SiteSectionCollection(); }
		}

		[ConfigurationProperty("columnWidths")]
		public ColumnWidthCollection ColumnWidths {
			get { return (ColumnWidthCollection)this["columnWidths"] ?? new ColumnWidthCollection(); }
		}

		[ConfigurationProperty("dropDowns")]
		public DropDownCollection DropDowns {
			get { return (DropDownCollection)this["dropDowns"] ?? new DropDownCollection(); }
		}

		[ConfigurationProperty("autoSearches")]
		public AutoSearchCollection AutoSearches {
			get { return (AutoSearchCollection)this["autoSearches"] ?? new AutoSearchCollection(); }
		}

		[ConfigurationProperty("itemSearches")]
		public ItemSearchCollection ItemSearches {
			get { return (ItemSearchCollection)this["itemSearches"] ?? new ItemSearchCollection(); }
		}

		[ConfigurationProperty("nuggets")]
		public NuggetCollection Nuggets {
			get { return (NuggetCollection)this["nuggets"] ?? new NuggetCollection(); }
		}

		[ConfigurationProperty("leftNuggets")]
		public LeftNuggetCollection LeftNuggets {
			get { return (LeftNuggetCollection)this["leftNuggets"] ?? new LeftNuggetCollection(); }
		}

		[ConfigurationProperty("dataListNuggets")]
		public DataListNuggetCollection DataListNuggets {
			get { return (DataListNuggetCollection)this["dataListNuggets"] ?? new DataListNuggetCollection(); }
		}

		[ConfigurationProperty("setupNuggets")]
		public SetupNuggetCollection SetupNuggets {
			get { return (SetupNuggetCollection)this["setupNuggets"] ?? new SetupNuggetCollection(); }
		}

		[ConfigurationProperty("homeNuggets")]
		public HomeNuggetCollection HomeNuggets {
			get { return (HomeNuggetCollection)this["homeNuggets"] ?? new HomeNuggetCollection(); }
		}

	}
}