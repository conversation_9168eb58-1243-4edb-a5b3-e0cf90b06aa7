﻿<browsers>
    <browser id="IE" parentID="Mozilla">
        <identification>
            <userAgent match="^Mozilla[^(]*\([C|c]ompatible;\s*MSIE (?'version'(?'major'\d+)(?'minor'\.\d+)(?'letters'\w*))(?'extra'[^)]*)" />
            <userAgent nonMatch="Opera|Go\.Web|Windows CE|EudoraWeb" />
        </identification>

        <capture>
        </capture>

        <capabilities>
            <capability name="browser"              value="IE" />
            <capability name="extra"                value="${extra}" />
            <capability name="isColor"              value="true" />
            <capability name="letters"              value="${letters}" />
            <capability name="majorversion"         value="${major}" />
            <capability name="minorversion"         value="${minor}" />
            <capability name="screenBitDepth"       value="8" />
            <capability name="type"                 value="IE${major}" />
            <capability name="version"              value="${version}" />
        </capabilities>
    </browser>

    <browser id="IE5to9" parentID="IE">
        <identification>
            <capability name="majorversion" match="^[5-9]" />
        </identification>

        <capture>
        </capture>

        <capabilities>
            <capability name="activexcontrols"     value="true" />
            <capability name="backgroundsounds"    value="true" />
            <capability name="cookies"             value="true" />
            <capability name="css1"                value="true" />
            <capability name="css2"                value="true" />
            <capability name="ecmascriptversion"   value="1.2" />
            <capability name="frames"              value="true" />
            <capability name="javaapplets"         value="true" />
            <capability name="javascript"          value="true" />
            <capability name="jscriptversion"      value="5.0" />
            <capability name="msdomversion"        value="${majorversion}${minorversion}" />
            <capability name="supportsCallback"    value="true" />
            <capability name="supportsFileUpload"  value="true" />
            <capability name="supportsMultilineTextBoxDisplay" value="true" />
            <capability name="supportsMaintainScrollPositionOnPostback" value="true" />
            <capability name="supportsVCard"       value="true" />
            <capability name="supportsXmlHttp"     value="true" />
            <capability name="tables"              value="true" />
            <capability name="tagwriter"           value="System.Web.UI.HtmlTextWriter" />
            <capability name="vbscript"            value="true" />
            <capability name="w3cdomversion"       value="1.0" />
            <capability name="xml"                 value="true" />
        </capabilities>
    </browser>

    <browser id="IE6to9" parentID="IE5to9">
        <identification>
            <capability name="majorversion" match="[6-9]" />
        </identification>

        <capture>
        </capture>

        <capabilities>
            <capability name="jscriptversion"          value="5.6" />
            <capability name="ExchangeOmaSupported"    value="true" />
        </capabilities>
    </browser>

    <!-- sample UA "Mozilla/4.0 (compatible; MSIE 6.0; Windows 95; PalmSource; Blazer 3.0) 16;160x160" -->
    <browser id="Treo600" parentID="IE6to9">
        <identification>
            <userAgent match="PalmSource; Blazer" />
        </identification>

        <capture>
            <userAgent match="PalmSource; Blazer 3\.0\)\s\d+;(?'screenPixelsHeight'\d+)x(?'screenPixelsWidth'\d+)$" />
        </capture>

        <capabilities>
            <capability name="browser"                                  value="Blazer 3.0" />
            <capability name="cachesAllResponsesWithExpires"            value="false" />
            <capability name="canInitiateVoiceCall"                     value="true" />
            <capability name="canRenderEmptySelects"                    value="true" />
            <capability name="canSendMail"                              value="true" />
            <capability name="cookies"                                  value="true" />
            <capability name="ecmascriptversion"                        value="1.1" />
            <capability name="hidesRightAlignedMultiselectScrollbars"   value="false" />
            <capability name="inputType"                                value="keyboard" />
            <capability name="isColor"                                  value="true" />
            <capability name="javascript"                               value="true" />
            <capability name="jscriptversion"                           value="0.0" />
            <capability name="maximumHrefLength"                        value="10000" />
            <capability name="maximumRenderedPageSize"                  value="300000" />
            <capability name="mobileDeviceManufacturer"                 value="" />
            <capability name="mobileDeviceModel"                        value="" />
            <capability name="preferredImageMime"                       value="image/jpeg" />
            <capability name="preferredRenderingMime"                   value="text/html" />
            <capability name="preferredRenderingType"                   value="html32" />
            <capability name="preferredRequestEncoding"                 value="utf-8" />
            <capability name="preferredResponseEncoding"                value="utf-8" />
            <capability name="rendersBreaksAfterHtmlLists"              value="true" />
            <capability name="requiredMetaTagNameValue"                 value="PalmComputingPlatform" />
            <capability name="requiresAttributeColonSubstitution"       value="false" />
            <capability name="requiresContentTypeMetaTag"               value="false" />
            <capability name="requiresControlStateInSession"            value="false" />
            <capability name="requiresDBCSCharacter"                    value="false" />
            <capability name="requiresFullyQualifiedRedirectUrl"        value="false" />
            <capability name="requiresHtmlAdaptiveErrorReporting"       value="false" />
            <capability name="requiresLeadingPageBreak"                 value="false" />
            <capability name="requiresNoBreakInFormatting"              value="false" />
            <capability name="requiresOutputOptimization"               value="false" />
            <capability name="requiresPostRedirectionHandling"          value="false" />
            <capability name="requiresPragmaNoCacheHeader"              value="true" />
            <capability name="requiresUniqueFilePathSuffix"             value="true" />
            <capability name="requiresUniqueHtmlCheckboxNames"          value="false" />
            <capability name="screenBitDepth"                           value="24" />
            <capability name="screenCharactersHeight"                   value="13" />
            <capability name="screenCharactersWidth"                    value="32" />
            <capability name="screenPixelsHeight"                       value="${screenPixelsHeight}" />
            <capability name="screenPixelsWidth"                        value="${screenPixelsWidth}" />
            <capability name="supportsAccessKeyAttribute"               value="true" />
            <capability name="supportsBodyColor"                        value="true" />
            <capability name="supportsBold"                             value="true" />
            <capability name="supportsCharacterEntityEncoding"          value="true" />
            <capability name="supportsCss"                              value="false" />
            <capability name="supportsDivAlign"                         value="true" />
            <capability name="supportsDivNoWrap"                        value="false" />
            <capability name="supportsEmptyStringInCookieValue"         value="true" />
            <capability name="supportsFileUpload"                       value="false" />
            <capability name="supportsFontColor"                        value="true" />
            <capability name="supportsFontName"                         value="false" />
            <capability name="supportsFontSize"                         value="true" />
            <capability name="supportsImageSubmit"                      value="true" />
            <capability name="supportsIModeSymbols"                     value="false" />
            <capability name="supportsInputIStyle"                      value="false" />
            <capability name="supportsInputMode"                        value="false" />
            <capability name="supportsItalic"                           value="true" />
            <capability name="supportsJPhoneMultiMediaAttributes"       value="false" />
            <capability name="supportsJPhoneSymbols"                    value="false" />
            <capability name="supportsMultilineTextBoxDisplay"          value="true" />
            <capability name="supportsQueryStringInFormAction"          value="true" />
            <capability name="supportsRedirectWithCookie"               value="true" />
            <capability name="supportsSelectMultiple"                   value="true" />
            <capability name="supportsUncheck"                          value="true" />
            <capability name="tables"                                   value="true" />
            <capability name="type"                                     value="Handspring Treo 600" />
        </capabilities>

        <controlAdapters />
    </browser>

    <browser id="IE5" parentID="IE5to9">
        <identification>
            <capability name="majorversion" match="^5$" />
        </identification>
    </browser>

    <browser id="IE50" parentID="IE5">
        <identification>
            <capability name="minorversion" match="^\.0"/>
        </identification>
        <controlAdapters>
            <adapter controlType="System.Web.UI.WebControls.Menu"
                     adapterType="System.Web.UI.WebControls.Adapters.MenuAdapter" />
        </controlAdapters>
    </browser>

    <browser id="IE55" parentID="IE5">
        <identification>
            <capability name="minorversion" match="^\.5"/>
        </identification>
        <capabilities>
            <capability name="jscriptversion"          value="5.5" />
            <capability name="ExchangeOmaSupported"    value="true" />
        </capabilities>
    </browser>

    <browser id="IE5to9Mac" parentID="IE5to9">
        <identification>
            <capability name="platform" match="(MacPPC|Mac68K)" />
        </identification>
    
        <controlAdapters>
            <adapter controlType="System.Web.UI.WebControls.Menu"
                     adapterType="System.Web.UI.WebControls.Adapters.MenuAdapter" />
        </controlAdapters>
    </browser>

    <browser id="IE4" parentID="IE">
        <identification>
            <userAgent match="MSIE 4" />
        </identification>

        <capture>
        </capture>

        <capabilities>
            <capability name="activexcontrols"     value="true" />
            <capability name="backgroundsounds"    value="true" />
            <capability name="cdf"                 value="true" />
            <capability name="cookies"             value="true" />
            <capability name="css1"                value="true" />
            <capability name="ecmascriptversion"   value="1.2" />
            <capability name="frames"              value="true" />
            <capability name="javaapplets"         value="true" />
            <capability name="javascript"          value="true" />
            <capability name="jscriptversion"      value="3.0" />
            <capability name="msdomversion"        value="4.0" />
            <capability name="supportsFileUpload"  value="true" />
            <capability name="supportsMultilineTextBoxDisplay" value="false" />
            <capability name="supportsMaintainScrollPositionOnPostback" value="true" />
            <capability name="tables"              value="true" />
            <capability name="tagwriter"           value="System.Web.UI.HtmlTextWriter" />
            <capability name="vbscript"            value="true" />
        </capabilities>
    
        <controlAdapters>
            <adapter controlType="System.Web.UI.WebControls.Menu"
                     adapterType="System.Web.UI.WebControls.Adapters.MenuAdapter" />
        </controlAdapters>
    </browser>

    <browser id="IE3" parentID="IE">
        <identification>
            <capability name="majorversion" match="^3" />
        </identification>

        <capture>
        </capture>

        <capabilities>
            <capability name="activexcontrols"     value="true" />
            <capability name="backgroundsounds"    value="true" />
            <capability name="cookies"             value="true" />
            <capability name="css1"                value="true" />
            <capability name="ecmascriptversion"   value="1.0" />
            <capability name="frames"              value="true" />
            <capability name="javaapplets"         value="true" />
            <capability name="javascript"          value="true" />
            <capability name="jscriptversion"      value="1.0" />
            <capability name="supportsMultilineTextBoxDisplay" value="false" />
            <capability name="tables"              value="true" />
            <capability name="vbscript"            value="true" />
        </capabilities>

        <controlAdapters>
            <adapter controlType="System.Web.UI.WebControls.Menu"
                     adapterType="System.Web.UI.WebControls.Adapters.MenuAdapter" />
        </controlAdapters>
    </browser>

    <browser id="IE3win16" parentID="IE3">
        <identification>
            <userAgent match="16bit|Win(dows 3\.1|16)" />
        </identification>

        <capture>
        </capture>

        <capabilities>
            <capability name="activexcontrols" value="false" />
            <capability name="javaapplets"     value="false" />
        </capabilities>
    </browser>

    <browser id="IE3win16a" parentID="IE3win16">
        <identification>
        <capability name="extra" match="^a" />
        </identification>

        <capture>
        </capture>

        <capabilities>
            <capability name="beta"        value="true" />
            <capability name="javascript"  value="false" />
            <capability name="vbscript"    value="false" />
        </capabilities>
    </browser>

    <browser id="IE3Mac" parentID="IE3">
        <identification>
            <userAgent match="PPC Mac|Macintosh.*(68K|PPC)|Mac_(PowerPC|PPC|68(K|000))" />
        </identification>

        <capture>
        </capture>

        <capabilities>
            <capability name="activexcontrols" value="false" />
            <capability name="vbscript"        value="false" />
        </capabilities>
    </browser>

    <gateway id="IE3AK" parentID="IE3">
        <identification>
            <capability name="extra" match="; AK;" />
        </identification>

        <capture>
        </capture>

        <capabilities>
            <capability name="ak"  value="true" />
        </capabilities>
    </gateway>

    <gateway id="IE3SK" parentID="IE3">
        <identification>
            <capability name="extra" match="; SK;" />
        </identification>

        <capture>
        </capture>

        <capabilities>
            <capability name="sk"  value="true" />
        </capabilities>
    </gateway>

    <browser id="IE2" parentID="IE">
        <identification>
            <capability name="majorversion" match="^2" />
        </identification>

        <capture>
        </capture>

        <capabilities>
            <capability name="backgroundsounds"    value="true" />
            <capability name="cookies"             value="true" />
            <capability name="tables"              value="true" />
        </capabilities>
    
        <controlAdapters>
            <adapter controlType="System.Web.UI.WebControls.Menu"
                     adapterType="System.Web.UI.WebControls.Adapters.MenuAdapter" />
        </controlAdapters>
    </browser>

    <browser id="IE1minor5" parentID="IE">
        <identification>
            <capability name="version" match="^1\.5" />
        </identification>

        <capture>
        </capture>

        <capabilities>
            <capability name="cookies" value="true" />
            <capability name="tables"  value="true" />
        </capabilities>
    
        <controlAdapters>
            <adapter controlType="System.Web.UI.WebControls.Menu"
                     adapterType="System.Web.UI.WebControls.Adapters.MenuAdapter" />
        </controlAdapters>
    </browser>

    <gateway id="IEAOL" parentID="IE">
        <identification>
            <capability name="extra" match="; AOL" />
        </identification>

        <capture>
        </capture>

        <capabilities>
            <capability name="aol"    value="true" />
            <capability name="frames" value="true" />
        </capabilities>
    </gateway>

    <gateway id="IEbeta" parentID="IE">
        <identification>
            <capability name="letters" match="^([bB]|ab)" />
        </identification>

        <capture>
        </capture>

        <capabilities>
            <capability name="beta"    value="true" />
        </capabilities>
    </gateway>

    <gateway id="IEupdate" parentID="IE">
        <identification>
            <capability name="extra" match="; Update a;" />
        </identification>

        <capture>
        </capture>

        <capabilities>
            <capability name="authenticodeupdate"  value="true" />
        </capabilities>
    </gateway>
</browsers>
