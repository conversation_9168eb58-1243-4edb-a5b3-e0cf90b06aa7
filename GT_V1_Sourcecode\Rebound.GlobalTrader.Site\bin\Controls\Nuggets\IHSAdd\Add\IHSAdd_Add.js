Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.IHSAdd_Add=function(n){Rebound.GlobalTrader.Site.Controls.Forms.IHSAdd_Add.initializeBase(this,[n]);this._intNewID=0};Rebound.GlobalTrader.Site.Controls.Forms.IHSAdd_Add.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.IHSAdd_Add.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown))},dispose:function(){this.isDisposed||Rebound.GlobalTrader.Site.Controls.Forms.IHSAdd_Add.callBaseMethod(this,"dispose")},cancelClicked:function(){$R_FN.navigateBack()},formShown:function(){this._blnFirstTimeShown&&(this.addCancel(Function.createDelegate(this,this.cancelClicked)),this.addSave(Function.createDelegate(this,this.saveClicked)));this.getFieldDropDownData("ctlPartStatus");this.getFieldDropDownData("ctlManufacturer");this.getFieldDropDownData("ctlMsl");this.getFieldDropDownData("ctlCountryOfManufacture");this.getFieldDropDownData("ctlPackage");this.getFieldDropDownData("ctlECCNCode");this.setFieldValue("ctlDateRequired",$R_FN.shortDate())},saveClicked:function(){if(this.validateForm()){this.showSaving(!0);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/IHSAdd");n.set_DataObject("IHSAdd");n.set_DataAction("AddNew");n.addParameter("Part",this.getFieldValue("ctlPartNo"));n.addParameter("PartStatus",this.getFieldValue("ctlPartStatus"));n.addParameter("Manufacturer",this.getFieldValue("ctlManufacturer"));n.addParameter("Package",this.getFieldValue("ctlPackage"));n.addParameter("PackageCode",this.getFieldValue("ctlPackageCode"));n.addParameter("HTSCode",this.getFieldValue("ctlHTSCode"));n.addParameter("Date",this.getFieldValue("ctlDateRequired"));n.addParameter("Description",this.getFieldValue("ctlDescription"));n.addParameter("ECCNCode",this.getFieldValue("ctlECCNCode"));n.addParameter("CountryOfManufacture",this.getFieldValue("ctlCountryOfManufacture"));n.addParameter("MSLLevel",this.getFieldDropDownText("ctlMsl"));n.addDataOK(Function.createDelegate(this,this.saveEditComplete));n.addError(Function.createDelegate(this,this.saveEditError));n.addTimeout(Function.createDelegate(this,this.saveEditError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null}},saveEditError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},saveEditComplete:function(n){n._result.NewID>0?(this._intNewID=n._result.NewID,this.showSavedOK(!0),this.onSaveComplete()):(this._strErrorMessage=n._errorMessage,this.onSaveError())},validateForm:function(){this.onValidate();var n=!0;return this.checkFieldEntered("ctlPartNo")||(n=!1),this.checkFieldEntered("ctlPartStatus")||(n=!1),this.checkFieldEntered("ctlManufacturer")||(n=!1),this.checkFieldEntered("ctlPackage")||(n=!1),this.checkFieldEntered("ctlDateRequired")||(n=!1),n||this.showError(!0),n}};Rebound.GlobalTrader.Site.Controls.Forms.IHSAdd_Add.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.IHSAdd_Add",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);