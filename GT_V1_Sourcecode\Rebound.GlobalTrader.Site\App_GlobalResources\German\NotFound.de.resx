<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="AllPurchaseOrdersDueIn" xml:space="preserve">
    <value>Es gibt keine Kaufaufträge, die innen passend sind</value>
  </data>
  <data name="ApprovedCustomersOnStop" xml:space="preserve">
    <value>Es gibt z.Z. keine anerkannten Kunden auf Anschlag</value>
  </data>
  <data name="Company" xml:space="preserve">
    <value>Traurig, kann diese Firma nicht gefunden werden, oder Sie haben nicht die Erlaubnis, sie zu sehen.</value>
  </data>
  <data name="Contact" xml:space="preserve">
    <value>Traurig, kann dieser Kontakt nicht gefunden werden, oder Sie haben nicht die Erlaubnis, ihn zu sehen.</value>
  </data>
  <data name="CreditNote" xml:space="preserve">
    <value>Traurig, kann diese Kreditnote nicht gefunden werden, oder Sie haben nicht die Erlaubnis, sie zu sehen.</value>
  </data>
  <data name="CRMANotAvailable" xml:space="preserve">
    <value>Keine Rechnungslinien sind für das Hinzufügen diesem Kunden RMA vorhanden. Benutzen Sie das " Edit" Anlage, zum irgendeiner Linie Informationen zu ändern.</value>
  </data>
  <data name="CRMAReceivingLines" xml:space="preserve">
    <value>Dieses CRMA hat z.Z. keine Linien, den Empfang zu erwarten</value>
  </data>
  <data name="CusReqSourcingResults" xml:space="preserve">
    <value>Diese Kunden-Anforderung hat keine Auftreten-Resultate</value>
  </data>
  <data name="CustomerRequirement" xml:space="preserve">
    <value>Traurig, kann diese Kunden-Anforderung nicht gefunden werden</value>
  </data>
  <data name="CustomerRMA" xml:space="preserve">
    <value>Traurig, kann diese Kunden-Anforderung nicht gefunden werden</value>
  </data>
  <data name="DebitNote" xml:space="preserve">
    <value>Traurig, kann diese Belastungsanzeige nicht gefunden werden, oder Sie haben nicht die Erlaubnis, sie zu sehen.</value>
  </data>
  <data name="DivisionDocHeaderImage" xml:space="preserve">
    <value>Diese Abteilung hat kein Dokumentenüberschriftbild</value>
  </data>
  <data name="DivisionMembers" xml:space="preserve">
    <value>Diese Abteilung hat keine Mitglieder</value>
  </data>
  <data name="DocHeaderImage" xml:space="preserve">
    <value>Es gibt z.Z. kein Dokumenten-Überschrift-Bild</value>
  </data>
  <data name="Document" xml:space="preserve">
    <value>Traurig, kann dieses Dokument nicht gefunden werden, oder Sie haben nicht die Erlaubnis, es zu sehen.</value>
  </data>
  <data name="EnterCompanyName" xml:space="preserve">
    <value>Tragen Sie bitte den Firmennamen ein</value>
  </data>
  <data name="FormProblems" xml:space="preserve">
    <value>Es gab einige Probleme mit Ihrer Form&lt;br /&gt;Überprüfen Sie bitte die Form und versuchen Sie noch einmal.</value>
  </data>
  <data name="Generic" xml:space="preserve">
    <value>Traurig, wurden keine Daten gefunden</value>
  </data>
  <data name="GoodsIn" xml:space="preserve">
    <value>Traurig, dieses können Waren in der Anmerkung nicht gefunden werden, oder Sie haben nicht die Erlaubnis, sie zu sehen.</value>
  </data>
  <data name="Invoice" xml:space="preserve">
    <value>Traurig, kann diese Rechnung nicht gefunden werden, oder Sie haben nicht die Erlaubnis, sie zu sehen.</value>
  </data>
  <data name="InvoiceNotAvailable" xml:space="preserve">
    <value>Keine Verkaufsauftrags-Hausanschlussleitungen sind für das Hinzufügen dieser Rechnung vorhanden.</value>
  </data>
  <data name="Lot" xml:space="preserve">
    <value>Traurig, kann dieses Los nicht gefunden werden, oder Sie haben nicht die Erlaubnis, es zu sehen.</value>
  </data>
  <data name="MailMessage" xml:space="preserve">
    <value>Traurig, existiert diese Mitteilung nicht, oder Sie haben nicht Erlaubnis, sie zu sehen</value>
  </data>
  <data name="MailMessageGroupMembers" xml:space="preserve">
    <value>Die Post-Gruppe hat keine Mitglieder</value>
  </data>
  <data name="Manufacturer" xml:space="preserve">
    <value>Traurig, kann dieser Hersteller nicht gefunden werden, oder Sie haben nicht die Erlaubnis, ihn zu sehen.</value>
  </data>
  <data name="MyApprovedCustomersOnStop" xml:space="preserve">
    <value>Ich habe z.Z. keine anerkannten Kunden auf Anschlag</value>
  </data>
  <data name="MyApprovedCustomersOnStop_ForUser" xml:space="preserve">
    <value>{0} hat z.Z. keine anerkannten Kunden auf Anschlag</value>
  </data>
  <data name="MyMessages" xml:space="preserve">
    <value>Ich habe z.Z. keine Mitteilungen</value>
  </data>
  <data name="MyOpenPurchaseOrders" xml:space="preserve">
    <value>Ich habe z.Z. keine geöffneten Kaufaufträge</value>
  </data>
  <data name="MyOpenPurchaseOrders_ForUser" xml:space="preserve">
    <value>{0} currently has no open Purchase Orders</value>
  </data>
  <data name="MyOpenQuotes" xml:space="preserve">
    <value>Ich habe z.Z. keine geöffneten Anführungsstriche</value>
  </data>
  <data name="MyOpenQuotes_ForUser" xml:space="preserve">
    <value>{0} hat z.Z. keine geöffneten Anführungsstriche</value>
  </data>
  <data name="MyOpenRequirements" xml:space="preserve">
    <value>Ich habe z.Z. keine geöffneten Anforderungen</value>
  </data>
  <data name="MyOpenRequirements_ForUser" xml:space="preserve">
    <value>{0} hat z.Z. keine geöffneten Anforderungen</value>
  </data>
  <data name="MyOpenSalesOrders" xml:space="preserve">
    <value>Ich habe z.Z. keine geöffneten Verkaufs-Aufträge</value>
  </data>
  <data name="MyOpenSalesOrders_ForUser" xml:space="preserve">
    <value>{0} hat z.Z. keine geöffneten Verkaufs-Aufträge</value>
  </data>
  <data name="MyRecentActivity" xml:space="preserve">
    <value>Ich habe keine neue Tätigkeit</value>
  </data>
  <data name="MyRecentActivity_ForUser" xml:space="preserve">
    <value>{0} hat keine neue Tätigkeit</value>
  </data>
  <data name="MyRecentlyShippedOrders" xml:space="preserve">
    <value>Ich habe z.Z. keine vor kurzem versendeten Aufträge</value>
  </data>
  <data name="MyRecentlyShippedOrders_ForUser" xml:space="preserve">
    <value>{0} hat z.Z. keine vor kurzem versendeten Aufträge</value>
  </data>
  <data name="MyScheduledTasks" xml:space="preserve">
    <value>Ich habe z.Z. keine zeitlich geplanten Aufgaben</value>
  </data>
  <data name="MyScheduledTasks_ForUser" xml:space="preserve">
    <value>{0} hat z.Z. keine zeitlich geplanten Aufgaben</value>
  </data>
  <data name="MyToDoList" xml:space="preserve">
    <value>Ich habe z.Z. keine Einzelteile auf meinem, zum der Liste zu tun</value>
  </data>
  <data name="Page" xml:space="preserve">
    <value>Traurig, kann diese Seite nicht gefunden werden, oder Sie haben nicht die Erlaubnis, sie zu sehen.</value>
  </data>
  <data name="PartsBeingOrderedToday" xml:space="preserve">
    <value>Es hat keine Teile gegeben, die heute bestellt werden</value>
  </data>
  <data name="PartSearch" xml:space="preserve">
    <value>Traurig, konnten keine Teile gefunden werden, um Ihre Suche zusammenzubringen</value>
  </data>
  <data name="POReceivingLines" xml:space="preserve">
    <value>Dieser Kaufauftrag hat keine Linien, den Empfang zu erwarten</value>
  </data>
  <data name="PurchaseOrder" xml:space="preserve">
    <value>Traurig, kann dieser Kaufauftrag nicht gefunden werden, oder Sie haben nicht die Erlaubnis, ihn zu sehen.</value>
  </data>
  <data name="Quote" xml:space="preserve">
    <value>Traurig, kann dieser Anführungsstrich nicht gefunden werden, oder Sie haben nicht die Erlaubnis, ihn zu sehen.</value>
  </data>
  <data name="ReceivedOrders" xml:space="preserve">
    <value>Es gibt z.Z. keine empfangenen Aufträge</value>
  </data>
  <data name="Report" xml:space="preserve">
    <value>Traurig, kann dieser Report nicht gefunden werden, oder Sie haben nicht die Erlaubnis, ihn zu sehen.</value>
  </data>
  <data name="ReportData" xml:space="preserve">
    <value>Dieser Report hat keine Daten für die spezifizierten Parameter</value>
  </data>
  <data name="ReportParameters" xml:space="preserve">
    <value>Dieser Report hat keine Parameter</value>
  </data>
  <data name="Reports" xml:space="preserve">
    <value>Traurig, gibt es z.Z. keine angebrachten Reports</value>
  </data>
  <data name="SalesOrder" xml:space="preserve">
    <value>Traurig, kann dieser Verkaufs-Auftrag nicht gefunden werden, oder Sie haben nicht die Erlaubnis, ihn zu sehen.</value>
  </data>
  <data name="SalesOrdersReadyToShip" xml:space="preserve">
    <value>Es gibt keine Verkaufs-Aufträge, die bereit sind zu versenden</value>
  </data>
  <data name="SecurityGroupMembers" xml:space="preserve">
    <value>Diese Sicherheits-Gruppe hat keine Mitglieder</value>
  </data>
  <data name="SecurityGroupPermissionsGeneral" xml:space="preserve">
    <value>Es gibt z.Z. keine Erlaubnis, die eingestellt werden kann</value>
  </data>
  <data name="SecurityGroupPermissionsReports" xml:space="preserve">
    <value>Es gibt z.Z. keine Reports, die Erlaubnis haben, die eingestellt werden kann</value>
  </data>
  <data name="SecurityUserGroups" xml:space="preserve">
    <value>Dieser Benutzer ist nicht ein Mitglied irgendwelcher Sicherheits-Gruppen - dieser gibt ihnen vollen Zugriff zum System</value>
  </data>
  <data name="Service" xml:space="preserve">
    <value>Traurig, kann dieser Service nicht gefunden werden, oder Sie haben nicht die Erlaubnis, ihn zu sehen.</value>
  </data>
  <data name="ShipSOMainInfo_ShippedLines" xml:space="preserve">
    <value>Dieser Verkaufs-Auftrag hat keine versendeten Linien</value>
  </data>
  <data name="SOLineAllocations" xml:space="preserve">
    <value>Diese Verkaufs-Auftrags-Linie hat keine Verteilungen</value>
  </data>
  <data name="SOLinesShipped" xml:space="preserve">
    <value>Diese Verkaufs-Auftrags-Linie hat keine versendeten Linien</value>
  </data>
  <data name="SOShippingLines" xml:space="preserve">
    <value>Dieser Verkaufs-Auftrag hat keine Linien, Versand zu erwarten</value>
  </data>
  <data name="SourcingLinks" xml:space="preserve">
    <value>Traurig, gibt es z.Z. keine Auftreten-Verbindungen</value>
  </data>
  <data name="SRMANotAvailable" xml:space="preserve">
    <value>Keine Kaufauftraglinien sind für das Hinzufügen diesem Lieferanten RMA vorhanden. Benutzen Sie das " Edit" Anlage, zum irgendeiner Linie Informationen zu ändern.</value>
  </data>
  <data name="SRMAShippingLines" xml:space="preserve">
    <value>Dieser Lieferant RMA hat z.Z. keine Linien, Versand zu erwarten</value>
  </data>
  <data name="Stock" xml:space="preserve">
    <value>Traurig, kann dieses auf lagereinzelteil nicht gefunden werden, oder Sie haben nicht die Erlaubnis, es zu sehen.</value>
  </data>
  <data name="SupplierRMA" xml:space="preserve">
    <value>Traurig, kann dieser Lieferant RMA nicht gefunden werden, oder Sie haben nicht die Erlaubnis, es zu sehen.</value>
  </data>
  <data name="TeamMembers" xml:space="preserve">
    <value>Diese Mannschaft hat keine Mitglieder</value>
  </data>
  <data name="TodaysShippedOrders" xml:space="preserve">
    <value>Es gibt keine versendeten Aufträge heute</value>
  </data>
  <data name="TopSalespersons" xml:space="preserve">
    <value>Die Spitzenverkäufer können nicht hergestellt werden</value>
  </data>
</root>