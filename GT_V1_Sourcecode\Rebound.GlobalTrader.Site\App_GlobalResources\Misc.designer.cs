//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option or rebuild the Visual Studio project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.VisualStudio.Web.Application.StronglyTypedResourceProxyBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class Misc {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Misc() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Resources.Misc", global::System.Reflection.Assembly.Load("App_GlobalResources"));
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Active.
        /// </summary>
        internal static string Active {
            get {
                return ResourceManager.GetString("Active", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Active-Unconfirmed.
        /// </summary>
        internal static string ActiveUnconfirmed {
            get {
                return ResourceManager.GetString("ActiveUnconfirmed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Actual Landed Cost.
        /// </summary>
        internal static string ActualLandedCost {
            get {
                return ResourceManager.GetString("ActualLandedCost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add to Existing IPO/PO.
        /// </summary>
        internal static string AddExiIPO {
            get {
                return ResourceManager.GetString("AddExiIPO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New.
        /// </summary>
        internal static string AddNew {
            get {
                return ResourceManager.GetString("AddNew", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create New IPO/PO.
        /// </summary>
        internal static string AddNewIPO {
            get {
                return ResourceManager.GetString("AddNewIPO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Supplier Invoice.
        /// </summary>
        internal static string AddSupplierInvoice {
            get {
                return ResourceManager.GetString("AddSupplierInvoice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to All.
        /// </summary>
        internal static string All {
            get {
                return ResourceManager.GetString("All", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allocated.
        /// </summary>
        internal static string Allocated {
            get {
                return ResourceManager.GetString("Allocated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allocations.
        /// </summary>
        internal static string Allocations {
            get {
                return ResourceManager.GetString("Allocations", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Alternate.
        /// </summary>
        internal static string Alternate {
            get {
                return ResourceManager.GetString("Alternate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Alternate.
        /// </summary>
        internal static string Alternative {
            get {
                return ResourceManager.GetString("Alternative", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Amended.
        /// </summary>
        internal static string Amended {
            get {
                return ResourceManager.GetString("Amended", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Approve.
        /// </summary>
        internal static string Approve {
            get {
                return ResourceManager.GetString("Approve", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Approved and Un Exported.
        /// </summary>
        internal static string ApproveAndUnExported {
            get {
                return ResourceManager.GetString("ApproveAndUnExported", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Approved.
        /// </summary>
        internal static string Approved {
            get {
                return ResourceManager.GetString("Approved", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to [By: {0}  ({1})].
        /// </summary>
        internal static string ApprovedByAndDate {
            get {
                return ResourceManager.GetString("ApprovedByAndDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rebound Global:Trader.
        /// </summary>
        internal static string AppTitle {
            get {
                return ResourceManager.GetString("AppTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AS9120 Required.
        /// </summary>
        internal static string AS9120 {
            get {
                return ResourceManager.GetString("AS9120", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Authorised.
        /// </summary>
        internal static string Authorised {
            get {
                return ResourceManager.GetString("Authorised", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Authorised Part Allocated.
        /// </summary>
        internal static string AuthorisedPartAllocated {
            get {
                return ResourceManager.GetString("AuthorisedPartAllocated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Find a part number.
        /// </summary>
        internal static string AutoSearchPart {
            get {
                return ResourceManager.GetString("AutoSearchPart", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Awaits Inspection.
        /// </summary>
        internal static string AwaitsInspection {
            get {
                return ResourceManager.GetString("AwaitsInspection", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to beginning with.
        /// </summary>
        internal static string BeginsWith {
            get {
                return ResourceManager.GetString("BeginsWith", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HUBRFQ.
        /// </summary>
        internal static string BillofMaterial {
            get {
                return ResourceManager.GetString("BillofMaterial", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HUBRFQ.
        /// </summary>
        internal static string BOM {
            get {
                return ResourceManager.GetString("BOM", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BOM_{0}_{1}.
        /// </summary>
        internal static string BOMDoc {
            get {
                return ResourceManager.GetString("BOMDoc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BOM Import.
        /// </summary>
        internal static string BOMImport {
            get {
                return ResourceManager.GetString("BOMImport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Both options.
        /// </summary>
        internal static string Bothoptions {
            get {
                return ResourceManager.GetString("Bothoptions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Browse.
        /// </summary>
        internal static string Browse {
            get {
                return ResourceManager.GetString("Browse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Browser Error.
        /// </summary>
        internal static string BrowserError {
            get {
                return ResourceManager.GetString("BrowserError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Browser Warning.
        /// </summary>
        internal static string BrowserWarning {
            get {
                return ResourceManager.GetString("BrowserWarning", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Built for.
        /// </summary>
        internal static string BuiltFor {
            get {
                return ResourceManager.GetString("BuiltFor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bulk Print.
        /// </summary>
        internal static string BulkPrint {
            get {
                return ResourceManager.GetString("BulkPrint", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Certificate Of Conformity.
        /// </summary>
        internal static string CertificateOfConformance {
            get {
                return ResourceManager.GetString("CertificateOfConformance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Changed {0}.
        /// </summary>
        internal static string ChangedField {
            get {
                return ResourceManager.GetString("ChangedField", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0}.
        /// </summary>
        internal static string ChangedFieldLog {
            get {
                return ResourceManager.GetString("ChangedFieldLog", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Changed {0} to {1}.
        /// </summary>
        internal static string ChangedFieldToValue {
            get {
                return ResourceManager.GetString("ChangedFieldToValue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} : {1}.
        /// </summary>
        internal static string ChangedFieldToValueLog {
            get {
                return ResourceManager.GetString("ChangedFieldToValueLog", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Change Password.
        /// </summary>
        internal static string ChangePassword {
            get {
                return ResourceManager.GetString("ChangePassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Checked.
        /// </summary>
        internal static string Checked {
            get {
                return ResourceManager.GetString("Checked", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to View Reports for Purchase Hub.
        /// </summary>
        internal static string ChkInvMsg {
            get {
                return ResourceManager.GetString("ChkInvMsg", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Clear.
        /// </summary>
        internal static string Clear {
            get {
                return ResourceManager.GetString("Clear", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Clear All.
        /// </summary>
        internal static string ClearAll {
            get {
                return ResourceManager.GetString("ClearAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Click to be reminded again in.
        /// </summary>
        internal static string ClickToSnooze {
            get {
                return ResourceManager.GetString("ClickToSnooze", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BOM.
        /// </summary>
        internal static string ClientBOM {
            get {
                return ResourceManager.GetString("ClientBOM", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Client Invoice.
        /// </summary>
        internal static string ClientInvoice {
            get {
                return ResourceManager.GetString("ClientInvoice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Clone &amp; Add HUBRFQ.
        /// </summary>
        internal static string CloneAndAddHUBRFQ {
            get {
                return ResourceManager.GetString("CloneAndAddHUBRFQ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Clone &amp; Send HUB     .
        /// </summary>
        internal static string CloneAndSendHUB {
            get {
                return ResourceManager.GetString("CloneAndSendHUB", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Closed.
        /// </summary>
        internal static string Closed {
            get {
                return ResourceManager.GetString("Closed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Company_{0}_{1}.
        /// </summary>
        internal static string CMPDocuments {
            get {
                return ResourceManager.GetString("CMPDocuments", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Collapse All.
        /// </summary>
        internal static string CollapseAll {
            get {
                return ResourceManager.GetString("CollapseAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Commercial Invoice.
        /// </summary>
        internal static string CommercialInvoice {
            get {
                return ResourceManager.GetString("CommercialInvoice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Company.
        /// </summary>
        internal static string Company {
            get {
                return ResourceManager.GetString("Company", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ready To Ship.
        /// </summary>
        internal static string CompanyOnStop {
            get {
                return ResourceManager.GetString("CompanyOnStop", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Company.
        /// </summary>
        internal static string CompanyShort {
            get {
                return ResourceManager.GetString("CompanyShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Complete.
        /// </summary>
        internal static string Complete {
            get {
                return ResourceManager.GetString("Complete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Completed.
        /// </summary>
        internal static string Completed {
            get {
                return ResourceManager.GetString("Completed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Confirmed.
        /// </summary>
        internal static string Confirmed {
            get {
                return ResourceManager.GetString("Confirmed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contact Mfr.
        /// </summary>
        internal static string ContactMfr {
            get {
                return ResourceManager.GetString("ContactMfr", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contacts.
        /// </summary>
        internal static string Contacts {
            get {
                return ResourceManager.GetString("Contacts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contacts for {0}.
        /// </summary>
        internal static string ContactsForCompany {
            get {
                return ResourceManager.GetString("ContactsForCompany", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contact.
        /// </summary>
        internal static string ContactShort {
            get {
                return ResourceManager.GetString("ContactShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to containing.
        /// </summary>
        internal static string Contains {
            get {
                return ResourceManager.GetString("Contains", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Country Of Origin.
        /// </summary>
        internal static string CountryOfOrigin {
            get {
                return ResourceManager.GetString("CountryOfOrigin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Credit Note.
        /// </summary>
        internal static string Credit {
            get {
                return ResourceManager.GetString("Credit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Credit No.
        /// </summary>
        internal static string CreditNo {
            get {
                return ResourceManager.GetString("CreditNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Credit Note.
        /// </summary>
        internal static string CreditNote {
            get {
                return ResourceManager.GetString("CreditNote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Credit Notes.
        /// </summary>
        internal static string CreditNotes {
            get {
                return ResourceManager.GetString("CreditNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Credit.
        /// </summary>
        internal static string CreditNoteShort {
            get {
                return ResourceManager.GetString("CreditNoteShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Credits.
        /// </summary>
        internal static string CreditNotesShort {
            get {
                return ResourceManager.GetString("CreditNotesShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Credit Notes.
        /// </summary>
        internal static string Credits {
            get {
                return ResourceManager.GetString("Credits", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer RMA.
        /// </summary>
        internal static string CRMA {
            get {
                return ResourceManager.GetString("CRMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to CRMA_{0}_{1}.
        /// </summary>
        internal static string CRMADocuments {
            get {
                return ResourceManager.GetString("CRMADocuments", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cross Match.
        /// </summary>
        internal static string CrossMatch {
            get {
                return ResourceManager.GetString("CrossMatch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to CRX B2B.
        /// </summary>
        internal static string CRXB2BLabel {
            get {
                return ResourceManager.GetString("CRXB2BLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ImportCSV.
        /// </summary>
        internal static string CSV_Import {
            get {
                return ResourceManager.GetString("CSV_Import", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Current.
        /// </summary>
        internal static string Current {
            get {
                return ResourceManager.GetString("Current", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer Requirement.
        /// </summary>
        internal static string CusReq {
            get {
                return ResourceManager.GetString("CusReq", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Import Tool.
        /// </summary>
        internal static string CustomerBrowseReqImport {
            get {
                return ResourceManager.GetString("CustomerBrowseReqImport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Print Req Enquiry Form.
        /// </summary>
        internal static string CustomerBrowseReqPrint {
            get {
                return ResourceManager.GetString("CustomerBrowseReqPrint", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer Quoted.
        /// </summary>
        internal static string CustomerQuoted {
            get {
                return ResourceManager.GetString("CustomerQuoted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Print Req Enquiry Form for this company.
        /// </summary>
        internal static string CustomerReqPrint {
            get {
                return ResourceManager.GetString("CustomerReqPrint", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Requirement.
        /// </summary>
        internal static string CustomerRequirement {
            get {
                return ResourceManager.GetString("CustomerRequirement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer Requirement No.
        /// </summary>
        internal static string CustomerRequirementNo {
            get {
                return ResourceManager.GetString("CustomerRequirementNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Requirements.
        /// </summary>
        internal static string CustomerRequirements {
            get {
                return ResourceManager.GetString("CustomerRequirements", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Req.
        /// </summary>
        internal static string CustomerRequirementShort {
            get {
                return ResourceManager.GetString("CustomerRequirementShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reqs.
        /// </summary>
        internal static string CustomerRequirementsShort {
            get {
                return ResourceManager.GetString("CustomerRequirementsShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer RMA.
        /// </summary>
        internal static string CustomerRMA {
            get {
                return ResourceManager.GetString("CustomerRMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer RMA No.
        /// </summary>
        internal static string CustomerRMANo {
            get {
                return ResourceManager.GetString("CustomerRMANo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer RMAs.
        /// </summary>
        internal static string CustomerRMAs {
            get {
                return ResourceManager.GetString("CustomerRMAs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to CRMA.
        /// </summary>
        internal static string CustomerRMAShort {
            get {
                return ResourceManager.GetString("CustomerRMAShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to CRMAs.
        /// </summary>
        internal static string CustomerRMAsShort {
            get {
                return ResourceManager.GetString("CustomerRMAsShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer.
        /// </summary>
        internal static string CustomerShort {
            get {
                return ResourceManager.GetString("CustomerShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lock to save your search, unlock to clear it.
        /// </summary>
        internal static string DataListNuggetStateButtonTooltip {
            get {
                return ResourceManager.GetString("DataListNuggetStateButtonTooltip", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Days.
        /// </summary>
        internal static string Days {
            get {
                return ResourceManager.GetString("Days", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 1 day.
        /// </summary>
        internal static string Days_1 {
            get {
                return ResourceManager.GetString("Days_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 2 days.
        /// </summary>
        internal static string Days_2 {
            get {
                return ResourceManager.GetString("Days_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 3 days.
        /// </summary>
        internal static string Days_3 {
            get {
                return ResourceManager.GetString("Days_3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 4 days.
        /// </summary>
        internal static string Days_4 {
            get {
                return ResourceManager.GetString("Days_4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deauthorised.
        /// </summary>
        internal static string Deauthorised {
            get {
                return ResourceManager.GetString("Deauthorised", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Debit Note.
        /// </summary>
        internal static string Debit {
            get {
                return ResourceManager.GetString("Debit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Debit No.
        /// </summary>
        internal static string DebitNo {
            get {
                return ResourceManager.GetString("DebitNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Debit Note.
        /// </summary>
        internal static string DebitNote {
            get {
                return ResourceManager.GetString("DebitNote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Debit Notes.
        /// </summary>
        internal static string DebitNotes {
            get {
                return ResourceManager.GetString("DebitNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Debit.
        /// </summary>
        internal static string DebitNoteShort {
            get {
                return ResourceManager.GetString("DebitNoteShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Debits.
        /// </summary>
        internal static string DebitNotesShort {
            get {
                return ResourceManager.GetString("DebitNotesShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Debit Notes.
        /// </summary>
        internal static string Debits {
            get {
                return ResourceManager.GetString("Debits", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Default.
        /// </summary>
        internal static string Default {
            get {
                return ResourceManager.GetString("Default", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Part Despatched.
        /// </summary>
        internal static string Despatched {
            get {
                return ResourceManager.GetString("Despatched", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Disapproved.
        /// </summary>
        internal static string Disapproved {
            get {
                return ResourceManager.GetString("Disapproved", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Discontinued.
        /// </summary>
        internal static string Discontinued {
            get {
                return ResourceManager.GetString("Discontinued", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Discontinued-Unconfirmed.
        /// </summary>
        internal static string DiscontinuedUnconfirmed {
            get {
                return ResourceManager.GetString("DiscontinuedUnconfirmed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Division.
        /// </summary>
        internal static string Division {
            get {
                return ResourceManager.GetString("Division", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Division Header.
        /// </summary>
        internal static string DivisionHeader {
            get {
                return ResourceManager.GetString("DivisionHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contains.
        /// </summary>
        internal static string DLNFilter_Contains {
            get {
                return ResourceManager.GetString("DLNFilter_Contains", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ends with.
        /// </summary>
        internal static string DLNFilter_EndsWith {
            get {
                return ResourceManager.GetString("DLNFilter_EndsWith", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Starts with.
        /// </summary>
        internal static string DLNFilter_StartsWith {
            get {
                return ResourceManager.GetString("DLNFilter_StartsWith", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Last updated.
        /// </summary>
        internal static string DLUP {
            get {
                return ResourceManager.GetString("DLUP", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0}_{1}_{2}_{3}.doc.
        /// </summary>
        internal static string DocFileName {
            get {
                return ResourceManager.GetString("DocFileName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0}_{1}_{2}_{3}.docx.
        /// </summary>
        internal static string DocxFileName {
            get {
                return ResourceManager.GetString("DocxFileName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PDF Report.
        /// </summary>
        internal static string DownloadGIPDF {
            get {
                return ResourceManager.GetString("DownloadGIPDF", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Word Report.
        /// </summary>
        internal static string DownloadGIWord {
            get {
                return ResourceManager.GetString("DownloadGIWord", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Download PDF.
        /// </summary>
        internal static string DownloadPDF {
            get {
                return ResourceManager.GetString("DownloadPDF", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dual.
        /// </summary>
        internal static string Dual {
            get {
                return ResourceManager.GetString("Dual", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit info - After Release.
        /// </summary>
        internal static string EditinfoAfterRelease {
            get {
                return ResourceManager.GetString("EditinfoAfterRelease", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ....
        /// </summary>
        internal static string Ellipses {
            get {
                return ResourceManager.GetString("Ellipses", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ending with.
        /// </summary>
        internal static string EndsWith {
            get {
                return ResourceManager.GetString("EndsWith", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to EUU_{0}_{1}.
        /// </summary>
        internal static string EndUserUnderTakingPDF {
            get {
                return ResourceManager.GetString("EndUserUnderTakingPDF", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to EOL.
        /// </summary>
        internal static string EOL {
            get {
                return ResourceManager.GetString("EOL", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to =.
        /// </summary>
        internal static string EqualTo {
            get {
                return ResourceManager.GetString("EqualTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error.
        /// </summary>
        internal static string Error {
            get {
                return ResourceManager.GetString("Error", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0}_{1}_{2}_{3}.csv.
        /// </summary>
        internal static string ExcelCsvFileName {
            get {
                return ResourceManager.GetString("ExcelCsvFileName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0}_{1}_{2}_{3}.xlsx.
        /// </summary>
        internal static string ExcelFileName {
            get {
                return ResourceManager.GetString("ExcelFileName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0}_{1}_{2}_{3}.xls.
        /// </summary>
        internal static string ExcelXlsFileName {
            get {
                return ResourceManager.GetString("ExcelXlsFileName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Excess Label.
        /// </summary>
        internal static string Excess {
            get {
                return ResourceManager.GetString("Excess", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Existing Debit Note.
        /// </summary>
        internal static string ExistingDebitNote {
            get {
                return ResourceManager.GetString("ExistingDebitNote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Existing Goods In Note.
        /// </summary>
        internal static string ExistingGI {
            get {
                return ResourceManager.GetString("ExistingGI", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Existing Invoice.
        /// </summary>
        internal static string ExistingInvoice {
            get {
                return ResourceManager.GetString("ExistingInvoice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Expand All.
        /// </summary>
        internal static string ExpandAll {
            get {
                return ResourceManager.GetString("ExpandAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to expires.
        /// </summary>
        internal static string Expires {
            get {
                return ResourceManager.GetString("Expires", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Settings for {0}.
        /// </summary>
        internal static string Explain_Settings_Company {
            get {
                return ResourceManager.GetString("Explain_Settings_Company", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Settings for all companies.
        /// </summary>
        internal static string Explain_Settings_Global {
            get {
                return ResourceManager.GetString("Explain_Settings_Global", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Preferences and settings personal to you.
        /// </summary>
        internal static string Explain_Settings_Personal {
            get {
                return ResourceManager.GetString("Explain_Settings_Personal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Security users, groups and permissions.
        /// </summary>
        internal static string Explain_Settings_Security {
            get {
                return ResourceManager.GetString("Explain_Settings_Security", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Exported.
        /// </summary>
        internal static string Exported {
            get {
                return ResourceManager.GetString("Exported", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Exported Only.
        /// </summary>
        internal static string ExportedOnly {
            get {
                return ResourceManager.GetString("ExportedOnly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Female.
        /// </summary>
        internal static string Female {
            get {
                return ResourceManager.GetString("Female", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Default: {0}.
        /// </summary>
        internal static string FieldDefaultValue {
            get {
                return ResourceManager.GetString("FieldDefaultValue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Filter.
        /// </summary>
        internal static string Filter {
            get {
                return ResourceManager.GetString("Filter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Filter Results.
        /// </summary>
        internal static string FilterResults {
            get {
                return ResourceManager.GetString("FilterResults", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Firm Alternate.
        /// </summary>
        internal static string FirmAlternative {
            get {
                return ResourceManager.GetString("FirmAlternative", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to For.
        /// </summary>
        internal static string For {
            get {
                return ResourceManager.GetString("For", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to From Authorised SO Service Line.
        /// </summary>
        internal static string FromAuthorisedSOServiceLine {
            get {
                return ResourceManager.GetString("FromAuthorisedSOServiceLine", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to From Client Invoice.
        /// </summary>
        internal static string FromClientInvoice {
            get {
                return ResourceManager.GetString("FromClientInvoice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to From Closed SO Line.
        /// </summary>
        internal static string FromClosedSOLine {
            get {
                return ResourceManager.GetString("FromClosedSOLine", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to From Invoice Line.
        /// </summary>
        internal static string FromCreditNote {
            get {
                return ResourceManager.GetString("FromCreditNote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to From Customer RMA.
        /// </summary>
        internal static string FromCustomerRMA {
            get {
                return ResourceManager.GetString("FromCustomerRMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to From Customer RMA Line.
        /// </summary>
        internal static string FromCustomerRMALine {
            get {
                return ResourceManager.GetString("FromCustomerRMALine", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to From Invoice.
        /// </summary>
        internal static string FromInvoice {
            get {
                return ResourceManager.GetString("FromInvoice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to From Invoice Line.
        /// </summary>
        internal static string FromInvoiceLine {
            get {
                return ResourceManager.GetString("FromInvoiceLine", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to From Master Part List.
        /// </summary>
        internal static string FromMasterPartList {
            get {
                return ResourceManager.GetString("FromMasterPartList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to From Purchase Order Line.
        /// </summary>
        internal static string FromPurchaseOrderLine {
            get {
                return ResourceManager.GetString("FromPurchaseOrderLine", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to From Purchase Orders.
        /// </summary>
        internal static string FromPurchaseOrders {
            get {
                return ResourceManager.GetString("FromPurchaseOrders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to From Purchase Requisitions.
        /// </summary>
        internal static string FromPurchaseRequisitions {
            get {
                return ResourceManager.GetString("FromPurchaseRequisitions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to From Quotes.
        /// </summary>
        internal static string FromQuotes {
            get {
                return ResourceManager.GetString("FromQuotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to From Requirements.
        /// </summary>
        internal static string FromRequirements {
            get {
                return ResourceManager.GetString("FromRequirements", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to From Requirement Sourcing Results.
        /// </summary>
        internal static string FromRequirementSourcingResult {
            get {
                return ResourceManager.GetString("FromRequirementSourcingResult", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to From Sales Orders.
        /// </summary>
        internal static string FromSalesOrders {
            get {
                return ResourceManager.GetString("FromSalesOrders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to From Service.
        /// </summary>
        internal static string FromService {
            get {
                return ResourceManager.GetString("FromService", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to From Sourcing Results.
        /// </summary>
        internal static string FromSourcingResult {
            get {
                return ResourceManager.GetString("FromSourcingResult", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to From Stock.
        /// </summary>
        internal static string FromStock {
            get {
                return ResourceManager.GetString("FromStock", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to From Stock Lot.
        /// </summary>
        internal static string FromStockLot {
            get {
                return ResourceManager.GetString("FromStockLot", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to From Supplier RMA Line.
        /// </summary>
        internal static string FromSupplierRMALine {
            get {
                return ResourceManager.GetString("FromSupplierRMALine", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Goods In Note.
        /// </summary>
        internal static string GI {
            get {
                return ResourceManager.GetString("GI", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allocated by {0} SO line(s).
        /// </summary>
        internal static string GIDocketSODatePromisedLines {
            get {
                return ResourceManager.GetString("GIDocketSODatePromisedLines", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GI_{0}_{1}.
        /// </summary>
        internal static string GIDocuments {
            get {
                return ResourceManager.GetString("GIDocuments", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GILine_{0}_{1}.
        /// </summary>
        internal static string GILineDocuments {
            get {
                return ResourceManager.GetString("GILineDocuments", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Print All Line.
        /// </summary>
        internal static string GIPrintAllLine {
            get {
                return ResourceManager.GetString("GIPrintAllLine", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Print Line.
        /// </summary>
        internal static string GIPrintLine {
            get {
                return ResourceManager.GetString("GIPrintLine", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Goods In.
        /// </summary>
        internal static string GoodsIn {
            get {
                return ResourceManager.GetString("GoodsIn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Goods In Label.
        /// </summary>
        internal static string GoodsInLabel {
            get {
                return ResourceManager.GetString("GoodsInLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Goods In No.
        /// </summary>
        internal static string GoodsInNo {
            get {
                return ResourceManager.GetString("GoodsInNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GI.
        /// </summary>
        internal static string GoodsInShort {
            get {
                return ResourceManager.GetString("GoodsInShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GP.
        /// </summary>
        internal static string GP {
            get {
                return ResourceManager.GetString("GP", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &gt;.
        /// </summary>
        internal static string GreaterThan {
            get {
                return ResourceManager.GetString("GreaterThan", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &gt;=.
        /// </summary>
        internal static string GreaterThanOrEqualTo {
            get {
                return ResourceManager.GetString("GreaterThanOrEqualTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GT.
        /// </summary>
        internal static string GT {
            get {
                return ResourceManager.GetString("GT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Historical.
        /// </summary>
        internal static string Historical {
            get {
                return ResourceManager.GetString("Historical", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HK.
        /// </summary>
        internal static string HK {
            get {
                return ResourceManager.GetString("HK", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to More HUBRFQ....
        /// </summary>
        internal static string HomeNuggetBom {
            get {
                return ResourceManager.GetString("HomeNuggetBom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select user.
        /// </summary>
        internal static string HomepageSelectUser {
            get {
                return ResourceManager.GetString("HomepageSelectUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to View for myself?.
        /// </summary>
        internal static string HomepageViewForMyself {
            get {
                return ResourceManager.GetString("HomepageViewForMyself", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hours.
        /// </summary>
        internal static string Hours {
            get {
                return ResourceManager.GetString("Hours", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 1 hour.
        /// </summary>
        internal static string Hours_1 {
            get {
                return ResourceManager.GetString("Hours_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 2 hours.
        /// </summary>
        internal static string Hours_2 {
            get {
                return ResourceManager.GetString("Hours_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 4 hours.
        /// </summary>
        internal static string Hours_4 {
            get {
                return ResourceManager.GetString("Hours_4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 8 hours.
        /// </summary>
        internal static string Hours_8 {
            get {
                return ResourceManager.GetString("Hours_8", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hub Credit Notes.
        /// </summary>
        internal static string HubCredit {
            get {
                return ResourceManager.GetString("HubCredit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hub Credit Note.
        /// </summary>
        internal static string HubCreditNote {
            get {
                return ResourceManager.GetString("HubCreditNote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hub Invoice.
        /// </summary>
        internal static string HubInvoice {
            get {
                return ResourceManager.GetString("HubInvoice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HUBRFQ.
        /// </summary>
        internal static string HUBRFQShort {
            get {
                return ResourceManager.GetString("HUBRFQShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hub Only.
        /// </summary>
        internal static string Hub_Only {
            get {
                return ResourceManager.GetString("Hub Only", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hub &amp; Sales.
        /// </summary>
        internal static string Hub_Sales {
            get {
                return ResourceManager.GetString("Hub Sales", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IHS_{0}_{1}.
        /// </summary>
        internal static string IHSDocuments {
            get {
                return ResourceManager.GetString("IHSDocuments", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IHS Active.
        /// </summary>
        internal static string IHS_Active {
            get {
                return ResourceManager.GetString("IHS_Active", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IHS Active Unconfirmed.
        /// </summary>
        internal static string IHS_ActiveUnconfirmed {
            get {
                return ResourceManager.GetString("IHS_ActiveUnconfirmed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IHS Contact Mfr.
        /// </summary>
        internal static string IHS_ContactMfr {
            get {
                return ResourceManager.GetString("IHS_ContactMfr", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IHS Discontinued.
        /// </summary>
        internal static string IHS_Discontinued {
            get {
                return ResourceManager.GetString("IHS_Discontinued", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IHS Discontinued Unconfirmed.
        /// </summary>
        internal static string IHS_DiscontinuedUnconfirmed {
            get {
                return ResourceManager.GetString("IHS_DiscontinuedUnconfirmed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IHS EOL.
        /// </summary>
        internal static string IHS_EOL {
            get {
                return ResourceManager.GetString("IHS_EOL", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IHS NRFND.
        /// </summary>
        internal static string IHS_NRFND {
            get {
                return ResourceManager.GetString("IHS_NRFND", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IHS Transferred.
        /// </summary>
        internal static string IHS_Transferred {
            get {
                return ResourceManager.GetString("IHS_Transferred", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to CSV Import.
        /// </summary>
        internal static string ImportCSV {
            get {
                return ResourceManager.GetString("ImportCSV", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bom Import Tool.
        /// </summary>
        internal static string ImportTool {
            get {
                return ResourceManager.GetString("ImportTool", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Inactive.
        /// </summary>
        internal static string Inactive {
            get {
                return ResourceManager.GetString("Inactive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Include Credits?.
        /// </summary>
        internal static string IncludeCredits {
            get {
                return ResourceManager.GetString("IncludeCredits", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Information.
        /// </summary>
        internal static string Information {
            get {
                return ResourceManager.GetString("Information", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to CIP_{0}_{1}.
        /// </summary>
        internal static string INSPDFDocuments {
            get {
                return ResourceManager.GetString("INSPDFDocuments", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Internal Purchase Order.
        /// </summary>
        internal static string InternalPurchaseOrder {
            get {
                return ResourceManager.GetString("InternalPurchaseOrder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IPO.
        /// </summary>
        internal static string InternalPurchaseOrderShort {
            get {
                return ResourceManager.GetString("InternalPurchaseOrderShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to In Transit.
        /// </summary>
        internal static string InTransit {
            get {
                return ResourceManager.GetString("InTransit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to INVOICE_{0}_{1}.
        /// </summary>
        internal static string InvDocuments {
            get {
                return ResourceManager.GetString("InvDocuments", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invoice.
        /// </summary>
        internal static string Invoice {
            get {
                return ResourceManager.GetString("Invoice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invoice Line Allocations for this CRMA.
        /// </summary>
        internal static string InvoiceLineAllocationsForCRMA {
            get {
                return ResourceManager.GetString("InvoiceLineAllocationsForCRMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invoice Lines for CRMA.
        /// </summary>
        internal static string InvoiceLinesForCRMA {
            get {
                return ResourceManager.GetString("InvoiceLinesForCRMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sales Order Service Lines.
        /// </summary>
        internal static string InvoiceLinesServices {
            get {
                return ResourceManager.GetString("InvoiceLinesServices", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invoice No.
        /// </summary>
        internal static string InvoiceNo {
            get {
                return ResourceManager.GetString("InvoiceNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invoices.
        /// </summary>
        internal static string Invoices {
            get {
                return ResourceManager.GetString("Invoices", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invoice.
        /// </summary>
        internal static string InvoiceShort {
            get {
                return ResourceManager.GetString("InvoiceShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invoices.
        /// </summary>
        internal static string InvoicesShort {
            get {
                return ResourceManager.GetString("InvoicesShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to POD_{0}_{1}.
        /// </summary>
        internal static string InvPODDocuments {
            get {
                return ResourceManager.GetString("InvPODDocuments", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Internal Purchase Orders.
        /// </summary>
        internal static string IPO {
            get {
                return ResourceManager.GetString("IPO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IPO OFFERS.
        /// </summary>
        internal static string IPOOFFERS {
            get {
                return ResourceManager.GetString("IPOOFFERS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IPO REQUESTED SUPPORT.
        /// </summary>
        internal static string IPOREQUESTEDSUPPORT {
            get {
                return ResourceManager.GetString("IPOREQUESTEDSUPPORT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0}_{1}_{2}_{3}.json.
        /// </summary>
        internal static string Jsonfile {
            get {
                return ResourceManager.GetString("Jsonfile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Jump to.
        /// </summary>
        internal static string JumpTo {
            get {
                return ResourceManager.GetString("JumpTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Division KPI.
        /// </summary>
        internal static string KPIDivision {
            get {
                return ResourceManager.GetString("KPIDivision", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sales KPI.
        /// </summary>
        internal static string KPISales {
            get {
                return ResourceManager.GetString("KPISales", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Team KPI.
        /// </summary>
        internal static string KPITeam {
            get {
                return ResourceManager.GetString("KPITeam", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Landed Cost Calculator.
        /// </summary>
        internal static string LandedCostCalculator {
            get {
                return ResourceManager.GetString("LandedCostCalculator", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Large.
        /// </summary>
        internal static string Large {
            get {
                return ResourceManager.GetString("Large", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &lt;.
        /// </summary>
        internal static string LessThan {
            get {
                return ResourceManager.GetString("LessThan", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &lt;=.
        /// </summary>
        internal static string LessThanOrEqualTo {
            get {
                return ResourceManager.GetString("LessThanOrEqualTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to *** Inactive ***.
        /// </summary>
        internal static string LinePagingInactive {
            get {
                return ResourceManager.GetString("LinePagingInactive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Last Month.
        /// </summary>
        internal static string LM {
            get {
                return ResourceManager.GetString("LM", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Loading.
        /// </summary>
        internal static string Loading {
            get {
                return ResourceManager.GetString("Loading", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Login.
        /// </summary>
        internal static string Login {
            get {
                return ResourceManager.GetString("Login", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} minutes.
        /// </summary>
        internal static string LoginTimeoutInMinutes {
            get {
                return ResourceManager.GetString("LoginTimeoutInMinutes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lot.
        /// </summary>
        internal static string Lot {
            get {
                return ResourceManager.GetString("Lot", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lot No.
        /// </summary>
        internal static string LotNo {
            get {
                return ResourceManager.GetString("LotNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lot.
        /// </summary>
        internal static string Lots {
            get {
                return ResourceManager.GetString("Lots", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lots.
        /// </summary>
        internal static string LotShort {
            get {
                return ResourceManager.GetString("LotShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lots.
        /// </summary>
        internal static string LotsShort {
            get {
                return ResourceManager.GetString("LotsShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Last Year.
        /// </summary>
        internal static string LY {
            get {
                return ResourceManager.GetString("LY", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MAC Address.
        /// </summary>
        internal static string MacAddress {
            get {
                return ResourceManager.GetString("MacAddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Male.
        /// </summary>
        internal static string Male {
            get {
                return ResourceManager.GetString("Male", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Manual Allocation to IPO/PO.
        /// </summary>
        internal static string ManualAllocation {
            get {
                return ResourceManager.GetString("ManualAllocation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Manufacturer.
        /// </summary>
        internal static string ManufacturerShort {
            get {
                return ResourceManager.GetString("ManufacturerShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Master Application Settings.
        /// </summary>
        internal static string MasterApplicationSettings {
            get {
                return ResourceManager.GetString("MasterApplicationSettings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to max.
        /// </summary>
        internal static string Max {
            get {
                return ResourceManager.GetString("Max", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Medium.
        /// </summary>
        internal static string Medium {
            get {
                return ResourceManager.GetString("Medium", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MFGExcel_{0}_{1}.
        /// </summary>
        internal static string MfeDocuments {
            get {
                return ResourceManager.GetString("MfeDocuments", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MFG_{0}_{1}.
        /// </summary>
        internal static string MfpDocuments {
            get {
                return ResourceManager.GetString("MfpDocuments", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to minutes.
        /// </summary>
        internal static string Minutes {
            get {
                return ResourceManager.GetString("Minutes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 10 minutes.
        /// </summary>
        internal static string Minutes_10 {
            get {
                return ResourceManager.GetString("Minutes_10", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 15 minutes.
        /// </summary>
        internal static string Minutes_15 {
            get {
                return ResourceManager.GetString("Minutes_15", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 30 minutes.
        /// </summary>
        internal static string Minutes_30 {
            get {
                return ResourceManager.GetString("Minutes_30", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 5 minutes.
        /// </summary>
        internal static string Minutes_5 {
            get {
                return ResourceManager.GetString("Minutes_5", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to More Internal Purchase Orders.
        /// </summary>
        internal static string MoreOpenInternalPurchaseOrders {
            get {
                return ResourceManager.GetString("MoreOpenInternalPurchaseOrders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to More Purchase Orders.
        /// </summary>
        internal static string MoreOpenPurchaseOrders {
            get {
                return ResourceManager.GetString("MoreOpenPurchaseOrders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to More Quotes.
        /// </summary>
        internal static string MoreOpenQuotes {
            get {
                return ResourceManager.GetString("MoreOpenQuotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to More Requirements.
        /// </summary>
        internal static string MoreOpenRequirements {
            get {
                return ResourceManager.GetString("MoreOpenRequirements", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to More Sales Orders.
        /// </summary>
        internal static string MoreOpenSalesOrders {
            get {
                return ResourceManager.GetString("MoreOpenSalesOrders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to More Purchase Orders Due In.
        /// </summary>
        internal static string MorePurchaseOrdersDueIn {
            get {
                return ResourceManager.GetString("MorePurchaseOrdersDueIn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to More Received Purchase Orders.
        /// </summary>
        internal static string MoreReceivedOrders {
            get {
                return ResourceManager.GetString("MoreReceivedOrders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to More Sales Orders Ready to Ship.
        /// </summary>
        internal static string MoreSalesOrdersReadyToShip {
            get {
                return ResourceManager.GetString("MoreSalesOrdersReadyToShip", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Move into Stock.
        /// </summary>
        internal static string MoveintoStock {
            get {
                return ResourceManager.GetString("MoveintoStock", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &lt; Select &gt;.
        /// </summary>
        internal static string MSelect {
            get {
                return ResourceManager.GetString("MSelect", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MTD.
        /// </summary>
        internal static string MTD {
            get {
                return ResourceManager.GetString("MTD", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to My.
        /// </summary>
        internal static string My {
            get {
                return ResourceManager.GetString("My", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to My Supplier Approvals.
        /// </summary>
        internal static string MySupplierApprovals {
            get {
                return ResourceManager.GetString("MySupplierApprovals", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to (Max 41).
        /// </summary>
        internal static string Narrative {
            get {
                return ResourceManager.GetString("Narrative", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New.
        /// </summary>
        internal static string New {
            get {
                return ResourceManager.GetString("New", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New CAF.
        /// </summary>
        internal static string NewCreditLimit {
            get {
                return ResourceManager.GetString("NewCreditLimit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New Credit Note.
        /// </summary>
        internal static string NewCreditNote {
            get {
                return ResourceManager.GetString("NewCreditNote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New Customer Requirement.
        /// </summary>
        internal static string NewCustomerRequirement {
            get {
                return ResourceManager.GetString("NewCustomerRequirement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New Customer RMA.
        /// </summary>
        internal static string NewCustomerRMA {
            get {
                return ResourceManager.GetString("NewCustomerRMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New Debit Note.
        /// </summary>
        internal static string NewDebitNote {
            get {
                return ResourceManager.GetString("NewDebitNote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New EPR.
        /// </summary>
        internal static string NewEPR {
            get {
                return ResourceManager.GetString("NewEPR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New Goods In Note.
        /// </summary>
        internal static string NewGI {
            get {
                return ResourceManager.GetString("NewGI", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New Goods In.
        /// </summary>
        internal static string NewGoodsIn {
            get {
                return ResourceManager.GetString("NewGoodsIn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New Invoice.
        /// </summary>
        internal static string NewInvoice {
            get {
                return ResourceManager.GetString("NewInvoice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New Line Item.
        /// </summary>
        internal static string NewLineItem {
            get {
                return ResourceManager.GetString("NewLineItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to From Lot.
        /// </summary>
        internal static string NewLineLot {
            get {
                return ResourceManager.GetString("NewLineLot", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New NPR.
        /// </summary>
        internal static string NewNPR {
            get {
                return ResourceManager.GetString("NewNPR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New Purchase Order.
        /// </summary>
        internal static string NewPurchaseOrder {
            get {
                return ResourceManager.GetString("NewPurchaseOrder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New Purchase Requisition.
        /// </summary>
        internal static string NewPurchaseRequisition {
            get {
                return ResourceManager.GetString("NewPurchaseRequisition", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New Quote.
        /// </summary>
        internal static string NewQuote {
            get {
                return ResourceManager.GetString("NewQuote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New Sales Order.
        /// </summary>
        internal static string NewSalesOrder {
            get {
                return ResourceManager.GetString("NewSalesOrder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New Short Shipment.
        /// </summary>
        internal static string NewShortShipment {
            get {
                return ResourceManager.GetString("NewShortShipment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New Supplier RMA.
        /// </summary>
        internal static string NewSupplierRMA {
            get {
                return ResourceManager.GetString("NewSupplierRMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New Supplier RMA Shipment.
        /// </summary>
        internal static string NewSupplierRMAShipment {
            get {
                return ResourceManager.GetString("NewSupplierRMAShipment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Next Month.
        /// </summary>
        internal static string NM {
            get {
                return ResourceManager.GetString("NM", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No.
        /// </summary>
        internal static string No {
            get {
                return ResourceManager.GetString("No", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to NoBid.
        /// </summary>
        internal static string NoBid {
            get {
                return ResourceManager.GetString("NoBid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No Matches.
        /// </summary>
        internal static string NoMatches {
            get {
                return ResourceManager.GetString("NoMatches", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Non Preferred Source.
        /// </summary>
        internal static string NonPreferred {
            get {
                return ResourceManager.GetString("NonPreferred", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Not Authorised.
        /// </summary>
        internal static string NotAuthorised {
            get {
                return ResourceManager.GetString("NotAuthorised", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Not Completed.
        /// </summary>
        internal static string NotCompleted {
            get {
                return ResourceManager.GetString("NotCompleted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notes:.
        /// </summary>
        internal static string Notes {
            get {
                return ResourceManager.GetString("Notes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Not Quoted.
        /// </summary>
        internal static string NotQuoted {
            get {
                return ResourceManager.GetString("NotQuoted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Not Sent.
        /// </summary>
        internal static string Notsent {
            get {
                return ResourceManager.GetString("Notsent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to NPR.
        /// </summary>
        internal static string NPR {
            get {
                return ResourceManager.GetString("NPR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to NPR.
        /// </summary>
        internal static string NPRShort {
            get {
                return ResourceManager.GetString("NPRShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to NRFND.
        /// </summary>
        internal static string NRFND {
            get {
                return ResourceManager.GetString("NRFND", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Number.
        /// </summary>
        internal static string Number {
            get {
                return ResourceManager.GetString("Number", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No..
        /// </summary>
        internal static string NumberShort {
            get {
                return ResourceManager.GetString("NumberShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to #.
        /// </summary>
        internal static string NumberShorter {
            get {
                return ResourceManager.GetString("NumberShorter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Next Year.
        /// </summary>
        internal static string NY {
            get {
                return ResourceManager.GetString("NY", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to of.
        /// </summary>
        internal static string Of {
            get {
                return ResourceManager.GetString("Of", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Offer Added.
        /// </summary>
        internal static string OfferAdded {
            get {
                return ResourceManager.GetString("OfferAdded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Offers Added.
        /// </summary>
        internal static string OffersAdded {
            get {
                return ResourceManager.GetString("OffersAdded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to OGEL Lines.
        /// </summary>
        internal static string OGELLines {
            get {
                return ResourceManager.GetString("OGELLines", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open.
        /// </summary>
        internal static string Open {
            get {
                return ResourceManager.GetString("Open", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Click here for Sales Dashboard.
        /// </summary>
        internal static string OpenPowerBISales {
            get {
                return ResourceManager.GetString("OpenPowerBISales", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ordered.
        /// </summary>
        internal static string Ordered {
            get {
                return ResourceManager.GetString("Ordered", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sales Order Generated.
        /// </summary>
        internal static string OrderGenerated {
            get {
                return ResourceManager.GetString("OrderGenerated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Original Offer.
        /// </summary>
        internal static string OriginalOffer {
            get {
                return ResourceManager.GetString("OriginalOffer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Overdue.
        /// </summary>
        internal static string Overdue {
            get {
                return ResourceManager.GetString("Overdue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Packing Slip.
        /// </summary>
        internal static string PackingSlip {
            get {
                return ResourceManager.GetString("PackingSlip", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Page.
        /// </summary>
        internal static string Page {
            get {
                return ResourceManager.GetString("Page", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Paid.
        /// </summary>
        internal static string Paid {
            get {
                return ResourceManager.GetString("Paid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Part Allocated.
        /// </summary>
        internal static string PartAllocated {
            get {
                return ResourceManager.GetString("PartAllocated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Partial release to client.
        /// </summary>
        internal static string Partial {
            get {
                return ResourceManager.GetString("Partial", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Partial Released.
        /// </summary>
        internal static string PartialReleased {
            get {
                return ResourceManager.GetString("PartialReleased", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Part No.
        /// </summary>
        internal static string PartNo {
            get {
                return ResourceManager.GetString("PartNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Part Posted.
        /// </summary>
        internal static string PartPosted {
            get {
                return ResourceManager.GetString("PartPosted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Part Received.
        /// </summary>
        internal static string PartReceived {
            get {
                return ResourceManager.GetString("PartReceived", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Part Shipped.
        /// </summary>
        internal static string PartShipped {
            get {
                return ResourceManager.GetString("PartShipped", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fogot password.
        /// </summary>
        internal static string Password {
            get {
                return ResourceManager.GetString("Password", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to %.
        /// </summary>
        internal static string Pct {
            get {
                return ResourceManager.GetString("Pct", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PDF Document.
        /// </summary>
        internal static string PDFDocument {
            get {
                return ResourceManager.GetString("PDFDocument", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0}_{1}_{2}_{3}.pdf.
        /// </summary>
        internal static string PDFFileName {
            get {
                return ResourceManager.GetString("PDFFileName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to %.
        /// </summary>
        internal static string PercentSymbol {
            get {
                return ResourceManager.GetString("PercentSymbol", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Per Page.
        /// </summary>
        internal static string PerPage {
            get {
                return ResourceManager.GetString("PerPage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Placed.
        /// </summary>
        internal static string Placed {
            get {
                return ResourceManager.GetString("Placed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchase Order.
        /// </summary>
        internal static string PO {
            get {
                return ResourceManager.GetString("PO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PO_{0}_{1}.
        /// </summary>
        internal static string PODocuments {
            get {
                return ResourceManager.GetString("PODocuments", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rebound-Dubai.
        /// </summary>
        internal static string POHub {
            get {
                return ResourceManager.GetString("POHub", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchase Order Lines for this Debit Note.
        /// </summary>
        internal static string POLinesForDebitNote {
            get {
                return ResourceManager.GetString("POLinesForDebitNote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchase Order Lines for use by this Supplier RMA.
        /// </summary>
        internal static string POLinesForSRMA {
            get {
                return ResourceManager.GetString("POLinesForSRMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Price Request.
        /// </summary>
        internal static string POQuote {
            get {
                return ResourceManager.GetString("POQuote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to POREPORT_{0}_{1}.
        /// </summary>
        internal static string PORPDFDocumentsNew {
            get {
                return ResourceManager.GetString("PORPDFDocumentsNew", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Possible Alternate.
        /// </summary>
        internal static string PossibleAlternative {
            get {
                return ResourceManager.GetString("PossibleAlternative", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Posted.
        /// </summary>
        internal static string Posted {
            get {
                return ResourceManager.GetString("Posted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Created with {0}.
        /// </summary>
        internal static string PrintAd {
            get {
                return ResourceManager.GetString("PrintAd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hazardous Message.
        /// </summary>
        internal static string PrintHazardous {
            get {
                return ResourceManager.GetString("PrintHazardous", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Current print set to:.
        /// </summary>
        internal static string PrintOption {
            get {
                return ResourceManager.GetString("PrintOption", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to High.
        /// </summary>
        internal static string PriorityHigh {
            get {
                return ResourceManager.GetString("PriorityHigh", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Normal.
        /// </summary>
        internal static string PriorityNormal {
            get {
                return ResourceManager.GetString("PriorityNormal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pro Forma Invoice.
        /// </summary>
        internal static string ProFormaInvoice {
            get {
                return ResourceManager.GetString("ProFormaInvoice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pro Forma Receiving Docket.
        /// </summary>
        internal static string ProFormaReceivingDocket {
            get {
                return ResourceManager.GetString("ProFormaReceivingDocket", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Prospect.
        /// </summary>
        internal static string ProspectShort {
            get {
                return ResourceManager.GetString("ProspectShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rebound Dubai.
        /// </summary>
        internal static string PurchaseHub {
            get {
                return ResourceManager.GetString("PurchaseHub", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchase Order.
        /// </summary>
        internal static string PurchaseOrder {
            get {
                return ResourceManager.GetString("PurchaseOrder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchase Order No.
        /// </summary>
        internal static string PurchaseOrderNo {
            get {
                return ResourceManager.GetString("PurchaseOrderNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchase Orders.
        /// </summary>
        internal static string PurchaseOrders {
            get {
                return ResourceManager.GetString("PurchaseOrders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PO.
        /// </summary>
        internal static string PurchaseOrderShort {
            get {
                return ResourceManager.GetString("PurchaseOrderShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to POs.
        /// </summary>
        internal static string PurchaseOrdersShort {
            get {
                return ResourceManager.GetString("PurchaseOrdersShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Price Request.
        /// </summary>
        internal static string PurchaseQuote {
            get {
                return ResourceManager.GetString("PurchaseQuote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PurchaseQuote_{0}_{1}.
        /// </summary>
        internal static string PurchaseQuoteDoc {
            get {
                return ResourceManager.GetString("PurchaseQuoteDoc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchase Requisition.
        /// </summary>
        internal static string PurchaseRequisition {
            get {
                return ResourceManager.GetString("PurchaseRequisition", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchase Requisitions.
        /// </summary>
        internal static string PurchaseRequisitions {
            get {
                return ResourceManager.GetString("PurchaseRequisitions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pur Reqs.
        /// </summary>
        internal static string PurchaseRequisitionsShort {
            get {
                return ResourceManager.GetString("PurchaseRequisitionsShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to QC_{0}_{1}.
        /// </summary>
        internal static string QCDocument {
            get {
                return ResourceManager.GetString("QCDocument", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quote.
        /// </summary>
        internal static string Qu {
            get {
                return ResourceManager.GetString("Qu", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ({0} ordered, {1} shipped).
        /// </summary>
        internal static string QuantityOrderedAndShipped {
            get {
                return ResourceManager.GetString("QuantityOrderedAndShipped", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quarantined.
        /// </summary>
        internal static string Quarantined {
            get {
                return ResourceManager.GetString("Quarantined", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quarantine Product.
        /// </summary>
        internal static string QuarantineProduct {
            get {
                return ResourceManager.GetString("QuarantineProduct", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &lt; quick search &gt;.
        /// </summary>
        internal static string QuickSearchWaterMark {
            get {
                return ResourceManager.GetString("QuickSearchWaterMark", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quote.
        /// </summary>
        internal static string Quote {
            get {
                return ResourceManager.GetString("Quote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to QUOTED TO.
        /// </summary>
        internal static string QUOTEDTO {
            get {
                return ResourceManager.GetString("QUOTEDTO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quote No.
        /// </summary>
        internal static string QuoteNo {
            get {
                return ResourceManager.GetString("QuoteNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quotes.
        /// </summary>
        internal static string Quotes {
            get {
                return ResourceManager.GetString("Quotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quote.
        /// </summary>
        internal static string QuoteShort {
            get {
                return ResourceManager.GetString("QuoteShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quotes.
        /// </summary>
        internal static string QuotesShort {
            get {
                return ResourceManager.GetString("QuotesShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ready.
        /// </summary>
        internal static string Ready {
            get {
                return ResourceManager.GetString("Ready", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Receipt and linked stock.
        /// </summary>
        internal static string ReceiptAndLinkedStock {
            get {
                return ResourceManager.GetString("ReceiptAndLinkedStock", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Receipt, linked stock and shipments.
        /// </summary>
        internal static string ReceiptLinkedStockAndShipments {
            get {
                return ResourceManager.GetString("ReceiptLinkedStockAndShipments", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Receipt only.
        /// </summary>
        internal static string ReceiptOnly {
            get {
                return ResourceManager.GetString("ReceiptOnly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Receive Customer RMA.
        /// </summary>
        internal static string ReceiveCustomerRMA {
            get {
                return ResourceManager.GetString("ReceiveCustomerRMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Receive CRMA.
        /// </summary>
        internal static string ReceiveCustomerRMAShort {
            get {
                return ResourceManager.GetString("ReceiveCustomerRMAShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Receive CRMAs.
        /// </summary>
        internal static string ReceiveCustomerRMAsShort {
            get {
                return ResourceManager.GetString("ReceiveCustomerRMAsShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Received.
        /// </summary>
        internal static string Received {
            get {
                return ResourceManager.GetString("Received", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Received Customer RMAs.
        /// </summary>
        internal static string ReceivedCRMAs {
            get {
                return ResourceManager.GetString("ReceivedCRMAs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Received CRMAs.
        /// </summary>
        internal static string ReceivedCustomerRMAShort {
            get {
                return ResourceManager.GetString("ReceivedCustomerRMAShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Received Purchase Orders.
        /// </summary>
        internal static string ReceivedPurchaseOrders {
            get {
                return ResourceManager.GetString("ReceivedPurchaseOrders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Received POs.
        /// </summary>
        internal static string ReceivedPurchaseOrderShort {
            get {
                return ResourceManager.GetString("ReceivedPurchaseOrderShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Receive Purchase Order.
        /// </summary>
        internal static string ReceivePurchaseOrder {
            get {
                return ResourceManager.GetString("ReceivePurchaseOrder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Receive PO.
        /// </summary>
        internal static string ReceivePurchaseOrderShort {
            get {
                return ResourceManager.GetString("ReceivePurchaseOrderShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Receive POs.
        /// </summary>
        internal static string ReceivePurchaseOrdersShort {
            get {
                return ResourceManager.GetString("ReceivePurchaseOrdersShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Receiving Docket.
        /// </summary>
        internal static string ReceivingDocket {
            get {
                return ResourceManager.GetString("ReceivingDocket", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Recent.
        /// </summary>
        internal static string Recent {
            get {
                return ResourceManager.GetString("Recent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Recent Parts Sourced.
        /// </summary>
        internal static string RecentPartsSourced {
            get {
                return ResourceManager.GetString("RecentPartsSourced", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rejected Label.
        /// </summary>
        internal static string RejectedLabel {
            get {
                return ResourceManager.GetString("RejectedLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Related Receipt.
        /// </summary>
        internal static string RelatedReceipt {
            get {
                return ResourceManager.GetString("RelatedReceipt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Released to client.
        /// </summary>
        internal static string Released {
            get {
                return ResourceManager.GetString("Released", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Report.
        /// </summary>
        internal static string Report {
            get {
                return ResourceManager.GetString("Report", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Report NPR.
        /// </summary>
        internal static string ReportNPR {
            get {
                return ResourceManager.GetString("ReportNPR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to STO.
        /// </summary>
        internal static string ReportSTO {
            get {
                return ResourceManager.GetString("ReportSTO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to denotes a required field.
        /// </summary>
        internal static string RequiredField {
            get {
                return ResourceManager.GetString("RequiredField", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Required outwork.
        /// </summary>
        internal static string Requiredoutwork {
            get {
                return ResourceManager.GetString("Requiredoutwork", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Requirement Sourcing Results.
        /// </summary>
        internal static string RequirementSourcingResults {
            get {
                return ResourceManager.GetString("RequirementSourcingResults", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reselect.
        /// </summary>
        internal static string Reselect {
            get {
                return ResourceManager.GetString("Reselect", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Results Limit.
        /// </summary>
        internal static string ResultsLimit {
            get {
                return ResourceManager.GetString("ResultsLimit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to result(s).
        /// </summary>
        internal static string ResultsWithOptionalS {
            get {
                return ResourceManager.GetString("ResultsWithOptionalS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Return to supplier.
        /// </summary>
        internal static string Returntosupplier {
            get {
                return ResourceManager.GetString("Returntosupplier", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ROHS2.
        /// </summary>
        internal static string ROHS2 {
            get {
                return ResourceManager.GetString("ROHS2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to RoHS Compliant.
        /// </summary>
        internal static string ROHSCompliant {
            get {
                return ResourceManager.GetString("ROHSCompliant", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to RoHS Exempt.
        /// </summary>
        internal static string ROHSExempt {
            get {
                return ResourceManager.GetString("ROHSExempt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to RoHS Non-compliant.
        /// </summary>
        internal static string ROHSNonCompliant {
            get {
                return ResourceManager.GetString("ROHSNonCompliant", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to RoHS Not Applicable.
        /// </summary>
        internal static string ROHSNotApplicable {
            get {
                return ResourceManager.GetString("ROHSNotApplicable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ROHS2.
        /// </summary>
        internal static string ROHSROHS2 {
            get {
                return ResourceManager.GetString("ROHSROHS2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to RoHS 5/6.
        /// </summary>
        internal static string ROHSROHS56 {
            get {
                return ResourceManager.GetString("ROHSROHS56", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to RoHS 6/6.
        /// </summary>
        internal static string ROHSROHS66 {
            get {
                return ResourceManager.GetString("ROHSROHS66", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to RoHS Unknown.
        /// </summary>
        internal static string ROHSUnknown {
            get {
                return ResourceManager.GetString("ROHSUnknown", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New RFQ from client.
        /// </summary>
        internal static string RPQ {
            get {
                return ResourceManager.GetString("RPQ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sales Order.
        /// </summary>
        internal static string SalesOrder {
            get {
                return ResourceManager.GetString("SalesOrder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sales Order No.
        /// </summary>
        internal static string SalesOrderNo {
            get {
                return ResourceManager.GetString("SalesOrderNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sales Orders.
        /// </summary>
        internal static string SalesOrders {
            get {
                return ResourceManager.GetString("SalesOrders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SO.
        /// </summary>
        internal static string SalesOrderShort {
            get {
                return ResourceManager.GetString("SalesOrderShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SOs.
        /// </summary>
        internal static string SalesOrdersShort {
            get {
                return ResourceManager.GetString("SalesOrdersShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Saving.
        /// </summary>
        internal static string Saving {
            get {
                return ResourceManager.GetString("Saving", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Schedular is already enabled for this client..
        /// </summary>
        internal static string schedularenablealready {
            get {
                return ResourceManager.GetString("schedularenablealready", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Scrap.
        /// </summary>
        internal static string Scrap {
            get {
                return ResourceManager.GetString("Scrap", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Search.
        /// </summary>
        internal static string Search {
            get {
                return ResourceManager.GetString("Search", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Searching.
        /// </summary>
        internal static string Searching {
            get {
                return ResourceManager.GetString("Searching", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to (Max 16).
        /// </summary>
        internal static string SecondRef {
            get {
                return ResourceManager.GetString("SecondRef", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select.
        /// </summary>
        internal static string Select {
            get {
                return ResourceManager.GetString("Select", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select All.
        /// </summary>
        internal static string SelectAll {
            get {
                return ResourceManager.GetString("SelectAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Currently Selected Part.
        /// </summary>
        internal static string SelectedPart {
            get {
                return ResourceManager.GetString("SelectedPart", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sending.
        /// </summary>
        internal static string Sending {
            get {
                return ResourceManager.GetString("Sending", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to On Hub.
        /// </summary>
        internal static string SendToPurchaseHub {
            get {
                return ResourceManager.GetString("SendToPurchaseHub", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sent to Customer Only.
        /// </summary>
        internal static string senttocustomerOnly {
            get {
                return ResourceManager.GetString("senttocustomerOnly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Serial.
        /// </summary>
        internal static string Serial {
            get {
                return ResourceManager.GetString("Serial", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Service.
        /// </summary>
        internal static string Service {
            get {
                return ResourceManager.GetString("Service", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Service Name.
        /// </summary>
        internal static string ServiceName {
            get {
                return ResourceManager.GetString("ServiceName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Services.
        /// </summary>
        internal static string Services {
            get {
                return ResourceManager.GetString("Services", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Service.
        /// </summary>
        internal static string ServiceShort {
            get {
                return ResourceManager.GetString("ServiceShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Services.
        /// </summary>
        internal static string ServicesShort {
            get {
                return ResourceManager.GetString("ServicesShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Global Settings.
        /// </summary>
        internal static string Setup_CertificateSettings {
            get {
                return ResourceManager.GetString("Setup_CertificateSettings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Warnings.
        /// </summary>
        internal static string Setup_CompanyDetails_Warnings {
            get {
                return ResourceManager.GetString("Setup_CompanyDetails_Warnings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Company Settings.
        /// </summary>
        internal static string Setup_CompanySettings {
            get {
                return ResourceManager.GetString("Setup_CompanySettings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Root Cause Code.
        /// </summary>
        internal static string Setup_EigthDCode {
            get {
                return ResourceManager.GetString("Setup_EigthDCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Global Security Settings.
        /// </summary>
        internal static string Setup_GCSecurity {
            get {
                return ResourceManager.GetString("Setup_GCSecurity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Global Security Settings.
        /// </summary>
        internal static string Setup_GlobalSecurity {
            get {
                return ResourceManager.GetString("Setup_GlobalSecurity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Global Settings.
        /// </summary>
        internal static string Setup_GlobalSettings {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Global Settings.
        /// </summary>
        internal static string Setup_GTUpdate {
            get {
                return ResourceManager.GetString("Setup_GTUpdate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invoice.
        /// </summary>
        internal static string Setup_InvoiceSetting {
            get {
                return ResourceManager.GetString("Setup_InvoiceSetting", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Company Settings.
        /// </summary>
        internal static string Setup_OGELLicensesSettings {
            get {
                return ResourceManager.GetString("Setup_OGELLicensesSettings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Personal Settings.
        /// </summary>
        internal static string Setup_Personal {
            get {
                return ResourceManager.GetString("Setup_Personal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Company Settings.
        /// </summary>
        internal static string Setup_PrinterSettings {
            get {
                return ResourceManager.GetString("Setup_PrinterSettings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Security Settings.
        /// </summary>
        internal static string Setup_Security {
            get {
                return ResourceManager.GetString("Setup_Security", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Master Status.
        /// </summary>
        internal static string Setup_SetupList {
            get {
                return ResourceManager.GetString("Setup_SetupList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipped.
        /// </summary>
        internal static string Ship {
            get {
                return ResourceManager.GetString("Ship", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ship ASAP.
        /// </summary>
        internal static string ShipASAP {
            get {
                return ResourceManager.GetString("ShipASAP", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipped.
        /// </summary>
        internal static string Shipped {
            get {
                return ResourceManager.GetString("Shipped", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipped Lines.
        /// </summary>
        internal static string ShippedLines {
            get {
                return ResourceManager.GetString("ShippedLines", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipping Info.
        /// </summary>
        internal static string ShippingInfo {
            get {
                return ResourceManager.GetString("ShippingInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ship Sales Order.
        /// </summary>
        internal static string ShipSalesOrder {
            get {
                return ResourceManager.GetString("ShipSalesOrder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ship SO.
        /// </summary>
        internal static string ShipSalesOrderShort {
            get {
                return ResourceManager.GetString("ShipSalesOrderShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ship SOs.
        /// </summary>
        internal static string ShipSalesOrdersShort {
            get {
                return ResourceManager.GetString("ShipSalesOrdersShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ship Supplier RMA.
        /// </summary>
        internal static string ShipSupplierRMA {
            get {
                return ResourceManager.GetString("ShipSupplierRMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ship SRMA.
        /// </summary>
        internal static string ShipSupplierRMAShort {
            get {
                return ResourceManager.GetString("ShipSupplierRMAShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ship SRMAs.
        /// </summary>
        internal static string ShipSupplierRMAsShort {
            get {
                return ResourceManager.GetString("ShipSupplierRMAsShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Short Shipment.
        /// </summary>
        internal static string ShortShipment {
            get {
                return ResourceManager.GetString("ShortShipment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Short Shipped.
        /// </summary>
        internal static string ShortShipped {
            get {
                return ResourceManager.GetString("ShortShipped", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Showing {0} of {1} result(s).
        /// </summary>
        internal static string ShowingXOfYResults {
            get {
                return ResourceManager.GetString("ShowingXOfYResults", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SI_{0}_{1}.
        /// </summary>
        internal static string SIDocuments {
            get {
                return ResourceManager.GetString("SIDocuments", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sales Order.
        /// </summary>
        internal static string SO {
            get {
                return ResourceManager.GetString("SO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SOED_{0}_{1}.
        /// </summary>
        internal static string SOExcelDocDocuments {
            get {
                return ResourceManager.GetString("SOExcelDocDocuments", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Images.
        /// </summary>
        internal static string SOImages {
            get {
                return ResourceManager.GetString("SOImages", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sold.
        /// </summary>
        internal static string Sold {
            get {
                return ResourceManager.GetString("Sold", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SO_{0}_{1}.
        /// </summary>
        internal static string SOPDFDocuments {
            get {
                return ResourceManager.GetString("SOPDFDocuments", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sales Order Report.
        /// </summary>
        internal static string SOReport {
            get {
                return ResourceManager.GetString("SOReport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SOR_{0}_{1}.
        /// </summary>
        internal static string SORPDFDocuments {
            get {
                return ResourceManager.GetString("SORPDFDocuments", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SOREPORT_{0}_{1}.
        /// </summary>
        internal static string SORPDFDocumentsNew {
            get {
                return ResourceManager.GetString("SORPDFDocumentsNew", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sourcing Results.
        /// </summary>
        internal static string SourcingResults {
            get {
                return ResourceManager.GetString("SourcingResults", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sourcing.
        /// </summary>
        internal static string SourcingShort {
            get {
                return ResourceManager.GetString("SourcingShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Check Supplier / Manufacturer Data.
        /// </summary>
        internal static string SourcingSupplier {
            get {
                return ResourceManager.GetString("SourcingSupplier", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier RMA.
        /// </summary>
        internal static string SRMA {
            get {
                return ResourceManager.GetString("SRMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SRMA_{0}_{1}.
        /// </summary>
        internal static string SRMADocuments {
            get {
                return ResourceManager.GetString("SRMADocuments", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier RMA Shipment.
        /// </summary>
        internal static string SRMAShipment {
            get {
                return ResourceManager.GetString("SRMAShipment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to (Standard ship in {0}).
        /// </summary>
        internal static string StandardShipping {
            get {
                return ResourceManager.GetString("StandardShipping", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Status.
        /// </summary>
        internal static string Status {
            get {
                return ResourceManager.GetString("Status", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stock.
        /// </summary>
        internal static string Stock {
            get {
                return ResourceManager.GetString("Stock", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to available.
        /// </summary>
        internal static string StockAvailable {
            get {
                return ResourceManager.GetString("StockAvailable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to STK_{0}_{1}.
        /// </summary>
        internal static string StockDocuments {
            get {
                return ResourceManager.GetString("StockDocuments", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stock Item.
        /// </summary>
        internal static string StockItem {
            get {
                return ResourceManager.GetString("StockItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stock.
        /// </summary>
        internal static string StockItems {
            get {
                return ResourceManager.GetString("StockItems", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stock Item.
        /// </summary>
        internal static string StockItemShort {
            get {
                return ResourceManager.GetString("StockItemShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stock.
        /// </summary>
        internal static string StockItemsShort {
            get {
                return ResourceManager.GetString("StockItemsShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stock Label.
        /// </summary>
        internal static string StockLabel {
            get {
                return ResourceManager.GetString("StockLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to left to allocate.
        /// </summary>
        internal static string StockLeftToAllocate {
            get {
                return ResourceManager.GetString("StockLeftToAllocate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stock No.
        /// </summary>
        internal static string StockNumberShort {
            get {
                return ResourceManager.GetString("StockNumberShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ordered.
        /// </summary>
        internal static string StockOrdered {
            get {
                return ResourceManager.GetString("StockOrdered", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stock Provision(%).
        /// </summary>
        internal static string StockProvision {
            get {
                return ResourceManager.GetString("StockProvision", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} available.
        /// </summary>
        internal static string StockQuantityAvailable {
            get {
                return ResourceManager.GetString("StockQuantityAvailable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} at {1}.
        /// </summary>
        internal static string StockQuantityAvailableAt {
            get {
                return ResourceManager.GetString("StockQuantityAvailableAt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stock.
        /// </summary>
        internal static string StockShort {
            get {
                return ResourceManager.GetString("StockShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier Adviced.
        /// </summary>
        internal static string SupplierAdvice {
            get {
                return ResourceManager.GetString("SupplierAdvice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier Invoice.
        /// </summary>
        internal static string SupplierInvoice {
            get {
                return ResourceManager.GetString("SupplierInvoice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open Supplier/PO Approvals.
        /// </summary>
        internal static string SupplierPOApproval {
            get {
                return ResourceManager.GetString("SupplierPOApproval", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier RMA.
        /// </summary>
        internal static string SupplierRMA {
            get {
                return ResourceManager.GetString("SupplierRMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier RMA No.
        /// </summary>
        internal static string SupplierRMANo {
            get {
                return ResourceManager.GetString("SupplierRMANo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier RMAs.
        /// </summary>
        internal static string SupplierRMAs {
            get {
                return ResourceManager.GetString("SupplierRMAs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier RMA Shipment.
        /// </summary>
        internal static string SupplierRMAShipment {
            get {
                return ResourceManager.GetString("SupplierRMAShipment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SRMA.
        /// </summary>
        internal static string SupplierRMAShort {
            get {
                return ResourceManager.GetString("SupplierRMAShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SRMAs.
        /// </summary>
        internal static string SupplierRMAsShort {
            get {
                return ResourceManager.GetString("SupplierRMAsShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier.
        /// </summary>
        internal static string SupplierShort {
            get {
                return ResourceManager.GetString("SupplierShort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to TBC.
        /// </summary>
        internal static string TBC {
            get {
                return ResourceManager.GetString("TBC", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Team.
        /// </summary>
        internal static string Team {
            get {
                return ResourceManager.GetString("Team", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Thumbnail.
        /// </summary>
        internal static string Thumbnail {
            get {
                return ResourceManager.GetString("Thumbnail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This Month.
        /// </summary>
        internal static string TM {
            get {
                return ResourceManager.GetString("TM", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Today.
        /// </summary>
        internal static string Today {
            get {
                return ResourceManager.GetString("Today", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Today Open Purchase Orders.
        /// </summary>
        internal static string TodayOpenPurchaseOrders {
            get {
                return ResourceManager.GetString("TodayOpenPurchaseOrders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reminder.
        /// </summary>
        internal static string ToDoAlert {
            get {
                return ResourceManager.GetString("ToDoAlert", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total.
        /// </summary>
        internal static string Total {
            get {
                return ResourceManager.GetString("Total", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total stock provision value.
        /// </summary>
        internal static string TotalStocKProvisionValue {
            get {
                return ResourceManager.GetString("TotalStocKProvisionValue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Traceable Source.
        /// </summary>
        internal static string Traceable {
            get {
                return ResourceManager.GetString("Traceable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Transferred.
        /// </summary>
        internal static string Transferred {
            get {
                return ResourceManager.GetString("Transferred", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Trusted Source.
        /// </summary>
        internal static string Trusted {
            get {
                return ResourceManager.GetString("Trusted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This Year.
        /// </summary>
        internal static string TY {
            get {
                return ResourceManager.GetString("TY", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to UK.
        /// </summary>
        internal static string UK {
            get {
                return ResourceManager.GetString("UK", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unapprove.
        /// </summary>
        internal static string Unapprove {
            get {
                return ResourceManager.GetString("Unapprove", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unapproved.
        /// </summary>
        internal static string Unapproved {
            get {
                return ResourceManager.GetString("Unapproved", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unchecked.
        /// </summary>
        internal static string Unchecked {
            get {
                return ResourceManager.GetString("Unchecked", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unposted.
        /// </summary>
        internal static string Unposted {
            get {
                return ResourceManager.GetString("Unposted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Un-Process Sales Orders.
        /// </summary>
        internal static string UnProcessSalesOrders {
            get {
                return ResourceManager.GetString("UnProcessSalesOrders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} by {1}.
        /// </summary>
        internal static string UpdatedByDateAndUser {
            get {
                return ResourceManager.GetString("UpdatedByDateAndUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Upload PDF.
        /// </summary>
        internal static string UploadPDF {
            get {
                return ResourceManager.GetString("UploadPDF", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Upload Pictures.
        /// </summary>
        internal static string UploadPictures {
            get {
                return ResourceManager.GetString("UploadPictures", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Forgot user name.
        /// </summary>
        internal static string Username {
            get {
                return ResourceManager.GetString("Username", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to View.
        /// </summary>
        internal static string View {
            get {
                return ResourceManager.GetString("View", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Warning.
        /// </summary>
        internal static string Warning {
            get {
                return ResourceManager.GetString("Warning", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 1 week.
        /// </summary>
        internal static string Weeks_1 {
            get {
                return ResourceManager.GetString("Weeks_1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 2 weeks.
        /// </summary>
        internal static string Weeks_2 {
            get {
                return ResourceManager.GetString("Weeks_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Welcome.
        /// </summary>
        internal static string Welcome {
            get {
                return ResourceManager.GetString("Welcome", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} day(s) ago.
        /// </summary>
        internal static string XDaysAgo {
            get {
                return ResourceManager.GetString("XDaysAgo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yes.
        /// </summary>
        internal static string Yes {
            get {
                return ResourceManager.GetString("Yes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yesterday.
        /// </summary>
        internal static string Yesterday {
            get {
                return ResourceManager.GetString("Yesterday", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to YTD.
        /// </summary>
        internal static string YTD {
            get {
                return ResourceManager.GetString("YTD", resourceCulture);
            }
        }
    }
}
