-- Drop the procedure if it already exists
IF OBJECT_ID('dbo.usp_nugget_GetDetailsByNameOrCodeCompany', 'P') IS NOT NULL
BEGIN
    DROP PROCEDURE dbo.usp_nugget_GetDetailsByNameOrCodeCompany;
END
GO
/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-201556]		Hau Nguyen			31-May-2024		CREATE			Get Detail Group code contact 
===========================================================================================
*/
-- Create the new procedure
CREATE PROCEDURE [dbo].[usp_nugget_GetDetailsByNameOrCodeCompany] 
   @NameSearch nvarchar(50) = NULL,
   @CodeSearch nvarchar(50) = NULL,
   @CodeType nvarchar(100) = NULL        
      
    WITH RECOMPILE        
      
AS         
BEGIN  
    SET NOCOUNT ON;
     
    BEGIN  
        WITH cteSearch AS (
            SELECT ItemId as Id,
                   Code,
                   ContactName as [Name]
            FROM dbo.tbContactGroup
            WHERE ((@NameSearch IS NULL) OR (ContactName LIKE @NameSearch + '%'))
              AND ((@CodeSearch IS NULL) OR (Code LIKE @CodeSearch + '%'))
              AND ((@CodeType IS NULL) OR (ContactGroupType = @CodeType))
              AND Inactive = 0
              AND (ItemId IS NOT NULL)
        )          
        SELECT *              
        FROM cteSearch;  
    END  
END;
GO