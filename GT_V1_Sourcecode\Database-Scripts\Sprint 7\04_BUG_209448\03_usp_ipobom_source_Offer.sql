﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

IF OBJECT_ID('[usp_ipobom_source_Offer]', 'P') IS NOT NULL
	DROP PROC [dbo].[usp_ipobom_source_Offer]
GO

CREATE PROCEDURE [dbo].[usp_ipobom_source_Offer] --  101,'testpart100'
	----QuantityQuoted MPNQuoted ManufacturerName DateCode         
	--PackageType ProductType SPQ MOQ Leadtime(wks) Rohs (Y/N)         
	--TQSA LTB FactorySealed MSL UnitPrice(EUR) Notes      
	--********************************************************************************************                              
	--* RP 09.03.2011:                              
	--* - add recompile option                              
	--*                              
	--* RP 25.05.2010:                              
	--* - remove UNIONS, process Clients in code                              
	--*                              
	--* SK 17.02.2010:                              
	--* - adjust display of external client data                               
	--*                              
	--* SK 20.01.2010:                              
	--* - add ClientId to parameters and predicate: if equal display data as now, if not show                                
	--*   ClientName as customer  - with no hyperlink - and do not show any price                                
	--*                              
	--* RP 18.01.2010:                              
	--* - coalesce SupplierName and ManufacturerName                              
	--*                              
	--* RP 05.06.2009:                              
	--* - search with LIKE                              
	--*                              
	--* SK 01.06.2009:                              
	--* - add order by clause                              
	--*Marker     Changed by      Date         Remarks                                
	--*[001]      Vinay           16/10/2012   Display supplier type in stock grid                              
	--********************************************************************************************         
	/*
===========================================================================================
TASK      			UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[Bug-209448]		Ngai To				26-Jul-2024		UPDATE			Bug 209448: [PROD Bug] Supplier names do not appear on the sourcing screen
===========================================================================================
*/
	@ClientId INT,
	@PartSearch NVARCHAR(50),
	@Index INT = 1,
	@StartDate DATETIME = NULL,
	@FinishDate DATETIME = NULL,
	@IsPoHUB BIT = NULL
	WITH RECOMPILE
AS
BEGIN
	--DECLARE VARIABLE                      
	DECLARE @Month INT
	DECLARE @FROMDATE DATETIME
	DECLARE @ENDDATE DATETIME
	DECLARE @OutPutDate DATETIME
	DECLARE @FinishDateVW DATETIME
	DECLARE @FROMDATEVW DATETIME
	DECLARE @ENDDATEVW DATETIME

	SET @Month = 6

	/*                      
        When we get index 1 then we find the maximum date from matching record                      
        and decsrease no of month for the start date.                      
     */
	DECLARE @HUBName NVARCHAR(300)

	SELECT TOP 1 @HUBName = CompanyName
	FROM tbCompany
	WHERE ClientNo = @ClientId
		AND IsPOHub = 1

	IF @Index = 1
	BEGIN
		SELECT @FinishDateVW = MAX(ISNULL(OfferStatusChangeDate, OriginalEntryDate))
		FROM tbSourcingResult tsr
		WHERE tsr.SourcingTable IN ('PQ', 'EXPH', 'OFPH')
			AND tsr.FullPart LIKE @PartSearch

		SET @FROMDATEVW = dbo.ufn_get_date_from_datetime(DATEADD(month, - @Month, @FinishDateVW))
		SET @ENDDATEVW = dbo.ufn_get_date_from_datetime(@FinishDateVW)

		SELECT @FinishDate = MAX(ISNULL(OfferStatusChangeDate, OriginalEntryDate))
		FROM [BorisGlobalTraderImports].dbo.tbOffer o
		JOIN tbClient cl ON o.ClientNo = cl.ClientId
		WHERE (
				(o.ClientNo = @ClientId)
				OR (
					o.ClientNo <> @ClientId
					-- AND cl.OwnDataVisibleToOthers = 1))                     
					AND (
						CASE 
							WHEN o.ClientNo = 114
								THEN cast(1 AS BIT)
							ELSE cl.OwnDataVisibleToOthers
							END
						) = 1
					)
				)
			AND FullPart LIKE @PartSearch

		SET @FROMDATE = dbo.ufn_get_date_from_datetime(DATEADD(month, - @Month, @FinishDate))
		SET @ENDDATE = dbo.ufn_get_date_from_datetime(@FinishDate)
	END
	ELSE
	BEGIN
		SET @FROMDATE = dbo.ufn_get_date_from_datetime(@StartDate)
		SET @ENDDATE = dbo.ufn_get_date_from_datetime(@FinishDate)
		SET @FROMDATEVW = dbo.ufn_get_date_from_datetime(@StartDate)
		SET @ENDDATEVW = dbo.ufn_get_date_from_datetime(@FinishDate)
	END

	--SET THE OUTPUT DATE                      
	SET @OutPutDate = DATEADD(month, - @Month, @FinishDate)

	-- If Index value equal to 3 then more than one year data will be pick from archive database.
	IF @Index = 3
	BEGIN
			;

		WITH cteSearch
		AS (
			SELECT o.OfferId,
				o.FullPart COLLATE DATABASE_DEFAULT AS FullPart,
				o.Part,
				o.ManufacturerNo,
				o.DateCode,
				o.ProductNo,
				o.PackageNo,
				o.Quantity,
				CASE 
					WHEN o.ClientNo = 114
						THEN 0
					ELSE o.Price
					END AS Price --o.Price                          
				,
				o.OriginalEntryDate,
				o.Salesman,
				o.SupplierNo,
				o.CurrencyNo,
				o.ROHS,
				o.UpdatedBy,
				o.DLUP,
				o.OfferStatusNo,
				ISNULL(o.OfferStatusChangeDate, o.OriginalEntryDate) AS OfferStatusChangeDate,
				o.OfferStatusChangeLoginNo,
				m.ManufacturerCode COLLATE DATABASE_DEFAULT AS ManufacturerCode,
				p.ProductName COLLATE DATABASE_DEFAULT AS ProductName,
				c.CurrencyCode COLLATE DATABASE_DEFAULT AS CurrencyCode,
				c.CurrencyDescription COLLATE DATABASE_DEFAULT AS CurrencyDescription
				--, ISNULL(s.CompanyName, o.SupplierName) COLLATE DATABASE_DEFAULT AS SupplierName                             
				,
				--CASE 
				--	WHEN o.ClientNo = 114
				--		THEN @HUBName
				--	ELSE ISNULL(s.CompanyName, o.SupplierName) COLLATE DATABASE_DEFAULT
				--	END AS SupplierName,
				ISNULL(s.CompanyName, o.SupplierName) COLLATE DATABASE_DEFAULT AS SupplierName,
				ISNULL(m.ManufacturerName, o.ManufacturerName) COLLATE DATABASE_DEFAULT AS ManufacturerName,
				s.EMail COLLATE DATABASE_DEFAULT AS SupplierEmail,
				l.EmployeeName COLLATE DATABASE_DEFAULT AS SalesmanName,
				l2.EmployeeName COLLATE DATABASE_DEFAULT AS OfferStatusChangeEmployeeName,
				g.PackageName COLLATE DATABASE_DEFAULT AS PackageName,
				o.Notes COLLATE DATABASE_DEFAULT AS Notes,
				o.ClientNo,
				cl.ClientId,
				cl.ClientName COLLATE DATABASE_DEFAULT AS ClientName,
				cl.OwnDataVisibleToOthers AS ClientDataVisibleToOthers
				--[001] code start                              
				,
				isnull(cotype.Name, '') COLLATE DATABASE_DEFAULT AS SupplierType
				--[001] code end  
				,
				cl.ClientCode COLLATE DATABASE_DEFAULT AS ClientCode,
				o.SPQ COLLATE DATABASE_DEFAULT AS SPQ,
				o.LeadTime COLLATE DATABASE_DEFAULT AS LeadTime,
				o.ROHSStatus COLLATE DATABASE_DEFAULT AS ROHSStatus,
				o.FactorySealed COLLATE DATABASE_DEFAULT AS FactorySealed
				-- , o.MSL           
				,
				ml.MSLLevel COLLATE DATABASE_DEFAULT AS MSL,
				o.IPOBOMNo,
				o.SupplierTotalQSA COLLATE DATABASE_DEFAULT AS SupplierTotalQSA,
				o.SupplierLTB COLLATE DATABASE_DEFAULT AS SupplierLTB,
				o.SupplierMOQ COLLATE DATABASE_DEFAULT AS SupplierMOQ,
				ishub = 0
			FROM [BorisGlobalTraderArchive].dbo.tbOffer_arc o
			LEFT JOIN dbo.tbManufacturer m ON o.ManufacturerNo = m.ManufacturerId
			LEFT JOIN dbo.tbProduct p ON o.ProductNo = p.ProductId
			LEFT JOIN dbo.tbCurrency c ON o.CurrencyNo = c.CurrencyId
			LEFT JOIN dbo.tbCompany s ON o.SupplierNo = s.CompanyId
			LEFT JOIN dbo.tbLogin l ON o.Salesman = l.LoginId
			LEFT JOIN dbo.tbLogin l2 ON o.OfferStatusChangeLoginNo = l2.LoginId
			LEFT JOIN dbo.tbPackage g ON o.PackageNo = g.PackageId
			JOIN tbClient cl ON o.ClientNo = cl.ClientId
			--[001] code start                              
			LEFT JOIN dbo.tbCompanyType cotype ON s.TypeNo = cotype.CompanyTypeId
			LEFT JOIN tbMSLLevel ml ON o.MSLLevelNo = ml.MSLLevelId
			--[001] code end                               
			WHERE (
					(o.ClientNo = @ClientId)
					OR (
						o.ClientNo <> @ClientId
						-- AND cl.OwnDataVisibleToOthers = 1))         
						AND (
							CASE 
								WHEN o.ClientNo = 114
									THEN cast(1 AS BIT)
								ELSE cl.OwnDataVisibleToOthers
								END
							) = 1
						)
					)
				AND (
					(@IsPoHUB IS NULL)
					OR (
						NOT @IsPoHUB IS NULL
						AND isnull(o.IsPoHub, 0) = @IsPoHUB
						)
					)
				AND o.FullPart LIKE @PartSearch
				AND (
					dbo.ufn_get_date_from_datetime(ISNULL(o.OfferStatusChangeDate, o.OriginalEntryDate)) BETWEEN @FROMDATE
						AND @ENDDATE
					)
			-- ORDER BY ISNULL(o.OfferStatusChangeDate, o.OriginalEntryDate) DESC                              
			
			UNION ALL
			
			SELECT OfferId,
				FullPart COLLATE DATABASE_DEFAULT AS FullPart,
				Part COLLATE DATABASE_DEFAULT AS Part,
				ManufacturerNo,
				DateCode COLLATE DATABASE_DEFAULT AS DateCode,
				ProductNo,
				PackageNo,
				Quantity,
				CASE 
					WHEN ClientNo = 114
						THEN 0
					ELSE Price
					END AS Price --Price                                 
				,
				OriginalEntryDate,
				Salesman,
				SupplierNo,
				CurrencyNo,
				ROHS,
				UpdatedBy,
				DLUP,
				OfferStatusNo,
				OfferStatusChangeDate,
				OfferStatusChangeLoginNo,
				ManufacturerCode,
				ProductName,
				CurrencyCode,
				CurrencyDescription
				-- , SupplierName COLLATE DATABASE_DEFAULT  as SupplierName            
				,
				--CASE 
				--	WHEN ClientNo = 114
				--		THEN @HUBName
				--	ELSE SupplierName COLLATE DATABASE_DEFAULT
				--	END AS SupplierName,
				SupplierName COLLATE DATABASE_DEFAULT AS SupplierName,
				ManufacturerName COLLATE DATABASE_DEFAULT AS ManufacturerName,
				SupplierEmail COLLATE DATABASE_DEFAULT AS SupplierEmail,
				SalesmanName COLLATE DATABASE_DEFAULT AS SalesmanName,
				OfferStatusChangeEmployeeName COLLATE DATABASE_DEFAULT AS OfferStatusChangeEmployeeName,
				PackageName COLLATE DATABASE_DEFAULT AS PackageName,
				Notes COLLATE DATABASE_DEFAULT AS Notes,
				ClientNo,
				ClientId,
				ClientName COLLATE DATABASE_DEFAULT AS ClientName,
				ClientDataVisibleToOthers
				--[001] code start                          
				,
				SupplierType COLLATE DATABASE_DEFAULT AS SupplierType
				--[001] code end            
				,
				ClientCode COLLATE DATABASE_DEFAULT AS ClientCode,
				SPQ COLLATE DATABASE_DEFAULT AS SPQ,
				LeadTime COLLATE DATABASE_DEFAULT AS LeadTime,
				ROHSStatus COLLATE DATABASE_DEFAULT AS ROHSStatus,
				FactorySealed COLLATE DATABASE_DEFAULT AS FactorySealed,
				MSL COLLATE DATABASE_DEFAULT AS MSL,
				IPOBOMNo,
				SupplierTotalQSA,
				SupplierLTB COLLATE DATABASE_DEFAULT AS SupplierLTB,
				SupplierMOQ COLLATE DATABASE_DEFAULT AS SupplierMOQ,
				ishub
			FROM [vwSourcingDataHubRFQ]
			WHERE (
					(ClientNo = @ClientId)
					OR (
						ClientNo <> @ClientId
						-- AND ClientDataVisibleToOthers = 1))            
						AND (
							CASE 
								WHEN ClientNo = 114
									THEN cast(1 AS BIT)
								ELSE ClientDataVisibleToOthers
								END
							) = 1
						)
					)
				AND FullPart LIKE @PartSearch
				AND (
					dbo.ufn_get_date_from_datetime(ISNULL(OfferStatusChangeDate, OriginalEntryDate)) BETWEEN @FROMDATEVW
						AND @ENDDATEVW
					)
				-- ORDER BY ISNULL(OfferStatusChangeDate, OriginalEntryDate) DESC                     
				--SELECT THE OUT DATE         
			)
		SELECT *,
			dbo.ufn_GetSupplierMessage(SupplierNo) AS SupplierMessage
		FROM cteSearch
		ORDER BY ISNULL(OfferStatusChangeDate, OriginalEntryDate) DESC
			--SELECT THE OUT DATE    
	END
	ELSE
	BEGIN
			;

		WITH cteSearch
		AS (
			SELECT o.OfferId,
				o.FullPart COLLATE DATABASE_DEFAULT AS FullPart,
				o.Part,
				o.ManufacturerNo,
				o.DateCode,
				o.ProductNo,
				o.PackageNo,
				o.Quantity
				--, o.Price 
				,
				CASE 
					WHEN o.ClientNo = 114
						THEN 0
					ELSE o.Price
					END AS Price,
				o.OriginalEntryDate,
				o.Salesman,
				o.SupplierNo,
				o.CurrencyNo,
				o.ROHS,
				o.UpdatedBy,
				o.DLUP,
				o.OfferStatusNo,
				ISNULL(o.OfferStatusChangeDate, o.OriginalEntryDate) AS OfferStatusChangeDate,
				o.OfferStatusChangeLoginNo,
				m.ManufacturerCode COLLATE DATABASE_DEFAULT AS ManufacturerCode,
				p.ProductName COLLATE DATABASE_DEFAULT AS ProductName,
				c.CurrencyCode COLLATE DATABASE_DEFAULT AS CurrencyCode,
				c.CurrencyDescription COLLATE DATABASE_DEFAULT AS CurrencyDescription
				-- , ISNULL(s.CompanyName, o.SupplierName) COLLATE DATABASE_DEFAULT AS SupplierName      
				,
				--CASE 
				--	WHEN o.ClientNo = 114
				--		THEN @HUBName
				--	ELSE ISNULL(s.CompanyName, o.SupplierName) COLLATE DATABASE_DEFAULT
				--	END AS SupplierName,
				ISNULL(s.CompanyName, o.SupplierName) COLLATE DATABASE_DEFAULT AS SupplierName,
				ISNULL(m.ManufacturerName, o.ManufacturerName) COLLATE DATABASE_DEFAULT AS ManufacturerName,
				s.EMail COLLATE DATABASE_DEFAULT AS SupplierEmail,
				l.EmployeeName COLLATE DATABASE_DEFAULT AS SalesmanName,
				l2.EmployeeName COLLATE DATABASE_DEFAULT AS OfferStatusChangeEmployeeName,
				g.PackageName COLLATE DATABASE_DEFAULT AS PackageName,
				o.Notes COLLATE DATABASE_DEFAULT AS Notes,
				o.ClientNo,
				cl.ClientId,
				cl.ClientName COLLATE DATABASE_DEFAULT AS ClientName,
				cl.OwnDataVisibleToOthers AS ClientDataVisibleToOthers
				--[001] code start                              
				,
				isnull(cotype.Name, '') COLLATE DATABASE_DEFAULT AS SupplierType
				--[001] code end            
				,
				cl.ClientCode COLLATE DATABASE_DEFAULT AS ClientCode,
				o.SPQ COLLATE DATABASE_DEFAULT AS SPQ,
				o.LeadTime COLLATE DATABASE_DEFAULT AS LeadTime,
				o.ROHSStatus COLLATE DATABASE_DEFAULT AS ROHSStatus,
				o.FactorySealed COLLATE DATABASE_DEFAULT AS FactorySealed
				-- , o.MSL           
				,
				ml.MSLLevel COLLATE DATABASE_DEFAULT AS MSL,
				o.IPOBOMNo,
				o.SupplierTotalQSA COLLATE DATABASE_DEFAULT AS SupplierTotalQSA,
				o.SupplierLTB COLLATE DATABASE_DEFAULT AS SupplierLTB,
				o.SupplierMOQ COLLATE DATABASE_DEFAULT AS SupplierMOQ,
				ishub = 0
			FROM [BorisGlobalTraderImports].dbo.tbOffer o
			LEFT JOIN dbo.tbManufacturer m ON o.ManufacturerNo = m.ManufacturerId
			LEFT JOIN dbo.tbProduct p ON o.ProductNo = p.ProductId
			LEFT JOIN dbo.tbCurrency c ON o.CurrencyNo = c.CurrencyId
			LEFT JOIN dbo.tbCompany s ON o.SupplierNo = s.CompanyId
			LEFT JOIN dbo.tbLogin l ON o.Salesman = l.LoginId
			LEFT JOIN dbo.tbLogin l2 ON o.OfferStatusChangeLoginNo = l2.LoginId
			LEFT JOIN dbo.tbPackage g ON o.PackageNo = g.PackageId
			JOIN tbClient cl ON o.ClientNo = cl.ClientId
			--[001] code start                              
			LEFT JOIN dbo.tbCompanyType cotype ON s.TypeNo = cotype.CompanyTypeId
			LEFT JOIN tbMSLLevel ml ON o.MSLLevelNo = ml.MSLLevelId
			--[001] code end                               
			WHERE (
					(o.ClientNo = @ClientId)
					OR (
						o.ClientNo <> @ClientId
						--AND cl.OwnDataVisibleToOthers = 1))         
						AND (
							CASE 
								WHEN o.ClientNo = 114
									THEN cast(1 AS BIT)
								ELSE cl.OwnDataVisibleToOthers
								END
							) = 1
						)
					)
				AND (
					(@IsPoHUB IS NULL)
					OR (
						NOT @IsPoHUB IS NULL
						AND isnull(o.IsPoHub, 0) = @IsPoHUB
						)
					)
				AND o.FullPart LIKE @PartSearch
				AND (
					dbo.ufn_get_date_from_datetime(ISNULL(o.OfferStatusChangeDate, o.OriginalEntryDate)) BETWEEN @FROMDATE
						AND @ENDDATE
					)
			-- ORDER BY ISNULL(o.OfferStatusChangeDate, o.OriginalEntryDate) DESC                              
			
			UNION ALL
			
			SELECT OfferId,
				FullPart COLLATE DATABASE_DEFAULT AS FullPart,
				Part COLLATE DATABASE_DEFAULT AS Part,
				ManufacturerNo,
				DateCode COLLATE DATABASE_DEFAULT AS DateCode,
				ProductNo,
				PackageNo,
				Quantity
				--, Price                              
				,
				CASE 
					WHEN ClientNo = 114
						THEN 0
					ELSE Price
					END AS Price,
				OriginalEntryDate,
				Salesman,
				SupplierNo,
				CurrencyNo,
				ROHS,
				UpdatedBy,
				DLUP,
				OfferStatusNo,
				OfferStatusChangeDate,
				OfferStatusChangeLoginNo,
				ManufacturerCode,
				ProductName,
				CurrencyCode,
				CurrencyDescription
				-- , SupplierName COLLATE DATABASE_DEFAULT  as SupplierName                           
				,
				--CASE 
				--	WHEN ClientNo = 114
				--		THEN @HUBName
				--	ELSE SupplierName COLLATE DATABASE_DEFAULT
				--	END AS SupplierName,
				SupplierName COLLATE DATABASE_DEFAULT AS SupplierName,
				ManufacturerName COLLATE DATABASE_DEFAULT AS ManufacturerName,
				SupplierEmail COLLATE DATABASE_DEFAULT AS SupplierEmail,
				SalesmanName COLLATE DATABASE_DEFAULT AS SalesmanName,
				OfferStatusChangeEmployeeName COLLATE DATABASE_DEFAULT AS OfferStatusChangeEmployeeName,
				PackageName COLLATE DATABASE_DEFAULT AS PackageName,
				Notes COLLATE DATABASE_DEFAULT AS Notes,
				ClientNo,
				ClientId,
				ClientName COLLATE DATABASE_DEFAULT AS ClientName,
				ClientDataVisibleToOthers
				--[001] code start                          
				,
				SupplierType COLLATE DATABASE_DEFAULT AS SupplierType
				--[001] code end            
				,
				ClientCode COLLATE DATABASE_DEFAULT AS ClientCode,
				SPQ COLLATE DATABASE_DEFAULT AS SPQ,
				LeadTime COLLATE DATABASE_DEFAULT AS LeadTime,
				ROHSStatus COLLATE DATABASE_DEFAULT AS ROHSStatus,
				FactorySealed COLLATE DATABASE_DEFAULT AS FactorySealed,
				MSL COLLATE DATABASE_DEFAULT AS MSL,
				IPOBOMNo,
				SupplierTotalQSA,
				SupplierLTB COLLATE DATABASE_DEFAULT AS SupplierLTB,
				SupplierMOQ COLLATE DATABASE_DEFAULT AS SupplierMOQ,
				ishub
			FROM [vwSourcingDataHubRFQ]
			WHERE (
					(ClientNo = @ClientId)
					OR (
						ClientNo <> @ClientId
						-- AND ClientDataVisibleToOthers = 1))            
						AND (
							CASE 
								WHEN ClientNo = 114
									THEN cast(1 AS BIT)
								ELSE ClientDataVisibleToOthers
								END
							) = 1
						)
					)
				AND FullPart LIKE @PartSearch
				AND (
					dbo.ufn_get_date_from_datetime(ISNULL(OfferStatusChangeDate, OriginalEntryDate)) BETWEEN @FROMDATEVW
						AND @ENDDATEVW
					)
				-- ORDER BY ISNULL(OfferStatusChangeDate, OriginalEntryDate) DESC                     
				--SELECT THE OUT DATE         
			)
		SELECT *,
			dbo.ufn_GetSupplierMessage(SupplierNo) AS SupplierMessage
		FROM cteSearch
		ORDER BY ISNULL(OfferStatusChangeDate, OriginalEntryDate) DESC
			--SELECT THE OUT DATE                             
			--SELECT THE OUT DATE    
	END

	SELECT @OutPutDate AS OutPutDate
END
