//--------------------------------------------------------------------------------------------------------
// RP 11.12.2009:
// - clear related dropdown cache
//--------------------------------------------------------------------------------------------------------
//Marker     Changed by      Date         Remarks
//[001]      Suhail       25/04/2018   Added contact and company name while sending mail via Add New Communication Note
using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using System.IO;
using System.Text;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets.Data
{
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class BOMPVV : Rebound.GlobalTrader.Site.Data.Base
    {

        public override void ProcessRequest(HttpContext context)
        {
            if (base.init(context))
            {
                switch (Action)
                {
                    case "GetListData": GetListData(); break;
                    case "GetListDataTemp": GetListDataTemp(); break;

                    case "GetData": GetData(); break;
                    case "Delete": Delete(); break;
                    case "SaveEdit": SaveEdit(); break;
                    case "GetCheckData": GetCheckData(); break;
                    case "GetDataTemp": GetDataTemp(context); break;
                    case "SaveEditTemp": SaveEditTemp(); break;
                    case "DeleteTemp": DeleteTemp(); break;

                    default: WriteErrorActionNotFound(); break;
                }
            }
        }
        private void GetListDataTemp()
        {
            string BomIdGenerated = GetFormValue_String("idGenerated");

            List<SourcingResult> lst = SourcingResult.GetListPVVQuestionTemp(BomIdGenerated, Convert.ToBoolean(SessionManager.IsPOHub));
            if (lst == null)
            {
                WriteErrorDataNotFound();
            }
            else
            {
                try
                {
                    JsonObject jsn = new JsonObject();
                    JsonObject jsnItems = new JsonObject(true);
                    foreach (SourcingResult ln in lst)
                    {
                        JsonObject jsnItem = new JsonObject();
                        jsnItem.AddVariable("PVVQuestionId", ln.PVVQuestionId);
                        jsnItem.AddVariable("PVVQuestionName", Functions.ReplaceLineBreaks(ln.PVVQuestionName));
                        jsnItem.AddVariable("PVVAnswerId", ln.PVVAnswerId);
                        jsnItem.AddVariable("PVVAnswerName", Functions.ReplaceLineBreaks(ln.PVVAnswerName));
                        jsnItem.AddVariable("HUBRFQNo", Functions.ReplaceLineBreaks(ln.HUBRFQNo));
                        jsnItem.AddVariable("BomId", ln.BomId);
                        jsnItems.AddVariable(jsnItem);
                        jsnItem.Dispose(); jsnItem = null;
                    }
                    jsn.AddVariable("Results", jsnItems);
                    OutputResult(jsn);
                    jsn.Dispose(); jsn = null;
                }
                catch (Exception e)
                {
                    WriteError(e);
                }
            }
        }
        /// <summary>
        /// Gets data for a stock item
        /// </summary>
        /// 


        private void GetListData()
        {
            int? BomId = ID;
            List<SourcingResult> lst = SourcingResult.GetListPVVQuestion(BomId, Convert.ToBoolean(SessionManager.IsPOHub));
            if (lst == null)
            {
                WriteErrorDataNotFound();
            }
            else
            {
                try
                {
                    JsonObject jsn = new JsonObject();
                    JsonObject jsnItems = new JsonObject(true);
                    foreach (SourcingResult ln in lst)
                    {
                        JsonObject jsnItem = new JsonObject();
                        jsnItem.AddVariable("PVVQuestionId", ln.PVVQuestionId);
                        jsnItem.AddVariable("PVVQuestionName", Functions.ReplaceLineBreaks(ln.PVVQuestionName));
                        jsnItem.AddVariable("PVVAnswerId", ln.PVVAnswerId);
                        jsnItem.AddVariable("PVVAnswerName", Functions.ReplaceLineBreaks(ln.PVVAnswerName));
                        jsnItem.AddVariable("HUBRFQNo", Functions.ReplaceLineBreaks(ln.HUBRFQNo));
                        jsnItem.AddVariable("BomId", ln.BomId);
                        jsnItems.AddVariable(jsnItem);
                        jsnItem.Dispose(); jsnItem = null;
                    }
                    jsn.AddVariable("Results", jsnItems);
                    OutputResult(jsn);
                    jsn.Dispose(); jsn = null;
                }
                catch (Exception e)
                {
                    WriteError(e);
                }
            }
        }

        private void GetCheckData()
        {
            int? BomId = ID;
            List<SourcingResult> lst = SourcingResult.GetListCOunt(BomId, Convert.ToBoolean(SessionManager.IsPOHub));
            if (lst == null)
            {
                WriteErrorDataNotFound();
            }
            else
            {
                try
                {
                    JsonObject jsn = new JsonObject();
                    JsonObject jsnItems = new JsonObject(true);
                    foreach (SourcingResult ln in lst)
                    {
                        JsonObject jsnItem = new JsonObject();
                        jsnItem.AddVariable("PVVQuestionId", ln.PVVQuestionId);
                        jsnItem.AddVariable("PVVQuestionName", Functions.ReplaceLineBreaks(ln.PVVQuestionName));
                        jsnItem.AddVariable("PVVAnswerId", ln.PVVAnswerId);
                        jsnItem.AddVariable("PVVAnswerName", Functions.ReplaceLineBreaks(ln.PVVAnswerName));
                        jsnItem.AddVariable("HUBRFQNo", Functions.ReplaceLineBreaks(ln.HUBRFQNo));
                        jsnItem.AddVariable("BomId", ln.BomId);
                        jsnItems.AddVariable(jsnItem);
                        jsnItem.Dispose(); jsnItem = null;
                    }
                    jsn.AddVariable("Results", jsnItems);
                    OutputResult(jsn);
                    jsn.Dispose(); jsn = null;
                }
                catch (Exception e)
                {
                    WriteError(e);
                }
            }
        }
        public void GetData()
        {

            int? BomId = ID;
            List<SourcingResult> lst = SourcingResult.GetDataPVV(ID);
            if (lst == null)
            {
                WriteErrorDataNotFound();
            }
            else
            {
                try
                {
                    JsonObject jsn = new JsonObject();
                    JsonObject jsnItems = new JsonObject(true);
                    foreach (SourcingResult ln in lst)
                    {
                        JsonObject jsnItem = new JsonObject();
                        jsnItem.AddVariable("PVVQuestionId", ln.PVVQuestionId);
                        jsnItem.AddVariable("PVVQuestionName", Functions.ReplaceLineBreaks(ln.PVVQuestionName));
                        jsnItem.AddVariable("PVVAnswerId", ln.PVVAnswerId);
                        jsnItem.AddVariable("PVVAnswerName", Functions.ReplaceLineBreaks(ln.PVVAnswerName));
                        jsnItem.AddVariable("HUBRFQNo", Functions.ReplaceLineBreaks(ln.HUBRFQNo));
                        jsnItem.AddVariable("BomId", ln.BomId);
                        jsnItems.AddVariable(jsnItem);
                        jsnItem.Dispose(); jsnItem = null;
                    }
                    jsn.AddVariable("Items", jsnItems);
                    OutputResult(jsn);
                    jsn.Dispose(); jsn = null;
                }
                catch (Exception e)
                {
                    WriteError(e);
                }
            }


        }
        public void GetDataTemp(HttpContext context)
        {

            string BomIdGenerated = GetFormValue_String("idGenerated");
            List<SourcingResult> lst = SourcingResult.GetDataPVVTemp(BomIdGenerated);
            if (lst == null)
            {
                WriteErrorDataNotFound();
            }
            else
            {
                try
                {
                    JsonObject jsn = new JsonObject();
                    JsonObject jsnItems = new JsonObject(true);
                    foreach (SourcingResult ln in lst)
                    {
                        JsonObject jsnItem = new JsonObject();
                        jsnItem.AddVariable("PVVQuestionId", ln.PVVQuestionId);
                        jsnItem.AddVariable("PVVQuestionName", Functions.ReplaceLineBreaks(ln.PVVQuestionName));
                        jsnItem.AddVariable("PVVAnswerId", ln.PVVAnswerId);
                        jsnItem.AddVariable("PVVAnswerName", Functions.ReplaceLineBreaks(ln.PVVAnswerName));
                        jsnItem.AddVariable("HUBRFQNo", Functions.ReplaceLineBreaks(ln.HUBRFQNo));
                        jsnItem.AddVariable("BomId", ln.BomId);
                        jsnItems.AddVariable(jsnItem);
                        jsnItem.Dispose(); jsnItem = null;
                    }
                    jsn.AddVariable("Items", jsnItems);
                    OutputResult(jsn);
                    jsn.Dispose(); jsn = null;
                }
                catch (Exception e)
                {
                    WriteError(e);
                }
            }


        }

        private void Delete()
        {
            try
            {
                JsonObject jsn = new JsonObject();
                bool blnOK = BOM.PVVDelete(ID, LoginID);
                jsn.AddVariable("Result", blnOK);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch (Exception ex)
            {
                WriteError(ex);
            }
        }
        private void DeleteTemp()
        {
            try
            {
                string BomIdGenerated = GetFormValue_String("idGenerated");

                JsonObject jsn = new JsonObject();
                bool blnOK = BOM.PVVDeleteTemp(BomIdGenerated, LoginID);
                jsn.AddVariable("Result", blnOK);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch (Exception ex)
            {
                WriteError(ex);
            }
        }
        private void SaveEdit()
        {
            try
            {
                //CacheManager.ClearStoredDropDown("BOM", new object[] { SessionManager.ClientID });
                JsonObject jsn = new JsonObject();
                bool blnOK = true;
                int? bomid = GetFormValue_Int("BOMNo");
                BOM bom = BOM.Get(bomid);
                if (bom != null)
                {
                    // bom.BOMCode = GetFormValue_String("Code");
                    bom.BOMName = GetFormValue_String("Name") + "-" + SessionManager.ClientID.ToString();
                    bom.Notes = GetFormValue_String("PVVAnswers");
                    bom.UpdatedBy = LoginID;
                    bom.Inactive = GetFormValue_Boolean("Inactive");
                    bom.CompanyName = GetFormValue_String("Company");
                    bom.ContactNo = GetFormValue_Int("Contact");
                    bom.ClientNo = (int)SessionManager.ClientID;
                    bom.CurrencyNo = GetFormValue_Int("Currency");
                    bom.CurrentSupplier = GetFormValue_String("CurrentSupplier");
                    bom.QuoteRequired = GetFormValue_NullableDateTime("QuoteRequired");
                    bom.AS9120 = GetFormValue_Boolean("AS9120");
                    bom.Contact2Id = GetFormValue_NullableInt("Contact2No");
                    blnOK = bom.UpdatePVV();
                }
                jsn.AddVariable("Result", blnOK);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch (Exception ex)
            {
                WriteError(ex);
            }
        }
        private void SaveEditTemp()
        {
            try
            {
                JsonObject jsn = new JsonObject();
                bool blnOK = true;
                string bomidGenerated = GetFormValue_String("idGenerated");

                BOM bom = new BOM();

                bom.Notes = GetFormValue_String("PVVAnswers");
                bom.UpdatedBy = LoginID;
                bom.ClientNo = (int)SessionManager.ClientID;

                blnOK = bom.UpdatePVVTemp(bomidGenerated);
                jsn.AddVariable("Result", blnOK);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch (Exception ex)
            {
                WriteError(ex);
            }
        }
    }
}
