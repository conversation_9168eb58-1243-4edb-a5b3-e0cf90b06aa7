using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Collections;

namespace Rebound.GlobalTrader.Site.Controls.Forms {
	[DefaultProperty("")]
	[ToolboxData("<{0}:DesignBase runat=server></{0}:DesignBase>")]
	public class DesignBase : WebControl, INamingContainer {

		#region Locals

		public HtmlControl h4;
		public HyperLink hypQuickHelp;
		public Label lblQuickHelpText;
		public Panel pnlLoading;
		public Panel pnlSaving;
		public Panel pnlSavedOK;
		public Panel pnlHeader;
		public Panel pnlValidateError;
		public Panel pnlExplain;
		public Panel pnlContent;
		public Panel pnlContentInner;
		public Panel pnlNotes;
		public MultiStep ctlMultiStep;
		public Panel pnlValidateErrorText;

		#endregion

		#region Properties

		/// <summary>
		/// Content container
		/// </summary>
		private ITemplate _tmpContent = null;
		[PersistenceMode(PersistenceMode.InnerProperty)]
		[TemplateContainer(typeof(FormContainer))]
		[TemplateInstance(TemplateInstance.Single)]
		public ITemplate Content {
			get { return _tmpContent; }
			set { _tmpContent = value; }
		}

		/// <summary>
		/// Explanation text on how to use the field
		/// </summary>
		private ITemplate _tmpExplanation = null;
		[PersistenceMode(PersistenceMode.InnerProperty)]
		[TemplateContainer(typeof(FormContainer))]
		[TemplateInstance(TemplateInstance.Single)]
		public ITemplate Explanation {
			get { return _tmpExplanation; }
			set { _tmpExplanation = value; }
		}

		/// <summary>
		/// Links template
		/// </summary>
		private ITemplate _tmpLinks = null;
		[PersistenceMode(PersistenceMode.InnerProperty)]
		[TemplateContainer(typeof(FormContainer))]
		[TemplateInstance(TemplateInstance.Single)]
		public ITemplate Links {
			get { return _tmpLinks; }
			set { _tmpLinks = value; }
		}


		/// <summary>
		/// Should quickhelp be shown?
		/// </summary>
		private bool _blnShowQuickHelp = false;
		public bool ShowQuickHelp {
			get { return _blnShowQuickHelp; }
			set { _blnShowQuickHelp = value; }
		}

		/// <summary>
		/// Is quick-help allowed?
		/// </summary>
		private bool _blnAllowQuickHelp = false;
		public bool AllowQuickHelp {
			get { return _blnAllowQuickHelp; }
			set { _blnAllowQuickHelp = value; }
		}

		/// <summary>
		/// Should the required fields explanation text be shown?
		/// </summary>
		private bool _blnShowRequiredFieldsExplanation = true;
		public bool ShowRequiredFieldsExplanation {
			get { return _blnShowRequiredFieldsExplanation; }
			set { _blnShowRequiredFieldsExplanation = value; }
		}

		/// <summary>
		/// Title text
		/// </summary>
		private string _strTitleText;
		public string TitleText {
			get { return _strTitleText; }
			set { _strTitleText = value; }
		}

		/// <summary>
		/// Reference to the Forms.Base comntr
		/// </summary>
		private Forms.Base _ctlFormsBaseControl;
		public Forms.Base FormsBaseControl {
			get { return _ctlFormsBaseControl; }
			set { _ctlFormsBaseControl = value; }
		}

		/// <summary>
		/// Number of steps in the form
		/// </summary>
		private int _intNumberOfSteps;
		public int NumberOfSteps {
			get { return _intNumberOfSteps; }
			set { _intNumberOfSteps = value; }
		}

		/// <summary>
		/// Pattern for multi-step form table objects
		/// </summary>
		private string _strMultiStepFormNamePattern = "frmStep{0}";
		public string MultiStepFormNamePattern {
			get { return _strMultiStepFormNamePattern; }
			set { _strMultiStepFormNamePattern = value; }
		}

		private bool _blnHideHeader = false;
		public bool HideHeader {
			get { return _blnHideHeader; }
			set { _blnHideHeader = value; }
		}

		public string SavingMessageResource { get; set; }
		public string SavedOKMessageResource { get; set; }
		public string LoadingMessageResource { get; set; }

		#endregion

		#region Overrides

		protected override void OnPreRender(EventArgs e) {
			ControlBuilders.CreateLiteralInsideParent(h4, _strTitleText);
			base.OnPreRender(e);
		}

		/// <summary>
		/// create controls
		/// </summary>
		protected override void CreateChildControls() {

			CssClass = "form invisible";

			//form header
			pnlHeader = ControlBuilders.CreatePanel("formHeader");

			//title
			h4 = new HtmlGenericControl("h4");
			h4.ID = "h4";
			pnlHeader.Controls.Add(h4);

			//quickhelp
			if (!_blnAllowQuickHelp) _blnShowQuickHelp = false;
			Panel pnlQuickHelpButtons = ControlBuilders.CreatePanelInsideParent(pnlHeader, "formQuickHelpButtons");
			pnlQuickHelpButtons.ID = "pnlQuickHelpButtons";
			hypQuickHelp = ControlBuilders.CreateHyperLinkInsideParent(pnlQuickHelpButtons, "", "javascript:void(0);");
			hypQuickHelp.ID = "hypQuickHelp";
			hypQuickHelp.CssClass = (_blnShowQuickHelp) ? "quickHelpButtonOn" : "quickHelpButtonOff";
			string strQuickHelpText = (_blnShowQuickHelp) ? "QuickHelp On" : "QuickHelp Off";
			lblQuickHelpText = ControlBuilders.CreateLabelInsideParent(hypQuickHelp, "", strQuickHelpText);
			lblQuickHelpText.ID = "lblQuickHelpText";
			ControlBuilders.CreateImageInsideParent(hypQuickHelp, "", "~/images/x.gif", 14, 16);
			Functions.SetCSSVisibility(pnlQuickHelpButtons, _blnAllowQuickHelp);

			//form explanation
			pnlExplain = ControlBuilders.CreatePanelInsideParent(pnlHeader, "formInstructions");
			pnlExplain.ID = "pnlExplain";
			if (Explanation != null) {
				Functions.AddControlsFromTemplate(pnlExplain, _tmpExplanation);
				for (int i = 0; i < pnlExplain.Controls[0].Controls.Count; i++) {
					Control ctl = pnlExplain.Controls[0].Controls[i];
					if (ctl is MultiStep) {
						((MultiStep)ctl).RelatedForm = this;
						ctlMultiStep = (MultiStep)ctl;
						ctlMultiStep.MultiStepFormNamePattern = _strMultiStepFormNamePattern;
					}
				}
			}

			//validation error message
			pnlValidateError = ControlBuilders.CreatePanelInsideParent(pnlHeader, "errorSummary");
			pnlValidateError.ID = "pnlValidateError";
			ControlBuilders.CreateLiteralInsideParent(pnlValidateError, Functions.GetGlobalResource("FormErrors", "FormProblems"));
			pnlValidateErrorText = ControlBuilders.CreatePanelInsideParent(pnlValidateError);
			Functions.SetCSSVisibility(pnlValidateError, false);

			//form content
			pnlContent = ControlBuilders.CreatePanel("formContent");
			pnlContent.ID = "pnlContent";

			//loading message
			pnlLoading = ControlBuilders.CreatePanelInsideParent(pnlContent, "loading");
			pnlLoading.ID = "pnlLoading";
			ControlBuilders.CreateLiteralInsideParent(pnlLoading, Functions.GetGlobalResource("Misc", "Loading"));
			Functions.SetCSSVisibility(pnlLoading, false);

			//saving message
			pnlSaving = ControlBuilders.CreatePanelInsideParent(pnlContent, "saving");
			pnlSaving.ID = "pnlSaving";
			ControlBuilders.CreateLiteralInsideParent(pnlSaving, Functions.GetGlobalResource("Misc", "Saving"));
			Functions.SetCSSVisibility(pnlSaving, false);

			//saving message
			pnlSavedOK = ControlBuilders.CreatePanelInsideParent(pnlContent, "formSavedOK");
			pnlSavedOK.ID = "pnlSavedOK";
			ControlBuilders.CreateLiteralInsideParent(pnlSavedOK, Functions.GetGlobalResource("Messages", "SavedOK"));
			Functions.SetCSSVisibility(pnlSavedOK, false);

			//form itself (table)
			pnlContentInner = ControlBuilders.CreatePanelInsideParent(pnlContent);
			pnlContentInner.ID = "pnlContentInner";
			if (_tmpContent != null) {
				//process form fields
				FormContainer cnt = new FormContainer();
				_tmpContent.InstantiateIn(cnt);
				for (int i = 0; i < cnt.Controls.Count; i++) {
					if (cnt.Controls[i] is Tables.Form) {
						Tables.Form ctlTable = (Tables.Form)cnt.Controls[i];
						pnlContentInner.Controls.Add(ctlTable);
						for (int j = 0; j < cnt.Controls[i].Controls.Count; i++) {
							Control ctl = ctlTable.Controls[j];
							if (ctl is FormField) {
								((FormField)ctl).ShowQuickHelp = _blnShowQuickHelp;
								ctlTable.Controls.Add(ctl);
							}
							if (ctl is LabelFormField) pnlContentInner.Controls.Add(ctl);
						}
					}
				}
				cnt.Dispose(); cnt = null;
			}

			//add explanation of the required fields indicator if needed
			pnlNotes = ControlBuilders.CreatePanel("formNotes");
			Label lbl = ControlBuilders.CreateLabelInsideParent(pnlNotes, "requiredField");
			ControlBuilders.CreateLiteralInsideParent(lbl, "*");
			ControlBuilders.CreateLiteralInsideParent(pnlNotes, String.Format(" {0}", Functions.GetGlobalResource("misc", "RequiredField")));
			Functions.SetCSSVisibility(pnlNotes, ShowRequiredFieldsExplanation);

			Controls.Add(pnlHeader);
			Controls.Add(pnlContent);
			Controls.Add(pnlNotes);

			base.CreateChildControls();
		}

		#endregion

		public object FindContentControl(string strControlName) {
			EnsureChildControls();
			return (Functions.FindControlRecursive(pnlContent, strControlName));
		}

		public object FindExplanationControl(string strControlName) {
			EnsureChildControls();
			return (Functions.FindControlRecursive(pnlExplain, strControlName));
		}

		public void MakeChildControls() {
			EnsureChildControls();
		}

	}
}