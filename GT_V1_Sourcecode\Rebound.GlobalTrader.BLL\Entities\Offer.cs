﻿//Marker     Changed by      Date         Remarks    
//[001]      Vinay           16/10/2012   Display supplier type in stock grid  
//[002]      <PERSON><PERSON><PERSON><PERSON>  19/07/2021   Add new function for Partwatcg Match
//[003]      Soorya Vyas     11/04/2023   RP-223 - Reverse Logistics Dubai Setup
//[004]      <PERSON>    14-04-2023   [RP-1421] add revers logistic similar to strategic offer on HUBRFQ page
using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using Rebound.GlobalTrader.DAL;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.BLL {
		public partial class Offer : BizObject {
		
		#region Properties

		protected static DAL.OfferElement Settings {
			get { return Globals.Settings.Offers; }
		}

		/// <summary>
		/// OfferId
		/// </summary>
		public System.Int32 OfferId { get; set; }		
		/// <summary>
		/// FullPart
		/// </summary>
		public System.String FullPart { get; set; }		
		/// <summary>
		/// Part
		/// </summary>
		public System.String Part { get; set; }		
		/// <summary>
		/// ManufacturerNo
		/// </summary>
		public System.Int32? ManufacturerNo { get; set; }		
		/// <summary>
		/// DateCode
		/// </summary>
		public System.String DateCode { get; set; }		
		/// <summary>
		/// ProductNo
		/// </summary>
		public System.Int32? ProductNo { get; set; }		
		/// <summary>
		/// PackageNo
		/// </summary>
		public System.Int32? PackageNo { get; set; }		
		/// <summary>
		/// Quantity
		/// </summary>
		public System.Int32 Quantity { get; set; }
        /// <summary>
        /// TargetPrice
        /// </summary>
        public System.Int32 TargetPrice { get; set; }
        /// <summary>
        /// Price
        /// </summary>
        public System.Double Price { get; set; }		
		/// <summary>
		/// OriginalEntryDate
		/// </summary>
		public System.DateTime? OriginalEntryDate { get; set; }		
		/// <summary>
		/// Salesman
		/// </summary>
		public System.Int32? Salesman { get; set; }		
		/// <summary>
		/// SupplierNo
		/// </summary>
		public System.Int32 SupplierNo { get; set; }		
		/// <summary>
		/// CurrencyNo
		/// </summary>
		public System.Int32? CurrencyNo { get; set; }		
		/// <summary>
		/// ROHS
		/// </summary>
		public System.Byte? ROHS { get; set; }		
		/// <summary>
		/// UpdatedBy
		/// </summary>
		public System.Int32? UpdatedBy { get; set; }		
		/// <summary>
		/// DLUP
		/// </summary>
		public System.DateTime DLUP { get; set; }		
		/// <summary>
		/// OfferStatusNo
		/// </summary>
		public System.Int32? OfferStatusNo { get; set; }		
		/// <summary>
		/// OfferStatusChangeDate
		/// </summary>
		public System.DateTime? OfferStatusChangeDate { get; set; }		
		/// <summary>
		/// OfferStatusChangeLoginNo
		/// </summary>
		public System.Int32? OfferStatusChangeLoginNo { get; set; }		
		/// <summary>
		/// SupplierName
		/// </summary>
		public System.String SupplierName { get; set; }		
		/// <summary>
		/// Notes
		/// </summary>
		public System.String Notes { get; set; }		
		/// <summary>
		/// ManufacturerName
		/// </summary>
		public System.String ManufacturerName { get; set; }		
		/// <summary>
		/// ProductName
		/// </summary>
		public System.String ProductName { get; set; }		
		/// <summary>
		/// PackageName
		/// </summary>
		public System.String PackageName { get; set; }		
		/// <summary>
		/// ClientNo
		/// </summary>
		public System.Int32? ClientNo { get; set; }		
		/// <summary>
		/// ManufacturerCode
		/// </summary>
		public System.String ManufacturerCode { get; set; }		
		/// <summary>
		/// CurrencyCode
		/// </summary>
		public System.String CurrencyCode { get; set; }		
		/// <summary>
		/// CurrencyDescription
		/// </summary>
		public System.String CurrencyDescription { get; set; }		
		/// <summary>
		/// SupplierEmail
		/// </summary>
		public System.String SupplierEmail { get; set; }		
		/// <summary>
		/// SalesmanName
		/// </summary>
		public System.String SalesmanName { get; set; }		
		/// <summary>
		/// OfferStatusChangeEmployeeName
		/// </summary>
		public System.String OfferStatusChangeEmployeeName { get; set; }		
		/// <summary>
		/// ClientId
		/// </summary>
		public System.Int32 ClientId { get; set; }		
		/// <summary>
		/// ClientName
		/// </summary>
		public System.String ClientName { get; set; }		
		/// <summary>
		/// ClientDataVisibleToOthers
		/// </summary>
		public System.Boolean? ClientDataVisibleToOthers { get; set; }
        //[001] code start
        /// <summary>
        /// SupplierType
        /// </summary>
        public System.String SupplierType { get; set; }
        //[001] code end
            /// <summary>
        /// SupplierEpo  hard code data
        /// </summary>
        public System.String SupplierEpo { get; set; }
        public System.String SupplierReverseLogistic { get; set; } //[003]
            
        public System.String ClientCode { get; set; }

        public System.String MSL { get; set; }
        public System.String SPQ { get; set; }
        public System.String LeadTime { get; set; }
        public System.String RoHSStatus { get; set; }
        public System.String FactorySealed { get; set; }
        public System.Int32 IPOBOMNo { get; set; }
        public System.String SupplierTotalQSA { get; set; }
        public System.String SupplierMOQ { get; set; }
        public System.String SupplierLTB { get; set; }
        public System.Boolean IsSourcingHub { get; set; }
        public System.String ProductDescription { get; set; }
        public System.Int32? MSLLevelNo { get; set; }
        public System.String productNameDescrip { get; set; }
        public System.Boolean isIncludeAltPart { get; set; }
        public System.Int32? RowNum { get; set; }
        public System.Int32? TotalCount { get; set; }
        public System.String Description { get; set; }
        public System.String SupplierMessage { get; set; }
        //[001] code start
        /// <summary>
        /// Log Details CrossMatch
        /// </summary>
        public System.String LogDetails { get; set; }
        //[001] code end
        /// <summary>
        /// EpoId (from Table)
        /// </summary>
        public System.Int32 EpoId { get; set; }
        public System.Int32 ReverseLogisticId { get; set; } //[003]
        /// <summary>
        /// EpoStatusNo (from Table)
        /// usp_ipobom_source_Epo
        /// </summary>
        public System.Int32? EpoStatusNo { get; set; }
        public System.Int32? ReverseLogisticStatusNo { get; set; }
        /// <summary>
        /// EpoStatusChangeDate (from Table)
        /// usp_ipobom_source_Epo
        /// </summary>
        public System.DateTime? EpoStatusChangeDate { get; set; }
        public System.DateTime? ReverseLogisticStatusChangeDate { get; set; } //[003]
        /// <summary>
        /// EpoStatusChangeLoginNo (from Table)
        /// usp_ipobom_source_Epo
        /// </summary>
        public System.Int32? EpoStatusChangeLoginNo { get; set; }
        public System.Int32? ReverseLogisticStatusChangeLoginNo { get; set; } //[003]
        /// <summary>
        /// OfferStatusChangeEmployeeName (from usp_ipobom_source_Epo)
        /// </summary>
        public System.String EpoStatusChangeEmployeeName { get; set; }
        public System.String ReverseLogisticStatusChangeEmployeeName { get; set; } //[003]
        /// <summary>
        /// UpliftPrice (from usp_ipobom_source_Epo)
        /// </summary>
        public System.Double UpliftPrice { get; set; }
        ///PackageDescription
        public System.String PackageDescription { get; set; }
        public System.Int32? CustomerRequirementId { get; set; }
        //add from hear
        /// <summary>
        /// SourcingResultId
        /// </summary>
        public System.Int32 SourcingResultId { get; set; }
        /// <summary>
        /// CustomerRequirementNo
        /// </summary>
        public System.Int32 CustomerRequirementNo { get; set; }
        /// <summary>
        /// SourcingTable
        /// </summary>
        public System.String SourcingTable { get; set; }
        /// <summary>
        /// SourcingTableItemNo
        /// </summary>
        public System.Int32? SourcingTableItemNo { get; set; }
        /// <summary>
        /// TypeName
        /// </summary>
        public System.String TypeName { get; set; }
        public System.Double? SupplierPrice { get; set; }
        public System.String POHubSupplierName { get; set; }
        public System.Int32? POHubCompanyNo { get; set; }
        public System.String IsPoHub { get; set; }
        public System.Int32? POHubReleaseBy { get; set; }

        public System.String ClientSupplierName { get; set; }
        public System.Int32? ClientCompanyNo { get; set; }
        /// <summary>
        /// UPLiftPrice
        /// </summary>
        public System.Double? UPLiftPrice { get; set; }
        public System.Int32 ClientCurrencyNo { get; set; }
        public System.String ClientCurrencyCode { get; set; }
        public System.Double? ConvertedSourcingPrice { get; set; }
        public System.String MslSpqFactorySealed { get; set; }
        public double? EstimatedShippingCost { get; set; }
        public string SupplierManufacturerName { get; set; }
        public string SupplierDateCode { get; set; }
        public string SupplierPackageType { get; set; }
        public string SupplierProductType { get; set; }
        public string SupplierNotes { get; set; }
        public DateTime? DeliveryDate { get; set; }
        public System.Boolean? SourcingRelease { get; set; }
        public bool IsClosed { get; set; }
        public string ROHSStatus { get; set; }
        public string RegionName { get; set; }
        public System.Int32? RegionNo { get; set; }

        public string HubRFQName { get; set; }
        public System.Int32? HubRFQNo { get; set; }
        public bool IsSoCreated { get; set; }
        public string TermsName { get; set; }
        public bool IsApplyPOBankFee { get; set; }
        public string SourceRef { get; set; }
        public bool IsReleased { get; set; }
        public bool Recalled { get; set; }
        public string SourcingNotes { get; set; }
        public double? OriginalPrice { get; set; }
        public System.Int32? ActualCurrencyNo { get; set; }
        public System.String ActualCurrencyCode { get; set; }
        public System.Int32 SourcingReleasedCount { get; set; }
        public System.String MSLLevelText { get; set; }
        public double? ActualPrice { get; set; }
        public double? SupplierPercentage { get; set; }
        //[001] start
        /// <summary>
        /// SupplierWarranty
        /// </summary>
        public System.Int32? SupplierWarranty { get; set; }
        /// <summary>
        /// NonPreferredCompany
        /// </summary>
        public System.Boolean? NonPreferredCompany { get; set; }
        //[001] end
        //[003] start
        public System.Boolean IsTestingRecommended { get; set; }
        //[003] end
        public System.Boolean? IsImageAvailable { get; set; }
        /// <summary>
        /// PriorityId (from Table)
        /// </summary>
        public System.Int32 PriorityId { get; set; }
        /// <summary>
        /// PriorityNo 
        /// </summary>
        public System.Int32 PriorityNo { get; set; }
        public System.Int32 IHSCountryOfOriginNo { get; set; }
        public System.String IHSCountryOfOriginName { get; set; }
        public System.Int32 CountryOfOriginNo { get; set; }
        public System.String CountryOfOriginName { get; set; }
        public System.Int32? ReReleased { get; set; }
        public System.Boolean? PartWatchMatch { get; set; }
        public System.Boolean? DiffrentClientOffer { get; set; }
        /// <summary>
		/// CurrencyNo
		/// </summary>
		public System.Int32 EPOCurrencyNo { get; set; }

        public int curpage { get; set; }
        public string OfferSource { get; set; }

        public int REQStatus { get; set; }
        public System.Boolean OfferAddFlag { get; set; }
        public string UnitCostPrice { get; set; }
        /// <summary>
		/// ClientUPLiftPrice
		/// </summary>
		public System.Double? ClientUPLiftPrice { get; set; }
        public string BulkEditBy { get; set; }
        public DateTime? BulkEditDate { get; set; }
        public string BulkEditAction { get; set; }

        public System.String SupplierValidateMessage { get; set; }
        public System.Boolean SupplierCountValid { get; set; }
        public int? DivisionStatus { get; set; }
        public bool IsRestrictMfr { get; set; }
        public string SourcingType { get; set; }
        public string SourcingTypeDescription { get; set; }
        public bool AllowRemoveOffer { get; set; }
        #endregion

        #region Methods

        /// <summary>
        /// Delete
        /// Calls [usp_delete_Offer]
        /// </summary>
        public static bool Delete(System.Int32? offerId) {
			return Rebound.GlobalTrader.DAL.SiteProvider.Offer.Delete(offerId);
		}
		/// <summary>
		/// Insert
        /// Calls [usp_insert_OfferNew]
		/// </summary>
        public static Int32 Insert(System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? productNo, System.Int32? packageNo, System.Int32? quantity, System.Double? price, System.Int32? currencyNo, System.DateTime? originalEntryDate, System.Int32? salesman, System.Int32? supplierNo, System.String supplierName, System.Byte? rohs, System.Int32? offerStatusNo, System.String notes, System.Int32? updatedBy, System.Int32? clientNo, bool? isPoHub)
        {
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.Offer.Insert(part, manufacturerNo, dateCode, productNo, packageNo, quantity, price, currencyNo, originalEntryDate, salesman, supplierNo, supplierName, rohs, offerStatusNo, notes, updatedBy, clientNo, isPoHub);
			return objReturn;
		}

        /// <summary>
        /// Insert
        /// Calls [usp_insert_OfferNew]
        /// </summary>
        public static Int32 IPOBOMInsert(System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? productNo, System.Int32? packageNo, System.Int32? quantity, System.Double? price, System.Int32? currencyNo, System.DateTime? originalEntryDate, System.Int32? salesman, System.Int32? supplierNo, System.String supplierName, System.Byte? rohs, System.Int32? offerStatusNo, System.String notes, System.Int32? updatedBy, System.Int32? clientNo, bool? isPoHub, System.String supplierTotalQSA, System.String supplierMOQ, System.String supplierLTB, System.String msl, System.String spq, System.String leadTime, System.String factorySealed, System.String rohsStatus,System.Int32? mslLevelNo)
        {
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.Offer.IPOBOMInsert(part, manufacturerNo, dateCode, productNo, packageNo, quantity, price, currencyNo, originalEntryDate, salesman, supplierNo, supplierName, rohs, offerStatusNo, notes, updatedBy, clientNo, isPoHub, supplierTotalQSA, supplierMOQ, supplierLTB, msl, spq, leadTime, factorySealed, rohsStatus,mslLevelNo);
            return objReturn;
        }
        public static Int32 BOMManagerAPIOfferInsert(int Supplier,
                                                     string SupplierName,
                                                     string PartNo,
                                                     int ROHS,
                                                     System.Int32? ManufacturerNo,
                                                     string ManufacturerName,
                                                     System.String DateCode,
                                                     System.Int32? ProductNo,
                                                     string ProductName,
                                                     System.Int32? PackageNo,
                                                     string PackageName,
                                                     System.Int32? Quantity,
                                                     System.Double? Price,
                                                     System.Int32? Currency,
                                                     int OfferStatus,
                                                     System.String SupplierTotalQSA,
                                                     System.String SupplierMOQ,
                                                     System.String SupplierLTB,
                                                     int MSLNo,
                                                     System.String SPQ,
                                                     System.String LeadTime,
                                                     System.String FactorySealed,
                                                     System.String ROHSStatus,
                                                     string Notes,
                                                     int BOMManagerID,
                                                     int ClientID,
                                                     int alterCRNumber,
                                                     int? supplierWarranty,
                                                     int? countryOfOriginNo,
                                                     double? sellPrice,
                                                     double? shippingCost,
                                                     string reason,
                                                     int regionNo,
                                                     DateTime? deliveryDate,
                                                     bool? isTestingRecommended)
        {
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.Offer.BOMManagerAPIOfferInsert(Supplier,
                                                                                                   SupplierName,
                                                                                                   PartNo,
                                                                                                   ROHS,
                                                                                                   ManufacturerNo,
                                                                                                   ManufacturerName,
                                                                                                   DateCode,
                                                                                                   ProductNo,
                                                                                                   ProductName,
                                                                                                   PackageNo,
                                                                                                   PackageName,
                                                                                                   Quantity,
                                                                                                   Price,
                                                                                                   Currency,
                                                                                                   OfferStatus,
                                                                                                   SupplierTotalQSA,
                                                                                                   SupplierMOQ,
                                                                                                   SupplierLTB,
                                                                                                   MSLNo,
                                                                                                   SPQ,
                                                                                                   LeadTime,
                                                                                                   FactorySealed,
                                                                                                   ROHSStatus,
                                                                                                   Notes,
                                                                                                   BOMManagerID,
                                                                                                   ClientID,
                                                                                                   alterCRNumber,
                                                                                                   supplierWarranty,
                                                                                                   countryOfOriginNo,
                                                                                                   sellPrice,
                                                                                                   shippingCost,
                                                                                                   reason,
                                                                                                   regionNo,
                                                                                                   deliveryDate,
                                                                                                   isTestingRecommended);
            return objReturn;
        }

        /// <summary>
        /// Insert
        /// Calls [usp_offer_clone_AddToRequirement]
        /// </summary>
        public static Int32 CloneOfferAddToReq(System.Int32 offerId, System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? productNo, System.Int32? packageNo, System.Int32? quantity, System.Double? price, System.Int32? currencyNo, System.DateTime? originalEntryDate, System.Int32? salesman, System.Int32? supplierNo, System.String supplierName, System.Byte? rohs, System.Int32? offerStatusNo, System.String notes, System.Int32? updatedBy, System.Int32? clientNo, bool? isPoHub, System.String supplierTotalQSA, System.String supplierMOQ, System.String supplierLTB, System.String msl, System.String spq, System.String leadTime, System.String factorySealed, System.String rohsStatus, System.Int32? customerRequirementNo, System.Int32? mslLevelNo,out System.String strLinkMessage)
        {
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.Offer.CloneOfferAddToReq(offerId, part, manufacturerNo, dateCode, productNo, packageNo, quantity, price, currencyNo, originalEntryDate, salesman, supplierNo, supplierName, rohs, offerStatusNo, notes, updatedBy, clientNo, isPoHub, supplierTotalQSA, supplierMOQ, supplierLTB, msl, spq, leadTime, factorySealed, rohsStatus, customerRequirementNo, mslLevelNo, out strLinkMessage);
            return objReturn;
        }

        /// <summary>
        /// Insert
        /// Calls [usp_AltPartInfo_clone_AddToRequirement]
        /// </summary>
        public static Int32 CloneAltPartInfoAddToReq(System.Int32 AltpartInfoId, System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? productNo, System.Int32? packageNo, System.Int32? quantity, System.Double? price, System.Int32? currencyNo, System.DateTime? originalEntryDate, System.Int32? salesman, System.Int32? supplierNo, System.String supplierName, System.Byte? rohs, System.Int32? offerStatusNo, System.String notes, System.Int32? updatedBy, System.Int32? clientNo, bool? isPoHub, System.String supplierTotalQSA, System.String supplierMOQ, System.String supplierLTB, System.String msl, System.String spq, System.String leadTime, System.String factorySealed, System.String rohsStatus, System.Int32? customerRequirementNo, System.Int32? mslLevelNo,out System.String strLinkMessage)
        {
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.Offer.CloneAltPartInfoAddToReq(AltpartInfoId, part, manufacturerNo, dateCode, productNo, packageNo, quantity, price, currencyNo, originalEntryDate, salesman, supplierNo, supplierName, rohs, offerStatusNo, notes, updatedBy, clientNo, isPoHub, supplierTotalQSA, supplierMOQ, supplierLTB, msl, spq, leadTime, factorySealed, rohsStatus, customerRequirementNo, mslLevelNo, out strLinkMessage);
            return objReturn;
        }

        //codee add for CrossMatch Clone
        /// <summary>
        /// Insert
        /// Calls [usp_offer_clone_CrossMatchRequirement]
        /// </summary>
        public static Int32 CloneOfferCrossMatchReq(System.Int32 offerId, System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? productNo, System.Int32? packageNo, System.Int32? quantity, System.Double? price, System.Int32? currencyNo, System.DateTime? originalEntryDate, System.Int32? salesman, System.Int32? supplierNo, System.String supplierName, System.Byte? rohs, System.Int32? offerStatusNo, System.String notes, System.Int32? updatedBy, System.Int32? clientNo, bool? isPoHub, System.String supplierTotalQSA, System.String supplierMOQ, System.String supplierLTB, System.String msl, System.String spq, System.String leadTime, System.String factorySealed, System.String rohsStatus, System.Int32? customerRequirementNo, System.Int32? mslLevelNo, out System.String strLinkMessage)
        {
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.Offer.CloneOfferCrossMatchReq(offerId, part, manufacturerNo, dateCode, productNo, packageNo, quantity, price, currencyNo, originalEntryDate, salesman, supplierNo, supplierName, rohs, offerStatusNo, notes, updatedBy, clientNo, isPoHub, supplierTotalQSA, supplierMOQ, supplierLTB, msl, spq, leadTime, factorySealed, rohsStatus, customerRequirementNo, mslLevelNo, out strLinkMessage);
            return objReturn;
        }

        //code end

        

       

		/// <summary>
		/// Insert (without parameters)
        /// Calls [usp_insert_OfferNew]
		/// </summary>
		public Int32 Insert() {
            return Rebound.GlobalTrader.DAL.SiteProvider.Offer.Insert(Part, ManufacturerNo, DateCode, ProductNo, PackageNo, Quantity, Price, CurrencyNo, OriginalEntryDate, Salesman, SupplierNo, SupplierName, ROHS, OfferStatusNo, Notes, UpdatedBy, ClientNo, null);
		}
		/// <summary>
		/// Get //anand
		/// Calls [usp_select_Offer]
		/// </summary>
		public static Offer Get(System.Int32? offerId,bool? isPoHub) {
			Rebound.GlobalTrader.DAL.OfferDetails objDetails = Rebound.GlobalTrader.DAL.SiteProvider.Offer.Get(offerId,isPoHub);
			if (objDetails == null) {
				return null;
			} else {
				Offer obj = new Offer();
				obj.OfferId = objDetails.OfferId;
				obj.FullPart = objDetails.FullPart;
				obj.Part = objDetails.Part;
				obj.ManufacturerNo = objDetails.ManufacturerNo;
				obj.DateCode = objDetails.DateCode;
				obj.ProductNo = objDetails.ProductNo;
				obj.PackageNo = objDetails.PackageNo;
				obj.Quantity = objDetails.Quantity;
				obj.Price = objDetails.Price;
				obj.OriginalEntryDate = objDetails.OriginalEntryDate;
				obj.Salesman = objDetails.Salesman;
				obj.SupplierNo = objDetails.SupplierNo;
				obj.CurrencyNo = objDetails.CurrencyNo;
				obj.ROHS = objDetails.ROHS;
				obj.UpdatedBy = objDetails.UpdatedBy;
				obj.DLUP = objDetails.DLUP;
				obj.OfferStatusNo = objDetails.OfferStatusNo;
				obj.OfferStatusChangeDate = objDetails.OfferStatusChangeDate;
				obj.OfferStatusChangeLoginNo = objDetails.OfferStatusChangeLoginNo;
				obj.Notes = objDetails.Notes;
				obj.ManufacturerName = objDetails.ManufacturerName;
				obj.ProductName = objDetails.ProductName;
				obj.PackageName = objDetails.PackageName;
				obj.SupplierName = objDetails.SupplierName;
                obj.SupplierTotalQSA = objDetails.SupplierTotalQSA;
                obj.SupplierLTB = objDetails.SupplierLTB;
                obj.SupplierMOQ = objDetails.SupplierMOQ;
                
                obj.MSL = objDetails.MSL;
                obj.SPQ = objDetails.SPQ;
                obj.LeadTime = objDetails.LeadTime;
                obj.FactorySealed = objDetails.FactorySealed;
                obj.RoHSStatus = objDetails.RoHSStatus;
                obj.ProductDescription = objDetails.ProductDescription;
                obj.ProductInactive = objDetails.ProductInactive;
                obj.MSLLevelNo = objDetails.MSLLevelNo;
                obj.productNameDescrip = objDetails.productNameDescrip;
                obj.PackageDescription = objDetails.PackageDescription;

                objDetails = null;
				return obj;
			}
		}

        /// <summary>
        /// Get //anand
        /// Calls [usp_select_AltPart]
        /// </summary>
        public static Offer GetAltPart(System.Int32? AlternativePartId, bool? isPoHub)
        {
            Rebound.GlobalTrader.DAL.OfferDetails objDetails = Rebound.GlobalTrader.DAL.SiteProvider.Offer.GetAltPart(AlternativePartId, isPoHub);
            if (objDetails == null)
            {
                return null;
            }
            else
            {
                Offer obj = new Offer();
                obj.OfferId = objDetails.OfferId;
                obj.FullPart = objDetails.FullPart;
                obj.Part = objDetails.Part;
                obj.ManufacturerNo = objDetails.ManufacturerNo;
                obj.DateCode = objDetails.DateCode;
                obj.ProductNo = objDetails.ProductNo;
                obj.PackageNo = objDetails.PackageNo;
                obj.Quantity = objDetails.Quantity;
                obj.Price = objDetails.Price;
                obj.OriginalEntryDate = objDetails.OriginalEntryDate;
                obj.Salesman = objDetails.Salesman;
                obj.SupplierNo = objDetails.SupplierNo;
                obj.CurrencyNo = objDetails.CurrencyNo;
                obj.ROHS = objDetails.ROHS;
                obj.UpdatedBy = objDetails.UpdatedBy;
                obj.DLUP = objDetails.DLUP;
                obj.OfferStatusNo = objDetails.OfferStatusNo;
                obj.OfferStatusChangeDate = objDetails.OfferStatusChangeDate;
                obj.OfferStatusChangeLoginNo = objDetails.OfferStatusChangeLoginNo;
                obj.Notes = objDetails.Notes;
                obj.ManufacturerName = objDetails.ManufacturerName;
                obj.ProductName = objDetails.ProductName;
                obj.PackageName = objDetails.PackageName;
                obj.SupplierName = objDetails.SupplierName;
                obj.SupplierTotalQSA = objDetails.SupplierTotalQSA;
                obj.SupplierLTB = objDetails.SupplierLTB;
                obj.SupplierMOQ = objDetails.SupplierMOQ;

                obj.MSL = objDetails.MSL;
                obj.SPQ = objDetails.SPQ;
                obj.LeadTime = objDetails.LeadTime;
                obj.FactorySealed = objDetails.FactorySealed;
                obj.RoHSStatus = objDetails.RoHSStatus;
                obj.ProductDescription = objDetails.ProductDescription;
                obj.ProductInactive = objDetails.ProductInactive;
                obj.MSLLevelNo = objDetails.MSLLevelNo;
                obj.productNameDescrip = objDetails.productNameDescrip;
                obj.PackageDescription = objDetails.PackageDescription;

                objDetails = null;
                return obj;
            }
        }

        /// <summary>
        /// Get
        /// Calls [usp_select_EditEpo]
        /// </summary>
        public static Offer GetEpo(System.Int32? EpoId, bool? isPoHub)
        {
            Rebound.GlobalTrader.DAL.OfferDetails objDetails = Rebound.GlobalTrader.DAL.SiteProvider.Offer.GetEpo(EpoId, isPoHub);
            if (objDetails == null)
            {
                return null;
            }
            else
            {
                Offer obj = new Offer();
                obj.EpoId = objDetails.EpoId;
                obj.FullPart = objDetails.FullPart;
                obj.Part = objDetails.Part;
                obj.ManufacturerNo = objDetails.ManufacturerNo;
                obj.DateCode = objDetails.DateCode;
                obj.ProductNo = objDetails.ProductNo;
                obj.PackageNo = objDetails.PackageNo;
                obj.Quantity = objDetails.Quantity;
                obj.Price = objDetails.Price;
                obj.OriginalEntryDate = objDetails.OriginalEntryDate;
                obj.Salesman = objDetails.Salesman;
                obj.SupplierNo = objDetails.SupplierNo;
                obj.CurrencyNo = objDetails.CurrencyNo;
                obj.ROHS = objDetails.ROHS;
                obj.UpdatedBy = objDetails.UpdatedBy;
                obj.DLUP = objDetails.DLUP;
                obj.EpoStatusNo = objDetails.EpoStatusNo;
                obj.EpoStatusChangeDate = objDetails.EpoStatusChangeDate;
                obj.EpoStatusChangeLoginNo = objDetails.EpoStatusChangeLoginNo;
                obj.Notes = objDetails.Notes;
                obj.ManufacturerName = objDetails.ManufacturerName;
                obj.ProductName = objDetails.ProductName;
                obj.PackageName = objDetails.PackageName;
                obj.SupplierName = objDetails.SupplierName;
                obj.SupplierTotalQSA = objDetails.SupplierTotalQSA;
                obj.SupplierLTB = objDetails.SupplierLTB;
                obj.SupplierMOQ = objDetails.SupplierMOQ;

                obj.MSL = objDetails.MSL;
                obj.SPQ = objDetails.SPQ;
                obj.LeadTime = objDetails.LeadTime;
                obj.FactorySealed = objDetails.FactorySealed;
                obj.RoHSStatus = objDetails.RoHSStatus;
                obj.ProductDescription = objDetails.ProductDescription;
                obj.ProductInactive = objDetails.ProductInactive;
                obj.MSLLevelNo = objDetails.MSLLevelNo;
                obj.productNameDescrip = objDetails.productNameDescrip;
                obj.UpliftPrice = objDetails.UpliftPrice;
                obj.Description = objDetails.Description;
                obj.PackageDescription = objDetails.PackageDescription;

                objDetails = null;
                return obj;
            }
        }

        /// <summary>
        /// Get
        /// Calls [usp_select_StockHUBRFQ]
        /// </summary>
        public static Offer GetStockHUBRFQ(System.Int32? StockId, bool? isPoHub)
        {
            Rebound.GlobalTrader.DAL.OfferDetails objDetails = Rebound.GlobalTrader.DAL.SiteProvider.Offer.GetStockHUBRFQ(StockId, isPoHub);
            if (objDetails == null)
            {
                return null;
            }
            else
            {
                Offer obj = new Offer();
                obj.EpoId = objDetails.EpoId;
                obj.FullPart = objDetails.FullPart;
                obj.Part = objDetails.Part;
                obj.ManufacturerNo = objDetails.ManufacturerNo;
                obj.DateCode = objDetails.DateCode;
                obj.ProductNo = objDetails.ProductNo;
                obj.PackageNo = objDetails.PackageNo;
                obj.Quantity = objDetails.Quantity;
                obj.Price = objDetails.Price;
                obj.OriginalEntryDate = objDetails.OriginalEntryDate;
                obj.Salesman = objDetails.Salesman;
                obj.SupplierNo = objDetails.SupplierNo;
                obj.CurrencyNo = objDetails.CurrencyNo;
                obj.ROHS = objDetails.ROHS;
                obj.UpdatedBy = objDetails.UpdatedBy;
                obj.DLUP = objDetails.DLUP;
                obj.EpoStatusNo = objDetails.EpoStatusNo;
                obj.EpoStatusChangeDate = objDetails.EpoStatusChangeDate;
                obj.EpoStatusChangeLoginNo = objDetails.EpoStatusChangeLoginNo;
                obj.Notes = objDetails.Notes;
                obj.ManufacturerName = objDetails.ManufacturerName;
                obj.ProductName = objDetails.ProductName;
                obj.PackageName = objDetails.PackageName;
                obj.SupplierName = objDetails.SupplierName;
                obj.SupplierTotalQSA = objDetails.SupplierTotalQSA;
                obj.SupplierLTB = objDetails.SupplierLTB;
                obj.SupplierMOQ = objDetails.SupplierMOQ;
                obj.MSL = objDetails.MSL;
                obj.SPQ = objDetails.SPQ;
                obj.LeadTime = objDetails.LeadTime;
                obj.FactorySealed = objDetails.FactorySealed;
                obj.RoHSStatus = objDetails.RoHSStatus;
                obj.ProductDescription = objDetails.ProductDescription;
                obj.ProductInactive = objDetails.ProductInactive;
                obj.MSLLevelNo = objDetails.MSLLevelNo;
                obj.productNameDescrip = objDetails.productNameDescrip;
                obj.UpliftPrice = objDetails.UpliftPrice;
                obj.Description = objDetails.Description;
                obj.PackageDescription = objDetails.PackageDescription;
                obj.SupplierCountValid = objDetails.SupplierCountValid;
                obj.SupplierValidateMessage = objDetails.SupplierValidateMessage;
                objDetails = null;
                return obj;
            }
        }

        /// <summary>
        /// [004]
        /// </summary>
        /// <param name="ReverseLogisticId"></param>
        /// <param name="isPoHub"></param>
        /// <returns></returns>
        public static Offer GetReverseLogistic(System.Int32? ReverseLogisticId, bool? isPoHub)
        {
            Rebound.GlobalTrader.DAL.OfferDetails objDetails = Rebound.GlobalTrader.DAL.SiteProvider.Offer.GetReverseLogistic(ReverseLogisticId, isPoHub);
            if (objDetails == null)
            {
                return null;
            }
            else
            {
                Offer obj = new Offer();
                obj.ReverseLogisticId = objDetails.ReverseLogisticId;
                obj.FullPart = objDetails.FullPart;
                obj.Part = objDetails.Part;
                obj.ManufacturerNo = objDetails.ManufacturerNo;
                obj.DateCode = objDetails.DateCode;
                obj.ProductNo = objDetails.ProductNo;
                obj.PackageNo = objDetails.PackageNo;
                obj.Quantity = objDetails.Quantity;
                obj.Price = objDetails.Price;
                obj.OriginalEntryDate = objDetails.OriginalEntryDate;
                obj.Salesman = objDetails.Salesman;
                obj.SupplierNo = objDetails.SupplierNo;
                obj.CurrencyNo = objDetails.CurrencyNo;
                obj.ROHS = objDetails.ROHS;
                obj.UpdatedBy = objDetails.UpdatedBy;
                obj.DLUP = objDetails.DLUP;
                obj.ReverseLogisticStatusNo = objDetails.ReverseLogisticStatusNo;
                obj.ReverseLogisticStatusChangeDate = objDetails.ReverseLogisticStatusChangeDate;
                obj.ReverseLogisticStatusChangeLoginNo = objDetails.ReverseLogisticStatusChangeLoginNo;
                obj.Notes = objDetails.Notes;
                obj.ManufacturerName = objDetails.ManufacturerName;
                obj.ProductName = objDetails.ProductName;
                obj.PackageName = objDetails.PackageName;
                obj.SupplierName = objDetails.SupplierName;
                obj.SupplierTotalQSA = objDetails.SupplierTotalQSA;
                obj.SupplierLTB = objDetails.SupplierLTB;
                obj.SupplierMOQ = objDetails.SupplierMOQ;

                obj.MSL = objDetails.MSL;
                obj.SPQ = objDetails.SPQ;
                obj.LeadTime = objDetails.LeadTime;
                obj.FactorySealed = objDetails.FactorySealed;
                obj.RoHSStatus = objDetails.RoHSStatus;
                obj.ProductDescription = objDetails.ProductDescription;
                obj.ProductInactive = objDetails.ProductInactive;
                obj.MSLLevelNo = objDetails.MSLLevelNo;
                obj.productNameDescrip = objDetails.productNameDescrip;
                obj.UpliftPrice = objDetails.UpliftPrice;
                obj.Description = objDetails.Description;
                obj.PackageDescription = objDetails.PackageDescription;

                objDetails = null;
                return obj;
            }
        }

        //code 
        /// <summary>
        /// Get CrossMatch Auto Search Details
        /// Calls [usp_CrossMatch_SearchLog_select]
        /// </summary>
        public static Offer GetCrossMatchAutoSearch(System.Int32 BomId, System.Int32 userId, int ClientId)
        {
            Rebound.GlobalTrader.DAL.OfferDetails objDetails = Rebound.GlobalTrader.DAL.SiteProvider.Offer.GetCrossMatchAutoSearch(BomId, userId, ClientId);
            if (objDetails == null)
            {
                return null;
            }
            else
            {
                Offer obj = new Offer();
                obj.LogDetails = objDetails.LogDetails;
               
                objDetails = null;
                return obj;
            }
        }
            //end



		/// <summary>
		/// Source
		/// Calls [usp_source_Offer]
		/// </summary>
        public static List<Offer> Source(System.Int32? clientId, System.String partSearch, System.Int32? index, DateTime? maxDate, out DateTime? outDate, System.Boolean? blnReferesh, bool IsServerLocal, System.Boolean? IsPOHub, System.Int32? sortIndex, System.Int32? sortDirection)
        {
            DateTime? StartDate = null;
            DateTime? EndDate = null;
            if (index == 2 && maxDate.HasValue)
            {
                StartDate = (!blnReferesh.Value) ? maxDate.Value.AddMonths(-6) : maxDate.Value.AddMonths(-12);
                EndDate = maxDate.Value;
            }
            else if (index == 3 && maxDate.HasValue)
            {
                StartDate = DateTime.Parse("1900-01-01 00:00:00.000");
                EndDate = maxDate.Value;
            }

            List<OfferDetails> lstDetails = (IsPOHub.Value)
                ? Rebound.GlobalTrader.DAL.SiteProvider.Offer.SourceArchive(clientId, partSearch, index, StartDate, EndDate, out outDate, IsServerLocal, IsPOHub, sortIndex, sortDirection)
                : Rebound.GlobalTrader.DAL.SiteProvider.Offer.Source(clientId, partSearch, index, StartDate, EndDate, out outDate, IsServerLocal, IsPOHub, sortIndex, sortDirection);
            if (lstDetails == null)
            {
                return new List<Offer>();
            }
            else
            {
				List<Offer> lst = new List<Offer>();
                foreach (OfferDetails objDetails in lstDetails)
                {
					Rebound.GlobalTrader.BLL.Offer obj = new Rebound.GlobalTrader.BLL.Offer();
					obj.OfferId = objDetails.OfferId;
					obj.FullPart = objDetails.FullPart;
					obj.Part = objDetails.Part;
					obj.ManufacturerNo = objDetails.ManufacturerNo;
					obj.DateCode = objDetails.DateCode;
					obj.ProductNo = objDetails.ProductNo;
					obj.PackageNo = objDetails.PackageNo;
					obj.Quantity = objDetails.Quantity;
					obj.Price = objDetails.Price;
					obj.OriginalEntryDate = objDetails.OriginalEntryDate;
					obj.Salesman = objDetails.Salesman;
					obj.SupplierNo = objDetails.SupplierNo;
					obj.CurrencyNo = objDetails.CurrencyNo;
					obj.ROHS = objDetails.ROHS;
					obj.UpdatedBy = objDetails.UpdatedBy;
					obj.DLUP = objDetails.DLUP;
					obj.OfferStatusNo = objDetails.OfferStatusNo;
					obj.OfferStatusChangeDate = objDetails.OfferStatusChangeDate;
					obj.OfferStatusChangeLoginNo = objDetails.OfferStatusChangeLoginNo;
					obj.ManufacturerCode = objDetails.ManufacturerCode;
					obj.ProductName = objDetails.ProductName;
					obj.CurrencyCode = objDetails.CurrencyCode;
					obj.CurrencyDescription = objDetails.CurrencyDescription;
					obj.SupplierName = objDetails.SupplierName;
					obj.ManufacturerName = objDetails.ManufacturerName;
					obj.SupplierEmail = objDetails.SupplierEmail;
					obj.SalesmanName = objDetails.SalesmanName;
					obj.OfferStatusChangeEmployeeName = objDetails.OfferStatusChangeEmployeeName;
					obj.PackageName = objDetails.PackageName;
					obj.Notes = objDetails.Notes;
					obj.ClientNo = objDetails.ClientNo;
					obj.ClientId = objDetails.ClientId;
					obj.ClientName = objDetails.ClientName;
					obj.ClientDataVisibleToOthers = objDetails.ClientDataVisibleToOthers;
                    //[001] code start
                    obj.SupplierType = objDetails.SupplierType;
                    //[001] code end
                    obj.ClientCode = objDetails.ClientCode;
                    obj.IsSourcingHub = objDetails.IsSourcingHub;
                    obj.SupplierMessage = objDetails.SupplierMessage;

                    lst.Add(obj);
					obj = null;
				}
				lstDetails = null;
				return lst;
			}
		}

        public static List<APIExternalLinksDetails> APIOffersBOMManager(int BOMManagerID, out DataTable Parts, string Part, int? CustomerReqID, int curPage = 1, int Rpp = 5)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Offer.OfferAPIBOMManager(BOMManagerID, out Parts, Part, CustomerReqID, curPage, Rpp);
        }
        
        public static int LyticaApiLog(int BOMManagerID, int? ClientID)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Offer.LyticaApiLog(BOMManagerID, ClientID);
        }
        public static List<LyticaAPI> GetLyticaAPIData(int BOMManagerID, int? CustomerReqID, string Parts,  out DataTable dtPart,int curPage = 1, int Rpp = 5)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Offer.GetLyticaAPIData(BOMManagerID, CustomerReqID, Parts, out dtPart, curPage, Rpp);
        }
        public static DataSet GetLyticaAPIAlternateData(string Parts, int curPage = 1, int Rpp = 5)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Offer.GetLyticaAPIAlternateData( Parts, curPage, Rpp);
        }

        public static List<Offer> SourceEpo(System.Int32? clientId, System.String partSearch, System.Int32? index, DateTime? maxDate, out DateTime? outDate, System.Boolean? blnReferesh, bool IsServerLocal, System.Boolean? IsPOHub)
        {
            DateTime? StartDate = null;
            DateTime? EndDate = null;
            if (index == 2 && maxDate.HasValue)
            {
                StartDate = (!blnReferesh.Value) ? maxDate.Value.AddMonths(-6) : maxDate.Value.AddMonths(-12);
                EndDate = maxDate.Value;
            }
            else if (index == 3 && maxDate.HasValue)
            {
                StartDate = DateTime.Parse("1900-01-01 00:00:00.000");
                EndDate = maxDate.Value;
            }

            List<OfferDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.Offer.SourceEpo(clientId, partSearch, index, StartDate, EndDate, out outDate, IsServerLocal, IsPOHub);
            if (lstDetails == null)
            {
                return new List<Offer>();
            }
            else
            {
                string ScrLoginEmail = Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["ScrLoginEmail"]);
                List<Offer> lst = new List<Offer>();
                foreach (OfferDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.Offer obj = new Rebound.GlobalTrader.BLL.Offer();
                    obj.EpoId = objDetails.EpoId;
                    obj.FullPart = objDetails.FullPart;
                    obj.Part = objDetails.Part;
                    obj.ManufacturerNo = objDetails.ManufacturerNo;
                    obj.DateCode = objDetails.DateCode;
                    obj.ProductNo = objDetails.ProductNo;
                    obj.PackageNo = objDetails.PackageNo;
                    obj.Quantity = objDetails.Quantity;
                    obj.Price = objDetails.Price;
                    obj.OriginalEntryDate = objDetails.OriginalEntryDate;
                    obj.Salesman = objDetails.Salesman;
                    obj.SupplierNo = objDetails.SupplierNo;
                    obj.CurrencyNo = objDetails.CurrencyNo;
                    obj.ROHS = objDetails.ROHS;
                    obj.UpdatedBy = objDetails.UpdatedBy;
                    obj.DLUP = objDetails.DLUP;
                    obj.EpoStatusNo = objDetails.EpoStatusNo;
                    obj.EpoStatusChangeDate = objDetails.EpoStatusChangeDate;
                    obj.EpoStatusChangeLoginNo = objDetails.EpoStatusChangeLoginNo;
                    obj.ManufacturerCode = objDetails.ManufacturerCode;
                    obj.ProductName = objDetails.ProductName;
                    obj.CurrencyCode = objDetails.CurrencyCode;
                    obj.CurrencyDescription = objDetails.CurrencyDescription;
                    obj.SupplierName = objDetails.SupplierName;
                    obj.ManufacturerName = objDetails.ManufacturerName;

                    if (IsPOHub == false)
                        obj.SupplierEmail = ScrLoginEmail;
                    else
                        obj.SupplierEmail = objDetails.SupplierEmail;
                    
                    obj.SalesmanName = objDetails.SalesmanName;
                    obj.EpoStatusChangeEmployeeName = objDetails.EpoStatusChangeEmployeeName;
                    obj.PackageName = objDetails.PackageName;
                    obj.Notes = objDetails.Notes;
                    obj.ClientNo = objDetails.ClientNo;
                    obj.ClientId = objDetails.ClientId;
                    obj.ClientName = objDetails.ClientName;
                    obj.ClientDataVisibleToOthers = objDetails.ClientDataVisibleToOthers;
                    //[001] code start
                    obj.SupplierType = objDetails.SupplierType;
                    //[001] code end
                    obj.ClientCode = objDetails.ClientCode;
                    obj.MSL = objDetails.MSL;
                    obj.SPQ = objDetails.SPQ;
                    obj.LeadTime = objDetails.LeadTime;
                    obj.RoHSStatus = objDetails.RoHSStatus;
                    obj.FactorySealed = objDetails.FactorySealed;
                    obj.IPOBOMNo = objDetails.IPOBOMNo;
                    obj.SupplierTotalQSA = objDetails.SupplierTotalQSA;
                    obj.SupplierLTB = objDetails.SupplierLTB;
                    obj.SupplierMOQ = objDetails.SupplierMOQ;
                    obj.IsSourcingHub = objDetails.IsSourcingHub;
                    obj.UpliftPrice = objDetails.UpliftPrice;
                    obj.SupplierEpo = objDetails.SupplierEpo;
                    obj.Description = objDetails.Description;
                    obj.OfferAddFlag = objDetails.OfferAddFlag;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }

        /// <summary>
        /// [003]
        /// Source ReverseLogistics
        /// Calls [[usp_ipobom_source_ReverseLogistic] 
        /// </summary>
        public static List<Offer> SourceReverseLogistics(System.Int32? clientId, System.String partSearch, System.Int32? index, DateTime? maxDate, out DateTime? outDate, System.Boolean? blnReferesh, bool IsServerLocal, System.Boolean? IsPOHub)
        {
            DateTime? StartDate = null;
            DateTime? EndDate = null;
            if (index == 2 && maxDate.HasValue)
            {
                StartDate = (!blnReferesh.Value) ? maxDate.Value.AddMonths(-6) : maxDate.Value.AddMonths(-12);
                EndDate = maxDate.Value;
            }
            else if (index == 3 && maxDate.HasValue)
            {
                StartDate = DateTime.Parse("1900-01-01 00:00:00.000");
                EndDate = maxDate.Value;
            }

            List<OfferDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.Offer.SourceReverseLogistics(clientId, partSearch, index, StartDate, EndDate, out outDate, IsServerLocal, IsPOHub);
            if (lstDetails == null)
            {
                return new List<Offer>();
            }
            else
            {
                List<Offer> lst = new List<Offer>();
                foreach (OfferDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.Offer obj = new Rebound.GlobalTrader.BLL.Offer();
                    obj.ReverseLogisticId = objDetails.ReverseLogisticId;
                    obj.FullPart = objDetails.FullPart;
                    obj.Part = objDetails.Part;
                    obj.ManufacturerNo = objDetails.ManufacturerNo;
                    obj.DateCode = objDetails.DateCode;
                    obj.ProductNo = objDetails.ProductNo;
                    obj.PackageNo = objDetails.PackageNo;
                    obj.Quantity = objDetails.Quantity;
                    obj.Price = objDetails.Price;
                    obj.OriginalEntryDate = objDetails.OriginalEntryDate;
                    obj.Salesman = objDetails.Salesman;
                    obj.SupplierNo = objDetails.SupplierNo;
                    obj.CurrencyNo = objDetails.CurrencyNo;
                    obj.ROHS = objDetails.ROHS;
                    obj.UpdatedBy = objDetails.UpdatedBy;
                    obj.DLUP = objDetails.DLUP;
                    obj.ReverseLogisticStatusNo = objDetails.ReverseLogisticStatusNo;
                    obj.ReverseLogisticStatusChangeDate = objDetails.ReverseLogisticStatusChangeDate;
                    obj.ReverseLogisticStatusChangeLoginNo = objDetails.ReverseLogisticStatusChangeLoginNo;
                    obj.ManufacturerCode = objDetails.ManufacturerCode;
                    obj.ProductName = objDetails.ProductName;
                    obj.CurrencyCode = objDetails.CurrencyCode;
                    obj.CurrencyDescription = objDetails.CurrencyDescription;
                    obj.SupplierName = objDetails.SupplierName;
                    obj.ManufacturerName = objDetails.ManufacturerName;
                    obj.SupplierEmail = objDetails.SupplierEmail;
                    obj.SalesmanName = objDetails.SalesmanName;
                    obj.ReverseLogisticStatusChangeEmployeeName = objDetails.ReverseLogisticStatusChangeEmployeeName;
                    obj.PackageName = objDetails.PackageName;
                    obj.Notes = objDetails.Notes;
                    obj.ClientNo = objDetails.ClientNo;
                    obj.ClientId = objDetails.ClientId;
                    obj.ClientName = objDetails.ClientName;
                    obj.ClientDataVisibleToOthers = objDetails.ClientDataVisibleToOthers;
                    //[001] code start
                    obj.SupplierType = objDetails.SupplierType;
                    //[001] code end
                    obj.ClientCode = objDetails.ClientCode;
                    obj.MSL = objDetails.MSL;
                    obj.SPQ = objDetails.SPQ;
                    obj.LeadTime = objDetails.LeadTime;
                    obj.RoHSStatus = objDetails.RoHSStatus;
                    obj.FactorySealed = objDetails.FactorySealed;
                    obj.IPOBOMNo = objDetails.IPOBOMNo;
                    obj.SupplierTotalQSA = objDetails.SupplierTotalQSA;
                    obj.SupplierLTB = objDetails.SupplierLTB;
                    obj.SupplierMOQ = objDetails.SupplierMOQ;
                    obj.IsSourcingHub = objDetails.IsSourcingHub;
                    obj.UpliftPrice = objDetails.UpliftPrice;
                    obj.SupplierReverseLogistic = objDetails.SupplierReverseLogistic;
                    obj.Description = objDetails.Description;
                    obj.OfferAddFlag = objDetails.OfferAddFlag;
                    //test
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }

        /// <summary>
        /// [003]
        /// Source ReverseLogistics
        /// Calls [usp_source_GetReverseLogisticBulkEditHistory] 
        /// </summary>
        public static List<Offer> SourceReverseLogisticBulkEditHistory(System.Int32? clientId, System.String partSearch, System.Int32? index, DateTime? maxDate, out DateTime? outDate, System.Boolean? blnReferesh, bool IsServerLocal, System.Boolean? IsPOHub)
        {
            DateTime? StartDate = null;
            DateTime? EndDate = null;
            if (index == 2 && maxDate.HasValue)
            {
                StartDate = (!blnReferesh.Value) ? maxDate.Value.AddMonths(-6) : maxDate.Value.AddMonths(-12);
                EndDate = maxDate.Value;
            }
            else if (index == 3 && maxDate.HasValue)
            {
                StartDate = DateTime.Parse("1900-01-01 00:00:00.000");
                EndDate = maxDate.Value;
            }

            List<OfferDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.Offer.SourceReverseLogisticBulkEditHistory(clientId, partSearch, index, StartDate, EndDate, out outDate, IsServerLocal, IsPOHub);
            if (lstDetails == null)
            {
                return new List<Offer>();
            }
            else
            {
                List<Offer> lst = new List<Offer>();
                foreach (OfferDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.Offer obj = new Rebound.GlobalTrader.BLL.Offer();
                    obj.ReverseLogisticId = objDetails.ReverseLogisticId;
                    obj.FullPart = objDetails.FullPart;
                    obj.Part = objDetails.Part;
                    obj.ManufacturerNo = objDetails.ManufacturerNo;
                    obj.DateCode = objDetails.DateCode;
                    obj.ProductNo = objDetails.ProductNo;
                    obj.PackageNo = objDetails.PackageNo;
                    obj.Quantity = objDetails.Quantity;
                    obj.Price = objDetails.Price;
                    obj.OriginalEntryDate = objDetails.OriginalEntryDate;
                    obj.Salesman = objDetails.Salesman;
                    obj.SupplierNo = objDetails.SupplierNo;
                    obj.CurrencyNo = objDetails.CurrencyNo;
                    obj.ROHS = objDetails.ROHS;
                    obj.UpdatedBy = objDetails.UpdatedBy;
                    obj.DLUP = objDetails.DLUP;
                    obj.ReverseLogisticStatusNo = objDetails.ReverseLogisticStatusNo;
                    obj.ReverseLogisticStatusChangeDate = objDetails.ReverseLogisticStatusChangeDate;
                    obj.ReverseLogisticStatusChangeLoginNo = objDetails.ReverseLogisticStatusChangeLoginNo;
                    obj.ManufacturerCode = objDetails.ManufacturerCode;
                    obj.ProductName = objDetails.ProductName;
                    obj.CurrencyCode = objDetails.CurrencyCode;
                    obj.CurrencyDescription = objDetails.CurrencyDescription;
                    obj.SupplierName = objDetails.SupplierName;
                    obj.ManufacturerName = objDetails.ManufacturerName;
                    obj.SupplierEmail = objDetails.SupplierEmail;
                    obj.SalesmanName = objDetails.SalesmanName;
                    obj.ReverseLogisticStatusChangeEmployeeName = objDetails.ReverseLogisticStatusChangeEmployeeName;
                    obj.PackageName = objDetails.PackageName;
                    obj.Notes = objDetails.Notes;
                    obj.ClientNo = objDetails.ClientNo;
                    obj.ClientId = objDetails.ClientId;
                    obj.ClientName = objDetails.ClientName;
                    obj.ClientDataVisibleToOthers = objDetails.ClientDataVisibleToOthers;
                    //[001] code start
                    obj.SupplierType = objDetails.SupplierType;
                    //[001] code end
                    obj.ClientCode = objDetails.ClientCode;
                    obj.MSL = objDetails.MSL;
                    obj.SPQ = objDetails.SPQ;
                    obj.LeadTime = objDetails.LeadTime;
                    obj.RoHSStatus = objDetails.RoHSStatus;
                    obj.FactorySealed = objDetails.FactorySealed;
                    obj.IPOBOMNo = objDetails.IPOBOMNo;
                    obj.SupplierTotalQSA = objDetails.SupplierTotalQSA;
                    obj.SupplierLTB = objDetails.SupplierLTB;
                    obj.SupplierMOQ = objDetails.SupplierMOQ;
                    obj.IsSourcingHub = objDetails.IsSourcingHub;
                    obj.UpliftPrice = objDetails.UpliftPrice;
                    obj.SupplierReverseLogistic = objDetails.SupplierReverseLogistic;
                    obj.Description = objDetails.Description;
                    obj.BulkEditBy = objDetails.BulkEditBy;
                    obj.BulkEditDate = objDetails.BulkEditDate;
                    obj.BulkEditAction = objDetails.BulkEditAction;
                    //test
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }


        /// <summary>
        /// Source Ipo
        /// Calls [usp_ipobom_source_Epo] new sp for bind epo data from tbsourcingresult
        /// </summary>
        public static List<Offer> SourceIpo(System.Int32? clientId, System.String partSearch, System.Int32? index, DateTime? maxDate, out DateTime? outDate, System.Boolean? blnReferesh, bool IsServerLocal, System.Boolean? IsPOHub)
        {
            DateTime? StartDate = null;
            DateTime? EndDate = null;
            if (index == 2 && maxDate.HasValue)
            {
                StartDate = (!blnReferesh.Value) ? maxDate.Value.AddMonths(-6) : maxDate.Value.AddMonths(-12);
                EndDate = maxDate.Value;
            }
            else if (index == 3 && maxDate.HasValue)
            {
                StartDate = DateTime.Parse("1900-01-01 00:00:00.000");
                EndDate = maxDate.Value;
            }

            List<OfferDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.Offer.SourceIpo(clientId, partSearch, index, StartDate, EndDate, out outDate, IsServerLocal, IsPOHub);
            if (lstDetails == null)
            {
                return new List<Offer>();
            }
            else
            {
                List<Offer> lst = new List<Offer>();
                foreach (OfferDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.Offer obj = new Rebound.GlobalTrader.BLL.Offer();
                    obj.SourcingResultId = objDetails.SourcingResultId;
                    obj.CustomerRequirementNo = objDetails.CustomerRequirementNo;
                    obj.SourcingTable = objDetails.SourcingTable;
                    obj.SourcingTableItemNo = objDetails.SourcingTableItemNo;
                    obj.TypeName = objDetails.TypeName;
                    obj.FullPart = objDetails.FullPart;
                    obj.Part = objDetails.Part;
                    obj.ManufacturerNo = objDetails.ManufacturerNo;
                    obj.DateCode = objDetails.DateCode;
                    obj.ProductNo = objDetails.ProductNo;
                    obj.PackageNo = objDetails.PackageNo;
                    obj.Quantity = objDetails.Quantity;
                    obj.Price = objDetails.Price;
                    obj.EPOCurrencyNo = objDetails.EPOCurrencyNo;
                    obj.OriginalEntryDate = objDetails.OriginalEntryDate;
                    obj.Salesman = objDetails.Salesman;
                    obj.SupplierNo = objDetails.SupplierNo;
                    obj.UpdatedBy = objDetails.UpdatedBy;
                    obj.DLUP = objDetails.DLUP;
                    obj.ROHS = objDetails.ROHS;
                    obj.OfferStatusNo = objDetails.OfferStatusNo;
                    obj.OfferStatusChangeDate = objDetails.OfferStatusChangeDate;
                    obj.OfferStatusChangeLoginNo = objDetails.OfferStatusChangeLoginNo;
                    obj.Notes = objDetails.Notes;
                    obj.ManufacturerName = objDetails.ManufacturerName;
                    obj.ManufacturerCode = objDetails.ManufacturerCode;
                    obj.CurrencyCode = objDetails.CurrencyCode;
                    obj.SupplierName = objDetails.SupplierName;
                    obj.ProductName = objDetails.ProductName;
                    obj.ProductDescription = objDetails.ProductDescription;
                    obj.PackageName = objDetails.PackageName;
                    obj.PackageDescription = objDetails.PackageDescription;
                    obj.SalesmanName = objDetails.SalesmanName;
                    obj.OfferStatusChangeEmployeeName = objDetails.OfferStatusChangeEmployeeName;
                    obj.SupplierPrice = objDetails.SupplierPrice;
                    obj.POHubCompanyNo = objDetails.POHubCompanyNo;
                    obj.POHubSupplierName = objDetails.POHubSupplierName;
                    obj.POHubReleaseBy = objDetails.POHubReleaseBy;
                    obj.ClientCompanyNo = objDetails.ClientCompanyNo;
                    obj.ClientSupplierName = objDetails.ClientSupplierName;
                    obj.UPLiftPrice = objDetails.UPLiftPrice;
                    obj.ClientCurrencyNo = objDetails.ClientCurrencyNo;
                    obj.ClientCurrencyCode = objDetails.ClientCurrencyCode;
                    obj.ConvertedSourcingPrice = objDetails.ConvertedSourcingPrice;
                    obj.MslSpqFactorySealed = objDetails.MslSpqFactorySealed;
                    obj.EstimatedShippingCost = objDetails.EstimatedShippingCost;
                    obj.SupplierType = objDetails.SupplierType;
                    obj.DeliveryDate = objDetails.DeliveryDate;
                    obj.ActualPrice = objDetails.ActualPrice;
                    obj.SupplierPercentage = objDetails.SupplierPercentage;
                    obj.SupplierManufacturerName = objDetails.SupplierManufacturerName;
                    obj.SupplierDateCode = objDetails.SupplierDateCode;
                    obj.SupplierPackageType = objDetails.SupplierPackageType;
                    obj.SupplierProductType = objDetails.SupplierProductType;
                    obj.SupplierMOQ = objDetails.SupplierMOQ;
                    obj.SupplierTotalQSA = objDetails.SupplierTotalQSA;
                    obj.SupplierLTB = objDetails.SupplierLTB;
                    obj.SupplierNotes = objDetails.SupplierNotes;
                    obj.SourcingRelease = objDetails.SourcingRelease;
                    obj.SPQ = objDetails.SPQ;
                    obj.LeadTime = objDetails.LeadTime;
                    obj.ROHSStatus = objDetails.ROHSStatus;
                    obj.FactorySealed = objDetails.FactorySealed;
                    obj.MSL = objDetails.MSL;
                    obj.IsClosed = objDetails.IsClosed;
                    obj.ClientNo = objDetails.ClientNo;
                    obj.RegionName = objDetails.RegionName;
                    obj.IsClosed = objDetails.IsClosed;
                    obj.IsSoCreated = objDetails.IsSoCreated;
                    obj.TermsName = objDetails.TermsName;
                    obj.IsApplyPOBankFee = objDetails.IsApplyPOBankFee;
                    obj.SourceRef = objDetails.SourceRef;
                    obj.OriginalPrice = objDetails.OriginalPrice;
                    obj.ActualCurrencyNo = objDetails.ActualCurrencyNo;
                    obj.ActualCurrencyCode = objDetails.ActualCurrencyCode;
                    obj.SourcingReleasedCount = objDetails.SourcingReleasedCount;
                    obj.SupplierWarranty = objDetails.SupplierWarranty;
                    obj.MSLLevelNo = objDetails.MSLLevelNo;
                    obj.MSLLevelText = objDetails.MSLLevelText;
                    obj.IsTestingRecommended = objDetails.IsTestingRecommended;
                    obj.IsImageAvailable = objDetails.IsImageAvailable;
                    obj.PriorityNo = objDetails.PriorityNo;
                    obj.IHSCountryOfOriginNo = objDetails.IHSCountryOfOriginNo;
                    obj.IHSCountryOfOriginName = objDetails.IHSCountryOfOriginName;
                    obj.CountryOfOriginNo = objDetails.CountryOfOriginNo;
                    obj.CountryOfOriginName = objDetails.CountryOfOriginName;
                    obj.ReReleased = objDetails.ReReleased;

                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }


        /// <summary>
        /// Offer All Source
        /// Calls [usp_source_Offer_PQ_Trusted]
        /// </summary>
        public static List<Offer> SourceOfferAll(System.Int32? clientId, System.String partSearch, System.Int32? index, DateTime? maxDate, out DateTime? outDate, System.Boolean? blnReferesh, bool IsServerLocal)
        {
            DateTime? StartDate = null;
            DateTime? EndDate = null;
            if (index == 2 && maxDate.HasValue)
            {
                StartDate = (!blnReferesh.Value) ? maxDate.Value.AddMonths(-6) : maxDate.Value.AddMonths(-12);
                EndDate = maxDate.Value;
            }
            else if (index == 3 && maxDate.HasValue)
            {
                StartDate = DateTime.Parse("1900-01-01 00:00:00.000");
                EndDate = maxDate.Value;
            }

            List<OfferDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.Offer.SourceOfferAll(clientId, partSearch, index, StartDate, EndDate, out outDate, IsServerLocal);
            if (lstDetails == null)
            {
                return new List<Offer>();
            }
            else
            {
                List<Offer> lst = new List<Offer>();
                foreach (OfferDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.Offer obj = new Rebound.GlobalTrader.BLL.Offer();
                    obj.OfferId = objDetails.OfferId;
                    obj.FullPart = objDetails.FullPart;
                    obj.Part = objDetails.Part;
                    obj.ManufacturerNo = objDetails.ManufacturerNo;
                    obj.DateCode = objDetails.DateCode;
                    obj.ProductNo = objDetails.ProductNo;
                    obj.PackageNo = objDetails.PackageNo;
                    obj.Quantity = objDetails.Quantity;
                    obj.Price = objDetails.Price;
                    obj.OriginalEntryDate = objDetails.OriginalEntryDate;
                    obj.Salesman = objDetails.Salesman;
                    obj.SupplierNo = objDetails.SupplierNo;
                    obj.CurrencyNo = objDetails.CurrencyNo;
                    obj.ROHS = objDetails.ROHS;
                    obj.UpdatedBy = objDetails.UpdatedBy;
                    obj.DLUP = objDetails.DLUP;
                    obj.OfferStatusNo = objDetails.OfferStatusNo;
                    obj.OfferStatusChangeDate = objDetails.OfferStatusChangeDate;
                    obj.OfferStatusChangeLoginNo = objDetails.OfferStatusChangeLoginNo;
                    obj.ManufacturerCode = objDetails.ManufacturerCode;
                    obj.ProductName = objDetails.ProductName;
                    obj.CurrencyCode = objDetails.CurrencyCode;
                    obj.CurrencyDescription = objDetails.CurrencyDescription;
                    obj.SupplierName = objDetails.SupplierName;
                    obj.ManufacturerName = objDetails.ManufacturerName;
                    obj.SupplierEmail = objDetails.SupplierEmail;
                    obj.SalesmanName = objDetails.SalesmanName;
                    obj.OfferStatusChangeEmployeeName = objDetails.OfferStatusChangeEmployeeName;
                    obj.PackageName = objDetails.PackageName;
                    obj.Notes = objDetails.Notes;
                    obj.ClientNo = objDetails.ClientNo;
                    obj.ClientId = objDetails.ClientId;
                    obj.ClientName = objDetails.ClientName;
                    obj.ClientDataVisibleToOthers = objDetails.ClientDataVisibleToOthers;
                    //[001] code start
                    obj.SupplierType = objDetails.SupplierType;
                    //[001] code end
                    obj.ClientCode = objDetails.ClientCode;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }

        /// <summary>
        /// Source
        /// Calls [[usp_ipobom_source_Offer]]
        /// </summary>
        public static List<Offer> IPOBOMSource(System.Int32? clientId, System.String partSearch, System.Int32? index, DateTime? maxDate, out DateTime? outDate, System.Boolean? blnReferesh, bool IsServerLocal,System.Boolean? isPohub)
        {
            DateTime? StartDate = null;
            DateTime? EndDate = null;
            if (index == 2 && maxDate.HasValue)
            {
                StartDate = (!blnReferesh.Value) ? maxDate.Value.AddMonths(-6) : maxDate.Value.AddMonths(-12);
                EndDate = maxDate.Value;
            }
            else if (index == 3 && maxDate.HasValue)
            {
                StartDate = DateTime.Parse("1900-01-01 00:00:00.000");
                EndDate = maxDate.Value;
            }

            List<OfferDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.Offer.IPOBOMSource(clientId, partSearch, index, StartDate, EndDate, out outDate, IsServerLocal, isPohub);
            if (lstDetails == null)
            {
                return new List<Offer>();
            }
            else
            {
                List<Offer> lst = new List<Offer>();
                foreach (OfferDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.Offer obj = new Rebound.GlobalTrader.BLL.Offer();
                    obj.OfferId = objDetails.OfferId;
                    obj.FullPart = objDetails.FullPart;
                    obj.Part = objDetails.Part;
                    obj.ManufacturerNo = objDetails.ManufacturerNo;
                    obj.DateCode = objDetails.DateCode;
                    obj.ProductNo = objDetails.ProductNo;
                    obj.PackageNo = objDetails.PackageNo;
                    obj.Quantity = objDetails.Quantity;
                    obj.Price = objDetails.Price;
                    obj.OriginalEntryDate = objDetails.OriginalEntryDate;
                    obj.Salesman = objDetails.Salesman;
                    obj.SupplierNo = objDetails.SupplierNo;
                    obj.CurrencyNo = objDetails.CurrencyNo;
                    obj.ROHS = objDetails.ROHS;
                    obj.UpdatedBy = objDetails.UpdatedBy;
                    obj.DLUP = objDetails.DLUP;
                    obj.OfferStatusNo = objDetails.OfferStatusNo;
                    obj.OfferStatusChangeDate = objDetails.OfferStatusChangeDate;
                    obj.OfferStatusChangeLoginNo = objDetails.OfferStatusChangeLoginNo;
                    obj.ManufacturerCode = objDetails.ManufacturerCode;
                    obj.ProductName = objDetails.ProductName;
                    obj.CurrencyCode = objDetails.CurrencyCode;
                    obj.CurrencyDescription = objDetails.CurrencyDescription;
                    obj.SupplierName = objDetails.SupplierName;
                    obj.ManufacturerName = objDetails.ManufacturerName;
                    obj.SupplierEmail = objDetails.SupplierEmail;
                    obj.SalesmanName = objDetails.SalesmanName;
                    obj.OfferStatusChangeEmployeeName = objDetails.OfferStatusChangeEmployeeName;
                    obj.PackageName = objDetails.PackageName;
                    obj.Notes = objDetails.Notes;
                    obj.ClientNo = objDetails.ClientNo;
                    obj.ClientId = objDetails.ClientId;
                    obj.ClientName = objDetails.ClientName;
                    obj.ClientDataVisibleToOthers = objDetails.ClientDataVisibleToOthers;
                    //[001] code start
                    obj.SupplierType = objDetails.SupplierType;
                    //[001] code end
                    obj.ClientCode = objDetails.ClientCode;
                    obj.MSL = objDetails.MSL;
                    obj.SPQ = objDetails.SPQ;
                    obj.LeadTime = objDetails.LeadTime;
                    obj.RoHSStatus = objDetails.RoHSStatus;
                    obj.FactorySealed = objDetails.FactorySealed;
                    obj.IPOBOMNo = objDetails.IPOBOMNo;
                    obj.SupplierTotalQSA = objDetails.SupplierTotalQSA;
                    obj.SupplierLTB = objDetails.SupplierLTB;
                    obj.SupplierMOQ = objDetails.SupplierMOQ;
                    obj.IsSourcingHub = objDetails.IsSourcingHub;
                    obj.SupplierMessage = objDetails.SupplierMessage;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
	
            
        /// <summary>
		/// Update 
		/// Calls [usp_update_Offer]
		/// </summary>
        public static bool Update(System.Int32? offerId, System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? productNo, System.Int32? packageNo, System.Int32? quantity, System.Double? price, System.Int32? currencyNo, System.Int32? salesman, System.Int32? offerStatusNo, System.Int32? supplierNo, System.Byte? rohs, System.String notes, System.Int32? updatedBy, bool? isPoHub)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Offer.Update(offerId, part, manufacturerNo, dateCode, productNo, packageNo, quantity, price, currencyNo, salesman, offerStatusNo, supplierNo, rohs, notes, updatedBy, isPoHub);
		}

        /// <summary>
        /// Update 
        /// Calls [usp_update_AltPart]
        /// </summary>
        public static bool UpdateAltPart(System.Int32? AlternativePartId, System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? productNo, System.Int32? packageNo, System.Int32? quantity, System.Double? price, System.Int32? currencyNo, System.Int32? salesman, System.Int32? offerStatusNo, System.Int32? supplierNo, System.Byte? rohs, System.String notes, System.Int32? updatedBy, bool? isPoHub)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Offer.UpdateAltPart(AlternativePartId, part, manufacturerNo, dateCode, productNo, packageNo, quantity, price, currencyNo, salesman, offerStatusNo, supplierNo, rohs, notes, updatedBy, isPoHub);
        }

        /// <summary>
        /// Update Epo Sourcing
        /// Calls [usp_update_Epo]
        /// </summary>
        public static bool UpdateEpo(System.Int32? EpoId, System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? productNo, System.Int32? packageNo, System.Int32? quantity, System.Double? price, System.Int32? currencyNo, System.Int32? salesman, System.Int32? EpoStatusNo, System.Int32? supplierNo, System.Byte? rohs, System.String notes, System.Int32? updatedBy, bool? isPoHub)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Offer.UpdateEpo(EpoId, part, manufacturerNo, dateCode, productNo, packageNo, quantity, price, currencyNo, salesman, EpoStatusNo, supplierNo, rohs, notes, updatedBy, isPoHub);
        }

        /// <summary>
        /// Update
        /// Calls [usp_ipobom_update_Offer]
        /// </summary>
        public static bool IPOBOMUpdate(System.Int32? offerId, System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? productNo, System.Int32? packageNo, System.Int32? quantity, System.Double? price, System.Int32? currencyNo, System.Int32? salesman, System.Int32? offerStatusNo, System.Int32? supplierNo, System.Byte? rohs, System.String notes, System.Int32? updatedBy, bool? isPoHub, System.String supplierTotalQSA, System.String supplierMOQ, System.String supplierLTB, System.String MSL, System.String SPQ, System.String leadTime, System.String factorySealed, System.String rohsStatus,System.Int32? mslLevelNo)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Offer.IPOBOMUpdate(offerId, part, manufacturerNo, dateCode, productNo, packageNo, quantity, price, currencyNo, salesman, offerStatusNo, supplierNo, rohs, notes, updatedBy, isPoHub, supplierTotalQSA, supplierMOQ, supplierLTB, MSL, SPQ, leadTime, factorySealed, rohsStatus,mslLevelNo);
        }

        /// <summary>
        /// Update
        /// Calls [usp_update_AltPart]
        /// </summary>
        public static bool UpdateAltPartInfo(System.Int32? AltPartId, System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? productNo, System.Int32? packageNo, System.Int32? quantity, System.Double? price, System.Int32? currencyNo, System.Int32? salesman, System.Int32? offerStatusNo, System.Int32? supplierNo, System.Byte? rohs, System.String notes, System.Int32? updatedBy, bool? isPoHub, System.String supplierTotalQSA, System.String supplierMOQ, System.String supplierLTB, System.String MSL, System.String SPQ, System.String leadTime, System.String factorySealed, System.String rohsStatus, System.Int32? mslLevelNo)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Offer.UpdateAltPartInfo(AltPartId, part, manufacturerNo, dateCode, productNo, packageNo, quantity, price, currencyNo, salesman, offerStatusNo, supplierNo, rohs, notes, updatedBy, isPoHub, supplierTotalQSA, supplierMOQ, supplierLTB, MSL, SPQ, leadTime, factorySealed, rohsStatus, mslLevelNo);
        }

        /// <summary>
        /// Update Epo
        /// Calls [usp_ipobom_update_Epo]
        /// </summary>
        public static bool IPOBOMUpdateEpo(System.Int32? EpoId, System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? productNo, System.Int32? packageNo, System.Int32? quantity, System.Double? price, System.Int32? currencyNo, System.Int32? salesman, System.Int32? EpoStatusNo, System.Int32? supplierNo, System.Byte? rohs, System.String notes, System.Int32? updatedBy, bool? isPoHub, System.String supplierTotalQSA, System.String supplierMOQ, System.String supplierLTB, System.String MSL, System.String SPQ, System.String leadTime, System.String factorySealed, System.String rohsStatus, System.Int32? mslLevelNo, System.String Description, System.Double? VirtualCostPrice)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Offer.IPOBOMUpdateEpo(EpoId, part, manufacturerNo, dateCode, productNo, packageNo, quantity, price, currencyNo, salesman, EpoStatusNo, supplierNo, rohs, notes, updatedBy, isPoHub, supplierTotalQSA, supplierMOQ, supplierLTB, MSL, SPQ, leadTime, factorySealed, rohsStatus, mslLevelNo, Description, VirtualCostPrice);
        }

        /// <summary>
        /// Update Stock info list
        /// Calls [usp_HUBRFQ_update_Stock]
        /// </summary>
        //public static bool UpdateStockHUBRFQ(System.Int32? StockId, System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? productNo, System.Int32? packageNo, System.Int32? quantity, System.Double? price, System.Double? ClientUpliftPrice, System.Int32? salesman,  System.Int32? supplierNo, System.Byte? rohs, System.String notes, System.Int32? updatedBy, bool? isPoHub)
        //{
        //    return Rebound.GlobalTrader.DAL.SiteProvider.Offer.UpdateStockHUBRFQ(StockId, part, manufacturerNo, dateCode, productNo, packageNo, quantity, price, ClientUpliftPrice, salesman, supplierNo, rohs, notes, updatedBy, isPoHub);
        //}
        public static bool UpdateStockHUBRFQ(System.Int32? StockId, System.Double? price, System.Double? ClientUpliftPrice,   System.String notes, System.Int32? updatedBy, bool? isPoHub, System.Int32? supplierNo)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Offer.UpdateStockHUBRFQ(StockId,  price, ClientUpliftPrice,   notes, updatedBy, isPoHub, supplierNo);
        }


        /// <summary>
        /// Update ReverseLogistic //[003]
        /// Calls [usp_ipobom_update_ReverseLogistic]
        /// </summary>
        public static bool IPOBOMUpdateReverseLogistic(System.Int32? ReverseLogisticId, System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? productNo, System.Int32? packageNo, System.Int32? quantity, System.Double? price, System.Int32? currencyNo, System.Int32? salesman, System.Int32? ReverseLogisticStatusNo, System.Int32? supplierNo, System.Byte? rohs, System.String notes, System.Int32? updatedBy, bool? isPoHub, System.String supplierTotalQSA, System.String supplierMOQ, System.String supplierLTB, System.String MSL, System.String SPQ, System.String leadTime, System.String factorySealed, System.String rohsStatus, System.Int32? mslLevelNo, System.String Description)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Offer.IPOBOMUpdateReverseLogistic(ReverseLogisticId, part, manufacturerNo, dateCode, productNo, packageNo, quantity, price, currencyNo, salesman, ReverseLogisticStatusNo, supplierNo, rohs, notes, updatedBy, isPoHub, supplierTotalQSA, supplierMOQ, supplierLTB, MSL, SPQ, leadTime, factorySealed, rohsStatus, mslLevelNo, Description);
        }


        /// <summary>
        /// Update (without parameters)
        /// Calls [usp_update_Offer]
        /// </summary>
        public bool Update() {
            return Rebound.GlobalTrader.DAL.SiteProvider.Offer.Update(OfferId, Part, ManufacturerNo, DateCode, ProductNo, PackageNo, Quantity, Price, CurrencyNo, Salesman, OfferStatusNo, SupplierNo, ROHS, Notes, UpdatedBy, null);
		}
		/// <summary>
		/// UpdateForSourcing
		/// Calls [usp_update_Offer_for_sourcing]
		/// </summary>
		public static bool UpdateForSourcing(System.Int32? offerId, System.Int32? quantity, System.Double? price, System.String notes, System.Int32? updatedBy) {
			return Rebound.GlobalTrader.DAL.SiteProvider.Offer.UpdateForSourcing(offerId, quantity, price, notes, updatedBy);
		}
		/// <summary>
		/// UpdateOfferStatus
		/// Calls [usp_update_Offer_OfferStatus]
		/// </summary>
		public static bool UpdateOfferStatus(System.Int32? offerNo, System.Int32? offerStatusNo, System.Int32? updatedBy) {
			return Rebound.GlobalTrader.DAL.SiteProvider.Offer.UpdateOfferStatus(offerNo, offerStatusNo, updatedBy);
		}

        /// <summary>
        /// UpdateAltPartStatus
        /// Calls [usp_update_AltPartStatus]
        /// </summary>
        public static bool UpdateAltPartsStatus(System.Int32? AlternativePart, System.Int32? offerStatusNo, System.Int32? updatedBy)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Offer.UpdateAltPartsStatus(AlternativePart, offerStatusNo, updatedBy);
        }

        /// <summary>
		/// UpdateOfferStatus
		/// Calls [usp_update_Strategic_StrategicStatus]
		/// </summary>
		public static bool UpdateEPOStatus(System.Int32? offerNo, System.Int32? offerStatusNo, System.Int32? updatedBy)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Offer.UpdateEPOStatus(offerNo, offerStatusNo, updatedBy);
        }

        /// <summary>
		/// UpdateOfferStatus
		/// Calls [usp_update_ReverseLT_ReverseLTStatus]
		/// </summary>
		public static bool UpdateReverseLogisticsStatus(System.Int32? offerNo, System.Int32? offerStatusNo, System.Int32? updatedBy)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Offer.UpdateReverseLogisticsStatus(offerNo, offerStatusNo, updatedBy);
        }

        private static Offer PopulateFromDBDetailsObject(OfferDetails obj) {
            Offer objNew = new Offer();
			objNew.OfferId = obj.OfferId;
			objNew.FullPart = obj.FullPart;
			objNew.Part = obj.Part;
			objNew.ManufacturerNo = obj.ManufacturerNo;
			objNew.DateCode = obj.DateCode;
			objNew.ProductNo = obj.ProductNo;
			objNew.PackageNo = obj.PackageNo;
			objNew.Quantity = obj.Quantity;
			objNew.Price = obj.Price;
			objNew.OriginalEntryDate = obj.OriginalEntryDate;
			objNew.Salesman = obj.Salesman;
			objNew.SupplierNo = obj.SupplierNo;
			objNew.CurrencyNo = obj.CurrencyNo;
			objNew.ROHS = obj.ROHS;
			objNew.UpdatedBy = obj.UpdatedBy;
			objNew.DLUP = obj.DLUP;
			objNew.OfferStatusNo = obj.OfferStatusNo;
			objNew.OfferStatusChangeDate = obj.OfferStatusChangeDate;
			objNew.OfferStatusChangeLoginNo = obj.OfferStatusChangeLoginNo;
			objNew.SupplierName = obj.SupplierName;
			objNew.Notes = obj.Notes;
			objNew.ManufacturerName = obj.ManufacturerName;
			objNew.ProductName = obj.ProductName;
			objNew.PackageName = obj.PackageName;
			objNew.ClientNo = obj.ClientNo;
			objNew.ManufacturerCode = obj.ManufacturerCode;
			objNew.CurrencyCode = obj.CurrencyCode;
			objNew.CurrencyDescription = obj.CurrencyDescription;
			objNew.SupplierEmail = obj.SupplierEmail;
			objNew.SalesmanName = obj.SalesmanName;
			objNew.OfferStatusChangeEmployeeName = obj.OfferStatusChangeEmployeeName;
			objNew.ClientId = obj.ClientId;
			objNew.ClientName = obj.ClientName;
			objNew.ClientDataVisibleToOthers = obj.ClientDataVisibleToOthers;
            return objNew;
        }

        #endregion

        /// <summary>
        /// Source
        /// Calls [[usp_CrossMatch_Offer]]
        /// </summary>
        public static List<Offer> CrossMatch(System.Int32? clientId, System.Int32? pageIndex, System.Int32? pageSize, System.Int32? orderBy, System.String sortDir, System.String partSearch, System.String PartMatch, System.String months, System.Int32? monthTime, System.Int32? vendorNo, System.Int32? currencyNo, System.Boolean? isManufaurer, System.Int32? NoOfTopRecord, bool IsServerLocal, System.Boolean? isPohub, System.Int32? BomID, System.Boolean? IncludeAltPart, System.Int32? ReqId)
        {

            List<OfferDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.Offer.CrossMatch(clientId, pageIndex, pageSize, orderBy, sortDir, partSearch, PartMatch, months, monthTime, vendorNo, currencyNo, isManufaurer, NoOfTopRecord, IsServerLocal, isPohub, BomID,IncludeAltPart, ReqId);
            if (lstDetails == null)
            {
                return new List<Offer>();
            }
            else
            {
                List<Offer> lst = new List<Offer>();
                foreach (OfferDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.Offer obj = new Rebound.GlobalTrader.BLL.Offer();
                    obj.OfferId = objDetails.OfferId;
                    obj.FullPart = objDetails.FullPart;
                    obj.Part = objDetails.Part;
                    obj.ManufacturerNo = objDetails.ManufacturerNo;
                    obj.DateCode = objDetails.DateCode;
                    obj.ProductNo = objDetails.ProductNo;
                    obj.PackageNo = objDetails.PackageNo;
                    obj.Quantity = objDetails.Quantity;
                    obj.Price = objDetails.Price;
                    obj.OriginalEntryDate = objDetails.OriginalEntryDate;
                    obj.Salesman = objDetails.Salesman;
                    obj.SupplierNo = objDetails.SupplierNo;
                    obj.CurrencyNo = objDetails.CurrencyNo;
                    obj.ROHS = objDetails.ROHS;
                    obj.UpdatedBy = objDetails.UpdatedBy;
                    obj.DLUP = objDetails.DLUP;
                    obj.OfferStatusNo = objDetails.OfferStatusNo;
                    obj.OfferStatusChangeDate = objDetails.OfferStatusChangeDate;
                    obj.OfferStatusChangeLoginNo = objDetails.OfferStatusChangeLoginNo;
                    obj.ManufacturerCode = objDetails.ManufacturerCode;
                    obj.ProductName = objDetails.ProductName;
                    obj.CurrencyCode = objDetails.CurrencyCode;
                    obj.CurrencyDescription = objDetails.CurrencyDescription;
                    obj.SupplierName = objDetails.SupplierName;
                    obj.ManufacturerName = objDetails.ManufacturerName;
                    obj.SupplierEmail = objDetails.SupplierEmail;
                    obj.SalesmanName = objDetails.SalesmanName;
                    obj.OfferStatusChangeEmployeeName = objDetails.OfferStatusChangeEmployeeName;
                    obj.PackageName = objDetails.PackageName;
                    obj.Notes = objDetails.Notes;
                    obj.ClientNo = objDetails.ClientNo;
                    obj.ClientId = objDetails.ClientId;
                    obj.ClientName = objDetails.ClientName;
                    obj.ClientDataVisibleToOthers = objDetails.ClientDataVisibleToOthers;
                    //[001] code start
                    obj.SupplierType = objDetails.SupplierType;
                    //[001] code end
                    obj.ClientCode = objDetails.ClientCode;
                    obj.MSL = objDetails.MSL;
                    obj.SPQ = objDetails.SPQ;
                    obj.LeadTime = objDetails.LeadTime;
                    obj.RoHSStatus = objDetails.RoHSStatus;
                    obj.FactorySealed = objDetails.FactorySealed;
                    obj.IPOBOMNo = objDetails.IPOBOMNo;
                    obj.SupplierTotalQSA = objDetails.SupplierTotalQSA;
                    obj.SupplierLTB = objDetails.SupplierLTB;
                    obj.SupplierMOQ = objDetails.SupplierMOQ;
                    obj.IsSourcingHub = objDetails.IsSourcingHub;
                    obj.isIncludeAltPart = objDetails.isIncludeAltPart;
                    obj.RowNum = objDetails.RowNum;
                    obj.TotalCount = objDetails.TotalCount;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        public static DataTable GetPartMatchRequirementDetails(System.Int32? clientId, System.Int32? OfferId)
        {
            DataTable dtMatchedReq = Rebound.GlobalTrader.DAL.SiteProvider.Offer.GetPartMatchRequirementDetails(clientId, OfferId);
            return dtMatchedReq;

        }
        //public static void AddSourcingResultsforMatchedRequirements(System.Int32? OfferId, System.Int32? ClientId, DataTable dtREquirement)
        //{
        //    Rebound.GlobalTrader.DAL.SiteProvider.Offer.AddSourcingResultsforMatchedRequirements(OfferId, ClientId, dtREquirement);


        //}

        /// Update ReverseLogistic Bulk record //[003]
        /// Calls [usp_ipobom_update_ReverseLogisticBulk]
        /// </summary>
        public static bool IPOBOMUpdateReverseLogisticBulk(System.String ReverseLogisticIds, bool? isBulk, System.Int32? updatedBy/*,bool? isPoHub*/)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Offer.IPOBOMUpdateReverseLogisticBulk(ReverseLogisticIds, isBulk, updatedBy/*,isPoHub*/);
        }
        /// <summary>
        /// Source
        /// Calls [usp_source_AltParts]
        /// </summary>
        public static List<Offer> SourceAltParts(System.Int32? clientId, System.String partSearch, System.Int32? index, DateTime? maxDate, out DateTime? outDate, System.Boolean? blnReferesh, bool IsServerLocal, System.Boolean? IsPOHub, System.Int32? sortIndex, System.Int32? sortDirection)
        {
            DateTime? StartDate = null;
            DateTime? EndDate = null;
            if (index == 2 && maxDate.HasValue)
            {
                StartDate = (!blnReferesh.Value) ? maxDate.Value.AddMonths(-6) : maxDate.Value.AddMonths(-12);
                EndDate = maxDate.Value;
            }
            else if (index == 3 && maxDate.HasValue)
            {
                StartDate = DateTime.Parse("1900-01-01 00:00:00.000");
                EndDate = maxDate.Value;
            }

            List<OfferDetails> lstDetails = (IsPOHub.Value)
                ? Rebound.GlobalTrader.DAL.SiteProvider.Offer.SourceAltPartsArchive(clientId, partSearch, index, StartDate, EndDate, out outDate, IsServerLocal, IsPOHub, sortIndex, sortDirection)
                : Rebound.GlobalTrader.DAL.SiteProvider.Offer.SourceAltParts(clientId, partSearch, index, StartDate, EndDate, out outDate, IsServerLocal, IsPOHub, sortIndex, sortDirection);
            if (lstDetails == null)
            {
                return new List<Offer>();
            }
            else
            {
                List<Offer> lst = new List<Offer>();
                foreach (OfferDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.Offer obj = new Rebound.GlobalTrader.BLL.Offer();
                    obj.OfferId = objDetails.OfferId;
                    obj.FullPart = objDetails.FullPart;
                    obj.Part = objDetails.Part;
                    obj.ManufacturerNo = objDetails.ManufacturerNo;
                    obj.DateCode = objDetails.DateCode;
                    obj.ProductNo = objDetails.ProductNo;
                    obj.PackageNo = objDetails.PackageNo;
                    obj.Quantity = objDetails.Quantity;
                    obj.Price = objDetails.Price;
                    obj.OriginalEntryDate = objDetails.OriginalEntryDate;
                    obj.Salesman = objDetails.Salesman;
                    obj.SupplierNo = objDetails.SupplierNo;
                    obj.CurrencyNo = objDetails.CurrencyNo;
                    obj.ROHS = objDetails.ROHS;
                    obj.UpdatedBy = objDetails.UpdatedBy;
                    obj.DLUP = objDetails.DLUP;
                    obj.OfferStatusNo = objDetails.OfferStatusNo;
                    obj.OfferStatusChangeDate = objDetails.OfferStatusChangeDate;
                    obj.OfferStatusChangeLoginNo = objDetails.OfferStatusChangeLoginNo;
                    obj.ManufacturerCode = objDetails.ManufacturerCode;
                    obj.ProductName = objDetails.ProductName;
                    obj.CurrencyCode = objDetails.CurrencyCode;
                    obj.CurrencyDescription = objDetails.CurrencyDescription;
                    obj.SupplierName = objDetails.SupplierName;
                    obj.ManufacturerName = objDetails.ManufacturerName;
                    obj.SupplierEmail = objDetails.SupplierEmail;
                    obj.SalesmanName = objDetails.SalesmanName;
                    obj.OfferStatusChangeEmployeeName = objDetails.OfferStatusChangeEmployeeName;
                    obj.PackageName = objDetails.PackageName;
                    obj.Notes = objDetails.Notes;
                    obj.ClientNo = objDetails.ClientNo;
                    obj.ClientId = objDetails.ClientId;
                    obj.ClientName = objDetails.ClientName;
                    obj.ClientDataVisibleToOthers = objDetails.ClientDataVisibleToOthers;
                    //[001] code start
                    obj.SupplierType = objDetails.SupplierType;
                    //[001] code end
                    obj.ClientCode = objDetails.ClientCode;
                    obj.IsSourcingHub = objDetails.IsSourcingHub;
                    obj.SupplierMessage = objDetails.SupplierMessage;

                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        

        public static List<Offer> POHubAutoSourcing(int customerRequirementId, int sortIndex, int sortDir, int tableLength, int clientNo, int loginNo, out string ihsResult, out string lyticaResult)
        {
            List<OfferDetails> lstDetails = SiteProvider.Offer.POHubAutoSourcing(customerRequirementId, sortIndex, sortDir, tableLength, clientNo, loginNo, out ihsResult, out lyticaResult);
            if (lstDetails == null)
            {
                return new List<Offer>();
            }
            List<Offer> lst = new List<Offer>();
            foreach (OfferDetails objDetails in lstDetails)
            {
                Offer obj = new Offer
                {
                    OfferId = objDetails.OfferId,
                    ClientNo = objDetails.ClientNo,
                    Part = objDetails.Part,
                    ManufacturerNo = objDetails.ManufacturerNo,
                    ManufacturerCode = objDetails.ManufacturerCode,
                    ManufacturerName = objDetails.ManufacturerName,
                    ProductNo = objDetails.ProductNo,
                    ProductName = objDetails.ProductName,
                    ProductDescription = objDetails.ProductDescription,
                    SupplierName = objDetails.SupplierName,
                    OriginalEntryDate = objDetails.OriginalEntryDate,
                    Quantity = objDetails.Quantity,
                    Price = objDetails.Price,
                    UpliftPrice = objDetails.UpliftPrice,
                    CurrencyCode = objDetails.CurrencyCode,
                    DivisionStatus = objDetails.DivisionStatus,
                    IsRestrictMfr = objDetails.IsRestrictMfr,
                    IsSourcingHub = objDetails.IsSourcingHub,
                    ROHS = objDetails.ROHS,
                    SourcingType = objDetails.SourcingType,
                    SourcingTypeDescription = objDetails.SourcingTypeDescription,
                    SourcingResultId = objDetails.SourcingResultId,
                    AllowRemoveOffer = objDetails.AllowRemoveOffer
                };
                lst.Add(obj);
            }
            return lst;
        }

        public static bool POHubBulkUpdateEpo(string epoIds, string action, int? loginNo)
        {
            return SiteProvider.Offer.POHubBulkUpdateEpo(epoIds, action, loginNo);
        }
    }
}
