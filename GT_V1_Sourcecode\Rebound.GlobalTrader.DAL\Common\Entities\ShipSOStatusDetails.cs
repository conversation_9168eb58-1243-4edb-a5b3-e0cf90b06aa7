﻿using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

namespace Rebound.GlobalTrader.DAL
{
    public class ShipSOStatusDetails
    {
        #region Constructors

        public ShipSOStatusDetails() { }

        #endregion

        #region Properties

        /// <summary>
        /// StatusId
        /// </summary>
        public System.Int32 StatusId { get; set; }


        /// <summary>
        /// StatusName
        /// </summary>
        public System.String StatusName { get; set; }

        #endregion
    }
}
