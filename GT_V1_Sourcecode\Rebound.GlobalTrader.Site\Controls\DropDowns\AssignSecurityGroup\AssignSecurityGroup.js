Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.AssignSecurityGroup=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.AssignSecurityGroup.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DropDowns.AssignSecurityGroup.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.DropDowns.AssignSecurityGroup.callBaseMethod(this,"initialize");this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK))},dispose:function(){this.isDisposed||Rebound.GlobalTrader.Site.Controls.DropDowns.AssignSecurityGroup.callBaseMethod(this,"dispose")},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/AssignSecurityGroup");this._objData.set_DataObject("AssignSecurityGroup");this._objData.set_DataAction("GetData")},dataCallOK:function(){var t=this._objData._result,n;if(t.Types)for(n=0;n<t.Types.length;n++)this.addOption(t.Types[n].Name,t.Types[n].ID)}};Rebound.GlobalTrader.Site.Controls.DropDowns.AssignSecurityGroup.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.AssignSecurityGroup",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);