﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="ActiveVendors" xml:space="preserve">
    <value>Verzeichnet alle Verkäufer, die zwischen den spezifizierten Daten aktiv sind</value>
  </data>
  <data name="ApprovedCustomersOnStop" xml:space="preserve">
    <value>Verzeichnet alle Kunden z.Z. auf Anschlag.</value>
  </data>
  <data name="AutoEnteredSuppliers_Unedited" xml:space="preserve">
    <value>Verzeichnet alle Lieferanten durch Datumstrecke, die dem System als neue Firmen hinzugefügt worden sind, wenn Informationen aus auf lager Scheiben und eMail importiert worden sind.</value>
  </data>
  <data name="ClosedRequirementsReasons" xml:space="preserve">
    <value>Verzeichnet die Zahl den Anforderungen, die für jeden Grund geschlossen werden.</value>
  </data>
  <data name="CommunicationLogActivityforaUser" xml:space="preserve">
    <value>Verzeichnet alle Kommunikationsmaschinenbordbuchtätigkeiten für die vorgewählte Benutzer- und Datumstrecke.</value>
  </data>
  <data name="CompaniesApprovedToPurchaseFrom" xml:space="preserve">
    <value>Verzeichnet alle Lieferanten, denen genehmigt worden sind, um zu kaufen von und ihre verbundenen Verkäufernamen.</value>
  </data>
  <data name="CompaniesNotContacted" xml:space="preserve">
    <value>Verzeichnet alle nicht in Verbindung getretenen Firmen da das vorgewählte Stichdatum.</value>
  </data>
  <data name="ContactEmailList" xml:space="preserve">
    <value>Verzeichnet alle Kontaktemail address durch Land.</value>
  </data>
  <data name="ContactsNotContacted" xml:space="preserve">
    <value>Verzeichnet alle nicht in Verbindung getretenen Firmakontakte da das vorgewählte Stichdatum.</value>
  </data>
  <data name="CreditNotes" xml:space="preserve">
    <value>Alle Kreditnoten für Einzelteile gingen innerhalb eines Bereiches des spezifizierten Datums zurück</value>
  </data>
  <data name="CreditNotesforaCustomer" xml:space="preserve">
    <value>Alle Kreditnoten für einen Kunden für Einzelteile gingen innerhalb eines Bereiches des spezifizierten Datums zurück</value>
  </data>
  <data name="CreditNotesforaSalesperson" xml:space="preserve">
    <value>Alle Kreditnoten für einen Verkäufer für Einzelteile gingen innerhalb eines Bereiches des spezifizierten Datums zurück</value>
  </data>
  <data name="CustomerListforSalesperson" xml:space="preserve">
    <value>Verzeichnet Kunden für einen Verkäufer</value>
  </data>
  <data name="CustomerOnTimeDeliveryReport" xml:space="preserve">
    <value>Verzeichnet Anlieferungsstatistiken durch Kunden innerhalb des Bereiches des spezifizierten Datums. Aufträge werden rechtzeitig betrachtet, wenn sie auf oder vor dem promissed Datum versendet werden.</value>
  </data>
  <data name="CustomerStatement" xml:space="preserve">
    <value>Verzeichnet alle ausstehenden Rechnungen für den vorgewählten Kunden.</value>
  </data>
  <data name="DailyCustomerRequirementsbySalesperson_Detailed" xml:space="preserve">
    <value>Verzeichnet die eingeführten Kundenanforderungen jeden Tag vom Verkäufer</value>
  </data>
  <data name="DailyCustomerRequirementsbySalesperson_Summary" xml:space="preserve">
    <value>Verzeichnet die Zahl den Kundenanforderungen, die täglich für jeden Kunden vom Verkäufer eingeführt werden</value>
  </data>
  <data name="DailyCustomerRequirementsbySalesperson_Totals" xml:space="preserve">
    <value>Verzeichnet die Zahl den Kundenanforderungen, die täglich vom Verkäufer eingeführt werden</value>
  </data>
  <data name="DailyImports" xml:space="preserve">
    <value>Listendetails durch Datumstrecke der Vorrat Angebote und der reqs von den Lieferanten.</value>
  </data>
  <data name="DailyImportsBySource" xml:space="preserve">
    <value>Listendetails durch Datumstrecke der Vorrat Angebote und der reqs vom Lieferanten aufgegliedert durch die Quelle der Informationen.</value>
  </data>
  <data name="DaysSinceLastInvoicebyContact" xml:space="preserve">
    <value>Verzeichnet die Zahl Tagen, da ein Kontakt fakturiert worden ist.</value>
  </data>
  <data name="DaysSinceLastInvoicebyCustomer" xml:space="preserve">
    <value>Verzeichnet die Zahl Tagen, da ein Kunde fakturiert worden ist.</value>
  </data>
  <data name="GoodsReceived" xml:space="preserve">
    <value>Eine Zusammenfassung des Vorrates empfangen durch Datumstrecke</value>
  </data>
  <data name="GoodsReceivedNotInvoiced" xml:space="preserve">
    <value>Eine Zusammenfassung des Vorrates empfangen aber nicht durch Datumstrecke fakturiert</value>
  </data>
  <data name="GoodsReceivedShipmentDetails" xml:space="preserve">
    <value>Eine Zusammenfassung des Vorrates empfangen mit Versanddetails durch Datumstrecke.</value>
  </data>
  <data name="GrossProfitBreakdown" xml:space="preserve">
    <value>Gliedert versendete Aufträge innerhalb eines Bereiches des spezifizierten Datums durch Bruttogewinnstrecken auf.</value>
  </data>
  <data name="IntrastatReportforEECArrivals_CustomerRMAs" xml:space="preserve">
    <value>Verzeichnen Sie alle Gebrauchsgüter, die von den EWG-Ländern empfangen werden, ausschließlich deren versendet innerhalb des Landes des Empfangs.</value>
  </data>
  <data name="IntrastatReportforEECArrivals_Purchases" xml:space="preserve">
    <value>Verzeichnen Sie alle Gebrauchsgüter, die von den EWG-Ländern empfangen, ausschließlich deren versendet innerhalb des Landes des Empfangs.</value>
  </data>
  <data name="IntrastatReportforEECDispatches_Sales" xml:space="preserve">
    <value>Verzeichnen Sie alle Gebrauchsgüter, die zu den EWG-Ländern versendet werden, ausschließlich deren versendet innerhalb des Landes der Abfertigung.</value>
  </data>
  <data name="IntrastatReportforEECDispatches_SupplierRMAs" xml:space="preserve">
    <value>Verzeichnen Sie alle Gebrauchsgüter, die zu den EWG-Ländern versendet werden, ausschließlich deren versendet innerhalb des Landes der Abfertigung.</value>
  </data>
  <data name="InventoryLocationReport" xml:space="preserve">
    <value>Alle Warenbestandeinzelteile bestellt durch Position</value>
  </data>
  <data name="InventoryLocationReportforLot" xml:space="preserve">
    <value>Alle Warenbestandeinzelteile für ein vorgewähltes Los bestellt durch Position.</value>
  </data>
  <data name="InvoicesSortedbyInvoiceNumber" xml:space="preserve">
    <value>Verzeichnet alle Rechnungslinie Einzelteile innerhalb eines Bereiches des spezifizierten Datums, der durch Rechnungszahl, mit Bruttogewinnberechnungen bestellt wird.</value>
  </data>
  <data name="InvoicesSortedbyInvoiceNumberforaCustomer" xml:space="preserve">
    <value>Verzeichnet alle Rechnungslinie Einzelteile für den vorgewählten Kunden innerhalb eines Bereiches des spezifizierten Datums, der durch Rechnungszahl, mit Bruttogewinnberechnungen bestellt wird.</value>
  </data>
  <data name="InvoicesSortedbyInvoiceNumberforaSalesperson" xml:space="preserve">
    <value>Verzeichnet alle Rechnungslinie Einzelteile für den vorgewählten Verkäufer innerhalb eines Bereiches des spezifizierten Datums, der durch Rechnungszahl, mit Bruttogewinnberechnungen bestellt wird.</value>
  </data>
  <data name="LoginStatistics" xml:space="preserve">
    <value>Listenzahl der Zeitbenutzer haben zwischen Strecken des spezifizierten Datums angemeldet.</value>
  </data>
  <data name="LoginStatisticsbyName" xml:space="preserve">
    <value>Listenzahl der spezifischen Benutzer der Zeiten haben zwischen Strecken des spezifizierten Datums angemeldet.</value>
  </data>
  <data name="NumberofAccountsbySalesperson" xml:space="preserve">
    <value>Verzeichnet die Zahl den Konten, die mit einer Verkaufsperson verbunden werden.</value>
  </data>
  <data name="NumberofOffersbyVendor" xml:space="preserve">
    <value>Verzeichnet die Zahl Angeboten für jeden Verkäufer.</value>
  </data>
  <data name="NumberofOffersHistorybyVendor" xml:space="preserve">
    <value>Verzeichnet die Zahl historischen Angeboten für jeden Verkäufer.</value>
  </data>
  <data name="NumberofRequirementsbyVendor" xml:space="preserve">
    <value>Verzeichnet die Zahl Anforderungen für jeden Verkäufer.</value>
  </data>
  <data name="OpenCustomerRMAs" xml:space="preserve">
    <value>Verzeichnet ganz geöffneten Kunden RMAs mit Kostenberechnunginformationen, wo vorhanden.</value>
  </data>
  <data name="OpenCustomerRMAsforaCustomer" xml:space="preserve">
    <value>Verzeichnet ganz geöffneten Kunden RMAs für den vorgewählten Kunden mit Kostenberechnunginformationen, wo vorhanden.</value>
  </data>
  <data name="OpenCustomerRMAsforaCustomerwithReasons" xml:space="preserve">
    <value>Verzeichnet ganz geöffneten Kunden RMAs mit Gründen für Rückkehr und für den vorgewählten Kunden mit Kostenberechnunginformationen, wo vorhanden.</value>
  </data>
  <data name="OpenCustomerRMAsforaSaleperson" xml:space="preserve">
    <value>Verzeichnet ganz geöffneten Kunden RMAs für den vorgewählten Verkäufer mit Kostenberechnunginformationen, wo vorhanden.</value>
  </data>
  <data name="OpenCustomerRMAsforaSalepersonwithReasons" xml:space="preserve">
    <value>Verzeichnet ganz geöffneten Kunden RMAs mit Gründen für Rückkehr und für den vorgewählten Verkäufer mit Kostenberechnunginformationen, wo vorhanden.</value>
  </data>
  <data name="OpenCustomerRMAswithReasons" xml:space="preserve">
    <value>Verzeichnet ganz geöffneten Kunden RMAs mit Gründen zu Rückkehr- und Kostenberechnunginformation, wo vorhanden.</value>
  </data>
  <data name="OpenRequirementsbyCustomer" xml:space="preserve">
    <value>Verzeichnet Gesamtzahl der geöffneten reqs gegen jeden Kunden</value>
  </data>
  <data name="OpenRequirementsReportBySalesperson" xml:space="preserve">
    <value>Zeigt ganz geöffnete Anforderungen für den vorgewählten Verkäufer</value>
  </data>
  <data name="OpenSalesOrders" xml:space="preserve">
    <value>Verzeichnet ganz geöffnete Verkaufsaufträge mit Berechnungen der Verteilungsinformationen und des Bruttogewinns.</value>
  </data>
  <data name="OpenSalesOrdersforSalesperson" xml:space="preserve">
    <value>Verzeichnet ganz geöffnete Verkaufsaufträge für den spezifizierten Verkäufer mit Berechnungen der Verteilungsinformationen und des Bruttogewinns.</value>
  </data>
  <data name="OpenSupplierRMAs" xml:space="preserve">
    <value>Verzeichnet ganz geöffneten Lieferanten RMAs.</value>
  </data>
  <data name="OpenSupplierRMAsforaBuyer" xml:space="preserve">
    <value>Verzeichnet ganz geöffneten Lieferanten RMAs für den vorgewählten Kunden.</value>
  </data>
  <data name="OpenSupplierRMAsforaBuyerwithReasons" xml:space="preserve">
    <value>Verzeichnet ganz geöffneten Lieferanten RMAs mit Gründen für Rückkehr für den vorgewählten Kaunfer.</value>
  </data>
  <data name="OpenSupplierRMAsforaSupplier" xml:space="preserve">
    <value>Verzeichnet ganz geöffneten Lieferanten RMAs für den vorgewählten Lieferanten.</value>
  </data>
  <data name="OpenSupplierRMAsforaSupplierwithReasons" xml:space="preserve">
    <value>Verzeichnet ganz geöffneten Lieferanten RMAs mit Gründen für Rückkehr für den vorgewählten Lieferanten. </value>
  </data>
  <data name="OpenSupplierRMAswithReasons" xml:space="preserve">
    <value>Verzeichnet ganz geöffneten Lieferanten RMAs mit Gründen für Rückkehr.</value>
  </data>
  <data name="OrdersToBeShipped" xml:space="preserve">
    <value>Alle hervorragenden herauf bis das vorgewählte Datum zu versendenden Aufträge</value>
  </data>
  <data name="OrdersToBeShippedBySalesperson" xml:space="preserve">
    <value>Alle hervorragenden herauf bis das vorgewählte Datum zu versendenden Aufträge vom Verkäufer</value>
  </data>
  <data name="OustandingCustomerInvoices" xml:space="preserve">
    <value>Verzeichnet alle ausstehenden Rechnungen.</value>
  </data>
  <data name="PickSheetSalesOrdersBasic" xml:space="preserve">
    <value>Verzeichnet alle passenden und bereit Verkaufsaufträge zu versenden.</value>
  </data>
  <data name="PickSheetSalesOrdersDetailed" xml:space="preserve">
    <value>Verzeichnet alle passenden und bereit Verkaufsaufträge, mit zusätzlichen Details (Partienummer, Weiterverkaufwerte, PO #, usw.) zu versenden.</value>
  </data>
  <data name="PickSheetSupplierRMAs" xml:space="preserve">
    <value>Verzeichnet allen Lieferanten passendes und bereit RMAs zu versenden</value>
  </data>
  <data name="PurchaseOrdersDueIn" xml:space="preserve">
    <value>Alle hervorragenden informierten Kaufaufträge passend innen innerhalb des vorgewählten Datumbereiches.</value>
  </data>
  <data name="PurchaseOrdersDueInforBuyer" xml:space="preserve">
    <value>Alle hervorragenden informierten Kaufaufträge passend innen für einen Kunden innerhalb des vorgewählten Datumbereiches</value>
  </data>
  <data name="PurchaseOrdersDueInforSalesperson" xml:space="preserve">
    <value>Alle hervorragenden informierten Kaufaufträge passend innen für einen Verkäufer innerhalb des vorgewählten Datumbereiches.</value>
  </data>
  <data name="PurchaseRequisitions" xml:space="preserve">
    <value>List all posted sales order items that are not fully allocated.</value>
  </data>
  <data name="PurchaseRequisitionsforaCustomer" xml:space="preserve">
    <value>Verzeichnen Sie alle informierten Verkaufsauftragseinzelteile für einen Kunden, die nicht völlig zugeteilt.</value>
  </data>
  <data name="PurchaseRequisitionsforaSalesPerson" xml:space="preserve">
    <value>Verzeichnen Sie alle informierten Verkaufsauftragseinzelteile für eine Verkaufsperson, die nicht völlig zugeteilt werden.</value>
  </data>
  <data name="ReceivedCustomerRMAs" xml:space="preserve">
    <value>Verzeichnet allen empfangenen Kunden RMAs mit Kostenberechnunginformationen, wo vorhanden.</value>
  </data>
  <data name="ReceivedCustomerRMAsforaCustomer" xml:space="preserve">
    <value>Verzeichnet allen empfangenen Kunden RMAs für den vorgewählten Kunden mit Kostenberechnunginformationen, wo vorhanden.</value>
  </data>
  <data name="ReceivedCustomerRMAsforaCustomerwithReasons" xml:space="preserve">
    <value>Verzeichnet allen empfangenen Kunden RMAs mit Gründen für Rückkehr für den vorgewählten Kunden mit Kostenberechnunginformationen, wo vorhanden.</value>
  </data>
  <data name="ReceivedCustomerRMAsforaSaleperson" xml:space="preserve">
    <value>Verzeichnet allen empfangenen Kunden RMAs für den vorgewählten Verkäufer mit Kostenberechnunginformationen, wo vorhanden.</value>
  </data>
  <data name="ReceivedCustomerRMAsforaSalepersonwithReasons" xml:space="preserve">
    <value>Verzeichnet allen empfangenen Kunden RMAs mit Gründen für Rückkehr für den vorgewählten Verkäufer mit Kostenberechnunginformationen, wo vorhanden.</value>
  </data>
  <data name="ReceivedCustomerRMAswithReasons" xml:space="preserve">
    <value>Verzeichnet allen empfangenen Kunden RMAs mit Gründen zu Rückkehr- und Kostenberechnunginformation, wo vorhanden.</value>
  </data>
  <data name="ReceivedGoodsValuationbyCountry" xml:space="preserve">
    <value>Verzeichnet den Wert des Vorrates empfangen durch Land innerhalb des Bereiches des spezifizierten Datums.</value>
  </data>
  <data name="ShippedGoodsValuationbyCountry" xml:space="preserve">
    <value>Verzeichnet den Wert des Vorrates versendet durch Land innerhalb des Bereiches des spezifizierten Datums.</value>
  </data>
  <data name="ShippedOrdersSortedbyInvoiceNumber" xml:space="preserve">
    <value>Verzeichnet alle versendeten Aufträge innerhalb eines Bereiches des spezifizierten Datums, der durch die Rechnungszahl bestellt wird, die durch Los aufgegliedert wird (wenn anwendbar).</value>
  </data>
  <data name="ShippedOrdersSortedbyInvoiceNumberforaCustomer" xml:space="preserve">
    <value>Verzeichnet alle versendeten Aufträge für den vorgewählten Kunden innerhalb eines Bereiches des spezifizierten Datums, der durch die Rechnungszahl bestellt wird, die durch Los aufgegliedert wird (wenn anwendbar).</value>
  </data>
  <data name="ShippedOrdersSortedbyInvoiceNumberforaSalesperson" xml:space="preserve">
    <value>Verzeichnet alle versendeten Aufträge für den vorgewählten Verkäufer innerhalb eines Bereiches des spezifizierten Datums, der durch die Rechnungszahl bestellt wird, die durch Los aufgegliedert wird (wenn anwendbar).</value>
  </data>
  <data name="ShippedSalesforLot" xml:space="preserve">
    <value>Zeigt Details der versendeten Linie Einzelteile für viel innerhalb eines Bereiches des spezifizierten Datums.</value>
  </data>
  <data name="ShippedSupplierRMAs" xml:space="preserve">
    <value>Verzeichnet allen versendeten Lieferanten RMAs.</value>
  </data>
  <data name="ShippedSupplierRMAsforaBuyer" xml:space="preserve">
    <value>Verzeichnet allen versendeten Lieferanten RMAs für den vorgewählten Kaunfer.</value>
  </data>
  <data name="ShippedSupplierRMAsforaBuyerwithReasons" xml:space="preserve">
    <value>Verzeichnet allen versendeten Lieferanten RMAs mit Gründen für Rückkehr für den vorgewählten Kaunfer.</value>
  </data>
  <data name="ShippedSupplierRMAsforaSupplier" xml:space="preserve">
    <value>Verzeichnet allen versendeten Lieferanten RMAs für den vorgewählten Lieferanten.</value>
  </data>
  <data name="ShippedSupplierRMAsforaSupplierwithReasons" xml:space="preserve">
    <value>Verzeichnet allen versendeten Lieferanten RMAs mit Gründen für Rückkehr für den vorgewählten Lieferanten.</value>
  </data>
  <data name="ShippedSupplierRMAswithReasons" xml:space="preserve">
    <value>Verzeichnet allen versendeten Lieferanten RMAs mit Gründen für Rückkehr.</value>
  </data>
  <data name="StockCount" xml:space="preserve">
    <value>Verzeichnet allen Vorrat für ein Lager durch Position mit Extraspalten für körperliche Zählimpulse und Anmerkungen manuell eintragen.</value>
  </data>
  <data name="StockList" xml:space="preserve">
    <value>Verzeichnet allen Vorrat mit der Fähigkeit, Einzelteile auf Auftrag und/oder zugeteilte Einzelteile und/oder Einzelteil von den Losen herauszufiltern, die auf Einfluss sind.</value>
  </data>
  <data name="StockValuation" xml:space="preserve">
    <value>Alle auf lagereinzelteile bestellt durch Gesamtwert</value>
  </data>
  <data name="SummaryBookedOrdersbyCustomer" xml:space="preserve">
    <value>Zeigen Sie Gesamtkosten, Werte, Bruttogewinn, Seitenrand für Verkaufsaufträge durch den Kunden, der innerhalb eines Bereiches des spezifizierten Datums angemeldet wird.</value>
  </data>
  <data name="SummaryBookedOrdersbyDivision" xml:space="preserve">
    <value>Zeigen Sie Gesamtkosten, Werte, Bruttogewinn, Seitenrand für Verkaufsaufträge durch die Abteilung, die innerhalb eines Bereiches des spezifizierten Datums angemeldet wird.</value>
  </data>
  <data name="SummaryBookedOrdersbySalesperson" xml:space="preserve">
    <value>Zeigen Sie Gesamtkosten, Werte, Bruttogewinn, Seitenrand für Verkaufsaufträge durch den Verkäufer, der innerhalb eines Bereiches des spezifizierten Datums angemeldet wird.</value>
  </data>
  <data name="SummaryOpenOrdersbyCustomer" xml:space="preserve">
    <value>Zeigen Sie Gesamtkosten, Werte, Bruttogewinn, Seitenrand für geöffnete Aufträge durch Kunden für eine Strecke des spezifizierten Datums.</value>
  </data>
  <data name="SummaryOpenOrdersbyDivision" xml:space="preserve">
    <value>Zeigen Sie Gesamtkosten, Werte, Bruttogewinn, Seitenrand für geöffnete Aufträge durch Abteilung für eine Strecke des spezifizierten Datums.</value>
  </data>
  <data name="SummaryOpenOrdersbySalesperson" xml:space="preserve">
    <value>Zeigen Sie Gesamtkosten, Werte, Bruttogewinn, Seitenrand für geöffnete Aufträge durch Verkäufer für eine Strecke des spezifizierten Datums.</value>
  </data>
  <data name="SummaryShippedSalesbyCustomer" xml:space="preserve">
    <value>Zeigen Sie Gesamtkosten, Werte, Bruttogewinn, Seitenrand für versendete Aufträge durch Kunden für eine Strecke des spezifizierten Datums.</value>
  </data>
  <data name="SummaryShippedSalesbyDivision" xml:space="preserve">
    <value>Zeigen Sie Gesamtkosten, Werte, Bruttogewinn, Seitenrand für versendete Aufträge durch Abteilung für eine Strecke des spezifizierten Datums.</value>
  </data>
  <data name="SummaryShippedSalesbySalesperson" xml:space="preserve">
    <value>Zeigen Sie Gesamtkosten, Werte, Bruttogewinn, Seitenrand für versendete Aufträge durch Verkäufer für eine Strecke des spezifizierten Datums.</value>
  </data>
  <data name="SupplierOnTimeDeliveryReport" xml:space="preserve">
    <value>Verzeichnet Anlieferungsstatistiken durch Lieferanten innerhalb des Bereiches des spezifizierten Datums. Aufträge werden rechtzeitig betrachtet, wenn sie auf oder vor der Lieferfrist am Kaufauftrag empfangen werden.</value>
  </data>
  <data name="UserList" xml:space="preserve">
    <value>Verzeichnet Sonderkommandos aller Benutzer.</value>
  </data>
</root>