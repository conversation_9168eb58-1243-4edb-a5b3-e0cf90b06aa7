﻿//Marker     Changed by         Date         Remarks
//[001]      A<PERSON><PERSON><PERSON>     25/10/2021    Added class for Warning Type.
using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using Rebound.GlobalTrader.DAL;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.BLL
{
    public partial class WarningType
    {
        #region Properties

        protected static DAL.RohsStatusElement Settings
        {
            get { return Globals.Settings.RohsStatuss; }
        }

        /// <summary>
        /// ROHSStatusId
        /// </summary>
        public System.Int32 WarningId { get; set; }
        /// <summary>
        /// Name
        /// </summary>
        public System.String WarningName { get; set; }

        /// <summary>
        /// LableTypeId (from Table)
        /// </summary>
        public System.Int32 LableTypeId { get; set; }
        /// <summary>
        /// LableTypeName (from Table)
        /// </summary>
        public System.String LableTypeName { get; set; }
        #endregion

        #region Methods

        /// <summary>
        /// Delete
        /// Calls [usp_delete_ROHSStatus]
        /// </summary>
        public static bool Delete(System.Int32? rohsStatusId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.RohsStatus.Delete(rohsStatusId);
        }
        /// <summary>
        /// DropDown
        /// Calls [usp_dropdown_ROHSStatus]
        /// </summary>
        public static List<WarningType> DropDown()
        {
            List<WarningTypeDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.WarningType.DropDown();
            if (lstDetails == null)
            {
                return new List<WarningType>();
            }
            else
            {
                List<WarningType> lst = new List<WarningType>();
                foreach (WarningTypeDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.WarningType obj = new Rebound.GlobalTrader.BLL.WarningType();
                    obj.WarningId = objDetails.WarningId;
                    obj.WarningName = objDetails.WarningName;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }


        /// <summary>
        /// DropDown
        /// Calls [usp_dropdown_LabelType]
        /// </summary>
        public static List<WarningType> LableTypeDropDown()
        {
            List<WarningTypeDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.WarningType.LableTypeDropDown();
            if (lstDetails == null)
            {
                return new List<WarningType>();
            }
            else
            {
                List<WarningType> lst = new List<WarningType>();
                foreach (WarningTypeDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.WarningType obj = new Rebound.GlobalTrader.BLL.WarningType();
                    obj.LableTypeId = objDetails.LableTypeId;
                    obj.LableTypeName = objDetails.LableTypeName;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }


        #endregion
    }
}
