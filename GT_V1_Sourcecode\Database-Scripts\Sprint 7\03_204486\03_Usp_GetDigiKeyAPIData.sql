﻿GO

IF OBJECT_ID('dbo.Usp_GetDigiKeyAPIData', 'P') IS NOT NULL
BEGIN
    DROP PROCEDURE dbo.Usp_GetDigiKeyAPIData;
END

GO
	/****** Object:  StoredProcedure [dbo].[Usp_GetDigiKeyAPIData]    Script Date: 7/26/2024 11:42:06 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO 

/*
===========================================================================================
TASK      			UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-204486]			Phuc Hoang			26-Jul-2024		Create			Sourcing - the Digikey API to be added to the supplier data feeds section
===========================================================================================
*/

CREATE PROCEDURE [dbo].[Usp_GetDigiKeyAPIData] 
--**************************************************************************************                                      
	--* - add search on SupplierPart                                     
	--*Marker     Changed by      Date         Remarks                                      
	--*[001]      Manoj           15/05/2023   FutureElectronics                                    
--**************************************************************************************                                      
	@ClientId INT,
	@PartSearch NVARCHAR(50),
	@ApiURLKeyId INT WITH RECOMPILE 
AS 
BEGIN

Declare @SupType varchar(300);

SELECT @SupType = coty.Name
FROM tbCompany co
	join tbCompanyType coty on co.TypeNo = coty.CompanyTypeId
WHERE co.clientno = 114
	and co.FullName = 'DigiKeyCorporationUSD'
	and co.IsSupplier = 1
	and co.POApproved = 1;

SELECT tf.SupplierAPIID,
	tf.QuantityAvailable QuantityInSupplier,
	tf.QuantityAvailable QuantityOnOrder --, 1 ManufacturerNo  --st.ManufacturerNo              
	,(
		select top 1 ManufacturerId
		from dbo.tbManufacturer
		where partid.[ManufacturerName] = ManufacturerName
	) ManufacturerNo --, tf.pr ProductName --pr.ProductName                
	--, case when ipo.InternalPurchaseOrderId is null then po.CompanyNo else ipo.CompanyNo end  AS SupplierNo                                      
	--, case when ipo.InternalPurchaseOrderId is null then ISNULL(co.CompanyName, '') else ISNULL(coc.CompanyName, '') end AS SupplierName                       
	,0 SupplierNo
	,null ResalePrice --st.ResalePrice                                      
	,null ROHS --st.ROHS  iif(partid.rohs='Y',1,0)                                    
	,'WarehouseName' WarehouseName --wh.WarehouseName                                      
	,'Location' Location --st.Location                                      
	,partid.PackageType PackageName --pk.PackageName                
	,'SupplierPart' SupplierPart --st.SupplierPart                
	,tf.clientno ClientNo --st.ClientNo                                      
	,cl.ClientName ClientName --cl.ClientName                                      
	,'ClientDataVisibleToOthers' ClientDataVisibleToOthers --cl.OwnDataVisibleToOthers AS ClientDataVisibleToOthers                                                
	,'N' SupplierLTB
	,'TR' SupplierMOQ
	,tf.QuantityAvailable QuantityAvailable
	,tf.currencyCode ClientBaseCurrencyCode --cu.CurrencyCode AS ClientBaseCurrencyCode                         
	,cl.ClientCode ClientCode --cl.ClientCode                    
	,'' ClientDataVisibleToOthers --ClientDataVisibleToOthers     
	,'TR' SPQ
	,'N' LTB
	,tf.QuantityAvailable UpliftPrice
	,tf.DLUP DLUP
	,tf.QuantityAvailable Quantity
	,tf.PartNumber Part
	,tf.DLUP OriginalEntryDate
	,tf.DLUP GTDate
	,partid.[ProductDescription] [Description]
	,partid.ManufacturerName ManufacturerCode --mf.ManufacturerCode                        
	,partid.DateCode DateCode --st.DateCode              
	,tf.DatasourceName SupplierName            
	,@SupType SupplierType
	,tf.WebUrl Reference --, tf.offers_categories_subcategory_name ProductName    
	,partid.ProductName ProductName
	,partid.PackageType PackageType --pk.PackageName                
	,partid.ECCN ECCN --, tf.offers_documents_publish_date PublishDate   
	,Null PublishDate,
	tf.QuantityMinimum MOQ,
	tf.QuantityMinimum VirtualCostPrice,
	(
		select top 1 CONCAT('$', UnitPrice)
		from tbSupplierAPIPricing
		where SupplierAPINo = tf.SupplierAPIID
	) as UnitCostPrice,
	(
		select top 1 ApiName
		from tbApiURLKey apik
		where apik.ApiURLKeyId = tf.ApiURLKeyNo
	) as ApiSourceName
FROM dbo.tbSupplierAPI tf
	LEFT JOIN dbo.tbDigiKeyAPIPartAttributes partid on tf.SupplierAPIID = partid.SupplierAPINo
	JOIN dbo.tbClient cl ON cl.ClientId = tf.ClientNo
WHERE (
		(tf.ClientNo = @ClientId)
		and tf.ClientNo <> 109
		AND (tf.PartNumber LIKE @PartSearch)
	)
	AND tf.ApiURLKeyNo = @ApiURLKeyId
	AND tf.DLUP >= DATEADD(day,-3, GETDATE())
	
ORDER BY tf.DLUP Desc 
	
End
GO