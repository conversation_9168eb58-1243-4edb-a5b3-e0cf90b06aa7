//----------------------------------------------------------------------------------------------------------------
// RP 14.10.2009:
// - retrofixes from v3.0.34
//Marker     Changed by      Date         Remarks
//[001]      Vinay           26/07/2012   Add compulsory incoterms field when create Credit and debit note. :ESMS No:- 105
//----------------------------------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets.Data
{
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class CreditAdd : Rebound.GlobalTrader.Site.Data.Base
    {
        public override void ProcessRequest(HttpContext context)
        {
            if (base.init(context))
            {
                switch (Action)
                {
                    case "AddNew": AddNew(); break;
                    case "GetShipVia": GetShipVia(); break;
                    case "GetCRMA": GetCRMA(); break;
                    default: WriteErrorActionNotFound(); break;
                }
            }
        }

        /// <summary>
        /// Add new credit
        /// </summary>
        public void AddNew()
        {
            try
            {
                int intResult = Credit.Insert(
                    SessionManager.ClientID
                    , GetFormValue_Int("CMNo")
                    , GetFormValue_Int("ContactNo")
                    , GetFormValue_NullableDateTime("CreditDate", DateTime.Now)
                    , GetFormValue_NullableInt("CurrencyNo")
                    , GetFormValue_NullableInt("RaisedBy")
                    , GetFormValue_NullableInt("Salesman")
                    , GetFormValue_String("Notes")
                    , GetFormValue_String("Instructions")
                    , GetFormValue_NullableInt("ShipViaNo")
                    , GetFormValue_String("Account")
                    , GetFormValue_NullableDouble("ShippingCost", 0)
                    , GetFormValue_NullableDouble("Freight", 0)
                    , GetFormValue_NullableInt("DivisionNo")
                    , GetFormValue_NullableInt("TaxNo")
                    , GetFormValue_NullableInt("InvoiceNo")
                    , GetFormValue_NullableInt("CustomerRMANo")
                    , GetFormValue_NullableDateTime("ReferenceDate", DateTime.Now)
                    , GetFormValue_String("CustomerPO")
                    , GetFormValue_String("CustomerReturn")
                    , GetFormValue_String("CustomerDebit")
                    , GetFormValue_NullableInt("Salesman2No")
                    , GetFormValue_Double("Salesman2Percent")
                    , GetFormValue_NullableInt("Incoterm")
                    //[001] start code
                    , GetFormValue_NullableDouble("CreditNoteBankFee", 0)
                    //[001] end code
                    , LoginID
                    , GetFormValue_Boolean("isClientInvoice")
                    , GetFormValue_NullableInt("ClientInvLineNo", null)
                    , FileUploadManager.GetDocumentHeaderImageNameForAdd(GetFormValue_Int("DivisionNo"), true, (int)SessionManager.ClientID)
                    ,GetFormValue_NullableInt("DivisionHeaderNo")
                    );

                if (intResult > 0)
                {

                    #region CreditNoteIssueNotification
                        Credit CreditIssueNotify = Credit.GetCreditNoteIssueNotificationDetails(intResult);
                        if (CreditIssueNotify != null)
                        {
                            string strcompany = string.Empty;
                            CreditNoteIssueNotification(CreditIssueNotify);
                        }
                    #endregion

                    //return result
                    JsonObject jsn = new JsonObject();
                    jsn.AddVariable("NewID", intResult);
                    OutputResult(jsn);
                    jsn.Dispose();
                    jsn = null;
                }
                else
                {
                    WriteErrorSQLActionFailed("Insert");
                }
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

        /// <summary>
        /// Get Shipping method cost and charge
        /// </summary>
        private void GetShipVia()
        {
            try
            {
                BLL.ShipVia sv = BLL.ShipVia.Get(ID,SessionManager.ClientID);
                if (sv == null)
                {
                    WriteErrorDataNotFound();
                }
                else
                {
                    JsonObject jsn = new JsonObject();
                    jsn.AddVariable("Cost", Functions.FormatCurrency(sv.Cost, 2));
                    jsn.AddVariable("Charge", Functions.FormatCurrency(BLL.Currency.ConvertValueFromBaseCurrency(sv.Charge, GetFormValue_Int("CreditCurrencyNo"), GetFormValue_DateTime("CreditDate")), 2));
                    OutputResult(jsn);
                    jsn.Dispose();
                    jsn = null;
                }
                sv = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

        /// <summary>
        /// Get CRMA for new Credit
        /// </summary>
        private void GetCRMA()
        {
            try
            {
                BLL.CustomerRma crma = BLL.CustomerRma.GetForNewCreditNote(ID);
                if (crma == null)
                {
                    WriteErrorDataNotFound();
                }
                else
                {
                    JsonObject jsn = new JsonObject();
                    jsn.AddVariable("CRMANumber", crma.CustomerRMANumber);
                    jsn.AddVariable("CustomerNo", crma.CompanyNo);
                    jsn.AddVariable("Customer", crma.CompanyName);
                    jsn.AddVariable("Contact", crma.ContactName);
                    jsn.AddVariable("ContactNo", crma.ContactNo);
                    jsn.AddVariable("Warehouse", crma.WarehouseName);
                    jsn.AddVariable("WarehouseNo", crma.WarehouseNo);
                    jsn.AddVariable("Authoriser", crma.AuthoriserName);
                    jsn.AddVariable("AuthorisedBy", crma.AuthorisedBy);
                    jsn.AddVariable("Salesman", crma.SalesmanName);
                    jsn.AddVariable("SalesmanNo", crma.Salesman);
                    jsn.AddVariable("Division", crma.DivisionName);
                    jsn.AddVariable("DivisionNo", crma.DivisionNo);
                    jsn.AddVariable("InvoiceNo", crma.InvoiceNo);
                    jsn.AddVariable("Invoice", crma.InvoiceNumber);
                    jsn.AddVariable("TaxNo", crma.TaxNo);
                    jsn.AddVariable("Tax", crma.TaxName);
                    jsn.AddVariable("SalesOrderNo", crma.SalesOrderNo);
                    jsn.AddVariable("SalesOrder", crma.SalesOrderNumber);
                    jsn.AddVariable("ShippingAccountNo", crma.Account);
                    jsn.AddVariable("ShipViaNo", crma.ShipViaNo);
                    jsn.AddVariable("ShipVia", crma.ShipViaName);
                    jsn.AddVariable("RMADate", Functions.FormatDate(crma.CustomerRMADate));
                    jsn.AddVariable("InvoiceDate", Functions.FormatDate(crma.InvoiceDate));
                    jsn.AddVariable("InvoiceDateRaw", Functions.FormatDate(crma.InvoiceDate, true, true, true));
                    jsn.AddVariable("Currency", Functions.FormatCurrencyDescription(crma.CurrencyDescription, crma.CurrencyCode));
                    jsn.AddVariable("CurrencyNo", crma.CurrencyNo);
                    jsn.AddVariable("CurrencyCode", crma.CurrencyCode);
                    jsn.AddVariable("DLUP", Functions.FormatDLUP(crma.DLUP, crma.UpdatedBy));
                    jsn.AddVariable("Instructions", Functions.ReplaceLineBreaks(crma.Instructions));
                    jsn.AddVariable("ShippingCostVal", Functions.FormatCurrency(crma.InvoiceShippingCost));
                    jsn.AddVariable("CustomerPO", crma.InvoiceCustomerPO);
                    jsn.AddVariable("FreightVal", Functions.FormatCurrency(crma.InvoiceFreight));
                    jsn.AddVariable("Salesman2", crma.Salesman2);
                    jsn.AddVariable("Salesman2Percent", Functions.FormatNumeric(crma.Salesman2Percent));
                    //[001] code start
                    jsn.AddVariable("Incoterm", crma.IncotermName);
                    jsn.AddVariable("IncotermNo", crma.IncotermNo);
                    //[001] code end
                    jsn.AddVariable("DivisionHeaderNo", crma.DivisionHeaderNo);
                    jsn.AddVariable("DivisionHeaderName", crma.DivisionHeaderName);
                    OutputResult(jsn);
                    jsn.Dispose();
                    jsn = null;
                }
                crma = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }
        public void CreditNoteIssueNotification(Credit CreditIssueNotify)
        {
            try
            {
                #region Power App Notification
                HttpClient client = new HttpClient();
                client.BaseAddress = new Uri(CreditIssueNotify.PowerAppUrl);
                client.DefaultRequestHeaders.Accept.Clear();
                client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
                string rdURL = string.Empty;
                string Subject = "Credit Note Issue Notification";
                rdURL = "<a href = " + string.Format(Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["redirecturi"])) + "/Ord_CreditNoteDetail.aspx?crd=" + CreditIssueNotify.CreditId + " > Credit Note " + CreditIssueNotify.CreditNumber + " </a>";
                HttpRequestMessage request = new HttpRequestMessage(HttpMethod.Post, client.BaseAddress);
                var body = $"{{\"Email\": \"{CreditIssueNotify.SalesmanEmailID}\"," +
                           $"\"Email2\":\"{CreditIssueNotify.Salesman2EmailID}\"," +
                           $"\"Subject\":\"{Subject}\"," +
                           $"\"Name\":\"{CreditIssueNotify.SalesmanName}\"," +
                           $"\"Name2\":\"{CreditIssueNotify.Salesman2Name}\"," +
                           $"\"Customer\":\"{CreditIssueNotify.CompanyName.Replace("\"", "\\\"")}\"," +
                           $"\"Contact\":\"{CreditIssueNotify.ContactName}\"," +
                           $"\"ReboundUrl\":\"{rdURL}\"," +
                           $"\"InvoiceNumber\":\"{CreditIssueNotify.InvoiceNo}\"," +
                           $"\"CustomerPO\":\"{CreditIssueNotify.CustomerPO}\"}}";
                var content = new StringContent(body, Encoding.UTF8, "application/json");
                request.Content = content;
                client.SendAsync(request).ConfigureAwait(false);
                #endregion


                #region GTNotification Sales Person
                var MsgBody = MailTemplateManager.GetMessage_CreditNoteIssueNotification_Salesman(CreditIssueNotify);
                BLL.MailMessage.Insert(   SessionManager.LoginID
                                        , CreditIssueNotify.Salesman
                                        , Subject
                                        , MsgBody
                                        , CreditIssueNotify.CompanyNo
                                        , SessionManager.LoginID
                                        );
                #endregion

                #region GTNotification Additional Sales Person
                if (CreditIssueNotify.Salesman2Percent > 0)
                {
                    MsgBody = MailTemplateManager.GetMessage_CreditNoteIssueNotification_AdditionalSalesman(CreditIssueNotify);
                    BLL.MailMessage.Insert(   SessionManager.LoginID
                                            , CreditIssueNotify.Salesman2
                                            , Subject
                                            , MsgBody
                                            , CreditIssueNotify.CompanyNo
                                            , SessionManager.LoginID
                                           );
                }
                #endregion

            }
            catch (Exception e)
            {
                Console.WriteLine(e.Message);
                throw new Exception();
            }
        }
    }
}