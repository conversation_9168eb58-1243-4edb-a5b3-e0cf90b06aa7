﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO




/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-201934]		An.TranTan			06-May-2024		Update			Change the comparison logic between import supplier part and requirement part.
[US-205174]		An.TranTan			17-Jun-2024		Update			Re-format script + update logic:
																	- Update column number to validate because of new column CLIENT NO insert at Column2
																	- Specific client id for tbCustomerRequirement
[US-205174]		An.TranTan			05-Jul-2024		Update			Remove logic check supplier name within client, allow get from all clients
[US-205174]		Phuc Hoang			08-Jul-2024		Update			Remove logic check Duplicate
[BUG-208825]	An.TranTan			15-Jul-2024		Update			Update validate: check cuplicate for all 25 columns
																	- Between rows in the import file
																	- For rows in import file vs existed purchase request lines.
[BUG-208826]	An.TranTan			17-Jul-2024		Update			Remove check part no and allow import price for requirement has multiple part no
[BUG-208826]	An.TranTan			19-Jul-2024		Update			Show duplicate error message for all duplicate row in import file
[BUG-208826]	An.TranTan			19-Jul-2024		Update			Using NOT EXISTS instead of LEFT JOIN when validate REQUIREMENT to prevent duplicate record in case 
																		multiple requirements have same number within a client
[BUG-208826]	An.TranTan			23-Jul-2024		Update			Update error message and get exact error line number from import file instead of ROW_NUMBER()
[BUG-211786]	An.TranTan			05-Sep-2024		Update			Validate delivery date using UK format
===========================================================================================
*/
CREATE OR ALTER       PROCEDURE [dbo].[usp_ExcelUpload_Error_PriceQuote]
    @UserId INT = 0,
    @ClientId INT = 0,
    @SelectedClientId int = 0
WITH RECOMPILE
AS
BEGIN
    SELECT PriceQuoteImportId,
           (case
                when len(Column1) > 30 then
                    dbo.stripAlphahnumeric(SUBSTRING(Column1, 0, 30))
                else
                    dbo.stripAlphahnumeric(Column1)
            end
           ) as REQUIREMENTNo,
           (case
                when len(Column7) > 100 then
                    dbo.stripAlphahtestingMfr(SUBSTRING(Column7, 0, 100))
                else
                    dbo.stripAlphahtestingMfr(Column7)
            end
           ) as ManufacturerName,
           (case
                when len(Column4) > 30 then
                    dbo.stripAlphahnumeric(SUBSTRING(Column4, 0, 30))
                else
                    dbo.stripAlphahnumeric(Column4)
            end
           ) as Part,
           FLOOR(dbo.stripNumeric(Column10)) as Quantity,
           (case
                when len(Column3) > 128 then
                    dbo.stripAlphahnumeric(SUBSTRING(Column3, 0, 128))
                else
                    dbo.stripAlphahnumeric(Column3)
            end
           ) as SupplierName,                              
           (case
                when len(Column8) > 30 then
                    dbo.stripAlphahnumeric2(SUBSTRING(Column8, 0, 30))
                else
                    dbo.stripAlphahnumeric2(Column8)
            end
           ) as DateCode,
           cast(dbo.stripNumeric(Column5) as FLOAT) as SupplierCost,
           (case
                when len(Column17) > 3 then
                    dbo.stripAlphahnumeric(SUBSTRING(Column17, 0, 3))
                else
                    dbo.stripAlphahnumeric(Column17)
            end
           ) as CurrencyCode,
           1 as isValid,
           cast('' as varchar(max)) as ValidationMessage

    into #tbPriceQuoteImport_tempData
    from BorisGlobalTraderImports.dbo.tbPriceQuoteImport_tempData tbtemp WITH (NOLOCK)
    where tbtemp.ClientId = @ClientId
          and tbtemp.SelectedClientId = @SelectedClientId
          and tbtemp.CreatedBy = @UserId

    /*********** Updation Validation Query **************/

    --CurrencyCode No length 3  & mandotary--                                                            
    update TmpR
    set TmpR.ValidationMessage = case                                                             
                                     when len(TRl.Column17) < 3 then
                                         ISNULL(ValidationMessage, '') + 'Currency Code  accepts 3 characters.'
                                         + '<br/>'
                                     else
                                         TmpR.ValidationMessage
                                 end
    from #tbPriceQuoteImport_tempData TmpR
        inner Join BorisGlobalTraderImports.dbo.tbPriceQuoteImport_tempData TRl WITH (NOLOCK)
            on Trl.PriceQuoteImportId = TmpR.PriceQuoteImportId

    --Requirement mandotary and existed in HUBRFQ--                                                            
	update TmpR
	set TmpR.ValidationMessage = ValidationMessage + 'Requirement is mandotary.' + '<br/>'
    from #tbPriceQuoteImport_tempData TmpR
        inner Join BorisGlobalTraderImports.dbo.tbPriceQuoteImport_tempData TRl
            on Trl.PriceQuoteImportId = TmpR.PriceQuoteImportId
	where TRl.Column1 IS NULL OR LEN(TRl.Column1) = 0

	update TmpR
	set TmpR.ValidationMessage = ValidationMessage + 'Requirement does not Exist or HUBRFQ does not exist for this Requirement.' + '<br/>'
    from #tbPriceQuoteImport_tempData TmpR
        inner Join BorisGlobalTraderImports.dbo.tbPriceQuoteImport_tempData TRl
            on Trl.PriceQuoteImportId = TmpR.PriceQuoteImportId
	where isnull(TRl.Column1, '') <> ''
		and not exists (select 1 from tbCustomerRequirement with (nolock)
						where CustomerRequirementNumber = TRl.Column1 
							and ClientNo = TRl.Column2
							and BOMNo IS NOT NULL
						)

    --Part length 30  & mandotary--                                                            
    update TmpR
    SET TmpR.ValidationMessage = CASE
           
                                     WHEN len(TRl.Column4) > 30 THEN
                                         ISNULL(ValidationMessage, '') + 'Supplier Part No only accepts 30 characters '
                                         + '<br/>'      
                                     ELSE
                                         TmpR.ValidationMessage
                                 END
    FROM #tbPriceQuoteImport_tempData TmpR
        inner Join BorisGlobalTraderImports.dbo.tbPriceQuoteImport_tempData TRl
            on Trl.PriceQuoteImportId = TmpR.PriceQuoteImportId

    --- DateCode length 5--                                                            
    update TmpR
    set TmpR.ValidationMessage = case
                                     when len(TRl.Column8) > 5
                                          and TmpR.DateCode <> TRl.Column8 then
                                         ISNULL(ValidationMessage, '')
                                         + 'DateCode Field only accepts 5 characters and AlphaNumeric values.'
                                         + '<br/>'                                                             
                                     when len(TRl.Column8) > 5 then
                                         ISNULL(ValidationMessage, '') + 'DateCode Field only accepts 5 characters.'
                                         + '<br/>'
                                     else
                                         TmpR.ValidationMessage
                                 end
    from #tbPriceQuoteImport_tempData TmpR
        inner Join BorisGlobalTraderImports.dbo.tbPriceQuoteImport_tempData TRl
            on Trl.PriceQuoteImportId = TmpR.PriceQuoteImportId

	-- Check suppliername existed in GT client
	update TmpR
	set TmpR.ValidationMessage = ISNULL(TmpR.ValidationMessage, '') + 'This supplier does not exist on the DMCC client, unable to import.<br/>'
	from #tbPriceQuoteImport_tempData TmpR
		inner Join BorisGlobalTraderImports.dbo.tbPriceQuoteImport_tempData TRl
            on Trl.PriceQuoteImportId = TmpR.PriceQuoteImportId
	where not exists (
		select 1 from dbo.tbCompany co with (nolock)
		jOIN dbo.tbCompanyAddress ca with (nolock)
                 ON co.CompanyId = ca.CompanyNo AND ca.DefaultBilling = 1
		where co.Inactive = 0 
			AND co.POApproved = 1
			AND ca.CeaseDate IS NULL
			AND co.CompanyName = TRl.Column3
	)

	--- Validate delivery date format--                                                            
    update TmpR
    set TmpR.ValidationMessage = case
                                     when TRY_PARSE(TRl.Column23 AS DATE USING 'en-GB') IS NULL then
                                         ISNULL(ValidationMessage, '') + 'Delivery Date is invalid. Accept formart: dd/mm/yyyy.<br/>'
                                     else
                                         TmpR.ValidationMessage
                                 end
    from #tbPriceQuoteImport_tempData TmpR
        inner Join BorisGlobalTraderImports.dbo.tbPriceQuoteImport_tempData TRl
            on Trl.PriceQuoteImportId = TmpR.PriceQuoteImportId 

	--- Validate duplicate for all columns within import file ---
	;WITH cteDuplicateRows AS (
		SELECT TL.PriceQuoteImportId
			,COUNT(*) OVER (PARTITION BY RL.[Column1]
										,RL.[Column2]
										,RL.[Column3]
										,RL.[Column4]
										,RL.[Column5]
										,RL.[Column6]
										,RL.[Column7]
										,RL.[Column8]
										,RL.[Column9]
										,RL.[Column10]
										,RL.[Column11]
										,RL.[Column12]
										,RL.[Column13]
										,RL.[Column14]
										,RL.[Column15]
										,RL.[Column16]
										,RL.[Column17]
										,RL.[Column18]
										,RL.[Column19]
										,RL.[ClientId]
										,RL.[Column20]
										,RL.[Column21]
										,RL.[Column22]
										,RL.[Column23]
										,RL.[Column24]
										,RL.[Column25]) AS NumberOfOccur
		FROM #tbPriceQuoteImport_tempData TL
        INNER JOIN BorisGlobalTraderImports.dbo.tbPriceQuoteImport_tempData RL
			ON RL.PriceQuoteImportId = TL.PriceQuoteImportId
	)
	UPDATE temp 
	SET temp.ValidationMessage = ValidationMessage + ' This Price has duplicated record(s) within this Import file.<br/>'
	FROM #tbPriceQuoteImport_tempData temp
		JOIN cteDuplicateRows cte ON cte.PriceQuoteImportId = temp.PriceQuoteImportId
	WHERE cte.NumberOfOccur > 1

	--- Validate duplicate for import file with existed purchase request line ---
	UPDATE TL 
	SET TL.ValidationMessage = ValidationMessage + ' Unable to import, a quote with these details already exists on the HUBRFQ.'
	FROM #tbPriceQuoteImport_tempData TL
        INNER JOIN BorisGlobalTraderImports.dbo.tbPriceQuoteImport_tempData RL WITH (NOLOCK)
            ON RL.PriceQuoteImportId = TL.PriceQuoteImportId
		INNER JOIN tbCustomerRequirement cr WITH (NOLOCK)
			ON cr.CustomerRequirementNumber = RL.Column1
				AND cr.ClientNo = RL.Column2
		INNER JOIN tbPurchaseRequestLine prl WITH (NOLOCK)
			ON prl.CustomerRequirementNo = cr.CustomerRequirementId
		INNER JOIN tbPurchaseRequestLineDetail prld WITH (NOLOCK)
			ON prld.PurchaseRequestLineNo = prl.PurchaseRequestLineId
		INNER JOIN tbcompany cp WITH (NOLOCK)
			ON cp.CompanyId = prld.CompanyNo
		INNER JOIN tbCurrency cu WITH (NOLOCK)
			ON cu.CurrencyId = prld.CurrencyNo
		LEFT JOIN tbOfferStatus os WITH (NOLOCK)
			ON os.OfferStatusId = prld.OfferStatusNo
		LEFT JOIN tbRegion re WITH (NOLOCK)
			ON re.RegionId = prld.RegionNO
	WHERE lower(RL.[Column3]) = lower(cp.CompanyName) COLLATE SQL_Latin1_General_CP1_CI_AI
		AND dbo.ufn_get_fullpart(RL.Column4) = prl.FullPart
		AND ISNULL(RL.Column6,'') = ISNULL(prld.ROHSStatus,'')
		AND ISNULL(RL.Column7,'') = ISNULL(prld.ManufacturerName,'')
		AND ISNULL(RL.Column8,'') = ISNULL(prld.DateCode,'')
		AND ISNULL(RL.Column9,'') = ISNULL(prld.PackageType,'')
		AND ISNULL(RL.Column10,'') = ISNULL(prld.Quantity,'')
		AND ISNULL(RL.Column11,'') = ISNULL(os.[Name],'')
		AND ISNULL(RL.Column12, '') = ISNULL(prld.SPQ, '')
		AND ISNULL(RL.Column13, '') = ISNULL(prld.FactorySealed, '')
		AND ISNULL(RL.Column14, '') = ISNULL(prld.TotalQuantityAvailableInStock, '')
		AND ISNULL(RL.Column15, '') = ISNULL(prld.MOQ, '')
		AND ISNULL(RL.Column16, '') = ISNULL(prld.LTB, '')
		AND RL.Column17 = cu.CurrencyCode
		AND ISNULL(RL.Column18, '') = ISNULL(prld.BuyPrice, '')
		AND ISNULL(RL.Column19, '') = ISNULL(prld.SellPrice, '')
		AND ISNULL(RL.Column20, '') = ISNULL(prld.ShippingCost, '')
		AND ISNULL(RL.Column21, '') = ISNULL(prld.LeadTime, '')
		AND ISNULL(RL.Column22, '') = ISNULL(re.RegionName, '')
		AND TRY_PARSE(RL.Column23 AS DATE USING 'en-GB') = CONVERT(Date, prld.DeliveryDate, 103)
		AND ISNULL(RL.Column24, '') = ISNULL(prld.Notes, '')
		AND ISNULL(RL.Column25, '') = ISNULL(prld.MSL, '')
 
    /*********** Updation Validation Query  ENDs **************/
    UPDATE #tbPriceQuoteImport_tempData
    SET isvalid = 0
    WHERE ISNULL(ValidationMessage, '') <> ''

    /*********** Select Final Query  **************/
	SELECT RL.[LineNumber] AS 'LINE NO.',
	       RL.[column1]  AS 'REQUIREMENT',
	       RL.[column2]  AS 'CLIENT NO.',
	       RL.[Column3]  AS 'SUPPLIER NAME',
	       RL.[Column4]  AS 'SUPPLIER PART',
	       RL.[Column5]  AS 'SUPPLIER COST',
	       RL.[column6]  AS 'ROHS',
	       RL.[Column7]  AS 'MANUFACTURER',
	       RL.[Column8]  AS 'DateCode',
	       RL.[Column9]  AS 'PACKAGE',
	       RL.[Column10] AS 'OFFERED QUANTITY',
	       RL.[Column11] AS 'OFFER STATUS',
	       RL.[Column12] AS 'SPQ',
	       RL.[Column13] AS 'FACTORY SEALED',
	       RL.[Column14] AS 'QTY IN STOCK',
	       RL.[Column15] AS 'MOQ',
	       RL.[Column16] AS 'LAST TIME BUY',
	       RL.[Column17] AS 'CURRENCY',
	       RL.[Column18] AS 'BUY PRICE',
	       RL.[Column19] AS 'SELL PRICE',
	       RL.[Column20] AS 'SHIPPING COST',
	       RL.[Column21] AS 'LEAD TIME',
	       RL.[Column22] AS 'REGION',
	       RL.[Column23] AS 'Delivery Date',
	       RL.[Column24] AS 'NOTES',
	       RL.[Column25] AS 'MSL',
	       IIF(TL.ValidationMessage = '', '', '<b>' + TL.ValidationMessage + '<b/>') as 'Reason for Excel Upload failed',
	       RL.OriginalFilename
	FROM #tbPriceQuoteImport_tempData TL
	    inner join BorisGlobalTraderImports.dbo.tbPriceQuoteImport_tempData RL
	        on RL.PriceQuoteImportId = TL.PriceQuoteImportId
	WHERE isValid <> 1
	ORDER BY RL.[LineNumber] ASC
END
GO


