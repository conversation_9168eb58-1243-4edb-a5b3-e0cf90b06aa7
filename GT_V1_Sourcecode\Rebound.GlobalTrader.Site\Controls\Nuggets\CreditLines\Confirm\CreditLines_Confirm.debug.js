///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.CreditLines_Confirm = function(element) {
    Rebound.GlobalTrader.Site.Controls.Forms.CreditLines_Confirm.initializeBase(this, [element]);
    this._intRequirementLineID = -1;
    this._intBOMID=-1
    this._CreditLineIds='';
};

Rebound.GlobalTrader.Site.Controls.Forms.CreditLines_Confirm.prototype = {

    get_BomCode: function() { return this._BomCode; }, set_BomCode: function(value) { if (this._BomCode !== value) this._BomCode = value; },
    get_BomName: function() { return this._BomName; }, set_BomName: function(value) { if (this._BomName !== value) this._BomName = value; },
    get_BomCompanyName: function() { return this._BomCompanyName; }, set_BomCompanyName: function(value) { if (this._BomCompanyName !== value) this._BomCompanyName = value; },
    get_BomCompanyNo: function() { return this._BomCompanyNo; }, set_BomCompanyNo: function(value) { if (this._BomCompanyNo !== value) this._BomCompanyNo = value; },
    get_SalesManNo: function() { return this._SalesManNo; }, set_SalesManNo: function(value) { if (this._SalesManNo !== value) this._SalesManNo = value; },
    get_SalesManName: function() { return this._SalesManName; }, set_SalesManName: function(value) { if (this._SalesManName !== value) this._SalesManName = value; },

    initialize: function() {
        Rebound.GlobalTrader.Site.Controls.Forms.CreditLines_Confirm.callBaseMethod(this, "initialize");
        this._strDataPath = "controls/Nuggets/CreditLines";
		this._strDataObject = "CreditLines";
        this.addShown(Function.createDelegate(this, this.formShown));
    },

    dispose: function() {
        if (this.isDisposed) return;
        if (this._frmConfirm) this._frmConfirm.dispose();
        this._frmConfirm = null;
        this._intRequirementLineID = null;
        Rebound.GlobalTrader.Site.Controls.Forms.CreditLines_Confirm.callBaseMethod(this, "dispose");
    },

    formShown: function() {
  
        if (this._blnFirstTimeShown) {
            this._frmConfirm = this.getFieldComponent("frmConfirm");
            this._frmConfirm.addClickYesEvent(Function.createDelegate(this, this.yesClicked));
            this._frmConfirm.addClickNoEvent(Function.createDelegate(this, this.noClicked));
        }
    },

    yesClicked: function() {
        this.showSaving(true);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData(this._strDataPath);
		obj.set_DataObject(this._strDataObject);
		obj.set_DataAction("SendCreditNoteToPOHUB");
		obj.addParameter("CreditLineID", this._CreditLineIds);
      
        obj.addDataOK(Function.createDelegate(this, this.saveComplete));
        obj.addError(Function.createDelegate(this, this.saveError));
        obj.addTimeout(Function.createDelegate(this, this.saveError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },


    noClicked: function() {
        this.showSaving(false);
        this.onNotConfirmed();
    },

    saveError: function(args) {
        this.showSaving(false);
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    saveComplete: function(args) {
        this.showSaving(false);
        if (args._result.Result) {
            this.onSaveComplete();
        } else {
            this._strErrorMessage = args._errorMessage;
            this.onSaveError();
        }
    }



};

Rebound.GlobalTrader.Site.Controls.Forms.CreditLines_Confirm.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CreditLines_Confirm", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
