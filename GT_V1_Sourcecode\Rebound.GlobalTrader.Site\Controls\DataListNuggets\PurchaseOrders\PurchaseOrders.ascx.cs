/* Marker     changed by      date         Remarks  
   [001]      <PERSON><PERSON><PERSON>     27-Sep-2018   REB-13083 Change request PO - delivery status
*/
using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.DataListNuggets {
	public partial class PurchaseOrders : Base {

		#region Properties
        private bool _IsGlobalLogin = false;
        public bool IsGlobalLogin
        {
            get { return _IsGlobalLogin; }
            set { _IsGlobalLogin = value; }
        }
		private bool _IsGSA = false;
		public bool IsGSA
		{
			get { return _IsGSA; }
			set { _IsGSA = value; }
		}
		#endregion
		#region Overrides

		/// <summary>
		/// OnInit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
            IsGlobalLogin = SessionManager.IsGlobalUser.Value;
			IsGSA = SessionManager.IsGSA.Value;
			SetDataListNuggetType("PurchaseOrders");
			base.OnInit(e);
			TitleText = Functions.GetGlobalResource("Nuggets", "PurchaseOrders");
			AddScriptReference("Controls.DataListNuggets.PurchaseOrders.PurchaseOrders.js");
            
			SetupTable();
		}

		protected override void OnLoad(EventArgs e) {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.DataListNuggets.PurchaseOrders", ctlDesignBase.ClientID);
            _scScriptControlDescriptor.AddProperty("intBuyerID", _objQSManager.BuyerID > 0 ? _objQSManager.BuyerID : 0);
            _scScriptControlDescriptor.AddProperty("blnPOHub", Convert.ToBoolean(SessionManager.IsPOHub));
            _scScriptControlDescriptor.AddProperty("IsGlobalLogin", _IsGlobalLogin);
			_scScriptControlDescriptor.AddProperty("IsGSA", _IsGSA);
			base.OnLoad(e);
		}

		protected override void RenderAdditionalState() {
			string strViewLevel = this.GetSavedStateValue("ViewLevel");
			if (!string.IsNullOrEmpty(strViewLevel)) {
				((Pages.Content)Page).CurrentTab = Convert.ToInt32(strViewLevel);
				_enmViewLevel = (ViewLevelList)Convert.ToInt32(strViewLevel);
				this.OnAskPageToChangeTab();
			}
			base.RenderAdditionalState();
		}

		#endregion

		private void SetupTable() {
			_tbl.AllowSelection = false;
			_tbl.SortColumnDirection = Rebound.GlobalTrader.Site.Enumerations.SortColumnDirection.DESC;
            _tbl.Columns.Add(new FlexiDataColumn("PurchaseNo", SessionManager.IsPOHub.Value? "IPONo":"Blank", WidthManager.GetWidth(WidthManager.ColumnWidth.SystemDocumentNumber), true));
			_tbl.Columns.Add(new FlexiDataColumn("PartNo", "Manufacturer", WidthManager.GetWidth(WidthManager.ColumnWidth.PartNo), true));
			_tbl.Columns.Add(new FlexiDataColumn("Price", WidthManager.GetWidth(WidthManager.ColumnWidth.CurrencyValue), true));
			_tbl.Columns.Add(new FlexiDataColumn("QuantityOrdered", "QuantityOutstanding", WidthManager.GetWidth(WidthManager.ColumnWidth.Quantity), true));
			_tbl.Columns.Add(new FlexiDataColumn("Company", "Contact", WidthManager.GetWidth(WidthManager.ColumnWidth.CompanyName), true));
			_tbl.Columns.Add(new FlexiDataColumn("DeliveryDate", WidthManager.GetWidth(WidthManager.ColumnWidth.Date), true));
            _tbl.Columns.Add(new FlexiDataColumn("Status", "DeliveryStatus", Unit.Pixel(90), false));
            _tbl.Columns.Add(new FlexiDataColumn("ClientName", Unit.Empty, true));
            //[001] start
            //_tbl.Columns.Add(new FlexiDataColumn("DeliveryStatus", Unit.Empty, true));
            //[001] end
		}

	}
}
