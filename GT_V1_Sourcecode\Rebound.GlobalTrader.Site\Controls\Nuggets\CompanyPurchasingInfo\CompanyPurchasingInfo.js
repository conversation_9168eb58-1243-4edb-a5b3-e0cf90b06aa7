Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyPurchasingInfo=function(n){Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyPurchasingInfo.initializeBase(this,[n]);this._intCompanyID=-1;this._inactive=!1};Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyPurchasingInfo.prototype={get_intCompanyID:function(){return this._intCompanyID},set_intCompanyID:function(n){this._intCompanyID!==n&&(this._intCompanyID=n)},get_ibtnEdit:function(){return this._ibtnEdit},set_ibtnEdit:function(n){this._ibtnEdit!==n&&(this._ibtnEdit=n)},get_tblOpenPOs:function(){return this._tblOpenPOs},set_tblOpenPOs:function(n){this._tblOpenPOs!==n&&(this._tblOpenPOs=n)},get_pnlOpenPOs:function(){return this._pnlOpenPOs},set_pnlOpenPOs:function(n){this._pnlOpenPOs!==n&&(this._pnlOpenPOs=n)},get_pnlGetOpenPOs:function(){return this._pnlGetOpenPOs},set_pnlGetOpenPOs:function(n){this._pnlGetOpenPOs!==n&&(this._pnlGetOpenPOs=n)},get_hypGetOpenPOs:function(){return this._hypGetOpenPOs},set_hypGetOpenPOs:function(n){this._hypGetOpenPOs!==n&&(this._hypGetOpenPOs=n)},get_pnlLoadingOpenPOs:function(){return this._pnlLoadingOpenPOs},set_pnlLoadingOpenPOs:function(n){this._pnlLoadingOpenPOs!==n&&(this._pnlLoadingOpenPOs=n)},get_pnlOpenPOsError:function(){return this._pnlOpenPOsError},set_pnlOpenPOsError:function(n){this._pnlOpenPOsError!==n&&(this._pnlOpenPOsError=n)},get_tblOverduePOs:function(){return this._tblOverduePOs},set_tblOverduePOs:function(n){this._tblOverduePOs!==n&&(this._tblOverduePOs=n)},get_pnlOverduePOs:function(){return this._pnlOverduePOs},set_pnlOverduePOs:function(n){this._pnlOverduePOs!==n&&(this._pnlOverduePOs=n)},get_pnlLoadingOverduePOs:function(){return this._pnlLoadingOverduePOs},set_pnlLoadingOverduePOs:function(n){this._pnlLoadingOverduePOs!==n&&(this._pnlLoadingOverduePOs=n)},get_pnlOverduePOsError:function(){return this._pnlOverduePOsError},set_pnlOverduePOsError:function(n){this._pnlOverduePOsError!==n&&(this._pnlOverduePOsError=n)},get_pnlGetOverduePOs:function(){return this._pnlGetOverduePOs},set_pnlGetOverduePOs:function(n){this._pnlGetOverduePOs!==n&&(this._pnlGetOverduePOs=n)},get_hypGetOverduePOs:function(){return this._hypGetOverduePOs},set_hypGetOverduePOs:function(n){this._hypGetOverduePOs!==n&&(this._hypGetOverduePOs=n)},get_CanStopSupplier:function(){return this._CanStopSupplier},set_CanStopSupplier:function(n){this._CanStopSupplier!==n&&(this._CanStopSupplier=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyPurchasingInfo.callBaseMethod(this,"initialize");this._strPathToData="controls/Nuggets/CompanyPurchasingInfo";this._strDataObject="CompanyPurchasingInfo";this.addRefreshEvent(Function.createDelegate(this,this.getData));$addHandler(this._hypGetOpenPOs,"click",Function.createDelegate(this,this.getOpenPOs));$addHandler(this._hypGetOverduePOs,"click",Function.createDelegate(this,this.getOverduePOs));this._fldYearToDate=this.getEllipsesControl("ctlYearToDate");this._fldYearToDate.addSetupData(Function.createDelegate(this,this.getYearToDate));this._fldLastYear=this.getEllipsesControl("ctlLastYear");this._fldLastYear.addSetupData(Function.createDelegate(this,this.getLastYear));this._ibtnEdit&&($R_IBTN.addClick(this._ibtnEdit,Function.createDelegate(this,this.showEditForm)),this._frmEdit=$find(this._aryFormIDs[0]),this._frmEdit.addCancel(Function.createDelegate(this,this.cancelEdit)),this._frmEdit.addSaveComplete(Function.createDelegate(this,this.saveEditComplete)),this._frmEdit.addSaveError(Function.createDelegate(this,this.saveEditError)))},dispose:function(){this.isDisposed||(this._hypGetOpenPOs&&$clearHandlers(this._hypGetOpenPOs),this._hypGetOverduePOs&&$clearHandlers(this._hypGetOverduePOs),this._ibtnEdit&&$R_IBTN.clearHandlers(this._ibtnEdit),this._frmEdit&&this._frmEdit.dispose(),this._tblOpenPOs&&this._tblOpenPOs.dispose(),this._tblOverduePOs&&this._tblOverduePOs.dispose(),this._intCompanyID=null,this._ibtnEdit=null,this._frmEdit=null,this._tblOpenPOs=null,this._pnlOpenPOs=null,this._pnlGetOpenPOs=null,this._hypGetOpenPOs=null,this._pnlLoadingOpenPOs=null,this._pnlOpenPOsError=null,this._tblOverduePOs=null,this._pnlOverduePOs=null,this._pnlLoadingOverduePOs=null,this._pnlOverduePOsError=null,this._pnlGetOverduePOs=null,this._hypGetOverduePOs=null,this._CanStopSupplier=null,this._blnEditHubSupplier=null,this._inactive=null,Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyPurchasingInfo.callBaseMethod(this,"dispose"))},getData:function(){this.getData_Start();this.showOpenPOsGetData(!0);this.showOpenPOsError(!1);this.showOverduePOsGetData(!0);this.showOverduePOsError(!1);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("GetData");n.addParameter("id",this._intCompanyID);n.addDataOK(Function.createDelegate(this,this.getDataOK));n.addError(Function.createDelegate(this,this.getDataError));n.addTimeout(Function.createDelegate(this,this.getDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();this.getOpenPOs();this.getOverduePOs();n=null},getDataOK:function(n){var t=n._result;this.setFieldValue("ctlIsApproved",$R_FN.getApprovedByAndDate(t.IsApproved,t.ApprovedByAndDate));this.setFieldValue("hidIsApproved",t.IsApproved);this.setFieldValue("ctlCurrency",t.Currency);this.setFieldValue("hidCurrencyNo",t.CurrencyNo);this.setFieldValue("ctlTerms",t.Terms);this.setFieldValue("hidTermsNo",t.TermsNo);this.setFieldValue("ctlRating",t.Rating);this.setFieldValue("ctlShipVia",t.ShipVia);this.setFieldValue("hidShipViaNo",t.ShipViaNo);this.setFieldValue("ctlShippingAccountNo",t.ShippingAccountNo);this.setFieldValue("hidContactNo",t.ContactNo);this.setFieldValue("ctlContact",$RGT_nubButton_Contact(t.ContactNo,t.ContactName));this.setFieldValue("ctlYearToDate",t.YearToDate);this.setFieldValue("ctlLastYear",t.LastYear);this.setFieldValue("ctlSupplierNo",t.SupplierCode);this.setFieldValue("hidPOShipCountryNo",t.DefaultPOCountryNo);this.setFieldValue("ctlPOShipCountry",t.DefaultPOCountry);this.setFieldValue("ctlOnStop",t.OnStop);this.setFieldValue("hidSupOnStop",t.OnStop);this._inactive=t.Inactive;this._ibtnEdit&&$R_IBTN.enableButton(this._ibtnEdit,!this._inactive);this.setDLUP(t.DLUP);this.getDataOK_End()},getDataError:function(n){this.showError(!0,n.get_ErrorMessage())},getOpenPOs:function(){this.showLoadingOpenPOs(!0);this.showOpenPOsError(!1);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("GetOpenPOs");n.addParameter("id",this._intCompanyID);n.addDataOK(Function.createDelegate(this,this.getOpenPOsOK));n.addError(Function.createDelegate(this,this.getOpenPOsError));n.addTimeout(Function.createDelegate(this,this.getOpenPOsError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getOpenPOsOK:function(n){res=n._result;this.showLoadingOpenPOs(!1);this.showOpenPOsError(!1);this._tblOpenPOs.clearTable();this.processPOList(this._tblOpenPOs);this._tblOpenPOs.resizeColumns()},getOpenPOsError:function(n){this.showLoadingOpenPOs(!1);this.showOpenPOsError(!0,n.get_ErrorMessage())},showLoadingOpenPOs:function(n){$R_FN.showElement(this._pnlLoadingOpenPOs,n);$R_FN.showElement(this._pnlOpenPOs,!n);this.showLoading(n);n&&$R_FN.showElement(this._pnlGetOpenPOs,!1)},showOpenPOsError:function(n,t){$R_FN.showElement(this._pnlOpenPOsError,n);n&&(this.showLoading(!1),$R_FN.showElement(this._pnlOpenPOs,!1),$R_FN.showElement(this._pnlGetOpenPOs,!1),$R_FN.setInnerHTML(this._pnlOpenPOsError,t))},showOpenPOsGetData:function(n){$R_FN.showElement(this._pnlGetOpenPOs,n);n&&(this.showLoading(!1),$R_FN.showElement(this._pnlLoadingOpenPOs,!1),$R_FN.showElement(this._pnlOpenPOs,!1),$R_FN.setInnerHTML(this._pnlOpenPOsError,!1))},processPOList:function(n){var i,t,r;if(res.Items)for(i=0;i<res.Items.length;i++)t=res.Items[i],r=[$RGT_nubButton_PurchaseOrder(t.ID,t.No),t.Date,t.Amount],n.addRow(r,t.ID,!1),t=null,r=null},getOverduePOs:function(){this.showLoadingOverduePOs(!0);this.showOverduePOsError(!1);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("GetOverduePOs");n.addParameter("id",this._intCompanyID);n.addDataOK(Function.createDelegate(this,this.getOverduePOsOK));n.addError(Function.createDelegate(this,this.getOverduePOsError));n.addTimeout(Function.createDelegate(this,this.getOverduePOsError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getOverduePOsOK:function(n){res=n._result;this.showLoadingOverduePOs(!1);this.showOverduePOsError(!1);this._tblOverduePOs.clearTable();this.processPOList(this._tblOverduePOs);this._tblOverduePOs.resizeColumns()},getOverduePOsError:function(n){this.showLoadingOverduePOs(!1);this.showOverduePOsError(!0,n.get_ErrorMessage())},showLoadingOverduePOs:function(n){$R_FN.showElement(this._pnlLoadingOverduePOs,n);$R_FN.showElement(this._pnlOverduePOs,!n);this.showLoading(n);n&&$R_FN.showElement(this._pnlGetOverduePOs,!1)},showOverduePOsError:function(n,t){$R_FN.showElement(this._pnlOverduePOsError,n);n&&(this.showLoading(!1),$R_FN.showElement(this._pnlOverduePOs,!1),$R_FN.showElement(this._pnlGetOverduePOs,!1),$R_FN.setInnerHTML(this._pnlOverduePOsError,t))},showOverduePOsGetData:function(n){$R_FN.showElement(this._pnlGetOverduePOs,n);n&&(this.showLoading(!1),$R_FN.showElement(this._pnlLoadingOverduePOs,!1),$R_FN.showElement(this._pnlOverduePOs,!1),$R_FN.setInnerHTML(this._pnlOverduePOsError,!1))},showEditForm:function(){var n=this._intCompanyID;this._frmEdit.getFieldComponent("ctlContact")._intCompanyID=n;this._frmEdit.setFieldValue("ctlApproved",Boolean.parse(this.getFieldValue("hidIsApproved")));this._frmEdit.setFieldValue("ctlCurrency",this.getFieldValue("hidCurrencyNo"));this._frmEdit.setFieldValue("ctlTerms",this.getFieldValue("hidTermsNo"));this._frmEdit.setFieldValue("ctlRating",this.getFieldValue("ctlRating"));this._frmEdit.setFieldValue("ctlShipVia",this.getFieldValue("hidShipViaNo"));this._frmEdit.setFieldValue("ctlShippingAccountNo",this.getFieldValue("ctlShippingAccountNo"));this._frmEdit.setFieldValue("ctlContact",this.getFieldValue("hidContactNo"));this._frmEdit.setFieldValue("ctlSupplierNo",this.getFieldValue("ctlSupplierNo"));this._frmEdit.setFieldValue("ctlCountry",this.getFieldValue("hidPOShipCountryNo"));this._frmEdit.showField("ctlOnStop",this._CanStopSupplier);this._frmEdit.setFieldValue("ctlOnStop",this.getFieldValue("ctlOnStop"));this._frmEdit._globalLoginClientNo=this._globalLoginClientNo;this.showForm(this._frmEdit,!0)},cancelEdit:function(){this.showContent(!0);this.showForm(this._frmEdit,!1)},saveEditComplete:function(){this.showForm(this._frmEdit,!1);this.getData();this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully);this.onSaveEditComplete()},saveEditError:function(){this.showForm(this._frmEdit,!1);this.showError(!0,this._frmEdit._strErrorMessage)},getYearToDate:function(){var n=this._fldYearToDate._objData;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("GetYearToDate");n.addParameter("id",this._intCompanyID)},getLastYear:function(){var n=this._fldLastYear._objData;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("GetLastYear");n.addParameter("id",this._intCompanyID)},enableButtons:function(n){this._ibtnEdit&&$R_IBTN.enableButton(this._ibtnEdit,n&&!this._inactive)}};Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyPurchasingInfo.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyPurchasingInfo",Rebound.GlobalTrader.Site.Controls.Nuggets.Base);