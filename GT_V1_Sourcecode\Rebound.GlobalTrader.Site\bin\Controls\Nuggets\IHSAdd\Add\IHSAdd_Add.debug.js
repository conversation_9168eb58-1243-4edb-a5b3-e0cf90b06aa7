///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------

//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.IHSAdd_Add = function(element) { 
	Rebound.GlobalTrader.Site.Controls.Forms.IHSAdd_Add.initializeBase(this, [element]);
	this._intNewID = 0;
	
};

Rebound.GlobalTrader.Site.Controls.Forms.IHSAdd_Add.prototype = {
	
	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.Forms.IHSAdd_Add.callBaseMethod(this, "initialize");
		this.addShown(Function.createDelegate(this, this.formShown));
	},
	
	dispose: function() { 
		if (this.isDisposed) return;
		Rebound.GlobalTrader.Site.Controls.Forms.IHSAdd_Add.callBaseMethod(this, "dispose");
	},
	
	cancelClicked: function() {
		$R_FN.navigateBack();
	},
	
	formShown: function() {
		if (this._blnFirstTimeShown) {
			this.addCancel(Function.createDelegate(this, this.cancelClicked));
			this.addSave(Function.createDelegate(this, this.saveClicked));

		}
		this.getFieldDropDownData("ctlPartStatus");
		this.getFieldDropDownData("ctlManufacturer");
		this.getFieldDropDownData("ctlMsl");
		this.getFieldDropDownData("ctlCountryOfManufacture");
		this.getFieldDropDownData("ctlPackage");
		this.getFieldDropDownData("ctlECCNCode");
		this.setFieldValue("ctlDateRequired", $R_FN.shortDate());
	},

	saveClicked: function() {
		if (!this.validateForm()) return;
		this.showSaving(true);
		var obj = new Rebound.GlobalTrader.Site.Data();
		obj.set_PathToData("controls/Nuggets/IHSAdd");
		obj.set_DataObject("IHSAdd");
		obj.set_DataAction("AddNew");
		obj.addParameter("Part", this.getFieldValue("ctlPartNo"));
		obj.addParameter("PartStatus", this.getFieldValue("ctlPartStatus"));
		obj.addParameter("Manufacturer", this.getFieldValue("ctlManufacturer"));
		obj.addParameter("Package", this.getFieldValue("ctlPackage"));
		obj.addParameter("PackageCode", this.getFieldValue("ctlPackageCode"));
		obj.addParameter("HTSCode", this.getFieldValue("ctlHTSCode"));
		obj.addParameter("Date", this.getFieldValue("ctlDateRequired"));
		obj.addParameter("Description", this.getFieldValue("ctlDescription"));
		obj.addParameter("ECCNCode", this.getFieldValue("ctlECCNCode"));
		obj.addParameter("CountryOfManufacture", this.getFieldValue("ctlCountryOfManufacture"));
		obj.addParameter("MSLLevel", this.getFieldDropDownText("ctlMsl"));
		obj.addDataOK(Function.createDelegate(this, this.saveEditComplete));
		obj.addError(Function.createDelegate(this, this.saveEditError));
		obj.addTimeout(Function.createDelegate(this, this.saveEditError));
		$R_DQ.addToQueue(obj);
		$R_DQ.processQueue();
		obj = null;
	},

	saveEditError: function(args) {
		this._strErrorMessage = args._errorMessage;
		this.onSaveError();
	},
	
	saveEditComplete: function(args) {
		if (args._result.NewID > 0) {
			this._intNewID = args._result.NewID;
			this.showSavedOK(true);
			this.onSaveComplete();
		} else {
			this._strErrorMessage = args._errorMessage;
			this.onSaveError();
		}
	},	

	validateForm: function() {
		this.onValidate();
		var blnOK = true;
		if (!this.checkFieldEntered("ctlPartNo")) blnOK = false;
		if (!this.checkFieldEntered("ctlPartStatus")) blnOK = false;

		if (!this.checkFieldEntered("ctlManufacturer")) blnOK = false;
		if (!this.checkFieldEntered("ctlPackage")) blnOK = false;
		if (!this.checkFieldEntered("ctlDateRequired")) blnOK = false;
		if (!blnOK) this.showError(true);
		return blnOK;
	}
		



};

Rebound.GlobalTrader.Site.Controls.Forms.IHSAdd_Add.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.IHSAdd_Add", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
