﻿
GO

CREATE OR ALTER PROCEDURE [dbo].[usp_insert_SourcingResultWithOffer]                   
--***************************************************************************************************                  
--* RP 16.02.2010:                  
--* - Copy the result to the Offer table (unless it is from Excess)    
--* Action: Altered  By: Abhinav Saxena  Date: 08-09-2023 Comment: For RP-2228  (AS6081)               
--***************************************************************************************************                  
    @CustomerRequirementNo int                  
  , @TypeName nvarchar(40) = NULL                  
  , @Notes nvarchar(128) = NULL                  
  , @Part nvarchar(30)                  
  , @ManufacturerNo int = NULL                  
  , @DateCode nvarchar(5) = NULL                  
  , @ProductNo int = NULL                  
  , @PackageNo int = NULL                  
  , @Quantity int                  
  , @Price float                  
  , @CurrencyNo int = NULL                  
  , @OriginalEntryDate datetime = NULL                  
  , @Salesman int = NULL                  
  , @OfferStatusNo int = NULL                  
  , @SupplierNo int = NULL                  
  , @ROHS tinyint = NULL                 
  , @ClientNo int                  
  , @UpdatedBy int = NULL              
    , @SuplierPrice float = NULL                     
 , @EstimatedShippingCost FLOAT=0                   
 , @DeliveryDate DATETIME=NULL                
 , @PUHUB BIT=0                
   , @SPQ nvarchar(50)=NULL                
 , @LeadTime nvarchar(50)=NULL                
 , @ROHSStatus nvarchar(50)=NULL                
 , @FactorySealed nvarchar(50)=NULL                
 , @MSL nvarchar(50)=NULL               
 , @SupplierTotalQSA nvarchar(30) = null              
 , @SupplierLTB nvarchar(30) = null              
 , @SupplierMOQ nvarchar(30) = null              
 , @RegionNo int = NULL                
 , @MSLLevelNo int = NULL              
 , @SupplierWarranty     int = NULL          
 , @isTestingRecommended bit = 0         
 , @IHSCountryOfOriginNo int = NULL      
 , @TypeOfSupplier   INT=NULL  
 , @ReasonForSupplier  INT=NULL  
 , @RiskOfSupplier   INT=NULL
 , @CountryNo INT=NULL      
 , @SourcingResultId int OUTPUT             
 , @LinkCurrencyMsg varchar(150)=null OUTPUT                  
AS --                  
   BEGIN          
   IF (@CustomerRequirementNo is not null)    
 begin      
 UPDATE tbCustomerRequirement set REQStatus = 3 where CustomerRequirementId = @CustomerRequirementNo          
 end     
     IF (@CustomerRequirementNo <= 0)           
      BEGIN         
   return        
   end        
   set @LinkCurrencyMsg =''          
   DECLARE @LinkMultiCurrencyNo INT              
   DECLARE @CustClientNo INT             
   DECLARE @HubCurrencyName varchar(20)          
   DECLARE @ClientCode varchar(20)             
          
   SELECT @CustClientNo = ClientNo FROM tbCustomerRequirement where CustomerRequirementId = @CustomerRequirementNo                         
          
   SELECT @LinkMultiCurrencyNo = l.LinkMultiCurrencyId            
   FROM tbCurrency c left join tbLinkMultiCurrency l   on l.GlobalCurrencyNo = c.GlobalCurrencyNo             
   WHERE  l.ClientNo = @CustClientNo and c.ClientNo = 114 AND c.CurrencyId = @CurrencyNo                
             
     IF @LinkMultiCurrencyNo IS NULL          
     BEGIN          
       SELECT @ClientCode = ClientCode  FROM tbclient where clientid=@CustClientNo          
    select @HubCurrencyName = CurrencyCode   from tbCurrency where CurrencyId = @CurrencyNo          
       SET @LinkCurrencyMsg = 'Cannot use '+@HubCurrencyName+' currency for the '+@ClientCode+' client. Kindly contact administrator.';          
    SET @SourcingResultId = 0          
    RETURN          
     END             
              
 SET @Notes = LTRIM(RTRIM(REPLACE(REPLACE(REPLACE(@Notes              
                    , CHAR(9), ' / ')              
                    , CHAR(10), ' / ')              
                    , CHAR(13), ' / ')))     
              
                  
        DECLARE @SourcingTableItemNo int           
  declare @OfferId int               
  declare @OfferPorductNo int              
  DECLARE @GlobalProductNo int              
  declare @SupplierName nvarchar(100)              
  SELECT @GlobalProductNo = GlobalProductNo FROM tbProduct where ProductId = @ProductNo              
     SELECT @OfferPorductNo = ProductId FROM tbProduct WHERE  ClientNo = @ClientNo and GlobalProductNo = @GlobalProductNo                 
  declare @IsPoHUB bit              
  set @IsPoHUB = 1              
  SELECT @SupplierName = CompanyName                  
                         FROM   tbCompany                  
                       WHERE  CompanyId = @SupplierNo              
                  
        EXEC usp_insert_IPOOffer               
      @Part                        
    , @ManufacturerNo                        
    , @DateCode                        
, @OfferPorductNo              
    , @PackageNo              
    , @Quantity               
    , @SuplierPrice               
    , @CurrencyNo              
    , @OriginalEntryDate              
    , @Salesman              
    , @SupplierNo              
    , @SupplierName              
    , @ROHS              
    , @OfferStatusNo              
    , @Notes              
    , @UpdatedBy              
    , @ClientNo              
    , @SupplierTotalQSA              
    , @SupplierLTB              
    , @SupplierMOQ              
    , @MSL              
    , @SPQ              
    , @LeadTime              
    , @FactorySealed              
    , @ROHSStatus              
    , @OfferId  OUTPUT              
    , @IsPoHUB              
    , @MSLLevelNo              
                  
       -- SET @OfferId = scope_identity()                  
               
  DECLARE @SourcingResultNo int                 
   IF @OfferId > 0              
   BEGIN              
     EXEC usp_insert_SourcingResult_From_QuotesToClient @CustomerRequirementNo,@OfferId,@UpdatedBy,@Price,@SuplierPrice,@EstimatedShippingCost,    
  @PackageNo,@DeliveryDate,@RegionNo,@SupplierWarranty,@isTestingRecommended,@IHSCountryOfOriginNo,@TypeOfSupplier,@ReasonForSupplier,@RiskOfSupplier,@CountryNo, @SourcingResultNo OUTPUT              
   END                 
             SET @SourcingResultId = @SourcingResultNo              
                           
                                       
END 
GO


