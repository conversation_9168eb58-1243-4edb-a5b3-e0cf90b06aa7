<%@ Control Language="C#" CodeBehind="BOMItems_NoBidConfirm.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_NoBidConfirm" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="false" AllowQuickHelp="false">
<Explanation>
    <asp:Label ID="lblExplainNoBid" runat="server" CssClass="invisible"><%=Functions.GetGlobalResource("Messages", "ConfirmNoBid")%></asp:Label>
		<asp:Label ID="lblExplainRecallNoBid" runat="server" CssClass="invisible"><%=Functions.GetGlobalResource("Messages", "ConfirmRecallNoBid")%></asp:Label>
</Explanation>
	<Content>
		<ReboundUI_Table:Form id="frm" runat="server">		     
						<ReboundUI_Form:FormField id="ctlNotes" runat="server" FieldID="txtNotes" ResourceTitle="Notes" IsRequiredField="true">
				<Field><ReboundUI:ReboundTextBox ID="txtNotes" runat="server" Width="450" Rows="2" TextMode="MultiLine" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlConfirm" runat="server" FieldID="ctlConfirmation" ResourceTitle="SaveConfirm">
				<Field><ReboundUI:Confirmation ID="ctlConfirmation" runat="server" /></Field>
			</ReboundUI_Form:FormField>
		</ReboundUI_Table:Form>		 
	</Content>
</ReboundUI_Form:DesignBase>
