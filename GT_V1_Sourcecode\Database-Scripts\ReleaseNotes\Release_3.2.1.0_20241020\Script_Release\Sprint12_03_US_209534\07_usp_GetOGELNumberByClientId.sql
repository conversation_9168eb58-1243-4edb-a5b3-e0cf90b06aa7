﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER   PROCEDURE [dbo].[usp_GetOGELNumberByClientId] @ClientId INT = 0
AS
/*
 *[001]		Created		<PERSON><PERSON><PERSON><PERSON>		31-03-2023		Add new dropdown for OGEL number.		

===========================================================================================
TASK      			UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-208619]			Ngai To				16-Jul-2024		UPDATE			US-208619: OGEL approval dropdown to be moved out of code to the setup screen
[US-208619]			Ngai To				23-Jul-2024		UPDATE			US-208619: Change GBOGE2024/0756 - OGEL PCB Comp MIL to GBOGE2024/00756 - OGEL PCB Comp MIL
[US-209534]			NgaiTo		 		17-Sep-2024		UPDATE			209534: OGEL approval dropdown to be moved out of code to the setup screen
===========================================================================================
*/
BEGIN
	CREATE TABLE #tempOGEL (
		ID INT,
		OGELNumber NVARCHAR(MAX)
		)

	INSERT INTO #tempOGEL VALUES(0,'Not Applicable')

	INSERT INTO #tempOGEL
	SELECT OgelId, OgelNumber AS OGELNumber
	FROM tbOGELLicense
	WHERE Inactive = 0
	ORDER BY OgelNumber
	--SELECT 1, ISNULL(OGELNumber, '') AS OGELNumber
	--FROM tbClient
	--WHERE ClientId = @ClientId

	--INSERT INTO #tempOGEL VALUES(2,'GBOGE2024/00532 - OGEL MIL GMST')
	--INSERT INTO #tempOGEL VALUES(3,'GBOGE2024/00756 - OGEL PCB Comp MIL')

	SELECT *
	FROM #tempOGEL
	ORDER BY ID ASC

	DROP TABLE #tempOGEL
END
GO


