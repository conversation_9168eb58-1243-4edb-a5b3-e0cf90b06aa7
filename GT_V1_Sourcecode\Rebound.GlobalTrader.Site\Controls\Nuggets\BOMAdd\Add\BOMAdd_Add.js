Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.BOMAdd_Add=function(n){Rebound.GlobalTrader.Site.Controls.Forms.BOMAdd_Add.initializeBase(this,[n]);this._intNewID=0;this._intCompanyID=0;this._strCompanyName="";this._strOriginalFilename="";this._strGeneratedFilename="";this._IsRecord=!1;this._strGeneratedID="";this._BomCompanyNo=0;this._inActive=!1;this._hasZeroAnswerId=!1;this._firstTimeUpload=!0};Rebound.GlobalTrader.Site.Controls.Forms.BOMAdd_Add.prototype={get_intCompanyID:function(){return this._intCompanyID},set_intCompanyID:function(n){this._intCompanyID!==n&&(this._intCompanyID=n)},get_strCompanyName:function(){return this._strCompanyName},set_strCompanyName:function(n){this._strCompanyName!==n&&(this._strCompanyName=n)},get_tblPVVBOM:function(){return this._tblPVVBOM},set_tblPVVBOM:function(n){this._tblPVVBOM!==n&&(this._tblPVVBOM=n)},get_ibtnEditPPV:function(){return this._ibtnEditPPV},set_ibtnEditPPV:function(n){this._ibtnEditPPV!==n&&(this._ibtnEditPPV=n)},get_ibtnDeletePPV:function(){return this._ibtnDeletePPV},set_ibtnDeletePPV:function(n){this._ibtnDeletePPV!==n&&(this._ibtnDeletePPV=n)},get_ibtnViewPPV:function(){return this._ibtnViewPPV},set_ibtnViewPPV:function(n){this._ibtnViewPPV!==n&&(this._ibtnViewPPV=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.BOMAdd_Add.callBaseMethod(this,"initialize");this.addCancel(Function.createDelegate(this,this.cancelClicked));this.addSave(Function.createDelegate(this,this.saveClicked));this.addShown(Function.createDelegate(this,this.formShown));document.getElementById("removeUploadedFile").addEventListener("click",Function.createDelegate(this,function(n){n.preventDefault();this.removeFile()}))},dispose:function(){this.isDisposed||(this._intNewID=null,this._strGeneratedID=null,this._intCompanyID=null,this._strCompanyName=null,this._strOriginalFilename=null,this._strGeneratedFilename=null,this._tblPVVBOM&&this._tblPVVBOM.dispose(),this._tblPVVBOM=null,this._BomCompanyNo=null,this._ibtnDeletePPV=null,this._ibtnEditPPV=null,this._ibtnViewPPV=null,this._IsRecord=null,this._inActive=null,this._firstTimeUpload=null,Rebound.GlobalTrader.Site.Controls.Forms.BOMAdd_Add.callBaseMethod(this,"dispose"))},cancelClicked:function(){$R_FN.navigateBack()},formShown:function(){this._blnFirstTimeShown&&(this._ctlMail=$find(this.getField("ctlSendMailMessage").ID),this._ctlMail._ctlRelatedForm=this);this._intCompanyID>0?(this.setFieldValue("ctlCompany",this._intCompanyID,null,$R_FN.setCleanTextValue(this._strCompanyName)),this.getContact()):$find(this.getField("ctlCompany").ControlID)&&$find(this.getField("ctlCompany").ControlID)._aut.addSelectionMadeEvent(Function.createDelegate(this,this.getContact));this.getFieldDropDownData("ctlCurrency");this.getFieldDropDownData("ctlSalesman");this.getPPVData();this._strGeneratedFilename?this.showPurchaseHubFields(!0):this.showPurchaseHubFields(!1)},getPPVData:function(){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/BOMPVV");n.set_DataObject("BOMPVV");n.set_DataAction("GetDataTemp");n.addParameter("idGenerated",this._strGeneratedID);n.addDataOK(Function.createDelegate(this,this.getPPVDataOK));n.addError(Function.createDelegate(this,this.getPPVDataError));n.addTimeout(Function.createDelegate(this,this.getPPVDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getPPVDataOK:function(n){var i=n._result,u,r,t;if(this._tblPVVBOM.clearTable(),i.Items){for(r=0;r<i.Items.length;r++)t=i.Items[r],this._BomCompanyNo=t.PVVAnswerId,u=[$R_FN.setCleanTextValue(t.PVVQuestionName),$R_FN.setCleanTextValue(t.PVVAnswerName)],this._tblPVVBOM.addRow(u,t.ID,!1),t=null,u=null,this._tblPVVBOM.resizeColumns();this._IsRecord=i.Items.length>0?!0:!1;this._hasZeroAnswerId=i.Items.some(n=>n.PVVAnswerName==="")}this._inActive=this._BomCompanyNo==0?!1:!0;this.enableButtons(!0)},enableButtons:function(n){n?(this._ibtnEditPPV&&$R_IBTN.enableButton(this._ibtnEditPPV,!0),this._ibtnDeletePPV&&$R_IBTN.enableButton(this._ibtnDeletePPV,n&&this._IsRecord&&this._inActive),this.get_ibtnViewPPV&&$R_IBTN.enableButton(this._ibtnViewPPV,n&&this._IsRecord)):(this.get_ibtnEditPPV&&$R_IBTN.enableButton(this._ibtnEditPPV,!1),this.get_ibtnDeletePPV&&$R_IBTN.enableButton(this._ibtnDeletePPV,!1),this._ibtnViewPPV&&$R_IBTN.enableButton(this._ibtnViewPPV,!1))},getPPVDataError:function(n){this.showError(!0,n.get_ErrorMessage())},getContact:function(){this.getSOData();this.getFieldControl("ctlContact")._intCompanyID=this.getFieldValue("ctlCompany");this.getFieldDropDownData("ctlContact")},getSOData:function(){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/CompanySalesInfo");n.set_DataObject("CompanySalesInfo");n.set_DataAction("GetData");n.addParameter("id",this.getFieldValue("ctlCompany"));n.addDataOK(Function.createDelegate(this,this.getSODataOK));n.addError(Function.createDelegate(this,this.getSODataError));n.addTimeout(Function.createDelegate(this,this.getSODataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getSODataOK:function(n){var t=n._result;this.setFieldValue("ctlCurrency",t.CurrencyNo);this.setFieldValue("ctlContact",t.ContactNo);this.setFieldValue("ctlAS9120",t.IsTraceability)},getSODataError:function(n){this.showError(!0,n.get_ErrorMessage())},saveClicked:function(){if(this.validateForm()){this.showSaving(!0);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/BOMAdd");n.set_DataObject("BOMAdd");n.set_DataAction("AddNew");n.addParameter("Name",this.getFieldValue("ctlName"));n.addParameter("Company",this.getFieldValue("ctlCompany"));n.addParameter("Contact",this.getFieldValue("ctlContact"));n.addParameter("InActive",this.getFieldValue("ctlInActive"));n.addParameter("Notes",this.getFieldValue("ctlNotes"));n.addParameter("CurrencyNo",this.getFieldValue("ctlCurrency"));n.addParameter("CurrentSupplier",this.getFieldValue("ctlCurrentSupplier"));n.addParameter("QuoteRequired",this.getFieldValue("ctlQuoteRequired"));n.addParameter("AS9120",this.getFieldValue("ctlAS9120"));n.addParameter("Contact2",this.getFieldValue("ctlSalesman"));n.addParameter("OriginalFilename",this._strOriginalFilename);n.addParameter("GeneratedFilename",this._strGeneratedFilename);n.addParameter("GeneratedBomID",this._strGeneratedID);n.addParameter("AssignUserNo",this.getFieldValue("ctlBuyer"));let t=this.stripCurrency($find(this.getField("ctlCompany").ControlID)._strSelectedText);n.addParameter("CompanyName",t);n.addParameter("aryRecipientLoginIDs",$R_FN.arrayToSingleString(this._ctlMail._aryRecipientLoginIDs));n.addDataOK(Function.createDelegate(this,this.saveEditComplete));n.addError(Function.createDelegate(this,this.saveEditError));n.addTimeout(Function.createDelegate(this,this.saveEditError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null}},saveEditError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},saveEditComplete:function(n){n._result.Result?n._result.NewID>0&&(this._intNewID=n._result.NewID,this.showSavedOK(!0),this.onSaveComplete()):this.showError(!0,n._result.ValidationMessage)},validateForm:function(){this.onValidate();var n=this.autoValidateFields();return this._strGeneratedFilename&&(this.checkFieldEntered("ctlBuyer")||(n=!1,this.setFieldInError("ctlBuyer",!0,$R_RES.RequiredFieldMissingMessage)),this._hasZeroAnswerId&&(n=!1,this.showError(!0,"Please make sure to answer all the PPV/BOM Qualification questions."))),n},importExcelData:function(n,t){this._strOriginalFilename=n;this._strGeneratedFilename=t;this.showFileProcessing(!0);var i=new Rebound.GlobalTrader.Site.Data;i._intTimeoutMilliseconds=2e5;i.set_PathToData("controls/Nuggets/UtilityBOMImport");i.set_DataObject("UtilityBOMImport");i.set_DataAction("ImportData");i.addParameter("originalFilename",n);i.addParameter("generatedFilename",t);i.addParameter("ClientId",$("#ctl00_ddlClientByMaster_ddl").val());i.addParameter("ColumnHeader","YES");i.addDataOK(Function.createDelegate(this,this.importExcelDataOK));i.addError(Function.createDelegate(this,this.importExcelDataError));i.addTimeout(Function.createDelegate(this,this.importExcelDataError));$R_DQ.addToQueue(i);$R_DQ.processQueue();i=null},importExcelDataOK:function(n){$("#uploadedFileName").text(this._strOriginalFilename);$("#uploadedFile").css("display","block");var t=n._result.IsLimitExceeded;let i=t?n._result.LimitErrorMessage:"BOM Import file upload successfully!";alert(i);this.showFileProcessing(!1);this.showPurchaseHubFields(!0)},importExcelDataError:function(n){alert(n._errorMessage.split("<br/>")[0]);$("#excelipload").prop("disabled",!1).css("opacity",5.5);$("input:file").filter(function(){return this.files.length==0}).prop("disabled",!1);this.showFileProcessing(!1);this._strOriginalFilename=null;this._strGeneratedFilename=null},removeFile:function(){let t=confirm("Are you sure to remove this file?");if(t&&this._strGeneratedFilename){this.showFileProcessing(!0);var n=new Rebound.GlobalTrader.Site.Data;n._intTimeoutMilliseconds=2e5;n.set_PathToData("controls/Nuggets/UtilityBOMImport");n.set_DataObject("UtilityBOMImport");n.set_DataAction("RemoveFile");n.addParameter("generatedFilename",this._strGeneratedFilename);n.addParameter("ClientId",$("#ctl00_ddlClientByMaster_ddl").val());n.addDataOK(Function.createDelegate(this,this.removeFileOK));n.addError(Function.createDelegate(this,this.removeFileError));n.addTimeout(Function.createDelegate(this,this.removeFileError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null}},removeFileOK:function(n){n._result.Status&&($("#excelipload").prop("disabled",!1).css("opacity",5.5),$("input:file").filter(function(){return this.files.length==0}).prop("disabled",!1),this._strOriginalFilename=null,this._strGeneratedFilename=null,$("#uploadedFileName").text(""),$("#uploadedFile").css("display","none"),alert("File remove successfully!"));this.showFileProcessing(!1);this.showPurchaseHubFields(!1)},removeFileError:function(n){alert(n._errorMessage.split("<br/>")[0]);this.showFileProcessing(!1)},showPurchaseHubFields:function(n){n&&this._firstTimeUpload&&($("#ctl00_cphMain_ctlAdd_ctlDB_ctl14_ctlAdd_ctlDB_ctlBuyer_tdTitle").append('<span class="requiredField">*<\/span>'),this.getFieldDropDownData("ctlBuyer"),this._firstTimeUpload=!1);this.showFormField("ctlBuyer",n);this.showFormField("ctlSendMailMessage",n)},stripCurrency:function(n){if(!n)return"";let t=n.lastIndexOf("(");return t>0?n.substring(0,t).trim():n},showFileProcessing:function(n){n?($R_IBTN.enableButton(this._ibtnSave,!1),$R_IBTN.enableButton(this._ibtnSave_Footer,!1),$("#divLoader").show()):($R_IBTN.enableButton(this._ibtnSave,!0),$R_IBTN.enableButton(this._ibtnSave_Footer,!0),$("#divLoader").hide())}};Rebound.GlobalTrader.Site.Controls.Forms.BOMAdd_Add.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.BOMAdd_Add",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);