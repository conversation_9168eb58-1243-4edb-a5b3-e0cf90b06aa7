﻿/*
===========================================================================================  
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-221997]     Phuc Hoang		 25-Nov-2024		CREATE		Product Backlog Item 221997: Lytica - Digest data into GT
===========================================================================================  
*/

GO

UPDATE ly
SET ly.[AveragePrice] = TRY_CONVERT(FLOAT, glb.[50thPercentile])
	,ly.[TargetPrice] = TRY_CONVERT(FLOAT, glb.[70thPercentile])
	,ly.[MarketLeading] = TRY_CONVERT(FLOAT, glb.[MarketLeading])
	,ly.[30thPercentile] = TRY_CONVERT(FLOAT, glb.[30thPercentile])
	,ly.[LifeCycle] = glb.LifecycleRisk
    ,ly.[lifeCycleStatus] = glb.Lifecycle
    ,ly.[OverAllRisk] = glb.OverallRisk
FROM [dbo].[tbGlobalMarketPlaceData] glb
INNER JOIN  [dbo].[tbLyticaAPI] ly 
	ON glb.MPNMatched = ly.OriginalPartSearched AND glb.ManufacturerMatched = ly.Manufacturer

GO

UPDATE glb
SET glb.Updated = 1
FROM [dbo].[tbGlobalMarketPlaceData] glb
INNER JOIN  [dbo].[tbLyticaAPI] ly 
	ON glb.MPNMatched = ly.OriginalPartSearched AND glb.ManufacturerMatched = ly.Manufacturer

GO

INSERT INTO [dbo].[tbLyticaAPI]
           ([Commodity]
           ,[OriginalPartSearched]
           ,[Manufacturer]
           ,[AveragePrice]
           ,[TargetPrice]
           ,[MarketLeading]
           ,[LifeCycle]
           ,[lifeCycleStatus]
           ,[OverAllRisk]
           ,[PartBreadth]
           ,[ManufacturerBreadth]
           ,[DueDiligence]
           ,[PartConcentration]
           ,[UpdatedBy]
           ,[OriginalEntryDate]
           ,[DLUP]
           ,[InActive]
		   ,[30thPercentile])
SELECT 
			glb.LyticaCommodity
           ,glb.MPNMatched
           ,glb.ManufacturerMatched
           ,TRY_CONVERT(FLOAT, glb.[50thPercentile])
           ,TRY_CONVERT(FLOAT, glb.[70thPercentile])
           ,TRY_CONVERT(FLOAT, glb.[MarketLeading])
           ,glb.LifecycleRisk
           ,glb.Lifecycle
           ,glb.OverallRisk
           ,glb.MPNBreadthRisk
           ,glb.MFRBreadthRisk
           ,glb.DueDiligence
           ,glb.MPNConcentration
           ,NULL
           ,glb.[Date]
           ,GETDATE()
           ,0
		   ,TRY_CONVERT(FLOAT, glb.[30thPercentile])
FROM [dbo].[tbGlobalMarketPlaceData] glb
LEFT JOIN [dbo].[tbLyticaAPI] ly 
	ON glb.MPNMatched = ly.OriginalPartSearched AND glb.ManufacturerMatched = ly.Manufacturer
WHERE ly.LyticaAPIId IS NULL

GO