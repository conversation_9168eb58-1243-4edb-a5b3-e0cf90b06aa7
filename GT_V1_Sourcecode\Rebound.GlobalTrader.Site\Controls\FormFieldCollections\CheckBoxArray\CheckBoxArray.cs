using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Collections;
using Rebound.GlobalTrader.Site.Controls.Forms;

namespace Rebound.GlobalTrader.Site.Controls.FormFieldCollections {
	[DefaultProperty("")]
	[ToolboxData("<{0}:CheckBoxArray runat=server></{0}:CheckBoxArray>")]
	public class CheckBoxArray : Base, INamingContainer {

		#region Locals

		HtmlTable _tbl;

		#endregion

		#region Properties

		#endregion

		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
			AddScriptReference("Controls.FormFieldCollections.CheckBoxArray.CheckBoxArray");
			AddScriptReference("Controls.Basic.ImageCheckBox.ImageCheckBox");
			RemoveCSSClass = true;
		}

		protected override void OnLoad(EventArgs e) {
			_tbl = new HtmlTable();
			_tbl.CellPadding = 0;
			_tbl.CellSpacing = 0;
			_tbl.Border = 0;
			_tbl.Attributes["class"] = "checkBoxForm";
			AddControl(_tbl);
			SetupScriptDescriptors();
			base.OnLoad(e);
		}

		private void SetupScriptDescriptors() {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.FormFieldCollections.CheckBoxArray", this.ClientID);
			_scScriptControlDescriptor.AddElementProperty("tbl", _tbl.ClientID);
		}

	}
}
