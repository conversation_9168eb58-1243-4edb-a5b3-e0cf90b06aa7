//-------------------------------------------------------------------------------------------
// RP 12.01.2010:
// - convert values for YTD / Last year as they are returned in base currency
//
// RP 17.11.2009:
// - use specific query for sales info to cut down data going through pipes
// - get the YTD / last year values in one hit
//Marker     Changed by      Date         Remarks
//[001]      Vinay           11/06/2012   This need to Add Incoterms field in company section
//[002]      Shashi Keshar   21/01/2016  Need to add Insurance File No and Insured Amount
//[003]      Suhail          02/05/2018   Added Credit Limit2  
//-------------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets.Data
{
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class CompanySalesInfo : Rebound.GlobalTrader.Site.Data.Base
    {

        public override void ProcessRequest(HttpContext context)
        {
            try
            {

                if (base.init(context))
                {
                    switch (Action)
                    {
                        case "GetData": GetData(); break;
                        case "GetOpenSOs": GetOpenSOs(); break;
                        case "GetOverdueSOs": GetOverdueSOs(); break;
                        case "SaveEdit": SaveEdit(); break;
                        case "GetCreditHistory": GetCreditHistory(); break;
                        case "GetInsuranceHistory": GetInsuranceHistory(); break;
                        case "GetDefaultSalesInfo": GetDefaultSalesInfo(); break;
                        case "GetAddressDetails": GetAddressDetails(); break;
                        case "GetYearToDateNew": GetYearToDateNew(); break;
                        case "GetLastYearNew": GetLastYearNew(); break;
                        default: WriteErrorActionNotFound(); break;
                    }
                }
            }
            catch (Exception ex) {
                var log = ex.Message;
            }
        }

        /// <summary>
        /// Get main data
        /// </summary>
        private void GetData()
        {
            Company cm = Company.GetSalesInfo(ID);
            JsonObject jsn = null;
            if (cm != null)
            {
                jsn = new JsonObject();
                jsn.AddVariable("Salesman", cm.SalesmanName);
                jsn.AddVariable("SalesmanNo", cm.Salesman);
                jsn.AddVariable("IsApproved", cm.SOApproved);
                jsn.AddVariable("Currency", Functions.FormatCurrencyDescription(cm.SOCurrencyDescription, cm.SOCurrencyCode));
                jsn.AddVariable("CurrencyNo", cm.SOCurrencyNo);
                jsn.AddVariable("CurrencyCode", cm.SOCurrencyCode);
                jsn.AddVariable("CustomerNo", cm.CustomerCode);
                jsn.AddVariable("Rating", cm.SORating);
                jsn.AddVariable("OnStop", cm.OnStop);
                jsn.AddVariable("Terms", cm.SOTermsName);
                jsn.AddVariable("TermsNo", cm.SOTermsNo);
                jsn.AddVariable("Tax", cm.SOTaxName);
                jsn.AddVariable("TaxNo", cm.SOTaxNo);
                jsn.AddVariable("ContactName", cm.DefaultSOContactName);
                jsn.AddVariable("ContactNo", cm.DefaultSOContactNo);
                jsn.AddVariable("IsShippingWaived", cm.ShippingCharge);
                jsn.AddVariable("ShipVia", cm.DefaultSalesShipViaName);
                jsn.AddVariable("ShipViaNo", cm.DefaultSalesShipViaNo);
                jsn.AddVariable("FreightVal", Functions.FormatCurrency(cm.DefaultSalesFreightCharge, 2));
                jsn.AddVariable("ShippingCostVal", Functions.FormatCurrency(cm.DefaultSalesShippingCost, 2));
                jsn.AddVariable("ShippingAccountNo", cm.DefaultSalesShipViaAccount);
                string strBillToAddress = Functions.ReplaceLineBreaks(AddressManager.ToLongString(cm.DefaultBillingAddress));
                jsn.AddVariable("BillToAddress", strBillToAddress);
                jsn.AddVariable("ShipToAddressNo", cm.DefaultShippingAddress.AddressId);
                jsn.AddVariable("CreditLimit", Functions.FormatConvertedCurrency(cm.CreditLimit, cm.SOCurrencyNo, cm.SOCurrencyCode, cm.CreditLimit / cm.ExchangeRate, SessionManager.ClientCurrencyID, SessionManager.ClientCurrencyCode, 2));
                jsn.AddVariable("CreditLimitRaw", Functions.FormatCurrency(cm.CreditLimit,null, 2));
                jsn.AddVariable("Current", Functions.FormatConvertedCurrency(cm.CurrentMonth, cm.SOCurrencyNo, cm.SOCurrencyCode, cm.CurrentMonth / cm.ExchangeRate, SessionManager.ClientCurrencyID, SessionManager.ClientCurrencyCode, 2));
                jsn.AddVariable("Balance", Functions.FormatConvertedCurrency(cm.Balance, cm.SOCurrencyNo, cm.SOCurrencyCode, cm.Balance / cm.ExchangeRate, SessionManager.ClientCurrencyID, SessionManager.ClientCurrencyCode, 2));
                jsn.AddVariable("Days30", Functions.FormatConvertedCurrency(cm.Days30, cm.SOCurrencyNo, cm.SOCurrencyCode, cm.Days30 / cm.ExchangeRate, SessionManager.ClientCurrencyID, SessionManager.ClientCurrencyCode, 2));
                jsn.AddVariable("Days60", Functions.FormatConvertedCurrency(cm.Days60, cm.SOCurrencyNo, cm.SOCurrencyCode, cm.Days60 / cm.ExchangeRate, SessionManager.ClientCurrencyID, SessionManager.ClientCurrencyCode, 2));
                jsn.AddVariable("Days90", Functions.FormatConvertedCurrency(cm.Days90, cm.SOCurrencyNo, cm.SOCurrencyCode, cm.Days90 / cm.ExchangeRate, SessionManager.ClientCurrencyID, SessionManager.ClientCurrencyCode, 2));
                jsn.AddVariable("Days120", Functions.FormatConvertedCurrency(cm.Days120, cm.SOCurrencyNo, cm.SOCurrencyCode, cm.Days120 / cm.ExchangeRate, SessionManager.ClientCurrencyID, SessionManager.ClientCurrencyCode, 2));
                jsn.AddVariable("DLUP", Functions.FormatDLUP(cm.DLUP, cm.UpdatedBy));
                jsn.AddVariable("ApprovedByAndDate", Functions.FormatUPbyOn(cm.SOApprovedDate, cm.SOApprovedBy));
                jsn.AddVariable("IsTraceability", cm.IsTraceability);
                jsn.AddVariable("ApprovedByAndDate", Functions.FormatUPbyOn(cm.SOApprovedDate, cm.SOApprovedBy));

                jsn.AddVariable("InsuranceFileNo", cm.InsuranceFileNo);
                jsn.AddVariable("InsuredAmountRaw", Functions.FormatCurrency(cm.InsuredAmount, null, 2));
                //jsn.AddVariable("InsuredAmount", Functions.FormatConvertedCurrency(cm.InsuredAmount, cm.SOCurrencyNo, cm.SOCurrencyCode, cm.InsuredAmount / cm.ExchangeRate, SessionManager.ClientCurrencyID, SessionManager.ClientCurrencyCode, 2));
                jsn.AddVariable("InsuredAmount", Functions.FormatConvertedCurrency(cm.InsuredAmount, cm.InsuredAmountCurrencyNo, cm.InsuredAmountCurrencyCode, cm.InsuredAmount / cm.ExchangeRate, SessionManager.ClientCurrencyID, SessionManager.ClientCurrencyCode, 2));
                jsn.AddVariable("StopStatus", cm.StopStatus);
                jsn.AddVariable("GlobalCurNo", cm.GlobalCurrencyNo);
                jsn.AddVariable("Days1", Functions.FormatConvertedCurrency(cm.Days1, cm.SOCurrencyNo, cm.SOCurrencyCode, cm.Days1 / cm.ExchangeRate, SessionManager.ClientCurrencyID, SessionManager.ClientCurrencyCode, 2));
                jsn.AddVariable("WarehouseName", cm.WarehouseName);
                jsn.AddVariable("WarehouseNo", cm.WarehouseNo);
                //this year SO value to date
                double? dblSalesOrderYTD = 0;
                BLL.Company cmThisYear = BLL.Company.SummariseThisYearSalesOrderValue(ID);
                if (cmThisYear != null) dblSalesOrderYTD = cmThisYear.SalesOrderValueYTDInBase;
                jsn.AddVariable("ThisYearValue", Functions.FormatCurrency(dblSalesOrderYTD, cm.SOCurrencyCode, 2));
                cmThisYear = null;

                //last year SO value
                double? dblValueLastYear = 0;
                BLL.Company cmLastYear = BLL.Company.SummariseLastYearSalesOrderValue(ID);
                if (cmLastYear != null) dblValueLastYear = cmLastYear.SalesOrderValueLastYearInBase;
                jsn.AddVariable("LastYearValue", Functions.FormatCurrency(dblValueLastYear, cm.SOCurrencyCode, 2));
                cmLastYear = null;

                double dblOpenSOTotal = 0;
                DateTime currencyDate;
                List<SalesOrder> lst = SalesOrder.GetListOpenForCompany(ID);
                for (int i = 0; i < lst.Count; i++)
                {
                    SalesOrder SOTotal = SalesOrder.GetOpenLineSummaryValues(lst[i].SalesOrderId);
                    currencyDate = lst[i].CurrencyDate == null ? (DateTime)lst[i].DateOrdered : (DateTime)lst[i].CurrencyDate;
                    if (SOTotal.CurrencyNo != cm.SOCurrencyNo)
                    {
                        dblOpenSOTotal += BLL.Currency.ConvertValueBetweenTwoCurrencies(SOTotal.TotalValue, SOTotal.CurrencyNo, Convert.ToInt32(cm.SOCurrencyNo), currencyDate);
                    }
                    else
                    {
                        dblOpenSOTotal += Convert.ToDouble(SOTotal.TotalValue);
                    }
                    SOTotal = null;
                }
                lst = null;
                double dblBalanceWithOpenOrders = Convert.ToDouble(cm.Balance) + dblOpenSOTotal;
                jsn.AddVariable("BalanceWithOpenSalesOrders", Functions.FormatCurrency(dblBalanceWithOpenOrders, cm.SOCurrencyCode,2));
                jsn.AddVariable("BalanceWithOpenSalesOrdersVal", dblBalanceWithOpenOrders);
                jsn.AddVariable("InvoiceNotExport", Functions.FormatCurrency(SalesOrder.GetInvoiceNotExported(ID), cm.SOCurrencyCode, 2));

                jsn.AddVariable("NotesToInvoice", Functions.ReplaceLineBreaks(cm.NotesToInvoice));
                // [002] Code Start
                jsn.AddVariable("ActualCreditLimit", Functions.FormatConvertedCurrency(cm.ActualCreditLimit, cm.SOCurrencyNo, cm.SOCurrencyCode, cm.ActualCreditLimit / cm.ExchangeRate, SessionManager.ClientCurrencyID, SessionManager.ClientCurrencyCode, 2));
                jsn.AddVariable("ActualCreditLimitRaw", Functions.FormatCurrency(cm.ActualCreditLimit, null, 2));
                jsn.AddVariable("InsuredAmountCurrencyNo", cm.InsuredAmountCurrencyNo);

                string companyNotes = Company.GetAdvisoryNotes(ID);
                jsn.AddVariable("CompanyAdvisoryNotes", Functions.ReplaceLineBreaks(companyNotes));
                jsn.AddVariable("Inactive", cm.Inactive);

                // [002] Code End
                cm = null;
                OutputResult(jsn);
                jsn.Dispose();
                jsn = null;
            }
        }

        /// <summary>
        /// Gets the open sales orders
        /// </summary>
        private void GetOpenSOs()
        {
            JsonObject jsn = new JsonObject();
            JsonObject jsnItems = new JsonObject(true);
            List<SalesOrder> lst = SalesOrder.GetListOpenForCompany(ID);
            for (int i = 0; i < lst.Count; i++)
            {
                JsonObject jsnItem = new JsonObject();
                jsnItem.AddVariable("ID", lst[i].SalesOrderId);
                jsnItem.AddVariable("No", lst[i].SalesOrderNumber);
                jsnItem.AddVariable("Date", Functions.FormatDate(lst[i].DateOrdered));
                SalesOrder SOTotal = SalesOrder.GetSummaryValues(lst[i].SalesOrderId);
                jsnItem.AddVariable("Amount", Functions.FormatCurrency(SOTotal.TotalValue, lst[i].CurrencyCode, 2));
                SOTotal = null;
                jsnItems.AddVariable(jsnItem);
                jsnItem.Dispose(); jsnItem = null;
            }
            lst = null;
            jsn.AddVariable("Items", jsnItems);
            OutputResult(jsn);
            jsn.Dispose(); jsn = null;
        }

        /// <summary>
        /// Gets the overdue sales orders
        /// </summary>
        private void GetOverdueSOs()
        {
            JsonObject jsn = new JsonObject();
            JsonObject jsnItems = new JsonObject(true);
            List<SalesOrder> lst = SalesOrder.GetListOverdueForCompany(ID);
            for (int i = 0; i < lst.Count; i++)
            {
                JsonObject jsnItem = new JsonObject();
                jsnItem.AddVariable("ID", lst[i].SalesOrderId);
                jsnItem.AddVariable("No", lst[i].SalesOrderNumber);
                jsnItem.AddVariable("Date", Functions.FormatDate(lst[i].DateOrdered));
                SalesOrder SOTotal = SalesOrder.GetSummaryValues(lst[i].SalesOrderId);
                jsnItem.AddVariable("Amount", Functions.FormatCurrency(SOTotal.TotalValue, lst[i].CurrencyCode, 2));
                SOTotal = null;
                jsnItems.AddVariable(jsnItem);
                jsnItem.Dispose(); jsnItem = null;
            }
            lst = null;
            jsn.AddVariable("Items", jsnItems);
            OutputResult(jsn);
            jsn.Dispose(); jsn = null;
        }

        /// <summary>
        /// Save Edit
        /// </summary>
        public void SaveEdit()
        {
            try
            {
                bool blnResult = BLL.Company.UpdateSalesInfo(
                    ID
                    , GetFormValue_String("CustomerNo")
                    , GetFormValue_NullableInt("Salesman")
                    , GetFormValue_Boolean("IsApproved")
                    , GetFormValue_NullableInt("Rating")
                    , GetFormValue_NullableInt("TermsNo")
                    , GetFormValue_NullableInt("CurrencyNo")
                    , GetFormValue_NullableInt("TaxNo")
                    , GetFormValue_NullableInt("ContactNo")
                    , GetFormValue_NullableInt("ShipViaNo")
                    , GetFormValue_String("ShippingAccountNo")
                    , GetFormValue_Boolean("OnStop")
                    , GetFormValue_Boolean("IsShippingWaived")
                    , GetFormValue_NullableDouble("CreditLimit")
                     , GetFormValue_String("InsuranceFileNo")
                    , GetFormValue_NullableDouble("InsuredAmount")
                    , SessionManager.LoginID
                     , GetFormValue_String("StopStatus")
                      , GetFormValue_String("NotesToInvoice")
                      // [002] Code Start
                      , GetFormValue_NullableDouble("ActualCreditLimit")
                    // [002] Code End
                    , GetFormValue_NullableInt("WarehouseNo")
                    , GetFormValue_NullableInt("InsuredAmountCurrencyNo")

                    );
                //write result to JSON
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Result", blnResult);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

        /// <summary>
        /// Get Year to Date amount
        /// </summary>
        private void GetYearToDate()
        {
            BLL.Company cm = BLL.Company.SummariseThisYearSalesOrderValue(ID);
            if (cm == null)
            {
                WriteErrorDataNotFound();
            }
            else
            {
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Value", Functions.FormatCurrency(cm.SalesOrderValueYTDInBase, cm.SOCurrencyCode, 2));
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            cm = null;
        }

        /// <summary>
        /// Get last year amount
        /// </summary>
        private void GetLastYear()
        {
            BLL.Company cm = BLL.Company.SummariseLastYearSalesOrderValue(ID);
            if (cm == null)
            {
                WriteErrorDataNotFound();
            }
            else
            {
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Value", Functions.FormatCurrency(cm.SalesOrderValueLastYearInBase, cm.SOCurrencyCode, 2));
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            cm = null;
        }

        /// <summary>
        /// Gets the credit history (credit limit changes) for this company
        /// </summary>
        private void GetCreditHistory()
        {
            JsonObject jsn = new JsonObject();
            JsonObject jsnItems = new JsonObject(true);
            List<Audit> lst = Audit.GetListCreditHistoryForCompany(ID);
            for (int i = 0; i < lst.Count; i++)
            {
                JsonObject jsnItem = new JsonObject();
                jsnItem.AddVariable("ID", lst[i].AuditId);
                jsnItem.AddVariable("From", lst[i].CreditLimit);
                jsnItem.AddVariable("To", lst[i].CreditLimitNew);
                jsnItem.AddVariable("Date", Functions.FormatDate(lst[i].DLUP));
                jsnItem.AddVariable("Time", Functions.FormatTime(lst[i].DLUP));
                jsnItem.AddVariable("By", lst[i].EmployeeName);
                jsnItems.AddVariable(jsnItem);
                jsnItem.Dispose(); jsnItem = null;
            }
            lst = null;
            jsn.AddVariable("CreditHist", jsnItems);
            OutputResult(jsn);
            jsn.Dispose(); jsn = null;
        }

        /// <summary>
        /// Gets the credit history (credit limit changes) for this company
        /// </summary>
        private void GetInsuranceHistory()
        {
            JsonObject jsn = new JsonObject();
            JsonObject jsnItems = new JsonObject(true);
            List<Audit> lst = Audit.GetListInsuranceHistoryForCompany(ID);
            for (int i = 0; i < lst.Count; i++)
            {
                JsonObject jsnItem = new JsonObject();
                jsnItem.AddVariable("ID", lst[i].InsHistoryId);
                jsnItem.AddVariable("From", lst[i].InsuredAmount);
                jsnItem.AddVariable("To", lst[i].InsuredAmountNew);
                jsnItem.AddVariable("Date", Functions.FormatDate(lst[i].DLUP));
                jsnItem.AddVariable("Time", Functions.FormatTime(lst[i].DLUP));
                jsnItem.AddVariable("By", lst[i].EmployeeName);
                jsnItems.AddVariable(jsnItem);
                jsnItem.Dispose(); jsnItem = null;
            }
            lst = null;
            jsn.AddVariable("InsuranceHist", jsnItems);
            OutputResult(jsn);
            jsn.Dispose(); jsn = null;
        }
        private void GetDefaultSalesInfo()
        {
            Company cm = Company.GetDefaultSalesInfo(ID);
            JsonObject jsn = new JsonObject();
            jsn.AddVariable("FreightVal", Functions.FormatCurrency(cm.DefaultSalesFreightCharge, 2));
            int? intDocumentCurrencyNo = GetFormValue_NullableInt("DocCurrencyNo", null);
            if (intDocumentCurrencyNo == null) intDocumentCurrencyNo = cm.SOCurrencyNo;
            if (intDocumentCurrencyNo != null && intDocumentCurrencyNo != SessionManager.ClientCurrencyID)
            {
                jsn.AddVariable("FreightInDocCurrency", Functions.FormatCurrency(Currency.ConvertValueFromBaseCurrency(cm.DefaultSalesFreightCharge, (int)intDocumentCurrencyNo, (DateTime)GetFormValue_NullableDateTime("DocDate", DateTime.Now)), 2));
            }
            jsn.AddVariable("ShippingCostVal", Functions.FormatCurrency(cm.DefaultSalesShippingCost, 2));
            jsn.AddVariable("Currency", Functions.FormatCurrencyDescription(cm.SOCurrencyDescription, cm.SOCurrencyCode));
            jsn.AddVariable("CurrencyNo", cm.SOCurrencyNo);
            jsn.AddVariable("ShipViaNo", cm.DefaultSalesShipViaNo);
            jsn.AddVariable("Tax", cm.SOTaxName);
            jsn.AddVariable("TaxNo", cm.SOTaxNo);
            jsn.AddVariable("CurrencyCode", cm.SOCurrencyCode);
            jsn.AddVariable("BillToAddress", Functions.ReplaceLineBreaks(AddressManager.ToLongString(cm.DefaultBillingAddress)));
            jsn.AddVariable("ShipToAddressNo", cm.DefaultShippingAddress.AddressId);
            jsn.AddVariable("OGEL", cm.DefaultShippingAddress.OGEL);
            // ESMS #14
            jsn.AddVariable("TaxbyAddress", cm.DefaultShippingAddress.TaxbyAddress);
            jsn.AddVariable("TaxValue", cm.DefaultShippingAddress.TaxValue);
            // End
            //[001] code start
            jsn.AddVariable("Incoterm", cm.DefaultShippingAddress.IncotermName);
            jsn.AddVariable("IncotermNo", cm.DefaultShippingAddress.IncotermNo);
            //[001] code end
            //jsn.AddVariable("ShippingAccountNo", cm.DefaultSalesShipViaAccount);
            jsn.AddVariable("ShippingAccountNo", cm.DefaultShippingAddress.ShipViaAccount);
            jsn.AddVariable("Terms", cm.SOTermsName);
            jsn.AddVariable("TermsNo", cm.SOTermsNo);
            jsn.AddVariable("ContactNo", cm.DefaultSOContactNo);
            jsn.AddVariable("IsShippingWaived", cm.ShippingCharge);
            jsn.AddVariable("Traceability", cm.IsTraceability);
            jsn.AddVariable("GlobalCurrencyNo", cm.GlobalCurrencyNo);
            jsn.AddVariable("ShipViaDefaultSPAdrShipViaNo", cm.DefaultShippingAddress.ShipViaNo);
            //new parameter
            BLL.ShipVia sv = null;
            if (cm.DefaultShippingAddress.ShipViaNo != null)
            {
                sv = BLL.ShipVia.Get(cm.DefaultShippingAddress.ShipViaNo, SessionManager.ClientID);
            }

            if (sv != null)
            {
                //jsn.AddVariable("ShipViaDefaultSPAdrShipViaNo", cm.DefaultShippingAddress.ShipViaNo);
                jsn.AddVariable("ShipViaDivisionHeaderNo", sv.DivisionHeaderNo);
                jsn.AddVariable("ShipViaTaxbyAddress", sv.TaxNo);
                jsn.AddVariable("ShipViaIncotermNo", sv.IncotermNo);
                jsn.AddVariable("ShipViaTaxValue", sv.TaxName);
                //added for cost and shipping cost
                jsn.AddVariable("ShippingCostVal", Functions.FormatCurrency(sv.Cost, 2));
                //jsn.AddVariable("FreightVal", Functions.FormatCurrency(BLL.Currency.ConvertValueFromBaseCurrency(sv.Charge, GetFormValue_Int("SOCurrencyNo"), GetFormValue_DateTime("SODate")), 2));
                //jsn.AddVariable("CurrencyChangeOnly", GetFormValue_Boolean("CurrencyChangeOnly"));
                jsn.AddVariable("FreightVal", Functions.FormatCurrency(sv.Charge, 2));
                jsn.AddVariable("IsSameAsShipCost", Convert.ToBoolean(sv.IsSameAsShipCost));
                if (intDocumentCurrencyNo == null) intDocumentCurrencyNo = cm.SOCurrencyNo;
                if (intDocumentCurrencyNo != null && intDocumentCurrencyNo != SessionManager.ClientCurrencyID)
                {
                    jsn.AddVariable("FreightInDocCurrency", Functions.FormatCurrency(Currency.ConvertValueFromBaseCurrency(sv.Charge, (int)intDocumentCurrencyNo, (DateTime)GetFormValue_NullableDateTime("DocDate", DateTime.Now)), 2));
                }
                //code end
            }
            else
            {

                //jsn.AddVariable("ShipViaDefaultSPAdrShipViaNo", cm.DefaultShippingAddress.ShipViaNo);
                jsn.AddVariable("ShipViaDivisionHeaderNo", 0);
                jsn.AddVariable("ShipViaTaxbyAddress", 0);
                jsn.AddVariable("ShipViaIncotermNo", 0);
                jsn.AddVariable("ShipViaTaxValue", "");
                jsn.AddVariable("ShippingCostVal", 0);
                jsn.AddVariable("FreightVal", 0);
            }
            //jsn.AddVariable("ShipToDefaultSPAdrShipViaNo", cm.DefaultShippingAddress.ShipViaNo);
            jsn.AddVariable("ShipToDivisionHeaderNo", cm.DefaultShippingAddress.DivisionHeaderNo);
            jsn.AddVariable("ShipToIncotermNo", cm.DefaultShippingAddress.IncotermNo);
            jsn.AddVariable("ShipToTaxbyAddress", cm.DefaultShippingAddress.TaxbyAddress);
            jsn.AddVariable("ShipToTaxValue", cm.DefaultShippingAddress.TaxValue);
            sv = null;

            cm = null;
            OutputResult(jsn);
            jsn.Dispose();
            jsn = null;
        }
        //      private void GetDefaultSalesInfo() {
        //	Company cm = Company.GetDefaultSalesInfo(ID);
        //	JsonObject jsn = new JsonObject();
        //          string SetDefultAddNewSales= GetFormValue_String("AddSales");
        //          jsn.AddVariable("FreightVal", Functions.FormatCurrency(cm.DefaultSalesFreightCharge, 2));
        //	int? intDocumentCurrencyNo = GetFormValue_NullableInt("DocCurrencyNo", null);
        //	if (intDocumentCurrencyNo == null) intDocumentCurrencyNo = cm.SOCurrencyNo;
        //	if (intDocumentCurrencyNo != null && intDocumentCurrencyNo != SessionManager.ClientCurrencyID) {
        //		jsn.AddVariable("FreightInDocCurrency", Functions.FormatCurrency(Currency.ConvertValueFromBaseCurrency(cm.DefaultSalesFreightCharge, (int)intDocumentCurrencyNo, (DateTime)GetFormValue_NullableDateTime("DocDate", DateTime.Now)), 2));
        //	}
        //	jsn.AddVariable("ShippingCostVal", Functions.FormatCurrency(cm.DefaultSalesShippingCost, 2));
        //	jsn.AddVariable("Currency", Functions.FormatCurrencyDescription(cm.SOCurrencyDescription, cm.SOCurrencyCode));
        //	jsn.AddVariable("CurrencyNo", cm.SOCurrencyNo);
        //	jsn.AddVariable("ShipViaNo", cm.DefaultSalesShipViaNo);
        //	jsn.AddVariable("Tax", cm.SOTaxName);
        //	jsn.AddVariable("TaxNo", cm.SOTaxNo);
        //	jsn.AddVariable("CurrencyCode", cm.SOCurrencyCode);
        //	jsn.AddVariable("BillToAddress", Functions.ReplaceLineBreaks(AddressManager.ToLongString(cm.DefaultBillingAddress)));
        //	jsn.AddVariable("ShipToAddressNo", cm.DefaultShippingAddress.AddressId);
        //          // ESMS #14
        //          //jsn.AddVariable("TaxbyAddress", cm.DefaultShippingAddress.TaxbyAddress);
        //          //jsn.AddVariable("TaxValue", cm.DefaultShippingAddress.TaxValue);
        //	// End
        //          //[001] code start
        //          jsn.AddVariable("Incoterm", cm.DefaultShippingAddress.IncotermName);
        //          if ((SetDefultAddNewSales == "NewSalesOrder") && (cm.DefaultShippingAddress.ShipViaNo > 0 && cm.DefaultShippingAddress.ShipViaNo != null))
        //              {
        //                  BLL.ShipVia sv = BLL.ShipVia.Get(cm.DefaultShippingAddress.ShipViaNo, SessionManager.ClientID);
        //                  if (sv == null)
        //                  {
        //                      jsn.AddVariable("DefaultShippingAddressShipViaNo", cm.DefaultShippingAddress.ShipViaNo);
        //                      jsn.AddVariable("IncotermNo", cm.DefaultShippingAddress.IncotermNo);
        //                      jsn.AddVariable("TaxbyAddress", cm.DefaultShippingAddress.TaxbyAddress);
        //                      jsn.AddVariable("TaxValue", cm.DefaultShippingAddress.TaxValue);
        //                      //jsn.AddVariable("DivisionHeaderNo", cm.DefaultShippingAddress.DivisionHeaderNo);
        //              }
        //                  else
        //                  {
        //                      jsn.AddVariable("DefaultShippingAddressShipViaNo", cm.DefaultShippingAddress.ShipViaNo);
        //                      jsn.AddVariable("DivisionHeaderNo", sv.DivisionHeaderNo);
        //                      jsn.AddVariable("TaxbyAddress", sv.TaxNo);
        //                      jsn.AddVariable("IncotermNo", sv.IncotermNo);
        //                      jsn.AddVariable("TaxValue", sv.TaxName);

        //                  }
        //                  sv = null;
        //              }
        //              else
        //              {
        //                  jsn.AddVariable("DefaultShippingAddressShipViaNo", cm.DefaultShippingAddress.ShipViaNo);
        //                  jsn.AddVariable("IncotermNo", cm.DefaultShippingAddress.IncotermNo);
        //                  jsn.AddVariable("TaxbyAddress", cm.DefaultShippingAddress.TaxbyAddress);
        //                  jsn.AddVariable("TaxValue", cm.DefaultShippingAddress.TaxValue);
        //                  //jsn.AddVariable("DivisionHeaderNo", cm.DefaultShippingAddress.DivisionHeaderNo);
        //          }


        //          //[001] code end
        //          jsn.AddVariable("ShippingAccountNo", cm.DefaultSalesShipViaAccount);
        //          jsn.AddVariable("Terms", cm.SOTermsName);
        //          jsn.AddVariable("TermsNo", cm.SOTermsNo);
        //          jsn.AddVariable("ContactNo", cm.DefaultSOContactNo);
        //	jsn.AddVariable("IsShippingWaived", cm.ShippingCharge);
        //          jsn.AddVariable("Traceability", cm.IsTraceability);
        //          jsn.AddVariable("GlobalCurrencyNo", cm.GlobalCurrencyNo);

        //          cm = null;
        //	OutputResult(jsn);
        //	jsn.Dispose();
        //	jsn = null;
        //}
        private void GetAddressDetails()
        {
            Address Add1 = Company.GetShippingAddress(ID);
            JsonObject jsn = new JsonObject();
            jsn.AddVariable("TaxbyAddress", Add1.TaxbyAddress);
            jsn.AddVariable("TaxValue", Add1.TaxValue);
            //[001] code start
            jsn.AddVariable("Incoterm", Add1.IncotermName);
            jsn.AddVariable("IncotermNo", Add1.IncotermNo);
            //[001] code end
            jsn.AddVariable("ShipViaAccountNo", Add1.ShipViaAccount);
            jsn.AddVariable("IsOGELAddress", Add1.OGEL);
            Add1 = null;
            OutputResult(jsn);
            jsn.Dispose();
            jsn = null;
        }

        private void GetYearToDateNew()
        {
            BLL.Company cm = BLL.Company.SummariseThisYearandLastYearSalesValue(ID, true);
            JsonObject jsn = new JsonObject();
            if (cm == null)
            {
                jsn.AddVariable("Value", Functions.FormatCurrency(0, 2));
            }
            else
            {
                jsn.AddVariable("Value", string.Format("{0} | {1} ({2})", Functions.FormatCurrency(cm.SalesResale, SessionManager.ClientCurrencyCode, 2), Functions.FormatCurrency(cm.SalesGrossProfit, SessionManager.ClientCurrencyCode, 2), Functions.FormatPercentage(cm.Margin, 2)));
            }
            OutputResult(jsn);
            jsn.Dispose();
            cm = null;
        }

        private void GetLastYearNew()
        {
            BLL.Company cm = BLL.Company.SummariseThisYearandLastYearSalesValue(ID, false);
            JsonObject jsn = new JsonObject();
            if (cm == null)
            {
                jsn.AddVariable("Value", Functions.FormatCurrency(0, 2));
            }
            else
            {
                jsn.AddVariable("Value", string.Format("{0} | {1} ({2})", Functions.FormatCurrency(cm.SalesResale, SessionManager.ClientCurrencyCode, 2), Functions.FormatCurrency(cm.SalesGrossProfit, SessionManager.ClientCurrencyCode, 2), Functions.FormatPercentage(cm.Margin, 2)));
            }
            OutputResult(jsn);
            jsn.Dispose();
            cm = null;
        }
    }
}
