Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DataListNuggets");Rebound.GlobalTrader.Site.Controls.DataListNuggets.ProspectiveOffers=function(n){Rebound.GlobalTrader.Site.Controls.DataListNuggets.ProspectiveOffers.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DataListNuggets.ProspectiveOffers.prototype={initialize:function(){this.showLeftPanel();this.addSetupDataCallEvent(Function.createDelegate(this,this.setupDataCall));this.addGetDataOKEvent(Function.createDelegate(this,this.getDataOK));this.addInitCompleteEvent(Function.createDelegate(this,this.initAfterBaseIsReady));this._strPathToData="controls/DataListNuggets/ProspectiveOffers";this._strDataObject="ProspectiveOffers";Rebound.GlobalTrader.Site.Controls.DataListNuggets.ProspectiveOffers.callBaseMethod(this,"initialize")},initAfterBaseIsReady:function(){this.addPageTabChangedEvent(Function.createDelegate(this,this.pageTabChanged));this.updateFilterVisibility();this.getData()},dispose:function(){this.isDisposed||(this._intSalesPersonID=null,Rebound.GlobalTrader.Site.Controls.DataListNuggets.ProspectiveOffers.callBaseMethod(this,"dispose"))},pageTabChanged:function(){this._table._intCurrentPage=1;this._enmViewLevel=this._intCurrentTab;this.updateFilterVisibility();this.getData()},setupDataCall:function(){this._objData.addParameter("ViewLevel",this._enmViewLevel)},getDataOK:function(){for(var n,i,t=0,r=this._objResult.Results.length;t<r;t++)n=this._objResult.Results[t],i=[$R_FN.createNubButton("Ord_ProspectiveCSDetail.aspx?PROId="+n.ID,n.ID),$R_FN.setCleanTextValue(n.ImportDate),$R_FN.setCleanTextValue(n.Source),n.Rows,$R_FN.setCleanTextValue(n.ImportStatus),$R_FN.setCleanTextValue(n.Supplier),$R_FN.setCleanTextValue(n.ImportedBy)],this._table.addRow(i,n.ID,!1),i=null,n=null},updateFilterVisibility:function(){this.getFilterField("ctlImportedBy").show(this._enmViewLevel!=0)},showLeftPanel:function(){var n=document.getElementById("ctl00_pnlLeftButton"),t;n!=undefined&&n.classList.contains("leftbarButton_Off")&&(t=$("#ctl00_pnlLeftButton a:first"),t[0].click())}};Rebound.GlobalTrader.Site.Controls.DataListNuggets.ProspectiveOffers.registerClass("Rebound.GlobalTrader.Site.Controls.DataListNuggets.ProspectiveOffers",Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base,Sys.IDisposable);