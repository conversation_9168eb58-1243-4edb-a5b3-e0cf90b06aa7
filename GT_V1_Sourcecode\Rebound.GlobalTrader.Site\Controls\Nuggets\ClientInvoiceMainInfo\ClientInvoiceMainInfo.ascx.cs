//Marker     Changed by      Date               Remarks
//[001]      Vinay           12/06/2013         CR:- Client Invoice
using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site.Controls;
using Rebound.GlobalTrader.Site;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets {
    public partial class ClientInvoiceMainInfo : Base
    {

		#region Locals

		protected IconButton _ibtnEdit;
		protected IconButton _ibtnNotify;

		#endregion

		#region Properties

        private int _intClientInvoiceID = -1;
		public int ClientInvoiceID {
            get { return _intClientInvoiceID; }
            set { _intClientInvoiceID = value; }
		}

		private bool _blnCanEdit = true;
		public bool CanEdit {
			get { return _blnCanEdit; }
			set { _blnCanEdit = value; }
		}


		private bool _blnCanNotify = true;
		public bool CanNotify {
			get { return _blnCanNotify; }
			set { _blnCanNotify = value; }
		}

       

        private bool _blnCanEditURNNumber = true;
        public bool CanEditURNNumber
        {
            get { return _blnCanEditURNNumber; }
            set { _blnCanEditURNNumber = value; }
        }

		#endregion

		#region Overrides

		/// <summary>
		/// Oninit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
			WireUpControls();
            AddScriptReference("Controls.Nuggets.ClientInvoiceMainInfo.ClientInvoiceMainInfo.js");
            TitleText = Functions.GetGlobalResource("Nuggets", "ClientInvoiceMainInfo");
            if (_objQSManager.ClientInvoiceID > 0) _intClientInvoiceID = _objQSManager.ClientInvoiceID;

		}

		protected override void OnPreRender(EventArgs e) {
            _ibtnEdit.Visible = _blnCanEdit;
            _ibtnNotify.Visible = _blnCanNotify;

           
            
			SetupScriptDescriptors();
			base.OnPreRender(e);
		}

		#endregion

		/// <summary>
		/// Sets up AJAX script descriptors
		/// </summary>
		private void SetupScriptDescriptors() {
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Nuggets.ClientInvoiceMainInfo", ctlDesignBase.ClientID);
			if (_blnCanEdit) _scScriptControlDescriptor.AddElementProperty("ibtnEdit", _ibtnEdit.ClientID);
			if (_blnCanNotify) _scScriptControlDescriptor.AddElementProperty("ibtnNotify", _ibtnNotify.ClientID);
            _scScriptControlDescriptor.AddProperty("intClientInvoiceID", _intClientInvoiceID);
            _scScriptControlDescriptor.AddProperty("blnCanEditMainInfo", _blnCanEdit);
            _scScriptControlDescriptor.AddProperty("blnCanEditURNNumber", _blnCanEditURNNumber);
		}

		/// <summary>
		/// Wire up controls from the ascx
		/// </summary>
		private void WireUpControls() {
			_ibtnEdit = FindIconButton("ibtnEdit");
			_ibtnNotify = FindIconButton("ibtnNotify");
		}

	}
}
