﻿CREATE OR ALTER PROCEDURE usp_selectAll_SystemWarningMessage_for_Client    
@ClientNo int                
AS                 
/*              
Action: Created      Action By: <PERSON><PERSON><PERSON><PERSON>      Dated: 20-10-2021              
*/              
    SELECT  SystemWarningMessageId                
          , swm.ClientNo                 
          , wm.WarningName AS  WarningKey                
          , WarningText                
          , swm.DLUP                
          , swm.UpdatedBy               
    , CASE WHEN swm.ApplyToCatagoryNo=1 THEN 'Manufacturer'              
              WHEN swm.ApplyToCatagoryNo=2 THEN 'Product'              
           WHEN swm.ApplyToCatagoryNo=3 THEN 'Vendor'              
           ELSE '' END AS ApplyToCatagory               
          ,CASE WHEN swm.ApplyTo=0 THEN 'ALL' ELSE  CASE WHEN swm.ApplyToCatagoryNo=1 THEN mn.ManufacturerName              
           WHEN swm.ApplyToCatagoryNo=2 THEN pd.ProductName +' --> ' +pd.ProductDescription              
     WHEN swm.ApplyToCatagoryNo=3 THEN cm.CompanyName ELSE '' END END              
     AS [Applyto]              
  ,swm.InActive            
  ,pd.ProductName          
  ,pd.ProductDescription        
  ---- added to get the product top category       
  ,(select ProductCategoryNo from tbGlobalProductName where tbGlobalProductName.globalproductnameid = (select top 1 gp.GlobalProductNameNo from tbGlobalProduct gp where gp.GlobalProductId = pd.GlobalProductNo)) as GlobalProductcategoryId          
  ----- end here    
  ,(select top 1 gp.GlobalProductNameNo from tbGlobalProduct gp  where gp.GlobalProductId=pd.GlobalProductNo) as GlobalProductNameId          
  ,pd.GlobalProductNo  as ProductId        
    FROM    dbo.tbSystemWarningMessage  swm               
     INNER JOIN dbo.tbWarningMessage wm ON swm.WarningNo=wm.WarningId              
  LEFT JOIN dbo.tbManufacturer mn ON swm.ApplyTo=mn.ManufacturerId              
  LEFT JOIN dbo.tbProduct pd ON swm.ApplyTo=pd.ProductId              
  LEFT JOIN dbo.tbCompany cm ON swm.ApplyTo=cm.CompanyId              
    WHERE   swm.ClientNo = @ClientNo                
        -- AND swm.InActive=0              
                   
 ORDER BY DLUP DESC   
   