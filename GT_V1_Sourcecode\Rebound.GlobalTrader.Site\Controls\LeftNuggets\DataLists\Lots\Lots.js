Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists");Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Lots=function(n){Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Lots.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Lots.prototype={initialize:function(){this.addGetDataOKEvent(Function.createDelegate(this,this.getDataOK));this._strPathToData="controls/DataListNuggets/Lots";this._strDataObject="Lots";Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Lots.callBaseMethod(this,"initialize")},dispose:function(){this.isDisposed||Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Lots.callBaseMethod(this,"dispose")},getDataOK:function(){for(var n,t=0,i=this._objResult.Results.length;t<i;t++)n=this._objResult.Results[t],this._tbl.addRow([String.format('<a href="{0}">{1}<\/a>',$RGT_gotoURL_Lot(n.ID),$R_FN.setCleanTextValue(n.Name))],n.ID,!1),n=null}};Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Lots.registerClass("Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Lots",Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Base,Sys.IDisposable);