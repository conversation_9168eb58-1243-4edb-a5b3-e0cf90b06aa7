﻿using Rebound.GlobalTrader.DAL;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Rebound.GlobalTrader.BLL
{
    public partial class SourcingAuditLog : BizObject
    {
        #region Properties
        protected static DAL.SourcingAuditLogElement Settings
        {
            get { return Globals.Settings.SourcingAuditLog; }
        }

        public int SourcingAuditLogId { get; set; }
        public int SourcingId { get; set; }
        public string SourcingType { get; set; }
        public int BatchId { get; set; }
        public string PartNo { get; set; }
        public string Action { get; set; }
        public string OldValue { get; set; }
        public string UpdatedBy { get; set; }
        public DateTime UpdatedDate { get; set; }
        public int TotalCount { get; set; }
        #endregion
        #region Methods
        public static List<SourcingAuditLog> GetBulkEditLog(int? order,
                                                            int? sortDir,
                                                            int? pageIndex,
                                                            int? pageSize,
                                                            string sourcingType,
                                                            string partSearch,
                                                            int? userId,
                                                            DateTime? updatedDateFrom,
                                                            DateTime? updatedDateTo)
        {
            List<SourcingAuditLog> lst = new List<SourcingAuditLog>();
            List<SourcingAuditLogDetails> lstDetails = SiteProvider.SourcingAuditLog.GetBulkEditLog(order,
                                                                                                    sortDir,
                                                                                                    pageIndex,
                                                                                                    pageSize,
                                                                                                    sourcingType,
                                                                                                    partSearch,
                                                                                                    userId,
                                                                                                    updatedDateFrom,
                                                                                                    updatedDateTo);

            if (lstDetails == null) return lst;
            foreach (var objDetails in lstDetails)
            {
                var obj = new SourcingAuditLog()
                {
                    SourcingAuditLogId = objDetails.SourcingAuditLogId,
                    SourcingId = objDetails.SourcingId,
                    SourcingType = objDetails.SourcingType,
                    BatchId = objDetails.BatchId,
                    PartNo = objDetails.PartNo,
                    Action = objDetails.Action,
                    OldValue = objDetails.OldValue,
                    UpdatedBy = objDetails.UpdatedBy,
                    UpdatedDate = objDetails.UpdatedDate,
                    TotalCount = objDetails.TotalCount
                };
                lst.Add(obj);
            }
            return lst;
        }
        #endregion
    }
}
