﻿using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;

namespace Rebound.GlobalTrader.BLL {
    public partial class GoodsInStatus {
		/// <summary>
		/// An enum representation of the 'tbGoodsInStatus' table.
		/// </summary>
		/// <remark>This enumeration contains the items contained in the table tbGoodsInStatus</remark>
		[Serializable]
		public enum List {
            GIReceived = 1,
            GIPartiallyInspected = 2,
            GIInspected = 3,
            GIAwaitingQuery = 4,
            GIPartiallyReleased = 5,
            GIReleased = 6,
            GIPartiallyQuarantined = 7,
            GIQuarantined = 8,
            InvoicePaid = 9
        }		

	

		
	}
}