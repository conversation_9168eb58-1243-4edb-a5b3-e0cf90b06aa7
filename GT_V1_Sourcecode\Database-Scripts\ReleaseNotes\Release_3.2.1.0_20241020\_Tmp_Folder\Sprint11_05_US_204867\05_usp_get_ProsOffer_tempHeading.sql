﻿/*   
===========================================================================================  
TASK			UPDATED BY      DATE			ACTION		DESCRIPTION  
[US-204867]		An.TranTan		24-Sep-2024		Create		Get prospective offer raw data headers
===========================================================================================  
*/ 
CREATE OR ALTER PROCEDURE usp_get_ProsOffer_tempHeading 
	@UserId INT
AS
BEGIN
	SELECT ColumnId, ColumnHeading
	FROM (
		SELECT TOP 1 Column1,Column2,Column3,Column4,Column5,Column6,Column7,<PERSON>umn8,Column9,Column10,Column11
		FROM BorisGlobalTraderimports.dbo.tbProspectiveOffer_tempHeading
		WHERE CreatedBy = @UserId
		ORDER BY CreatedDate DESC
	) AS SourceTable
	UNPIVOT
	(
		ColumnHeading FOR ColumnId IN (Column1,Column2,Column3,Column4,Column5,Column6,Column7,Column8,Column9,Column10,Column11)
	) AS UnpivotTable;
END
/* --Test script
	exec usp_get_ProsOffer_tempHeading @UserId = 6670
*/
