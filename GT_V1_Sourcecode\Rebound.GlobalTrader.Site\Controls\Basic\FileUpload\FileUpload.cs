/*
Marker     Changed by      Date         Remarks
[001]      Vinay           07/05/2012   This need to upload pdf document for different section
*/
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;

namespace Rebound.GlobalTrader.Site.Controls {

	[ToolboxData("<{0}:FileUpload runat=server></{0}:FileUpload>")]
	public class FileUpload : WebControl, IScriptControl {

		protected ScriptManager _sm;
		protected HtmlControl _ifmUpload;
		protected Panel _pnlAllowedExtensions;


		private string _strAllowedExtensions = "";
		/// <summary>
		/// Allowed extensions for the uploaded file
		/// Separate each one with a double pipe, don't include the dot
		/// e.g. "jpg||jpeg"
		/// </summary>
		public string AllowedExtensions {
			get { return _strAllowedExtensions; }
			set { _strAllowedExtensions = value; }
		}

		private double _dblMaxFileSizeInMb = 4;
		public double MaxFileSizeInMb {
			get { return _dblMaxFileSizeInMb; }
			set { _dblMaxFileSizeInMb = value; }
		}

        // [001] code start
        private string _strSectionName;
        /// <summary>

        /// Section denotes to screen which fileupload belong
        /// section name use to rename the file name
        /// </summary>
        public string SectionName
        {
            get { return _strSectionName; }
            set { _strSectionName = value; }
        }
        //[001] code end

		protected override void CreateChildControls() {

			//file upload frame
			_ifmUpload = new HtmlGenericControl("iframe");
			_ifmUpload.ID = "ifmUpload";
			_ifmUpload.Attributes["src"] = String.Format("FileUpload.aspx?mxs={0}", _dblMaxFileSizeInMb);
			_ifmUpload.Attributes["style"] = "border-style:none; width:100%; height:25px;";
			_ifmUpload.Attributes["marginheight"] = "0";
			_ifmUpload.Attributes["marginwidth"] = "0";
			_ifmUpload.Attributes["scrolling"] = "no";
			Controls.Add(_ifmUpload);

			//allowed types
			_pnlAllowedExtensions = ControlBuilders.CreatePanel("uploadFileTypes");
			_pnlAllowedExtensions.Visible = (_strAllowedExtensions != "");
			ControlBuilders.CreateLiteralInsideParent(_pnlAllowedExtensions, String.Format("(.{0})", _strAllowedExtensions.Replace("||", ", .")));
			Controls.Add(_pnlAllowedExtensions);

			base.CreateChildControls();
		}

		protected override void OnPreRender(EventArgs e) {
			if (!this.DesignMode) {
				_sm = ScriptManager.GetCurrent(Page);
				if (_sm == null) throw new HttpException("A ScriptManager control must exist on the current page.");
				_sm.RegisterScriptControl(this);
			}
			base.OnPreRender(e);
		}

		protected override void Render(HtmlTextWriter writer) {
			if (!this.DesignMode) _sm.RegisterScriptDescriptors(this);
			base.Render(writer);
		}

		#region IScriptControl Members

		protected virtual IEnumerable<ScriptReference> GetScriptReferences() {
			bool blnDebug = false;
#if (DEBUG)
			blnDebug = true;
#endif
			return new ScriptReference[] { Functions.GetScriptReference(blnDebug, "Rebound.GlobalTrader.Site", "Controls.Basic.FileUpload.FileUpload", true) };
		}

		protected virtual IEnumerable<ScriptDescriptor> GetScriptDescriptors() {
			ScriptControlDescriptor descriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.FileUpload", this.ClientID);
			descriptor.AddElementProperty("ifmUpload", _ifmUpload.ClientID);
			descriptor.AddProperty("strAllowedExtensions", _strAllowedExtensions);
			descriptor.AddProperty("dblMaxFileSizeInMb", _dblMaxFileSizeInMb);
            // [001] code start
            descriptor.AddProperty("strSectionName", _strSectionName);
            // [001] code end
			return new ScriptDescriptor[] { descriptor };
		}

		IEnumerable<ScriptReference> IScriptControl.GetScriptReferences() {
			return GetScriptReferences();
		}

		IEnumerable<ScriptDescriptor> IScriptControl.GetScriptDescriptors() {
			return GetScriptDescriptors();
		}

		#endregion
	}
}
