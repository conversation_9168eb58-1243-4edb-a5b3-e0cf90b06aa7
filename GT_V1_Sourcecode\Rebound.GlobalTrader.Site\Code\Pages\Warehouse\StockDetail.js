Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Warehouse");Rebound.GlobalTrader.Site.Pages.Warehouse.StockDetail=function(n){Rebound.GlobalTrader.Site.Pages.Warehouse.StockDetail.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Pages.Warehouse.StockDetail.prototype={get_ctlPageTitle:function(){return this._ctlPageTitle},set_ctlPageTitle:function(n){this._ctlPageTitle!==n&&(this._ctlPageTitle=n)},get_ctlMainInfo:function(){return this._ctlMainInfo},set_ctlMainInfo:function(n){this._ctlMainInfo!==n&&(this._ctlMainInfo=n)},get_ctlAllocations:function(){return this._ctlAllocations},set_ctlAllocations:function(n){this._ctlAllocations!==n&&(this._ctlAllocations=n)},get_ctlStockLog:function(){return this._ctlStockLog},set_ctlStockLog:function(n){this._ctlStockLog!==n&&(this._ctlStockLog=n)},get_ctlStockImages:function(){return this._ctlStockImages},set_ctlStockImages:function(n){this._ctlStockImages!==n&&(this._ctlStockImages=n)},get_ctlStockDocuments:function(){return this._ctlStockDocuments},set_ctlStockDocuments:function(n){this._ctlStockDocuments!==n&&(this._ctlStockDocuments=n)},get_ctlRelatedStock:function(){return this._ctlRelatedStock},set_ctlRelatedStock:function(n){this._ctlRelatedStock!==n&&(this._ctlRelatedStock=n)},get_pnlQuantities:function(){return this._pnlQuantities},set_pnlQuantities:function(n){this._pnlQuantities!==n&&(this._pnlQuantities=n)},get_pnlQuantityInStock:function(){return this._pnlQuantityInStock},set_pnlQuantityInStock:function(n){this._pnlQuantityInStock!==n&&(this._pnlQuantityInStock=n)},get_lblQuantityInStock:function(){return this._lblQuantityInStock},set_lblQuantityInStock:function(n){this._lblQuantityInStock!==n&&(this._lblQuantityInStock=n)},get_pnlQuantityOnOrder:function(){return this._pnlQuantityOnOrder},set_pnlQuantityOnOrder:function(n){this._pnlQuantityOnOrder!==n&&(this._pnlQuantityOnOrder=n)},get_lblQuantityOnOrder:function(){return this._lblQuantityOnOrder},set_lblQuantityOnOrder:function(n){this._lblQuantityOnOrder!==n&&(this._lblQuantityOnOrder=n)},get_pnlQuantityAllocated:function(){return this._pnlQuantityAllocated},set_pnlQuantityAllocated:function(n){this._pnlQuantityAllocated!==n&&(this._pnlQuantityAllocated=n)},get_lblQuantityAllocated:function(){return this._lblQuantityAllocated},set_lblQuantityAllocated:function(n){this._lblQuantityAllocated!==n&&(this._lblQuantityAllocated=n)},get_lblQuantityAvailable:function(){return this._lblQuantityAvailable},set_lblQuantityAvailable:function(n){this._lblQuantityAvailable!==n&&(this._lblQuantityAvailable=n)},get_pnlQuarantined:function(){return this._pnlQuarantined},set_pnlQuarantined:function(n){this._pnlQuarantined!==n&&(this._pnlQuarantined=n)},get_pnlStatus:function(){return this._pnlStatus},set_pnlStatus:function(n){this._pnlStatus!==n&&(this._pnlStatus=n)},get_lblStatus:function(){return this._lblStatus},set_lblStatus:function(n){this._lblStatus!==n&&(this._lblStatus=n)},get_ctlStockPDFDragDrop:function(){return this._ctlStockPDFDragDrop},set_ctlStockPDFDragDrop:function(n){this._ctlStockPDFDragDrop!==n&&(this._ctlStockPDFDragDrop=n)},get_ctlStockImagesDragDrop:function(){return this._ctlStockImagesDragDrop},set_ctlStockImagesDragDrop:function(n){this._ctlStockImagesDragDrop!==n&&(this._ctlStockImagesDragDrop=n)},get_IsGlobalLogin:function(){return this._IsGlobalLogin},set_IsGlobalLogin:function(n){this._IsGlobalLogin!==n&&(this._IsGlobalLogin=n)},initialize:function(){Rebound.GlobalTrader.Site.Pages.Warehouse.StockDetail.callBaseMethod(this,"initialize")},goInit:function(){this._ctlMainInfo&&this._ctlMainInfo.addGotData(Function.createDelegate(this,this.ctlStockMainInfo_GotData));this._ctlAllocations&&this._ctlAllocations.addSaveEditComplete(Function.createDelegate(this,this.ctlAllocations_SaveEditComplete));Rebound.GlobalTrader.Site.Pages.Warehouse.StockDetail.callBaseMethod(this,"goInit")},dispose:function(){this.isDisposed||(this._ctlMainInfo&&this._ctlMainInfo.dispose(),this._ctlPageTitle&&this._ctlPageTitle.dispose(),this._ctlAllocations&&this._ctlAllocations.dispose(),this._ctlStockLog&&this._ctlStockLog.dispose(),this._ctlStockImages&&this._ctlStockImages.dispose(),this._ctlRelatedStock&&this._ctlRelatedStock.dispose(),this._ctlStockDocuments&&this._ctlStockDocuments.dispose(),this._ctlStockPDFDragDrop&&this._ctlStockPDFDragDrop.dispose(),this._ctlStockImagesDragDrop&&this._ctlStockImagesDragDrop.dispose(),this._ctlPageTitle=null,this._ctlMainInfo=null,this._ctlAllocations=null,this._ctlStockLog=null,this._ctlStockImages=null,this._ctlRelatedStock=null,this._pnlQuantities=null,this._pnlQuantityInStock=null,this._lblQuantityInStock=null,this._pnlQuantityOnOrder=null,this._lblQuantityOnOrder=null,this._pnlQuantityAllocated=null,this._lblQuantityAllocated=null,this._lblQuantityAvailable=null,this._pnlQuarantined=null,this._pnlStatus=null,this._lblStatus=null,this._ctlStockImagesDragDrop=null,this._IsGlobalLogin=null,Rebound.GlobalTrader.Site.Pages.Warehouse.StockDetail.callBaseMethod(this,"dispose"))},ctlStockMainInfo_GotData:function(){this.updateQuantities(this._ctlMainInfo._intQuantityInStock,this._ctlMainInfo._intQuantityOnOrder,this._ctlMainInfo._intQuantityAllocated,this._ctlMainInfo._intQuantityAvailable);this.updateQuarantined(this._ctlMainInfo._blnQuarantined);this.updateStatus();this.updatePartNoTitle();this._ctlRelatedStock&&this._ctlRelatedStock.refresh();this._ctlStockLog&&this._ctlStockLog.refresh();this._ctlAllocations&&this._ctlAllocations.refresh();this._ctlStockImages&&this._ctlStockImages.refresh();this._ctlStockDocuments&&this._ctlStockDocuments.getData();this._ctlStockPDFDragDrop&&this._ctlStockPDFDragDrop.getData();this._ctlStockImagesDragDrop&&this._ctlStockImagesDragDrop.refresh();this._ctlMainInfo._IsGlobalLogin=this._IsGlobalLogin},updateQuantities:function(n,t,i,r){$R_FN.setInnerHTML(this._lblQuantityInStock,n>0?n:0);$R_FN.setInnerHTML(this._lblQuantityOnOrder,t>0?t:0);$R_FN.setInnerHTML(this._lblQuantityAllocated,i>0?i:0);$R_FN.setInnerHTML(this._lblQuantityAvailable,r>0?r:0);var u=250,o=Math.max(n+t,i),f=o>0?u/o:1,e={InStock:Math.min(u,Math.round(n*f)),OnOrder:Math.min(u,Math.round(t*f)),Allocated:Math.min(u,Math.round(i*f)),Available:Math.min(u,Math.round(r*f))};this._pnlQuantityInStock.style.width=String.format("{0}px",e.InStock);this._pnlQuantityOnOrder.style.width=String.format("{0}px",e.OnOrder+e.InStock);this._pnlQuantityAllocated.style.width=String.format("{0}px",e.Allocated)},ctlAllocations_SaveEditComplete:function(){this._ctlMainInfo&&this._ctlMainInfo.refresh();this._ctlStockLog&&this._ctlStockLog.refresh()},updateQuarantined:function(n){$R_FN.showElement(this._pnlQuarantined,n)},updateStatus:function(){var n=this._ctlMainInfo.getFieldValue("hidStatus");$R_FN.setInnerHTML(this._lblStatus,n);$R_FN.showElement(this._pnlStatus,n.length>0);n=null},updatePartNoTitle:function(){this._ctlPageTitle.updateTitle(this._ctlMainInfo.getFieldValue("ctlPartNo"))}};Rebound.GlobalTrader.Site.Pages.Warehouse.StockDetail.registerClass("Rebound.GlobalTrader.Site.Pages.Warehouse.StockDetail",Rebound.GlobalTrader.Site.Pages.Content,Sys.IDisposable);