///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//----------------------------------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//
// RP 06.10.2009:
// - ensure "Include Closed" is passed to ASHX
//---------------------------------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.ItemSearch");

Rebound.GlobalTrader.Site.Controls.ItemSearch.QuoteLines = function(element) { 
	Rebound.GlobalTrader.Site.Controls.ItemSearch.QuoteLines.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.ItemSearch.QuoteLines.prototype = {

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.ItemSearch.QuoteLines.callBaseMethod(this, "initialize");
		this.addSetupData(Function.createDelegate(this, this.doSetupData));
		this.addGetDataComplete(Function.createDelegate(this, this.doGetDataComplete));
	},

	dispose: function() { 
		if (this.isDisposed) return;
		Rebound.GlobalTrader.Site.Controls.ItemSearch.QuoteLines.callBaseMethod(this, "dispose");
	},
	
	doSetupData: function() {
		this._objData.set_PathToData("controls/ItemSearch/QuoteLines");
		this._objData.set_DataObject("QuoteLines");
		this._objData.set_DataAction("GetData");
		this._objData.addParameter("Part", this.getFieldValue("ctlPartNo"));
		this._objData.addParameter("CM", this.getFieldValue("ctlCompany"));
		this._objData.addParameter("QuoteNoLo", this.getFieldValue_Min("ctlQuoteNo"));
		this._objData.addParameter("QuoteNoHi", this.getFieldValue_Max("ctlQuoteNo"));
		this._objData.addParameter("DateQuotedFrom", this.getFieldValue("ctlDateQuotedFrom"));
		this._objData.addParameter("DateQuotedTo", this.getFieldValue("ctlDateQuotedTo"));
		this._objData.addParameter("IncludeClosed", this.getFieldValue("ctlIncludeClosed"));
	},
	
	doGetDataComplete: function() {
		for (var i = 0, l = this._objResult.Results.length; i < l; i++) {
			var row = this._objResult.Results[i];
			var aryData = [
				row.No
				, $R_FN.setCleanTextValue(row.CMName)
				, $R_FN.writePartNo(row.Part, row.ROHS)
				, $R_FN.setCleanTextValue(row.Date)
				, row.Quantity
				, row.Price
			];
			this._tblResults.addRow(aryData, row.ID, false);
			aryData = null; row = null;
		}
	}
		
};

Rebound.GlobalTrader.Site.Controls.ItemSearch.QuoteLines.registerClass("Rebound.GlobalTrader.Site.Controls.ItemSearch.QuoteLines", Rebound.GlobalTrader.Site.Controls.ItemSearch.Base, Sys.IDisposable);
