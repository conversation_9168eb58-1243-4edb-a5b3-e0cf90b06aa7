Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.AutoSearch");Rebound.GlobalTrader.Site.Controls.AutoSearch.Base=function(n){Rebound.GlobalTrader.Site.Controls.AutoSearch.Base.initializeBase(this,[n]);this._intTimeoutID=-1;this._intCharactersToEnterBeforeSearch=-1;this._dataAction="GetData";this._strLastSearch="";this._strCurrentSearch="";this._aryValues=[];this._aryIDs=[];this._aryExtras=[];this._aryDataParams=[];this._hypReselect=null;this._intCurrentSelection=-1;this._blnHasResults=!1;this._strPathToData="";this._strDataObject="";this._varComboExtraText="";this._blnIsSelectionAllowed=!0;this._evtTriggerByClickEvent=!1};Rebound.GlobalTrader.Site.Controls.AutoSearch.Base.prototype={get_lblResults:function(){return this._lblResults},set_lblResults:function(n){this._lblResults!==n&&(this._lblResults=n)},get_hypClose:function(){return this._hypClose},set_hypClose:function(n){this._hypClose!==n&&(this._hypClose=n)},get_pnlResults:function(){return this._pnlResults},set_pnlResults:function(n){this._pnlResults!==n&&(this._pnlResults=n)},get_pnlContainer:function(){return this._pnlContainer},set_pnlContainer:function(n){this._pnlContainer!==n&&(this._pnlContainer=n)},get_txtRelatedTextBox:function(){return this._txtRelatedTextBox},set_txtRelatedTextBox:function(n){this._txtRelatedTextBox!==n&&(this._txtRelatedTextBox=n)},get_intTimeoutID:function(){return this._intTimeoutID},set_intTimeoutID:function(n){this._intTimeoutID!==n&&(this._intTimeoutID=n)},get_dblSearchDelay:function(){return this._dblSearchDelay},set_dblSearchDelay:function(n){this._dblSearchDelay!==n&&(this._dblSearchDelay=n)},get_intCharactersToEnterBeforeSearch:function(){return this._intCharactersToEnterBeforeSearch},set_intCharactersToEnterBeforeSearch:function(n){this._intCharactersToEnterBeforeSearch!==n&&(this._intCharactersToEnterBeforeSearch=n)},get_DataAction:function(){return this._dataAction},set_DataAction:function(n){this._dataAction!==n&&(this._dataAction=n)},get_result:function(){return this._result},set_result:function(n){this._result!==n&&(this._result=n)},get_pnlLoading:function(){return this._pnlLoading},set_pnlLoading:function(n){this._pnlLoading!==n&&(this._pnlLoading=n)},get_pnlResultsOuter:function(){return this._pnlResultsOuter},set_pnlResultsOuter:function(n){this._pnlResultsOuter!==n&&(this._pnlResultsOuter=n)},get_lblInstructions:function(){return this._lblInstructions},set_lblInstructions:function(n){this._lblInstructions!==n&&(this._lblInstructions=n)},get_lblSelectedValue:function(){return this._lblSelectedValue},set_lblSelectedValue:function(n){this._lblSelectedValue!==n&&(this._lblSelectedValue=n)},get_enmResultsActionType:function(){return this._enmResultsActionType},set_enmResultsActionType:function(n){this._enmResultsActionType!==n&&(this._enmResultsActionType=n)},get_varSelectedValue:function(){return this._varSelectedValue},set_varSelectedValue:function(n){this._varSelectedValue!==n&&(this._varSelectedValue=n)},get_varSelectedID:function(){return this._varSelectedID},set_varSelectedID:function(n){this._varSelectedID!==n&&(this._varSelectedID=n)},get_hypReselect:function(){return this._hypReselect},set_hypReselect:function(n){this._hypReselect!==n&&(this._hypReselect=n)},get_hypButtonTrigger:function(){return this._hypButtonTrigger},set_hypButtonTrigger:function(n){this._hypButtonTrigger!==n&&(this._hypButtonTrigger=n)},get_blnTriggerByButton:function(){return this._blnTriggerByButton},set_blnTriggerByButton:function(n){this._blnTriggerByButton!==n&&(this._blnTriggerByButton=n)},get_evtTriggerByClickEvent:function(){return this._evtTriggerByClickEvent},set_evtTriggerByClickEvent:function(n){this._evtTriggerByClickEvent!==n&&(this._evtTriggerByClickEvent=n)},get_intCurrentLogin:function(){return this._intCurrentLogin},set_intCurrentLogin:function(n){this._intCurrentLogin!==n&&(this._intCurrentLogin=n)},get_intPOHubClientNo:function(){return this._intPOHubClientNo},set_intPOHubClientNo:function(n){this._intPOHubClientNo!==n&&(this._intPOHubClientNo=n)},get_intGlobalLoginClientNo:function(){return this._intGlobalLoginClientNo},set_intGlobalLoginClientNo:function(n){this._intGlobalLoginClientNo!==n&&(this._intGlobalLoginClientNo=n)},get_blnIsSelectionAllowed:function(){return this._blnIsSelectionAllowed},set_blnIsSelectionAllowed:function(n){this._blnIsSelectionAllowed!==n&&(this._blnIsSelectionAllowed=n)},get_strApprovalType:function(){return this._strApprovalType},set_strApprovalType:function(n){this._strApprovalType!==n&&(this._strApprovalType=n)},get_intDocmentId:function(){return this._intDocmentId},set_intDocmentId:function(n){this._intDocmentId!==n&&(this._intDocmentId=n)},get_txtGroup:function(){return this._txtGroup},set_txtGroup:function(n){this._txtGroup!==n&&(this._txtGroup=n)},addDataReturnedEvent:function(n){this.get_events().addHandler("DataReturned",n)},removeDataReturnedEvent:function(n){this.get_events().removeHandler("DataReturned",n)},onDataReturned:function(){var n=this.get_events().getHandler("DataReturned");n&&n(this,Sys.EventArgs.Empty)},addSelectionMadeEvent:function(n){this.get_events().addHandler("SelectionMade",n)},removeSelectionMadeEvent:function(n){this.get_events().removeHandler("SelectionMade",n)},onSelectionMade:function(){var n=this.get_events().getHandler("SelectionMade");n&&n(this,Sys.EventArgs.Empty)},addSetupParametersEvent:function(n){this.get_events().addHandler("SetupParameters",n)},removeSetupParametersEvent:function(n){this.get_events().removeHandler("SetupParameters",n)},onSetupParameters:function(){var n=this.get_events().getHandler("SetupParameters");n&&n(this,Sys.EventArgs.Empty)},initialize:function(){Rebound.GlobalTrader.Site.Controls.AutoSearch.Base.callBaseMethod(this,"initialize");$addHandler(this._hypClose,"click",Function.createDelegate(this,this.closeResults));this._blnTriggerByButton?$addHandler(this._hypButtonTrigger,"click",Function.createDelegate(this,this.initiateSearch)):$addHandler(this._txtRelatedTextBox,"keyup",Function.createDelegate(this,this.textboxKeyUp));this._evtTriggerByClickEvent&&$addHandler(this._txtRelatedTextBox,"click",Function.createDelegate(this,this.initiateClickSearch));$addHandler(this._hypReselect,"click",Function.createDelegate(this,this.reselect));this._varSelectedID&&this.doItemClick(this._txtRelatedTextBox.value,this._varSelectedID)},dispose:function(){this.get_element()&&$clearHandlers(this.get_element());this._hypClose&&$clearHandlers(this._hypClose);this._hypReselect&&$clearHandlers(this._hypReselect);this._txtRelatedTextBox&&$clearHandlers(this._txtRelatedTextBox);this._hypButtonTrigger&&$clearHandlers(this._hypButtonTrigger);this._intPOHubClientNo=null;this._intGlobalLoginClientNo=null;this._strApprovalType=null;this._intDocmentId=null;this._txtGroup=null;this._blnIsSelectionAllowed=null;Rebound.GlobalTrader.Site.Controls.AutoSearch.Base.callBaseMethod(this,"dispose")},setupDataObject:function(n){this._strPathToData=String.format("Controls/AutoSearch/{0}",n);this._strDataObject=n},closeResults:function(){$R_FN.showElement(this._pnlContainer,!1)},textboxKeyUp:function(n){var t,i;if(n.keyCode&&(t=n.keyCode),n.which&&(t=n.which),t==27){this.closeResults();return}if(this._enmResultsActionType==$R_ENUM$AutoSearchResultsActionType.RaiseEvent&&this._blnHasResults){if(t==Sys.UI.Key.enter){if(this._blnIsSelectionAllowed==!1){this.closeResults();return}if(this._intCurrentSelection<0){this.closeResults();return}i=$get(this.getDivID(this._intCurrentSelection));this.doItemClick(this._aryValues[this._intCurrentSelection],this._aryIDs[this._intCurrentSelection],this._aryExtras[this._intCurrentSelection]);return}if(t==Sys.UI.Key.up){if(this._blnIsSelectionAllowed==!1){this.closeResults();return}this._intCurrentSelection-=1;this._intCurrentSelection<0&&(this._intCurrentSelection=0);this.showSelection();return}if(t==Sys.UI.Key.down){if(this._blnIsSelectionAllowed==!1){this.closeResults();return}this._intCurrentSelection+=1;this._intCurrentSelection>=this._aryValues.length&&(this._intCurrentSelection=this._aryValues.length-1);this.showSelection();return}}this.initiateSearch()},initiateSearch:function(){if($R_FN.showElement(this._pnlContainer,!0),this._strCurrentSearch=this._txtRelatedTextBox.value.trim(),this._strCurrentSearch!=this._strLastSearch||!(this._intCharactersToEnterBeforeSearch>0))if(this._strLastSearch=this._strCurrentSearch,this._strCurrentSearch.length>=this._intCharactersToEnterBeforeSearch){clearTimeout(this._intTimeoutID);var n=String.format("$find('{0}').startSearch()",this.get_element().id);this._intTimeoutID=setTimeout(n,this._dblSearchDelay*1e3);n=null;$R_FN.showElement(this._lblInstructions,!1);$R_FN.showElement(this._lblResults,!0);$R_FN.setInnerHTML(this._lblResults,this._pnlLoading.innerHTML);$R_FN.setInnerHTML(this._pnlResults,"")}else $R_FN.showElement(this._lblResults,!1),$R_FN.showElement(this._lblInstructions,!0),$R_FN.showElement(this._pnlResultsOuter,!1),$R_FN.showElement(this._pnlResults,!1)},initiateClickSearch:function(){if($R_FN.showElement(this._pnlContainer,!0),this._strCurrentSearch=this._txtRelatedTextBox.value.trim(),this._strCurrentSearch.length==0){clearTimeout(this._intTimeoutID);var n=String.format("$find('{0}').startSearch()",this.get_element().id);this._intTimeoutID=setTimeout(n,this._dblSearchDelay*1e3);n=null;$R_FN.showElement(this._lblInstructions,!1);$R_FN.showElement(this._lblResults,!0);$R_FN.setInnerHTML(this._lblResults,this._pnlLoading.innerHTML);$R_FN.setInnerHTML(this._pnlResults,"")}else $R_FN.showElement(this._lblResults,!1),$R_FN.showElement(this._lblInstructions,!0),$R_FN.showElement(this._pnlResultsOuter,!1),$R_FN.showElement(this._pnlResults,!1)},startSearch:function(){var r=this._txtRelatedTextBox.value.trim(),n,t,i,u;if(!(r.length<this._intCharactersToEnterBeforeSearch)){for($R_FN.showElement(this._pnlResultsOuter,!0),$R_FN.showElement(this._pnlLoading,!0),$R_FN.showElement(this._pnlResults,!1),$R_FN.setInnerHTML(this._lblResults,this._pnlLoading.innerHTML),n=new Rebound.GlobalTrader.Site.Data,n.set_PathToData(this._strPathToData),n.set_DataObject(this._strDataObject),n.set_DataAction(this._dataAction),n.addParameter("search",r+"%"),this._strDataObject=="LyticaManufacturers"&&(t=document.getElementById("ctl00_cphMain_ctlBOMItems_ctlDB_ctl13_ctlPartNo_lbl").textContent,(t.includes("Stock Alert")||t.includes("In Stock"))&&(t=document.querySelector("#ctl00_cphMain_ctlBOMItems_ctlDB_ctl13_ctlPartNo_lbl .dropdown").firstChild.data),t=this.beautifyPartNumber(t),n.addParameter("LyticaPartNo",t)),i=0,u=this._aryDataParams.length;i<u;i++)n.addParameter(this._aryDataParams[i].name,this._aryDataParams[i].value);n.addParameter("cLogIn",this._intCurrentLogin);n.addParameter("intPOHubClientNo",this._intPOHubClientNo);n.addParameter("GlobalLoginClientNo",this._intGlobalLoginClientNo);n.addParameter("txtGroup",this._txtGroup);n.addParameter("ApprovalType",this._strApprovalType);n.addParameter("DocmentId",this._intDocmentId);n.addDataOK(Function.createDelegate(this,this.searchDataComplete));n.addError(Function.createDelegate(this,this.searchDataError));n.addTimeout(Function.createDelegate(this,this.searchDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null}},searchDataError:function(){this.closeResults()},searchDataComplete:function(n){this._result=n._result;$R_FN.setInnerHTML(this._pnlResults,"");$R_FN.setInnerHTML(this._lblResults,String.format("{0} result(s)",this._result.TotalRecords));Array.clear(this._aryValues);Array.clear(this._aryIDs);Array.clear(this._aryExtras);this._intCurrentSelection=-1;this.onDataReturned();$R_FN.showElement(this._pnlContainer,!0);$R_FN.showElement(this._lblInstructions,!1);$R_FN.showElement(this._pnlLoading,!1);$R_FN.showElement(this._pnlResults,!0);this._blnHasResults=!0},addResultItem:function(n,t,i,r){var u=document.createElement("div"),f;u.className="item";$R_FN.setInnerHTML(u,n);this._enmResultsActionType==$R_ENUM$AutoSearchResultsActionType.RaiseEvent&&(this._blnIsSelectionAllowed&&(u.className+=" itemRaiseEvent"),f=this.get_element().id,this._blnIsSelectionAllowed&&(u.onclick=function(){$find(f).doItemClick(t,i,r)}),u.id=this.getDivID(this._aryValues.length),Array.add(this._aryValues,t),Array.add(this._aryIDs,i),Array.add(this._aryExtras,r));this._pnlResults.appendChild(u);u=null},doItemClick:function(n,t,i){$R_FN.showElement(this._pnlContainer,!1);this._varSelectedValue=n;this._varSelectedID=t;this._varSelectedExtraData=i;this._varComboExtraText=i;this._txtRelatedTextBox.value=this._varSelectedValue;$R_FN.setInnerHTML(this._lblSelectedValue,this._varSelectedValue);var r=t>0;$R_FN.showElement(this._txtRelatedTextBox,!r);$R_FN.showElement(this._lblSelectedValue,r);$R_FN.showElement(this._hypReselect,r);this.onSelectionMade()},addDataParameter:function(n,t){n&&t&&Array.add(this._aryDataParams,{name:n,value:t})},reselect:function(){$R_FN.showElement(this._hypReselect,!1);$R_FN.showElement(this._lblSelectedValue,!1);this._varSelectedValue=null;this._varComboExtraText=null;this._varSelectedID=null;this._varSelectedExtraData=null;this._intCurrentSelection=-1;this._blnHasResults=!1;$R_FN.setInnerHTML(this._lblSelectedValue,"");$R_FN.showElement(this._txtRelatedTextBox,!0);this._txtRelatedTextBox.value=""},showSelection:function(){var n=$get(this.getDivID(this._intCurrentSelection)),t=$get(this.getDivID(this._intCurrentSelection-1)),i=$get(this.getDivID(this._intCurrentSelection+1)),r;n&&Sys.UI.DomElement.addCssClass(n,"itemSelected");t&&Sys.UI.DomElement.removeCssClass(t,"itemSelected");i&&Sys.UI.DomElement.removeCssClass(i,"itemSelected");r=Sys.UI.DomElement.getBounds(n);this._pnlResultsOuter.scrollTop=r.height*this._intCurrentSelection},getDivID:function(n){return String.format("{0}_div{1}",this._element.id,n)},beautifyPartNumber:function(n){return n=n.replace(" (Alternate)",""),n=n.replace("&","_AMPERSAND_"),n=n.replace("#","_HASH_"),n=n.replace("=","_EQUALS_"),n.trim()}};Rebound.GlobalTrader.Site.Controls.AutoSearch.Base.registerClass("Rebound.GlobalTrader.Site.Controls.AutoSearch.Base",Sys.UI.Control,Sys.IDisposable);