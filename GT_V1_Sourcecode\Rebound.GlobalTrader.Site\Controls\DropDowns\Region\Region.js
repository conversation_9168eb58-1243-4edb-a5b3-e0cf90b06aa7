Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.Region=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.Region.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DropDowns.Region.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.DropDowns.Region.callBaseMethod(this,"initialize");this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK))},dispose:function(){this.isDisposed||(this._intPOHubClientNo=null,Rebound.GlobalTrader.Site.Controls.DropDowns.Region.callBaseMethod(this,"dispose"))},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/Region");this._objData.set_DataObject("Region");this._objData.set_DataAction("GetData")},dataCallOK:function(){var t=this._objData._result,n;if(t.Regions)for(n=0;n<t.Regions.length;n++)this.addOption(t.Regions[n].Name,t.Regions[n].ID)}};Rebound.GlobalTrader.Site.Controls.DropDowns.Region.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.Region",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);