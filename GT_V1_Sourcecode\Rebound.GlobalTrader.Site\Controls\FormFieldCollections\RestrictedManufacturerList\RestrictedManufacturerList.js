Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.FormFieldCollections");Rebound.GlobalTrader.Site.Controls.FormFieldCollections.RestrictedManufacturerList=function(n){Rebound.GlobalTrader.Site.Controls.FormFieldCollections.RestrictedManufacturerList.initializeBase(this,[n]);this._aryRecipientLoginIDs=[];this._aryRecipientLoginNames=[];this._intNumberRecipients=0;this._manufacturerNameArr=[]};Rebound.GlobalTrader.Site.Controls.FormFieldCollections.RestrictedManufacturerList.prototype={get_pnlSelected:function(){return this._pnlSelected},set_pnlSelected:function(n){this._pnlSelected!==n&&(this._pnlSelected=n)},get_lblSelected:function(){return this._lblSelected},set_lblSelected:function(n){this._lblSelected!==n&&(this._lblSelected=n)},get_autLoginOrGroup:function(){return this._autLoginOrGroup},set_autLoginOrGroup:function(n){this._autLoginOrGroup!==n&&(this._autLoginOrGroup=n)},initialize:function(){this._autLoginOrGroup._intGlobalLoginClientNo=this._intGlobalClientNo;this._autLoginOrGroup.addSelectionMadeEvent(Function.createDelegate(this,this.newRecipientSelected));this.showSelected();Rebound.GlobalTrader.Site.Controls.FormFieldCollections.RestrictedManufacturerList.callBaseMethod(this,"initialize")},dispose:function(){this.isDisposed||(this._autLoginOrGroup&&this._autLoginOrGroup.dispose(),this._pnlSelected=null,this._lblSelected=null,this._autLoginOrGroup=null,this._aryRecipientLoginIDs=null,this._aryRecipientLoginNames=null,this._intNumberRecipients=null,this._manufacturerNameArr=null,Rebound.GlobalTrader.Site.Controls.FormFieldCollections.RestrictedManufacturerList.callBaseMethod(this,"dispose"))},newRecipientSelected:function(){this.addNewLoginRecipient(this._autLoginOrGroup._varSelectedID,this._autLoginOrGroup._varSelectedValue)},addNewLoginRecipient:function(n,t){Array.contains(this._aryRecipientLoginIDs,n)||(Array.add(this._aryRecipientLoginIDs,n),Array.add(this._aryRecipientLoginNames,t),this._intNumberRecipients+=1);this.showSelected();this._autLoginOrGroup.reselect()},showSelected:function(){$R_FN.showElement(this._pnlSelected,this._intNumberRecipients>0);var t="",n="";for(i=0,l=this._aryRecipientLoginIDs.length;i<l;i++)n+=this._manufacturerNameArr.length>0&&this._manufacturerNameArr.includes(this._aryRecipientLoginNames[i])?'<div class="mailRecipient" style="color: #EE0000;">':'<div class="mailRecipient">',n+=this._aryRecipientLoginNames[i],t=String.format("$find('{0}').removeLoginRecipient({1});",this._element.id,i),n+=String.format('&nbsp;&nbsp;&nbsp;<a href="javascript:void(0);" class="quickSearchReselect" onclick="{0}">[x]<\/a>',t),n+="<\/div>";$R_FN.setInnerHTML(this._lblSelected,n)},removeLoginRecipient:function(n){Array.removeAt(this._aryRecipientLoginIDs,n);Array.removeAt(this._aryRecipientLoginNames,n);this._intNumberRecipients-=1;this.showSelected()},setValue_To:function(n){this._ctlRelatedForm.setFieldValue("ctlRestrictedManufacturer",n)},getValue_To:function(){return this._ctlRelatedForm.getFieldValue("ctlRestrictedManufacturer")},validateFields:function(){var n=!0;return this._ctlRelatedForm.resetFormFields(),this._intNumberRecipients==0&&(this._ctlRelatedForm.setFieldInError("ctlRestrictedManufacturer",!0,$R_RES.RequiredFieldMissingMessage),n=!1),n},resetFields:function(){Array.clear(this._aryRecipientLoginIDs);Array.clear(this._aryRecipientLoginNames);Array.clear(this._manufacturerNameArr);this._intNumberRecipients=0;this.showSelected()}};Rebound.GlobalTrader.Site.Controls.FormFieldCollections.RestrictedManufacturerList.registerClass("Rebound.GlobalTrader.Site.Controls.FormFieldCollections.RestrictedManufacturerList",Rebound.GlobalTrader.Site.Controls.Forms.LabelFormField,Sys.IDisposable);