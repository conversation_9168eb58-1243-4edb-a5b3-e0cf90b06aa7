///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------

Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");

Rebound.GlobalTrader.Site.Controls.DropDowns.ProductSource = function(element) { 
	Rebound.GlobalTrader.Site.Controls.DropDowns.ProductSource.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.DropDowns.ProductSource.prototype = {

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.DropDowns.ProductSource.callBaseMethod(this, "initialize");
		this.addSetupDataCall(Function.createDelegate(this, this.setupDataCall));
		this.addGetDataOK(Function.createDelegate(this, this.dataCallOK));
	},
	
	dispose: function() { 
		if (this.isDisposed) return;
		Rebound.GlobalTrader.Site.Controls.DropDowns.ProductSource.callBaseMethod(this, "dispose");
	},
	
	setupDataCall: function() { 
		this._objData.set_PathToData("controls/DropDowns/ProductSource");
		this._objData.set_DataObject("ProductSource");
		this._objData.set_DataAction("GetData");
	},
	
	dataCallOK: function() { 
		var result = this._objData._result;
		if (result.Types) {
			for (var i = 0; i < result.Types.length; i++) {
				this.addOption(result.Types[i].Name, result.Types[i].ID);
			}
		}
	}

};

Rebound.GlobalTrader.Site.Controls.DropDowns.ProductSource.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.ProductSource", Rebound.GlobalTrader.Site.Controls.DropDowns.Base);
