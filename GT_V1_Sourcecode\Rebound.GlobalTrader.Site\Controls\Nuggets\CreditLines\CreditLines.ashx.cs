//Marker     Changed by      Date         Remarks
//[001]      Shashi Keshar   06/10/2016   This hass been changed to show fileds in Client Invoice Grid   .

using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using System.Linq;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets.Data
{
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class CreditLines : Rebound.GlobalTrader.Site.Data.Base
    {

        public override void ProcessRequest(HttpContext context)
        {
            if (base.init(context))
            {
                switch (Action)
                {
                    case "GetLines": GetLines(); break;
                    case "GetData": GetData(); break;
                    case "AddNew": AddNew(); break;
                    case "SaveEdit": SaveEdit(); break;
                    case "Delete": Delete(); break;
                    case "GetServiceForNew": GetServiceForNew(); break;
                    case "GetCRMALineForNew": GetCRMALineForNew(); break;
                    case "GetInvoiceLineForNew": GetInvoiceLineForNew(); break;
                    case "SendCreditNoteToPOHUB": SendCreditNoteToPOHUB(); break;
                    case "GetClientInvoice": GetClientInvoice(); break;
                    case "GetClientInvoiceLineForNew": GetClientInvoiceLineForNew(); break;
                    default: WriteErrorActionNotFound(); break;
                }
            }
        }

        /// <summary>
        /// get all creditLines for specified credit
        /// </summary>
        private void GetLines()
        {
            Credit credit = null;
            try
            {
                credit = Credit.Get(ID);
                if (credit == null)
                {
                    WriteErrorDataNotFound();
                }
                else
                {
                    List<CreditLine> lstAll = CreditLine.GetListForCredit(credit.CreditId);
                    ProcessLines("Lines", lstAll, credit);
                }
            }
            catch (Exception e)
            {
                WriteError(e);
            }
            finally
            {
                credit = null;
            }
        }

        private void GetClientInvoice()
        {
            ClientInvoice invoiceLine = null;
            try
            {

                List<ClientInvoiceLine> lstAll = ClientInvoiceLine.GetClientInvoice(ID, GetFormValue_NullableInt("LineNo"));
                ProcessClientInvoiceLines("Lines", lstAll, invoiceLine);

            }
            catch (Exception e)
            {
                WriteError(e);
            }
            finally
            {
                invoiceLine = null;
            }
        }
        private void ProcessClientInvoiceLines(string strListVarName, List<ClientInvoiceLine> lst, ClientInvoice inv)
        {
            try
            {
                JsonObject jsn = new JsonObject();
                JsonObject jsnItems = new JsonObject(true);
                foreach (ClientInvoiceLine ln in lst)
                {
                    JsonObject jsnItem = new JsonObject();

                    jsnItem.AddVariable("ID", ln.ClientInvoiceLineId);
                    jsnItem.AddVariable("No", ln.ClientInvoiceNo);
                    jsnItem.AddVariable("Num", ln.ClientInvoiceNumber);
                    jsnItem.AddVariable("Date", Functions.FormatDate(ln.DateReceived));
                    jsnItem.AddVariable("Price", Functions.FormatCurrency(ln.Price, ln.CurrencyCode));
                    jsnItem.AddVariable("Quantity", ln.QtyReceived);
                    jsnItem.AddVariable("Part", ln.Part);
                    jsnItem.AddVariable("GoodsInNo", ln.GoodsInNo);
                    //[001] Start Here
                    jsnItem.AddVariable("GoodsInNumber", ln.GoodsInNumber);
                    jsnItem.AddVariable("DebitNumber", ln.DebitNumber>0 ? Convert.ToString(ln.DebitNumber) : "");
                    jsnItem.AddVariable("InternalPurchaseOrderNumber", ln.InternalPurchaseOrderNumber);
                    //[001] End Here
                    jsnItems.AddVariable(jsnItem);
                    jsnItem.Dispose(); jsnItem = null;
                }
                jsn.AddVariable(strListVarName, jsnItems);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }

            catch (Exception e)
            {
                WriteError(e);
            }
        }
        /// <summary>
        /// Processes a list of creditLines
        /// </summary>
        private void ProcessLines(string strListVarName, List<CreditLine> lst, Credit cr)
        {
            try
            {
                double dblSubTotal = 0;
                double dblTax = 0;
                var listMfrNotes = Manufacturer.GetAdvisoryNotes(lst.Select(x => x.ManufacturerNo).ToList(), (int)SessionManager.ClientID);
                JsonObject jsn = new JsonObject();
                JsonObject jsnItems = new JsonObject(true);

                foreach (CreditLine ln in lst)
                {
                    JsonObject jsnItem = new JsonObject();
                    double dblLineTotal = (double)ln.Price * (double)ln.Quantity;
                    double dblLineTax = (ln.Taxable) ? (dblLineTotal * ((cr.TaxRate == null) ? 0 : (double)cr.TaxRate / 100)) : 0;
                    jsnItem.AddVariable("ID", ln.CreditLineId);
                    jsnItem.AddVariable("Part", ln.Part);
                    jsnItem.AddVariable("ManufacturerNo", ln.ManufacturerNo);
                    jsnItem.AddVariable("Manufacturer", ln.ManufacturerCode);
                    string mfrNotes = !Functions.HasNumbericValue(ln.ManufacturerNo) ? "" : listMfrNotes.Find(x => x.ManufacturerId == (int)ln.ManufacturerNo).AdvisoryNotes;
                    jsnItem.AddVariable("MfrAdvisoryNotes", Functions.ReplaceLineBreaks(mfrNotes));
                    //jsnItem.AddVariable("Product", ln.ProductName);
                    jsnItem.AddVariable("Product", ln.ProductDescription);
                    jsnItem.AddVariable("Package", ln.PackageName);
                    jsnItem.AddVariable("Quantity", ln.Quantity);
                    jsnItem.AddVariable("LC", Functions.FormatCurrency(ln.LandedCost, SessionManager.ClientCurrencyCode,true));
                    jsnItem.AddVariable("DC", ln.DateCode);
                    jsnItem.AddVariable("CustomerPart", ln.CustomerPart);
                    jsnItem.AddVariable("ROHS", ln.ROHS);
                    jsnItem.AddVariable("Price", Functions.FormatCurrency(ln.Price, ln.CurrencyCode,true));
                    jsnItem.AddVariable("Total", Functions.FormatCurrency(dblLineTotal, ln.CurrencyCode, 2,true));
                    jsnItem.AddVariable("Tax", Functions.FormatCurrency(dblLineTax, ln.CurrencyCode, 2,true));
                    //jsnItem.AddVariable("IsIpo", ln.Ipo>0?true:false);
                    jsnItem.AddVariable("IsIpo", ln.Ipo > 0 ? true : false);
                    jsnItem.AddVariable("ParentCreditLineId", ln.ParentCreditLineId > 0 ? true : false);
                    jsnItem.AddVariable("IsClientInvoiceLineId", ln.ClientInvoiceLineId > 0 ? true : false);
                    jsnItem.AddVariable("ParentCreditLineNo", ln.ParentCreditLineNo > 0 ? false : true);
                    jsnItem.AddVariable("IsPoHub", SessionManager.IsPOHub);
                    jsnItems.AddVariable(jsnItem);
                    jsnItem.Dispose(); jsnItem = null;
                    dblSubTotal += dblLineTotal;
                    dblTax += dblLineTax;
                }
                jsn.AddVariable(strListVarName, jsnItems);
                cr.Freight = (cr.Freight == null) ? 0 : cr.Freight;
                dblTax += (double)cr.Freight * ((double)cr.TaxRate / 100);
                jsn.AddVariable("SubTotal", Functions.FormatCurrency(dblSubTotal, cr.CurrencyCode, 2,true));
                jsn.AddVariable("Freight", Functions.FormatCurrency(cr.Freight, cr.CurrencyCode, 2,true));
                jsn.AddVariable("Tax", Functions.FormatCurrency(dblTax, cr.CurrencyCode, 2,true));
                jsn.AddVariable("Total", Functions.FormatCurrency(dblSubTotal + dblTax + cr.Freight, cr.CurrencyCode, 2,true));
                jsn.AddVariable("FreightRaw", Functions.FormatCurrency(cr.Freight, null,2,true));
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

        /// <summary>
        /// get creditLine by key
        /// </summary>
        private void GetData()
        {
            CreditLine ln = null;
            try
            {
                ln = CreditLine.Get(ID);
                if (ln == null)
                {
                    WriteErrorDataNotFound();
                }
                else
                {
                    JsonObject jsn = new JsonObject();
                    jsn.AddVariable("CreditNo", ln.CreditNo);
                    jsn.AddVariable("Part", ln.Part);
                    jsn.AddVariable("ROHS", ln.ROHS);
                    jsn.AddVariable("DC", ln.DateCode);
                    jsn.AddVariable("ManufacturerNo", ln.ManufacturerNo);
                    jsn.AddVariable("Manufacturer", ln.ManufacturerName);
                    string mfrNotes = Manufacturer.GetAdvisoryNotes(ln.ManufacturerNo ?? 0, (int)SessionManager.ClientID);
                    jsn.AddVariable("MfrAdvisoryNotes", Functions.ReplaceLineBreaks(mfrNotes));
                    jsn.AddVariable("Quantity", Functions.FormatNumeric(ln.Quantity));
                    jsn.AddVariable("Price", Functions.FormatCurrency(ln.Price, ln.CurrencyCode));
                    jsn.AddVariable("PriceRaw", Functions.FormatCurrency(ln.Price));
                    jsn.AddVariable("LandedCostRaw", Functions.FormatCurrency(ln.LandedCost));
                    string strLandedCost = Functions.FormatCurrency(ln.LandedCost, SessionManager.ClientCurrencyCode);
                    if (ln.CurrencyNo != SessionManager.ClientCurrencyID) strLandedCost = String.Format("{0} ({1})", strLandedCost, Functions.FormatCurrency(ln.LandedCostInCreditCurrency, ln.CurrencyCode));
                    jsn.AddVariable("LandedCost", strLandedCost);
                    jsn.AddVariable("ProductNo", ln.ProductNo);
                    jsn.AddVariable("Product", ln.ProductDescription);
                    jsn.AddVariable("Package", ln.PackageDescription);
                    jsn.AddVariable("CustomerPart", ln.CustomerPart);
                    jsn.AddVariable("Taxable", ln.Taxable);
                    jsn.AddVariable("StockNo", ln.StockNo);
                    jsn.AddVariable("ServiceNo", ln.ServiceNo);
                    jsn.AddVariable("IsService", ln.IsService);
                    jsn.AddVariable("CurrencyCode", ln.CurrencyCode);
                    jsn.AddVariable("CurrencyNo", ln.CurrencyNo);
                    jsn.AddVariable("CurrencyRate", ln.CurrencyRate);
                    jsn.AddVariable("LineNotes", Functions.ReplaceLineBreaks(ln.LineNotes));
                    OutputResult(jsn);
                    jsn.Dispose();
                    jsn = null;
                }
            }
            catch (Exception e)
            {
                WriteError(e);
            }
            finally
            {
                ln = null;
            }
        }

        /// <summary>
        /// Add new creditLine
        /// </summary>
        public void AddNew()
        {
            try
            {
                CreditLine ln = new CreditLine();
                ln.CreditNo = ID;
                if (GetFormValue_Boolean("LineIsService"))
                {
                    ln.ServiceNo = GetFormValue_NullableInt("ServiceNo");
                    ln.Part = GetFormValue_String("Service");
                    ln.CustomerPart = GetFormValue_String("ServiceDescription");
                    ln.ClientInvoiceLineId = GetFormValue_NullableInt("ClientInvLineNo",null);
                }
                else
                {
                    ln.Part = GetFormValue_String("Part");
                    ln.CustomerPart = GetFormValue_String("CustomerPart");
                    ln.ManufacturerNo = GetFormValue_NullableInt("MfrNo");
                    ln.DateCode = GetFormValue_String("DateCode");
                    ln.PackageNo = GetFormValue_NullableInt("PackageNo");
                    ln.ProductNo = GetFormValue_NullableInt("ProductNo");
                    ln.ROHS = GetFormValue_Byte("ROHS");
                    if (GetFormValue_String("ClientInvoice") == "CLIENTINVOICE")
                    {
                        ln.ServiceNo = -1;
                        ln.ClientInvoiceLineId = GetFormValue_NullableInt("ClientInvLineNo");
                    }
                    else
                    {
                        ln.InvoiceLineNo = GetFormValue_NullableInt("InvoiceLineNo");
                    }
                }
                ln.LandedCost = GetFormValue_NullableDouble("LandedCost");
                ln.Quantity = GetFormValue_Int("Quantity");
                ln.Price = GetFormValue_Double("Price");
                ln.Taxable = GetFormValue_Boolean("Taxable");
                ln.CustomerRMALineNo = GetFormValue_NullableInt("CustomerRMALineNo");
                ln.LandedCost = GetFormValue_NullableDouble("LandedCost");
                ln.Notes = GetFormValue_String("LineNotes");
                ln.UpdatedBy = LoginID;
                int intNewCreditLineID = ln.Insert();
                if (ln.CreditId > 0)
                {
                    WebServices service = new WebServices();
                    service.NotifyCreditNotesToPoHub("", (SessionManager.POHubMailGroupId ?? 0).ToString(), Functions.GetGlobalResource("MailTemplates", "CreditNotesToPoHub"), ln.CreditId);
                }
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Result", intNewCreditLineID > 0);
                jsn.AddVariable("NewID", intNewCreditLineID);
                OutputResult(jsn);
                jsn.Dispose();
                jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

        /// <summary>
        /// Update an existing creditLine
        /// </summary>
        public void SaveEdit()
        {
            try
            {
                CreditLine cl = CreditLine.Get(ID);
                if (GetFormValue_Boolean("LineIsService"))
                {
                    cl.Part = GetFormValue_String("Service");
                    cl.CustomerPart = GetFormValue_String("ServiceDescription");
                }
                cl.Quantity = GetFormValue_Int("Quantity");
                cl.Price = GetFormValue_Double("Price");
                cl.LandedCost = GetFormValue_Double("LandedCost");
                cl.Notes = GetFormValue_String("LineNotes");
                cl.UpdatedBy = LoginID;
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Result", cl.Update());
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

        /// <summary>
        /// Delete
        /// </summary>
        public void Delete()
        {
            try
            {
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Result", CreditLine.Delete(ID));
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

        /// <summary>
        /// Get CRMA Line for a new Credit Line
        /// </summary>
        private void GetCRMALineForNew()
        {
            try
            {
                CustomerRmaLine ln = CustomerRmaLine.Get(ID);
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Part", ln.Part);
                jsn.AddVariable("DC", ln.DateCode);
                jsn.AddVariable("CustomerPart", ln.CustomerPart);
                jsn.AddVariable("Quantity", Functions.FormatNumeric(ln.Quantity));
                jsn.AddVariable("ManufacturerNo", ln.ManufacturerNo);
                jsn.AddVariable("Manufacturer", ln.ManufacturerName);
                jsn.AddVariable("ProductNo", ln.ProductNo);
                jsn.AddVariable("PackageNo", ln.PackageNo);
                jsn.AddVariable("ROHS", ln.ROHS);
                int intCreditCurrencyID = GetFormValue_Int("CreditCurrencyNo");
                DateTime dtmReferenceDate = GetFormValue_DateTime("ReferenceDate");
                if (intCreditCurrencyID != ln.CurrencyNo) ln.Price = BLL.Currency.ConvertValueBetweenTwoCurrencies(ln.Price, ln.CurrencyNo, intCreditCurrencyID, dtmReferenceDate);
                jsn.AddVariable("Price", Functions.FormatCurrency(ln.Price));
                jsn.AddVariable("LandedCost", Functions.FormatCurrency(ln.LandedCost));
                if (intCreditCurrencyID != SessionManager.ClientCurrencyID)
                {
                    Double dblRate = BLL.Currency.GetCurrentRateAtDate(intCreditCurrencyID, dtmReferenceDate);
                    jsn.AddVariable("CurrencyRate", dblRate);
                    jsn.AddVariable("LandedCostConverted", Functions.FormatCurrency(BLL.Currency.ConvertValueFromBaseCurrency(ln.LandedCost, dblRate), GetFormValue_String("CreditCurrencyCode")));
                }
                jsn.AddVariable("ProductDescription", ln.ProductDescription);
                jsn.AddVariable("PackageDescription", ln.PackageDescription);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

        /// <summary>
        /// Get Invoice Line for a new Credit Line
        /// </summary>
        private void GetInvoiceLineForNew()
        {
            try
            {
                InvoiceLine ln = InvoiceLine.Get(ID);
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("InvoiceLineNo", ln.InvoiceLineId);
                jsn.AddVariable("Part", ln.Part);
                jsn.AddVariable("CustomerPart", ln.CustomerPart);
                jsn.AddVariable("ManufacturerNo", ln.ManufacturerNo);
                jsn.AddVariable("Manufacturer", ln.ManufacturerName);
                jsn.AddVariable("DC", ln.DateCode);
                jsn.AddVariable("Qty", Functions.FormatNumeric(ln.QuantityShipped));
                jsn.AddVariable("PackageNo", ln.PackageNo);
                jsn.AddVariable("ProductNo", ln.ProductNo);
                jsn.AddVariable("ROHS", ln.ROHS);
                int intCreditCurrencyID = GetFormValue_Int("CreditCurrencyNo");
                DateTime dtmReferenceDate = GetFormValue_DateTime("ReferenceDate");
                if (intCreditCurrencyID != ln.CurrencyNo) ln.Price = BLL.Currency.ConvertValueBetweenTwoCurrencies(ln.Price, (int)ln.CurrencyNo, intCreditCurrencyID, dtmReferenceDate);
                jsn.AddVariable("Price", Functions.FormatCurrency(ln.Price));
                jsn.AddVariable("LandedCost", Functions.FormatCurrency(ln.LandedCost));
                if (intCreditCurrencyID != SessionManager.ClientCurrencyID)
                {
                    Double dblRate = BLL.Currency.GetCurrentRateAtDate(intCreditCurrencyID, GetFormValue_DateTime("ReferenceDate"));
                    jsn.AddVariable("CurrencyRate", dblRate);
                    jsn.AddVariable("LandedCostConverted", Functions.FormatCurrency(BLL.Currency.ConvertValueFromBaseCurrency(ln.LandedCost, dblRate), GetFormValue_String("CreditCurrencyCode")));
                }
                jsn.AddVariable("ProductDescription", ln.ProductDescription);
                jsn.AddVariable("PackageDescription", ln.PackageDescription);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

        /// <summary>
        /// Gets data for a new Line based on a service item
        /// </summary>
        public void GetServiceForNew()
        {
            try
            {
                Service svc = Service.Get(ID);
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("ServiceName", svc.ServiceName);
                jsn.AddVariable("ServiceDescription", svc.ServiceDescription);

                //convert cost and price to Credit currency if required
                jsn.AddVariable("Cost", Functions.FormatCurrency(svc.Cost));
                int intCreditCurrencyID = GetFormValue_Int("CreditCurrencyNo");
                if (intCreditCurrencyID != SessionManager.ClientCurrencyID)
                {
                    Double dblCurrencyRate = BLL.Currency.GetCurrentRateAtDate(intCreditCurrencyID, GetFormValue_DateTime("ReferenceDate"));
                    jsn.AddVariable("CurrencyRate", dblCurrencyRate);
                    jsn.AddVariable("CostConverted", Functions.FormatCurrency(BLL.Currency.ConvertValueFromBaseCurrency(svc.Cost, dblCurrencyRate), GetFormValue_String("CreditCurrencyCode")));
                    svc.Price = BLL.Currency.ConvertValueFromBaseCurrency(svc.Price, dblCurrencyRate);
                }
                jsn.AddVariable("Price", Functions.FormatCurrency(svc.Price));

                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
                svc = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }
        /// <summary>
        /// Get Invoice Line for a new Credit Line
        /// </summary>
        private void GetClientInvoiceLineForNew()
        {
            try
            {
                ClientInvoiceLine ln = ClientInvoiceLine.Get(ID);
                JsonObject jsn = new JsonObject();

                jsn.AddVariable("ClientInvoiceLineId", ln.ClientInvoiceLineId);
                jsn.AddVariable("ClientInvoiceNo", ln.ClientInvoiceNo);
                jsn.AddVariable("GoodsInNo", ln.GoodsInNo);
                jsn.AddVariable("Price", Functions.FormatCurrency(ln.Price));
                jsn.AddVariable("Part", ln.Part);
                jsn.AddVariable("QtyReceived", ln.QtyReceived);
                jsn.AddVariable("DateReceived", ln.DateReceived);
                jsn.AddVariable("CurrencyCode", ln.CurrencyCode);
                jsn.AddVariable("DateCode", ln.DateCode);
                jsn.AddVariable("ProductNo", ln.ProductNo);
                jsn.AddVariable("SupplierPart", ln.SupplierPart);
                jsn.AddVariable("ROHS", ln.ROHS);
                jsn.AddVariable("PackageNo", ln.PackageNo);
                jsn.AddVariable("Landedcost", ln.Landedcost);
                jsn.AddVariable("ManufacturerNo", ln.ManufacturerNo);
                jsn.AddVariable("ManufacturerCode", ln.ManufacturerCode);
                jsn.AddVariable("ManufacturerName", ln.ManufacturerName);
                jsn.AddVariable("CurrencyNo", ln.CurrencyNo);


                int intCreditCurrencyID = GetFormValue_Int("CreditCurrencyNo");
                DateTime dtmReferenceDate = GetFormValue_DateTime("ReferenceDate");
                if (intCreditCurrencyID != ln.CurrencyNo) ln.Price = BLL.Currency.ConvertValueBetweenTwoCurrencies(ln.Price, (int)ln.CurrencyNo, intCreditCurrencyID, dtmReferenceDate);

                if (intCreditCurrencyID != SessionManager.ClientCurrencyID)
                {
                    Double dblRate = BLL.Currency.GetCurrentRateAtDate(intCreditCurrencyID, GetFormValue_DateTime("ReferenceDate"));
                    jsn.AddVariable("CurrencyRate", dblRate);
                    jsn.AddVariable("LandedCostConverted", Functions.FormatCurrency(BLL.Currency.ConvertValueFromBaseCurrency(ln.Landedcost, dblRate), GetFormValue_String("CreditCurrencyCode")));
                }
                jsn.AddVariable("ProductDescription", ln.ProductDescription);
                jsn.AddVariable("PackageDescription", ln.PackageDescription);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

        /// <summary>
        /// Update an existing creditLine
        /// </summary>
        public void SendCreditNoteToPOHUB()
        {
            try
            {
                CreditLine cl = new CreditLine();

                cl.CreditLineID = GetFormValue_String("CreditLineID");
                cl.UpdatedBy = LoginID;
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Result", cl.CreditNoteToPOHUB());
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

    }
}