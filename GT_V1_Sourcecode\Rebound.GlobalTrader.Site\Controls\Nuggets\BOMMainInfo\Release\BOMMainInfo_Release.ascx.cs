using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site.Controls;
using Rebound.GlobalTrader.Site;

namespace Rebound.GlobalTrader.Site.Controls.Forms {
    public partial class BOMMainInfo_Release : Base
    {
        #region Locals
        protected FlexiDataTable _tblAllReleasedetails;
        protected Panel _pnlLoadingAllRelease;
        protected Panel _pnlAllReleaseError;
        protected Panel _pnlAllRelease;
        #endregion

        #region Properties

        private int _intBOMID;
		public int LineID {
			get {
				return _intBOMID;
			}
			set {
				_intBOMID = value;
			}
		}

		#endregion

		#region Overrides

		/// <summary>
		/// OnInit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
            //TitleText = Functions.GetGlobalResource("FormTitles", "Confirm");
            AddScriptReference("Controls.Nuggets.BOMMainInfo.Release.BOMMainInfo_Release.js");
            WireUpControls();
            SetupTable();
        }

        /// <summary>
        /// OnPreRender
        /// </summary>
        /// <param name="e"></param>
        protected override void OnPreRender(EventArgs e) {
            SetupScriptDescriptors();
			base.OnPreRender(e);
		}

        #endregion
        private void WireUpControls()
        {

            _tblAllReleasedetails = (FlexiDataTable)ctlDesignBase.FindContentControl("tblAllReleasedetails");
            //_pnlLoadingAllRelease = (Panel)ctlDesignBase.FindContentControl("pnlLoadingAllRelease");
            //_pnlAllReleaseError = (Panel)ctlDesignBase.FindContentControl("pnlAllReleaseError");
            //_pnlAllRelease = (Panel)ctlDesignBase.FindContentControl("pnlAllRelease");
        
    }

        private void SetupTable()
        {
            _tblAllReleasedetails.AllowSelection = false;
            _tblAllReleasedetails.Columns.Add(new FlexiDataColumn("ReqNo", WidthManager.GetWidth(WidthManager.ColumnWidth.SystemDocumentNumber), false));
            _tblAllReleasedetails.Columns.Add(new FlexiDataColumn("PartNo", "DeliveryDate", WidthManager.GetWidth(WidthManager.ColumnWidth.PartNo)));
            _tblAllReleasedetails.Columns.Add(new FlexiDataColumn("BuyPrice", WidthManager.GetWidth(WidthManager.ColumnWidth.CurrencyValue)));
            _tblAllReleasedetails.Columns.Add(new FlexiDataColumn("UnitSellPrice", WidthManager.GetWidth(WidthManager.ColumnWidth.CurrencyValue)));

        }
        /// <summary>
        /// Setup Script Descriptors
        /// </summary>
        private void SetupScriptDescriptors() {
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Forms.BOMMainInfo_Release", ctlDesignBase.ClientID);
			_scScriptControlDescriptor.AddProperty("intBOMID", _intBOMID);
            _scScriptControlDescriptor.AddComponentProperty("tblAllReleasedetails", FindFieldControl("ctlSerialNoDetail", "tblAllReleasedetails").ClientID);
            //_scScriptControlDescriptor.AddElementProperty("ctlRelease", FindFieldControl("ctlConfirmRelease", "ctlRelease").ClientID);
            //_scScriptControlDescriptor.AddComponentProperty("pnlLoadingAllRelease", FindFieldControl("ctlSerialNoDetail", "pnlLoadingAllRelease").ClientID);
            //_scScriptControlDescriptor.AddComponentProperty("pnlAllReleaseError", FindFieldControl("ctlSerialNoDetail", "pnlAllReleaseError").ClientID);
            //_scScriptControlDescriptor.AddComponentProperty("pnlAllRelease", FindFieldControl("ctlSerialNoDetail", "pnlAllRelease").ClientID);
            


        }


    }
}