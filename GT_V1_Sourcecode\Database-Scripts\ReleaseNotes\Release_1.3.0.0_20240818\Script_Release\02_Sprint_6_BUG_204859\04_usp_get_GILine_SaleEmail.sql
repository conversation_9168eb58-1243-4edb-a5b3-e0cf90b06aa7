﻿/* 
===========================================================================================
TASK      			UPDATED BY     		DATE         		ACTION 			DESCRIPTION
[BUG-204859]		Cuong.DoX			12-JULY-2024		Create			Select email of the sale person of the SO of the GI line
===========================================================================================
*/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

-- =============================================
-- Author:		Cuongdx
-- Create date: 12 Jul 2024
-- Description:	Select email of the sale person of the SO of the GI line
-- =============================================
CREATE OR ALTER PROCEDURE usp_get_GILine_SaleEmail
	-- Add the parameters for the stored procedure here
	@GoodsInLineId INT,
	@SaleLoginId NVARCHAR(200) OUTPUT,
	@StockId INT OUTPUT,
	@PartNo NVARCHAR(100) OUTPUT,
	@SOLineNumbers NVARCHAR(MAX) OUTPUT
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;
    -- Insert statements for Sale Email here
	SELECT @SaleLoginId = ISNULL(so.Salesman,0)
		   ,@StockId =  ISNULL(sk.StockId,0)
		   ,@PartNo = ISNULL(sk.Part,'')
	FROM tbStock sk
	JOIN tbAllocation al
        ON sk.StockId = al.StockNo
	JOIN tbSalesOrderLine sol
        ON sol.SalesOrderLineId = al.SalesOrderLineNo
    JOIN tbSalesOrder so
        ON so.SalesOrderId = sol.SalesOrderNo
	INNER JOIN tbpurchaseOrderLine pol
        on sk.PurchaseOrderLineNo = pol.PurchaseOrderLineId
	INNER JOIN tbGoodsInLine gil
		 ON pol.PurchaseOrderLineId = gil.PurchaseOrderLineNo 
	WHERE gil.GoodsInLineId = @GoodsInLineId

	SELECT @SOLineNumbers = STUFF((
		SELECT '-' + CAST(sol.SalesOrderNo AS VARCHAR)+'_'+ CAST(so.SalesOrderNumber AS VARCHAR) + '_' + CAST(sol.SalesOrderLineId AS VARCHAR)
		from tbStock sk
		JOIN tbAllocation al
			ON sk.StockId = al.StockNo
		JOIN tbSalesOrderLine sol
			ON sol.SalesOrderLineId = al.SalesOrderLineNo
		JOIN tbSalesOrder so
			ON so.SalesOrderId = sol.SalesOrderNo
		INNER JOIN tbpurchaseOrderLine pol
			on sk.PurchaseOrderLineNo = pol.PurchaseOrderLineId
		INNER JOIN tbGoodsInLine gil
			 ON pol.PurchaseOrderLineId = gil.PurchaseOrderLineNo 
		WHERE gil.GoodsInLineId = @GoodsInLineId
		FOR XML PATH(''), TYPE
	).value('.', 'NVARCHAR(MAX)'), 1, 1, '');

END	

GO
