SET
  ANSI_NULLS ON
GO
SET
  QUOTED_IDENTIFIER ON
GO
  ALTER Procedure [dbo].[usp_IPOBOM_Source_OfferPH_BOMManager] 
  --********************************************************************************************                                                                            
  -- Change : Vinay: 22 Apr 2019: Move tbOffer in Import and Archive Database                                                                        
  --********************************************************************************************                                                                                
  @ClientId INT,
  @PartSearch VARCHAR(MAX) = null,
  @Index int = 1,
  @StartDate datetime = NULL,
  @FinishDate datetime = NULL,
  @<PERSON>OMManagerNo int = 1,
  @CallType int = 1,
  @curPage int = 1,
  @Rpp int = 10 --,@IsPoHUB bit=NULL                                                
  WITH RECOMPILE AS BEGIN --DECLARE VARIABLE                                                                    
  DECLARE @Month int DECLARE @FROMDATE DATETIME DECLARE @ENDDATE DATETIME DECLARE @OutPutDate DATETIME DECLARE @FinishDateVW DATETIME DECLARE @FROMDATEVW DATETIME DECLARE @ENDDATEVW DATETIME
select
  * into #tbEMSOffers from tbEMSOffers where 1=2                                             
  /*If partsearch is not coming from screen then it will get all part from customerrequirement table for that bom                                              
   else particular part offer will come.                                              
   */
  create table #Parttemp(PartNames varchar(100) COLLATE Latin1_General_CI_AS, CustomerRequirementId int, ReqStatus int )                                              
  IF (
    @PartSearch = ''
    or @PartSearch is null
  ) begin
insert into
  #Parttemp                                               
select
  cus.FullPart,
  CustomerRequirementId,
  REQStatus
from
  dbo.tbCustomerRequirement cus
where
  cus.BOMManagerNo = @BOMManagerNo
end
else begin
insert into
  #Parttemp                                              
select
  cus.FullPart,
  CustomerRequirementId,
  REQStatus
from
  dbo.tbCustomerRequirement cus
where
  cus.BOMManagerNo = @BOMManagerNo
  and CustomerRequirementId = @PartSearch --SELECT DISTINCT CAST(value AS varchar(100)) PartNames  FROM STRING_SPLIT(@PartSearch, ',')                                             
  delete #Parttemp where PartNames =''                                               
end
SET
  @Month = 6
  /*                         
   When we get index 1 then we find the maximum date from matching record                                   
   and decsrease no of month for the start date.                   
   */
  IF @Index = 1 BEGIN
SELECT
  @FinishDateVW = MAX(ISNULL(OfferStatusChangeDate, OriginalEntryDate))
FROM
  tbSourcingResult tsr
  join #Parttemp prt                                              
  on tsr.FullPart = prt.PartNames
WHERE
  tsr.SourcingTable in('PQ', 'EXPH', 'OFPH')
  AND --tsr.FullPart LIKE @PartSearch                                                 
  tsr.FullPart = prt.PartNames
SET
  @FROMDATEVW = dbo.ufn_get_date_from_datetime(DATEADD(month, - @Month, @FinishDateVW))
SET
  @ENDDATEVW = dbo.ufn_get_date_from_datetime(@FinishDateVW)
SELECT
  @FinishDate = MAX(ISNULL(OfferStatusChangeDate, OriginalEntryDate))
FROM
  [BorisGlobalTraderImports].dbo.tbOffer o
  JOIN tbClient cl ON o.ClientNo = cl.ClientId
  join #Parttemp prt on  o.FullPart = prt.PartNames                                              
WHERE
  -- o.ClientNo = @ClientId AND                                                
  o.FullPart = prt.PartNames --FullPart LIKE @PartSearch                                                                        
SET
  @FROMDATE = dbo.ufn_get_date_from_datetime(DATEADD(month, - @Month, @FinishDate))
SET
  @ENDDATE = dbo.ufn_get_date_from_datetime(@FinishDate)
END
ELSE BEGIN
SET
  @FROMDATE = dbo.ufn_get_date_from_datetime(@StartDate)
SET
  @ENDDATE = dbo.ufn_get_date_from_datetime(@FinishDate)
SET
  @FROMDATEVW = dbo.ufn_get_date_from_datetime(@StartDate)
SET
  @ENDDATEVW = dbo.ufn_get_date_from_datetime(@FinishDate)
END --SET THE OUTPUT DATE                                                                    
SET
  @OutPutDate = DATEADD(month, - @Month, @FinishDate) -- If Index value equal to 3 then more than one year data will be pick from archive database.         
  IF @Index = 3 BEGIN --delete offers  from   tbEMSOffers offers join #Parttemp prt on offers.FullPart = prt.PartNames                                              
  --where bommanagerno = @BOMManagerNo                                         
;

WITH cteSearch AS(
  /*SELECT                                                  
   o.OfferId                     
   , o.FullPart                                                                            
   , o.Part                
   , o.ManufacturerNo                                                                            
   , o.DateCode                                                                            
   , o.ProductNo                                                                            
   , o.PackageNo                                                           
   , o.Quantity                            
   , o.Price                                                                            
   , o.OriginalEntryDate                                 
   , o.Salesman                                                                            
   , o.SupplierNo                                                                            
   , o.CurrencyNo                                                     
   , o.ROHS                                                                         
   , o.UpdatedBy                                                   
   , o.DLUP                                                                            
   , o.OfferStatusNo                                                                         
   , ISNULL(o.OfferStatusChangeDate, o.OriginalEntryDate) AS OfferStatusChangeDate                                                         
   , o.OfferStatusChangeLoginNo                                                                            
   , m.ManufacturerCode                                                                            
   , p.ProductName                                                                            
   , c.CurrencyCode                                                                            
   , c.CurrencyDescription                                                           
   , ISNULL(s.CompanyName, o.SupplierName) AS SupplierName                                                                            
   , ISNULL(m.ManufacturerName, o.ManufacturerName) AS ManufacturerName                                                                            
   , s.EMail AS SupplierEmail                                                                  
   , l.EmployeeName AS SalesmanName                                                                            
   , l2.EmployeeName AS OfferStatusChangeEmployeeName                                                                            
   , g.PackageName                                                 
   , o.Notes                                                                            
   , o.ClientNo                                                                            
   , cl.ClientId                                                                          
   , cl.ClientName                                                                            
   , isnull(cl.OwnDataVisibleToOthers,0) AS ClientDataVisibleToOthers            
   --[001] code start                                                                            
   , isnull(cotype.Name,'') as SupplierType              
   --[001] code end                                                    
   , cl.ClientCode                                                        
   , o.SPQ                                                      
   , o.LeadTime                              
   , o.ROHSStatus                                                      
   , o.FactorySealed                                                      
   --,  o.MSL             
   , ml.MSLLevel as MSL                                                
   , 0 as IPOBOMNo                                                      
   ,o.SupplierTotalQSA                                                      
   ,o.SupplierLTB                                        
   ,o.SupplierMOQ                                                      
   ,ishub=0                                                    
   ,prt.CustomerRequirementId                                              
   FROM  [BorisGlobalTraderArchive].dbo.tbOffer_arc o                                                
   LEFT JOIN dbo.tbManufacturer m ON o.ManufacturerNo = m.ManufacturerId                                                                      
   LEFT JOIN dbo.tbProduct p ON o.ProductNo = p.ProductId                                                                            
   LEFT JOIN dbo.tbCurrency c ON o.CurrencyNo = c.CurrencyId                                                                            
   LEFT JOIN dbo.tbCompany s ON o.SupplierNo = s.CompanyId                                                                            
   LEFT JOIN dbo.tbLogin l ON o.Salesman = l.LoginId                                                                            
   LEFT JOIN dbo.tbLogin l2 ON o.OfferStatusChangeLoginNo = l2.LoginId                                                                            
   LEFT JOIN dbo.tbPackage g ON o.PackageNo = g.PackageId                                                                            
   JOIN    tbClient cl ON o.ClientNo = cl.ClientId                                                       
   join #Parttemp prt  on o.FullPart=prt.PartNames                                              
   --[001] code start                      
   LEFT JOIN dbo.tbCompanyType cotype ON s.TypeNo=cotype.CompanyTypeId                                                   
   left join tbMSLLevel ml on o.MSLLevelNo = ml.MSLLevelId                                                   
   
   --[001] code end                                                                             
   WHERE                                                  
   -- Espire: 2 Aug 2017: Comment the client filter from HUB side                                                 
   --o.ClientNo = @ClientId                                                    
   --  AND ((@IsPoHUB is NULL)                                                     
   --            OR (not @IsPoHUB is NULL AND isnull(o.IsPoHub,0)= @IsPoHUB ))                                                        
   o.FullPart = prt.PartNames                                              
   --o.FullPart LIKE @PartSearch                                                                
   AND (dbo.ufn_get_date_from_datetime(ISNULL(o.OfferStatusChangeDate, o.OriginalEntryDate)) between @FROMDATE   AND  @ENDDATE)                                                                              
   --ORDER BY ISNULL(o.OfferStatusChangeDate, o.OriginalEntryDate) DESC                                                    
   union all                                                      
   */
  select
    OfferId,
    FullPart,
    Part,
    ManufacturerNo,
    DateCode,
    ProductNo,
    PackageNo,
    Quantity,
    Price,
    OriginalEntryDate,
    Salesman,
    SupplierNo,
    CurrencyNo,
    ROHS,
    UpdatedBy,
    DLUP,
    OfferStatusNo,
    OfferStatusChangeDate,
    OfferStatusChangeLoginNo,
    ManufacturerCode,
    ProductName,
    CurrencyCode,
    CurrencyDescription,
    SupplierName,
    ManufacturerName,
    SupplierEmail,
    SalesmanName,
    OfferStatusChangeEmployeeName,
    PackageName,
    Replace(Notes, CHAR(10), ' ') as Notes,
    ClientNo,
    ClientId,
    ClientName,
    ClientDataVisibleToOthers --[001] code start                                                          
,
    SupplierType --[001] code end                                                          
,
    ClientCode,
    '' as SPQ,
    '' as LeadTime,
    '' as ROHSStatus,
    '' as FactorySealed,
    '' as MSL,
    0 as IPOBOMNo,
    '' as SupplierTotalQSA,
    '' as SupplierLTB,
    '' as SupplierMOQ,
    ishub,
    prt.CustomerRequirementId,
    vwsource.POHubCompanyNo as POHubCompanyNo
  from
    [vwSourcingData] vwsource
    join #Parttemp prt on vwsource.FullPart = prt.PartNames                                              
  where
    --((ClientNo = @ClientId)                                                                          
    --      OR (ClientNo <> @ClientId                                                                          
    --   AND ClientDataVisibleToOthers = 1))   AND                                                       
    --vwsource.FullPart LIKE @PartSearch                                                                      
    vwsource.FullPart = prt.PartNames
    AND (
      dbo.ufn_get_date_from_datetime(ISNULL(OfferStatusChangeDate, OriginalEntryDate)) between @FROMDATEVW
      AND @ENDDATEVW
    )
) --select *,dbo.ufn_GetSupplierMessage(SupplierNo) as  SupplierMessage  from cteSearch ORDER BY ISNULL(OfferStatusChangeDate, OriginalEntryDate) DESC                                                  
insert into
  #tbEMSOffers           
  (
    BOMManagerNo,
    OfferId,
    FullPart,
    Part,
    ManufacturerNo,
    DateCode,
    ProductNo,
    PackageNo,
    Quantity,
    Price,
    OriginalEntryDate,
    Salesman,
    SupplierNo,
    CurrencyNo,
    ROHS,
    UpdatedBy,
    DLUP,
    OfferStatusNo,
    OfferStatusChangeDate,
    OfferStatusChangeLoginNo,
    ManufacturerCode,
    ProductName,
    CurrencyCode,
    CurrencyDescription,
    SupplierName,
    ManufacturerName,
    SupplierEmail,
    SalesmanName,
    OfferStatusChangeEmployeeName,
    PackageName,
    Notes,
    ClientNo,
    ClientId,
    ClientName,
    ClientDataVisibleToOthers,
    SupplierType,
    ClientCode,
    SPQ,
    LeadTime,
    ROHSStatus,
    FactorySealed,
    MSL,
    IPOBOMNo,
    SupplierManufacturerName,
    SupplierDateCode,
    SupplierPackageType,
    SupplierProductType,
    SupplierMOQ,
    SupplierTotalQSA,
    SupplierLTB,
    SupplierNotes,
    MSLLevelNo,
    SupplierMessage,
    CustomerRequirementId,
    Alternatestatus,
    alternateCustomerRequirementNumber,
    POHubCompanyNo
  )
select
  @BOMManagerNo,
  OfferId,
  FullPart,
  Part,
  ManufacturerNo,
  DateCode,
  ProductNo,
  PackageNo,
  Quantity,
  Price,
  OriginalEntryDate,
  Salesman,
  SupplierNo,
  CurrencyNo,
  ROHS,
  UpdatedBy,
  DLUP,
  OfferStatusNo,
  OfferStatusChangeDate,
  OfferStatusChangeLoginNo,
  ManufacturerCode,
  ProductName,
  CurrencyCode,
  CurrencyDescription,
  SupplierName,
  isnull(ManufacturerName, '') as ManufacturerName,
  SupplierEmail,
  SalesmanName,
  OfferStatusChangeEmployeeName,
  PackageName,
  Notes,
  ClientNo,
  ClientId,
  ClientName,
  ClientDataVisibleToOthers,
  SupplierType,
  ClientCode,
  SPQ,
  LeadTime,
  ROHSStatus,
  FactorySealed,
  MSL,
  IPOBOMNo,
  '' as 'SupplierManufacturerName',
  '' as 'SupplierDateCode',
  1 as 'SupplierPackageType',
  1 as 'SupplierProductType',
  SupplierMOQ,
  SupplierTotalQSA,
  SupplierLTB,
  '1' as SupplierNotes,
  1 as MSLLevelNo,
  dbo.ufn_GetSupplierMessage(SupplierNo),
  CustomerRequirementId,
  null,
  null,
  POHubCompanyNo
from
  cteSearch
where
  isnull(suppliername, '') <> ''
ORDER BY
  ISNULL(OfferStatusChangeDate, OriginalEntryDate) DESC delete a
from
  #tbEMSOffers a join tbemsoffers b on a.offerid =b.offerid where a.bommanagerno=b.bommanagerno                                            
insert into
  tbEMSOffers (
    BOMManagerNo,
    OfferId,
    FullPart,
    Part,
    ManufacturerNo,
    DateCode,
    ProductNo,
    PackageNo,
    Quantity,
    Price,
    OriginalEntryDate,
    Salesman,
    SupplierNo,
    CurrencyNo,
    ROHS,
    UpdatedBy,
    DLUP,
    OfferStatusNo,
    OfferStatusChangeDate,
    OfferStatusChangeLoginNo,
    ManufacturerCode,
    ProductName,
    CurrencyCode,
    CurrencyDescription,
    SupplierName,
    ManufacturerName,
    SupplierEmail,
    SalesmanName,
    OfferStatusChangeEmployeeName,
    PackageName,
    Notes,
    ClientNo,
    ClientId,
    ClientName,
    ClientDataVisibleToOthers,
    SupplierType,
    ClientCode,
    SPQ,
    LeadTime,
    ROHSStatus,
    FactorySealed,
    MSL,
    IPOBOMNo,
    SupplierManufacturerName,
    SupplierDateCode,
    SupplierPackageType,
    SupplierProductType,
    SupplierMOQ,
    SupplierTotalQSA,
    SupplierLTB,
    SupplierNotes,
    MSLLevelNo,
    SupplierMessage,
    CustomerRequirementId,
    Alternatestatus,
    alternateCustomerRequirementNumber,
    POHubCompanyNo
  )
select
  @BOMManagerNo,
  OfferId,
  FullPart,
  Part,
  ManufacturerNo,
  DateCode,
  ProductNo,
  PackageNo,
  Quantity,
  Price,
  OriginalEntryDate,
  Salesman,
  SupplierNo,
  CurrencyNo,
  ROHS,
  UpdatedBy,
  DLUP,
  OfferStatusNo,
  OfferStatusChangeDate,
  OfferStatusChangeLoginNo,
  ManufacturerCode,
  ProductName,
  CurrencyCode,
  CurrencyDescription,
  SupplierName,
  isnull(ManufacturerName, '') as ManufacturerName,
  SupplierEmail,
  SalesmanName,
  OfferStatusChangeEmployeeName,
  PackageName,
  Notes,
  ClientNo,
  ClientId,
  ClientName,
  ClientDataVisibleToOthers,
  SupplierType,
  ClientCode,
  SPQ,
  LeadTime,
  ROHSStatus,
  FactorySealed,
  MSL,
  IPOBOMNo,
  '' as 'SupplierManufacturerName',
  '' as 'SupplierDateCode',
  1 as 'SupplierPackageType',
  1 as 'SupplierProductType',
  SupplierMOQ,
  SupplierTotalQSA,
  SupplierLTB,
  '1' as SupplierNotes,
  1 as MSLLevelNo,
  dbo.ufn_GetSupplierMessage(SupplierNo),
  CustomerRequirementId,
  null,
  null,
  POHubCompanyNo
from
  #tbEMSOffers                                             
select
  BOMManagerNo,
  EMSOfferId as OfferId,
  FullPart,
  Part,
  ManufacturerNo,
  DateCode,
  ProductNo,
  PackageNo,
  Quantity,
  Price,
  OriginalEntryDate,
  Salesman,
  SupplierNo,
  CurrencyNo,
  ROHS,
  UpdatedBy,
  DLUP,
  OfferStatusNo,
  OfferStatusChangeDate,
  OfferStatusChangeLoginNo,
  ManufacturerCode,
  ProductName,
  CurrencyCode,
  CurrencyDescription,
  SupplierName,
  isnull(ManufacturerName, '') as ManufacturerName,
  SupplierEmail,
  SalesmanName,
  OfferStatusChangeEmployeeName,
  PackageName,
  Notes,
  ClientNo,
  ClientId,
  ClientName,
  ClientDataVisibleToOthers,
  SupplierType,
  ClientCode,
  SPQ,
  LeadTime,
  ROHSStatus,
  FactorySealed,
  MSL,
  IPOBOMNo,
  SupplierManufacturerName,
  SupplierDateCode,
  SupplierPackageType,
  SupplierProductType,
  SupplierMOQ,
  SupplierTotalQSA,
  SupplierLTB,
  SupplierNotes,
  MSLLevelNo,
  dbo.ufn_GetSupplierMessage(SupplierNo),
  offers.CustomerRequirementId,
  POHubCompanyNo
from
  tbEMSOffers offers
  join #Parttemp prt on offer.CustomerRequirementId = prt.CustomerRequirementId and offers.FullPart = prt.PartNames                                              
where
  bommanagerno = @BOMManagerNo
  and isnull(Alternatestatus, 0) = 0
union
all
select
  BOMManagerNo,
  EMSOfferId as OfferId,
  FullPart,
  Part,
  ManufacturerNo,
  DateCode,
  ProductNo,
  PackageNo,
  Quantity,
  Price,
  OriginalEntryDate,
  Salesman,
  SupplierNo,
  CurrencyNo,
  ROHS,
  UpdatedBy,
  DLUP,
  OfferStatusNo,
  OfferStatusChangeDate,
  OfferStatusChangeLoginNo,
  ManufacturerCode,
  ProductName,
  CurrencyCode,
  CurrencyDescription,
  SupplierName,
  isnull(ManufacturerName, '') as ManufacturerName,
  SupplierEmail,
  SalesmanName,
  OfferStatusChangeEmployeeName,
  PackageName,
  Notes,
  ClientNo,
  ClientId,
  ClientName,
  ClientDataVisibleToOthers,
  SupplierType,
  ClientCode,
  SPQ,
  LeadTime,
  ROHSStatus,
  FactorySealed,
  MSL,
  IPOBOMNo,
  SupplierManufacturerName,
  SupplierDateCode,
  SupplierPackageType,
  SupplierProductType,
  SupplierMOQ,
  SupplierTotalQSA,
  SupplierLTB,
  SupplierNotes,
  MSLLevelNo,
  dbo.ufn_GetSupplierMessage(SupplierNo),
  offers.CustomerRequirementId,
  POHubCompanyNo
from
  tbEMSOffers offers
  join #Parttemp prt on offer.alternateCustomerRequirementNumber = prt.CustomerRequirementId         
  --and offers.FullPart = prt.PartNames                                              
where
  bommanagerno = @BOMManagerNo
  and Alternatestatus = 1
END
ELSE BEGIN -- delete offers  from   tbEMSOffers offers join #Parttemp prt on offers.FullPart = prt.PartNames                                              
--where bommanagerno = @BOMManagerNo                                               
;

WITH cteSearch1 AS(
  SELECT
    o.OfferId,
    o.FullPart,
    o.Part,
    o.ManufacturerNo,
    o.DateCode,
    o.ProductNo,
    o.PackageNo,
    o.Quantity,
    o.Price,
    o.OriginalEntryDate,
    o.Salesman,
    o.SupplierNo,
    o.CurrencyNo,
    o.ROHS,
    o.UpdatedBy,
    o.DLUP,
    o.OfferStatusNo,
    ISNULL(o.OfferStatusChangeDate, o.OriginalEntryDate) AS OfferStatusChangeDate,
    o.OfferStatusChangeLoginNo,
    m.ManufacturerCode,
    p.ProductName,
    c.CurrencyCode,
    c.CurrencyDescription,
    ISNULL(s.CompanyName, o.SupplierName) AS SupplierName,
    ISNULL(m.ManufacturerName, o.ManufacturerName) AS ManufacturerName,
    s.EMail AS SupplierEmail,
    l.EmployeeName AS SalesmanName,
    l2.EmployeeName AS OfferStatusChangeEmployeeName,
    g.PackageName,
    o.Notes,
    o.ClientNo,
    cl.ClientId,
    cl.ClientName,
    isnull(cl.OwnDataVisibleToOthers, 0) AS ClientDataVisibleToOthers --[001] code start                                                                            
,
    isnull(cotype.Name, '') as SupplierType --[001] code end                                                          
,
    cl.ClientCode,
    o.SPQ,
    o.LeadTime,
    o.ROHSStatus,
    o.FactorySealed --,  o.MSL                                                         
,
    ml.MSLLevel as MSL,
    0 as IPOBOMNo,
    o.SupplierTotalQSA,
    o.SupplierLTB,
    o.SupplierMOQ,
    ishub = 0,
    prt.CustomerRequirementId,
    o.SupplierNo as POHubCompanyNo
  FROM
    [BorisGlobalTraderImports].dbo.tbOffer o
    LEFT JOIN dbo.tbManufacturer m ON o.ManufacturerNo = m.ManufacturerId
    LEFT JOIN dbo.tbProduct p ON o.ProductNo = p.ProductId
    LEFT JOIN dbo.tbCurrency c ON o.CurrencyNo = c.CurrencyId
    LEFT JOIN dbo.tbCompany s ON o.SupplierNo = s.CompanyId
    LEFT JOIN dbo.tbLogin l ON o.Salesman = l.LoginId
    LEFT JOIN dbo.tbLogin l2 ON o.OfferStatusChangeLoginNo = l2.LoginId
    LEFT JOIN dbo.tbPackage g ON o.PackageNo = g.PackageId
    JOIN tbClient cl ON o.ClientNo = cl.ClientId --[001] code start                                                                            
    LEFT JOIN dbo.tbCompanyType cotype ON s.TypeNo = cotype.CompanyTypeId
    left join tbMSLLevel ml on o.MSLLevelNo = ml.MSLLevelId
    join #Parttemp prt on o.FullPart=prt.PartNames                                              
    --[001] code end                                        
  WHERE
    -- Espire: 2 Aug 2017: Comment the client filter from HUB side                                          
    --o.ClientNo = @ClientId                    
    --  AND ((@IsPoHUB is NULL)                                                     
    --            OR (not @IsPoHUB is NULL AND isnull(o.IsPoHub,0)= @IsPoHUB ))                                                        
    --o.FullPart LIKE @PartSearch                                                                
    o.FullPart = prt.PartNames
    AND (
      dbo.ufn_get_date_from_datetime(
        ISNULL(o.OfferStatusChangeDate, o.OriginalEntryDate)
      ) between @FROMDATE
      AND @ENDDATE
    )
    and o.IsBomManager <> 1 --ORDER BY ISNULL(o.OfferStatusChangeDate, o.OriginalEntryDate) DESC                                                 
  union
  all
  select
    OfferId,
    FullPart,
    Part,
    ManufacturerNo,
    DateCode,
    ProductNo,
    PackageNo,
    Quantity,
    Price,
    OriginalEntryDate,
    Salesman,
    SupplierNo,
    CurrencyNo,
    ROHS,
    UpdatedBy,
    DLUP,
    OfferStatusNo,
    OfferStatusChangeDate,
    OfferStatusChangeLoginNo,
    ManufacturerCode,
    ProductName,
    CurrencyCode,
    CurrencyDescription,
    SupplierName,
    ManufacturerName,
    SupplierEmail,
    SalesmanName,
    OfferStatusChangeEmployeeName,
    PackageName,
    Replace(Notes, CHAR(10), ' ') as Notes,
    ClientNo,
    ClientId,
    ClientName,
    ClientDataVisibleToOthers --[001] code start                                                         
,
    SupplierType --[001] code end                                                        
,
    ClientCode,
    '' as SPQ,
    '' as LeadTime,
    '' as ROHSStatus,
    '' as FactorySealed,
    '' as MSL,
    0 as IPOBOMNo,
    '' as SupplierTotalQSA,
    '' as SupplierLTB,
    '' as SupplierMOQ,
    ishub,
    prt.CustomerRequirementId,
    vwsource.POHubCompanyNo as POHubCompanyNo
  from
    [vwSourcingData] vwsource
    join #Parttemp prt on vwsource.FullPart = prt.PartNames                   
  where
    --((ClientNo = @ClientId)                                                                          
    --      OR (ClientNo <> @ClientId                          
    --   AND ClientDataVisibleToOthers = 1))   AND                                                       
    --FullPart LIKE @PartSearch                                                                      
    vwsource.FullPart = prt.PartNames
    AND (
      dbo.ufn_get_date_from_datetime(ISNULL(OfferStatusChangeDate, OriginalEntryDate)) between @FROMDATEVW
      AND @ENDDATEVW
    )
) --select * ,dbo.ufn_GetSupplierMessage(SupplierNo) as  SupplierMessage from cteSearch1  ORDER BY ISNULL(OfferStatusChangeDate, OriginalEntryDate) DESC                                                  
insert into
  #tbEMSOffers                                               
  (
    BOMManagerNo,
    OfferId,
    FullPart,
    Part,
    ManufacturerNo,
    DateCode,
    ProductNo,
    PackageNo,
    Quantity,
    Price,
    OriginalEntryDate,
    Salesman,
    SupplierNo,
    CurrencyNo,
    ROHS,
    UpdatedBy,
    DLUP,
    OfferStatusNo,
    OfferStatusChangeDate,
    OfferStatusChangeLoginNo,
    ManufacturerCode,
    ProductName,
    CurrencyCode,
    CurrencyDescription,
    SupplierName,
    ManufacturerName,
    SupplierEmail,
    SalesmanName,
    OfferStatusChangeEmployeeName,
    PackageName,
    Notes,
    ClientNo,
    ClientId,
    ClientName,
    ClientDataVisibleToOthers,
    SupplierType,
    ClientCode,
    SPQ,
    LeadTime,
    ROHSStatus,
    FactorySealed,
    MSL,
    IPOBOMNo,
    SupplierManufacturerName,
    SupplierDateCode,
    SupplierPackageType,
    SupplierProductType,
    SupplierMOQ,
    SupplierTotalQSA,
    SupplierLTB,
    SupplierNotes,
    MSLLevelNo,
    SupplierMessage,
    CustomerRequirementId,
    Alternatestatus,
    alternateCustomerRequirementNumber,
    POHubCompanyNo
  )
select
  @BOMManagerNo,
  OfferId,
  FullPart,
  Part,
  ManufacturerNo,
  DateCode,
  ProductNo,
  PackageNo,
  Quantity,
  Price,
  OriginalEntryDate,
  Salesman,
  SupplierNo,
  CurrencyNo,
  ROHS,
  UpdatedBy,
  DLUP,
  OfferStatusNo,
  OfferStatusChangeDate,
  OfferStatusChangeLoginNo,
  ManufacturerCode,
  ProductName,
  CurrencyCode,
  CurrencyDescription,
  SupplierName,
  isnull(ManufacturerName, '') as ManufacturerName,
  SupplierEmail,
  SalesmanName,
  OfferStatusChangeEmployeeName,
  PackageName,
  Notes,
  ClientNo,
  ClientId,
  ClientName,
  ClientDataVisibleToOthers,
  SupplierType,
  ClientCode,
  SPQ,
  LeadTime,
  ROHSStatus,
  FactorySealed,
  MSL,
  IPOBOMNo,
  '' as 'SupplierManufacturerName',
  '' as 'SupplierDateCode',
  1 as 'SupplierPackageType',
  1 as 'SupplierProductType',
  SupplierMOQ,
  SupplierTotalQSA,
  SupplierLTB,
  '1' as SupplierNotes,
  1 as MSLLevelNo,
  dbo.ufn_GetSupplierMessage(SupplierNo),
  CustomerRequirementId,
  null,
  null,
  POHubCompanyNo
from
  cteSearch1
where
  isnull(suppliername, '') <> ''
ORDER BY
  ISNULL(OfferStatusChangeDate, OriginalEntryDate) DESC delete a
from
  #tbEMSOffers a join tbemsoffers b on a.offerid =b.offerid   where a.bommanagerno=b.bommanagerno                                        
insert into
  tbEMSOffers (
    BOMManagerNo,
    OfferId,
    FullPart,
    Part,
    ManufacturerNo,
    DateCode,
    ProductNo,
    PackageNo,
    Quantity,
    Price,
    OriginalEntryDate,
    Salesman,
    SupplierNo,
    CurrencyNo,
    ROHS,
    UpdatedBy,
    DLUP,
    OfferStatusNo,
    OfferStatusChangeDate,
    OfferStatusChangeLoginNo,
    ManufacturerCode,
    ProductName,
    CurrencyCode,
    CurrencyDescription,
    SupplierName,
    ManufacturerName,
    SupplierEmail,
    SalesmanName,
    OfferStatusChangeEmployeeName,
    PackageName,
    Notes,
    ClientNo,
    ClientId,
    ClientName,
    ClientDataVisibleToOthers,
    SupplierType,
    ClientCode,
    SPQ,
    LeadTime,
    ROHSStatus,
    FactorySealed,
    MSL,
    IPOBOMNo,
    SupplierManufacturerName,
    SupplierDateCode,
    SupplierPackageType,
    SupplierProductType,
    SupplierMOQ,
    SupplierTotalQSA,
    SupplierLTB,
    SupplierNotes,
    MSLLevelNo,
    SupplierMessage,
    CustomerRequirementId,
    Alternatestatus,
    alternateCustomerRequirementNumber,
    POHubCompanyNo
  )
select
  @BOMManagerNo,
  OfferId,
  FullPart,
  Part,
  ManufacturerNo,
  DateCode,
  ProductNo,
  PackageNo,
  Quantity,
  Price,
  OriginalEntryDate,
  Salesman,
  SupplierNo,
  CurrencyNo,
  ROHS,
  UpdatedBy,
  DLUP,
  OfferStatusNo,
  OfferStatusChangeDate,
  OfferStatusChangeLoginNo,
  ManufacturerCode,
  ProductName,
  CurrencyCode,
  CurrencyDescription,
  SupplierName,
  isnull(ManufacturerName, '') as ManufacturerName,
  SupplierEmail,
  SalesmanName,
  OfferStatusChangeEmployeeName,
  PackageName,
  Notes,
  ClientNo,
  ClientId,
  ClientName,
  ClientDataVisibleToOthers,
  SupplierType,
  ClientCode,
  SPQ,
  LeadTime,
  ROHSStatus,
  FactorySealed,
  MSL,
  IPOBOMNo,
  '' as 'SupplierManufacturerName',
  '' as 'SupplierDateCode',
  1 as 'SupplierPackageType',
  1 as 'SupplierProductType',
  SupplierMOQ,
  SupplierTotalQSA,
  SupplierLTB,
  '1' as SupplierNotes,
  1 as MSLLevelNo,
  dbo.ufn_GetSupplierMessage(SupplierNo),
  CustomerRequirementId,
  null,
  null,
  POHubCompanyNo
from
  #tbEMSOffers                                             
  declare @TotalRecords int,
  @skip int
select
  @TotalRecords = count(EMSOfferId)
from
  tbEMSOffers offers
  join #Parttemp prt on offers.CustomerRequirementId = prt.CustomerRequirementId --and  offers.FullPart = prt.PartNames                                              
where
  bommanagerno = @BOMManagerNo
  and isnull(suppliername, '') <> ''
set
  @skip = (@Rpp * (@curPage - 1)) if (
    @skip >= @TotalRecords
    and @TotalRecords > 0
  ) begin
set
  @curPage = CAST(@TotalRecords / @Rpp as int)
set
  @skip = CAST((@Rpp * (@curPage - 1)) as int)
end if(@skip < 0) begin
set
  @skip = 0
end --select * into #tempfinaltbEMSOffers from tbEMSOffers where 1 = 2              
--insert into #tempfinaltbEMSOffers        
select
  offers.BOMManagerNo,
  EMSOfferId as OfferId,
  offers.FullPart,
  offers.Part,
  offers.ManufacturerNo,
  offers.DateCode,
  offers.ProductNo,
  offers.PackageNo,
  offers.Quantity,
  offers.Price,
  offers.OriginalEntryDate,
  offers.Salesman,
  offers.SupplierNo,
  offers.CurrencyNo,
  offers.ROHS,
  offers.UpdatedBy,
  offers.DLUP,
  offers.OfferStatusNo,
  offers.OfferStatusChangeDate,
  offers.OfferStatusChangeLoginNo,
  ManufacturerCode,
  ProductName,
  CurrencyCode,
  offers.CurrencyDescription,
  offers.SupplierName,
  isnull(offers.ManufacturerName, '') as ManufacturerName,
  offers.SupplierEmail,
  offers.SalesmanName,
  offers.OfferStatusChangeEmployeeName,
  offers.PackageName,
  offers.Notes,
  offers.ClientNo,
  offers.ClientId,
  offers.ClientName,
  offers.ClientDataVisibleToOthers,
  offers.SupplierType,
  offers.ClientCode,
  offers.SPQ,
  offers.LeadTime,
  offers.ROHSStatus,
  offers.FactorySealed,
  offers.MSL,
  offers.IPOBOMNo,
  offers.SupplierManufacturerName,
  offers.SupplierDateCode,
  offers.SupplierPackageType,
  offers.SupplierProductType,
  offers.SupplierMOQ,
  offers.SupplierTotalQSA,
  offers.SupplierLTB,
  offers.SupplierNotes,
  offers.MSLLevelNo,
  dbo.ufn_GetSupplierMessage(offers.SupplierNo) as SupplierNo2,
  offers.CustomerRequirementId,
  @TotalRecords as TotalRecords,
  prt.ReqStatus as REQStatus,
case
    when offers.EMSOfferID in (
      select
        offerId
      from
        tbAutoSource tbs
      where
        tbs.CustomerRequirementId = prt.CustomerRequirementId
        and tbs.BOMManagerNo = @BOMManagerNo
        and tbs.VendorCategory = 'Offers'
        and isNull(tbs.isDeleted, 0) = 0
    ) then 1
    else 0
  end as 'OfferAddFlag',
  POHubCompanyNo into #tempfinaltbEMSOffers                    
from
  tbEMSOffers offers
  join #Parttemp prt on offers.CustomerRequirementId = prt.CustomerRequirementId and  offers.FullPart = prt.PartNames                              
  --left join tbAutoSource tbs on tbs.CustomerRequirementId=prt.CustomerRequirementId and tbs.BOMManagerNo=@BOMManagerNo and tbs.VendorCategory='EMS Offers'                                
where
  offers.bommanagerno = @BOMManagerNo
  and isnull(Alternatestatus, 0) = 0
union
all
select
  offers.BOMManagerNo,
  EMSOfferId as OfferId,
  offers.FullPart,
  offers.Part,
  offers.ManufacturerNo,
  offers.DateCode,
  offers.ProductNo,
  offers.PackageNo,
  offers.Quantity,
  offers.Price,
  offers.OriginalEntryDate,
  offers.Salesman,
  offers.SupplierNo,
  offers.CurrencyNo,
  offers.ROHS,
  offers.UpdatedBy,
  offers.DLUP,
  offers.OfferStatusNo,
  offers.OfferStatusChangeDate,
  offers.OfferStatusChangeLoginNo,
  ManufacturerCode,
  ProductName,
  CurrencyCode,
  offers.CurrencyDescription,
  offers.SupplierName,
  isnull(offers.ManufacturerName, '') as ManufacturerName,
  offers.SupplierEmail,
  offers.SalesmanName,
  offers.OfferStatusChangeEmployeeName,
  offers.PackageName,
  offers.Notes,
  offers.ClientNo,
  offers.ClientId,
  offers.ClientName,
  offers.ClientDataVisibleToOthers,
  offers.SupplierType,
  offers.ClientCode,
  offers.SPQ,
  offers.LeadTime,
  offers.ROHSStatus,
  offers.FactorySealed,
  offers.MSL,
  offers.IPOBOMNo,
  offers.SupplierManufacturerName,
  offers.SupplierDateCode,
  offers.SupplierPackageType,
  offers.SupplierProductType,
  offers.SupplierMOQ,
  offers.SupplierTotalQSA,
  offers.SupplierLTB,
  offers.SupplierNotes,
  offers.MSLLevelNo,
  dbo.ufn_GetSupplierMessage(offers.SupplierNo) as SupplierNo2,
  offers.CustomerRequirementId,
  @TotalRecords as TotalRecords,
  prt.ReqStatus as REQStatus,
case
    when offers.EMSOfferID in (
      select
        offerId
      from
        tbAutoSource tbs
      where
        tbs.CustomerRequirementId = prt.CustomerRequirementId
        and tbs.BOMManagerNo = @BOMManagerNo
        and tbs.VendorCategory = 'Offers'
        and isNull(tbs.isDeleted, 0) = 0
    ) then 1
    else 0
  end as 'OfferAddFlag',
  POHubCompanyNo
from
  tbEMSOffers offers
  join #Parttemp prt on offers.alternateCustomerRequirementNumber = prt.CustomerRequirementId --and  offers.FullPart = prt.PartNames                              
  --left join tbAutoSource tbs on tbs.CustomerRequirementId=prt.CustomerRequirementId and tbs.BOMManagerNo=@BOMManagerNo and tbs.VendorCategory='EMS Offers'                                
where
  offers.bommanagerno = @BOMManagerNo
  and Alternatestatus = 1
select
  *
from
  #tempfinaltbEMSOffers offers        
order by
  offers.DLUP desc OFFSET @skip ROWS FETCH NEXT @Rpp ROWS ONLY
END
SELECT
  @OutPutDate AS OutPutDate
END