///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 09.09.2010:
// - hide Salesperson filter on "My" tab
//
// RP 06.01.2010:
// - fully dispose everything
//
// RP 23.12.2009:
// - render state on server
//
// RP 26.10.2009:
// - Changes due to changes in base class
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DataListNuggets");
Rebound.GlobalTrader.Site.Controls.DataListNuggets.CustomerRequirements = function(element) { 
	Rebound.GlobalTrader.Site.Controls.DataListNuggets.CustomerRequirements.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.DataListNuggets.CustomerRequirements.prototype = {
    get_table: function () { return this._table; }, get_table: function (v) { if (this._table !== v) this._table = v; },
    get_intSalesPersonID: function() {return this._intSalesPersonID; }, set_intSalesPersonID: function(value) { if (this._intSalesPersonID !== value) this._intSalesPersonID = value; },
    get_IsGSA: function () { return this._IsGSA; }, set_IsGSA: function (v) { if (this._IsGSA !== v) this._IsGSA = v; },

    initialize: function() {
        this.addSetupDataCallEvent(Function.createDelegate(this, this.setupDataCall));
        this.addGetDataOKEvent(Function.createDelegate(this, this.getDataOK));
        this.addInitCompleteEvent(Function.createDelegate(this, this.initAfterBaseIsReady));
        this._strPathToData = "controls/DataListNuggets/CustomerRequirements";
        this._strDataObject = "CustomerRequirements";
        Rebound.GlobalTrader.Site.Controls.DataListNuggets.CustomerRequirements.callBaseMethod(this, "initialize");
    },
    initAfterBaseIsReady: function() {
        this.addPageTabChangedEvent(Function.createDelegate(this, this.pageTabChanged));
        this.applySalesPersonFilter();
        this.updateFilterVisibility();
        this.getData();
    },
    dispose: function() {
        if (this.isDisposed) return;
        this._intSalesPersonID = null;
        this._IsGSA = null;
        Rebound.GlobalTrader.Site.Controls.DataListNuggets.CustomerRequirements.callBaseMethod(this, "dispose");
    },

    pageTabChanged: function () {
        this._table._intCurrentPage = 1;
        this._enmViewLevel = this._intCurrentTab;
        this.updateFilterVisibility();
        this.getData();
    },

    setupDataCall: function() {
        this._objData.addParameter("ViewLevel", this._enmViewLevel);
    },

    getDataOK: function (args) {
        if (this._objResult.Results.length == 0) {
            //$("#show").hide();
            //$("#fixicon").hide();
            HideKubIcon();
        }
        else {
            //$("#show").hide();
            //$("#fixicon").show();
            HideKubIcon();
        }
        for (var i = 0, l = this._objResult.Results.length; i < l; i++) {
            var row = this._objResult.Results[i];
            var objExtraData = { StrPartNo: row.Part };
            var aryData = [
				$RGT_nubButton_CustomerRequirement(row.ID, row.No)
				, $R_FN.writeDoubleCellValue($R_FN.writePartNo(row.Part, row.ROHS), $RGT_nubButton_Manufacturer(row.MfrNo, row.Mfr))
				, row.Quantity
                , $R_FN.writeDoubleCellValue(row.blnMakeYellow == true ? '<span style="background-color:yellow;">' + $RGT_nubButton_Company(row.CMNo, row.CM) + '</span>':$RGT_nubButton_Company(row.CMNo, row.CM), $RGT_nubButton_Contact(row.ContactNo, row.Contact))
                //<% --Code start[001]-- %>
                , $R_FN.writeDoubleCellValue(row.Salesman, row.IndustryName)
                 //<% --Code End[001]-- %>
                , $R_FN.writeDoubleCellValue(row.Received, row.Promised)
               //, $RGT_nubButton_BOM(row.BOMNo, row.BOMCode)
                //, $R_FN.writeDoubleCellValue($RGT_nubButton_BOM(row.BOMNo, row.BOMName), $R_FN.setCleanTextValue(row.REQStatusName))
                , $R_FN.writeDoubleCellValue($RGT_nubButton_BOM(row.BOMNo, row.BOMName), $R_FN.setCleanTextValue(row.BomStatus))
                //, $RGT_nubButton_BOM(row.BOMNo, row.BOMName)
                , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.TotalValue), $R_FN.setCleanTextValue(row.TotalBase))
                //, $R_FN.setCleanTextValue(row.REQStatusName)
            ];
            this._table.addRow(aryData, row.ID, false, objExtraData);
            aryData = null; row = null;
        }
    },

    updateFilterVisibility: function() {
        this.getFilterField("ctlSalesman").show(this._enmViewLevel != 0);
        this.getFilterField("ctlClientName").show(this._IsGSA);
    },
    applySalesPersonFilter: function() {
        if ((this._intSalesPersonID) && this._intSalesPersonID > 0)
            this.getFilterField("ctlSalesman").setValue(this._intSalesPersonID);
    }
};
Rebound.GlobalTrader.Site.Controls.DataListNuggets.CustomerRequirements.registerClass("Rebound.GlobalTrader.Site.Controls.DataListNuggets.CustomerRequirements", Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base, Sys.IDisposable);
