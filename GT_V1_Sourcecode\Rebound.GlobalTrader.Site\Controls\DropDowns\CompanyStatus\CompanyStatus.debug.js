///<reference name="MicrosoftAjax.js" />
//-----------------------------------------------------------------------------------------
// RP 06.10.2009:
// - Changes due to changes in base class
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");

Rebound.GlobalTrader.Site.Controls.DropDowns.CompanyStatus = function(element) {
	Rebound.GlobalTrader.Site.Controls.DropDowns.CompanyStatus.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.DropDowns.CompanyStatus.prototype = {
	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.DropDowns.CompanyStatus.callBaseMethod(this, "initialize");
		this.addSetupDataCall(Function.createDelegate(this, this.setupDataCall));
		this.addGetDataOK(Function.createDelegate(this, this.dataCallOK));
	},

	dispose: function() { 
		if (this.isDisposed) return;
		this._intPOHubClientNo = null;
		Rebound.GlobalTrader.Site.Controls.DropDowns.CompanyStatus.callBaseMethod(this, "dispose");
	},
	
	setupDataCall: function() { 
		this._objData.set_PathToData("controls/DropDowns/CompanyStatus");
		this._objData.set_DataObject("CompanyStatus");
		this._objData.set_DataAction("GetData");
	},

	dataCallOK: function() { 
		var result = this._objData._result;
		if (result.CompanyStatus) {
			for (var i = 0; i < result.CompanyStatus.length; i++) {
				this.addOption(result.CompanyStatus[i].Name, result.CompanyStatus[i].ID);
			}
		}
	}
};

Rebound.GlobalTrader.Site.Controls.DropDowns.CompanyStatus.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.CompanyStatus", Rebound.GlobalTrader.Site.Controls.DropDowns.Base);
