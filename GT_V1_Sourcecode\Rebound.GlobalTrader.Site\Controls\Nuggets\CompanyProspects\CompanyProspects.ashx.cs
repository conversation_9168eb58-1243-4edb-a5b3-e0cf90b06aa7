
/*
Marker     Changed by      Date         Remarks
[001]       Bhooma Nand   28/01/2021       Added New Nugget for CRM prospects
*/
using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site;


namespace Rebound.GlobalTrader.Site.Controls.Nuggets.Data
{
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class CompanyProspects : Rebound.GlobalTrader.Site.Data.Base
    {

        public override void ProcessRequest(HttpContext context)
        {
            if (base.init(context))
            {
                switch (Action)
                {
                    case "GetData": GetData(); break;
                    case "SaveEdit": SaveEdit(); break;
                    case "GetCompanyDetailInactive": GetCompanyDetailInactive(); break;
                    default: WriteErrorActionNotFound(); break;
                }
            }
        }

        /// <summary>
        /// Gets the main data
        /// </summary>
        public JsonObject GetData(Company crmProspect)
        {
            JsonObject jsn = null;
            if (crmProspect == null)
                crmProspect = new Company();
            if (crmProspect != null)
            {
                jsn = new JsonObject();
                jsn.AddVariable("ID", crmProspect.ID);
                jsn.AddVariable("ProspectTypeId", crmProspect.ProspectTypeId);
                jsn.AddVariable("ProspectTypeName", Functions.ReplaceLineBreaks(crmProspect.ProspectTypeName));
                jsn.AddVariable("IscrmProspectBoardLevel", crmProspect.IscrmProspectBoardLevel);
                jsn.AddVariable("IsFinalAssembly", crmProspect.IsFinalAssembly);
                jsn.AddVariable("EndCustomer", Functions.ReplaceLineBreaks(crmProspect.EndCustomer));
                jsn.AddVariable("IndustryId", crmProspect.IndustryId);
                jsn.AddVariable("IndustryName", Functions.ReplaceLineBreaks(crmProspect.IndustryName));
                jsn.AddVariable("LimitedEstimate", crmProspect.LimitedEstimate);
                jsn.AddVariable("HealthRating", Functions.ReplaceLineBreaks(crmProspect.HealthRating));
                jsn.AddVariable("ElectronicSpendId", crmProspect.ElectronicSpendId);
                jsn.AddVariable("ElectronicSpendName", Functions.ReplaceLineBreaks(crmProspect.ElectronicSpendName));
                jsn.AddVariable("FrequencyOfPurchaseId", crmProspect.FrequencyOfPurchaseId);
                jsn.AddVariable("FrequencyOfPurchaseName", Functions.ReplaceLineBreaks(crmProspect.FrequencyOfPurchaseName));
                jsn.AddVariable("CommoditiesId", crmProspect.CommoditiesId);
                jsn.AddVariable("CommoditiesName", Functions.ReplaceLineBreaks(crmProspect.CommoditiesName));
                jsn.AddVariable("TurnoverId", crmProspect.TurnoverId);
                jsn.AddVariable("TurnoverName", Functions.ReplaceLineBreaks(crmProspect.TurnoverName));
                jsn.AddVariable("IsIndustry", crmProspect.IsIndustry);
                jsn.AddVariable("IsIndustryAlreadyAssign", crmProspect.IsIndustryAlreadyAssign);
                jsn.AddVariable("Inactive", crmProspect.Inactive);
                //industry types
                JsonObject jsnITs = new JsonObject(true);
                List<BLL.IndustryType> lst = BLL.IndustryType.GetActiveList();
                if (lst.Count > 0)
                {
                    List<BLL.CompanyIndustryType> lstCurrentTypes = BLL.CompanyIndustryType.GetListForCompany(ID);
                    for (int i = 0; i < lst.Count; i++)
                    {
                        JsonObject jsnItem = new JsonObject();
                        jsnItem.AddVariable("ID", lst[i].IndustryTypeId);
                        jsnItem.AddVariable("Name", lst[i].Name);
                        bool blnSelected = false;
                        foreach (BLL.CompanyIndustryType typ in lstCurrentTypes)
                        {
                            if (typ.IndustryTypeNo == lst[i].IndustryTypeId) { blnSelected = true; break; }
                        }
                        jsnItem.AddVariable("Selected", blnSelected);
                        jsnITs.AddVariable(jsnItem);
                    }
                    lstCurrentTypes = null;
                }
                lst = null;
                jsn.AddVariable("IndustryTypes", jsnITs);
            }
            crmProspect = null;
            return jsn;
        }
        public void GetData()
        {
            Company crmProspect = Company.GetCRMProspects(ID);
            OutputResult(GetData(crmProspect));
            crmProspect = null;
        }

        /// <summary>
        /// Save Edit
        /// </summary>
        public void SaveEdit()
        {
            try
            {
                bool blnResult = Company.UpdateCRMProspects(
                   ID
                   , GetFormValue_NullableInt("CRMProspectId")
                   , GetFormValue_NullableInt("ProspectTypeId")
                   , GetFormValue_NullableBoolean("IscrmProspectBoardLevel")
                   , GetFormValue_NullableBoolean("IsFinalAssembly")
                   , GetFormValue_String("EndCustomer")
                   , GetFormValue_NullableBoolean("IsIndustry")
                   , GetFormValue_String("LimitedEstimate")
                   , GetFormValue_String("HealthRating")
                   , GetFormValue_NullableInt("ElectronicSpendId")
                   , GetFormValue_NullableInt("FrequencyOfPurchaseId")
                   //, GetFormValue_NullableInt("CommoditiesId")
                   , GetFormValue_String("Commoditiestext")
                   , GetFormValue_NullableInt("TurnoverId")
                   , LoginID
                   , SessionManager.ClientID

                );
                //save industry types
                CompanyIndustryType.DeleteAllForCompany(ID);
                Array aryIndustryTypes = Functions.JavascriptStringToArray(GetFormValue_String("IndustryTypes"));
                for (int i = 0; i < aryIndustryTypes.Length; i++)
                {
                    CompanyIndustryType.Insert(ID, (Convert.ToInt32(aryIndustryTypes.GetValue(i))));
                }
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Result", blnResult);
                OutputResult(jsn);
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }
        private void GetCompanyDetailInactive()
        {
            Company cm = Company.GetCompanyDetailInactive(ID);
            if (cm == null)
            {
                WriteErrorDataNotFound();
            }
            else
            {
                JsonObject jsn = null;
                if (cm != null)
                {
                    jsn = new JsonObject();
                    jsn.AddVariable("CompanyId", cm.CompanyId);
                    jsn.AddVariable("Inactive", cm.Inactive);
                }
                cm = null;
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            cm = null;
        }
    }
}


