﻿using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

namespace Rebound.GlobalTrader.DAL
{
    public class AS6081Details
    {
        #region Constructors

        public AS6081Details() { }

        #endregion

        #region Properties

        /// <summary>
        /// IncotermId (from Table)
        /// </summary>
        public System.Int32 TypeOfSupplierId { get; set; }

        /// <summary>
        /// Name (from Table)
        /// </summary>
        public System.String Name { get; set; }

        public System.Int32? Id { get; set; }
        public System.Int32? AssignmentHistoryId { get; set; }
        public System.String DocumentNumber { get; set; }
        public System.Int32? DocumentId { get; set; }
        public System.String AssignedTo { get; set; }
        public System.String AssignedBy { get; set; }
        public System.DateTime? LogDate { get; set; }
        public System.String AssignmentType { get; set; }
        public System.Boolean? IsCountryFound { get; set; }
        public System.String CountryName { get; set; }
        public System.Int32? CountryNo { get; set; }
        //[001] code end
        #endregion
    }
}
