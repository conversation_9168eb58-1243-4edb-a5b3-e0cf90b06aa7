﻿/*===========================================================================================  
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-209544]     CuongDox		 17-Sep-2024		CREATE		Add table to store files uploaded
===========================================================================================  
*/
use BorisGlobalTraderImports

DROP TABLE IF EXISTS tbHUBOfferImportLargeFile;

CREATE TABLE tbHUBOfferImportLargeFile (
    ID INT IDENTITY(1,1) NOT NULL,
    OriginalFileName VARCHAR(255) NOT NULL,
    GeneratedFileName VARCHAR(255) NOT NULL,
	GeneratedErrorFile VARCHAR(255) NULL,
    UtilityLogId INT NOT NULL,
    status VARCHAR(50) NOT NULL,
	DLUP datetime,
	ClientId INT,
	UpdatedBy INT,
);


ALTER TABLE [dbo].tbHUBOfferImportLargeFile ADD  CONSTRAINT [DF_tbHUBOfferImportLargeFile_DLUP]  DEFAULT (getdate()) FOR [DLUP]
GO
