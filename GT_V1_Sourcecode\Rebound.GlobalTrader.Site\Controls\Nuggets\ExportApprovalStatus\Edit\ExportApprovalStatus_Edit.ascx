<%--
Marker     Changed by      Date         Remarks
[001]      A<PERSON><PERSON><PERSON>  09/09/2021   Add update supplier approval details form
[002]      A<PERSON><PERSON><PERSON>  27/10/2021   Add new draft button.
--%>
<%@ Control Language="C#" CodeBehind="ExportApprovalStatus_Edit.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.ExportApprovalStatus_Edit" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>

<%@ Register TagPrefix="cc1" Namespace="Rebound.GlobalTrader.Site" Assembly="Rebound.GlobalTrader.Site" %>
<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="true" ShowQuickHelp="false">

	<Links>
		<ReboundUI:IconButton ID="ibtnSave" runat="server" IconGroup="Nugget" IconTitleResource="Save" IconCSSType="Save" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
		<ReboundUI:IconButton ID="ibtnCancel" runat="server" IconGroup="Nugget" IconTitleResource="Cancel" IconCSSType="Cancel" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
    </Links>
	
	<Explanation><%=Functions.GetGlobalResource("FormExplanations", "ExportApproval_Edit")%></Explanation>
	
	<Content>
	<!-- Step 1 ------------------------------------------------------------------->
		<ReboundUI_Table:Form id="frmStep1" runat="server">
            
            <ReboundUI_Form:FormField id="ctlSalesPerson" runat="server" FieldID="lblSalesPerson" ResourceTitle="OGELSalesPerson">
	            <Field><asp:Label ID="lblSalesPerson" runat="server" /></Field>
            </ReboundUI_Form:FormField>
            <ReboundUI_Form:FormField id="ctlSalesOrder" runat="server" FieldID="lblSalesOrder" ResourceTitle="SalesOrder">
	            <Field><asp:Label ID="lblSalesOrder" runat="server" /></Field>
            </ReboundUI_Form:FormField>
            <ReboundUI_Form:FormField id="ctlSOLineNo" runat="server" FieldID="lblSOLineNo" ResourceTitle="OGELSOLine">
	            <Field><asp:Label ID="lblSOLineNo" runat="server" /></Field>
            </ReboundUI_Form:FormField>
			<ReboundUI_Form:FormField id="ctlCustomer" runat="server" FieldID="lblCustomer" ResourceTitle="Customer">
	            <Field><asp:Label ID="lblCustomer" runat="server" /></Field>
            </ReboundUI_Form:FormField>
            <ReboundUI_Form:FormField id="ctlPartNumber" runat="server" FieldID="lblPartNumber" ResourceTitle="PartNumber">
	            <Field><asp:Label ID="lblPartNumber" runat="server" /></Field>
            </ReboundUI_Form:FormField>
			<ReboundUI_Form:FormField id="ctlWarehouse" runat="server" FieldID="lblWarehouse" ResourceTitle="ShipFromWarehouse">
				<Field><asp:Label ID="lblWarehouse" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			<ReboundUI_Form:FormField id="ctlCountry" runat="server" FieldID="lblCountry" ResourceTitle="ShipFromCountry">
				<Field><asp:Label ID="lblCountry" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			<ReboundUI_Form:FormField id="ctlCustomerName" runat="server" FieldID="lblCustomerName" ResourceTitle="ShipToCustomerName">
				<Field><asp:Label ID="lblCustomerName" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			<ReboundUI_Form:FormField id="ctlCustomerCountry" runat="server" FieldID="lblCustomerCountry" ResourceTitle="ShipToCustomerCountry">
				<Field><asp:Label ID="lblCustomerCountry" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			<ReboundUI_Form:FormField id="ctlCommodityCode" runat="server" FieldID="lblCommodityCode" ResourceTitle="CommodityCode">
				<Field><asp:Label ID="lblCommodityCode" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			<ReboundUI_Form:FormField id="ctlECCN" runat="server" FieldID="lblECCN" ResourceTitle="ECCN">
				<Field><asp:Label ID="lblECCN" runat="server" /></Field>
			</ReboundUI_Form:FormField>

             <ReboundUI_Form:FormField id="ctlDestinationCountry" runat="server" FieldID="ddlDestinationCountry" ResourceTitle="EndDestinationCountry" IsRequiredField="true" >
                <Field><ReboundDropDown:Country ID="ddlDestinationCountry" runat="server" IncludeNoValue="true" NoValue_Value="" InitialValue="" /></Field>
            </ReboundUI_Form:FormField>

            <ReboundUI_Form:FormField id="ctlMilitaryuse" runat="server" FieldID="ddlMilitaryuse" ResourceTitle="OgelMilitaryUse" IsRequiredField="true" >
                <Field><ReboundDropDown:Military ID="ddlMilitaryuse" runat="server" IncludeNoValue="true" NoValue_Value="" InitialValue="" /></Field>
            </ReboundUI_Form:FormField>

              
            <ReboundUI_Form:FormField id="ctlEndUser" runat="server" FieldID="txtEndUser" ResourceTitle="OgelEndUser" IsRequiredField="true" >
				<Field><ReboundUI:ReboundTextBox ID="txtEndUser" runat="server" Style="height:49px; width:310px" Width="310" TextMode="MultiLine" Rows="2" CountChar="true" /></Field>          
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlPartApplication" runat="server" FieldID="txtPartApplication" ResourceTitle="PartApplication" IsRequiredField="true" >
				<Field><ReboundUI:ReboundTextBox ID="txtPartApplication" runat="server" Style="height:49px; width:310px" Width="310" TextMode="MultiLine" Rows="2" CountChar="true" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlExportControl" runat="server" FieldID="ddlExportControl" ResourceTitle="ExportControl" IsRequiredField="true" >
				<Field>
					<asp:DropDownList ID="ddlExportControl" runat="server">
						<asp:ListItem Text="No" Value="0"></asp:ListItem>
						<asp:ListItem Text="Yes" Value="1"></asp:ListItem>
					</asp:DropDownList>
				</Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlAerospaceUse" runat="server" FieldID="ddlAerospaceUse" ResourceTitle="AerospaceUse" IsRequiredField="true" >
				<Field>
					<asp:DropDownList ID="ddlAerospaceUse" runat="server">
						<asp:ListItem Text="No" Value="0"></asp:ListItem>
						<asp:ListItem Text="Yes" Value="1"></asp:ListItem>
					</asp:DropDownList>
				</Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlPartTested" runat="server" FieldID="txtPartTested" ResourceTitle="PartTested" IsRequiredField="true" >
				<Field><ReboundUI:ReboundTextBox ID="txtPartTested" runat="server" Style="height:49px; width:310px" Width="310" TextMode="MultiLine" Rows="2" CountChar="true" /></Field>
			</ReboundUI_Form:FormField>

		</ReboundUI_Table:Form>
		
		
          
	</Content>
	
</ReboundUI_Form:DesignBase>



