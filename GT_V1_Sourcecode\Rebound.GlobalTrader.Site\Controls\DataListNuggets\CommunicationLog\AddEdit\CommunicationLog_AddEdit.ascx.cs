using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.Forms {
	public partial class CommunicationLog_AddEdit : Base {

		#region Locals


		#endregion

		#region Properties

		/// <summary>
		/// CompanyID
		/// </summary>
		private int _intCompanyID = -1;
		public int CompanyID {
			get { return _intCompanyID; }
			set { _intCompanyID = value; }
		}

		/// <summary>
		/// ContactID
		/// </summary>
		private int _intContactID = -1;
		public int ContactID {
			get { return _intContactID; }
			set { _intContactID = value; }
		}
		private string _strTitleEdit = "";
		public string TitleEdit {
			get { return _strTitleEdit; }
			set { _strTitleEdit = value; }
		}

		private string _strTitleAdd = "";
		public string TitleAdd {
			get { return _strTitleAdd; }
			set { _strTitleAdd = value; }
		}

		#endregion

		#region Overrides

		/// <summary>
		/// OnInit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
			_strTitleEdit = Functions.GetGlobalResource("FormTitles", "CommunicationLog_Edit");
			_strTitleAdd = Functions.GetGlobalResource("FormTitles", "CommunicationLog_Add");
			TitleText = _strTitleAdd;
			AddScriptReference("Controls.DataListNuggets.CommunicationLog.AddEdit.CommunicationLog_AddEdit.js");
		}

		/// <summary>
		/// OnPreRender
		/// </summary>
		/// <param name="e"></param>
		protected override void OnPreRender(EventArgs e) {
			//SetFieldDefault("ctlDueDate", Functions.FormatDate(DateTime.Now));
			if (_intCompanyID < 1) _intCompanyID = _objQSManager.CompanyID;
			if (_intContactID < 1) _intContactID = _objQSManager.ContactID;
			SetupScriptDescriptors();
			base.OnPreRender(e);
		}

		#endregion

		/// <summary>
		/// Setup Script Descriptors
		/// </summary>
		private void SetupScriptDescriptors() {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Forms.CommunicationLog_AddEdit", ctlDesignBase.ClientID);
			_scScriptControlDescriptor.AddProperty("intCompanyID", _intCompanyID);
			_scScriptControlDescriptor.AddProperty("strTitleAdd", _strTitleAdd);
			_scScriptControlDescriptor.AddProperty("strTitleEdit", _strTitleEdit);

		}

	}
}