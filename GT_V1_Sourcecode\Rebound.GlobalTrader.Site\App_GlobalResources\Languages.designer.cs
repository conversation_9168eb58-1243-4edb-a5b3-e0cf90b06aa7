//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option or rebuild the Visual Studio project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.VisualStudio.Web.Application.StronglyTypedResourceProxyBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class Languages {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Languages() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Resources.Languages", global::System.Reflection.Assembly.Load("App_GlobalResources"));
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Afrikaans.
        /// </summary>
        internal static string Afrikaans {
            get {
                return ResourceManager.GetString("Afrikaans", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Albanian.
        /// </summary>
        internal static string Albanian {
            get {
                return ResourceManager.GetString("Albanian", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Armenian.
        /// </summary>
        internal static string Armenian {
            get {
                return ResourceManager.GetString("Armenian", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Basque.
        /// </summary>
        internal static string Basque {
            get {
                return ResourceManager.GetString("Basque", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Belarusian.
        /// </summary>
        internal static string Belarusian {
            get {
                return ResourceManager.GetString("Belarusian", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bulgarian.
        /// </summary>
        internal static string Bulgarian {
            get {
                return ResourceManager.GetString("Bulgarian", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Chinese.
        /// </summary>
        internal static string Chinese {
            get {
                return ResourceManager.GetString("Chinese", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Chinese (Hong Kong).
        /// </summary>
        internal static string Chinese_HongKong {
            get {
                return ResourceManager.GetString("Chinese_HongKong", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Chinese (Macau).
        /// </summary>
        internal static string Chinese_Macau {
            get {
                return ResourceManager.GetString("Chinese_Macau", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Chinese (Singapore).
        /// </summary>
        internal static string Chinese_Singapore {
            get {
                return ResourceManager.GetString("Chinese_Singapore", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Chinese (Taiwan).
        /// </summary>
        internal static string Chinese_Taiwan {
            get {
                return ResourceManager.GetString("Chinese_Taiwan", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Croatian.
        /// </summary>
        internal static string Croatian {
            get {
                return ResourceManager.GetString("Croatian", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Czech.
        /// </summary>
        internal static string Czech {
            get {
                return ResourceManager.GetString("Czech", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Danish.
        /// </summary>
        internal static string Danish {
            get {
                return ResourceManager.GetString("Danish", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dutch.
        /// </summary>
        internal static string Dutch {
            get {
                return ResourceManager.GetString("Dutch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dutch (Belgium).
        /// </summary>
        internal static string Dutch_Belgium {
            get {
                return ResourceManager.GetString("Dutch_Belgium", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to English.
        /// </summary>
        internal static string English {
            get {
                return ResourceManager.GetString("English", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to English (Australia).
        /// </summary>
        internal static string English_Australia {
            get {
                return ResourceManager.GetString("English_Australia", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to English (Canada).
        /// </summary>
        internal static string English_Canada {
            get {
                return ResourceManager.GetString("English_Canada", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to English (Ireland).
        /// </summary>
        internal static string English_Ireland {
            get {
                return ResourceManager.GetString("English_Ireland", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to English (NewZealand).
        /// </summary>
        internal static string English_NewZealand {
            get {
                return ResourceManager.GetString("English_NewZealand", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to English (SouthAfrica).
        /// </summary>
        internal static string English_SouthAfrica {
            get {
                return ResourceManager.GetString("English_SouthAfrica", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to English (UnitedStates).
        /// </summary>
        internal static string English_UnitedStates {
            get {
                return ResourceManager.GetString("English_UnitedStates", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to English (Zimbabwe).
        /// </summary>
        internal static string English_Zimbabwe {
            get {
                return ResourceManager.GetString("English_Zimbabwe", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Estonian.
        /// </summary>
        internal static string Estonian {
            get {
                return ResourceManager.GetString("Estonian", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Finnish.
        /// </summary>
        internal static string Finnish {
            get {
                return ResourceManager.GetString("Finnish", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to French.
        /// </summary>
        internal static string French {
            get {
                return ResourceManager.GetString("French", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to French (Belgium).
        /// </summary>
        internal static string French_Belgium {
            get {
                return ResourceManager.GetString("French_Belgium", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to French (Canada).
        /// </summary>
        internal static string French_Canada {
            get {
                return ResourceManager.GetString("French_Canada", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to French (Luxembourg).
        /// </summary>
        internal static string French_Luxembourg {
            get {
                return ResourceManager.GetString("French_Luxembourg", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to French (Principality of Monaco).
        /// </summary>
        internal static string French_Monaco {
            get {
                return ResourceManager.GetString("French_Monaco", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to French (Switzerland).
        /// </summary>
        internal static string French_Switzerland {
            get {
                return ResourceManager.GetString("French_Switzerland", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Georgian.
        /// </summary>
        internal static string Georgian {
            get {
                return ResourceManager.GetString("Georgian", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to German.
        /// </summary>
        internal static string German {
            get {
                return ResourceManager.GetString("German", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to German (Austria).
        /// </summary>
        internal static string German_Austria {
            get {
                return ResourceManager.GetString("German_Austria", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to German (Switzerland).
        /// </summary>
        internal static string German_Switzerland {
            get {
                return ResourceManager.GetString("German_Switzerland", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Greek.
        /// </summary>
        internal static string Greek {
            get {
                return ResourceManager.GetString("Greek", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Gujarati.
        /// </summary>
        internal static string Gujarati {
            get {
                return ResourceManager.GetString("Gujarati", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hebrew.
        /// </summary>
        internal static string Hebrew {
            get {
                return ResourceManager.GetString("Hebrew", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hindi.
        /// </summary>
        internal static string Hindi {
            get {
                return ResourceManager.GetString("Hindi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to hrvatski.
        /// </summary>
        internal static string hrvatski_BosnaiHercegovina {
            get {
                return ResourceManager.GetString("hrvatski_BosnaiHercegovina", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hungarian.
        /// </summary>
        internal static string Hungarian {
            get {
                return ResourceManager.GetString("Hungarian", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Icelandic.
        /// </summary>
        internal static string Icelandic {
            get {
                return ResourceManager.GetString("Icelandic", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Indonesian.
        /// </summary>
        internal static string Indonesian {
            get {
                return ResourceManager.GetString("Indonesian", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Italian.
        /// </summary>
        internal static string Italian {
            get {
                return ResourceManager.GetString("Italian", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Italian (Switzerland).
        /// </summary>
        internal static string Italian_Switzerland {
            get {
                return ResourceManager.GetString("Italian_Switzerland", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Japanese.
        /// </summary>
        internal static string Japanese {
            get {
                return ResourceManager.GetString("Japanese", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kannada.
        /// </summary>
        internal static string Kannada {
            get {
                return ResourceManager.GetString("Kannada", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kazakh.
        /// </summary>
        internal static string Kazakh {
            get {
                return ResourceManager.GetString("Kazakh", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kiswahili.
        /// </summary>
        internal static string Kiswahili_Kenya {
            get {
                return ResourceManager.GetString("Kiswahili_Kenya", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Konkani.
        /// </summary>
        internal static string Konkani {
            get {
                return ResourceManager.GetString("Konkani", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Korean.
        /// </summary>
        internal static string Korean {
            get {
                return ResourceManager.GetString("Korean", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Kyrgyz.
        /// </summary>
        internal static string Kyrgyz {
            get {
                return ResourceManager.GetString("Kyrgyz", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Latvian.
        /// </summary>
        internal static string Latvian {
            get {
                return ResourceManager.GetString("Latvian", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lithuanian.
        /// </summary>
        internal static string Lithuanian {
            get {
                return ResourceManager.GetString("Lithuanian", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Macedonian.
        /// </summary>
        internal static string Macedonian {
            get {
                return ResourceManager.GetString("Macedonian", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Malay.
        /// </summary>
        internal static string Malay {
            get {
                return ResourceManager.GetString("Malay", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Maltese.
        /// </summary>
        internal static string Maltese {
            get {
                return ResourceManager.GetString("Maltese", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Marathi.
        /// </summary>
        internal static string Marathi {
            get {
                return ResourceManager.GetString("Marathi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mongolian.
        /// </summary>
        internal static string Mongolian {
            get {
                return ResourceManager.GetString("Mongolian", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Norwegian.
        /// </summary>
        internal static string Norwegian {
            get {
                return ResourceManager.GetString("Norwegian", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Polish.
        /// </summary>
        internal static string Polish {
            get {
                return ResourceManager.GetString("Polish", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Portuguese.
        /// </summary>
        internal static string Portuguese {
            get {
                return ResourceManager.GetString("Portuguese", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Portuguese (Brazil).
        /// </summary>
        internal static string Portuguese_Brazil {
            get {
                return ResourceManager.GetString("Portuguese_Brazil", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Punjabi.
        /// </summary>
        internal static string Punjabi {
            get {
                return ResourceManager.GetString("Punjabi", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Romanian.
        /// </summary>
        internal static string Romanian {
            get {
                return ResourceManager.GetString("Romanian", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Russian.
        /// </summary>
        internal static string Russian {
            get {
                return ResourceManager.GetString("Russian", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sanskrit.
        /// </summary>
        internal static string Sanskrit {
            get {
                return ResourceManager.GetString("Sanskrit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Slovak.
        /// </summary>
        internal static string Slovak {
            get {
                return ResourceManager.GetString("Slovak", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Slovenian.
        /// </summary>
        internal static string Slovenian {
            get {
                return ResourceManager.GetString("Slovenian", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Spanish.
        /// </summary>
        internal static string Spanish {
            get {
                return ResourceManager.GetString("Spanish", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Spanish (Argentina).
        /// </summary>
        internal static string Spanish_Argentina {
            get {
                return ResourceManager.GetString("Spanish_Argentina", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Spanish (Bolivia).
        /// </summary>
        internal static string Spanish_Bolivia {
            get {
                return ResourceManager.GetString("Spanish_Bolivia", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Spanish (Chile).
        /// </summary>
        internal static string Spanish_Chile {
            get {
                return ResourceManager.GetString("Spanish_Chile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Spanish (Colombia).
        /// </summary>
        internal static string Spanish_Colombia {
            get {
                return ResourceManager.GetString("Spanish_Colombia", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Spanish (CostaRica).
        /// </summary>
        internal static string Spanish_CostaRica {
            get {
                return ResourceManager.GetString("Spanish_CostaRica", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Spanish (DominicanRepublic).
        /// </summary>
        internal static string Spanish_DominicanRepublic {
            get {
                return ResourceManager.GetString("Spanish_DominicanRepublic", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Spanish (Ecuador).
        /// </summary>
        internal static string Spanish_Ecuador {
            get {
                return ResourceManager.GetString("Spanish_Ecuador", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Spanish (ElSalvador).
        /// </summary>
        internal static string Spanish_ElSalvador {
            get {
                return ResourceManager.GetString("Spanish_ElSalvador", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Spanish (Guatemala).
        /// </summary>
        internal static string Spanish_Guatemala {
            get {
                return ResourceManager.GetString("Spanish_Guatemala", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Spanish (Honduras).
        /// </summary>
        internal static string Spanish_Honduras {
            get {
                return ResourceManager.GetString("Spanish_Honduras", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Spanish (Mexico).
        /// </summary>
        internal static string Spanish_Mexico {
            get {
                return ResourceManager.GetString("Spanish_Mexico", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Spanish (Nicaragua).
        /// </summary>
        internal static string Spanish_Nicaragua {
            get {
                return ResourceManager.GetString("Spanish_Nicaragua", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Spanish (Panama).
        /// </summary>
        internal static string Spanish_Panama {
            get {
                return ResourceManager.GetString("Spanish_Panama", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Spanish (Paraguay).
        /// </summary>
        internal static string Spanish_Paraguay {
            get {
                return ResourceManager.GetString("Spanish_Paraguay", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Spanish (Peru).
        /// </summary>
        internal static string Spanish_Peru {
            get {
                return ResourceManager.GetString("Spanish_Peru", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Spanish (PuertoRico).
        /// </summary>
        internal static string Spanish_PuertoRico {
            get {
                return ResourceManager.GetString("Spanish_PuertoRico", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Spanish (Uruguay).
        /// </summary>
        internal static string Spanish_Uruguay {
            get {
                return ResourceManager.GetString("Spanish_Uruguay", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Spanish (Venezuela).
        /// </summary>
        internal static string Spanish_Venezuela {
            get {
                return ResourceManager.GetString("Spanish_Venezuela", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Swedish.
        /// </summary>
        internal static string Swedish {
            get {
                return ResourceManager.GetString("Swedish", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Syriac.
        /// </summary>
        internal static string Syriac {
            get {
                return ResourceManager.GetString("Syriac", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tamil.
        /// </summary>
        internal static string Tamil {
            get {
                return ResourceManager.GetString("Tamil", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tatar.
        /// </summary>
        internal static string Tatar {
            get {
                return ResourceManager.GetString("Tatar", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Telugu.
        /// </summary>
        internal static string Telugu {
            get {
                return ResourceManager.GetString("Telugu", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Thai.
        /// </summary>
        internal static string Thai {
            get {
                return ResourceManager.GetString("Thai", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Turkish.
        /// </summary>
        internal static string Turkish {
            get {
                return ResourceManager.GetString("Turkish", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ukrainian.
        /// </summary>
        internal static string Ukrainian {
            get {
                return ResourceManager.GetString("Ukrainian", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Urdu.
        /// </summary>
        internal static string Urdu {
            get {
                return ResourceManager.GetString("Urdu", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vietnamese.
        /// </summary>
        internal static string Vietnamese {
            get {
                return ResourceManager.GetString("Vietnamese", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Welsh.
        /// </summary>
        internal static string Welsh {
            get {
                return ResourceManager.GetString("Welsh", resourceCulture);
            }
        }
    }
}
