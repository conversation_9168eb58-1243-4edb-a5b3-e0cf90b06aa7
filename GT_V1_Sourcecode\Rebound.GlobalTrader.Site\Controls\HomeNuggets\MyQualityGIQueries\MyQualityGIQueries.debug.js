///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//Code Merge for GI Screen
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.HomeNuggets");

Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyQualityGIQueries = function (element) {
    Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyQualityGIQueries.initializeBase(this, [element]);
    this._tblMyQualityGIQueries = null;
};

Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyQualityGIQueries.prototype = {

    get_tblMyQualityGIQueries: function () { return this._tblMyQualityGIQueries; }, set_tblMyQualityGIQueries: function (v) { if (this._tblMyQualityGIQueries !== v) this._tblMyQualityGIQueries = v; },

    initialize: function () {
        Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyQualityGIQueries.callBaseMethod(this, "initialize");
        this.addRefreshEvent(Function.createDelegate(this, this.getData));
    },

    dispose: function () {
        if (this.isDisposed) return;
        if (this._tblMyQualityGIQueries) this._tblMyQualityGIQueries.dispose();
        this._tblMyQualityGIQueries = null;
        Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyQualityGIQueries.callBaseMethod(this, "dispose");
    },

    setupLoadingState: function () {
        this._tblMyQualityGIQueries.show(false);
        Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyQualityGIQueries.callBaseMethod(this, "setupLoadingState");
    },

    getData: function () {
        this.setupLoadingState();
        this._tblMyQualityGIQueries.clearTable();
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/HomeNuggets/MyQualityGIQueries");
        obj.set_DataObject("MyQualityGIQueries");
        obj.set_DataAction("GetData");
        obj._intTimeOutMiliseconds = 1;
        obj.addParameter("rowcount", this._intRowCount);
        obj.addDataOK(Function.createDelegate(this, this.getDataComplete));
        obj.addError(Function.createDelegate(this, this.homeNuggetDataError));
        obj.addTimeout(Function.createDelegate(this, this.homeNuggetDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getDataComplete: function (args) {
        //
        var result = args._result;
        this.showNoneFoundOrContent(result.Count);
        for (var i = 0; i < result.Items.length; i++) {
            var row = result.Items[i];
            var aryData = [
                //$R_FN.setCleanTextValue(row.GINumber),
                $RGT_nubButton_QueryGILine(row.GoodsInId, row.GoodsInLineId, row.GINumber, true),
                $RGT_nubButton_SalesOrder(row.SalesOrderNo, row.SalesOrderNumber),
                $R_FN.setCleanTextValue(row.DateSent),
                
                $R_FN.setCleanTextValue(row.Status)
            ];
            this._tblMyQualityGIQueries.addRow(aryData, null);
            row = null;
        }
        this._tblMyQualityGIQueries.show(result.Count > 0);
        this.hideLoading();
    }
};

Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyQualityGIQueries.registerClass("Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyQualityGIQueries", Rebound.GlobalTrader.Site.Controls.HomeNuggets.Base, Sys.IDisposable);
