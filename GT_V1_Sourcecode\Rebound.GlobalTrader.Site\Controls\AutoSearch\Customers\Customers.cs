using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Collections;

namespace Rebound.GlobalTrader.Site.Controls.AutoSearch {
	[DefaultProperty("")]
	[ToolboxData("<{0}:Customers runat=server></{0}:Customers>")]
	public class Customers : Base {

		#region Overrides

		/// <summary>
		/// OnInit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
            AddScriptReference("Controls.AutoSearch.Customers.Customers.js");
			SetAutoSearchType("Customers");
		}

		/// <summary>
		/// OnLoad
		/// </summary>
		/// <param name="e"></param>
		protected override void OnLoad(EventArgs e) {
			CharactersToEnterBeforeSearch = 2;
			SetupScriptDescriptors();
			base.OnLoad(e);
		}

		#endregion

		/// <summary>
		/// SetupScriptDescriptors
		/// </summary>
		private void SetupScriptDescriptors() {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.AutoSearch.Customers", ClientID);
		}

	}
}