Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.PurchaseOrderStatus=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.PurchaseOrderStatus.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DropDowns.PurchaseOrderStatus.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.DropDowns.PurchaseOrderStatus.callBaseMethod(this,"initialize");this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK))},dispose:function(){this.isDisposed||Rebound.GlobalTrader.Site.Controls.DropDowns.PurchaseOrderStatus.callBaseMethod(this,"dispose")},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/PurchaseOrderStatus");this._objData.set_DataObject("PurchaseOrderStatus");this._objData.set_DataAction("GetData")},dataCallOK:function(){var t=this._objData._result,n;if(t.Types)for(n=0;n<t.Types.length;n++)this.addOption(t.Types[n].Name,t.Types[n].ID)}};Rebound.GlobalTrader.Site.Controls.DropDowns.PurchaseOrderStatus.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.PurchaseOrderStatus",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);