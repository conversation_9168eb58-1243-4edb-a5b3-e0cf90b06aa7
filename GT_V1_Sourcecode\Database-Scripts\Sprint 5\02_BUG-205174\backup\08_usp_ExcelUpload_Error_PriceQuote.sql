﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

IF OBJECT_ID('dbo.usp_ExcelUpload_Error_PriceQuote', 'P') IS NOT NULL
BEGIN
    DROP PROCEDURE dbo.usp_ExcelUpload_Error_PriceQuote
END
GO

/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-201934]		An.TranTan			06-May-2024		Update			Change the comparison logic between import supplier part and requirement part.
===========================================================================================
*/
CREATE PROCEDURE [dbo].[usp_ExcelUpload_Error_PriceQuote]                                                                                                                              
 @UserId INT=0 ,                                                            
 @ClientId INT =0,                                                            
 @SelectedClientId int=0                                                                                                                            
  /*          
   SELECT * FROM BorisGlobalTraderImports.dbo.tbPriceQuoteImport_tempData      
   
   exec [usp_ExcelUpload_Error_PriceQuote_test] 5959,114,101

   */                                                   
WITH RECOMPILE AS                                                                 
    BEGIN                                      
SELECT       
PriceQuoteImportId,                                  
 (case   when len(Column1)>30 then dbo.stripAlphahnumeric(SUBSTRING(Column1,0, 30)) else dbo.stripAlphahnumeric(Column1) end )as REQUIREMENTNo  
 ,(select top 1 c.CustomerRequirementNumber  from  dbo.tbCustomerRequirement c where c.CustomerRequirementNumber=Column1 and c.BOMNo IS NOT NULL ) as RequirementSNo                        
 ,(case   when len( Column6)>100 then dbo.stripAlphahtestingMfr(SUBSTRING(Column6,0, 100))else dbo.stripAlphahtestingMfr(Column6) end )as ManufacturerName                                
 ,(case   when len(Column3)>30 then dbo.stripAlphahnumeric(SUBSTRING(Column3,0, 30)) else dbo.stripAlphahnumeric(Column3) end )as Part                                      
 ,FLOOR(dbo.stripNumeric(Column9)) as Quantity                                  
 ,(case   when len(Column2)>128 then dbo.stripAlphahnumeric(SUBSTRING(Column2,0, 128))else dbo.stripAlphahnumeric(Column2) end )as SupplierName                        
 --,(select top 1 CompanyId from tbcompany  c where lower(c.CompanyName)=lower(Column5) COLLATE SQL_Latin1_General_CP1_CI_AI) as   [SupplierSNo]                         
 ----,(case   when len(Column6)>50 then dbo.stripAlphahnumeric(SUBSTRING(Column6,0, 50))else dbo.stripAlphahnumeric(Column6) end )as SupplierPart                                    
 --,(case   when len(Column7)>50 then dbo.stripAlphahnumeric(SUBSTRING(Column7,0, 50))else dbo.stripAlphahnumeric(Column7) end )as PackageName                                  
 , (case   when len(Column7)>30 then dbo.stripAlphahnumeric2(SUBSTRING(Column7,0, 30))else dbo.stripAlphahnumeric2(Column7) end )as DateCode                                 
 ,cast(dbo.stripNumeric(Column4) as FLOAT ) as SupplierCost                                    
 ,(case   when len(Column16)>3 then dbo.stripAlphahnumeric(SUBSTRING(Column16,0, 3))else dbo.stripAlphahnumeric(Column16) end )as CurrencyCode                                                                    
 --, (case   when len(Column11)>30 then dbo.stripAlphahnumeric2(SUBSTRING(Column11,0, 30))else dbo.stripAlphahnumeric2(Column11) end )as SPQ                                                                                                     
 --, (case   when len(Column12)>30 then dbo.stripAlphahnumeric2(SUBSTRING(Column12,0, 30))else dbo.stripAlphahnumeric2(Column12) end )as SupplierMOQ                                  
 --,dbo.stripNumeric(Column13) as Qty_in_Stock                                     
 --, (case   when len(Column14)>30 then dbo.stripAlphahnumeric2(SUBSTRING(Column14,0, 30))else dbo.stripAlphahnumeric2(Column14) end )as LeadTime                                    
 --,dbo.stripAlphahnumeric(10) as OfferStatus                                
 --,dbo.stripAlphahnumeric(Column5) as ROHS                                
 --,(case   when len(Column17)>50 then dbo.stripAlphahnumeric(SUBSTRING(Column17,0, 50))else dbo.stripAlphahnumeric(Column17) end )as FactorySealed                                  
 --,dbo.stripAlphahnumeric(Column18) as Region                                 
 --,dbo.stripAlphahnumeric(Column19) as DescriptionNotes                          
 ,1 as isValid           
 ,cast('' as varchar(max))  as ValidationMessage  
 ,[dbo].ufn_get_supplier_part_status(dbo.ufn_get_fullpart(column3),tbcust.FullPart)  as PartStatus
 -- (select part from dbo.tbCustomerRequirement tbcust where tbcust.CustomerRequirementNumber=Column1)= column3 then 'Y' 
	--else 'N' end as 'PartStatus'
	,ROW_NUMBER() OVER (partition by Column1,Column6,Column3,Column2 order by Column1) AS RowID 
 into #tbPriceQuoteImport_tempData                                                 
 from  BorisGlobalTraderImports.dbo.tbPriceQuoteImport_tempData  tbtemp
 join dbo.tbCustomerRequirement tbcust on --tbtemp.SelectedClientId=tbcust.ClientNo and 
 tbtemp.Column1= tbcust.CustomerRequirementNumber
 where tbtemp.ClientId=@ClientId and tbtemp.SelectedClientId= @SelectedClientId and tbtemp.CreatedBy=@UserId                                        
                                                     
 /*********** Updation Validation Query **************/                                    
                                 
  --CurrencyCode No length 3  & mandotary--                                                            
 update  TmpR set                                                             
 TmpR.ValidationMessage=  case                                                  
 --when   isnull(TRl.Column10,'')=''                                                           
 --then  ISNULL(ValidationMessage, '') +'CurrencyCode is mandotary.' +'<br/>'                             
 --when   len(TRl.Column10)>3 and  TmpR.CurrencyCode<>TRl.Column10                                                              
 --then  ISNULL(ValidationMessage, '') +'CurrencyCode only accepts 3 characters and AlphaNumeric values.' +'<br/>'                                                               
 --when  TmpR.CurrencyCode<>TRl.Column10                                                        
 --then  ISNULL(ValidationMessage, '') + 'CurrencyCode only accepts AlphaNumeric values.' +'<br/>'                                                             
 when  len(TRl.Column16)<3                                                             
 then  ISNULL(ValidationMessage, '') + 'Currency Code  accepts 3 characters.'+'<br/>'                                                             
 else TmpR.ValidationMessage end                                                            
 from                                                            
  #tbPriceQuoteImport_tempData    TmpR inner Join                                                              
  BorisGlobalTraderImports.dbo.tbPriceQuoteImport_tempData    TRl                                                             
  on Trl.PriceQuoteImportId=TmpR.PriceQuoteImportId                                   
                                 
 --Requirement No length 10  & mandotary--                                                            
 update  TmpR set                                                             
  TmpR.ValidationMessage=  case                                                  
  when   isnull(TRl.Column1,'')=''                                                           
  then  ISNULL(ValidationMessage, '') +'Requirement is mandotary.' +'<br/>'                               
  when   isnull(TmpR.RequirementSNo,0)=0                                                           
  then  ISNULL(ValidationMessage, '') +'Requirement does not Exist or HUBRFQ does not exist for this Requirement' +'<br/>'                       
  --when   len(TRl.Column1)>10                                                            
  --then  ISNULL(ValidationMessage, '') +'Requirement No only accepts 10 characters and Integer values.' +'<br/>'                                                               
  --when  TmpR.REQUIREMENTNo<>TRl.Column1               
  --then  ISNULL(ValidationMessage, '') + 'Requirement No only accepts Integer values.' +'<br/>'                                                             
  --when  len(TRl.Column1)>10                                                             
  --then  ISNULL(ValidationMessage, '') + 'Requirement No only accepts 10 characters.'+'<br/>'                                                             
  else TmpR.ValidationMessage end     
  from                
  #tbPriceQuoteImport_tempData    TmpR inner Join                                                            
  BorisGlobalTraderImports.dbo.tbPriceQuoteImport_tempData    TRl                                    
  on Trl.PriceQuoteImportId=TmpR.PriceQuoteImportId                             
                                
--Part length 30  & mandotary--                                                            
 update  TmpR SET                                                      
 TmpR.ValidationMessage=  CASE                                                  
 --when   isnull(TRl.Column3,'')=''                                                           
 --then  ISNULL(ValidationMessage, '') +'Part No is mandotary.' +'<br/>'               
 WHEN   len(TRl.Column3)>30                                                               
 THEN  ISNULL(ValidationMessage,'') +'Supplier Part No only accepts 30 characters ' +'<br/>'                                                           
 --WHEN  TmpR.Part<>TRl.Column3                                                              
 --THEN  ISNULL(ValidationMessage, '') + 'Supplier Part No only accepts AlphaNumeric values.' +'<br/>'                                                             
 --WHEN  len(TRl.Column3)>30   THEN  ISNULL(ValidationMessage, '') + 'Supplier Part No only accepts 30 characters.'+'<br/>'       
ELSE TmpR.ValidationMessage END                                                   
 FROM                                                            
 #tbPriceQuoteImport_tempData    TmpR inner Join                                                              
 BorisGlobalTraderImports.dbo.tbPriceQuoteImport_tempData    TRl                                                             
 on Trl.PriceQuoteImportId=TmpR.PriceQuoteImportId                                                            
  --- DateCode length 5--                                                            
 update  TmpR set                                                             
 TmpR.ValidationMessage= case                         
 when    len(TRl.Column7)>5 and TmpR.DateCode<>TRl.Column7                                                       
 then  ISNULL(ValidationMessage, '') +'DateCode Field only accepts 5 characters and AlphaNumeric values.' +'<br/>'                                                              
 --when TmpR.DateCode<>TRl.Column8                                                             
 --then  ISNULL(ValidationMessage, '') +'DateCode Field only accepts AlphaNumeric values.' +'<br/>'                                                             
 when    len(TRl.Column7)>5                                                             
 then  ISNULL(ValidationMessage, '') +'DateCode Field only accepts 5 characters.'+'<br/>'                                                              
 else TmpR.ValidationMessage end                                                            
 from                                                            
 #tbPriceQuoteImport_tempData    TmpR inner Join                                                              
 BorisGlobalTraderImports.dbo.tbPriceQuoteImport_tempData    TRl                                                             
 on Trl.PriceQuoteImportId=TmpR.PriceQuoteImportId    
 
 update #tbPriceQuoteImport_tempData
 set ValidationMessage = isnull(ValidationMessage,'') +' Requirment part does not match with supplier part.'
 where PartStatus = 'N'
   
   --select *, ROW_NUMBER() OVER (partition by requirementno,manufacturername,part,suppliername order by requirementno) AS RowID    from #tbPriceQuoteImport_tempData
   --select * from    #tbPriceQuoteImport_tempData
   update #tbPriceQuoteImport_tempData
 set ValidationMessage = isnull(ValidationMessage,'') +' Duplicate record with same RequirementId, Part, Manufacturer and Supplier.'
 where rowid >1
    /*                                                        
 --- ManufacturerName length 100--                                                            
 update  TmpR set                                                            
 TmpR.ValidationMessage= case                                                            
 --when   len(TRl.Column2) >100 and TmpR.ManufacturerName<>TRl.Column2                                                            
 --then  ISNULL(ValidationMessage, '') +'ManufacturerName only accepts 100 characters and AlphaNumeric values.' +'<br/>'               
 --when TmpR.ManufacturerName<>TRl.Column2                                                            
 --then  ISNULL(ValidationMessage, '') +'ManufacturerName only accepts AlphaNumeric values.' +'<br/>'                                
 when len(TRl.Column6) >100                                                            
 then  ISNULL(ValidationMessage, '') +'ManufacturerName only accepts 100 characters.'+'<br/>'                                                             
 else TmpR.ValidationMessage end                                                            
 from                   
 #tbPriceQuoteImport_tempData    TmpR inner Join                                                              
 BorisGlobalTraderImports.dbo.tbPriceQuoteImport_tempData    TRl                                                             
 on Trl.PriceQuoteImportId=TmpR.PriceQuoteImportId                                                            
                                                
--- Quantity   & mandotary--                                       
 update  TmpR set                                                            
 TmpR.ValidationMessage= case                                                
 --when   isnull(TRl.Column4,'')=0                                                           
 --then  ISNULL(ValidationMessage, '') +'Quantity Field is mandotary and value should be greater than zero' +'<br/>'                                                  
 --when   len(TRl.Column4) >2 and TmpR.Quantity<>TRl.Column4          
 --then  ISNULL(ValidationMessage, '')+'Quantity Field only accepts 2 characters and Integer values.' +'<br/>'                                              
 when TmpR.Quantity<>TRl.Column4                                                         
 then  ISNULL(ValidationMessage, '')+'Quantity Field only accepts Integer values.' +'<br/>'                                                             
 --when Len(TRl.Column4) >2                                                            
 --then  ISNULL(ValidationMessage, '')+'Quantity Field only accepts 2 characters.'+'<br/>'                                                            
 else TmpR.ValidationMessage end                                                            
 from                                                            
 #tbPriceQuoteImport_tempData    TmpR inner Join                       
 BorisGlobalTraderImports.dbo.tbPriceQuoteImport_tempData    TRl                                                             
 on Trl.PriceQuoteImportId=TmpR.PriceQuoteImportId                                                            
                                               
--- Price/SupplierCost length 4--                                                            
 update  TmpR set                                                            
 TmpR.ValidationMessage= case                                                   
 when    cast(TmpR.SupplierCost as varchar(100))<>cast(TRl.Column9 as varchar(100))                                                       
 then  ISNULL(ValidationMessage, '') +'SupplierCost Field only accepts Integer values.' +'<br/>'                                                             
 --when cast(TmpR.Price as varchar(100))<>cast(TRl.Price as varchar(100))                                                              
 --then  ISNULL(ValidationMessage, '') +'Cost Field only accepts Integer values.' +'<br/>'                                                                             
 else TmpR.ValidationMessage end                                                            
 from                                                
 #tbPriceQuoteImport_tempData    TmpR inner Join                                                              
 BorisGlobalTraderImports.dbo.tbPriceQuoteImport_tempData    TRl                                                             
 on Trl.PriceQuoteImportId=TmpR.PriceQuoteImportId                                        
                                       
--- LeadTime length 50--                                                            
 update  TmpR set               
 TmpR.ValidationMessage= case                                                            
 when len(TRl.Column14) >50 and TmpR.LeadTime<>TRl.Column14                                                            
 then  ISNULL(ValidationMessage, '') +'LeadTime Field only accepts 50 AlphaNumeric and Integer values.' +'<br/>'                                                              
 when TmpR.LeadTime<>TRl.Column14                                                            
 then  ISNULL(ValidationMessage, '') +'LeadTime Field only accepts AlphaNumeric values.' +'<br/>'                                                             
when len(TRl.Column14) >50                                                            
 then  ISNULL(ValidationMessage, '') +'LeadTime Field only accepts 50 characters.'+'<br/>'                                                             
 else TmpR.ValidationMessage end                                                            
 from                                                            
 #tbPriceQuoteImport_tempData    TmpR inner Join                                                              
 BorisGlobalTraderImports.dbo.tbPriceQuoteImport_tempData    TRl                                                             
 on Trl.PriceQuoteImportId=TmpR.PriceQuoteImportId                                                            
                                                            
--- SPQ length 5--                                                            
 update  TmpR set                               
 TmpR.ValidationMessage= case                                                            
 when   len(TRl.Column11)>5                                                             
 then  ISNULL(ValidationMessage, '') +'SPQ Field only accepts 5 characters' +'<br/>'                                                             
 --when TmpR.SPQ<>TRl.Column11                                                      
 --then  ISNULL(ValidationMessage, '') +'SPQ Field only accepts AlphaNumeric values.' +'<br/>'                                                              
 else TmpR.ValidationMessage end                                                            
 from                                                            
 #tbPriceQuoteImport_tempData    TmpR inner Join                                                              
 BorisGlobalTraderImports.dbo.tbPriceQuoteImport_tempData    TRl                                                  
 on Trl.PriceQuoteImportId=TmpR.PriceQuoteImportId                                                            
                   
--- SupplierMOQ length 50--                             
 update  TmpR set                                                            
 TmpR.ValidationMessage= case                                                            
 when  len(TRl.Column12)>50  and  TmpR.SupplierMOQ<>TRl.Column12                                                             
 then  ISNULL(ValidationMessage, '') +'MOQ Field only accepts 50 characters and AlphaNumeric values.' +'<br/>'                                                             
 when TmpR.SupplierMOQ<>TRl.Column12                                                             
 then  ISNULL(ValidationMessage, '') +'MOQ Field only accepts AlphaNumeric values.' +'<br/>'                                                             
 when  len(TRl.Column12)>50                                                             
 then  ISNULL(ValidationMessage, '') +'MOQ Field only accepts 50 characters.'+'<br/>'                                                            
 else TmpR.ValidationMessage end                           
 from                                              
 #tbPriceQuoteImport_tempData    TmpR inner Join                                                
 BorisGlobalTraderImports.dbo.tbPriceQuoteImport_tempData    TRl                                                             
 on Trl.PriceQuoteImportId=TmpR.PriceQuoteImportId                                                            
                                                            
--- SupplierName length 128--                  
 update  TmpR set                             
 TmpR.ValidationMessage= case                                         
 when  len(TRl.Column5) >128                                                        
 then  ISNULL(ValidationMessage, '') +'SupplierName Field only accepts 128 characters and AlphaNumeric values.' +'<br/>'                
 when isnull(TmpR.SupplierSNo,0)=0                                                          
 then  ISNULL(ValidationMessage, '') +'SupplierName does not exits.' +'<br/>'                          
 --when TmpR.SupplierName<>TRl.Column5                                                             
 --then  ISNULL(ValidationMessage, '') +'SupplierName Field only accepts AlphaNumeric values.' +'<br/>'                                                             
 --when  len(TRl.Column5) >128                                                            
 --then  ISNULL(ValidationMessage, '') +'SupplierName Field only accepts 128 characters.'+'<br/>'                                                            
 else TmpR.ValidationMessage end                                                            
 from                              
 #tbPriceQuoteImport_tempData    TmpR inner Join                                                              
 BorisGlobalTraderImports.dbo.tbPriceQuoteImport_tempData    TRl                                                             
 on Trl.PriceQuoteImportId=TmpR.PriceQuoteImportId                                                            
                                       
                                                            
--- FactorySealed length 50--                                                            
 update  TmpR set                      
 TmpR.ValidationMessage= case                                                            
 --when   len(TRl.Column17) >50 and TmpR.FactorySealed<>TRl.Column17                                                             
 --then  ISNULL(ValidationMessage, '')  +'FactorySealed Field only accepts 50 characters and AlphaNumeric values.' +'<br/>'                                                             
 --when TmpR.FactorySealed<>TRl.Column17                                                  
 --then  ISNULL(ValidationMessage, '')  +'FactorySealed Field only accepts Integer values.' +'<br/>'                                          
 when   len(TRl.Column17) >50                                                            
 then  ISNULL(ValidationMessage, '')  +'FactorySealed Field only accepts 50 characters.'+'<br/>'                                                            
 else TmpR.ValidationMessage end                                                  
 from                                                            
 #tbPriceQuoteImport_tempData    TmpR inner Join                                                              
 BorisGlobalTraderImports.dbo.tbPriceQuoteImport_tempData    TRl                                                             
 on Trl.PriceQuoteImportId=TmpR.PriceQuoteImportId                                                            
                               
   --- Description/Notes length 500--   description                       
 update  TmpR set                                                            
 TmpR.ValidationMessage= case                                                                                                
 when   len(TRl.Column19) >500           
 then  ISNULL(ValidationMessage, '')  +'Notes Field only accepts 500 characters.'+'<br/>'                                          
 else TmpR.ValidationMessage end                                                            
 from                                                            
 #tbPriceQuoteImport_tempData    TmpR inner Join                            
 BorisGlobalTraderImports.dbo.tbPriceQuoteImport_tempData    TRl                                                             
 on Trl.PriceQuoteImportId=TmpR.PriceQuoteImportId                                                            
                         
  --- Qty In Stock length 20--                          
 update  TmpR set                                                            
 TmpR.ValidationMessage= case                                                                                                
 when   len(TRl.Column13) >20                                     
 then  ISNULL(ValidationMessage, '')  +'Qty In Stock only accepts 20 characters.'+'<br/>'                      
 when   TRl.Column13  <> TmpR.Qty_in_Stock                                                            
 then  ISNULL(ValidationMessage, '')  +'Qty In Stock only accepts Integer values.'+'<br/>'                            
 else TmpR.ValidationMessage end                                          
 from                                                            
 #tbPriceQuoteImport_tempData    TmpR inner Join                                                              
 BorisGlobalTraderImports.dbo.tbPriceQuoteImport_tempData    TRl                                                             
 on Trl.PriceQuoteImportId=TmpR.PriceQuoteImportId                                                            
                                                                                                                                            
    */                                                       
/*********** Updation Validation Query  ENDs **************/                                                            
 UPDATE #tbPriceQuoteImport_tempData    SET                                                             
 --ValidationMessage = ValidationMessage +'. Please verify source data and column mappings and try again'                                                 
 isvalid=0                                                    
 WHERE ISNULL(ValidationMessage,'')<>''                                                            
                                                            
/**                    
 Select  @TotalCount= 'TotalCount-'+ cast(count(isvalid) as varchar(100))                                                             
 from #tbPriceQuoteImport_tempData     where isnull(isvalid,0)=0                                                       
 **/                                                            
                                                            
 /*********** Select Final Query  **************/                                                            
 SELECT ROW_NUMBER()OVER( ORDER BY  RL.[PriceQuoteImportId] ASC) AS SNo                                
 ,RL.[column1]    AS 'REQUIREMENT'          
 ,RL.[Column2]  AS 'SUPPLIER NAME'        
 ,RL.[Column3]  AS 'SUPPLIER PART'                                  
 ,RL.[Column4]  AS 'SUPPLIER COST'          
 ,RL.[Column5]  AS 'ROHS'           
 ,RL.[column6]  AS 'MANUFACTURER'                          
 ,RL.[Column7]  AS 'DateCode'         
 ,RL.[Column8]  AS 'PACKAGE'         
 ,RL.[Column6]  AS 'OFFERED QUANTITY'         
 ,RL.[Column10] AS 'OFFER STATUS'                                    
 ,RL.[Column11] AS 'SPQ'          
 ,RL.[Column12] AS 'FACTORY SEALED'                                    
 ,RL.[Column12] AS  'QTY IN STOCK'         
 ,RL.[Column14] AS 'MOQ'         
 ,RL.[Column15] AS 'LAST TIME BUY'         
 ,RL.[Column16] AS 'CURRENCY'          
 ,RL.[Column20] AS 'BUY PRICE'        
 ,RL.[Column20] AS 'SELL PRICE'         
 ,RL.[Column20] AS 'SHIPPING COST'        
 ,RL.[Column20] AS 'LEAD TIME'          
 ,RL.[Column21] AS 'REGION'            
 ,RL.[Column22] AS 'Delivery Date'          
 ,RL.[Column23] AS 'NOTES'          
 ,RL.[Column24] AS 'MSL'      
 ,IIF(TL.ValidationMessage='','','<b>'+TL.ValidationMessage+'<b/>') as 'Reason for Excel Upload failed'                                                                                      
 ,RL.OriginalFilename                                                         
 from #tbPriceQuoteImport_tempData    TL                                                            
 inner join BorisGlobalTraderImports.dbo.tbPriceQuoteImport_tempData RL                                         
 on RL.PriceQuoteImportId=TL.PriceQuoteImportId where isValid<>1                   
END   
  

GO


