﻿/*===========================================================================================  
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-213080]     An.TranTan		 27-Sep-2024		CREATE		Init default value for 2 new permisisons
===========================================================================================  
*/
DECLARE @tbSecurityFunctions TABLE (SecurityFunctionId INT);
INSERT INTO @tbSecurityFunctions VALUES(7000011),(7000012);

DECLARE @tbHubSecurityGroups TABLE(SecurityGroupId INT, IsAdmin BIT);
INSERT INTO @tbHubSecurityGroups
SELECT 
	SecurityGroupId, 
	Administrator
FROM tbSecurityGroup
WHERE ClientNo = 114	--DMCC

--Clear all existing permissions
DELETE tbSecurityGroupSecurityFunctionPermission
WHERE SecurityFunctionNo IN (SELECT SecurityFunctionId FROM @tbSecurityFunctions)

;WITH cte AS(
	SELECT SecurityGroupId,
		SecurityFunctionId,
		IsAdmin
	FROM @tbHubSecurityGroups, @tbSecurityFunctions
)
INSERT INTO tbSecurityGroupSecurityFunctionPermission 
(  
	SecurityGroupNo  
    ,SecurityFunctionNo  
    ,IsAllowed  
    ,DLUP
) 
SELECT 
	SecurityGroupId,
	SecurityFunctionId,	
	IsAdmin, --default: allow for admin only
	GETDATE()
FROM cte 
