///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// add comment here on any change
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");

Rebound.GlobalTrader.Site.Controls.DropDowns.HICStatus = function (element) {
    Rebound.GlobalTrader.Site.Controls.DropDowns.HICStatus.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.DropDowns.HICStatus.prototype = {

    initialize: function () {
        Rebound.GlobalTrader.Site.Controls.DropDowns.HICStatus.callBaseMethod(this, "initialize");
        this.addSetupDataCall(Function.createDelegate(this, this.setupDataCall));
        this.addGetDataOK(Function.createDelegate(this, this.dataCallOK));
    },

    dispose: function () {
        if (this.isDisposed) return;
        Rebound.GlobalTrader.Site.Controls.DropDowns.HICStatus.callBaseMethod(this, "dispose");
    },

    setupDataCall: function () {
        this._objData.set_PathToData("controls/DropDowns/HICStatus");
        this._objData.set_DataObject("HICStatus");
        this._objData.set_DataAction("GetData");
    },

    dataCallOK: function () {
        var result = this._objData._result;
        if (result != null) {
            if (result.Types) {
                for (var i = 0; i < result.Types.length; i++) {
                    this.addOption(result.Types[i].Name, result.Types[i].ID);
                }
            }
        }
    }

};

Rebound.GlobalTrader.Site.Controls.DropDowns.HICStatus.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.HICStatus", Rebound.GlobalTrader.Site.Controls.DropDowns.Base);
