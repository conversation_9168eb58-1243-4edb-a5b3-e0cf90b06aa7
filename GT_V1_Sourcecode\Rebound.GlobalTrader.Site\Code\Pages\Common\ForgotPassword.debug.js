///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference name="~/Controls/IconButton/IconButton.js" />
///<reference name="~/Controls/ReboundTextBox/ReboundTextBox.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.ForgotPassword");

Rebound.GlobalTrader.Site.Pages.ForgotPassword = function() {
    this._intPauseTime = 250; //milliseconds
    this._selectedType = 0;
    
};

Rebound.GlobalTrader.Site.Pages.ForgotPassword.prototype = {
    //get_rad: function() { return this._rad; }, set_rad: function(v) { if (this._rad !== v) this._rad = v; },
    get_ctlUser: function() { return this._ctlUser; }, set_ctlUser: function(v) { if (this._ctlUser !== v) this._ctlUser = v; },

    initialize: function() {
        for (var i = 0; i < 2; i++) {
            var rad = $get(String.format("{0}_{1}", "ctlForgotChoice_ctl02_rad", i));
            if (rad) $addHandler(rad, "click", Function.createDelegate(this, this.changedSelection));
            rad = null;
        }
        this.changedSelection();
    },

    dispose: function() {
        if (this.isDisposed) return;
        //		this._pnlDBCheck = null;
        //		this._pnlCheckingDatabase = null;
        //		this._pnlNoDatabase = null;
        //		this._pnlDatabaseOK = null;
        this._pnlLogin = null;
        this.isDisposed = true;
    },

    changedSelection: function() {
        this.getSelectedItem();
        $R_FN.showElement(document.getElementById("ctlUsername"), (this._selectedType == 0));
        $R_FN.showElement(document.getElementById("ctlUserEmail"), (this._selectedType == 1));
        $R_FN.showElement(document.getElementById("ctlActiveClient"), (this._selectedType == 1));
    },
    getSelectedItem: function() {
        for (var i = 0; i < 2; i++) {
            var rad = $get(String.format("{0}_{1}", "ctlForgotChoice_ctl02_rad", i));
            if (rad) {
                if (rad.checked) {
                    this._selectedType = Number.parseInvariant(rad.value.toString());
                    return;
                }
            }
        }
    },

    doReboundClientChoice: function(intClientID, strRedirectPath) {
        Rebound.GlobalTrader.Site.WebServices.DoReboundClientChoice(intClientID, strRedirectPath, Function.createDelegate(this, this.doReboundClientChoiceOK));
    },

    doReboundClientChoiceOK: function(str) {
        if (str.length > 0) location.href = str;
    }

};

Rebound.GlobalTrader.Site.Pages.ForgotPassword.registerClass("Rebound.GlobalTrader.Site.Pages.ForgotPassword", null, Sys.IDisposable);
