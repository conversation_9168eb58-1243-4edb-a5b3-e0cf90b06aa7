﻿///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />

Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.EditEpoBulk = function (element) {
    Rebound.GlobalTrader.Site.Controls.Forms.EditEpoBulk.initializeBase(this, [element]);
    this._ID = "";
    this._availableID = "";
    this._offerAddFlag = false;
};

Rebound.GlobalTrader.Site.Controls.Forms.EditEpoBulk.prototype = {

    initialize: function () {
        Rebound.GlobalTrader.Site.Controls.Forms.EditEpoBulk.callBaseMethod(this, "initialize");
        this.addShown(Function.createDelegate(this, this.formShown));
    },
    formShown: function () {
        if (this._blnFirstTimeShown) {
            this.addSave(Function.createDelegate(this, this.saveClicked));
        }
        this.checkWarningStock();
        $('input[type=radio][name=BulkEditOptions]').change(function () {
            $('.remove-warning').css("display", (this.value == 'Remove' && $('#offerAddFlag').val() == 'true') ? '' : 'none');
        });
    },

    dispose: function () {
        if (this.isDisposed) return;
        this._ID = null;
        this._availableID = null;
        this._offerAddFlag = null;
        Rebound.GlobalTrader.Site.Controls.Forms.EditEpoBulk.callBaseMethod(this, "dispose");
    },

    saveClicked: function () {
        var bulkOption = $("[name='BulkEditOptions'][type='radio']:checked").val();
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("Controls/Nuggets/POHubSourcing");
        obj.set_DataObject("POHubSourcing");
        obj.set_DataAction("EditEpoBulk");
        obj.addParameter("EpoIDs", bulkOption == 'Remove' ? this._availableID : this._ID);
        obj.addParameter("Option", bulkOption);
        obj.addDataOK(Function.createDelegate(this, this.saveEditComplete));
        obj.addError(Function.createDelegate(this, this.saveEditError));
        obj.addTimeout(Function.createDelegate(this, this.saveEditError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    saveEditError: function (args) {
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    saveEditComplete: function (args) {
        if (args._result.Result == true) {
            this.onSaveComplete();
        } else {
            this._strErrorMessage = args._errorMessage;
            this.onSaveError();
        }
    },
    checkWarningStock: function () {
        $('#offerAddFlag').val(this._offerAddFlag);
        var bulkOption = $("[name='BulkEditOptions'][type='radio']:checked").val();
        $('.remove-warning').css("display", (bulkOption == 'Remove' && this._offerAddFlag == true) ? '' : 'none');
    }
};

Rebound.GlobalTrader.Site.Controls.Forms.EditEpoBulk.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.EditEpoBulk", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
