//--------------------------------------------------------------------------------------------------------------
// RP 15.02.2010:
// - add tooltip for search type button
//--------------------------------------------------------------------------------------------------------------
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Collections;
using Rebound.GlobalTrader.Site.Controls.Nuggets;
using Rebound.GlobalTrader.Site.Enumerations;

namespace Rebound.GlobalTrader.Site.Controls.FilterDataItemRows {
	[DefaultProperty("")]
	[ToolboxData("<{0}:TextBox runat=server></{0}:TextBox>")]
	public class TextBox : Base {

		#region Locals

		protected ReboundTextBox _txt;
		protected HyperLink _hypSearchType;

		#endregion

		#region Properties

		private Unit _untTextBoxWidth = Unit.Pixel(120);
		public Unit TextBoxWidth {
			get { return _untTextBoxWidth; }
			set { _untTextBoxWidth = value; }
		}

		private TextFilterSearchType _enmInitialSearchType = TextFilterSearchType.StartsWith;
		public TextFilterSearchType InitialSearchType {
			get { return _enmInitialSearchType; }
			set { _enmInitialSearchType = value; }
		}

		#endregion

		#region Overrides

		/// <summary>
		/// OnInit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			FieldType = Type.TextBox;
			base.OnInit(e);
			AddScriptReference(Functions.GetScriptReference(_blnConfigurationIsDebug, "Rebound.GlobalTrader.Site", "Controls.FilterDataItemRows.TextBox.TextBox.js", true));
		}

		/// <summary>
		/// OnLoad
		/// </summary>
		/// <param name="e"></param>
		protected override void OnLoad(EventArgs e) {
			EnsureChildControls();
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.TextBox", ClientID);
			base.OnLoad(e);
		}

		protected override void OnPreRender(EventArgs e) {
			_scScriptControlDescriptor.AddElementProperty("txt", _txt.ClientID);
			_scScriptControlDescriptor.AddElementProperty("hypSearchType", _hypSearchType.ClientID);
			_scScriptControlDescriptor.AddProperty("enmSearchType", (int)_enmInitialSearchType);
			_scScriptControlDescriptor.AddProperty("strSearchType_StartsWith", Functions.GetGlobalResource("Misc", string.Format("DLNFilter_{0}", TextFilterSearchType.StartsWith.ToString())));
			_scScriptControlDescriptor.AddProperty("strSearchType_EndsWith", Functions.GetGlobalResource("Misc", string.Format("DLNFilter_{0}", TextFilterSearchType.EndsWith.ToString())));
			_scScriptControlDescriptor.AddProperty("strSearchType_Contains", Functions.GetGlobalResource("Misc", string.Format("DLNFilter_{0}", TextFilterSearchType.Contains.ToString())));
			base.OnPreRender(e);
		}
		/// <summary>
		/// create controls
		/// </summary>
		protected override void CreateChildControls() {
			base.CreateChildControls();

			//add search type button
			_hypSearchType = ControlBuilders.CreateHyperLink();
			ControlBuilders.CreateImageInsideParent(_hypSearchType, "", "~/images/x.gif", 25, 16);
			_lblField.Controls.Add(_hypSearchType);
			_hypSearchType.NavigateUrl = "javascript:void(0);";
			SetInitialSearchType(_enmInitialSearchType);

			//text box
			_txt = new ReboundTextBox();
			_txt.ID = "txt";
			_txt.Width = _untTextBoxWidth;
			if (ForLeftNugget) _txt.Width = Unit.Pixel(153);
			_lblField.Controls.Add(_txt);
		}

		public override void SetDefaultValue() {
			SetInitialValue((DefaultValue ?? "").Replace("%", ""));
			base.SetDefaultValue();
		}

		public override void Reset() {
			EnsureChildControls();
			_txt.Text = "";
			Enable(false);
			base.Reset();
		}

		#endregion

		public void SetInitialValue(string strValue) {
			strValue = HttpUtility.UrlDecode(strValue.Trim());
			if (String.IsNullOrEmpty(strValue)) return;
			EnsureChildControls();
			if (strValue == "%" || strValue == "%%" || strValue == "%%%") {
				strValue = "";
			} else if (strValue.StartsWith("%") && strValue.EndsWith("%")) {
				strValue = strValue.TrimStart(new char[] { '%' }).TrimEnd(new char[] { '%' });
				SetInitialSearchType(TextFilterSearchType.Contains);
			} else if (strValue.StartsWith("%")) {
				strValue = strValue.TrimStart(new char[] { '%' });
				SetInitialSearchType(TextFilterSearchType.EndsWith);
			} else if (strValue.EndsWith("%")) {
				strValue = strValue.TrimEnd(new char[] { '%' });
				SetInitialSearchType(TextFilterSearchType.StartsWith);
			}
			_txt.Text = strValue;
			if (!string.IsNullOrEmpty(_txt.Text)) Enable(true);
		}

		public void SetInitialSearchType(TextFilterSearchType enmSearchType) {
			_enmInitialSearchType = enmSearchType;
			_hypSearchType.CssClass = string.Format("searchType_{0}", _enmInitialSearchType.ToString());
			_hypSearchType.ToolTip = Functions.GetGlobalResource("Misc", string.Format("DLNFilter_{0}", _enmInitialSearchType.ToString()));
		}

	}

}