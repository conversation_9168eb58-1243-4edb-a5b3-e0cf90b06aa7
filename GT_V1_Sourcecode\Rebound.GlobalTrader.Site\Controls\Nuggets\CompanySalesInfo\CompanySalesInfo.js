Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");Rebound.GlobalTrader.Site.Controls.Nuggets.CompanySalesInfo=function(n){Rebound.GlobalTrader.Site.Controls.Nuggets.CompanySalesInfo.initializeBase(this,[n]);this._intCompanyID=-1;this._warehouseNo=-1};Rebound.GlobalTrader.Site.Controls.Nuggets.CompanySalesInfo.prototype={get_intCompanyID:function(){return this._intCompanyID},set_intCompanyID:function(n){this._intCompanyID!==n&&(this._intCompanyID=n)},get_ibtnEdit:function(){return this._ibtnEdit},set_ibtnEdit:function(n){this._ibtnEdit!==n&&(this._ibtnEdit=n)},get_ibtnAdd:function(){return this._ibtnAdd},set_ibtnAdd:function(n){this._ibtnAdd!==n&&(this._ibtnAdd=n)},get_tblOpenSOs:function(){return this._tblOpenSOs},set_tblOpenSOs:function(n){this._tblOpenSOs!==n&&(this._tblOpenSOs=n)},get_pnlOpenSOs:function(){return this._pnlOpenSOs},set_pnlOpenSOs:function(n){this._pnlOpenSOs!==n&&(this._pnlOpenSOs=n)},get_pnlGetOpenSOs:function(){return this._pnlGetOpenSOs},set_pnlGetOpenSOs:function(n){this._pnlGetOpenSOs!==n&&(this._pnlGetOpenSOs=n)},get_hypGetOpenSOs:function(){return this._hypGetOpenSOs},set_hypGetOpenSOs:function(n){this._hypGetOpenSOs!==n&&(this._hypGetOpenSOs=n)},get_pnlLoadingOpenSOs:function(){return this._pnlLoadingOpenSOs},set_pnlLoadingOpenSOs:function(n){this._pnlLoadingOpenSOs!==n&&(this._pnlLoadingOpenSOs=n)},get_pnlOpenSOsError:function(){return this._pnlOpenSOsError},set_pnlOpenSOsError:function(n){this._pnlOpenSOsError!==n&&(this._pnlOpenSOsError=n)},get_tblOverdueSOs:function(){return this._tblOverdueSOs},set_tblOverdueSOs:function(n){this._tblOverdueSOs!==n&&(this._tblOverdueSOs=n)},get_pnlOverdueSOs:function(){return this._pnlOverdueSOs},set_pnlOverdueSOs:function(n){this._pnlOverdueSOs!==n&&(this._pnlOverdueSOs=n)},get_pnlLoadingOverdueSOs:function(){return this._pnlLoadingOverdueSOs},set_pnlLoadingOverdueSOs:function(n){this._pnlLoadingOverdueSOs!==n&&(this._pnlLoadingOverdueSOs=n)},get_pnlOverdueSOsError:function(){return this._pnlOverdueSOsError},set_pnlOverdueSOsError:function(n){this._pnlOverdueSOsError!==n&&(this._pnlOverdueSOsError=n)},get_pnlGetOverdueSOs:function(){return this._pnlGetOverdueSOs},set_pnlGetOverdueSOs:function(n){this._pnlGetOverdueSOs!==n&&(this._pnlGetOverdueSOs=n)},get_hypGetOverdueSOs:function(){return this._hypGetOverdueSOs},set_hypGetOverdueSOs:function(n){this._hypGetOverdueSOs!==n&&(this._hypGetOverdueSOs=n)},get_tblCreditHistory:function(){return this._tblCreditHistory},set_tblCreditHistory:function(n){this._tblCreditHistory!==n&&(this._tblCreditHistory=n)},get_pnlCreditHistory:function(){return this._pnlCreditHistory},set_pnlCreditHistory:function(n){this._pnlCreditHistory!==n&&(this._pnlCreditHistory=n)},get_pnlLoadingCreditHistory:function(){return this._pnlLoadingCreditHistory},set_pnlLoadingCreditHistory:function(n){this._pnlLoadingCreditHistory!==n&&(this._pnlLoadingCreditHistory=n)},get_pnlCreditHistoryError:function(){return this._pnlCreditHistoryError},set_pnlCreditHistoryError:function(n){this._pnlCreditHistoryError!==n&&(this._pnlCreditHistoryError=n)},get_pnlGetCreditHistory:function(){return this._pnlGetCreditHistory},set_pnlGetCreditHistory:function(n){this._pnlGetCreditHistory!==n&&(this._pnlGetCreditHistory=n)},get_hypGetCreditHistory:function(){return this._hypGetCreditHistory},set_hypGetCreditHistory:function(n){this._hypGetCreditHistory!==n&&(this._hypGetCreditHistory=n)},get_tblInsuranceHistory:function(){return this._tblInsuranceHistory},set_tblInsuranceHistory:function(n){this._tblInsuranceHistory!==n&&(this._tblInsuranceHistory=n)},get_pnlInsuranceHistory:function(){return this._pnlInsuranceHistory},set_pnlInsuranceHistory:function(n){this._pnlInsuranceHistory!==n&&(this._pnlInsuranceHistory=n)},get_pnlLoadingInsuranceHistory:function(){return this._pnlLoadingInsuranceHistory},set_pnlLoadingInsuranceHistory:function(n){this._pnlLoadingInsuranceHistory!==n&&(this._pnlLoadingInsuranceHistory=n)},get_pnlInsuranceHistoryError:function(){return this._pnlInsuranceHistoryError},set_pnlInsuranceHistoryError:function(n){this._pnlInsuranceHistoryError!==n&&(this._pnlInsuranceHistoryError=n)},get_pnlGetInsuranceHistory:function(){return this._pnlGetInsuranceHistory},set_pnlGetInsuranceHistory:function(n){this._pnlGetInsuranceHistory!==n&&(this._pnlGetInsuranceHistory=n)},get_hypGetInsuranceHistory:function(){return this._hypGetInsuranceHistory},set_hypGetInsuranceHistory:function(n){this._hypGetInsuranceHistory!==n&&(this._hypGetInsuranceHistory=n)},get_Status:function(){return this._Status},set_Status:function(n){this._Status!==n&&(this._Status=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Nuggets.CompanySalesInfo.callBaseMethod(this,"initialize");this._strPathToData="controls/Nuggets/CompanySalesInfo";this._strDataObject="CompanySalesInfo";this.addRefreshEvent(Function.createDelegate(this,this.getData));this._fldYearToDate=this.getEllipsesControl("ctlYearToDateNew");this._fldYearToDate.addSetupData(Function.createDelegate(this,this.getYearToDate));this._fldLastYear=this.getEllipsesControl("ctlLastYearNew");this._fldLastYear.addSetupData(Function.createDelegate(this,this.getLastYear));this._ibtnEdit&&($R_IBTN.addClick(this._ibtnEdit,Function.createDelegate(this,this.showEditForm)),this._frmEdit=$find(this._aryFormIDs[0]),this._frmEdit.addCancel(Function.createDelegate(this,this.cancelEdit)),this._frmEdit.addSaveComplete(Function.createDelegate(this,this.saveEditComplete)),this._frmEdit.addSaveError(Function.createDelegate(this,this.saveEditError)));$addHandler(this._hypGetOpenSOs,"click",Function.createDelegate(this,this.getOpenSOs));$addHandler(this._hypGetOverdueSOs,"click",Function.createDelegate(this,this.getOverdueSOs));$addHandler(this._hypGetCreditHistory,"click",Function.createDelegate(this,this.getCreditHistory));$addHandler(this._hypGetInsuranceHistory,"click",Function.createDelegate(this,this.getInsuranceHistory))},dispose:function(){this.isDisposed||(this._hypGetOpenSOs&&$clearHandlers(this._hypGetOpenSOs),this._hypGetOverdueSOs&&$clearHandlers(this._hypGetOverdueSOs),this._hypGetCreditHistory&&$clearHandlers(this._hypGetCreditHistory),this._ibtnEdit&&$R_IBTN.clearHandlers(this._ibtnEdit),this._frmEdit&&this._frmEdit.dispose(),this._tblOpenSOs&&this._tblOpenSOs.dispose(),this._tblOverdueSOs&&this._tblOverdueSOs.dispose(),this._tblCreditHistory&&this._tblCreditHistory.dispose(),this._intCompanyID=null,this._ibtnEdit=null,this._ibtnAdd=null,this._tblOpenSOs=null,this._pnlOpenSOs=null,this._pnlGetOpenSOs=null,this._hypGetOpenSOs=null,this._pnlLoadingOpenSOs=null,this._pnlOpenSOsError=null,this._tblOverdueSOs=null,this._pnlOverdueSOs=null,this._pnlLoadingOverdueSOs=null,this._pnlOverdueSOsError=null,this._pnlGetOverdueSOs=null,this._hypGetOverdueSOs=null,this._tblCreditHistory=null,this._pnlCreditHistory=null,this._pnlLoadingCreditHistory=null,this._pnlCreditHistoryError=null,this._pnlGetCreditHistory=null,this._hypGetCreditHistory=null,this._tblInsuranceHistory=null,this._pnlInsuranceHistory=null,this._pnlLoadingInsuranceHistory=null,this._pnlInsuranceHistoryError=null,this._pnlGetInsuranceHistory=null,this._hypGetInsuranceHistory=null,Rebound.GlobalTrader.Site.Controls.Nuggets.CompanySalesInfo.callBaseMethod(this,"dispose"))},getData:function(){this.getData_Start();this.showOpenSOsGetData(!0);this.showOpenSOsError(!1);this.showOverdueSOsGetData(!0);this.showOverdueSOsError(!1);this.showCreditHistoryGetData(!0);this.showCreditHistoryError(!1);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("GetData");n.addParameter("id",this._intCompanyID);n.addDataOK(Function.createDelegate(this,this.getDataOK));n.addError(Function.createDelegate(this,this.getDataError));n.addTimeout(Function.createDelegate(this,this.getDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();this.getOpenSOs();this.getOverdueSOs();this.getCreditHistory();this.getInsuranceHistory();n=null},getDataOK:function(n){var t=n._result;t&&(this.setFieldValue("ctlSalesperson",t.Salesman),this.setFieldValue("hidSalespersonNo",t.SalesmanNo),this.setFieldValue("ctlIsApproved",$R_FN.getApprovedByAndDate(t.IsApproved,t.ApprovedByAndDate)),this.setFieldValue("hidIsApproved",t.IsApproved),this.setFieldValue("ctlCurrency",t.Currency),this.setFieldValue("hidCurrencyNo",t.CurrencyNo),this.setFieldValue("hidCurrencyCode",t.CurrencyCode),this.setFieldValue("ctlCustomerNo",t.CustomerNo),this.setFieldValue("ctlTerms",t.Terms),this.setFieldValue("hidTermsNo",t.TermsNo),this.setFieldValue("hidTaxNo",t.TaxNo),this.setFieldValue("ctlRating",t.Rating),this.setFieldValue("ctlIsOnStop",t.OnStop),this.setFieldValue("ctlIsShippingWaived",t.IsShippingWaived),this.setFieldValue("ctlShipVia",t.ShipVia),this.setFieldValue("hidShipViaNo",t.ShipViaNo),this.setFieldValue("ctlShippingAccountNo",t.ShippingAccountNo),this.setFieldValue("hidContactNo",t.ContactNo),this.setFieldValue("ctlContact",$RGT_nubButton_Contact(t.ContactNo,t.ContactName)),this.setFieldValue("ctlCreditLimit",t.CreditLimit),this.setFieldValue("hidCreditLimit",t.CreditLimitRaw),this.setFieldValue("ctlYearToDate",t.YearToDate),this.setFieldValue("ctlLastYear",t.LastYear),this.setFieldValue("ctlBalance",t.Balance),this.setFieldValue("ctlCurrent",t.Current),this.setFieldValue("ctlDays30",t.Days30),this.setFieldValue("ctlDays60",t.Days60),this.setFieldValue("ctlDays90",t.Days90),this.setFieldValue("ctlDays120",t.Days120),this.setFieldValue("ctlYearToDate",t.ThisYearValue),this.setFieldValue("ctlLastYear",t.LastYearValue),this.setFieldValue("ctlBalanceWithOpenOrders",t.BalanceWithOpenSalesOrders),this.setFieldValue("ctlInvoiceNotExport",t.InvoiceNotExport),this.setFieldValue("ctlInsuranceFileNo",t.InsuranceFileNo),this.setFieldValue("ctlInsuredAmount",t.InsuredAmount),this.setFieldValue("hidInsuredAmount",t.InsuredAmountRaw),this.setFieldValue("ctlStopStatus",t.StopStatus),this.setFieldValue("ctlNotesToInvoice",$R_FN.setCleanTextValue(t.NotesToInvoice)),this.setFieldValue("ctlCreditLimit2",t.ActualCreditLimit),this.setFieldValue("hidActCreditLimit",t.ActualCreditLimitRaw),this.setFieldValue("ctlDays1",t.Days1),this.setFieldValue("ctlPreferredWarehouse",t.WarehouseName),this._warehouseNo=t.WarehouseNo,this.setFieldValue("hidInsuredAmountCurrencyNo",t.InsuredAmountCurrencyNo),this._ibtnAdd&&$R_IBTN.enableButton(this._ibtnAdd,!t.Inactive),this._ibtnEdit&&$R_IBTN.enableButton(this._ibtnEdit,!t.Inactive),this.setDLUP(t.DLUP),this.getDataOK_End())},getDataError:function(n){this.showError(!0,n.get_ErrorMessage())},getOpenSOs:function(){this.showLoadingOpenSOs(!0);this.showOpenSOsError(!1);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("GetOpenSOs");n.addParameter("id",this._intCompanyID);n.addDataOK(Function.createDelegate(this,this.getOpenSOsOK));n.addError(Function.createDelegate(this,this.getOpenSOsError));n.addTimeout(Function.createDelegate(this,this.getOpenSOsError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getOpenSOsOK:function(n){(res=n._result,res)&&(this.showLoadingOpenSOs(!1),this.showOpenSOsError(!1),this._tblOpenSOs.clearTable(),this.processSOList(this._tblOpenSOs),this._tblOpenSOs.resizeColumns())},getOpenSOsError:function(n){this.showLoadingOpenSOs(!1);this.showOpenSOsError(!0,n.get_ErrorMessage())},showLoadingOpenSOs:function(n){$R_FN.showElement(this._pnlLoadingOpenSOs,n);$R_FN.showElement(this._pnlOpenSOs,!n);this.showLoading(n);n&&$R_FN.showElement(this._pnlGetOpenSOs,!1)},showOpenSOsError:function(n,t){$R_FN.showElement(this._pnlOpenSOsError,n);n&&(this.showLoading(!1),$R_FN.showElement(this._pnlOpenSOs,!1),$R_FN.showElement(this._pnlGetOpenSOs,!1),$R_FN.setInnerHTML(this._pnlOpenSOsError,t))},showOpenSOsGetData:function(n){$R_FN.showElement(this._pnlGetOpenSOs,n);n&&(this.showLoading(!1),$R_FN.showElement(this._pnlLoadingOpenSOs,!1),$R_FN.showElement(this._pnlOpenSOs,!1),$R_FN.setInnerHTML(this._pnlOpenSOsError,!1))},processSOList:function(n){var i,t,r;if(res.Items)for(i=0;i<res.Items.length;i++)t=res.Items[i],r=[$RGT_nubButton_SalesOrder(t.ID,t.No),t.Date,t.Amount],n.addRow(r,t.ID,!1),t=null,r=null},getOverdueSOs:function(){this.showLoadingOverdueSOs(!0);this.showOverdueSOsError(!1);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("GetOverdueSOs");n.addParameter("id",this._intCompanyID);n.addDataOK(Function.createDelegate(this,this.getOverdueSOsOK));n.addError(Function.createDelegate(this,this.getOverdueSOsError));n.addTimeout(Function.createDelegate(this,this.getOverdueSOsError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getOverdueSOsOK:function(n){(res=n._result,res)&&(this.showLoadingOverdueSOs(!1),this.showOverdueSOsError(!1),this._tblOverdueSOs.clearTable(),this.processSOList(this._tblOverdueSOs),this._tblOverdueSOs.resizeColumns())},getOverdueSOsError:function(n){this.showLoadingOverdueSOs(!1);this.showOverdueSOsError(!0,n.get_ErrorMessage())},showLoadingOverdueSOs:function(n){$R_FN.showElement(this._pnlLoadingOverdueSOs,n);$R_FN.showElement(this._pnlOverdueSOs,!n);this.showLoading(n);n&&$R_FN.showElement(this._pnlGetOverdueSOs,!1)},showOverdueSOsError:function(n,t){$R_FN.showElement(this._pnlOverdueSOsError,n);n&&(this.showLoading(!1),$R_FN.showElement(this._pnlOverdueSOs,!1),$R_FN.showElement(this._pnlGetOverdueSOs,!1),$R_FN.setInnerHTML(this._pnlOverdueSOsError,t))},showOverdueSOsGetData:function(n){$R_FN.showElement(this._pnlGetOverdueSOs,n);n&&(this.showLoading(!1),$R_FN.showElement(this._pnlLoadingOverdueSOs,!1),$R_FN.showElement(this._pnlOverdueSOs,!1),$R_FN.setInnerHTML(this._pnlOverdueSOsError,!1))},getCreditHistory:function(){this.showLoadingCreditHistory(!0);this.showCreditHistoryError(!1);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("GetCreditHistory");n.addParameter("id",this._intCompanyID);n.addDataOK(Function.createDelegate(this,this.getCreditHistoryOK));n.addError(Function.createDelegate(this,this.getCreditHistoryError));n.addTimeout(Function.createDelegate(this,this.getCreditHistoryError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getCreditHistoryOK:function(n){(res=n._result,res)&&(this.showLoadingCreditHistory(!1),this.showCreditHistoryError(!1),this._tblCreditHistory.clearTable(),this.processCreditHistory(this._tblCreditHistory),this._tblCreditHistory.resizeColumns())},getCreditHistoryError:function(n){this.showLoadingCreditHistory(!1);this.showCreditHistoryError(!0,n.get_ErrorMessage())},showLoadingCreditHistory:function(n){$R_FN.showElement(this._pnlLoadingCreditHistory,n);$R_FN.showElement(this._pnlCreditHistory,!n);this.showLoading(n);n&&$R_FN.showElement(this._pnlGetCreditHistory,!1)},showCreditHistoryError:function(n,t){$R_FN.showElement(this._pnlCreditHistoryError,n);n&&(this.showLoading(!1),$R_FN.showElement(this._pnlCreditHistory,!1),$R_FN.showElement(this._pnlGetCreditHistory,!1),$R_FN.setInnerHTML(this._pnlCreditHistoryError,t))},showCreditHistoryGetData:function(n){$R_FN.showElement(this._pnlGetCreditHistory,n);n&&(this.showLoading(!1),$R_FN.showElement(this._pnlLoadingCreditHistory,!1),$R_FN.showElement(this._pnlCreditHistory,!1),$R_FN.setInnerHTML(this._pnlCreditHistoryError,!1))},processCreditHistory:function(n){var i,t,r;if(res.CreditHist)for(i=0;i<res.CreditHist.length;i++)t=res.CreditHist[i],r=[t.From,t.To,t.Date+" "+t.Time,t.By],n.addRow(r,t.ID,!1),t=null,r=null},getInsuranceHistory:function(){this.showLoadingInsuranceHistory(!0);this.showInsuranceHistoryError(!1);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("GetInsuranceHistory");n.addParameter("id",this._intCompanyID);n.addDataOK(Function.createDelegate(this,this.getInsuranceHistoryOK));n.addError(Function.createDelegate(this,this.getInsuranceHistoryError));n.addTimeout(Function.createDelegate(this,this.getInsuranceHistoryError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getInsuranceHistoryOK:function(n){(res=n._result,res)&&(this.showLoadingInsuranceHistory(!1),this.showInsuranceHistoryError(!1),this._tblInsuranceHistory.clearTable(),this.processInsuranceHistory(this._tblInsuranceHistory),this._tblInsuranceHistory.resizeColumns())},getInsuranceHistoryError:function(n){this.showLoadingInsuranceHistory(!1);this.showInsuranceHistoryError(!0,n.get_ErrorMessage())},showLoadingInsuranceHistory:function(n){$R_FN.showElement(this._pnlLoadingInsuranceHistory,n);$R_FN.showElement(this._pnlInsuranceHistory,!n);this.showLoading(n);n&&$R_FN.showElement(this._pnlGetInsuranceHistory,!1)},showInsuranceHistoryError:function(n,t){$R_FN.showElement(this._pnlInsuranceHistoryError,n);n&&(this.showLoading(!1),$R_FN.showElement(this._pnlInsuranceHistory,!1),$R_FN.showElement(this._pnlGetInsuranceHistory,!1),$R_FN.setInnerHTML(this._pnlInsuranceHistoryError,t))},showInsuranceHistoryGetData:function(n){$R_FN.showElement(this._pnlGetInsuranceHistory,n);n&&(this.showLoading(!1),$R_FN.showElement(this._pnlLoadingInsuranceHistory,!1),$R_FN.showElement(this._pnlInsuranceHistory,!1),$R_FN.setInnerHTML(this._pnlInsuranceHistoryError,!1))},processInsuranceHistory:function(n){var i,t,r;if(res.InsuranceHist)for(i=0;i<res.InsuranceHist.length;i++)t=res.InsuranceHist[i],r=[t.From,t.To,t.Date+" "+t.Time,t.By],n.addRow(r,t.ID,!1),t=null,r=null},showEditForm:function(){this._frmEdit._globalLoginClientNo=this._globalLoginClientNo;$R_FN.showElement(this._ibtnEdit,!1);var n=this._intCompanyID;this._frmEdit.getFieldComponent("ctlContact")._intCompanyID=n;this._frmEdit.setFieldValue("ctlSalesperson",this.getFieldValue("hidSalespersonNo"));this._frmEdit.setFieldValue("ctlIsApproved",Boolean.parse(this.getFieldValue("hidIsApproved")));this._frmEdit.setFieldValue("ctlCurrency",this.getFieldValue("hidCurrencyNo"));this._frmEdit.setFieldValue("ctlCustomerNo",this.getFieldValue("ctlCustomerNo"));this._frmEdit.setFieldValue("ctlTerms",this.getFieldValue("hidTermsNo"));this._frmEdit.setFieldValue("ctlRating",this.getFieldValue("ctlRating"));this._frmEdit.setFieldValue("ctlIsOnStop",this.getFieldValue("ctlIsOnStop"));this._frmEdit.setFieldValue("ctlIsShippingWaived",this.getFieldValue("ctlIsShippingWaived"));this._frmEdit.setFieldValue("ctlShipVia",this.getFieldValue("hidShipViaNo"));this._frmEdit.setFieldValue("ctlShippingAccountNo",this.getFieldValue("ctlShippingAccountNo"));this._frmEdit.setFieldValue("ctlContact",this.getFieldValue("hidContactNo"));this._frmEdit.setFieldValue("ctlCreditLimit",this.getFieldValue("hidCreditLimit"));this._frmEdit.setCurrencyLabel(this.getFieldValue("hidCurrencyCode"));this._frmEdit.setFieldValue("ctlInsuranceFileNo",this.getFieldValue("ctlInsuranceFileNo"));this._frmEdit.setFieldValue("ctlInsuredAmount",this.getFieldValue("hidInsuredAmount"));this._frmEdit.setFieldValue("ctlStopStatus",this.getFieldValue("ctlStopStatus"));this._frmEdit.showField("ctlStopStatus",this._Status);this.showForm(this._frmEdit,!0);this._frmEdit.style="left:10%;top:10%";this._frmEdit.setFieldValue("ctlNotesToInvoice",this.getFieldValue("ctlNotesToInvoice"));this._frmEdit.setFieldValue("ctlActualCreditLimit",this.getFieldValue("hidActCreditLimit"));$R_FN.showElement(this._ibtnEdit,!0);this._frmEdit.setFieldValue("ctlPreferredWarehouse",this._warehouseNo);this._frmEdit.setFieldValue("ctlInsuredAmountCurrency",this.getFieldValue("hidInsuredAmountCurrencyNo"))},cancelEdit:function(){this.showContent(!0);this.showForm(this._frmEdit,!1)},saveEditComplete:function(){this.showForm(this._frmEdit,!1);this.getData();this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully);this.onSaveEditComplete()},saveEditError:function(){this.showForm(this._frmEdit,!1);this.showError(!0,this._frmEdit._strErrorMessage)},getYearToDate:function(){var n=this._fldYearToDate._objData;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("GetYearToDateNew");n.addParameter("id",this._intCompanyID)},getLastYear:function(){var n=this._fldLastYear._objData;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("GetLastYearNew");n.addParameter("id",this._intCompanyID)}};Rebound.GlobalTrader.Site.Controls.Nuggets.CompanySalesInfo.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.CompanySalesInfo",Rebound.GlobalTrader.Site.Controls.Nuggets.Base);