Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DataListNuggets");Rebound.GlobalTrader.Site.Controls.DataListNuggets.NPR=function(n){Rebound.GlobalTrader.Site.Controls.DataListNuggets.NPR.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DataListNuggets.NPR.prototype={initialize:function(){this.addGetDataOKEvent(Function.createDelegate(this,this.getDataOK));this.addInitCompleteEvent(Function.createDelegate(this,this.initAfterBaseIsReady));this._strPathToData="controls/DataListNuggets/NPR";this._strDataObject="NPR";Rebound.GlobalTrader.Site.Controls.DataListNuggets.NPR.callBaseMethod(this,"initialize")},initAfterBaseIsReady:function(){this.getData()},dispose:function(){this.isDisposed||Rebound.GlobalTrader.Site.Controls.DataListNuggets.NPR.callBaseMethod(this,"dispose")},getDataOK:function(){for(var n,i,t=0,r=this._objResult.Results.length;t<r;t++)n=this._objResult.Results[t],i=[$R_FN.writeDoubleCellValue($RGT_nubButton_NPRNugget(n.ID,n.NprNo,n.GILIneNo),$RGT_nubButton_PurchaseOrder(n.POID,n.PONo)),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(n.Part),$R_FN.setCleanTextValue(n.Location)),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(n.Quantity),$R_FN.setCleanTextValue(n.UPrice)),$RGT_nubButton_Company(n.CMNo,n.CM),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(n.AuthBy),$R_FN.setCleanTextValue(n.CompBy)),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(n.NprDate),$R_FN.setCleanTextValue(n.Actn))],this._table.addRow(i,n.ID,!1),i=null,n=null}};Rebound.GlobalTrader.Site.Controls.DataListNuggets.NPR.registerClass("Rebound.GlobalTrader.Site.Controls.DataListNuggets.NPR",Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base,Sys.IDisposable);