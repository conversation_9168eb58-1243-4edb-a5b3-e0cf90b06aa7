Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.ItemSearch");Rebound.GlobalTrader.Site.Controls.ItemSearch.IhsSearch=function(n){Rebound.GlobalTrader.Site.Controls.ItemSearch.IhsSearch.initializeBase(this,[n]);this._strpart="";this._searchType=""};Rebound.GlobalTrader.Site.Controls.ItemSearch.IhsSearch.prototype={addPotentialStatusChange:function(n){this.get_events().addHandler("PotentialStatusChange",n)},removePotentialStatusChange:function(n){this.get_events().removeHandler("PotentialStatusChange",n)},onPotentialStatusChange:function(){var n=this.get_events().getHandler("PotentialStatusChange");n&&n(this,Sys.EventArgs.Empty)},initialize:function(){Rebound.GlobalTrader.Site.Controls.ItemSearch.IhsSearch.callBaseMethod(this,"initialize");this.addSetupData(Function.createDelegate(this,this.doSetupData));this.addGetDataComplete(Function.createDelegate(this,this.doGetDataComplete));this.addSearched(Function.createDelegate(this,this.doSearched))},dispose:function(){this.isDisposed||(this._strpart=null,this._searchType=null,Rebound.GlobalTrader.Site.Controls.ItemSearch.IhsSearch.callBaseMethod(this,"dispose"))},doSetupData:function(){this._objData.set_PathToData("controls/ItemSearch/IhsSearch");this._objData.set_DataObject("IhsSearch");this._objData.set_DataAction("GetData");this._objData.addParameter("partsearch",this._strpart);this._objData.addParameter("searchType",this._searchType)},getGroupValue:function(){},doGetDataComplete:function(){var t,i;for(this._invoiceExist=!1,t=0,i=this._objResult.Results.length;t<i;t++){var n=this._objResult.Results[t],r=[$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(n.ID),$R_FN.showBoldText($R_FN.setCleanTextValue(n.PartStatus))),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(n.Manufacturer),$R_FN.setCleanTextValue(n.ROHSName)),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(n.CountryOfOrigin),$R_FN.setCleanTextValue(n.HTSCode)),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(n.Packaging),$R_FN.setCleanTextValue(n.PackagingSize)),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(n.Descriptions),$R_FN.setCleanTextValue(n.ECCNCode)),],u={PartName:n.ID,ManufacturerNo:n.ManufacturerNo,Manufacturer:n.Manufacturer,Descriptions:n.Descriptions,ROHSNo:n.ROHSNo,ROHSName:n.ROHSName,CountryOfOrigin:n.CountryOfOrigin,CountryOfOriginNo:n.CountryOfOriginNo,LifeCycleStage:n.PartStatus,HTSCode:n.HTSCode,AveragePrice:n.AveragePrice,Packaging:n.Packaging,PackagingSize:n.PackagingSize,IHSPartsId:n.IHSPartsId,ResultType:n.ResultType,ihsCurrencyCode:n.ihsCurrencyCode,ProdDesc:n.ProdDesc,ProdNo:n.ProdNo,IHSProdDesc:n.IHSProdDesc,IHSDutyCode:n.IHSDutyCode,ManufacturerFullName:n.ManufacturerFullName,IHSProduct:n.IHSProduct,ECCNCode:n.ECCNCode};this._tblResults.addRow(r,n.Ids,!1,u);r=null;n=null}},doSearched:function(){this.onPotentialStatusChange()}};Rebound.GlobalTrader.Site.Controls.ItemSearch.IhsSearch.registerClass("Rebound.GlobalTrader.Site.Controls.ItemSearch.IhsSearch",Rebound.GlobalTrader.Site.Controls.ItemSearch.Base,Sys.IDisposable);