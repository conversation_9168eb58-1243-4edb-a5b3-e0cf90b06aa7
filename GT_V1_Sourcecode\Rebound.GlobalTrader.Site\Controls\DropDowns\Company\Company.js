Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.Company=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.Company.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DropDowns.Company.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.DropDowns.Company.callBaseMethod(this,"initialize");this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK))},dispose:function(){this.isDisposed||Rebound.GlobalTrader.Site.Controls.DropDowns.Company.callBaseMethod(this,"dispose")},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/Company");this._objData.set_DataObject("Company");this._objData.set_DataAction("GetData")},dataCallOK:function(){var t=this._objData._result,n;if(t.Companies)for(n=0;n<t.Companies.length;n++)this.addOption(t.Companies[n].Name,t.Companies[n].ID)}};Rebound.GlobalTrader.Site.Controls.DropDowns.Company.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.Company",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);