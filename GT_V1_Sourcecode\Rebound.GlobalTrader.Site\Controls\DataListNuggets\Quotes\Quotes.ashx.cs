/* Marker     changed by      date         Remarks
/* [0001]      A<PERSON><PERSON><PERSON>  14/07/2014  ESMS Ref:172 - Issue in Part Searching in whole GT 
/* [0002]      <PERSON><PERSON><PERSON>    1/08/2018   [REB-12517]: Filter on quotes marked as important on the quote screen
	[0003]      Ravi          19-09-2023      RP-2338  AS6081 Search/Filter functionality on different pages 
 */

using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site.Enumerations;

namespace Rebound.GlobalTrader.Site.Controls.DataListNuggets.Data
{

	public class Quotes : Base
	{

		/// <summary>
		/// Gets the main data
		/// </summary>
		protected override void GetData()
		{
			JsonObject jsn = new JsonObject();
			string AS6081 = GetFormValue_String("AS6081"); //[0003]
														   //check view level
			ViewLevelList enmViewLevel = (ViewLevelList)GetFormValue_Int("ViewLevel");
			//double? d = GetFormValue_NullableDouble("TotalHi");

			int? SelectedclientNo = null;
			int? SessionClientNo = SessionManager.ClientID;
			bool? blnMakeYellow = false;
			if (SessionManager.IsGSA == true && SessionManager.IsGlobalUser == false)
			{
				SelectedclientNo = GetFormValue_NullableInt("Client");
				if (SelectedclientNo != null)
				{
					blnMakeYellow = true;
				}
				else
				{
					blnMakeYellow = false;
				}

			}
			else
			{
				blnMakeYellow = false;
			}
			//get data	
			List<QuoteLine> lst = QuoteLine.DataListNugget(
				SessionManager.ClientID
				, (enmViewLevel == ViewLevelList.Team) ? (int?)SessionManager.LoginTeamID : null
				, (enmViewLevel == ViewLevelList.Division) ? (int?)SessionManager.LoginDivisionID : null
				, (enmViewLevel == ViewLevelList.My) ? (int?)LoginID : null
				, GetFormValue_NullableInt("SortIndex")
				, GetFormValue_NullableInt("SortDir", SortColumnDirection.ASC)
				, GetFormValue_NullableInt("PageIndex", 0)
				, GetFormValue_NullableInt("PageSize", 10)
				  //[0001] start code
				  //, GetFormValue_StringForPartSearch("Part")
				  , GetFormValue_PartForLikeSearch("Part")
				//[0001] end code
				//, GetFormValue_StringForNameSearch("Contact")
				, GetFormValue_StringForNameSearchDecode("Contact")
				//, GetFormValue_StringForNameSearch("CMName")
				, GetFormValue_StringForNameSearchDecode("CMName")
				, GetFormValue_NullableInt("Salesman")
				, GetFormValue_Boolean("IncludeClosed")
				, GetFormValue_NullableInt("QuoteNoLo")
				, GetFormValue_NullableInt("QuoteNoHi")
				, GetFormValue_NullableDateTime("DateQuotedFrom")
				, GetFormValue_NullableDateTime("DateQuotedTo")
				, GetFormValue_Boolean("RecentOnly")
				//[0002] start
				, GetFormValue_Boolean("Important")
				//[0002] end
				, GetFormValue_NullableDouble("TotalLo")
				, GetFormValue_NullableDouble("TotalHi")
				, GetFormValue_NullableInt("QStatus")
				, GetFormValue_NullableDouble("TotalProfitLo")
				, GetFormValue_NullableDouble("TotalProfitHi")
				, (AS6081 == "1" ? true : (AS6081 == "2" ? false : (bool?)null)) //[0003]
				, GetFormValue_NullableInt("Client")
				, SessionManager.LoginID
			);

			//check counts
			jsn.AddVariable("TotalRecords", (lst.Count > 0) ? lst[0].RowCnt : 0);

			//format data
			JsonObject jsnRowsArray = new JsonObject(true);
			for (int i = 0; i < lst.Count; i++)
			{
				JsonObject jsnRow = new JsonObject();
				jsnRow.AddVariable("ID", lst[i].QuoteId);
				jsnRow.AddVariable("No", lst[i].QuoteNumber);
				jsnRow.AddVariable("Part", lst[i].Part);
				jsnRow.AddVariable("Price", Functions.FormatCurrency(lst[i].Price, lst[i].CurrencyCode, 5, true));
				jsnRow.AddVariable("Quantity", Functions.FormatNumeric(lst[i].Quantity));
				jsnRow.AddVariable("Date", Functions.FormatDate(lst[i].DateQuoted));
				jsnRow.AddVariable("CM", lst[i].CompanyName);
				jsnRow.AddVariable("CMNo", lst[i].CompanyNo);
				jsnRow.AddVariable("Contact", lst[i].ContactName);
				jsnRow.AddVariable("ContactNo", lst[i].ContactNo);
				jsnRow.AddVariable("ROHS", lst[i].ROHS);
				jsnRow.AddVariable("Salesman", lst[i].SalesmanName);
				jsnRow.AddVariable("TotalBase", Functions.FormatCurrency(lst[i].TotalInBase, SessionManager.ClientCurrencyCode, true));
				jsnRow.AddVariable("TotalValue", Functions.FormatCurrency(lst[i].TotalValue, lst[i].CurrencyCode, 2, true));
				jsnRow.AddVariable("QStatus", lst[i].QuoteStatusName);
				jsnRow.AddVariable("OfferProfit", Functions.FormatCurrency(lst[i].OfferProfit, lst[i].CurrencyCode, 2, true));
				jsnRow.AddVariable("AS6081", lst[i].AS6081); //[0003]
				jsnRow.AddVariable("blnMakeYellow", blnMakeYellow);
				jsnRow.AddVariable("QuoteOfferedDate", Functions.FormatDate(lst[i].QuoteOfferedDate));
				//DateTime? taskReminderDate = null;
				//if (lst[i].QuoteOfferedDate != null)
				//{
				//	taskReminderDate = lst[i].QuoteOfferedDate.Value.AddDays(3);
				//}
				//jsnRow.AddVariable("TaskReminderDate", Functions.FormatDate(taskReminderDate));
				//jsnRow.AddVariable("TaskReminderTime", Functions.FormatTime(taskReminderDate));
				jsnRow.AddVariable("DateOfferStatus", lst[i].DateOfferStatus);
				jsnRow.AddVariable("TaskCount", lst[i].TaskCount);
				jsnRow.AddVariable("HasUnFinishedTask", lst[i].HasUnFinishedTask);
				jsnRowsArray.AddVariable(jsnRow);
				jsnRow.Dispose();
				jsnRow = null;
			}
			jsn.AddVariable("Results", jsnRowsArray);
			OutputResult(jsn);
			jsnRowsArray.Dispose();
			jsnRowsArray = null;
			jsn.Dispose();
			jsn = null;
			base.GetData();
		}

		protected override void AddFilterStates()
		{
			//Prevent filter state for Tab
			//AddExplicitFilterState("ViewLevel", GetFormValue_Int("ViewLevel"));
			AddFilterState("Part");
			AddFilterState("Contact");
			AddFilterState("CMName");
			AddFilterState("Salesman");
			AddFilterState("IncludeClosed");
			AddFilterState("QuoteNo");
			AddFilterState("DateQuotedFrom");
			AddFilterState("DateQuotedTo");
			AddFilterState("RecentOnly");
			AddFilterState("AS6081"); //[0003] 
			base.AddFilterStates();
		}

	}
}
