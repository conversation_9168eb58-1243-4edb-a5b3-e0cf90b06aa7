﻿using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using Rebound.GlobalTrader.DAL;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.BLL
{
    public partial class ToDo : BizObject
    {

        #region Properties

        protected static DAL.ToDoElement Settings
        {
            get { return Globals.Settings.ToDos; }
        }

        /// <summary>
        /// ToDoId
        /// </summary>
        public System.Int32 ToDoId { get; set; }
        /// <summary>
        /// LoginNo
        /// </summary>
        public System.Int32? LoginNo { get; set; }
        /// <summary>
        /// Subject
        /// </summary>
        public System.String Subject { get; set; }
        /// <summary>
        /// DateAdded
        /// </summary>
        public System.DateTime? DateAdded { get; set; }
        /// <summary>
        /// DueDate
        /// </summary>
        public System.DateTime? DueDate { get; set; }
        /// <summary>
        /// ToDoText
        /// </summary>
        public System.String ToDoText { get; set; }
        /// <summary>
        /// Priority
        /// </summary>
        public System.Int32? Priority { get; set; }
        /// <summary>
        /// IsComplete
        /// </summary>
        public System.Boolean IsComplete { get; set; }
        /// <summary>
        /// ReminderDate
        /// </summary>
        public System.DateTime? ReminderDate { get; set; }
        /// <summary>
        /// ReminderText
        /// </summary>
        public System.String ReminderText { get; set; }
        /// <summary>
        /// CompanyNo
        /// </summary>
        public System.Int32? CompanyNo { get; set; }
        /// <summary>
        /// ReminderHasBeenNotified
        /// </summary>
        public System.Boolean ReminderHasBeenNotified { get; set; }
        /// <summary>
        /// RelatedMailMessageNo
        /// </summary>
        public System.Int32? RelatedMailMessageNo { get; set; }
        /// <summary>
        /// UpdatedBy
        /// </summary>
        public System.Int32? UpdatedBy { get; set; }
        /// <summary>
        /// DLUP
        /// </summary>
        public System.DateTime DLUP { get; set; }
        /// <summary>
        /// LoginName
        /// </summary>
        public System.String LoginName { get; set; }
        public System.Int32? RowCnt { get; set; }
        /// <summary>
        /// RowNum
        /// </summary>
        public System.Int32? RowNum { get; set; }
        public System.Int32? ToDoListId { get; set; }
        public System.DateTime? CreatedDateFrom { get; set; }
        public System.DateTime? CreatedDateTo { get; set; }
        public System.DateTime? TaskDateFrom { get; set; }
        public System.DateTime? TaskDateTo { get; set; }
        public System.String TaskType { get; set; }
        public System.String TaskStatus { get; set; }
        public System.String CustomerName { get; set; }
        public System.String SalesPersonName { get; set; }
        public System.String ToDoTypeName { get; set; }
        public System.String ToDoTypeDescription { get; set; }
        public System.Boolean Inactive { get; set; }
        public System.Boolean? HasReview { get; set; }
        public System.String CompanyName { get; set; }
        public System.Int32? ToDoListTypeId { get; set; }
        public System.String TaskTitle { get; set; }
        public System.Int32? Contact { get; set; }
        public System.DateTime? TaskReminderDate { get; set; }
        public System.Int32? QuoteNo { get; set; }
        public System.Int32? QuoteNumber { get; set; }
        public System.Int32? ToDoCategoryNo { get; set; }
        public System.Boolean? DailyReminder { get; set; }
        public string ToDoCategoryName { get; set; }
        #endregion

        #region Methods

        /// <summary>
        /// Delete
        /// Calls [usp_delete_ToDo]
        /// </summary>
        public static bool Delete(System.Int32? toDoId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.ToDo.Delete(toDoId);
        }
        /// <summary>
        /// Insert
        /// Calls [usp_insert_ToDo]
        /// </summary>
        public static Int32 Insert(System.Int32? loginNo, System.String subject, System.DateTime? dateAdded, System.DateTime? dueDate, System.String toDoText, System.Int32? priority, System.Boolean? isComplete, System.DateTime? reminderDate, System.String reminderText, System.Boolean? reminderHasBeenNotified, System.Int32? companyNo, System.Int32? relatedMailMessageNo, System.Int32? updatedBy, System.Boolean? HasReview, System.Int32? ToDoListTypeId, System.Int32? Contact, System.Int32? QuoteNo, System.Int32? ToDoCategoryNo, System.Boolean? DailyReminder, System.Int32? SalesOrderNo)
        {
            Int32 objReturn = DAL.SiteProvider.ToDo.Insert(loginNo, subject, dateAdded, dueDate, toDoText, priority, isComplete, reminderDate, reminderText, reminderHasBeenNotified, companyNo, relatedMailMessageNo, updatedBy, HasReview, ToDoListTypeId, Contact, QuoteNo, ToDoCategoryNo, DailyReminder, SalesOrderNo);
            return objReturn;
        }
        /// <summary>
        /// Get
        /// Calls [usp_select_ToDo]
        /// </summary>
        public static ToDo Get(System.Int32? toDoId)
        {
            Rebound.GlobalTrader.DAL.ToDoDetails objDetails = Rebound.GlobalTrader.DAL.SiteProvider.ToDo.Get(toDoId);
            if (objDetails == null)
            {
                return null;
            }
            else
            {
                ToDo obj = new ToDo();
                obj.LoginName = objDetails.LoginName;
                obj.ToDoId = objDetails.ToDoId;
                obj.LoginNo = objDetails.LoginNo;
                obj.Subject = objDetails.Subject;
                obj.DateAdded = objDetails.DateAdded;
                obj.DueDate = objDetails.DueDate;
                obj.ToDoText = objDetails.ToDoText;
                obj.Priority = objDetails.Priority;
                obj.IsComplete = objDetails.IsComplete;
                obj.ReminderDate = objDetails.ReminderDate;
                obj.ReminderText = objDetails.ReminderText;
                obj.CompanyNo = objDetails.CompanyNo;
                obj.ReminderHasBeenNotified = objDetails.ReminderHasBeenNotified;
                obj.RelatedMailMessageNo = objDetails.RelatedMailMessageNo;
                obj.UpdatedBy = objDetails.UpdatedBy;
                obj.DLUP = objDetails.DLUP;
                obj.DailyReminder = objDetails.DailyReminder;
                obj.HasReview = objDetails.HasReview;
                obj.CompanyNo = objDetails.CompanyNo;
                obj.CompanyName = objDetails.CompanyName;
                obj.ToDoListTypeId = objDetails.ToDoListTypeId;
                obj.Contact = objDetails.Contact;
                obj.QuoteNumber = objDetails.QuoteNumber;
                obj.QuoteNo = objDetails.QuoteNo;
                obj.CustomerName = objDetails.CustomerName;
                objDetails = null;
                return obj;
            }
        }
        /// <summary>
        /// GetListAlertForLogin
        /// Calls [usp_selectAll_ToDo_Alert_for_Login]
        /// </summary>
        public static List<ToDo> GetListAlertForLogin(System.Int32? loginNo, System.DateTime? now)
        {
            List<ToDoDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.ToDo.GetListAlertForLogin(loginNo, now);
            if (lstDetails == null)
            {
                return new List<ToDo>();
            }
            else
            {
                List<ToDo> lst = new List<ToDo>();
                foreach (ToDoDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.ToDo obj = new Rebound.GlobalTrader.BLL.ToDo();
                    obj.ToDoId = objDetails.ToDoId;
                    obj.ReminderText = objDetails.ReminderText;
                    obj.DueDate = objDetails.DueDate;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        /// <summary>
        /// GetListByLogin
        /// Calls [usp_selectAll_ToDo_by_Login]
        /// </summary>
        public static List<ToDo> GetListByLogin(System.Int32? loginNo)
        {
            List<ToDoDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.ToDo.GetListByLogin(loginNo);
            if (lstDetails == null)
            {
                return new List<ToDo>();
            }
            else
            {
                List<ToDo> lst = new List<ToDo>();
                foreach (ToDoDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.ToDo obj = new Rebound.GlobalTrader.BLL.ToDo();
                    obj.LoginName = objDetails.LoginName;
                    obj.ToDoId = objDetails.ToDoId;
                    obj.LoginNo = objDetails.LoginNo;
                    obj.Subject = objDetails.Subject;
                    obj.DateAdded = objDetails.DateAdded;
                    obj.DueDate = objDetails.DueDate;
                    obj.ToDoText = objDetails.ToDoText;
                    obj.Priority = objDetails.Priority;
                    obj.IsComplete = objDetails.IsComplete;
                    obj.ReminderDate = objDetails.ReminderDate;
                    obj.ReminderText = objDetails.ReminderText;
                    obj.CompanyNo = objDetails.CompanyNo;
                    obj.ReminderHasBeenNotified = objDetails.ReminderHasBeenNotified;
                    obj.RelatedMailMessageNo = objDetails.RelatedMailMessageNo;
                    obj.UpdatedBy = objDetails.UpdatedBy;
                    obj.DLUP = objDetails.DLUP;
                    obj.CompanyNo = objDetails.CompanyNo;
                    obj.CompanyName = objDetails.CompanyName;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        /// <summary>
        /// GetListForMailMessage
        /// Calls [usp_selectAll_ToDo_for_MailMessage]
        /// </summary>
        public static List<ToDo> GetListForMailMessage(System.Int32? mailMessageNo)
        {
            List<ToDoDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.ToDo.GetListForMailMessage(mailMessageNo);
            if (lstDetails == null)
            {
                return new List<ToDo>();
            }
            else
            {
                List<ToDo> lst = new List<ToDo>();
                foreach (ToDoDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.ToDo obj = new Rebound.GlobalTrader.BLL.ToDo();
                    obj.LoginName = objDetails.LoginName;
                    obj.ToDoId = objDetails.ToDoId;
                    obj.LoginNo = objDetails.LoginNo;
                    obj.Subject = objDetails.Subject;
                    obj.DateAdded = objDetails.DateAdded;
                    obj.DueDate = objDetails.DueDate;
                    obj.ToDoText = objDetails.ToDoText;
                    obj.Priority = objDetails.Priority;
                    obj.IsComplete = objDetails.IsComplete;
                    obj.ReminderDate = objDetails.ReminderDate;
                    obj.ReminderText = objDetails.ReminderText;
                    obj.CompanyNo = objDetails.CompanyNo;
                    obj.ReminderHasBeenNotified = objDetails.ReminderHasBeenNotified;
                    obj.RelatedMailMessageNo = objDetails.RelatedMailMessageNo;
                    obj.UpdatedBy = objDetails.UpdatedBy;
                    obj.DLUP = objDetails.DLUP;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        /// <summary>
        /// Update
        /// Calls [usp_update_ToDo]
        /// </summary>
        public static bool Update(System.Int32? toDoId, System.Int32? loginNo, System.String subject, System.DateTime? dueDate, System.String toDoText, System.Int32? priority, System.Boolean? isComplete, System.DateTime? reminderDate, System.String reminderText, System.Boolean? reminderHasBeenNotified, System.Int32? companyNo, System.Int32? relatedMailMessageNo, System.Int32? updatedBy, System.Boolean? HasReview, System.Int32? ToDoListTypeId, System.Int32? ContactNo)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.ToDo.Update(toDoId, loginNo, subject, dueDate, toDoText, priority, isComplete, reminderDate, reminderText, reminderHasBeenNotified, companyNo, relatedMailMessageNo, updatedBy, HasReview, ToDoListTypeId, ContactNo);
        }
        /// <summary>
        /// Update (without parameters)
        /// Calls [usp_update_ToDo]
        /// </summary>
        public bool Update()
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.ToDo.Update(ToDoId, LoginNo, Subject, DueDate, ToDoText, Priority, IsComplete, ReminderDate, ReminderText, ReminderHasBeenNotified, CompanyNo, RelatedMailMessageNo, UpdatedBy, HasReview, ToDoListTypeId, Contact);
        }
        /// <summary>
        /// UpdateComplete
        /// Calls [usp_update_ToDo_Complete]
        /// </summary>
        public static bool UpdateComplete(System.Int32? toDoId, System.Boolean? isComplete, System.Int32? updatedBy)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.ToDo.UpdateComplete(toDoId, isComplete, updatedBy);
        }
        /// <summary>
        /// UpdateDismiss
        /// Calls [usp_update_ToDo_Dismiss]
        /// </summary>
        public static bool UpdateDismiss(System.Int32? toDoId, System.Int32? updatedBy)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.ToDo.UpdateDismiss(toDoId, updatedBy);
        }
        /// <summary>
        /// UpdateSnooze
        /// Calls [usp_update_ToDo_Snooze]
        /// </summary>
        public static bool UpdateSnooze(System.Int32? toDoId, System.Int32? snoozeMinutes, System.Int32? updatedBy)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.ToDo.UpdateSnooze(toDoId, snoozeMinutes, updatedBy);
        }

        private static ToDo PopulateFromDBDetailsObject(ToDoDetails obj)
        {
            ToDo objNew = new ToDo();
            objNew.ToDoId = obj.ToDoId;
            objNew.LoginNo = obj.LoginNo;
            objNew.Subject = obj.Subject;
            objNew.DateAdded = obj.DateAdded;
            objNew.DueDate = obj.DueDate;
            objNew.ToDoText = obj.ToDoText;
            objNew.Priority = obj.Priority;
            objNew.IsComplete = obj.IsComplete;
            objNew.ReminderDate = obj.ReminderDate;
            objNew.ReminderText = obj.ReminderText;
            objNew.CompanyNo = obj.CompanyNo;
            objNew.ReminderHasBeenNotified = obj.ReminderHasBeenNotified;
            objNew.RelatedMailMessageNo = obj.RelatedMailMessageNo;
            objNew.UpdatedBy = obj.UpdatedBy;
            objNew.DLUP = obj.DLUP;
            objNew.LoginName = obj.LoginName;
            return objNew;
        }
        /// <summary>
        /// Calls [usp_selectall_toDoListTask]
        /// </summary>
        /// <param name="createdDateFrom"></param>
        /// <param name="createdDateTo"></param>
        /// <param name="taskDateFrom"></param>
        /// <param name="taskDateTo"></param>
        /// <param name="taskType"></param>
        /// <param name="taskStatus"></param>
        /// <param name="customerName"></param>
        /// <param name="salesPerson"></param>
        /// <returns></returns>
        public static List<ToDo> DataListNugget(System.DateTime? createdDateFrom,
                                                System.DateTime? createdDateTo,
                                                System.DateTime? taskDateFrom,
                                                System.DateTime? taskDateTo,
                                                System.Int32? taskType,
                                                System.String taskStatus,
                                                System.String customerName,
                                                System.Int32? salesPerson,
                                                System.Int32? loginId,
                                                System.Int32? clientId,
                                                System.Int32? OrderBy,
                                                System.Int32? SortDir,
                                                System.Int32? PageIndex,
                                                System.Int32? PageSize,
                                                System.Boolean ReviewOnly,
                                                System.DateTime? TaskReminderDate,
                                                int? TodoID,
                                                int? taskCategoryNo,
                                                string quoteNumber)
        {
            List<ToDoDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.ToDo.DataListNugget(createdDateFrom,
                                                                                                     createdDateTo,
                                                                                                     taskDateFrom,
                                                                                                     taskDateTo,
                                                                                                     taskType,
                                                                                                     taskStatus,
                                                                                                     customerName,
                                                                                                     salesPerson,
                                                                                                     loginId,
                                                                                                     clientId,
                                                                                                     OrderBy,
                                                                                                     SortDir,
                                                                                                     PageIndex,
                                                                                                     PageSize,
                                                                                                     ReviewOnly,
                                                                                                     TaskReminderDate,
                                                                                                     TodoID,
                                                                                                     taskCategoryNo,
                                                                                                     quoteNumber);
            if (lstDetails == null)
            {
                return new List<ToDo>();
            }
            else
            {
                List<ToDo> lst = new List<ToDo>();
                foreach (ToDoDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.ToDo obj = new Rebound.GlobalTrader.BLL.ToDo();
                    obj.ToDoListId = objDetails.ToDoListId;
                    obj.CreatedDateFrom = objDetails.CreatedDateFrom;
                    obj.CreatedDateTo = objDetails.CreatedDateTo;
                    obj.TaskDateFrom = objDetails.TaskDateFrom;
                    obj.TaskDateTo = objDetails.TaskDateTo;
                    obj.TaskType = objDetails.TaskType;
                    obj.TaskStatus = objDetails.TaskStatus;
                    obj.CustomerName = objDetails.CustomerName;
                    obj.SalesPersonName = objDetails.SalesPersonName;
                    obj.RowCnt = objDetails.RowCnt;
                    obj.ReminderDate = objDetails.ReminderDate;
                    obj.IsComplete = objDetails.IsComplete;
                    obj.TaskTitle = objDetails.TaskTitle;
                    obj.TaskReminderDate = objDetails.TaskReminderDate;
                    obj.ToDoCategoryNo = objDetails.ToDoCategoryNo;
                    obj.ToDoCategoryName = objDetails.ToDoCategoryName;
                    obj.QuoteNo = objDetails.QuoteNo;
                    obj.QuoteNumber = objDetails.QuoteNumber;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }

        public static DataTable DataListNugget_Export(System.DateTime? createdDateFrom, System.DateTime? createdDateTo, System.DateTime? taskDateFrom, System.DateTime? taskDateTo, System.String taskType, System.String taskStatus, System.String customerName, System.Int32? salesPerson, System.Int32? loginId, System.Int32? clientId, System.Int32? OrderBy, System.Int32? SortDir, System.Int32? PageIndex, System.Int32? PageSize, System.Boolean ReviewOnly, System.DateTime? TaskReminderDate)
        {
            DataTable dt = Rebound.GlobalTrader.DAL.SiteProvider.ToDo.DataListNugget_Export(createdDateFrom, createdDateTo, taskDateFrom, taskDateTo, taskType, taskStatus, customerName, salesPerson, loginId, clientId, OrderBy, SortDir, PageIndex, PageSize, ReviewOnly, TaskReminderDate);
            return dt;
        }
        #endregion
        #region To Do List Type
        public static List<ToDo> GetToDoList()
        {
            List<ToDoDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.ToDo.GetToDoList();
            if (lstDetails == null)
            {
                return new List<ToDo>();
            }
            else
            {
                List<ToDo> lst = new List<ToDo>();
                foreach (ToDoDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.ToDo obj = new Rebound.GlobalTrader.BLL.ToDo();
                    obj.ToDoListId = objDetails.ToDoListId;
                    obj.ToDoTypeName = objDetails.ToDoTypeName;
                    obj.ToDoTypeDescription = objDetails.ToDoTypeDescription;
                    obj.Inactive = objDetails.Inactive;
                    obj.UpdatedBy = objDetails.UpdatedBy;
                    obj.DLUP = objDetails.DLUP;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        public static ToDo GetToDoListType(System.Int32? ToDoListId)
        {
            Rebound.GlobalTrader.DAL.ToDoDetails objDetails = Rebound.GlobalTrader.DAL.SiteProvider.ToDo.GetToDoListType(ToDoListId);
            if (objDetails == null)
            {
                return null;
            }
            else
            {
                ToDo obj = new ToDo();
                obj.ToDoListId = objDetails.ToDoListId;
                obj.ToDoTypeName = objDetails.ToDoTypeName;
                obj.ToDoTypeDescription = objDetails.ToDoTypeDescription;
                obj.Inactive = objDetails.Inactive;
                obj.UpdatedBy = objDetails.UpdatedBy;
                obj.DLUP = objDetails.DLUP;
                objDetails = null;
                return obj;
            }
        }
        public static Int32 InsertToDoListType(System.String ToDoTypeName, System.String ToDoTypeDescription, System.Int32? UpdatedBy)
        {
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.ToDo.InsertToDoListType(ToDoTypeName, ToDoTypeDescription, UpdatedBy);
            return objReturn;
        }
        public static bool UpdateToDoListType(System.Int32? ToDoListId, System.String ToDoTypeName, System.String ToDoTypeDescription, System.Boolean? Inactive, System.Int32? UpdatedBy)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.ToDo.UpdateToDoListType(ToDoListId, ToDoTypeName, ToDoTypeDescription, Inactive, UpdatedBy);
        }
        /// <summary>
        /// Update (without parameters)
        /// Calls [usp_update_Package]
        /// </summary>
        public bool UpdateToDoListType()
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.ToDo.UpdateToDoListType(ToDoListId, ToDoTypeName, ToDoTypeDescription, Inactive, UpdatedBy);
        }
        #endregion
        public static List<ToDo> DropDownToDoListType()
        {
            List<ToDoDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.ToDo.DropDownToDoListType();
            if (lstDetails == null)
            {
                return new List<ToDo>();
            }
            else
            {
                List<ToDo> lst = new List<ToDo>();
                foreach (ToDoDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.ToDo obj = new Rebound.GlobalTrader.BLL.ToDo();
                    obj.ToDoListId = objDetails.ToDoListId;
                    obj.ToDoTypeName = objDetails.ToDoTypeName;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
    }
}