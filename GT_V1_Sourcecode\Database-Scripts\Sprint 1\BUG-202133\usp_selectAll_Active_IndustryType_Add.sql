
IF OBJECT_ID('usp_selectAll_Active_IndustryType', 'P') IS NOT NULL
DROP PROC usp_selectAll_Active_IndustryType
GO

/****** Object:  StoredProcedure [dbo].[usp_selectAll_Active_IndustryType]    Script Date: 5/3/2024 2:47:40 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

-- =============================================
-- Author:		<Cuong,,Do>
-- Create date: <05/02/2024,,>
-- Description:	<Return all Industry Type with active=1,,>
-- =============================================
CREATE PROCEDURE [dbo].[usp_selectAll_Active_IndustryType]

AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;

    -- Insert statements for procedure here
	SELECT	IndustryTypeId
	,		[Name]
	,		Inactive
	,		UpdatedBy
	,		DLUP	
	FROM	dbo.tbIndustryType
	WHERE Inactive = 0
	ORDER BY 2
	
END
GO

