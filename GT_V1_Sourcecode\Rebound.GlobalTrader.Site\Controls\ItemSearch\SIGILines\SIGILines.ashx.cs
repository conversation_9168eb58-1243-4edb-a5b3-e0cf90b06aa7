/* Marker           Change By           Date            Remarks
 * [001]            <PERSON>        28-01-2023      [RP-1326] filter for Released GI only
 * 
 * 
 */
using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site.Enumerations;

namespace Rebound.GlobalTrader.Site.Controls.ItemSearch.Data
{
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class SIGILines : Rebound.GlobalTrader.Site.Data.ItemSearch.Base
    {

        protected override void GetData()
        {
            List<BLL.SupplierInvoiceLine> lst = null;
            try
            {
                int? GoodsInNo = GetFormValue_NullableInt("GoodsInNo", null);
                bool? isClientInvoice = GetFormValue_NullableBoolean("IsClientInvoice", null);
                int InvoiceClientNo = GetFormValue_Int("InvoiceClientNo");
                //string FromCurrencyCode = GetFormValue_String("FromCurrencyCode");
                lst = BLL.SupplierInvoiceLine.ItemSearch(
                    isClientInvoice == true ? InvoiceClientNo : SessionManager.ClientID
                    , GetFormValue_NullableInt("Order", 0)
                    , GetFormValue_NullableInt("SortDir", SortColumnDirection.ASC)
                    , GetFormValue_NullableInt("PageIndex", 0)
                    , GetFormValue_NullableInt("PageSize", 10)
                    , GetFormValue_Int("CompanyNo")
                    , GetFormValue_NullableBoolean("IncludeInvoiced", null)
                    , GetFormValue_NullableDateTime("GIDateFrom", null)
                    , GetFormValue_NullableDateTime("GIDateTo", null)
                    , (GoodsInNo.HasValue && GoodsInNo.Value > 0) ? GoodsInNo : null
                    , Convert.ToBoolean(SessionManager.IsPOHub)
                    , isClientInvoice
                    , GetFormValue_NullableInt("PONoLo")
                    , GetFormValue_NullableInt("PONoHi")
                    , SessionManager.LoginID
                   // , GetFormValue_NullableBoolean("IncludeUnreleaseGI", null)
                    , GetFormValue_NullableBoolean("ShowReleaseGI", null) // [001]
                    );
                JsonObject jsn = new JsonObject();
                JsonObject jsnItems = new JsonObject(true);
                //double? TotalLineValue = 0;
                string strShipInCost = string.Empty;
                double? ShipCostValue = 0;
                for (int i = 0; i < lst.Count; i++)
                {
                    ShipCostValue = 0;
                    //show Ship in cost in GI and base currency if appropriate
                    if (lst[i].CurrencyNo == SessionManager.ClientCurrencyID)
                    {
                        ShipCostValue = lst[i].ShipInCost;
                        strShipInCost = Functions.FormatCurrency(lst[i].ShipInCost, SessionManager.ClientCurrencyCode, 5);
                    }
                    else
                    {
                        ShipCostValue = BLL.Currency.ConvertValueFromBaseCurrency(lst[i].ShipInCost, (int)lst[i].CurrencyNo, (DateTime)lst[i].DateReceived);
                        strShipInCost = String.Format("{0}", Functions.FormatCurrency(ShipCostValue, lst[i].CurrencyCode, 5));
                    }

                    //TotalLineValue = lst[i].Price * (double)lst[i].QtyReceived;
                    JsonObject jsnItem = new JsonObject();
                    jsnItem.AddVariable("ID", lst[i].GoodsInLineNo);
                    jsnItem.AddVariable("GINumber", lst[i].GoodsInNumber);
                    jsnItem.AddVariable("GoodsInNo", lst[i].GoodsInNo);
                    jsnItem.AddVariable("Part", lst[i].Part);
                    jsnItem.AddVariable("PONumber", lst[i].PurchaseOrderNumber);
                    jsnItem.AddVariable("QtyReceived", lst[i].QtyReceived);
                    jsnItem.AddVariable("Price", Functions.FormatCurrency(lst[i].Price, lst[i].CurrencyCode, 5));
                    jsnItem.AddVariable("LineTotal", Functions.FormatCurrency(lst[i].TotalLineValue, lst[i].CurrencyCode, 5));
                    jsnItem.AddVariable("LineTotalValue", Functions.FormatCurrency(lst[i].TotalLineValue, 5));
                    jsnItem.AddVariable("ShipInCost", strShipInCost);
                    jsnItem.AddVariable("ShipInCostVal", ShipCostValue);
                    jsnItem.AddVariable("NPRPrinted", lst[i].NPRPrinted);
                    jsnItem.AddVariable("blnFromGoodsIn", (lst[i].GoodsInNo == GoodsInNo));
                    jsnItem.AddVariable("GIDate", Functions.FormatDate(lst[i].DateReceived));
                    jsnItem.AddVariable("DebitId", (lst[i].DebitId));
                    jsnItem.AddVariable("DebitNumber", (lst[i].DebitNumber));
                    jsnItem.AddVariable("IsUnReleasedLine", (lst[i].IsUnReleasedLine));
                    //jsnItem.AddVariable("ShowReleasedLine", (lst[i].ShowReleasedLine)); //[001]
                    jsnItems.AddVariable(jsnItem);
                    jsnItem.Dispose();
                    jsnItem = null;
                }
                // jsn.AddVariable("Count", (lst.Count > 0) ? lst[0].RowCnt : 0);
                jsn.AddVariable("Results", jsnItems);
                OutputResult(jsn);
                jsnItems.Dispose();
                jsnItems = null;
                jsn.Dispose();
                jsn = null;
            }
            catch (Exception ex)
            {
                WriteError(ex);
            }
            finally
            {
                lst = null;
            }
            base.GetData();
        }
    }
}
