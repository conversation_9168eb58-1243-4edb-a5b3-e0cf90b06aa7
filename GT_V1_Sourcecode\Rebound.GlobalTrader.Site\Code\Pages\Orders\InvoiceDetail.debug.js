///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference name="~/Controls/IconButton/IconButton.js" />
///<reference name="~/Controls/ReboundTextBox/ReboundTextBox.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//Marker     Changed by      Date         Remarks
//[001]      Vinay           12/10/2012   Upload PDF document for invoices
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Orders");

Rebound.GlobalTrader.Site.Pages.Orders.InvoiceDetail = function (el) {
    Rebound.GlobalTrader.Site.Pages.Orders.InvoiceDetail.initializeBase(this, [el]);
};

Rebound.GlobalTrader.Site.Pages.Orders.InvoiceDetail.prototype = {

    get_intInvoiceID: function () { return this._intInvoiceID; }, set_intInvoiceID: function (v) { if (this._intInvoiceID !== v) this._intInvoiceID = v; },
    get_btnPrint: function () { return this._btnPrint; }, set_btnPrint: function (v) { if (this._btnPrint !== v) this._btnPrint = v; },
    get_ctlMainInfo: function () { return this._ctlMainInfo; }, set_ctlMainInfo: function (v) { if (this._ctlMainInfo !== v) this._ctlMainInfo = v; },
    get_ctlLines: function () { return this._ctlLines; }, set_ctlLines: function (v) { if (this._ctlLines !== v) this._ctlLines = v; },
    get_ctlLinesDeleted: function () { return this._ctlLinesDeleted; }, set_ctlLinesDeleted: function (v) { if (this._ctlLinesDeleted !== v) this._ctlLinesDeleted = v; },
    get_lblStatus: function () { return this._lblStatus; }, set_lblStatus: function (v) { if (this._lblStatus !== v) this._lblStatus = v; },
    get_pnlStatus: function () { return this._pnlStatus; }, set_pnlStatus: function (v) { if (this._pnlStatus !== v) this._pnlStatus = v; },
    // [001] code start
    get_ctlInvoicePDF: function () { return this._ctlInvoicePDF; }, set_ctlInvoicePDF: function (v) { if (this._ctlInvoicePDF !== v) this._ctlInvoicePDF = v; },
    // [001] code end
    get_ctlInvoicePDFDragDrop: function () { return this._ctlInvoicePDFDragDrop; }, set_ctlInvoicePDFDragDrop: function (v) { if (this._ctlInvoicePDFDragDrop !== v) this._ctlInvoicePDFDragDrop = v; },
    get_IsGlobalLogin: function () { return this._IsGlobalLogin; }, set_IsGlobalLogin: function (v) { if (this._IsGlobalLogin !== v) this._IsGlobalLogin = v; },
    get_ctlInvoicePODPDFDragDrop: function () { return this._ctlInvoicePODPDFDragDrop; }, set_ctlInvoicePODPDFDragDrop: function (v) { if (this._ctlInvoicePODPDFDragDrop !== v) this._ctlInvoicePODPDFDragDrop = v; },
    get_ctlInvoiceExportHistory: function () { return this._ctlInvoiceExportHistory; }, set_ctlInvoiceExportHistory: function (v) { if (this._ctlInvoiceExportHistory !== v) this._ctlInvoiceExportHistory = v; },
    get_AllowGenerateXml: function () { return this._AllowGenerateXml; }, set_AllowGenerateXml: function (v) { if (this._AllowGenerateXml !== v) this._AllowGenerateXml = v; },

    initialize: function () {
        Rebound.GlobalTrader.Site.Pages.Orders.InvoiceDetail.callBaseMethod(this, "initialize");
        document.getElementById("btnFormatAction").addEventListener("click", Function.createDelegate(this, this.selectPrintFormat));
        document.getElementById("btnFormatCancel").addEventListener("click", Function.createDelegate(this, this.closePopup));
    },

    goInit: function () {
        if (this._btnPrint) this._btnPrint.addPrint(Function.createDelegate(this, this.printInvoice));
        if (this._btnPrint) this._btnPrint.addEmail(Function.createDelegate(this, this.emailInvoice));
        if (this._btnPrint) this._btnPrint.addExtraButtonClick(Function.createDelegate(this, this.printOtherDocs));
        if (this._ctlMainInfo) this._ctlMainInfo.addGetDataComplete(Function.createDelegate(this, this.ctlMainInfo_GetDataComplete));
        if (this._ctlLines) this._ctlLines.addGetDeletedData(Function.createDelegate(this, this.ctlLines_GetDeletedData));
        // [001] code start
        if (this._ctlInvoicePDF) this._ctlInvoicePDF.getData();
        // [001] code end
        if (this._ctlInvoicePDFDragDrop) this._ctlInvoicePDFDragDrop.getData();
        if (this._ctlInvoicePODPDFDragDrop) this._ctlInvoicePODPDFDragDrop.getData();
        if (this._ctlInvoiceExportHistory) this._ctlInvoiceExportHistory.getData();
        Rebound.GlobalTrader.Site.Pages.Orders.InvoiceDetail.callBaseMethod(this, "goInit");
    },

    dispose: function () {
        if (this.isDisposed) return;
        if (this._ctlMainInfo) this._ctlMainInfo.dispose();
        if (this._ctlLines) this._ctlLines.dispose();
        if (this._ctlLinesDeleted) this._ctlLinesDeleted.dispose();
        if (this._btnPrint) this._btnPrint.dispose();
        this._btnPrint = null;
        this._ctlMainInfo = null;
        this._ctlLines = null;
        this._ctlLinesDeleted = null;
        this._pnlStatus = null;
        this._lblStatus = null;
        this._intInvoiceID = null;
        //[001] code start
        this._ctlInvoicePDF = null;
        //[001] code end
        this._IsGlobalLogin = null;
        Rebound.GlobalTrader.Site.Pages.Orders.InvoiceDetail.callBaseMethod(this, "dispose");
    },

    printInvoice: function () {
        //$R_FN.openPrintWindow($R_ENUM$PrintObject.Invoice, this._intInvoiceID);
        this.openFormatModal();
    },

    emailInvoice: function () {
        $R_FN.openPrintWindow($R_ENUM$PrintObject.Invoice, this._intInvoiceID, true);
    },

    selectPrintFormat: function () {
        var selectedFormat = $('input[name="rdInvoiceFormat"]:checked').val();
        if (selectedFormat == 'XML') {
            $R_FN.openPrintWindow($R_ENUM$PrintObject.XmlInvoice, this._intInvoiceID);
        } else {
            $R_FN.openPrintWindow($R_ENUM$PrintObject.Invoice, this._intInvoiceID);
        }

        this.closePopup();
    },

    openFormatModal: function () {
        let defaultFormat = (this._AllowGenerateXml && this._ctlMainInfo.getFieldValue("hidGlobalClientNo") == '108') ? 'XML' : 'PDF';
        $("#overlay").css("display", "block");
        $('input:radio[name=rdInvoiceFormat]').val([defaultFormat]);
        $('#optionXML').css("display", this._AllowGenerateXml ? 'block' : 'none');
        $("#formatModal").dialog("open");
    },

    closePopup: function () {
        $("#overlay").css("display", "none");
        $("#formatModal").dialog("close");
    },

    printOtherDocs: function () {
        if (this._btnPrint._strExtraButtonClickCommand == "PrintPackingSlip") $R_FN.openPrintWindow($R_ENUM$PrintObject.PackingSlip, this._intInvoiceID);
        if (this._btnPrint._strExtraButtonClickCommand == "EmailPackingSlip") $R_FN.openPrintWindow($R_ENUM$PrintObject.PackingSlip, this._intInvoiceID, true);
        if (this._btnPrint._strExtraButtonClickCommand == "PrintCertificateOfConformance") $R_FN.openPrintWindow($R_ENUM$PrintObject.CertificateOfConformance, this._intInvoiceID);
        if (this._btnPrint._strExtraButtonClickCommand == "EmailCertificateOfConformance") $R_FN.openPrintWindow($R_ENUM$PrintObject.CertificateOfConformance, this._intInvoiceID, true);
        //For commercial invoice
        if (this._btnPrint._strExtraButtonClickCommand == "PrintCommercialInvoice") $R_FN.openPrintWindow($R_ENUM$PrintObject.CommercialInvoice, this._intInvoiceID);
        if (this._btnPrint._strExtraButtonClickCommand == "EmailCommercialInvoice") $R_FN.openPrintWindow($R_ENUM$PrintObject.CommercialInvoice, this._intInvoiceID, true);
        //For Invoice Include Cocf
        if (this._btnPrint._strExtraButtonClickCommand == "PrintInvoiceWithCoC") $R_FN.openPrintWindow($R_ENUM$PrintObject.InvoiceWithCocf, this._intInvoiceID);
        if (this._btnPrint._strExtraButtonClickCommand == "EmailInvoiceWithCoC") $R_FN.openPrintWindow($R_ENUM$PrintObject.InvoiceWithCocf, this._intInvoiceID, true);

        //For Packing Slip Include Cocf
        if (this._btnPrint._strExtraButtonClickCommand == "PrintPackingSlipWithCoc") $R_FN.openPrintWindow($R_ENUM$PrintObject.PackingSlipWithCocf, this._intInvoiceID);
        if (this._btnPrint._strExtraButtonClickCommand == "EmailPackingSlipWithCoc") $R_FN.openPrintWindow($R_ENUM$PrintObject.PackingSlipWithCocf, this._intInvoiceID, true);
        if (this._btnPrint._strExtraButtonClickCommand == "PrintLog") $R_FN.openPrintLogWindow($R_ENUM$PrintObject.PrintLog, this._intInvoiceID, false, "Invoice");
        if (this._btnPrint._strExtraButtonClickCommand == "PrintDivisionHeaaderLog") $R_FN.openPrintLogWindow($R_ENUM$PrintObject.PrintDivisionHeaderLog, this._intInvoiceID, false, "InvoiceDivisionHeader");
    },

    ctlMainInfo_GetDataComplete: function () {
        $R_FN.setInnerHTML(this._lblStatus, this._ctlMainInfo.getFieldValue("hidStatus"));
        this._ctlLines._intGlobalClientNo = this._IsGlobalLogin == true ? this._ctlMainInfo.getFieldValue("hidGlobalClientNo") : null;
        this._ctlMainInfo._IsGlobalLogin = this._IsGlobalLogin;

        this.setLineFieldsFromHeader();
        this._ctlLines.getData();
        this._ctlLinesDeleted.getData();
    },

    ctlLines_GetDeletedData: function () {
        this._ctlLinesDeleted.getData();
    },

    setLineFieldsFromHeader: function () {
        var strInvoiceNumber = this._ctlMainInfo.getFieldValue("hidNo");
        var strCustomer = this._ctlMainInfo.getFieldValue("hidCompany");
        var strCurrency = this._ctlMainInfo.getFieldValue("hidCurrencyCode");
        var intCurrency = this._ctlMainInfo.getFieldValue("hidCurrencyNo");
        var strDate = this._ctlMainInfo.getFieldValue("ctlDateShipped");
        if (this._ctlLines) {
            this._ctlLines._blnExported = this._ctlMainInfo._blnExported;
            if (this._ctlLines._frmAdd) this._ctlLines._frmAdd.setFieldsFromHeader(strInvoiceNumber, strCustomer, strCurrency, intCurrency, strDate);
        }
    }



};
Rebound.GlobalTrader.Site.Pages.Orders.InvoiceDetail.registerClass("Rebound.GlobalTrader.Site.Pages.Orders.InvoiceDetail", Rebound.GlobalTrader.Site.Pages.Content, Sys.IDisposable);
