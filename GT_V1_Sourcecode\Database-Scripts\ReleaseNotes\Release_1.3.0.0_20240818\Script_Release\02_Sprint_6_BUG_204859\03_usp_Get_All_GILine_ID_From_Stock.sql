﻿/* 
===========================================================================================
TASK      			UPDATED BY     		DATE         		ACTION 			DESCRIPTION
[BUG-204859]		Cuong.DoX			12-JULY-2024		Create			Get All GILine ID from Stock where GILine is not released 
===========================================================================================
*/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
-- =============================================
-- Author:		Cuongdx
-- Create date: 12 Jul 2024
-- Description:	Get All GILine ID from Stock
-- =============================================
CREATE OR ALTER PROCEDURE usp_Get_All_GILine_ID_From_Stock
	-- Add the parameters for the stored procedure here
	@StockId int
AS
BEGIN
	SELECT GoodsInLineNo
	FROM tbStock a
	INNER JOIN tbGoodsInline gil
	ON a.GoodsInLineNo = gil.GoodsInLineId
	WHERE a.StockId = @StockId
	      AND gil.InspectedBy IS NULL
END	

GO
