﻿/*
Marker     Changed by               Date         Remarks
[001]      A<PERSON><PERSON><PERSON>           29/07/2021   Implement a new dropdown for supplier Approval Status
*/
using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;

namespace Rebound.GlobalTrader.DAL
{
    public abstract class SupplierApprovalStatusProvider : DataAccess
    {
        static private SupplierApprovalStatusProvider _instance = null;

        static public SupplierApprovalStatusProvider Instance
        {
            get
            {
                if (_instance == null) _instance = (SupplierApprovalStatusProvider)Activator.CreateInstance(Type.GetType(Globals.Settings.SupplierApprovalStatus.ProviderType));
                return _instance;
            }
        }
        public SupplierApprovalStatusProvider()
        {
            this.ConnectionString = Globals.Settings.SupplierApprovalStatus.ConnectionString;
        }
        #region Method
        public abstract List<SupplierApprovalStatusDetails> DropDown();
        #endregion
    }
}
