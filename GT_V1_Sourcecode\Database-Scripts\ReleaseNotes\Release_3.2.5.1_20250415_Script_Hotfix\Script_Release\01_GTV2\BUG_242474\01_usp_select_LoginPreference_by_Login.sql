﻿
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


/*============================================================================================================================================================== 
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-216229]     NgaiTo		 	 31-Oct-2024		UPDATE		216229: Login - Remove duplicated fields in Stored Procedure usp_select_LoginPreference_by_Login
[US-242474]     Phuc Hoang		 15-Apr-2024		UPDATE		Bug 242474: Login Issue
================================================================================================================================================================ 
*/

CREATE OR ALTER PROCEDURE [dbo].[usp_select_LoginPreference_by_Login]         
@LoginNo int        
AS        
SELECT p.LoginNo
	, p.ShowMessageAlert
	, p.DefaultSiteLanguageNo
	, p.DefaultListPageSize
	, p.NumberRecentlyViewedPages
	, p.UpdatedBy
	, p.DLUP
	, p.DefaultHomePageTab
	, p.DefaultListPageView
	, p.BackgroundImage
	, p.SaveDataListNuggetStateByDefault
	, p.LoginTimeout
	, ISNULL(p.SendEmail, 0) AS SendEmail
	, p.PrinterNo
	, p.LabelPathNo
	, l.Code as DefaultSiteLanguageCode   
	, pr.PrinterName          
FROM dbo.tbLoginPreference p        
JOIN dbo.tbSiteLanguage l ON p.DefaultSiteLanguageNo = l.SiteLanguageId        
Left Join tbPrinter pr ON p.PrinterNo = pr.PrinterId      
WHERE p.LoginNo    = @LoginNo 


GO


