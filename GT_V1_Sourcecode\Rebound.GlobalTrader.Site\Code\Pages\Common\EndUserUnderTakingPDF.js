Type.registerNamespace("Rebound.GlobalTrader.Site.Pages");Rebound.GlobalTrader.Site.Pages.EndUserUnderTakingPDF=function(n){Rebound.GlobalTrader.Site.Pages.EndUserUnderTakingPDF.initializeBase(this,[n]);this._intSalesOrderLineID=0};Rebound.GlobalTrader.Site.Pages.EndUserUnderTakingPDF.prototype={get_ctlPageTitle:function(){return this._ctlPageTitle},set_ctlPageTitle:function(n){this._ctlPageTitle!==n&&(this._ctlPageTitle=n)},get_ctlMainInfo:function(){return this._ctlMainInfo},set_ctlMainInfo:function(n){this._ctlMainInfo!==n&&(this._ctlMainInfo=n)},get_ctlManufacturerPDFDragDrop:function(){return this._ctlManufacturerPDFDragDrop},set_ctlManufacturerPDFDragDrop:function(n){this._ctlManufacturerPDFDragDrop!==n&&(this._ctlManufacturerPDFDragDrop=n)},initialize:function(){Rebound.GlobalTrader.Site.Pages.EndUserUnderTakingPDF.callBaseMethod(this,"initialize")},goInit:function(){this._ctlMainInfo&&this._ctlMainInfo.addSaveEditComplete(Function.createDelegate(this,this.ctlMainInfo_EditComplete));this._ctlManufacturerPDFDragDrop&&this._ctlManufacturerPDFDragDrop.getData();Rebound.GlobalTrader.Site.Pages.EndUserUnderTakingPDF.callBaseMethod(this,"goInit")},dispose:function(){this.isDisposed||(this._ctlPageTitle&&this._ctlPageTitle.dispose(),this._ctlMainInfo&&this._ctlMainInfo.dispose(),this._ctlManufacturerEXCELDragDrop&&this._ctlManufacturerEXCELDragDrop.dispose(),this._intSalesOrderLineID=null,this._ctlPageTitle=null,this._ctlMainInfo=null,this._ctlManufacturerPDF=null,Rebound.GlobalTrader.Site.Pages.EndUserUnderTakingPDF.callBaseMethod(this,"dispose"))},ctlMainInfo_EditComplete:function(){}};Rebound.GlobalTrader.Site.Pages.EndUserUnderTakingPDF.registerClass("Rebound.GlobalTrader.Site.Pages.EndUserUnderTakingPDF",Rebound.GlobalTrader.Site.Pages.Content,Sys.IDisposable);