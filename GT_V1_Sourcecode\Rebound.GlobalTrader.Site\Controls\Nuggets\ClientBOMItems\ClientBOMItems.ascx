<%@ Control Language="C#" CodeBehind="ClientBOMItems.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Nuggets.ClientBOMItems" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" BoxType="Standard">
    <Links>
     <%--   <ReboundUI:IconButton ID="ibtnRelease" runat="server" IconButtonMode="hyperlink"
            IconGroup="Nugget" IconTitleResource="Release" IsInitiallyEnabled="false" />
            
     <ReboundUI:IconButton ID="ibtnUnRelease" runat="server" IconButtonMode="hyperlink"
            IconGroup="Nugget" IconTitleResource="UnRelease" IsInitiallyEnabled="false" />
            --%>
        <ReboundUI:IconButton ID="ibtnAdd" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget"
            IconTitleResource="Add" IconCSSType="Add" /> 
      <%--  <ReboundUI:IconButton ID="ibtnDelete" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget"
            IconTitleResource="Delete" IconCSSType="Delete" IsInitiallyEnabled="false" />

         <ReboundUI:IconButton ID="ibtnNoBid" runat="server" IconButtonMode="hyperlink"
            IconGroup="Nugget" IconTitleResource="NoBid" IsInitiallyEnabled="false" />
            <ReboundUI:IconButton ID="ibtnRecallNoBid" runat="server" IconButtonMode="hyperlink"
            IconGroup="Nugget" IconTitleResource="RecallNoBid" IsInitiallyEnabled="false" />
         <ReboundUI:IconButton ID="ibtnNote" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="AddNewHUBFRQNote" IconCSSType="Add" IsInitiallyEnabled="false" />
  --%>
         <ReboundUI:IconButton ID="ibtnAddLineItem" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="AddLineItem" IconCSSType="Add" IsInitiallyEnabled="true" />
        <ReboundUI:IconButton ID="ibtnExportPurchaseHUB" runat="server" IconButtonMode="hyperlink"  IconGroup="Nugget" ToolTip="Add to HUBRFQ"  IconTitleResource="AddToHUBRFQ"/> 
  
    </Links>
    <Content>

       <ReboundUI:TabStrip ID="ctlTabs" runat="server">
            <TabsTemplate>
                <ReboundUI:Tab ID="ctlTabStock" runat="server" IsSelected="true" RelatedContentPanelID="pnlTabStock"
                    TitleText="CustReq" />
            </TabsTemplate>
            <TabsContent>
                <asp:Panel ID="pnlTabStock" runat="server">
                    <ReboundUI:FlexiDataTable ID="tblStock" runat="server" PanelHeight="250" AllowSelection="true" />
                </asp:Panel>
            </TabsContent>
        </ReboundUI:TabStrip>
        
         <asp:Panel ID="pnlLoadingLineDetail" runat="server" CssClass="subInfoLoading invisible">
            <%=Functions.GetGlobalResource("Misc", "Loading")%></asp:Panel>
        <asp:Panel ID="pnlLineDetailError" runat="server" CssClass="error invisible" />
        <asp:Panel ID="pnlLineDetail" runat="server" CssClass="invisible">
            <table class="threeCols">
				<tr>
					<td class="col1">
						<table cellpadding="0" cellspacing="0" border="0" class="dataItems">
						<%--	<ReboundUI:DataItemRow id="ctlCompany" runat="server" ResourceTitle="Company" />--%>
							<ReboundUI:DataItemRow id="hidCompanyID" runat="server" FieldType="Hidden" />
							<ReboundUI:DataItemRow id="hidCompanyName" runat="server" FieldType="Hidden" />
						<%--<ReboundUI:DataItemRow id="ctlContact" runat="server" ResourceTitle="Contact" />--%>
							<ReboundUI:DataItemRow id="hidContactID" runat="server" FieldType="Hidden" />
							<ReboundUI:DataItemRow id="hidContactName" runat="server" FieldType="Hidden" />
							<ReboundUI:DataItemRow id="ctlQuantity" runat="server" ResourceTitle="Quantity" />
							<ReboundUI:DataItemRow id="ctlPartNo" runat="server" ResourceTitle="PartNo" />
							<ReboundUI:DataItemRow id="ctlROHS" runat="server" ResourceTitle="ROHS" />
							<ReboundUI:DataItemRow id="hidROHS" runat="server" FieldType="Hidden" />
							<ReboundUI:DataItemRow id="ctlCustomerPart" runat="server" ResourceTitle="CustomerPartNo" />
							<ReboundUI:DataItemRow id="ctlManufacturer" runat="server" ResourceTitle="Manufacturer" />
							<ReboundUI:DataItemRow id="hidManufacturer" runat="server" FieldType="Hidden" />
							<ReboundUI:DataItemRow id="hidManufacturerNo" runat="server" FieldType="Hidden" />
							<ReboundUI:DataItemRow id="ctlDateCode" runat="server" ResourceTitle="DateCode" />
							<ReboundUI:DataItemRow id="ctlProduct" runat="server" ResourceTitle="Product" />
                            <ReboundUI:DataItemRow id="ctlPrdDutyCodeRate" runat="server" ResourceTitle="DutyCodeRate" />
							<ReboundUI:DataItemRow id="hidProductID" runat="server" FieldType="Hidden" />
							<ReboundUI:DataItemRow id="ctlPackage" runat="server" ResourceTitle="Package" />
							<ReboundUI:DataItemRow id="hidPackageID" runat="server" FieldType="Hidden" />
							<ReboundUI:DataItemRow id="ctlPartWatch" runat="server" ResourceTitle="PartWatch" FieldType="CheckBox" />
							<ReboundUI:DataItemRow id="ctlFactorySealed" runat="server" ResourceTitle="FactorySealed" FieldType="CheckBox"  />
							
							<ReboundUI:DataItemRow id="ctlMSL" runat="server" ResourceTitle="MSL" />
                            <ReboundUI:DataItemRow id="ctlIsNoBid"  runat="server" />
                            <ReboundUI:DataItemRow id="ctlIsNoBidNotes" runat="server" ResourceTitle="NoBidNotes" />
                            <ReboundUI:DataItemRow id="hidMSL" runat="server" FieldType="Hidden" />
                             <ReboundUI:DataItemRow id="ctlBOMHeader" runat="server" ResourceTitle="IPOBOM" />
					</table>
					</td>
					<td class="col2">
						<table cellpadding="0" cellspacing="0" border="0" class="dataItems">
							<ReboundUI:DataItemRow id="ctlTargetPrice" runat="server" ResourceTitle="CustomerTargetPrice" />
							<ReboundUI:DataItemRow id="hidPrice" runat="server" FieldType="Hidden" />
							<ReboundUI:DataItemRow id="ctlCurrency" runat="server" ResourceTitle="Currency" />
							<ReboundUI:DataItemRow id="hidCurrencyID" runat="server" FieldType="Hidden" />
							<ReboundUI:DataItemRow id="ctlDateRequired" runat="server" ResourceTitle="CustDateRequired" />
							<ReboundUI:DataItemRow id="ctlClosed" runat="server" ResourceTitle="Closed" />
							<ReboundUI:DataItemRow id="ctlClosedReason" runat="server" ResourceTitle="Reason" />
							<ReboundUI:DataItemRow id="ctlUsage" runat="server" ResourceTitle="Usage" />
							<ReboundUI:DataItemRow id="hidUsageID" runat="server" FieldType="Hidden" />
							<ReboundUI:DataItemRow id="ctlBOM" runat="server" ResourceTitle="BOMChk" FieldType="CheckBox" />
							<ReboundUI:DataItemRow id="ctlBOMName" runat="server" ResourceTitle="BOMName" />
							<ReboundUI:DataItemRow id="ctlNotes" runat="server" ResourceTitle="CustomerNotes" />
							<ReboundUI:DataItemRow id="ctlInstructions" runat="server" ResourceTitle="InternalNotes" />
						    <ReboundUI:DataItemRow id="hidDisplayStatus" runat="server" FieldType="Hidden" />
						    <ReboundUI:DataItemRow id="hidBOMID" runat="server" FieldType="Hidden" />
						    <ReboundUI:DataItemRow id="hidBOMHeaderDisplayStatus" runat="server" FieldType="Hidden" />
						   <%-- <ReboundUI:DataItemRow id="ctlBOMHeader" runat="server" ResourceTitle="IPOBOM" />--%>
						     <ReboundUI:DataItemRow id="ctlPQA" runat="server" ResourceTitle="PQA" FieldType="CheckBox" />
						     <ReboundUI:DataItemRow id="ctlObsolete" runat="server" ResourceTitle="Obsolete" FieldType="CheckBox"/>
						     <ReboundUI:DataItemRow id="ctlLastTimeBuy" runat="server"  ResourceTitle="LastTimeBuy" FieldType="CheckBox"/>
						</table>
					</td>
					<td class="col3">
					<table cellpadding="0" cellspacing="0" border="0" class="dataItems">
					        
					       
							
							
							<ReboundUI:DataItemRow id="ctlRefirbsAcceptable" runat="server" ResourceTitle="RefirbsAcceptable" FieldType="CheckBox"/>
							<ReboundUI:DataItemRow id="ctlTestingRequired" runat="server"  ResourceTitle="TestingRequired" FieldType="CheckBox"/>	
                        
                            <ReboundUI:DataItemRow id="ctlAlternativesAccepted" runat="server" ResourceTitle="AlternativesAccepted" FieldType="CheckBox"/>
							<ReboundUI:DataItemRow id="ctlRepeatBusiness" runat="server"  ResourceTitle="RepeatBusiness" FieldType="CheckBox"/>
                        						
							<ReboundUI:DataItemRow id="ctlTargetSellPrice" runat="server" ResourceTitle="TargetSellPrice" FieldType="Hidden"/>							
							<ReboundUI:DataItemRow id="ctlCompetitorBestoffer" runat="server" ResourceTitle="CompetitorBestoffer" />
							
							<ReboundUI:DataItemRow id="ctlTargetSellPriceHidden" runat="server" FieldType="Hidden"  />							
							<ReboundUI:DataItemRow id="ctlCompetitorBestofferHidden" runat="server" FieldType="Hidden"  />
							
							<ReboundUI:DataItemRow id="ctlCustomerDecisionDate" runat="server" ResourceTitle="CustomerDecisionDate" />
							<ReboundUI:DataItemRow id="ctlRFQClosingDate" runat="server" ResourceTitle="RFQClosingDate" />
							<ReboundUI:DataItemRow id="ctlQuoteValidityRequiredHid" runat="server" FieldType="Hidden"/>	
							<ReboundUI:DataItemRow id="ctlQuoteValidityRequired" runat="server" ResourceTitle="QuoteValidityRequired"/>							
							<ReboundUI:DataItemRow id="ctlType" runat="server" ResourceTitle="Type" />
							<ReboundUI:DataItemRow id="ctlTypeHid" runat="server" ResourceTitle="Type" FieldType="Hidden"/>
							<ReboundUI:DataItemRow id="ctlOrderToPlace" runat="server" ResourceTitle="OrderToPlace" FieldType="CheckBox" />
							<ReboundUI:DataItemRow id="ctlRequirementforTraceability" runat="server" ResourceTitle="RequirementforTraceability" />
							<ReboundUI:DataItemRow id="ctlRequirementforTraceabilityHid" runat="server" FieldType="Hidden" />
						  <ReboundUI:DataItemRow id="ctlEAU" runat="server" ResourceTitle="EAU" />
						</table>
					</td>
				</tr>
			</table>
        </asp:Panel>
    </Content>
   <Forms>
       <%-- <ReboundForm:BOMItems_Confirm ID="ctlConfirm" runat="server" />--%>
        <ReboundForm:ClientBOMItems_Add ID="ctlAdd" runat="server" />
        <%--<ReboundForm:BOMItems_Delete ID="ctlDelete" runat="server" />
        <ReboundForm:BOMItems_UnRelease ID="ctlUnRelease" runat="server" />
        <ReboundForm:BOMItems_NoBidConfirm ID="BOMItems_NoBidConfirm" runat="server" />
        <ReboundForm:BOMItems_AddExpedite ID="ctlExpedite" runat="server" />--%>
        <ReboundForm:BOMImport_Form ID="ctlAddLineItem" runat="server" />
       <ReboundForm:ClientBOMItems_AddToHUBRFQ ID="ctlAddToHUBRFQ" runat="server" />
    </Forms>
</ReboundUI_Nugget:DesignBase>
