///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 30.03.2010:
// - ensure line is fully loaded before we allow edits (to stop the wrong data being
//   edited)
//
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");

Rebound.GlobalTrader.Site.Controls.Nuggets.CRMAReceivingLines = function(element) { 
	Rebound.GlobalTrader.Site.Controls.Nuggets.CRMAReceivingLines.initializeBase(this, [element]);
	this._intCRMAID = -1;
	this._intInvoiceLineAllocationID = -1;
	this._intCRMALineID = -1;
	this._intLineCount = 0;
	this._blnLineLoaded = false;
	this._intGlobalClientNo = -1;
	this._serialExist = false;
	this._reqSerialNo = false;
};

Rebound.GlobalTrader.Site.Controls.Nuggets.CRMAReceivingLines.prototype = {

	get_intCRMAID: function() { return this._intCRMAID; }, set_intCRMAID: function(v) { if (this._intCRMAID !== v) this._intCRMAID = v; },
	get_intLineID: function() { return this._intInvoiceLineAllocationID; }, set_intLineID: function(v) { if (this._intInvoiceLineAllocationID !== v) this._intInvoiceLineAllocationID = v; },
	get_ibtnReceive: function() { return this._ibtnReceive; }, set_ibtnReceive: function(v) { if (this._ibtnReceive !== v)  this._ibtnReceive = v; }, 
	get_tblAll: function() { return this._tblAll; }, set_tblAll: function(v) { if (this._tblAll !== v)  this._tblAll = v; }, 
	get_hypPrev: function() { return this._hypPrev; }, set_hypPrev: function(v) { if (this._hypPrev !== v)  this._hypPrev = v; }, 
	get_hypNext: function() { return this._hypNext; }, set_hypNext: function(v) { if (this._hypNext !== v)  this._hypNext = v; }, 
	get_lblLineNumber: function() { return this._lblLineNumber; }, set_lblLineNumber: function(v) { if (this._lblLineNumber !== v)  this._lblLineNumber = v; }, 
	get_pnlLineDetail: function() { return this._pnlLineDetail; }, set_pnlLineDetail: function(v) { if (this._pnlLineDetail !== v)  this._pnlLineDetail = v; }, 
	get_pnlLoadingLineDetail: function() { return this._pnlLoadingLineDetail; }, set_pnlLoadingLineDetail: function(v) { if (this._pnlLoadingLineDetail !== v)  this._pnlLoadingLineDetail = v; }, 
	get_pnlLineDetailError: function () { return this._pnlLineDetailError; }, set_pnlLineDetailError: function (v) { if (this._pnlLineDetailError !== v) this._pnlLineDetailError = v; },
	get_serialExist: function () { return this._serialExist; }, set_serialExist: function (v) { if (this._serialExist !== v) this._serialExist = v; },
	get_reqSerialNo: function () { return this._reqSerialNo; }, set_reqSerialNo: function (v) { if (this._reqSerialNo !== v) this._reqSerialNo = v; },
	
	addSaveReceiveComplete: function(handler) { this.get_events().addHandler("SaveReceiveComplete", handler); },
	removeSaveReceiveComplete: function(handler) { this.get_events().removeHandler("SaveReceiveComplete", handler); },
	onSaveReceiveComplete: function() { 
		var handler = this.get_events().getHandler("SaveReceiveComplete");
		if (handler) handler(this, Sys.EventArgs.Empty); 
	},
		
	initialize: function () {
	    
		Rebound.GlobalTrader.Site.Controls.Nuggets.CRMAReceivingLines.callBaseMethod(this, "initialize");	
		
		//data
		this._strDataPath = "controls/Nuggets/CRMAReceivingLines";
		this._strDataObject = "CRMAReceivingLines";
		
		//nugget events
		this.addRefreshEvent(Function.createDelegate(this, this.getData));
		
		//other controls
		this._tblAll.addSelectedIndexChanged(Function.createDelegate(this, this.tbl_SelectedIndexChanged));
		$addHandler(this._hypPrev, "click", Function.createDelegate(this, this.prevLine));
		$addHandler(this._hypNext, "click", Function.createDelegate(this, this.nextLine));

		//add form
		if (this._ibtnReceive) {
		    $R_IBTN.addClick(this._ibtnReceive, Function.createDelegate(this, this.showReceiveForm));
			this._frmReceive = $find(this._aryFormIDs[0]);
			this._frmReceive.addCancel(Function.createDelegate(this, this.hideReceiveForm));
			this._frmReceive.addSaveComplete(Function.createDelegate(this, this.saveReceiveComplete));

			this._frmConfirm = $find(this._aryFormIDs[1]);
			this._frmConfirm.addNotConfirmed(Function.createDelegate(this, this.cancelConfirm));
			this._frmConfirm.addSaveComplete(Function.createDelegate(this, this.saveConfirmComplete));
	    }
		
		this.getData();
		$R_FN.showElement(this._pnlLineDetail, false);
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this._hypPrev) $clearHandlers(this._hypPrev);
		if (this._hypNext) $clearHandlers(this._hypNext);
		if (this._ibtnReceive) $R_IBTN.clearHandlers(this._ibtnReceive);
		if (this._frmReceive) this._frmReceive.dispose();
		if (this._tblAll) this._tblAll.dispose();
		this._hypPrev = null;
		this._hypNext = null;
		this._ibtnReceive = null;
		this._frmReceive = null;
		this._intCRMAID = null;
		this._intLineID = null;
		this._tblAll = null;
		this._lblLineNumber = null;
		this._pnlLineDetail = null;
		this._pnlLoadingLineDetail = null;
		this._pnlLineDetailError = null;
		this._intInvoiceLineAllocationID = null;
		this._intCRMALineID = null;
		this._intLineCount = null;
		this._intGlobalClientNo = null;
		this._serialExist = null;
		this._reqSerialNo = null;
		Rebound.GlobalTrader.Site.Controls.Nuggets.CRMAReceivingLines.callBaseMethod(this, "dispose");
	},
	
	getData: function () {
	    
		this.enableEditButtons(false);
		$R_FN.showElement(this._pnlLineDetail, false);
		this.showLoading(true);
		var obj = new Rebound.GlobalTrader.Site.Data();
		obj.set_PathToData(this._strDataPath);
		obj.set_DataObject(this._strDataObject);
		obj.set_DataAction("GetLines");
		obj.addParameter("id", this._intCRMAID);
		obj.addDataOK(Function.createDelegate(this, this.getDataOK));
		obj.addError(Function.createDelegate(this, this.getDataError));
		obj.addTimeout(Function.createDelegate(this, this.getDataError));
		$R_DQ.addToQueue(obj);
		$R_DQ.processQueue();
		obj = null;
	},

	getDataOK: function(args) { 
		this.showLoading(false);
		var result = args._result;
		var blnHasRows = false;
		this._tblAll.clearTable();
		if (result.Lines) {
			for (var i = 0; i < result.Lines.length; i++) {
				var row = result.Lines[i];
				var aryData = [
					$R_FN.writeDoubleCellValue($RGT_nubButton_Stock(row.StockNo, row.Part, row.ROHS), $R_FN.setCleanTextValue(row.CustomerPart))
					, $R_FN.writeDoubleCellValue($RGT_nubButton_Manufacturer(row.ManufacturerNo, row.Manufacturer, row.MfrAdvisoryNotes), $R_FN.setCleanTextValue(row.DC))
					, $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Product), $R_FN.setCleanTextValue(row.Package))
					, $R_FN.writeDoubleCellValue(row.Quantity, row.Received)
					, row.Outstanding
					, $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.ReturnDate), $R_FN.setCleanTextValue(row.Reason))
				];
				var objExtra = {Outstanding:Number.parseLocale(row.Outstanding.toString()), ILAID:row.ILAID};
				this._tblAll.addRow(aryData, row.ID, row.ID == this._intCRMALineID, objExtra);
				blnHasRows = true;
				row = null;
			}
		}
		this._tblAll.resizeColumns();
		this.showContent(true);
		this.showContentLoading(false);
		this.showNoData(!blnHasRows);
	},
	
	getDataError: function(args) {
		this.showError(true, args.get_ErrorMessage());
	},
	
	enableEditButtons: function(bln) {
		if (bln) {
	    	if (this._ibtnReceive) $R_IBTN.enableButton(this._ibtnReceive, this._tblAll.getSelectedExtraData().Outstanding > 0 && this._blnLineLoaded);
		} else {
			if (this._ibtnReceive) $R_IBTN.enableButton(this._ibtnReceive, false);
		}
	},
	
	tbl_SelectedIndexChanged: function() {
		this.enableEditButtons(true);
		this._intInvoiceLineAllocationID = this._tblAll.getSelectedExtraData().ILAID;
		this._intCRMALineID = this._tblAll._varSelectedValue;
		this._intLineCount = this._tblAll.countRows();
		this.getLineData();
		//$R_FN.scrollPageToElement(this._element);
	},

	prevLine: function() {
		var intNewIndex = this._tblAll._intSelectedIndex - 1;
		if (intNewIndex < 0) return;
		this._tblAll.selectRow(intNewIndex, true);
	},
	
	nextLine: function() {
		var intNewIndex = this._tblAll._intSelectedIndex + 1;
		if (intNewIndex >= this._intLineCount) return;
		this._tblAll.selectRow(intNewIndex, true);
	},

	getLineData: function() {
		this.showLoading(true);
		this._blnLineLoaded = false;
		this.enableEditButtons(false);
		$R_FN.showElement(this._pnlLoadingLineDetail, true);
		$R_FN.showElement(this._pnlLineDetailError, false);
		$R_FN.showElement(this._pnlLineDetail, false);
		var obj = new Rebound.GlobalTrader.Site.Data();
		obj.set_PathToData("Controls/Nuggets/CRMALines");
		obj.set_DataObject("CRMALines");
		obj.set_DataAction("GetData");
		obj.addParameter("id", this._intCRMALineID);
		obj.addDataOK(Function.createDelegate(this, this.getLineDataOK));
		obj.addError(Function.createDelegate(this, this.getLineDataError));
		obj.addTimeout(Function.createDelegate(this, this.getLineDataError));
		$R_DQ.addToQueue(obj);
		$R_DQ.processQueue();
		obj = null;
	},

	getLineDataError: function(args) {
		this.showLoading(false);
		$R_FN.showElement(this._pnlLoadingLineDetail, false);
		$R_FN.showElement(this._pnlLineDetailError, true);
		$R_FN.setInnerHTML(this._pnlLineDetailError, args.get_ErrorMessage());
	},

	getLineDataOK: function(args) {
		this.showLoading(false);
		$R_FN.showElement(this._pnlLineDetailError, false);
		var result = args._result;
		this.setFieldValue("ctlQuantityOrdered", result.Quantity);
		this.setFieldValue("ctlQuantityReceived", result.Received);
		this.setFieldValue("ctlQuantityOutstanding", Number.parseLocale(result.Quantity.toString()) - Number.parseLocale(result.Received.toString()));
		this.setFieldValue("ctlPartNo", $R_FN.writePartNo(result.Part, result.ROHS));
		this.setFieldValue("ctlReason", $R_FN.setCleanTextValue(result.Reason));
		this.setFieldValue("ctlManufacturer", $RGT_nubButton_Manufacturer(result.ManufacturerNo, result.Manufacturer, result.MfrAdvisoryNotes));
		this.setFieldValue("hidManufacturer", $R_FN.setCleanTextValue(result.Manufacturer));
		this.setFieldValue("hidManufacturerNo", result.ManufacturerNo);
		this.setFieldValue("ctlDateCode", $R_FN.setCleanTextValue(result.DC));
		this.setFieldValue("ctlCustomerPart", $R_FN.setCleanTextValue(result.CustomerPart));
		this.setFieldValue("ctlProduct", $R_FN.setCleanTextValue(result.Product));
		this.setFieldValue("hidProductNo", result.ProductNo);
		this.setFieldValue("ctlPackage", $R_FN.setCleanTextValue(result.Package));
		this.setFieldValue("hidPackageNo", result.PackageNo);
		this.setFieldValue("ctlReturnDate", result.ReturnDate);
		this.setFieldValue("ctlROHS", $R_FN.writeROHS(result.ROHS));
		this.setFieldValue("hidROHS", result.ROHS);
		this.setFieldValue("ctlLineNotes", $R_FN.setCleanTextValue(result.LineNotes));
		this._reqSerialNo = result.ReqSerialNo;
		
		$R_FN.showElement(this._pnlLineDetail, true);
		$R_FN.showElement(this._pnlLoadingLineDetail, false);
		$R_FN.setInnerHTML(this._lblLineNumber, String.format($R_RES.LineXOfY, this._tblAll._intSelectedIndex + 1, this._intLineCount));
		this._blnLineLoaded = true;
		this.enableEditButtons(true);
	},
	
	showReceiveForm: function() {
		this._frmReceive._intCRMAID = this._intCRMAID;
		this._frmReceive._intCRMALineID = this._intCRMALineID;
		this._frmReceive._intInvoiceLineAllocationID = this._intInvoiceLineAllocationID;
		this._frmReceive._intQuantityOutstanding = Number.parseLocale(this.getFieldValue("ctlQuantityOutstanding").toString());
		this._frmReceive._intGlobalClientNo = this._intGlobalClientNo;
		this._frmReceive._reqSerialNo = this._reqSerialNo;
		this.showForm(this._frmReceive, true);
	},
	
	hideReceiveForm: function () {
	    if (this._frmReceive._countSerialRecords > 0) {
	        this.showForm(this._frmConfirm, true);
	        this._frmConfirm._intInvoiceLineID = this._frmReceive._intInvoiceLineID;
	     }
	    else {
	        this.showForm(this._frmReceive, false);
	        this._tblAll.resizeColumns();
	    }
		//this.showForm(this._frmReceive, false);
		//this._tblAll.resizeColumns();
	},
	
	saveReceiveComplete: function() {
		this.hideReceiveForm();
		this.getData();
		this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
		this.onSaveReceiveComplete();
	},
	hideConfirmForm: function () {
	    this.showForm(this._frmConfirm, false);
	},

	cancelConfirm: function () {
	    this.hideConfirmForm();
	},
	saveConfirmComplete: function () {
	    this.hideConfirmForm();
	}
		
};

Rebound.GlobalTrader.Site.Controls.Nuggets.CRMAReceivingLines.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.CRMAReceivingLines", Rebound.GlobalTrader.Site.Controls.Nuggets.Base);
