﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Communications" xml:space="preserve">
    <value>Kommunikation-Startseit</value>
  </data>
  <data name="Communications_MailMessageGroups" xml:space="preserve">
    <value>Mail Message Groups</value>
  </data>
  <data name="Profile_MailMessages" xml:space="preserve">
    <value>Post-Mitteilungen</value>
  </data>
  <data name="Profile_ToDo" xml:space="preserve">
    <value>Zu Liste tun</value>
  </data>
  <data name="Contact" xml:space="preserve">
    <value>Kontakt-Startseit</value>
  </data>
  <data name="Contact_CompanyAdd" xml:space="preserve">
    <value>Addieren Sie New Company</value>
  </data>
  <data name="Contact_CompanyBrowse" xml:space="preserve">
    <value>Grasen Sie Firmen</value>
  </data>
  <data name="Contact_CompanyDetail" xml:space="preserve">
    <value>Firma-Sonderkommando</value>
  </data>
  <data name="Contact_ContactDetail" xml:space="preserve">
    <value>Kontakt-Sonderkommando</value>
  </data>
  <data name="Contact_ManufacturerAdd" xml:space="preserve">
    <value>Addieren Sie neuen Hersteller</value>
  </data>
  <data name="Contact_ManufacturerBrowse" xml:space="preserve">
    <value>Grasen Sie Hersteller</value>
  </data>
  <data name="Contact_ManufacturerDetail" xml:space="preserve">
    <value>Hersteller-Sonderkommando</value>
  </data>
  <data name="Home" xml:space="preserve">
    <value>Startseit</value>
  </data>
  <data name="Orders" xml:space="preserve">
    <value>Aufträge-Startseit</value>
  </data>
  <data name="Orders_CreditNoteAdd" xml:space="preserve">
    <value>Addieren Sie neue Kreditnote</value>
  </data>
  <data name="Orders_CreditNoteBrowse" xml:space="preserve">
    <value>Grasen Sie Kreditnoten</value>
  </data>
  <data name="Orders_CustomerRequirementAdd" xml:space="preserve">
    <value>Addieren Sie neue Anforderung</value>
  </data>
  <data name="Orders_CustomerRequirementBrowse" xml:space="preserve">
    <value>Grasen Sie Kunden-Anforderungen</value>
  </data>
  <data name="Orders_CustomerRMAAdd" xml:space="preserve">
    <value>Addieren Sie neuen Kunden RMA</value>
  </data>
  <data name="Orders_CustomerRMABrowse" xml:space="preserve">
    <value>Grasen Sie Kunden RMAs</value>
  </data>
  <data name="Orders_DebitNoteAdd" xml:space="preserve">
    <value>Addieren Sie neue Belastungsanzeige</value>
  </data>
  <data name="Orders_DebitNoteBrowse" xml:space="preserve">
    <value>Grasen Sie Belastungsanzeigen</value>
  </data>
  <data name="Orders_InvoiceAdd" xml:space="preserve">
    <value>Addieren Sie neue Rechnung</value>
  </data>
  <data name="Orders_InvoiceBrowse" xml:space="preserve">
    <value>Rechnungen</value>
  </data>
  <data name="Orders_PurchaseOrderAdd" xml:space="preserve">
    <value>Addieren Sie neuen Kaufauftrag</value>
  </data>
  <data name="Orders_PurchaseOrderBrowse" xml:space="preserve">
    <value>Grasen Sie Kaufaufträge</value>
  </data>
  <data name="Orders_PurchaseRequisitionBrowse" xml:space="preserve">
    <value>Kauf-Forderungen</value>
  </data>
  <data name="Orders_QuoteAdd" xml:space="preserve">
    <value>Addieren Sie neuen Preisangabe</value>
  </data>
  <data name="Orders_QuoteBrowse" xml:space="preserve">
    <value>Preisangaben</value>
  </data>
  <data name="Orders_SalesOrderAdd" xml:space="preserve">
    <value>Addieren Sie neuen Verkaufs-Auftrag</value>
  </data>
  <data name="Orders_SalesOrderBrowse" xml:space="preserve">
    <value>Grasen Sie Verkaufs-Aufträge</value>
  </data>
  <data name="Orders_Sourcing" xml:space="preserve">
    <value>Auftreten</value>
  </data>
  <data name="Orders_SupplierRMAAdd" xml:space="preserve">
    <value>Addieren Sie neuen Lieferanten RMA</value>
  </data>
  <data name="Orders_SupplierRMABrowse" xml:space="preserve">
    <value>Grasen Sie Lieferanten RMAs</value>
  </data>
  <data name="Reports" xml:space="preserve">
    <value>Reports-Startseit</value>
  </data>
  <data name="Reports_ReportDetail" xml:space="preserve">
    <value>Berichten Sie über Detail</value>
  </data>
  <data name="Setup" xml:space="preserve">
    <value>Einstellung-Startseit</value>
  </data>
  <data name="Setup_CompanyDetails_Country" xml:space="preserve">
    <value>Einstellungen-firma Einstellung - Länder</value>
  </data>
  <data name="Setup_CompanyDetails_Currency" xml:space="preserve">
    <value>Einstellungen-firma Einstellung - Währungen</value>
  </data>
  <data name="Setup_CompanyDetails_Division" xml:space="preserve">
    <value>Einstellungen-firma Einstellung - Abteilungen</value>
  </data>
  <data name="Setup_CompanyDetails_Product" xml:space="preserve">
    <value>Einstellungen-firma Einstellung - Produkte</value>
  </data>
  <data name="Setup_CompanyDetails_SequenceNumber" xml:space="preserve">
    <value>Einstellungen-firma Einstellung - Folgenummern</value>
  </data>
  <data name="Setup_CompanyDetails_ShippingMethod" xml:space="preserve">
    <value>Einstellungen-firma Einstellung - Verschiffen-Methoden</value>
  </data>
  <data name="Setup_CompanyDetails_SourcingLinks" xml:space="preserve">
    <value>Einstellungen-firma Einstellung - Auftreten-Verbindungen</value>
  </data>
  <data name="Setup_GlobalSettings_StockLogReason" xml:space="preserve">
    <value>Einstellungs-globale Einstellungen - auf lagermaschinenbordbuch-Gründe</value>
  </data>
  <data name="Setup_CompanyDetails_Tax" xml:space="preserve">
    <value>Einstellungen-firma Einstellung - Steuern</value>
  </data>
  <data name="Setup_CompanyDetails_Team" xml:space="preserve">
    <value>Einstellungen-firma Einstellung - Mannschaften</value>
  </data>
  <data name="Setup_CompanyDetails_Terms" xml:space="preserve">
    <value>Einstellungen-firma Einstellung - Ausdrücke</value>
  </data>
  <data name="Setup_CompanyDetails_User" xml:space="preserve">
    <value>Einstellungen-firma Einstellung - Benutzer</value>
  </data>
  <data name="Setup_CompanyDetails_Warehouse" xml:space="preserve">
    <value>Einstellungen-firma Einstellung - Lager</value>
  </data>
  <data name="Setup_GlobalSettings_CommunicationLogType" xml:space="preserve">
    <value>Einstellungen Setupcompany - Kommunikations-Maschinenbordbuch-Arten</value>
  </data>
  <data name="Setup_GlobalSettings_CompanyType" xml:space="preserve">
    <value>Einstellungs-globale Einstellungen - Firma-Arten</value>
  </data>
  <data name="Setup_GlobalSettings_IndustryType" xml:space="preserve">
    <value>Einstellungs-globale Einstellungen - Industrie-Arten</value>
  </data>
  <data name="Setup_GlobalSettings_MasterCountryList" xml:space="preserve">
    <value>Einstellungs-globale Einstellungen - Vorlagenland-Liste</value>
  </data>
  <data name="Setup_GlobalSettings_MasterCurrencyList" xml:space="preserve">
    <value>Einstellungs-globale Einstellungen - Vorlagenwährung-Liste</value>
  </data>
  <data name="Setup_GlobalSettings_Package" xml:space="preserve">
    <value>Einstellungs-globale Einstellungen - Pakete</value>
  </data>
  <data name="Setup_GlobalSettings_ProductType" xml:space="preserve">
    <value>Einstellungs-globale Einstellungen - Produkt-Arten</value>
  </data>
  <data name="Setup_GlobalSettings_Reason" xml:space="preserve">
    <value>Einstellungs-globale Einstellungen - Anführungsstrich-nahe Gründe</value>
  </data>
  <data name="Setup_Personal_UserProfile" xml:space="preserve">
    <value>Einstellungs-persönliche Einstellungen - mein Profil</value>
  </data>
  <data name="Setup_Security_Groups" xml:space="preserve">
    <value>Einstellungs-Sicherheits-Einstellungen - Sicherheits-Gruppen</value>
  </data>
  <data name="Setup_Security_Users" xml:space="preserve">
    <value>Einstellungs-Sicherheits-Einstellungen - Sicherheits-Benutzer</value>
  </data>
  <data name="Warehouse" xml:space="preserve">
    <value>Lager-Startseit</value>
  </data>
  <data name="Warehouse_GoodsInBrowse" xml:space="preserve">
    <value>Grasen Sie Waren innen</value>
  </data>
  <data name="Warehouse_LotsAdd" xml:space="preserve">
    <value>Addieren Sie neues Los</value>
  </data>
  <data name="Warehouse_LotsBrowse" xml:space="preserve">
    <value>Grasen Sie Lose</value>
  </data>
  <data name="Warehouse_LotsDetail" xml:space="preserve">
    <value>Los</value>
  </data>
  <data name="Warehouse_ReceiveCustomerRMABrowse" xml:space="preserve">
    <value>Grasen Sie empfangen Kunden RMAs</value>
  </data>
  <data name="Warehouse_ReceiveCustomerRMADetail" xml:space="preserve">
    <value>Empfangen Sie Kunden RMA</value>
  </data>
  <data name="Warehouse_ReceivePurchaseOrderBrowse" xml:space="preserve">
    <value>Grasen Sie empfangen Kaufaufträge</value>
  </data>
  <data name="Warehouse_ReceivePurchaseOrderDetail" xml:space="preserve">
    <value>Empfangen Sie Kaufauftrag</value>
  </data>
  <data name="Warehouse_ServicesAdd" xml:space="preserve">
    <value>Addieren Sie neuen Service</value>
  </data>
  <data name="Warehouse_ServicesBrowse" xml:space="preserve">
    <value>Grasen Sie Dienstleistungen</value>
  </data>
  <data name="Warehouse_ServicesDetail" xml:space="preserve">
    <value>Service</value>
  </data>
  <data name="Warehouse_ShipSalesOrderBrowse" xml:space="preserve">
    <value>Grasen Sie Schiffs-Verkaufs-Aufträge</value>
  </data>
  <data name="Warehouse_ShipSalesOrderDetail" xml:space="preserve">
    <value>Schiffs-Verkaufs-Auftrag</value>
  </data>
  <data name="Warehouse_ShipSupplierRMABrowse" xml:space="preserve">
    <value>Grasen Sie Schiffs-Lieferanten RMAs</value>
  </data>
  <data name="Warehouse_ShipSupplierRMADetail" xml:space="preserve">
    <value>Schiffs-Lieferant RMA</value>
  </data>
  <data name="Warehouse_StockAdd" xml:space="preserve">
    <value>Addieren Sie neues auf lagereinzelteil</value>
  </data>
  <data name="Warehouse_StockBrowse" xml:space="preserve">
    <value>Grasen Sie Vorrat</value>
  </data>
  <data name="Warehouse_StockDetail" xml:space="preserve">
    <value>Auf lagereinzelteil</value>
  </data>
  <data name="Orders_CreditNoteDetail" xml:space="preserve">
    <value>Kreditnote-Sonderkommando</value>
  </data>
  <data name="Orders_CustomerRequirementDetail" xml:space="preserve">
    <value>Kunden-Anforderungs-Sonderkommando</value>
  </data>
  <data name="Orders_CustomerRMADetail" xml:space="preserve">
    <value>Sonderkommando des Kunden-RMA</value>
  </data>
  <data name="Orders_DebitNoteDetail" xml:space="preserve">
    <value>Belastungsanzeige-Sonderkommando</value>
  </data>
  <data name="Orders_InvoiceDetail" xml:space="preserve">
    <value>Rechnungs-Sonderkommando</value>
  </data>
  <data name="Orders_PurchaseOrderDetail" xml:space="preserve">
    <value>Kaufauftrag-Detail</value>
  </data>
  <data name="Orders_QuoteDetail" xml:space="preserve">
    <value>Preisangaben-Sonderkommando</value>
  </data>
  <data name="Orders_SalesOrderDetail" xml:space="preserve">
    <value>Verkaufs-Auftrags--Sonderkommando</value>
  </data>
  <data name="Orders_SupplierRMADetail" xml:space="preserve">
    <value>Sonderkommando des Lieferanten-RMA</value>
  </data>
  <data name="Warehouse_GoodsInAdd" xml:space="preserve">
    <value>Fügen Sie neue Waren in der Anmerkung hinzu</value>
  </data>
  <data name="Warehouse_GoodsInDetail" xml:space="preserve">
    <value>Waren im-Sonderkommando</value>
  </data>
  <data name="Setup_CompanyDetails_MailGroups" xml:space="preserve">
    <value>Einstellungen-firma Einstellung - Post-Gruppen</value>
  </data>
  <data name="Setup_CompanyDetails_PrintedDocuments" xml:space="preserve">
    <value>Einstellungen-firma Einstellung - gedruckte Dokumente</value>
  </data>
  <data name="Setup_GlobalSettings_CountingMethod" xml:space="preserve">
    <value>Einstellungs-globale Einstellungen - Zählung-Methoden</value>
  </data>
</root>