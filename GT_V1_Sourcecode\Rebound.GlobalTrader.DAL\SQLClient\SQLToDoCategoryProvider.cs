﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Data.SqlClient;

namespace Rebound.GlobalTrader.DAL.SqlClient
{
    public class SqlToDoCategoryProvider : ToDoCategoryProvider
    {
        public override List<ToDoCategoryDetails> DropDown()
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_dropdown_ToDoCategory", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<ToDoCategoryDetails> lst = new List<ToDoCategoryDetails>();
                while (reader.Read())
                {
                    ToDoCategoryDetails obj = new ToDoCategoryDetails();
                    obj.ToDoCategoryId = GetReaderValue_Int32(reader, "ToDoCategoryId", 0);
                    obj.ToDoCategoryName = GetReaderValue_String(reader, "ToDoCategoryName", "");
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to get ToDoCategory", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
    }
}