﻿SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
/*   
===========================================================================================  
TASK			UPDATED BY			DATE			ACTION		DESCRIPTION  
[US-211441]		Trung Pham Van		24-Sep-2024		CREATE		Get Prospective Offer Details By Id
[US-211441]		Trung Pham Van		09-Oct-2024		UPDATE		Refactor script, get ihs, lytica price and fix performance
[US-211441]		Trung Pham Van		14-Oct-2024		UPDATE		Remove Round, Add Sentdate
[US-215028]		Trung Pham Van		04-Nov-2024		UPDATE		Change name of temporary table
[US-215028]		Trung Pham Van		06-Nov-2024		UPDATE		Get SentProspectiveOfferAt for customer requirement(s)
[US-215434]		Phuc Hoang			06-Nov-2024		UPDATE		[PROD Bug] The existing Lytica Price is not shown in HUBRFQ
[US-215028]		Trung Pham Van		11-Nov-2024		UPDATE		Correct mfr of requirement
[US-215028]		Trung Pham Van		12-Nov-2024		UPDATE		Add FullPart columns, Remove unused column
[US-221304]		Trung Pham Van		14-Nov-2024		Update		Add NewOfferPriceFromProspective column, get DateRelease
[US-221304]		Trung Pham Van		21-Nov-2024		Update		Convert currency, Get new prospective offer price
[US-221304]		Trung Pham Van		26-Nov-2024		UPDATE		Check data has been sent or not
===========================================================================================  
*/
CREATE OR ALTER PROCEDURE [dbo].[usp_Get_GTOffer_Details]
	@ProspectiveOfferId INT = NULL,
	@LineIds VARCHAR(MAX) = NULL,
	@MonthRange INT = 12,
	@MinOfferQty INT = 0,
	@CurPage INT = 1,
	@Rpp INT = 10
AS
BEGIN
	-- Get the client currency number
    DECLARE @ClientCurrencyNo INT, @CurrencyCode VARCHAR(MAX), @GlobalClientCurrencyNo INT
    SELECT @ClientCurrencyNo = CurrencyNo FROM tbClient WHERE ClientId = 114
	SELECT @CurrencyCode = CurrencyCode,  @GlobalClientCurrencyNo = GlobalCurrencyNo FROM tbCurrency WHERE CurrencyId = @ClientCurrencyNo

	-- Create a temporary table to store filtered customer requirements
	SELECT prol.ProspectiveOfferLineId,
		cr.CustomerRequirementId,
	    cr.Price,
	    cr.Part,
		cr.FullPart,
	    cr.ReceivedDate,
	    cr.Quantity,
	    cr.CompanyNo,
	    cr.CurrencyNo,
	    cr.BOMName,
	    cr.BOMNo,
	    cr.DLUP,
		c.GlobalCurrencyNo,
		cr.SentProspectiveOfferAt,
		mfr.ManufacturerName,
		cr.NewOfferPriceFromProspective,
		prol.Price AS ProspectiveOfferPrice
	INTO #tempGTOffers
	FROM tbCustomerRequirement cr
	JOIN tbManufacturer mfr ON cr.ManufacturerNo = mfr.ManufacturerId
	JOIN tbProspectiveOfferLines prol ON cr.FullPart = prol.FullPart
	JOIN tbCurrency c ON c.CurrencyId = cr.CurrencyNo
	WHERE prol.ProspectiveOfferLineId IN (SELECT CAST(value AS INT) FROM STRING_SPLIT(@LineIds, ','))
	  AND DATEDIFF(MONTH, cr.ReceivedDate, GETDATE()) <= @MonthRange
	  AND prol.ProspectiveOfferNo = @ProspectiveOfferId
	  AND cr.Quantity >= @MinOfferQty
	  AND cr.BOMNo IS NOT NULL;

	SELECT DISTINCT
		cr.ProspectiveOfferLineId,
	    cr.CustomerRequirementId,
	    cr.BOMNo,
	    cr.Part AS PartNo,
	    (c.CompanyName + ' (' + FORMAT(cr.ReceivedDate, 'dd/MM/yyyy') + ')') AS HUBRFQCustomer,
	    cr.Quantity AS QuantityOffered,
	    cr.Price AS UploadedOfferPrice,
		cr.GlobalCurrencyNo AS ReqGlobalCurrencyNo,
	    NULL AS GTLowestOffer,
	    NULL AS GTHighestOffer,
	    @CurrencyCode AS Currency,
	    @GlobalClientCurrencyNo AS CurrencyNo,
	    ISNULL(
			(ihs.AveragePrice / dbo.ufn_get_exchange_rate(cur.CurrencyId, ihs.DLUP)) * dbo.ufn_get_exchange_rate(@ClientCurrencyNo, GETDATE()),
			ISNULL(dbo.ufn_extract_IHS_AvgPrice(ihs.Descriptions), 0)
		) AS IHSAvgPrice,
        ISNULL(lytica.AveragePrice, 0) AS LyticaAvgPrice,
	    ql.Price AS QuotePrice,
	    ql.Quantity AS QuoteQTY,
	    sol.Price AS SOPrice,
	    sol.Quantity AS SOQTY,
	    sol.Taxable AS SOTaxable,
	    so.DateOrdered AS SODate,
	    dbo.ufn_get_taxrate(so.TaxNo, so.ClientNo, ISNULL(so.CurrencyDate, so.DateOrdered)) AS SOTaxRate,
	    cr.ManufacturerName AS Manufacturer,
	    l.EmployeeName AS FileShared,
	    1 AS IsFromGT,
		cr.SentProspectiveOfferAt,
		cr.NewOfferPriceFromProspective,
		CASE WHEN bom.DateRelease IS NOT NULL THEN FORMAT(bom.DateRelease, 'dd/MM/yyyy') ELSE '' END AS DateRelease,
		cr.CurrencyNo AS CusReqCurrencyNo,
		@ClientCurrencyNo AS ClientCurrencyNo,
		cr.ProspectiveOfferPrice AS ProspectiveOfferPrice,
		CASE WHEN logs.ProspectiveOfferLineNo IN (SELECT CAST(value AS INT) FROM STRING_SPLIT(@LineIds, ',')) THEN 1 ELSE 0 END AS IsProspectiveSent	
	FROM tbProspectiveOfferLines prol
	JOIN tbProspectiveOffers pro ON pro.ProspectiveOfferId = prol.ProspectiveOfferNo
	JOIN tbManufacturer mfr ON mfr.ManufacturerId = prol.ManufacturerNo
	JOIN #tempGTOffers cr ON cr.FullPart = prol.FullPart
	JOIN tbBOM bom ON bom.BOMId = cr.BOMNo
	JOIN tbCompany c ON bom.CompanyNo = c.CompanyId
	JOIN tbLogin l ON l.LoginId = c.Salesman
	LEFT JOIN tbProspectiveOfferLogs logs ON logs.CustomerRequirementNo = cr.CustomerRequirementId
	LEFT JOIN tbSourcingResult sr ON sr.CustomerRequirementNo = cr.CustomerRequirementId
	LEFT JOIN tbQuoteLine ql ON sr.SourcingResultId = ql.SourcingResultNo
	LEFT JOIN tbSalesOrderLine sol ON ql.QuoteLineId = sol.QuoteLineNo
	LEFT JOIN tbSalesOrder so ON sol.SalesOrderNo = so.SalesOrderId
	LEFT JOIN tbIHSparts ihs ON ihs.FullPart = prol.FullPart AND ihs.ManufacturerFullName = mfr.ManufacturerName
	LEFT JOIN tbCurrency cur ON cur.CurrencyCode = ihs.ColPriceCurrency
    --LEFT JOIN tbLyticaAPI lytica ON dbo.ufn_get_fullpart(OriginalPartSearched) = prol.Part AND lytica.Manufacturer = mfr.ManufacturerName
	OUTER APPLY (
		SELECT  TOP 1 *
			FROM tbLyticaAPI
			WHERE OriginalPartSearched = cr.Part 
			AND ISNULL(Inactive, 0) = 0
			AND (Manufacturer = cr.ManufacturerName OR Manufacturer LIKE cr.ManufacturerName + '%' OR Manufacturer LIKE [dbo].[ufn_GetFirstWord](cr.ManufacturerName) + '%')
	) lytica
	WHERE cr.ProspectiveOfferLineId IN (SELECT CAST(value AS INT) FROM STRING_SPLIT(@LineIds, ',')) AND pro.ProspectiveOfferId = @ProspectiveOfferId
	GROUP BY cr.ProspectiveOfferLineId,
		cr.CustomerRequirementId,
	    cr.BOMNo,
	    cr.Part,
		cr.FullPart,
		cr.Price,
	    c.CompanyName,
		cr.ReceivedDate,
	    cr.Quantity,
		ihs.AveragePrice,
		ihs.DLUP,
		cr.GlobalCurrencyNo,
		cur.CurrencyId,
		ihs.Descriptions,
		lytica.AveragePrice,
	    ql.Price,
	    ql.Quantity,
	    sol.Price,
	    sol.Quantity,
	    sol.Taxable,
	    so.DateOrdered,
		so.TaxNo,
		so.ClientNo,
		so.CurrencyDate,
		so.DateOrdered,
	    mfr.ManufacturerName,
	    l.EmployeeName,
		cr.SentProspectiveOfferAt,
		cr.ManufacturerName,
		cr.NewOfferPriceFromProspective,
		bom.DateRelease,
		cr.CurrencyNo,
		cr.ProspectiveOfferPrice,
		logs.ProspectiveOfferLineNo
	DROP TABLE #tempGTOffers;
END
