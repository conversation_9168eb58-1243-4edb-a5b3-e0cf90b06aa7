using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;

namespace Rebound.GlobalTrader.Site.Controls.DropDowns.Data {
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class ShortShipmentStatus : Rebound.GlobalTrader.Site.Data.DropDowns.Base {

        public override void ProcessRequest(HttpContext context) {
            SetDropDownType("ShortShipmentStatus");
            base.ProcessRequest(context);
        }

        protected override void GetData() {
            string strValue = string.Empty;
            string strquery = string.Empty;
            int? IsPartialShortShipmentStatus;
            IsPartialShortShipmentStatus = GetFormValue_NullableInt("IsPartialShortShipmentStatus");
            int? _IsPartialShortShipmentStatus24;
            _IsPartialShortShipmentStatus24 = GetFormValue_NullableInt("_IsPartialShortShipmentStatus24");

            int? _IsPartialShortShipmentStatus25;
            _IsPartialShortShipmentStatus25 = GetFormValue_NullableInt("_IsPartialShortShipmentStatus25");
            if (_IsPartialShortShipmentStatus24 >0)
            {
                strValue  = strValue + "2,3";
            }
            if (_IsPartialShortShipmentStatus25 >0)
            {
                strValue = strValue + "4,5";
            }
            if (_IsPartialShortShipmentStatus24>0 && _IsPartialShortShipmentStatus25>0)
            {
                strValue = "";
                strValue = strValue + "2,3,4,5";
            }
            strquery = strValue;

            //string strCacheOptions = CacheManager.SerializeOptions(new object[] { SessionManager.Culture });
            //string strCachedData = CacheManager.GetDropDownData(_objDropDown.ID, strCacheOptions);
            //if (string.IsNullOrEmpty(strCachedData)) {
            JsonObject jsn = new JsonObject();
                JsonObject jsnList = new JsonObject(true);
                List<BLL.ShortShipment> lst = BLL.ShortShipment.DropDown(IsPartialShortShipmentStatus, strquery, SessionManager.LoginID,SessionManager.ClientID);
                for (int i = 0; i < lst.Count; i++) {
                    JsonObject jsnItem = new JsonObject();
                    jsnItem.AddVariable("ID", lst[i].StatusId);
                    jsnItem.AddVariable("Name", lst[i].Status);
                    jsnList.AddVariable(jsnItem);
                    jsnItem.Dispose(); jsnItem = null;
                }
                lst.Clear(); lst = null;
                jsn.AddVariable("Types", jsnList);
                jsnList.Dispose(); jsnList = null;
                //CacheManager.StoreDropDown(_objDropDown.ID, strCacheOptions, jsn.Result, CacheManager.CacheExpiryType.OneWorkingDay);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            //} else {
            //    _context.Response.Write(strCachedData);
            //}
            //strCachedData = null;
        }
    }
}
