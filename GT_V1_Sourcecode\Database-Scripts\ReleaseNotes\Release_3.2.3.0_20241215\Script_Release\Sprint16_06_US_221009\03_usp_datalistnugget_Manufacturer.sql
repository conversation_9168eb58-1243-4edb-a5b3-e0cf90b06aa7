﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO
/*   
===========================================================================================  
TASK         UPDATED BY   DATE			ACTION		DESCRIPTION  
[US-221009]  An.TranTan   29-Nov-2024	Update		Prioritize restricted message  
===========================================================================================  
*/  
CREATE OR ALTER PROCEDURE [dbo].[usp_datalistnugget_Manufacturer] 
	@OrderBy INT = 1
	,@SortDir INT = 1
	,@PageIndex INT = 0
	,@PageSize INT = 10
	,@NameSearch NVARCHAR(50) = NULL
	,@CodeSearch NVARCHAR(50) = NULL
	,@GroupCode NVARCHAR(50) = NULL
	,@GroupName NVARCHAR(50) = NULL
	,@ClientID INT = 0
	WITH RECOMPILE
AS
DECLARE @StartPage INT
		,@EndPage INT;

SET @StartPage = (@PageIndex * @PageSize + 1)
SET @EndPage = ((@PageIndex + 1) * @PageSize)

;WITH cteSearch
AS (
	SELECT ManufacturerId
		,ManufacturerCode
		,ManufacturerName
		,Inactive
		,URL
		,ConflictResource
		,isnull((
				SELECT count(IHSPartsNo)
				FROM tbManufacturer
				WHERE ManufacturerId = vm.ManufacturerId
					AND Inactive = 0
					AND IHSPartsNo IS NOT NULL
				), 0) AS SystemManufacturer
		,ROW_NUMBER() OVER (
			ORDER BY --            
				CASE 
					WHEN @OrderBy = 1
						AND @SortDir = 2
						THEN ManufacturerName
					END DESC
				,CASE 
					WHEN @OrderBy = 1
						THEN ManufacturerCode
					END
				,CASE 
					WHEN @OrderBy = 2
						AND @SortDir = 2
						THEN ManufacturerName
					END DESC
				,CASE 
					WHEN @OrderBy = 2
						THEN ManufacturerName
					END
			) AS RowNum
		,ContactName AS GroupName
		,Code AS GroupCode
	FROM dbo.vwManufacturerList vm
	WHERE (
			(@NameSearch IS NULL)
			OR (
				NOT @NameSearch IS NULL
				AND FullName LIKE @NameSearch
				)
			)
		AND (
			(@CodeSearch IS NULL)
			OR (
				NOT @CodeSearch IS NULL
				AND ManufacturerCode LIKE @CodeSearch
				)
			)
		AND Inactive = 0 -- GA 23 June 2016 as Test       
		---------- for manufacturer code group AM ---------------      
		AND (
			(@GroupCode IS NULL)
			OR (
				NOT @GroupCode IS NULL
				AND Code LIKE @GroupCode
				)
			)
		AND (
			(@GroupName IS NULL)
			OR (
				NOT @GroupName IS NULL
				AND ContactName LIKE @GroupName
				)
			)
		---------- for manufacturer code group end here ---------------      
	)
SELECT *
	,(SELECT count(*) FROM cteSearch ) AS RowCnt
	,dbo.ufn_get_MfrNotes(cte.ManufacturerId,@ClientID) AS Notes
FROM cteSearch cte
WHERE cte.RowNum BETWEEN @StartPage AND @EndPage
ORDER BY cte.RowNum
GO


