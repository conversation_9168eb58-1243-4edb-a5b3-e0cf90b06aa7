﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].[usp_ImportSupplierColumnMapping_PriceQuote]                      
@SupplierNo INT,          
@ClientType_con INT    
/*    
EXEC usp_ImportSupplierColumnMapping_PriceQuote 114,3    
Select top 1 * FROM BorisGlobalTraderimports.dbo.tbImportMapping_PriceQuote order by Importdate desc  
*/   
AS                      
BEGIN                
SELECT   TOP 1    
[ManufacturerColumn],                
[PartColumn],                  
[QuantityColumn],                 
[PriceColumn],                 
[DateCodeColumn],                
[ProductColumn],                
[PackageColumn],                
-- ISNULL([CurrencyColumn],0),                
[FixedCurrencyColumn],                
[DescriptionColumn],                 
[AlternatePartColumn],                
[SupplierPartColumn],                
[ROHSColumn],         
[FileType],        
[RequirementColumn],        
[SupplierNameColumn],        
[SPQColumn],        
[MOQColumn],        
[QtyInStockColumn],         
[LeadTimeColumn],        
[OfferStatusColumn],         
[FactorySealedColumn],         
[Region]  AS RegionColumn ,    
[Last_Time_Buy] AS Last_Time_BuyColumn ,  
[Buy_Price] AS Buy_PriceColumn ,  
[Sell_Price] AS Sell_PriceColumn ,  
[Shipping_Cost] AS Shipping_CostColumn ,  
[DeliveryDateColumn],
MSLColumn
   FROM BorisGlobalTraderimports.dbo.tbImportMapping_PriceQuote WHERE [ClientID] =@SupplierNo           
   ORDER BY importDate DESC    
    
 END   
GO