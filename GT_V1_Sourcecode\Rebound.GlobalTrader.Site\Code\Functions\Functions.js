Type.registerNamespace("Rebound.GlobalTrader.Site");Rebound.GlobalTrader.Site.Functions=function(){Rebound.GlobalTrader.Site.Functions.initializeBase(this)};var $R_FN=Rebound.GlobalTrader.Site.Functions;Rebound.GlobalTrader.Site.Functions.setInnerHTML=function(n,t){n&&typeof t!="undefined"&&(t==null&&(t=""),n.innerHTML=t.toString().trim())};Rebound.GlobalTrader.Site.Functions.addOptionToListBox=function(n,t,i,r){n&&n.options.add(new Option(t,i,r))};Rebound.GlobalTrader.Site.Functions.clearListBox=function(n){if(n)for(var t=n.options.length-1;t>=0;t--)n.remove(t)};Rebound.GlobalTrader.Site.Functions.doesListBoxHaveSelection=function(n){var i,t;if(!n)return!1;for(i=!1,t=n.options.length-1;t>=0;t--)if(n.options[t].selected){i=!0;break}return i};Rebound.GlobalTrader.Site.Functions.updateAnchor=function(n,t,i){n&&(t&&(n.innerHTML=t),i&&(htp.href=i))};Rebound.GlobalTrader.Site.Functions.showElement=function(n,t,i){n&&(t?(Sys.UI.DomElement.removeCssClass(n,"invisible"),i&&Sys.UI.DomElement.addCssClass(n,i)):(Sys.UI.DomElement.addCssClass(n,"invisible"),i&&Sys.UI.DomElement.removeCssClass(n,i)))};Rebound.GlobalTrader.Site.Functions.isElementVisible=function(n){return n?!Sys.UI.DomElement.containsCssClass(n,"invisible"):!1};Rebound.GlobalTrader.Site.Functions.showElementIfVisible=function(n,t,i){n&&(Sys.UI.DomElement.containsCssClass(n,"invisible")||$R_FN.showElement(n,t,i))};Rebound.GlobalTrader.Site.Functions.toggleCssClass=function(n,t,i,r){if(n&&t&&i){if(r){var u=t;t=i;i=u;u=null}Sys.UI.DomElement.removeCssClass(n,t);Sys.UI.DomElement.addCssClass(n,i)}};Rebound.GlobalTrader.Site.Functions.isEntered=function(n){return n?(n=n.trim(),n.length>0):!1};Rebound.GlobalTrader.Site.Functions.isValidEmail=function(n){if(!n||n.trim().length==0)return!0;return/^([\w]+)(.[\w]+)*@([\w]+)(.[\w]{2,3}){1,2}$/.test(n)};Rebound.GlobalTrader.Site.Functions.isValidURL=function(n){if(!n||n.trim().length==0)return!0;return/(ftp|http|https):\/\/(\w+:{0,1}\w*@)?(\S+)(:[0-9]+)?(\/|\/([\w#!:.?+=&%@!\-\/]))?/.test(n)};Rebound.GlobalTrader.Site.Functions.formatEmail=function(n){return n?n.trim().length==0?"":(n=n.trim(),!n.startsWith("mailto:"))?String.format("mailto:{0}",n):n:""};Rebound.GlobalTrader.Site.Functions.formatURL=function(n){return n?n.trim().length==0?"":(n=n.trim(),!n.startsWith("http"))?String.format("http://{0}",n):n:""};Rebound.GlobalTrader.Site.Functions.restrictStringTo30Characters=function(n){return n.length>30?n.substring(0,30)+" ...":n};Rebound.GlobalTrader.Site.Functions.fadeElement=function(n,t,i,r,u){if(n){i||(i=0);r||(r=1);var f=[];t?(Array.add(f,new AjaxControlToolkit.Animation.ScriptAction(n,0,0,String.format("$R_FN.showElement($get('{0}'), true);",n.id))),Array.add(f,new AjaxControlToolkit.Animation.FadeInAnimation(n,$R_ELEMENT_FADE_TIME,$R_ANIMATE_FPS,i,r))):(Array.add(f,new AjaxControlToolkit.Animation.FadeOutAnimation(n,$R_ELEMENT_FADE_TIME,$R_ANIMATE_FPS,i,r)),Array.add(f,new AjaxControlToolkit.Animation.ScriptAction(n,0,0,String.format("$R_FN.showElement($get('{0}'), false);",n.id))));u&&Array.add(f,new AjaxControlToolkit.Animation.ScriptAction(n,0,0,u));AjaxControlToolkit.Animation.SequenceAnimation.play(n,0,0,f,1)}};Rebound.GlobalTrader.Site.Functions.setElementOpacity=function(n,t){n&&(t||(t=100),n.style.opacity=t/100,n.style.filter="alpha(opacity="+t+")")};Rebound.GlobalTrader.Site.Functions.showModalBG=function(n){var t=$get("modalBG");$R_FN.showElement(t,n)};Rebound.GlobalTrader.Site.Functions.getNumericalComparator=function(n){var t="=";switch(Number.parseInvariant(n.toString())){case $R_ENUM$NumericalComparisonType.EqualTo:t="=";break;case $R_ENUM$NumericalComparisonType.GreaterThan:t=">";break;case $R_ENUM$NumericalComparisonType.LessThan:t="<";break;case $R_ENUM$NumericalComparisonType.GreaterThanOrEqualTo:t=">=";break;case $R_ENUM$NumericalComparisonType.LessThanOrEqualTo:t="<="}return t};Rebound.GlobalTrader.Site.Functions.setWidthFromOneToAnother=function(n,t,i){if(n&&t){i||(i=0);var r=Sys.UI.DomElement.getBounds(n).width+i;r>=0&&t.style&&(t.style.width=String.format("{0}px",r))}};Rebound.GlobalTrader.Site.Functions.setWidth=function(n,t){n&&(t||(t=0),t>=0&&n.style&&(n.style.width=String.format("{0}px",t)))};Rebound.GlobalTrader.Site.Functions.checkNumeric=function(n){return n?n.value.trim()==""?!0:!isNaN(Number.parseLocale(n.value).toString()):!1};Rebound.GlobalTrader.Site.Functions.setToUpperCaseAfterKeyUp=function(n){if(n){var t=$R_FN.getCaretPosition(n);n.value=n.value.toUpperCase();$R_FN.setCaretPosition(n,t)}};Rebound.GlobalTrader.Site.Functions.setCaretPosition=function(n,t){if(n!=null)if(n.createTextRange){var i=n.createTextRange();i.move("character",t);i.select()}else n.selectionStart?(n.focus(),n.setSelectionRange(t,t)):n.focus()};Rebound.GlobalTrader.Site.Functions.getCaretPosition=function(n){var t,i,r;if(n!=null)return t=0,document.selection?(n.focus(),i=document.selection.createRange(),r=document.selection.createRange().text.length,i.moveStart("character",-n.value.length),t=i.text.length-r):(n.selectionStart||n.selectionStart==0)&&(t=n.selectionStart),t};Rebound.GlobalTrader.Site.Functions.clearAllSelections=function(){if(document.selection&&document.selection.clear(),window.getSelection){var n=window.getSelection();n.removeAllRanges()}};Rebound.GlobalTrader.Site.Functions.createNubButton=function(n,t,i,r,u,f,e,o){var c="&nbsp;",s,h;return typeof t!="undefined"&&t.toString().length>0&&(n||(n=""),n==""&&(n="javascript:void(0);"),i||(i="Left"),s="nubButton",i!=""&&(s+=" nubButtonAlign"+i),t=$R_FN.setCleanTextValue(t),h="",r&&(u?typeof u.toString().toLowerCase()=="string"&&(u='"'+u+'"'):u="null",h=String.format(' onmouseover="$R_PAGE._ctlToolTip.registerDynamicElement(this, {0}, {1}, {2}, {3});"',u,f,e,o?o:"null")),c=String.format('<a href="{0}" class="{1}"{2}>{3}<\/a>',n,s,h,t)),c};Rebound.GlobalTrader.Site.Functions.createNubButtonCustomClass=function(n,t,i,r,u,f,e,o,s){var l="&nbsp;",h,c;return typeof t!="undefined"&&t.toString().length>0&&(n||(n=""),n==""&&(n="javascript:void(0);"),r||(r="Left"),h="nubButton",r!=""&&(h+=" nubButtonAlign"+r),i!=""&&(h+=" "+i),t=$R_FN.setCleanTextValue(t),c="",u&&(f?typeof f.toString().toLowerCase()=="string"&&(f='"'+f+'"'):f="null",c=String.format(' onmouseover="$R_PAGE._ctlToolTip.registerDynamicElement(this, {0}, {1}, {2}, {3});"',f,e,o,s?s:"null")),l=String.format('<a href="{0}" class="{1}"{2}>{3}<\/a>',n,h,c,t)),l};Rebound.GlobalTrader.Site.Functions.createImageCheckBox=function(n){var t='<div class="imageCheckBoxDisabled">';return t+=String.format('<img style="border-width: 0px;" src="images/x.gif" class="{0}" />',n?"on":"off"),t+"<\/div>"};Rebound.GlobalTrader.Site.Functions.createStarRating=function(n){var t,i;for(n||(n="0"),n=Number.parseInvariant(n.toString()),t='<span class="starsOuter starsOuterReadOnly">',i=0;i<5;i++)t+=String.format('<span class="stars {0}">',n>i?"starsSaved":"starsEmpty"),t+='<img style="border-width: 0px; height: 15px; width: 13px;" src="images/x.gif"/>',t+="<\/span>";return t+"<\/span>"};Rebound.GlobalTrader.Site.Functions.showRedText=function(n){if(!n)return"";return'<span style="color:red">'+n+"<\/span> "};Rebound.GlobalTrader.Site.Functions.showRedBoldText=function(n){if(!n)return"";return'<span style="color:red;font-weight:bold;">'+n+"<\/span> "};Rebound.GlobalTrader.Site.Functions.showBoldText=function(n){if(!n)return"";return'<span style="font-weight:bold;">'+n+"<\/span> "};Rebound.GlobalTrader.Site.Functions.arrayToSingleString=function(n,t){return n?(t||(t="||"),n.join(t)):""};Rebound.GlobalTrader.Site.Functions.singleStringToArray=function(n,t){return n?n.trim().length==0?[]:(t||(t="||"),n.split(t)):[]};Rebound.GlobalTrader.Site.Functions.getTimestamp=function(){var n=new Date;return n.localeFormat("F")};Rebound.GlobalTrader.Site.Functions.shortDate=function(n){var t=n?new Date(Date.parseLocale(n.toString())):new Date;return t.localeFormat("d")};Rebound.GlobalTrader.Site.Functions.shortDateAndTime=function(n){var t=n?new Date(Date.parseLocale(n.toString())):new Date;return String.format("{0} {1}",t.localeFormat(Sys.CultureInfo.CurrentCulture.dateTimeFormat.ShortDatePattern),t.localeFormat(Sys.CultureInfo.CurrentCulture.dateTimeFormat.ShortTimePattern))};Rebound.GlobalTrader.Site.Functions.shortYear=function(n){if(n&&n.length>0){var t=n.match(/\d+/g),i=t[2].substring(2),r=t[1],u=t[0];return u+"/"+r+"/"+i}return""};Rebound.GlobalTrader.Site.Functions.firstDayOfMonth=function(n){var t=n?new Date(Date.parseLocale(n.toString())):new Date;return t.setDate(1),t.localeFormat(Sys.CultureInfo.CurrentCulture.dateTimeFormat.ShortDatePattern)};Rebound.GlobalTrader.Site.Functions.lastDayOfMonth=function(n){var t=n?new Date(Date.parseLocale(n.toString())):new Date;return t.setMonth(t.getMonth()+1),t.setDate(1),t.setDate(t.getDate()-1),t.localeFormat(Sys.CultureInfo.CurrentCulture.dateTimeFormat.ShortDatePattern)};Rebound.GlobalTrader.Site.Functions.oneWeekAgo=function(n){var t=n?new Date(n):new Date;return t.setDate(t.getDate()-7),t.localeFormat(Sys.CultureInfo.CurrentCulture.dateTimeFormat.ShortDatePattern)};Rebound.GlobalTrader.Site.Functions.yesterday=function(n){var t=n?new Date(n):new Date;return t.setDate(t.getDate()-1),t.localeFormat(Sys.CultureInfo.CurrentCulture.dateTimeFormat.ShortDatePattern)};Rebound.GlobalTrader.Site.Functions.getCleanTextValue=function(n){return n=(n+"").trim(),n=n.replace(/[+]/g,":PLUS:"),n.replace(/[&]/g,":AND:")};Rebound.GlobalTrader.Site.Functions.setCleanTextValue=function(n,t){return typeof n=="undefined"&&(n=""),n=(n+"").trim(),n=n.replace(/(:PLUS:)/g,"+"),n=n.replace(/(:QUOTE:)/g,'"'),n=n.replace(/((:AND:)|(&amp;))/g,"&"),t&&(n=n.replace(/(\n)/g,"<br />")),n};Rebound.GlobalTrader.Site.Functions.setCleanTextURL=function(n){return typeof n=="undefined"&&(n=""),n=(n+"").trim(),n=n.replace(/(:PLUS:)/g,"+"),n=n.replace(/(:AND:)/g,"&"),n.replace(/[+]/g,"%2B")};Rebound.GlobalTrader.Site.Functions.setCleanTextValueForBackSlash=function(n,t){return typeof n=="undefined"&&(n=""),n=(n+"").trim(),n=n.replace(/(:PLUS:)/g,"+"),n=n.replace(/(:QUOTE:)/g,'"'),n=n.replace(/((:AND:)|(&amp;))/g,"&"),n=n.replace(/(:BACKSLASH:)/g,"\\"),t&&(n=n.replace(/(\n)/g,"<br />")),n};Rebound.GlobalTrader.Site.Functions.replaceBRTags=function(n){return n||(n=""),n.replace(/((<br \/>)|(<br>)|(<br\/>))/gi,"\r\n")};Rebound.GlobalTrader.Site.Functions.getDateFromDateAndTime=function(n,t){var i=Date.parseLocale(n),r=t.split(":");return i.setHours(r[0]),i.setMinutes(r[1]),i};Rebound.GlobalTrader.Site.Functions.parseComparisonToMinMax=function(n,t){n=Number.parseLocale(n.toString());t=Number.parseLocale(t.toString());var i;switch(n){case $R_ENUM$NumericalComparisonType.EqualTo:i={Min:t,Max:t};break;case $R_ENUM$NumericalComparisonType.GreaterThan:i={Min:t+1,Max:$R_MAX};break;case $R_ENUM$NumericalComparisonType.GreaterThanOrEqualTo:i={Min:t,Max:$R_MAX};break;case $R_ENUM$NumericalComparisonType.LessThan:i={Min:$R_MIN,Max:t-1};break;case $R_ENUM$NumericalComparisonType.LessThanOrEqualTo:i={Min:$R_MIN,Max:t}}return i};Rebound.GlobalTrader.Site.Functions.getDateFromDateAndTimeFields=function(n,t,i,r){var u=Date.parseLocale(i.getFieldValue(n));return u&&(u.setHours(i.getFieldControl(t).getHours()),u.setMinutes(i.getFieldControl(t).getMinutes()),r&&(u=u.localeFormat("F"))),u};Rebound.GlobalTrader.Site.Functions.openPrintStockWindow=function(n,t,i){var r=String.format("Stockprint.aspx?{0}={1}",$R_QS_PrintObject,n);t>0&&(r+=String.format("&{0}={1}",$R_QS_GenericID,t));i&&(r+=String.format("&{0}=1",$R_QS_EmailMode));window.open(r,"winPrint","left=20,top=20,width=770,height=574,toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=yes,scrollbars=yes")};Rebound.GlobalTrader.Site.Functions.openPrintWindow=function(n,t,i){var r=String.format("Print.aspx?{0}={1}",$R_QS_PrintObject,n);t>0&&(r+=String.format("&{0}={1}",$R_QS_GenericID,t));i&&(r+=String.format("&{0}=1",$R_QS_EmailMode));window.open(r,"winPrint","left=20,top=20,width=770,height=574,toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=yes,scrollbars=yes")};Rebound.GlobalTrader.Site.Functions.openPrintQuoteWindow=function(n,t,i){var r=String.format("Print.aspx?{0}={1}",$R_QS_PrintObject,n);return t>0&&(r+=String.format("&{0}={1}",$R_QS_GenericID,t)),i&&(r+=String.format("&{0}=1",$R_QS_EmailMode)),window.open(r,"winPrint","left=20,top=20,width=770,height=574,toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=yes,scrollbars=yes")};Rebound.GlobalTrader.Site.Functions.openPrintWindowWithMultiples=function(n,t){var i=String.format("Print.aspx?{0}={1}",$R_QS_PrintObject,n);t.length>0&&(i+=String.format("&{0}={1}",$R_QS_LineIDs,t));window.open(i,"winPrint","left=20,top=20,width=770,height=574,toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=yes,scrollbars=yes")};Rebound.GlobalTrader.Site.Functions.openPrintWindowWithMultiplesforDebitandCredit=function(n,t,i){var r=String.format("Print.aspx?{0}={1}",$R_QS_PrintObject,t);i.length>0&&(r+=String.format("&{0}={1}",n,i));window.open(r,"winPrint","left=20,top=20,width=770,height=574,toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=yes,scrollbars=yes")};Rebound.GlobalTrader.Site.Functions.openReportWindow=function(n,t){var i=String.format("PrintReport.aspx?{0}={1}",$R_QS_ReportID,n);t.length>0&&(i+=String.format("&{0}={1}",$R_QS_ReportParameters,t));window.open(i,"winReport","left=20,top=20,width=770,height=574,toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=yes,scrollbars=yes")};Rebound.GlobalTrader.Site.Functions.openEmailStatusWindow=function(n,t){var i=String.format("PrintEmailStatus.aspx?{0}={1}","ies",n);t.length>0&&(i+=String.format("&{0}={1}",$R_QS_ReportParameters,t));window.open(i,"winEmailStatus","left=50,top=80,width=700,height=500,toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=yes,scrollbars=yes")};Rebound.GlobalTrader.Site.Functions.openHelpWindow=function(n){var t=String.format("Help/_PageHelp.aspx?{0}={1}",$R_QS_HelpPage,escape(n));window.open(t,"winHelp","left=20,top=20,width=750,height=574,toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=no,scrollbars=yes")};Rebound.GlobalTrader.Site.Functions.openSalesCalc=function(){var t="SaleCalculator.aspx",n=window.open(t,"winSales","left=20,top=100,width=300,height=400,toolbar=no,resizable=no,status=no,directories=no,location=no,menubar=no,scrollbars=no;");n.location=="about:blank"?(n.location.hef=t,n.focus()):n.focus()};Rebound.GlobalTrader.Site.Functions.openSalesCalcQoute=function(n,t,i,r,u,f){var o="SaleCalculator.aspx?quotecalc="+n+"&cqyt="+t+"&cbuyPrice="+i+"&cduty="+r+"&cFreight="+u+"&cMarginReq="+f+"",e=window.open(o,"winSales","left=630,top=85,width=300,height=400,toolbar=no,resizable=no,status=no,directories=no,location=no,menubar=no,scrollbars=no;");e.location=="about:blank"?(e.location.hef=o,e.focus()):e.focus()};Rebound.GlobalTrader.Site.Functions.openSalesCalcQouteEdit=function(n,t,i,r,u,f){var o="SaleCalculator.aspx?quotecalc="+n+"&cqyt="+t+"&cbuyPrice="+i+"&cduty="+r+"&cFreight="+u+"&cMarginReq="+f+"",e=window.open(o,"winSales","left=750,top=100,width=300,height=400,toolbar=no,resizable=no,status=no,directories=no,location=no,menubar=no,scrollbars=no;");e.location=="about:blank"?(e.location.hef=o,e.focus()):e.focus()};Rebound.GlobalTrader.Site.Functions.openExchangeRate=function(){var t="ExchangeRate.aspx",n=window.open(t,"winSales","left=20,top=100,width=325,height=200,toolbar=no,resizable=no,status=no,directories=no,location=no,menubar=no,scrollbars=no;");n.location=="about:blank"?(n.location.hef=t,n.focus()):n.focus()};Rebound.GlobalTrader.Site.Functions.openGermanyExchangeRate=function(){var t="GermanyExchangeRate.aspx",n=window.open(t,"winSales","left=20,top=100,width=1095,height=510,toolbar=no,resizable=no,status=no,directories=no,location=no,menubar=no,scrollbars=no;");n.location=="about:blank"?(n.location.hef=t,n.focus()):n.focus()};Rebound.GlobalTrader.Site.Functions.openClientExchangeRate=function(){var t="ClientExchangeRate.aspx",n=window.open(t,"winSales","left=20,top=100,width=375,height=230,toolbar=no,resizable=no,status=no,directories=no,location=no,menubar=no,scrollbars=no;");n.location=="about:blank"?(n.location.hef=t,n.focus()):n.focus()};Rebound.GlobalTrader.Site.Functions.openCSLsearch=function(){var t="CSL_Search.aspx",n=window.open(t,"winSales","left=20,top=100,width=1000,height=500,toolbar=no,resizable=no,status=no,directories=no,location=no,menubar=no,scrollbars=yes;");n.location=="about:blank"?(n.location.hef=t,n.focus()):n.focus()};Rebound.GlobalTrader.Site.Functions.openPowerBIProject=function(n,t,i,r){var f=n+"Home/Index/"+t.toString()+"?LoginId="+i.toString()+"&Reportno="+r,u=window.open(f,"winSales","left=25,top=100,width=1200,height=600,toolbar=no,resizable=no,status=no,directories=no,location=no,menubar=no,scrollbars=yes;");u.location=="about:blank"?(u.location.hef=f,u.focus()):u.focus()};Rebound.GlobalTrader.Site.Functions.openPowerBIProjectNewTab=function(n){var t=n;window.open(t,"_blank")};Rebound.GlobalTrader.Site.Functions.openProduct=function(){var t="ProductSearch.aspx",n=window.open(t,"winSales","left=20,top=100,width=730,height=400,toolbar=no,resizable=no,status=no,directories=no,location=no,menubar=no,scrollbars=no;");n.location=="about:blank"?(n.location.hef=t,n.focus()):n.focus()};Rebound.GlobalTrader.Site.Functions.openPDFWindow=function(n){var t=String.format("PDFs/{0}{1}.pdf",$R_QS_ReportID,n);window.open(t,"winPDF","left=20,top=20,width=750,height=574,toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=no,scrollbars=yes")};Rebound.GlobalTrader.Site.Functions.windowScrollPosition=function(){var n=document.body.scrollTop;return n==0&&(n=window.pageYOffset?window.pageYOffset:document.body.parentElement?document.body.parentElement.scrollTop:0),n};Rebound.GlobalTrader.Site.Functions.getCreateStatement=function(n,t,i){var r="$create(",f,u,e;if(r+=String.format("{0}",n),f=t?t.length>0:!1,f){for(r+=", {",u=0,e=t.length;u<e;u++)u>0&&(r+=", "),r+=String.format('"{0}": {1}',t[u][0],t[u][1]);r+="}"}else r+=", null";return r+=", null, null",r+=String.format(', $get("{0}")',i),r+");"};Rebound.GlobalTrader.Site.Functions.AsciiValue=function(n){var i,t;for(n=n.charAt(0),i=0;i<256;++i)if(t=i.toString(16),t.length==1&&(t="0"+t),t="%"+t,t=unescape(t),t==n)break;return i};Rebound.GlobalTrader.Site.Functions.scrollPageToElement=function(n,t){if(n){t||(t=-10);var i=Sys.UI.DomElement.getBounds(n).y+t;Sys.Browser.agent==Sys.Browser.Safari&&(i+=window.scrollY);setTimeout(String.format("$R_FN.finishScrollPageToElement({0})",i),0)}};Rebound.GlobalTrader.Site.Functions.finishScrollPageToElement=function(n){window.scroll(0,n)};Rebound.GlobalTrader.Site.Functions.writeDoubleCellValue=function(n,t){return typeof n=="undefined"&&(n="&nbsp;"),n||(n="&nbsp;"),n==""&&(n="&nbsp;"),typeof t=="undefined"&&(t="&nbsp;"),t||(t="&nbsp;"),t==""&&(t="&nbsp;"),String.format('<div class="doubleValueTop">{0}<\/div>{1}',n,t)};Rebound.GlobalTrader.Site.Functions.writeTriCellValue=function(n,t,i){return typeof n=="undefined"&&(n="&nbsp;"),n||(n="&nbsp;"),n==""&&(n="&nbsp;"),typeof t=="undefined"&&(t="&nbsp;"),t||(t="&nbsp;"),t==""&&(t="&nbsp;"),typeof i=="undefined"&&(i="&nbsp;"),i||(i="&nbsp;"),i==""&&(i="&nbsp;"),String.format('<div class="doubleValueTop">{0}<\/div><div class="doubleValueTop">{1}<\/div>{2}',n,t,i)};Rebound.GlobalTrader.Site.Functions.writeDoubleCellValueSourceStock=function(n,t,i){return typeof n=="undefined"&&(n="&nbsp;"),n||(n="&nbsp;"),n==""&&(n="&nbsp;"),typeof t=="undefined"&&(t="&nbsp;"),t||(t="&nbsp;"),t==""&&(t="&nbsp;"),typeof i=="undefined"&&(i="0"),i||(i="0"),i==""&&(i="0"),String.format('<div class="doubleValueTop">{0} ({2})<\/div> <div class ="doubleValueStockBottom">{1}<\/div>',n,t,i)};Rebound.GlobalTrader.Site.Functions.writeDoubleCellValueAlignRightSupplier=function(n,t,i,r){return typeof n=="undefined"&&(n="&nbsp;"),n||(n="&nbsp;"),n==""&&(n="&nbsp;"),typeof t=="undefined"&&(t="&nbsp;"),t||(t="&nbsp;"),t==""&&(t="&nbsp;"),typeof i=="undefined"&&(i="&nbsp;"),i||(i="&nbsp;"),i==""&&(i="&nbsp;"),r?String.format('<div class="doubleValueTop">{0}<img height="12" src="App_Themes/Original/images/buttons/misc/user-disabled-icon.png" /><\/div> <div class="doubleValueBotom" ><span>{1}<\/span> <span class ="doubleValueAlignRight" >{2}<\/span><\/div>',n,t,i):String.format('<div class="doubleValueTop">{0}<\/div> <div class="doubleValueBotom" ><span>{1}<\/span> <span class ="doubleValueAlignRight" >{2}<\/span><\/div>',n,t,i)};Rebound.GlobalTrader.Site.Functions.splitDoubleCellValue=function(n){var t='<div class="doubleValueTop">';return n.startsWith(t)?[n.substring(t.length,n.indexOf("<\/div>",t.length)),n.substring(n.indexOf("<\/div>",t.length)+6,n.length)]:[n]};Rebound.GlobalTrader.Site.Functions.navigateBack=function(){history.length>1?history.back():location.href="Default.aspx"};Rebound.GlobalTrader.Site.Functions.writeNumericValue=function(n){return n||(n=0),n==undefined&&(n=0),String.format("{0}",n)};Rebound.GlobalTrader.Site.Functions.findParentElementOfType=function(n,t){if(!n)return null;t=t.toUpperCase();for(var i=!1,r=!0;!i&&r;)n.tagName?n.tagName.toUpperCase()==t?i=!0:n.parentNode?n=n.parentNode:r=!1:r=!1;return i||(n=null),n};Rebound.GlobalTrader.Site.Functions.isValidNumber=function(n){if(!n)return!0;var t=0,i=!0;return t=Number.parseInvariant(n),t||(i=!1),i};Rebound.GlobalTrader.Site.Functions.formatCurrency=function(n,t,i,r){var u,f;return i||(i=5),n||(n=0),n=Number.parseInvariant(Number.parseInvariant(n.toString()).toFixed(i).toString()),u=n.localeFormat(String.format("n{0}",i)),r||(f=u.substring(0,u.lastIndexOf(Sys.CultureInfo.CurrentCulture.numberFormat.CurrencyDecimalSeparator)),f=f.replace(new RegExp(Sys.CultureInfo.CurrentCulture.numberFormat.NumberGroupSeparator,"gi"),""),u=f+u.substring(u.lastIndexOf(Sys.CultureInfo.CurrentCulture.numberFormat.CurrencyDecimalSeparator))),t&&(u+=String.format(" {0}",t)),u};Rebound.GlobalTrader.Site.Functions.setTextFilterFieldValueAndSearchType=function(n,t){t=t.toString().trim();t=="%"||t=="%%"||t=="%%%"?t="":t.startsWith("%")&&t.endsWith("%")?(t=t.substring(1,t.length-1),n.setSearchType(1)):t.startsWith("%")?(t=t.substring(1),n.setSearchType(2)):t.endsWith("%")&&(t=t.substring(0,t.length-1),n.setSearchType(0));n.setValue(t)};Rebound.GlobalTrader.Site.Functions.preventEnterKeyForControl=function(n,t){if(n){var i;return(t.keyCode&&(i=t.keyCode),t.which&&(i=t.which),i==Sys.UI.Key.enter)?!1:!0}};Rebound.GlobalTrader.Site.Functions.writeROHS=function(n){var t=$R_RES.ROHSUnknown;switch(n){case 0:t=$R_RES.ROHSUnknown;break;case 1:t=$R_RES.ROHSCompliant;break;case 2:t=$R_RES.ROHSNonCompliant;break;case 3:t=$R_RES.ROHSExempt;break;case 4:t=$R_RES.ROHSNotApplicable;break;case 5:t=$R_RES.ROHS2;break;case 6:t=$R_RES.ROHS56;break;case 7:t=$R_RES.ROHS66;break;default:t=""}return $R_FN.writePartNo(t,n)};Rebound.GlobalTrader.Site.Functions.writePartNo=function(n,t){n=$R_FN.setCleanTextValue(n);var i=n;if(t||(t=0),t=Number.parseInvariant(t.toString()),t>0&&t>=1&&t<=7&&t!=0&&t!=4){i='<div class="rohs ';strAlt="";switch(t){case 1:i+="rohsCompliant";strAlt=$R_RES.ROHSCompliant;break;case 2:i+="rohsNonCompliant";strAlt=$R_RES.ROHSNonCompliant;break;case 3:i+="rohsExempt";strAlt=$R_RES.ROHSExempt;break;case 5:i+="rohsROHS2";strAlt=$R_RES.ROHS2;break;case 6:i+="rohsROHS56";strAlt=$R_RES.ROHS56;break;case 7:i+="rohsROHS66";strAlt=$R_RES.ROHS66}i+=String.format('" title="{0}">{1}<\/div>',strAlt,n)}return i};Rebound.GlobalTrader.Site.Functions.writeSupplierName=function(n,t){var r=$R_FN.setCleanTextValue(n),i;if(t>0)for(i=0;i<5;i++)i==0&&(r+="&nbsp; "),r+=String.format('<img src="{0}" />',i<t?$RGT_STAR_IMG:$RGT_STAR_EMPTY_IMG);return r};Rebound.GlobalTrader.Site.Functions.setCookie=function(n,t,i){var r=new Date,u;r.setDate(r.getDate()+i);u=t+(i==null?"":"; expires="+r.toUTCString());document.cookie=n+"=";document.cookie=n+"="+u};Rebound.GlobalTrader.Site.Functions.getCookie=function(n){for(var r,u,i=document.cookie.split(";"),t=0;t<i.length;t++)if(r=i[t].substr(0,i[t].indexOf("=")),u=i[t].substr(i[t].indexOf("=")+1),r=r.replace(/^\s+|\s+$/g,""),r==n)return unescape(u)};Rebound.GlobalTrader.Site.Functions.deleteCookie=function(n,t){$R_FN.setCookie(n,t,-10)};Rebound.GlobalTrader.Site.Functions.registerClass("Rebound.GlobalTrader.Site.Functions");Array.prototype.sum=function(){for(var t=0,n=0,i=this.length;n<i;n++)t+=Number.parseLocale(this[n].toString());return t};Rebound.GlobalTrader.Site.Functions.openNPRPrintWindow=function(n,t){var i="";n>0&&(i+=String.format("NPRPrint.aspx?{0}={1}",$R_QS_GenericID,n));t>0&&(i+=String.format("&{0}={1}",$R_QS_NPRID,t));window.open(i,"winNPRPrint","left=20,top=20,width=870,height=674,toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=yes,scrollbars=yes")};Rebound.GlobalTrader.Site.Functions.createNubButtonNPR=function(n,t,i,r,u,f,e,o,s){var c="&nbsp;",l,a,v,h;return typeof t!="undefined"&&t.toString().length>0&&(l="javascript:void(0);",r||(r="Left"),a="topMenuRolloverLink",t=$R_FN.setCleanTextValue(t),v=String.format('&nbsp;&nbsp;<a href="javascript:void(0);" onclick="$RGT_openNPRLog($R_ENUM$PrintObject.ReportNPRLog,{0})" >Log<\/a>',n),h="",u&&(f?typeof f.toString().toLowerCase()=="string"&&(f='"'+f+'"'):f="null",h=String.format(' onmouseover="$R_PAGE._ctlToolTip.registerDynamicElement(this, {0}, {1}, {2}, {3});"',f,e,o,s?s:"null")),c=String.format('<div class="{1}"><a href="{0}" onclick="{4}" {2}>{3}<\/a>{5}<\/div>',l,a,h,t,String.format("$RGT_openNPRWindow({0},{1})",i,n),v)),c};Rebound.GlobalTrader.Site.Functions.createNubButtonNPRNugget=function(n,t,i,r,u,f,e,o,s){var c="&nbsp;",l,a,v,h;return typeof t!="undefined"&&t.toString().length>0&&(l="javascript:void(0);",a="nubButton nubButtonAlignLeft",t=$R_FN.setCleanTextValue(t),v=String.format('&nbsp;&nbsp;<a href="javascript:void(0);" onclick="$RGT_openNPRLog($R_ENUM$PrintObject.ReportNPRLog,{0})" >Log<\/a>',n),h="",u&&(f?typeof f.toString().toLowerCase()=="string"&&(f='"'+f+'"'):f="null",h=String.format(' onmouseover="$R_PAGE._ctlToolTip.registerDynamicElement(this, {0}, {1}, {2}, {3});"',f,e,o,s?s:"null")),c=String.format('<div><a href="{0}" class="{1}" onclick="{4}" {2}>{3}<\/a>{5}<\/div>',l,a,h,t,String.format("$RGT_openNPRWindow({0},{1})",i,n),null)),c};Rebound.GlobalTrader.Site.Functions.createNubButtonSTO=function(n,t,i,r,u,f,e,o,s){var c="&nbsp;",l,a,v,h;return typeof t!="undefined"&&t.toString().length>0&&(l="javascript:void(0);",r||(r="Left"),a="topMenuRolloverLink",t=$R_FN.setCleanTextValue(t),t="STO ",v=String.format('&nbsp;&nbsp;<a href="javascript:void(0);" onclick="$RGT_openNPRLog($R_ENUM$PrintObject.STO,{0})" >'+n+"<\/a>",n),h="",u&&(f?typeof f.toString().toLowerCase()=="string"&&(f='"'+f+'"'):f="null",h=String.format(' onmouseover="$R_PAGE._ctlToolTip.registerDynamicElement(this, {0}, {1}, {2}, {3});"',f,e,o,s?s:"null")),c=String.format('<div class="{1}"><a href="{0}" onclick="{4}" {2}>{3}<\/a>{5}<\/div>',l,a,h,t,String.format("$RGT_openNPRWindow({0},{1})",i,n),v)),c};Rebound.GlobalTrader.Site.Functions.createNubButtonSOShip=function(n,t,i,r,u,f){var o="&nbsp;",s,e;return typeof t!="undefined"&&t.toString().length>0&&(s="javascript:void(0);",i||(i="Left"),e="",i!=""&&(e+=" nubButtonAlign"+i),t=$R_FN.setCleanTextValue(t),o=t.toString().length<=60?'<span style="color:red">'+t+"<\/style>":String.format('{2}<br/><a onmouseover="{4}" onmouseout="{5}" class="{1}" href="{0}" id="{3}" >...<\/a>',s,e,'<span style="color:red">'+t.substring(0,59)+"<\/style>",r,u,f)),o};Rebound.GlobalTrader.Site.Functions.createNubButtonSOShipMSL=function(n,t,i,r,u,f,e){var o="&nbsp;",s="",c,h;return typeof t!="undefined"&&t.toString().length>0&&(c="javascript:void(0);",i||(i="Left"),h="",i!=""&&(h+=" nubButtonAlign"+i),e!=""&&e.toString().length>0&&(s="/MSL: "+e),t=$R_FN.setCleanTextValue(t),o=t.toString().length<=60?'<span style="color:red">'+t+s+"<\/style>":String.format('{2}<br/><a onmouseover="{4}" onmouseout="{5}" class="{1}" href="{0}" id="{3}" >...<\/a>',c,h,'<span style="color:red">'+t.substring(0,59)+s+"<\/style>",r,u,f)),(t==""||t.toString().length<=0)&&e!=""&&e.toString().length>0&&(o='<span style="color:red">MSL: '+e+"<\/style>"),o};Rebound.GlobalTrader.Site.Functions.openPrintNPRWindow=function(n,t,i){var r=String.format("Print.aspx?{0}={1}",$R_QS_PrintObject,n);t>0&&(r+=String.format("&{0}={1}",$R_QS_NPRID,t));i&&(r+=String.format("&{0}=1",$R_QS_EmailMode));window.open(r,"winPrint","left=20,top=20,width=770,height=574,toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=yes,scrollbars=yes")};Rebound.GlobalTrader.Site.Functions.openManufacturerSearch=function(n){var t=window.open(n,"winSales","left=20,top=100,width=1200,height=500,toolbar=no,resizable=no,status=no,directories=no,location=no,menubar=no,scrollbars=yes;");t.location=="supplierManufacturerSearch.aspx"?(t.location.hef=strURL,t.focus()):t.focus()};Rebound.GlobalTrader.Site.Functions.writeProductSource=function(n){var t="";switch(n){case 1:t=$R_RES.NonPreferred;break;case 2:t=$R_RES.Traceable;break;case 3:t=$R_RES.Trusted;break;default:t=""}return t};Rebound.GlobalTrader.Site.Functions.getApprovedByAndDate=function(n,t){var i='<div class="imageCheckBoxDisabled">';return i+=String.format('<img style="border-width: 0px;" src="images/x.gif" class="{0}" />',n?"on":"off"),typeof t!="undefined"&&t.toString().length>0&&(i+='&nbsp;&nbsp;<span style="font-size:9px;vertical-align:top;color:#808080;">'+t+"<\/span>"),i+"<\/div>"};Rebound.GlobalTrader.Site.Functions.showYellowText=function(n){if(!n)return"";return'<span style="background-color: rgb(255, 255, 0);">'+n+"<\/span> "};Rebound.GlobalTrader.Site.Functions.showYellowTextImportant=function(n){if(!n)return"";return'<span class="yellow-background">'+n+"<\/span> "};Rebound.GlobalTrader.Site.Functions.showLargeFonts=function(n){if(!n)return"";return'<span  class="showLargeFonts">'+n+"<\/span> "};Rebound.GlobalTrader.Site.Functions.showLargeFontsWithColor=function(n){if(!n)return"";return'<span  class="showLargeFontsWithColor">'+n+"<\/span> "};Rebound.GlobalTrader.Site.Functions.openEPRWindow=function(n,t,i,r){var u="";n>0&&(u+=String.format("EPR.aspx?{0}={1}",$R_QS_PurchaseOrderID,n));t>0&&(u+=String.format("&{0}={1}",$R_QS_EPRId,t));typeof i!="undefined"&&i.length>0&&(u+=String.format("&{0}={1}","polids",i));typeof r!="undefined"&&r.length>0&&(u+=String.format("&{0}={1}","pols",r));window.open(u,"winEPR","left=20,top=20,width=870,height=674,toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=yes,scrollbars=yes")};Rebound.GlobalTrader.Site.Functions.openCreditLimitWindow=function(n,t){var i="";n>0&&(i+=String.format("CreditLimit.aspx?{0}={1}","cm",n));t>0&&(i+=String.format("&{0}={1}","CreditLimit",t));window.open(i,"winCreditLimit","left=20,top=20,width=870,height=674,toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=yes,scrollbars=yes")};Rebound.GlobalTrader.Site.Functions.openCreditLimitLogWindow=function(n,t){var i="";n>0&&(i+=String.format("CreditLimitLog.aspx?{0}={1}","cm",n));t>0&&(i+=String.format("&{0}={1}","CreditLimit",t));window.open(i,"winCreditLimit","left=20,top=20,width=870,height=674,toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=yes,scrollbars=yes")};Rebound.GlobalTrader.Site.Functions.createNubButtonEPR=function(n,t,i,r,u,f,e,o,s){var c="&nbsp;",l,a,v,h;return typeof t!="undefined"&&t.toString().length>0&&(l="javascript:void(0);",r||(r="Left"),a="topMenuRolloverLink",i=i+"-"+t,v=String.format('&nbsp;&nbsp;<a href="javascript:void(0);" onclick="$RGT_openEPRLog($R_ENUM$PrintObject.ReportEPRLog,{0})" >Log<\/a>',t),h="",u&&(f?typeof f.toString().toLowerCase()=="string"&&(f='"'+f+'"'):f="null",h=String.format(' onmouseover="$R_PAGE._ctlToolTip.registerDynamicElement(this, {0}, {1}, {2}, {3});"',f,e,o,s?s:"null")),c=String.format('<div class="{1}"><a href="{0}" onclick="{4}" {2}>{3}<\/a>{5}<\/div>',l,a,h,i,String.format("$RGT_openEPRWindow({0},{1})",n,t),v)),c};Rebound.GlobalTrader.Site.Functions.createNubButtonCreditLimit=function(n,t,i,r,u,f,e,o,s){var c="&nbsp;",l,a,v,h;return typeof t!="undefined"&&t.toString().length>0&&(l="javascript:void(0);",r||(r="Left"),a="topMenuRolloverLink",i=i+"-"+t,v=String.format('&nbsp;&nbsp;<a href="javascript:void(0);" onclick="$RGT_openCreditLimitLog($R_ENUM$PrintObject.ReportCreditLimitLog,{0})" >Log<\/a>',t),h="",u&&(f?typeof f.toString().toLowerCase()=="string"&&(f='"'+f+'"'):f="null",h=String.format(' onmouseover="$R_PAGE._ctlToolTip.registerDynamicElement(this, {0}, {1}, {2}, {3});"',f,e,o,s?s:"null")),c=String.format('<div class="{1}"><a href="{0}" onclick="{4}" {2}>{3}<\/a>{5}<\/div>',l,a,h,i,String.format("$RGT_openCreditLimitWindow({0},{1})",n,t),v)),c};Rebound.GlobalTrader.Site.Functions.showSerialNumber=function(n,t){var i="&nbsp;",i=n;return parseInt(t)>0&&(i+=String.format(" ({0})",t)),i};Rebound.GlobalTrader.Site.Functions.showInvoiceSerialNumber=function(n,t){var i="&nbsp;",i=n;return parseInt(t)>0&&(i+=String.format("{0}",t)),i};Rebound.GlobalTrader.Site.Functions.openPrintEPRWindow=function(n,t,i){var r=String.format("Print.aspx?{0}={1}",$R_QS_PrintObject,n);t>0&&(r+=String.format("&{0}={1}",$R_QS_EPRId,t));i&&(r+=String.format("&{0}=1",$R_QS_EmailMode));window.open(r,"winPrint","left=20,top=20,width=770,height=574,toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=yes,scrollbars=yes")};Rebound.GlobalTrader.Site.Functions.openPrintWindowCustReqWithMultiples=function(n,t){var i=String.format("Print.aspx?{0}={1}",$R_QS_PrintObject,n);t>0&&(i+=String.format("&{0}={1}",$R_QS_GenericID,t));window.open(i,"winPrint","left=20,top=20,width=950,height=574,toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=yes,scrollbars=yes")};Rebound.GlobalTrader.Site.Functions.openEPRWindowEmail=function(n,t){var i="";n>0&&(i+=String.format("EPR.aspx?{0}={1}",$R_QS_PurchaseOrderID,n));t>0&&(i+=String.format("&{0}={1}",$R_QS_EPRId,t));window.open(i,"winEPR","left=20,top=20,width=870,height=674,toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=yes,scrollbars=yes");location.href=$RGT_gotoURL_PurchaseOrder(n)};Rebound.GlobalTrader.Site.Functions.openForgotPassword=function(){window.open("Forget_Password.aspx","winForgetPassword","left=20,top=20,width=500,height=400,toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=yes,scrollbars=yes")};Rebound.GlobalTrader.Site.Functions.openPrintLogWindow=function(n,t,i,r){var u=String.format("Print.aspx?{0}={1}",$R_QS_PrintObject,n);t>0&&(u+=String.format("&{0}={1}",$R_QS_GenericID,t));i&&(u+=String.format("&{0}=1",$R_QS_EmailMode));r&&(u+=String.format("&section={0}",r));window.open(u,"winPrint","left=20,top=20,width=770,height=574,toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=yes,scrollbars=yes")};Rebound.GlobalTrader.Site.Functions.showHazardous=function(n,t){n=$R_FN.setCleanTextValue(n);var i=n;return t&&(i='<div class="hazardous ',strAlt="",i+=" ",strAlt=$R_RES.HazardousMessage,i+=String.format('" title="{0}">{1}<\/div>',strAlt,n)),i};Rebound.GlobalTrader.Site.Functions.showHazardousNew=function(n,t,i){n=$R_FN.setCleanTextValue(n);var r=n;return t&&(r='<div class="hazardous ',strAlt="",r+=" ",strAlt=i,r+=String.format('" title="{0}">{1}<\/div>',strAlt,n)),r};Rebound.GlobalTrader.Site.Functions.showHazardousandOrdViaIPO=function(n,t,i){n=$R_FN.setCleanTextValue(n);var r=n;return strAlt="",(t==!0||i==!0)&&(r='<div class="hazardous ',t&&(r+=" ",strAlt=$R_RES.HazardousMessage),i&&(strAlt+="\n\n"+$R_FN.setCleanTextValue($R_RES.OrderViaIPOonlyMessage)),r+=String.format('" title="{0}">{1}<\/div>',strAlt,n)),r};Rebound.GlobalTrader.Site.Functions.showHazardousandOrdViaIPONew=function(n,t,i,r,u){n=$R_FN.setCleanTextValue(n);var f=n;return strAlt="",(t==!0||i==!0||u==!0)&&(f='<div class="hazardous ',strAlt=r.replaceAll("&#013;","\n\n"),f+=String.format('" title="{0}">{1}<\/div>',strAlt,n)),f};Rebound.GlobalTrader.Site.Functions.showProductWarning=function(n,t,i,r,u){var f="",e;return n=$R_FN.setCleanTextValue(n),e="",strAlt="",t==!0||i==!0||u==!0?(strAlt=r.replaceAll("&#013;","\n\n"),t==!0&&(f=f+'<span class="hazardous" title="'+strAlt+'"><\/span>'),i==!0&&(f=f+'<span class="hazardousIpo" title="'+strAlt+'"><\/span>'),u==!0&&(f=f+'<span class="hazardousRh" title="'+strAlt+'"><\/span>'),e+=String.format("<div>{0}{1}<div>",n,f)):e=n,e};Rebound.GlobalTrader.Site.Functions.showProductWarningIndividual=function(n,t,i,r,u,f,e){var o="",s;return n=$R_FN.setCleanTextValue(n),s="",Haz="",IPO="",Rt="",t==!0||i==!0||r==!0?(Haz=u.replace("&#013;","\n"),IPO=f.replace("&#013;","\n"),Rt=e.replace("&#013;","\n"),t==!0&&(o=o+'<span class="hazardous" title="'+Haz+'"><\/span>'),i==!0&&(o=o+'<span class="hazardousIpo" title="'+IPO+'"><\/span>'),r==!0&&(o=o+'<span class="hazardousRh" title="'+Rt+'"><\/span>'),s+=String.format("<div>{0}{1}<div>",n,o)):s=n,s};Rebound.GlobalTrader.Site.Functions.showReselPriceMessage=function(n,t){return n?t!=null&&t.length>0?String.format('<span class="ihspartstatusdoc"  title="{0}"> {1} <\/span>',t,n):n:""};Rebound.GlobalTrader.Site.Functions.getCheckboxText=function(n,t){var i='<div class="imageCheckBoxDisabled">';return i+=String.format('<img style="border-width: 0px;" src="images/x.gif" class="{0}" />',n?"on":"off"),typeof t!="undefined"&&t.toString().length>0&&(i+='&nbsp;&nbsp;<span style="font-size:9px;vertical-align:top;color:#808080;">'+t+"<\/span>"),i+"<\/div>"};Rebound.GlobalTrader.Site.Functions.setCellValueWithBackground=function(n,t){return typeof n=="undefined"&&(n=""),n=(n+"").trim(),n=n.replace(/(:PLUS:)/g,"+"),n=n.replace(/(:QUOTE:)/g,'"'),n=n.replace(/((:AND:)|(&amp;))/g,"&"),t!=""&&(n="<span class='"+t+"'>"+n+"<\/span>"),n};Rebound.GlobalTrader.Site.Functions.getSecLevel=function(n){var t="=";switch(Number.parseInvariant(n.toString())){case 1:t="My";break;case 2:t="Team";break;case 3:t="Division";break;case 4:t="Company"}return t};Rebound.GlobalTrader.Site.Functions.openDocumentTree=function(n,t,i){var u="AllDocumentInformation.aspx?DocNo="+n+"&ActionType="+t+"&DocId="+i,r=window.open(u,"winTreeView","left=20,top=100,width=450,height=570,toolbar=no,resizable=no,status=no,directories=no,location=no,menubar=no,scrollbars=no");r.location=="about:blank"?(r.location.hef=u,r.focus()):r.focus()};Rebound.GlobalTrader.Site.Functions.showGILine=function(n,t){return"&nbsp;"+(parseInt(t)>0?String.format("S({0})",n):n)};Rebound.GlobalTrader.Site.Functions.openCrossMatch=function(n,t){var r="CrossMatch.aspx?BomNo="+n+"&BomCode="+t,i=window.open(r,"wincrossmatch","directories=no,titlebar=no,toolbar=no,location=no,status=no,menubar=no,scrollbars=no,resizable=no,width=720,height=800");i.location=="about:blank"?(i.location.hef=r,i.focus()):i.focus()};Rebound.GlobalTrader.Site.Functions.openCrossMatchPage=function(){var t="CrossMatch.aspx",n=window.open(t,"wincrossmatch","directories=no,titlebar=no,toolbar=no,location=no,status=no,menubar=no,scrollbars=no,resizable=no,width=720,height=800");n.location=="about:blank"?(n.location.hef=t,n.focus()):n.focus()};Rebound.GlobalTrader.Site.Functions.showNotes=function(n,t){strName=$R_FN.setCleanTextValue(n);var i=strName;return i='<div class=" ',strAlt="",i+=" ",strAlt=$R_FN.setCleanTextValue(t),i+String.format('" title="{0}">{1} ...<\/div>',strAlt,strName)};Rebound.GlobalTrader.Site.Functions.showExportedDate=function(n,t){strName=$R_FN.setCleanTextValue(n);var i=strName;return i="<div id='divExport' class=\" ",strAlt="",i+=" ",strAlt=$R_FN.setCleanTextValue(t),i+String.format('" title="{0}">{1} <\/div>',strAlt,strName)};Rebound.GlobalTrader.Site.Functions.showIHSstatusDefi=function(n,t){n=$R_FN.setCleanTextValue(n);var i=n;return t&&(i='<div class="ihspartstatusdoc',strAlt="",i+=" ",strAlt=t,i+=String.format('" title="{0}">{1}<\/div>',strAlt,n)),i};Rebound.GlobalTrader.Site.Functions.showPOCounrtySectionDefi=function(n,t){n=$R_FN.setCleanTextValue(n);var i=n;return t&&(i="<div id='POCounrtySection' class=\"poCourntySectionWarnning",strAlt="",i+=" ",strAlt=t,i+=String.format('" title="{0}">{1}<\/div>',strAlt,n)),i};Rebound.GlobalTrader.Site.Functions.showIHSECCNCodeDefi=function(n,t){n=$R_FN.setCleanTextValue(n);var i=n;return t&&(i='<div class="ihspartstatusdoc',strAlt="",i+=" ",strAlt=t.replaceAll("<br />".trim(),""),i+=String.format('" title="{0}">{1}<\/div>',strAlt,n)),i};Rebound.GlobalTrader.Site.Functions.showRestCountry=function(n,t){n=$R_FN.setCleanTextValue(n);var i=n;return t&&(i='<div class="ihspartstatusdoc',strAlt="",i+=" ",strAlt=t,i+=String.format('" title="{0}">{1}<\/div>',strAlt,n)),i};Rebound.GlobalTrader.Site.Functions.showRestCountry=function(n,t){n=$R_FN.setCleanTextValue(n);var i=n;return t&&(i='<div class="ihspartstatusdoc',strAlt="",i+=" ",strAlt=t,i+=String.format('" title="{0}">{1}<\/div>',strAlt,n)),i};Rebound.GlobalTrader.Site.Functions.openIHSPDFWindow=function(n,t){var i=String.format("IHSPDFDocument.aspx?ihs="+n+"&iFrom="+t);window.open(i,"winIHSPDFDocument","left=20,top=20,width=900,height=400,toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=yes,scrollbars=yes")};Rebound.GlobalTrader.Site.Functions.openBomItemIHSWindow=function(n,t){var i=String.format("IHSPDFDocument.aspx?ihs="+n+"&iFrom="+t);window.open(i,"winIHSPDFDocument","left=20,top=20,width=900,height=400,toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=yes,scrollbars=yes")};Rebound.GlobalTrader.Site.Functions.openEndUserUndertakingPDFWindow=function(n){var t=String.format("EndUserUnderTakingPDF.aspx?SOLineID="+n);window.open(t,"winEndUserUndertakingPDFDocument","left=20,top=20,width=900,height=400,toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=yes,scrollbars=yes")};Rebound.GlobalTrader.Site.Functions.openCIPPDFWindow=function(n,t){var i=String.format("CIPDocumentPDF.aspx?intCertificateID="+n+"&intCompanyID="+t);window.open(i,"winCIPPDFDocument","left=20,top=20,width=900,height=400,toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=yes,scrollbars=yes")};Rebound.GlobalTrader.Site.Functions.showSupplierMessage=function(n,t){return n?t!=null&&t.length>0?String.format('<span class="hazardous"  style="background-color: yellow; background-position: right -1px; !important" title="{0}"> {1} <\/span>',t,n):n:""};Rebound.GlobalTrader.Site.Functions.openSupplierApprovalDocWindow=function(n,t,i){var r=String.format("SupplierApprovalDoc.aspx?saID="+n+"&upldTyp="+t+"&editScreen="+i);window.open(r,"winIHSPDFDocument","left=20,top=20,width=900,height=400,toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=yes,scrollbars=yes")};Rebound.GlobalTrader.Site.Functions.openSupplierApprovalImageWindow=function(n,t,i){var r=String.format("SupplierApprovalImage.aspx?saID="+n+"&upldTyp="+t+"&editScreen="+i);window.open(r,"winIHSPDFDocument","left=20,top=20,width=900,height=400,toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=yes,scrollbars=yes")};Rebound.GlobalTrader.Site.Functions.openGIImageWindow=function(n){var t=String.format("GILineImageDocument.aspx?gilId="+n);window.open(t,"winGIImageDocument","left=20,top=20,width=900,height=400,toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=yes,scrollbars=yes")};Rebound.GlobalTrader.Site.Functions.getApprovedStatus=function(n,t){var i='<div class="imageCheckBoxDisabled">';return i+=String.format('<img style="border-width: 0px;" src="images/x.gif" class="{0}" />',n==!0?"on":"off"),typeof t!="undefined"&&t.toString().length>0&&(i+='&nbsp;&nbsp;<span style="font-size:9px;vertical-align:top;color:#808080;">'+t+"<\/span>"),i+"<\/div>"};Rebound.GlobalTrader.Site.Functions.showStockAvailable=function(n,t,i){strName=$R_FN.setCleanTextValue(n);var r=strName;return r='<div class="',strAlt="",r+=" ",strAlt=$R_FN.setCleanTextValue(t),r+String.format('" title="{0}">{1} <a href="{2}" target=\'_blank\'><img  title="{0}" src=\'../../../App_Themes/Original/images/hazardous/ihspartstatuspng.png\' /><\/a><\/div>',strAlt,strName,i)};Rebound.GlobalTrader.Site.Functions.showStockAvailableNew=function(n,t,i,r,u,f,e){strName=$R_FN.setCleanTextValue(n);strAlt="";strAlt=$R_FN.setCleanTextValue(e);var o=String.format("<div class='item' title=\"{0}\">{0}<\/div>",t),s=String.format("<div class='item' title=\"{0}\">{0}<\/div>",i),h=String.format("<div class='item' title=\"{0}\">{0}<\/div>",r),c=String.format("<div class='item' title=\"{0}\">{0}<\/div>",u);return'<div class="dropdown '+String.format('" title="{0}"> {1} {2}<div class=\'dropdown-content\'>{3}{4}{5}{6}{7}<\/div><\/div>',strAlt,strName,"<span class='dropbtn' style='color: #ff6a00'>Stock Alert!<\/span>","<div class='notes'>Stock across all Clients<\/div>",o,s,h,c)};Rebound.GlobalTrader.Site.Functions.showStockAvailableHUBRFQ=function(n,t,i,r,u,f,e,o){strName=$R_FN.setCleanTextValue(n);strAlt="";strAlt=$R_FN.setCleanTextValue(e);var s='<div class="dropdown ',h=String.format("<div class='item' title=\"{0}\">{0}<\/div>",t),c=String.format("<div class='item' title=\"{0}\">{0}<\/div>",i),l=String.format("<div class='item' title=\"{0}\">{0}<\/div>",r),a=String.format("<div class='item' title=\"{0}\">{0}<\/div>",u);return s+=String.format('" title="{0}"> {1} {2}<div class=\'dropdown-content\'>{3}{4}{5}{6}{7}<\/div><\/div>',strAlt,strName,"<span class='dropbtn' style='color: #ff6a00'>Stock Alert!<\/span>","<div class='notes'>Stock across all Clients<\/div>",h,c,l,a),s+o};Rebound.GlobalTrader.Site.Functions.showStockAvailableNA=function(n,t){strName=$R_FN.setCleanTextValue(n);var i=strName;return i='<div class="dropdown',strAlt="",i+=" ",strAlt=$R_FN.setCleanTextValue(t),i+String.format("\" title=\"{0}\"> {1} <span class='dropbtn' style='color: #ff6a00'>Stock Alert!<\/span><\/div>",strAlt,strName)};Rebound.GlobalTrader.Site.Functions.showStockAvailableNewBackup=function(n,t,i){strName=$R_FN.setCleanTextValue(n);var r=strName;return r='<div class="dropdown',strAlt="",r+=" ",strAlt=$R_FN.setCleanTextValue(t),r+String.format("\" title=\"{0}\">{1}<button class='dropbtn'>Stock<\/button><div class='dropdown-content'><a href=\"{2}\" target='_blank'>Link 1<\/a><a href=\"{2}\" target='_blank'>Link 2<\/a><a href=\"{2}\" target='_blank'>Link 3<\/a><a href=\"{2}\" target='_blank'>Link 4<\/a><\/div><\/div>",strAlt,strName,i)};Rebound.GlobalTrader.Site.Functions.createNubButtonGILineShortShipment=function(n,t,i,r,u,f,e,o,s){var h="&nbsp;",v,c,l,a;return typeof t!="undefined"&&t.toString().length>0&&(v="javascript:void(0);",r||(r="Left"),c="topMenuRolloverLink",t=$R_FN.setCleanTextValue(t),l=String.format('&nbsp;&nbsp;<a href="javascript:void(0);" onclick="$R_FN.openPrintWindow($R_ENUM$PrintObject.PrintSSLog,{0})" >Log<\/a>',n),a="",u&&(f?typeof f.toString().toLowerCase()=="string"&&(f='"'+f+'"'):f="null",a=String.format(' onmouseover="$R_PAGE._ctlToolTip.registerDynamicElement(this, {0}, {1}, {2}, {3});"',f,e,o,s?s:"null")),h=String.format('<div class="{0}">{1}{2}<\/div>',c,$RGT_nubButton_ShortShipmentDetails(n,n),l)),h};Rebound.GlobalTrader.Site.Functions.openCustomTemplateWindow=function(n,t,i){var r=String.format("CustomTemplate.aspx?{0}={1}",$R_QS_PrintObject,n);t>0&&(r+=String.format("&{0}={1}",$R_QS_GenericID,t));i&&(r+=String.format("&{0}=1",$R_QS_EmailMode));window.open(r,"winPrint","left=20,top=20,width="+screen.width+",height="+screen.height+",toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=yes,scrollbars=yes")};Rebound.GlobalTrader.Site.Functions.openSanctioned=function(){var t="CSL_Sanctioned.aspx",n=window.open(t,"winSales","left=20,top=100,width=1000,height=500,toolbar=no,resizable=no,status=no,directories=no,location=no,menubar=no,scrollbars=yes;");n.location=="about:blank"?(n.location.hef=t,n.focus()):n.focus()};Rebound.GlobalTrader.Site.Functions.highlightBackgroundColorOfText=function(n,t){n.charAt(0)==="#"&&(n=n.substr(1));t==!0?$("#"+n).addClass("AS6081HighlightBackgroundColorOfText"):$("#"+n).removeClass("AS6081HighlightBackgroundColorOfText")};Rebound.GlobalTrader.Site.Functions.openSupplierInvoiceDetailWindow=function(n){var t=String.format("SupplierInvoiceDetail.aspx?{0}={1}","SID",n);window.open(t,"winPrint","left=20,top=20,width="+screen.width+",height="+screen.height+",toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=yes,scrollbars=yes")};Rebound.GlobalTrader.Site.Functions.createAdvisoryNotesIcon=function(n,t){if(n){let i=$R_FN.setCleanTextValue(n);return i=i.replace(/(<br \/>)/g,"&#10;"),t||(t=""),String.format('<span class="advisory-notes {1}" title="{0}"><\/span>',i,t)}return""};Rebound.GlobalTrader.Site.Functions.parseDateFromUKFormat=function(n){const[t,i,r]=n.split("/").map(Number);return new Date(r,i-1,t)};