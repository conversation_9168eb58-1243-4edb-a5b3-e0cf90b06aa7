﻿///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />

Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");

Rebound.GlobalTrader.Site.Controls.DropDowns.Completed = function(element) {
Rebound.GlobalTrader.Site.Controls.DropDowns.Completed.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.DropDowns.Completed.prototype = {

    initialize: function() {
    Rebound.GlobalTrader.Site.Controls.DropDowns.Completed.callBaseMethod(this, "initialize");
        this.addSetupDataCall(Function.createDelegate(this, this.setupDataCall));
        this.addGetDataOK(Function.createDelegate(this, this.dataCallOK));
    },

    dispose: function() {
        if (this.isDisposed) return;
        Rebound.GlobalTrader.Site.Controls.DropDowns.Completed.callBaseMethod(this, "dispose");
    },

    setupDataCall: function() {
    this._objData.set_PathToData("controls/DropDowns/Completed");
    this._objData.set_DataObject("Completed");
        this._objData.set_DataAction("GetData");
    },

    dataCallOK: function() {
        var result = this._objData._result;
        if (result.Items) {
            for (var i = 0; i < result.Items.length; i++) {
                this.addOption(result.Items[i].Name, result.Items[i].ID);
            }
        }
    }

};

Rebound.GlobalTrader.Site.Controls.DropDowns.Completed.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.Completed", Rebound.GlobalTrader.Site.Controls.DropDowns.Base);
