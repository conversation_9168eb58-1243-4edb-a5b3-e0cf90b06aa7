Type.registerNamespace("Rebound.GlobalTrader.Site.Pages");Rebound.GlobalTrader.Site.Pages.Content=function(n){Rebound.GlobalTrader.Site.Pages.Content.initializeBase(this,[n]);this._intTimeoutTopIcons=-1;this._blnLeftPanelVisible=!1;this._blnAlertsOpen=!1;this._blnMessagesOpen=!1;this._blnModalBackgroundOnOriginally=!1;this._objMessages={}};Rebound.GlobalTrader.Site.Pages.Content.prototype={get_bdyBody:function(){return this._bdyBody},set_bdyBody:function(n){this._bdyBody!==n&&(this._bdyBody=n)},get_strPageTheme:function(){return this._strPageTheme},set_strPageTheme:function(n){this._strPageTheme!==n&&(this._strPageTheme=n)},get_ancCloseLeft:function(){return this._ancCloseLeft},set_ancCloseLeft:function(n){this._ancCloseLeft!==n&&(this._ancCloseLeft=n)},get_pnlLeftContent:function(){return this._pnlLeftContent},set_pnlLeftContent:function(n){this._pnlLeftContent!==n&&(this._pnlLeftContent=n)},get_pnlLeftButton:function(){return this._pnlLeftButton},set_pnlLeftButton:function(n){this._pnlLeftButton!==n&&(this._pnlLeftButton=n)},get_pnlShadow:function(){return this._pnlShadow},set_pnlShadow:function(n){this._pnlShadow!==n&&(this._pnlShadow=n)},get_blnLeftPanelVisible:function(){return this._blnLeftPanelVisible},set_blnLeftPanelVisible:function(n){this._blnLeftPanelVisible!==n&&(this._blnLeftPanelVisible=n)},get_pnlNewMessages:function(){return this._pnlNewMessages},set_pnlNewMessages:function(n){this._pnlNewMessages!==n&&(this._pnlNewMessages=n)},get_lblNewMessagesTitle:function(){return this._lblNewMessagesTitle},set_lblNewMessagesTitle:function(n){this._lblNewMessagesTitle!==n&&(this._lblNewMessagesTitle=n)},get_tblNewMessages:function(){return this._tblNewMess3ages},set_tblNewMessages:function(n){this._tblNewMessages!==n&&(this._tblNewMessages=n)},get_hypCloseNewMessages:function(){return this._hypCloseNewMessages},set_hypCloseNewMessages:function(n){this._hypCloseNewMessages!==n&&(this._hypCloseNewMessages=n)},get_blnPageShouldCheckMessages:function(){return this._blnPageShouldCheckMessages},set_blnPageShouldCheckMessages:function(n){this._blnPageShouldCheckMessages!==n&&(this._blnPageShouldCheckMessages=n)},get_pnlToDoAlert:function(){return this._pnlToDoAlert},set_pnlToDoAlert:function(n){this._pnlToDoAlert!==n&&(this._pnlToDoAlert=n)},get_tblAlerts:function(){return this._tblAlerts},set_tblAlerts:function(n){this._tblAlerts!==n&&(this._tblAlerts=n)},get_ibtnOpenItem:function(){return this._ibtnOpenItem},set_ibtnOpenItem:function(n){this._ibtnOpenItem!==n&&(this._ibtnOpenItem=n)},get_ibtnDismiss:function(){return this._ibtnDismiss},set_ibtnDismiss:function(n){this._ibtnDismiss!==n&&(this._ibtnDismiss=n)},get_ibtnSnooze:function(){return this._ibtnSnooze},set_ibtnSnooze:function(n){this._ibtnSnooze!==n&&(this._ibtnSnooze=n)},get_pnlMainArea:function(){return this._pnlMainArea},set_pnlMainArea:function(n){this._pnlMainArea!==n&&(this._pnlMainArea=n)},get_divMainArea:function(){return this._divMainArea},set_divMainArea:function(n){this._divMainArea!==n&&(this._divMainArea=n)},get_pnlTitleBar:function(){return this._pnlTitleBar},set_pnlTitleBar:function(n){this._pnlTitleBar!==n&&(this._pnlTitleBar=n)},get_objBackgroundImages:function(){return this._objBackgroundImages},set_objBackgroundImages:function(n){this._objBackgroundImages!==n&&(this._objBackgroundImages=n)},get_ddlSnoozeTime:function(){return this._ddlSnoozeTime},set_ddlSnoozeTime:function(n){this._ddlSnoozeTime!==n&&(this._ddlSnoozeTime=n)},get_ctlToolTip:function(){return this._ctlToolTip},set_ctlToolTip:function(n){this._ctlToolTip!==n&&(this._ctlToolTip=n)},get_pnlIcons:function(){return this._pnlIcons},set_pnlIcons:function(n){this._pnlIcons!==n&&(this._pnlIcons=n)},get_hypProfile:function(){return this._hypProfile},set_hypProfile:function(n){this._hypProfile!==n&&(this._hypProfile=n)},get_ddlClientByMaster:function(){return this._ddlClientByMaster},set_ddlClientByMaster:function(n){this._ddlClientByMaster!==n&&(this._ddlClientByMaster=n)},get_intMasterLoginNo:function(){return this._intMasterLoginNo},set_intMasterLoginNo:function(n){this._intMasterLoginNo!==n&&(this._intMasterLoginNo=n)},get_intDefaultClientNo:function(){return this._intDefaultClientNo},set_intDefaultClientNo:function(n){this._intDefaultClientNo!==n&&(this._intDefaultClientNo=n)},addCheckLoggedIn:function(n){this.get_events().addHandler("CheckLoggedIn",n)},removeCheckLoggedIn:function(n){this.get_events().removeHandler("CheckLoggedIn",n)},onCheckLoggedIn:function(){var n=this.get_events().getHandler("CheckLoggedIn");n&&n(this,Sys.EventArgs.Empty)},addGetAlerts:function(n){this.get_events().addHandler("GetAlerts",n)},removeGetAlerts:function(n){this.get_events().removeHandler("GetAlerts",n)},onGetAlerts:function(){var n=this.get_events().getHandler("GetAlerts");n&&n(this,Sys.EventArgs.Empty)},addSnoozeAlert:function(n){this.get_events().addHandler("SnoozeAlert",n)},removeSnoozeAlert:function(n){this.get_events().removeHandler("SnoozeAlert",n)},onSnoozeAlert:function(){var n=this.get_events().getHandler("SnoozeAlert");n&&n(this,Sys.EventArgs.Empty)},addDismissAlert:function(n){this.get_events().addHandler("DismissAlert",n)},removeDismissAlert:function(n){this.get_events().removeHandler("DismissAlert",n)},onDismissAlert:function(){var n=this.get_events().getHandler("DismissAlert");n&&n(this,Sys.EventArgs.Empty)},addGotoAlert:function(n){this.get_events().addHandler("GotoAlert",n)},removeGotoAlert:function(n){this.get_events().removeHandler("GotoAlert",n)},onGotoAlert:function(){var n=this.get_events().getHandler("GotoAlert");n&&n(this,Sys.EventArgs.Empty)},addCheckForMessages:function(n){this.get_events().addHandler("CheckForMessages",n)},removeCheckForMessages:function(n){this.get_events().removeHandler("CheckForMessages",n)},onCheckForMessages:function(){var n=this.get_events().getHandler("CheckForMessages");n&&n(this,Sys.EventArgs.Empty)},addAddMailMessages:function(n){this.get_events().addHandler("AddMailMessages",n)},removeAddMailMessages:function(n){this.get_events().removeHandler("AddMailMessages",n)},onAddMailMessages:function(){var n=this.get_events().getHandler("AddMailMessages");n&&n(this,Sys.EventArgs.Empty)},initialize:function(){this._objBackgroundImages=Sys.Serialization.JavaScriptSerializer.deserialize(this._objBackgroundImages);$addHandler(this._ancCloseLeft,"click",Function.createDelegate(this,this.toggleLeftBar));this._intLoginTimeoutID=setTimeout(Function.createDelegate(this,this.logout),$R_LOGIN_TIMEOUT*6e4);this.onCheckLoggedIn();this._blnPageShouldCheckMessages&&(this.checkForMessages(),this._tblAlerts.addSelectedIndexChanged(Function.createDelegate(this,this.selectAlert)),$R_IBTN.addClick(this._ibtnOpenItem,Function.createDelegate(this,this.gotoAlert)),$R_IBTN.addClick(this._ibtnDismiss,Function.createDelegate(this,this.dismissAlert)),$R_IBTN.addClick(this._ibtnSnooze,Function.createDelegate(this,this.snoozeAlert)),$addHandler(this._hypCloseNewMessages,"click",Function.createDelegate(this,this.closeNewMessages)));$addHandler(window,"resize",Function.createDelegate(this,this.windowResize));this.addCheckLoggedIn(Function.createDelegate(this,this.doCheckLoggedIn));this.addGetAlerts(Function.createDelegate(this,this.doGetAlerts));this.addSnoozeAlert(Function.createDelegate(this,this.doSnoozeAlert));this.addDismissAlert(Function.createDelegate(this,this.doDismissAlert));this.addGotoAlert(Function.createDelegate(this,this.doGotoAlert));this.addCheckForMessages(Function.createDelegate(this,this.doCheckForMessages));this.addAddMailMessages(Function.createDelegate(this,this.doAddMessages));$addHandler(this._pnlIcons,"mouseover",Function.createDelegate(this,this.showTopIcons));$addHandler(this._hypProfile,"mouseover",Function.createDelegate(this,this.showTopIcons));$addHandler(this._pnlIcons,"mouseout",Function.createDelegate(this,this.hideTopIcons));$addHandler(this._hypProfile,"mouseout",Function.createDelegate(this,this.hideTopIcons));this._ddlClientByMaster.clearDropDown();this._ddlClientByMaster._intMasterLoginNo=this._intMasterLoginNo;this._ddlClientByMaster.getData();this._ddlClientByMaster.addChanged(Function.createDelegate(this,this.LoginWithOtherClient));setTimeout(Function.createDelegate(this,this.setDropdownValue),800);Rebound.GlobalTrader.Site.Pages.Content.callBaseMethod(this,"initialize")},goInit:function(){},dispose:function(){this.isDisposed||(this.clearTimeout(),this._pnlIcons&&$clearHandlers(this._pnlIcons),this._hypProfile&&$clearHandlers(this._hypProfile),this._element&&$clearHandlers(this._element),window&&$clearHandlers(window),this._ibtnOpenItem&&$R_IBTN.clearHandlers(this._ibtnOpenItem),this._ibtnDismiss&&$R_IBTN.clearHandlers(this._ibtnDismiss),this._ibtnSnooze&&$R_IBTN.clearHandlers(this._ibtnSnooze),this._hypCloseNewMessages&&$clearHandlers(this._hypCloseNewMessages),this._ancCloseLeft&&$clearHandlers(this._ancCloseLeft),this._tblNewMessages&&this._tblNewMessages.dispose(),this._tblAlerts&&this._tblAlerts.dispose(),this._ddlSnoozeTime&&this._ddlSnoozeTime.dispose(),this._ctlToolTip&&this._ctlToolTip.dispose(),this._strPageTheme=null,this._ancCloseLeft=null,this._pnlLeftContent=null,this._pnlLeftButton=null,this._pnlShadow=null,this._blnLeftPanelVisible=null,this._pnlNewMessages=null,this._lblNewMessagesTitle=null,this._tblNewMessages=null,this._hypCloseNewMessages=null,this._blnPageShouldCheckMessages=null,this._pnlToDoAlert=null,this._tblAlerts=null,this._ibtnOpenItem=null,this._ibtnDismiss=null,this._ibtnSnooze=null,this._pnlMainArea=null,this._divMainArea=null,this._pnlTitleBar=null,this._objBackgroundImages=null,this._ddlSnoozeTime=null,this._bdyBody=null,this._ctlToolTip=null,this._hypProfile=null,this._pnlIcons=null,this._intTimeoutTopIcons=null,this._ddlClientByMaster=null,Rebound.GlobalTrader.Site.Pages.Content.callBaseMethod(this,"dispose"),this.isDisposed=!0)},showTopIcons:function(){this.clearTimeout();$R_FN.showElement(this._pnlIcons,!0)},hideTopIcons:function(){var n=this._element.id,t=function(){$find(n).finishHideTopIcons()};this._intTimeoutTopIcons=setTimeout(t,50)},finishHideTopIcons:function(){this.clearTimeout();$R_FN.showElement(this._pnlIcons,!1)},toggleLeftBar:function(){this._blnLeftPanelVisible=!this._blnLeftPanelVisible;this._pnlMainArea.className=this._blnLeftPanelVisible?"mainArea_On":"mainArea_Off";this._pnlLeftContent.className=this._blnLeftPanelVisible?"leftbarContent":"invisible";this._pnlLeftButton.className=this._blnLeftPanelVisible?"leftbarButton_On":"leftbarButton_Off";this._pnlShadow.className=this._blnLeftPanelVisible?"invisible":"mainAreaShadow";this.resizeAllTables();Rebound.GlobalTrader.Site.WebServices.SetLeftPanelVisibleSession(this._blnLeftPanelVisible)},windowResize:function(){this.resizeAllTables()},resizeAllTables:function(){for(var n=Sys.Application.getComponents(),t=0,i=n.length;t<i;t++)n[t].ControlType_FlexiDataTable&&n[t].resizeColumns();n=null},checkLoginSuccess:function(n){n||this.logout()},checkLoginFailed:function(){this.logout()},logout:function(){this.stopCheckForMessages();clearTimeout(this._intLoginTimeoutID);location.href=String.format("{0}?ret={1}",$R_URL_Logout,escape(location.href))},startCheckForMessages:function(){this.stopCheckForMessages();this._intMessageCheckTimeoutID=setTimeout(Function.createDelegate(this,this.checkForMessages),$R_MESSAGE_CHECK_TIMEOUT*6e4)},stopCheckForMessages:function(){clearTimeout(this._intMessageCheckTimeoutID)},checkForMessages:function(){this.stopCheckForMessages();this._blnMessagesOpen||this.onCheckForMessages()},checkForMessagesSuccess:function(n){if(this.startCheckForMessages(),n!=""){var t=Sys.Serialization.JavaScriptSerializer.deserialize(n);if(t.LOGGED_OUT==""){location.href=$R_URL_Logout;return}t.Messages&&this._blnPageShouldCheckMessages&&(this._tblNewMessages.clearTable(),this._objMessages=t.Messages,this.onAddMailMessages(),this._objMessages=null,t.Messages.length>0&&(this._tblNewMessages.resizeColumns(),$R_FN.showElement(this._pnlNewMessages,!0),this._blnMessagesOpen=!0,$R_SHOW_MESSAGE_ALERT&&alert(this._lblNewMessagesTitle.innerHTML)));t.Alerts&&!this._blnAlertsOpen&&this.populateAlerts(t.Alerts)}},getAlerts:function(){this.onGetAlerts()},getAlertsComplete:function(n){if(n!=""){var t=Sys.Serialization.JavaScriptSerializer.deserialize(n);t.Alerts&&this.populateAlerts(t.Alerts)}},populateAlerts:function(n){var t,r,i,u,f;for(this.enableAlertButtons(!1),this._tblAlerts.clearTable(),t=0,r=n.length;t<r;t++)i=n[t],u=[$R_FN.setCleanTextValue(i.Text),i.DueDate],this._tblAlerts.addRow(u,i.ID,!1);n.length>0?(this._tblAlerts.resizeColumns(),this._blnAlertsOpen||($R_FN.showElement(this._pnlToDoAlert,!0),f=Math.max(175,$R_FN.windowScrollPosition()+25),this._pnlToDoAlert.style.top=String.format("{0}px",f),this._blnModalBackgroundOnOriginally=$R_FN.isElementVisible($get("modalBG")),$R_FN.showModalBG(!0)),this._blnAlertsOpen=!0):this.closeAlerts()},closeAlerts:function(){this._blnAlertsOpen&&(this._blnModalBackgroundOnOriginally||$R_FN.showModalBG(!1),this._blnAlertsOpen=!1,$R_FN.showElement(this._pnlToDoAlert,!1))},checkForMessagesFailed:function(){this.startCheckForMessages()},selectAlert:function(){this.enableAlertButtons(!0)},enableAlertButtons:function(n){n?($R_IBTN.enableButton(this._ibtnOpenItem,this._tblAlerts._varSelectedValue>0),$R_IBTN.enableButton(this._ibtnDismiss,this._tblAlerts._varSelectedValue>0),$R_IBTN.enableButton(this._ibtnSnooze,this._tblAlerts._varSelectedValue>0)):($R_IBTN.enableButton(this._ibtnOpenItem,!1),$R_IBTN.enableButton(this._ibtnDismiss,!1),$R_IBTN.enableButton(this._ibtnSnooze,!1))},gotoAlert:function(){this.onGotoAlert()},dismissAlert:function(){this.onDismissAlert()},dismissAlertComplete:function(){this.getAlerts()},snoozeAlert:function(){this.onSnoozeAlert()},snoozeAlertComplete:function(){this.getAlerts()},changeBackgroundImage:function(n){this._bdyBody.style.backgroundImage=String.format("url({0})",this._objBackgroundImages[n.toLowerCase()])},closeNewMessages:function(){$R_FN.showElement(this._pnlNewMessages,!1)},doCheckLoggedIn:function(){Rebound.GlobalTrader.Site.WebServices.CheckLoggedIn(Function.createDelegate(this,this.checkLoginSuccess),Function.createDelegate(this,this.checkLoginFailed))},doGetAlerts:function(){Rebound.GlobalTrader.Site.WebServices.CheckForAlerts(Function.createDelegate(this,this.getAlertsComplete))},doSnoozeAlert:function(){Rebound.GlobalTrader.Site.WebServices.SnoozeAlert(this._tblAlerts._varSelectedValue,this._ddlSnoozeTime.getValue(),Function.createDelegate(this,this.snoozeAlertComplete))},doDismissAlert:function(){Rebound.GlobalTrader.Site.WebServices.DismissAlert(this._tblAlerts._varSelectedValue,Function.createDelegate(this,this.dismissAlertComplete))},doGotoAlert:function(){location.href=$RGT_gotoURL_ToDo(this._tblAlerts._varSelectedValue)},doCheckForMessages:function(){Rebound.GlobalTrader.Site.WebServices.CheckForMessages(Function.createDelegate(this,this.checkForMessagesSuccess),Function.createDelegate(this,this.checkForMessagesFailed))},doAddMessages:function(){for(var n,r,t=0,i=this._objMessages.length;t<i;t++)n=this._objMessages[t],r=[$R_FN.setCleanTextValue(n.From),$RGT_nubButton_MailMessage(n.ID,n.Subject),n.DateSent],this._tblNewMessages.addRow(r,n.ID,!1,null,"newMessage"),n=null},clearTimeout:function(){this._intTimeoutTopIcons!=-1&&clearTimeout(this._intTimeoutTopIcons)},LoginWithOtherClient:function(){location.href=String.format("{0}?ret={1}&CID={2}","LoginWithOtherClient.aspx",escape(location.href),this._ddlClientByMaster.getValue())},setDropdownValue:function(){this._ddlClientByMaster.setValue(this._intDefaultClientNo)}};Rebound.GlobalTrader.Site.Pages.Content.registerClass("Rebound.GlobalTrader.Site.Pages.Content",Sys.UI.Control,Sys.IDisposable);