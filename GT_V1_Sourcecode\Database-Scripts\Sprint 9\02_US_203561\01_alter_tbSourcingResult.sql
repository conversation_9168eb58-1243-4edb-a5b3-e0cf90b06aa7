﻿/* 
===========================================================================================
TASK      		UPDATED BY     	DATE         ACTION		DESCRIPTION
[US-203561]		An.TranTan		20-Aug-2024  UPDATE		Add SellPriceLessReason 
===========================================================================================
*/
IF COL_LENGTH('dbo.tbSourcingResult', 'SellPriceLessReason') IS NULL
BEGIN
   ALTER TABLE dbo.tbSourcingResult ADD SellPriceLessReason NVARCHAR(128) NULL
END