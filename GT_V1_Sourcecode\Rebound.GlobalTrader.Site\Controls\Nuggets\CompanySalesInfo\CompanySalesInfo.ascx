<%--
     Marker     Changed by      Date         Remarks
    [001]      Shashi Keshar   20/01/2012   Added Insurance History in Detail Section--%>
<%--[002]      Suhail          02/05/2018   Added Credit Limit2  --%>
<%@ Control Language="C#" CodeBehind="CompanySalesInfo.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Nuggets.CompanySalesInfo" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" BoxType="Standard">
	<Links>
		<ReboundUI:IconButton ID="ibtnEdit" runat="server" IconGroup="Nugget" IconTitleResource="Edit" IconCSSType="Edit" IconButtonMode="Hyperlink" Href="javascript:void(0);" />
	</Links>
	
	<Content>
		<table class="twoCols">
			<tr>
				<td class="col1">
					<table cellpadding="0" cellspacing="0" border="0" class="dataItems">
						<ReboundUI:DataItemRow id="ctlSalesperson" runat="server" ResourceTitle="Salesperson" />
						<ReboundUI:DataItemRow id="hidSalespersonNo" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="ctlIsApproved" runat="server" ResourceTitle="IsApproved" />
						<strong>
						<ReboundUI:DataItemRow id="ctlStopStatus" runat="server" ResourceTitle="StopStatus" />
						</strong>
						<ReboundUI:DataItemRow id="hidIsApproved" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="ctlCurrency" runat="server" ResourceTitle="Currency" />
						<ReboundUI:DataItemRow id="hidCurrencyNo" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="hidCurrencyCode" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="ctlCustomerNo" runat="server" ResourceTitle="CustomerNo" />
						<ReboundUI:DataItemRow id="ctlTerms" runat="server" ResourceTitle="Terms" />
						<ReboundUI:DataItemRow id="hidTermsNo" runat="server" FieldType="Hidden" />
				<%--//ESMS #14--%>
					<%--	<ReboundUI:DataItemRow id="ctlTax" runat="server" ResourceTitle="Tax" />
--%>						<ReboundUI:DataItemRow id="hidTaxNo" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="ctlRating" runat="server" ResourceTitle="Rating" FieldType="StarRating" />
						<ReboundUI:DataItemRow id="ctlIsOnStop" runat="server" ResourceTitle="IsOnStop" FieldType="CheckBox" />
						<ReboundUI:DataItemRow id="ctlIsShippingWaived" runat="server" ResourceTitle="IsShippingWaived" FieldType="CheckBox" />
						<ReboundUI:DataItemRow id="ctlShipVia" runat="server"  ResourceTitle="DefaultShipVia" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="hidShipViaNo" runat="server" FieldType="Hidden"  />
						<ReboundUI:DataItemRow id="ctlShippingAccountNo" runat="server" ResourceTitle="DefaultShippingAccountNo" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="ctlContact" runat="server" ResourceTitle="DefaultContactNo" />
						<ReboundUI:DataItemRow id="hidContactNo" runat="server" FieldType="Hidden" />
                        <ReboundUI:DataItemRow id="ctlNotesToInvoice" runat="server" ResourceTitle="NotesToInvoice" />
                        <ReboundUI:DataItemRow id="hidInsuredAmountCurrencyNo" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="hidInsuredAmountCurrencyCode" runat="server" FieldType="Hidden" />
					</table>
				</td>
				<td class="col2">
					<div class="dataItem_Title"><%=Functions.GetGlobalResource("FormFields", "OpenSOs")%></div>
					<asp:Panel ID="pnlGetOpenSOs" runat="server" CssClass="getData"><asp:HyperLink ID="hypGetOpenSOs" runat="server" NavigateUrl="javascript:void(0);"><%=Functions.GetGlobalResource("Buttons", "GetData")%></asp:HyperLink></asp:Panel>
					<asp:Panel ID="pnlLoadingOpenSOs" runat="server" CssClass="subInfoLoading invisible"><%=Functions.GetGlobalResource("Misc", "Loading")%></asp:Panel>
					<asp:Panel ID="pnlOpenSOsError" runat="server" CssClass="error invisible" />
					<asp:Panel ID="pnlOpenSOs" runat="server" CssClass="invisible"><ReboundUI:FlexiDataTable ID="tblOpenSOs" runat="server" PanelHeight="70" /></asp:Panel>
					<div class="dataItem_Title dataItem_TitleUnderneathList"><%=Functions.GetGlobalResource("FormFields", "OverdueSOs")%></div>
					<asp:Panel ID="pnlGetOverdueSOs" runat="server" CssClass="getData"><asp:HyperLink ID="hypGetOverdueSOs" runat="server" NavigateUrl="javascript:void(0);"><%=Functions.GetGlobalResource("Buttons", "GetData")%></asp:HyperLink></asp:Panel>
					<asp:Panel ID="pnlLoadingOverdueSOs" runat="server" CssClass="subInfoLoading invisible"><%=Functions.GetGlobalResource("Misc", "Loading")%></asp:Panel>
					<asp:Panel ID="pnlOverdueSOsError" runat="server" CssClass="error invisible" />
					<asp:Panel ID="pnlOverdueSOs" runat="server" CssClass="invisible"><ReboundUI:FlexiDataTable ID="tblOverdueSOs" runat="server" PanelHeight="70" /></asp:Panel>
				</td>
			</tr>
			<tr><td colspan="2"><div class="line"></div></td></tr>
		
			<tr>
				<td>
					<table cellpadding="0" cellspacing="0" border="0" class="dataItems">
                    <ReboundUI:DataItemRow id="ctlPreferredWarehouse" runat="server" ResourceTitle="PreferredWarehouse" />
						<ReboundUI:DataItemRow id="ctlCreditLimit" runat="server" ResourceTitle="MaxExpoCreditLimit" />
                      <%--  [002] Code Start--%>
                        <ReboundUI:DataItemRow id="ctlCreditLimit2" runat="server" ResourceTitle="CreditLimit" />
                        <ReboundUI:DataItemRow id="hidActCreditLimit" runat="server" FieldType="Hidden" />
                         <%--  [002] Code End--%>
						<ReboundUI:DataItemRow id="hidCreditLimit" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="ctlYearToDate" runat="server" ResourceTitle="YearToDate" ShowEllipses="false" />
						<ReboundUI:DataItemRow id="ctlLastYear" runat="server" ResourceTitle="LastYear" ShowEllipses="false" />
						<ReboundUI:DataItemRow id="ctlBalance" runat="server" ResourceTitle="Balance" />
						<ReboundUI:DataItemRow id="ctlCurrent" runat="server" ResourceTitle="Current" />
                         <ReboundUI:DataItemRow id="ctlDays1" runat="server" ResourceTitle="Days1" />
						<ReboundUI:DataItemRow id="ctlDays30" runat="server" ResourceTitle="Days30" />
                       
						<ReboundUI:DataItemRow id="ctlDays60" runat="server" ResourceTitle="Days60" />
						<ReboundUI:DataItemRow id="ctlDays90" runat="server" ResourceTitle="Days90" />
						<ReboundUI:DataItemRow id="ctlDays120" runat="server" ResourceTitle="Days120" />
						
						<ReboundUI:DataItemRow id="ctlBalanceWithOpenOrders" runat="server" ResourceTitle="BalanceWithOpenOrders" />
						<ReboundUI:DataItemRow id="ctlInvoiceNotExport" runat="server" ResourceTitle="InvoiceNotExport" />
					<%--	<ReboundUI:DataItemRow id="ctlSpacer" runat="server" FieldType="SeparatorWithLine" />--%>
                        
                <ReboundUI:DataItemRow id="ctlYearToDateNew" runat="server" ResourceTitle="YearToDateNew" ShowEllipses="true" />
				<ReboundUI:DataItemRow id="ctlLastYearNew" runat="server" ResourceTitle="LastYearNew" ShowEllipses="true" />
						
					</table>
				</td>
				<td class="col3">
					<div class="dataItem_Title"><%=Functions.GetGlobalResource("FormFields", "CreditHistory")%></div>
					<asp:Panel ID="pnlGetCreditHistory" runat="server" CssClass="getData"><asp:HyperLink ID="hypGetCreditHistory" runat="server" NavigateUrl="javascript:void(0);"><%=Functions.GetGlobalResource("Buttons", "GetData")%></asp:HyperLink></asp:Panel>
					<asp:Panel ID="pnlLoadingCreditHistory" runat="server" CssClass="subInfoLoading invisible"><%=Functions.GetGlobalResource("Misc", "Loading")%></asp:Panel>
					<asp:Panel ID="pnlCreditHistoryError" runat="server" CssClass="error invisible" />
					<asp:Panel ID="pnlCreditHistory" runat="server" CssClass="invisible"><ReboundUI:FlexiDataTable ID="tblCreditHistory" runat="server" PanelHeight="70" /></asp:Panel>
								
				</td>
			</tr>
			<tr><td colspan="2"><div class="line"></div></td></tr>
				<tr>
				    <td> 
			<table cellpadding="0" cellspacing="0" border="0" class="dataItems">
			            <ReboundUI:DataItemRow id="ctlInsuranceFileNo" runat="server"  ResourceTitle="InsuranceFileNo"/>
				        <ReboundUI:DataItemRow id="ctlInsuredAmount" runat="server"  ResourceTitle="InsuredAmount"/>
						<ReboundUI:DataItemRow id="hidInsuredAmount" runat="server"  FieldType="Hidden"/>
			
			</table>
			<td class="col3">
			<div class="dataItem_Title"><%=Functions.GetGlobalResource("FormFields", "InsuranceHistory")%></div>
				    <asp:Panel ID="pnlGetInsuranceHistory" runat="server" CssClass="getData"><asp:HyperLink ID="hypGetInsuranceHistory" runat="server" NavigateUrl="javascript:void(0);"><%=Functions.GetGlobalResource("Buttons", "GetData")%></asp:HyperLink></asp:Panel>
					<asp:Panel ID="pnlLoadingInsuranceHistory" runat="server" CssClass="subInfoLoading invisible"><%=Functions.GetGlobalResource("Misc", "Loading")%></asp:Panel>
					<asp:Panel ID="pnlInsuranceHistoryError" runat="server" CssClass="error invisible" />
					<asp:Panel ID="pnlInsuranceHistory" runat="server" CssClass="invisible"><ReboundUI:FlexiDataTable ID="tblInsuranceHistory" runat="server" PanelHeight="70" /></asp:Panel>


				
			    </td>
			</td> </tr>
			
		</table>
	</Content>
	
	<Forms>
		<ReboundForm:CompanySalesInfo_Edit id="ctlEdit" runat="server" />
	</Forms>
</ReboundUI_Nugget:DesignBase>
