Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.GILineBarcodesStatus=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.GILineBarcodesStatus.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DropDowns.GILineBarcodesStatus.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.DropDowns.GILineBarcodesStatus.callBaseMethod(this,"initialize");this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK))},dispose:function(){this.isDisposed||Rebound.GlobalTrader.Site.Controls.DropDowns.GILineBarcodesStatus.callBaseMethod(this,"dispose")},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/GILineBarcodesStatus");this._objData.set_DataObject("GILineBarcodesStatus");this._objData.set_DataAction("GetData")},dataCallOK:function(){var n=this._objData._result,t;if(n!=null&&n.Types)for(t=0;t<n.Types.length;t++)this.addOption(n.Types[t].Name,n.Types[t].ID)}};Rebound.GlobalTrader.Site.Controls.DropDowns.GILineBarcodesStatus.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.GILineBarcodesStatus",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);