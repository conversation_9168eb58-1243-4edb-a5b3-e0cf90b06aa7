//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option or rebuild the Visual Studio project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.VisualStudio.Web.Application.StronglyTypedResourceProxyBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class Status {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Status() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Resources.Status", global::System.Reflection.Assembly.Load("App_GlobalResources"));
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Accepted.
        /// </summary>
        internal static string Accepted {
            get {
                return ResourceManager.GetString("Accepted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allocated.
        /// </summary>
        internal static string Allocated {
            get {
                return ResourceManager.GetString("Allocated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Amended.
        /// </summary>
        internal static string Amended {
            get {
                return ResourceManager.GetString("Amended", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Checked.
        /// </summary>
        internal static string Authorised {
            get {
                return ResourceManager.GetString("Authorised", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Checked (Part Allocated).
        /// </summary>
        internal static string AuthorisedPartAllocated {
            get {
                return ResourceManager.GetString("AuthorisedPartAllocated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Available.
        /// </summary>
        internal static string Available {
            get {
                return ResourceManager.GetString("Available", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Available (Part Allocated).
        /// </summary>
        internal static string Available_PartAllocated {
            get {
                return ResourceManager.GetString("Available_PartAllocated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Available (Unallocated).
        /// </summary>
        internal static string Available_Unallocated {
            get {
                return ResourceManager.GetString("Available_Unallocated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Awaiting PO Approval.
        /// </summary>
        internal static string AwaitingPOApproval {
            get {
                return ResourceManager.GetString("AwaitingPOApproval", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Awaiting SO Check.
        /// </summary>
        internal static string AwaitingSOCheck {
            get {
                return ResourceManager.GetString("AwaitingSOCheck", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Awaits Inspection.
        /// </summary>
        internal static string AwaitsInspection {
            get {
                return ResourceManager.GetString("AwaitsInspection", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Checked.
        /// </summary>
        internal static string Checked {
            get {
                return ResourceManager.GetString("Checked", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Closed.
        /// </summary>
        internal static string Closed {
            get {
                return ResourceManager.GetString("Closed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Company On Stop.
        /// </summary>
        internal static string CompanyOnStop {
            get {
                return ResourceManager.GetString("CompanyOnStop", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Company over credit limit.
        /// </summary>
        internal static string CompanyOverCreditLimit {
            get {
                return ResourceManager.GetString("CompanyOverCreditLimit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Complete.
        /// </summary>
        internal static string Complete {
            get {
                return ResourceManager.GetString("Complete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Confirmed.
        /// </summary>
        internal static string Confirmed {
            get {
                return ResourceManager.GetString("Confirmed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Declined.
        /// </summary>
        internal static string Declined {
            get {
                return ResourceManager.GetString("Declined", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Despatched.
        /// </summary>
        internal static string Despatched {
            get {
                return ResourceManager.GetString("Despatched", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fully Allocated.
        /// </summary>
        internal static string FullyAllocated {
            get {
                return ResourceManager.GetString("FullyAllocated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GI Awaiting Query.
        /// </summary>
        internal static string GIAwaitingQuery {
            get {
                return ResourceManager.GetString("GIAwaitingQuery", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GI Inspected.
        /// </summary>
        internal static string GIInspected {
            get {
                return ResourceManager.GetString("GIInspected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GI Partially Inspected.
        /// </summary>
        internal static string GIPartiallyInspected {
            get {
                return ResourceManager.GetString("GIPartiallyInspected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GI Partially Quarantined.
        /// </summary>
        internal static string GIPartiallyQuarantined {
            get {
                return ResourceManager.GetString("GIPartiallyQuarantined", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GI Partially Released.
        /// </summary>
        internal static string GIPartiallyReleased {
            get {
                return ResourceManager.GetString("GIPartiallyReleased", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GI Quarantined.
        /// </summary>
        internal static string GIQuarantined {
            get {
                return ResourceManager.GetString("GIQuarantined", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GI Received.
        /// </summary>
        internal static string GIReceived {
            get {
                return ResourceManager.GetString("GIReceived", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GI Released.
        /// </summary>
        internal static string GIReleased {
            get {
                return ResourceManager.GetString("GIReleased", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Selected country having potential high failure rate. .
        /// </summary>
        internal static string HighRiskCountryStatus {
            get {
                return ResourceManager.GetString("HighRiskCountryStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hold for the remaining balance.
        /// </summary>
        internal static string HoldRemainBalance {
            get {
                return ResourceManager.GetString("HoldRemainBalance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Inactive.
        /// </summary>
        internal static string Inactive {
            get {
                return ResourceManager.GetString("Inactive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Released.
        /// </summary>
        internal static string Inspected {
            get {
                return ResourceManager.GetString("Inspected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to In Transit.
        /// </summary>
        internal static string InTransit {
            get {
                return ResourceManager.GetString("InTransit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invoice Paid.
        /// </summary>
        internal static string InvoicePaid {
            get {
                return ResourceManager.GetString("InvoicePaid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New.
        /// </summary>
        internal static string New {
            get {
                return ResourceManager.GetString("New", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to None in stock.
        /// </summary>
        internal static string NoneInStock {
            get {
                return ResourceManager.GetString("NoneInStock", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Goods in not released.
        /// </summary>
        internal static string NotInspected {
            get {
                return ResourceManager.GetString("NotInspected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Un Posted.
        /// </summary>
        internal static string NotPosted {
            get {
                return ResourceManager.GetString("NotPosted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Offered.
        /// </summary>
        internal static string Offered {
            get {
                return ResourceManager.GetString("Offered", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to [Stop Status:M].
        /// </summary>
        internal static string OnHoldStatus {
            get {
                return ResourceManager.GetString("OnHoldStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to On Order.
        /// </summary>
        internal static string OnOrder {
            get {
                return ResourceManager.GetString("OnOrder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open.
        /// </summary>
        internal static string Open {
            get {
                return ResourceManager.GetString("Open", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ordered.
        /// </summary>
        internal static string Ordered {
            get {
                return ResourceManager.GetString("Ordered", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Part Allocated.
        /// </summary>
        internal static string PartAllocated {
            get {
                return ResourceManager.GetString("PartAllocated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Part Released.
        /// </summary>
        internal static string PartInspected {
            get {
                return ResourceManager.GetString("PartInspected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Part Posted.
        /// </summary>
        internal static string PartPosted {
            get {
                return ResourceManager.GetString("PartPosted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Part Received.
        /// </summary>
        internal static string PartReceived {
            get {
                return ResourceManager.GetString("PartReceived", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Part Shipped.
        /// </summary>
        internal static string PartShipped {
            get {
                return ResourceManager.GetString("PartShipped", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Placed.
        /// </summary>
        internal static string Placed {
            get {
                return ResourceManager.GetString("Placed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PO Approved.
        /// </summary>
        internal static string POApproved {
            get {
                return ResourceManager.GetString("POApproved", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Posted.
        /// </summary>
        internal static string Posted {
            get {
                return ResourceManager.GetString("Posted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quarantined.
        /// </summary>
        internal static string Quarantined {
            get {
                return ResourceManager.GetString("Quarantined", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ready to ship.
        /// </summary>
        internal static string ReadyToShip {
            get {
                return ResourceManager.GetString("ReadyToShip", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Received.
        /// </summary>
        internal static string Received {
            get {
                return ResourceManager.GetString("Received", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SO closed.
        /// </summary>
        internal static string SalesOrderClosed {
            get {
                return ResourceManager.GetString("SalesOrderClosed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ship Partial.
        /// </summary>
        internal static string ShipPartial {
            get {
                return ResourceManager.GetString("ShipPartial", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipped.
        /// </summary>
        internal static string Shipped {
            get {
                return ResourceManager.GetString("Shipped", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Short Shipped.
        /// </summary>
        internal static string ShortShipped {
            get {
                return ResourceManager.GetString("ShortShipped", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stock in Quarantine.
        /// </summary>
        internal static string StockInQuarantine {
            get {
                return ResourceManager.GetString("StockInQuarantine", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unpaid on advance terms.
        /// </summary>
        internal static string TermsInAdvanceNotOK {
            get {
                return ResourceManager.GetString("TermsInAdvanceNotOK", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unallocated.
        /// </summary>
        internal static string Unallocated {
            get {
                return ResourceManager.GetString("Unallocated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unchecked.
        /// </summary>
        internal static string Unauthorised {
            get {
                return ResourceManager.GetString("Unauthorised", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unposted.
        /// </summary>
        internal static string Unposted {
            get {
                return ResourceManager.GetString("Unposted", resourceCulture);
            }
        }
    }
}
