Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Orders");Rebound.GlobalTrader.Site.Pages.Orders.QuoteDetail=function(n){Rebound.GlobalTrader.Site.Pages.Orders.QuoteDetail.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Pages.Orders.QuoteDetail.prototype={get_intQuoteID:function(){return this._intQuoteID},set_intQuoteID:function(n){this._intQuoteID!==n&&(this._intQuoteID=n)},get_ctlMainInfo:function(){return this._ctlMainInfo},set_ctlMainInfo:function(n){this._ctlMainInfo!==n&&(this._ctlMainInfo=n)},get_ctlQuoteLines:function(){return this._ctlQuoteLines},set_ctlQuoteLines:function(n){this._ctlQuoteLines!==n&&(this._ctlQuoteLines=n)},get_btnPrint:function(){return this._btnPrint},set_btnPrint:function(n){this._btnPrint!==n&&(this._btnPrint=n)},get_lblStatus:function(){return this._lblStatus},set_lblStatus:function(n){this._lblStatus!==n&&(this._lblStatus=n)},get_pnlStatus:function(){return this._pnlStatus},set_pnlStatus:function(n){this._pnlStatus!==n&&(this._pnlStatus=n)},initialize:function(){Rebound.GlobalTrader.Site.Pages.Orders.QuoteDetail.callBaseMethod(this,"initialize")},goInit:function(){this._ctlMainInfo&&this._ctlMainInfo.addGetDataComplete(Function.createDelegate(this,this.ctlMainInfo_GetDataComplete));this._ctlMainInfo&&this._ctlMainInfo.addSaveEditComplete(Function.createDelegate(this,this.ctlMainInfo_SaveEditComplete));this._ctlMainInfo&&this._ctlMainInfo.addSaveCloseComplete(Function.createDelegate(this,this.ctlMainInfo_SaveCloseComplete));this._ctlQuoteLines&&this._ctlQuoteLines.addPotentialStatusChange(Function.createDelegate(this,this.ctlLines_PotentialStatusChange));this._ctlQuoteLines&&this._ctlQuoteLines.addRefereshSOButtonChange(Function.createDelegate(this,this.ctlLines_RefereshSOButtonChange));this._btnPrint&&this._btnPrint.addPrint(Function.createDelegate(this,this.printQuote));this._btnPrint&&this._btnPrint.addEmail(Function.createDelegate(this,this.emailQuote));this._btnPrint&&this._btnPrint.addExtraButtonClick(Function.createDelegate(this,this.printOtherDocs));Rebound.GlobalTrader.Site.Pages.Orders.QuoteDetail.callBaseMethod(this,"goInit")},dispose:function(){this.isDisposed||(this._btnPrint&&this._btnPrint.dispose(),this._ctlMainInfo&&this._ctlMainInfo.dispose(),this._ctlQuoteLines&&this._ctlQuoteLines.dispose(),this._btnPrint=null,this._ctlMainInfo=null,this._ctlQuoteLines=null,this._intQuoteID=null,this._lblStatus=null,this._pnlStatus=null,Rebound.GlobalTrader.Site.Pages.Orders.QuoteDetail.callBaseMethod(this,"dispose"))},ctlMainInfo_GetDataComplete:function(){this._ctlQuoteLines.setFieldsFromHeader(this._ctlMainInfo.getFieldValue("hidCustomerName"),this._ctlMainInfo.getFieldValue("hidCustomerID"),this._ctlMainInfo.getFieldValue("hidContactNo"),this._ctlMainInfo.getFieldValue("hidCurrencyNo"),this._ctlMainInfo.getFieldValue("hidCurrencyCode"),this._ctlMainInfo.getFieldValue("ctlDateQuoted"),this._ctlMainInfo._blnCanCreateSO);this._ctlQuoteLines._blnAS9120=this._ctlMainInfo.getFieldValue("ctlAS9120");this.updateQuoteMainInfoControl();$R_FN.setInnerHTML(this._lblStatus,this._ctlMainInfo.getFieldValue("hidHStatus"))},ctlMainInfo_SaveEditComplete:function(){this._ctlQuoteLines._blnAS9120=this._ctlMainInfo.getFieldValue("ctlAS9120");this._ctlQuoteLines.getTabData()},ctlMainInfo_SaveCloseComplete:function(){this._ctlQuoteLines._blnAS9120=this._ctlMainInfo.getFieldValue("ctlAS9120");this._ctlQuoteLines.getTabData()},ctlLines_PotentialStatusChange:function(){this.updateQuoteMainInfoControl()},ctlLines_RefereshSOButtonChange:function(){this._ctlMainInfo.getData()},printQuote:function(){var t=$R_FN.openPrintQuoteWindow($R_ENUM$PrintObject.Quote,this._intQuoteID),n=$("#ctl00_cphMain_ctlPageTitle_ctl20_lblStatus").text();t.opener&&(n=="Pending"||n=="New"||n=="Partially Offered")&&setTimeout(()=>{this.updateQuoteStatus()},3e3)},emailQuote:function(){var t=$R_FN.openPrintQuoteWindow($R_ENUM$PrintObject.Quote,this._intQuoteID,!0),n=$("#ctl00_cphMain_ctlPageTitle_ctl20_lblStatus").text();t.opener!=null&&(n=="Pending"||n=="New"||n=="Partially Offered")&&window.addEventListener("message",function(n){n.origin===location.origin&&n.data&&this._ctlMainInfo.getData()}.bind(this),!1)},customTemplate:function(){$R_FN.openCustomTemplateWindow($R_ENUM$PrintObject.Quote,this._intQuoteID)},printOtherDocs:function(){this._btnPrint._strExtraButtonClickCommand=="EmailQOHTML"&&$R_FN.openPrintWindow($R_ENUM$PrintObject.QuoteEmail,this._intQuoteID,!0);this._btnPrint._strExtraButtonClickCommand=="PrintLog"&&$R_FN.openPrintLogWindow($R_ENUM$PrintObject.PrintLog,this._intQuoteID,!1,"Quote");this._btnPrint._strExtraButtonClickCommand=="ExportToExcel"&&this.exportClicked();this._btnPrint._strExtraButtonClickCommand=="CustomTemplate"&&this.customTemplate()},updateQuoteMainInfoControl:function(){this._ctlMainInfo.clearMessages();this._ctlMainInfo._blnAllLineContainSource=this._ctlQuoteLines._blnAllLineContainSource;this._ctlMainInfo._blnAllLineContainMSL=this._ctlQuoteLines._blnAllLineContainMSL;this._ctlMainInfo._blnAllLineContainProduct=this._ctlQuoteLines._blnAllLineContainProduct;this._ctlMainInfo.getFieldValue("ctlAS9120")==!0?(this._btnPrint&&$R_FN.showElement(this._btnPrint._element,this._ctlQuoteLines._blnAllLineContainSource),this._ctlMainInfo._ibtnCreateSalesOrder&&$R_IBTN.enableButton(this._ctlMainInfo._ibtnCreateSalesOrder,this._ctlMainInfo._blnCanCreateSO&&this._ctlMainInfo._TotalQuantityLine&&this._ctlQuoteLines._blnAllLineContainSource)):this._btnPrint&&$R_FN.showElement(this._btnPrint._element,!0);this._ctlMainInfo.getFieldValue("ctlAS9120")==!0&&this._ctlQuoteLines._blnAllLineContainSource==!1?this._ctlMainInfo.showProductSourceMessage():this._ctlMainInfo.clearMessages()},exportClicked:function(){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/QuoteMainInfo");n.set_DataObject("QuoteMainInfo");n.set_DataAction("ExportQuoteReport");n.addParameter("id",this._intQuoteID);n._intTimeoutMilliseconds=9e4;n.addDataOK(Function.createDelegate(this,this.exportComplete));n.addError(Function.createDelegate(this,this.exportError));n.addTimeout(Function.createDelegate(this,this.exportError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},exportError:function(n){this.showError(!0,n.get_ErrorMessage())},exportComplete:function(n){if(n._result.Result==1){var t=new Date,i=!1;i=this.UrlExists(window.location.origin+"/"+n._result.FileURL);i==!0&&(location.href=String.format("{0}?t={1}",n._result.FileURL,t.getTime()));t=null}},updateQuoteStatus:function(){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/QuoteMainInfo");n.set_DataObject("QuoteMainInfo");n.set_DataAction("UpdatePrintQuoteStatus");n.addParameter("id",this._intQuoteID);n._intTimeoutMilliseconds=9e4;n.addDataOK(Function.createDelegate(this,this.updateQuoteStatusComplete));n.addError(Function.createDelegate(this,this.updateQuoteStatusError));n.addTimeout(Function.createDelegate(this,this.updateQuoteStatusError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},updateQuoteStatusError:function(n){this.showError(!0,n.get_ErrorMessage())},updateQuoteStatusComplete:function(n){n._result.Result&&this._ctlMainInfo.getData()},UrlExists:function(n){var t=new XMLHttpRequest;t.open("HEAD",n,!1);try{t.send()}catch(i){}return t.status!=404}};Rebound.GlobalTrader.Site.Pages.Orders.QuoteDetail.registerClass("Rebound.GlobalTrader.Site.Pages.Orders.QuoteDetail",Rebound.GlobalTrader.Site.Pages.Content,Sys.IDisposable);