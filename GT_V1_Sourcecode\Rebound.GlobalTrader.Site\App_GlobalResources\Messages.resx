<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="AddNotAllowed_Company" xml:space="preserve">
    <value>Sorry, you do not have permissions to add a new Company</value>
  </data>
  <data name="AddNotAllowed_Credit" xml:space="preserve">
    <value>Sorry, you do not have permissions to add a new Credit Note</value>
  </data>
  <data name="AddNotAllowed_CRMA" xml:space="preserve">
    <value>Sorry, you do not have permissions to add a new Customer RMA</value>
  </data>
  <data name="AddNotAllowed_Debit" xml:space="preserve">
    <value>Sorry, you do not have permissions to add a new Debit Note</value>
  </data>
  <data name="AddNotAllowed_GoodsIn" xml:space="preserve">
    <value>Sorry, you do not have permissions to add a new Goods In Note</value>
  </data>
  <data name="AddNotAllowed_Invoice" xml:space="preserve">
    <value>Sorry, you do not have permissions to add a new Invoice</value>
  </data>
  <data name="AddNotAllowed_Lot" xml:space="preserve">
    <value>Sorry, you do not have permissions to add a new Lot</value>
  </data>
  <data name="AddNotAllowed_Manufacturer" xml:space="preserve">
    <value>Sorry, you do not have permissions to add a new Manufacturer</value>
  </data>
  <data name="AddNotAllowed_PurchaseOrder" xml:space="preserve">
    <value>Sorry, you do not have permissions to add a new Purchase Order</value>
  </data>
  <data name="AddNotAllowed_Quote" xml:space="preserve">
    <value>Sorry, you do not have permissions to add a new Quote</value>
  </data>
  <data name="AddNotAllowed_Requirement" xml:space="preserve">
    <value>Sorry, you do not have permissions to add a new Customer Requirement</value>
  </data>
  <data name="AddNotAllowed_SalesOrder" xml:space="preserve">
    <value>Sorry, you do not have permissions to add a new Sales Order</value>
  </data>
  <data name="AddNotAllowed_Service" xml:space="preserve">
    <value>Sorry, you do not have permissions to add a new Service</value>
  </data>
  <data name="AddNotAllowed_SRMA" xml:space="preserve">
    <value>Sorry, you do not have permissions to add a new Supplier RMA</value>
  </data>
  <data name="AddNotAllowed_Stock" xml:space="preserve">
    <value>Sorry, you do not have permissions to add a new Stock Item</value>
  </data>
  <data name="AllModulesVisible" xml:space="preserve">
    <value>All modules are currently visible</value>
  </data>
  <data name="ApplicationError" xml:space="preserve">
    <value>Sorry, there was a problem. The following details have been noted in your Computer's event log and emailed to Rebound staff for attention.</value>
  </data>
  <data name="BrowserWarning" xml:space="preserve">
    <value>You are using {0} Version {1}&lt;br /&gt;We recommend you use Firefox 2+, Internet Explorer 8+, Chrome 1+ or Safari 3+.</value>
  </data>
  <data name="ConfirmAuthorise" xml:space="preserve">
    <value>Are you sure you would like to authorise this order?</value>
  </data>
  <data name="ConfirmDeauthorise" xml:space="preserve">
    <value>Are you sure you would like to de-authorise this order?</value>
  </data>
  <data name="ConfirmDeleteCompanyAddress" xml:space="preserve">
    <value>Are you sure you would like to delete this address?</value>
  </data>
  <data name="DataNotFound" xml:space="preserve">
    <value>Error: No data found</value>
  </data>
  <data name="DocumentSendError" xml:space="preserve">
    <value>Sorry, there was a problem sending your message, please try again or ask your System Administrator to check the Email settings</value>
  </data>
  <data name="DocumentSentOK" xml:space="preserve">
    <value>Your message was sent successfully</value>
  </data>
  <data name="DoubleClickToViewMail" xml:space="preserve">
    <value>Double-click a message to view</value>
  </data>
  <data name="HiddenModules" xml:space="preserve">
    <value>Hidden modules</value>
  </data>
  <data name="LandedCostCalculationError" xml:space="preserve">
    <value>Sorry, there was an error</value>
  </data>
  <data name="LoginLicenceProblem" xml:space="preserve">
    <value>Sorry, there was a problem with your application licence</value>
  </data>
  <data name="LoginLicenceProblem_TooManyUsers" xml:space="preserve">
    <value>Sorry, there was a problem with your application licence&lt;br /&gt;You have exceeded your permitted number of users.</value>
  </data>
  <data name="LoginPasswordNotRecognised" xml:space="preserve">
    <value>Sorry, that password is not recognised</value>
  </data>
  <data name="LoginUsernameNotRecognised" xml:space="preserve">
    <value>Sorry, that username is not recognised</value>
  </data>
  <data name="NewMailMessages" xml:space="preserve">
    <value>You have new message(s)</value>
  </data>
  <data name="NoAccountingInfo" xml:space="preserve">
    <value>There is no accounting information to display</value>
  </data>
  <data name="OnStop" xml:space="preserve">
    <value>This Company is On Stop</value>
  </data>
  <data name="ReportParameterValidationError" xml:space="preserve">
    <value>Please enter all required parameters</value>
  </data>
  <data name="RFQSendError" xml:space="preserve">
    <value>Sorry, there was a problem sending your message, please try again or ask your System Administrator to check the Email settings</value>
  </data>
  <data name="RFQSentOK" xml:space="preserve">
    <value>Your message was sent successfully</value>
  </data>
  <data name="SavedOK" xml:space="preserve">
    <value>Your changes were saved successfully</value>
  </data>
  <data name="SelectAddress" xml:space="preserve">
    <value>Select address to view or edit</value>
  </data>
  <data name="SelectContactToView" xml:space="preserve">
    <value>Select contact to view</value>
  </data>
  <data name="SOAuthorisation_OverCreditLimit" xml:space="preserve">
    <value>The total of open orders takes the company over its credit limit, this Sales Order cannot be authorised</value>
  </data>
  <data name="SOLines_OverCreditLimit" xml:space="preserve">
    <value>This line would take the company over its credit limit, it cannot be posted</value>
  </data>
  <data name="StockQuarantined" xml:space="preserve">
    <value>This Stock is Quarantined</value>
  </data>
  <data name="SureDeleteAddress" xml:space="preserve">
    <value>Are you sure you would like to delete this address?</value>
  </data>
  <data name="SureDeleteContact" xml:space="preserve">
    <value>Are you sure you would like to delete this contact?</value>
  </data>
  <data name="SureDeleteContactLogItem" xml:space="preserve">
    <value>Are you sure you would like to delete this contact log item?</value>
  </data>
  <data name="YouSearchedFor" xml:space="preserve">
    <value>You searched for</value>
  </data>
  <data name="Sending" xml:space="preserve">
    <value>Sending</value>
  </data>
  <data name="ConfirmApprove" xml:space="preserve">
    <value>Are you sure you would like to approve this order?</value>
  </data>
  <data name="ConfirmDisapprove" xml:space="preserve">
    <value>Are you sure you would like to un-approve this order?</value>
  </data>
  <data name="ConfirmExport" xml:space="preserve">
    <value>Are you sure you would like to export this invoice?</value>
  </data>
  <data name="ConfirmRelease" xml:space="preserve">
    <value>Are you sure you would like to release this invoice for editing?</value>
  </data>
  <data name="ConfirmAppSettingsEditAutoApproveSO" xml:space="preserve">
    <value>You have chosen to auto approve SOs: All unauthorised SOs will be authorised and cannot be undone. Are you sure you would like to continue?</value>
  </data>
  <data name="SOPostLine_OverCreditLimit" xml:space="preserve">
    <value>The balance of all posted line takes the company over its credit limit, this Sales Order cannot be posted</value>
  </data>
  <data name="PrintLabelFolderMessage" xml:space="preserve">
    <value>Please share the required folder or contact network administration</value>
  </data>
  <data name="AddNotAllowed_SupplierInvoice" xml:space="preserve">
    <value>Sorry, you do not have permissions to add a new Supplier Invoice</value>
  </data>
  <data name="RefereshMessage" xml:space="preserve">
    <value>Failure – Due to Page Refresh</value>
  </data>
  <data name="AS9120Message" xml:space="preserve">
    <value>To edit this, please remove all lines</value>
  </data>
  <data name="CCLineMessage" xml:space="preserve">
    <value>Signed for and on behalf of Rebound Electronics (UK) Ltd.</value>
  </data>
  <data name="ForgotMessage" xml:space="preserve">
    <value>Your GT login password: {0}</value>
  </data>
  <data name="ForgotPwdSubject" xml:space="preserve">
    <value>Forgot your password</value>
  </data>
  <data name="ForgotPasswordEmailSent" xml:space="preserve">
    <value>Your credentials has been sent to your email address</value>
  </data>
  <data name="ForgotUserName" xml:space="preserve">
    <value>Dear User,

As requested, please find bellow the login detail 


{0}

Thanks,
Admin</value>
  </data>
  <data name="ForgotUserSubject" xml:space="preserve">
    <value>Forgot your user name and password</value>
  </data>
  <data name="LoginUsernameEmailNotRecognised" xml:space="preserve">
    <value>Sorry, that username or email is not recognised</value>
  </data>
  <data name="AddNotAllowed_BOM" xml:space="preserve">
    <value>Sorry, you do not have permissions to add a new HUBRFQ</value>
  </data>
  <data name="CreateIPOAlreadyExist" xml:space="preserve">
    <value>IPO already created from sales order.</value>
  </data>
  <data name="CreateIPOFailed" xml:space="preserve">
    <value>Some error occured while processing your request.</value>
  </data>
  <data name="CreateIPONotInSR" xml:space="preserve">
    <value>Sales order line not in sourcing result.</value>
  </data>
  <data name="CreateIPOSuccess" xml:space="preserve">
    <value>Thank you for your order. Please note your order will not be processed until your Sales Order is checked</value>
  </data>
  <data name="BOMReleased" xml:space="preserve">
    <value>HUBRFQ Released</value>
  </data>
  <data name="PurchaseRequest" xml:space="preserve">
    <value>HUBRFQ {0} sent for Price Request</value>
  </data>
  <data name="SOLines_OverCreditLimitIPO" xml:space="preserve">
    <value>This line would take the company over its credit limit, it cannot be created IPO</value>
  </data>
  <data name="CreateIPOSubject" xml:space="preserve">
    <value>New IPO created</value>
  </data>
  <data name="BOMAssigned" xml:space="preserve">
    <value>HUBRFQ Assigned</value>
  </data>
  <data name="ConfirmAuthoriseCompanyOnStop" xml:space="preserve">
    <value>Are you sure you would like to authorise this order? &lt;br /&gt; You are checking/authorising an order against an account currently on stop.</value>
  </data>
  <data name="SOLRecalled" xml:space="preserve">
    <value>Sourcing Result provided by PO HUb for this salesorder line(s) ( {0} ) are recalled. Please contact your buyer</value>
  </data>
  <data name="BOMNoBid" xml:space="preserve">
    <value>HUBRFQ No-Bid</value>
  </data>
  <data name="ConfirmNoBid" xml:space="preserve">
    <value>Are you sure you would like to No-Bid this HUBRFQ?</value>
  </data>
  <data name="ConfirmRecallNoBid" xml:space="preserve">
    <value>Are you sure you would like to Recall No-Bid this HUBRFQ?</value>
  </data>
  <data name="ConfirmExportCredit" xml:space="preserve">
    <value>Are you sure you would like to export this Credit Note?</value>
  </data>
  <data name="ConfirmReleaseCredit" xml:space="preserve">
    <value>Are you sure you would like to release this Credit Note for editing?</value>
  </data>
  <data name="SOAuthorise" xml:space="preserve">
    <value>Sales order has been checked: {0}</value>
  </data>
  <data name="ConfirmReleasePO" xml:space="preserve">
    <value>Are you sure you would like to Release selected PO line locked by EPR?</value>
  </data>
  <data name="ConfirmUnReleasePO" xml:space="preserve">
    <value>Are you sure you would like to Un-Release this PO line?</value>
  </data>
  <data name="ConfirmHold" xml:space="preserve">
    <value>Are you sure you would like to hold this invoice export?</value>
  </data>
  <data name="ConfirmUnHold" xml:space="preserve">
    <value>Are you sure you would like to un-hold this invoice export?</value>
  </data>
  <data name="PartWatchMatchMsg" xml:space="preserve">
    <value>You have a new Partwatch Match</value>
  </data>
  <data name="PartERAIMessage" xml:space="preserve">
    <value>PRODUCT IS ERAI HIGH RISK</value>
  </data>
  <data name="SupplierERAIMessage" xml:space="preserve">
    <value>SUPPLIER IS ERAI REPORTED</value>
  </data>
  <data name="SupplierApprovalByLineManager" xml:space="preserve">
    <value>Supplier Approval By Line Manager</value>
  </data>
  <data name="SupplierApprovalByQuality" xml:space="preserve">
    <value>Supplier Approval Completed</value>
  </data>
  <data name="SupplierApprovalSupplierChnage" xml:space="preserve">
    <value>Supplier Approval request is no longer required for PO :</value>
  </data>
  <data name="GIQuerySales" xml:space="preserve">
    <value>Goods-In Line ({0}) status</value>
  </data>
  <data name="ConfirmExportDebit" xml:space="preserve">
    <value>Are you sure you would like to export this Debit Note?</value>
  </data>
  <data name="ConfirmReleaseDebit" xml:space="preserve">
    <value>Are you sure you would like to release this Debit Note for editing?</value>
  </data>
  <data name="SupplierApprovalForQualityEscalate" xml:space="preserve">
    <value>Supplier Approval Request Escalated</value>
  </data>
  <data name="SupplierApprovalForQualityReview" xml:space="preserve">
    <value>Supplier Approval Request For Quality Team Review</value>
  </data>
  <data name="GT" xml:space="preserve">
    <value>GT</value>
  </data>
  <data name="SORRequestSubject" xml:space="preserve">
    <value>Sales Order ~SONO~ Approval Request</value>
  </data>
  <data name="OGELApprovalStatus" xml:space="preserve">
    <value>OGEL Approval Status</value>
  </data>
  <data name="SendExportApproval" xml:space="preserve">
    <value>Sales Order {0} Export Approval Notification</value>
  </data>
  <data name="HUBRFQRaised" xml:space="preserve">
    <value>HUBRFQ Raised {0} : Reverse Logistic Offer Match</value>
  </data>
  <data name="OnPremierCustomer" xml:space="preserve">
    <value />
  </data>
  <data name="OnTier2PremierCustomer" xml:space="preserve">
    <value />
  </data>
  <data name="BOMManagerReleased" xml:space="preserve">
    <value>BOM Manager Released</value>
  </data>
  <data name="PurchaseRequestBOMManager" xml:space="preserve">
    <value>BOM Manager {0} sent for Price Request</value>
  </data>
  <data name="CloseBOMManager" xml:space="preserve">
    <value>BOM Manager {0} has been closed.</value>
  </data>
  <data name="AddNotAllowed_PriceQuoteUtility" xml:space="preserve">
    <value>Sorry, you do not have permissions to add a new Price Quotes</value>
  </data>
  <data name="BOMManagerNoBid" xml:space="preserve">
    <value>BOM Manager No Bid</value>
  </data>
  <data name="BOMManagerRecall" xml:space="preserve">
    <value>BOM Manager Recall</value>
  </data>
  <data name="BOMManagerRecallNoBid" xml:space="preserve">
    <value>BOM Manager Recall No Bid</value>
  </data>
  <data name="CompanySanctioned" xml:space="preserve">
    <value>This Company is Sanctioned</value>
  </data>
  <data name="POCloseSubject" xml:space="preserve">
    <value>Purchase Order {0}  Line ({1}) Closed</value>
  </data>
  <data name="ConfirmReadyToShip" xml:space="preserve">
    <value>This functionality allows a stopped company's sales order to ship, Are you sure?</value>
  </data>
  <data name="AddNotAllowed_GroupCodeCompany" xml:space="preserve">
    <value>Sorry, you do not have permissions to add a new Group Code</value>
  </data>
  <data name="AddNotAllowed_GroupCodeCompanyAdd" xml:space="preserve">
    <value>Sorry, you do not have permissions to add a new Group Code</value>
  </data>
  <data name="CreateIPOFailedNotEnoughStock" xml:space="preserve">
    <value>Available Stock is running out, please check the associated Stock or update SO's QTY</value>
  </data>
  <data name="GIQuarantine" xml:space="preserve">
    <value>NOTIFICATION - Sales Order(s) Deallocation</value>
  </data>
  <data name="RLStockIntroduction1" xml:space="preserve">
    <value>Dear Reverse Logistics Team Member,

A HUBRFQ has been raised for a part that we have in stock. Please review the details of the {0} here: 
Company Name: {1}

{2}

&lt;i&gt;Note: Each notification email only contains a maximum of 20 records&lt;/i&gt;

Please visit and action.

Kind Regards
Global Trader</value>
  </data>
  <data name="RLStockIntroduction2" xml:space="preserve">
    <value>Dear Reverse Logistics Team Member,

HUBRFQ(s) has been raised for a part which we have in stock. Please review details of the HUBRFQ(s) here:
{0}
&lt;i&gt;Note: Each notification email only contains a maximum of 20 records&lt;/i&gt;

Please visit and action.

Kind Regards
Global Trader</value>
  </data>
  <data name="RLStockIntroductionOutlook1" xml:space="preserve">
    <value>Dear Reverse Logistics Team Member,&lt;br/&gt;

A HUBRFQ has been raised for a part that we have in stock. Please review the details of the {0} here:&lt;br/&gt;
Company Name: {1}</value>
  </data>
  <data name="RLStockIntroductionOutlook2" xml:space="preserve">
    <value>Dear Reverse Logistics Team Member,&lt;br/&gt;&lt;br/&gt;

HUBRFQ(s) has been raised for a part which we have in stock. Please review details of the HUBRFQ(s) here:
</value>
  </data>
  <data name="RLStockSubject" xml:space="preserve">
    <value>HUBRFQ(s) raised for RL In-Stock {0}</value>
  </data>
  <data name="RLStockSubject1" xml:space="preserve">
    <value>A HUBRFQ {0} raised for RL In-Stock</value>
  </data>
  <data name="AddNotAllowed_IHS" xml:space="preserve">
    <value>Sorry, you do not have permissions to add a new Product Catalogue</value>
  </data>
  <data name="SOLines_DifferCurrency" xml:space="preserve">
    <value>The order currency differs from the customers base currency, this could lead to a variance in line total vs order value.</value>
  </data>
</root>