﻿using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.DAL;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Web;
using Newtonsoft.Json;
using System.Threading.Tasks;
using System.Net;
using System.Net.Http.Headers;

namespace Rebound.GlobalTrader.Site.Code.Common
{
    public static class APIIntegration
    {

        public static List<APIExternalLinks> APIOfferIntegration(string Part, bool IsServerLocal, bool HitAPI)
        {
            string SourcingName = "";
            List<ApiKeyDetails> lstApiDetails = APIExternalLinks.APIExternalLinksDetails("Sourcing", "FE", Convert.ToInt32(SessionManager.ClientID), IsServerLocal);
            List<APIExternalLinks> lst = new List<APIExternalLinks>();
            List<APIExternalLinks> newlst = null;
            ApiResponse apiResponse = new ApiResponse();
            try
            {
                foreach (var rw in lstApiDetails)
                {
                    if (HitAPI)
                    {
                        apiResponse = APIAPIExternalLinksOffers(rw.URL, rw.HostName, rw.Licensekey, Part);
                        if (apiResponse.Status)
                        {
                            newlst = APIExternalLinks.SourceMultipleParts(apiResponse.Result, Convert.ToInt32(SessionManager.ClientID), Part, IsServerLocal, rw.ApiName, rw.ApiURLKeyId);
                        }
                    }
                    else
                    {

                        newlst = APIExternalLinks.SourceFromDBMultipleParts(SessionManager.ClientID, Part, IsServerLocal, SourcingName, rw.ApiURLKeyId);
                    }
                }
                if (newlst != null)
                    lst.AddRange(newlst);

                return lst;
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        public static ApiResponse APIAPIExternalLinksOffers(string ApiURL, string HostName, string Xorbweaverlicensekey, string partSearch)
        {
            string DATA = "{" + '"' + "parts" + '"' + ':' + '[' + partSearch + ']' + "}";
            ApiResponse apiResponse = new ApiResponse();

            System.Net.HttpWebRequest request = (System.Net.HttpWebRequest)System.Net.WebRequest.Create(ApiURL);
            request.Method = "POST";
            request.ContentType = "application/json";
            request.Accept = "application/json,text/javascript";
            request.Host = HostName; //"api.orbweaver.com"
            request.Headers.Add("x-orbweaver-licensekey", Xorbweaverlicensekey);        //"IA7VB-DAUOL-2QAWQ-FGVHF-53SQK"
            request.ContentLength = DATA.Length;
            using (System.IO.Stream webStream = request.GetRequestStream())
            using (System.IO.StreamWriter requestWriter = new System.IO.StreamWriter(webStream, System.Text.Encoding.ASCII))
            {
                requestWriter.Write(DATA);
            }

            try
            {
                System.Net.WebResponse webResponse = request.GetResponse();
                using (System.IO.Stream webStream = webResponse.GetResponseStream() ?? System.IO.Stream.Null)
                using (System.IO.StreamReader responseReader = new System.IO.StreamReader(webStream))
                {
                    string response = responseReader.ReadToEnd();
                    //return response;
                    apiResponse.Status = true;
                    apiResponse.Result = response;
                    return apiResponse;
                }
            }
            catch (Exception e)
            {
                Console.Out.WriteLine("-----------------");
                Console.Out.WriteLine(e.Message);
                //return e.Message;
                apiResponse.Status = false;
                apiResponse.Result = e.Message;
                return apiResponse;
            }
        }

        public static List<LyticaAPI> LyticaApiCall(string partJson, int? UpdatedBy)
        {
            var configs = BLL.SettingItem.GetList();
            var config = configs.Find(x => x.SettingItemName.Equals("LyticaApiTurnOn", StringComparison.InvariantCultureIgnoreCase));
            if (config.DefaultValue.Equals("false", StringComparison.InvariantCultureIgnoreCase))
            {
                return new List<LyticaAPI>();
            }
            HttpMessageHandler handler = new HttpClientHandler();
            var url = ConfigurationManager.AppSettings["LyticaAPIurl"];
            string userid = ConfigurationManager.AppSettings["LyticaAPUserId"];
            string password = ConfigurationManager.AppSettings["LyticaAPPassword"];
            var httpClient = new HttpClient(handler)
            {
                BaseAddress = new Uri(url),
                Timeout = new TimeSpan(0, 2, 0)
            };

            httpClient.DefaultRequestHeaders.Add("ContentType", "application/json");

            //This is the key section you were missing    
            var plainTextBytes = System.Text.Encoding.UTF8.GetBytes(userid + ":" + password);
            string val = System.Convert.ToBase64String(plainTextBytes);
            httpClient.DefaultRequestHeaders.Add("Authorization", "Basic " + val);
            //var myContent = JsonConvert.SerializeObject("{" + '"' + "origMPN" + '"' + ":" + '"' + "WR08X1200FTL" + '"' + "}");
            //var buffer = System.Text.Encoding.UTF8.GetBytes(myContent);
            //var byteContent = new ByteArrayContent(buffer);
            var data = partJson;// @"{""origMPN"": ""1060-16-0988|RC0603FR-0720KL|WR08X1200FTL""} ";
            var content = new StringContent(data, Encoding.UTF8, "application/json");
            HttpResponseMessage response = httpClient.PostAsync(url, content).Result;
            //string content = string.Empty;
            string APIResponseJson = string.Empty;
            using (StreamReader stream = new StreamReader(response.Content.ReadAsStreamAsync().Result))
            {
                APIResponseJson = stream.ReadToEnd();
            }
            //Insert the coming json in db
            List<LyticaAPI> LyticaApiList = APIExternalLinks.InsretLyticaAPI(APIResponseJson, UpdatedBy);
            return LyticaApiList;
        }

        public static List<LyticaAPI> GetLyticaDataFromApi(string partJson)
        {
            var configs = BLL.SettingItem.GetList();
            var config = configs.Find(x => x.SettingItemName.Equals("LyticaApiTurnOn", StringComparison.InvariantCultureIgnoreCase));
            if (config.DefaultValue.Equals("false", StringComparison.InvariantCultureIgnoreCase))
            {
                return new List<LyticaAPI>();
            }

            HttpMessageHandler handler = new HttpClientHandler();
            var url = ConfigurationManager.AppSettings["LyticaAPIurl"];
            string userid = ConfigurationManager.AppSettings["LyticaAPUserId"];
            string password = ConfigurationManager.AppSettings["LyticaAPPassword"];
            var httpClient = new HttpClient(handler)
            {
                BaseAddress = new Uri(url),
                Timeout = new TimeSpan(0, 2, 0)
            };

            httpClient.DefaultRequestHeaders.Add("ContentType", "application/json");

            //This is the key section you were missing    
            var plainTextBytes = System.Text.Encoding.UTF8.GetBytes(userid + ":" + password);
            string val = System.Convert.ToBase64String(plainTextBytes);
            httpClient.DefaultRequestHeaders.Add("Authorization", "Basic " + val);
            //var myContent = JsonConvert.SerializeObject("{" + '"' + "origMPN" + '"' + ":" + '"' + "WR08X1200FTL" + '"' + "}");
            //var buffer = System.Text.Encoding.UTF8.GetBytes(myContent);
            //var byteContent = new ByteArrayContent(buffer);
            var data = partJson;// @"{""origMPN"": ""1060-16-0988|RC0603FR-0720KL|WR08X1200FTL""} ";
            var content = new StringContent(data, Encoding.UTF8, "application/json");

            var task = Task.Run(async () =>
            {
                return await httpClient.PostAsync(url, content);
            });

            var response = task.Result;
            //HttpResponseMessage responseMesage = response.Result;
            //string content = string.Empty;
            string APIResponseJson = string.Empty;
            using (StreamReader stream = new StreamReader(response.Content.ReadAsStreamAsync().Result))
            {
                APIResponseJson = stream.ReadToEnd();
            }
            List<LyticaAPI> LyticaApiList = new List<LyticaAPI>();
            if (!string.IsNullOrEmpty(APIResponseJson))
            {
                LyticaApiList = JsonConvert.DeserializeObject<List<LyticaAPI>>(APIResponseJson);

            }

            return LyticaApiList;
        }

        public static OAuth2AccessTokenModel GetDigikeyAccessToken()
        {
            //ServicePointManager.SecurityProtocol = (SecurityProtocolType)3072;
            //ServicePointManager.ServerCertificateValidationCallback = delegate { return true; };

            // Build up the body for the token request
            var body = new List<KeyValuePair<string, string>>
            {
                new KeyValuePair<string, string>(OAuth2Constants.ClientId, ConfigurationManager.AppSettings["DigikeyClientId"]),
                new KeyValuePair<string, string>(OAuth2Constants.ClientSecret, ConfigurationManager.AppSettings["DigikeyClientSecret"]),
                new KeyValuePair<string, string>(OAuth2Constants.GrantType, OAuth2Constants.GrantTypes.ClientCredentials)
            };

            var digikeyBaseUrl = ConfigurationManager.AppSettings["DigikeyBaseUrl"];
            var tokenEndpoint = digikeyBaseUrl + ConfigurationManager.AppSettings["DigikeyTokenUrl"];

            var httpClient = new HttpClient { BaseAddress = new Uri(digikeyBaseUrl) };
            var requestMessage = new HttpRequestMessage(HttpMethod.Post, new Uri(tokenEndpoint));
            requestMessage.Headers.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
            requestMessage.Content = new FormUrlEncodedContent(body);

            var task = Task.Run(async () =>
            {
                return await httpClient.SendAsync(requestMessage).ConfigureAwait(false);
            });
            var tokenResponse = task.Result;

            var text = tokenResponse.Content.ReadAsStringAsync();

            // Check if there was an error in the response
            if (tokenResponse.IsSuccessStatusCode)
            {
                tokenResponse.EnsureSuccessStatusCode();
                // Deserialize and return model
                var errorResponse = JsonConvert.DeserializeObject<OAuth2AccessTokenModel>(text.Result);

                return errorResponse;
            }
            else
            {
                return new OAuth2AccessTokenModel();
            }
        }

        public static HttpResponseMessage DigikeyKeywordSearch(string keyword, string accessToken, string tokenType, int offset = 0)
        {
            try
            {
                var digikeyBaseUrl = ConfigurationManager.AppSettings["DigikeyBaseUrl"];
                var searchEndpoint = digikeyBaseUrl + ConfigurationManager.AppSettings["DigikeyProductsUrl"];
                var clientId = ConfigurationManager.AppSettings["DigikeyClientId"];
                var request = new KeywordSearchRequest
                {
                    Keywords = keyword,
                    RecordCount = 50, //50 is maximun per request
                    Offset = offset
                };

                var httpClient = new HttpClient { BaseAddress = new Uri(digikeyBaseUrl) };
                httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue(tokenType, accessToken);
                httpClient.DefaultRequestHeaders.Add("X-DIGIKEY-Client-Id", clientId);

                var requestMessage = new HttpRequestMessage(HttpMethod.Post, new Uri(searchEndpoint));
                requestMessage.Headers.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
                
                requestMessage.Content = new StringContent(JsonConvert.SerializeObject(request), Encoding.UTF8, "application/json"); ;

                var task = Task.Run(async () =>
                {
                    return await httpClient.SendAsync(requestMessage).ConfigureAwait(false);
                });

                var response = task.Result;

                var postResponse = new HttpResponseMessage();

                if (response.IsSuccessStatusCode)
                {
                    if (response.Content != null)
                    {
                        postResponse = response;
                    }
                }
                else
                {
                    var errorMessage = response.Content.ReadAsStringAsync();
                    var resp = new HttpResponseMessage(response.StatusCode)
                    {
                        Content = response.Content,
                        ReasonPhrase = response.ReasonPhrase
                    };
                    throw new System.Exception(response.ReasonPhrase);
                }

                return postResponse;
            }
            catch (Exception exception)
            {
                throw exception;
            }
        }
    }
}