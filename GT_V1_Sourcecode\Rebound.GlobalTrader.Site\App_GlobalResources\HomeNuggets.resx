<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="AllPurchaseOrdersDueIn" xml:space="preserve">
    <value>All Purchase Orders Due In</value>
  </data>
  <data name="AllPurchaseOrdersDueInWithin7Days" xml:space="preserve">
    <value>All Purchase Orders Due In (Within 7 Days)</value>
  </data>
  <data name="AllSalesOrdersReadyToShipWithin7Days" xml:space="preserve">
    <value>All Sales Orders Ready To Ship (Within 7 Days)</value>
  </data>
  <data name="AllStatistics" xml:space="preserve">
    <value>Statistics (as of last day's trading)</value>
  </data>
  <data name="ApprovedCustomersOnStop" xml:space="preserve">
    <value>Approved Customers On Stop</value>
  </data>
  <data name="Bom" xml:space="preserve">
    <value>HUBRFQ</value>
  </data>
  <data name="MyApprovedCustomersOnStop" xml:space="preserve">
    <value>My Approved Customers On Stop</value>
  </data>
  <data name="MyApprovedCustomersOnStop_ForUser" xml:space="preserve">
    <value>{0}'s Approved Customers On Stop</value>
  </data>
  <data name="MyMessages" xml:space="preserve">
    <value>My Messages</value>
  </data>
  <data name="MyOpenPurchaseOrders" xml:space="preserve">
    <value>My Open Purchase Orders</value>
  </data>
  <data name="MyOpenPurchaseOrders_ForUser" xml:space="preserve">
    <value>{0}'s Open Purchase Orders</value>
  </data>
  <data name="MyOpenQuotes" xml:space="preserve">
    <value>My Open Quotes</value>
  </data>
  <data name="MyOpenQuotes_ForUser" xml:space="preserve">
    <value>{0}'s Open Quotes</value>
  </data>
  <data name="MyOpenRequirements" xml:space="preserve">
    <value>My Open Requirements</value>
  </data>
  <data name="MyOpenRequirements_ForUser" xml:space="preserve">
    <value>{0}'s Open Requirements</value>
  </data>
  <data name="MyOpenSalesOrders" xml:space="preserve">
    <value>My Open Sales Orders</value>
  </data>
  <data name="MyOpenSalesOrders_ForUser" xml:space="preserve">
    <value>{0}'s Open Sales Orders</value>
  </data>
  <data name="MyRecentActivity" xml:space="preserve">
    <value>My Recent Activity</value>
  </data>
  <data name="MyRecentActivity_ForUser" xml:space="preserve">
    <value>{0}'s Recent Activity</value>
  </data>
  <data name="OrdersDueOut_ForUser" xml:space="preserve">
    <value>{0}'s Order Due Out</value>
  </data>
  <data name="MyRecentlyReceivedOrders" xml:space="preserve">
    <value>My Recently Received Orders</value>
  </data>
  <data name="MyRecentlyReceivedOrders_ForUser" xml:space="preserve">
    <value>{0}'s Recently Received Orders</value>
  </data>
  <data name="MyRecentlyShippedOrders" xml:space="preserve">
    <value>My Recently Shipped Orders</value>
  </data>
  <data name="MyRecentlyShippedOrders_ForUser" xml:space="preserve">
    <value>{0}'s Recently Shipped Orders</value>
  </data>
  <data name="MyScheduledTasks" xml:space="preserve">
    <value>My Scheduled Tasks</value>
  </data>
  <data name="MyStatistics" xml:space="preserve">
    <value>My Statistics (as of last day's trading)</value>
  </data>
  <data name="MyStatistics_ForUser" xml:space="preserve">
    <value>{0}'s Statistics (as of last day's trading)</value>
  </data>
  <data name="MyToDoList" xml:space="preserve">
    <value>My To Do List</value>
  </data>
  <data name="PartsBeingOrderedToday" xml:space="preserve">
    <value>Parts Being Ordered Today</value>
  </data>
  <data name="PartsBeingOrderedToday_ForUser" xml:space="preserve">
    <value>{0}'s Parts Being Ordered Today</value>
  </data>
  <data name="ReceivedOrders" xml:space="preserve">
    <value>Received Orders</value>
  </data>
  <data name="SalesOrdersReadyToShip" xml:space="preserve">
    <value>Sales Orders Ready To Ship</value>
  </data>
  <data name="ShippedOrders" xml:space="preserve">
    <value>Shipped Orders</value>
  </data>
  <data name="StockItemsBelowTheMinimumQuantity" xml:space="preserve">
    <value>Stock Items Below The Minimum Quantity</value>
  </data>
  <data name="TableActivity" xml:space="preserve">
    <value>Recent Activity</value>
  </data>
  <data name="TodayOpenPurchaseOrders" xml:space="preserve">
    <value>Un-Approved Purchase Orders</value>
  </data>
  <data name="TodaysReceivedOrders" xml:space="preserve">
    <value>Today's Received Orders</value>
  </data>
  <data name="TodaysShippedOrders" xml:space="preserve">
    <value>Today's Shipped Orders</value>
  </data>
  <data name="TopSalespersons" xml:space="preserve">
    <value>Top Salespeople This Month</value>
  </data>
  <data name="UncheckedIPO" xml:space="preserve">
    <value>IPOs waiting SO approval</value>
  </data>
  <data name="OpenSupplierPOApproval" xml:space="preserve">
    <value>My Supplier Approvals</value>
  </data>
  <data name="UnProcessSalesOrders" xml:space="preserve">
    <value>Un-Process Sales Orders</value>
  </data>
  <data name="OrdersDueOut" xml:space="preserve">
    <value>Orders Due Out</value>
  </data>
  <data name="CustomerOrderValue_ForUser" xml:space="preserve">
    <value>Customer Order Value</value>
  </data>
  <data name="PODeliveryStatus" xml:space="preserve">
    <value>Purchase Order Delivery Status</value>
  </data>
  <data name="MyGIQueries" xml:space="preserve">
    <value>My GI Queries</value>
  </data>
  <data name="GT" xml:space="preserve">
    <value>GT</value>
  </data>
  <data name="PowerBIActivity" xml:space="preserve">
    <value>My Power BI Report Activity</value>
  </data>
  <data name="PowerBIActivity_ForUser" xml:space="preserve">
    <value>{0}'s Power BI Report Activity</value>
  </data>
  <data name="PowerBISalesDashboard" xml:space="preserve">
    <value>PowerBI Sales Dashboard</value>
  </data>
  <data name="MyQualityGIQueries" xml:space="preserve">
    <value>Quality Team GI Queries</value>
  </data>
  <data name="BOMManager" xml:space="preserve">
    <value>BOM Manager</value>
  </data>
</root>