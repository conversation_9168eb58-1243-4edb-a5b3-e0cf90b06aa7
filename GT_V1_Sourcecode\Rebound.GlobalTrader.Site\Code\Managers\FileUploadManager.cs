/*
Marker     Changed by      Date         Remarks
[001]      Vinay           07/05/2012   This need to upload pdf document for different section
[002]      Vinay           05/09/2012   Print Label
[003]      Vinay           19/10/2012   Print Label
[004]      Vinay           27/03/2015   Ticket Number. 	221
[005]      Umendra         30/10/2020   Import Source Tool 

*/

using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.BLL;
using System.Text;
using System.IO;
using System.Drawing;
using System.Collections.Generic;
using Microsoft.Azure.Storage;
using Microsoft.Azure.Storage.Blob;
using Microsoft.Azure.Storage.Auth;
using Rebound.GlobalTrader.Site.Code.Common;

namespace Rebound.GlobalTrader.Site
{
    public class FileUploadManager
    {

        internal const int DOCUMENT_HEADER_IMAGE_WIDTH = 720;

        internal static System.Drawing.Bitmap GetResizedBitmapConstrainedByWidth(System.Drawing.Image img, int intConstrainedWidth, int intResolution)
        {
            Double dblAspectRatio = (double)img.Width / (double)img.Height;
            int intH = img.Height;
            int intW = img.Width;
            if (img.Width != intConstrainedWidth)
            {
                intW = intConstrainedWidth;
                intH = (int)((double)intConstrainedWidth / dblAspectRatio);
            }
            Bitmap bmp = new Bitmap(img, intW, intH);
            bmp.SetResolution(intResolution, intResolution);
            return bmp;
        }

        internal static System.Drawing.Bitmap GetResizedBitmapConstrainedByHeight(System.Drawing.Image img, int intConstrainedHeight, int intResolution)
        {
            Double dblAspectRatio = (double)img.Height / (double)img.Width;
            int intH = img.Height;
            int intW = img.Width;
            if (img.Height != intConstrainedHeight)
            {
                intH = intConstrainedHeight;
                intW = (int)((double)intConstrainedHeight / dblAspectRatio);
            }
            Bitmap bmp = new Bitmap(img, intW, intH);
            bmp.SetResolution(intResolution, intResolution);
            return bmp;
        }

        internal static byte[] BitmapToByteArray(System.Drawing.Bitmap bmp, System.Drawing.Imaging.ImageFormat frmImageFormat)
        {
            MemoryStream objMS = new MemoryStream();
            bmp.Save(objMS, frmImageFormat);
            return objMS.ToArray();
        }

        internal static byte[] ImageToByteArray(System.Drawing.Image img, System.Drawing.Imaging.ImageFormat frmImageFormat)
        {
            MemoryStream objMS = new MemoryStream();
            img.Save(objMS, frmImageFormat);
            return objMS.ToArray();
        }

        #region Temporary Upload Files

        internal static string GetTemporaryUploadFilePath()
        {
            return HttpContext.Current.Server.MapPath(String.Format("~/User/UploadTemp/"));
        }

        internal static string GetTemporaryUploadFilePath(string strFile)
        {
            return String.Format("{0}{1}", GetTemporaryUploadFilePath(), strFile);
        }
        internal static string GetTnCUploadFilePath()
        {
            return HttpContext.Current.Server.MapPath(String.Format("~/User/TnC/"));
        }

        /// <summary>
        /// Clears files from UploadTemp folder that are at least a day old
        /// </summary>
        internal static void ClearOldTemporaryFiles()
        {
            try
            {
                foreach (string strFile in Directory.GetFiles(GetTemporaryUploadFilePath()))
                {
                    //Espire: 07 Jan 21
                    FileInfo fi = new FileInfo(strFile);
                    if (fi.LastWriteTime <= DateTime.Now.AddDays(-1))
                    {
                        fi.Delete();
                    }
                    //string strName = Path.GetFileNameWithoutExtension(strFile);
                    //int intFileDay = Convert.ToInt32(strName.Substring(0, 8));
                    //int intYesterday = (DateTime.Now.Year * 10000) + (DateTime.Now.Month * 100) + DateTime.Now.Day - 1;
                    //if (intFileDay <= intYesterday) File.Delete(strFile);
                }
            }
            catch { }
        }

        internal static string GetTempFilename(string strOriginalName)
        {
            return GetTempFilename(strOriginalName, DateTime.Now);
        }

        /// <summary>
        /// Strings the current date (in an orderable form) to the front of the passed filename to ensure originality
        /// </summary>
        internal static string GetTempFilename(string strOriginalName, DateTime dtm)
        {
            string str = dtm.Year.ToString();
            str += (dtm.Month >= 10) ? dtm.Month.ToString() : string.Format("0{0}", dtm.Month);
            str += (dtm.Day >= 10) ? dtm.Day.ToString() : string.Format("0{0}", dtm.Day);
            str += "_";
            str += (dtm.Hour >= 10) ? dtm.Hour.ToString() : string.Format("0{0}", dtm.Hour);
            str += (dtm.Minute >= 10) ? dtm.Minute.ToString() : string.Format("0{0}", dtm.Minute);
            str += "_";
            str += SessionManager.LoginID;
            str += "_";
            str += strOriginalName;
            return str;
        }

        #endregion

        #region CSVs

        internal static string GetCSVFilePath_Absolute()
        {
            return HttpContext.Current.Server.MapPath(GetCSVFilePath_Raw());
        }

        internal static string GetCSVFilePath_Relative(bool blnIncludeTilde)
        {
            string strPath = GetCSVFilePath_Raw();
            if (!blnIncludeTilde) strPath = strPath.Replace("~/", "");
            return strPath;
        }

        internal static string GetCSVFilePath_Raw()
        {
            return "~/User/CSV";
        }

        internal static string GetUploadTempCSVFilePath_Relative(bool blnIncludeTilde)
        {
            string strPath = GetUploadTempFilePath_Raw();
            if (!blnIncludeTilde) strPath = strPath.Replace("~/", "");
            return strPath;
        }

        internal static string GetUploadTempFilePath_Raw()
        {
            return "~/User/UploadTemp";
        }

        /// <summary>
        /// Clears all CSVs for the current login
        /// </summary>
        internal static void ClearLoginsOldCSVs()
        {
            try
            {
                foreach (string strFile in Directory.GetFiles(GetCSVFilePath_Absolute(), String.Format("report_u{0}*.csv", SessionManager.LoginID), SearchOption.TopDirectoryOnly))
                {
                    File.Delete(strFile);
                }
            }
            catch (Exception) { }
        }

        #endregion

        #region Document Header Images

        internal static string GetDocumentHeaderImagePath_Division(int intDivisionID, bool blnRelative)
        {
            string strDirectory = FileUploadManager.GetDocumentHeaderDirectoryPath(blnRelative);
            string strSep = (blnRelative) ? "/" : "\\";
            return String.Format("{0}{1}division_{2}.jpg", strDirectory, strSep, intDivisionID);
        }
        //[004] code start
        internal static string GetDocumentHeaderImagePath_DivisionNew(int intDivisionID, bool blnRelative)
        {
            string strDirectory = FileUploadManager.GetDocumentHeaderDirectoryPathNew(blnRelative);
            string strSep = "";// (blnRelative) ? "/" : "\\";
            return String.Format("{0}{1}division_{2}.jpg", strDirectory, strSep, intDivisionID);
        }
        internal static string GetDocumentHeaderImageName_DivisionNew(int intDivisionID, bool blnRelative)
        {
            //string strDirectory = FileUploadManager.GetDocumentHeaderDirectoryPathNew(blnRelative);
            //string strSep = "";// (blnRelative) ? "/" : "\\";
            return String.Format("division_{0}.jpg", intDivisionID);
        }

        internal static string GetDocumentHeaderImagePath_ClientNew(int intClientID, bool blnRelative)
        {
            string strDirectory = FileUploadManager.GetDocumentHeaderDirectoryPathNew(blnRelative);
            string strSep = "";// (blnRelative) ? "/" : "\\";
            return String.Format("{0}{1}client_{2}.jpg", strDirectory, strSep, intClientID);
        }
        internal static string GetDocumentHeaderImageName_ClientNew(int intClientID, bool blnRelative)
        {
            //string strDirectory = FileUploadManager.GetDocumentHeaderDirectoryPathNew(blnRelative);
            // string strSep = "";// (blnRelative) ? "/" : "\\";
            return String.Format("client_{0}.jpg", intClientID);
        }
        //[004] code end
        internal static string GetDocumentHeaderImagePath_ClientAzure(int intClientID, bool blnRelative)
        {
            string strDirectory = FileUploadManager.GetDocumentHeaderDirectoryPathNew(blnRelative);
            string strSep = "";// (blnRelative) ? "/" : "\\";
            return String.Format("{0}{1}client_{2}.jpg", strDirectory, strSep, intClientID);
        }
        internal static string GetDocumentHeaderImagePath_DivisionNewAzure(int intDivisionID, bool blnRelative)
        {
            string strDirectory = FileUploadManager.GetDocumentHeaderDirectoryPathNew(blnRelative);
            string strSep = "";// (blnRelative) ? "/" : "\\";
            return String.Format("{0}{1}division_{2}.jpg", strDirectory, strSep, intDivisionID);
        }

        internal static string GetDocumentHeaderImagePath_Client(int intClientID, bool blnRelative)
        {
            string strDirectory = FileUploadManager.GetDocumentHeaderDirectoryPath(blnRelative);
            string strSep = (blnRelative) ? "/" : "\\";
            return String.Format("{0}{1}client_{2}.jpg", strDirectory, strSep, intClientID);
        }

        //For CC
        internal static string GetCertificateOfConformancePath_Client(int intClientID, bool blnRelative)
        {
            string strDirectory = FileUploadManager.GetCertificateOfConformanceDirectoryPath(blnRelative);
            string strSep = (blnRelative) ? "/" : "\\";
            return String.Format("{0}{1}client_{2}.jpg", strDirectory, strSep, intClientID);
        }
        //[004] code start
        internal static string GetCertificateOfConformancePath_ClientNew(int intClientID, bool blnRelative)
        {
            string strDirectory = FileUploadManager.GetCertificateOfConformanceDirectoryPathNew(blnRelative);
            string strSep = "";// (blnRelative) ? "/" : "\\";
            return String.Format("{0}{1}client_{2}.jpg", strDirectory, strSep, intClientID);
        }

        internal static string GetCertificateOfConformanceName_ClientNew(int intClientID, bool blnRelative)
        {
            // string strDirectory = FileUploadManager.GetCertificateOfConformanceDirectoryPathNew(blnRelative);
            // string strSep = "";// (blnRelative) ? "/" : "\\";
            return String.Format("client_{0}.jpg", intClientID);
        }

        internal static string GetTermsDocPath_Client(int intClientID, string section)
        {
            string strDirectory = FileUploadManager.GetTermsDirectoryPath();
            string strSep = "";// (blnRelative) ? "/" : "\\";
            return String.Format("{0}{1}{3}_{2}.pdf", strDirectory, strSep, intClientID, section);
        }

        internal static string GetTermsDocName_Client(int intClientID, string section)
        {
            //string strDirectory = FileUploadManager.GetTermsDirectoryPath();
            //string strSep = "";// (blnRelative) ? "/" : "\\";
            return String.Format("{0}_{1}.pdf", section, intClientID);
        }
        internal static string GetTermsTxtName_Client(int intClientID, string section)
        {
            //string strDirectory = FileUploadManager.GetTermsDirectoryPath();
            //string strSep = "";// (blnRelative) ? "/" : "\\";
            return String.Format("{0}_{1}.txt", section, intClientID);
        }
        //[004] code end
        //End CC
        internal static string GetTermsImagePath_Client(int intClientID, string section)
        {
            string strDirectory = FileUploadManager.GetTermsDirectoryPath();
            string strSep = "";// (blnRelative) ? "/" : "\\";
            return String.Format("{0}{1}{3}_{2}.png", strDirectory, strSep, intClientID, section);
        }
        internal static string GetTermsImageName_Client(int intClientID, string section)
        {
            //  string strDirectory = FileUploadManager.GetTermsDirectoryPath();
            //  string strSep = "";// (blnRelative) ? "/" : "\\";
            return String.Format("{0}_{1}.png", section, intClientID);
        }

        internal static string GetDocumentHeaderDirectoryPath(bool blnRelative)
        {
            string strDir = "~/User/images/docheaders";
            if (!blnRelative) strDir = HttpContext.Current.Server.MapPath(strDir);
            return strDir;
        }
        //[004] code start
        //New method:- get image from shared folder
        internal static string GetDocumentHeaderDirectoryPathNew(bool blnRelative)
        {
            string strDir = Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["DocHeaderDirPath"]);
            //if (!blnRelative) strDir = HttpContext.Current.Server.MapPath(strDir);
            return strDir;
        }
        //[004] code end

        //For CC
        internal static string GetCertificateOfConformanceDirectoryPath(bool blnRelative)
        {
            string strDir = "~/User/images/signature";
            if (!blnRelative) strDir = HttpContext.Current.Server.MapPath(strDir);
            return strDir;
        }

        //[004] code start
        //New method:- get image from shared folder
        internal static string GetCertificateOfConformanceDirectoryPathNew(bool blnRelative)
        {
            string strDir = Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["COCDirectoryPath"]);
            //if (!blnRelative) strDir = HttpContext.Current.Server.MapPath(strDir);
            return strDir;
        }

        internal static string GetTermsDirectoryPath()
        {
            string strDir = Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["TermsPath"]);
            //if (!blnRelative) strDir = HttpContext.Current.Server.MapPath(strDir);
            return strDir;
        }
        //[004] code end
        //End CC


        internal static bool DivisionHasHeaderImage(int intDivisionID, out bool blnUseCompanyHeaderForInvoice)
        {
            bool blnReturn = false;
            blnUseCompanyHeaderForInvoice = true;
            Division dv = Division.GetHasDocumentHeader(intDivisionID);
            if (dv != null)
            {
                if (dv.HasDocumentHeaderImage) blnReturn = File.Exists(GetDocumentHeaderImagePath_Division(intDivisionID, false));
                blnUseCompanyHeaderForInvoice = dv.UseCompanyHeaderForInvoice;
            }
            dv = null;
            return blnReturn;
        }

        //[004] code start
        internal static bool DivisionHasHeaderImageNew(int intDivisionID, out bool blnUseCompanyHeaderForInvoice)
        {
            bool blnReturn = false;
            blnUseCompanyHeaderForInvoice = true;
            Division dv = Division.GetHasDocumentHeader(intDivisionID);
            if (dv != null)
            {
                if (dv.HasDocumentHeaderImage) blnReturn = File.Exists(GetDocumentHeaderImagePath_DivisionNew(intDivisionID, false));
                blnUseCompanyHeaderForInvoice = dv.UseCompanyHeaderForInvoice;
            }
            dv = null;
            return blnReturn;
        }
        internal static bool DivisionHasHeaderImageNewAzure(int intDivisionID, out bool blnUseCompanyHeaderForInvoice)
        {
            string strDivisionName = FileUploadManager.GetDocumentHeaderImageName_DivisionNew(intDivisionID, true).Replace("~/", "");
            // string sasURL = AzureBlobSA.GetSasUrl("gtdocmgmt");
            // string bothirl = AzureBlobSA.GetSasBlobUrl("gtdocmgmt", strDivisionName, sasURL, "docheaders");
            //if (File.Exists(logopath))
            // Uri blobUri = new Uri(bothirl);
            //CloudBlockBlob blob = new CloudBlockBlob(blobUri);

            bool blnReturn = false;
            blnUseCompanyHeaderForInvoice = true;
            Division dv = Division.GetHasDocumentHeader(intDivisionID);
            if (dv != null)
            {
                if (dv.HasDocumentHeaderImage)
                {
                    //string logopath = GetDocumentHeaderImagePath_DivisionNewAzure(intDivisionID, false);
                    //Uri blobUri = new Uri(logopath);
                    //CloudBlockBlob blob = new CloudBlockBlob(blobUri);
                    // if (blob.Exists())
                    // {
                    blnReturn = true;
                    // }
                    //blnReturn = File.Exists(GetDocumentHeaderImagePath_ClientNew(intClientID, false));
                }
                blnUseCompanyHeaderForInvoice = dv.UseCompanyHeaderForInvoice;
            }
            dv = null;
            return blnReturn;
        }
        internal static bool DivisionHasHeaderImageNewAzure(int intDivisionID, out bool blnUseCompanyHeaderForInvoice, out string strDivisionImageName)
        {
            strDivisionImageName = "";// FileUploadManager.GetDocumentHeaderImageName_DivisionNew(intDivisionID, true).Replace("~/", "");
            // string sasURL = AzureBlobSA.GetSasUrl("gtdocmgmt");
            // string bothirl = AzureBlobSA.GetSasBlobUrl("gtdocmgmt", strDivisionName, sasURL, "docheaders");
            //if (File.Exists(logopath))
            // Uri blobUri = new Uri(bothirl);
            //CloudBlockBlob blob = new CloudBlockBlob(blobUri);

            bool blnReturn = false;
            blnUseCompanyHeaderForInvoice = true;
            Division dv = Division.GetHasDocumentHeader(intDivisionID);
            if (dv != null)
            {
                if (dv.HasDocumentHeaderImage)
                {
                    //string logopath = GetDocumentHeaderImagePath_DivisionNewAzure(intDivisionID, false);
                    //Uri blobUri = new Uri(logopath);
                    //CloudBlockBlob blob = new CloudBlockBlob(blobUri);
                    // if (blob.Exists())
                    // {
                    blnReturn = true;
                    // }
                    //blnReturn = File.Exists(GetDocumentHeaderImagePath_ClientNew(intClientID, false));
                }
                blnUseCompanyHeaderForInvoice = dv.UseCompanyHeaderForInvoice;
                strDivisionImageName = dv.DocumentHeaderImageName;
            }
            dv = null;
            return blnReturn;
        }
        //[004] code end

        internal static bool ClientHasHeaderImage(int intClientID)
        {
            bool blnReturn = false;
            BLL.Client cl = BLL.Client.Get(intClientID);
            if (cl != null)
            {
                if (cl.HasDocumentHeaderImage) blnReturn = File.Exists(GetDocumentHeaderImagePath_ClientNew(intClientID, false));
            }
            cl = null;
            return blnReturn;
        }
        internal static bool ClientHasHeaderImageAzure(int intClientID, out string DocumentHeaderImageName)
        {

            DocumentHeaderImageName = "";
            bool blnReturn = false;
            BLL.Client cl = BLL.Client.Get(intClientID);
            if (cl != null)
            {
                if (cl.HasDocumentHeaderImage)
                {
                    blnReturn = true;
                    //string logopath = cl.DocumentHeaderImageName;// GetDocumentHeaderImagePath_ClientAzure(intClientID, false);
                    //Uri blobUri = new Uri(logopath);

                    //string accountname = ConfigurationManager.AppSettings.Get("StorageName");
                    //string accesskey = ConfigurationManager.AppSettings.Get("StorageKey1");
                    //StorageCredentials creden = new StorageCredentials(accountname, accesskey);
                    //CloudStorageAccount acc = new CloudStorageAccount(creden, useHttps: true);
                    //CloudBlobClient cbclient = acc.CreateCloudBlobClient();
                    //CloudBlobContainer cont = cbclient.GetContainerReference("gtdocmgmt");
                    //CloudBlobDirectory directory = cont.GetDirectoryReference("docheaders");
                    //string strFileName = String.Format("client_{0}.jpg", intClientID);

                    //CloudBlockBlob blob = directory.GetBlockBlobReference(strFileName);
                    ////CloudBlockBlob blob = new CloudBlockBlob(blobUri);
                    //if (blob.Exists())
                    //{
                    //    blnReturn = true;
                    //}
                    //blnReturn = File.Exists(GetDocumentHeaderImagePath_ClientNew(intClientID, false));
                }
                DocumentHeaderImageName = cl.DocumentHeaderImageName;
            }
            cl = null;
            return blnReturn;
        }

        internal static bool ClientInvoiceHasHeaderImageAzure(int CIHId, out string DocumentHeaderImageName)
        {

            DocumentHeaderImageName = "";
            bool blnReturn = false;
            BLL.Client cl = BLL.Client.GetClientInvoice(CIHId);
            if (cl != null)
            {
                if (cl.HasDocumentHeaderImage)
                {
                    blnReturn = true;
                }
                DocumentHeaderImageName = cl.DocumentHeaderImageName;
            }
            cl = null;
            return blnReturn;
        }

        internal static bool ClientHasHeaderImageAzure(int intClientID)
        {


            bool blnReturn = false;
            BLL.Client cl = BLL.Client.Get(intClientID);
            if (cl != null)
            {
                if (cl.HasDocumentHeaderImage)
                {
                    string logopath = GetDocumentHeaderImagePath_ClientAzure(intClientID, false);
                    Uri blobUri = new Uri(logopath);

                    string accountname = ConfigurationManager.AppSettings.Get("StorageName");
                    string accesskey = ConfigurationManager.AppSettings.Get("StorageKey1");
                    StorageCredentials creden = new StorageCredentials(accountname, accesskey);
                    CloudStorageAccount acc = new CloudStorageAccount(creden, useHttps: true);
                    CloudBlobClient cbclient = acc.CreateCloudBlobClient();
                    CloudBlobContainer cont = cbclient.GetContainerReference("gtdocmgmt");
                    CloudBlobDirectory directory = cont.GetDirectoryReference("docheaders");
                    string strFileName = String.Format("client_{0}.jpg", intClientID);

                    CloudBlockBlob blob = directory.GetBlockBlobReference(strFileName);
                    //CloudBlockBlob blob = new CloudBlockBlob(blobUri);
                    if (blob.Exists())
                    {
                        blnReturn = true;
                    }
                    //blnReturn = File.Exists(GetDocumentHeaderImagePath_ClientNew(intClientID, false));
                }
            }
            cl = null;
            return blnReturn;
        }

        //internal static string GetDocumentHeaderImagePathNew(int? intDivisionID, bool blnInvoice)
        //{
        //    string strImage = GetDocumentHeaderImagePath_ClientNew((int)SessionManager.ClientID, true).Replace("~/", "");
        //    if (intDivisionID != null)
        //    {
        //        bool blnUseCompanyHeaderForInvoice = false;
        //        if (DivisionHasHeaderImageNew((int)intDivisionID, out blnUseCompanyHeaderForInvoice))
        //        {
        //            //if this is an invoice and the division reverts back to the company header for invoice, use that otherwise get the division header
        //            if (!blnInvoice || !blnUseCompanyHeaderForInvoice) strImage = FileUploadManager.GetDocumentHeaderImagePath_DivisionNew((int)intDivisionID, true).Replace("~/", "");
        //        }
        //    }
        //    return strImage;
        //}

        internal static string GetDocumentHeaderImagePathNew(int? intDivisionID, bool blnInvoice)
        {
            string strImage = GetDocumentHeaderImagePath_ClientNew((int)SessionManager.ClientID, true).Replace("~/", "");

            bool blnHasLogDivImg = false;
            if (SessionManager.LoginDivisionID != null)
            {
                bool blnUseCompanyHeaderForInvoice = false;
                if (DivisionHasHeaderImageNew((int)SessionManager.LoginDivisionID, out blnUseCompanyHeaderForInvoice))
                {
                    //if this is an invoice and the division reverts back to the company header for invoice, use that otherwise get the division header
                    if (!blnInvoice || !blnUseCompanyHeaderForInvoice)
                    {
                        strImage = FileUploadManager.GetDocumentHeaderImagePath_DivisionNew((int)SessionManager.LoginDivisionID, true).Replace("~/", "");
                        blnHasLogDivImg = true;
                    }
                }
            }
            if (intDivisionID != null && blnHasLogDivImg == false)
            {
                bool blnUseCompanyHeaderForInvoice = false;
                if (DivisionHasHeaderImageNew((int)intDivisionID, out blnUseCompanyHeaderForInvoice))
                {
                    //if this is an invoice and the division reverts back to the company header for invoice, use that otherwise get the division header
                    if (!blnInvoice || !blnUseCompanyHeaderForInvoice) strImage = FileUploadManager.GetDocumentHeaderImagePath_DivisionNew((int)intDivisionID, true).Replace("~/", "");
                }
            }
            return strImage;
        }
        internal static string GetDocumentHeaderImageNameNew(int? intDivisionID, bool blnInvoice)
        {
            string strImage = GetDocumentHeaderImageName_ClientNew((int)SessionManager.ClientID, true).Replace("~/", "");

            bool blnHasLogDivImg = false;
            if (SessionManager.LoginDivisionID != null)
            {
                bool blnUseCompanyHeaderForInvoice = false;
                if (DivisionHasHeaderImageNewAzure((int)SessionManager.LoginDivisionID, out blnUseCompanyHeaderForInvoice))
                {
                    //if this is an invoice and the division reverts back to the company header for invoice, use that otherwise get the division header
                    if (!blnInvoice || !blnUseCompanyHeaderForInvoice)
                    {
                        strImage = FileUploadManager.GetDocumentHeaderImageName_DivisionNew((int)SessionManager.LoginDivisionID, true).Replace("~/", "");
                        blnHasLogDivImg = true;
                    }
                }
            }
            if (intDivisionID != null && blnHasLogDivImg == false)
            {
                bool blnUseCompanyHeaderForInvoice = false;
                if (DivisionHasHeaderImageNewAzure((int)intDivisionID, out blnUseCompanyHeaderForInvoice))
                {
                    //if this is an invoice and the division reverts back to the company header for invoice, use that otherwise get the division header
                    if (!blnInvoice || !blnUseCompanyHeaderForInvoice) strImage = FileUploadManager.GetDocumentHeaderImageName_DivisionNew((int)intDivisionID, true).Replace("~/", "");
                }
            }
            return strImage;
        }
        internal static string GetDocumentHeaderImageNamePathIPO(int? intDivisionID, bool blnInvoice, int clientNo)
        {
            string strImage = GetDocumentHeaderImageName_ClientNew(clientNo, true).Replace("~/", "");
            bool blnHasLogDivImg = false;
            if (SessionManager.LoginDivisionID != null)
            {
                bool blnUseCompanyHeaderForInvoice = false;
                if (DivisionHasHeaderImageNewAzure((int)SessionManager.LoginDivisionID, out blnUseCompanyHeaderForInvoice))
                {
                    //if this is an invoice and the division reverts back to the company header for invoice, use that otherwise get the division header
                    if (!blnInvoice || !blnUseCompanyHeaderForInvoice)
                    {
                        strImage = FileUploadManager.GetDocumentHeaderImageName_DivisionNew((int)SessionManager.LoginDivisionID, true).Replace("~/", "");
                        blnHasLogDivImg = true;
                    }
                }
            }
            if (intDivisionID != null && blnHasLogDivImg == false)
            {
                bool blnUseCompanyHeaderForInvoice = false;
                if (DivisionHasHeaderImageNewAzure((int)intDivisionID, out blnUseCompanyHeaderForInvoice))
                {
                    //if this is an invoice and the division reverts back to the company header for invoice, use that otherwise get the division header
                    if (!blnInvoice || !blnUseCompanyHeaderForInvoice) strImage = FileUploadManager.GetDocumentHeaderImageName_DivisionNew((int)intDivisionID, true).Replace("~/", "");
                }
            }
            return strImage;
        }
        /// <summary>
        /// Get the document header name during document insertion. 
        /// </summary>
        /// <param name="intDivisionID"></param>
        /// <param name="blnInvoice"></param>
        /// <param name="clientNo"></param>
        /// <returns></returns>
        internal static string GetDocumentHeaderImageNameForAdd(int? intDivisionID, bool blnInvoice, int clientNo)
        {
            string strDivisionFileName = "";
            // string strImage = GetDocumentHeaderImageName_ClientNew(clientNo, true).Replace("~/", "");
            string strImage = "";
            Client cl = BLL.Client.Get(clientNo);
            if (cl != null)
                strImage = cl.DocumentHeaderImageName;

            bool blnHasLogDivImg = false;
            //if (SessionManager.LoginDivisionID != null)
            //{
            //    bool blnUseCompanyHeaderForInvoice = false;
            //    if (DivisionHasHeaderImageNewAzure((int)SessionManager.LoginDivisionID, out blnUseCompanyHeaderForInvoice,out strDivisionFileName))
            //    {
            //        //if this is an invoice and the division reverts back to the company header for invoice, use that otherwise get the division header
            //        if (!blnInvoice || !blnUseCompanyHeaderForInvoice)
            //        {
            //            if (!string.IsNullOrEmpty(strDivisionFileName))
            //            {
            //                strImage = strDivisionFileName;// FileUploadManager.GetDocumentHeaderImageName_DivisionNew((int)SessionManager.LoginDivisionID, true).Replace("~/", "");
            //                blnHasLogDivImg = true;
            //            }
            //        }
            //    }
            //}
            if (intDivisionID != null && blnHasLogDivImg == false)
            {
                bool blnUseCompanyHeaderForInvoice = false;
                if (DivisionHasHeaderImageNewAzure((int)intDivisionID, out blnUseCompanyHeaderForInvoice, out strDivisionFileName))
                {
                    //if this is an invoice and the division reverts back to the company header for invoice, use that otherwise get the division header
                    if (!blnInvoice || !blnUseCompanyHeaderForInvoice)
                    {
                        if (!string.IsNullOrEmpty(strDivisionFileName)) // if division header image is found
                        {
                            strImage = strDivisionFileName;// FileUploadManager.GetDocumentHeaderImageName_DivisionNew((int)intDivisionID, true).Replace("~/", "");
                        }
                        else
                        {
                            strImage = cl.DocumentHeaderImageName;  // if division header image is not found then set client header image
                        }
                    }
                }
                else
                {
                    strImage = cl.DocumentHeaderImageName;  // if division header image is not found then set client header image
                }
            }

            // if no image present on "Printed Document" and "Division" section then application should return client_101.jpeg file or should return blank header image name
            if (string.IsNullOrEmpty(strImage))
            {
                strImage = FileUploadManager.GetClientDefaultDocumentHeaderImage((int)intDivisionID, true, clientNo);
            }
            return strImage;
        }
        internal static string GetDocumentHeaderImageNameIPO(int? intDivisionID, bool blnInvoice, int clientNo)
        {
            string strImage = GetDocumentHeaderImageName_ClientNew(clientNo, true).Replace("~/", "");
            bool blnHasLogDivImg = false;
            if (SessionManager.LoginDivisionID != null)
            {
                bool blnUseCompanyHeaderForInvoice = false;
                if (DivisionHasHeaderImageNewAzure((int)SessionManager.LoginDivisionID, out blnUseCompanyHeaderForInvoice))
                {
                    //if this is an invoice and the division reverts back to the company header for invoice, use that otherwise get the division header
                    if (!blnInvoice || !blnUseCompanyHeaderForInvoice)
                    {
                        strImage = FileUploadManager.GetDocumentHeaderImageName_DivisionNew((int)SessionManager.LoginDivisionID, true).Replace("~/", "");
                        blnHasLogDivImg = true;
                    }
                }
            }
            if (intDivisionID != null && blnHasLogDivImg == false)
            {
                bool blnUseCompanyHeaderForInvoice = false;
                if (DivisionHasHeaderImageNewAzure((int)intDivisionID, out blnUseCompanyHeaderForInvoice))
                {
                    //if this is an invoice and the division reverts back to the company header for invoice, use that otherwise get the division header
                    if (!blnInvoice || !blnUseCompanyHeaderForInvoice) strImage = FileUploadManager.GetDocumentHeaderImageName_DivisionNew((int)intDivisionID, true).Replace("~/", "");
                }
            }
            return strImage;
        }

        internal static string GetClientDefaultDocumentHeaderImage(int? intDivisionID, bool blnInvoice, int clientNo)
        {
            string strImage = GetDocumentHeaderImageName_ClientNew(clientNo, true).Replace("~/", "");
            bool blnHasLogDivImg = false;
            //if (SessionManager.LoginDivisionID != null)
            //{
            //    bool blnUseCompanyHeaderForInvoice = false;
            //    if (DivisionHasHeaderImageNewAzure((int)SessionManager.LoginDivisionID, out blnUseCompanyHeaderForInvoice))
            //    {
            //        //if this is an invoice and the division reverts back to the company header for invoice, use that otherwise get the division header
            //        if (!blnInvoice || !blnUseCompanyHeaderForInvoice)
            //        {
            //            strImage = FileUploadManager.GetDocumentHeaderImageName_DivisionNew((int)SessionManager.LoginDivisionID, true).Replace("~/", "");
            //            blnHasLogDivImg = true;
            //        }
            //    }
            //}
            if (intDivisionID != null && blnHasLogDivImg == false)
            {
                bool blnUseCompanyHeaderForInvoice = false;
                if (DivisionHasHeaderImageNewAzure((int)intDivisionID, out blnUseCompanyHeaderForInvoice))
                {
                    //if this is an invoice and the division reverts back to the company header for invoice, use that otherwise get the division header
                    if (!blnInvoice || !blnUseCompanyHeaderForInvoice) strImage = FileUploadManager.GetDocumentHeaderImageName_DivisionNew((int)intDivisionID, true).Replace("~/", "");
                }
            }
            return strImage;
        }


        internal static string GetDocumentHeaderImageNameIPO_back(int? intDivisionID, bool blnInvoice, int clientNo, string ClientImageFileName, string DivisionImageFileName)
        {
            string strImage = ClientImageFileName;//"client_101.jpg";
            if (intDivisionID != null && intDivisionID > 0)
            {
                strImage = DivisionImageFileName;//"division_150097.jpg";

            }
            return strImage;
        }

        internal static string GetDocumentHeaderImageNameWareHouse_back(int? intDivisionID, bool blnInvoice, int clientNo, string ClientImageFileName, string DivisionImageFileName)
        {
            string strImage = ClientImageFileName; //"client_101.jpg";
            if (intDivisionID != null && intDivisionID > 0)
            {
                strImage = DivisionImageFileName;
            }
            return strImage;
        }
        internal static string GetDocumentHeaderImageNameWareHouse(int? intDivisionID, bool blnInvoice, int clientNo)
        {
            string strImage = GetDocumentHeaderImageName_ClientNew(clientNo, true).Replace("~/", "");
            if (intDivisionID != null && intDivisionID > 0)
            {
                bool blnUseCompanyHeaderForInvoice = false;
                if (DivisionHasHeaderImageNewAzure((int)intDivisionID, out blnUseCompanyHeaderForInvoice))
                {
                    //if this is an invoice and the division reverts back to the company header for invoice, use that otherwise get the division header
                    if (!blnInvoice || !blnUseCompanyHeaderForInvoice) strImage = FileUploadManager.GetDocumentHeaderImageName_DivisionNew((int)intDivisionID, true).Replace("~/", "");
                }
            }
            return strImage;
        }




        internal static string GetDocumentHeaderImagePathWareHouse(int? intDivisionID, bool blnInvoice, int clientNo)
        {
            string strImage = GetDocumentHeaderImagePath_ClientNew(clientNo, true).Replace("~/", "");
            if (intDivisionID != null && intDivisionID > 0)
            {
                bool blnUseCompanyHeaderForInvoice = false;
                if (DivisionHasHeaderImageNew((int)intDivisionID, out blnUseCompanyHeaderForInvoice))
                {
                    //if this is an invoice and the division reverts back to the company header for invoice, use that otherwise get the division header
                    if (!blnInvoice || !blnUseCompanyHeaderForInvoice) strImage = FileUploadManager.GetDocumentHeaderImagePath_DivisionNew((int)intDivisionID, true).Replace("~/", "");
                }
            }
            return strImage;
        }


        internal static string GetDocumentHeaderImagePathIPO(int? intDivisionID, bool blnInvoice, int clientNo)
        {
            string strImage = GetDocumentHeaderImagePath_ClientNew(clientNo, true).Replace("~/", "");
            if (intDivisionID != null)
            {
                bool blnUseCompanyHeaderForInvoice = false;
                if (DivisionHasHeaderImageNew((int)intDivisionID, out blnUseCompanyHeaderForInvoice))
                {
                    //if this is an invoice and the division reverts back to the company header for invoice, use that otherwise get the division header
                    if (!blnInvoice || !blnUseCompanyHeaderForInvoice) strImage = FileUploadManager.GetDocumentHeaderImagePath_DivisionNew((int)intDivisionID, true).Replace("~/", "");
                }
            }
            return strImage;
        }
        //[004] code end

        internal static string GetDocumentHeaderImagePath(int? intDivisionID, bool blnInvoice)
        {
            string strImage = GetDocumentHeaderImagePath_Client((int)SessionManager.ClientID, true).Replace("~/", "");
            if (intDivisionID != null)
            {
                bool blnUseCompanyHeaderForInvoice = false;
                if (DivisionHasHeaderImage((int)intDivisionID, out blnUseCompanyHeaderForInvoice))
                {
                    //if this is an invoice and the division reverts back to the company header for invoice, use that otherwise get the division header
                    if (!blnInvoice || !blnUseCompanyHeaderForInvoice) strImage = FileUploadManager.GetDocumentHeaderImagePath_Division((int)intDivisionID, true).Replace("~/", "");
                }
            }
            return strImage;
        }
        //For CC
        internal static string GetCertificateOfConformancePath(int? intDivisionID, bool blnInvoice)
        {
            string strImage = GetCertificateOfConformancePath_Client((int)SessionManager.ClientID, true).Replace("~/", "");
            return strImage;
        }
        //[004] code start
        internal static string GetCertificateOfConformancePathNew(int? intDivisionID, bool blnInvoice)
        {
            string strImage = GetCertificateOfConformancePath_ClientNew((int)SessionManager.ClientID, true).Replace("~/", "");
            return strImage;
        }
        internal static string GetCertificateOfConformancePathNew(int? intDivisionID, bool blnInvoice, int ClientNo)
        {
            string strImage = GetCertificateOfConformancePath_ClientNew((int)ClientNo, true).Replace("~/", "");
            return strImage;
        }
        internal static string GetCertificateOfConformanceNameNew(int? intDivisionID, bool blnInvoice, int ClientNo)
        {
            string strImage = GetCertificateOfConformanceName_ClientNew((int)ClientNo, true).Replace("~/", "");
            return strImage;
        }

        internal static string GetTermsDocPath(int? intDivisionID, bool blnInvoice)
        {
            string strImage = GetCertificateOfConformancePath_ClientNew((int)SessionManager.ClientID, true).Replace("~/", "");
            return strImage;
        }
        //[004] code end
        //End CC

        internal static string GetTermConditionImagePath(int? intDivisionID, string section)
        {
            string strImage = GetTermsImagePath_Client((int)SessionManager.ClientID, section).Replace("~/", "");
            return strImage;
        }
        internal static string GetTermConditionImageName(int? intDivisionID, string section)
        {
            string strImage = GetTermsImageName_Client((int)SessionManager.ClientID, section).Replace("~/", "");
            return strImage;
        }
        internal static string GetTermConditionPDFPath(int? intDivisionID, string section)
        {
            string strPdf = GetTermsDocPath_Client((int)SessionManager.ClientID, section).Replace("~/", "");
            return strPdf;
        }
        #endregion

        #region Upload PDF Documents
        // [001] code start
        internal static string GetPDFUploadFilePath(string subFolder)
        {
            string configPath = Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["PDFDocumentPhysicalURL"]);
            return String.Format(configPath + "{0}/", subFolder);
        }
        // [001] code end

        internal static string GetAzurePDFUploadFilePath(string subFolder)
        {
            string configPath = Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["PDFDocumentPhysicalAzureURL"]);
            return String.Format(configPath + "{0}/", subFolder);
        }
        internal static string GetExcelUploadAzureBlobFilePath(string subFolder)
        {
            string configPath = Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["ExcelDocumentPhysicalURLForAzureBlob"]);
            return String.Format(configPath + "{0}/", subFolder);
        }
        internal static string GetExcelUploadFilePath(string subFolder)
        {
            string configPath = Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["ExcelDocumentPhysicalURL"]);
            return String.Format(configPath + "{0}/", subFolder);
        }
        internal static string GetCSVUploadFilePath(string subFolder)
        {
            string configPath = Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["CSVDocumentPhysicalURL"]);
            return String.Format(configPath + "{0}/", subFolder);
        }


        //[003] code start
        internal static string GetClientPathOfLablePrint()
        {
            return Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["ClientFolderPathOfLablePrint"]);
        }

        internal static string GetClientPathOfLablePrintPDF()
        {
            return Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["ClientFolderPathOfLablePrintPDF"]);
        }
        internal static string GetDelimiter()
        {
            return Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["Delimiter"]);
        }

        internal static string GetNiceLabelPathAPI()
        {
            return Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["NiceLabelAPI"]);
        }

        internal static bool GetIsNiceLabelText()
        {
            return Convert.ToBoolean(System.Configuration.ConfigurationManager.AppSettings["EnableNiceLabelText"]);
        }

        internal static string GetNiceLablePath()
        {
            return Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["NiceLabelPath"]);
        }
        //[003] code end

        //[005]code start
        internal static string GetExcelSourceResultFilePath(string subFolder)
        {
            string configPath = Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["ExcelImportSourcePhysicalURL"]);
            return String.Format(configPath + "{0}/", subFolder);
        }
        //[005]code end

        //Added by Arpit
        /// <summary> 
        /// code for printing from UAT2 Label only
        /// </summary>
        /// <returns></returns>
        internal static bool GetIsPrintToProdLabel()
        {
            return Convert.ToBoolean(System.Configuration.ConfigurationManager.AppSettings["IsPrintToProdLabel"]);
        }

        #endregion

        #region StockImage

        internal static string GetStockUploadImagePath()
        {
            return Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["StockImagePhysicalURL"]);
        }
        internal static string GetStockUploadImagePathAzure()
        {
            return Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["StockAzureImagePhysicalURL"]);
        }
        internal static string GetStockImageFilename(System.String stockNo, System.String strFileExt)
        {
            return GetStockImageFilename(stockNo, strFileExt, DateTime.Now);
        }

        internal static string GetDocImageFilename(System.String intDocNo, System.String strFileExt)
        {
            return GetDocImageFilename(intDocNo, strFileExt, DateTime.Now);
        }

        internal static string GetStockDashboard()
        {
            return Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["StockDashboard"]);
            //if (!isSecure)
            //    return Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["StockDashboard"]);
            //else
            //    return Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["StockDashboardSecure"]);
        }
        internal static string GetSalesDashboard()
        {
            return Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["SalesDashboard"]);
            //if (!isSecure)
            //    return Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["SalesDashboard"]);
            //else
            //    return Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["SalesDashboardSecure"]);
        }

        /// <summary>
        /// Strings the current date (in an orderable form) to the front of the passed filename to ensure originality
        /// </summary>
        internal static string GetStockImageFilename(System.String stockNo, System.String strFileExt, DateTime dtm)
        {
            string str = stockNo;
            str += "_";
            str += dtm.Year.ToString();
            str += dtm.Month.ToString("d2");
            str += dtm.Day.ToString("d2");
            str += dtm.Hour.ToString("d2");
            //str += dtm.Minute.ToString("d2");
            //str += dtm.Second.ToString("d2");
            //str += dtm.Millisecond.ToString("d2");
            str += Guid.NewGuid().ToString();
            str += strFileExt;
            //str += "_";
            // str += strFileName;
            return str;
        }

        internal static string GetDocImageFilename(System.String intDocNo, System.String strFileExt, DateTime dtm)
        {
            string str = intDocNo;
            str += "_";
            str += dtm.Year.ToString();
            str += dtm.Month.ToString("d2");
            str += dtm.Day.ToString("d2");
            str += dtm.Hour.ToString("d2");
            //str += dtm.Minute.ToString("d2");
            //str += dtm.Second.ToString("d2");
            //str += dtm.Millisecond.ToString("d2");
            str += Guid.NewGuid().ToString();
            str += strFileExt;
            //str += "_";
            // str += strFileName;
            return str;
        }

        internal static bool StockImageForSAN()
        {
            return Convert.ToBoolean(System.Configuration.ConfigurationManager.AppSettings["StockImageForSAN"]);
        }

        internal static bool StockImageForMedia()
        {
            return Convert.ToBoolean(System.Configuration.ConfigurationManager.AppSettings["StockImageForMedia"]);
        }

        internal static string GetSourcingImagePath()
        {
            return Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["SourcingImageURL"]);
        }
        internal static string GetSOPaymentAttachmentPath()
        {
            return Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["SOPaymentAttachment"]);
        }
        #endregion


        #region Export CSV Document
        // [001] code start
        /// <summary>
        /// Writes report to a CSV file and returns the filename 
        /// </summary>
        /// <returns></returns>
        public static string ExportToCSV(int reportId, int? Id, string CurrencyCode, string Export)
        {
            JsonObject jsn = new JsonObject();
            string fileName = string.Empty;
            List<List<object>> lstData = null;
            List<BLL.ReportColumn> lstColumns = null;

            try
            {
                string strHeadings = string.Empty;
                string fileSuffix = $"{SessionManager.ClientID}{DateTime.Now:mmss}";

                if (reportId == (int)Report.List.RequirementWithBOM)
                {
                    lstData = CustomerRequirement.GetBOMListForCRList(Id, SessionManager.ClientID, 0);
                    lstColumns = BLL.ReportColumn.GetListForReport((int)Report.List.RequirementWithBOM);
                    fileName = $"PriceRequest{(Export == "E" ? "E" : "")}_{fileSuffix}.csv";
                    strHeadings = FormatTextForCSV(Functions.GetGlobalResource("ReportTitles", Report.List.RequirementWithBOM), false, true);
                }

                if (reportId == (int)Report.List.PurchaseQuote)
                {
                    lstData = PurchaseOrderLine.GetPurchaseQuoteLineList(Id, SessionManager.ClientID, 0);
                    lstColumns = BLL.ReportColumn.GetListForReport((int)Report.List.PurchaseQuote);
                    fileName = $"PriceRequest{(Export == "E" ? "E" : "")}_{fileSuffix}.csv";
                    strHeadings = FormatTextForCSV(Functions.GetGlobalResource("ReportTitles", Report.List.PurchaseQuote), false, true);
                }

                strHeadings += FormatTextForCSV("Abbreviation-> SPQ: Standard Pack Quantity, MOQ: Minimum Order Quantity, TQSA: Total Quantity of Stock Available, LTB: Last Time Buy (Y/N)", false, true);
                strHeadings += FormatTextForCSV(Functions.GetGlobalResource("Misc", "AppTitle"), false, true);
                strHeadings += FormatTextForCSV(String.Format(Functions.GetGlobalResource("Reports", "DateAndLogin"),
                    Functions.FormatDate(Functions.GetUKLocalTime(), true, true), SessionManager.LoginFullName), false, true);

                if (lstData != null && lstData[0].Count > 13)
                {
                    strHeadings += FormatTextForCSV(Functions.GetGlobalResource("Misc", "Notes") + lstData[0][14], false, true);
                }

                StringBuilder sbCSV = new StringBuilder(strHeadings);

                if (lstData != null)
                {
                    for (int intCol = 0; intCol < lstColumns.Count; intCol++)
                    {
                        if (intCol == 0) sbCSV.Append(FormatTextForCSV("", false, false));

                        if (lstColumns[intCol].TitleResource.Contains("UnitPrice"))
                        {
                            sbCSV.Append(FormatTextForCSV($"{Functions.GetGlobalResource("Reports", lstColumns[intCol].TitleResource)} ({CurrencyCode})",
                                true, (intCol == lstColumns.Count - 1)));
                        }
                        else
                        {
                            sbCSV.Append(FormatTextForCSV(Functions.GetGlobalResource("Reports", lstColumns[intCol].TitleResource),
                                true, (intCol == lstColumns.Count - 1)));
                        }
                    }

                    for (int intRow = 0; intRow < lstData.Count; intRow++)
                    {
                        for (int intCol = 0; intCol < lstColumns.Count; intCol++)
                        {
                            if (intCol == 0) sbCSV.Append(FormatTextForCSV((intRow + 1).ToString(), false, (intCol == lstColumns.Count - 1)));

                            sbCSV.Append(FormatTextForCSV(Pages.PrintReport.GetFormattedItem(
                                (int)lstColumns[intCol].ReportColumnFormatNo, lstData[intRow][intCol]),
                                true, (intCol == lstColumns.Count - 1)));
                        }
                    }
                }
                else
                {
                    sbCSV.AppendLine(Functions.GetGlobalResource("NotFound", "ReportData"));
                }

                File.WriteAllText($"{FileUploadManager.GetTemporaryUploadFilePath()}/{fileName}", sbCSV.ToString());
            }
            catch (Exception)
            {
            }
            finally
            {
                jsn.Dispose();
            }

            return fileName;
        }

        public static string ExportToCSVPR(int reportId, int? Id, string CurrencyCode, string Export)
        {
            JsonObject jsn = new JsonObject();
            string fileName = string.Empty;
            List<List<object>> lstData = null;
            List<BLL.ReportColumn> lstColumns = null;
            try
            {
                JsonObject jsnItems = new JsonObject(true);
                string strHeadings = string.Empty;
                //if (reportId == (int)Report.List.RequirementWithBOM)
                //{
                //    lstData = CustomerRequirement.GetBOMListForCRList(Id, SessionManager.ClientID, 0);
                //    lstColumns = BLL.ReportColumn.GetListForReport((int)Report.List.RequirementWithBOM);
                //    if (Export == "E")
                //    {
                //        fileName = String.Format("PriceRequestE_{0}.csv", SessionManager.ClientID + Convert.ToDateTime(DateTime.Now).ToString("mmss"));
                //    }
                //    else
                //    {
                //        fileName = String.Format("PriceRequest_{0}.csv", SessionManager.ClientID + Convert.ToDateTime(DateTime.Now).ToString("mmss"));
                //    }
                //    strHeadings = FormatTextForCSV(Functions.GetGlobalResource("ReportTitles", Report.List.RequirementWithBOM), false, true);
                //}
                if (reportId == (int)Report.List.PurchaseQuote)
                {
                    lstData = PurchaseOrderLine.GetPurchaseQuoteLineList(Id, SessionManager.ClientID, 0);
                    lstColumns = BLL.ReportColumn.GetListForReport((int)Report.List.PurchaseQuote);
                    if (Export == "E")
                    {
                        fileName = String.Format("PriceRequestE_{0}.csv", SessionManager.ClientID + Convert.ToDateTime(DateTime.Now).ToString("mmss"));
                    }
                    else
                    {
                        fileName = String.Format("PriceRequest_{0}.csv", SessionManager.ClientID + Convert.ToDateTime(DateTime.Now).ToString("mmss"));
                    }
                    //fileName = String.Format("PurchaseRequest.csv");
                    strHeadings = FormatTextForCSV(Functions.GetGlobalResource("ReportTitles", Report.List.PurchaseQuote), false, true);
                }

                //add headings (get into string so we can replace variables in call to GetReportData below)

                //strHeadings += FormatTextForCSV("", false, true);
                strHeadings += FormatTextForCSV("Abbreviation-> SPQ: Standard Pack Quantity, MOQ: Minimum Order Quantity, TQSA: Total Quantity of Stock Available, LTB: Last Time Buy (Y/N)", false, true);
                //"Abbreviation-> SPQ: Standard Pack Quantity, MOQ: Minimum Order Quantity, TQSA: Total Quantity of Stock Available, LTB: Last Time Buy (Y/N)"
                strHeadings += FormatTextForCSV(Functions.GetGlobalResource("Misc", "AppTitle"), false, true);
                strHeadings += FormatTextForCSV(String.Format(Functions.GetGlobalResource("Reports", "DateAndLogin"), Functions.FormatDate(Functions.GetUKLocalTime(), true, true), SessionManager.LoginFullName), false, true);
                if (lstData[0].Count > 13)
                {
                    strHeadings += FormatTextForCSV(Functions.GetGlobalResource("Misc", "Notes") + lstData[0][14], false, true);
                }
                StringBuilder sbCSV = new StringBuilder(strHeadings);
                //sbCSV.Append(BlankCSVLine());

                if (lstData != null)
                {
                    //setup summarising variables
                    int lstColumnCount = lstColumns.Count;
                    StringBuilder sbSummaryColumnHeadings = new StringBuilder("");
                    StringBuilder sbSummaryColumnCount = new StringBuilder("");
                    //add column headings
                    for (int intCol = 0; intCol < lstColumns.Count; intCol++)
                    {
                        if (intCol == 0) sbCSV.Append(FormatTextForCSV("", false, false)); //blank for row count col
                        sbCSV.Append(FormatTextForCSV(Functions.GetGlobalResource("Reports", lstColumns[intCol].TitleResource), true, (intCol == lstColumns.Count - 1)));

                        //if (lstColumns[intCol].TitleResource.Contains("UnitPrice"))
                        //{
                        //    sbCSV.Append(FormatTextForCSV(Functions.GetGlobalResource("Reports", lstColumns[intCol].TitleResource) + " ( " + CurrencyCode + " )", true, (intCol == lstColumns.Count - 1)));
                        //}
                        //else
                        //{

                        //}
                    }

                    //step through all of data
                    for (int intRow = 0; intRow < lstData.Count; intRow++)
                    {
                        for (int intCol = 0; intCol < lstColumns.Count; intCol++)
                        {
                            //add row number
                            if (intCol == 0) sbCSV.Append(FormatTextForCSV((intRow + 1).ToString(), false, (intCol == lstColumns.Count - 1)));
                            //add data
                            sbCSV.Append(FormatTextForCSV(Pages.PrintReport.GetFormattedItem((int)lstColumns[intCol].ReportColumnFormatNo, lstData[intRow][intCol]), true, (intCol == lstColumns.Count - 1)));
                        }
                    }
                }

                else
                {
                    sbCSV.AppendLine(Functions.GetGlobalResource("NotFound", "ReportData"));
                }
                //sbCSV.Append(BlankCSVLine());
                //sbCSV.Append("Abbreviation:" + System.Environment.NewLine);                     
                //sbCSV.Append("SPQ: Standard Pack Quantity" + System.Environment.NewLine);
                //sbCSV.Append("MOQ: Minimum Order Quantity" + System.Environment.NewLine);
                //sbCSV.Append("TQSA: Total Quantity of Stock Available" + System.Environment.NewLine);
                //sbCSV.Append("LTB: Last Time Buy (Y/N)" + System.Environment.NewLine);

                //now save csv to a file                    
                File.WriteAllText(String.Format("{0}/{1}", FileUploadManager.GetTemporaryUploadFilePath(), fileName), sbCSV.ToString());
            }
            catch (Exception)
            {
                //WriteError(e);
            }
            finally
            {
                jsn.Dispose();
                jsn = null;
            }
            return fileName;
        }

        public static string ExportToCSVForClientListPR(int reportId, int? Id, string CurrencyCode, Int32 CompanyId, Int32 srNo)
        {
            JsonObject jsn = new JsonObject();
            string fileName = string.Empty;
            List<List<object>> lstData = null;
            List<BLL.ReportColumn> lstColumns = null;
            try
            {
                JsonObject jsnItems = new JsonObject(true);
                string strHeadings = string.Empty;
                if (reportId == (int)Report.List.RequirementWithBOM)
                {
                    lstData = CustomerRequirement.GetBOMListForCRList(Id, SessionManager.ClientID, CompanyId);
                    lstColumns = BLL.ReportColumn.GetListForReport((int)Report.List.RequirementWithBOM);
                    fileName = String.Format("PriceRequestM_{1}_{0}.csv", SessionManager.ClientID + Convert.ToDateTime(DateTime.Now).ToString("mmss"), srNo.ToString());
                    strHeadings = FormatTextForCSV(Functions.GetGlobalResource("ReportTitles", Report.List.RequirementWithBOM), false, true);
                }
                if (reportId == (int)Report.List.PurchaseQuote)
                {
                    lstData = PurchaseOrderLine.GetPurchaseQuoteLineList(Id, SessionManager.ClientID, CompanyId);
                    lstColumns = BLL.ReportColumn.GetListForReport((int)Report.List.PurchaseQuote);
                    fileName = String.Format("PriceRequestM_{1}_{0}.csv", SessionManager.ClientID + Convert.ToDateTime(DateTime.Now).ToString("mmss"), srNo.ToString());
                    //fileName = String.Format("PurchaseRequest.csv");
                    strHeadings = FormatTextForCSV(Functions.GetGlobalResource("ReportTitles", Report.List.PurchaseQuote), false, true);
                }
                //add headings (get into string so we can replace variables in call to GetReportData below)
                //strHeadings += FormatTextForCSV("", false, true);
                strHeadings += FormatTextForCSV("Abbreviation-> SPQ: Standard Pack Quantity, MOQ: Minimum Order Quantity, TQSA: Total Quantity of Stock Available, LTB: Last Time Buy (Y/N)", false, true);
                strHeadings += FormatTextForCSV(Functions.GetGlobalResource("Misc", "AppTitle"), false, true);
                strHeadings += FormatTextForCSV(String.Format(Functions.GetGlobalResource("Reports", "DateAndLogin"), Functions.FormatDate(Functions.GetUKLocalTime(), true, true), SessionManager.LoginFullName), false, true);
                if (lstData[0].Count > 13)
                {
                    strHeadings += FormatTextForCSV(Functions.GetGlobalResource("Misc", "Notes") + lstData[0][14], false, true);
                }
                StringBuilder sbCSV = new StringBuilder(strHeadings);
                //sbCSV.Append(BlankCSVLine());
                //int[] CompanyId=null;
                if (lstData != null)
                {
                    //setup summarising variables
                    int lstColumnCount = lstColumns.Count;
                    //List<double> lstColumnSum = new List<double>(lstColumns.Count);
                    StringBuilder sbSummaryColumnHeadings = new StringBuilder("");
                    //StringBuilder sbSummaryColumnSum = new StringBuilder("");
                    StringBuilder sbSummaryColumnCount = new StringBuilder("");
                    //StringBuilder sbSummaryColumnAverage = new StringBuilder("");                               
                    //add column headings
                    for (int intCol = 0; intCol < lstColumns.Count; intCol++)
                    {
                        if (intCol == 0) sbCSV.Append(FormatTextForCSV("", false, false)); //blank for row count col

                        sbCSV.Append(FormatTextForCSV(Functions.GetGlobalResource("Reports", lstColumns[intCol].TitleResource), true, (intCol == lstColumns.Count - 1)));

                        //if (lstColumns[intCol].TitleResource.Contains("UnitPrice"))
                        //{
                        //    sbCSV.Append(FormatTextForCSV(Functions.GetGlobalResource("Reports", lstColumns[intCol].TitleResource) + " ( " + CurrencyCode + " )", true, (intCol == lstColumns.Count - 1)));
                        //}
                        //else
                        //{
                        //    sbCSV.Append(FormatTextForCSV(Functions.GetGlobalResource("Reports", lstColumns[intCol].TitleResource), true, (intCol == lstColumns.Count - 1)));
                        //}
                    }

                    //step through all of data
                    for (int intRow = 0; intRow < lstData.Count; intRow++)
                    {
                        for (int intCol = 0; intCol < lstColumns.Count; intCol++)
                        {
                            //add row number
                            if (intCol == 0) sbCSV.Append(FormatTextForCSV((intRow + 1).ToString(), false, (intCol == lstColumns.Count - 1)));
                            //add data
                            //if (lstColumns[intCol].TitleResource.Contains("Company"))
                            //{
                            //    sbCSV.Append(FormatTextForCSV(Pages.PrintReport.GetFormattedItem((int)lstColumns[intCol].ReportColumnFormatNo, CompanyId), true, (intCol == lstColumns.Count - 1)));
                            //}
                            // else
                            //{
                            sbCSV.Append(FormatTextForCSV(Pages.PrintReport.GetFormattedItem((int)lstColumns[intCol].ReportColumnFormatNo, lstData[intRow][intCol]), true, (intCol == lstColumns.Count - 1)));
                            //}
                        }
                    }
                }

                else
                {
                    sbCSV.AppendLine(Functions.GetGlobalResource("NotFound", "ReportData"));
                }
                //sbCSV.Append(BlankCSVLine());
                //sbCSV.Append("Abbreviation:" + System.Environment.NewLine);                
                //sbCSV.Append("SPQ: Standard Pack Quantity" + System.Environment.NewLine);
                //sbCSV.Append("MOQ: Minimum Order Quantity" + System.Environment.NewLine);
                //sbCSV.Append("TQSA: Total Quantity of Stock Available" + System.Environment.NewLine);
                //sbCSV.Append("LTB: Last Time Buy (Y/N)" + System.Environment.NewLine);

                /* Standard Pack Quantity (SPQ)
                 Minimum Order Quantity (MOQ)
                 Total quantity of stock available(TQSA)
                 Last time buy (LTB)
                  */
                //now save csv to a file                    
                File.WriteAllText(String.Format("{0}/{1}", FileUploadManager.GetTemporaryUploadFilePath(), fileName), sbCSV.ToString());
            }
            catch (Exception)
            {
                //WriteError(e);
            }
            finally
            {
                jsn.Dispose();
                jsn = null;
            }
            return fileName;
        }

        public static string ExportToCSVForClientList(int reportId, int? Id, string CurrencyCode, Int32 CompanyId, Int32 srNo)
        {
            JsonObject jsn = new JsonObject();
            string fileName = string.Empty;
            List<List<object>> lstData = null;
            List<BLL.ReportColumn> lstColumns = null;
            try
            {
                JsonObject jsnItems = new JsonObject(true);
                string strHeadings = string.Empty;
                if (reportId == (int)Report.List.RequirementWithBOM)
                {
                    lstData = CustomerRequirement.GetBOMListForCRList(Id, SessionManager.ClientID, CompanyId);
                    lstColumns = BLL.ReportColumn.GetListForReport((int)Report.List.RequirementWithBOM);
                    fileName = String.Format("PriceRequestM_{1}_{0}.csv", SessionManager.ClientID + Convert.ToDateTime(DateTime.Now).ToString("mmss"), srNo.ToString());
                    strHeadings = FormatTextForCSV(Functions.GetGlobalResource("ReportTitles", Report.List.RequirementWithBOM), false, true);
                }
                if (reportId == (int)Report.List.PurchaseQuote)
                {
                    lstData = PurchaseOrderLine.GetPurchaseQuoteLineList(Id, SessionManager.ClientID, CompanyId);
                    lstColumns = BLL.ReportColumn.GetListForReport((int)Report.List.PurchaseQuote);
                    fileName = String.Format("PriceRequestM_{1}_{0}.csv", SessionManager.ClientID + Convert.ToDateTime(DateTime.Now).ToString("mmss"), srNo.ToString());
                    //fileName = String.Format("PurchaseRequest.csv");
                    strHeadings = FormatTextForCSV(Functions.GetGlobalResource("ReportTitles", Report.List.PurchaseQuote), false, true);
                }
                //add headings (get into string so we can replace variables in call to GetReportData below)
                //strHeadings += FormatTextForCSV("", false, true);
                strHeadings += FormatTextForCSV("Abbreviation-> SPQ: Standard Pack Quantity, MOQ: Minimum Order Quantity, TQSA: Total Quantity of Stock Available, LTB: Last Time Buy (Y/N)", false, true);
                strHeadings += FormatTextForCSV(Functions.GetGlobalResource("Misc", "AppTitle"), false, true);
                strHeadings += FormatTextForCSV(String.Format(Functions.GetGlobalResource("Reports", "DateAndLogin"), Functions.FormatDate(Functions.GetUKLocalTime(), true, true), SessionManager.LoginFullName), false, true);
                if (lstData[0].Count > 13)
                {
                    strHeadings += FormatTextForCSV(Functions.GetGlobalResource("Misc", "Notes") + lstData[0][14], false, true);
                }
                StringBuilder sbCSV = new StringBuilder(strHeadings);
                //sbCSV.Append(BlankCSVLine());
                //int[] CompanyId=null;
                if (lstData != null)
                {
                    //setup summarising variables
                    int lstColumnCount = lstColumns.Count;
                    //List<double> lstColumnSum = new List<double>(lstColumns.Count);
                    StringBuilder sbSummaryColumnHeadings = new StringBuilder("");
                    //StringBuilder sbSummaryColumnSum = new StringBuilder("");
                    StringBuilder sbSummaryColumnCount = new StringBuilder("");
                    //StringBuilder sbSummaryColumnAverage = new StringBuilder("");                               
                    //add column headings
                    for (int intCol = 0; intCol < lstColumns.Count; intCol++)
                    {
                        if (intCol == 0) sbCSV.Append(FormatTextForCSV("", false, false)); //blank for row count col
                        if (lstColumns[intCol].TitleResource.Contains("UnitPrice"))
                        {
                            sbCSV.Append(FormatTextForCSV(Functions.GetGlobalResource("Reports", lstColumns[intCol].TitleResource) + " ( " + CurrencyCode + " )", true, (intCol == lstColumns.Count - 1)));
                        }
                        else
                        {
                            sbCSV.Append(FormatTextForCSV(Functions.GetGlobalResource("Reports", lstColumns[intCol].TitleResource), true, (intCol == lstColumns.Count - 1)));
                        }
                    }

                    //step through all of data
                    for (int intRow = 0; intRow < lstData.Count; intRow++)
                    {
                        for (int intCol = 0; intCol < lstColumns.Count; intCol++)
                        {
                            //add row number
                            if (intCol == 0) sbCSV.Append(FormatTextForCSV((intRow + 1).ToString(), false, (intCol == lstColumns.Count - 1)));
                            //add data
                            //if (lstColumns[intCol].TitleResource.Contains("Company"))
                            //{
                            //    sbCSV.Append(FormatTextForCSV(Pages.PrintReport.GetFormattedItem((int)lstColumns[intCol].ReportColumnFormatNo, CompanyId), true, (intCol == lstColumns.Count - 1)));
                            //}
                            // else
                            //{
                            sbCSV.Append(FormatTextForCSV(Pages.PrintReport.GetFormattedItem((int)lstColumns[intCol].ReportColumnFormatNo, lstData[intRow][intCol]), true, (intCol == lstColumns.Count - 1)));
                            //}
                        }
                    }
                }

                else
                {
                    sbCSV.AppendLine(Functions.GetGlobalResource("NotFound", "ReportData"));
                }
                //sbCSV.Append(BlankCSVLine());
                //sbCSV.Append("Abbreviation:" + System.Environment.NewLine);                
                //sbCSV.Append("SPQ: Standard Pack Quantity" + System.Environment.NewLine);
                //sbCSV.Append("MOQ: Minimum Order Quantity" + System.Environment.NewLine);
                //sbCSV.Append("TQSA: Total Quantity of Stock Available" + System.Environment.NewLine);
                //sbCSV.Append("LTB: Last Time Buy (Y/N)" + System.Environment.NewLine);

                /* Standard Pack Quantity (SPQ)
                 Minimum Order Quantity (MOQ)
                 Total quantity of stock available(TQSA)
                 Last time buy (LTB)
                  */
                //now save csv to a file                    
                File.WriteAllText(String.Format("{0}/{1}", FileUploadManager.GetTemporaryUploadFilePath(), fileName), sbCSV.ToString());
            }
            catch (Exception)
            {
                //WriteError(e);
            }
            finally
            {
                jsn.Dispose();
                jsn = null;
            }
            return fileName;
        }

        private static string FormatTextForCSV(string strIn, bool blnLeadingComma, bool blnEndingLineFeed)
        {
            strIn = strIn.Replace(@"""", @"""""");
            return string.Format(@"{0}""{1}""{2}", (blnLeadingComma) ? "," : "", strIn, (blnEndingLineFeed) ? System.Environment.NewLine : "");
        }

        private static string BlankCSVLine()
        {
            return string.Format(@"""""{0}", System.Environment.NewLine);
        }
        // [001] code end
        #endregion

        internal static string GetDocumentHeaderImageNameInsert(int? intDivisionID, bool blnInvoice, int clientNo)
        {
            string strDivisionFileName = "";
            // string strImage = GetDocumentHeaderImageName_ClientNew(clientNo, true).Replace("~/", "");
            string strImage = "";
            Client cl = BLL.Client.Get(clientNo);
            if (cl != null)
                strImage = cl.DocumentHeaderImageName;

            bool blnHasLogDivImg = false;
            if (SessionManager.LoginDivisionID != null)
            {
                bool blnUseCompanyHeaderForInvoice = false;
                if (DivisionHasHeaderImageNewAzure((int)SessionManager.LoginDivisionID, out blnUseCompanyHeaderForInvoice, out strDivisionFileName))
                {
                    //if this is an invoice and the division reverts back to the company header for invoice, use that otherwise get the division header
                    if (!blnInvoice || !blnUseCompanyHeaderForInvoice)
                    {
                        if (!string.IsNullOrEmpty(strDivisionFileName))
                        {
                            strImage = strDivisionFileName;// FileUploadManager.GetDocumentHeaderImageName_DivisionNew((int)SessionManager.LoginDivisionID, true).Replace("~/", "");
                            blnHasLogDivImg = true;
                        }
                    }
                }
            }
            if (intDivisionID != null && blnHasLogDivImg == false)
            {
                bool blnUseCompanyHeaderForInvoice = false;
                if (DivisionHasHeaderImageNewAzure((int)intDivisionID, out blnUseCompanyHeaderForInvoice, out strDivisionFileName))
                {
                    //if this is an invoice and the division reverts back to the company header for invoice, use that otherwise get the division header
                    if (!blnInvoice || !blnUseCompanyHeaderForInvoice)
                    {
                        if (!string.IsNullOrEmpty(strDivisionFileName))
                        {
                            strImage = strDivisionFileName;// FileUploadManager.GetDocumentHeaderImageName_DivisionNew((int)intDivisionID, true).Replace("~/", "");
                        }
                    }
                }
            }
            return strImage;
        }
    }
}
