﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO
IF OBJECT_ID('dbo.usp_insert_PurchaseRequestLine_PriceQuote', 'P') IS NOT NULL
    DROP PROCEDURE dbo.usp_insert_PurchaseRequestLine_PriceQuote
GO
/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-205174]		An.TranTan			02-Jul-2024		Update			Using CustomerRequirementId instead of CustomerRequirementNumber
===========================================================================================
*/
CREATE PROCEDURE [dbo].[usp_insert_PurchaseRequestLine_PriceQuote]
    --******************************************************************************************                                
    --******************************************************************************************                                
    @PurchaseRequestNo INT,
    @xmlCustReqNo XML,
    @UpdatedBy INT = NULL,
    @Rowaffected int = NULL OUTPUT,
    @IsPoHub BIT
AS
BEGIN
	IF OBJECT_ID('tempdb..#tempRequirementToBeImported') IS NOT NULL 
		DROP TABLE #tempRequirementToBeImported
	CREATE TABLE #tempRequirementToBeImported
	(
		CustomerRequirementId INT NULL
	)
	INSERT INTO #tempRequirementToBeImported
	SELECT [Table].[Column].value('CustReqNo [1]', 'int')
	FROM @xmlCustReqNo.nodes('/ CustomerRequirements / CustomerRequirement') AS [Table]([Column]);

    INSERT INTO dbo.tbPurchaseRequestLine
    (
        PurchaseRequestNo,
        CustomerRequirementNo,
        BOMNo,
        FullPart,
        Part,
        Price,
        Closed,
        UpdatedBy,
        DLUP
    )
    SELECT @PurchaseRequestNo,
           cr.CustomerRequirementId,
           cr.BOMNo,
           cr.FullPart,
           cr.Part,
           0,
           0,
           @UpdatedBy,
           CURRENT_TIMESTAMP
    FROM dbo.tbCustomerRequirement cr
        JOIN #tempRequirementToBeImported temp ON temp.CustomerRequirementId = cr.CustomerRequirementId
	WHERE NOT EXISTS (SELECT 1 FROM tbPurchaseRequestLine
						WHERE PurchaseRequestNo = @PurchaseRequestNo
						AND CustomerRequirementNo = temp.CustomerRequirementId)
          and cr.BOMNo is not null
    SELECT @Rowaffected = @@ROWCOUNT
END
GO