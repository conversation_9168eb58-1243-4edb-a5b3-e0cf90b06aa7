//--------------------------------------------------------------------------------------------------------
// RP 11.12.2009:
// - cache data
//--------------------------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;

namespace Rebound.GlobalTrader.Site.Controls.DropDowns.Data {
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class ViewLevelSecurity : Rebound.GlobalTrader.Site.Data.DropDowns.Base
    {

        public override void ProcessRequest(HttpContext context) {
            SetDropDownType("ViewLevelSecurity");
            base.ProcessRequest(context);
        }

        protected override void GetData()
        {            
            string strCacheOptions = CacheManager.SerializeOptions(new object[] { SessionManager.Culture });
            string strCachedData = CacheManager.GetDropDownData(_objDropDown.ID, strCacheOptions);
           if (string.IsNullOrEmpty(strCachedData))
            {
                JsonObject jsn = new JsonObject();
                JsonObject jsnRegions = new JsonObject(true);
                foreach (BLL.Reason region in BLL.Reason.DropDownForViewLevel())
                {
                    JsonObject jsnRegion = new JsonObject();
                    jsnRegion.AddVariable("ID", region.ReasonId);
                    jsnRegion.AddVariable("Name", region.Name);
                    jsnRegions.AddVariable(jsnRegion);
                    jsnRegion.Dispose(); jsnRegion = null;
                }
                jsn.AddVariable("ViewLevelSecuritys", jsnRegions);
                jsnRegions.Dispose();
                jsnRegions = null;
                CacheManager.StoreDropDown(_objDropDown.ID, strCacheOptions, jsn.Result, CacheManager.CacheExpiryType.OneWorkingDay);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            else
            {
                _context.Response.Write(strCachedData);
            }
            strCachedData = null;
        }
    }
}
