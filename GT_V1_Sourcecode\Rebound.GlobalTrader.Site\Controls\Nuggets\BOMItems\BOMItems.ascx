<%@ Control Language="C#" CodeBehind="BOMItems.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Nuggets.BOMItems" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<link href="../../../css/Kub/AddNewRequirementPage/main.css" rel="stylesheet" />
<link href="../../../css/Kub/HubRfq/chatBot.css" rel="stylesheet" />

<script src="js/AS6081.js" type="text/javascript"></script>
<style>
    #ctl00_cphMain_ctlBOMItems_ctlDB_ctl13_ctlTabs_ctl01_tblStock_th_9 {
        width: 20px !important;
    }
    /*AS6081*/
    /*HubRFQ line item table start*/
    #ctl00_cphMain_ctlBOMItems_ctlDB_ctl13_ctlTabs_ctl01_tblStock_th_0, #ctl00_cphMain_ctlBOMItems_ctlDB_ctl13_ctlTabs_ctl01_tblStock_tbl tr td:nth-child(1) {
        width: 4% !important;
    }

    #ctl00_cphMain_ctlBOMItems_ctlDB_ctl13_ctlTabs_ctl01_tblStock_th_1, #ctl00_cphMain_ctlBOMItems_ctlDB_ctl13_ctlTabs_ctl01_tblStock_tbl tr td:nth-child(2) {
        width: 8% !important;
    }

    #ctl00_cphMain_ctlBOMItems_ctlDB_ctl13_ctlTabs_ctl01_tblStock_th_2, #ctl00_cphMain_ctlBOMItems_ctlDB_ctl13_ctlTabs_ctl01_tblStock_tbl tr td:nth-child(3) {
        width: 8% !important;
    }

    #ctl00_cphMain_ctlBOMItems_ctlDB_ctl13_ctlTabs_ctl01_tblStock_th_3, #ctl00_cphMain_ctlBOMItems_ctlDB_ctl13_ctlTabs_ctl01_tblStock_tbl tr td:nth-child(4) {
        width: 8% !important;
    }

    #ctl00_cphMain_ctlBOMItems_ctlDB_ctl13_ctlTabs_ctl01_tblStock_th_4, #ctl00_cphMain_ctlBOMItems_ctlDB_ctl13_ctlTabs_ctl01_tblStock_tbl tr td:nth-child(5) {
        width: 8% !important;
    }

    #ctl00_cphMain_ctlBOMItems_ctlDB_ctl13_ctlTabs_ctl01_tblStock_th_5, #ctl00_cphMain_ctlBOMItems_ctlDB_ctl13_ctlTabs_ctl01_tblStock_tbl tr td:nth-child(6) {
        width: 8% !important;
    }

    #ctl00_cphMain_ctlBOMItems_ctlDB_ctl13_ctlTabs_ctl01_tblStock_th_6, #ctl00_cphMain_ctlBOMItems_ctlDB_ctl13_ctlTabs_ctl01_tblStock_tbl tr td:nth-child(7) {
        width: 8% !important;
    }

    #ctl00_cphMain_ctlBOMItems_ctlDB_ctl13_ctlTabs_ctl01_tblStock_th_7, #ctl00_cphMain_ctlBOMItems_ctlDB_ctl13_ctlTabs_ctl01_tblStock_tbl tr td:nth-child(8) {
        width: 8% !important;
    }

    #ctl00_cphMain_ctlBOMItems_ctlDB_ctl13_ctlTabs_ctl01_tblStock_th_8, #ctl00_cphMain_ctlBOMItems_ctlDB_ctl13_ctlTabs_ctl01_tblStock_tbl tr td:nth-child(9) {
        width: 8% !important;
    }

    #ctl00_cphMain_ctlBOMItems_ctlDB_ctl13_ctlTabs_ctl01_tblStock_th_9, #ctl00_cphMain_ctlBOMItems_ctlDB_ctl13_ctlTabs_ctl01_tblStock_tbl tr td:nth-child(10) {
        width: 8% !important;
    }

    #ctl00_cphMain_ctlBOMItems_ctlDB_ctl13_ctlTabs_ctl01_tblStock_th_10, #ctl00_cphMain_ctlBOMItems_ctlDB_ctl13_ctlTabs_ctl01_tblStock_tbl tr td:nth-child(11) {
        width: 8% !important;
    }

    #ctl00_cphMain_ctlBOMItems_ctlDB_ctl13_ctlTabs_ctl01_tblStock_th_11, #ctl00_cphMain_ctlBOMItems_ctlDB_ctl13_ctlTabs_ctl01_tblStock_tbl tr td:nth-child(12) {
        width: 15% !important;
    }
    .dropdown {
        position: relative;
        display: inline-block;
    }

    .dropdown-content {
        display: none;
        position: absolute;
        background-color: #f1f1f1;
        min-width: 160px;
        box-shadow: 0px 8px 16px 0px rgb(0 0 0 / 20%);
        z-index: 5;
        /*top: -1px;
        margin-left: 143px;*/
        /*padding-left:10px;*/
    }

        .dropdown-content a {
            color: #0000ff;
            padding: 8px 7px;
            text-decoration: none;
            display: block;
        }

            .dropdown-content a:hover {
                background-color: #ddd;
            }

        .dropdown-content .notes {
            font-weight: bold;
            padding: 10px 10px 0px;
        }
        .dropdown-content .item {
            color: #0000ff;
            padding: 10px;
            display: block;
        }

            .dropdown-content .item:hover {
                background-color: #ddd;
            }

    .dropdown:hover .dropdown-content {
        display: block;
    }

    .dropdown:hover .dropbtn {
        /*background-color: #3e8e41;*/
    }
    /*HubRFQ line item table end*/

    /*AS6081 end */

    #ctl00_cphMain_ctlBOMItems_ctlDB_ctl13_cmbManufacturertxt {
        width: 250px;
        background: #fff;
        border: 1px solid #4f834d;
        float:left;
    }
    .padding-td-5px {
        padding: 3px !important;
    }
    #ctl00_cphMain_ctlBOMItems_ctlDB_ctl13_cmbManufactureraut {
        float: left;
        padding: 3px 0;
    }
    #ctl00_cphMain_ctlBOMItems_ctlDB_ctl13_cmbManufactureraut_ctl03 {
        border: solid 1px #159855;
        padding: 0 5px;
        background-color: #fff;
    }
</style>
<script src="../../../js/Kub/AddNewRequirementPage/jquery-3.1.1.min.js"></script>
<script src="../../../js/Kub/ConfigureKubHUBRFQ.js"></script>
<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" BoxType="Standard">
    <Links>
        <ReboundUI:IconButton ID="ibtnRelease" runat="server" IconButtonMode="hyperlink"
            IconGroup="Nugget" IconTitleResource="Release" IsInitiallyEnabled="false" />

        <ReboundUI:IconButton ID="ibtnUnRelease" runat="server" IconButtonMode="hyperlink"
            IconGroup="Nugget" IconTitleResource="UnRelease" IsInitiallyEnabled="false" />

        <ReboundUI:IconButton ID="ibtnAdd" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget"
            IconTitleResource="Add" IconCSSType="Add" />
        <ReboundUI:IconButton ID="ibtnDelete" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget"
            IconTitleResource="Delete" IconCSSType="Delete" IsInitiallyEnabled="false" />

        <ReboundUI:IconButton ID="ibtnNoBid" runat="server" IconButtonMode="hyperlink"
            IconGroup="Nugget" IconTitleResource="NoBid" IsInitiallyEnabled="false" />
        <ReboundUI:IconButton ID="ibtnRecallNoBid" runat="server" IconButtonMode="hyperlink"
            IconGroup="Nugget" IconTitleResource="RecallNoBid" IsInitiallyEnabled="false" />
        <ReboundUI:IconButton ID="ibtnNote" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="AddNewHUBFRQNote" IconCSSType="Add" IsInitiallyEnabled="false" />

        <%--start code by umendra 26-10-2020--%>
        <%--<ReboundUI:IconButton ID="ibtnImportSrcReslt" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="ImportSourcingResult" IconCSSType="Add" IsInitiallyEnabled="true" />--%>
        <%-- end code by umendra 26-10-2020--%>


        <ReboundUI:IconButton ID="ibtnApplyPartwatch" runat="server" IconButtonMode="hyperlink"
            IconGroup="Nugget" IconTitleResource="ApplyPartwatch" IsInitiallyEnabled="true" />

        <ReboundUI:IconButton ID="ibtnRemovePartwatch" runat="server" IconButtonMode="hyperlink"
            IconGroup="Nugget" IconTitleResource="RemovePartwatch" IsInitiallyEnabled="true" />


        <ReboundUI:IconButton ID="ibtnImportSrcReslt" runat="server" IconButtonMode="hyperlink"
            IconGroup="Nugget" IconTitleResource="ImportSourcingResult" style="display:none;"/>
        <ReboundUI:IconButton ID="ibtnHubImportSR" runat="server" IconButtonMode="hyperlink"
            IconGroup="Nugget" IconTitleResource="ImportSourcingResult" IsInitiallyEnabled="true"/>

        <ReboundUI:IconButton ID="ibtnExportToExcel" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget"
            IconTitleResource="ExportToExcel" IsInitiallyEnabled="true" />
    </Links>

    <Content>
        <div id="As6081WarningMsg" class="nuggetMessages">
            <div class="nuggetMessage nuggetMessageError" id="dvtxt"></div>
        </div>
        <ReboundUI:TabStrip ID="ctlTabs" runat="server">
            <TabsTemplate>
                <ReboundUI:Tab ID="ctlTabStock" runat="server" IsSelected="true" RelatedContentPanelID="pnlTabStock"
                    TitleText="CustReq" />
            </TabsTemplate>
            <TabsContent>
                <asp:Panel ID="pnlTabStock" runat="server">
                    <ReboundUI:FlexiDataTable ID="tblStock" runat="server" PanelHeight="250" AllowSelection="true" />
                </asp:Panel>
            </TabsContent>
        </ReboundUI:TabStrip>

        <asp:Panel ID="pnlLoadingLineDetail" runat="server" CssClass="subInfoLoading invisible">
            <%=Functions.GetGlobalResource("Misc", "Loading")%>
        </asp:Panel>
        <asp:Panel ID="pnlLineDetailError" runat="server" CssClass="error invisible" />
        <asp:Panel ID="pnlLineDetail" runat="server" CssClass="invisible">
            <table class="threeCols">
                <tr>
                    <td class="col1">
                        <table cellpadding="0" cellspacing="0" border="0" class="dataItems">
                            <%--<ReboundUI:DataItemRow id="ctlCompany" runat="server" ResourceTitle="Company" />--%>
                            <ReboundUI:DataItemRow ID="hidCompanyID" runat="server" FieldType="Hidden" />
                            <ReboundUI:DataItemRow ID="hidCompanyName" runat="server" FieldType="Hidden" />
                            <%--<ReboundUI:DataItemRow id="ctlContact" runat="server" ResourceTitle="Contact" />--%>
                            <ReboundUI:DataItemRow ID="hidContactID" runat="server" FieldType="Hidden" />
                            <ReboundUI:DataItemRow ID="hidContactName" runat="server" FieldType="Hidden" />
                            <ReboundUI:DataItemRow ID="ctlQuantity" runat="server" ResourceTitle="Quantity" />
                            <ReboundUI:DataItemRow ID="ctlPartNo" runat="server" ResourceTitle="PartNo" />
                            <ReboundUI:DataItemRow ID="ctlAs6081Required" runat="server" ResourceTitle="AS6081Filter" />
                            <ReboundUI:DataItemRow ID="ctlROHS" runat="server" ResourceTitle="ROHS" />
                            <ReboundUI:DataItemRow ID="hidROHS" runat="server" FieldType="Hidden" />
                            <ReboundUI:DataItemRow ID="ctlCustomerPart" runat="server" ResourceTitle="CustomerPartNo" />
                            <ReboundUI:DataItemRow ID="ctlManufacturer" runat="server" ResourceTitle="Manufacturer" />
                            <ReboundUI:DataItemRow ID="hidManufacturer" runat="server" FieldType="Hidden" />
                            <ReboundUI:DataItemRow ID="hidManufacturerNo" runat="server" FieldType="Hidden" />
                            <ReboundUI:DataItemRow ID="hidMfrAdvisoryNotes" runat="server" FieldType="Hidden" />
                            <ReboundUI:DataItemRow ID="ctlDateCode" runat="server" ResourceTitle="DateCode" />
                            <ReboundUI:DataItemRow ID="ctlProduct" runat="server" ResourceTitle="Product" />
                            <ReboundUI:DataItemRow ID="ctlPrdDutyCodeRate" runat="server" ResourceTitle="DutyCodeRate" />
                            <ReboundUI:DataItemRow ID="hidProductID" runat="server" FieldType="Hidden" />
                            <ReboundUI:DataItemRow ID="ctlPackage" runat="server" ResourceTitle="Package" />
                            <ReboundUI:DataItemRow ID="hidPackageID" runat="server" FieldType="Hidden" />
                            <ReboundUI:DataItemRow ID="ctlPartWatch" runat="server" ResourceTitle="PartWatch" FieldType="CheckBox" />
                            <ReboundUI:DataItemRow ID="ctlFactorySealed" runat="server" ResourceTitle="FactorySealed" FieldType="CheckBox" />

                            <ReboundUI:DataItemRow ID="ctlMSL" runat="server" ResourceTitle="MSL" />
                            <%-- Hidden Rule --%>
                            <ReboundUI:DataItemRow ID="ctlIsNoBid" runat="server" />
                            <ReboundUI:DataItemRow ID="ctlIsNoBidNotes" runat="server" ResourceTitle="NoBidNotes" />
                            <ReboundUI:DataItemRow ID="hidMSL" runat="server" FieldType="Hidden" />

                            <ReboundUI:DataItemRow ID="ctlCountryOfOrigin" runat="server" ResourceTitle="CountryOfOrigin" />
                            <ReboundUI:DataItemRow ID="ctlLifeCycleStage" runat="server" ResourceTitle="LifeCycleStage" />
                            <ReboundUI:DataItemRow ID="ctlIHSProduct" runat="server" ResourceTitle="IHSProductName" />
                            <ReboundUI:DataItemRow ID="ctlHTSCode" runat="server" ResourceTitle="HTSCode" />
                            <ReboundUI:DataItemRow ID="ctlECCNCode" runat="server" ResourceTitle="ECCNCode" />
                            <ReboundUI:DataItemRow ID="ctlPackagingSize" runat="server" ResourceTitle="PackagingSize" />
                            <ReboundUI:DataItemRow ID="ctlDescriptions" runat="server" ResourceTitle="Descriptions" />
                        </table>
                    </td>
                    <td class="col2">
                        <table cellpadding="0" cellspacing="0" border="0" class="dataItems">
                            <ReboundUI:DataItemRow ID="ctlTargetPrice" runat="server" ResourceTitle="CustomerTargetPrice" />
                            <ReboundUI:DataItemRow ID="hidPrice" runat="server" FieldType="Hidden" />
                            <ReboundUI:DataItemRow ID="ctlCurrency" runat="server" ResourceTitle="Currency" />
                            <ReboundUI:DataItemRow ID="hidCurrencyID" runat="server" FieldType="Hidden" />
                            <ReboundUI:DataItemRow ID="ctlDateRequired" runat="server" ResourceTitle="CustDateRequired" />
                            <ReboundUI:DataItemRow ID="ctlClosed" runat="server" ResourceTitle="Closed" />
                            <ReboundUI:DataItemRow ID="ctlClosedReason" runat="server" ResourceTitle="Reason" />
                            <ReboundUI:DataItemRow ID="ctlUsage" runat="server" ResourceTitle="Usage" />
                            <ReboundUI:DataItemRow ID="hidUsageID" runat="server" FieldType="Hidden" />
                            <ReboundUI:DataItemRow ID="ctlBOM" runat="server" ResourceTitle="BOMChk" FieldType="CheckBox" />
                            <ReboundUI:DataItemRow ID="ctlBOMName" runat="server" ResourceTitle="BOMName" />
                            <ReboundUI:DataItemRow ID="ctlNotes" runat="server" ResourceTitle="CustomerNotes" />
                            <ReboundUI:DataItemRow ID="ctlInstructions" runat="server" ResourceTitle="InternalNotes" />
                            <ReboundUI:DataItemRow ID="hidDisplayStatus" runat="server" FieldType="Hidden" />
                            <ReboundUI:DataItemRow ID="hidBOMID" runat="server" FieldType="Hidden" />
                            <ReboundUI:DataItemRow ID="hidBOMHeaderDisplayStatus" runat="server" FieldType="Hidden" />
                            <%-- <ReboundUI:DataItemRow id="ctlBOMHeader" runat="server" ResourceTitle="IPOBOM" />--%>
                            <ReboundUI:DataItemRow ID="ctlPQA" runat="server" ResourceTitle="PQA" FieldType="CheckBox" />
                            <ReboundUI:DataItemRow ID="ctlObsolete" runat="server" ResourceTitle="Obsolete" FieldType="CheckBox" />
                            <ReboundUI:DataItemRow ID="ctlLastTimeBuy" runat="server" ResourceTitle="LastTimeBuy" FieldType="CheckBox" />
                            <%-- Hidden rule --%>
                            <ReboundUI:DataItemRow ID="ctlPriceRequest" runat="server" ResourceTitle="PriceRequest" />
                            <asp:TableRow>
                                <asp:TableCell></asp:TableCell>
                                <asp:TableCell>
                            <a id="myplasegrde" class="MagicBoX"  href="javascript:void(0);">
                                    <img src="images/blackbox.gif" style="height: 29px; width: 29px; border-width: 0px;"></a>
                                </asp:TableCell>

                            </asp:TableRow>
                            <tr id="CmbLyticaManufacture">
                                <td style="font-weight: bold;" class="padding-td-5px">
                                    <label id="lblmrf">Lytica Manufacturer Reference</label>
                                </td>
                                <td ID="ManufacturerError1" class="padding-td-5px" style="padding-left: 0px !important;">
                                    <ReboundUI:ComboNew class="wdt195 " ID="cmbManufacturer" runat="server"
                                        AutoSearchControlAssembly="Rebound.GlobalTrader.Site" AutoSearchControlType="LyticaManufacturers"
                                        ParentControlID="cmbManufacturer" />
                                    <span id="spnManufacturerr"></span>
                                </td>
                                <td class="padding-td-5px">
                                    <label id="lblRsMFR"><span id="spanmfr" class="MFRResticted"></span></label>
                                </td>
                            </tr>
                            <ReboundUI:DataItemRow ID="ctlAveragePrice" runat="server" ResourceTitle="AveragePriceLytica" />
                            <ReboundUI:DataItemRow ID="ctlTargetPriceAPI" runat="server" ResourceTitle="TargetPriceLytica" />
                            <ReboundUI:DataItemRow ID="ctlMarketLeading" runat="server" ResourceTitle="MarketLeadingLytica" />
                        </table>
                    </td>
                    <td class="col3">
                        <table cellpadding="0" cellspacing="0" border="0" class="dataItems">
                            <ReboundUI:DataItemRow ID="ctlRefirbsAcceptable" runat="server" ResourceTitle="RefirbsAcceptable" FieldType="CheckBox" />
                            <ReboundUI:DataItemRow ID="ctlTestingRequired" runat="server" ResourceTitle="TestingRequired" FieldType="CheckBox" />

                            <ReboundUI:DataItemRow ID="ctlAlternativesAccepted" runat="server" ResourceTitle="AlternativesAccepted" FieldType="CheckBox" />
                            <ReboundUI:DataItemRow ID="ctlRepeatBusiness" runat="server" ResourceTitle="RepeatBusiness" FieldType="CheckBox" />

                            <ReboundUI:DataItemRow ID="ctlTargetSellPrice" runat="server" ResourceTitle="TargetSellPrice" FieldType="Hidden" />
                            <ReboundUI:DataItemRow ID="ctlCompetitorBestoffer" runat="server" ResourceTitle="CompetitorBestoffer" />

                            <ReboundUI:DataItemRow ID="ctlTargetSellPriceHidden" runat="server" FieldType="Hidden" />
                            <ReboundUI:DataItemRow ID="ctlCompetitorBestofferHidden" runat="server" FieldType="Hidden" />

                            <ReboundUI:DataItemRow ID="ctlCustomerDecisionDate" runat="server" ResourceTitle="CustomerDecisionDate" />
                            <ReboundUI:DataItemRow ID="ctlRFQClosingDate" runat="server" ResourceTitle="RFQClosingDate" />
                            <ReboundUI:DataItemRow ID="ctlQuoteValidityRequiredHid" runat="server" FieldType="Hidden" />
                            <ReboundUI:DataItemRow ID="ctlQuoteValidityRequired" runat="server" ResourceTitle="QuoteValidityRequired" />
                            <ReboundUI:DataItemRow ID="ctlType" runat="server" ResourceTitle="Type" />
                            <ReboundUI:DataItemRow ID="ctlTypeHid" runat="server" ResourceTitle="Type" FieldType="Hidden" />
                            <ReboundUI:DataItemRow ID="ctlOrderToPlace" runat="server" ResourceTitle="OrderToPlace" FieldType="CheckBox" />
                            <ReboundUI:DataItemRow ID="ctlRequirementforTraceability" runat="server" ResourceTitle="RequirementforTraceability" />
                            <ReboundUI:DataItemRow ID="ctlRequirementforTraceabilityHid" runat="server" FieldType="Hidden" />
                            <ReboundUI:DataItemRow ID="ctlEAU" runat="server" ResourceTitle="EAU" />
                            <ReboundUI:DataItemRow ID="ctlCustomerRefNo" runat="server" ResourceTitle="CustomerRefNo" />
                        </table>
                    </td>
                </tr>
            </table>
        </asp:Panel>
        <%--<script src="js/jquery.min.js"></script>--%>
        <style type="text/css">
            /* Button used to open the chat form - fixed at the bottom of the page */
            .open-button {
                /*top: 144px; 
                        left: 1076px;*/
                position: absolute;
                bottom: -3px;
                right: 15px;
                border: 3px solid #f1f1f1;
                z-index: 9;
                /* bottom: 28px; */
                right: 1px;
                width: 25px;
                font-size: xx-large;
                text-align: center;
                height: 20px;
                /* color: black; */
                background-color: black;
                padding-top: 5px;
            }

            .open-buttons {
                /*top: 144px; 
                        left: 1076px;*/
                /*position: absolute;*/
                bottom: -3px;
                /*right: 15px;*/
                border: 3px solid #f1f1f1;
                z-index: 9;
                /* bottom: 28px; */
                /*right: 1px;*/
                width: 25px;
                font-size: xx-large;
                text-align: center;
                height: 20px;
                /* color: black; */
                background-color: black;
                padding-top: 5px;
            }

            /* The popup chat - hidden by default */
            .chat-popup {
                /*display: none;
                        position: absolute;*/
                bottom: 29px;
                border: 3px solid #f1f1f1;
                z-index: 9;
                bottom: 28px;
                right: 1px;
                width: 300px;
                font-size: xx-large;
                background-color: darkslategray;
                text-align: right;
                min-height: 100px;
            }

            .popupCloseButtons {
                border: 3px solid #999;
                border-radius: 50px;
                cursor: pointer;
                display: inline-block;
                font-family: arial;
                font-weight: bold;
                position: absolute;
                top: -17px;
                right: -5px;
                font-size: 14px;
                line-height: 10px;
                width: 30px;
                height: 20px;
                text-align: center;
                background-color: black;
            }

            #mydiv {
                top: 350px;
                right: 500px;
                z-index: 9;
                font-size: xx-large;
                background-color: white;
                text-align: right;
                position: absolute;
                height: 0px;
                width: 450px;
            }

            #mydivheader {
                top: 191px;
                left: 800px;
                cursor: move;
                z-index: 10;
            }

            .tbForm {
                color: black;
                text-align: left;
                width: 100%;
                margin: 6px 0px;
            }
            /* IHS Part Search Popup css start */


            /* The Modal (background) */
            .modal {
                display: none; /* Hidden by default */
                position: fixed; /* Stay in place */
                z-index: 1; /* Sit on top */
                padding-top: 100px; /* Location of the box */
                left: 0;
                top: 0;
                width: 100%; /* Full width */
                height: 100%; /* Full height */
                overflow: auto; /* Enable scroll if needed */
                background-color: rgb(0,0,0); /* Fallback color */
                background-color: rgba(0,0,0,0.4); /* Black w/ opacity */
            }

            /* Modal Content */
            .modal-content {
                background-color: #66a75e;
                margin: auto;
                padding: 20px;
                border: 1px solid #66a75e;
                width: 50%;
                position: relative;
                overflow: hidden;
            }

                .modal-content table tr td, .modal-content table tr th {
                    text-align: left;
                }
            /* The Close Button */
            .close {
                color: #aaaaaa;
                float: right;
                font-size: 28px;
                font-weight: bold;
            }

                .close:hover,
                .close:focus {
                    color: #000;
                    text-decoration: none;
                    cursor: pointer;
                }

            .okbtn {
                cursor: pointer;
                position: absolute;
                bottom: 50px;
                right: 35px;
                padding: 7px 13px;
                background: #5e845a;
                float: left;
                border-radius: 2px;
                border: 1px #5e845a solid;
            }

                .okbtn:hover {
                    background: #56a34e;
                }

            .GridPartdetails table.dataTable td {
                border-color: #5d8857;
                border-top-color: rgb(93, 136, 87);
                background-color: #c8fac2;
                color: #000;
                font-size: 10px;
                font-family: Verdana !important;
                position: relative;
                border-top: none !important;
            }

            .LoaderPopup {
                background: rgba(0,0,0,.4);
                cursor: pointer;
                display: none;
                /* height: auto; */
                height: 100%;
                position: absolute;
                text-align: center;
                top: 170px;
                width: 100%;
                z-index: 10000;
            }

            .cssload-loader {
                width: 244px;
                height: 49px;
                line-height: 49px;
                text-align: center;
                position: absolute;
                left: 50%;
                top: 50%;
                transform: translate(-50%, -50%);
                -o-transform: translate(-50%, -50%);
                -ms-transform: translate(-50%, -50%);
                -webkit-transform: translate(-50%, -50%);
                -moz-transform: translate(-50%, -50%);
                font-family: helvetica, arial, sans-serif;
                text-transform: uppercase;
                font-weight: 900;
                font-size: 18px;
                color: rgb(206,66,51);
                letter-spacing: 0.2em;
                background-color: aliceblue;
            }







            #mydivheader .chat-popup {
                width: auto !important;
            }

            #mydivheader .popupCloseButton {
                border: 2px solid #fff;
                top: -11px;
                right: -8px;
                width: 20px;
                height: 20px;
            }



            #myModal .modal-content span, #myModal .modal-content label, #myModal input {
                width: auto !important;
            }

            #ctl00_cphMain_ctlAdd_ctlDB_ctl14_ctlAdd_ctlDB_ctlPartDetail_tdTitle {
                display: none;
            }

            #ctl00_cphMain_ctlBOMItems_ctlDB_ctl13_ctlPartNo_lbl center a {
                float: left;
                margin-left: 63px;
                margin-top: -16px;
            }

            /*.LoaderPopup {
                            background: rgba(0,0,0,.4);
                            cursor: pointer;
                            display: none;
                            /* height: auto; */
            /*height: 100%;
                            position: absolute;
                            text-align: center;
                            top: -49px;
                            width: 100%;
                            z-index: 10000;
                        }*/

            /*.cssload-loader {
                            width: 244px;
                            height: 49px;
                            line-height: 49px;
                            text-align: center;
                            position: absolute;
                            left: 50%;
                            top: 50%;
                            transform: translate(-50%, -50%);
                            -o-transform: translate(-50%, -50%);
                            -ms-transform: translate(-50%, -50%);
                            -webkit-transform: translate(-50%, -50%);
                            -moz-transform: translate(-50%, -50%);
                            font-family: helvetica, arial, sans-serif;
                            text-transform: uppercase;
                            font-weight: 900;
                            font-size: 18px;
                            color: rgb(206,66,51);
                            letter-spacing: 0.2em;
                            background-color: aliceblue;
                        }*/
            #tbpartdet {
                box-shadow: 2px 1px 22px 3px #000;
            }

            .firstth {
                background-color: #5490ce;
                color: #fff;
                font-size: 12px !important;
                text-align: center !important;
                padding: 10px;
            }

            .firttd {
                background-color: #1d66b0;
                padding: 15px !important;
                color: #fff !important;
            }

            .secondtd {
                background-color: #1c334a;
                padding: 15px !important;
                color: #fff !important;
            }

            .last-td {
                background-color: #5490ce;
                padding: 15px !important;
                color: #fff !important;
            }

            .dropDownAdd, .MagicBoX {
                left: 52%;
                position: absolute;
                width: 20px;
                height: 17px;
                background-repeat: no-repeat;
                background-position: center center;
            }
            /* IHS Part Search Popup css start */
        </style>

        <div id="mydiv" style="display: none">
            <div id="closePoppartdetails" class="popupCloseButtons" style="color: white;">&times;</div>
            <div class="LoaderPopup" id="divBlockBox" style="left: 10px;">
                <div>
                    <div class="cssload-loader">Loading..</div>
                </div>
            </div>
            <table class="chat-popup tbForm" id="tbpartdet" style="display: none; border-spacing: 0px;">
                <tr>
                    <th colspan="2" style="text-align: left;" class="title firstth"><b>Part Name:</b> <b><span
                        id="spnpartname"></span></b>
                    </th>
                </tr>
                <tr>
                    <td class="title firttd">Sold to Customer: <span id="spnsoldtocuston"></span>
                    </td>

                    <td class="title secondtd">Current Customer: <span id="spnCurrentCust"></span>
                    </td>
                </tr>
                <tr>
                    <td class="title firttd">Sale Price: <span id="spnLastSoldPrice"></span>
                    </td>
                    <td class="title secondtd">Sale Price: <span id="spnCustLastSoldPrice"></span>
                    </td>
                </tr>
                <tr>
                    <td class="title firttd">Sale Date: <span id="spnlastsoldon"></span>
                    </td>
                    <td class="title secondtd">Sale Date: <span id="spnCustlastsoldon"></span>
                    </td>
                </tr>
                <tr>
                    <td class="title firttd">Quantity: <span id="spnLastQuantity"></span>
                    </td>

                    <td class="title secondtd">Quantity: <span id="spnCustQuantity"></span>
                    </td>
                </tr>
                <tr>
                    <td class="title firttd">Supplier Type: <span id="spnLastSupplierType"></span>
                    </td>

                    <td class="title secondtd">Supplier Type: <span id="spnCustSupplierType"></span>
                    </td>
                </tr>
                <tr>
                    <td class="title firttd">Date Code: <span id="spnLastDatecode"></span>
                    </td>

                    <td class="title secondtd">Date Code: <span id="spnCustDatecode"></span>
                    </td>
                </tr>
                <tr>
                    <td class="title firttd">Date Purchased: <span id="spnLastDatePurchased"></span>
                    </td>

                    <td class="title secondtd">Date Purchased: <span id="spnCustDatePurchased"></span>
                    </td>
                </tr>
                <tr>
                    <td class="title firttd">Customer Region: <span id="spnLastCustomerRegion"></span>
                    </td>

                    <td class="title secondtd">Customer Region: <span id="spnCustomerRegion"></span>
                    </td>
                </tr>
                <tr>
                    <td colspan="2" class="title last-td">12 months Average Rebound Re-Sale Price: <span
                        id="spnAvgPrice"></span>
                    </td>
                </tr>
                <tr>
                    <td colspan="2" class="title last-td">Lowest Sale Price In Last 12 Months: <span id="spnLastPricePaid12"></span>
                    </td>
                </tr>
                <tr>
                    <td colspan="2" class="title last-td">Best Buy Price In Last 12 Months: <span id="spnCleintBestPricePaid12"></span>
                    </td>
                </tr>


                <tr style="display: none;">
                    <td class="title">Stock Detail 
                                <table cellpadding="0" cellspacing="0" border="0" class="stockQuantities">
                                    <tbody>
                                        <tr>
                                            <td class="inStock">
                                                <div class="number">
                                                    <span id="spnInStock"></span>
                                                </div>
                                                In Stock</td>
                                            <td class="sep">&nbsp;</td>
                                            <td class="onOrder">
                                                <div class="number"><span id="spnOnOrder"></span></div>
                                                On Order</td>
                                            <td class="sep">&nbsp;</td>
                                            <td class="allocated">
                                                <div class="number"><span id="spnAllocated"></span></div>
                                                Allocated</td>
                                            <td class="sep">&nbsp;</td>
                                            <td class="available" style="color: blueviolet">
                                                <div class="number"><span id="spnAvailable"></span></div>
                                                Available</td>
                                        </tr>
                                    </tbody>
                                </table>
                    </td>
                </tr>
            </table>

        </div>

    </Content>
    <Forms>
        <ReboundForm:BOMItems_Confirm ID="ctlConfirm" runat="server" />
        <ReboundForm:BOMItems_Add ID="ctlAdd" runat="server" />
        <ReboundForm:BOMItems_Delete ID="ctlDelete" runat="server" />
        <ReboundForm:BOMItems_UnRelease ID="ctlUnRelease" runat="server" />
        <ReboundForm:BOMItems_NoBidConfirm ID="BOMItems_NoBidConfirm" runat="server" />
        <ReboundForm:BOMItems_AddExpedite ID="ctlExpedite" runat="server" />
        <%--start code by umendra--%>
        <%--<ReboundForm:BOMImportSourcingResult ID="ctlImprtSrcResult" runat="server" />--%>
        <ReboundForm:HubImportSourcingResult ID="ctlHubImportSR" runat="server" />
        <%--<ReboundForm:HubImportSourcingResult ID="ctlImportSourcingResult" runat="server" />--%>
        <%-- end code by umendra--%>
        <ReboundForm:BOMItems_ConfirmPartwatch ID="BOMItems_ConfirmPartwatch" runat="server" />
        <ReboundForm:BOMItems_ConfirmRemovePartwatch ID="BOMItems_ConfirmRemovePartwatch" runat="server" />
    </Forms>
</ReboundUI_Nugget:DesignBase>
