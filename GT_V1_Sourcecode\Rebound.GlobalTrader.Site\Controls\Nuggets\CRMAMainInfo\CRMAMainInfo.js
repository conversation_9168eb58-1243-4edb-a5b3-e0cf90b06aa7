Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");Rebound.GlobalTrader.Site.Controls.Nuggets.CRMAMainInfo=function(n){Rebound.GlobalTrader.Site.Controls.Nuggets.CRMAMainInfo.initializeBase(this,[n]);this._intCRMAID=-1;this._IsHubAutoCRMA=!1};Rebound.GlobalTrader.Site.Controls.Nuggets.CRMAMainInfo.prototype={get_intCRMAID:function(){return this._intCRMAID},set_intCRMAID:function(n){this._intCRMAID!==n&&(this._intCRMAID=n)},get_intContactID:function(){return this._intContactID},set_intContactID:function(n){this._intContactID!==n&&(this._intContactID=n)},get_ibtnEdit:function(){return this._ibtnEdit},set_ibtnEdit:function(n){this._ibtnEdit!==n&&(this._ibtnEdit=n)},get_ibtnAdd:function(){return this._ibtnAdd},set_ibtnAdd:function(n){this._ibtnAdd!==n&&(this._ibtnAdd=n)},get_tblExpHist:function(){return this._tblExpHist},set_tblExpHist:function(n){this._tblExpHist!==n&&(this._tblExpHist=n)},get_pnlExpHist:function(){return this._pnlExpHist},set_pnlExpHist:function(n){this._pnlExpHist!==n&&(this._pnlExpHist=n)},get_pnlLoadingExpHist:function(){return this._pnlLoadingExpHist},set_pnlLoadingExpHist:function(n){this._pnlLoadingExpHist!==n&&(this._pnlLoadingExpHist=n)},get_pnlExpHistError:function(){return this._pnlExpHistError},set_pnlExpHistError:function(n){this._pnlExpHistError!==n&&(this._pnlExpHistError=n)},get_ibtnViewTree:function(){return this._ibtnViewTree},set_ibtnViewTree:function(n){this._ibtnViewTree!==n&&(this._ibtnViewTree=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Nuggets.CRMAMainInfo.callBaseMethod(this,"initialize");this.addRefreshEvent(Function.createDelegate(this,this.getData));this._ibtnEdit&&($R_IBTN.addClick(this._ibtnEdit,Function.createDelegate(this,this.showEditForm)),this._frmEdit=$find(this._aryFormIDs[0]),this._frmEdit._intCRMAID=this._intCRMAID,this._frmEdit.addCancel(Function.createDelegate(this,this.cancelEdit)),this._frmEdit.addSaveComplete(Function.createDelegate(this,this.saveEditComplete)));this._ibtnAdd&&($R_IBTN.addClick(this._ibtnAdd,Function.createDelegate(this,this.showAddForm)),this._frmAdd=$find(this._aryFormIDs[1]),this._frmAdd.addCancel(Function.createDelegate(this,this.cancelAddForm)),this._frmAdd.addSaveComplete(Function.createDelegate(this,this.saveAddComplete)));this._ibtnViewTree&&$R_IBTN.addClick(this._ibtnViewTree,Function.createDelegate(this,this.OpenDocTree));this._blnIsNoDataFound||this._blnHasInitialData||this.getData()},dispose:function(){this.isDisposed||(this._ibtnEdit&&$R_IBTN.clearHandlers(this._ibtnEdit),this._ibtnAdd&&$R_IBTN.clearHandlers(this._ibtnAdd),this._ibtnAdd=null,this._frmEdit&&this._frmEdit.dispose(),this._frmEdit=null,this._ibtnEdit=null,this._intCRMAID=null,this._intContactID=null,Rebound.GlobalTrader.Site.Controls.Nuggets.CRMAMainInfo.callBaseMethod(this,"dispose"))},getData:function(){this.getData_Start();var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/CRMAMainInfo");n.set_DataObject("CRMAMainInfo");n.set_DataAction("GetData");n.addParameter("id",this._intCRMAID);n.addDataOK(Function.createDelegate(this,this.getDataOK));n.addError(Function.createDelegate(this,this.getDataError));n.addTimeout(Function.createDelegate(this,this.getDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();this.getExpHist();n=null},getDataOK:function(n){var t=n._result;this.setFieldValue("ctlCustomerName",$RGT_nubButton_Company(t.CustomerNo,t.Customer,null,null,null,t.CustomerAdvisoryNotes));this.setFieldValue("ctlWarehouse",$R_FN.setCleanTextValue(t.Warehouse));this._frmEdit&&this._frmEdit.setFieldValue("ctlWarehouse_Label",$R_FN.setCleanTextValue(t.Warehouse));this.setFieldValue("ctlContact",$RGT_nubButton_Contact(t.ContactNo,t.Contact));this.setFieldValue("ctlAuthoriser",$R_FN.setCleanTextValue(t.Authoriser));this.setFieldValue("ctlDivision",$R_FN.setCleanTextValue(t.Division));this.setFieldValue("ctlRMADate",$R_FN.setCleanTextValue(t.RMADate));this.setFieldValue("ctlInvoice",$RGT_nubButton_Invoice(t.InvoiceNo,t.Invoice));this.setFieldValue("ctlSalesOrder",$RGT_nubButton_SalesOrder(t.SalesOrderNo,t.SalesOrder));this.setFieldValue("ctlShipVia",$R_FN.setCleanTextValue(t.ShipVia));this.setFieldValue("ctlIncoterm",$R_FN.setCleanTextValue(t.Incoterm));this.setFieldValue("ctlShippingAccountNo",$R_FN.setCleanTextValue(t.ShippingAccountNo));this.setFieldValue("ctlNotes",$R_FN.setCleanTextValue(t.Notes));this.setFieldValue("ctlInstructions",$R_FN.setCleanTextValue(t.Instructions));this.setFieldValue("ctlRefNo",t.RefNumber);this.showField("ctlRefNo",t.RefNumber>0);this.setFieldValue("hidNo",t.CRMANumber);this.setFieldValue("hidCustomer",$R_FN.setCleanTextValue(t.Customer));this.setFieldValue("hidWarehouseNo",t.WarehouseNo);this.setFieldValue("hidCustomerNo",t.CustomerNo);this.setFieldValue("hidShipViaNo",t.ShipViaNo);this.setFieldValue("hidSalesOrder",t.SalesOrder);this.setFieldValue("hidSalesOrderNo",t.SalesOrderNo);this.setFieldValue("hidContact",$R_FN.setCleanTextValue(t.Contact));this.setFieldValue("hidContactNo",t.ContactNo);this.setFieldValue("hidInvoice",t.Invoice);this.setFieldValue("hidInvoiceNo",t.InvoiceNo);this.setFieldValue("hidDivisionNo",t.DivisionNo);this.setFieldValue("hidAuthorisedBy",t.AuthorisedBy);this.setFieldValue("hidIncotermNo",t.IncotermNo);this.setFieldValue("ctlCustomerRejectionNo",t.CustomerRejectionNo);this._IsHubAutoCRMA=t.IsHubAutoCRMA;this.setFieldValue("ctlAS6081",t.AS6081==1?"Yes":"No");$R_FN.highlightBackgroundColorOfText("ctl00_cphMain_ctlMainInfo_ctlDB_ctl13_ctlAS6081_lbl",t.AS6081);this.setDLUP(t.DLUP);this.getDataOK_End()},getDataError:function(n){this.showError(!0,n.get_ErrorMessage())},showEditForm:function(){this._frmEdit._intLineID=this._intLineID;this._frmEdit.setFieldValue("ctlCustomer",this.getFieldValue("hidCustomer"));this._frmEdit.setFieldValue("ctlContact",this.getFieldValue("hidContact"));this._frmEdit.setFieldValue("ctlDivision",this.getFieldValue("hidDivisionNo"));this._frmEdit.setFieldValue("ctlWarehouse",this.getFieldValue("hidWarehouseNo"));this._frmEdit.setFieldValue("ctlAuthorisedBy",this.getFieldValue("hidAuthorisedBy"));this._frmEdit.setFieldValue("ctlRMADate",this.getFieldValue("ctlRMADate"));this._frmEdit.setFieldValue("ctlInvoice",this.getFieldValue("hidInvoice"));this._frmEdit.setFieldValue("ctlSalesOrder",this.getFieldValue("hidSalesOrder"));this._frmEdit.setFieldValue("ctlShipVia",this.getFieldValue("hidShipViaNo"));this._frmEdit.setFieldValue("ctlShippingAccount",this.getFieldValue("ctlShippingAccountNo"));this._frmEdit.setFieldValue("ctlNotes",this.getFieldValue("ctlNotes"));this._frmEdit.setFieldValue("ctlInstructions",this.getFieldValue("ctlInstructions"));this._frmEdit.setFieldValue("ctlIncoterm",this.getFieldValue("hidIncotermNo"));this._frmEdit.showField("ctlRaisedByLbl",this._IsHubAutoCRMA);this._frmEdit.showField("ctlAuthorisedBy",!this._IsHubAutoCRMA);this._frmEdit._IsHubAutoCRMA=this._IsHubAutoCRMA;this._IsHubAutoCRMA&&(this._frmEdit._hidRaisedByNo=this.getFieldValue("hidAuthorisedBy"));this._frmEdit.setFieldValue("ctlAuthorisedBy",this.getFieldValue("hidAuthorisedBy"));this._frmEdit.setFieldValue("ctlRaisedByLbl",this.getFieldValue("ctlAuthoriser"));this._frmEdit.showField("ctlShipViaLbl",this._IsHubAutoCRMA);this._frmEdit.showField("ctlShipVia",!this._IsHubAutoCRMA);this._frmEdit.showField("ctlWarehouse_Label",this._IsHubAutoCRMA);this._frmEdit.showField("ctlWarehouse",!this._IsHubAutoCRMA);this._IsHubAutoCRMA&&(this._frmEdit._clientWarehouseNo=this.getFieldValue("hidWarehouseNo"));this._IsHubAutoCRMA&&(this._frmEdit._hidShipViaNo=this.getFieldValue("hidShipViaNo"));this._frmEdit.setFieldValue("ctlShipViaLbl",this.getFieldValue("ctlShipVia"));this._frmEdit.setFieldValue("ctlCustomerRejectionNo",this.getFieldValue("ctlCustomerRejectionNo"));this.showForm(this._frmEdit,!0)},hideEditForm:function(){this.showForm(this._frmEdit,!1)},cancelEdit:function(){this.hideEditForm()},editFormShown:function(){},saveEditComplete:function(){this.hideEditForm();this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully);this.getData()},showAddForm:function(){this._frmAdd._intCustomerRMAID=this._intCRMAID;this._frmAdd.setFieldValue("ctlExpediteNotes","");this.showForm(this._frmAdd,!0)},cancelAddForm:function(){this.showForm(this._frmAdd,!1);this.showContent(!0)},saveAddComplete:function(){this.showForm(this._frmAdd,!1);this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully);this.getData()},getExpHist:function(){this.showExpHistError(!1);this.showLoadingExpHist(!0);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/CRMAMainInfo");n.set_DataObject("CRMAMainInfo");n.set_DataAction("GetExpediteHistory");n.addParameter("id",this._intCRMAID);n.addDataOK(Function.createDelegate(this,this.getExpHistOK));n.addError(Function.createDelegate(this,this.getExpHistError));n.addTimeout(Function.createDelegate(this,this.getExpHistError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getExpHistOK:function(n){res=n._result;this.showLoadingExpHist(!1);this.showExpHistError(!1);this._tblExpHist.clearTable();this.processExpHistList(this._tblExpHist);this._tblExpHist.resizeColumns()},getExpHistError:function(n){this.showLoadingExpHist(!1);this.showExpHistError(!0,n.get_ErrorMessage())},showLoadingExpHist:function(n){$R_FN.showElement(this._pnlLoadingExpHist,n);$R_FN.showElement(this._pnlExpHist,!n);this.showLoading(n);n&&$R_FN.showElement(this._pnlGetExpHist,!1)},showExpHistError:function(n,t){$R_FN.showElement(this._pnlExpHistError,n);n&&(this.showLoading(!1),$R_FN.showElement(this._pnlExpHist,!1),$R_FN.showElement(this._pnlGetExpHist,!1),$R_FN.setInnerHTML(this._pnlExpHistError,t))},showExpHistGetData:function(n){$R_FN.showElement(this._pnlGetExpHist,n);n&&(this.showLoading(!1),$R_FN.showElement(this._pnlLoadingExpHist,!1),$R_FN.showElement(this._pnlExpHist,!1),$R_FN.setInnerHTML(this._pnlExpHistError,!1))},processExpHistList:function(n){var i,t,r;if(res.ExpHist)for(i=0;i<res.ExpHist.length;i++)t=res.ExpHist[i],r=[$R_FN.setCleanTextValue(t.Notes),$R_FN.writeDoubleCellValue(t.Date+" "+t.Time),t.EmployeeName],n.addRow(r,t.ID,!1),t=null,r=null},OpenDocTree:function(){$R_FN.openDocumentTree(this._intCRMAID,"CRMA",this.getFieldValue("hidNo"))}};Rebound.GlobalTrader.Site.Controls.Nuggets.CRMAMainInfo.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.CRMAMainInfo",Rebound.GlobalTrader.Site.Controls.Nuggets.Base);