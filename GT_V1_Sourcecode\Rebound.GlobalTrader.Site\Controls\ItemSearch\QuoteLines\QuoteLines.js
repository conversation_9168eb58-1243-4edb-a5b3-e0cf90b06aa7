Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.ItemSearch");Rebound.GlobalTrader.Site.Controls.ItemSearch.QuoteLines=function(n){Rebound.GlobalTrader.Site.Controls.ItemSearch.QuoteLines.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.ItemSearch.QuoteLines.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.ItemSearch.QuoteLines.callBaseMethod(this,"initialize");this.addSetupData(Function.createDelegate(this,this.doSetupData));this.addGetDataComplete(Function.createDelegate(this,this.doGetDataComplete))},dispose:function(){this.isDisposed||Rebound.GlobalTrader.Site.Controls.ItemSearch.QuoteLines.callBaseMethod(this,"dispose")},doSetupData:function(){this._objData.set_PathToData("controls/ItemSearch/QuoteLines");this._objData.set_DataObject("QuoteLines");this._objData.set_DataAction("GetData");this._objData.addParameter("Part",this.getFieldValue("ctlPartNo"));this._objData.addParameter("CM",this.getFieldValue("ctlCompany"));this._objData.addParameter("QuoteNoLo",this.getFieldValue_Min("ctlQuoteNo"));this._objData.addParameter("QuoteNoHi",this.getFieldValue_Max("ctlQuoteNo"));this._objData.addParameter("DateQuotedFrom",this.getFieldValue("ctlDateQuotedFrom"));this._objData.addParameter("DateQuotedTo",this.getFieldValue("ctlDateQuotedTo"));this._objData.addParameter("IncludeClosed",this.getFieldValue("ctlIncludeClosed"))},doGetDataComplete:function(){for(var n,i,t=0,r=this._objResult.Results.length;t<r;t++)n=this._objResult.Results[t],i=[n.No,$R_FN.setCleanTextValue(n.CMName),$R_FN.writePartNo(n.Part,n.ROHS),$R_FN.setCleanTextValue(n.Date),n.Quantity,n.Price],this._tblResults.addRow(i,n.ID,!1),i=null,n=null}};Rebound.GlobalTrader.Site.Controls.ItemSearch.QuoteLines.registerClass("Rebound.GlobalTrader.Site.Controls.ItemSearch.QuoteLines",Rebound.GlobalTrader.Site.Controls.ItemSearch.Base,Sys.IDisposable);