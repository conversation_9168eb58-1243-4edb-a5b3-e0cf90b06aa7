﻿SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
/*   
===========================================================================================  
TASK			UPDATED BY			DATE			ACTION		DESCRIPTION  
[US-211441]		Trung Pham Vanw		24-Sep-2024		CREATE		Get Prospective Offer Status By Id
===========================================================================================  
*/
CREATE OR ALTER PROCEDURE usp_Get_ProspectiveOffer_Status
	@ProId INT
AS
BEGIN
	SET NOCOUNT ON;

	SELECT pro.ProspectiveOfferId
		,pro.SupplierNo AS SupplierId
		,c.CompanyName AS SupplierName
		,pro.SourceFileName
		,pro.ImportRowCount
		,pro.ImportStatus
		,l.EmployeeName AS ImportedBy
		,pro.DLUP AS ImportDate
    FROM tbProspectiveOffers pro
	JOIN tbLogin l ON l.LoginId = pro.CreatedBy
	LEFT JOIN tbCompany c ON c.CompanyId = pro.SupplierNo
	WHERE pro.ProspectiveOfferId = @ProId
END
GO
