using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.ItemSearch {
	public partial class SalesOrderLinesClose : Base {

		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
			SetItemSearchType("SalesOrderLines");
			AddScriptReference("Controls.ItemSearch.SalesOrderLinesClose.SalesOrderLinesClose.js");
		}

		protected override void OnLoad(EventArgs e) {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.ItemSearch.SalesOrderLinesClose", ctlDesignBase.ClientID);
			base.OnLoad(e);
		}

		protected override void OnPreRender(EventArgs e) {
			ctlDesignBase.MakeChildControls();
			ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("SalesOrder", WidthManager.GetWidth(WidthManager.ColumnWidth.CompanyName), true));
			ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("Company", Unit.Empty, true));
			ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("PartNo", WidthManager.GetWidth(WidthManager.ColumnWidth.CompanyName), true));
			ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("DateOrdered", WidthManager.GetWidth(WidthManager.ColumnWidth.Date), true));
			ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("Price", WidthManager.GetWidth(WidthManager.ColumnWidth.CurrencyValue), true));
			ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("Quantity", WidthManager.GetWidth(WidthManager.ColumnWidth.Quantity), true));
			ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("Salesman", WidthManager.GetWidth(WidthManager.ColumnWidth.ContactName), true));
			ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("CustomerPO", WidthManager.GetWidth(WidthManager.ColumnWidth.SystemDocumentNumber), true));
			ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("Cost", WidthManager.GetWidth(WidthManager.ColumnWidth.CurrencyValue), true));
			base.OnPreRender(e);
		}
	}
}