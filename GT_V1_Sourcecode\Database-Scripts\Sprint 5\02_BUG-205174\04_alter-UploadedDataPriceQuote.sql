﻿/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-205174]		An.TranTan			28-Jun-2024		Update			Drop revelant SP and Add 1 more column
===========================================================================================
*/
IF OBJECT_ID('dbo.usp_saveExcelBulkSave_PriceQuote', 'P') IS NOT NULL
	DROP PROCEDURE dbo.usp_saveExcelBulkSave_PriceQuote
GO
IF TYPE_ID(N'UploadedDataPriceQuote') IS NOT NULL
	DROP TYPE [dbo].[UploadedDataPriceQuote]
GO

CREATE TYPE [dbo].[UploadedDataPriceQuote] AS TABLE(
	[Column1] [nvarchar](max) NULL DEFAULT (NULL),
	[Column2] [nvarchar](max) NULL DEFAULT (NULL),
	[Column3] [nvarchar](max) NULL DEFAULT (NULL),
	[Column4] [nvarchar](max) NULL DEFAULT (NULL),
	[Column5] [nvarchar](max) NULL DEFAULT (NULL),
	[Column6] [nvarchar](max) NULL DEFAULT (NULL),
	[Column7] [nvarchar](max) NULL DEFAULT (NULL),
	[Column8] [nvarchar](max) NULL DEFAULT (NULL),
	[Column9] [nvarchar](max) NULL DEFAULT (NULL),
	[Column10] [nvarchar](max) NULL DEFAULT (NULL),
	[Column11] [nvarchar](max) NULL DEFAULT (NULL),
	[Column12] [nvarchar](max) NULL DEFAULT (NULL),
	[Column13] [nvarchar](max) NULL DEFAULT (NULL),
	[Column14] [nvarchar](max) NULL DEFAULT (NULL),
	[Column15] [nvarchar](max) NULL DEFAULT (NULL),
	[Column16] [nvarchar](max) NULL DEFAULT (NULL),
	[Column17] [nvarchar](max) NULL DEFAULT (NULL),
	[Column18] [nvarchar](max) NULL DEFAULT (NULL),
	[Column19] [nvarchar](max) NULL DEFAULT (NULL),
	[Column20] [nvarchar](max) NULL DEFAULT (NULL),
	[Column21] [nvarchar](max) NULL DEFAULT (NULL),
	[Column22] [nvarchar](max) NULL DEFAULT (NULL),
	[Column23] [nvarchar](max) NULL DEFAULT (NULL),
	[Column24] [nvarchar](max) NULL DEFAULT (NULL),
	[Column25] [nvarchar](max) NULL DEFAULT (NULL)
)
GO

