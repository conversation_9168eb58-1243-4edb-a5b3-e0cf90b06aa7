Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");Rebound.GlobalTrader.Site.Controls.Nuggets.ContactGroup=function(n){Rebound.GlobalTrader.Site.Controls.Nuggets.ContactGroup.initializeBase(this,[n]);this._strContactType="";this._arrPOLineIds=[]};Rebound.GlobalTrader.Site.Controls.Nuggets.ContactGroup.prototype={get_strContactType:function(){return this._strContactType},set_strContactType:function(n){this._strContactType!==n&&(this._strContactType=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Nuggets.ContactGroup.callBaseMethod(this,"initialize");this.showRefresh(!1);this.showLoading(!1);this.showContentLoading(!1);this._frmAdd=$find(this._aryFormIDs[0])},showAddForm:function(){this._frmAdd._strContactType=this._strContactType;_globalContactType=this._strContactType;this.showForm(this._frmAdd,!0)},cancelClicked:function(){window.location.href="Con_ManufacturerBrowse.aspx"},dispose:function(){this.isDisposed||(this._tbl&&this._tbl.dispose(),this._tbl=null,this._strContactType=null,Rebound.GlobalTrader.Site.Controls.Nuggets.ContactGroup.callBaseMethod(this,"dispose"))},getData:function(){},getDataOK:function(){},writeCheckbox:function(){},getControlID:function(n,t,i){return String.format("{0}_{1}{2}",i._element.id,n,t)},getCheckBox:function(n,t){return $find(this.getControlID("chk",n,t))},getCheckedCellValue:function(){},getDataError:function(n){this.showError(!0,n.get_ErrorMessage())},saveEdit:function(){},cancelEdit:function(){this.hideEditForm();this.showContent(!0)},showEditForm:function(){},hideEditForm:function(){this.showForm(this._frmEdit,!1)},saveEditComplete:function(){},saveEditError:function(){this.showError(!0,this._frmEdit._strErrorMessage)}};Rebound.GlobalTrader.Site.Controls.Nuggets.ContactGroup.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.ContactGroup",Rebound.GlobalTrader.Site.Controls.Nuggets.Base);