﻿/* left nuggets */
/********************************************************/
.leftNuggetIconOn_Generic {
	background-image: url(images/leftnuggets/icons/generic.jpg);
}
.leftNuggetIconOff_Generic {
	background-image: url(images/leftnuggets/icons/generic_off.jpg);
}
.leftNuggetIconOn_ViewOptions {
	background-image: url(images/leftnuggets/icons/options.jpg);
}
.leftNuggetIconOff_ViewOptions {
	background-image: url(images/leftnuggets/icons/options_off.jpg);
}
.leftNuggetIconOn_Search {
	background-image: url(images/leftnuggets/icons/search.jpg);
}
.leftNuggetIconOff_Search {
	background-image: url(images/leftnuggets/icons/search_off.jpg);
}
.leftNuggetIconOn_Selection {
	background-image: url(images/leftnuggets/icons/selection.jpg);
}
.leftNuggetIconOff_Selection {
	background-image: url(images/leftnuggets/icons/selection_off.jpg);
}
.leftNuggetIconOn_RecentlyViewed {
	background-image: url(images/leftnuggets/icons/recently.jpg);
}
.leftNuggetIconOff_RecentlyViewed {
	background-image: url(images/leftnuggets/icons/recently_off.jpg);
}
.leftNuggetIconOn_List {
	background-image: url(images/leftnuggets/icons/list.jpg);
}
.leftNuggetIconOff_List {
	background-image: url(images/leftnuggets/icons/list_off.jpg);
}
.leftNuggetIconOn_QuickJump {
	background-image: url(images/leftnuggets/icons/generic.jpg);
}
.leftNuggetIconOff_QuickJump {
	background-image: url(images/leftnuggets/icons/generic_off.jpg);
}
.leftNuggetIconOn_SourcingLinks {
	background-image: url(images/leftnuggets/icons/links.jpg);
}
.leftNuggetIconOff_SourcingLinks {
	background-image: url(images/leftnuggets/icons/links_off.jpg);
}

/* homeNugget */
/*********************************************************************/
.homepageNugget, .homepageNugget a, .homepageNugget .contentItem, .homepageNugget .contentItem a {
	font-size: 10px;
}
.homepageNugget .overdue {
	background-image: url(images/nuggets/overdue.gif);
	background-repeat: repeat-x;
	background-position: bottom;
	background-color: #ff6464;
	padding: 3px 3px 0px;
	color: #000000;
}
.homepageNugget .overdue .nubButton {
	background-image: url(images/nubs/overdue.gif);
	color: #000000;
}
.homepageNugget .overdue .nubButton:hover, .homepageNugget .overdue table.dataTable tr:hover .nubButton {
	color: #000000 !important;
}
.homepageNugget .overdue table.dataTable tr:hover td {
	background-color:transparent !important;
	background-image:url(images/overlays/white25.png);
}


/* recently viewed left nugget */
/*********************************************************************/
.recentlyViewedLockOff, .recentlyViewedLockOn {
	background-repeat: no-repeat;
	background-position: center right;
	padding-right: 20px;
	cursor: pointer;
	position: absolute;
	right: 2px;
	top: 0px;
}
.recentlyViewedLockOn {
	background-image: url(images/leftnuggets/recentlyviewed/pin_on.png);
	background-position: right 1px;
	height: 16px;
	right: -2px;
}
.recentlyViewedLockOff {
	background-image: url(images/leftnuggets/recentlyviewed/pin_off.png);
}
.leftNuggetContent ul.recentlyViewed li {
	padding-right: 15px;
	position: relative;
	width: 175px;
}