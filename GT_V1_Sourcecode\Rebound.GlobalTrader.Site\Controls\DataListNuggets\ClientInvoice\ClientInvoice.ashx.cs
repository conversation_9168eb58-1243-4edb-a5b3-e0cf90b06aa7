//Marker     Changed by      Date               Remarks
//[005]      Prakash           11/04/2014         Add Client Invoice
using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site.Enumerations;

namespace Rebound.GlobalTrader.Site.Controls.DataListNuggets.Data {

    public class ClientInvoice : Base
    {

		private string _strCallType;

		protected override void GetData() {

			_strCallType = "ALL";
            if (GetFormValue_Boolean("CanNotBeExported")) _strCallType = "CANNOTBEEXPORTED";

           SupplierInvoiceStatus enmStatus = (SupplierInvoiceStatus)GetFormValue_Int("Status");

            List<BLL.ClientInvoice> lst = BLL.ClientInvoice.DataListNugget(
				SessionManager.ClientID
				, GetFormValue_NullableInt("SortIndex")
				, GetFormValue_NullableInt("SortDir", SortColumnDirection.ASC)
				, GetFormValue_NullableInt("PageIndex", 0)
				, GetFormValue_NullableInt("PageSize", 10)
                , GetFormValue_NullableInt("CINoLo")
                , GetFormValue_NullableInt("CINoHi")
                , GetFormValue_NullableInt("URNNoLo")
                , GetFormValue_NullableInt("URNNoHi")
                , GetFormValue_NullableInt("PONoLo")
                , GetFormValue_NullableInt("PONoHi")
			    , GetFormValue_NullableInt("GINoLo")
				, GetFormValue_NullableInt("GINoHi")
                , GetFormValue_NullableDateTime("ClientInvoiceDateFrom")
                , GetFormValue_NullableDateTime("ClientInvoiceDateTo")
                //, GetFormValue_StringForNameSearch("CMName")
                , GetFormValue_StringForNameSearchDecode("CMName")
				, GetFormValue_Boolean("RecentOnly")
                , (enmStatus == SupplierInvoiceStatus.ExportedOnly && _strCallType == "ALL") ? (bool?)true : null
                , (enmStatus == SupplierInvoiceStatus.ApproveandUnExported && _strCallType == "ALL") ? (bool?)true : null
                , GetFormValue_NullableBoolean("CanNotBeExported", null)
                , GetFormValue_NullableInt("Client", null)
                , SessionManager.IsPOHub
			);
			JsonObject jsn = new JsonObject();
			jsn.AddVariable("TotalRecords", (lst.Count > 0) ? lst[0].RowCnt : 0);
			JsonObject jsnRowsArray = new JsonObject(true);
			for (int i = 0; i < lst.Count; i++) {
				if (i < lst.Count) {
					JsonObject jsnRow = new JsonObject();
                    jsnRow.AddVariable("ID", lst[i].ClientInvoiceID);
                    jsnRow.AddVariable("No", lst[i].ClientInvoiceNumber);
                    jsnRow.AddVariable("Name", lst[i].Clientname);
                    jsnRow.AddVariable("CompanyNo", lst[i].CompanyNo);
					jsnRow.AddVariable("GINo", lst[i].GoodsInNumber);
                    jsnRow.AddVariable("GI", lst[i].GoodsInNo);
                    jsnRow.AddVariable("PO", lst[i].PurchaseOrderNumber);
                    jsnRow.AddVariable("PONo", lst[i].PurchaseOrderNo);
                    jsnRow.AddVariable("INVDate", Functions.FormatDate(lst[i].ClientInvoiceDate));
					jsnRow.AddVariable("Part", lst[i].Part);
					jsnRow.AddVariable("Value",Functions.FormatCurrency(lst[i].InvoiceAmount,lst[i].CurrencyCode, 5));
                    jsnRow.AddVariable("URNNumber", (lst[i].URNNumber.HasValue) ? Convert.ToString(lst[i].URNNumber.Value) : "");
                    jsnRow.AddVariable("IPO", lst[i].InternalPurchaseOrderNumber);
                    jsnRow.AddVariable("IPONo", lst[i].InternalPurchaseOrderNo);
					jsnRowsArray.AddVariable(jsnRow);
					jsnRow.Dispose();
					jsnRow = null;
				}
			}
			jsn.AddVariable("Results", jsnRowsArray);
			OutputResult(jsn);
			jsnRowsArray.Dispose();
			jsnRowsArray = null;
			jsn.Dispose();
			jsn = null;
			lst = null;
			base.GetData();
		}

        protected override void AddFilterStates()
        {
            AddExplicitFilterState("CallType", _strCallType);
            AddFilterState("CINo");
            AddFilterState("URNNo");
            AddFilterState("PONo");
            AddFilterState("GINo");
            AddFilterState("ClientInvoiceDateFrom");
            AddFilterState("ClientInvoiceDateTo");
            AddFilterState("CMName");
            AddFilterState("RecentOnly");
            AddFilterState("Status");
            AddFilterState("Client");            
            base.AddFilterStates();
        }

	}
}
