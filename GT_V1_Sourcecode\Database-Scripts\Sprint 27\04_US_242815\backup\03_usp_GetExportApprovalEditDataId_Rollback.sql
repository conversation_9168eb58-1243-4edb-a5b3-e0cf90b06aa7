﻿CREATE OR ALTER PROCEDURE usp_GetExportApprovalEditDataId        
@ExportApprovalId  INT        
AS        
/*        
 *[001]  Created  <PERSON><PERSON><PERSON><PERSON>  18-04-2023  Add new procedure to get the data of export approval Edit Screen.           
 */        
BEGIN        
SET NOCOUNT ON        
DECLARE @ExportPowerUrl NVARCHAR(MAX)='';    
DECLARE @EUUFormUploadName NVARCHAR(MAX)=NULL;    
  
CREATE TABLE #tempEditExportApproval        
(        
ExportApprovalId   INT,     
ExportApprovalDetailsId  INT,     
SalesPersonName    NVARCHAR(MAX),        
SalesOrderNumber   INT,        
LineNumber     INT,        
CustomerName    NVARCHAR(MAX),        
PART      NVARCHAR(MAX),        
DestinationCountryId  INT,      
MilitaryUseId    INT,      
EndUserText        NVARCHAR(MAX),  
ExportApprovalStatusId  INT          
)        
        
INSERT INTO #tempEditExportApproval        
SELECT          
ISNULL(easg.ExportApprovalId,0),   
ISNULL(ead.ExportApprovalDetailsId,0),       
lgnsp.EmployeeName,        
so.SalesOrderNumber,        
sol.SOSerialNo,        
co.CompanyName,        
sol.FullPart,  
ISNULL(ead.EndDestinationCountryNo,0),  
ISNULL(ead.MilitaryUseNo,0),  
ISNULL(ead.ENDUserText,''),  
easg.ApprovalStatusId     
FROM  tbSO_ExportApprovalStatusOGEL easg  
LEFT OUTER JOIN  tbSO_ExportApprovalDetails ead   
ON ead.ExportApprovalNo=easg.ExportApprovalId     
LEFT OUTER JOIN tbSalesOrder so ON easg.SalesOrderNo=so.SalesOrderId        
LEFT OUTER JOIN tbLogin lgnsp ON so.Salesman=lgnsp.LoginId        
LEFT OUTER JOIN tbSalesOrderLine sol ON easg.SalesOrderLineNo=sol.SalesOrderLineId        
LEFT OUTER JOIN tbCompany co ON so.CompanyNo=co.CompanyId            
WHERE easg.ExportApprovalId=@ExportApprovalId        
        
SELECT        
 *        
  From #tempEditExportApproval        
        
DROP TABLE #tempEditExportApproval        
SET NOCOUNT OFF        
END  