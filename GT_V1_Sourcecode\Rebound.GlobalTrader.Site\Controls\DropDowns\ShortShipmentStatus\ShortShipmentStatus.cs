﻿//Marker     Changed by      Date         Remarks
//[001]      Vinay           21/11/2012   Please make Rosh as a compulsory field on the following:- Requirements,Quotes,PO,SO
using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;

namespace Rebound.GlobalTrader.Site.Controls.DropDowns {
	public partial class ShortShipmentStatus : Base {
		
		protected override void OnInit(EventArgs e) {
			CanAddTo = false;
            //[001] code start
			//IncludeNoValue = false;
			//NoValue_Value = "0";
			//InitialValue = "0";
            //[001] code end
			base.OnInit(e);
		}

		protected override void OnLoad(EventArgs e) {
            SetDropDownType("ShortShipmentStatus");
            AddScriptReference("Controls.DropDowns.ShortShipmentStatus.ShortShipmentStatus");
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.DropDowns.ShortShipmentStatus", ClientID);
			base.OnLoad(e);
		}

	}
}