<%--
Marker     changed by      date         Remarks
[001]      Umendra         18/01/2019    Adding View Tree Button
[RP-2339]	Ravi		   11-10-2023		AS6081 GT Documents - Show AS6081 on detail screens
--%>
<%@ Control Language="C#" CodeBehind="CRMAMainInfo.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Nuggets.CRMAMainInfo" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" TitleText="Main Information" BoxType="Standard">
    <Links>
        <ReboundUI:IconButton ID="ibtnEdit" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="Edit" IconCSSType="Edit" />
        <ReboundUI:IconButton ID="ibtnAdd" runat="server" IconGroup="Nugget" IconTitleResource="InternalLog" IconCSSType="Add" IconButtonMode="HyperLink" Alignment="Left" />
        <%--[001] code Start--%>
        <ReboundUI:IconButton ID="ibtnViewTree" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="ViewTree" IconCSSType="Add" />
    	<%--[001] code End--%>
    </Links>
    <Content>
        <table class="twoCols">
            <tr>
                <td class="col1">
                    <table cellpadding="0" cellspacing="0" border="0" class="dataItems">
                        <ReboundUI:DataItemRow ID="ctlCustomerName" runat="server" ResourceTitle="Customer" />
                        <ReboundUI:DataItemRow ID="ctlContact" runat="server" ResourceTitle="Contact" />
                        <ReboundUI:DataItemRow ID="ctlDivision" runat="server" ResourceTitle="Division" />
                        <ReboundUI:DataItemRow ID="ctlWarehouse" runat="server" ResourceTitle="Warehouse" />
                        <ReboundUI:DataItemRow ID="ctlAuthoriser" runat="server" ResourceTitle="AuthorisedBy" />
                        <ReboundUI:DataItemRow ID="ctlRMADate" runat="server" ResourceTitle="RMADate" />
                        <ReboundUI:DataItemRow ID="ctlInvoice" runat="server" ResourceTitle="InvoiceNo" />
                        <ReboundUI:DataItemRow ID="ctlSalesOrder" runat="server" ResourceTitle="SalesOrderNo" />
                        <ReboundUI:DataItemRow ID="ctlAS6081" runat="server" ResourceTitle="AS6081MainInfoLabel" /> <%--[RP-2339]--%>

                        <ReboundUI:DataItemRow ID="hidNo" runat="server" FieldType="Hidden" />
                        <ReboundUI:DataItemRow ID="hidAuthorisedBy" runat="server" FieldType="Hidden" />
                        <ReboundUI:DataItemRow ID="hidInvoice" runat="server" FieldType="Hidden" />
                        <ReboundUI:DataItemRow ID="hidInvoiceNo" runat="server" FieldType="Hidden" />
                        <ReboundUI:DataItemRow ID="hidDivisionNo" runat="server" FieldType="Hidden" />
                        <ReboundUI:DataItemRow ID="hidCustomer" runat="server" FieldType="Hidden" />
                        <ReboundUI:DataItemRow ID="hidCustomerNo" runat="server" FieldType="Hidden" />
                        <ReboundUI:DataItemRow ID="hidContactNo" runat="server" FieldType="Hidden" />
                        <ReboundUI:DataItemRow ID="hidContact" runat="server" FieldType="Hidden" />
                        <ReboundUI:DataItemRow ID="hidSalesOrderNo" runat="server" FieldType="Hidden" />
                        <ReboundUI:DataItemRow ID="hidSalesOrder" runat="server" FieldType="Hidden" />
                        <ReboundUI:DataItemRow ID="hidShipViaNo" runat="server" FieldType="Hidden" />
                        <ReboundUI:DataItemRow ID="hidWarehouseNo" runat="server" FieldType="Hidden" />
                        <ReboundUI:DataItemRow ID="hidIncotermNo" runat="server" FieldType="Hidden" />
                    </table>
                </td>
                <td class="col2">
                    <table cellpadding="0" cellspacing="0" border="0" class="dataItems">
                        <ReboundUI:DataItemRow ID="ctlShipVia" runat="server" ResourceTitle="ShipVia" />
                        <ReboundUI:DataItemRow ID="ctlIncoterm" runat="server" ResourceTitle="Incoterm" />
                        <ReboundUI:DataItemRow ID="ctlShippingAccountNo" runat="server" ResourceTitle="ShippingAccountNo" />
                        <ReboundUI:DataItemRow ID="ctlSep1" runat="server" FieldType="SeparatorWithLine" />
                        <ReboundUI:DataItemRow ID="ctlInstructions" runat="server" ResourceTitle="Instructions" />
                        <ReboundUI:DataItemRow ID="ctlNotes" runat="server" ResourceTitle="CustomerNotes" />
                        <ReboundUI:DataItemRow ID="ctlRefNo" runat="server" ResourceTitle="RefNo" />
                        <ReboundUI:DataItemRow ID="ctlCustomerRejectionNo" runat="server" ResourceTitle="CustomerRejectionNo" />


                    </table>
                    <table cellpadding="0" cellspacing="0" border="0" class="dataItems">
                        <div class="dataItem_Title dataItem_TitleUnderneathList">
                            <%=Functions.GetGlobalResource("FormFields", "InternalLog")%>
                        </div>
                        <asp:Panel ID="pnlLoadingExpHist" runat="server" CssClass="subInfoLoading invisible"><%=Functions.GetGlobalResource("Misc", "Loading")%></asp:Panel>
                        <asp:Panel ID="pnlExpHistError" runat="server" CssClass="error invisible" />
                        <asp:Panel ID="pnlExpHist" runat="server" CssClass="invisible">
                            <ReboundUI:FlexiDataTable ID="tblExpHist" runat="server" PanelHeight="70" />
                        </asp:Panel>
                    </table>
                </td>
            </tr>
        </table>
    </Content>

    <Forms>
        <reboundform:crmamaininfo_edit id="ctlCRMAEdit" runat="server" />
        <reboundform:crmamaininfo_addexpedite id="ctlExpedite" runat="server" />
    </Forms>

</ReboundUI_Nugget:DesignBase>
