﻿//Marker     Changed by         Date         Remarks
//[001]      <PERSON><PERSON><PERSON><PERSON>     10/09/2021    Added new class for precogs Supplier.
using System;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using System.Web.Caching;
using System.Data.Common;

namespace Rebound.GlobalTrader.DAL.SqlClient
{
    public class SqlPrecogsSupplierProvider : PrecogsSupplierProvider
    {
        /// <summary>
        /// DropDown 
        /// Calls [usp_dropdown_ROHSStatus]
        /// </summary>
        public override List<PurchaseMethodDetails> DropDown()
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_dropdown_PrecogsSupplier", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<PurchaseMethodDetails> lst = new List<PurchaseMethodDetails>();
                while (reader.Read())
                {
                    PurchaseMethodDetails obj = new PurchaseMethodDetails();
                    obj.PurchaseMethodId = GetReaderValue_Int32(reader, "PrecogsSupplierId", 0);
                    obj.Name = GetReaderValue_String(reader, "Name", "");
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get RohsStatuss", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
    }
}
