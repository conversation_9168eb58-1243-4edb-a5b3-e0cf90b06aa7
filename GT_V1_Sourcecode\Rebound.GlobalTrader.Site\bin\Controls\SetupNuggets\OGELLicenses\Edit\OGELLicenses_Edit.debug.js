///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//Marker     changed by      date         Remarks
//[001]      NgaiTo          10/04/2024   Add/edit OGELLicenses in setup screen
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.OGELLicenses_Edit = function(element) {
    Rebound.GlobalTrader.Site.Controls.Forms.OGELLicenses_Edit.initializeBase(this, [element]);
    this._intItemID = 0;
};

Rebound.GlobalTrader.Site.Controls.Forms.OGELLicenses_Edit.prototype = {
    initialize: function() {
        Rebound.GlobalTrader.Site.Controls.Forms.OGELLicenses_Edit.callBaseMethod(this, "initialize");
        this.addShown(Function.createDelegate(this, this.formShown));
    },

    dispose: function() {
        if (this.isDisposed) return;
        this._intItemID = null;
        Rebound.GlobalTrader.Site.Controls.Forms.OGELLicenses_Edit.callBaseMethod(this, "dispose");
    },

    formShown: function() {
        if (this._blnFirstTimeShown) {
            this.addSave(Function.createDelegate(this, this.saveClicked));
            this._strPathToData = "controls/SetupNuggets/OGELLicenses";
            this._strDataObject = "OGELLicenses";
        }
    },

    clearNewItemValues: function() {
        this.setFormFieldsToDefaults();
    },

    saveClicked: function() {
        this.resetFormFields();
        if (this.validateForm()) this.saveEdit();
    },

    validateForm: function() {
        var blnOK = this.autoValidateFields();
        return blnOK;
    },

    saveEdit: function() {
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData(this._strPathToData);
        obj.set_DataObject(this._strDataObject);
        obj.set_DataAction("SaveEdit");
        obj.addParameter("ID", this._intItemID);
        obj.addParameter("OgelNumber", this.getFieldValue("ctlOgelNumber"));
        obj.addParameter("Description", this.getFieldValue("ctlDescription"));
        obj.addParameter("InActive", this.getFieldValue("ctlInActive"));
        obj.addDataOK(Function.createDelegate(this, this.saveEditOK));
        obj.addError(Function.createDelegate(this, this.saveEditError));
        obj.addTimeout(Function.createDelegate(this, this.saveEditError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    saveEditError: function(args) {
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    saveEditOK: function (args) {
        if (args._result.Result == -1) {
            this.showError(true, "'OGEL Number' is already exists.");
            return
        } else {
            this.onSaveComplete();
        }
    }

};

Rebound.GlobalTrader.Site.Controls.Forms.OGELLicenses_Edit.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.OGELLicenses_Edit", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
