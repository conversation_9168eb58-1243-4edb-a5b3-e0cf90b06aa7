using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.Site.Controls.DropDowns.Data {
	[WebService(Namespace = "http://tempuri.org/")]
	[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class ClientByMaster : Rebound.GlobalTrader.Site.Data.DropDowns.Base
    {

		public override void ProcessRequest(HttpContext context) {
            SetDropDownType("ClientByMaster");
			base.ProcessRequest(context);
		}

		protected override void GetData() {

            Int32? intMastLoginNo = GetFormValue_NullableInt("MasterLoginNo");
           string strOptions = CacheManager.SerializeOptions(new object[] { intMastLoginNo??0 });
           string strCachedData = "";// CacheManager.GetDropDownData(_objDropDown.ID, strOptions);

			if (string.IsNullOrEmpty(strCachedData)) {
				JsonObject jsn = new JsonObject();
				JsonObject jsnList = new JsonObject(true);
                List<BLL.Client> lst = BLL.Client.GeClientByMaster(intMastLoginNo);
				for (int i = 0; i < lst.Count; i++) {
					JsonObject jsnItem = new JsonObject();
					jsnItem.AddVariable("ID", lst[i].ClientId);
					jsnItem.AddVariable("Name", lst[i].ClientName);
                    jsnItem.AddVariable("Code", lst[i].ClientCode);
					jsnList.AddVariable(jsnItem);
					jsnItem.Dispose(); jsnItem = null;
				}
				lst.Clear(); lst = null;
				jsn.AddVariable("Clients", jsnList);
				jsnList.Dispose(); jsnList = null;
				CacheManager.StoreDropDown(_objDropDown.ID, strOptions, jsn.Result, CacheManager.CacheExpiryType.OneHour);
				OutputResult(jsn);
				jsn.Dispose(); jsn = null;
			} else {
				_context.Response.Write(strCachedData);
			}
			strCachedData = null;
		}
	}
}
