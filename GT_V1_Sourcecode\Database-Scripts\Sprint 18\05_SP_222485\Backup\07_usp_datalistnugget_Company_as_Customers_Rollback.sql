﻿
GO

/****** Object:  StoredProcedure [dbo].[usp_datalistnugget_Company_as_Customers]    Script Date: 12/26/2024 9:44:38 AM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].[usp_datalistnugget_Company_as_Customers]                                    
--********************************************************************************************                                                           
--* SK 08.06.2009:                                    
--* - include SO Terms                                    
--*                                    
--* RP 02.06.2009:                                    
--* - added OnStop                                    
--*                                    
--* RP 20.05.2009:                                    
--* - copied and upgraded from old [usp_list_Company_as_Customers_by_Client_with_filter]                  
--*[002] Updated by <PERSON><PERSON><PERSON> for RP-2422 for making city,state and county search accent insensitive  
--*[003] Updated by <PERSON><PERSON> for RP-3003
--TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
--[US-201556]		Trung Pham			31-May-2024		UPDATE			Add GroupCodeName Filter Functionality for Premier Accounts
/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-208529]		Trung Pham			08-Aug-2024		UPDATE			Add company type into filter/ Add ClientId = -1 to get All Company
===========================================================================================
*/
--********************************************************************************************                                    
    @ClientId int                                    
  , @TeamId int = NULL                                    
  , @DivisionId int = NULL                                    
  , @LoginId int = NULL                                    
  , @OrderBy int = 1                                    
  , @SortDir int = 1                                    
  , @PageIndex int = 0                                    
  , @PageSize int = 10                                    
  , @NameSearch nvarchar(50) = NULL                                    
  , @TypeSearch nvarchar(50) = NULL                                    
  , @CitySearch nvarchar(50) = NULL                                    
  , @CountrySearch nvarchar(50) = NULL                                    
  , @SalesmanSearch int = NULL                                    
  , @CustomerRatingLo int = NULL                                    
  , @CustomerRatingHi int = NULL                                    
  , @SupplierRatingLo int = NULL                                    
  , @SupplierRatingHi int = NULL                                    
  , @CustomerCode nvarchar(10) = NULL                                    
  , @TelNo nvarchar(30) = NULL                                   
  , @ZIP nvarchar(20) = NULL                                 
  , @IsGlobalLogin BIT = 0                              
  , @StateSearch nvarchar(70) = NULL                                    
  , @CountySearch nvarchar(70) = NULL                             
  , @region int = null                            
  , @emailSearch nvarchar(50) = NULL                          
  , @CountryNo int = null                           
  , @RelatedMFR nvarchar(100)=NULL                
  , @IndustryType nvarchar(100)=NULL
  , @GroupCode nvarchar(100)=NULL
  , @CompanyTypeId INT = NULL
  , @IsGSA BIT =0                
  , @CurrentClient INT=0              
  , @SelectedLogin INT=0           
  , @InsuranceCertificateNo nvarchar(50) = NULL      
  , @CertificateCategoryNo int = null      
    WITH RECOMPILE                                    
AS                                     
    DECLARE @StartPage int                                    
      , @EndPage int                                    
    SET @StartPage = (@PageIndex * @PageSize + 1)                                    
    SET @EndPage = ((@PageIndex + 1) * @PageSize)                                    
                                    
-- semi-colon needed for WITH 
IF((@InsuranceCertificateNo IS NOT NULL) OR (@CertificateCategoryNo  IS NOT NULL))
BEGIN
;   WITH    cteSearch                                    
              AS (SELECT    co.CompanyId                                    
                          , co.CompanyName                                    
                          , co.Telephone                                    
                          , ct.[Name] AS CompanyType                                    
                          , ad.City     
                 , cy.CountryName AS Country                                    
                          , lg.EmployeeName AS SalesmanName                                    
                    -- , dbo.ufn_days_since_last_contact_for_company(co.CompanyId) AS DaysSinceContact  
					      , DATEDIFF(DAY, (SELECT MAX(DLUP) FROM dbo.tbCommunicationLog WITH (NOLOCK) WHERE CompanyNo = co.CompanyId), GETDATE()) AS DaysSinceContact --  [003] Code 
                          , co.OnStop                                    
          , tm.TermsName                       
                          , co.CustomerCode                                    
                          , co.SupplierCode                       
                          , ad.ZIP                                 
                          , cl.ClientName  as ClientName                       
        ,co.EMail    
  ,CIC.CertificateNumber   
  ,crtc.CertificateCategoryName   
  ,(select count(*) from tbToDo where CompanyNo=co.companyid) as TaskCount                       
 , (select dbo.[ufn_cout_CRMProspectsComplete] (co.companyid)) AS CRMCompleteCount                      
                -- 02 April, Need to get below column according to salesman                                
       , (SELECT top 1  [GrossProfit]  FROM [TBGPSummary] where CompanyNo=co.CompanyId                              
         )  as SalesGrossProfit                              
        , (SELECT top 1 TotalSales   FROM [TBGPSummary] where CompanyNo=co.CompanyId                               
        )  as SalesTurnover                                   
      --                    , ROW_NUMBER() OVER (ORDER BY --                                    
      --                                         case WHEN @OrderBy = 1                                    
      --                                                   AND @SortDir = 2 THEN co.CompanyName                                    
      --                                         END DESC                                     
      --                       , case WHEN @OrderBy = 1 THEN co.CompanyName                                    
      --                                         END                                      
      --                                       , case WHEN @OrderBy = 2                                    
      --                                                   AND @SortDir = 2 THEN ct.Name                                    
      --END DESC                                            , case WHEN @OrderBy = 2                                    
      --                                                   AND @SortDir = 2 THEN ct.Name                                    
      --                                         END                                      
      --                                       , case WHEN @OrderBy = 3                                    
      --       AND @SortDir = 2 THEN ad.City                                    
      --                                         END DESC                                    
      --                                , case WHEN @OrderBy = 3                                    
      --                                                   AND @SortDir = 2 THEN ad.City                                    
      --               END DESC                                    
      --                                       , case WHEN @OrderBy = 5                                    
      --                                                   AND @SortDir = 2 THEN lg.EmployeeName                                    
      --                                         END DESC                                    
 --                                       , case WHEN @OrderBy = 5 THEN lg.EmployeeName                                    
      --                                         END                                    
      --                                       , case WHEN @OrderBy = 6                                    
      --                               AND @SortDir = 2 THEN dbo.ufn_days_since_last_contact_for_company(co.CompanyId)                                    
      --                                         END DESC                  
      --                         , case WHEN @OrderBy = 6 THEN dbo.ufn_days_since_last_contact_for_company(co.CompanyId)                                    
      --                                         END) AS RowNum              
    , ROW_NUMBER() OVER (ORDER BY --                                    
                                               case WHEN @OrderBy = 1                                    
          AND @SortDir = 2 THEN co.CompanyName                                    
                                               END DESC                                     
                             , case WHEN @OrderBy = 1 THEN co.CompanyName                                    
                                               END                                      
                                             , case WHEN @OrderBy = 3                         
                                                         AND @SortDir = 2 THEN ct.Name                                    
      END DESC                                            , case WHEN @OrderBy = 3                                    
                                    AND @SortDir = 2 THEN ct.Name                                    
                                               END                                      
                                             , case WHEN @OrderBy = 4                                    
             AND @SortDir = 2 THEN ad.City                                    
                                               END DESC                                    
                                      , case WHEN @OrderBy = 4                                    
                                                         AND @SortDir = 2 THEN ad.City                                    
                     END DESC                                    
                                             , case WHEN @OrderBy = 6                                    
                                                         AND @SortDir = 2 THEN lg.EmployeeName                                    
                                               END DESC                                    
                                             , case WHEN @OrderBy = 6 THEN lg.EmployeeName                                    
                                               END  
											   /*
                                             , case WHEN @OrderBy = 7                                    
                                                         AND @SortDir = 2 THEN dbo.ufn_days_since_last_contact_for_company(co.CompanyId)                                    
                                               END DESC                                    
                                             , case WHEN @OrderBy = 7 THEN dbo.ufn_days_since_last_contact_for_company(co.CompanyId)                                    
                                               END) AS RowNum */
											   /* [003] Code Start*/  
											   , case WHEN @OrderBy = 7                                    
                                                         AND @SortDir = 2 THEN DATEDIFF(DAY, (SELECT MAX(DLUP) FROM dbo.tbCommunicationLog WITH (NOLOCK) WHERE CompanyNo = co.CompanyId), GETDATE())                                   
                                               END DESC                                    
                                             , case WHEN @OrderBy = 7 THEN DATEDIFF(DAY, (SELECT MAX(DLUP) FROM dbo.tbCommunicationLog WITH (NOLOCK) WHERE CompanyNo = co.CompanyId), GETDATE())                                    
                                               END) AS RowNum 
											   /* [003] Code END*/  
                  FROM      dbo.tbCompany co                                    
                  LEFT JOIN dbo.tbCompanyType ct ON co.TypeNo = ct.CompanyTypeId                                    
                  LEFT JOIN dbo.tbCompanyAddress ca ON co.CompanyId = ca.CompanyNo                                    
                                                       AND ca.DefaultBilling = 1                                    
                  LEFT JOIN dbo.tbAddress ad ON ca.AddressNo = ad.AddressId                                    
                  LEFT JOIN dbo.tbCountry cy ON ad.CountryNo = cy.CountryId                                    
                              AND co.ClientNo = cy.ClientNo                  
   LEFT JOIN dbo.tbLogin lg ON lg.LoginId = co.Salesman                                    
                  LEFT JOIN dbo.tbTerms tm ON co.SOTermsNo = tm.TermsId                 
        LEFT JOIN dbo.TbClient cl ON cl.ClientId  = co.ClientNo                           
      LEFT JOIN dbo.tbRegion rg ON rg.RegionId = ad.RegionNo            
    LEFT JOIN dbo.tbCompanyCertificate CIC ON co.CompanyId = CIC.CompanyNo  and CIC.inactive=0 --and CIC.CertificateNumber LIKE @InsuranceCertificateNo     
  LEFT JOIN dbo.tbCertificateCategory crtc ON CIC.CertificateCategoryNo = crtc.CertificateCategoryId     and crtc.inactive=0                                                  
                  WHERE    (( @IsGlobalLogin=1 AND(@ClientId=0 OR co.ClientNo = @ClientId)) OR (@IsGlobalLogin=0 AND co.ClientNo = @ClientId ) OR (@IsGSA=1 AND (@ClientId=0 OR co.ClientNo = @ClientId))
				  OR (@ClientId = -1 AND @IsGlobalLogin = 1 AND @IsGSA = 0)) AND ((co.IsCompany = 1 AND @CompanyTypeId = 3) OR (co.IsSupplier = 1 AND @CompanyTypeId = 2) OR @CompanyTypeId = 1 OR @CompanyTypeId IS NULL)
    AND     co.Inactive = 0                                      
                            AND co.SOApproved = 1                                    
                            AND ca.CeaseDate IS NULL                                    
             --AND co.ClientNo = @ClientId                              
                            AND ((@TeamId IS NULL)                       
                                 OR (NOT @TeamId IS NULL                                    
                                     AND lg.TeamNo = @TeamId))                                    
                            AND ((@DivisionId IS NULL)                           
                    OR (NOT @DivisionId IS NULL                                    
                                     AND lg.DivisionNo = @DivisionId))                                    
                            AND ((@LoginId IS NULL)                                    
                                 OR (NOT @LoginId IS NULL                                    
                   AND co.Salesman = @LoginId))                          
                            AND ((@NameSearch IS NULL)                                    
                                 OR (NOT @NameSearch IS NULL                                    
                                     AND co.FullName LIKE @NameSearch))                                    
                            AND ((@TypeSearch IS NULL)                                    
    OR (NOT @TypeSearch IS NULL                                    
                           AND ct.Name LIKE @TypeSearch))                   
                       
                            AND ((@CitySearch IS NULL)                                    
                                 OR (NOT @CitySearch IS NULL                                    
                                                   
          /* [002] Code Start*/                
                                   --  AND City LIKE @CitySearch))                    
                          AND City  COLLATE Latin1_general_CI_AI LIKE @CitySearch COLLATE Latin1_general_CI_AI))                
                          /* [002] Code END*/                
                            AND ((@CountrySearch IS NULL)                                    
                                 OR (NOT @CountrySearch IS NULL                                    
                                     AND cy.CountryName LIKE @CountrySearch))                                    
                            AND ((@SalesmanSearch IS NULL)                                    
                         OR (NOT @SalesmanSearch IS NULL                                    
                                     AND co.Salesman = @SalesmanSearch))                                    
                            AND ((@CustomerCode IS NULL)                                    
                 OR (NOT @CustomerCode IS NULL                                    
                          AND co.CustomerCode LIKE @CustomerCode))                                    
                            AND ((@CustomerRatingLo IS NULL)                                    
                      OR (NOT @CustomerRatingLo IS NULL                       
                                     AND co.SORating >= @CustomerRatingLo))                                    
                            AND ((@CustomerRatingHi IS NULL)                                    
                                 OR (NOT @CustomerRatingHi IS NULL                                    
                                     AND co.SORating <= @CustomerRatingHi))                                    
      AND ((@SupplierRatingLo IS NULL)                                    
                       OR (NOT @SupplierRatingLo IS NULL                                    
                                     AND co.PORating >= @SupplierRatingLo))                                    
                      AND ((@SupplierRatingHi IS NULL)                                    
                                 OR (NOT @SupplierRatingHi IS NULL                                    
                               AND co.PORating <= @SupplierRatingHi))                                    
                            AND ((@TelNo IS NULL)                                    
                                 OR (NOT @TelNo IS NULL                                    
                      AND co.Telephone LIKE @TelNo))                                    
                            AND ((@ZIP IS NULL)                                  
                      OR (NOT @ZIP IS NULL                                  
                                     AND ad.ZIP LIKE @ZIP))                                  
        AND ((@StateSearch IS NULL)                                    
                                 OR (NOT @StateSearch IS NULL                                    
       /* [002] Code Start*/                
                   --  AND ad.[State] LIKE @StateSearch))                    
                          AND ad.[State]  COLLATE Latin1_general_CI_AI LIKE @StateSearch COLLATE Latin1_general_CI_AI))                
                          /* [002] Code END*/                
              
                                      
            AND ((@CountySearch IS NULL)                                    
                                 OR (NOT @CountySearch IS NULL                                    
              /* [002] Code Start*/                
                              --  AND ad.County LIKE @CountySearch))                   
                          AND ad.County  COLLATE Latin1_general_CI_AI LIKE @CountySearch COLLATE Latin1_general_CI_AI))                
                          /* [002] Code END*/               
                          
           AND ((@region IS NULL)                                    
                                 OR (NOT @region IS NULL                         
     AND rg.RegionId LIKE @region))                               
           -- Eamil search                         
          AND ((@emailSearch IS NULL)                              
                                 OR (NOT @emailSearch IS NULL                              
                                     AND co.EMail LIKE @emailSearch))                          
                      
       AND ((@CountryNo IS NULL)                              
                                 OR (NOT @CountryNo IS NULL                              
                                     AND ad.CountryNo = @CountryNo))           
----------------------------------------------------------------------------            
 AND ((@InsuranceCertificateNo IS NULL)                                              
OR (NOT @InsuranceCertificateNo IS NULL                                              
       AND CIC.CertificateNumber LIKE @InsuranceCertificateNo))      
              
   AND ((@CertificateCategoryNo IS NULL)                                                
            OR (NOT @CertificateCategoryNo IS NULL                                                
                                     AND crtc.CertificateCategoryId = @CertificateCategoryNo))    
            
-----------------------------------------------------------------------------           
                    
   AND (@IsGSA=0 OR (@CurrentClient=@ClientId) OR             
     (@IsGSA=1 AND co.CompanyId IN(SELECT CompanyNo FROM dbo.ufn_GSA_GetMyCompnayIds (@SelectedLogin,@CurrentClient))))                                         
                      
     AND ((@RelatedMFR IS NULL)                                    
                                 OR (NOT @RelatedMFR IS NULL                                    
                                  AND co.CompanyId in (SELECT                       
              a.SupplierCompanyNo                           
            FROM  dbo.tbManufacturerLink a                          
            JOIN dbo.tbManufacturer b                          
             ON a.ManufacturerNo = b.ManufacturerId                          
            WHERE b.ManufacturerName  LIKE @RelatedMFR)))
	AND ((@GroupCode IS NULL)                                    
                                 OR (NOT @GroupCode IS NULL                                    
                                  AND co.GroupCodeNo in (SELECT                       
              ItemId
            FROM  dbo.tbContactGroup
            WHERE ContactName  LIKE @GroupCode)))
                   
   AND ((@IndustryType IS NULL)                                    
                                 OR (NOT @IndustryType IS NULL                                    
                                     AND co.CompanyId in (select a.CompanyNo                
    from tbCompanyIndustryType a                  
    ,tbIndustryType b                  
    where a.IndustryTypeNo = b.IndustryTypeId                  
    and b.Name   LIKE @IndustryType)))                                      
                 )                                    
        SELECT  *                                    
              , (SELECT count(*)                                    
                 FROM   cteSearch                                    
                ) AS RowCnt                                    
        FROM    cteSearch                                    
        WHERE   RowNum BETWEEN @StartPage AND @EndPage                                    
  ORDER BY RowNum 
END
ELSE
BEGIN
;   WITH    cteSearch                                    
              AS (SELECT    co.CompanyId                                    
                          , co.CompanyName                                    
                          , co.Telephone                                    
                          , ct.[Name] AS CompanyType                                    
                          , ad.City                                    
                 , cy.CountryName AS Country                                    
                          , lg.EmployeeName AS SalesmanName                                    
                    --- , dbo.ufn_days_since_last_contact_for_company(co.CompanyId) AS DaysSinceContact   
					   , DATEDIFF(DAY, (SELECT MAX(DLUP) FROM dbo.tbCommunicationLog WITH (NOLOCK) WHERE CompanyNo = co.CompanyId), GETDATE()) AS DaysSinceContact -- [003] Code 
                          , co.OnStop                                    
          , tm.TermsName                       
                          , co.CustomerCode                                    
                          , co.SupplierCode                       
                          , ad.ZIP                                 
                          , cl.ClientName  as ClientName                       
        ,co.EMail    
  ,'' AS CertificateNumber   
  ,'' AS CertificateCategoryName   
  ,(select count(*) from tbToDo where CompanyNo=co.companyid) as TaskCount                       
 , (select dbo.[ufn_cout_CRMProspectsComplete] (co.companyid)) AS CRMCompleteCount                      
                -- 02 April, Need to get below column according to salesman            
       , (SELECT top 1  [GrossProfit]  FROM [TBGPSummary] where CompanyNo=co.CompanyId                              
         )  as SalesGrossProfit                              
        , (SELECT top 1 TotalSales   FROM [TBGPSummary] where CompanyNo=co.CompanyId                               
        )  as SalesTurnover                                   
      --                    , ROW_NUMBER() OVER (ORDER BY --                                    
      --                                         case WHEN @OrderBy = 1                                    
      --                                                   AND @SortDir = 2 THEN co.CompanyName                                    
      --                                         END DESC                                     
      --                       , case WHEN @OrderBy = 1 THEN co.CompanyName                                    
      --                                         END                                      
      --                                       , case WHEN @OrderBy = 2                                    
      --                                                   AND @SortDir = 2 THEN ct.Name                                    
      --END DESC                                            , case WHEN @OrderBy = 2                                    
      --                                                   AND @SortDir = 2 THEN ct.Name                                    
      --                                         END                                      
      --                                       , case WHEN @OrderBy = 3                                    
      --       AND @SortDir = 2 THEN ad.City                                    
      --                                         END DESC                                    
      --                                , case WHEN @OrderBy = 3                                    
      --                                                   AND @SortDir = 2 THEN ad.City                                    
      --               END DESC                                    
      --                                       , case WHEN @OrderBy = 5                                    
      --                                                   AND @SortDir = 2 THEN lg.EmployeeName                                    
      --                                         END DESC                                    
 --                                       , case WHEN @OrderBy = 5 THEN lg.EmployeeName                                    
      --                                         END                                    
      --                                       , case WHEN @OrderBy = 6                                    
      --                                                   AND @SortDir = 2 THEN dbo.ufn_days_since_last_contact_for_company(co.CompanyId)                                    
      --                                         END DESC                  
      --                         , case WHEN @OrderBy = 6 THEN dbo.ufn_days_since_last_contact_for_company(co.CompanyId)                                    
      --                                         END) AS RowNum              
    , ROW_NUMBER() OVER (ORDER BY --                                    
                                               case WHEN @OrderBy = 1                                    
          AND @SortDir = 2 THEN co.CompanyName                                    
                                               END DESC                                     
                             , case WHEN @OrderBy = 1 THEN co.CompanyName                                    
                                               END                                      
                            , case WHEN @OrderBy = 3                         
                      AND @SortDir = 2 THEN ct.Name             
      END DESC                                            , case WHEN @OrderBy = 3                                    
                                    AND @SortDir = 2 THEN ct.Name                                    
                                               END                                      
                                             , case WHEN @OrderBy = 4                                    
             AND @SortDir = 2 THEN ad.City                                    
                                               END DESC                                    
                                      , case WHEN @OrderBy = 4                                    
                                                         AND @SortDir = 2 THEN ad.City                                    
                     END DESC                                    
                                             , case WHEN @OrderBy = 6                                    
                                                         AND @SortDir = 2 THEN lg.EmployeeName                                    
                                               END DESC                                    
                                             , case WHEN @OrderBy = 6 THEN lg.EmployeeName                                    
                                               END   
											   /*
                                             , case WHEN @OrderBy = 7                                    
                                                         AND @SortDir = 2 THEN dbo.ufn_days_since_last_contact_for_company(co.CompanyId)                                    
                                               END DESC                                    
                                             , case WHEN @OrderBy = 7 THEN dbo.ufn_days_since_last_contact_for_company(co.CompanyId)                                    
                                               END) AS RowNum */
											   /* [003] Code START*/  
											   , case WHEN @OrderBy = 7                                    
                                                         AND @SortDir = 2 THEN DATEDIFF(DAY, (SELECT MAX(DLUP) FROM dbo.tbCommunicationLog WITH (NOLOCK) WHERE CompanyNo = co.CompanyId), GETDATE())                                    
                                               END DESC                                    
                                             , case WHEN @OrderBy = 7 THEN DATEDIFF(DAY, (SELECT MAX(DLUP) FROM dbo.tbCommunicationLog WITH (NOLOCK) WHERE CompanyNo = co.CompanyId), GETDATE())                                    
                                               END) AS RowNum 
											   /* [003] Code END*/  
                  FROM      dbo.tbCompany co                                    
                  LEFT JOIN dbo.tbCompanyType ct ON co.TypeNo = ct.CompanyTypeId                                    
                  LEFT JOIN dbo.tbCompanyAddress ca ON co.CompanyId = ca.CompanyNo                                    
                                                       AND ca.DefaultBilling = 1                                    
                  LEFT JOIN dbo.tbAddress ad ON ca.AddressNo = ad.AddressId                                    
                  LEFT JOIN dbo.tbCountry cy ON ad.CountryNo = cy.CountryId                                    
                                                AND co.ClientNo = cy.ClientNo                                    
   LEFT JOIN dbo.tbLogin lg ON lg.LoginId = co.Salesman                                    
                  LEFT JOIN dbo.tbTerms tm ON co.SOTermsNo = tm.TermsId                 
        LEFT JOIN dbo.TbClient cl ON cl.ClientId  = co.ClientNo                           
      LEFT JOIN dbo.tbRegion rg ON rg.RegionId = ad.RegionNo            
                                                
                  WHERE    (( @IsGlobalLogin=1 AND(@ClientId=0 OR co.ClientNo = @ClientId)) OR (@IsGlobalLogin=0 AND co.ClientNo = @ClientId ) OR (@IsGSA=1 AND (@ClientId=0 OR co.ClientNo = @ClientId))
				  OR (@ClientId = -1 AND @IsGlobalLogin = 1 AND @IsGSA = 0)) AND ((co.IsCompany = 1 AND @CompanyTypeId = 3) OR (co.IsSupplier = 1 AND @CompanyTypeId = 2) OR @CompanyTypeId = 1 OR @CompanyTypeId IS NULL)
    AND     co.Inactive = 0                                      
                            AND co.SOApproved = 1                                    
                            AND ca.CeaseDate IS NULL                                    
             --AND co.ClientNo = @ClientId                
                            AND ((@TeamId IS NULL)                       
                                 OR (NOT @TeamId IS NULL                                    
                    AND lg.TeamNo = @TeamId))                                    
                            AND ((@DivisionId IS NULL)                           
                    OR (NOT @DivisionId IS NULL                                    
                                     AND lg.DivisionNo = @DivisionId))                                    
                            AND ((@LoginId IS NULL)                                    
                                 OR (NOT @LoginId IS NULL                                    
                   AND co.Salesman = @LoginId))                          
                            AND ((@NameSearch IS NULL)                                    
                                 OR (NOT @NameSearch IS NULL                                    
                                     AND co.FullName LIKE @NameSearch))                                    
                            AND ((@TypeSearch IS NULL)                                    
    OR (NOT @TypeSearch IS NULL                                    
                           AND ct.Name LIKE @TypeSearch))                   
                       
                            AND ((@CitySearch IS NULL)                                    
                                 OR (NOT @CitySearch IS NULL                                    
                                                   
          /* [002] Code Start*/                
                                   --  AND City LIKE @CitySearch))                    
                          AND City  COLLATE Latin1_general_CI_AI LIKE @CitySearch COLLATE Latin1_general_CI_AI))                
                          /* [002] Code END*/                
                            AND ((@CountrySearch IS NULL)                                    
                                 OR (NOT @CountrySearch IS NULL                                    
                                     AND cy.CountryName LIKE @CountrySearch))                                    
                            AND ((@SalesmanSearch IS NULL)                                    
                         OR (NOT @SalesmanSearch IS NULL                                    
                                     AND co.Salesman = @SalesmanSearch))                                    
                            AND ((@CustomerCode IS NULL)                                    
                                 OR (NOT @CustomerCode IS NULL                                    
                                     AND co.CustomerCode LIKE @CustomerCode))                                    
                            AND ((@CustomerRatingLo IS NULL)                                    
                      OR (NOT @CustomerRatingLo IS NULL                       
                                     AND co.SORating >= @CustomerRatingLo))                                    
                            AND ((@CustomerRatingHi IS NULL)                                    
                                 OR (NOT @CustomerRatingHi IS NULL                                    
                                     AND co.SORating <= @CustomerRatingHi))                                    
      AND ((@SupplierRatingLo IS NULL)                                    
                       OR (NOT @SupplierRatingLo IS NULL                                    
                                     AND co.PORating >= @SupplierRatingLo))                                    
                      AND ((@SupplierRatingHi IS NULL)                                    
                                 OR (NOT @SupplierRatingHi IS NULL                                    
                               AND co.PORating <= @SupplierRatingHi))                                    
                            AND ((@TelNo IS NULL)                           
                                 OR (NOT @TelNo IS NULL                                    
                      AND co.Telephone LIKE @TelNo))                                    
                            AND ((@ZIP IS NULL)                                  
                      OR (NOT @ZIP IS NULL                                  
                                     AND ad.ZIP LIKE @ZIP))                                  
        AND ((@StateSearch IS NULL)                                    
                                 OR (NOT @StateSearch IS NULL                                    
       /* [002] Code Start*/                
                   --  AND ad.[State] LIKE @StateSearch))                    
                          AND ad.[State]  COLLATE Latin1_general_CI_AI LIKE @StateSearch COLLATE Latin1_general_CI_AI))                
                          /* [002] Code END*/                
              
                                      
            AND ((@CountySearch IS NULL)                                    
                                 OR (NOT @CountySearch IS NULL                                    
              /* [002] Code Start*/                
                              --  AND ad.County LIKE @CountySearch))                   
                          AND ad.County  COLLATE Latin1_general_CI_AI LIKE @CountySearch COLLATE Latin1_general_CI_AI))                
                          /* [002] Code END*/               
                          
           AND ((@region IS NULL)                                    
                                 OR (NOT @region IS NULL                         
     AND rg.RegionId LIKE @region))                               
           -- Eamil search                         
          AND ((@emailSearch IS NULL)                              
                                 OR (NOT @emailSearch IS NULL                              
                                     AND co.EMail LIKE @emailSearch))                          
                      
       AND ((@CountryNo IS NULL)                              
                                 OR (NOT @CountryNo IS NULL                              
                                     AND ad.CountryNo = @CountryNo))           
        
                    
   AND (@IsGSA=0 OR (@CurrentClient=@ClientId) OR             
     (@IsGSA=1 AND co.CompanyId IN(SELECT CompanyNo FROM dbo.ufn_GSA_GetMyCompnayIds (@SelectedLogin,@CurrentClient))))                                         
                      
     AND ((@RelatedMFR IS NULL)                                    
                                 OR (NOT @RelatedMFR IS NULL                                    
                                  AND co.CompanyId in (SELECT                       
              a.SupplierCompanyNo                           
            FROM  dbo.tbManufacturerLink a                          
            JOIN dbo.tbManufacturer b                          
             ON a.ManufacturerNo = b.ManufacturerId                          
            WHERE b.ManufacturerName  LIKE @RelatedMFR)))
	AND ((@GroupCode IS NULL)                                    
                                 OR (NOT @GroupCode IS NULL                                    
                                  AND co.GroupCodeNo in (SELECT                       
              ItemId                           
            FROM  dbo.tbContactGroup                                         
            WHERE ContactName  LIKE @GroupCode)))
                   
   AND ((@IndustryType IS NULL)                                    
                                 OR (NOT @IndustryType IS NULL                                    
                                     AND co.CompanyId in (select a.CompanyNo                
    from tbCompanyIndustryType a                  
    ,tbIndustryType b                  
    where a.IndustryTypeNo = b.IndustryTypeId                  
    and b.Name   LIKE @IndustryType)))                                      
                 )                                    
        SELECT  *        
              , (SELECT count(*)                                    
                 FROM   cteSearch                                    
          ) AS RowCnt                                    
        FROM    cteSearch                               
        WHERE   RowNum BETWEEN @StartPage AND @EndPage                                    
  ORDER BY RowNum 
END


GO


