﻿/*===========================================================================================  
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-212456]     An.TranTan		 18-SEP-2024		CREATE		Get list Prospective Offer
[US-212456]     An.TranTan		 03-OCT-2024		UPDATE		Change filter salesperson => Imported By
[US-212456]     An.TranTan		 09-OCT-2024		UPDATE		Change filter Imported By from textbox => dropdown
===========================================================================================  
*/
GO
CREATE OR ALTER PROC usp_datalistnugget_ProspectiveOffer (
	@TeamId INT = NULL
	,@DivisionId INT = NULL
	,@LoginId INT = NULL
	,@OrderBy INT = 1
	,@SortDir INT = 1
	,@PageIndex INT = 0
	,@PageSize INT = 10
	,@FileNameSearch NVARCHAR(100) = NULL
	,@PartSearch NVARCHAR(50) = NULL
	,@DateUploadFrom DATETIME = NULL
	,@DateUploadTo DATETIME = NULL
	--,@ImportedBySearch NVARCHAR(100) = NULL
	,@ImportedBy INT = NULL
	,@SupplierSearch NVARCHAR(100) = NULL
)
AS
BEGIN
	DECLARE @StartPage INT = (@PageIndex * @PageSize + 1)
			,@EndPage INT = ((@PageIndex + 1) * @PageSize);

	IF(@DateUploadFrom IS NOT NULL)
		SET @DateUploadFrom = dbo.ufn_get_start_of_day_for_date(@DateUploadFrom)
	IF(@DateUploadTo IS NOT NULL)
		SET @DateUploadTo = dbo.ufn_get_start_of_day_for_date(@DateUploadTo)

	;WITH cteSearch
	AS (
		SELECT 
			po.ProspectiveOfferId,
			po.SupplierNo AS SupplierId,
			cp.CompanyName AS SupplierName,
			po.SourceFileName,
			po.ImportRowCount,
			po.ImportStatus,
			l.EmployeeName AS ImportedBy,
			po.DLUP AS ImportDate,
			ROW_NUMBER() OVER 
				(ORDER BY 
					CASE WHEN @OrderBy = 1 AND @SortDir = 2 THEN ProspectiveOfferId
						END DESC
					,CASE WHEN @OrderBy = 1 THEN ProspectiveOfferId
						END
				) AS RowNum
		FROM dbo.tbProspectiveOffers po WITH (NOLOCK)
			JOIN dbo.tbCompany cp WITH (NOLOCK) ON cp.CompanyId = po.SupplierNo
			JOIn dbo.tbLogin l WITH(NOLOCK) ON l.LoginId = po.CreatedBy
		WHERE
			(@FileNameSearch IS NULL OR po.SourceFileName LIKE @FileNameSearch)
			AND (@PartSearch IS NULL 
					OR EXISTS (SELECT TOP 1 1 FROM dbo.tbProspectiveOfferLines pol WITH (NOLOCK)
								WHERE
									pol.ProspectiveOfferNo = po.ProspectiveOfferId
									AND (dbo.ufn_get_fullpart(pol.Part) LIKE @PartSearch
									OR dbo.ufn_get_fullpart(pol.AlternativePart) LIKE @PartSearch
									OR dbo.ufn_get_fullpart(pol.SupplierPart) LIKE @PartSearch)
					)
				)
			AND (@DateUploadFrom IS NULL OR po.DLUP >= @DateUploadFrom)
			AND (@DateUploadTo IS NULL OR po.DLUP <= @DateUploadTo)
			AND (@SupplierSearch IS NULL OR cp.CompanyName LIKE @SupplierSearch)
			AND (@TeamId IS NULL OR ISNULL(l.TeamNo, 0) = @TeamId)
			AND (@DivisionId IS NULL OR l.DivisionNo = @DivisionId)
			AND (@LoginId IS NULL OR l.LoginId = @LoginId)
			--AND (@ImportedBySearch IS NULL OR l.EmployeeName LIKE @ImportedBySearch)
			AND (@ImportedBy IS NULL OR l.LoginId = @ImportedBy)
	)
	SELECT *
		,( SELECT count(*) FROM cteSearch ) AS RowCnt
	FROM cteSearch
	WHERE RowNum BETWEEN @StartPage AND @EndPage
	ORDER BY RowNum
END
GO

/*test script
exec usp_datalistnugget_ProspectiveOffer
	@TeamId  = NULL
	,@DivisionId  = NULL
	,@LoginId  = NULL
	,@OrderBy  = 1
	,@SortDir  = 1
	,@PageIndex  = 0
	,@PageSize  = 10
	,@FileNameSearch  = NULL
	,@PartSearch  = 'BAV%'
	,@DateUploadFrom  = NULL
	,@DateUploadTo  = NULL
	,@ImportedBy  = 6670
	,@SupplierSearch  = NULL
*/
