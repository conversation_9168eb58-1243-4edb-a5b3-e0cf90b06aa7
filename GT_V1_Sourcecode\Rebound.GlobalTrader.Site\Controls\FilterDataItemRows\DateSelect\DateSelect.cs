using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Collections;
using Rebound.GlobalTrader.Site.Controls.Nuggets;
using Rebound.GlobalTrader.Site.Controls.DropDowns;

namespace Rebound.GlobalTrader.Site.Controls.FilterDataItemRows {
    [DefaultProperty("")]
    [ToolboxData("<{0}:DateSelect runat=server></{0}:DateSelect>")]
    public class DateSelect : Base {

        #region Locals

        protected ReboundTextBox _txt;
        protected Controls.Calendar _cal;

        #endregion

        #region Properties

        /// <summary>
        /// Width of text box
        /// </summary>
        private Unit _untTextBoxWidth;
        public Unit TextBoxWidth {
            get { return _untTextBoxWidth; }
            set { _untTextBoxWidth = value; }
        }

        /// <summary>
        /// Comparison type
        /// </summary>
        private NumericalComparison.NumericalComparisonType _enmComparisonType = NumericalComparison.NumericalComparisonType.EqualTo;
        public NumericalComparison.NumericalComparisonType ComparisonType {
            get { return _enmComparisonType; }
            set { _enmComparisonType = value; }
        }

        #endregion

        #region Overrides

        /// <summary>
        /// OnInit
        /// </summary>
        /// <param name="e"></param>
        protected override void OnInit(EventArgs e) {
            FieldType = Type.DateSelect;
            base.OnInit(e);
			bool blnDebug = false;
#if (DEBUG)
			blnDebug = true;
#endif
			AddScriptReference(Functions.GetScriptReference(blnDebug, "Rebound.GlobalTrader.Site", "Controls.FilterDataItemRows.DateSelect.DateSelect.js", true));
        }

        /// <summary>
        /// OnLoad
        /// </summary>
        /// <param name="e"></param>
        protected override void OnLoad(EventArgs e) {
            EnsureChildControls();
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.DateSelect", ClientID);
            _scScriptControlDescriptor.AddElementProperty("txt", _txt.ClientID);
            _scScriptControlDescriptor.AddComponentProperty("cal", _cal.ClientID);
            _scScriptControlDescriptor.AddProperty("enmComparison", _enmComparisonType);
            base.OnLoad(e);
        }

        /// <summary>
        /// create controls
        /// </summary>
        protected override void CreateChildControls() {
            base.CreateChildControls();
            _txt = new ReboundTextBox();
            _txt.ID = "txt";
            _txt.Width = _untTextBoxWidth;
            _lblField.Controls.Add(_txt);

            _cal = new Controls.Calendar();
            _cal.RelatedTextBoxID = _txt.ID;
            _lblField.Controls.Add(_cal);
        }

        public override void SetDefaultValue() {
            DateTime dtmShakespeare = new DateTime(1564, 4, 25);
            DateTime dtmDefault = dtmShakespeare;
            switch (DefaultValue) {
                case "TODAY": dtmDefault = DateTime.Now; break;
                case "FIRSTDAYOFMONTH": dtmDefault = Functions.GetFirstDayOfMonth(DateTime.Now); break;
                case "LASTDAYOFMONTH": dtmDefault = Functions.GetLastDayOfMonth(DateTime.Now); break;
                case "ONEWEEKAGO": dtmDefault = Functions.GetOneWeekAgo(DateTime.Now); break;
                case "ONEWEEKAHEAD": dtmDefault = Functions.GetOneWeekAhead(DateTime.Now); break;
                default: if (!DateTime.TryParse(DefaultValue, out dtmDefault)) dtmDefault = dtmShakespeare; break;
            }
            if (dtmDefault != dtmShakespeare) SetInitialValue(Functions.FormatDate(dtmDefault));
            base.SetDefaultValue();
        }

        public override void Reset() {
			EnsureChildControls();
			_txt.Text = "";
            Enable(false);
            base.Reset();
        }

        #endregion

        #region Methods

        public void SetInitialValue(object objValue) {
			EnsureChildControls();
            _txt.Text = HttpUtility.UrlDecode(objValue.ToString().Trim());
			if (!string.IsNullOrEmpty(_txt.Text)) Enable(true);
        }

        #endregion

    }

}