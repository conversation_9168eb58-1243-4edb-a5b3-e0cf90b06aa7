///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />

Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");

Rebound.GlobalTrader.Site.Controls.Nuggets.EndUserUndertakingForm = function (element) {
    Rebound.GlobalTrader.Site.Controls.Nuggets.EndUserUndertakingForm.initializeBase(this, [element]);
    this._intSalesOrderID = -1;
    this._intLineID = -1;
    this.solineIds = [];
    this._ibtnEEUTemp = null;
    //this._ibtnEUUFormSA992C = null;
};

Array.prototype.remove = function (value) {
    if (this.indexOf(value) !== -1) {
        this.splice(this.indexOf(value), 1);
        return true;
    } else {
        return false;
    };
};

Rebound.GlobalTrader.Site.Controls.Nuggets.EndUserUndertakingForm.prototype = {
    get_intSalesOrderID: function () { return this._intSalesOrderID; }, set_intSalesOrderID: function (v) { if (this._intSalesOrderID !== v) this._intSalesOrderID = v; },
    get_tblAll: function () { return this._tblAll; }, set_tblAll: function (v) { if (this._tblAll !== v) this._tblAll = v; },
    get_ibtnEUUForm: function () { return this._ibtnEUUForm; }, set_ibtnEUUForm: function (v) { if (this._ibtnEUUForm !== v) this._ibtnEUUForm = v; },//[001]
    //get_ibtnEUUFormSA992C: function () { return this._ibtnEUUFormSA992C; }, set_ibtnEUUFormSA992C: function (v) { if (this._ibtnEUUFormSA992C !== v) this._ibtnEUUFormSA992C = v; },//[001]
    addRefreshEvent: function (handler) { this.get_events().addHandler("Refresh", handler); },
    removeRefreshEvent: function (handler) { this.get_events().removeHandler("Refresh", handler); },

    onRefreshChange: function () {
        var handler = this.get_events().getHandler("RefreshChange");
        if (handler) handler(this, Sys.EventArgs.Empty);
    },

    initialize: function () {
        this._strDataPath = "controls/Nuggets/EndUserUndertakingForm";
        this._strDataObject = "EndUserUndertakingForm";
        Rebound.GlobalTrader.Site.Controls.Nuggets.EndUserUndertakingForm.callBaseMethod(this, "initialize");
        this.addRefreshEvent(Function.createDelegate(this, this.getTabData_All));
        this.getTabData_All();
        this.getEEUFileData();
    },

    dispose: function () {
        if (this.isDisposed) return;
        Rebound.GlobalTrader.Site.Controls.Nuggets.EndUserUndertakingForm.callBaseMethod(this, "dispose");
    },

    getTabData_All: function () {
       // 
        var d = new Date();
        var n = d.getTime();
        //this.showLoading(true);
        this.getData_Start();
        //this._ctlTabStrip.showLoading(true);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData(this._strDataPath);
        obj.set_DataObject(this._strDataObject);
        obj.set_DataAction("GetLines_All");
        obj.addParameter("id", this._intSalesOrderID);
        obj.addParameter("CurrentTime", n);
        obj.addParameter("GetForUserUnderTakingForm", "EUU");
        obj.addDataOK(Function.createDelegate(this, this.getTabDataOK_All));
        obj.addError(Function.createDelegate(this, this.getTabDataError));
        obj.addTimeout(Function.createDelegate(this, this.getTabDataError));
        //
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getTabDataOK_All: function (args) {
        this.showLoading(false);
        //this._ctlTabStrip.showContent(true);
        this._tblAll.clearTable();
        this.solineIds = [];
        //$R_IBTN.enableButton(this._ibtnLineWarehouse, false);
        this.addLinesData(this._tblAll, args._result, "LinesAll");
        this._tblAll.resizeColumns();
    },

    getTabDataError: function (args) {
        this.showError(true, args.get_ErrorMessage());
    },

    getCurrentTable: function () {
        //var tbl;
        //switch (this._ctlTabStrip._selectedTabIndex) {
        //    case 0: tbl = this._tblAll; break;
        //    case 1: tbl = this._tblOpen; break;
        //    case 2: tbl = this._tblClosed; break;
        //}

        return this._tblAll;
    },

    addLinesData: function (tbl, result, strDataName) {
        var tbl = this.getCurrentTable();
        tbl.enable(true);

        // this._blnContainLine = false;
        //[001] code start
        if (this._intSalesOrderLineID > 0) {
            this._intLineID = this._intSalesOrderLineID;
        }
        //[001] code end

        if (result[strDataName]) {
            for (var i = 0; i < result[strDataName].length; i++) {
                //
                var row = result[strDataName][i];
                //if (row.IsFromIPO == true) {
                //    this.showHideOriginalAllocateButton(!row.IsFromIPO);
                //}



                var blnIsService = (row.ServiceNo > 0);
                var IsSourcingResultExist = row.IsSourcingResultExist;
                var IsCheckBoxEnabled = false;

                //Espire: remove the InternalPurchaseOrderNumber check in for enable check box
                //IsCheckBoxEnabled = (row.IsIPO == true && row.IsChecked == true && row.IsPosted == true && row.IsAllocated == false) ? true : false;
                //if (row.IsClone) {
                //    IsCheckBoxEnabled = (row.IsIPO == true && row.IsChecked == true && row.IsPosted == true && row.IsAllocated == false) ? true : false;
                //}
                //else {
                //    IsCheckBoxEnabled = (row.IsIPO == true && row.IsChecked == true && !(row.InternalPurchaseOrderNumber > 0) && row.IsPosted == true && row.IsAllocated == false) ? true : false;
                //}
                //if (this._blnAllIPOCreated == false) {
                //    this.solineIds = [];
                //}

                var aryData = [
                     row.LineNo
                    , $R_FN.writeDoubleCellValue($R_FN.writePartNo(row.Part, row.ROHS), $R_FN.setCleanTextValue(row.CustomerPart))
                    , (row.EUUFormRequired) ? "Yes" : "No"
                    , row.EUUPDFUploadDate
                    , row.EUUUPDFploadName
                    , row.EUUFormRequired ? ((row.IsEUUPDFAvailable) ? String.format("&nbsp;&nbsp;<center><a href=\"javascript:void(0);\" onclick=\"$RGT_openEndUserUndertakingDoc({0})\" title=\"Click to View and add docs\"><img border='0'  src=app_themes/original/images/IconButton/pdficon.jpg" + " width='30' height='26'></center></a>", row.LineID) : String.format("&nbsp;&nbsp;<a href=\"javascript:void(0);\" onclick=\"$RGT_openEndUserUndertakingDoc({0})\" style='text-decoration:none;' title=\"Click to add docs\"><center><b><img border='0'  src=app_themes/Original/images/buttons/sourcing/history_add.gif ></b></center></a>", row.LineID)) : ""
                ];

                var objExtraData = {
                      IsService: blnIsService
                    , Inactive: row.Inactive
                    , lEccnCode: row.ECCNCode
                    , lEccnCodeNo: row.ECCNCodeNo
                    , lPart: row.Part

                };
                var strCSS = "";
                //var IPOstrcsss = false;
                //if (objExtraData.posted) strCSS = "posted";
                //if (objExtraData.allocated) strCSS = "allocated";
                //if (objExtraData.shipped) strCSS = "shipped";
                //if (objExtraData.partShipped) strCSS = "partShipped";
                //if (objExtraData.Inactive) strCSS = "inactive";
                //if (row.IsIPO) IPOstrcsss = true;//tbl.addRowRowColor
                tbl.addRow(aryData, row.LineID, row.LineID == this._intLineID, objExtraData, strCSS);
                //                if (row.IsIPO == true) {
                
                row = null;

            }


        }
        this._intLineCount = tbl.countRows();
        if (strDataName == "LinesAll")
            this._blnContainLine = (this._intLineCount > 0);
        $R_FN.setInnerHTML(this._lblSubTotal, result.SubTotal);
        $R_FN.setInnerHTML(this._lblFreight, result.Freight);
        $R_FN.setInnerHTML(this._lblTax, result.Tax);
        $R_FN.setInnerHTML(this._lblTotal, result.Total);
        //[009] start
        this._bnlIsConfirmedAll = result.IsConfirmedAll;
        //[009] end
        this._totalLinePrice = result.TotalVal;
        if (this._ibtnPostAll) $R_IBTN.showButton(this._ibtnPostAll, this._aryLinesForPosting.length > 0);
        if (this._ibtnUnpostAll) $R_IBTN.showButton(this._ibtnUnpostAll, this._aryLinesForUnposting.length > 0);
        this.showContent(true);
        this.showContentLoading(false);
        //[001] code start
        if (this._intSalesOrderLineID > 0)
            this._intSalesOrderLineID = -1;
        //[001] code end
        this._anyLinePosted = result.AnyLinePosted;
        this._totalFreight = result.TotalFreight;
        this._taxRate = result.TaxRate;


        this.onRefreshChange();
    },

    getEEUFileData: function () {
        //this.showLoading(true);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("Controls/SetupNuggets/SourcingLinks");
        obj.set_DataObject("SourcingLinks");
        obj.set_DataAction("GetData");
        obj.addDataOK(Function.createDelegate(this, this.getEEUFileDataOK));
        obj.addError(Function.createDelegate(this, this.getEEUFileDataError));
        obj.addTimeout(Function.createDelegate(this, this.getEEUFileDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getEEUFileDataError: function (args) {
        this.showError(true, args.get_ErrorMessage());
    },

    getEEUFileDataOK: function (args) {
        var result = args._result;
        $R_FN.setInnerHTML(this._ibtnEUUForm, "");
        //$R_FN.setInnerHTML(this._ibtnEUUFormSA992C, "");
        var strPDF = "";
        //var strPDFEUUForms5A992C = "";

        if (result.Items) {
            for (var i = 0; i < result.Items.length; i++) {
                var row = result.Items[i];
                if (row.Name =="EUU Forms") {
                    strPDF += row.URL;
                }
                //if (row.Name == "EUU Forms (5A992C)") {
                //    strPDFEUUForms5A992C += row.URL;
                //}
                row = null;
            }
        }

        $('#ctl00_cphMain_SOEndUserUndertakingForm_ctlDB_ctl12_ibtnEUUForm_hyp').attr('href', strPDF);
        $('#ctl00_cphMain_SOEndUserUndertakingForm_ctlDB_ctl12_ibtnEUUForm_hyp').attr('target', "_blank");
        //$('#ctl00_cphMain_SOEndUserUndertakingForm_ctlDB_ctl12_ibtnEEUTemp_hyp').attr('download', "Download_EUU_form_Template");
        //$('#ctl00_cphMain_SOEndUserUndertakingForm_ctlDB_ctl12_ibtnEUUForm_hyp').attr('download', "_blank");
        //$('#ctl00_cphMain_SOEndUserUndertakingForm_ctlDB_ctl12_ibtnEEUTemp_hyp').attr('type', "Application/pdf");        

        //$('#ctl00_cphMain_SOEndUserUndertakingForm_ctlDB_ctl12_ibtnEEUTemp_hyp').attr('href', strPDFEUUForms5A992C);
        //$('#ctl00_cphMain_SOEndUserUndertakingForm_ctlDB_ctl12_ibtnEEUTemp_hyp').attr('download', "Download_EEU_form_Template");
        //$('#ctl00_cphMain_SOEndUserUndertakingForm_ctlDB_ctl12_ibtnEEUTemp_hyp').attr('type', "Application/pdf");
        this.getDataOK_End();
    },
};

Rebound.GlobalTrader.Site.Controls.Nuggets.EndUserUndertakingForm.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.EndUserUndertakingForm", Rebound.GlobalTrader.Site.Controls.Nuggets.Base);
