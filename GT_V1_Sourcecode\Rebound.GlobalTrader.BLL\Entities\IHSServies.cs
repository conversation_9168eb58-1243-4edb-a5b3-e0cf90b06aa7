﻿using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using Rebound.GlobalTrader.DAL;
using Rebound.GlobalTrader.BLL;
using System.IO;
using System.Net;
using System.Text;




namespace IHSPart
{
    class IHSServies
    {
        public class MfrPart
        {
            public string mfrFullName { get; set; }
        }

        public class Attributes
        {
            public MfrPart mfrPart { get; set; }
        }

        public class Root
        {
            public Int64 id { get; set; }
            public string prtNbr { get; set; }
            public string mfrName { get; set; }
            public string prtStatus { get; set; }
            public string prtDesc { get; set; }
            public Attributes attributes { get; set; }
            public Nullable<int> ManufacturerNo { get; set; }
            public string Descriptions { get; set; }
            public string CountryOfOrigin { get; set; }
            public Nullable<int> CountryOfOriginNo { get; set; }
            public string LifeCycleStage { get; set; }
            public string MSL { get; set; }
            public Nullable<int> MSLNo { get; set; }
            public string HTSCode { get; set; }
            public Nullable<double> AveragePrice { get; set; }
            public string Packaging { get; set; }
            public Nullable<int> PackagingSize { get; set; }
            public Nullable<int> UpdatedBy { get; set; }
            public Nullable<System.DateTime> OriginalEntryDate { get; set; }
            public Nullable<System.DateTime> DLUP { get; set; }
            public Nullable<bool> Inactive { get; set; }
        }
    }
}