﻿using Microsoft.Azure.Storage;
using Microsoft.Azure.Storage.Auth;
using Microsoft.Azure.Storage.Blob;
using Newtonsoft.Json;
using OfficeOpenXml;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site.Code.Common;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.IO;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using OfficeOpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Spreadsheet;
using OfficeOpenXml.Style;
using Newtonsoft.Json;


namespace Rebound.GlobalTrader.Site.Code.Common
{
    public class CustomerTemplate
    {
        public class CustomColumnList
        {
            public int columnId { get; set; }
            public string ColumnName { get; set; }
        }
        public class TestData
        {
            public string jsondata { get; set; }
            public string columns { get; set; }
            public string data { get; set; }
        }
        public class TestData1
        {
            public List<columnsinfo> columns { get; set; }
            public List<Dictionary<string, object>> data { get; set; }

            public bool IsError { get; set; }

            public string ErrorMessage { get; set; }
        }
        public class columnsinfo
        {
            public string title { get; set; }
            public string data { get; set; }
        }
        public class StatusMessage
        {
            public string status { get; set; }
            public string Message { get; set; }
        }
        public DataTable GetBOMManagerUploadMapping(int QuoteId)
        {
            return BOMManagerContract.GetBOMManagerUploadMapping(QuoteId, SessionManager.ClientID);
            //StatusMessage stts = new StatusMessage();

            //try
            //{
            //    DataTable dt = BOMManagerContract.GetBOMManagerUploadMapping(QuoteId, SessionManager.ClientID);
            //    foreach (DataRow dr in dt.Rows)
            //    {

            //        stts.status = dr["Status"].ToString();
            //        stts.Message = dr["Message"].ToString();
            //    }
            //    if (stts.status != "Success")
            //    {
            //        new Errorlog().LogMessage("Inside CustomTemplateController class, Method name : GetBOMManagerUploadMapping. Exception details:" + stts.Message);
            //    }
            //    return JsonConvert.SerializeObject(stts);
            //    //return Json(stts, JsonRequestBehavior.AllowGet);
            //}
            //catch (Exception ex)
            //{
            //    stts.status = "Fail";
            //    stts.Message = "Something went wrong.";
            //    new Errorlog().LogMessage("Inside CustomTemplateController class, Method name : GetBOMManagerUploadMapping. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
            //    return JsonConvert.SerializeObject(stts);
            //    //return Json(stts, JsonRequestBehavior.AllowGet);
            //}
        }
        public DataTable GetCustomTemplateMapping(int QuoteId)
        {
            return BOMManagerContract.GetCustomTemplateMapping(QuoteId, SessionManager.ClientID);
            //StatusMessage stts = new StatusMessage();

            //try
            //{
            //    DataTable dt = BOMManagerContract.GetCustomTemplateMapping(QuoteId, SessionManager.ClientID);
            //    foreach (DataRow dr in dt.Rows)
            //    {

            //        stts.status = "Success";
            //        stts.Message = dr["MappingDetails"].ToString();
            //    }
            //    if (stts.status != "Success")
            //    {
            //        new Errorlog().LogMessage("Inside CustomTemplateController class, Method name : GetCustomTemplateMapping. Exception details:" + stts.Message);
            //    }
            //    return JsonConvert.SerializeObject(stts);
            //    //return Json(stts, JsonRequestBehavior.AllowGet);
            //}
            //catch (Exception ex)
            //{
            //    stts.status = "Fail";
            //    stts.Message = "Something went wrong.";
            //    new Errorlog().LogMessage("Inside CustomTemplateController class, Method name : GetCustomTemplateMapping. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
            //    return JsonConvert.SerializeObject(stts);
            //    //return Json(stts, JsonRequestBehavior.AllowGet);
            //}
        }
        private DataTable _excelDt;
        private DataTable _CustomeTemplateDt;

        public DataTable GenerateCustomTemplateData(int QuoteId, string ColumnString, Boolean DefaulCheck, DataTable _excelDt)
        {
            DataTable CustomTemplateDt = new DataTable();
            DataTable dt = new DataTable();
            dt = _excelDt;

            //TempData.Keep("excelDt");
            //TempData["excelDt"] = dt;

            if (dt.Columns.Contains("LineNo") == false)
            {
                DataColumn Col = dt.Columns.Add("LineNo", System.Type.GetType("System.Int32"));
                Col.SetOrdinal(0);// to put the column in position 0;
                dt.AsEnumerable()
                .Select((row, index) => new { Row = row, Index = index })
                .ToList()
                .ForEach(item => item.Row.SetField("LineNo", item.Index + 1));
            }

            string JSONString = string.Empty;
            try
            {
                if (!string.IsNullOrWhiteSpace(QuoteId.ToString()))
                {
                    //int QuoteId = Convert.ToInt32(Request.QueryString["QuoteID"].ToString());
                    //int BOMManagerId = Convert.ToInt32(Request.QueryString["BOM"].ToString());
                    if (QuoteId > 0)
                    {
                        //code here
                        CustomTemplateDt = BOMManagerContract.GenerateCustomTemplateData(QuoteId, ColumnString);

                        JSONString = JsonConvert.SerializeObject(CustomTemplateDt);

                        //dt.AsEnumerable().Join(CustomTemplateDt.AsEnumerable(), dt => Convert.ToString(dt["part"]),
                        //    CustomTemplateDt => Convert.ToString(CustomTemplateDt["part"]),(dt, CustomTemplateDt) => new { dt, CustomTemplateDt }).ToList().ForEach(o => o.dt.SetField("price", o.CustomTemplateDt["unit price"].ToString()));
                        List<string> strarray = ColumnString.Split('|').ToList();
                        strarray.RemoveAt(strarray.Count - 1);
                        DataTable _dtMaster = dt.Copy();

                        string ExcelPart = "";
                        string ExcelMFR = "";
                        string ExcelProduct = "";
                        string ExcelPack = "";
                        string ExcelNotes = "";
                        string ExcelTotalPrice = "";
                        string ExcelUnitPrice = "";
                        string ExcelQuotedQuantity = "";
                        string ExcelCustomerPart = "";
                        string ExcelDC = "";
                        string ExcelDutyCode = "";
                        string ExcelETA = "";
                        string ExcelMOQ = "";
                        string ExcelSPQ = "";
                        string QuotedPrice = "";
                        foreach (string excelcolname in strarray)
                        {
                            //string[] col1 = excelcolname.Split('=');
                            List<string> col1 = excelcolname.Split('=').ToList();
                            col1.RemoveAt(1);
                            if (col1[0].ToString() == "Part")
                                ExcelPart = col1[1].ToString();
                            if (col1[0].ToString() == "Mfr")
                                ExcelMFR = col1[1].ToString();
                            if (col1[0].ToString() == "Product")
                                ExcelProduct = col1[1].ToString();
                            if (col1[0].ToString() == "Pack")
                                ExcelPack = col1[1].ToString();
                            if (col1[0].ToString() == "Notes")
                                ExcelNotes = col1[1].ToString();
                            if (col1[0].ToString() == "TotalPrice")
                                ExcelTotalPrice = col1[1].ToString();
                            if (col1[0].ToString() == "UnitPrice")
                                ExcelUnitPrice = col1[1].ToString();
                            if (col1[0].ToString() == "QuotedQuantity")
                                ExcelQuotedQuantity = col1[1].ToString();
                            if (col1[0].ToString() == "CustomerPart")
                                ExcelCustomerPart = col1[1].ToString();
                            if (col1[0].ToString() == "DC")
                                ExcelDC = col1[1].ToString();
                            if (col1[0].ToString() == "DutyCode")
                                ExcelDutyCode = col1[1].ToString();
                            if (col1[0].ToString() == "ETA")
                                ExcelETA = col1[1].ToString();
                            if (col1[0].ToString() == "MOQ")
                                ExcelMOQ = col1[1].ToString();
                            if (col1[0].ToString() == "SPQ")
                                ExcelSPQ = col1[1].ToString();
                            if (col1[0].ToString() == "QuotedPrice")
                                ExcelSPQ = col1[1].ToString();
                            if (col1[0].ToString() == "LeadTime")
                                ExcelSPQ = col1[1].ToString();
                            if (col1[0].ToString() == "DeliveryDate")
                                ExcelSPQ = col1[1].ToString();

                        }
                        _dtMaster.Columns.Add("Updated");
                        _dtMaster.Columns.Add("Quoted Unit Price");
                        _dtMaster.Columns.Add("Quoted Total Price");
                        _dtMaster.Columns.Add("Lead Time");
                        _dtMaster.Columns.Add("Delivery Date");
                        DataTable _dtChild = CustomTemplateDt;


                        _dtMaster.AsEnumerable().Join(_dtChild.AsEnumerable(), _dtmater => new { part = _dtmater.Field<string>(ExcelPart) + _dtmater.Field<int>("LineNo") },
                        _dtchild => new { part = Convert.ToString(_dtchild["part"]) + Convert.ToInt32(_dtchild["SNo"]) },//.Where(x => x._dtMaster.fe.[ExcelMFR] == _dtchild["ManufacturerNo"]),
                        (_dtmater, _dtchild) => new { _dtmater, _dtchild })//.Where(r=> r._dtmater[ExcelMFR]== r._dtchild["ManufacturerName"])
                        .ToList()
                       .ForEach(o =>
                       {
                           //o._dtmater[ExcelProduct] = o._dtchild["product"];
                           if (ExcelMFR != "")
                               o._dtmater[ExcelMFR] = o._dtchild["Mfr"];

                           if (ExcelProduct != "") o._dtmater[ExcelProduct] = o._dtchild["product"];
                           if (ExcelPack != "") o._dtmater[ExcelPack] = o._dtchild["Pack"];
                           if (ExcelNotes != "") o._dtmater[ExcelNotes] = o._dtchild["notes"];
                           if (ExcelTotalPrice != "") o._dtmater[ExcelTotalPrice] = o._dtchild["TotalPrice"];
                           if (ExcelUnitPrice != "") o._dtmater[ExcelUnitPrice] = o._dtchild["UnitPrice"];
                           if (ExcelQuotedQuantity != "") o._dtmater[ExcelQuotedQuantity] = o._dtchild["QuotedQuantity"];
                           if (ExcelCustomerPart != "") o._dtmater[ExcelCustomerPart] = o._dtchild["CustomerPart"];
                           if (ExcelDC != "") o._dtmater[ExcelDC] = o._dtchild["DC"];
                           if (ExcelDutyCode != "") o._dtmater[ExcelDutyCode] = o._dtchild["DutyCode"];
                           if (ExcelETA != "") o._dtmater[ExcelETA] = o._dtchild["ETA"];
                           if (ExcelMOQ != "") o._dtmater[ExcelMOQ] = o._dtchild["MOQ"];
                           if (ExcelSPQ != "") o._dtmater[ExcelSPQ] = o._dtchild["SPQ"];
                           o._dtmater["Updated"] = "Y";
                           o._dtmater["Quoted Unit Price"] = o._dtchild["QuotedPrice"];
                           o._dtmater["Quoted Total Price"] = o._dtchild["TotalPrice"];
                           o._dtmater["Lead Time"] = o._dtchild["LeadTime"];
                           var deliveryDate = o._dtchild["DeliveryDate"] != null ? Functions.FormatDate(Convert.ToDateTime(o._dtchild["DeliveryDate"])) : "";
                           o._dtmater["Delivery Date"] = deliveryDate;
                       });



                        var serializer = new System.Web.Script.Serialization.JavaScriptSerializer();
                        DataTable dtNew = new DataTable();
                        dtNew = _dtMaster.Clone();
                        //foreach (DataRow dr in _dtMaster.Rows)
                        //{
                        //    dr[""]
                        //}
                        //DataRow[] dtrowss = _dtMaster.Select("Updated= Y");//.Rows.Cast<DataRow>().Where(x => x["Updated"] == "Y").ToList();
                        var dtrows = _dtMaster.Rows.Cast<DataRow>().Where(x => x["Updated"] == "Y").ToList();
                        foreach (DataRow dr in dtrows)
                        {
                            DataRow drnew = dtNew.NewRow();
                            drnew = dr;
                            dtNew.ImportRow(dr);
                        }
                        dtNew.Columns.Remove("Updated");
                        if (DefaulCheck == false)
                        {
                            dtNew.Columns.Remove("Quoted Unit Price");
                            dtNew.Columns.Remove("Quoted Total Price");
                            dtNew.Columns.Remove("Lead Time");
                            dtNew.Columns.Remove("Delivery Date");
                        }
                        dtNew.Columns.Remove("LineNo");

                        _CustomeTemplateDt = dtNew;
                        return _CustomeTemplateDt;
                        //TestData t = new TestData();
                        //TestData1 t1 = new TestData1();
                        //List<columnsinfo> _col = new List<columnsinfo>();
                        //for (int i = 0; i <= dtNew.Columns.Count - 1; i++)
                        //{
                        //    _col.Add(new columnsinfo { title = dtNew.Columns[i].ColumnName, data = dtNew.Columns[i].ColumnName });
                        //}

                        //t1.columns = _col;
                        //string col = (string)serializer.Serialize(_col);
                        //t.columns = col;
                        //var lst = dtNew.AsEnumerable().Select(r => r.Table.Columns.Cast<DataColumn>().Select(c => new KeyValuePair<string, object>(c.ColumnName, r[c.Ordinal])).ToDictionary(z => z.Key, z => z.Value)).ToList();
                        //t1.data = lst;
                        //string data = serializer.Serialize(lst);
                        //t.data = data;
                        //string strNewDate = serializer.Serialize(t1);
                        //string teststring = "{\"iTotalRecords\":" + dtNew.Rows.Count + "," + "\"iTotalDisplayRecords\":" + dtNew.Rows.Count + "," + strNewDate.TrimStart('{');
                        //return JsonConvert.SerializeObject(teststring);
                        ////return Json(teststring, JsonRequestBehavior.AllowGet);
                        ////JSONString = Newtonsoft.Json.JsonConvert.SerializeObject(dt)

                    }
                }
                //return JsonConvert.SerializeObject(JSONString);
                return new DataTable();
                //return Json(JSONString, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Inside CustomTemplateController class, Method name : GenerateCustomTemplateData. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return new DataTable();
                //return JsonConvert.SerializeObject(JSONString);
                //return Json(JSONString, JsonRequestBehavior.AllowGet);
            }
        }
        public string GetCustomTemplateFile(int QuoteId, DataTable dt)
        {
            DataTable CustomTemplateDt = new DataTable();
            CustomTemplateDt = dt;
            //TempData.Keep("CustomeTemplateDt");
            //TempData["CustomeTemplateDt"] = CustomTemplateDt;
            DataTable excelDt = new DataTable();
            string filePath = "";
            string fileShortName = "";
            //int QuoteId = 0;
            try
            {
                if (!string.IsNullOrWhiteSpace(QuoteId.ToString()))
                {
                    QuoteId = Convert.ToInt32(QuoteId.ToString());
                    //int BOMManagerId = Convert.ToInt32(Request.QueryString["BOM"].ToString());
                    if (QuoteId > 0)
                    {
                        //code here
                        List<BOMManagerContract> BOMList = BOMManagerContract.GetBOMListForCustomerRequirement(QuoteId, SessionManager.ClientID, 1, 10, "", 1);

                        excelDt = DownloadBOMFile(BOMList[0].GeneratedFilename.ToString(), true);
                        filePath = FileUploadManager.GetTemporaryUploadFilePath() + BOMList[0].GeneratedFilename.ToString();
                        fileShortName = "QuoteNo_" + BOMList[0].QuoteNumber.ToString();
                    }
                }

                //string filePath = @"C:\Path\To\Your\Excel\File.xlsx";
                // Load the existing Excel file using EPPlus
                //using (var package = new ExcelPackage(new System.IO.FileInfo(filePath)))
                #region  to clear the excel file
                //using (var package = new ExcelPackage(new System.IO.FileInfo(filePath)))
                //{
                //    // Loop through all worksheets in the workbook
                //    foreach (var sheet in package.Workbook.Worksheets)
                //    {
                //        // Get the dimension of the worksheet
                //        var dimension = sheet.Dimension;

                //        if (dimension != null)
                //        {
                //            // Clear the content of each cell in the worksheet
                //            for (int row = dimension.Start.Row; row <= dimension.End.Row; row++)
                //            {
                //                for (int col = dimension.Start.Column; col <= dimension.End.Column; col++)
                //                {
                //                    var cell = sheet.Cells[row, col];
                //                    cell.Value = null; // Clear cell content
                //                }
                //            }
                //        }
                //    }

                //    // Save the changes back to the Excel file
                //    package.Save();
                //}
                #endregion  to clear the excel file
                #region to create row from column names
                DataTable transposedDataTable = new DataTable();
                transposedDataTable.Columns.Add("Column Name");

                // Add rows with column names
                foreach (DataColumn column in CustomTemplateDt.Columns)
                {
                    DataRow newRow = transposedDataTable.NewRow();
                    newRow["Column Name"] = column.ColumnName;
                    transposedDataTable.Rows.Add(newRow);
                }
                DataRow columnNamesRow = CustomTemplateDt.NewRow();
                //CustomTemplateDt.Columns.Remove("LineNo");

                // Populate the row with column names
                foreach (DataColumn column in CustomTemplateDt.Columns)
                {
                    columnNamesRow[column.ColumnName] = column.ColumnName;
                }


                // Add header the row to the DataTable
                if (!ContainDataRowInDataTable(CustomTemplateDt, columnNamesRow))
                {
                    CustomTemplateDt.Rows.InsertAt(columnNamesRow, 0);
                    //TempData.Keep("headerAdded");
                }

                // Display the DataTable with column names as the first row
                foreach (DataRow row in CustomTemplateDt.Rows)
                {
                    foreach (var item in row.ItemArray)
                    {
                        Console.Write(item + "\t");
                    }
                    Console.WriteLine();
                }
                #endregion to create row from column names
                // Create a new Excel package
                using (var package = new ExcelPackage())
                {
                    // Add a worksheet to the Excel package
                    var worksheet = package.Workbook.Worksheets.Add("Sheet1");
                    int rowid = 0;
                    // Write the DataTable data to the worksheet
                    for (int rowIndex = 0; rowIndex < CustomTemplateDt.Rows.Count; rowIndex++)
                    {
                        for (int colIndex = 0; colIndex < CustomTemplateDt.Columns.Count; colIndex++)
                        {
                            worksheet.Cells[rowIndex + 1, colIndex + 1].Value = CustomTemplateDt.Rows[rowIndex][colIndex];
                        }
                        rowid = rowIndex;
                    }
                    BLL.Quote qt = Quote.GetForPrint(QuoteId);
                    AddFooterData(worksheet, qt, rowid + 1);
                    string TCfilepat = FileUploadManager.GetTnCUploadFilePath() + "Quote_TC.txt";
                    string text = System.IO.File.ReadAllText(TCfilepat);
                    var worksheetTC = package.Workbook.Worksheets.Add("Ts & Cs");
                    string[] lines = text.Split(new[] { Environment.NewLine }, StringSplitOptions.None);
                    var linelist = text.Split(new[] { Environment.NewLine }, StringSplitOptions.None).ToList();
                    for (int rowIndex = 0; rowIndex < linelist.Count; rowIndex++)
                    {
                        worksheetTC.Cells[rowIndex + 1, 1].Value = linelist[rowIndex].ToString();
                        //for (int colIndex = 0; colIndex < linelist.Count; colIndex++)
                        //{
                        //    worksheetTC.Cells[rowIndex + 1, colIndex + 1].Value = linelist[rowIndex].ToString();
                        //}
                        //   rowid = rowIndex;
                    }

                    // Save the Excel package to a file
                    //string filePath = @"C:\Path\To\Your\Output\Workbook.xlsx";
                    //renmaming the file 

                    package.SaveAs(new System.IO.FileInfo(filePath));
                    File.Move(filePath, FileUploadManager.GetTemporaryUploadFilePath() + fileShortName + Path.GetExtension(filePath));
                    filePath = FileUploadManager.GetTemporaryUploadFilePath() + fileShortName + Path.GetExtension(filePath);
                    //byte[] fileBytes = System.IO.File.ReadAllBytes(filePath);
                    //string fileName = "myfile.ext";
                    //return File(fileBytes, System.Net.Mime.MediaTypeNames.Application.Octet, fileName);
                    //path = Server.MapPath("~/User/CSV/") + FilePath;
                    return filePath;

                    //byte[] bytes = System.IO.File.ReadAllBytes(filePath);
                    //System.IO.File.Delete(filePath);
                    //return JsonConvert.SerializeObject(new
                    //{
                    //    Base64String = Convert.ToBase64String(bytes, 0, bytes.Length),
                    //    fileName = fileShortName + System.IO.Path.GetExtension(filePath)
                    //});
                    //return Json(new
                    //{
                    //    Base64String = Convert.ToBase64String(bytes, 0, bytes.Length),
                    //    fileName = fileShortName + System.IO.Path.GetExtension(filePath)
                    //}, JsonRequestBehavior.AllowGet);
                }
            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Inside CustomTemplateController class, Method name : GetCustomTemplateFile. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return JsonConvert.SerializeObject("");
                //return Json("", JsonRequestBehavior.AllowGet);
            }

        }

        private bool ContainDataRowInDataTable(DataTable T, DataRow R)
        {
            foreach (DataRow item in T.Rows)
            {
                if (Enumerable.SequenceEqual(item.ItemArray, R.ItemArray))
                    return true;
            }
            return false;
        }
        private void AddFooterData(ExcelWorksheet ws, Quote quote, int startRowIndex)
        {
            startRowIndex = startRowIndex + 2;
            string strRowIdex = startRowIndex.ToString();
            string strHazardousPrint = "";
            if (string.IsNullOrEmpty(quote.SysDocHazardousHistoryText))
                strHazardousPrint = GetHazardousNotes(SystemDocument.FooterDocument.PrintHazardous, (int)SessionManager.ClientID);
            else
                strHazardousPrint = quote.SysDocHazardousHistoryText;
            string footerNote = string.Empty;
            //if (!string.IsNullOrEmpty(strHazardousPrint))
            //{
            //    string hazardousNote = "## " + strHazardousPrint;
            //    footerNote = footerNote + hazardousNote;
            //}
            string strFooter = "";
            if (string.IsNullOrEmpty(quote.FooterTextQuote))
                strFooter = GetFooterNotes(SystemDocument.ListForPrint.Quote);
            else
                strFooter = quote.FooterTextQuote;
            if (!string.IsNullOrEmpty(strFooter))
            {
                footerNote = footerNote + (!string.IsNullOrEmpty(footerNote) ? Environment.NewLine : "");
                footerNote = footerNote + strFooter;
            }
            if (Convert.ToBoolean(quote.AS9120))
            {
                string strFooterAS9120 = "";
                if (string.IsNullOrEmpty(quote.SysDocAS9120HistoryText))
                    strFooterAS9120 = GetFooterNotes(SystemDocument.ListForPrint.AS9120);
                else
                    strFooterAS9120 = quote.SysDocAS9120HistoryText;
                if (!string.IsNullOrEmpty(strFooterAS9120))
                {
                    footerNote = footerNote + (!string.IsNullOrEmpty(footerNote) ? Environment.NewLine : "");
                    footerNote = footerNote + strFooterAS9120;
                }
            }
            ws.Cells["A" + strRowIdex + ":" + "M" + strRowIdex].Value = footerNote;
            ws.Cells["A" + strRowIdex + ":" + "M" + strRowIdex].Merge = true;
            ws.Cells["A" + strRowIdex + ":" + "M" + strRowIdex].Style.WrapText = true;
            ws.Cells["A" + strRowIdex + ":" + "M" + strRowIdex].Style.Font.Name = "Tahoma";
            ws.Cells["A" + strRowIdex + ":" + "M" + strRowIdex].Style.Font.Size = smallFontSize;
            ws.Row(startRowIndex).Style.VerticalAlignment = ExcelVerticalAlignment.Top;
            ws.Row(startRowIndex).Height = 600;


        }
        int sFontSize = 8;
        int mFontSize = 9;
        private int smallFontSize { get { return sFontSize; } set { sFontSize = value; } }
        private string GetFooterNotes(SystemDocument.ListForPrint enmSystemDocument)
        {
            string strNotes = "";
            BLL.SystemDocumentFooter ft = BLL.SystemDocumentFooter.GetForClientAndDocument((int)SessionManager.ClientID, (int)enmSystemDocument);
            if (ft != null) strNotes = (String.IsNullOrEmpty(ft.FooterText)) ? "" : ft.FooterText;
            ft = null;
            return strNotes;
        }
        private string GetHazardousNotes(SystemDocument.FooterDocument enmSystemDocument, int ClientNo)
        {
            string strNotes = "";
            BLL.SystemDocumentFooter ft = BLL.SystemDocumentFooter.GetForClientAndDocument((int)ClientNo, (int)enmSystemDocument);
            if (ft != null) strNotes = (String.IsNullOrEmpty(ft.FooterText)) ? "" : ft.FooterText;
            ft = null;
            return strNotes;
        }
        public DataTable DownloadBOMFile(string GeneratedFilename, bool ReadOnly = false)
        {

            string accountname = ConfigurationManager.AppSettings.Get("StorageName");
            string accesskey = ConfigurationManager.AppSettings.Get("StorageKey1");

            /*Downloading excel file and reading it to show all data in grid*/

            StorageCredentials creden = new StorageCredentials(accountname, accesskey);
            CloudStorageAccount acc = new CloudStorageAccount(creden, useHttps: true);
            CloudBlobClient client = acc.CreateCloudBlobClient();
            CloudBlobContainer cont = client.GetContainerReference("gtdocmgmt");
            string strDir = FileUploadManager.GetTemporaryUploadFilePath();
            DataTable excelDt = new DataTable();
            if (cont.Exists())
            {
                CloudBlobDirectory directory = cont.GetDirectoryReference("UTILITYBOMMANAGER");
                //CloudBlockBlob cblob = directory.GetBlockBlobReference(GetFormValue_String("generatedFilename"));
                CloudBlockBlob cblob = directory.GetBlockBlobReference(GeneratedFilename.ToString());
                if (cblob.Exists())
                {
                    using (var fileStream = System.IO.File.OpenWrite(strDir + GeneratedFilename))
                    {
                        cblob.DownloadToStream(fileStream);
                    }
                }
                if (ReadOnly == true)
                    //excelDt = ConvertExcelToDataTableToRead(strDir + GeneratedFilename, "YES", GeneratedFilename);
                    //string abc = "";
                    excelDt = new DataTable();
                else
                {
                    excelDt = ConvertExcelToDataTableNew(strDir + GeneratedFilename, "YES", GeneratedFilename);

                    _excelDt = excelDt;
                }

            }
            return excelDt;
        }
        private DataTable ConvertExcelToDataTableNew(string FilePath, string chkhead, string FileName)
        {
            DataTable dt = new DataTable();
            FileInfo fi = new FileInfo(FilePath);
            try
            {
                if (fi.Exists)
                {
                    List<string> sheets = ExcelAdapter.GetSheet(FilePath);
                    if (sheets.Count > 0)
                    {
                        if (chkhead == "YES")
                        { dt = ExcelAdapter.ReadExcel(FilePath, sheets[0]); }
                        else
                        {
                            dt = ExcelAdapter.ReadExcel(FilePath, sheets[0], false);
                            int i = 1;
                            int c = 0;
                            foreach (DataColumn column in dt.Columns)
                            {
                                if (i <= 15)
                                {
                                    dt.Columns[c].ColumnName = "F" + i;
                                }
                                ++i;
                                ++c;

                            }
                        }

                    }
                }
                string Deletetempfolderfile = FileUploadManager.GetTemporaryUploadFilePath() + FileName;
                System.IO.File.Delete(Deletetempfolderfile);

            }
            catch (Exception ex)
            {
                string Deletetempfolderfile = FileUploadManager.GetTemporaryUploadFilePath() + FileName;
                System.IO.File.Delete(Deletetempfolderfile);
                if (ex.Message == "Column '' does not belong to table .")
                    throw new Exception("Failed to upload data. Please check spreadsheet and ensure column headings are set as the first row", new Exception("ExcelDataError"));
                else
                    throw new Exception("Error Occured while processing the excel file.Kindly review the data in the excel file.", new Exception("ExcelDataError"));
            }
            return dt;
        }
    }
}