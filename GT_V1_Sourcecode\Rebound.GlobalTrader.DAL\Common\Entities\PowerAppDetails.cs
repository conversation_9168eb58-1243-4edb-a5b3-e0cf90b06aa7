﻿//Marker     changed by      date          Remarks
//[001]      <PERSON><PERSON><PERSON><PERSON>   28-Jul-2021   Add new property for repeat order
using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
namespace Rebound.GlobalTrader.DAL
{
    public class PowerAppDetails
    {
        #region Constructors

        public PowerAppDetails() { }

        #endregion



        #region Properties
        public System.Int32? SOId { get; set; }
        public System.Boolean? IsNotifySO { get; set; }
        public System.Int32? LoginId { get; set; }
        public System.String LoginName { get; set; }
        public System.DateTime? LastVisited { get; set; }
        public System.String ReportName { get; set; }

        public System.Int32? SalesOrderNo { get; set; }
        public System.Int32? SalesOrderNumber { get; set; }
        public System.Int32? Salesman { get; set; }
        public System.String SalesmanName { get; set; }
        public System.Int32? SOSerialNo { get; set; }
        public System.Int32? ExportApprovalStatusId { get; set; }
        public System.String ExportApprovalStatus { get; set; }
        public System.Int32? ExportApprovalId { get; set; }
        public System.Boolean? Result { get; set; }
        public System.String FlowUrl { get; set; }
        #endregion
    }
}
