﻿GO

/****** Object:  StoredProcedure [dbo].[usp_Get_LyticaAPIData_ByKey]    Script Date: 10/24/2024 11:05:16 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-208862]		Trung Pham			15-Jul-2024		CREATE			Refresh lytica data when selected/opened requirement after 3 days
===========================================================================================
*/

CREATE OR ALTER PROCEDURE [dbo].[usp_Get_LyticaAPIData_ByKey]
(
@partNo VARCHAR(256),
@mfrCode VARCHAR(100)
)
AS
BEGIN
	SELECT 1 FROM tbLyticaAPI l
	INNER JOIN tbManufacturer m ON l.Manufacturer = m.ManufacturerName
	WHERE l.OriginalPartSearched = @partNo AND m.ManufacturerCode = @mfrCode AND GETDATE() >= CAST(DATEADD(DAY, 3, l.DLUP) AS DATE)
	ORDER BY l.DLUP DESC
END
GO
