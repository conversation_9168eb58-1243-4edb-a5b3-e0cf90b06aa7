﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.IO;
using System.Web;

namespace Rebound.GlobalTrader.Site
{
    internal class Errorlog
    {
        public void LogMessage(string message)
        {
            if (System.Configuration.ConfigurationManager.AppSettings["EnableLog"] != null)
            {
                bool enableLog = false;
                if (System.Configuration.ConfigurationManager.AppSettings["EnableLog"].ToString().ToLower() == "true")
                    enableLog = true;

                if (enableLog && System.Configuration.ConfigurationManager.AppSettings["LogDirectory"] != null)
                {
                    string logDirectory = HttpContext.Current.Server.MapPath(String.Format(System.Configuration.ConfigurationManager.AppSettings["LogDirectory"])); //System.Configuration.ConfigurationManager.AppSettings["LogDirectory"];
                    if (!string.IsNullOrEmpty(logDirectory))
                    {
                        if (!Directory.Exists(logDirectory))
                            Directory.CreateDirectory(logDirectory);

                        //string fileName = "Log_" + string.Format("{0:yyyy-MM-dd_hh-mm-ss}", DateTime.Now) + Guid.NewGuid().ToString() + ".log";
                        string fileName = "Log_" + string.Format("{0:yyyy-MM-dd}", DateTime.Now) + ".log";
                        string filePath = Path.Combine(logDirectory, fileName);

                        message += System.Environment.NewLine + DateTime.Now;
                        message += System.Environment.NewLine;
                        message += "----------------------------------------------------------------";
                        message += System.Environment.NewLine;
                        message += System.Environment.NewLine;

                        FileStream fs = new FileStream(filePath, FileMode.Append, FileAccess.Write);
                        StreamWriter writer = new StreamWriter(fs);
                        writer.Write(message);
                        writer.Close();
                    }
                }
            }
        }
        public void LogUserMessage(string message, string fileName)
        {
            if (System.Configuration.ConfigurationManager.AppSettings["EnableLog"] != null)
            {
                bool enableLog = false;
                if (System.Configuration.ConfigurationManager.AppSettings["EnableLog"].ToString().ToLower() == "true")
                    enableLog = false;

                if (enableLog && System.Configuration.ConfigurationManager.AppSettings["LogDirectory"] != null)
                {
                    string logDirectory = HttpContext.Current.Server.MapPath(String.Format(System.Configuration.ConfigurationManager.AppSettings["LogDirectory"]));
                    //string logDirectory = System.Configuration.ConfigurationManager.AppSettings["LogDirectory"];
                    if (!string.IsNullOrEmpty(logDirectory))
                    {
                        if (!Directory.Exists(logDirectory))
                            Directory.CreateDirectory(logDirectory);

                        //string fileName = "Log_For_" + strLoginId + string.Format("{0:yyyy-MM-dd_hh-mm-ss}", DateTime.Now)+ ".log";
                        string filePath = Path.Combine(logDirectory, fileName);

                        message += System.Environment.NewLine + DateTime.Now;
                        message += System.Environment.NewLine;
                        message += "----------------------------------------------------------------";
                        message += System.Environment.NewLine;
                        message += System.Environment.NewLine;
                        FileStream fs = fs = new FileStream(filePath, FileMode.Append, FileAccess.Write);
                        StreamWriter writer = new StreamWriter(fs);
                        writer.Write(message);
                        writer.Close();
                    }
                }
            }
        }
    }
}