CREATE OR ALTER PROCEDURE [dbo].[usp_kpi_getClientWiseCustomerRevenueSummary]
    @ClientNo int,
    @StartDate datetime = null,
    @EndDate datetime = null,
    @IncludeCredits bit = 1,
    @ViewMyReport bit = 0,
    @IntLoginId int = NULL,
    @IntTeamNo int = NULL,
    @IntDivisionNo int = NULL,
    @invMonth int = 0,
    @yearNo int = 0
AS
SET NOCOUNT ON
SET ANSI_WARNINGS OFF
SET ARITHIGNORE ON

-- Create the temporary tables    
CREATE TABLE #Invoices
(
    InvoiceID int,
    CurrencyRate float,
    CompanyNo int,
    ShippingCost float,
    Freight float
)

CREATE TABLE #InvoicePreSummary
(
    CompanyNo int,
    InvoiceID int,
    ShippingCost float,
    Freight float,
    Cost float,
    Resale float
)

CREATE TABLE #InvoiceSummary
(
    CompanyNo int,
    Cost float,
    Resale float
)

CREATE TABLE #Credits
(
    CreditNo int,
    CompanyNo int,
    CurrencyRate float
)

-- Put the list of invoices into a temporary table    
INSERT INTO #Invoices
SELECT InvoiceID,
       dbo.ufn_get_exchange_rate(a.CurrencyNo, a.InvoiceDate),
       a.CompanyNo,
       isnull(a.ShippingCost, 0),
       isnull(a.Freight, 0)
FROM dbo.tbInvoice a
    LEFT JOIN tbLogin lg
        on lg.LoginId = a.Salesman
WHERE a.ClientNo = @ClientNo
      --VIEW REPORT--  
      -- AND ((@ViewMyReport = 0)   OR (@ViewMyReport = 1   AND a.Salesman=@IntLoginId))   
      --Espire 28 Dec 2018:  REB12439 - Team and Division reports  
      AND (
              (@IntTeamNo IS NULL)
              OR (
                     NOT @IntTeamNo IS NULL
                     AND lg.TeamNo = @IntTeamNo
                 )
          )
      AND (
              (@IntDivisionNo IS NULL)
              OR (
                     NOT @IntDivisionNo IS NULL
                     AND lg.DivisionNo = @IntDivisionNo
                 )
          )
      AND (
              (@IntLoginId IS NULL)
              OR (
                     NOT @IntLoginId IS NULL
                     AND (
                             a.Salesman = @IntLoginId
                             OR (
                                    a.Salesman2 = @IntLoginId
                                    AND a.Salesman2Percent > 0
                                )
                         )
                 )
          )
      -------END -----   
      AND dbo.ufn_get_date_from_datetime(a.InvoiceDate)
      BETWEEN @StartDate AND @EndDate
      AND a.SupplierRMANo IS NULL

--prepare the summary of invoices - necessary to get the shipping and freight once per invoice     
INSERT INTO #InvoicePreSummary
SELECT i.CompanyNo,
       i.InvoiceID,
       i.ShippingCost,
       i.Freight / i.CurrencyRate,
       isnull(sum(ila.LandedCost * ila.Quantity), 0),
       isnull(sum((ila.Price * ila.Quantity) / i.CurrencyRate), 0)
FROM #Invoices i
    LEFT JOIN dbo.tbInvoiceLine il
        ON il.InvoiceNo = i.InvoiceId
    LEFT JOIN dbo.vwInvoiceLineAllocation ila
        ON ila.InvoiceLineNo = il.InvoiceLineId
GROUP BY i.CompanyNo,
         i.InvoiceID,
         i.ShippingCost,
         i.Freight / i.CurrencyRate

--summarise the invoices    
INSERT INTO #InvoiceSummary
SELECT CompanyNo,
       sum(Cost) + sum(ShippingCost),
       sum(Resale) + sum(Freight)
FROM #InvoicePreSummary
GROUP BY CompanyNo

--minus credits if required    
IF @IncludeCredits = 1
BEGIN

    CREATE TABLE #CreditSummary
    (
        CompanyNo int,
        Cost float,
        Resale float
    )

    --get all credits    
    INSERT INTO #Credits
    SELECT c.CreditId,
           c.CompanyNo,
           dbo.ufn_get_exchange_rate(c.CurrencyNo, c.InvoiceDate)
    FROM dbo.vwCredit c
        LEFT JOIN tbLogin lg
            on lg.LoginId = c.Salesman
    WHERE c.ClientNo = @ClientNo
          --VIEW REPORT--  
          -- AND ((@ViewMyReport = 0)   OR (@ViewMyReport = 1   AND Salesman=@IntLoginId))   
          --Espire 28 Dec 2018:  REB12439 - Team and Division reports  
          AND (
                  (@IntTeamNo IS NULL)
                  OR (
                         NOT @IntTeamNo IS NULL
                         AND lg.TeamNo = @IntTeamNo
                     )
              )
          AND (
                  (@IntDivisionNo IS NULL)
                  OR (
                         NOT @IntDivisionNo IS NULL
                         AND lg.DivisionNo = @IntDivisionNo
                     )
              )
          AND (
                  (@IntLoginId IS NULL)
                  OR (
                         NOT @IntLoginId IS NULL
                         AND (Salesman = @IntLoginId)
                     )
              )
          -------END -----  
          AND dbo.ufn_get_date_from_datetime(c.CreditDate)
          BETWEEN @StartDate AND @EndDate
    --summarise credits                                                                    
    INSERT INTO #CreditSummary
    SELECT cr.CompanyNo,
           isnull(sum(cr.CreditCost + cr.ShippingCost), 0),
           isnull(sum((cr.CreditValue + cr.Freight) / c.CurrencyRate), 0)
    FROM #Credits c
        LEFT JOIN dbo.vwCredit cr
            ON cr.CreditId = c.CreditNo
    GROUP BY cr.CompanyNo

    --subtract credits from invoices    
    UPDATE #InvoiceSummary
    SET Cost = i.Cost - c.Cost,
        Resale = i.Resale - c.Resale
    FROM #InvoiceSummary i
        JOIN #CreditSummary c
            ON c.CompanyNo = i.CompanyNo

    --add companies with no Invoices but some Credits    
    INSERT INTO #InvoiceSummary
    SELECT c.CompanyNo,
           -c.Cost,
           -c.Resale
    FROM #CreditSummary c
    WHERE NOT EXISTS
    (
        SELECT 1 FROM #Invoices WHERE CompanyNo = c.CompanyNo
    )

    DROP TABLE #CreditSummary
END

--return the data    
;
with CTE_KPICustomerRevenueSummary
as (SELECT @ClientNo AS ClientNo,
           co.CompanyName,
           co.CompanyId,
           co.Salesman,
           i.Cost,
           i.Resale,
           i.Resale - i.Cost AS GrossProfit,
           case i.Resale
               WHEN 0 THEN
                   -100
               ELSE
           ((i.Resale - i.Cost) / abs(i.Resale)) * 100
           END AS Margin,
           (
               SELECT count(*) FROM #Invoices WHERE CompanyNo = i.CompanyNo
           ) AS NoOfOrders,
           (
               SELECT count(*) FROM #Credits WHERE CompanyNo = i.CompanyNo
           ) AS NoOfCredits,
           @invMonth as invMonth,
           @yearNo as yearNo
    FROM #InvoiceSummary i
        JOIN tbCompany co
            ON co.CompanyId = i.CompanyNo
   )
INSERT INTO tbKPICustomersRevenueSummary
(
    ClientNo,
    CompanyName,
    CompanyId,
    Salesman,
    Cost,
    Resale,
    GrossProfit,
    Margin,
    NoOfOrders,
    NoOfCredits,
    invMonth,
    yearNo
)
SELECT *
from CTE_KPICustomerRevenueSummary nolock

DROP TABLE #Credits
DROP TABLE #Invoices
DROP TABLE #InvoiceSummary
DROP TABLE #InvoicePreSummary








