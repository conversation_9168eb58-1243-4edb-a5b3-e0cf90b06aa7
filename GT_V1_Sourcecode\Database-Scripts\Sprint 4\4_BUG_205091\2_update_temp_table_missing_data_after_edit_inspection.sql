GO
/* 
===========================================================================================
TASK      			UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[BUG-205091]		Trung Pham			20-Jun-2024		FIX				205091: [PROD Bug] The unstable data is missing in temp table. Copy from live table to temp table in order to consist
																		Update records that have inspection status = 1 means inprogress/ start inspection
===========================================================================================
*/


UPDATE tbGI_TempInspectionData
SET GeneralInspectionNotes = live.GeneralInspectionNotes,
	PrintableDC = live.PrintableDC
FROM tbGoodsInLine live 
INNER JOIN tbGI_TempInspectionData temp ON temp.GoodsInLineId = live.GoodsInLineId
WHERE live.InspectionStatus = 1 and (ISNULL(temp.GeneralInspectionNotes, '') != live.GeneralInspectionNotes
or ISNULL(temp.PrintableDC, '') != live.PrintableDC)