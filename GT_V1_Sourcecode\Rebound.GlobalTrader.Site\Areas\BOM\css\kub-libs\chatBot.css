.chat-screen {
    position: fixed;
    right: 0px;
    z-index: 9999;
    width: 100%;
    background: #fff;
    box-sizing: border-box;
    border-radius: 10px 10px 0px 0px;
    box-shadow: 0px 15px 20px rgba(0, 0, 0, 0.1);
    visibility: hidden;
}
.chat-screen label {
    font-weight: bold;
    text-decoration: underline;
}
.chat-screen.show-chat {
    -moz-transition: bottom 0.5s linear;
    -webkit-transition: bottom 0.5s linear;
    transition: bottom 0.5s linear;
    visibility: visible;
    bottom: 60px;
}

    .chat-screen .chat-header {
        background-color: #A6F99D;
        border-radius: 10px 10px 0 0;
        padding: 11px 10px;
        display: block;
    }

.head_text {
    color: #56954E;
    font-size:14px;
    font-weight:bold;
}
.chat-screen .chat-header .chat-header-title {
    display: inline-block;
    width: calc(100% - 50px);
    color: #fff;
    font-size: 14px;
}
.chat-screen .chat-header .chat-header-option {
    display: inline-block;
    width: 44px;
    color: #fff;
    font-size: 14px;
    text-align: right;
}
.chat-screen .chat-header .chat-header-option .dropdown .dropdown-toggle svg {
    color: #fff;
}
.chat-screen .chat-mail {
    padding: 30px;
    display: block;
}
.chat-screen .chat-mail input.form-control {
    border-radius: 30px;
    border: 1px solid #e1e1e1;
    color: #3b3f5c;
    font-size: 14px;
    padding: 0.55rem 1.25rem;
}
.chat-screen .chat-mail input.form-control:focus {
    box-shadow: none;
    border: 1px solid #add5fc;
}
.chat-screen .chat-mail .select2 .selection .select2-selection .select2-selection__rendered {
    border-radius: 30px;
    border: 1px solid #e1e1e1;
    height: calc(1.28em + 1.28rem + 2px);
    padding: 9px 20px;
    font-size: 14px;
}
.chat-screen .chat-mail .select2.select2-container--open .selection .select2-selection {
    box-shadow: none;
    border-radius: 30px;
}
.chat-screen .chat-mail button {
    background-image: linear-gradient(to right, #673ab7, #813bcb, #9e38de, #bc32ef, #dc22ff);
    border: none;
    padding: 0.58rem 1.25rem;
    transition: transform 0.5s ease;
}
.chat-screen .chat-mail .form-group {
    margin-bottom: 1.5rem;
}
.chat-screen .chat-body {
    padding: 10px;
    max-height: 392px;
    background: #f9f9f9;
    overflow-y:auto;
}
.chat-screen .chat-body .chat-start {
    border: 1px solid #f8d4ff;
    width: 150px;
    border-radius: 50px;
    padding: 6px 10px;
    font-size: 12px;
    margin: 0 auto;
    text-align: center;
    margin-bottom: 15px;
    background: #fff;
}
.chat-screen .chat-body .chat-bubble {
    font-size: 12px;
    padding: 10px 15px;
    box-shadow: none;
    display: inline-block;
    clear: both;
    margin-bottom: 10px;
    box-shadow: 0 5px 5px rgba(0, 0, 0, 0.02);
}
.chat-screen .chat-body .chat-bubble.you {
    background-image: linear-gradient(to right, #673ab7, #813bcb, #9e38de, #bc32ef, #dc22ff);
    color: #fff;
    border-radius: 0 15px 15px 15px;
    align-self: flex-start;
    display: table;
}
.chat-screen .chat-body .chat-bubble.me {
    background-image: linear-gradient(to right, #FFFFFF, #FFFFFF, #FFFFFF, #FFFFFF, #FFFFFF);
    color: #888ea8;
    border-radius: 15px 0px 15px 15px;
    float: right;
    align-self: flex-end;
    display: table;
}
.chat-screen .chat-input {
    width: 100%;
    position: relative;
    margin-bottom: -5px;
}
.chat-screen .chat-input input {
    width: 100%;
    background: #ffffff;
    padding: 15px 70px 15px 15px;
    border-radius: 0 0 15px 15px;
    resize: none;
    border-width: 1px 0 0 0;
    border-style: solid;
    border-color: #f8f8f8;
    color: #7a7a7a;
    font-weight: normal;
    font-size: 13px;
    transition: border-color 0.5s ease;
}
.chat-screen .chat-input input:focus {
    border-color: #f9dcff;
}
.chat-screen .chat-input input:focus + .input-action-icon a svg.feather-send {
    color: #bc32ef;
}
.chat-screen .chat-input .input-action-icon {
    width: 61px;
    white-space: nowrap;
    position: absolute;
    z-index: 1;
    top: 15px;
    right: 15px;
    text-align: right;
}
.chat-screen .chat-input .input-action-icon a {
    display: inline-block;
    margin-left: 5px;
    cursor: pointer;
}
.chat-screen .chat-input .input-action-icon a svg {
    height: 17px;
    width: 17px;
    color: #a9a9a9;
}
.chat-screen .chat-session-end {
    display: block;
    width: 100%;
    padding: 25px;
}
.chat-screen .chat-session-end h5 {
    font-size: 17px;
    text-align: center;
    font-weight: bold;
    margin-top: 20px;
}
.chat-screen .chat-session-end p {
    font-size: 14px;
    text-align: center;
    margin: 20px 0;
}
.chat-screen .chat-session-end .rate-me {
    width: 120px;
    margin: 40px auto;
}
.chat-screen .chat-session-end .rate-me .rate-bubble {
    display: inline-block;
    text-align: center;
    width: 50px;
}
.chat-screen .chat-session-end .rate-me .rate-bubble span {
    height: 50px;
    width: 50px;
    text-align: center;
    display: block;
    line-height: 46px;
    cursor: pointer;
    transition: transform 0.5s ease;
    margin-bottom: 7px;
}
.chat-screen .chat-session-end .rate-me .rate-bubble span:hover {
    transform: scale(1.1);
    transition: transform 0.5s ease;
}
.chat-screen .chat-session-end .rate-me .rate-bubble.great {
    margin-right: 12px;
    color: #43cc6c;
}
.chat-screen .chat-session-end .rate-me .rate-bubble.great span {
    background: #43cc6c;
    border-radius: 50px 50px 0 50px;
}
.chat-screen .chat-session-end .rate-me .rate-bubble.bad {
    color: #ef4252;
}
.chat-screen .chat-session-end .rate-me .rate-bubble.bad span {
    background: #ef4252;
    border-radius: 50px 50px 50px 0;
}
.chat-screen .chat-session-end .transcript-chat {
    display: block;
    text-align: center;
    margin-top: 80px;
    color: #0768f8;
    text-decoration: underline;
    line-height: 20px;
}
.chat-screen .powered-by {
    margin-top: 40px;
    text-align: center;
    font-size: 12px;
}
.chat-bot-icon1 {
    position: fixed;
    bottom: 6px;
    right: 5px;
    height: 50px;
    width: 50px;
    z-index: 9999;
    text-align: center;
    line-height: 50px;
    cursor: pointer;
    background-image: url('../../../../images/Kub/AddNewRequirementPage/KUB1.svg');
    background-repeat: no-repeat;
}

.chat-bot-icon {
    position: fixed;
    bottom: 6px;
    right: 5px;
    height: 50px;
    width: 50px;
    z-index: 9999;
    text-align: center;
    line-height: 50px;
    cursor: pointer;
    background-image: url('../../../../images/Kub/AddNewRequirementPage/KUB.svg');
    background-repeat: no-repeat;
    animation: up-down 7.5s infinite ease-in-out;
}

.bot_text {
    margin-left: 34px;
    color: #56954E;
    font-weight:bold;
    text-decoration:underline;
}

.chat-bot-icon img {
    height: 90px;
    width: 90px;
    position: absolute;
    right: -13px;
    top: -33px;
}
.chat-bot-icon svg {
    color: #fff;
    
    position: absolute;
    left: 13px;
    top: 9px;
    opacity:1;
    z-index: -1;
}
.chat-bot-icon svg.animate {
   
    opacity: 1;
    z-index: 1;
}




/*kub icon animation css starts*/

    /*.container #chatbot {
        margin: auto;
        position: absolute;
        bottom: 0;
        left: 0;
        top: 0;
        right: 0;*/
        /*border: 12px solid #3D3E45;
    border-radius: 5rem;*/
        /*background-image: url(../img/KUB.svg);
        background-repeat: no-repeat;
        background-size: 170px 459px;
        background-position: top;
    }*/

   

    .container #antenna {
        margin: auto;
        position: absolute;
        bottom: 0;
        left: 0;
        top: 0;
        right: 0;
        height: 20px;
        width: 10px;
        animation: antenna-appear 7.5s infinite ease-in-out;
    }

        .container #antenna #beam-pulsar {
            position: absolute;
            top: -12.5px;
            left: -5px;
            height: 16px;
            width: 16px;
            border-radius: 50%;
            background-color: #9D0026;
            animation: beam-pulsar-appear 7.5s infinite ease-in-out;
        }

.container .dot1 {
    height: 4px;
    width: 4px;
    margin: auto;
    position: absolute;
    bottom: 0;
    left: 0;
    top: 9px;
    right: 0;
    left: -17px;
    background-color: #C5C5C5;
    border-radius: 50%;
}

.container .dot1:nth-child(2) {
    left: 0;
}

.container .dot1:nth-child(3) {
    left: 17px;
}

    .container .dot {
        height: 4px;
        width: 4px;
        margin: auto;
        position: absolute;
        bottom: 0;
        left: 0;
        top: 9px;
        right: 0;
        left: -17px;
        background-color: #3D3E45;
        border-radius: 50%;
        animation: pulse-outer 7.5s infinite ease-in-out;
    }

        .container .dot:nth-child(2) {
            left: 0;
            animation: pulse-inner 7.5s infinite ease-in-out;
            animation-delay: 0.2s;
        }

        .container .dot:nth-child(3) {
            left: 17px;
            animation: pulse-outer 7.5s infinite ease-in-out;
            animation-delay: 0.4s;
        }

@keyframes pulse-inner {
    0% {
        transform: scale(1);
    }

    7.5% {
        transform: scale(1.5);
    }

    15% {
        transform: scale(1);
    }

    22.5% {
        transform: scale(1.5);
    }

    30% {
        transform: scale(1);
    }

    37.5% {
        transform: scale(1.5);
    }

    45% {
        top: 9px;
        transform: scale(1);
        height: 4px;
        border-bottom-left-radius: 50%;
        border-bottom-right-radius: 50%;
        transform: rotate(-370deg);
    }

    50% {
        top: 22.5px;
        height: 4px;
        border-top-left-radius: 50%;
        border-top-right-radius: 50%;
        border-bottom-left-radius: 3rem;
        border-bottom-right-radius: 3rem;
        transform: rotate(10deg);
    }

    55% {
        transform: rotate(-10deg);
    }

    60% {
        transform: rotate(10deg);
    }

    65% {
        transform: rotate(-10deg);
    }

    65% {
        transform: rotate(0deg);
    }

    85% {
        top: 22.5px;
        height: 6px;
        border-top-left-radius: 50%;
        border-top-right-radius: 50%;
        border-bottom-left-radius: 3rem;
        border-bottom-right-radius: 3rem;
        transform: rotate(0deg);
    }

    92.5% {
        top: 22.5px;
        height: 6px;
        border-top-left-radius: 50%;
        border-top-right-radius: 50%;
        border-bottom-left-radius: 2.5rem;
        border-bottom-right-radius: 2.5rem;
        transform: rotate(0deg);
    }

    100% {
        top: 0;
        height: 4px;
        border-radius: 50%;
        transform: rotate(-360deg);
    }
}

@keyframes pulse-outer {
    0% {
        transform: scale(1);
    }

    7.5% {
        transform: scale(1.5);
    }

    15% {
        transform: scale(1);
    }

    22.5% {
        transform: scale(1.5);
    }

    30% {
        transform: scale(1);
    }

    37.5% {
        transform: scale(1.5);
    }

    45% {
        transform: scale(1);
        height: 4px;
    }

    55% {
        transform: scale(1);
        height: 2px;
    }

    60% {
        height: 4px;
    }

    75% {
        height: 3px;
    }

    80% {
        transform: scale(1);
        height: 3px;
    }

    85% {
        height: 4px;
    }

    100% {
        height: 3px;
    }
}

@keyframes antenna-appear {
    0% {
        visibility: hidden;
        top: -10px;
        height: 0;
    }

    50% {
        visibility: hidden;
        top: -10px;
        height: 0;
    }

    55% {
        visibility: visible;
        top: -25px;
        height: 20px;
    }

    95% {
        visibility: visible;
        top: -25px;
        height: 20px;
    }

    100% {
        top: -10px;
        height: 0;
    }
}

@keyframes beam-appear {
    0% {
        visibility: hidden;
        top: -12.5px;
        height: 0;
    }

    50% {
        visibility: hidden;
        top: -12.5px;
        height: 0;
    }

    55% {
        visibility: visible;
        top: -12.5px;
        height: 20px;
        width: 20px;
    }

    100% {
        visibility: visible;
        top: -12.5px;
        height: 20px;
        width: 20px;
    }
}

@keyframes beam-pulsar-appear {
    0% {
        visibility: hidden;
        top: -12.5px;
        height: 0;
    }

    50% {
        visibility: hidden;
        top: -12.5px;
        height: 0;
    }

    55% {
        visibility: hidden;
        top: -12.5px;
        left: -5px;
        height: 20px;
        width: 20px;
        opacity: 1;
    }

    65% {
        top: -25px;
        left: -15px;
        height: 40px;
        width: 40px;
        opacity: 0;
        visibility: hidden;
    }

    74% {
        visibility: hidden;
        opacity: 0;
    }

    75% {
        visibility: visible;
        top: -12.5px;
        left: -5px;
        height: 20px;
        width: 20px;
        opacity: 1;
    }

    85% {
        top: -25px;
        left: -15px;
        height: 40px;
        width: 40px;
        opacity: 0;
        visibility: visible;
    }

    94% {
        visibility: hidden;
        opacity: 0;
    }

    100% {
        visibility: hidden;
        opacity: 0;
    }
}

@keyframes up-down {
    0% {
        transform: translate(0);
    }

    12.5% {
        transform: translate(0, 10%);
    }

    25% {
        transform: translate(0);
    }

    37.5% {
        transform: translate(0, 10%);
    }

    50% {
        transform: translate(0);
    }

    62.5% {
        transform: translate(0, 10%);
    }

    75% {
        transform: translate(0);
    }

    87.5% {
        transform: translate(0, 10%);
    }

    100% {
        transform: translate(0);
    }
}