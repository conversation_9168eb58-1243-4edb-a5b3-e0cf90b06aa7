Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.MSLLevelNo=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.MSLLevelNo.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DropDowns.MSLLevelNo.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.DropDowns.MSLLevelNo.callBaseMethod(this,"initialize");this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK))},dispose:function(){this.isDisposed||Rebound.GlobalTrader.Site.Controls.DropDowns.MSLLevelNo.callBaseMethod(this,"dispose")},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/MSLLevelNo");this._objData.set_DataObject("MSLLevelNo");this._objData.set_DataAction("GetData")},dataCallOK:function(){var t=this._objData._result,n;if(t.Types)for(n=0;n<t.Types.length;n++)this.addOption(t.Types[n].MSLLevels,t.Types[n].MSLLevelId)}};Rebound.GlobalTrader.Site.Controls.DropDowns.MSLLevelNo.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.MSLLevelNo",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);