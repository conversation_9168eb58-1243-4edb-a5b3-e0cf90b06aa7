﻿CREATE OR ALTER PROCEDURE [dbo].[usp_select_PurchaseOrderLine]                                              
--[RP-881]      Ravi            09/05/2023   RP-881(show notification if company purchaisng is 'on stop', stock will be quarantined and GI will not be released.)             
@PurchaseOrderLineId int                                                                              
AS                                                                                
 -- For EPR Start                                                      
 Declare @vEPRIds Varchar(1000)   ,@IsAuthorised bit                                                       
 Select top 1 @IsAuthorised =case when  isnull(E.Authorized,'')='' then cast(0 as bit) else cast(1 as bit) end                                                               
 From tbEPR E INNER JOIN tbPOLineEPR PE ON E.eprID = PE.eprNo                                                     
 Where PE.POLineNo = @PurchaseOrderLineId AND isnull(E.Inactive,0) = 0 AND isnull(PE.Inactive,0) = 0 and isnull(e.Authorized,'')!='' order by DLUP desc                             
                             
 IF OBJECT_ID('tempdb..#Results') IS NOT NULL DROP TABLE #Results                                
 select distinct eprno,CreatedOn  into #Results                            
 From  tbPOLineEPR                                                     
 Where POLineNo = @PurchaseOrderLineId AND isnull(Inactive,0) = 0 order by CreatedOn desc                                         
 Select  @vEPRIds = COALESCE(@vEPRIds + ',','') + Cast(eprno As Varchar)                                
 from #Results                                                      
 -- For EPR end                                            
DECLARE @ClientNo INT,@DefaultClientLotNo INT                                        
SELECT top 1 @ClientNo = case when ipo.InternalPurchaseOrderId IS null then po.ClientNo else ipo.ClientNo end FROM tbPurchaseOrder po                                         
join tbPurchaseOrderLine pol on po.PurchaseOrderId = pol.PurchaseOrderNo                                        
left join tbInternalPurchaseOrder ipo on ipo.PurchaseOrderNo = po.PurchaseOrderId                                        
JOIN tbClient c ON c.ClientId = po.ClientNo                                        
WHERE pol.PurchaseOrderLineId = @PurchaseOrderLineId                                        
                                        
SELECT top 1  @DefaultClientLotNo = LotId FROM tbLot                                         
 WHERE ClientNo = @ClientNo AND LotName = CONVERT(char(2), getdate(), 101) +RIGHT(YEAR(GETDATE()), 2) + ' Rebound Electronics Stock '+ DateName( month , DateAdd( month ,                                         
month(getdate()) , 0 ) - 1 )+' ' +cast(year(getdate()) as varchar(5))                             
                          
declare @SOPrice float                          
  declare @SOCurrecny int            
            
  ---            
              
select @SOPrice=isnull(isnull(sol.Price,0)/dbo.ufn_get_exchange_rate(so.CurrencyNo, so.DateOrdered),0)             
from tbInternalPurchaseOrderLine a join tbSalesOrderLine sol on a.SalesOrderLineNo=sol.SalesOrderLineId                 
join tbsalesorder SO on sol.SalesOrderNo=so.SalesOrderID            
where a.PurchaseOrderLineNo=@PurchaseOrderLineId                          
                          
                                 
                                        
                                        
SELECT *, cu.CurrencyCode as ClientCurrencyCode,                                        
(Select TOP 1 BOMName from tbbom where BomId=BomNo) AS BOMName ,@DefaultClientLotNo as DefaultClientLotNo                                        
, dbo.ufn_get_productdutyrate(ProductNo,getdate()) as ProductDutyRate --   (Rate %) on form   Purchase Order                                       
,@vEPRIds as 'EPRIds'                                     
,@IsAuthorised as IsAuthorised                        
,CASE WHEN  ISNULL(a.ReleaseBy,'')='' THEN CAST(0 AS BIT) ELSE CAST(1 AS BIT) END AS IsReleased                           
,a.OriginalDeliveryDate                              
, isnull(@SOPrice,0) as SOPrice                        
,isnull((a.ClientPrice * a.Quantity),0) as IpoLineTotal                    
--,isnull(ROUND(CAST( ((isnull(@SOPrice,0) * a.Quantity)* dbo.ufn_get_exchange_rate(a.CurrencyNo, a.PurchaseRequestDate))-(isnull(a.ClientPrice,0) * a.Quantity) as float),2),0)as LineProfit                  
,isnull(ROUND(CAST( ((isnull(@SOPrice,0) * a.Quantity)* dbo.ufn_get_exchange_rate(a.CurrencyNo, a.PurchaseRequestDate))-(isnull(a.ClientPrice,0) * a.Quantity)* dbo.ufn_get_exchange_rate(a.CurrencyNo, a.PurchaseRequestDate) as float),2),0)as LineProfit   
                   
,case when isnull(@SOPrice * a.Quantity,0)=0 then 0 else            
--isnull(ROUND(CAST(( ((isnull(@SOPrice,0) * a.Quantity)* dbo.ufn_get_exchange_rate(a.CurrencyNo, a.PurchaseRequestDate))-          
--(isnull(a.ClientPrice,0) * a.Quantity)) * 100 / (((isnull(@SOPrice,0) * a.Quantity)* dbo.ufn_get_exchange_rate(a.CurrencyNo, a.PurchaseRequestDate))) AS FLOAT), 2),0) end AS LineProfitPercentage           
isnull(ROUND(CAST(( ((isnull(@SOPrice,0) * a.Quantity)* dbo.ufn_get_exchange_rate(a.CurrencyNo, a.PurchaseRequestDate))-          
(isnull(a.ClientPrice,0) * a.Quantity)*dbo.ufn_get_exchange_rate(a.CurrencyNo, a.PurchaseRequestDate)) * 100 / (((isnull(@SOPrice,0) * a.Quantity)* dbo.ufn_get_exchange_rate(a.CurrencyNo, a.PurchaseRequestDate))) AS FLOAT), 2),0) end AS LineProfitPercentage                             
,cast(dbo.ufn_GetECCNMessage(a.ECCNCode,a.ClientNo)as nvarchar(900))as IHSECCNCodeDefination         
-- [RP-881] start            
, (           
        select          
        case when c.SupplierOnStop = 1 then 'Purchasing is ''On Stop''.  '            
                else ''            
            end          
    from tbCompany c          
    where c.CompanyId = a.CompanyNo)           
as OnSupplierStopMessage          
, ISNULL(c.IsSanctioned, 0) as 'IsSanctioned' --[RP-881]          
--[RP-881] end           
        
FROM  vwPurchaseOrderLine a                                        
LEFT JOIN tbCurrency cu on a.ClientCurrencyNo = cu.CurrencyId          
LEFT JOIN tbCompany c on c.CompanyId = a.CompanyNo --[RP-881]          
WHERE PurchaseOrderLineId = @PurchaseOrderLineId 
