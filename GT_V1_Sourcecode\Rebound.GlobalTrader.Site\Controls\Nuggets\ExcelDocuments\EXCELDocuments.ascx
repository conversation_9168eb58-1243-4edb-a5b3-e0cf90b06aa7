﻿<%@ Control Language="C#" AutoEventWireup="true" CodeBehind="EXCELDocuments.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Nuggets.EXCELDocuments" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" BoxType="Standard">
	<Links>
		<ReboundUI:IconButton ID="ibtnAdd" runat="server" IconGroup="Nugget" IconTitleResource="Add" IconCSSType="Add" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
	</Links>
	
	<Content>
		<asp:Panel ID="pnlEXCELDocuments" runat="server"></asp:Panel>
		<div class="clearing"></div>
	</Content>
	
	
	<Forms>
		<ReboundForm:EXCELDocuments_Add id="ctlAdd" runat="server" />
		<ReboundForm:EXCELDocuments_Delete id="ctlDelete" runat="server" />		
	</Forms>
	
</ReboundUI_Nugget:DesignBase>
