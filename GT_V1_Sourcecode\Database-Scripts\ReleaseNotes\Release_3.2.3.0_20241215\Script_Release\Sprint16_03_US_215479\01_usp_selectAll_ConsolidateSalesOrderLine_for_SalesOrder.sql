﻿SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-215479]		Trung Pham			24-Jul-2024		UPDATE			Get CountryOfOrigins
[US-215479]		Trung Pham			06-Dec-2024		UPDATE			Add default value
===========================================================================================
*/
CREATE OR ALTER PROCEDURE [dbo].[usp_selectAll_ConsolidateSalesOrderLine_for_SalesOrder]       
@SalesOrderId int               
, @IncludeInactive bit = 1              
as              
              
--declare @Notes nvarchar(500)              
              
--select top 1 @Notes = Notes from tbSalesOrderLine where SalesOrderNo = @SalesOrderId order by DLUP desc              
              
;WITH CTEGroup              
as (              
select sum(Quantity) as Quantity, Price,SalesOrderNo,Part,ManufacturerNo,PackageNo,ProductNo,DatePromised,CustomerPart,DateCode              
 ,ROHS, ProductSource, MAX(Notes) As Notes,Posted,MSLLevel,ContractNo,            
 ISNULL(PrintHazardous,0) as PrintHazardous ,ECCNCode, CountryOfOriginNo           
-- case when PrintHazardous is null then cast(0 as bit) else cast(1 as bit) end as PrintHazardous            
 from tbSalesOrderLine where SalesOrderNo = @SalesOrderId and isnull(Posted,0)=1 and Inactive IN (0, @IncludeInactive)               
 group by Price, SalesOrderNo,Part,ManufacturerNo,PackageNo,ProductNo,DatePromised,CustomerPart,DateCode,ROHS,ProductSource,Posted,MSLLevel,ContractNo,PrintHazardous,ECCNCode,CountryOfOriginNo              
              
)              
select CG.*,PR.ProductName,PK.PackageName,mf.ManufacturerCode,ManufacturerName,pr.DutyCode,          
 isnull(pr.IsHazardous,0) as IsProdHazardous                
 ,isnull(pr.IsOrderViaIPOonly,0) as IsOrderViaIPOonly    
 ,pr.ProductDescription
 ,isnull(col.GlobalCountryName,'N/A') as CountryOfOrigin
 from CTEGroup CG              
              
             LEFT JOIN dbo.tbProduct pr ON CG.ProductNo = pr.ProductId                                                        
    LEFT JOIN dbo.tbPackage pk ON CG.PackageNo = pk.PackageId                                                        
    LEFT JOIN dbo.tbManufacturer mf ON CG.ManufacturerNo = mf.ManufacturerId
	LEFT JOIN dbo.tbGlobalCountryList col ON CG.CountryOfOriginNo = col.GlobalCountryId


